using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 送达方服务接口定义
    /// </summary>
    public interface IDeliverService
    {
        /// <summary>
        /// 更新品牌单据体
        /// 注：需要自行保存
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="delivers"></param>
        void UpdateBrandEntry(UserContext topCtx, IEnumerable<DynamicObject> delivers);

        /// <summary>
        /// 反写经销商城市
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        void RewriteCity(UserContext userCtx, IEnumerable<DynamicObject> delivers);

        /// <summary>
        /// 更新销售组织
        /// 注：需要自行保存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        void UpdateSalOrg(UserContext userCtx, IEnumerable<DynamicObject> delivers);
        /// <summary>
        /// 更新销售组织，新逻辑：通过《客户销售组织与渠道关系》更新销售组织
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        void UpdateSalOrgBySales(UserContext userCtx, IEnumerable<DynamicObject> delivers);

        /// <summary>
        /// 生成商品授权清单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        void AddOrUpdateProductAuth(UserContext userCtx, IEnumerable<DynamicObject> delivers, bool IncludNoAudit = false, OperateOption option = null);


        void AddOrUpdateAgentBrandSeriesData(UserContext userCtx, List<DynamicObject> savingProductAuths);
        /// <summary>
        /// 更新【单据头.招商经销商】
        /// 注：需要自行保存
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="delivers"></param>
        void UpdateCrmDistributor(UserContext topCtx, IEnumerable<DynamicObject> delivers);

        /// <summary>
        /// 更新实控人信息
        /// 注：需要自行保存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        void UpdateBoss(UserContext userCtx, IEnumerable<DynamicObject> delivers);

        /// <summary>
        /// 更新《经销商》【招商经销商】和《招商经销商》的【经销商】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        void UpdateAgentAndCrmDistributor(UserContext userCtx, IEnumerable<DynamicObject> delivers);

        List<DynamicObject> GetDelivers(UserContext userCtx, List<string> agentIds);
    }
}
