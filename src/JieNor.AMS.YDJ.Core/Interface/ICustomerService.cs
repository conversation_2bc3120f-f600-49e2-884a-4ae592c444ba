using JieNor.AMS.YDJ.Core.DataEntity.Customer;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    public interface ICustomerService
    {
        /// <summary>
        /// 根据当前用户上下文获取客户id
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        string GetCustomerId(UserContext userCtx);

        /// <summary>
        /// 根据公司id和手机号码获取客户id
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="company"></param>
        /// <param name="phone"></param>
        /// <returns></returns>
        string GetCustomerId(UserContext userCtx, string company, string phone);

        /// <summary>
        /// 根据当前用户上下文获取客户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        DynamicObject GetCustomerInfo(UserContext userCtx);

        /// <summary>
        /// 根据公司id和手机号码获取客户
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="company"></param>
        /// <param name="phone"></param>
        /// <returns></returns>
        DynamicObject GetCustomerInfo(UserContext userCtx, string company, string phone);

        /// <summary>
        /// 根据公司id和手机号码获取客户
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="company"></param>
        /// <param name="phone"></param>
        /// <param name="dm"></param>
        /// <returns></returns>
        DynamicObject GetCustomerInfo(UserContext userCtx, HtmlForm htmlForm, string company, string phone, IDataManager dm);

        /// <summary>
        /// 根据客户唯一因子，判断是否冲突
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerUniqueItem">客户唯一因子</param>
        /// <param name="customerId">存在冲突的customerId</param>
        /// <returns></returns>
        bool CheckCustomerUnique(UserContext userCtx, CustomerUniqueItem customerUniqueItem, out string customerId);

        /// <summary>
        /// 根据客户唯一因子，获取系统能关联上的客户数据对象
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerUniqueItems">客户唯一因子</param>
        /// <param name="customerUniqueFields">客户唯一性字段</param>
        /// <returns></returns>
        Dictionary<string, DynamicObject> GetCustomerInfo(UserContext userCtx, IEnumerable<CustomerUniqueItem> customerUniqueItems, IEnumerable<string> customerUniqueFields = null);

        ///// <summary>
        ///// 创建客户共享链接
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="customerId"></param>
        ///// <param name="shareItem"></param>
        //void CreateCustomerLink(UserContext userCtx, string customerId, CustomerShareItem shareItem);

        ///// <summary>
        ///// 创建客户共享链接
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="dctCustomerLink"></param>
        //void CreateCustomerLink(UserContext userCtx, Dictionary<DynamicObject, IList<CustomerShareItem>> dctCustomerLink);

        /// <summary>
        /// 客户负责人去重并自动填充（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerForm">客户表单</param>
        /// <param name="customer">客户</param>
        void CustomerDutyDuplicateAndAutoFillMultipleDuty(UserContext userCtx, HtmlForm customerForm, DynamicObject customer);

        /// <summary>
        /// 添加客户负责人（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerForm">客户表单</param>
        /// <param name="customer">客户</param>
        /// <param name="dutyId">负责人</param>
        /// <param name="deptId">部门</param>
        void AddCustomerDuty(UserContext userCtx, HtmlForm customerForm, DynamicObject customer, string dutyId, string deptId);

        /// <summary>
        /// 替换负责人（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerForm">客户表单</param>
        /// <param name="customer">客户</param>  
        /// <param name="sourceDutyId">源负责人</param>
        /// <param name="targetDutyId">目标负责人</param>
        /// <param name="targetDeptId">目标部门</param>
        void ReplaceCustomerDuty(UserContext userCtx, HtmlForm customerForm, DynamicObject customer, string sourceDutyId, string targetDutyId, string targetDeptId);

        /// <summary>
        /// 计算当前会员积分和累计消费金额，当达到会员等级晋级条件，则晋级
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerId">客户</param>
        /// <param name="amount">本次确认金额（正数为收款，负数为退款）</param>
        void CalculateAvailableIntegralAndSumAmount(UserContext userCtx, string customerId, decimal amount);
    }
}
