using JieNor.AMS.YDJ.Core.DataEntity.Models;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta;
using Newtonsoft.Json.Linq;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 移动列表查询插件基类
    /// </summary>
    public abstract class AbstractMobileListServicePlugIn : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "buildQueryParameter":
                    buildQueryParameter(e);
                    break;
                case "changeQueryParameter":
                    changeQueryParameter(e);
                    break;
                //case "changeResult":
                //    changeResult(e);
                //    break;
                default:
                    return;
            }
        }

        private void buildQueryParameter(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }
            //构建列表查询字段
            BuildListQueryField(eventData);
        }

        private void changeQueryParameter(OnCustomServiceEventArgs e)
        {
            var result = new Dictionary<string, object> { };
            e.Result = result;

            //构建默认过滤项
            this.BuildListFilters(result);

            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }


        }

        ///// <summary>
        ///// 生成过滤条件信息
        ///// </summary>
        ///// <param name="e"></param>
        //public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        //{
        //    //构建默认过滤项
        //    this.BuildListFilters();
        //    //构建列表过滤方案
        //    this.BuildListColation();
        //    //构建列表查询字段
        //    this.BuildListQueryField();

        //}

        /// <summary>
        /// 创建列表查询字段信息
        /// </summary>
        protected virtual void BuildListQueryField(Dictionary<string, object> eventData)
        {
            var fields = eventData["fields"] as List<string>;
            if (fields != null)
            {
                var queryFieldKeys = this.GetListQueryFieldKeys();
                fields.AddRange(queryFieldKeys);
            }
        }

        /// <summary>
        /// 构建列表过滤条件
        /// </summary>
        /// <returns></returns>
        protected virtual void BuildListFilters(Dictionary<string, object> result)
        {
            Filters filters = new Filters();
            result["filters"] = filters;

            var lstFldKeys = this.GetListFilterFieldKeys();
            if (lstFldKeys.Any() == false) return;

            var billDataService = this.Container.GetService<IBillDataService>();

            Func<HtmlField, BasicFilters> defFilterFunc = (hField) =>
            {
                if (hField is HtmlComboField)
                {
                    List<ValueListItem> lstValues = new List<ValueListItem>();
                    var dataSource = billDataService.LoadAssistantData(this.Context, (hField as HtmlComboField).CategoryFilter, (hField as HtmlComboField).Filter);
                    if (dataSource.IsSuccess && dataSource.SrvData is List<BaseDataSummary>)
                    {
                        int iFlag = 0;
                        foreach (BaseDataSummary item in (dataSource.SrvData as List<BaseDataSummary>))
                        {
                            lstValues.Add(new ValueListItem()
                            {
                                Id = item.Id ,
                                Order = iFlag++,
                                Value = item.Name ,
                                Name = item.Name ,
                            });
                        }
                    }
                    if (lstValues.Any())
                    {
                        EnumFilters filter = new EnumFilters()
                        {
                            FieldId = hField.Id,
                            Operator = "=",
                            MultiSelect = false,
                            ValueList = lstValues,
                            DefaultValue = "",
                        };
                        return filter;
                    }
                }
                else if (hField is HtmlBaseDataField)
                {
                    ReferenceFilters filter = new ReferenceFilters()
                    {
                        FieldId = hField.Id,
                        FormId = (hField as HtmlBaseDataField).RefFormId,
                        MultiSelect = false,
                        Operator = "=",
                        DefaultValue = "",
                    };
                    return filter;
                }
                else if (hField is HtmlDecimalField
                || hField is HtmlDateField
                || hField is HtmlDateTimeField)
                {
                    var filter = new RegionFilters()
                    {
                        FieldId = hField.Id,
                        ElementType = hField.ElementType,
                        Operator = "between",
                        DefaultValue = "",
                    };
                    return filter;
                }
                return null;
            };


            foreach (var fldKey in lstFldKeys)
            {
                var field = this.HtmlForm.GetField(fldKey);
                var basicFilter = this.OnFilterCreating(field) ?? defFilterFunc.Invoke(field);
                if (basicFilter == null) continue;

                this.OnFilterCreated(field, basicFilter);

                if (basicFilter is EnumFilters)
                {
                    filters.Enums.Add(basicFilter as EnumFilters);
                }

                if (basicFilter is ReferenceFilters)
                {
                    filters.References.Add(basicFilter as ReferenceFilters);
                }

                if (basicFilter is RegionFilters)
                {
                    filters.Regions.Add(basicFilter as RegionFilters);
                }
            }
        }

        /// <summary>
        /// 创建字段过滤对象
        /// </summary>
        /// <param name="hField"></param>
        /// <returns></returns>
        protected virtual BasicFilters OnFilterCreating(HtmlField hField)
        {
            return null;
        }

        /// <summary>
        /// 字段过滤对象创建完毕
        /// </summary>
        /// <param name="hField"></param>
        /// <param name="filter"></param>
        protected virtual void OnFilterCreated(HtmlField hField, BasicFilters filter)
        {

        }

        /// <summary>
        /// 获取列表过滤字段信息
        /// </summary>
        /// <returns></returns>
        protected virtual IEnumerable<string> GetListFilterFieldKeys()
        {
            return new string[] { };
        }

        /// <summary>
        /// 获取列表查询字段标识
        /// </summary>
        /// <returns></returns>
        protected abstract IEnumerable<string> GetListQueryFieldKeys();

        /// <summary>
        /// 构建列表过滤方案
        /// </summary>
        /// <returns></returns>
        protected virtual void BuildListColation(Dictionary<string, object> eventData, Dictionary<string, object> result)
        {
            var colation = new Colation();
            result["colation"] = colation;
        }
    }
}
