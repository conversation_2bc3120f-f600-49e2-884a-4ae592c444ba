using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 销售机会服务接口定义
    /// </summary>
    public interface ICustomerRecordService
    {
        /// <summary>
        /// 检查商机防撞单
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="id">商机id</param>
        /// <param name="type">商机类型</param>
        /// <param name="customerId">客户id</param>
        /// <param name="phone">手机号（用于个人商机判断）</param>
        /// <param name="wechat">微信号（用于个人商机判断）</param>
        /// <param name="customerName">客户名称（用于公司商机判断）</param>
        /// <param name="saleCategoryId">意向品类id</param>
        /// <param name="errorMsg">撞单时的错误消息</param>
        /// <param name="hitedCustomerRecordObj">撞单的商机</param>
        /// <returns>是否撞单</returns>
        bool CheckHit(UserContext userCtx, string id, string type, string customerId, string phone, string wechat, string customerName, string saleCategoryId, string fdutyid, string fdeptid, out string errorMsg, out DynamicObject hitedCustomerRecordObj);

        /// <summary>
        /// 成单关闭
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerRecord">商机</param>
        /// <param name="phone">手机号</param>
        void Finish(UserContext userCtx, DynamicObject customerRecord, string phone = null);

        /// <summary>
        /// 更新组织架构
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerRecord">商机</param>
        /// <param name="allDepts">所有部门</param>
        void UpdateOrganization(UserContext userCtx, DynamicObject customerRecord, List<DynamicObject> allDepts);

        /// <summary>
        /// 销售成员去重（需要自行调用保存）
        /// </summary>
        /// <param name="customerRecord">商机</param>
        void DutyDuplicate(DynamicObject customerRecord);
        /// <summary>
        /// 根据部门获取所属门店的省市区
        /// </summary>
        /// <param name="context"></param>
        /// <param name="fdeptid"></param>
        /// <returns></returns>
        AreaObj GetAreaByDept(UserContext context, string fdeptid);
        /// <summary>
        /// 根据部门获取所属门店的省市区对象
        /// </summary>
        /// <param name="context"></param>
        /// <param name="fdeptid"></param>
        /// <returns></returns>
        AreaObjDetail GetAreaDetailByDept(UserContext context, string fdeptid);

        /// <summary>
        /// 根据部门列表获取所属门店的省市区对象
        /// </summary>
        /// <param name="context"></param>
        /// <param name="fdeptids"></param>
        /// <returns></returns>
        List<AreaObjDetail> GetAreaDetailByDept(UserContext context, List<string> fdeptids);

        /// <summary>
        /// 销售机会关闭（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerRecords">销售机会</param>
        /// <param name="closeReason">关闭原因</param>
        void Close(UserContext userCtx, IEnumerable<DynamicObject> customerRecords, string closeReason, string closeEnum = "");

        /// <summary>
        /// 销售机会反关闭（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerRecords">销售机会</param> 
        void UnClose(UserContext userCtx, IEnumerable<DynamicObject> customerRecords);
    }

    public class AreaObj
    {
        public string fcountry { get; set; }
        public string fprovince { get; set; }
        public string fcity { get; set; }
        public string fregion { get; set; }
    }


    public class AreaObjDetail
    {
        public string fdeptid { get; set; } = "";
        public BaseDataObj fcountry { get; set; } = new BaseDataObj();
        public BaseDataObj fprovince { get; set; } = new BaseDataObj();
        public BaseDataObj fcity { get; set; } = new BaseDataObj();
        public BaseDataObj fregion { get; set; } = new BaseDataObj();

        public string fmainorgid { get; set; }
    }

    public class BaseDataObj
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
    }
}