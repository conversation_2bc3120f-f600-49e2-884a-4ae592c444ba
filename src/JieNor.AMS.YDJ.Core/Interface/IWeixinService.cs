using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity.Weixin;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 微信服务接口定义
    /// </summary>
    public interface IWeixinService
    {
        /// <summary>
        /// 生成消费者小程序的小程序码（base64）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="data">数据，必要</param>
        /// <param name="page">小程序页面</param>
        /// <returns>小程序码</returns>
        WeixinWxaCode GenerateConsumerWxaCode(UserContext userCtx, string data, string page);

        /// <summary>
        /// 获取消费者小程序的小程序码（base64）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="scene">数据，必要</param>
        /// <returns>小程序码</returns>
        WeixinWxaCode GetConsumerWxaCode(UserContext userCtx, string scene);
    }
}