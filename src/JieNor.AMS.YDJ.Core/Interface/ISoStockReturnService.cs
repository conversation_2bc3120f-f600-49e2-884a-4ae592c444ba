using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 销售退货单服务接口
    /// </summary>
    public interface ISoStockReturnService
    {
        /// <summary>
        /// 初始化服务
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="htmlForm"></param>
        void InitService(UserContext userContext, HtmlForm htmlForm);

        /// <summary>
        /// 反写销售合同
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="isExcluded"></param>
        void WriteBackOrder(DynamicObject[] dataEntities, bool isExcluded);
    }
}
