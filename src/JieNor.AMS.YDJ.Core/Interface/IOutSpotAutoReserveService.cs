//using JieNor.Framework;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.SuperOrm.DataEntity;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Core.Interface
//{
//    /// <summary>
//    /// 出现货自动预留服务
//    /// </summary>
//    public interface IOutSpotAutoReserveService
//    {
//        /// <summary>
//        /// 自动预留
//        /// </summary>
//        /// <param name="userContext">用户上下文</param>
//        /// <param name="dataEntities">源单数据</param>
//        /// <param name="htmlForm">源单表单</param>
//        /// <param name="entryKey">源单明细id</param>
//        /// <param name="customerId">源单客户字段id</param>
//        /// <param name="reserveDateToId">源单预留日期字段id</param>
//        void AutoReserve(UserContext userContext, DynamicObject[] dataEntities, HtmlForm htmlForm, string entryKey, string customerId, string reserveDateToId);
//    }
//}
