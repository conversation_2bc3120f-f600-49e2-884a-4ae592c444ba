using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.AMS.YDJ.DataTransferObject.ProductPrice;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 商品价格服务接口定义
    /// </summary>
    public interface IProductPriceService
    {
        /// <summary>
        /// 获取商品价格
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="param">匹配商品价格所需要的参数</param>
        /// <returns>商品价格对象</returns>
        decimal GetPrice(UserContext userCtx, MatchParam param);
    }
}