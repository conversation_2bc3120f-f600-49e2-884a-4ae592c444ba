using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 销售合同协同检查操作服务
    /// </summary>
    public interface IOrderSyncCheckOperationService
    {
        DynamicObject[] SyncCheckOperation(UserContext userContext, string opCode, DynamicObject[] dataEntities, List<string> msg);
    }
}
