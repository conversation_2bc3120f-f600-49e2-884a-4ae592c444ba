using System.Collections.Generic;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.DataTransferObject.Rpt;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Core.Interface.Stock
{
    /// <summary>
    /// 库存服务接口定义
    /// </summary>
    public interface IInventoryService
    {
        /// <summary>
        /// 获取库存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <param name="showDetail">是否展示库存明细</param>
        /// <returns></returns>
        List<InventoryModel> GetInventory(UserContext userCtx, JArray productInfos, bool showDetail);

        /// <summary>
        /// 获取本地库存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <param name="showDetail">是否展示库存明细</param>
        /// <returns></returns>
        List<InventoryModel> GetLocalInventory(UserContext userCtx, JArray productInfos, bool showDetail);

        /// <summary>
        /// 获取远程库存
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productInfos">商品信息</param>
        /// <param name="showDetail">是否展示库存明细</param>
        /// <returns></returns>
        List<InventoryModel> GetRemoteInventory(UserContext userCtx, JArray productInfos, bool showDetail);

        /// <summary>
        /// 校验库存（仅用于销售意向/销售合同）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productEntrys">商品明细</param>
        /// <param name="formId">表单id（ydj_saleintention/ydj_order）</param>
        /// <param name="errorMsg">错误消息</param>
        /// <returns></returns>
        bool CheckInventory(UserContext userCtx, DynamicObjectCollection productEntrys, string formId, out string errorMsg);

        /// <summary>
        /// 构建数据包
        /// </summary>
        /// <param name="clientId">自定义id，用于数据区分</param>
        /// <param name="productId">商品id</param>
        /// <param name="auxPropValId">辅助属性id（与辅助属性值组合二选一）</param>
        /// <param name="auxPropVals">
        /// 辅助属性值组合（与辅助属性id二选一）
        /// 格式：
        /// [{
        ///     "auxPropId": "740943490174816262",
        ///     "valueId": "咖啡色",
        ///     "valueName": "咖啡色"
        /// },{
        ///     "auxPropId": "740943591442092038",
        ///     "valueId": "00154249/测试单人位/100*100",
        ///     "valueName": "00154249/测试单人位/100*100"
        /// },{
        ///     "auxPropId": "740943454586146822",
        ///     "valueId": "A类布",
        ///     "valueName": "A类布"
        /// }]
        /// </param>
        /// <param name="customDesc">定制说明（暂时不用）</param>
        /// <returns></returns>
        JObject BuildData(object clientId, object productId, object auxPropValId,
            object auxPropVals, object customDesc);

        /// <summary>
        /// 分页查询库存明细
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="invQueryPara">查询参数</param> 
        /// <returns></returns>
        BaseListPageData<InventoryDetailModel> PageInventoryDetail(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara);

        /// <summary>
        /// 分页查询库存汇总
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="invQueryPara">查询参数</param> 
        /// <returns></returns>
        BaseListPageData<InventorySummaryModel> PageInventorySummary(UserContext userCtx, StockSynthesizeQueryParaInfo invQueryPara);

        /// <summary>
        /// 检查库存锁定
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta">库存单据</param>
        /// <param name="dataEntities">库存数据</param>
        void CheckStockLock(UserContext userCtx, HtmlForm formMeta, DynamicObject[] dataEntities);

        /// <summary>
        /// 清空库存综合查询的扩展数据缓存标记
        /// </summary>
        /// <param name="userCtx"></param>
        void ClearExtendDataCache(UserContext userCtx);

        /// <summary>
        /// 填充先进先出库位
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta">库存表单</param>
        /// <param name="stockBills">库存单据</param>
        /// <param name="isInteraction">是否交互</param>
        /// <returns></returns>
        void FillFIFOStockInfos(UserContext userCtx, HtmlForm formMeta, IEnumerable<DynamicObject> stockBills, bool isInteraction);

        /// <summary>
        /// 填充预留库位
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formMeta">库存表单</param>
        /// <param name="stockBills">库存单据</param>
        /// <returns></returns>
        void FillReserveStockInfos(UserContext userCtx, HtmlForm formMeta, IEnumerable<DynamicObject> stockBills);
    }
}
