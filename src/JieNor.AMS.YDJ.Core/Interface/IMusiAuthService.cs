using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    public interface IMusiAuthService
    {
        string GetUrl(UserContext userCtx, HtmlForm htmlForm, string opcode);
       string GetToken(UserContext userCtx);
        bool ValidateSigeCode(UserContext userCtx, string account, string signcode);
    }
}
