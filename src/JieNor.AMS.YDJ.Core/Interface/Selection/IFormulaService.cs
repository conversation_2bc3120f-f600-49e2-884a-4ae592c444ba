using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core
{
    /// <summary>
    /// 公式服务接口定义
    /// </summary>
    public interface IFormulaService
    {
        /// <summary>
        /// 检查指定公式的语法是否符合要求，符合返回 true 不符合返回 false，参数 formula 为空时直接返回 false
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">待检查的公式</param>
        /// <param name="errorMessage">检查出错时的错误信息</param>
        /// <returns>返回公式的语法是否符合要求</returns>
        bool CheckSyntax(UserContext userCtx, string formula, out string errorMessage);

        /// <summary>
        /// 将指定的公式转换为 Python 函数表达式
        /// </summary>
        /// <param name="formula">待转换的公式</param>
        /// <returns>返回转换后的 Python 函数表达式</returns>
        string ConvertToPythonFunc(string formula);

        /// <summary>
        /// 尝试执行指定的表达式，并且返回表达式执行的结果值，内部会自动调用 ConvertToPythonFunc 将表达式转换成 Python 表达式
        /// </summary>
        /// <typeparam name="T">表达式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="expr">表达式</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>返回表达式的结果值</returns>
        T TryExecuteExpr<T>(UserContext userCtx, string expr, out string errorMessage);

        /// <summary>
        /// 尝试执行指定的 Python 表达式，并且返回表达式执行的结果值
        /// </summary>
        /// <typeparam name="T">表达式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="expr">Python 表达式</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>返回表达式的结果值</returns>
        T TryExecutePythonExpr<T>(UserContext userCtx, string expr, out string errorMessage);

        /// <summary>
        /// 尝试解析和执行指定的属性公式，并且返回公式执行的结果值
        /// 公式示例1："[床架类别]"=="底座床架"
        /// 公式示例2："[床架长]"==103 or "[床架长]"==204
        /// </summary>
        /// <typeparam name="T">公式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="propList">属性实体集合</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回公式执行的结果</returns>
        T TryParseAndExecutePropFormula<T>(
            UserContext userCtx, 
            string formula, 
            List<PropEntity> propList, 
            out string errorMessage, 
            List<DynamicObject> propDatas = null);

        /// <summary>
        /// 尝试将指定的属性公式转换为SQL形式的属性值过滤条件
        /// 公式示例1："[床架类别]"=="底座床架"
        /// 公式示例2："[床架长]"==103 or "[床架长]"==204
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回转换后的SQL条件</returns>
        string TryPropFormulaConvertToSqlWhere(UserContext userCtx, string formula, List<DynamicObject> propDatas = null);

        /// <summary>
        /// 尝试解析和执行指定的慕思SAP属性公式，并且返回公式执行的结果值
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <typeparam name="T">公式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="propList">属性实体集合</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回公式执行的结果</returns>
        T TryParseAndExecutePropFormulaForMusiSap<T>(
            UserContext userCtx,
            string formula,
            List<PropEntity> propList,
            out string errorMessage,
            List<DynamicObject> propDatas = null);

        /// <summary>
        /// 尝试将指定的慕思SAP属性公式转换为SQL形式的属性值过滤条件
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="restrictedPropNumber">被约束的属性编码</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回转换后的SQL条件</returns>
        string TryMusiSapPropFormulaConvertToSqlWhere(UserContext userCtx, string formula, string restrictedPropNumber, List<DynamicObject> propDatas = null);

        /// <summary>
        /// 尝试将指定的慕思SAP属性公式中的属性编码和属性值编码转换为属性名称和属性值名称
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="propDatas">公式中要用到的属性数据包集合</param>
        /// <param name="restrictedPropNumber">被约束的属性编码</param>
        /// <returns>返回转换后的公式</returns>
        string TryTranslateMusiSapPropFormula(UserContext userCtx, string formula, List<DynamicObject> propDatas, string restrictedPropNumber = "");

        /// <summary>
        /// 解析慕思SAP属性公式中的属性编码和属性值编码
        /// 未编译的公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// 已编译的公式示例：S005='C1411' OR S006=12 OR S006=13 OR S006=14 OR S006=15 OR S007='C1416' OR S007='C1417'
        /// </summary>
        /// <param name="formula">公式</param>
        /// <param name="needCompile">是否需要编译慕思SAP公式，默认需要编译</param>
        /// <returns>
        /// 返回解析出来的属性编码和属性值编码（属性编码为键，属性值编码集合为值的键值对）
        /// 比如：属性编码：S005、S006、S007，属性值编码：C1411、C1416、C1417
        /// </returns>
        Dictionary<string, List<string>> ParseMusiSapFormulaPropNumbers(string formula, bool needCompile = true);

        /// <summary>
        /// 解析指定的慕思SAP属性条件公式中的属性编码，并且根据属性编码加载属性数据包
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formulas">条件公式集合</param>
        /// <returns>返回解析出来的属性数据包</returns>
        List<DynamicObject> ParseMusiSapFormulaPropDatas(UserContext userCtx, List<string> formulas);

        /// <summary>
        /// 加载所有属性数据的键值对（用于替换选配约束条件公式中的属性编码）
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <returns>
        /// 返回所有属性数据信息，包括 fid、fname、fnumber、fdatatype（属性ID、属性名称、属性编码、属性数据类型）
        /// </returns>
        List<DynamicObject> LoadAllPropData(UserContext userCtx);

        /// <summary>
        /// 解析选配约束条件公式中的属性编码，并且根据属性编码加载属性数据包
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="constraints">选配约束条件</param>
        /// <returns>返回解析出来的属性数据包</returns>
        List<DynamicObject> ParseSelConstraintFormulaPropDatas(UserContext userCtx, DynamicObjectCollection constraints);

        /// <summary>
        /// 解析指定条件公式中的属性名称，并且根据属性名称加载属性数据包
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formulas">条件公式集合</param>
        /// <returns>返回解析出来的属性数据包</returns>
        List<DynamicObject> ParseFormulaPropDatas(UserContext userCtx, List<string> formulas);

        /// <summary>
        /// 解析公式中的属性名称
        /// 公式示例1："[床架类别]"=="底座床架"
        /// 公式示例2："[床架长]"==103 or "[床架长]"==204
        /// </summary>
        /// <param name="formula">公式</param>
        /// <returns>返回解析出来的属性名称集合</returns>
        List<string> ParseFormulaPropNames(string formula);

        /// <summary>
        /// 根据约束值条件加载属性值ID集合
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="sqlWheres">约束值条件</param>
        /// <param name="sqlWheres">多个约束值条件直接的逻辑分隔符，比如：or 或 and</param>
        /// <returns>返回属性值ID集合</returns>
        List<string> LoadPropValueIdsByWheres(UserContext userCtx, List<string> sqlWheres, string separator = "or");
    }
}