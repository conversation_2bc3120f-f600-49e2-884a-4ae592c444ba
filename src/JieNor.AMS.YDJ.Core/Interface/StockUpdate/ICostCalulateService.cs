using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface.StockUpdate
{
    /// <summary>
    /// 出入库成本计算服务
    /// </summary>
    public interface ICostCalulateService
    {

		string GetNearCostCalcPeriodMsg(UserContext ctx);

		/// <summary>
		/// 获取最近成本计算期间
		/// </summary>
		/// <param name="ctx"></param>
		/// <returns></returns>
		string GetNearCostCalcPeriod(UserContext ctx);


		/// <summary>
		/// 更新最新的成本计算期间
		/// </summary>
		/// <param name="ctx"></param>
		/// <returns></returns>
		void AddOrUpdateCostCalcPeriod(UserContext ctx, DateTime? date);




		/// <summary>
		/// 获取最近关账期间
		/// </summary>
		/// <param name="ctx"></param>
		/// <returns></returns>
		DateTime? GetNearClosePeriod(UserContext ctx);


		/// <summary>
		/// 更新最新的关账期间
		/// </summary>
		/// <param name="ctx"></param>
		/// <returns></returns>
		void AddOrUpdateClosePeriod(UserContext ctx, DateTime? period);


		/// <summary>
		/// 出入库成本计算：月末加权平均
		/// </summary>
		/// <param name="userCtx"></param>
		/// <param name="calPara"></param>
		/// <param name="option"></param>
		/// <returns></returns>
		IOperationResult CostCalcByEnd(UserContext ctx, CostCalculatePara calPara, OperateOption option);

		IOperationResult CostCalcByEnd_All(UserContext ctx, OperateOption option);

		IOperationResult CostCalcByEnd_Repeat(UserContext ctx, OperateOption option);


		/// <summary>
		/// 以期初盘点单的账存金额，更新期初库存余额
		/// </summary>
		/// <param name="userCtx"></param>
		/// <param name="billIds">对应的期初盘点单数据</param>
		/// <param name="option"></param>
		/// <returns></returns>
		void UpdateIniStockBalanceByVerify(UserContext ctx, List <string > billIds, OperateOption option);


		/// <summary>
		/// 更新出入库单的成本信息
		/// </summary>
		/// <param name="ctx"></param>
		/// <param name="stkFormId">库存单据</param>
		/// <param name="billDatas">库单单据数据</param>
		/// <returns>及时库存信息</returns>
		void  UpdateInvDataCostInfo(UserContext ctx, HtmlForm formMeta, List<DynamicObject> billDatas);




		/// <summary>
		/// 更新销售合同的成本信息
		/// </summary>
		/// <param name="ctx"></param>
		/// <param name="stkFormId">库存单据</param>
		/// <param name="billDatas">库单单据数据</param>
		/// <returns></returns>
		void UpdateSalesOrderCostInfo(UserContext ctx, HtmlForm formMeta, List<DynamicObject> billDatas);


		/// <summary>
		/// 更新销售合同的成本信息.
		/// 调用时机：月末成本计算
		/// 【单位成本】=【结余单位成本】，【成本】=(【基本单位数量】-【基本单位退款数量】)*【结余单位成本】
		/// </summary>
		/// <param name="ctx"></param>
		/// <param name="beginDate">开始日期</param>
		/// <param name="endDate">截止日期</param>
		/// <returns></returns>
		void UpdateSalesOrderCostInfo(UserContext ctx, DateTime beginDate, DateTime endDate);

		/// <summary>
		/// 更新销售合同的成本信息.
		/// 调用时机：小程序新增合同现货商品自动带出成本
		/// 【单位成本】=【库存成本】，【成本】=【销售数量】*【库存成本】
		/// </summary>
		/// <param name="ctx"></param>
		/// <param name="billDatas">合同单据数据</param>
		/// <returns></returns>
		void UpdateSalesOrderCostInfo(UserContext ctx, List<DynamicObject> billDatas);

		/// <summary>
		/// 从库存初始化日期或库存单据最小日期开始更新库存余额及成本
		/// 备注：本方法只作为后台方法调用，未开放给前端，如需调用请谨慎
		/// </summary>
		/// <param name="ctx"></param>
		/// <param name="option"></param>
		void UpdateStockBalance(UserContext ctx, OperateOption option);
	}
}
