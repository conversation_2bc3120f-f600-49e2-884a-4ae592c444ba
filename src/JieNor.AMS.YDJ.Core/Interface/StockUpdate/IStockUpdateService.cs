using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface.StockUpdate
{
    /// <summary>
    /// 库存更新服务
    /// </summary>
    public interface IStockUpdateService
    {
        /// <summary>
        /// 更新库存接口：支持多业务对象多场景一起更新，例如一批出的行为，一批入的行为在一起更新
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="inventoryData"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult UpdateInventory(UserContext userCtx, IEnumerable<StockUpdateObject> inventoryData, OperateOption option);
    }
}
