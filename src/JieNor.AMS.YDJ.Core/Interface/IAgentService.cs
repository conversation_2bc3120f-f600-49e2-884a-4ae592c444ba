using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 经销商服务接口定义
    /// </summary>
    public interface IAgentService
    {
        /// <summary>
        /// 获取业务经销商
        /// 先匹配【主经销商配置表】，再匹配【经销商】
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentNo">经销商编码</param>
        /// <returns>业务经销商id</returns>
        string GetBizAgentIdByNo(UserContext userCtx, string agentNo);

        /// <summary>
        /// 获取业务经销商
        /// 先匹配【主经销商配置表】，再匹配【经销商】
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentNos">经销商编码集合</param>
        /// <returns>
        /// {
        ///     { "经销商no", "业务经销商id" },
        ///     { "经销商no", "经销商id" }
        /// }
        /// </returns>
        Dictionary<string, string> GetBizAgentIdByNos(UserContext userCtx, IEnumerable<string> agentNos);

        /// <summary>
        /// 获取业务经销商
        /// 先匹配【主经销商配置表】，再匹配【经销商】
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentIds">经销商ids</param>
        /// <returns>
        /// {
        ///     { "经销商id", "业务经销商id" },
        ///     { "经销商id", "经销商id" }
        /// }
        /// </returns>
        Dictionary<string, string> GetBizAgentIdByIds(UserContext userCtx, IEnumerable<string> agentIds);

        /// <summary>
        /// 获取经销商下的子经销商
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        List<string> GetSubAgentIds(UserContext userCtx, string agentId);

        /// <summary>
        /// 获取主经销商
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentId">经销商id</param>
        /// <returns></returns>
        string GetMainAgentId(UserContext userCtx, string agentId);

        /// <summary>
        /// 获取主经销商
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentIds">子经销商ids</param>
        /// <returns>
        /// {
        ///     { "子经销商", "主经销商" },
        ///     { "子经销商", null }        // 没有时为null
        /// }
        /// </returns>
        Dictionary<string, string> GetMainAgentIds(UserContext userCtx, IEnumerable<string> agentIds);

        /// <summary>
        /// 更新实控人信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agents"></param>
        void UpdateBoss(UserContext userCtx, IEnumerable<DynamicObject> agents);
        /// <summary>
        /// 更新销售组织
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agents"></param>
        void UpdateSaleOrg(UserContext userCtx, IEnumerable<DynamicObject> agents);
        /// <summary>
        /// 更新《主经销商配置表》的【企业微信主体经销商】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agents"></param>
        void UpdateQYWXMainAgent(UserContext userCtx, IEnumerable<DynamicObject> agents);

        /// <summary>
        /// 同步经销商相关数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentIds"></param>
        Task SyncAsync(UserContext userCtx, IEnumerable<string> agentIds);

        /// <summary>
        /// 同步经销商相关数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentIds"></param>
        IOperationResult Sync(UserContext userCtx, IEnumerable<string> agentIds);

        /// <summary>
        /// 更新【单据头.招商经销商】
        /// 注：需要自行保存
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="agents"></param>
        void UpdateCrmDistributor(UserContext topCtx, IEnumerable<DynamicObject> agents);

        /// <summary>
        /// 反写《招商经销商》【单据头.售达方】
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="agents"></param>
        void RewriteCrmDistributor(UserContext topCtx, IEnumerable<DynamicObject> agents);

        /// <summary>
        /// 更新经销商关联的其它数据名称，如：组织名称、企业名称、数据中心里面的名称。
        /// </summary>
        /// <param name="topCtx">总部企业上下文</param>
        /// <param name="agents">经销商数据包</param>
        void UpdateRelationName(UserContext topCtx, IEnumerable<DynamicObject> agents);
        /// <summary>
        /// 根据经销商更新 企业名称、编码。
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="agents"></param>
        void UpdateCompany(UserContext topCtx, IEnumerable<DynamicObject> agents);

        /// <summary>
        /// 审核后的初始化（角色预设、运维人员预设）
        /// </summary>
        IOperationResult Initialize(UserContext userCtx, IEnumerable<DynamicObject> agents);

        /// <summary>
        /// 获取主子经销商(包含本身),若没有主子配置，则返回本身
        /// </summary>
        /// <param name="agentIds">经销商主键集合</param>
        /// <returns></returns>
        Dictionary<string, List<string>> GetMasterSubAgentsBatchAsync(UserContext topCtx, HashSet<string> agentIds);

        /// <summary>
        /// 获取二级经销商(平级数据)
        /// </summary>
        /// <param name="topCtx">上下文</param>
        /// <param name="parentIds">一级经销商主键集合</param>
        /// <returns></returns>
        List<DynamicObject> GetSecondLevelAgentsBatchAsync(UserContext topCtx, List<string> parentIds);
    }
}
