using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;

namespace JieNor.AMS.YDJ.Core.Interface.StockReserve
{
    /// <summary>
    /// 预留更新服务接口定义
    /// </summary>
    public interface IReserveUpdateService
    {
        /// <summary>
        /// 预留设置或预留更新
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm"></param>
        /// <param name="option"></param>
        /// <param name="reserveSettingInfos"></param>
        IOperationResult SetOrUpdateReserve(UserContext ctx, HtmlForm demandForm, List<ReserveSettingInfo> reserveSettingInfos, OperateOption option);


        /// <summary>
        /// 预留设置或预留更新
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="setting"></param>
        /// <param name="billForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option">参数选项，updateReserve等于true表示预留更新</param>
        /// <returns></returns>
        IOperationResult SetOrUpdateReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option);




        /// <summary>
        /// 更新销售合同对应的预留单的预留日期
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billForm"></param>
        /// <param name="demandBillDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        void UpdateReserveDate(UserContext ctx, HtmlForm billForm, IEnumerable<DynamicObject> demandBillDatas, OperateOption option);





        /// <summary>
        /// 预留删除----如果业务单据删除，则删除对应预留单，同时如果上游业务的预留单有转移，要重新转回去。
        /// 比如销售出库单，对应上游业务为销售合同，下推销售出库单保存时，会生成销售出库单的预留，同时把上游的销售合同的预留转出，
        /// 那么销售出库单删除时，要把对应预留单删除，同时把预留转回到上游的销售合同
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="reserveBillDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult DeleteReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option);





        ///// <summary>
        ///// 预留转移----针对库存调拨单，有源单的情况下，把源单的预留信息做转移，比如预留从A仓预留转为从B仓预留
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="transferBillDatas"></param>
        ///// <param name="option"></param>
        ///// <returns></returns>
        //IOperationResult TransferBillReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> transferBillDatas, OperateOption option);






        /// <summary>
        /// 完成预留转移----针对库存调拨单，有源单的情况下，根据调入仓在源单上做预留
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="transferBillDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult FinishTransferBillReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> transferBillDatas,
            OperateOption option);





        /// <summary>
        /// 恢复预留转移----针对库存调拨单，有源单的情况下，删除调入仓在源单上做预留
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="transferBillDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult RevertTransferBillReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> transferBillDatas,
            OperateOption option);





        /// <summary>
        /// 获取预留单信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm">业务单据标识</param> 
        ///  <param name="demandBillPKId">业务单据id</param> 
        /// <returns></returns>
        DynamicObject GetReserveBill(UserContext userCtx, string demandForm, string demandBillPKId);



        /// <summary>
        /// 获取预留单信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm">业务单据标识</param> 
        ///  <param name="demandBillDatas">业务单据id</param> 
        /// <returns></returns>
        List<DynamicObject> GetReserveBill(UserContext userCtx, string demandForm, List<DynamicObject> demandBillDatas);




        /// <summary>
        /// 检查是否有对应的预留单（用于业务单据作废校验等功能）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="result"></param>
        /// <param name="formMeta">业务单据模型</param>
        /// <param name="dataEntities">业务单据数据包</param>
        /// <returns>无预留的业务单据</returns>
        DynamicObject[] CheckReservebill(UserContext ctx, IOperationResult result, HtmlForm formMeta, DynamicObject[] dataEntities);




        /// <summary>
        /// 获取业务单据关联的预留单信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm">业务单据标识</param> 
        ///  <param name="demandBillDatas">业务单据</param> 
        /// <returns>key--业务单据id，value -- 预留单id</returns>
        Dictionary<string, string> GetReserveBillIDS(UserContext userCtx, string demandForm, IEnumerable<DynamicObject> demandBillDatas);



        /// <summary>
        /// 采购入库单反审核时，更新关联的销售合同的预留信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="reserveBillDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        IOperationResult UpdatePOInStockReserve(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option);

    }
}