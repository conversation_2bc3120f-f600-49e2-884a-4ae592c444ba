using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    public class StockSaveSetting
    {
        /// <summary>
        /// 采购入库&销售出库选单控制
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 需要反写的源单标识
        /// </summary>
        public string SourceFormId { get; set; }

        /// <summary>
        /// 上游单据控制字段标识
        /// </summary>
        public string SourceControlFieldKey { get; set; }

        /// <summary>
        /// 源单用于同当前单据明细行关联的字段标识，不提供默认为原单反写字段所在实体的主键字段
        /// </summary>
        public string SourceLinkFieldKey { get; set; }

        /// <summary>
        /// 当前表单用于关联的字段标识
        /// </summary>
        public string LinkIdFieldKey { get; set; }

        /// <summary>
        /// 关联表单类型的字段标识
        /// </summary>
        public string LinkFormFieldKey { get; set; }


        /// <summary>
        /// 目标关联单的过滤条件
        /// </summary>
        public string LinkFilterString { get; set; }

        /// <summary>
        /// 反写字段标识，如采购订单上的入库数量FStockInQty
        /// </summary>
        public string WritebackFieldKey { get; set; }

        /// <summary>
        /// 反写控制表达式：例如入库单的入库数量FQty相对采购订单而言，就是控制字段，这个字段的累积量就需要回填至采购订单上的
        /// </summary>        
        public string Expression { get; set; }


        /// <summary>
        /// 反写模式：0-自动判断模式，1-数量累积反写模式，2-下游业务有无发生模式，3-覆盖反写模式（可以是字段，可以是常量）
        /// </summary>
        /// <remarks>
        /// 表达式配置取决于反写字段的类型有3种方式：
        /// 1、反写字段为数量类型时，默认是使用此字段的累积反写机制
        /// 2、反写字段为布尔型时，默认是使用此字段的有无关联反写机制
        /// 3、反写字段为其它类型（如文本）等时，默认是使用此字段的覆盖反写机制
        /// </remarks>
        public int WritebackMode { get; set; }

        /// <summary>
        /// 超额检查条件
        /// </summary>
        public string ExcessCondition { get; set; }

        /// <summary>
        /// 超额时提示消息
        /// </summary>
        public string ExcessMessage { get; set; }

        /// <summary>
        /// 行关闭状态字段标识
        /// </summary>
        public string CloseStatusFieldKey { get; set; }

        /// <summary>
        /// 行关闭表达式
        /// </summary>
        public string CloseExpr { get; set; }

        /// <summary>
        /// 行关闭条件
        /// </summary>
        public string CloseCondition { get; set; }

        /// <summary>
        /// 行反关闭表达式
        /// </summary>
        public string UnCloseExpr { get; set; }

        /// <summary>
        /// 行反关闭条件
        /// </summary>
        public string UnCloseCondition { get; set; }
    }
}
