using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Barcode
{
    /// <summary>
    /// 扫描对象关联查询选项
    /// </summary>
    public class ScanResultLinkOption
    {
        /// <summary>
        /// 库存单据的作用实体标识
        /// </summary>
        public string ActiveEntityKey { get; set; }

        /// <summary>
        /// 关联表单类型字段标识
        /// </summary>
        public string LinkFormIdFieldKey { get; set; }

        /// <summary>
        /// 关联表单主键字段标识
        /// </summary>
        public string LinkBillIdFieldKey { get; set; }
    }
}
