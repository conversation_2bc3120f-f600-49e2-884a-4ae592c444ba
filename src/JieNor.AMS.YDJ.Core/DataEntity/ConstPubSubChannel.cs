using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    public class ConstPubSubChannel
    {


        public const string PrdDataIsolateChannel = "PrdDataIsolateChannel";


        public const string SakesControl = "SalesControl";


        public const string MPMenuChannel = "MPMenuChannel";

        /// <summary>
        /// 主经销商配置表变化
        /// </summary>
        public const string SubOrgRelationChangeChannel = "SubOrgRelationChangeChannel";


        /// <summary>
        /// 慕思AI云
        /// </summary>
        public const string MS_AICloud = "MS_AICloud";
    }



}
