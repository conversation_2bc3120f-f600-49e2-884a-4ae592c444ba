using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Customer
{
    /// <summary>
    /// 客户唯一性数据对象
    /// </summary>
    public class CustomerUniqueItem
    {
        public CustomerUniqueItem(string id)
        {
            this.Id = id;
        }

        /// <summary>
        /// 唯一项外部关联id，调用方便于根据返回值与外部数据进行映射处理
        /// </summary>
        public string Id { get; private set; }

        /// <summary>
        /// 客户姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string Wechat { get; set; }

        ///// <summary>
        ///// 省
        ///// </summary>
        //public string Province { get; set; }

        ///// <summary>
        ///// 市
        ///// </summary>
        //public string City { get; set; }

        ///// <summary>
        ///// 区
        ///// </summary>
        //public string Region { get; set; }

        ///// <summary>
        ///// 客户地址
        ///// </summary>
        //public string Address { get; set; }

        /// <summary>
        /// 客户类型
        /// </summary>
        public string Type { get; set; }
    }
}
