using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 个性化定制字段权限类
    /// 河南大商【1003290】定制化需求，禅道#6064
    /// </summary>
    public class FieldsPermissionInfo
    {
        /// <summary>
        /// 是否有权限的经销商
        /// </summary>
        public bool IsPermissionAgent { get; set; }

        /// <summary>
        /// 是否金额可见的角色
        /// </summary>
        public bool IsVisibleAmountRole { get; set; }
    }
}
