using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormModel.List;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Finance
{
    /// <summary>
    /// 财务处理中的查询对象实体
    /// 作者：zpf
    /// 日期：2022-07-27
    /// </summary>
    public class FinanceQueryObject
    {
        /// <summary>
        /// 查询参数对象 
        /// </summary>
        public SqlBuilderParameter SqlBuilderParameter { get; set; }

        /// <summary>
        /// 查询结果对象
        /// </summary>
        public QueryObject QueryObject { get; set; }

        /// <summary>
        /// 查询字段列表
        /// </summary>
        public IEnumerable<string> SqlSelectFieldList { get; set; }
    }
}
