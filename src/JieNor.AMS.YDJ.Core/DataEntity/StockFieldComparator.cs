using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Core.DataEntity
{
    /// <summary>
    /// 库存比较对象
    /// </summary>
    public class StockFieldComparator
    {
        public string MaterialIdFldKey { get; set; } = "fmaterialid";

        public string AttrInfoFldKey { get; set; } = "fattrinfo_e";

        public string CustomDescFldKey { get; set; } = "fcustomdesc";

        public string UnitIdFldKey { get; set; } = "funitid";

        public string MtonoFldKey { get; set; } = "fmtono";

        public string StoreHouseIdFldKey { get; set; } = "fstorehouseid";

        public string StoreLocationIdFldKey { get; set; } = "fstorelocationid";

        public string StockStatusFldKey { get; set; } = "fstockstatus";

        public StockFieldComparator()
        {

        }

        public bool Equal(DynamicObject dyn1, DynamicObject dyn2, Enu_StockCompareLevel level)
        {
            // 4：按 商品+辅助属性+定制说明+计量单位  匹配
            bool value = Convert.ToString(dyn1["fmaterialid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fmaterialid"]).Trim())
                         && Convert.ToString(dyn1["fattrinfo_e"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fattrinfo_e"]).Trim())
                         && Convert.ToString(dyn1["fcustomdesc"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fcustomdesc"]).Trim())
                         && Convert.ToString(dyn1["funitid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["funitid"]).Trim());

            // 加【物流跟踪号】匹配
            if (level <= Enu_StockCompareLevel.Three)
            {
                value = value
                        && (Convert.ToString(dyn1["fmtono"]).Trim()
                                .EqualsIgnoreCase(Convert.ToString(dyn2["fmtono"]).Trim()) ||
                            dyn2["fmtono"].IsNullOrEmptyOrWhiteSpace());
            }

            // 加【仓库】+【库存状态】匹配
            if (level <= Enu_StockCompareLevel.Two)
            {
                value = value
                        && Convert.ToString(dyn1["fstorehouseid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fstorehouseid"]).Trim())
                        && Convert.ToString(dyn1["fstockstatus"]).Trim()
                            .EqualsIgnoreCase(Convert.ToString(dyn2["fstockstatus"]).Trim());
            }

            return value;
        }

        public bool Equal(Dictionary<string, string> dyn1, Dictionary<string, string> dyn2, Enu_StockCompareLevel level)
        {
            // 4：按 商品+辅助属性+定制说明+计量单位  匹配
            bool value = Convert.ToString(dyn1[MaterialIdFldKey]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2[MaterialIdFldKey]).Trim())
                         && Convert.ToString(dyn1[AttrInfoFldKey]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2[AttrInfoFldKey]).Trim())
                         && Convert.ToString(dyn1[CustomDescFldKey]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2[CustomDescFldKey]).Trim())
                         && Convert.ToString(dyn1[UnitIdFldKey]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2[UnitIdFldKey]).Trim());

            // 加【物流跟踪号】匹配
            if (level <= Enu_StockCompareLevel.Three)
            {
                value = value
                        && (Convert.ToString(dyn1[MtonoFldKey]).Trim()
                                .EqualsIgnoreCase(Convert.ToString(dyn2[MtonoFldKey]).Trim()) ||
                            dyn2[MtonoFldKey].IsNullOrEmptyOrWhiteSpace());
            }

            // 加【仓库】+【库存状态】匹配
            if (level <= Enu_StockCompareLevel.Two)
            {
                value = value
                        && Convert.ToString(dyn1[StoreHouseIdFldKey]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2[StoreHouseIdFldKey]).Trim())
                        && Convert.ToString(dyn1[StockStatusFldKey]).Trim()
                            .EqualsIgnoreCase(Convert.ToString(dyn2[StockStatusFldKey]).Trim());
            }

            return value;
        }
    }
}
