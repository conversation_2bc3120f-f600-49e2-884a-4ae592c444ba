using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.DataEntity.Models
{
    /// <summary>
    /// 移动端通用列表筛选器
    /// </summary>
    public class Filters
    {
        /// <summary>
        /// 枚举类型过滤器
        /// </summary>
        public IList<EnumFilters> Enums { get; }

        /// <summary>
        /// 引用类型过滤器
        /// </summary>
        public IList<ReferenceFilters> References { get; }

        /// <summary>
        /// 区间范围过滤器
        /// </summary>
        public IList<RegionFilters> Regions { get; }

        public Filters()
        {
            Enums = new List<EnumFilters>();
            References = new List<ReferenceFilters>();
            Regions = new List<RegionFilters>();
        }
    }


    /// <summary>
    /// 移动端基础列表筛选器
    /// </summary>
    public class BasicFilters
    {
        /// <summary>
        /// 字段id
        /// </summary>
        public string FieldId { get; set; }

        /// <summary>
        /// 操作符（<,>,like,between等）
        /// </summary>
        public string Operator { get; set; }

        /// <summary>
        /// 默认值,多个值用英文逗号隔开
        /// </summary>
        public string DefaultValue { get; set; }
    }

    /// <summary>
    /// 移动端枚举列表筛选器
    /// </summary>
    public class EnumFilters:BasicFilters
    {
        /// <summary>
        /// 是否多选
        /// </summary>
        public bool MultiSelect { get; set; }

        /// <summary>
        /// 值列表
        /// </summary>
        public IEnumerable<ValueListItem> ValueList { get; set; }
    }

    /// <summary>
    /// 移动端引用列表筛选器
    /// </summary>
    public class ReferenceFilters : BasicFilters
    {
        /// <summary>
        /// 是否多选
        /// </summary>
        public bool MultiSelect { get; set; }
        /// <summary>
        /// 引用表单id
        /// </summary>
        public string FormId { get; set; }
        /// <summary>
        /// 操作码
        /// </summary>
        public string OPCode => "getReferenceFilters";
    }

    /// <summary>
    /// 移动端区间范围列表筛选器
    /// </summary>
    public class RegionFilters : BasicFilters
    {
        /// <summary>
        /// 元素类型
        /// </summary>
        public int ElementType { get; set; }

        /// <summary>
        /// 值列表
        /// </summary>
        public IEnumerable<ValueInfo> ValueList { get; set; }
    }

    /// <summary>
    /// 移动端通用列表筛选器值域
    /// </summary>
    public class ValueListItem : ValueInfo
    {
        /// <summary>
        /// 值的名称
        /// </summary>
        public string Name { get; set; }
    }
}
