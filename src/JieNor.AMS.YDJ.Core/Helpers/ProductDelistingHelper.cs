using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static IronPython.Runtime.Profiler;
using System.ComponentModel;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Core.Helpers
{
    /// <summary>
    /// 工厂退市商品帮助类
    /// </summary>
    public static class ProductDelistingHelper
    {
        public static readonly string formId = "ydj_productdelisting";

        /// <summary>
        /// 前置过滤，过滤掉提交和审核状态下触发的操作
        /// </summary>
        /// <param name="sourceFormId">来源单据</param>
        /// <param name="dataEntitys"></param>
        /// <param name="isAutoSubmitAfterSave">保存并提交</param>
        /// <param name="isAutoAuditAfterSave">保存并审核</param>
        public static List<DynamicObject> PrepareFilter(string sourceFormId, List<DynamicObject> dataEntitys, bool isAutoSubmitAfterSave = false, bool isAutoAuditAfterSave = false)
        {
            //查询所有需要判断的商品档案
            var _dataEntitys = dataEntitys
               .Where(a => (Convert.ToString(a["fstatus"]).Equals("B") || Convert.ToString(a["fstatus"]).Equals("C")) || isAutoSubmitAfterSave || isAutoAuditAfterSave)
               .ToList();
            if (sourceFormId.Equals("ydj_order"))
            {
                _dataEntitys = _dataEntitys.Where(a => !Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("销售转单")).ToList();
            }
            return _dataEntitys;
        }

        /// <summary>
        /// 明细行数据过滤
        /// </summary>
        /// <param name="sourceFormId"></param>
        /// <param name="entry"></param>
        /// <returns></returns>
        public static List<DynamicObject> PrepareFilterByEntry(string sourceFormId, DynamicObjectCollection entry)
        {
            if (sourceFormId.Equals("ydj_order"))
            {
                return entry.Where(a => Convert.ToInt32(a["fisoutspot"]) == 0 && (Convert.ToInt32(a["fclosestatus_e"]) == 0 || Convert.ToInt32(a["fclosestatus_e"]) == 2)).ToList();
            }
            //else if (sourceFormId.Equals("stk_sostockout") || sourceFormId.Equals("stk_sostockreturn"))
            //{
            //    return entry.Where(a => Convert.ToInt32(a["fisoutspot"]) == 0).ToList();
            //}
            return entry.ToList();
        }

        /// <summary>
        /// 前置校验是否在退市清单中，校验是否存在退市数据，存在则返回退市清单ID
        /// </summary>
        /// <param name="ctx">当前经销商上下文</param>
        /// <param name="allProductIds">需要校验的商品ID</param>
        /// <param name="allSelTypeIds">需要校验的型号ID</param>
        /// <returns></returns>
        public static List<string> PrepareValidationDelisting(UserContext ctx, List<string> allProductIds, List<string> allSelTypeIds)
        {
            var agentObj = ctx.LoadBizDataById("bas_agent", ctx.Company);
            if (Convert.ToBoolean(agentObj["fnotcountdelistling"]))
                return new List<string>();
            var topCtx = ctx.CreateTopOrgDBContext();
            //var products = GetDelistingProducts(topCtx);

            //使用商品内码查询所有匹配的退市清单
            var productMatch = GetDelistingProductIdList(topCtx, "fmaterialid", allProductIds);
            //使用型号内码查询所有匹配的退市清单
            var selTypeMatch = GetDelistingProductIdList(topCtx, "fseltypeid", allSelTypeIds);
            //加载所有匹配到的退市清单
            var allMatchIds = new List<string>();
            if (productMatch != null && productMatch.Any()) allMatchIds.AddRange(productMatch);
            if (selTypeMatch != null && selTypeMatch.Any()) allMatchIds.AddRange(selTypeMatch);
            if (allMatchIds.Count == 0)
                return new List<string>();
            return allMatchIds;
        }

        /// <summary>
        /// 获取受影响行信息（与快照做对比
        /// </summary>
        /// <param name="sourceForm"></param>
        /// <param name="billSnapshotObjs"></param>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        public static List<DelistingDataModel> GetSnapExceptData(HtmlForm sourceForm, List<DynamicObject> billSnapshotObjs, List<DynamicObject> dataEntity)
        {
            string productFieldId = "fproductid";
            List<DelistingDataModel> delistindDataModels = new List<DelistingDataModel>();
            List<DynamicObject> allValidateEntrys = new List<DynamicObject>();
            foreach (var item in dataEntity)
            {
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                if (item.DataEntityState.FromDatabase && existSnapshot != null)
                {
                    var entry = PrepareFilterByEntry(sourceForm.Id, item["fentry"] as DynamicObjectCollection);
                    var snapEntry = PrepareFilterByEntry(sourceForm.Id, existSnapshot["fentry"] as DynamicObjectCollection);
                    foreach (var entryItem in entry)
                    {
                        var snapEntryItem = snapEntry.Where(a => Convert.ToString(a["id"]).Equals(entryItem["id"])).FirstOrDefault();
                        if (snapEntryItem != null)
                        {
                            /*
                             * 【有效出库及转采购并提交总部或终审数量】定义如下：
                             * 将{【已提交总部或终审的采购数量】+【销售已出库数】}与合同商品行的【销售数量】进行对比：
                             * ①当【已提交总部或终审的采购数量】+【销售已出库数】-【销售已退换数量】<=【销售数量】，则取【已提交总部或终审的采购数量】+【销售已出库数】-【销售已退换数量】。
                             * ②当【已提交总部或终审的采购数量】+【销售已出库数】-【销售已退换数量】>【销售数量】，则取【销售数量】。
                             */
                            //对比数量，找出差值 
                            decimal snapBizQty = Convert.ToDecimal(snapEntryItem["fbizqty"]);
                            decimal orderBizQty = Convert.ToDecimal(entryItem["fbizqty"]);
                            decimal snapQty = Convert.ToDecimal(snapEntryItem["fbizqty"]) - Convert.ToDecimal(snapEntryItem["fbizoutqty"]) + Convert.ToDecimal(snapEntryItem["fbizreturnqty"]);
                            decimal orderQty = Convert.ToDecimal(entryItem["fbizqty"]) - Convert.ToDecimal(entryItem["fbizoutqty"]) + Convert.ToDecimal(entryItem["fbizreturnqty"]);
                            decimal snapEffectiveQty = 0;
                            decimal orderEffectiveQty = 0;
                            if (Convert.ToDecimal(snapEntryItem["fhqpurqty"]) + Convert.ToDecimal(snapEntryItem["fbizoutqty"]) - Convert.ToDecimal(snapEntryItem["fbizreturnqty"]) <= Convert.ToDecimal(snapEntryItem["fbizqty"]))
                            {
                                snapEffectiveQty = Convert.ToDecimal(snapEntryItem["fbizqty"]) - Convert.ToDecimal(snapEntryItem["fhqpurqty"]) + Convert.ToDecimal(snapEntryItem["fbizoutqty"]) - Convert.ToDecimal(snapEntryItem["fbizreturnqty"]);
                            }
                            else if (Convert.ToDecimal(snapEntryItem["fhqpurqty"]) + Convert.ToDecimal(snapEntryItem["fbizoutqty"]) - Convert.ToDecimal(snapEntryItem["fbizreturnqty"]) > Convert.ToDecimal(snapEntryItem["fbizqty"]))
                            {
                                snapEffectiveQty = Convert.ToDecimal(snapEntryItem["fbizqty"]);
                            }

                            if (Convert.ToDecimal(entryItem["fhqpurqty"]) + Convert.ToDecimal(entryItem["fbizoutqty"]) - Convert.ToDecimal(entryItem["fbizreturnqty"]) <= Convert.ToDecimal(entryItem["fbizqty"]))
                            {
                                orderEffectiveQty = Convert.ToDecimal(entryItem["fbizqty"]) - Convert.ToDecimal(entryItem["fhqpurqty"]) + Convert.ToDecimal(entryItem["fbizoutqty"]) - Convert.ToDecimal(entryItem["fbizreturnqty"]);
                            }
                            else if (Convert.ToDecimal(entryItem["fhqpurqty"]) + Convert.ToDecimal(entryItem["fbizoutqty"]) - Convert.ToDecimal(entryItem["fbizreturnqty"]) > Convert.ToDecimal(entryItem["fbizqty"]))
                            {
                                orderEffectiveQty = Convert.ToDecimal(entryItem["fbizqty"]);
                            }


                            //存在，更新
                            allValidateEntrys.Add(entryItem);
                            //判断商品是否一致，若商品不一致或辅助属性不一致，则快照商品删除，单据商品新增

                            if (Convert.ToString(snapEntryItem[productFieldId]).Equals(entryItem[productFieldId]))
                            {
                                if (Convert.ToString(snapEntryItem["fattrinfo_e"]).Trim().Equals(Convert.ToString(entryItem["fattrinfo_e"]).Trim()))
                                {
                                    if (snapQty > orderQty)
                                    {
                                        //减少了（快照数量>单据数量
                                        var obj = new DelistingDataModel()
                                        {
                                            formId = sourceForm.Id,
                                            formName = sourceForm.Caption,
                                            Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                            Qty = snapQty - orderQty,
                                            RealQty = snapEffectiveQty - orderEffectiveQty,
                                            calType = (int)CalType.Reduce,
                                            datType = (int)DataType.Modify
                                        };
                                        if (sourceForm.Id.Equals("ydj_order"))
                                        {
                                            obj.snapbizQty = snapBizQty;
                                            obj.orderbizQty = orderBizQty;
                                            obj.snapPurQty = Convert.ToDecimal(snapEntryItem["fhqpurqty"]);
                                            obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                            obj.snapOutQty = Convert.ToDecimal(snapEntryItem["fbizoutqty"]);
                                            obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                            obj.snapReturnQty = Convert.ToDecimal(snapEntryItem["fbizreturnqty"]);
                                            obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                                        }
                                        delistindDataModels.Add(obj);
                                    }
                                    else if (orderQty > snapQty)
                                    {
                                        var obj = new DelistingDataModel()
                                        {
                                            formId = sourceForm.Id,
                                            formName = sourceForm.Caption,
                                            Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                            Qty = orderQty - snapQty,
                                            RealQty = orderEffectiveQty - snapEffectiveQty,
                                            calType = (int)CalType.Add,
                                            datType = (int)DataType.Modify
                                        };
                                        if (sourceForm.Id.Equals("ydj_order"))
                                        {
                                            obj.snapbizQty = snapBizQty;
                                            obj.orderbizQty = orderBizQty;
                                            obj.snapPurQty = Convert.ToDecimal(snapEntryItem["fhqpurqty"]);
                                            obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                            obj.snapOutQty = Convert.ToDecimal(snapEntryItem["fbizoutqty"]);
                                            obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                            obj.snapReturnQty = Convert.ToDecimal(snapEntryItem["fbizreturnqty"]);
                                            obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                                        }
                                        //减少了（快照数量>单据数量
                                        delistindDataModels.Add(obj);
                                    }
                                }
                                else
                                {
                                    var obj = new DelistingDataModel()
                                    {
                                        formId = sourceForm.Id,
                                        formName = sourceForm.Caption,
                                        Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                        Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                        Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                        RealQty = orderEffectiveQty,
                                        calType = (int)CalType.Add,
                                        datType = (int)DataType.AttrinfoModify,
                                    };
                                    if (sourceForm.Id.Equals("ydj_order"))
                                    {
                                        obj.snapbizQty = snapBizQty;
                                        obj.orderbizQty = orderBizQty;
                                        obj.snapPurQty = Convert.ToDecimal(snapEntryItem["fhqpurqty"]);
                                        obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                        obj.snapOutQty = Convert.ToDecimal(snapEntryItem["fbizoutqty"]);
                                        obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                        obj.snapReturnQty = Convert.ToDecimal(snapEntryItem["fbizreturnqty"]);
                                        obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                                    }
                                    //商品一样，辅助属性不一样，把旧的减少，新增的增加
                                    delistindDataModels.Add(obj);

                                    obj = new DelistingDataModel()
                                    {
                                        formId = sourceForm.Id,
                                        formName = sourceForm.Caption,
                                        Material = snapEntryItem[productFieldId + "_ref"] as DynamicObject,
                                        Attrinfo = Convert.ToString(snapEntryItem["fattrinfo_e"]),
                                        Qty = Convert.ToDecimal(snapEntryItem["fbizqty"]),
                                        RealQty = snapEffectiveQty,
                                        calType = (int)CalType.Reduce,
                                        datType = (int)DataType.AttrinfoModify,
                                    };
                                    if (sourceForm.Id.Equals("ydj_order"))
                                    {
                                        obj.snapbizQty = snapBizQty;
                                        obj.orderbizQty = orderBizQty;
                                        obj.snapPurQty = Convert.ToDecimal(snapEntryItem["fhqpurqty"]);
                                        obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                        obj.snapOutQty = Convert.ToDecimal(snapEntryItem["fbizoutqty"]);
                                        obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                        obj.snapReturnQty = Convert.ToDecimal(snapEntryItem["fbizreturnqty"]);
                                        obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                                    };

                                    delistindDataModels.Add(obj);
                                }
                            }
                            else
                            {
                                var obj = new DelistingDataModel()
                                {
                                    formId = sourceForm.Id,
                                    formName = sourceForm.Caption,
                                    Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                    Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                    Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                    RealQty = orderEffectiveQty,
                                    calType = (int)CalType.Add,
                                    datType = (int)DataType.MaterialModify,
                                };
                                if (sourceForm.Id.Equals("ydj_order"))
                                {
                                    obj.snapbizQty = 0;
                                    obj.orderbizQty = orderBizQty;
                                    obj.snapPurQty = 0;
                                    obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                    obj.snapOutQty = 0;
                                    obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                    obj.snapReturnQty = 0;
                                    obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                                }
                                //商品不一样，旧的减少，新增增加
                                //商品一样，辅助属性不一样，把旧的减少，新增的增加
                                delistindDataModels.Add(obj);
                                obj = new DelistingDataModel()
                                {
                                    formId = sourceForm.Id,
                                    formName = sourceForm.Caption,
                                    Material = snapEntryItem[productFieldId + "_ref"] as DynamicObject,
                                    Attrinfo = Convert.ToString(snapEntryItem["fattrinfo_e"]),
                                    Qty = Convert.ToDecimal(snapEntryItem["fbizqty"]),
                                    RealQty = snapEffectiveQty,
                                    calType = (int)CalType.Reduce,
                                    datType = (int)DataType.MaterialModify,
                                };
                                if (sourceForm.Id.Equals("ydj_order"))
                                {
                                    obj.snapbizQty = snapBizQty;
                                    obj.orderbizQty = 0;
                                    obj.snapPurQty = Convert.ToDecimal(snapEntryItem["fhqpurqty"]);
                                    obj.orderPurQty = 0;
                                    obj.snapOutQty = Convert.ToDecimal(snapEntryItem["fbizoutqty"]);
                                    obj.orderOutQty = 0;
                                    obj.snapReturnQty = Convert.ToDecimal(snapEntryItem["fbizreturnqty"]);
                                    obj.orderReturnQty = 0;
                                }

                                delistindDataModels.Add(obj);
                            }
                        }
                        else
                        {
                            var obj = new DelistingDataModel()
                            {
                                formId = sourceForm.Id,
                                formName = sourceForm.Caption,
                                Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                                calType = (int)CalType.Add,
                                datType = (int)DataType.Add,
                            };
                            if (sourceForm.Id.Equals("ydj_order"))
                            {
                                obj.snapbizQty = 0;
                                obj.orderbizQty = Convert.ToDecimal(entryItem["fbizqty"]);
                                obj.snapPurQty = 0;
                                obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                obj.snapOutQty = 0;
                                obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                obj.snapReturnQty = 0;
                                obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                            }
                            //不存在，新增
                            delistindDataModels.Add(obj);
                        }
                    }
                    foreach (var entryItem in snapEntry)
                    {
                        if (!entry.Any(a => Convert.ToString(a["id"]).Equals(entryItem["id"])))
                        {
                            var obj = new DelistingDataModel()
                            {
                                formId = sourceForm.Id,
                                formName = sourceForm.Caption,
                                Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                                calType = (int)CalType.Reduce,
                                datType = (int)DataType.EntryDelete,
                            };
                            if (sourceForm.Id.Equals("ydj_order"))
                            {
                                obj.snapbizQty = 0;
                                obj.orderbizQty = Convert.ToDecimal(entryItem["fbizqty"]);
                                obj.snapPurQty = 0;
                                obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                obj.snapOutQty = 0;
                                obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                obj.snapReturnQty = 0;
                                obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                            }
                            //快照有，单据没有，删除
                            //不存在，新增
                            delistindDataModels.Add(obj);
                        }
                    }
                }
                else
                {
                    //当不存在快照，则人为是创建，每行都是新的
                    var entry = PrepareFilterByEntry(sourceForm.Id, item["fentry"] as DynamicObjectCollection);
                    foreach (var entryItem in entry)
                    {
                        var obj = new DelistingDataModel()
                        {
                            formId = sourceForm.Id,
                            formName = sourceForm.Caption,
                            Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                            Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                            RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                            calType = (int)CalType.Add,
                            datType = (int)DataType.Add,
                        };
                        if (sourceForm.Id.Equals("ydj_order"))
                        {
                            obj.snapbizQty = 0;
                            obj.orderbizQty = Convert.ToDecimal(entryItem["fbizqty"]);
                            obj.snapPurQty = 0;
                            obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                            obj.snapOutQty = 0;
                            obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                            obj.snapReturnQty = 0;
                            obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                        }
                        delistindDataModels.Add(obj);
                    }
                }
            }
            return delistindDataModels;
        }

        #region 销售合同专用，通用操作计算退市数据

        /// <summary>
        /// 获取受影响行信息（不与快照做对比
        /// </summary>
        /// <param name="sourceForm">来源单据</param>
        /// <param name="dataEntity">受影响数据</param>
        /// <param name="opCode">操作编码</param>
        /// <param name="rowIds">影响行，不传则整单明细行</param>
        /// <returns></returns>
        public static List<DelistingDataModel> GetExceptDataByOrder(HtmlForm sourceForm, List<DynamicObject> dataEntity, string opCode, List<string> rowIds = null)
        {
            int calType = 0;
            switch (opCode.ToLower())
            {
                case "cancel":
                case "orderclose":
                case "bizclose":
                case "delete":
                case "unchangebefore"://自定义的
                    calType = (int)CalType.Reduce;
                    break;
                case "uncancel":
                case "orderunclose":
                case "unclose":
                case "unchangeafter"://自定义的
                    calType = (int)CalType.Add;
                    break;
            }
            List<DelistingDataModel> delistindDataModels = new List<DelistingDataModel>();
            foreach (var item in dataEntity)
            {
                var entry = (item["fentry"] as DynamicObjectCollection).ToList();
                if (opCode.ToLower().Equals("unchangebefore") || opCode.ToLower().Equals("unchangeafter") || opCode.ToLower().Equals("delete") || opCode.ToLower().Equals("cancel") || opCode.ToLower().Equals("uncancel"))
                {
                    entry = PrepareFilterByEntry(sourceForm.Id, item["fentry"] as DynamicObjectCollection);
                }
                foreach (var entryItem in entry)
                {
                    decimal orderBizQty = Convert.ToDecimal(entryItem["fbizqty"]);
                    if (opCode.ToLower().Equals("unchangebefore"))
                    {
                        orderBizQty = Convert.ToDecimal(entryItem["fbizqty_chg"]);
                    }
                    decimal orderQty = orderBizQty - Convert.ToDecimal(entryItem["fbizoutqty"]) + Convert.ToDecimal(entryItem["fbizreturnqty"]);
                    decimal orderEffectiveQty = 0;
                    if (Convert.ToDecimal(entryItem["fhqpurqty"]) + Convert.ToDecimal(entryItem["fbizoutqty"]) - Convert.ToDecimal(entryItem["fbizreturnqty"]) <= orderBizQty)
                    {
                        orderEffectiveQty = orderBizQty - Convert.ToDecimal(entryItem["fhqpurqty"]) + Convert.ToDecimal(entryItem["fbizoutqty"]) - Convert.ToDecimal(entryItem["fbizreturnqty"]);
                    }
                    else if (Convert.ToDecimal(entryItem["fhqpurqty"]) + Convert.ToDecimal(entryItem["fbizoutqty"]) - Convert.ToDecimal(entryItem["fbizreturnqty"]) > orderBizQty)
                    {
                        orderEffectiveQty = orderBizQty;
                    }
                    if (rowIds != null && rowIds.Count > 0)
                    {
                        //指定行参与
                        if (rowIds.Any(a => a.Equals(Convert.ToString(entryItem["id"]))))
                        {
                            var obj = new DelistingDataModel()
                            {
                                formId = sourceForm.Id,
                                formName = sourceForm.Caption,
                                Material = entryItem["fproductid_ref"] as DynamicObject,
                                Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                Qty = orderQty,
                                RealQty = orderEffectiveQty,
                                calType = calType,
                                opCode = opCode
                            };
                            if (sourceForm.Id.Equals("ydj_order"))
                            {
                                obj.snapbizQty = 0;
                                obj.orderbizQty = orderBizQty;
                                obj.snapPurQty = 0;
                                obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                                obj.snapOutQty = 0;
                                obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                                obj.snapReturnQty = 0;
                                obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                            }
                            delistindDataModels.Add(obj);
                        }
                    }
                    else
                    {
                        var obj = new DelistingDataModel()
                        {
                            formId = sourceForm.Id,
                            formName = sourceForm.Caption,
                            Material = entryItem["fproductid_ref"] as DynamicObject,
                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                            Qty = orderQty,
                            RealQty = orderEffectiveQty,
                            calType = calType,
                            opCode = opCode
                        };
                        if (sourceForm.Id.Equals("ydj_order"))
                        {
                            obj.snapbizQty = 0;
                            obj.orderbizQty = orderBizQty;
                            obj.snapPurQty = 0;
                            obj.orderPurQty = Convert.ToDecimal(entryItem["fhqpurqty"]);
                            obj.snapOutQty = 0;
                            obj.orderOutQty = Convert.ToDecimal(entryItem["fbizoutqty"]);
                            obj.snapReturnQty = 0;
                            obj.orderReturnQty = Convert.ToDecimal(entryItem["fbizreturnqty"]);
                        }
                        delistindDataModels.Add(obj);
                    }
                }
            }
            return delistindDataModels;
        }

        /// <summary>
        /// 【通用】根据操作转换对应数据包进行退市计算
        /// </summary>
        /// <param name="context">当前上下文</param>
        /// <param name="htmlForm">当前表单</param>
        /// <param name="operationNo">操作编码</param>
        /// <param name="dataEntitys">受影响数据</param>
        /// <param name="selectRowIds">选择行（不传则整单）</param>
        public static void DealDelistingDataByCommon(UserContext context, HtmlForm htmlForm, string operationNo, List<DynamicObject> dataEntitys, List<string> selectRowIds = null)
        {
            if (dataEntitys == null || dataEntitys.Count <= 0) return;
            var refObjMgr = context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(context, dataEntitys.ToArray(), true, htmlForm, new List<string> { "fproductid" });

            dataEntitys = dataEntitys.Where(a => !Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("销售转单") && Convert.ToInt32(a["fisresellorder"]) == 0).ToList();
            if (dataEntitys == null || dataEntitys.Count <= 0) return;
            List<DelistingDataModel> changeDataModel = ProductDelistingHelper.GetExceptDataByOrder(htmlForm, dataEntitys, operationNo, selectRowIds);

            changeDataModel = changeDataModel.Where(t => t.Material != null && Convert.ToBoolean(t.Material?["fdelisting"])).ToList();
            List<DynamicObject> allValidateEntrys = new List<DynamicObject>();
            if (changeDataModel != null && changeDataModel.Any())
            {
                var allProductIds = changeDataModel.Select(t => Convert.ToString(t.Material?["id"])).ToList();
                var allSelTypeIds = changeDataModel.Select(t => Convert.ToString(t.Material?["fseltypeid"])).ToList();

                List<string> allDelistingIds = ProductDelistingHelper.PrepareValidationDelisting(context, allProductIds, allSelTypeIds);

                if (allDelistingIds.Count > 0)
                {
                    //Task task = Task.Run(() =>
                    //{
                    //ProductDelistingHelper.UpdateDelistingQty(this.Context, this.HtmlForm.Id, allValidateEntrys, allProductIds, allSelTypeIds, allDelistingIds);
                    ProductDelistingLogger logger = new ProductDelistingLogger(htmlForm.Id);
                    logger.AppendLine("changeDataModel：");
                    logger.AppendLine(JsonConvert.SerializeObject(changeDataModel));
                    logger.AppendLine("allDelistingIds：" + string.Join(",", allDelistingIds));
                    logger.AppendLine("operationNo：" + operationNo);
                    logger.Write();
                    ProductDelistingHelper.UpdateDelistingQtyNew(context, htmlForm.Id, changeDataModel, allDelistingIds);
                    //});
                    //ThreadWorker.QuequeTask(task, x =>
                    //{
                    //    if (x?.Exception != null)
                    //    {
                    //        this.OperationContext?.Container.GetService<ILogServiceEx>().Error("异常执行退市清单计算错误：" + x.Exception.Message);
                    //    }
                    //});
                }

            }
        }

        /// <summary>
        /// 【取消变更】根据操作转换对应数据包进行退市计算
        /// </summary>
        /// <param name="context">当前上下文</param>
        /// <param name="htmlForm">当前表单</param>
        /// <param name="operationNo">操作编码</param>
        /// <param name="beforeDataEntitys">受影响数据（单据数据）</param>
        /// <param name="afterDataEntitys">受影响数据（快照数据）</param>
        /// <param name="selectRowIds"></param>
        public static void DealDelistingDataByUnChange(UserContext context, HtmlForm htmlForm, string operationNo, List<DynamicObject> beforeDataEntitys, List<DynamicObject> afterDataEntitys, List<string> selectRowIds = null)
        {
            afterDataEntitys = afterDataEntitys.Where(a => !Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("销售转单") && Convert.ToInt32(a["fisresellorder"]) == 0).ToList();
            if (afterDataEntitys == null || afterDataEntitys.Count <= 0) return;

            var mate = context.Container.GetService<IMetaModelService>();
            var orderchgForm = mate.LoadFormModel(context, "ydj_order_chg");
            var refObjMgr = context.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(context, beforeDataEntitys.ToArray(), true, orderchgForm, new List<string> { "fproductid" });
            refObjMgr?.Load(context, afterDataEntitys.ToArray(), true, htmlForm, new List<string> { "fproductid" });

            //取消变更，把当前单据的都减少，重新把变更前的数据还原上去
            List<DelistingDataModel> changeDataModel = ProductDelistingHelper.GetExceptDataByOrder(htmlForm, beforeDataEntitys, "unchangebefore", selectRowIds);
            changeDataModel.AddRange(ProductDelistingHelper.GetExceptDataByOrder(htmlForm, afterDataEntitys, "unchangeafter", selectRowIds));


            changeDataModel = changeDataModel.Where(t => t.Material != null && Convert.ToBoolean(t.Material?["fdelisting"])).ToList();
            List<DynamicObject> allValidateEntrys = new List<DynamicObject>();
            if (changeDataModel != null && changeDataModel.Any())
            {
                var allProductIds = changeDataModel.Select(t => Convert.ToString(t.Material?["id"])).ToList();
                var allSelTypeIds = changeDataModel.Select(t => Convert.ToString(t.Material?["fseltypeid"])).ToList();

                List<string> allDelistingIds = ProductDelistingHelper.PrepareValidationDelisting(context, allProductIds, allSelTypeIds);

                if (allDelistingIds.Count > 0)
                {
                    //Task task = Task.Run(() =>
                    //{
                    //ProductDelistingHelper.UpdateDelistingQty(this.Context, this.HtmlForm.Id, allValidateEntrys, allProductIds, allSelTypeIds, allDelistingIds);
                    ProductDelistingLogger logger = new ProductDelistingLogger(htmlForm.Id);
                    logger.AppendLine("changeDataModel：");
                    logger.AppendLine(JsonConvert.SerializeObject(changeDataModel));
                    logger.AppendLine("allDelistingIds：" + string.Join(",", allDelistingIds));
                    logger.Write();
                    ProductDelistingHelper.UpdateDelistingQtyNew(context, htmlForm.Id, changeDataModel, allDelistingIds);
                    //});
                    //ThreadWorker.QuequeTask(task, x =>
                    //{
                    //    if (x?.Exception != null)
                    //    {
                    //        this.OperationContext?.Container.GetService<ILogServiceEx>().Error("异常执行退市清单计算错误：" + x.Exception.Message);
                    //    }
                    //});
                }

            }
        }

        #endregion

        #region 出库(退货)单专用

        /// <summary>
        /// 获取销售合同数据
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="htmlForm"></param>
        /// <returns></returns>
        public static List<DynamicObject> GetOrderData(UserContext userContext, List<DynamicObject> dataEntitys, HtmlForm htmlForm)
        {
            List<DynamicObject> result = new List<DynamicObject>();
            List<string> orderIds = new List<string>();
            if (htmlForm.Id.Equals("stk_sostockout") || htmlForm.Id.Equals("stk_sostockreturn"))
            {
                foreach (var item in dataEntitys)
                {
                    if (Convert.ToInt32(item["fisresellorder"]) == 1)
                        continue;
                    if (htmlForm.Id.Equals("stk_sostockreturn") && !Convert.ToString(item["freturntype"]).Equals("sostockreturn_biztype_01"))
                    {
                        continue;
                    }
                    var entry = PrepareFilterByEntry(htmlForm.Id, item["fentity"] as DynamicObjectCollection);
                    foreach (var entryItem in entry)
                    {
                        orderIds.Add(Convert.ToString(entryItem["fsoorderinterid"]));
                    }
                }
            }
            else if (htmlForm.Id.Equals("ydj_purchaseorder"))
            {
                foreach (var item in dataEntitys)
                {
                    var entry = item["fentity"] as DynamicObjectCollection;
                    foreach (var entryItem in entry)
                    {
                        orderIds.Add(Convert.ToString(entryItem["fsoorderinterid"]));
                    }
                }
            }
            orderIds = orderIds.Distinct().ToList();
            if (orderIds.Count > 0)
            {
                result = userContext.LoadBizDataById("ydj_order", orderIds, true);
            }
            return result;
        }

        /// <summary>
        /// 获取受影响行信息（不与快照做对比
        /// </summary>
        /// <param name="context"></param>
        /// <param name="htmlForm">来源业务对象</param>
        /// <param name="beforeDataEntitys">受影响前数量</param>
        /// <param name="DataEntitys">单据数据</param>
        /// <param name="opCode">操作编码</param>
        /// <returns></returns>
        public static void DealDelistingDataByStockOut(UserContext context, HtmlForm htmlForm, List<DynamicObject> beforeDataEntitys, List<DynamicObject> DataEntitys, string opCode)
        {
            DataEntitys = DataEntitys.Where(a => Convert.ToInt32(a["fisresellorder"]) == 0).ToList();
            if (htmlForm.Id.Equals("stk_sostockreturn"))
            {
                DataEntitys = DataEntitys.Where(a => Convert.ToString(a["freturntype"]).Equals("sostockreturn_biztype_01")).ToList();
            }
            if (DataEntitys == null || DataEntitys.Count <= 0) return;
            var auditAfterOrder = ProductDelistingHelper.GetOrderData(context, DataEntitys, htmlForm);

            if (beforeDataEntitys == null || beforeDataEntitys.Count == 0 || auditAfterOrder == null || auditAfterOrder.Count == 0) return;

            var mate = context.Container.GetService<IMetaModelService>();
            var orderForm = mate.LoadFormModel(context, "ydj_order");

            var changeDataModel = ProductDelistingHelper.GetExceptDataByStock(htmlForm, beforeDataEntitys, auditAfterOrder, DataEntitys, opCode);

            changeDataModel = changeDataModel.Where(t => t.Material != null && Convert.ToBoolean(t.Material?["fdelisting"])).ToList();
            List<DynamicObject> allValidateEntrys = new List<DynamicObject>();
            if (changeDataModel != null && changeDataModel.Any())
            {
                var allProductIds = changeDataModel.Select(t => Convert.ToString(t.Material?["id"])).ToList();
                var allSelTypeIds = changeDataModel.Select(t => Convert.ToString(t.Material?["fseltypeid"])).ToList();

                List<string> allDelistingIds = ProductDelistingHelper.PrepareValidationDelisting(context, allProductIds, allSelTypeIds);

                if (allDelistingIds.Count > 0)
                {
                    //Task task = Task.Run(() =>
                    //{
                    //    //ProductDelistingHelper.UpdateDelistingQty(this.Context, this.HtmlForm.Id, allValidateEntrys, allProductIds, allSelTypeIds, allDelistingIds);
                    ProductDelistingLogger logger = new ProductDelistingLogger(htmlForm.Id);
                    logger.AppendLine("changeDataModel：");
                    logger.AppendLine(JsonConvert.SerializeObject(changeDataModel));
                    logger.AppendLine("allDelistingIds：" + string.Join(",", allDelistingIds));
                    logger.Write();
                    ProductDelistingHelper.UpdateDelistingQtyNew(context, orderForm.Id, changeDataModel, allDelistingIds);
                    //});
                    //ThreadWorker.QuequeTask(task, x =>
                    //{
                    //    if (x?.Exception != null)
                    //    {
                    //        this.OperationContext?.Container.GetService<ILogServiceEx>().Error("异常执行退市清单计算错误：" + x.Exception.Message);
                    //    }
                    //});
                }
            }
        }


        /// <summary>
        /// 获取受影响行信息（不与快照做对比
        /// </summary>
        /// <param name="sourceForm">来源单据</param>
        /// <param name="orderDataEntity">受影响数据（销售合同数据）</param>
        /// <param name="dataEntity">受影响数据</param>
        /// <param name="opCode">操作编码</param>
        /// <returns></returns>
        public static List<DelistingDataModel> GetExceptDataByStock(HtmlForm sourceForm, List<DynamicObject> beforeDataEntitys, List<DynamicObject> afterDataEntitys, List<DynamicObject> dataEntity, string opCode)
        {
            int calType = 0;
            switch (opCode.ToLower())
            {
                case "stockoutreturnunaudit":
                case "stockoutaudit":
                    calType = (int)CalType.Reduce;
                    break;
                case "stockoutreturnaudit":
                case "stockoutunaudit":
                    calType = (int)CalType.Add;
                    break;
            }
            List<DelistingDataModel> delistindDataModels = new List<DelistingDataModel>();
            foreach (var item in dataEntity)
            {
                var entry = PrepareFilterByEntry(sourceForm.Id, item["fentity"] as DynamicObjectCollection);
                foreach (var entryItem in entry)
                {
                    string soorderinterid = Convert.ToString(entryItem["fsoorderinterid"]);
                    string soorderentryid = Convert.ToString(entryItem["fsoorderentryid"]);
                    decimal bizQty = Convert.ToDecimal(entryItem["fbizqty"]);
                    DynamicObject afterEntryItem = null;
                    DynamicObject beforeEntryItem = null;
                    foreach (var afterItem in afterDataEntitys)
                    {
                        if (!Convert.ToString(afterItem["id"]).Equals(soorderinterid)) continue;
                        var _entry = (afterItem["fentry"] as DynamicObjectCollection).Where(a => Convert.ToInt32(a["fisoutspot"]) == 0).ToList();
                        foreach (var _entryItem in _entry)
                        {
                            if (Convert.ToString(_entryItem["id"]).Equals(soorderentryid))
                            {
                                afterEntryItem = _entryItem;
                            }
                        }
                    }
                    foreach (var beforeItem in beforeDataEntitys)
                    {
                        if (!Convert.ToString(beforeItem["id"]).Equals(soorderinterid)) continue;
                        var _entry = (beforeItem["fentry"] as DynamicObjectCollection).Where(a => Convert.ToInt32(a["fisoutspot"]) == 0).ToList();
                        foreach (var _entryItem in _entry)
                        {
                            if (Convert.ToString(_entryItem["id"]).Equals(soorderentryid))
                            {
                                beforeEntryItem = _entryItem;
                            }
                        }
                    }
                    if (beforeEntryItem == null || afterEntryItem == null)
                    {
                        //源单数据未找到
                        return new List<DelistingDataModel>();
                    }
                    //对比数量，找出差值 
                    decimal snapBizQty = Convert.ToDecimal(beforeEntryItem["fbizqty"]);
                    decimal orderBizQty = Convert.ToDecimal(afterEntryItem["fbizqty"]);
                    decimal snapEffectiveQty = 0;
                    decimal orderEffectiveQty = 0;
                    if (Convert.ToDecimal(beforeEntryItem["fhqpurqty"]) + Convert.ToDecimal(beforeEntryItem["fbizoutqty"]) - Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]) <= Convert.ToDecimal(beforeEntryItem["fbizqty"]))
                    {
                        snapEffectiveQty = Convert.ToDecimal(beforeEntryItem["fbizqty"]) - Convert.ToDecimal(beforeEntryItem["fhqpurqty"]) + Convert.ToDecimal(beforeEntryItem["fbizoutqty"]) - Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]);
                    }
                    else if (Convert.ToDecimal(beforeEntryItem["fhqpurqty"]) + Convert.ToDecimal(beforeEntryItem["fbizoutqty"]) - Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]) > Convert.ToDecimal(beforeEntryItem["fbizqty"]))
                    {
                        snapEffectiveQty = Convert.ToDecimal(beforeEntryItem["fbizqty"]) - Convert.ToDecimal(beforeEntryItem["fbizqty"]);
                    }

                    if (Convert.ToDecimal(afterEntryItem["fhqpurqty"]) + Convert.ToDecimal(afterEntryItem["fbizoutqty"]) - Convert.ToDecimal(afterEntryItem["fbizreturnqty"]) <= Convert.ToDecimal(afterEntryItem["fbizqty"]))
                    {
                        orderEffectiveQty = Convert.ToDecimal(afterEntryItem["fbizqty"]) - Convert.ToDecimal(afterEntryItem["fhqpurqty"]) + Convert.ToDecimal(afterEntryItem["fbizoutqty"]) - Convert.ToDecimal(afterEntryItem["fbizreturnqty"]);
                    }
                    else if (Convert.ToDecimal(afterEntryItem["fhqpurqty"]) + Convert.ToDecimal(afterEntryItem["fbizoutqty"]) - Convert.ToDecimal(afterEntryItem["fbizreturnqty"]) > Convert.ToDecimal(afterEntryItem["fbizqty"]))
                    {
                        orderEffectiveQty = Convert.ToDecimal(afterEntryItem["fbizqty"]) - Convert.ToDecimal(afterEntryItem["fbizqty"]);
                    }


                    var obj = new DelistingDataModel()
                    {
                        formId = sourceForm.Id,
                        formName = sourceForm.Caption,
                        Material = entryItem["fmaterialid_ref"] as DynamicObject,
                        Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                        Qty = calType == 1 ? snapEffectiveQty - orderEffectiveQty : orderEffectiveQty - snapEffectiveQty,
                        RealQty = bizQty,
                        calType = calType,
                        opCode = opCode
                    };
                    obj.snapbizQty = snapBizQty;
                    obj.orderbizQty = orderBizQty;
                    obj.snapPurQty = Convert.ToDecimal(beforeEntryItem["fhqpurqty"]);
                    obj.orderPurQty = Convert.ToDecimal(afterEntryItem["fhqpurqty"]);
                    obj.snapOutQty = Convert.ToDecimal(beforeEntryItem["fbizoutqty"]);
                    obj.orderOutQty = Convert.ToDecimal(afterEntryItem["fbizoutqty"]);
                    obj.snapReturnQty = Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]);
                    obj.orderReturnQty = Convert.ToDecimal(afterEntryItem["fbizreturnqty"]);
                    delistindDataModels.Add(obj);
                }
            }
            return delistindDataModels;
        }


        #endregion

        #region 采购专用

        /// <summary>
        /// 获取受影响行信息（不与快照做对比
        /// </summary>
        /// <param name="context"></param>
        /// <param name="htmlForm">来源业务对象</param>
        /// <param name="beforeDataEntitys">受影响前数量</param>
        /// <param name="DataEntitys">单据数据</param>
        /// <param name="opCode">操作编码</param>
        /// <returns></returns>
        public static void DealDelistingDataByPurOrder(UserContext context, HtmlForm htmlForm, List<DynamicObject> beforeDataEntitys, List<DynamicObject> DataEntitys, string opCode)
        {
            if (DataEntitys == null || DataEntitys.Count <= 0) return;
            DataEntitys = DataEntitys.Where(a => Convert.ToInt32(a["fchangestatus"]) == 0).ToList();
            if (DataEntitys == null || DataEntitys.Count <= 0) return;
            var auditAfterOrder = ProductDelistingHelper.GetOrderData(context, DataEntitys, htmlForm);


            //var mate = context.Container.GetService<IMetaModelService>();
            //var orderForm = mate.LoadFormModel(context, "ydj_order");

            //var changeDataModel = ProductDelistingHelper.GetExceptDataByPurOrder(htmlForm, billSnapshotObjs, entryEntities);
            var changeDataModel = ProductDelistingHelper.GetExceptDataByPurOrder(htmlForm, beforeDataEntitys, auditAfterOrder, DataEntitys, opCode);

            changeDataModel = changeDataModel?.Where(t => t.Material != null && Convert.ToBoolean(t.Material?["fdelisting"])).ToList();
            List<DynamicObject> allValidateEntrys = new List<DynamicObject>();
            if (changeDataModel != null && changeDataModel.Any())
            {
                var allProductIds = changeDataModel.Select(t => Convert.ToString(t.Material?["id"])).ToList();
                var allSelTypeIds = changeDataModel.Select(t => Convert.ToString(t.Material?["fseltypeid"])).ToList();

                List<string> allDelistingIds = ProductDelistingHelper.PrepareValidationDelisting(context, allProductIds, allSelTypeIds);

                if (allDelistingIds.Count > 0)
                {
                    //Task task = Task.Run(() =>
                    //{
                    //    //ProductDelistingHelper.UpdateDelistingQty(this.Context, this.HtmlForm.Id, allValidateEntrys, allProductIds, allSelTypeIds, allDelistingIds);
                    ProductDelistingLogger logger = new ProductDelistingLogger(htmlForm.Id);
                    logger.AppendLine("changeDataModel：");
                    logger.AppendLine(JsonConvert.SerializeObject(changeDataModel));
                    logger.AppendLine("allDelistingIds：" + string.Join(",", allDelistingIds));
                    logger.AppendLine("opCode：" + opCode);
                    logger.Write();
                    ProductDelistingHelper.UpdateDelistingQtyNew(context, htmlForm.Id, changeDataModel, allDelistingIds);
                    //});
                    //ThreadWorker.QuequeTask(task, x =>
                    //{
                    //    if (x?.Exception != null)
                    //    {
                    //        this.OperationContext?.Container.GetService<ILogServiceEx>().Error("异常执行退市清单计算错误：" + x.Exception.Message);
                    //    }
                    //});
                }
            }
        }

        /// <summary>
        /// 获取受影响行信息（不与快照做对比
        /// </summary>
        /// <param name="sourceForm">来源单据</param>
        /// <param name="orderDataEntity">受影响数据（销售合同数据）</param>
        /// <param name="dataEntity">受影响数据</param>
        /// <param name="opCode">操作编码</param>
        /// <returns></returns>
        public static List<DelistingDataModel> GetExceptDataByPurOrder(HtmlForm sourceForm, List<DynamicObject> beforeDataEntitys, List<DynamicObject> afterDataEntitys, List<DynamicObject> dataEntity, string opCode)
        {
            int calType = 0;
            switch (opCode.ToLower())
            {
                case "submithq":
                    calType = (int)CalType.Add;
                    break;
                case "mssave":
                    calType = (int)CalType.Reduce;
                    break;
            }
            List<DelistingDataModel> delistindDataModels = new List<DelistingDataModel>();
            foreach (var item in dataEntity)
            {
                var entry = item["fentity"] as DynamicObjectCollection;
                foreach (var entryItem in entry)
                {
                    string soorderinterid = Convert.ToString(entryItem["fsoorderinterid"]);
                    string soorderentryid = Convert.ToString(entryItem["fsoorderentryid"]);
                    decimal bizQty = Convert.ToDecimal(entryItem["fbizqty"]);
                    if (string.IsNullOrWhiteSpace(soorderinterid))
                    {
                        //备货场景
                        var obj = new DelistingDataModel()
                        {
                            formId = sourceForm.Id,
                            formName = sourceForm.Caption,
                            Material = entryItem["fmaterialid_ref"] as DynamicObject,
                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                            Qty = bizQty,
                            RealQty = 0,
                            calType = calType,
                            opCode = opCode
                        };
                        delistindDataModels.Add(obj);
                    }
                    else
                    {

                        DynamicObject afterEntryItem = null;
                        DynamicObject beforeEntryItem = null;
                        foreach (var afterItem in afterDataEntitys)
                        {
                            if (!Convert.ToString(afterItem["id"]).Equals(soorderinterid)) continue;
                            var _entry = (afterItem["fentry"] as DynamicObjectCollection).Where(a => Convert.ToInt32(a["fisoutspot"]) == 0).ToList();
                            foreach (var _entryItem in _entry)
                            {
                                if (Convert.ToString(_entryItem["id"]).Equals(soorderentryid))
                                {
                                    afterEntryItem = _entryItem;
                                }
                            }
                        }
                        foreach (var beforeItem in beforeDataEntitys)
                        {
                            if (!Convert.ToString(beforeItem["id"]).Equals(soorderinterid)) continue;
                            var _entry = (beforeItem["fentry"] as DynamicObjectCollection).Where(a => Convert.ToInt32(a["fisoutspot"]) == 0).ToList();
                            foreach (var _entryItem in _entry)
                            {
                                if (Convert.ToString(_entryItem["id"]).Equals(soorderentryid))
                                {
                                    beforeEntryItem = _entryItem;
                                }
                            }
                        }
                        if (beforeEntryItem == null || afterEntryItem == null)
                        {
                            //源单数据未找到
                            return null;
                        }
                        //对比数量，找出差值 
                        decimal snapBizQty = Convert.ToDecimal(beforeEntryItem["fbizqty"]);
                        decimal orderBizQty = Convert.ToDecimal(afterEntryItem["fbizqty"]);
                        decimal snapEffectiveQty = 0;
                        decimal orderEffectiveQty = 0;
                        if (Convert.ToDecimal(beforeEntryItem["fhqpurqty"]) + Convert.ToDecimal(beforeEntryItem["fbizoutqty"]) - Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]) <= Convert.ToDecimal(beforeEntryItem["fbizqty"]))
                        {
                            snapEffectiveQty = Convert.ToDecimal(beforeEntryItem["fbizqty"]) - Convert.ToDecimal(beforeEntryItem["fhqpurqty"]) + Convert.ToDecimal(beforeEntryItem["fbizoutqty"]) - Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]);
                        }
                        else if (Convert.ToDecimal(beforeEntryItem["fhqpurqty"]) + Convert.ToDecimal(beforeEntryItem["fbizoutqty"]) - Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]) > Convert.ToDecimal(beforeEntryItem["fbizqty"]))
                        {
                            snapEffectiveQty = Convert.ToDecimal(beforeEntryItem["fbizqty"]) - Convert.ToDecimal(beforeEntryItem["fbizqty"]);
                        }

                        if (Convert.ToDecimal(afterEntryItem["fhqpurqty"]) + Convert.ToDecimal(afterEntryItem["fbizoutqty"]) - Convert.ToDecimal(afterEntryItem["fbizreturnqty"]) <= Convert.ToDecimal(afterEntryItem["fbizqty"]))
                        {
                            orderEffectiveQty = Convert.ToDecimal(afterEntryItem["fbizqty"]) - Convert.ToDecimal(afterEntryItem["fhqpurqty"]) + Convert.ToDecimal(afterEntryItem["fbizoutqty"]) - Convert.ToDecimal(afterEntryItem["fbizreturnqty"]);
                        }
                        else if (Convert.ToDecimal(afterEntryItem["fhqpurqty"]) + Convert.ToDecimal(afterEntryItem["fbizoutqty"]) - Convert.ToDecimal(afterEntryItem["fbizreturnqty"]) > Convert.ToDecimal(afterEntryItem["fbizqty"]))
                        {
                            orderEffectiveQty = Convert.ToDecimal(afterEntryItem["fbizqty"]) - Convert.ToDecimal(afterEntryItem["fbizqty"]);
                        }
                        //decimal RealQty = 0;
                        //if (orderEffectiveQty - snapEffectiveQty > 0)
                        //{
                        //    RealQty = orderEffectiveQty - snapEffectiveQty;
                        //    calType = (int)CalType.Reduce;
                        //}
                        //else if (snapEffectiveQty - orderEffectiveQty > 0)
                        //{
                        //    RealQty = snapEffectiveQty - orderEffectiveQty;
                        //    calType = (int)CalType.Add;
                        //}

                        var obj = new DelistingDataModel()
                        {
                            formId = sourceForm.Id,
                            formName = sourceForm.Caption,
                            Material = entryItem["fmaterialid_ref"] as DynamicObject,
                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                            Qty = bizQty,
                            RealQty = calType == 1 ? orderEffectiveQty - snapEffectiveQty : snapEffectiveQty - orderEffectiveQty,
                            calType = calType,
                            opCode = opCode
                        };
                        obj.snapbizQty = snapBizQty;
                        obj.orderbizQty = orderBizQty;
                        obj.snapPurQty = Convert.ToDecimal(beforeEntryItem["fhqpurqty"]);
                        obj.orderPurQty = Convert.ToDecimal(afterEntryItem["fhqpurqty"]);
                        obj.snapOutQty = Convert.ToDecimal(beforeEntryItem["fbizoutqty"]);
                        obj.orderOutQty = Convert.ToDecimal(afterEntryItem["fbizoutqty"]);
                        obj.snapReturnQty = Convert.ToDecimal(beforeEntryItem["fbizreturnqty"]);
                        obj.orderReturnQty = Convert.ToDecimal(afterEntryItem["fbizreturnqty"]);
                        delistindDataModels.Add(obj);
                    }
                }
            }
            return delistindDataModels;
        }

        #endregion


        /// <summary>
        /// 更新退市产品相关数量
        /// </summary>
        /// <param name="ctx">经销商上下文</param>
        /// <param name="billFormId">来源单据ID</param>
        /// <param name="allEntry">所有业务单据明细行</param>
        /// <param name="allProductIds">所有要更新的商品ID</param>
        /// <param name="allSelTypeIds">所有要更新的商品型号ID</param>
        /// <param name="allMatchIds">匹配的退市数据</param>
        /// <param name="calType">计算类型 1:加 2:减（销售合同不存在，销售合同每次重算</param>
        public static void UpdateDelistingQtyNew(UserContext ctx, string billFormId, List<DelistingDataModel> changeDataModel, List<string> allMatchIds, int calType = 0)
        {
            if (changeDataModel == null || changeDataModel.Count == 0) return;
            //ProductDelistingLogger logger = new ProductDelistingLogger(billFormId);
            //logger.AppendLine("------------记录入参------------");
            //logger.AppendLine("【billFormId】:" + billFormId);
            //logger.AppendLine("【allEntry】:" + JsonConvert.SerializeObject(allEntry));
            //logger.AppendLine("【allProductIds】:" + JsonConvert.SerializeObject(allProductIds));
            //logger.AppendLine("【allSelTypeIds】:" + JsonConvert.SerializeObject(allSelTypeIds));
            //logger.AppendLine("【calType】:" + calType);
            //logger.AppendLine("------------------------");
            var productFieldId = "fmaterialid";
            if (billFormId.EqualsIgnoreCase("ydj_order"))
            {
                productFieldId = "fproductid";
            }
            var topCtx = ctx.CreateTopOrgDBContext();
            var products = GetDelistingProducts(topCtx);

            //使用商品内码查询所有匹配的退市清单
            //var productMatch = GetDelistingProductIdList(topCtx, "fmaterialid", allProductIds);
            ////使用型号内码查询所有匹配的退市清单
            //var selTypeMatch = GetDelistingProductIdList(topCtx, "fseltypeid", allSelTypeIds);

            ////加载所有匹配到的退市清单
            //var allMatchIds = new List<string>();
            //if (productMatch != null && productMatch.Any()) allMatchIds.AddRange(productMatch);
            //if (selTypeMatch != null && selTypeMatch.Any()) allMatchIds.AddRange(selTypeMatch);
            //if (allMatchIds.Count == 0) return;

            var allDelist = topCtx.LoadBizDataById(formId, allMatchIds, true);
            if (allDelist == null || !allDelist.Any()) return;
            changeDataModel = changeDataModel.OrderBy(a => a.Material["fnumber"]).ToList();

            //取出辅助属性明细
            Dictionary<string, List<Tuple<string, string>>> dicAttrInfo = new Dictionary<string, List<Tuple<string, string>>>();
            var allAttrIds = changeDataModel.Select(a => a.Attrinfo).Distinct().ToList();
            if (allAttrIds != null && allAttrIds.Any())
            {
                dicAttrInfo = GetAttrInfo(topCtx, allAttrIds);
            }
            var mate = topCtx.Container.GetService<IMetaModelService>();
            var delistingForm = mate.LoadFormModel(topCtx, formId);
            var refObjMgr = topCtx.Container.GetService<LoadReferenceObjectManager>();
            //var getway = topCtx.Container.GetService<IHttpServiceInvoker>();

            //var recordForm = mate.LoadFormModel(topCtx, "ydj_delistingqtyrecord");

            // 加载数据
            //refObjMgr?.Load(topCtx, allDelist.ToArray(), true, delistingForm, new List<string> { "fmaterialid" });

            List<DynamicObject> saveDelist = new List<DynamicObject>();
            //List<DynamicObject> recordDatas = new List<DynamicObject>();
            //List<Dictionary<string, string>> keyValues = new List<Dictionary<string, string>>();
            var productDelistings = new List<DynamicObject>();
            //List<string> delRecordIds = new List<string>();
            var nowTime = DateTime.Now;//统一更新时间

            List<DynamicObject> matchDelist = new List<DynamicObject>();
            #region 查出来匹配上的退市清单以及关联退市清单
            foreach (var entry in changeDataModel)
            {
                List<DynamicObject> _matchDelist = new List<DynamicObject>();
                var currProductObj = entry.Material;
                var currProductId = Convert.ToString(currProductObj["id"]);
                var currSelTypeId = Convert.ToString(currProductObj["fseltypeid"]);
                var currAttrInfoId = entry.Attrinfo;

                //49683 【慕思UAT-退市产品提醒需求】按【型号】退市，销售合同调整销售数量后：①调整无关的标准品触发了更新逻辑。②调整对应定制品，未触发更新逻辑（点<初始数据更新重试>无问题）
                //判断商品是否标准品
                if (!Convert.ToBoolean(currProductObj["fispresetprop"])//允许选配
                    && !Convert.ToBoolean(currProductObj["fcustom"])//允许定制
                    && !Convert.ToBoolean(currProductObj["funstdtype"]))//非标产品
                {
                    //标品用商品判断，用类型判断会导致放大范围
                    _matchDelist = allDelist.Where(t =>
                    !Convert.ToString(t["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()
                    && Convert.ToString(t["fmaterialid"]).EqualsIgnoreCase(currProductId)
                    && !Convert.ToBoolean(t["ftype"])
                    ).ToList();
                    if ((_matchDelist == null || !_matchDelist.Any()) && !currSelTypeId.IsNullOrEmptyOrWhiteSpace())
                    {
                        //当商品找不到，则用型号找
                        //找的条件：型号相同，退市清单是定制
                        _matchDelist = allDelist
                            .Where(a => Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(currProductObj["fseltypeid"]))
                            && Convert.ToBoolean(a["ftype"])).ToList();
                    }
                }
                else
                {
                    //非标商品按照型号+辅助属性匹配判断
                    if (!currSelTypeId.IsNullOrEmptyOrWhiteSpace() && !currAttrInfoId.IsNullOrEmptyOrWhiteSpace())
                    {
                        _matchDelist = allDelist.Where(t =>
                        !Convert.ToString(t["fseltypeid"]).IsNullOrEmptyOrWhiteSpace()
                        && Convert.ToString(t["fseltypeid"]).EqualsIgnoreCase(currSelTypeId)
                        && Convert.ToBoolean(t["ftype"])
                        ).ToList();
                        if (_matchDelist != null && _matchDelist.Any())
                        {
                            List<Tuple<string, string>> attrDetail = new List<Tuple<string, string>>();
                            //匹配辅助属性
                            dicAttrInfo.TryGetValue(currAttrInfoId, out attrDetail);
                            var matchAttrList = new List<DynamicObject>();
                            var colorName = "";
                            foreach (var match in _matchDelist)
                            {
                                var propEntry = match["fpropentry"] as DynamicObjectCollection;
                                if (propEntry == null || !propEntry.Any())
                                {
                                    //定制品，没有设属性，匹配到了也算
                                    matchAttrList.Add(match);
                                    continue;
                                }
                                var matchPropEntry = propEntry.Where(t => attrDetail.Any(d => d.Item2.EqualsIgnoreCase(Convert.ToString(t["fpropvalueid"]))));
                                if (matchPropEntry != null && matchPropEntry.Any())
                                {
                                    var propValueObj = matchPropEntry.FirstOrDefault()["fpropvalueid_ref"] as DynamicObject;
                                    colorName = Convert.ToString(propValueObj["fname"]);
                                    matchAttrList.Add(match);
                                }
                            }
                            var _result = new List<DynamicObject>();
                            foreach (var item in matchAttrList)
                            {
                                var _product = (item["fmaterialid_ref"] as DynamicObject);
                                if (_product != null)
                                {
                                    //只查定制
                                    if (Convert.ToBoolean(_product["fispresetprop"])//允许选配
                                       || Convert.ToBoolean(_product["fcustom"])//允许定制
                                       || Convert.ToBoolean(_product["funstdtype"]))//非标产品
                                    {
                                        _result.Add(item);
                                        continue;
                                    }
                                }
                            }
                            _matchDelist = _result;
                        }
                    }
                }
                if (_matchDelist == null || !_matchDelist.Any()) continue;
                matchDelist.AddRange(_matchDelist);
            }
            if (matchDelist == null || !matchDelist.Any()) return;
            matchDelist = matchDelist.Distinct().ToList();
            var linkProductDelistings = ProductDelistingHelper.GetProductDelistingBySpecs(topCtx, matchDelist).ToList();
            refObjMgr?.Load(topCtx, linkProductDelistings.ToArray(), true, delistingForm, new List<string> { "fmaterialid" });
            #endregion
            #region 查询退市清单对应的销售合同数据,一次性查出，不做循环
            //是否是标准品
            bool isstandtype = false;
            bool onebyone = false;
            List<string> productIds = new List<string>();
            List<string> seltypeIds = new List<string>();
            List<Tuple<string, string>> propTuples = new List<Tuple<string, string>>();
            List<DynamicObject> salOrderDetails = new List<DynamicObject>();
            foreach (var delistObj in matchDelist)
            {
                isstandtype = !Convert.ToBoolean(delistObj["ftype"]);

                productDelistings = linkProductDelistings.Where(a =>
                Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                if (isstandtype)
                {
                    //当前是标准品，查定制品的退市清单，包含两种
                    /*
                     * 专用材料能匹配上，直接加进来
                     * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
                     */
                    //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
                    var _dynamics1 = linkProductDelistings.Where(a =>
                        Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
                        !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                        Convert.ToBoolean(a["ftype"]) &&
                        !(a["fpropentry"] as DynamicObjectCollection).Any() &&
                        !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                    if (_dynamics1.Count > 0)
                    {
                        //存在定制，需要把关联的标品的也带出来算
                        _dynamics1 = linkProductDelistings.Where(a =>
                        Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
                        !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                        !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                    }
                    productDelistings.AddRange(_dynamics1);
                }
                else
                {
                    //当前是定制品，查定制品的退市清单，包含两种
                    /*
                     * 专用材料能匹配上，直接加进来
                     * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
                     */
                    //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
                    var _dynamics1 = linkProductDelistings.Where(a =>
                        Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
                        !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                        !Convert.ToBoolean(a["ftype"]) &&
                        !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                    productDelistings.AddRange(_dynamics1);

                }
                var _bzp = new List<DynamicObject>();
                var _dzp = new List<DynamicObject>();

                //如果是标准品，存在其他标准品也是一样的专用材料
                productDelistings.ForEach(a =>
                {
                    if (!Convert.ToBoolean(a["ftype"]))
                        _bzp.Add(a);
                    else
                        _dzp.Add(a);
                });
                if (isstandtype)
                    _bzp.Add(delistObj);
                else
                    _dzp.Add(delistObj);
                foreach (var bzpItem in _bzp)
                {
                    var productId = Convert.ToString(bzpItem["fmaterialid"]);
                    var seltypeId = Convert.ToString(bzpItem["fseltypeid"]);
                    productIds.Add(productId);
                    seltypeIds.Add(seltypeId);
                }
                foreach (var dzpItem in _dzp)
                {
                    var productId = Convert.ToString(dzpItem["fmaterialid"]);
                    var seltypeId = Convert.ToString(dzpItem["fseltypeid"]);
                    productIds.Add(productId);
                    seltypeIds.Add(seltypeId);
                    var propEntry = dzpItem["fpropentry"] as DynamicObjectCollection;
                    if (propEntry != null && propEntry.Any())
                    {
                        foreach (var prop in propEntry)
                        {
                            var propId = Convert.ToString(prop["fpropid"]);
                            var propValId = Convert.ToString(prop["fpropvalueid"]);
                            propTuples.Add(new Tuple<string, string>(propId, propValId));
                        }
                    }
                }
            }

            //if (billFormId.Equals("ydj_order"))
            //{
            //    salOrderDetails = GetOrderDetails(topCtx, productIds, seltypeIds, propTuples, "ydj_order", ref keyValues);
            //}
            #endregion
            #region 循环明细行，查询并更新每个明细行对应的退市清单更新情况
            foreach (var entry in changeDataModel)
            {
                List<DynamicObject> _matchDelist = new List<DynamicObject>();
                var currProductObj = entry.Material;
                var currProductId = Convert.ToString(currProductObj["id"]);
                var currSelTypeId = Convert.ToString(currProductObj["fseltypeid"]);
                var currAttrInfoId = entry.Attrinfo;

                matchDelist = new List<DynamicObject>();
                //49683 【慕思UAT-退市产品提醒需求】按【型号】退市，销售合同调整销售数量后：①调整无关的标准品触发了更新逻辑。②调整对应定制品，未触发更新逻辑（点<初始数据更新重试>无问题）
                //判断商品是否标准品
                if (!Convert.ToBoolean(currProductObj["fispresetprop"])//允许选配
                    && !Convert.ToBoolean(currProductObj["fcustom"])//允许定制
                    && !Convert.ToBoolean(currProductObj["funstdtype"]))//非标产品
                {
                    //标品用商品判断，用类型判断会导致放大范围
                    matchDelist = allDelist.Where(t =>
                    !Convert.ToString(t["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()
                    && Convert.ToString(t["fmaterialid"]).EqualsIgnoreCase(currProductId)
                    && !Convert.ToBoolean(t["ftype"])
                    ).ToList();
                    if ((matchDelist == null || !matchDelist.Any()) && !currSelTypeId.IsNullOrEmptyOrWhiteSpace())
                    {
                        //当商品找不到，则用型号找
                        //找的条件：型号相同，退市清单是定制
                        matchDelist = allDelist
                            .Where(a => Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(currProductObj["fseltypeid"]))
                            && Convert.ToBoolean(a["ftype"])).ToList();
                    }
                }
                else
                {
                    //非标商品按照型号+辅助属性匹配判断&& !currAttrInfoId.IsNullOrEmptyOrWhiteSpace()
                    if (!currSelTypeId.IsNullOrEmptyOrWhiteSpace())
                    {
                        matchDelist = allDelist.Where(t =>
                        !Convert.ToString(t["fseltypeid"]).IsNullOrEmptyOrWhiteSpace()
                        && Convert.ToString(t["fseltypeid"]).EqualsIgnoreCase(currSelTypeId)
                        && Convert.ToBoolean(t["ftype"])
                        ).ToList();
                        if (matchDelist != null && matchDelist.Any())
                        {
                            //匹配辅助属性
                            List<Tuple<string, string>> attrDetail = new List<Tuple<string, string>>();
                            dicAttrInfo.TryGetValue(currAttrInfoId, out attrDetail);
                            var matchAttrList = new List<DynamicObject>();
                            var colorName = "";
                            foreach (var match in matchDelist)
                            {
                                var propEntry = match["fpropentry"] as DynamicObjectCollection;
                                if (propEntry == null || !propEntry.Any())
                                {
                                    //定制品，没有设属性，匹配到了也算
                                    matchAttrList.Add(match);
                                    continue;
                                }
                                var matchPropEntry = propEntry.Where(t => attrDetail.Any(d => d.Item2.EqualsIgnoreCase(Convert.ToString(t["fpropvalueid"]))));
                                if (matchPropEntry != null && matchPropEntry.Any())
                                {
                                    var propValueObj = matchPropEntry.FirstOrDefault()["fpropvalueid_ref"] as DynamicObject;
                                    colorName = Convert.ToString(propValueObj["fname"]);
                                    matchAttrList.Add(match);
                                }
                            }
                            var _result = new List<DynamicObject>();
                            foreach (var item in matchAttrList)
                            {
                                var _product = (item["fmaterialid_ref"] as DynamicObject);
                                if (_product != null)
                                {
                                    //只查定制
                                    if (Convert.ToBoolean(_product["fispresetprop"])//允许选配
                                       || Convert.ToBoolean(_product["fcustom"])//允许定制
                                       || Convert.ToBoolean(_product["funstdtype"]))//非标产品
                                    {
                                        _result.Add(item);
                                        continue;
                                    }
                                }
                            }
                            matchDelist = _result;
                        }
                    }
                }
                if (matchDelist == null || !matchDelist.Any()) continue;

                foreach (var delistObj in matchDelist)
                {
                    isstandtype = !Convert.ToBoolean(delistObj["ftype"]);
                    //var delistObj = matchDelist.FirstOrDefault();
                    //logger.AppendLine(delistObj, "计算前退市清单【delistObj】:");

                    productDelistings = linkProductDelistings.Where(a =>
                        Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                        !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                    if (isstandtype)
                    {
                        //当前是标准品，查定制品的退市清单，包含两种
                        /*
                         * 专用材料能匹配上，直接加进来
                         * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
                         */
                        //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
                        var _dynamics1 = linkProductDelistings.Where(a =>
                            Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
                            !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                            Convert.ToBoolean(a["ftype"]) &&
                            !(a["fpropentry"] as DynamicObjectCollection).Any() &&
                            !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                        if (_dynamics1.Count > 0)
                        {
                            //存在定制，需要把关联的标品的也带出来算
                            _dynamics1 = linkProductDelistings.Where(a =>
                            Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
                            !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                            !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                        }
                        productDelistings.AddRange(_dynamics1);
                    }
                    else
                    {
                        //当前是定制品，查定制品的退市清单，包含两种
                        /*
                         * 专用材料能匹配上，直接加进来
                         * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
                         */
                        //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
                        var _dynamics1 = linkProductDelistings.Where(a =>
                            Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
                            !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
                            !Convert.ToBoolean(a["ftype"]) &&
                            !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
                        productDelistings.AddRange(_dynamics1);

                    }
                    var _bzp = new List<DynamicObject>();
                    var _dzp = new List<DynamicObject>();

                    //如果是标准品，存在其他标准品也是一样的专用材料
                    productDelistings.ForEach(a =>
                    {
                        if (!Convert.ToBoolean(a["ftype"]))
                            _bzp.Add(a);
                        else
                            _dzp.Add(a);
                    });
                    if (isstandtype)
                        _bzp.Add(delistObj);
                    else
                        _dzp.Add(delistObj);

                    //计算【累计采购已下单数量】
                    var fsumpurorderqty = 0M;
                    //【占用型号累计采购已下单数量】
                    var fmatoccupysumpurqty = 0M;
                    //【可使用型号采购可下单数量】
                    var fsumpurqty = 0M;
                    //【库存占用累计采购已下单数量】
                    var finvoccupysumpurqty = 0M;
                    ///【库存占用累计销售已下单数量】
                    var finvoccupysumsalqty = 0M;
                    ///【销售可下单数量】
                    var fsalcanplaceorderqty = 0M;
                    ///【采购可下单数量】
                    var fpurcanplaceorderqty = 0M;
                    //计算【累计销售已下单数量】
                    var fsumsalorderqty = 0M;
                    var fsumsalorderqtyreal = 0M;
                    decimal exceedQty = 0;//超出数量
                    foreach (var bzpItem in _bzp)
                    {
                        var productId = Convert.ToString(bzpItem["fmaterialid"]);
                        var seltypeId = Convert.ToString(bzpItem["fseltypeid"]);

                        var _entry = (bzpItem["fentity"] as DynamicObjectCollection)
                        .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
                        if (!Convert.ToString(bzpItem["id"]).Equals(Convert.ToString(delistObj["id"])))
                        {
                            //其它退市清单，只做记录，不做更新
                            exceedQty += Convert.ToDecimal(_entry["fmatoccupysumpurqty"]);
                            continue;
                        }

                        var festimateqty = Convert.ToDecimal(_entry["festimateqty"]);//预估可采购数
                        fsumpurorderqty = Convert.ToDecimal(_entry["fsumpurorderqty"]);//累计采购数
                        fsumsalorderqty = Convert.ToDecimal(_entry["fsumsalorderqty"]);
                        fsumsalorderqtyreal = Convert.ToDecimal(_entry["fsumsalorderqtyreal"]);
                        if (billFormId.Equals("ydj_order"))
                        {
                            if (entry.calType == 1)
                            {
                                fsumsalorderqty = fsumsalorderqty + entry.Qty;
                                fsumsalorderqtyreal = fsumsalorderqtyreal + entry.RealQty;
                            }
                            else
                            {
                                fsumsalorderqty = fsumsalorderqty - entry.Qty;
                                fsumsalorderqtyreal = fsumsalorderqtyreal - entry.RealQty;
                            }
                        }
                        else if (billFormId.Equals("ydj_purchaseorder"))
                        {
                            if (entry.calType == 1)
                            {
                                fsumsalorderqty = fsumsalorderqty + entry.RealQty;
                                fsumpurorderqty = fsumpurorderqty + entry.Qty;
                            }
                            else
                            {
                                fsumsalorderqty = fsumsalorderqty - entry.RealQty;
                                fsumpurorderqty = fsumpurorderqty - entry.Qty;
                            }
                        }

                        decimal dzkylqty = 0;//定制可用量，也叫【可使用型号采购可下单数量】
                        dzkylqty = _dzp.Select(a => (a["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).Select(c => Convert.ToDecimal(c["festimateqty"])).Sum()).Sum();
                        //可用总量=成品可用量+定制可用量（300
                        decimal kysumqty = dzkylqty + festimateqty;
                        //下发数量>=累计采购已下单数量。则不需要占用定制
                        if (festimateqty >= fsumpurorderqty)
                        {
                            fmatoccupysumpurqty = 0;
                            fsumpurqty = dzkylqty;
                        }
                        else if (fsumpurorderqty > kysumqty)//累计采购已下单数量>可下单总量，爆单了
                        {
                            //若下发一百，可用总量300，查出来使用360，则占用200，剩下的可下单就0
                            //fmatoccupysumpurqty = dzkylqty == 0 ? fsumpurorderqty - festimateqty : dzkylqty;//全占用，剩下的都在自己这里体现
                            fmatoccupysumpurqty = dzkylqty == 0 ? dzkylqty : fsumpurorderqty - festimateqty;//全占用，剩下的都在自己这里体现
                            fsumpurqty = 0;
                            dzkylqty = 0;
                        }
                        else
                        {
                            //若下发一百，查出来使用160，则占用60
                            fmatoccupysumpurqty = fsumpurorderqty - festimateqty;//占用1个
                            fsumpurqty = kysumqty - fsumpurorderqty;//【可使用型号采购可下单数量】=总量（300）-采购已下单（160）=140
                            dzkylqty = kysumqty - fsumpurorderqty;
                        }

                        _entry["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
                        _entry["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量(过度计算值）
                        _entry["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
                        _entry["fpurqtyupdatetime"] = DateTime.Now;//采购可下单数量更新时间
                        _entry["fsalqtyupdatetime"] = DateTime.Now;//销售可下单数量更新时间
                        _entry["fenable"] = 1;//是否有效行
                        _entry["fmodifystatus"] = "2";//初始更新情况
                        _entry["fmatoccupysumsalqty"] = 0;//占用型号累计销售已下单数量
                        _entry["finvoccupysumsalqty"] = finvoccupysumsalqty;//库存占用累计销售已下单数量（0
                        _entry["fmatoccupysumpurqty"] = fmatoccupysumpurqty;//占用型号累计采购已下单数量
                        var _linkObj = linkProductDelistings.Where(_ => Convert.ToString(_["id"]).Equals(Convert.ToString(bzpItem["id"]))).FirstOrDefault();
                        if (_linkObj != null)
                        {
                            (_linkObj["fentity"] as DynamicObjectCollection).ForEach(c =>
                            {
                                if (Convert.ToString(c["id"]).Equals(Convert.ToString(_entry["id"])))
                                {
                                    c["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
                                    c["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
                                    c["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
                                    c["finvoccupysumsalqty"] = finvoccupysumsalqty;//库存占用累计销售已下单数量
                                    c["fmatoccupysumpurqty"] = fmatoccupysumpurqty;//占用型号累计采购已下单数量
                                }
                            });
                        }
                        exceedQty += fmatoccupysumpurqty;
                        continue;
                    }

                    foreach (var dzpItem in _dzp)
                    {
                        var _entry = (dzpItem["fentity"] as DynamicObjectCollection)
                        .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
                        var _propentry = (dzpItem["fpropentry"] as DynamicObjectCollection)
                        .FirstOrDefault();
                        var propEntrys = (dzpItem["fpropentry"] as DynamicObjectCollection).Select(a => Convert.ToString(a["fpropvalueid"])).ToList();
                        var productId = Convert.ToString(dzpItem["fmaterialid"]);
                        var seltypeId = Convert.ToString(dzpItem["fseltypeid"]);

                        var festimateqty = Convert.ToDecimal(_entry["festimateqty"]);//预估可下单采购量
                        fsumpurorderqty = Convert.ToDecimal(_entry["fsumpurorderqty"]);//累计采购数
                        fsumsalorderqty = Convert.ToDecimal(_entry["fsumsalorderqty"]);
                        fsumsalorderqtyreal = Convert.ToDecimal(_entry["fsumsalorderqtyreal"]);
                        if (Convert.ToString(dzpItem["id"]).Equals(Convert.ToString(delistObj["id"])))
                        {
                            if (billFormId.Equals("ydj_order"))
                            {
                                if (entry.calType == 1)
                                {
                                    fsumsalorderqty = fsumsalorderqty + entry.Qty;
                                    fsumsalorderqtyreal = fsumsalorderqtyreal + entry.RealQty;
                                }
                                else
                                {
                                    fsumsalorderqty = fsumsalorderqty + -entry.Qty;
                                    fsumsalorderqtyreal = fsumsalorderqtyreal - entry.RealQty;
                                }
                            }
                            else if (billFormId.Equals("ydj_purchaseorder"))
                            {
                                if (entry.calType == 1)
                                {
                                    fsumsalorderqty = fsumsalorderqty + entry.RealQty;
                                    fsumpurorderqty = fsumpurorderqty + entry.Qty;
                                }
                                else
                                {
                                    fsumsalorderqty = fsumsalorderqty - entry.RealQty;
                                    fsumpurorderqty = fsumpurorderqty - entry.Qty;
                                }
                            }

                            _entry["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
                            _entry["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
                            _entry["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
                            _entry["fpurqtyupdatetime"] = DateTime.Now;//采购可下单数量更新时间
                            _entry["fsalqtyupdatetime"] = DateTime.Now;//销售可下单数量更新时间
                            _entry["fenable"] = 1;//是否有效行
                            _entry["fmodifystatus"] = "2";//初始更新情况
                            _entry["fmatoccupysumsalqty"] = 0;//占用型号累计销售已下单数量
                        }


                        _entry["finvoccupysumpurqty"] = exceedQty;//库存占用累计销售已下单数量（0
                                                                  //【采购可下单数量】=【预估可下单采购数量】-【累计采购已下单数量】+【占用型号累计采购已下单数量】+【可使用型号采购可下单数量】-【库存占用累计采购已下单数量】
                        fpurcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + Convert.ToDecimal(_entry["fsumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumpurqty"]);//(200)
                                                                                                                                                                                                                                                                                        //计算规则：【销售可下单数量】=【预估采购可下单数量】(100-【累计销售已下单数量】(78-【累计采购已下单数量】(201+【占用型号累计采购已下单数量】(1+【可使用型号采购可下单数量】(-【库存占用累计销售已下单数量】
                        fsalcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumsalorderqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + Convert.ToDecimal(_entry["fsumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumsalqty"]);//(200);
                        _entry["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
                        _entry["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
                        var _linkObj = linkProductDelistings.Where(_ => Convert.ToString(_["id"]).Equals(Convert.ToString(dzpItem["id"]))).FirstOrDefault();
                        if (_linkObj != null)
                        {
                            (_linkObj["fentity"] as DynamicObjectCollection).ForEach(c =>
                            {
                                if (Convert.ToString(c["id"]).Equals(Convert.ToString(_entry["id"])))
                                {
                                    c["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
                                    c["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
                                    c["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
                                    c["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
                                    c["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
                                    c["finvoccupysumpurqty"] = exceedQty;//库存占用累计销售已下单数量（0
                                }
                            });
                        }
                    }

                    foreach (var bzpItem in _bzp)
                    {
                        decimal _fpurcanplaceorderqty = 0;
                        if (_dzp.Count > 0)
                        {
                            var _dzpEntry = (_dzp.FirstOrDefault()?["fentity"] as DynamicObjectCollection)
                         .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
                            _fpurcanplaceorderqty = Convert.ToDecimal(_dzpEntry["fpurcanplaceorderqty"]);
                        }
                        var _entry = (bzpItem["fentity"] as DynamicObjectCollection)
                        .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();

                        _entry["fsumpurqty"] = _fpurcanplaceorderqty;//可使用型号采购可下单数量

                        //【采购可下单数量】=【预估可下单采购数量】-【累计采购已下单数量】+【占用型号累计采购已下单数量】+【可使用型号采购可下单数量】-【库存占用累计采购已下单数量】
                        fpurcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + _fpurcanplaceorderqty - Convert.ToDecimal(_entry["finvoccupysumpurqty"]);//(200)
                                                                                                                                                                                                                                                                      //计算规则：【销售可下单数量】=【预估采购可下单数量】(100-【累计销售已下单数量】(78-【累计采购已下单数量】(201+【占用型号累计采购已下单数量】(1+【可使用型号采购可下单数量】(-【库存占用累计销售已下单数量】
                        fsalcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumsalorderqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + _fpurcanplaceorderqty - Convert.ToDecimal(_entry["finvoccupysumsalqty"]);//(200);
                        _entry["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
                        _entry["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
                    }

                    foreach (var _bzpItem in _bzp)
                    {
                        var _delistDataItem = saveDelist.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(_bzpItem["id"]))).FirstOrDefault();
                        saveDelist.Remove(_delistDataItem);
                    }
                    foreach (var _bzpItem in _dzp)
                    {
                        var _delistDataItem = saveDelist.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(_bzpItem["id"]))).FirstOrDefault();
                        saveDelist.Remove(_delistDataItem);
                    }
                    //saveDelist.AddRange(_bzp);
                    //saveDelist.AddRange(_dzp);

                    saveDelist.AddRange(_bzp);
                    saveDelist.AddRange(_dzp);
                    saveDelist = saveDelist.Distinct().ToList();
                    //先保存下来,若下发的这个数据包中，就有互相关联的退市清单，会互相影响
                    //var _sale = getway.InvokeBillOperation(topCtx, formId, saveDelist, "save",
                    //            new Dictionary<string, object>() {
                    //    { "IgnoreCheckPermssion","true" }//去掉权限校验
                    //            });
                    //topCtx.SaveBizData(formId, saveDelist);

                    //foreach (var _bzpItem in _bzp)
                    //{
                    //    var _delistDataItem = saveDelist.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(_bzpItem["id"]))).FirstOrDefault();
                    //    saveDelist.Remove(_delistDataItem);
                    //}
                    //foreach (var _bzpItem in _dzp)
                    //{
                    //    var _delistDataItem = saveDelist.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(_bzpItem["id"]))).FirstOrDefault();
                    //    saveDelist.Remove(_delistDataItem);
                    //}
                    //saveDelist.AddRange(_bzp);
                    //saveDelist.AddRange(_dzp);
                }
            }
            #endregion

            if (saveDelist.Any())
            {
                //logger.AppendLine(saveDelist, "计算后（含相关联退市清单）----【saveDelist】:");
                topCtx.SaveBizData(formId, saveDelist);
            }
            //logger.Write();
        }


        /// <summary>
        /// 获取下市产品内码列表
        /// 对比使用事务前后的效率（循环
        /// 不做动态，直接取表名
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="formId">表单ID</param>
        /// <param name="fieldKey">字段名</param>
        /// <param name="fieldValues">字段值</param>
        /// <returns></returns>
        public static List<string> GetDelistingProductIdList(UserContext userCtx, string fieldKey, List<string> fieldValues)
        {
            if (fieldKey.IsNullOrEmptyOrWhiteSpace() || fieldValues == null || !fieldValues.Any())
            {
                return null;
            }
            var topCtx = userCtx.CreateTopOrgDBContext();
            var dbSvc = topCtx.Container.GetService<IDBService>();

            List<string> result = new List<string>();
            using (var tran = topCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var sqlWhere = "";
                var tmpTblName = "";
                if (fieldValues.Count == 1)
                {
                    sqlWhere += $" WHERE PDL.{fieldKey} = '{fieldValues.FirstOrDefault()}' ";
                }
                else if (fieldValues.Count <= 50)
                {
                    sqlWhere += $" WHERE PDL.{fieldKey} IN ({fieldValues.JoinEx(",", true)}) ";
                }
                else
                {
                    tmpTblName = dbSvc.CreateTempTableWithDataList(topCtx, fieldValues, false);
                    sqlWhere += $" INNER JOIN {tmpTblName} AS TMP ON PDL.{fieldKey}=TMP.fid ";
                }

                var sql = $@"SELECT PDL.fid FROM t_ydj_productdelisting AS PDL WITH(NOLOCK) {sqlWhere}";
                var delistObjs = dbSvc.ExecuteDynamicObject(topCtx, sql);
                if (delistObjs != null && delistObjs.Any())
                {
                    result = delistObjs.Select(t => Convert.ToString(t["fid"])).ToList();
                }

                if (!tmpTblName.IsNullOrEmptyOrWhiteSpace())
                {
                    dbSvc.DeleteTempTableByName(topCtx, tmpTblName, true);
                }

                tran.Complete();
            }

            return result;
        }

        public static List<string> GetDelistingProducts(UserContext userCtx)
        {
            var objs = userCtx.LoadBizDataByFilter(formId, $" fmaterialid<>''", "fmaterialid", null);
            if (objs != null && objs.Any()) return objs.Select(a => Convert.ToString(a["fmaterialid"])).ToList();
            return new List<string>();
        }

        /// <summary>
        /// 批量获取
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productId"></param>
        /// <param name="selTypeId"></param>
        /// <param name="propTups"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public static List<DynamicObject> GetOrderDetails(UserContext userCtx, List<string> productIds, List<string> selTypeIds, List<Tuple<string, string>> propTups, string formId, ref List<Dictionary<string, string>> propMaps)
        {
            if (productIds.Count == 0 && selTypeIds.Count == 0) return new List<DynamicObject>();

            //创建总部视角上下文
            var topCtx = userCtx.CreateTopOrgDBContext();
            var slaveCtx = topCtx.CreateSlaveDBContext();
            var slaveDBSvc = slaveCtx.Container.GetService<IDBService>();
            var slaveDBSvcEx = slaveCtx.Container.GetService<IDBServiceEx>();
            var dbSvc = userCtx.Container.GetService<IDBService>();

            var allProductIds = new List<string>();
            allProductIds.AddRange(productIds);

            //如果型号传了值，则查询出该型号下的所有商品
            if (selTypeIds.Count > 0)
            {
                var productObjs = topCtx.LoadBizDataByNo("ydj_product", $"fseltypeid", selTypeIds);
                if (productObjs != null && productObjs.Any())
                {
                    allProductIds.AddRange(productObjs.Select(t => Convert.ToString(t["id"])));
                }
            }
            var allAttrInfoIds = new List<string>();
            if (propTups != null && propTups.Any())
            {
                //根据属性组合查询所有包含的辅助属性内码
                var propFilter = new List<string>();
                foreach (var tup in propTups)
                {
                    //propFilter.Add($"(fauxpropid='{tup.Item1}' AND fvalueid='{tup.Item2}')");
                    //propFilter.Add($"( fvalueid='{tup.Item2}')");
                    propFilter.Add($"( fvalueid='{tup.Item2}')");
                }
                var propSql = $"select distinct b.fattrinfo_e,a.fvalueid from T_BD_AUXPROPVALUEENTRY a with(nolock) inner join T_BD_AUXPROPVALUE b with(nolock) on a.fid=b.fid where a.fvalueid in  ('{string.Join("','", propTups.Select(a => a.Item2).ToList())}')";
                var propObjs = dbSvc.ExecuteDynamicObject(topCtx, propSql);
                if (propObjs != null && propObjs.Any())
                {
                    foreach (var propObjItem in propObjs)
                    {
                        Dictionary<string, string> keyValueItem = new Dictionary<string, string>();
                        keyValueItem.Add(Convert.ToString(propObjItem["fvalueid"]), Convert.ToString(propObjItem["fattrinfo_e"]));
                        propMaps.Add(keyValueItem);
                    }
                    allAttrInfoIds = propObjs.Select(t => Convert.ToString(t["fattrinfo_e"])).Where(a => !string.IsNullOrWhiteSpace(a)).Distinct().ToList();
                }
            }
            //全量测试不参与计算
            var noCheckDelisting = topCtx.LoadBizDataByFilter("bas_agent", $"fnotcountdelistling=1", "fid");
            List<string> noCheckDelistingIds = noCheckDelisting.Select(a => Convert.ToString(a["fbillhead_id"])).ToList();

            //SQL查询相关，默认用销售合同的
            var orderEntryTblName = "T_YDJ_ORDERENTRY";//明细表名
            var productFieldKey = "fproductid";//商品字段ID
            var qtyFieldKeys = "fbizqty,fbizoutqty,fbizreturnqty";//要查询的数量字段ID
            if (formId.EqualsIgnoreCase("ydj_purchaseorder"))
            {
                orderEntryTblName = "T_YDJ_POORDERENTRY";
                productFieldKey = "fmaterialid";
                qtyFieldKeys = "fbizqty";
            }
            string where = "";
            string tmpProductIdTbl = "";
            var materialTableJoin = "";
            allProductIds = allProductIds.Where(a => !string.IsNullOrWhiteSpace(a)).ToList();

            if (allProductIds.Count == 0 && allAttrInfoIds.Count == 0) return null;
            List<DynamicObject> orderEntryObjs = new List<DynamicObject>();
            if (formId.Equals("ydj_order"))
            {
                //销售合同查从库，采购查主库
                topCtx = topCtx.CreateSlaveDBContext();
            }
            else
            {
                topCtx = userCtx.CreateTopOrgDBContext();
            }
            using (var tran = topCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                List<string> whereSql = new List<string>();
                if (allProductIds.Any())
                {
                    if (allProductIds.Count == 1)
                    {
                        whereSql.Add($" {productFieldKey} = '{allProductIds.FirstOrDefault()}' ");
                        //where += $" ( {productFieldKey} = '{allProductIds.FirstOrDefault()}' ";
                    }
                    else if (allProductIds.Count <= 50)
                    {
                        whereSql.Add($"  {productFieldKey} IN ({allProductIds.JoinEx(",", true)}) ");
                        //where += $" ( {productFieldKey} IN ({allProductIds.JoinEx(",", true)}) ";
                    }
                    else
                    {
                        tmpProductIdTbl = dbSvc.CreateTempTableWithDataList(topCtx, allProductIds, false);
                        whereSql.Add($"  {productFieldKey} IN (SELECT fid FROM {tmpProductIdTbl}) ");
                        //where += $" OR OE.fattrinfo_e IN (SELECT fid FROM {tmpAttrinfoIdTbl}) ";
                        //materialTableJoin = $" INNER JOIN {tmpProductIdTbl} _mat on OE.{productFieldKey}=_mat.fid ";
                        //sql += $" AND {productFieldKey} IN (SELECT fid FROM {tmpProductIdTbl}) ";
                    }
                }

                var tmpAttrinfoIdTbl = "";
                if (allAttrInfoIds.Any())
                {
                    if (allAttrInfoIds.Count == 1)
                    {
                        whereSql.Add($"  OE.fattrinfo_e = '{allAttrInfoIds.FirstOrDefault()}' ");
                    }
                    else if (allAttrInfoIds.Count <= 50)
                    {
                        whereSql.Add($"  OE.fattrinfo_e IN ({allAttrInfoIds.JoinEx(",", true)}) ");
                    }
                    else
                    {
                        tmpAttrinfoIdTbl = dbSvc.CreateTempTableWithDataList(topCtx, allAttrInfoIds, false);
                        //materialTableJoin = $" INNER JOIN {tmpAttrinfoIdTbl} _mat on OE.fattrinfo_e=_mat.fid ";
                        whereSql.Add($"  OE.fattrinfo_e IN (SELECT fid FROM {tmpAttrinfoIdTbl}) ");
                    }
                }
                if (whereSql.Count == 1)
                {
                    where = "( " + whereSql.First() + " )";
                }
                else if (whereSql.Count == 2)
                {
                    where = "( " + whereSql[0] + " OR " + whereSql[1] + " )";
                }
                //where += " ) ";
                //查询行关闭状态为【正常】及【部分关闭】的数据
                string sql = "";
                if (formId.Equals("ydj_order"))
                {
                    sql = $@"select od.fid,OE.fentryid,mat.fid fmaterialid,mat.fseltypeid as fseltypeid,OE.fattrinfo_e as fattrinfo_e,case when mat.fispresetprop=1 or mat.fcustom=1 or mat.funstdtype=1 then '1' else 0 end ftype,OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty frealbizqty,
							 case when 
							 sum(ISNULL(case pur.fhqderstatus when  null then 0 when '' then 0  when '02' then purEntry.fbizqty when '03' then purEntry.fbizqty end,0) )+OE.fbizoutqty<=OE.fbizqty 
							 then OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty -sum(ISNULL(case pur.fhqderstatus when  null then 0 when '' then 0  when '02' then purEntry.fbizqty when '03' then purEntry.fbizqty end,0) )
							 else OE.fbizreturnqty end fbizqty from 
                            T_YDJ_ORDER od with(nolock)
                            inner join T_YDJ_ORDERENTRY OE with(nolock) on od.fid=OE.fid
                            INNER JOIN T_BD_MATERIAL mat with(nolock) on OE.{productFieldKey}=mat.fid
                            INNER JOIN t_bd_billtype bt with(nolock) on od.fbilltype=bt.fid
                            INNER JOIN t_bas_agent agent with(nolock) on od.fmainorgid=agent.fid
                            left join T_YDJ_POORDERENTRY purEntry with(nolock) on OE.fentryid=purEntry.fsourceentryid
                            left join T_YDJ_PURCHASEORDER pur with(nolock) on purEntry.fid=pur.fid
                            {materialTableJoin}
                            where (OE.fclosestatus='0'OR OE.fclosestatus='2')  and  OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty>0
                             and od.fisresellorder=0 and OE.fisoutspot=0 and bt.fname<>'销售转单' and OD.fcancelstatus=0 AND agent.fnotcountdelistling=0 AND 
                            {where}
							 group by OE.fbizqty,OE.fbizoutqty,OE.fbizreturnqty,od.fid,OE.fentryid,mat.fid,mat.fseltypeid,OE.fattrinfo_e,OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty,mat.fispresetprop,mat.fcustom,mat.funstdtype";
                    /*
                                        string billTypeTableName = slaveDBSvc.CreateTemporaryTableName(topCtx, false);
                                        string billTypeSql = $@"select fid into {billTypeTableName} from t_bd_billtype with(nolock) where fname<>'销售转单' ";
                                        slaveDBSvcEx.Execute(topCtx, billTypeSql);

                                        string orderEntryTableName = slaveDBSvc.CreateTemporaryTableName(topCtx, false);
                                        string orderEntrySql = $@"select OE.fid,OE.fentryid,OE.fproductid,OE.fattrinfo_e as fattrinfo_e,OE.fbizqty,OE.fbizoutqty,OE.fbizreturnqty,OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty frealbizqty  into {orderEntryTableName} 
                                        from T_YDJ_ORDERENTRY OE with(nolock) {tmpProductIdTbl} where (OE.fclosestatus='0'OR OE.fclosestatus='2')  and  OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty>0  and OE.fisoutspot=0 AND EXISTS (SELECT 1 FROM T_YDJ_ORDER AS OD WITH(NOLOCK)  WHERE OE.fid=OD.fid  AND od.fisresellorder=0 and OD.fcancelstatus=0 AND OD.fmainorgid not in ('{string.Join("','", noCheckDelistingIds)}') ) AND {where}";
                                        slaveDBSvcEx.Execute(topCtx, orderEntrySql);


                                        sql = $@"select od.fid,OE.fentryid,mat.fid fmaterialid,mat.fseltypeid as fseltypeid,OE.fattrinfo_e,case when mat.fispresetprop=1 or mat.fcustom=1 or mat.funstdtype=1 then '1' else 0 end ftype,OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty frealbizqty,
                                                 case when 
                                                 sum(ISNULL(case pur.fhqderstatus when  null then 0 when '' then 0  when '02' then purEntry.fbizqty when '03' then purEntry.fbizqty end,0) )+OE.fbizoutqty<=OE.fbizqty 
                                                 then OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty -sum(ISNULL(case pur.fhqderstatus when  null then 0 when '' then 0  when '02' then purEntry.fbizqty when '03' then purEntry.fbizqty end,0) )
                                                 else OE.fbizreturnqty end fbizqty from 
                                                T_YDJ_ORDER od with(nolock)
                                                inner join {orderEntryTableName} OE with(nolock) on od.fid=OE.fid
                                                INNER JOIN T_BD_MATERIAL mat with(nolock) on OE.fproductid=mat.fid
                                                INNER JOIN {billTypeTableName} bt with(nolock) on od.fbilltype=bt.fid
                                                left join T_YDJ_POORDERENTRY purEntry with(nolock) on OE.fentryid=purEntry.fsourceentryid
                                                left join T_YDJ_PURCHASEORDER pur with(nolock) on purEntry.fid=pur.fid
                                                INNER JOIN t_bas_agent agent with(nolock) on od.fmainorgid=agent.fid
                                                {materialTableJoin}
                                                where    od.fisresellorder=0  and OD.fcancelstatus=0 AND agent.fnotcountdelistling=0 AND agent.fforbidstatus=0 AND 
                                                {where}
                                                 group by OE.fbizqty,OE.fbizoutqty,OE.fbizreturnqty,od.fid,OE.fentryid,mat.fid,mat.fseltypeid,OE.fattrinfo_e,OE.fbizqty-OE.fbizoutqty+OE.fbizreturnqty,mat.fispresetprop,mat.fcustom,mat.funstdtype";
                    */
                    orderEntryObjs = slaveDBSvc.ExecuteDynamicObject(topCtx, sql).ToList();
                    tran.Complete();
                    //销售合同【行关闭状态】过滤
                    // sql += $" WHERE (fclosestatus='0'OR fclosestatus='2') AND fbizqty-fbizoutqty+fbizreturnqty>0    {where}";
                }
                else
                {
                    sql = $@"select mat.fid fmaterialid,OE.fentryid,mat.fseltypeid as fseltypeid,OE.fattrinfo_e as fattrinfo_e,case when mat.fispresetprop=1 or mat.fcustom=1 or mat.funstdtype=1 then '1' else 0 end ftype,SUM(OE.fbizqty) fbizqty from T_YDJ_POORDERENTRY OE with(nolock)
                            INNER JOIN T_BD_MATERIAL mat with(nolock) on OE.{productFieldKey}=mat.fid 
                            {materialTableJoin}
                            WHERE EXISTS (SELECT 1 FROM T_YDJ_PURCHASEORDER AS OD WITH(NOLOCK) INNER JOIN t_bas_agent agent with(nolock) on OD.fmainorgid=agent.fid WHERE OE.fid=OD.fid  AND agent.fnotcountdelistling=0 AND fhqderstatus in ('02'))  AND
                            {where}
                            GROUP BY mat.fid,OE.fentryid,mat.fseltypeid,OE.fattrinfo_e,mat.fispresetprop,mat.fcustom,mat.funstdtype";
                    orderEntryObjs = dbSvc.ExecuteDynamicObject(topCtx, sql).ToList();
                    tran.Complete();
                    //采购订单【总部合同状态】过滤
                    // sql += " WHERE EXISTS (SELECT 1 FROM T_YDJ_PURCHASEORDER AS OD WITH(NOLOCK) WHERE OE.fid=OD.fid AND fhqderstatus='02') ";
                }
            }
            return orderEntryObjs;
        }


        /// <summary>
        /// 标记商品为退市商品
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productIds">商品内码ID</param>
        /// <param name="selTypeIds">型号内码ID</param>
        public static void MarkProductDelisting(UserContext userCtx, List<string> productIds, List<string> selTypeIds)
        {
            //创建总部视角上下文
            var topCtx = userCtx.CreateTopOrgDBContext();
            var dbSvc = userCtx.Container.GetService<IDBService>();

            var allProductIds = new List<string>();
            if (productIds != null && productIds.Any())
            {
                allProductIds.AddRange(productIds);
            }

            //查询型号下的所有商品
            if (selTypeIds != null && selTypeIds.Any())
            {
                using (var tran = topCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                {
                    var tmpSelTypeIdTbl = "";
                    var sqlWhere = "";
                    if (selTypeIds.Count == 1)
                    {
                        sqlWhere += $" WHERE M.fseltypeid = '{selTypeIds.FirstOrDefault()}' ";
                    }
                    else if (selTypeIds.Count <= 50)
                    {
                        sqlWhere += $" WHERE M.fseltypeid IN ({selTypeIds.JoinEx(",", true)}) ";
                    }
                    else
                    {
                        tmpSelTypeIdTbl = dbSvc.CreateTempTableWithDataList(topCtx, selTypeIds, false);
                        sqlWhere += $" INNER JOIN {tmpSelTypeIdTbl} AS TMP ON M.fseltypeid=TMP.fid ";
                    }
                    var sql = $@"SELECT M.fid FROM T_BD_MATERIAL AS M WITH(NOLOCK) {sqlWhere}";

                    var productIdObjs = dbSvc.ExecuteDynamicObject(topCtx, sql);
                    if (productIdObjs != null && productIdObjs.Any())
                    {
                        allProductIds.AddRange(productIdObjs.Select(t => Convert.ToString(t["fid"])));
                    }

                    if (tmpSelTypeIdTbl.IsNullOrEmptyOrWhiteSpace())
                    {
                        dbSvc.DeleteTempTableByName(topCtx, tmpSelTypeIdTbl, true);
                    }

                    tran.Complete();
                }
            }
            allProductIds = allProductIds.Where(t => !t.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();

            if (!allProductIds.Any())
            {
                return;
            }

            //给匹配到的商品标记【退市】
            //注意：这里标记的商品可能会比实际需要退市的商品多，但是没关系，标记只是为了缩小后续业务判断的范围
            using (var tran = topCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                List<string> sqlList = new List<string>();
                foreach (var proId in allProductIds)
                {
                    sqlList.Add($"/*dialect*/UPDATE T_BD_MATERIAL SET fdelisting='1' WHERE fid='{proId}' ;");
                }
                var dbSvcEx = topCtx.Container.GetService<IDBServiceEx>();
                dbSvcEx.ExecuteBatch(topCtx, sqlList);

                tran.Complete();
            }
        }

        /// <summary>
        /// 根据辅助属性内码获取明细数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="attrInfoIds">属性内码ID</param>
        /// <returns></returns>
        public static Dictionary<string, List<Tuple<string, string>>> GetAttrInfo(UserContext userCtx, List<string> attrInfoIds)
        {
            var topCtx = userCtx.CreateTopOrgDBContext();
            var dbSvc = topCtx.Container.GetService<IDBService>();
            Dictionary<string, List<Tuple<string, string>>> result = new Dictionary<string, List<Tuple<string, string>>>();
            attrInfoIds = attrInfoIds.Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            if (attrInfoIds == null || !attrInfoIds.Any()) return result;

            using (var tran = topCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var sqlWhere = "";
                var tmpTblName = "";
                if (attrInfoIds.Count == 1)
                {
                    sqlWhere += $" WHERE A.fattrinfo_e = '{attrInfoIds.FirstOrDefault()}' ";
                }
                else if (attrInfoIds.Count <= 50)
                {
                    sqlWhere += $" WHERE A.fattrinfo_e IN ({attrInfoIds.JoinEx(",", true)}) ";
                }
                else
                {
                    tmpTblName = dbSvc.CreateTempTableWithDataList(topCtx, attrInfoIds, false);
                    sqlWhere += $" INNER JOIN {tmpTblName} AS TMP ON A.fattrinfo_e=TMP.fid ";
                }

                var sql = $@"select distinct a.fattrinfo_e,b.fauxpropid,b.fvalueid 
                            from T_BD_AUXPROPVALUE a with(nolock)
                            inner join T_BD_AUXPROPVALUEENTRY b with(nolock) on a.fid=b.fid
                             {sqlWhere}";
                var attrInfo = dbSvc.ExecuteDynamicObject(topCtx, sql);
                if (attrInfo != null && attrInfo.Any())
                {
                    var attrInfoGroup = attrInfo.Select(t => new
                    {
                        Id = Convert.ToString(t["fattrinfo_e"]),
                        AuxpropId = Convert.ToString(t["fauxpropid"]),
                        ValueId = Convert.ToString(t["fvalueid"])
                    })
                    .GroupBy(f => f.Id);
                    foreach (var group in attrInfoGroup)
                    {
                        result[group.Key] = group.Select(t => new Tuple<string, string>(t.AuxpropId, t.ValueId)).ToList();
                    }
                }

                if (!tmpTblName.IsNullOrEmptyOrWhiteSpace())
                {
                    dbSvc.DeleteTempTableByName(topCtx, tmpTblName, true);
                }

                tran.Complete();
            }

            return result;
        }

        /// <summary>
        /// 根据中台的数据ID查询退市商品可下单量
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="hqIds">中台数据ID</param>
        /// <returns></returns>
        public static List<string> GetDelistingProductByHqId(UserContext userCtx, List<string> hqIds)
        {
            List<string> result = new List<string>();
            if (hqIds != null && hqIds.Any())
            {
                var topCtx = userCtx.CreateTopOrgDBContext();
                var dbSvc = topCtx.Container.GetService<IDBService>();

                using (var tran = topCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                {
                    var sqlWhere = "";
                    var tmpTblName = "";
                    if (hqIds.Count == 1)
                    {
                        sqlWhere = $" WHERE PDL.fhqid='{hqIds.First()}'";
                    }
                    else if (hqIds.Count <= 50)
                    {
                        sqlWhere = $" WHERE PDL.fhqid IN ({hqIds.JoinEx(",", true)})";
                    }
                    else
                    {
                        tmpTblName = dbSvc.CreateTempTableWithDataList(topCtx, hqIds, false);
                        sqlWhere += $" INNER JOIN {tmpTblName} AS TMP ON PDL.fhqid=TMP.fid ";
                    }

                    var sql = $@"SELECT PDL.fid FROM T_YDJ_PRODUCTDELISTING AS PDL WITH(NOLOCK) {sqlWhere}";
                    var delistObjs = dbSvc.ExecuteDynamicObject(topCtx, sql);
                    if (delistObjs != null && delistObjs.Any())
                    {
                        result = delistObjs.Select(t => Convert.ToString(t["fid"])).ToList();
                    }

                    if (!tmpTblName.IsNullOrEmptyOrWhiteSpace())
                    {
                        dbSvc.DeleteTempTableByName(topCtx, tmpTblName, true);
                    }

                    tran.Complete();
                }
            }

            return result;
        }


        public static IEnumerable<DynamicObject> GetProductDelistingBySpecs(UserContext userCtx, List<DynamicObject> objs)
        {
            objs = objs.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["fspecmaterial"]))).ToList();
            if (objs.Count == 0) return new List<DynamicObject>();
            var topCtx = userCtx.CreateTopOrgDBContext();
            List<string> specs = objs.Select(a => Convert.ToString(a["fspecmaterial"])).Where(a => !a.IsNullOrEmptyOrWhiteSpace()).ToList();
            List<string> seltypeid = objs.Select(a => Convert.ToString(a["fseltypeid"])).ToList();
            List<string> ids = objs.Select(a => Convert.ToString(a["id"])).ToList();
            if (specs.Count == 0 && seltypeid.Count == 0) return new List<DynamicObject>();

            string filter = " (fspecmaterial in(@specmaterial) OR fseltypeid in (@seltypeid)) AND fid not in (@id)";
            filter = $" (fspecmaterial in('{string.Join("','", specs)}') OR fseltypeid in ('{string.Join("','", seltypeid)}')) ";
            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@specmaterial", System.Data.DbType.String, string.Join("','", specs)));
            sqlParams.Add(new SqlParam("@seltypeid", System.Data.DbType.String, string.Join("','", seltypeid)));
            //sqlParams.Add(new SqlParam("id", System.Data.DbType.String, string.Join("','", ids)));
            List<DynamicObject> dynamics = topCtx.LoadBizDataByFilter(formId, filter, true, null);
            return dynamics;
        }

        public static List<Tuple<string, string>> GetProps(UserContext userCtx, List<string> colors)
        {
            //.Where(c => !c.PropValNo.IsNullOrEmptyOrWhiteSpace()).Select(f => f.PropValNo).Distinct();
            List<Tuple<string, string>> lstPropval = new List<Tuple<string, string>>();
            if (colors != null && colors.Any())
            {
                var mate = userCtx.Container.GetService<IMetaModelService>();
                var db = userCtx.Container.GetService<IDBService>();
                var propvalForm = mate.LoadFormModel(userCtx, "sel_propvalue");
                var propvalNumberField = propvalForm.GetNumberField();

                string sql = $@"select distinct p.fid fpropid,pv.fid,pv.fnumber from T_SEL_PROP p with(nolock)
                            inner join T_SEL_PROPVALUE pv with(nolock) on p.fid=pv.fpropid
                            where p.fmainorgid='{userCtx.TopCompanyId}' and p.fforbidstatus=0 and pv.fforbidstatus=0 and pv.fnumber in ({colors.JoinEx(",", true)}) ";
                using (var reader = db.ExecuteReader(userCtx, sql))
                {
                    while (reader.Read())
                    {
                        string id = reader.GetValueToString(propvalForm.BillPKFldName);
                        string number = reader.GetValueToString(propvalNumberField.FieldName);
                        lstPropval.Add(new Tuple<string, string>(number, id));
                    }
                }
            }
            return lstPropval;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dynamicObjects"></param>
        /// <param name="productId"></param>
        /// <param name="seltypeId"></param>
        /// <param name="attrs"></param>
        /// <param name="productDelistingMat"></param>
        /// <returns></returns>
        public static List<DynamicObject> GetDetailByData(List<DynamicObject> dynamicObjects, string productId, string seltypeId, List<string> attrs, List<string> productDelistingMat, bool type = false)
        {
            if (dynamicObjects == null) return new List<DynamicObject>();
            List<DynamicObject> _result1 = new List<DynamicObject>();
            //按型号退市
            //并且不在退市清单里
            if (!string.IsNullOrWhiteSpace(seltypeId) && type)
            {
                _result1 = dynamicObjects
                 .Where(a => Convert.ToString(a["fseltypeid"]).Equals(seltypeId)
                 && productDelistingMat.IndexOf(Convert.ToString(a["fmaterialid"])) == -1)
                 .ToList();
            }

            if (!string.IsNullOrWhiteSpace(seltypeId))
            {
                dynamicObjects = (dynamicObjects.Where(a => Convert.ToString(a["fseltypeid"]).Equals(seltypeId)).ToList());
            }
            if (!string.IsNullOrWhiteSpace(productId))
            {
                dynamicObjects = (dynamicObjects.Where(a => Convert.ToString(a["fmaterialid"]).Equals(productId)).ToList());
            }
            if (attrs != null && attrs.Count > 0)
            {
                dynamicObjects = (dynamicObjects.Where(a => attrs.IndexOf(Convert.ToString(a["fattrinfo_e"])) >= 0).ToList());
            }
            //按型号退市
            if (!string.IsNullOrWhiteSpace(seltypeId) && type)
            {
                var _result = new List<DynamicObject>();
                //先查定制品
                _result = dynamicObjects.Where(a => Convert.ToString(a["ftype"]).Equals("1")).ToList();
                //再把标准品或定制品并且不在退市表里的商品算进来
                _result.AddRange(_result1);
                dynamicObjects = _result;
            }
            return dynamicObjects;
        }

        #region 没用的


        /// <summary>
        /// 通过【专用材料】找退市清单 
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="specmaterial"></param>
        /// <param name="id"></param>
        /// <returns>传id则排除自己</returns>
        public static IEnumerable<DynamicObject> GetProductDelistingBySpec(UserContext userCtx, DynamicObject obj)
        {
            var mate = userCtx.Container.GetService<IMetaModelService>();
            var delistingForm = mate.LoadFormModel(userCtx, formId);
            var refObjMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(userCtx, new[] { obj }, true, delistingForm, new List<string> { "fmaterialid" });
            string specmaterial = Convert.ToString(obj["fspecmaterial"]);
            string seltypeid = Convert.ToString(obj["fseltypeid"]);
            string id = Convert.ToString(obj["id"]);
            bool isstandtype = false;
            if (!Convert.ToBoolean((obj["fmaterialid_ref"] as DynamicObject)?["fispresetprop"])//允许选配
                && !Convert.ToBoolean((obj["fmaterialid_ref"] as DynamicObject)?["fcustom"])//允许定制
                && !Convert.ToBoolean((obj["fmaterialid_ref"] as DynamicObject)?["funstdtype"]))//非标产品
            {
                isstandtype = true;
            }
            if (string.IsNullOrWhiteSpace(specmaterial))
            {
                return new List<DynamicObject>();
            }
            var topCtx = userCtx.CreateTopOrgDBContext();
            string filter = " fspecmaterial=@specmaterial OR fseltypeid=@seltypeid";
            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@specmaterial", System.Data.DbType.String, specmaterial));
            sqlParams.Add(new SqlParam("@seltypeid", System.Data.DbType.String, seltypeid));
            if (!string.IsNullOrWhiteSpace(id))
            {
                filter += " AND fid<>@id ";
                sqlParams.Add(new SqlParam("@id", System.Data.DbType.String, id));
            }
            List<DynamicObject> dynamics = topCtx.LoadBizDataByFilter(formId, filter, false, sqlParams);
            List<DynamicObject> result = new List<DynamicObject>();
            if (dynamics.Count > 0)
            {
                // 加载数据
                refObjMgr?.Load(userCtx, dynamics.ToArray(), true, delistingForm, new List<string> { "fmaterialid" });
                foreach (var item in dynamics)
                {
                    if (isstandtype)
                    {
                        //只查定制
                        if (Convert.ToBoolean((item["fmaterialid_ref"] as DynamicObject)?["fispresetprop"])//允许选配
                           || Convert.ToBoolean((item["fmaterialid_ref"] as DynamicObject)?["fcustom"])//允许定制
                           || Convert.ToBoolean((item["fmaterialid_ref"] as DynamicObject)?["funstdtype"]))//非标产品
                        {
                            result.Add(item);
                        }
                    }
                    else
                    {
                        //只查标准
                        if (!Convert.ToBoolean((item["fmaterialid_ref"] as DynamicObject)?["fispresetprop"])//允许选配
                           && !Convert.ToBoolean((item["fmaterialid_ref"] as DynamicObject)?["fcustom"])//允许定制
                           && !Convert.ToBoolean((item["fmaterialid_ref"] as DynamicObject)?["funstdtype"]))//非标产品
                        {
                            result.Add(item);
                        }
                    }
                }
            }
            return result;
        }


        public static List<DelistingDataModel> GetSnapExceptDataBySalOrder(string sourceFormId, List<DynamicObject> billSnapshotObjs, List<DynamicObject> dataEntity)
        {
            string productFieldId = sourceFormId.Equals("ydj_order") ? "fproductid" : "fmaterialid";
            List<DelistingDataModel> delistindDataModels = new List<DelistingDataModel>();
            List<DynamicObject> allValidateEntrys = new List<DynamicObject>();
            foreach (var item in dataEntity)
            {
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                if (item.DataEntityState.FromDatabase && existSnapshot != null)
                {
                    var entry = item["fentry"] as DynamicObjectCollection;
                    var snapEntry = existSnapshot["fentry"] as DynamicObjectCollection;
                    foreach (var entryItem in entry)
                    {
                        var snapEntryItem = snapEntry.Where(a => Convert.ToString(a["id"]).Equals(entryItem["id"])).FirstOrDefault();
                        if (snapEntryItem != null)
                        {
                            //存在，更新
                            allValidateEntrys.Add(entryItem);
                            //判断商品是否一致，若商品不一致或辅助属性不一致，则快照商品删除，单据商品新增

                            if (Convert.ToString(snapEntryItem[productFieldId]).Equals(entryItem[productFieldId]))
                            {
                                if (Convert.ToString(snapEntryItem["fattrinfo_e"]).Trim().Equals(Convert.ToString(entryItem["fattrinfo_e"]).Trim()))
                                {
                                    //对比数量，找出差值
                                    decimal snapQty = Convert.ToDecimal(snapEntryItem["fbizqty"]);
                                    decimal orderQty = Convert.ToDecimal(entryItem["fbizqty"]);
                                    if (snapQty > orderQty)
                                    {
                                        //减少了（快照数量>单据数量
                                        delistindDataModels.Add(new DelistingDataModel()
                                        {
                                            Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                            Qty = snapQty - orderQty,
                                            RealQty = snapQty - orderQty,
                                            calType = (int)CalType.Reduce,
                                        });
                                    }
                                    else if (snapQty > orderQty)
                                    {
                                        //减少了（快照数量>单据数量
                                        delistindDataModels.Add(new DelistingDataModel()
                                        {
                                            Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                            Qty = orderQty - snapQty,
                                            RealQty = orderQty - snapQty,
                                            calType = (int)CalType.Add,
                                        });
                                    }
                                }
                                else
                                {
                                    //商品一样，辅助属性不一样，把旧的减少，新增的增加
                                    delistindDataModels.Add(new DelistingDataModel()
                                    {
                                        Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                        Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                        Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                        RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                                        calType = (int)CalType.Add,
                                    });
                                    delistindDataModels.Add(new DelistingDataModel()
                                    {
                                        Material = snapEntryItem[productFieldId + "_ref"] as DynamicObject,
                                        Attrinfo = Convert.ToString(snapEntryItem["fattrinfo_e"]),
                                        Qty = Convert.ToDecimal(snapEntryItem["fbizqty"]),
                                        RealQty = Convert.ToDecimal(snapEntryItem["fbizqty"]),
                                        calType = (int)CalType.Reduce,
                                    });
                                }
                            }
                            else
                            {
                                //商品不一样，旧的减少，新增增加
                                //商品一样，辅助属性不一样，把旧的减少，新增的增加
                                delistindDataModels.Add(new DelistingDataModel()
                                {
                                    Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                    Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                    Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                    RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                                    calType = (int)CalType.Add,
                                });
                                delistindDataModels.Add(new DelistingDataModel()
                                {
                                    Material = snapEntryItem[productFieldId + "_ref"] as DynamicObject,
                                    Attrinfo = Convert.ToString(snapEntryItem["fattrinfo_e"]),
                                    Qty = Convert.ToDecimal(snapEntryItem["fbizqty"]),
                                    RealQty = Convert.ToDecimal(snapEntryItem["fbizqty"]),
                                    calType = (int)CalType.Reduce,
                                });
                            }
                        }
                        else
                        {
                            //不存在，新增
                            delistindDataModels.Add(new DelistingDataModel()
                            {
                                Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                                calType = (int)CalType.Add,
                            });
                        }
                    }
                    foreach (var entryItem in snapEntry)
                    {
                        if (!entry.Any(a => Convert.ToString(a["id"]).Equals(entryItem["id"])))
                        {
                            //快照有，单据没有，删除
                            //不存在，新增
                            delistindDataModels.Add(new DelistingDataModel()
                            {
                                Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                                Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                                Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                                RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                                calType = 2,
                            });
                        }
                    }
                }
                else
                {
                    //当不存在快照，则人为是创建，每行都是新的
                    var entry = item["fentry"] as DynamicObjectCollection;
                    foreach (var entryItem in entry)
                    {
                        //商品一样，辅助属性不一样，把旧的减少，新增的增加
                        delistindDataModels.Add(new DelistingDataModel()
                        {
                            Material = entryItem[productFieldId + "_ref"] as DynamicObject,
                            Attrinfo = Convert.ToString(entryItem["fattrinfo_e"]),
                            Qty = Convert.ToDecimal(entryItem["fbizqty"]),
                            RealQty = Convert.ToDecimal(entryItem["fbizqty"]),
                            calType = (int)CalType.Add,
                        });
                    }
                }
            }
            return delistindDataModels;
        }
        //public static void GetMatchDelisting(UserContext ctx, List<DelistingDataModel> changeDataModel, List<string> allMatchIds)
        //{
        //    var topCtx = ctx.CreateTopOrgDBContext();
        //    var allDelist = topCtx.LoadBizDataById(formId, allMatchIds, true);
        //    if (allDelist == null || !allDelist.Any()) return;

        //    var mate = topCtx.Container.GetService<IMetaModelService>();
        //    var delistingForm = mate.LoadFormModel(topCtx, formId);
        //    var getway = topCtx.Container.GetService<IHttpServiceInvoker>();

        //    // 加载数据
        //    var refObjMgr = topCtx.Container.GetService<LoadReferenceObjectManager>();
        //    //取出辅助属性明细
        //    Dictionary<string, List<Tuple<string, string>>> dicAttrInfo = new Dictionary<string, List<Tuple<string, string>>>();
        //    var allAttrIds = changeDataModel.Select(a => a.Attrinfo).Distinct().ToList();
        //    if (allAttrIds != null && allAttrIds.Any())
        //    {
        //        dicAttrInfo = GetAttrInfo(topCtx, allAttrIds);
        //    }

        //    List<DynamicObject> matchDelist = new List<DynamicObject>();
        //    #region 查出来匹配上的退市清单以及关联退市清单
        //    foreach (var entry in changeDataModel)
        //    {
        //        List<DynamicObject> _matchDelist = new List<DynamicObject>();
        //        var currProductObj = entry.Material;
        //        var currProductId = Convert.ToString(currProductObj["id"]);
        //        var currSelTypeId = Convert.ToString(currProductObj["fseltypeid"]);
        //        var currAttrInfoId = entry.Attrinfo;


        //        //49683 【慕思UAT-退市产品提醒需求】按【型号】退市，销售合同调整销售数量后：①调整无关的标准品触发了更新逻辑。②调整对应定制品，未触发更新逻辑（点<初始数据更新重试>无问题）
        //        //判断商品是否标准品
        //        if (!Convert.ToBoolean(currProductObj["fispresetprop"])//允许选配
        //            && !Convert.ToBoolean(currProductObj["fcustom"])//允许定制
        //            && !Convert.ToBoolean(currProductObj["funstdtype"]))//非标产品
        //        {
        //            //标品用商品判断，用类型判断会导致放大范围
        //            _matchDelist = allDelist.Where(t =>
        //            !Convert.ToString(t["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()
        //            && Convert.ToString(t["fmaterialid"]).EqualsIgnoreCase(currProductId)
        //            && !Convert.ToBoolean(t["ftype"])
        //            ).ToList();
        //            if ((_matchDelist == null || !_matchDelist.Any()) && !currSelTypeId.IsNullOrEmptyOrWhiteSpace())
        //            {
        //                //当商品找不到，则用型号找
        //                //找的条件：型号相同，退市清单是定制
        //                _matchDelist = allDelist
        //                    .Where(a => Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(currProductObj["fseltypeid"]))
        //                    && Convert.ToBoolean(a["ftype"])).ToList();
        //            }
        //        }
        //        else
        //        {
        //            //非标商品按照型号+辅助属性匹配判断
        //            if (!currSelTypeId.IsNullOrEmptyOrWhiteSpace() && !currAttrInfoId.IsNullOrEmptyOrWhiteSpace())
        //            {
        //                _matchDelist = allDelist.Where(t =>
        //                !Convert.ToString(t["fseltypeid"]).IsNullOrEmptyOrWhiteSpace()
        //                && Convert.ToString(t["fseltypeid"]).EqualsIgnoreCase(currSelTypeId)
        //                && Convert.ToBoolean(t["ftype"])
        //                ).ToList();
        //                if (_matchDelist != null && _matchDelist.Any())
        //                {
        //                    //匹配辅助属性
        //                    var attrDetail = dicAttrInfo[currAttrInfoId];
        //                    var matchAttrList = new List<DynamicObject>();
        //                    var colorName = "";
        //                    foreach (var match in _matchDelist)
        //                    {
        //                        var propEntry = match["fpropentry"] as DynamicObjectCollection;
        //                        if (propEntry == null || !propEntry.Any())
        //                        {
        //                            //定制品，没有设属性，匹配到了也算
        //                            matchAttrList.Add(match);
        //                            continue;
        //                        }
        //                        var matchPropEntry = propEntry.Where(t => attrDetail.Any(d => d.Item2.EqualsIgnoreCase(Convert.ToString(t["fpropvalueid"]))));
        //                        if (matchPropEntry != null && matchPropEntry.Any())
        //                        {
        //                            var propValueObj = matchPropEntry.FirstOrDefault()["fpropvalueid_ref"] as DynamicObject;
        //                            colorName = Convert.ToString(propValueObj["fname"]);
        //                            matchAttrList.Add(match);
        //                        }
        //                    }
        //                    var _result = new List<DynamicObject>();
        //                    foreach (var item in matchAttrList)
        //                    {
        //                        var _product = (item["fmaterialid_ref"] as DynamicObject);
        //                        if (_product != null)
        //                        {
        //                            //只查定制
        //                            if (Convert.ToBoolean(_product["fispresetprop"])//允许选配
        //                               || Convert.ToBoolean(_product["fcustom"])//允许定制
        //                               || Convert.ToBoolean(_product["funstdtype"]))//非标产品
        //                            {
        //                                _result.Add(item);
        //                                continue;
        //                            }
        //                        }
        //                    }
        //                    _matchDelist = _result;
        //                }
        //            }
        //        }
        //        if (_matchDelist == null || !_matchDelist.Any()) continue;
        //        matchDelist.AddRange(_matchDelist);
        //    }
        //    if (matchDelist == null || !matchDelist.Any()) return;
        //    matchDelist = matchDelist.Distinct().ToList();
        //    var linkProductDelistings = ProductDelistingHelper.GetProductDelistingBySpecs(topCtx, matchDelist).ToList();
        //    refObjMgr?.Load(topCtx, linkProductDelistings.ToArray(), true, delistingForm, new List<string> { "fmaterialid" });
        //}

        ///// <summary>
        ///// 退市增减计算
        ///// </summary>
        ///// <param name="matchDelist">当前涉及的退市清单</param>
        ///// <param name="linkProductDelistings">关联占用的退市清单</param>
        ///// <param name="qty">增减数量</param>
        ///// <param name="calType">计算类型 1:加 2:减</param>
        ///// <returns>返回计算后的结果</returns>
        //public static List<DynamicObject> UpdateDelistingQty(List<DynamicObject> matchDelist, List<DynamicObject> linkProductDelistings, List<DelistingDataModel> changeDataModel, int calType = 0)
        //{
        //    foreach (var dataModel in changeDataModel)
        //    {

        //    }
        //    foreach (var delistObj in matchDelist)
        //    {
        //        bool isstandtype = !Convert.ToBoolean(delistObj["ftype"]);


        //        var productDelistings = linkProductDelistings.Where(a =>
        //                 Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                 !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //        if (isstandtype)
        //        {
        //            //当前是标准品，查定制品的退市清单，包含两种
        //            /*
        //             * 专用材料能匹配上，直接加进来
        //             * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
        //             */
        //            //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
        //            var _dynamics1 = linkProductDelistings.Where(a =>
        //                Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
        //                !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                Convert.ToBoolean(a["ftype"]) &&
        //                !(a["fpropentry"] as DynamicObjectCollection).Any() &&
        //                !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //            if (_dynamics1.Count > 0)
        //            {
        //                //存在定制，需要把关联的标品的也带出来算
        //                _dynamics1 = linkProductDelistings.Where(a =>
        //                Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
        //                !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //            }
        //            productDelistings.AddRange(_dynamics1);
        //        }
        //        else
        //        {
        //            //当前是定制品，查定制品的退市清单，包含两种
        //            /*
        //             * 专用材料能匹配上，直接加进来
        //             * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
        //             */
        //            //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
        //            var _dynamics1 = linkProductDelistings.Where(a =>
        //                Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
        //                !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                !Convert.ToBoolean(a["ftype"]) &&
        //                !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //            productDelistings.AddRange(_dynamics1);

        //        }
        //        var _bzp = new List<DynamicObject>();
        //        var _dzp = new List<DynamicObject>();

        //        //如果是标准品，存在其他标准品也是一样的专用材料
        //        productDelistings.ForEach(a =>
        //        {
        //            if (!Convert.ToBoolean(a["ftype"]))
        //                _bzp.Add(a);
        //            else
        //                _dzp.Add(a);
        //        });
        //        if (isstandtype)
        //            _bzp.Add(delistObj);
        //        else
        //            _dzp.Add(delistObj);

        //        //计算【累计采购已下单数量】
        //        var fsumpurorderqty = 0M;
        //        //【占用型号累计采购已下单数量】
        //        var fmatoccupysumpurqty = 0M;
        //        //【可使用型号采购可下单数量】
        //        var fsumpurqty = 0M;
        //        //【库存占用累计采购已下单数量】
        //        var finvoccupysumpurqty = 0M;
        //        ///【库存占用累计销售已下单数量】
        //        var finvoccupysumsalqty = 0M;
        //        ///【销售可下单数量】
        //        var fsalcanplaceorderqty = 0M;
        //        ///【采购可下单数量】
        //        var fpurcanplaceorderqty = 0M;
        //        //计算【累计销售已下单数量】
        //        var fsumsalorderqty = 0M;
        //        var fsumsalorderqtyreal = 0M;
        //        decimal exceedQty = 0;//超出数量
        //        foreach (var bzpItem in _bzp)
        //        {
        //            var productId = Convert.ToString(bzpItem["fmaterialid"]);
        //            var seltypeId = Convert.ToString(bzpItem["fseltypeid"]);

        //            var _entry = (bzpItem["fentity"] as DynamicObjectCollection)
        //            .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
        //            if (!Convert.ToString(bzpItem["id"]).Equals(Convert.ToString(delistObj["id"])))
        //            {
        //                //其它退市清单，只做记录，不做更新
        //                exceedQty += Convert.ToDecimal(_entry["fmatoccupysumpurqty"]);
        //                continue;
        //            }

        //            var festimateqty = Convert.ToDecimal(_entry["festimateqty"]);//预估可下单采购量
        //            var sumpurorderqty = Convert.ToDecimal(_entry["fsumpurorderqty"]);//预估可下单采购量
        //            fsumpurorderqty = sumpurorderqty;
        //            fsumsalorderqty = Convert.ToDecimal(_entry["fsumsalorderqty"]);
        //            fsumsalorderqtyreal = Convert.ToDecimal(_entry["fsumsalorderqtyreal"]);
        //            fsumpurorderqty = qty;//计算【累计采购已下单数量】
        //            if (calType == 2)
        //            {
        //                fsumpurorderqty = fsumpurorderqty * -1;
        //            }
        //            fsumpurorderqty = sumpurorderqty + fsumpurorderqty;


        //            decimal dzkylqty = 0;//定制可用量，也叫【可使用型号采购可下单数量】
        //            dzkylqty = _dzp.Select(a => (a["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).Select(c => Convert.ToDecimal(c["festimateqty"])).Sum()).Sum();
        //            //可用总量=成品可用量+定制可用量（300
        //            decimal kysumqty = dzkylqty + festimateqty;
        //            //下发数量>=累计采购已下单数量。则不需要占用定制
        //            if (festimateqty >= fsumpurorderqty)
        //            {
        //                fmatoccupysumpurqty = 0;
        //                fsumpurqty = dzkylqty;
        //            }
        //            else if (fsumpurorderqty > kysumqty)//累计采购已下单数量>可下单总量，爆单了
        //            {
        //                //若下发一百，可用总量300，查出来使用360，则占用200，剩下的可下单就0
        //                fmatoccupysumpurqty = dzkylqty == 0 ? fsumpurorderqty - festimateqty : dzkylqty;//全占用，剩下的都在自己这里体现
        //                fsumpurqty = 0;
        //                dzkylqty = 0;
        //            }
        //            else
        //            {
        //                //若下发一百，查出来使用160，则占用60
        //                fmatoccupysumpurqty = fsumpurorderqty - festimateqty;//占用1个
        //                fsumpurqty = kysumqty - fsumpurorderqty;//【可使用型号采购可下单数量】=总量（300）-采购已下单（160）=140
        //                dzkylqty = kysumqty - fsumpurorderqty;
        //            }

        //            _entry["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //            _entry["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //            _entry["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //            _entry["fpurqtyupdatetime"] = DateTime.Now;//采购可下单数量更新时间
        //            _entry["fsalqtyupdatetime"] = DateTime.Now;//销售可下单数量更新时间
        //            _entry["fenable"] = 1;//是否有效行
        //            _entry["fmodifystatus"] = "2";//初始更新情况
        //            _entry["fmatoccupysumsalqty"] = 0;//占用型号累计销售已下单数量
        //            _entry["finvoccupysumsalqty"] = finvoccupysumsalqty;//库存占用累计销售已下单数量（0
        //            _entry["fmatoccupysumpurqty"] = fmatoccupysumpurqty;//占用型号累计采购已下单数量
        //            var _linkObj = linkProductDelistings.Where(_ => Convert.ToString(_["id"]).Equals(Convert.ToString(bzpItem["id"]))).FirstOrDefault();
        //            if (_linkObj != null)
        //            {
        //                (_linkObj["fentity"] as DynamicObjectCollection).ForEach(c =>
        //                {
        //                    if (Convert.ToString(c["id"]).Equals(Convert.ToString(_entry["id"])))
        //                    {
        //                        c["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //                        c["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //                        c["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //                        c["finvoccupysumsalqty"] = finvoccupysumsalqty;//库存占用累计销售已下单数量
        //                        c["fmatoccupysumpurqty"] = fmatoccupysumpurqty;//占用型号累计采购已下单数量
        //                    }
        //                });
        //            }
        //            exceedQty += fmatoccupysumpurqty;
        //            continue;
        //        }

        //        foreach (var dzpItem in _dzp)
        //        {
        //            var _entry = (dzpItem["fentity"] as DynamicObjectCollection)
        //            .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
        //            var _propentry = (dzpItem["fpropentry"] as DynamicObjectCollection)
        //            .FirstOrDefault();
        //            var propEntrys = (dzpItem["fpropentry"] as DynamicObjectCollection).Select(a => Convert.ToString(a["fpropvalueid"])).ToList();
        //            var productId = Convert.ToString(dzpItem["fmaterialid"]);
        //            var seltypeId = Convert.ToString(dzpItem["fseltypeid"]);

        //            var festimateqty = Convert.ToDecimal(_entry["festimateqty"]);//预估可下单采购量
        //            var sumpurorderqty = Convert.ToDecimal(_entry["fsumpurorderqty"]);//预估可下单采购量
        //            fsumpurorderqty = sumpurorderqty;
        //            fsumsalorderqty = Convert.ToDecimal(_entry["fsumsalorderqty"]);
        //            fsumsalorderqtyreal = Convert.ToDecimal(_entry["fsumsalorderqtyreal"]);
        //            if (Convert.ToString(dzpItem["id"]).Equals(Convert.ToString(delistObj["id"])))
        //            {
        //                fsumpurorderqty = qty;//计算【累计采购已下单数量】
        //                if (calType == 2)
        //                {
        //                    fsumpurorderqty = fsumpurorderqty * -1;
        //                }
        //                fsumpurorderqty = sumpurorderqty + fsumpurorderqty;

        //                _entry["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //                _entry["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //                _entry["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //                _entry["fpurqtyupdatetime"] = DateTime.Now;//采购可下单数量更新时间
        //                _entry["fsalqtyupdatetime"] = DateTime.Now;//销售可下单数量更新时间
        //                _entry["fenable"] = 1;//是否有效行
        //                _entry["fmodifystatus"] = "2";//初始更新情况
        //                _entry["fmatoccupysumsalqty"] = 0;//占用型号累计销售已下单数量
        //            }


        //            _entry["finvoccupysumpurqty"] = exceedQty;//库存占用累计销售已下单数量（0
        //                                                      //【采购可下单数量】=【预估可下单采购数量】-【累计采购已下单数量】+【占用型号累计采购已下单数量】+【可使用型号采购可下单数量】-【库存占用累计采购已下单数量】
        //            fpurcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + Convert.ToDecimal(_entry["fsumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumpurqty"]);//(200)
        //                                                                                                                                                                                                                                                                            //计算规则：【销售可下单数量】=【预估采购可下单数量】(100-【累计销售已下单数量】(78-【累计采购已下单数量】(201+【占用型号累计采购已下单数量】(1+【可使用型号采购可下单数量】(-【库存占用累计销售已下单数量】
        //            fsalcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumsalorderqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + Convert.ToDecimal(_entry["fsumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumsalqty"]);//(200);
        //            _entry["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
        //            _entry["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
        //            var _linkObj = linkProductDelistings.Where(_ => Convert.ToString(_["id"]).Equals(Convert.ToString(dzpItem["id"]))).FirstOrDefault();
        //            if (_linkObj != null)
        //            {
        //                (_linkObj["fentity"] as DynamicObjectCollection).ForEach(c =>
        //                {
        //                    if (Convert.ToString(c["id"]).Equals(Convert.ToString(_entry["id"])))
        //                    {
        //                        c["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //                        c["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //                        c["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //                        c["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
        //                        c["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
        //                        c["finvoccupysumpurqty"] = exceedQty;//库存占用累计销售已下单数量（0
        //                    }
        //                });
        //            }
        //        }

        //        foreach (var bzpItem in _bzp)
        //        {
        //            decimal _fpurcanplaceorderqty = 0;
        //            if (_dzp.Count > 0)
        //            {
        //                var _dzpEntry = (_dzp.FirstOrDefault()?["fentity"] as DynamicObjectCollection)
        //             .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
        //                _fpurcanplaceorderqty = Convert.ToDecimal(_dzpEntry["fpurcanplaceorderqty"]);
        //            }
        //            var _entry = (bzpItem["fentity"] as DynamicObjectCollection)
        //            .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();

        //            _entry["fsumpurqty"] = _fpurcanplaceorderqty;//可使用型号采购可下单数量

        //            //【采购可下单数量】=【预估可下单采购数量】-【累计采购已下单数量】+【占用型号累计采购已下单数量】+【可使用型号采购可下单数量】-【库存占用累计采购已下单数量】
        //            fpurcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + _fpurcanplaceorderqty - Convert.ToDecimal(_entry["finvoccupysumpurqty"]);//(200)
        //                                                                                                                                                                                                                                                          //计算规则：【销售可下单数量】=【预估采购可下单数量】(100-【累计销售已下单数量】(78-【累计采购已下单数量】(201+【占用型号累计采购已下单数量】(1+【可使用型号采购可下单数量】(-【库存占用累计销售已下单数量】
        //            fsalcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumsalorderqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + _fpurcanplaceorderqty - Convert.ToDecimal(_entry["finvoccupysumsalqty"]);//(200);
        //            _entry["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
        //            _entry["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
        //        }
        //    }
        //    return matchDelist;
        //}




        ///// <summary>
        ///// 退市增减计算
        ///// </summary>
        ///// <param name="matchDelist">当前涉及的退市清单</param>
        ///// <param name="linkProductDelistings">关联占用的退市清单</param>
        ///// <param name="qty">增减数量</param>
        ///// <param name="calType">计算类型 1:加 2:减</param>
        ///// <returns>返回计算后的结果</returns>
        //public static List<DynamicObject> UpdateDelistingQty(List<DynamicObject> matchDelist, List<DynamicObject> linkProductDelistings, decimal qty, int calType = 0)
        //{
        //    foreach (var delistObj in matchDelist)
        //    {
        //        bool isstandtype = !Convert.ToBoolean(delistObj["ftype"]);


        //        var productDelistings = linkProductDelistings.Where(a =>
        //                 Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                 !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //        if (isstandtype)
        //        {
        //            //当前是标准品，查定制品的退市清单，包含两种
        //            /*
        //             * 专用材料能匹配上，直接加进来
        //             * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
        //             */
        //            //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
        //            var _dynamics1 = linkProductDelistings.Where(a =>
        //                Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
        //                !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                Convert.ToBoolean(a["ftype"]) &&
        //                !(a["fpropentry"] as DynamicObjectCollection).Any() &&
        //                !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //            if (_dynamics1.Count > 0)
        //            {
        //                //存在定制，需要把关联的标品的也带出来算
        //                _dynamics1 = linkProductDelistings.Where(a =>
        //                Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
        //                !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //            }
        //            productDelistings.AddRange(_dynamics1);
        //        }
        //        else
        //        {
        //            //当前是定制品，查定制品的退市清单，包含两种
        //            /*
        //             * 专用材料能匹配上，直接加进来
        //             * 专用材料匹配不上，查类型相同，属性明细为空，并且商品是定制
        //             */
        //            //型号匹配,专用材料不匹配，并且定制品，并且属性明细为空
        //            var _dynamics1 = linkProductDelistings.Where(a =>
        //                Convert.ToString(a["fseltypeid"]).Equals(Convert.ToString(delistObj["fseltypeid"])) &&
        //                !Convert.ToString(a["fspecmaterial"]).Equals(Convert.ToString(delistObj["fspecmaterial"])) &&
        //                !Convert.ToBoolean(a["ftype"]) &&
        //                !Convert.ToString(a["fhqid"]).Equals(Convert.ToString(delistObj["fhqid"]))).ToList();
        //            productDelistings.AddRange(_dynamics1);

        //        }
        //        var _bzp = new List<DynamicObject>();
        //        var _dzp = new List<DynamicObject>();

        //        //如果是标准品，存在其他标准品也是一样的专用材料
        //        productDelistings.ForEach(a =>
        //        {
        //            if (!Convert.ToBoolean(a["ftype"]))
        //                _bzp.Add(a);
        //            else
        //                _dzp.Add(a);
        //        });
        //        if (isstandtype)
        //            _bzp.Add(delistObj);
        //        else
        //            _dzp.Add(delistObj);

        //        //计算【累计采购已下单数量】
        //        var fsumpurorderqty = 0M;
        //        //【占用型号累计采购已下单数量】
        //        var fmatoccupysumpurqty = 0M;
        //        //【可使用型号采购可下单数量】
        //        var fsumpurqty = 0M;
        //        //【库存占用累计采购已下单数量】
        //        var finvoccupysumpurqty = 0M;
        //        ///【库存占用累计销售已下单数量】
        //        var finvoccupysumsalqty = 0M;
        //        ///【销售可下单数量】
        //        var fsalcanplaceorderqty = 0M;
        //        ///【采购可下单数量】
        //        var fpurcanplaceorderqty = 0M;
        //        //计算【累计销售已下单数量】
        //        var fsumsalorderqty = 0M;
        //        var fsumsalorderqtyreal = 0M;
        //        decimal exceedQty = 0;//超出数量
        //        foreach (var bzpItem in _bzp)
        //        {
        //            var productId = Convert.ToString(bzpItem["fmaterialid"]);
        //            var seltypeId = Convert.ToString(bzpItem["fseltypeid"]);

        //            var _entry = (bzpItem["fentity"] as DynamicObjectCollection)
        //            .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
        //            if (!Convert.ToString(bzpItem["id"]).Equals(Convert.ToString(delistObj["id"])))
        //            {
        //                //其它退市清单，只做记录，不做更新
        //                exceedQty += Convert.ToDecimal(_entry["fmatoccupysumpurqty"]);
        //                continue;
        //            }

        //            var festimateqty = Convert.ToDecimal(_entry["festimateqty"]);//预估可下单采购量
        //            var sumpurorderqty = Convert.ToDecimal(_entry["fsumpurorderqty"]);//预估可下单采购量
        //            fsumpurorderqty = sumpurorderqty;
        //            fsumsalorderqty = Convert.ToDecimal(_entry["fsumsalorderqty"]);
        //            fsumsalorderqtyreal = Convert.ToDecimal(_entry["fsumsalorderqtyreal"]);
        //            fsumpurorderqty = qty;//计算【累计采购已下单数量】
        //            if (calType == 2)
        //            {
        //                fsumpurorderqty = fsumpurorderqty * -1;
        //            }
        //            fsumpurorderqty = sumpurorderqty + fsumpurorderqty;


        //            decimal dzkylqty = 0;//定制可用量，也叫【可使用型号采购可下单数量】
        //            dzkylqty = _dzp.Select(a => (a["fentity"] as DynamicObjectCollection).Where(b => Convert.ToBoolean(b["fenable"])).Select(c => Convert.ToDecimal(c["festimateqty"])).Sum()).Sum();
        //            //可用总量=成品可用量+定制可用量（300
        //            decimal kysumqty = dzkylqty + festimateqty;
        //            //下发数量>=累计采购已下单数量。则不需要占用定制
        //            if (festimateqty >= fsumpurorderqty)
        //            {
        //                fmatoccupysumpurqty = 0;
        //                fsumpurqty = dzkylqty;
        //            }
        //            else if (fsumpurorderqty > kysumqty)//累计采购已下单数量>可下单总量，爆单了
        //            {
        //                //若下发一百，可用总量300，查出来使用360，则占用200，剩下的可下单就0
        //                fmatoccupysumpurqty = dzkylqty == 0 ? fsumpurorderqty - festimateqty : dzkylqty;//全占用，剩下的都在自己这里体现
        //                fsumpurqty = 0;
        //                dzkylqty = 0;
        //            }
        //            else
        //            {
        //                //若下发一百，查出来使用160，则占用60
        //                fmatoccupysumpurqty = fsumpurorderqty - festimateqty;//占用1个
        //                fsumpurqty = kysumqty - fsumpurorderqty;//【可使用型号采购可下单数量】=总量（300）-采购已下单（160）=140
        //                dzkylqty = kysumqty - fsumpurorderqty;
        //            }

        //            _entry["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //            _entry["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //            _entry["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //            _entry["fpurqtyupdatetime"] = DateTime.Now;//采购可下单数量更新时间
        //            _entry["fsalqtyupdatetime"] = DateTime.Now;//销售可下单数量更新时间
        //            _entry["fenable"] = 1;//是否有效行
        //            _entry["fmodifystatus"] = "2";//初始更新情况
        //            _entry["fmatoccupysumsalqty"] = 0;//占用型号累计销售已下单数量
        //            _entry["finvoccupysumsalqty"] = finvoccupysumsalqty;//库存占用累计销售已下单数量（0
        //            _entry["fmatoccupysumpurqty"] = fmatoccupysumpurqty;//占用型号累计采购已下单数量
        //            var _linkObj = linkProductDelistings.Where(_ => Convert.ToString(_["id"]).Equals(Convert.ToString(bzpItem["id"]))).FirstOrDefault();
        //            if (_linkObj != null)
        //            {
        //                (_linkObj["fentity"] as DynamicObjectCollection).ForEach(c =>
        //                {
        //                    if (Convert.ToString(c["id"]).Equals(Convert.ToString(_entry["id"])))
        //                    {
        //                        c["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //                        c["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //                        c["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //                        c["finvoccupysumsalqty"] = finvoccupysumsalqty;//库存占用累计销售已下单数量
        //                        c["fmatoccupysumpurqty"] = fmatoccupysumpurqty;//占用型号累计采购已下单数量
        //                    }
        //                });
        //            }
        //            exceedQty += fmatoccupysumpurqty;
        //            continue;
        //        }

        //        foreach (var dzpItem in _dzp)
        //        {
        //            var _entry = (dzpItem["fentity"] as DynamicObjectCollection)
        //            .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
        //            var _propentry = (dzpItem["fpropentry"] as DynamicObjectCollection)
        //            .FirstOrDefault();
        //            var propEntrys = (dzpItem["fpropentry"] as DynamicObjectCollection).Select(a => Convert.ToString(a["fpropvalueid"])).ToList();
        //            var productId = Convert.ToString(dzpItem["fmaterialid"]);
        //            var seltypeId = Convert.ToString(dzpItem["fseltypeid"]);

        //            var festimateqty = Convert.ToDecimal(_entry["festimateqty"]);//预估可下单采购量
        //            var sumpurorderqty = Convert.ToDecimal(_entry["fsumpurorderqty"]);//预估可下单采购量
        //            fsumpurorderqty = sumpurorderqty;
        //            fsumsalorderqty = Convert.ToDecimal(_entry["fsumsalorderqty"]);
        //            fsumsalorderqtyreal = Convert.ToDecimal(_entry["fsumsalorderqtyreal"]);
        //            if (Convert.ToString(dzpItem["id"]).Equals(Convert.ToString(delistObj["id"])))
        //            {
        //                fsumpurorderqty = qty;//计算【累计采购已下单数量】
        //                if (calType == 2)
        //                {
        //                    fsumpurorderqty = fsumpurorderqty * -1;
        //                }
        //                fsumpurorderqty = sumpurorderqty + fsumpurorderqty;

        //                _entry["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //                _entry["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //                _entry["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //                _entry["fpurqtyupdatetime"] = DateTime.Now;//采购可下单数量更新时间
        //                _entry["fsalqtyupdatetime"] = DateTime.Now;//销售可下单数量更新时间
        //                _entry["fenable"] = 1;//是否有效行
        //                _entry["fmodifystatus"] = "2";//初始更新情况
        //                _entry["fmatoccupysumsalqty"] = 0;//占用型号累计销售已下单数量
        //            }


        //            _entry["finvoccupysumpurqty"] = exceedQty;//库存占用累计销售已下单数量（0
        //                                                      //【采购可下单数量】=【预估可下单采购数量】-【累计采购已下单数量】+【占用型号累计采购已下单数量】+【可使用型号采购可下单数量】-【库存占用累计采购已下单数量】
        //            fpurcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + Convert.ToDecimal(_entry["fsumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumpurqty"]);//(200)
        //                                                                                                                                                                                                                                                                            //计算规则：【销售可下单数量】=【预估采购可下单数量】(100-【累计销售已下单数量】(78-【累计采购已下单数量】(201+【占用型号累计采购已下单数量】(1+【可使用型号采购可下单数量】(-【库存占用累计销售已下单数量】
        //            fsalcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumsalorderqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + Convert.ToDecimal(_entry["fsumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumpurqty"]) - Convert.ToDecimal(_entry["finvoccupysumsalqty"]);//(200);
        //            _entry["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
        //            _entry["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
        //            var _linkObj = linkProductDelistings.Where(_ => Convert.ToString(_["id"]).Equals(Convert.ToString(dzpItem["id"]))).FirstOrDefault();
        //            if (_linkObj != null)
        //            {
        //                (_linkObj["fentity"] as DynamicObjectCollection).ForEach(c =>
        //                {
        //                    if (Convert.ToString(c["id"]).Equals(Convert.ToString(_entry["id"])))
        //                    {
        //                        c["fsumpurorderqty"] = fsumpurorderqty;//累计采购已下单数量
        //                        c["fsumsalorderqty"] = fsumsalorderqty;//累计销售已下单数量
        //                        c["fsumsalorderqtyreal"] = fsumsalorderqtyreal;//累计销售已下单数量-real
        //                        c["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
        //                        c["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
        //                        c["finvoccupysumpurqty"] = exceedQty;//库存占用累计销售已下单数量（0
        //                    }
        //                });
        //            }
        //        }

        //        foreach (var bzpItem in _bzp)
        //        {
        //            decimal _fpurcanplaceorderqty = 0;
        //            if (_dzp.Count > 0)
        //            {
        //                var _dzpEntry = (_dzp.FirstOrDefault()?["fentity"] as DynamicObjectCollection)
        //             .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();
        //                _fpurcanplaceorderqty = Convert.ToDecimal(_dzpEntry["fpurcanplaceorderqty"]);
        //            }
        //            var _entry = (bzpItem["fentity"] as DynamicObjectCollection)
        //            .Where(b => Convert.ToBoolean(b["fenable"])).FirstOrDefault();

        //            _entry["fsumpurqty"] = _fpurcanplaceorderqty;//可使用型号采购可下单数量

        //            //【采购可下单数量】=【预估可下单采购数量】-【累计采购已下单数量】+【占用型号累计采购已下单数量】+【可使用型号采购可下单数量】-【库存占用累计采购已下单数量】
        //            fpurcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + _fpurcanplaceorderqty - Convert.ToDecimal(_entry["finvoccupysumpurqty"]);//(200)
        //                                                                                                                                                                                                                                                          //计算规则：【销售可下单数量】=【预估采购可下单数量】(100-【累计销售已下单数量】(78-【累计采购已下单数量】(201+【占用型号累计采购已下单数量】(1+【可使用型号采购可下单数量】(-【库存占用累计销售已下单数量】
        //            fsalcanplaceorderqty = Convert.ToDecimal(_entry["festimateqty"]) - Convert.ToDecimal(_entry["fsumsalorderqty"]) - Convert.ToDecimal(_entry["fsumpurorderqty"]) + Convert.ToDecimal(_entry["fmatoccupysumpurqty"]) + _fpurcanplaceorderqty - Convert.ToDecimal(_entry["finvoccupysumsalqty"]);//(200);
        //            _entry["fpurcanplaceorderqty"] = fpurcanplaceorderqty;//采购可下单数量
        //            _entry["fsalcanplaceorderqty"] = fsalcanplaceorderqty;//销售可下单数量
        //        }
        //    }
        //    return matchDelist;
        //}
        #endregion


        public class DelistingDataModel
        {
            /// <summary>
            /// 业务对象名称
            /// </summary>
            public string formId { get; set; }
            /// <summary>
            /// 业务对象名称
            /// </summary>
            public string formName { get; set; }
            /// <summary>
            /// 商品对象
            /// </summary>
            public DynamicObject Material { get; set; }
            /// <summary>
            /// 辅助属性
            /// </summary>
            public string Attrinfo { get; set; }
            /// <summary>
            /// 累计销售已下单数量
            /// =销售数量-销售已出库数+销售退换数量
            /// </summary>
            public decimal Qty { get; set; }
            /// <summary>
            /// 累计销售已下单数量过渡计算值
            /// ①依据操作前【销售数量】做计算，得到：
            /// A=销售合同.销售数量-【有效出库及转采购并提交总部或终审数量】
            /// ②依据操作后【销售数量】做计算，得到：
            /// B=销售合同.销售数量-【有效出库及转采购并提交总部或终审数量】
            /// ③将{B-A}
            /// 得到的数值，更新至退市表。
            /// </summary>
            public decimal RealQty { get; set; }
            /// <summary>
            /// 计算类型 1:加 2:减
            /// </summary>
            public int calType { get; set; }
            /// <summary>
            /// 操作状态；1：新增；2：修改
            /// </summary>
            public int datType { get; set; }
            /// <summary>
            /// 快照数量
            /// </summary>
            public decimal snapbizQty { get; set; }
            /// <summary>
            /// 单据数量
            /// </summary>
            public decimal orderbizQty { get; set; }
            /// <summary>
            /// 采购提交总部或驳回快照数量
            /// </summary>
            public decimal snapPurQty { get; set; }
            /// <summary>
            /// 采购提交总部或驳回单据数量
            /// </summary>
            public decimal orderPurQty { get; set; }
            /// <summary>
            /// 出库数量
            /// </summary>
            public decimal snapOutQty { get; set; }
            /// <summary>
            /// 出库数量
            /// </summary>
            public decimal orderOutQty { get; set; }
            /// <summary>
            /// 退货数量
            /// </summary>
            public decimal snapReturnQty { get; set; }
            /// <summary>
            /// 退货数量
            /// </summary>
            public decimal orderReturnQty { get; set; }
            /// <summary>
            /// 操作编码
            /// </summary>
            public string opCode { get; set; }
            public string remark { get; set; }
        }

        public enum CalType
        {
            /// <summary>
            /// 增加
            /// </summary>
            [Description("增加")]
            Add = 1,
            /// <summary>
            /// 减少
            /// </summary>
            [Description("减少")]
            Reduce = 2,
        }
        public enum DataType
        {
            /// <summary>
            /// 新增
            /// </summary>
            [Description("新增")]
            Add = 1,
            /// <summary>
            /// 修改
            /// </summary>
            [Description("修改")]
            Modify = 2,
            /// <summary>
            /// 辅助属性修改
            /// </summary>
            [Description("辅助属性修改")]
            AttrinfoModify = 3,
            /// <summary>
            /// 明细行删除后创建
            /// </summary>
            [Description("明细行删除后创建")]
            EntryModify = 4,
            /// <summary>
            /// 商品修改
            /// </summary>
            [Description("商品修改")]
            MaterialModify = 5,
            /// <summary>
            /// 明细行删除
            /// </summary>
            [Description("明细行删除")]
            EntryDelete = 6,
        }

        /// <summary>
        /// 日志记录
        /// </summary>
        public class ProductDelistingLogger
        {
            public string FormId { get; set; }

            private StringBuilder content;

            public ProductDelistingLogger(string formId)
            {
                this.FormId = formId;
                this.content = new StringBuilder();
            }

            public void AppendLine(string content = null)
            {
                this.content.AppendLine(content);
            }
            public void AppendLine(DynamicObject item, string content = null)
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("---------开始---------");
                sb.Append("fmaterialid：" + Convert.ToString(item["fmaterialid"]));
                sb.Append("fseltypeid：" + Convert.ToString(item["fseltypeid"]));
                sb.Append("fspecmaterial：" + Convert.ToString(item["fspecmaterial"]));
                sb.Append("fhqid：" + Convert.ToString(item["fhqid"]));
                foreach (var item2 in (item["fpropentry"] as DynamicObjectCollection))
                {
                    if (!string.IsNullOrWhiteSpace(Convert.ToString(item2["fpropvalueid"])))
                    {
                        sb.Append("fpropvalueid：" + Convert.ToString(item2["fpropvalueid"]));
                    }
                }
                foreach (var item2 in (item["fentity"] as DynamicObjectCollection))
                {
                    if (Convert.ToBoolean(item2["fenable"]))
                    {
                        sb.Append("fentity：" + JsonConvert.SerializeObject(item2));
                    }
                }
                sb.AppendLine("---------结束---------");
                this.content.AppendLine(sb.ToString());
            }
            public void AppendLine(IEnumerable<DynamicObject> objs, string content = null)
            {
                //只记录表头，属性，启用行，否则数据量太大
                StringBuilder sb = new StringBuilder();
                foreach (var item in objs)
                {
                    sb.AppendLine("---------开始---------");
                    sb.Append("fmaterialid：" + Convert.ToString(item["fmaterialid"]));
                    sb.Append("fseltypeid：" + Convert.ToString(item["fseltypeid"]));
                    sb.Append("fspecmaterial：" + Convert.ToString(item["fspecmaterial"]));
                    sb.Append("fhqid：" + Convert.ToString(item["fhqid"]));
                    foreach (var item2 in (item["fpropentry"] as DynamicObjectCollection))
                    {
                        if (!string.IsNullOrWhiteSpace(Convert.ToString(item2["fpropvalueid"])))
                        {
                            sb.Append("fpropvalueid：" + Convert.ToString(item2["fpropvalueid"]));
                        }
                    }
                    foreach (var item2 in (item["fentity"] as DynamicObjectCollection))
                    {
                        if (Convert.ToBoolean(item2["fenable"]))
                        {
                            sb.Append("fentity：" + JsonConvert.SerializeObject(item2));
                        }
                    }
                    sb.AppendLine("---------结束---------");
                }
                this.content.AppendLine(sb.ToString());
            }

            public void Write()
            {
                try
                {
                    string logName = $"ProductDelistingLog/{FormId}";

                    DebugUtil.WriteLogToFile(this.content.ToString(), logName);
                }
                catch (Exception e)
                {
                }
            }
        }
    }
}

