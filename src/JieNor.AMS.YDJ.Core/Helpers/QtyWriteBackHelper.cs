using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Core.Helpers
{
    /// <summary>
    /// 销售合同数量反写辅助类
    /// </summary>
    public class OrderQtyWriteBackHelper
    {
        /// <summary>
        /// 反写销售合同【已转采购数】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="targetForm"></param>
        /// <param name="targetDatas"></param>
        /// <param name="targetOpCode"></param>
        /// <param name="removeRowSrcIds"></param>
        public static void WriteBackTransPurQty(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            string targetOpCode,
            List<string> removeRowSrcIds = null
            )
        {
            if (targetDatas == null || !targetDatas.Any()) return;

            QtyWriteBackLogger logger = new QtyWriteBackLogger("ydj_order", "ftranspurqty");
            logger.AppendLine($"目标单据类型：{targetForm.Id}");
            logger.AppendLine($"操作码：{targetOpCode}");
            logger.AppendLine($"当前经销商：{userCtx.Company}");
            logger.AppendLine($"目标单据：{targetDatas.Select(s => $"{s["id"]}\t{s["fbillno"]}").JoinEx(",", false)}");
            //商品明细行新增【已转采购数】：按商品明细行，汇总统计下游【作废状态】=“否”的所有《采购订单》的【采购数量】。当《采购订单》在执行<保存><删除><作废><反作废>时，需自动更新该字段。

            var orderEntryIds = GetOrderEntryIds(userCtx, targetForm, targetDatas, removeRowSrcIds);
            if (orderEntryIds == null || !orderEntryIds.Any()) return;

            var sql = BuildWriteBackOrderTransPurQty(orderEntryIds);

            logger.AppendLine($"执行脚本：{sql.Key}");

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            var exeCount = dbServiceEx.Execute(userCtx, sql.Key, sql.Value);

            logger.AppendLine($"执行脚本影响数量：{exeCount}");
            logger.Write();
        }

        /// <summary>
        /// 反写销售合同【已推出库数】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="targetForm"></param>
        /// <param name="targetDatas"></param>
        /// <param name="targetOpCode"></param>
        /// <param name="removeRowSrcIds"></param>
        public static void WriteBackTransOutQty(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            string targetOpCode,
            List<string> removeRowSrcIds = null
            )
        {
            if (targetDatas == null || !targetDatas.Any()) return;

            QtyWriteBackLogger logger = new QtyWriteBackLogger("ydj_order", "ftransoutqty");
            logger.AppendLine($"目标单据类型：{targetForm.Id}");
            logger.AppendLine($"操作码：{targetOpCode}");
            logger.AppendLine($"当前经销商：{userCtx.Company}");
            logger.AppendLine($"目标单据：{targetDatas.Select(s => $"{s["id"]}\t{s["fbillno"]}").JoinEx(",", false)}");
            //商品明细行新增【已推出库数】：按商品明细行，汇总统计下游【作废状态】=“否”的所有《销售出库单》的【实发数量】。当《销售出库单》在执行<保存><删除><作废><反作废>时，需自动更新该字段。

            var orderEntryIds = GetOrderEntryIds(userCtx, targetForm, targetDatas, removeRowSrcIds);
            if (orderEntryIds == null || !orderEntryIds.Any()) return;

            var sql = BuildWriteBackOrderTransOutQty(orderEntryIds);

            logger.AppendLine($"执行脚本：{sql.Key}");

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            var exeCount = dbServiceEx.Execute(userCtx, sql.Key, sql.Value);

            logger.AppendLine($"执行脚本影响数量：{exeCount}");
            logger.Write();
        }

        /// <summary>
        /// 
        /// </summary>
        static List<string> _WriteBackHqDeliverQtyFormIds = new List<string> { "stk_postockin", "bcm_receptionscantask" };

        /// <summary>
        /// 反写采购订单和销售合同【总部已发货数】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="targetForm">目前单据：采购入库单或收货扫描任务</param>
        /// <param name="targetDatas">目标单据数据包</param>
        /// <param name="targetOpCode"></param>
        /// <param name="removeRowSrcIds">删除行的销售合同明细ID</param>
        public static void WriteBackHqDeliveryQty(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            string targetOpCode,
            List<string> removeRowSrcIds = null)
        {
            if (targetDatas == null || !targetDatas.Any()) return;

            // 目前支持采购入库单及收货扫描任务
            if (!_WriteBackHqDeliverQtyFormIds.Contains(targetForm.Id, StringComparer.OrdinalIgnoreCase)) return;

            QtyWriteBackLogger logger = new QtyWriteBackLogger("ydj_purchaseorder", "fhqdeliveryqty");
            logger.AppendLine($"目标单据类型：{targetForm.Id}");
            logger.AppendLine($"操作码：{targetOpCode}");
            logger.AppendLine($"当前经销商：{userCtx.Company}");
            logger.AppendLine($"目标单据：{targetDatas.Select(s => $"{s["id"]}\t{s["fbillno"]}").JoinEx(",", false)}");

            var poEntryIds = new List<string>();
            //var soEntryIds = new List<string>();
            if (removeRowSrcIds != null && removeRowSrcIds.Any())
            {
                //删除的明细行也要进行反写
                poEntryIds.AddRange(removeRowSrcIds);
                //soEntryIds.AddRange(soEntryIds);

                logger.AppendLine($"删除行：{removeRowSrcIds?.JoinEx(",", false)}");
            }

            // 采购入库单
            if (targetForm.Id.EqualsIgnoreCase("stk_postockin"))
            {
                var allEntrys = targetDatas.SelectMany(s => s["fentity"] as DynamicObjectCollection);
                var allPoEntryIds = allEntrys.Select(s => Convert.ToString(s["fpoorderentryid"])).Where(s => !s.IsNullOrEmptyOrWhiteSpace());
                poEntryIds.AddRange(allPoEntryIds);
            }
            // 收货扫描任务
            else if (targetForm.Id.EqualsIgnoreCase("bcm_receptionscantask"))
            {
                /*
                 * stk_otherstockin
                 * stk_sostockreturn
                 * ydj_order
                 * ydj_purchaseorder
                 */

                var allEntrys = targetDatas.SelectMany(s => s["ftaskentity"] as DynamicObjectCollection);
                var allPoEntryIds = allEntrys.Where(s => Convert.ToString(s["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder"))
                    .Select(s => Convert.ToString(s["fsourceentryid"]))
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace());
                poEntryIds.AddRange(allPoEntryIds);
            }

            if (!poEntryIds.Any())
            {
                logger.AppendLine("采购订单明细行：无");
                logger.Write();
                return;
            }

            // 去重
            poEntryIds = poEntryIds.Distinct().ToList();
            logger.AppendLine($"采购订单明细行：{poEntryIds.JoinEx(",", false)}");

            /*
             * 63126 【内部提单-慕思现场-反写异常】总部已发货数反写场景补充
             * http://pmp.jienor.com/www/index.php?m=task&f=view&taskID=63126
             * 一.合同，就依据所有下游关联的采购订单【总部已发货数】累计计算。
             * 二.采购订单这个细分场景
             * 1.有收货任务，全部基于收货任务入库。
             * 2.有收货任务，部分基于收货任务入库，部分手工入库。
             * 3.有收货任务，全部手工生成入库单。
             * 4.无收货任务，手工入库或者总部下发生成入库单。
             * 备注：一个采购订单明细行可以对应多个不同收货任务的行。
             * 系统实现逻辑：
             * 采购订单行里面有【累计下推收货作业数量】的数量字段，用这个数量去与当前行对应下游关联非作废采购入库单的【实收数量】汇总，进行比对，谁大取谁。
             */

            var dbService = userCtx.Container.GetService<IDBService>();
            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();

            List<string> sqlTexts = new List<string>();
            if (poEntryIds.Count > 50)
            {
                //用临时表关联查询
                var tempTable = dbService.CreateTempTableWithDataList(userCtx, poEntryIds);
                sqlTexts.Add($@"/*dialect*/
update pe set fbizhqdeliveryqty=(case when pe.fsumpushreceiveqty>ISNULL(se.fbizqty, 0) then pe.fsumpushreceiveqty else ISNULL(se.fbizqty, 0) end)
from T_YDJ_POORDERENTRY pe
left join 
(
    select te.fpoorderinterid, te.fpoorderentryid, SUM(fbizqty) as fbizqty, SUM(fqty) as fqty
    from t_stk_postockin t
    inner join t_stk_postockinentry te on t.fid=te.fid
    where t.fcancelstatus='0'
    group by te.fpoorderinterid, te.fpoorderentryid
) se on pe.fid=se.fpoorderinterid and pe.fentryid=se.fpoorderentryid
where exists(select 1 from {tempTable} t where t.fid=pe.fentryid)
");

                sqlTexts.Add($@"/*dialect*/
update oe set fbizhqdeliveryqty=pe.fbizhqdeliveryqty, fhqdeliveryqty=pe.fbizhqdeliveryqty
from T_YDJ_ORDERENTRY oe
inner join 
(
    select te.fsoorderinterid, te.fsoorderentryid, SUM(fbizhqdeliveryqty) as fbizhqdeliveryqty
    from t_ydj_purchaseorder t
    inner join t_ydj_poorderentry te on t.fid=te.fid
    where exists(select 1 from {tempTable} tmp where tmp.fid=te.fentryid) and t.fcancelstatus='0'
    group by te.fsoorderinterid, te.fsoorderentryid
) pe on oe.fid=pe.fsoorderinterid and oe.fentryid=pe.fsoorderentryid
");
            }
            else
            {
                sqlTexts.Add($@"/*dialect*/
update pe set fbizhqdeliveryqty=(case when pe.fsumpushreceiveqty>ISNULL(se.fbizqty, 0) then pe.fsumpushreceiveqty else ISNULL(se.fbizqty, 0) end)
from T_YDJ_POORDERENTRY pe
left join 
(
    select te.fpoorderinterid, te.fpoorderentryid, SUM(fbizqty) as fbizqty, SUM(fqty) as fqty
    from t_stk_postockin t
    inner join t_stk_postockinentry te on t.fid=te.fid
    where t.fcancelstatus='0'
    group by te.fpoorderinterid, te.fpoorderentryid
) se on pe.fid=se.fpoorderinterid and pe.fentryid=se.fpoorderentryid
where pe.fentryid in ({poEntryIds.JoinEx(",", true)})
");

                sqlTexts.Add($@"/*dialect*/
update oe set fbizhqdeliveryqty=pe.fbizhqdeliveryqty, fhqdeliveryqty=pe.fbizhqdeliveryqty
from T_YDJ_ORDERENTRY oe
inner join 
(
    select te.fsoorderinterid, te.fsoorderentryid, SUM(fbizhqdeliveryqty) as fbizhqdeliveryqty
    from t_ydj_purchaseorder t
    inner join t_ydj_poorderentry te on t.fid=te.fid
    where te.fentryid in ({poEntryIds.JoinEx(",", true)}) and t.fcancelstatus='0'
    group by te.fsoorderinterid, te.fsoorderentryid
) pe on oe.fid=pe.fsoorderinterid and oe.fentryid=pe.fsoorderentryid
");
            }

            dbServiceEx.ExecuteBatch(userCtx, sqlTexts);

            foreach (var sqlText in sqlTexts)
            {
                logger.AppendLine(sqlText);
            }
            logger.Write();
        }

        /// <summary>
        /// 反写销售合同【采购已入库数】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="targetForm"></param>
        /// <param name="targetDatas">目标单据数据包，如：采购入库单、采购退货单。</param>
        /// <param name="targetOpCode"></param>
        public static void WriteBackPurInQty(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            string targetOpCode)
        {
            // 《采购入库单》<审核>后增加反写逻辑
            //  反写《销售合同》的【已采购入库数】, 根据《采购入库单》对应商品行的【实收数量】进行累加反写上上游《销售合同》

            // 《采购入库单》<反审核>后增加反写逻辑
            // 反写《销售合同》的【已采购入库数】, 根据《采购入库单》对应商品行的【实收数量】进行扣减反写上上游《销售合同》

            // 《采购退货单》<审核>后增加反写逻辑
            // 反写《销售合同》的【已采购入库数】, 根据《采购退货单》对应商品行的【采购实退数量】进行扣减反写上上游《销售合同》

            // 《采购退货单》<反审核>后增加反写逻辑
            // 反写《销售合同》的【已采购入库数】, 根据《采购退货单》对应商品行的【采购实退数量】进行累加反写上上游《销售合同》

            if (targetDatas == null || !targetDatas.Any()) return;

            QtyWriteBackLogger logger = new QtyWriteBackLogger("ydj_order", "fpurinqty");
            logger.AppendLine($"目标单据类型：{targetForm.Id}");
            logger.AppendLine($"操作码：{targetOpCode}");
            logger.AppendLine($"当前经销商：{userCtx.Company}");
            logger.AppendLine($"目标单据：{targetDatas.Select(s => $"{s["id"]}\t{s["fbillno"]}").JoinEx(",", false)}");

            // 销售合同明细ID
            var orderEntryIds = GetOrderEntryIds(userCtx, targetForm, targetDatas);
            if (orderEntryIds == null || !orderEntryIds.Any()) return;

            var sql = BuildWriteBackOrderPurInQty(orderEntryIds);

            logger.AppendLine($"执行脚本：{sql.Key}");

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            var exeCount = dbServiceEx.Execute(userCtx, sql.Key, sql.Value);

            logger.AppendLine($"执行脚本影响数量：{exeCount}");
            logger.Write();
        }

        /// <summary>
        /// 反写收货扫描任务【已作业数量】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="targetForm"></param>
        /// <param name="targetDatas">目标单据数据包，如：采购入库单、采购退货单。</param>
        /// <param name="targetOpCode"></param>
        public static void WriteBackRecTaskWorkQty(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> poStockInObj,
            string targetOpCode)
        {
            //List<DynamicObject> targetDatas = new List<DynamicObject>();

            // 1. 获取所有采购入库单明细的收货扫描任务ID
            var recTaskEntryIds = poStockInObj
                    .SelectMany(s => s["fentity"] as DynamicObjectCollection)
                    .Select(s => new
                    {
                        freceptionid = Convert.ToString(s["freceptionid"]),
                        freceptionentryid = Convert.ToString(s["freceptionentryid"])
                    })
                    .Where(x => !string.IsNullOrWhiteSpace(x.freceptionid) && !string.IsNullOrWhiteSpace(x.freceptionentryid))
                    .Distinct()
                    .ToList();
            if (!recTaskEntryIds.Any()) return;

            // 只处理收货扫描任务单
            //if (!targetForm.Id.EqualsIgnoreCase("bcm_receptionscantask")) return;
            //if (targetDatas == null || !targetDatas.Any()) return;

            QtyWriteBackLogger logger = new QtyWriteBackLogger("bcm_receptionscantask", "fworkedqty");
            logger.AppendLine($"目标单据类型：bcm_receptionscantask");
            logger.AppendLine($"操作码：{targetOpCode}");
            logger.AppendLine($"当前经销商：{userCtx.Company}");
            logger.AppendLine($"目标单据：{poStockInObj.Select(s => $"{s["id"]}\t{s["fbillno"]}").JoinEx(",", false)}");

            // 1. 统计所有收货扫描任务明细ID
            var taskEntryIds = recTaskEntryIds.Select(a => a.freceptionentryid).ToList();
            if (!taskEntryIds.Any()) return;
            var taskId= recTaskEntryIds.Select(a => a.freceptionid).FirstOrDefault();

            // 2. 更新已作业数量（统计所有已审核采购入库单明细的实收数量，按收货扫描任务明细ID分组）
            var sqlUpdateWorkQty = $@"/*dialect*/
                    update te set fworkedqty=ISNULL(x.fqty,0)
                    from t_bcm_rescantaskentity te
                    left join (
                        select te.freceptionid,te.freceptionentryid, sum(te.fbizqty) as fqty
                        from t_stk_postockin t
                        inner join t_stk_postockinentry te on t.fid=te.fid
                        where t.fstatus='E' and te.freceptionentryid in ({taskEntryIds.JoinEx("','", true)}) AND freceptionid='{taskId}'
                        group by te.freceptionid,te.freceptionentryid
                    ) x on te.fentryid=x.freceptionentryid AND x.freceptionid=te.fid
                    where te.fentryid in ({taskEntryIds.JoinEx("','", true)}) AND te.fid='{taskId}';
";

            // 3. 更新待业务数量 = 本次作业数量 - 已作业数量
            var sqlUpdateWaitQty = $@"/*dialect*/
                        update te set fwaitworkqty=ISNULL(te.fcurrworkqty,0)-ISNULL(te.fworkedqty,0)
                        from t_bcm_rescantaskentity te
                        where te.fentryid in ({taskEntryIds.JoinEx("','", true)}) AND te.fid='{taskId}';
";

            // 4. 当待业务数量=0时，收货扫描任务.任务状态=已完成
            var sqlUpdateTaskStatus = $@"/*dialect*/
                    update t set ftaskstatus=(case  when fworkedqty=0 then 'ftaskstatus_01' when fwaitworkqty>0 AND fwaitworkqty>0 then 'ftaskstatus_03' when fwaitworkqty=0 then 'ftaskstatus_04'  else 'ftaskstatus_01' end)
                    from t_bcm_receptionscantask t
                    inner join (
                        select te.fid,sum(ISNULL(te.fwaitworkqty,0)) fwaitworkqty,sum(ISNULL(te.fworkedqty,0)) fworkedqty
                        from t_bcm_rescantaskentity te
                        where  te.fid='{taskId}'
                        group by te.fid
                    ) x on t.fid=x.fid
                    where t.fid ='{taskId}' ;
";

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            dbServiceEx.ExecuteBatch(userCtx, new List<string> { sqlUpdateWorkQty, sqlUpdateWaitQty, sqlUpdateTaskStatus });

            logger.AppendLine(sqlUpdateWorkQty);
            logger.AppendLine(sqlUpdateWaitQty);
            logger.AppendLine(sqlUpdateTaskStatus);
            logger.Write();
        }


        /// <summary>
        /// 反写销售合同【未出库数】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orders"></param>
        public static void WriteBackUnStockOutQty(UserContext userCtx, IEnumerable<DynamicObject> orders)
        {
            if (orders == null || !orders.Any()) return;

            QtyWriteBackLogger logger = new QtyWriteBackLogger("ydj_order", "funstockoutqty");
            logger.AppendLine($"目标单据类型：ydj_order");
            logger.AppendLine($"当前经销商：{userCtx.Company}{(userCtx.IsSecondOrg ? "(二级经销商)" : "")}");
            logger.AppendLine($"目标单据：{orders.Select(s => $"{s["id"]}\t{s["fbillno"]}").JoinEx(",", false)}");

            if (userCtx.IsSecondOrg)
            {
                // 当前二级经销商信息
                var currentAgent = userCtx.LoadBizBillHeadDataById("bas_agent", userCtx.Company, "fisnotmgrinv");
                var isNotMgrInv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
                logger.AppendLine($"管理库存：{!isNotMgrInv}");

                if (isNotMgrInv)
                {
                    logger.Write();
                    return;
                }
            }

            var orderForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_order");

            var orderEntryIds = GetOrderEntryIds(userCtx, orderForm, orders);

            var sql = BuildWriteBackOrderUnStockOutQty(orderEntryIds);

            logger.AppendLine($"执行脚本：{sql.Key}");

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            var exeCount = dbServiceEx.Execute(userCtx, sql.Key, sql.Value);

            logger.AppendLine($"执行脚本影响数量：{exeCount}");
            logger.Write();
        }

        /// <summary>
        /// 反写销售合同【销售退换中数量】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="targetForm"></param>
        /// <param name="targetDatas"></param>
        /// <param name="removeRowSrcIds"></param>
        public static void WriteBackReturningQty(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            List<string> removeRowSrcIds = null)
        {
            //不是《销售退货单》直接返回
            if (!targetForm.Id.EqualsIgnoreCase("stk_sostockreturn")) return;
            //没有数据时不进行处理
            if (targetDatas == null || !targetDatas.Any()) return;

            var orderEntryIds = GetOrderEntryIds(userCtx, targetForm, targetDatas, removeRowSrcIds);
            if (orderEntryIds == null || !orderEntryIds.Any()) return;

            List<KeyValuePair<string, IEnumerable<SqlParam>>> sqls = new List<KeyValuePair<string, IEnumerable<SqlParam>>>();

            sqls.Add(BuildWriteBackOrderReturningQty(orderEntryIds));

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            dbServiceEx.ExecuteBatch(userCtx, sqls);
        }

        /// <summary>
        /// 获取销售合同明细内码
        /// </summary>
        /// <param name="targetForm"></param>
        /// <param name="targetDatas"></param>
        /// <param name="removeSrcIds"></param>
        /// <returns></returns>
        private static IEnumerable<string> GetOrderEntryIds(UserContext userCtx, HtmlForm targetForm, IEnumerable<DynamicObject> targetDatas, IEnumerable<string> removeSrcIds = null)
        {
            List<string> orderEntryIds = new List<string>();

            if (targetDatas == null || !targetDatas.Any()) return orderEntryIds;

            // 采购退货单特殊处理
            if (targetForm.Id.EqualsIgnoreCase("stk_postockreturn"))
            {
                var inEntryIds = targetDatas.SelectMany(s => s["fentity"] as DynamicObjectCollection)
                    .Select(s => Convert.ToString(s["fpoinstockentryid"]))
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                    .ToList();

                var orderWhere = inEntryIds.Count == 1 ? $"='{inEntryIds[0]}'" : $"in('{string.Join("','", inEntryIds)}')";
                var orderSql = $@"/*dialect*/select fsoorderentryid from t_stk_postockin t with(nolock) 
            inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid
            where te.fentryid {orderWhere}";
                var dbService = userCtx.Container.GetService<IDBService>();
                var orderEntrys = dbService.ExecuteDynamicObject(userCtx, orderSql);

                orderEntryIds.AddRange(orderEntrys
                    .Select(o => Convert.ToString(o["fsoorderentryid"]))
                    .Distinct());
            }
            else
            {
                string entityKey = "fentity", orderEntryIdFieldId = "fsoorderentryid";

                switch (targetForm.Id.ToLower())
                {
                    case "ydj_order":
                        entityKey = "fentry";
                        orderEntryIdFieldId = "id";
                        break;
                }

                var targetOrderEntryIds = targetDatas.SelectMany(s => s[entityKey] as DynamicObjectCollection)
                    .Select(s => Convert.ToString(s[orderEntryIdFieldId]))
                    .Where(s => !s.IsNullOrEmptyOrWhiteSpace());

                orderEntryIds.AddRange(targetOrderEntryIds);
            }

            if (removeSrcIds != null && removeSrcIds.Any())
            {
                orderEntryIds.AddRange(removeSrcIds);
            }

            return orderEntryIds;
        }

        /// <summary>
        /// 反写销售合同所有数量（用于取消变更时，重新计算反写数量）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orders"></param>
        public static void WriteBackOrderAllQty(UserContext userCtx, IEnumerable<DynamicObject> orders)
        {
            var sqls = new List<KeyValuePair<string, IEnumerable<SqlParam>>>();

            // 不管理库存
            var isNotMgrInv = false;

            if (userCtx.IsSecondOrg)
            {
                var agentObj = userCtx.LoadBizBillHeadDataById("bas_agent", userCtx.Company, "fisnotmgrinv");

                isNotMgrInv = Convert.ToString(agentObj?["fisnotmgrinv"]).EqualsIgnoreCase("1");
            }

            var orderForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_order");

            var orderEntryIds = GetOrderEntryIds(userCtx, orderForm, orders);

            if (!isNotMgrInv)
            {
                // 系统参数服务
                var profileService = userCtx.Container.GetService<ISystemProfile>();
                // 销售退换货中允许变更合同
                var freturnmodifyorder = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "freturnmodifyorder", false);

                sqls.Add(BuildWriteBackOrderReturningQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderReturnQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderRefundQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderOutQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderTransPurQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderPurQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderHQDeliveryQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderPurInQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderTransOutQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderUnStockOutQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderReserveQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderHQPurQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderCloseStatus(orderEntryIds, freturnmodifyorder));
            }
            else
            {
                sqls.Add(BuildNotMgrInvWriteBackOrderQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderUnStockOutQty(orderEntryIds));
                sqls.Add(BuildWriteBackOrderCloseStatus(orderEntryIds));
            }

            try
            {
                StringBuilder sb = new StringBuilder();

                sb.AppendLine($"销售合同：{orders.Select(s => Convert.ToString(s["fbillno"])).JoinEx(",", false)}");

                foreach (var item in sqls)
                {
                    foreach (var sqlParam in item.Value)
                    {
                        sb.AppendLine($"declare {sqlParam.Name} nvarchar(100);");
                        sb.AppendLine($"set {sqlParam.Name}='{sqlParam.Value}';");
                    }

                    sb.AppendLine(item.Key);
                    sb.AppendLine();
                }

                DebugUtil.WriteLogToFile(sb.ToString(), "WriteBackOrderAllQty");
            }
            catch (Exception e)
            {
            }

            if (sqls.Any())
            {
                var dbService = userCtx.Container.GetService<IDBServiceEx>();
                dbService.ExecuteBatch(userCtx, sqls);
            }
        }

        public static Dictionary<string, string> GetJDF_FHFMap(UserContext userCtx, IEnumerable<DynamicObject> orders)
        {
            /*
             * --建立销售转单后，合同的Map关系
--OD_JDF：接单方合同
--OD_FHF：发货方合同
SELECT DISTINCT OD_JDF.fid AS jdfFid,OD_FHF.fid AS fhfFid  
INTO #TempTransFidMap 
FROM T_YDJ_ORDER AS OD_JDF with(nolock)
inner join T_YDJ_ORDERENTRY AS ODE_JDF with(nolock) on OD_JDF.fid=ODE_JDF.fid
inner join T_YDJ_ORDERENTRY AS ODE_FHF with(nolock) ON ODE_JDF.fentryid=ODE_FHF.fsourceentryid_e 
INNER JOIN T_YDJ_ORDER AS OD_FHF with(nolock) on ODE_FHF.fid=OD_FHF.fid
WHERE OD_JDF.fneedtransferorder='1'
AND (OD_JDF.fid in (select fid from #tempAllOrderId) or OD_FHF.fid in (select fid from #tempAllOrderId))
AND OD_JDF.fcancelstatus='0' AND OD_FHF.fcancelstatus='0' 
             */



            return new Dictionary<string, string>();
        }

        /// <summary>
        /// 反写【销售退换中数量】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderReturningQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set fbizreturningqty=ISNULL(te.fbizqty, 0)
from t_ydj_orderentry oe
left join (
    SELECT te.fsoorderentryid, SUM(te.fbizqty) fbizqty 
    FROM T_STK_SOSTOCKRETURN t
    INNER JOIN T_STK_SOSTOCKRETURNENTRY te on te.fid=t.fid
    WHERE t.fcancelstatus='0' AND t.fstatus='D' and t.freturntype='sostockreturn_biztype_01'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【销售退换数量】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderReturnQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set freturnqty=ISNULL(te.fqty, 0), fbizreturnqty=ISNULL(te.fbizqty, 0)
from t_ydj_orderentry oe
left join (
    SELECT te.fsoorderentryid, SUM(te.fqty) fqty, SUM(te.fbizqty) fbizqty 
    FROM T_STK_SOSTOCKRETURN t WITH(NOLOCK) 
    INNER JOIN T_STK_SOSTOCKRETURNENTRY te WITH(NOLOCK) on te.fid=t.fid
    WHERE t.fstatus='E' and t.freturntype='sostockreturn_biztype_01'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【销售退款数量】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderRefundQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set frefundqty=ISNULL(te.fqty, 0), fbizrefundqty=ISNULL(te.fbizqty, 0)
from t_ydj_orderentry oe
left join (
    SELECT te.fsoorderentryid, SUM(te.fqty) fqty, SUM(te.fbizqty) fbizqty 
    FROM T_STK_SOSTOCKRETURN t WITH(NOLOCK) 
    INNER JOIN T_STK_SOSTOCKRETURNENTRY te WITH(NOLOCK) on te.fid=t.fid
    WHERE t.fstatus='E' and t.freturntype='sostockreturn_biztype_02'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【销售出库数量】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderOutQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            sqlParams.Add(new SqlParam("@fautoalterstatus", DbType.String, "%fautoalterstatus\":true%"));

            // 【合同调拨出库自动更新单据状态】为【否】
            var sql = $@"/*dialect*/
update oe set foutqty=ISNULL(te.fqty, 0), fbizoutqty=ISNULL(te.fbizqty, 0)
from t_ydj_orderentry oe
left join (
    SELECT te.fsoorderentryid, SUM(te.fbizqty) fbizqty, SUM(te.fqty) fqty
    FROM T_STK_SOSTOCKOUT AS t
	INNER JOIN T_STK_SOSTOCKOUTENTRY AS te ON t.fid=te.fid 
    WHERE t.fstatus='E' and t.fcancelstatus='0'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
inner join t_ydj_order o on o.fid=oe.fid
inner join T_BD_BILLTYPEPARAM bm on o.fbilltype=bm.fid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)}) 
AND o.fneedtransferorder='0' --非转单
and bm.fparamset LIKE @fautoalterstatus;
";

            // 【合同调拨出库自动更新单据状态】为【是】
            sql += $@"
update oe set foutqty=ISNULL(te.fqty, 0), fbizoutqty=ISNULL(te.fbizqty, 0)
from t_ydj_orderentry oe
left join (
    SELECT t.fsoorderentryid, SUM(t.fbizqty) fbizqty, SUM(t.fqty) fqty FROM
    (
        SELECT te.fsoorderentryid, te.fbizqty, te.fqty  
        FROM T_STK_SOSTOCKOUT AS t 
        INNER JOIN T_STK_SOSTOCKOUTENTRY AS te ON t.fid=te.fid 
        WHERE t.fstatus='E' and t.fcancelstatus='0'
        AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
        UNION ALL 
        SELECT te.fsourceentryid as fsoorderentryid, te.fbizqty, te.fqty  
        FROM T_STK_INVTRANSFER AS t 
        INNER JOIN T_STK_INVTRANSFERENTRY AS te ON t.fid=te.fid 
        WHERE t.fstatus='E' and t.fcancelstatus='0' and te.fsourceformid='ydj_order'
        AND te.fsourceinterid in ({orderEntryIds.JoinEx(",", true)})
    ) t
    group by t.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
inner join t_ydj_order o on o.fid=oe.fid
left join T_BD_BILLTYPEPARAM bm on o.fbilltype=bm.fid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)}) 
AND o.fneedtransferorder='0' --非转单
and (bm.fparamset is null or bm.fparamset not LIKE @fautoalterstatus);
";

            // 销售转单
            sql += $@"
update ODE_JDF set foutqty=ISNULL(ODE_FHF.fqty, 0), fbizoutqty=ISNULL(ODE_FHF.fbizqty, 0)
from t_ydj_orderentry ODE_JDF
inner join t_ydj_order OD_JDF with(nolock) on OD_JDF.fid=ODE_JDF.fid
left join (
    select ODE_FHF.fsourceentryid_e, SUM(ISNULL(OTE.fbizqty,0)) fbizqty, SUM(ISNULL(OTE.fqty,0)) fqty
    from T_YDJ_ORDER OD_FHF
    inner join T_YDJ_ORDERENTRY ODE_FHF on OD_FHF.fid=ODE_FHF.fid
    LEFT JOIN T_STK_SOSTOCKOUTENTRY AS OTE ON ODE_FHF.fentryid=OTE.fsoorderentryid 
    LEFT JOIN T_STK_SOSTOCKOUT AS OT ON OTE.fid=OT.fid 
    where ODE_FHF.fsourceentryid_e in ({orderEntryIds.JoinEx(",", true)}) 
        and OD_FHF.fcancelstatus='0'
        AND (OT.fid IS NULL OR OT.fstatus='E') 
    group by ODE_FHF.fsourceentryid_e
) ODE_FHF ON ODE_JDF.fentryid=ODE_FHF.fsourceentryid_e 
where ODE_JDF.fentryid in ({orderEntryIds.JoinEx(",", true)}) 
AND OD_JDF.fneedtransferorder='1';
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【已转采购数】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderTransPurQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set ftranspurqty=ISNULL(te.fbizqty, 0)
from t_ydj_orderentry oe
left join (
    select te.fsoorderentryid, SUM(te.fqty) fqty, SUM(te.fbizqty) fbizqty 
    FROM T_YDJ_PURCHASEORDER t
    INNER JOIN T_YDJ_POORDERENTRY te on te.fid=t.fid
    WHERE t.fcancelstatus='0'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【已采购数】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderPurQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            // 非转单
            var sql = $@"/*dialect*/
update oe set fpurqty=ISNULL(te.fqty, 0), fbizpurqty=ISNULL(te.fbizqty, 0)
from t_ydj_orderentry oe
inner join t_ydj_order o on oe.fid=o.fid
left join (
    select te.fsoorderentryid, SUM(te.fqty) fqty, SUM(te.fbizqty) fbizqty 
    FROM T_YDJ_PURCHASEORDER t
    INNER JOIN T_YDJ_POORDERENTRY te on te.fid=t.fid
    WHERE t.fcancelstatus='0' and t.fstatus='E'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)})
AND o.fneedtransferorder='0';
";

            // 销售转单
            sql += $@"
update ODE_JDF set foutqty=ISNULL(ODE_FHF.fqty, 0), fbizoutqty=ISNULL(ODE_FHF.fbizqty, 0)
from t_ydj_orderentry ODE_JDF
inner join t_ydj_order OD_JDF with(nolock) on OD_JDF.fid=ODE_JDF.fid
left join (
    select ODE_FHF.fsourceentryid_e, SUM(ISNULL(POE_FHF.fbizqty,0)) fbizqty, SUM(ISNULL(POE_FHF.fqty,0)) fqty
    from T_YDJ_ORDER OD_FHF
    inner join T_YDJ_ORDERENTRY ODE_FHF on OD_FHF.fid=ODE_FHF.fid
    LEFT JOIN T_YDJ_POORDERENTRY AS POE_FHF ON ODE_FHF.fentryid=POE_FHF.fsoorderentryid 
    LEFT JOIN T_YDJ_PURCHASEORDER AS PO_FHF ON POE_FHF.fid=PO_FHF.fid 
    where ODE_FHF.fsourceentryid_e in ({orderEntryIds.JoinEx(",", true)}) 
        and OD_FHF.fcancelstatus='0'
        AND (PO_FHF.fid IS NULL OR PO_FHF.fstatus='E') 
    group by ODE_FHF.fsourceentryid_e
) ODE_FHF ON ODE_JDF.fentryid=ODE_FHF.fsourceentryid_e 
where ODE_JDF.fentryid in ({orderEntryIds.JoinEx(",", true)}) 
AND OD_JDF.fneedtransferorder='1';
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【总部已发货数】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderHQDeliveryQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set fhqdeliveryqty=ISNULL(te.fbizhqdeliveryqty, 0), fbizhqdeliveryqty=ISNULL(te.fbizhqdeliveryqty, 0)
from t_ydj_orderentry oe
left join (
    SELECT fsoorderentryid, SUM(fbizhqdeliveryqty) as fbizhqdeliveryqty 
    FROM T_YDJ_POORDERENTRY te
    inner join T_YDJ_PURCHASEORDER t on t.fid=te.fid
    WHERE t.fcancelstatus='0' and te.fsoorderentryid<>''
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【已采购入库数】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderPurInQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set fpurinqty=ISNULL(te.fqty,0)-ISNULL(te.freturnqty,0), fbizpurinqty=ISNULL(te.fbizqty,0)-ISNULL(te.fbizreturnqty,0)
from t_ydj_orderentry oe
left join (
    SELECT te.fsoorderentryid, SUM(te.fqty) as fqty, SUM(te.fbizqty) as fbizqty, SUM(ISNULL(poreturn.fqty, 0)) as freturnqty, SUM(ISNULL(poreturn.fbizqty, 0)) as fbizreturnqty
    FROM T_STK_POSTOCKIN t
    INNER JOIN T_STK_POSTOCKINENTRY te ON t.fid=te.fid
    LEFT JOIN (
        SELECT te.fpoinstockentryid,SUM(te.fqty) fqty,SUM(te.fbizqty) fbizqty 
        FROM T_STK_POSTOCKRETURN AS t 
        INNER JOIN T_STK_POSTOCKRETURNENTRY AS te ON t.fid=te.fid 
        WHERE t.fstatus='E' and t.fcancelstatus='0'
        GROUP BY te.fpoinstockentryid
    ) AS poreturn ON te.fentryid=poreturn.fpoinstockentryid 
    WHERE t.fstatus='E' and t.fcancelstatus='0'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【已推出库数】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderTransOutQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set ftransoutqty=ISNULL(te.fbizqty,0)
from t_ydj_orderentry oe
left join (
    SELECT te.fsoorderentryid, SUM(te.fbizqty) fbizqty, SUM(te.fqty) fqty
	FROM T_STK_SOSTOCKOUT t
	INNER JOIN T_STK_SOSTOCKOUTENTRY te on t.fid=te.fid
    WHERE t.fcancelstatus='0'
    AND te.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    group by te.fsoorderentryid
) te on te.fsoorderentryid=oe.fentryid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【未出库基本数量】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderUnStockOutQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update oe set funstockoutqty=fqty-foutqty+freturnqty
from t_ydj_orderentry oe
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【预留量】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderReserveQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update t set fbizreserveqty=isnull(x.fbizqty,0) ,freserveqty=isnull(x.fqty,0)
from t_ydj_orderentry as t 
left join (
    select oe.fid,oe.fentryid,re.fqty,re.fbizqty
    from t_ydj_orderentry oe
    inner join t_stk_reservebillentry re on oe.fentryid=re.fsourceentryid  and oe.fid=re.fsourceinterid
    where oe.fentryid in ({orderEntryIds.JoinEx(",", true)})
) x on t.fid=x.fid and t.fentryid =x.fentryid 
where t.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【已提交总部或终审采购数量】
        /// </summary>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderHQPurQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update t set fhqpurqty=isnull(x.fbizqty,0)
from t_ydj_orderentry as t 
left join (
    select poe.fsoorderentryid as fentryid, SUM(poe.fbizqty) fbizqty 
    from T_YDJ_POORDERENTRY poe with(nolock)
    inner join t_ydj_purchaseorder po with(nolock) on poe.fid=po.fid and po.fcancelstatus=0 and po.fhqderstatus in ('03','02')
    where poe.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})
    GROUP BY poe.fsoorderentryid
) x on t.fentryid =x.fentryid 
where t.fentryid in ({orderEntryIds.JoinEx(",", true)});
";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 不管理库存（二级分销合同）反写
        /// </summary>
        /// <param name="orderEntryIds"></param>
        /// <returns></returns>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildNotMgrInvWriteBackOrderQty(IEnumerable<string> orderEntryIds)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            var sql = $@"/*dialect*/
update secOrderEntry set foutqty=firSUM.foutqty, fbizoutqty=firSUM.fbizoutqty, ftransoutqty=firSUM.ftransoutqty, fpurinqty=firSUM.fpurinqty, fbizpurinqty=firSUM.fbizpurinqty, fdeliveryqty=firSUM.fdeliveryqty, fbizdeliveryqty=firSUM.fbizdeliveryqty, frefundqty=firSUM.frefundqty, fbizrefundqty=firSUM.fbizrefundqty, freturnqty=firSUM.freturnqty, fbizreturnqty=firSUM.fbizreturnqty, fhqdeliveryqty=firSUM.fhqdeliveryqty, fbizhqdeliveryqty=firSUM.fbizhqdeliveryqty,fhqpurqty=firSUM.fhqpurqty
from t_ydj_orderentry secOrderEntry
INNER JOIN 
(
	select secPOrderEntry.fsoorderentryid
		, SUM(firOrderEntry.foutqty) foutqty, SUM(firOrderEntry.fbizoutqty) fbizoutqty 
		, SUM(firOrderEntry.ftransoutqty) ftransoutqty
		, SUM(firOrderEntry.fpurinqty) fpurinqty, SUM(firOrderEntry.fbizpurinqty) fbizpurinqty 
		, SUM(firOrderEntry.fdeliveryqty) fdeliveryqty, SUM(firOrderEntry.fbizdeliveryqty) fbizdeliveryqty 
		, SUM(firOrderEntry.frefundqty) frefundqty, SUM(firOrderEntry.fbizrefundqty) fbizrefundqty 
		, SUM(firOrderEntry.freturnqty) freturnqty, SUM(firOrderEntry.fbizreturnqty) fbizreturnqty 
		, SUM(firOrderEntry.funstockoutqty) funstockoutqty
		, SUM(firOrderEntry.fhqdeliveryqty) fhqdeliveryqty, SUM(firOrderEntry.fbizhqdeliveryqty) fbizhqdeliveryqty
		, SUM(firOrderEntry.fhqpurqty) fhqpurqty
	from T_YDJ_POORDERENTRY secPOrderEntry with(nolock)
	inner join T_YDJ_PURCHASEORDER secPOrder with(nolock) on secPOrder.fid=secPOrderEntry.fid and secPOrder.fcancelstatus='0'
	inner join T_YDJ_ORDERENTRY firOrderEntry with(nolock) on secPOrderEntry.fentryid=firOrderEntry.fsourceentryid_e and firOrderEntry.fsourcetype_e='ydj_purchaseorder'
	inner join T_YDJ_ORDER firOrder with(nolock) on firOrderEntry.fid=firOrder.fid and firOrder.fcancelstatus='0'
	where secPOrderEntry.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)})  --过滤不管理库存
	group by secPOrderEntry.fsoorderentryid
) firSUM on secOrderEntry.fentryid=firSUM.fsoorderentryid
where secOrderEntry.fentryid in ({orderEntryIds.JoinEx(",", true)});
            ";

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }

        /// <summary>
        /// 反写【关闭状态】
        /// </summary>
        /// <param name="orderEntryIds"></param>
        /// <param name="freturnmodifyorder">销售退换货中允许变更合同</param>
        /// <returns></returns>
        public static KeyValuePair<string, IEnumerable<SqlParam>> BuildWriteBackOrderCloseStatus(IEnumerable<string> orderEntryIds, bool freturnmodifyorder = false)
        {
            List<SqlParam> sqlParams = new List<SqlParam>();

            string express = freturnmodifyorder ? "(te.fbizoutqty-(te.fbizreturnqty+te.fbizreturningqty))" : "(te.fbizoutqty-te.fbizreturnqty)";

            string orderFilter =
                $"fid in (select fid from t_ydj_orderentry where fentryid in ({orderEntryIds.JoinEx(",", true)}))";

            #region 行关闭状态

            // 手动关闭
            var sql = $@"/*dialect*/
update t set fclosestatus='4'
from t_ydj_orderentry as t 
where t.fentryid in ({orderEntryIds.JoinEx(",", true)})
and t.fbizqty=0 and t.fclosestatus<>'4';
";

            // 自动关闭
            sql += $@"
update te set fclosestatus='3'
from t_ydj_orderentry as te 
inner join t_ydj_order t on te.fid=t.fid
inner join T_BD_MATERIAL m with(nolock) on te.fproductid=m.fid
where te.fentryid in ({orderEntryIds.JoinEx(",", true)})
and te.fclosestatus<>'3' and te.fclosestatus<>'4'
and te.fbizqty>0 and te.fbizoutqty>0 AND (te.fbizqty-{express})=0 
and m.fsuiteflag='0';
";

            // 部分关闭
            sql += $@"
update te set fclosestatus='2'
from t_ydj_orderentry as te 
inner join t_ydj_order t on te.fid=t.fid
inner join T_BD_MATERIAL m with(nolock) on te.fproductid=m.fid
where te.fentryid in ({orderEntryIds.JoinEx(",", true)})
and te.fclosestatus<>'2' and te.fclosestatus<>'4'
and te.fbizoutqty>0 AND (te.fbizqty-{express})>0 and (te.fbizqty-{express})<te.fbizqty
and m.fsuiteflag='0';
";

            // 正常
            sql += $@"
update te set fclosestatus='0'
from t_ydj_orderentry as te 
inner join t_ydj_order t on te.fid=t.fid
inner join T_BD_MATERIAL m with(nolock) on te.fproductid=m.fid
where te.fentryid in ({orderEntryIds.JoinEx(",", true)})
and t.fneedtransferorder='0' and te.fclosestatus<>'0' and te.fclosestatus<>'4'
and te.fbizqty>0 AND (te.fbizoutqty=0 or ({express})<=0)
and m.fsuiteflag='0';
";

            #endregion

            #region 套件头关闭状态

            // 套件头关闭状态：自动关闭=所有子件行关闭状态=自动关闭/手动关闭，否则为正常
            sql += $@"
update te set fclosestatus='3'
from t_ydj_orderentry as te 
inner join t_ydj_order t on te.fid=t.fid
inner join T_BD_MATERIAL m with(nolock) on te.fproductid=m.fid
inner join 
(
    select oe.fsuitcombnumber, COUNT(1) allCount, SUM(Case when oe.fclosestatus='3' or oe.fclosestatus='4' then 1 else 0 end) sumLine34
    from t_ydj_orderentry oe
    inner join t_ydj_order o on oe.fid=o.fid
    inner join T_BD_MATERIAL m with(nolock) on oe.fproductid=m.fid
    where o.{orderFilter} and oe.fsuitcombnumber<>'' and m.fsuiteflag='0'
    group by oe.fsuitcombnumber
) Tmp on te.fsuitcombnumber=Tmp.fsuitcombnumber
where te.fentryid in ({orderEntryIds.JoinEx(",", true)})
and Tmp.allCount=Tmp.sumLine34
and m.fsuiteflag='1';
";

            sql += $@"
update te set fclosestatus='0'
from t_ydj_orderentry as te 
inner join t_ydj_order t on te.fid=t.fid
inner join T_BD_MATERIAL m with(nolock) on te.fproductid=m.fid
left join 
(
    select oe.fsuitcombnumber, COUNT(1) allCount, SUM(Case when oe.fclosestatus='3' or oe.fclosestatus='4' then 1 else 0 end) sumLine34
    from t_ydj_orderentry oe
    inner join t_ydj_order o on oe.fid=o.fid
    inner join T_BD_MATERIAL m with(nolock) on oe.fproductid=m.fid
    where o.{orderFilter} and oe.fsuitcombnumber<>'' and m.fsuiteflag='0'
    group by oe.fsuitcombnumber
) Tmp on te.fsuitcombnumber=Tmp.fsuitcombnumber
where te.fentryid in ({orderEntryIds.JoinEx(",", true)})
and (Tmp.fsuitcombnumber is null or Tmp.allCount<>Tmp.sumLine34)
and m.fsuiteflag='1';
";

            #endregion

            #region 表关闭状态

            // 手动关闭：所有行关闭状态=手动关闭
            sql += $@"
update t set fclosestatus='4'
from t_ydj_order t
inner join 
(
    SELECT OE.fid, COUNT(1) allCount, SUM(Case when OE.fclosestatus='4' then 1 else 0 end) sumLine4
    FROM T_YDJ_ORDERENTRY AS OE
    where OE.{orderFilter}
    GROUP BY OE.fid
) Tmp on t.fid=Tmp.fid
where t.{orderFilter} and t.fclosestatus<>'4';
";

            // 部分关闭：其中一行关闭状态=部分关闭
            sql += $@"
update t set fclosestatus='2'
from t_ydj_order t
inner join 
(
    SELECT OE.fid, COUNT(1) allCount, SUM(Case when OE.fclosestatus='0' then 1 else 0 end) sumLine0, SUM(Case when OE.fclosestatus='2' then 1 else 0 end) sumLine2
    FROM T_YDJ_ORDERENTRY AS OE
    where OE.{orderFilter}
    GROUP BY OE.fid
) Tmp on t.fid=Tmp.fid
where t.{orderFilter} and ((Tmp.sumLine0>0 OR Tmp.sumLine2>0) AND Tmp.sumLine0<Tmp.allCount) and t.fclosestatus<>'2';
";

            // 整单关闭：所有行关闭状态=自动关闭或手动关闭
            sql += $@"
update t set fclosestatus='1'
from t_ydj_order t
inner join 
(
    SELECT OE.fid, COUNT(1) allCount, SUM(Case when OE.fclosestatus='3' then 1 else 0 end) sumLine3, SUM(Case when OE.fclosestatus='4' then 1 else 0 end) sumLine4
    FROM T_YDJ_ORDERENTRY AS OE
    where OE.{orderFilter}
    GROUP BY OE.fid
) Tmp on t.fid=Tmp.fid
where t.{orderFilter} AND (Tmp.sumLine3+Tmp.sumLine4=Tmp.allCount AND Tmp.sumLine4<Tmp.allCount) and t.fclosestatus<>'1' and t.fclosestatus<>'4';
";

            // 正常：所有行关闭状态=正常
            sql += $@"
update t set fclosestatus='0'
from t_ydj_order t
inner join 
(
    SELECT OE.fid, COUNT(1) allCount, SUM(Case when OE.fclosestatus='0' then 1 else 0 end) sumLine0
    FROM T_YDJ_ORDERENTRY AS OE
    where OE.{orderFilter}
    GROUP BY OE.fid
) Tmp on t.fid=Tmp.fid
where t.{orderFilter} AND (Tmp.sumLine0=Tmp.allCount) and t.fclosestatus<>'0';
";

            #endregion

            return new KeyValuePair<string, IEnumerable<SqlParam>>(sql, sqlParams);
        }


        /// <summary>
        /// 反写销售合同【已提交总部或终审采购数量】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="targetForm"></param>
        /// <param name="targetDatas"></param>
        /// <param name="removeRowSrcIds"></param>
        public static void WriteBackHQPurQty(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            List<string> removeRowSrcIds = null)
        {
            //不是《采购订单》直接返回
            if (targetForm.Id != "ydj_purchaseorder") return;
            //没有数据时不进行处理
            if (targetDatas == null || !targetDatas.Any()) return;

            List<string> orderEntryIds = new List<string>();
            if (removeRowSrcIds != null && removeRowSrcIds.Any())
            {
                //删除的明细行也要进行反写
                orderEntryIds.AddRange(removeRowSrcIds);
            }
            foreach (var targetData in targetDatas)
            {
                if (Convert.ToInt32(targetData["fchangestatus"]) != 0) continue;
                var targetEntrys = targetData["fentity"] as DynamicObjectCollection;
                foreach (var entry in targetEntrys.ToArray())
                {
                    // 销售合同明细ID
                    var soOrderEntryId = Convert.ToString(entry["fsoorderentryid"]);
                    if (soOrderEntryId.IsNullOrEmptyOrWhiteSpace()) continue;

                    orderEntryIds.Add(soOrderEntryId);
                }
            }
            orderEntryIds = orderEntryIds.Distinct().ToList();

            if (!orderEntryIds.Any()) return;

            var sql = BuildWriteBackOrderHQPurQty(orderEntryIds);

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            dbServiceEx.Execute(userCtx, sql.Key, sql.Value);

            //         var orderSql = $@"/*dialect*/select fsoorderentryid,SUM(fbizqty) fbizqty from T_YDJ_POORDERENTRY poentry where exists (select 1 from t_ydj_purchaseorder po where po.fid=poentry.fid and po.fcancelstatus=0 and po.fhqderstatus in ('03','02'))
            //and poentry.fsoorderentryid in ({orderEntryIds.JoinEx(",", true)}) AND poentry.fsoorderentryid<>'' GROUP BY fsoorderentryid";

            //         var dbService = userCtx.Container.GetService<IDBService>();
            //         var orderEntrys = dbService.ExecuteDynamicObject(userCtx, orderSql);

            //         if (orderEntrys == null || !orderEntrys.Any()) return;

            //         var updateSqls = new List<string>();
            //         foreach (var item in orderEntrys)
            //         {
            //             updateSqls.Add($"/*dialect*/update t_ydj_orderentry set fhqpurqty={item["fbizqty"]} where fentryid='{item["fsoorderentryid"]}'");
            //         }

            //         var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            //         dbServiceEx.ExecuteBatch(userCtx, updateSqls);
        }

    }

    internal class QtyWriteBackLogger
    {
        public string FormId { get; set; }

        public string QtyFld { get; set; }

        private StringBuilder content;

        public QtyWriteBackLogger(string formId, string qtyFld)
        {
            this.FormId = formId;
            this.QtyFld = qtyFld;
            this.content = new StringBuilder();
        }

        public void AppendLine(string content = null)
        {
            this.content.AppendLine(content);
        }

        public void Write()
        {
            try
            {
                string logName = $"WriteBackLog/{FormId}_{QtyFld}";

                DebugUtil.WriteLogToFile(this.content.ToString(), logName);
            }
            catch (Exception e)
            {
            }
        }
    }
}
