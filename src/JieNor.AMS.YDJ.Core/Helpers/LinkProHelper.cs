using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Helpers
{
    /// <summary>
    /// 销售合同流程状态辅助类
    /// </summary>
    public class LinkProHelper
    {
        /// <summary>
        /// 反写销售合同【流程状态】
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="targetForm">销售合同的下游表单模型，比如：采购订单、采购入库单 等等。</param>
        /// <param name="targetDatas">销售合同的下游表单数据包，比如：采购订单、采购入库单 等等。</param>
        /// <param name="forceUpdate">强制更新</param>
        public static void WriteBackOrderLinkPro(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            List<string> sourceentryids = null,
            bool forceUpdate = false)
        {
            // 不管理库存
            bool fisnotmgrinv = false;

            if (userCtx.IsSecondOrg)
            {
                // 当前二级经销商信息
                var currentAgent = userCtx.LoadBizBillHeadDataById("bas_agent", userCtx.Company, "fisnotmgrinv");
                //如果当前经销商是分销商，且不管理库存，那么不更新流程状态信息
                //同时也是避免上游经销商合同变更退货后，二级自动变更审核导致二级和一级流程状态不同步
                fisnotmgrinv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
                if (!forceUpdate && fisnotmgrinv)
                {
                    return;
                }
            }

            var orderEntryIds = new List<string>();

            switch (targetForm.Id.ToLowerInvariant())
            {
                // 销售合同
                case "ydj_order":
                    orderEntryIds = LoadOrderEntryIdsByTargetOrder(userCtx, targetForm, targetDatas, "fentry", "id");
                    break;
                case "bcm_receptionscantask":
                    orderEntryIds = LoadOrderEntryIdsByTargetOrder(userCtx, targetForm, targetDatas, "ftaskentity", "id", sourceentryids);
                    break;
                // 采购订单
                case "ydj_purchaseorder":
                // 采购入库单
                case "stk_postockin":
                // 采购收货通知单
                case "pur_receiptnotice":
                // 销售出库单
                case "stk_sostockout":
                // 销售退货单
                case "stk_sostockreturn":
                // 销售发货通知单
                case "sal_deliverynotice":
                    orderEntryIds = LoadOrderEntryIdsByTargetOrder(userCtx, targetForm, targetDatas, "fentity", "fsoorderentryid", sourceentryids);
                    break;
            }

            if (fisnotmgrinv)
            {
                WriteBackSecOrderLinkProByOrderEntryIds(userCtx, orderEntryIds);
            }
            else
            {
                WriteBackOrderLinkProByOrderEntryIds(userCtx, orderEntryIds);
            }
        }

        private static List<string> LoadOrderEntryIdsByTargetOrder(
            UserContext userCtx,
            HtmlForm targetForm,
            IEnumerable<DynamicObject> targetDatas,
            string entityKey,
            string orderEntryIdFieldKey,
            List<string> sourceentryids = null
            )
        {
            var orderEntryIds = new List<string>();

            if (!sourceentryids.IsNullOrEmptyOrWhiteSpace())
            {
                return sourceentryids;
            }
            if (entityKey.EqualsIgnoreCase("ftaskentity"))
            {
                if (!sourceentryids.IsNullOrEmptyOrWhiteSpace())
                {
                    return sourceentryids;
                }
                else
                {
                    var entitys = targetDatas.SelectMany(o => o[entityKey] as DynamicObjectCollection).ToList();
                    var ids = entitys.Select(o => Convert.ToString(o?[orderEntryIdFieldKey])).ToList();
                    if (ids.Count == 0) return orderEntryIds;

                    string sql = $@"SELECT odmx.fentryid FROM t_ydj_orderentry odmx 
                                    INNER JOIN T_YDJ_POORDERENTRY purmx WITH(NOLOCK) ON odmx.fentryid = purmx.fsourceentryid
                                    INNER JOIN T_BCM_RESCANTASKENTITY b WITH(NOLOCK) ON purmx.fentryid = b.fsourceentryid
                                    where b.fentryid in ('{string.Join("','", ids)}')";
                    var dbService = userCtx.Container.GetService<IDBService>();
                    return dbService.ExecuteDynamicObject(userCtx, sql).Select(o => Convert.ToString(o["fentryid"])).ToList();
                }
            }
            foreach (var targetData in targetDatas)
            {
                var targetEntrys = targetData[entityKey] as DynamicObjectCollection;
                foreach (var entry in targetEntrys)
                {
                    var soOrderEntryId = Convert.ToString(entry[orderEntryIdFieldKey]);
                    if (soOrderEntryId.IsNullOrEmptyOrWhiteSpace()) continue;
                    orderEntryIds.Add(soOrderEntryId);
                }
            }

            return orderEntryIds;
        }

        /// <summary>
        /// 反写销售合同明细行的【流程状态】字段值
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="orderEntryIds">销售合同明细行ID</param>
        private static void WriteBackOrderLinkProByOrderEntryIds(UserContext userCtx, List<string> orderEntryIds, List<string> orderFidIds = null)
        {
            string logName = "OrderQtyWriteBackLog/OrderLinkPro/" + DateTime.Now.ToString("yyyyMMddHHmmsstt");
            StringBuilder logContent = new StringBuilder();
            //清理ID集合中的空字符串以及空白字符串
            if (orderEntryIds != null && orderEntryIds.Any())
            {
                List<string> noNullEntryIds = new List<string>();
                foreach (var item in orderEntryIds)
                {
                    if (!item.IsNullOrEmptyOrWhiteSpace())
                    {
                        noNullEntryIds.Add(item);
                    }
                }
                orderEntryIds = noNullEntryIds;
                logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  销售合同明细行内码（{orderEntryIds.Count}个）：{string.Join("，", orderEntryIds)}；");
            }
            if (orderFidIds != null && orderFidIds.Any())
            {
                List<string> noNullFidIds = new List<string>();
                foreach (var item in orderFidIds)
                {
                    if (!item.IsNullOrEmptyOrWhiteSpace())
                    {
                        noNullFidIds.Add(item);
                    }
                }
                orderFidIds = noNullFidIds;
                logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  销售合同内码（{orderFidIds.Count}个）：{string.Join("，", orderFidIds)}；");
            }

            orderEntryIds = orderEntryIds?.Distinct()?.ToList();
            if (orderEntryIds == null || !orderEntryIds.Any())
            {
                logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  销售合同明细行内码为空；");
                LogHelper.WriteLog(logName, logContent.ToString());
                return;
            }

            //1、先sql语句查找fentryid对应不管理库存下的二级明细行fentryid,map关系
            var dbService = userCtx.Container.GetService<IDBService>();
            var sqlText = string.Empty;
            var tempTable = string.Empty;
            if (orderEntryIds.IsGreaterThan(50))
            {
                //用临时表关联查询
                tempTable = dbService.CreateTempTableWithDataList(userCtx, orderEntryIds);
                sqlText = $@"
                         select t1.fentryid,t3.fentryid secFentryid
            	from t_ydj_order t0 with(nolock)
            	inner join t_ydj_orderentry t1 with(nolock) on t0.fid=t1.fid
            	inner join {tempTable} tt with(nolock) with(nolock) on tt.fid = t1.fentryid
            	inner join t_ydj_poorderentry t2 with(nolock) on t1.fsourceentryid_e=t2.fentryid
            	inner join t_ydj_purchaseorder t5 with(nolock) on t2.fid=t5.fid
            	inner join t_bas_agent m with(nolock) on t5.fmainorgid=m.fid and m.fisreseller='1' and m.fisnotmgrinv='1'
            	inner join t_ydj_orderentry t3 with(nolock) on t2.fsourceentryid=t3.fentryid 
            	where t0.fisresellorder='1'";
            }
            else if (orderEntryIds.Count == 1)
            {
                sqlText = $@"
                         select t1.fentryid,t3.fentryid secFentryid
            	from t_ydj_order t0 with(nolock)
            	inner join t_ydj_orderentry t1 with(nolock) on t0.fid=t1.fid
            	inner join t_ydj_poorderentry t2 with(nolock) on t1.fsourceentryid_e=t2.fentryid
            	inner join t_ydj_purchaseorder t5 with(nolock) on t2.fid=t5.fid
            	inner join t_bas_agent m with(nolock) on t5.fmainorgid=m.fid and m.fisreseller='1' and m.fisnotmgrinv='1'
            	inner join t_ydj_orderentry t3 with(nolock) on t2.fsourceentryid=t3.fentryid 
            	where t0.fisresellorder='1' and t1.fentryid='{orderEntryIds[0]}'";
            }
            else
            {
                sqlText = $@"
                         select t1.fentryid,t3.fentryid secFentryid
            	from t_ydj_order t0 with(nolock)
            	inner join t_ydj_orderentry t1 with(nolock) on t0.fid=t1.fid
            	inner join t_ydj_poorderentry t2 with(nolock) on t1.fsourceentryid_e=t2.fentryid
            	inner join t_ydj_purchaseorder t5 with(nolock) on t2.fid=t5.fid
            	inner join t_bas_agent m with(nolock) on t5.fmainorgid=m.fid and m.fisreseller='1' and m.fisnotmgrinv='1'
            	inner join t_ydj_orderentry t3 with(nolock) on t2.fsourceentryid=t3.fentryid 
            	where t0.fisresellorder='1' and t1.fentryid in('{string.Join("','", orderEntryIds)}')";
            }
            // 销售合同明细行ID与不管理库存的二级合同明细id映射字典
            var secOrderEntryMap = dbService
                .ExecuteDynamicObject(userCtx, sqlText)?.ToDictionary(item => Convert.ToString(item["fentryid"]), item => Convert.ToString(item["secFentryid"]));
            if (tempTable != string.Empty)
            {
                dbService.DeleteTempTableByName(userCtx, tempTable, true);
            }
            //2、如果找到map对应关系，则关联更新二级合同流程状态


            // 计算销售合同明细行的【流程状态】值
            var orderWhere = "";
            var orderWhereX = "";
            if (orderFidIds == null || !orderFidIds.Any())
            {
                orderWhere = orderEntryIds.Count == 1 ? $" fentryid ='{orderEntryIds[0]}'" : $"fentryid in('{string.Join("','", orderEntryIds)}')";

                orderWhereX = orderEntryIds.Count == 1 ? $" [#fentryid#] ='{orderEntryIds[0]}'" : $"[#fentryid#] in('{string.Join("','", orderEntryIds)}')";
            }
            else
            {
                orderWhere = orderFidIds.Count == 1 ? $" fid ='{orderFidIds[0]}'" : $"fid in('{string.Join("','", orderFidIds)}')";
            }
            logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  查询条件：{orderWhere}；");
            //3、【流程状态】的权重为
            //"已下采购"=“部份采购<"提交至总部"<"已终审"<"工厂已发货"=“工厂已部份发货”
            //<"已入库"=“部份入库”<"已出库"="部份出库"<"已退货"="部份退货"
            var orderSql = $@"/*dialect*/
			select t1.fentryid,
			case 
			when (t1.return2>0 or (t1.return1>0 and t1.fqty-t1.foutqty+freturnqty>0) or (t1.fqty=0 and t1.freturnqty>0)) then '已退货'
			when (t1.return2=0 and(t1.return1=0 or (t1.return1>0 and t1.fqty-t1.foutqty+freturnqty<=0 and t1.fqty<>0))) and t1.outcount>0 then '已出库'
            when (t1.transoutqty>0) then '已推出库' 
			when (t1.return1=0 and t1.outcount=0 and t1.incount>0 ) then '已入库'
            when (t1.hqincount>0 and t1.nocancelin4>0 and (t1.outcount=0 or t1.return1=0)) OR (t1.scancount>0) then '工厂已发货'
			when t1.hqpur3>0 and t1.nocancelin5=0 and (t1.outcount=0 or t1.return1=0) then '已终审'
			when t1.hqpur2>0 and t1.nocancelin5=0 and (t1.outcount=0 or t1.return1=0) then '提交至总部'
			when (t1.return1=0 and t1.outcount=0 and t1.incount=0 and t1.purcount>0) then '已下采购' 
            when (t1.transpurqty>0) then '已转采购' 
			else ''
			end flinkpro
			from 
			(
				select t1.fentryid,t1.fqty,t1.foutqty,t1.freturnqty,t1.fbizpurqty,t1.fbizoutqty,
				(
					--已审核且采购数量大于0的采购订单
					select count(1) from t_ydj_purchaseorder t with(nolock) 
					inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid and te.fqty>0
					where t.fstatus='E' 
					and te.fsourceentryid = t1.fentryid {GetFilterString(orderWhereX, "te.fsourceentryid")}
				) purcount,
				 (
				    --存在关联的收货扫描任务处于创建状态
				SELECT COUNT(1) FROM T_BCM_RECEPTIONSCANTASK a WITH(NOLOCK)
				INNER JOIN T_BCM_RESCANTASKENTITY b WITH(NOLOCK) ON b.fid=a.fid
				INNER JOIN T_YDJ_POORDERENTRY purmx WITH(NOLOCK) ON purmx.fentryid = b.fsourceentryid 
				WHERE purmx.fsourceentryid= t1.fentryid AND a.fstatus ='B' {GetFilterString(orderWhereX, "purmx.fsourceentryid")}
                ) scancount,
				(
					--已审核且总部状态等于提交至总部的总部采购订单
					select count(1)
					from t_ydj_purchaseorder t with(nolock) 
					inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid 
					inner join t_ydj_supplier spl with(nolock) on spl.fid=t.fsupplierid and spl.forgid<>''
					where  t.fstatus='E' 
					and te.fsourceentryid=t1.fentryid 
					and t.fhqderstatus='02'
                    {GetFilterString(orderWhereX, "te.fsourceentryid")}
				) hqpur2,
				(
					--已审核且总部状态等于已终审的总部采购订单
					select count(1)
					from t_ydj_purchaseorder t with(nolock) 
					inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid 
					inner join t_ydj_supplier spl with(nolock) on spl.fid=t.fsupplierid and spl.forgid<>''
					where  t.fstatus='E' 
					and te.fsourceentryid=t1.fentryid 
					and t.fhqderstatus='03'
                    {GetFilterString(orderWhereX, "te.fsourceentryid")}
				) hqpur3,
				(
					--未作废且未审核的采购入库单
					select count(1) from t_stk_postockin t with(nolock) 
					inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
					where t.fcancelstatus='0' and t.fstatus<>'E' 
					and te.fsoorderentryid=t1.fentryid
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) nocancelin4,
				(
					--未作废的采购入库单
					select count(1) from t_stk_postockin t with(nolock) 
					inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
					where t.fcancelstatus='0' 
					and te.fsoorderentryid=t1.fentryid
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) nocancelin5,
				(
					--未作废的总部采购入库单
					select count(1) from t_stk_postockin t with(nolock) 
					inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
					inner join t_ydj_supplier spl with(nolock) on spl.fid=t.fsupplierid 
					where t.fcancelstatus='0' 
					and te.fsoorderentryid=t1.fentryid
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) hqincount,
				(
					--已审核的采购入库单
					select count(1) from t_stk_postockin t with(nolock) 
					inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
					where t.fstatus='E' 
					and te.fsoorderentryid=t1.fentryid
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) incount,
				(
					--已审核的销售出库单
					select count(1) from t_stk_sostockout t with(nolock) 
					inner join t_stk_sostockoutentry te with(nolock) on te.fid=t.fid 
					where t.fstatus='E' 
					and te.fsoorderentryid=t1.fentryid
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) outcount,
				(
					--已审核的销售退货单-正常退换
					select count(1) from t_stk_sostockreturn t with(nolock) 
					inner join t_stk_sostockreturnentry te with(nolock) on te.fid=t.fid 
					where t.fstatus='E' 
					and te.fsoorderentryid=t1.fentryid and t.freturntype='sostockreturn_biztype_01'
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) return1,
				(
					--已审核的销售退货单-退货退款
					select count(1) from t_stk_sostockreturn t with(nolock) 
					inner join t_stk_sostockreturnentry te with(nolock) on te.fid=t.fid 
					where t.fstatus='E' 
					and te.fsoorderentryid=t1.fentryid and t.freturntype='sostockreturn_biztype_02'
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) return2,
				(
					--已有的采购订单-已转采购
					select isnull(sum(te.fbizqty),0) from t_ydj_purchaseorder t with(nolock) 
					inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid 
					where te.fsoorderentryid=t1.fentryid and t.fcancelstatus!=1  and t1.fclosestatus NOT IN ('4','3')
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) transpurqty,
				(
					--已有的采购订单-已转出库
					select isnull(sum(te.fbizqty),0) from t_stk_sostockout t with(nolock) 
					inner join t_stk_sostockoutentry te with(nolock) on te.fid=t.fid 
					where te.fsoorderentryid=t1.fentryid and t.fcancelstatus!=1  and t1.fclosestatus NOT IN ('4','3')
                    {GetFilterString(orderWhereX, "te.fsoorderentryid")}
				) transoutqty
				from t_ydj_orderentry t1 with(nolock) 
				where {orderWhere}
			) t1";

            var orderEntrys = dbService.ExecuteDynamicObject(userCtx, orderSql);
            var updateSqls = new List<string>();

            logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  查询结果数量：{orderEntrys.Count}；");
            foreach (var orderEntry in orderEntrys)
            {
                var entryId = Convert.ToString(orderEntry["fentryid"]);
                if (entryId.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                var linkPro = Convert.ToString(orderEntry["flinkpro"]);
                updateSqls.Add($"update t_ydj_orderentry set flinkpro='{linkPro}' where fentryid='{entryId}';");

                if (secOrderEntryMap != null && secOrderEntryMap.ContainsKey(entryId))
                {
                    var secFentryid = secOrderEntryMap[entryId];
                    if (!secFentryid.IsNullOrEmptyOrWhiteSpace())
                    {
                        updateSqls.Add($"update t_ydj_orderentry set flinkpro='{linkPro}' where fentryid='{secFentryid}';");
                    }
                }
            }

            logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  更新语句数量：{updateSqls.Count}；");
            if (!updateSqls.Any()) return;

            logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  更新语句：");
            for (int i = 0; i < updateSqls.Count; i++)
            {
                logContent.AppendLine($"　　{i + 1}：{updateSqls[i]}；");
            }

            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
            dbServiceEx.ExecuteBatch(userCtx, updateSqls);
            LogHelper.WriteLog(logName, logContent.ToString());
        }

        /// <summary>
        /// 反写二级销售合同明细行的【流程状态】字段值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderEntryIds"></param>
        /// <param name="orderFidIds"></param>
        private static void WriteBackSecOrderLinkProByOrderEntryIds(UserContext userCtx, List<string> orderEntryIds, List<string> orderFidIds = null)
        {
            string logName = "OrderQtyWriteBackLog/SecOrderLinkPro/" + DateTime.Now.ToString("yyyyMMddHHmmsstt");
            StringBuilder logContent = new StringBuilder();
            //清理ID集合中的空字符串以及空白字符串
            if (orderEntryIds != null && orderEntryIds.Any())
            {
                List<string> noNullEntryIds = new List<string>();
                foreach (var item in orderEntryIds)
                {
                    if (!item.IsNullOrEmptyOrWhiteSpace())
                    {
                        noNullEntryIds.Add(item);
                    }
                }
                orderEntryIds = noNullEntryIds;
                logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  销售合同明细行内码（{orderEntryIds.Count}个）：{string.Join("，", orderEntryIds)}；");
            }
            if (orderFidIds != null && orderFidIds.Any())
            {
                List<string> noNullFidIds = new List<string>();
                foreach (var item in orderFidIds)
                {
                    if (!item.IsNullOrEmptyOrWhiteSpace())
                    {
                        noNullFidIds.Add(item);
                    }
                }
                orderFidIds = noNullFidIds;
                logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  销售合同内码（{orderFidIds.Count}个）：{string.Join("，", orderFidIds)}；");
            }

            orderEntryIds = orderEntryIds?.Distinct()?.ToList();
            if (orderEntryIds == null || !orderEntryIds.Any())
            {
                logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  销售合同明细行内码为空；");
                LogHelper.WriteLog(logName, logContent.ToString());
                return;
            }

            //3、【流程状态】的权重为
            //"已下采购"=“部份采购<"提交至总部"<"已终审"<"工厂已发货"=“工厂已部份发货”
            //<"已入库"=“部份入库”<"已出库"="部份出库"<"已退货"="部份退货"
            var orderSql = $@"/*dialect*/
update te set te.flinkpro=TempData.flinkpro_cal
from t_ydj_orderentry te
inner join 
(
	select t4.fentryid,
	case 
	when (t1.return2>0 or (t1.return1>0 and t1.fqty-t1.foutqty+t1.freturnqty>0) or (t1.fqty=0 and t1.freturnqty>0)) then '已退货'
	when (t1.return2=0 and(t1.return1=0 or (t1.return1>0 and t1.fqty-t1.foutqty+t1.freturnqty<=0 and t1.fqty<>0))) and t1.outcount>0 then '已出库'
	when (t1.transoutqty>0) then '已推出库' 
	when (t1.return1=0 and t1.outcount=0 and t1.incount>0 ) then '已入库'
	when (t1.hqincount>0 and t1.nocancelin4>0 and (t1.outcount=0 or t1.return1=0)) OR (t1.scancount>0) then '工厂已发货'
	when t1.hqpur3>0 and t1.nocancelin5=0 and (t1.outcount=0 or t1.return1=0) then '已终审'
	when t1.hqpur2>0 and t1.nocancelin5=0 and (t1.outcount=0 or t1.return1=0) then '提交至总部'
	when (t1.return1=0 and t1.outcount=0 and t1.incount=0 and t1.purcount>0) then '已下采购' 
	when (t1.transpurqty>0) then '已转采购' 
	else ''
	end flinkpro_cal,t4.flinkpro flinkpro_old
	from 
	(
		select t1.fentryid,t1.fqty,t1.foutqty,t1.freturnqty,t1.fbizpurqty,t1.fbizoutqty,t3.secFentryid,
		(
			--已审核且采购数量大于0的采购订单
			select count(1) from t_ydj_purchaseorder t with(nolock) 
			inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid and te.fqty>0
			where t.fstatus='E' 
			and te.fsourceentryid = t1.fentryid
		) purcount,
		(
		    --存在关联的收货扫描任务处于创建状态
			SELECT COUNT(1) FROM T_BCM_RECEPTIONSCANTASK a WITH(NOLOCK)
			INNER JOIN T_BCM_RESCANTASKENTITY b WITH(NOLOCK) ON b.fid=a.fid
			INNER JOIN T_YDJ_POORDERENTRY purmx WITH(NOLOCK) ON purmx.fentryid = b.fsourceentryid 
			WHERE purmx.fsourceentryid= t1.fentryid AND a.fstatus ='B'
	    ) scancount,
		(
			--已审核且总部状态等于提交至总部的总部采购订单
			select count(1)
			from t_ydj_purchaseorder t with(nolock) 
			inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid 
			inner join t_ydj_supplier spl with(nolock) on spl.fid=t.fsupplierid and spl.forgid<>''
			where  t.fstatus='E' 
			and te.fsourceentryid=t1.fentryid 
			and t.fhqderstatus='02'
		) hqpur2,
		(
			--已审核且总部状态等于已终审的总部采购订单
			select count(1)
			from t_ydj_purchaseorder t with(nolock) 
			inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid 
			inner join t_ydj_supplier spl with(nolock) on spl.fid=t.fsupplierid and spl.forgid<>''
			where  t.fstatus='E' 
			and te.fsourceentryid=t1.fentryid 
			and t.fhqderstatus='03'
		) hqpur3,
		(
			--未作废且未审核的采购入库单
			select count(1) from t_stk_postockin t with(nolock) 
			inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
			where t.fcancelstatus='0' and t.fstatus<>'E' 
			and te.fsoorderentryid=t1.fentryid
		) nocancelin4,
		(
			--未作废的采购入库单
			select count(1) from t_stk_postockin t with(nolock) 
			inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
			where t.fcancelstatus='0' 
			and te.fsoorderentryid=t1.fentryid
		) nocancelin5,
		(
			--未作废的总部采购入库单
			select count(1) from t_stk_postockin t with(nolock) 
			inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
			inner join t_ydj_supplier spl with(nolock) on spl.fid=t.fsupplierid 
			where t.fcancelstatus='0' 
			and te.fsoorderentryid=t1.fentryid
		) hqincount,
		(
			--已审核的采购入库单
			select count(1) from t_stk_postockin t with(nolock) 
			inner join t_stk_postockinentry te with(nolock) on te.fid=t.fid 
			where t.fstatus='E' 
			and te.fsoorderentryid=t1.fentryid
		) incount,
		(
			--已审核的销售出库单
			select count(1) from t_stk_sostockout t with(nolock) 
			inner join t_stk_sostockoutentry te with(nolock) on te.fid=t.fid 
			where t.fstatus='E' 
			and te.fsoorderentryid=t1.fentryid
		) outcount,
		(
			--已审核的销售退货单-正常退换
			select count(1) from t_stk_sostockreturn t with(nolock) 
			inner join t_stk_sostockreturnentry te with(nolock) on te.fid=t.fid 
			where t.fstatus='E' 
			and te.fsoorderentryid=t1.fentryid and t.freturntype='sostockreturn_biztype_01'
		) return1,
		(
			--已审核的销售退货单-退货退款
			select count(1) from t_stk_sostockreturn t with(nolock) 
			inner join t_stk_sostockreturnentry te with(nolock) on te.fid=t.fid 
			where t.fstatus='E' 
			and te.fsoorderentryid=t1.fentryid and t.freturntype='sostockreturn_biztype_02'
		) return2,
		(
			--已有的采购订单-已转采购
			select isnull(sum(te.fbizqty),0) from t_ydj_purchaseorder t with(nolock) 
			inner join t_ydj_poorderentry te with(nolock) on te.fid=t.fid 
			where te.fsoorderentryid=t1.fentryid and t.fcancelstatus!=1  and t1.fclosestatus NOT IN ('4','3')
		) transpurqty,
		(
			--已有的采购订单-已转出库
			select isnull(sum(te.fbizqty),0) from t_stk_sostockout t with(nolock) 
			inner join t_stk_sostockoutentry te with(nolock) on te.fid=t.fid 
			where te.fsoorderentryid=t1.fentryid and t.fcancelstatus!=1  and t1.fclosestatus NOT IN ('4','3')
		) transoutqty
		from 
        (
            select t1.fentryid,t3.fentryid secFentryid
            from t_ydj_order t0 with(nolock)
            inner join t_ydj_orderentry t1 with(nolock) on t0.fid=t1.fid
            inner join t_ydj_poorderentry t2 with(nolock) on t1.fsourceentryid_e=t2.fentryid
            inner join t_ydj_purchaseorder t5 with(nolock) on t2.fid=t5.fid
            inner join t_bas_agent m with(nolock) on t5.fmainorgid=m.fid and m.fisreseller='1' and m.fisnotmgrinv='1'
            inner join t_ydj_orderentry t3 with(nolock) on t2.fsourceentryid=t3.fentryid 
            where t0.fisresellorder='1' and t3.fentryid in ({orderEntryIds.JoinEx(",", true)})
        ) t3 
		inner join t_ydj_orderentry t1 with(nolock) on t1.fentryid=t3.fentryid
		inner join t_ydj_order t2 with(nolock) on t1.fid=t2.fid 
		where t2.fisresellorder='1'--二级分销合同 
		and t3.secFentryid in ({orderEntryIds.JoinEx(",", true)})
	) t1
	inner join t_ydj_orderentry t4 with(nolock) on t1.secFentryid=t4.fentryid
) AS TempData ON TempData.fentryid=te.fentryid
WHERE te.fentryid in ({orderEntryIds.JoinEx(",", true)})
";

            var dbService = userCtx.Container.GetService<IDBServiceEx>();
            dbService.Execute(userCtx, orderSql);

            logContent.AppendLine($"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}  更新语句：" + orderSql);

            LogHelper.WriteLog(logName, logContent.ToString());
        }


        private static string GetFilterString(string orderWhereX, string fentryidKey)
        {
            if (orderWhereX.IsNullOrEmptyOrWhiteSpace())
            {
                return "";
            }

            var filter = " and " + orderWhereX.Replace("[#fentryid#]", fentryidKey);

            return filter;
        }

    }
}
