using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Helpers
{
    /// <summary>
    /// 单据状态助手
    /// 作者;zpf
    /// 日期：2022-06-06
    /// </summary>
    public static class DocumentStatusHelper
    {
        public static List<int> CloseStates { get; set; } = new List<int>() { (int)CloseStatus.Auto, (int)CloseStatus.Manual };

        ///// <summary>
        ///// 处理单据关闭状态
        ///// 作者：zpf
        ///// 日期：2022-06-06
        ///// </summary>
        ///// <param name="dataEntitys">单据对象集合</param>
        //public static void ProcCloseStatus(List<DynamicObject> dataEntitys)
        //{
        //    /*
        //     * 新需求：32383
        //     6. 《采购入库单》与《销售出库单》审核后关联反写回单据头【关闭状态】时, 判断下对应单据的明细【行关闭状态】
        //           1) 如果所有明细行的【行关闭状态】="正常"时, 更新单据头的【关闭状态】="正常"
        //           2) 如果所有明细行的【行关闭状态】="自动关闭"时, 更新单据头的【关闭状态】="整单关闭"
        //           3) 如果所有明细行的【行关闭状态】="部份关闭"时, 更新单据头的【关闭状态】="部份关闭"
        //           4) 如果所有明细行的【行关闭状态】同时存"自动关闭"与"手工关闭" 且 不存在"部份关闭"时, 更新单据头的【关闭状态】="整单关闭"
        //           5) 如果所有明细行的【行关闭状态】不为上述情况时, 更新单据头的【关闭状态】="部份关闭"
        //    */
        //    foreach (var dataEntity in dataEntitys)
        //    {
        //        var entityName = string.Empty;
        //        if (dataEntity.DynamicObjectType.Properties.ContainsKey("fentity"))
        //        {
        //            entityName = "fentity";
        //        }
        //        if (dataEntity.DynamicObjectType.Properties.ContainsKey("fentry"))
        //        {
        //            entityName = "fentry";
        //        }
        //        var entrys = dataEntity[entityName] as DynamicObjectCollection;
        //        var totalNormalCount = entrys.Where(t => t["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace() || Convert.ToString(t["fclosestatus_e"]) == "0").Count();//正常状态总数
        //        var totalAutoCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Auto).ToString()).Count();//自动关闭状态总数
        //        var totalPartCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Part).ToString()).Count();//部分关闭状态总数
        //        var totalManuallyCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Manual).ToString()).Count();//手动关闭状态总数

        //        if (totalNormalCount == entrys.Count)
        //        {
        //            dataEntity["fclosestatus"] = (int)CloseStatus.Default;
        //            if (!dataEntity["fclosedate"].IsNullOrEmpty())
        //            {
        //                dataEntity["fclosedate"] = null;
        //            }
        //        }
        //        else if (totalAutoCloseCount == entrys.Count || ((totalPartCloseCount > 0 || totalManuallyCloseCount > 0) && totalPartCloseCount == 0))
        //        {
        //            dataEntity["fclosestatus"] = (int)CloseStatus.Whole;
        //            dataEntity["fclosedate"] = DateTime.Now;
        //        }
        //        else
        //        {
        //            dataEntity["fclosestatus"] = (int)CloseStatus.Part;
        //            if (!dataEntity["fclosedate"].IsNullOrEmpty())
        //            {
        //                dataEntity["fclosedate"] = null;
        //            }
        //        }

        //        if (dataEntity["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
        //        {
        //            dataEntity["fclosestatus"] = (int)CloseStatus.Default;
        //            if (!dataEntity["fclosedate"].IsNullOrEmpty())
        //            {
        //                dataEntity["fclosedate"] = null;
        //            }
        //        }
        //    }
        //}

        ///// <summary>
        ///// 处理关闭状态
        ///// </summary>
        ///// <param name="dataEntity">单据信息</param>
        ///// <param name="entitys">明细信息</param>
        //public static void ProcCloseStatus_Order(DynamicObject dataEntity, List<DynamicObject> entitys = null)
        //{
        //    //如果是变更合同数量，则处理行关闭状态
        //    /*
        //     * 新需求：32383
        //     4. 《采购订单》与《销售合同》点击"变更完成" 时,增加更新单据头的【关闭状态】与明细行的所有【行关闭状态】 , 以下拿《销售合同》为例, 如果是 《采购订单》则用【采购数量】【采购入库数量】【采购退换数量】
        //       1)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "正常"
        //       2)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 不等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "部份关闭"
        //       3)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】= 0  时, 明细行的【行关闭状态】= "自动关闭"

        //       4) 如果所有明细行的【行关闭状态】="正常"时, 更新单据头的【关闭状态】="正常"
        //       5) 如果所有明细行的【行关闭状态】="自动关闭"时, 更新单据头的【关闭状态】="整单关闭"
        //       6) 如果所有明细行的【行关闭状态】="部份关闭"时, 更新单据头的【关闭状态】="部份关闭"
        //       7) 如果所有明细行的【行关闭状态】同时存"自动关闭"与"手工关闭" 且 不存在"部份关闭"时, 更新单据头的【关闭状态】="整单关闭"
        //       8) 如果所有明细行的【行关闭状态】不为上述情况时, 更新单据头的【关闭状态】="部份关闭"
        //    */
        //    var entrys = entitys == null ? (dataEntity["fentry"] as DynamicObjectCollection).ToList() : entitys;
        //    var changestatus = false;
        //    if (dataEntity.DynamicObjectType.Properties.ContainsKey("fchangestatus"))
        //    {
        //        var fchangestatus = Convert.ToString(dataEntity["fchangestatus"]);
        //        if (fchangestatus == "1")
        //        {
        //            changestatus = true;
        //        }
        //    };
        //    foreach (var entry in entrys)
        //    {
        //        var fbizqty = Convert.ToDecimal(entry["fbizqty"]);//销售数量
        //        var fbizoutqty = Convert.ToDecimal(entry["fbizoutqty"]);//销售已出库数                    
        //        var fbizreturnqty = Convert.ToDecimal(entry["fbizreturnqty"]);//销售已退换数量
        //        var orderQty = fbizqty - fbizoutqty + fbizreturnqty;
        //        var isFullOutStorehouse = fbizqty <= fbizoutqty + fbizreturnqty;//是否完全出仓,只要销售数量小于或者等于销售已出库数量减去销售已退换数量，也同样是完全出库了
        //        //增加一个前提, 如果原本明细行的【行关闭状态】不等于 "手工关闭"或"自动关闭"
        //        var containsStatus = entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace() ? false : CloseStates.Contains(Convert.ToInt32(entry["fclosestatus_e"]));

        //        if (!changestatus && !containsStatus)
        //        {
        //            if (orderQty > 0 && orderQty - fbizreturnqty == fbizqty)
        //            {
        //                entry["fclosestatus_e"] = (int)CloseStatus.Default;
        //            }
        //            else if (orderQty > 0 && orderQty - fbizreturnqty != fbizqty)
        //            {
        //                entry["fclosestatus_e"] = (int)CloseStatus.Part;
        //            }
        //            else if (orderQty == 0 || isFullOutStorehouse)
        //            {
        //                entry["fclosestatus_e"] = (int)CloseStatus.Auto;
        //            }
        //        }
        //    }
        //    //此处需要传递单据本身的明细对象
        //    UpdateClosestatus(dataEntity, (dataEntity["fentry"] as DynamicObjectCollection).ToList());

        //    if (dataEntity["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
        //    {
        //        dataEntity["fclosestatus"] = (int)CloseStatus.Default;
        //    }
        //}

        ///// <summary>
        ///// 修改单据头关闭状态
        ///// 作者：zpf
        ///// 日期：2022-06-09
        ///// </summary>
        ///// <param name="dataEntity"></param>
        ///// <param name="entrys"></param>
        //public static void UpdateClosestatus(DynamicObject dataEntity, List<DynamicObject> entrys)
        //{
        //    var totalNormalCount = entrys.Where(t => t["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace() || Convert.ToString(t["fclosestatus_e"]) == "0").Count();//正常状态总数
        //    var totalAutoCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Auto).ToString()).Count();//自动关闭状态总数
        //    var totalPartCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Part).ToString()).Count();//部分关闭状态总数
        //    var totalManuallyCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Manual).ToString()).Count();//手动关闭状态总数
        //    //http://dmp.jienor.com:81/zentao/task-view-34349.html  【慕思】销售合同和采购订单进行单据和商品行关闭后，关闭日期，关闭人值为空
        //    //单据头关闭日期跟随 单据头关闭状态 变动（不管其业务怎么变化）
        //    //1、部分关闭、正常：关闭日期 为空
        //    //2、整单关闭、手动关闭：关闭日期 为当前日期
        //    if (totalNormalCount == entrys.Count)
        //    {
        //        dataEntity["fclosestatus"] = (int)CloseStatus.Default;
        //        if (!dataEntity["fclosedate"].IsNullOrEmpty())
        //        {
        //            dataEntity["fclosedate"] = null;
        //        }
        //    }
        //    else if (totalManuallyCloseCount == entrys.Count)
        //    {
        //        dataEntity["fclosestatus"] = (int)CloseStatus.Manual;
        //        dataEntity["fclosedate"] = DateTime.Now;
        //    }
        //    else if (totalNormalCount == 0 && (totalAutoCloseCount == entrys.Count || ((totalAutoCloseCount > 0 || totalManuallyCloseCount > 0) && totalPartCloseCount == 0)))
        //    {
        //        dataEntity["fclosestatus"] = (int)CloseStatus.Whole;
        //        dataEntity["fclosedate"] = DateTime.Now;
        //    }
        //    else
        //    {
        //        dataEntity["fclosestatus"] = (int)CloseStatus.Part;
        //        if (!dataEntity["fclosedate"].IsNullOrEmpty())
        //        {
        //            dataEntity["fclosedate"] = null;
        //        }
        //    }
        //}

        ///// <summary>
        ///// 处理采购订单单据关闭状态
        ///// 作者：zpf
        ///// 日期：2022-06-06
        ///// </summary>
        ///// <param name="dataEntity">单据信息</param>
        ///// <param name="entitys">具体明细</param>
        //public static void ProcCloseStatus_PurchaseOrder(DynamicObject dataEntity, List<DynamicObject> entitys = null)
        //{
        //    //如果是变更合同数量，则处理行关闭状态
        //    /*
        //     * 新需求：32383
        //     4. 《采购订单》与《销售合同》点击"变更完成" 时,增加更新单据头的【关闭状态】与明细行的所有【行关闭状态】 , 以下拿《销售合同》为例, 如果是 《采购订单》则用【采购数量】【采购入库数量】【采购退换数量】
        //       1)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "正常"
        //       2)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 不等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "部份关闭"
        //       3)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】= 0  时, 明细行的【行关闭状态】= "自动关闭"

        //       4) 如果所有明细行的【行关闭状态】="正常"时, 更新单据头的【关闭状态】="正常"
        //       5) 如果所有明细行的【行关闭状态】="自动关闭"时, 更新单据头的【关闭状态】="整单关闭"
        //       6) 如果所有明细行的【行关闭状态】="部份关闭"时, 更新单据头的【关闭状态】="部份关闭"
        //       7) 如果所有明细行的【行关闭状态】同时存"自动关闭"与"手工关闭" 且 不存在"部份关闭"时, 更新单据头的【关闭状态】="整单关闭"
        //       8) 如果所有明细行的【行关闭状态】不为上述情况时, 更新单据头的【关闭状态】="部份关闭"
        //    */
        //    var entrys = entitys == null ? (dataEntity["fentity"] as DynamicObjectCollection).ToList() : entitys;

        //    var changestatus = false;
        //    if (dataEntity.DynamicObjectType.Properties.ContainsKey("fchangestatus"))
        //    {
        //        var fchangestatus = Convert.ToString(dataEntity["fchangestatus"]);
        //        if (fchangestatus == "1")
        //        {
        //            changestatus = true;
        //        }
        //    };
        //    foreach (var entry in entrys)
        //    {
        //        var fbizqty = Convert.ToDecimal(entry["fbizqty"]);//采购数量
        //        var fbizinstockqty = Convert.ToDecimal(entry["fbizinstockqty"]);//采购入库数量                    
        //        var fbizreturnqty = Convert.ToDecimal(entry["fbizreturnqty"]);//采购退换数量
        //        var orderQty = fbizqty - fbizinstockqty + fbizreturnqty;
        //        var isFullInStorehouse = fbizqty <= fbizinstockqty + fbizreturnqty;//是否完全出仓,只要采购数量小于或者等于采购入库数量减去采购退换数量，也同样是完全入库了
        //        //增加一个前提, 如果原本明细行的【行关闭状态】不等于 "手工关闭"或"自动关闭"
        //        var containsStatus = entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace() ? false : CloseStates.Contains(Convert.ToInt32(entry["fclosestatus_e"]));
        //        if (!changestatus && !containsStatus)
        //        {
        //            if (orderQty > 0 && orderQty - fbizreturnqty == fbizqty)
        //            {
        //                entry["fclosestatus_e"] = (int)CloseStatus.Default;
        //            }
        //            else if (orderQty > 0 && orderQty - fbizreturnqty != fbizqty)
        //            {
        //                entry["fclosestatus_e"] = (int)CloseStatus.Part;
        //            }
        //            else if (orderQty == 0 || isFullInStorehouse)
        //            {
        //                entry["fclosestatus_e"] = (int)CloseStatus.Auto;
        //            }
        //        }
        //    }
        //    //此处需要传递单据本身的明细对象
        //    UpdateClosestatus(dataEntity, (dataEntity["fentity"] as DynamicObjectCollection).ToList());

        //    if (dataEntity["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
        //    {
        //        dataEntity["fclosestatus"] = (int)CloseStatus.Default;
        //    }
        //}

        /// <summary>
        /// 计算采购订单的关闭状态字段值，包括单据头的关闭状态和单据体明细行的关闭状态。
        /// </summary>
        /// <param name="purOrderData">采购订单数据包</param>
        public static void CalcPurchaseOrderCloseStatus(DynamicObject purOrderData, string opCode = "", bool isCalcEntryClose = true)
        {
            if (purOrderData == null) return;

            // 如果关闭状态为空，则设置一个默认值
            if (purOrderData["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
            {
                purOrderData["fclosestatus"] = CloseStatusConst.Default;
            }

            #region 计算单据体明细行的【关闭状态】

            var entryDatas = purOrderData["fentity"] as DynamicObjectCollection;
            if (isCalcEntryClose)
            {
                foreach (var entry in entryDatas)
                {
                    // 如果行关闭状态为空，则设置一个默认值
                    if (entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace())
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Default;
                    }

                    // 行关闭状态
                    int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out var entryCloseStatus);

                    // 采购数量
                    var qty = Convert.ToDecimal(entry["fbizqty"]);

                    // 采购入库数量
                    var inStockQty = Convert.ToDecimal(entry["fbizinstockqty"]);

                    // 采购退换数量
                    var returnQty = Convert.ToDecimal(entry["fbizreturnqty"]);

                    // 满足该条件时，【行关闭状态】设置为“自动关闭”
                    // 行关闭状态不是自动关闭 且 行关闭状态不是手动关闭 且 入库数量>0 且 采购数量-(入库数量-退换数量)=0
                    if (entryCloseStatus != (int)CloseStatus.Auto &&
                        entryCloseStatus != (int)CloseStatus.Manual &&
                        inStockQty > 0 &&
                        qty - (inStockQty - returnQty) == 0)
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Auto;
                    }

                    // 满足该条件时，【行关闭状态】设置为“手动关闭”
                    // 行关闭状态不是手动关闭 且 采购数量=0 且 入库数量-退换数量<=0
                    if (entryCloseStatus != (int)CloseStatus.Manual &&
                        qty == 0 &&
                        inStockQty - returnQty <= 0)
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Manual;
                    }

                    // 满足该条件时，【行关闭状态】设置为“部分关闭”
                    // 行关闭状态不是部分关闭 且 行关闭状态不是手动关闭 且 入库数量>0 且 采购数量-(入库数量-退换数量)>0 且 采购数量-(入库数量-退换数量)<采购数量
                    if (entryCloseStatus != (int)CloseStatus.Part &&
                        entryCloseStatus != (int)CloseStatus.Manual &&
                        inStockQty > 0 &&
                        qty - (inStockQty - returnQty) > 0 &&
                        qty - (inStockQty - returnQty) < qty)
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Part;
                    }

                    // 满足该条件时，【行关闭状态】设置为“正常”
                    // 行关闭状态不是正常 且 行关闭状态不是手动关闭 且 (入库数量=0 或 (入库数量>0 且 入库数量-退换数量<=0))
                    if (entryCloseStatus != (int)CloseStatus.Default &&
                        entryCloseStatus != (int)CloseStatus.Manual &&
                        !opCode.EqualsIgnoreCase("bizclose") &&
                        (inStockQty == 0 || (inStockQty > 0 && inStockQty - returnQty <= 0)))
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Default;
                    }
                }
            }
            #endregion
            // 计算单据头的【关闭状态】
            CalcOrderBillHeadCloseStatus(purOrderData, entryDatas);
        }

        /// <summary>
        /// 计算采购订单的关闭状态字段值，包括单据头的关闭状态和单据体明细行的关闭状态。
        /// </summary>
        /// <param name="purOrderData">采购订单数据包</param>
        public static void CalcPurchaseOrderCloseStatusWhitfissuitflag(DynamicObject purOrderData, List<DynamicObject> productrData,string opCode = "", bool isCalcEntryClose = true)
        {
            if (purOrderData == null) return;

            // 如果关闭状态为空，则设置一个默认值
            if (purOrderData["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
            {
                purOrderData["fclosestatus"] = CloseStatusConst.Default;
            }

            #region 计算单据体明细行的【关闭状态】

            var entryDatas = purOrderData["fentity"] as DynamicObjectCollection;
            if (isCalcEntryClose)
            {
                foreach (var entry in entryDatas)
                {
                    // 如果行关闭状态为空，则设置一个默认值
                    if (entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace())
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Default;
                    }

                    // 行关闭状态
                    int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out var entryCloseStatus);

                    // 采购数量
                    var qty = Convert.ToDecimal(entry["fbizqty"]);

                    // 采购入库数量
                    var inStockQty = Convert.ToDecimal(entry["fbizinstockqty"]);

                    // 采购退换数量
                    var returnQty = Convert.ToDecimal(entry["fbizreturnqty"]);

                    // 满足该条件时，【行关闭状态】设置为“自动关闭”
                    // 行关闭状态不是自动关闭 且 行关闭状态不是手动关闭 且 入库数量>0 且 采购数量-(入库数量-退换数量)=0
                    if (entryCloseStatus != (int)CloseStatus.Auto &&
                        entryCloseStatus != (int)CloseStatus.Manual &&
                        inStockQty > 0 &&
                        qty - (inStockQty - returnQty) == 0)
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Auto;
                    }

                    // 满足该条件时，【行关闭状态】设置为“手动关闭”
                    // 行关闭状态不是手动关闭 且 采购数量=0 且 入库数量-退换数量<=0
                    if (entryCloseStatus != (int)CloseStatus.Manual &&
                        qty == 0 &&
                        inStockQty - returnQty <= 0)
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Manual;
                    }

                    // 满足该条件时，【行关闭状态】设置为“部分关闭”
                    // 行关闭状态不是部分关闭 且 行关闭状态不是手动关闭 且 入库数量>0 且 采购数量-(入库数量-退换数量)>0 且 采购数量-(入库数量-退换数量)<采购数量
                    if (entryCloseStatus != (int)CloseStatus.Part &&
                        entryCloseStatus != (int)CloseStatus.Manual &&
                        inStockQty > 0 &&
                        qty - (inStockQty - returnQty) > 0 &&
                        qty - (inStockQty - returnQty) < qty)
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Part;
                    }

                    // 满足该条件时，【行关闭状态】设置为“正常”
                    // 行关闭状态不是正常 且 行关闭状态不是手动关闭 且 (入库数量=0 或 (入库数量>0 且 入库数量-退换数量<=0))
                    if (entryCloseStatus != (int)CloseStatus.Default &&
                        entryCloseStatus != (int)CloseStatus.Manual &&
                        !opCode.EqualsIgnoreCase("bizclose") &&
                        (inStockQty == 0 || (inStockQty > 0 && inStockQty - returnQty <= 0)))
                    {
                        entry["fclosestatus_e"] = CloseStatusConst.Default;
                    }
                }
            }

            //判断商品明细是否为套件
            //子套件全部关闭
            var combFieldKv = new Dictionary<string, string>()
                    {
                        { "fsuitcombnumber", "fsuitcombnumber"}, //套件商品组合号
                        //{ "fsofacombnumber", "fsofacombnumber"}, //沙发商品组合
                    };
            var combFieldKvv = new Dictionary<string, string>()
                    {
                        { "Id", "Id"}, //套件商品组合号
                        //{ "fsofacombnumber", "fsofacombnumber"}, //沙发商品组合
                    };
            var flaglist = productrData.Select(x => new
            {
                Id = Convert.ToString(x["Id"])
            }).ToList();

            foreach (var item in combFieldKv)
            {
                // 套件商品组合号
                var combEntrys = entryDatas
                    .Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace())
                    .Select(x => new
                    {
                        SuitCombNumber = Convert.ToString(x[item.Key]),
                        BizQty = Convert.ToDecimal(x["fbizqty"]),
                        CloseStatus = Convert.ToString(x["fclosestatus_e"]),
                        Materialid=Convert.ToString(x["fmaterialid"])
                    })
                    .ToList()
                    .GroupBy(x => x)
                    .Select(x => new
                    {
                        SuitCombNumber = x.Key.SuitCombNumber,
                        BizQty = x.Key.BizQty,
                        CloseStatus = x.Key.CloseStatus,
                        Materialid=x.Key.Materialid
                    })
                    .ToList();

                foreach (var combEntry in combEntrys)
                {
                    //var temp = entryDatas.Where(x => combEntry.SuitCombNumber.EqualsIgnoreCase(Convert.ToString(x[item.Key])) && combEntry.Suiteflag != "1");
                    //var aftersalesObj = ServiceForm.GetBizDataById(Context, srvdata, true);
                    // 同组套件关闭状态改成一样
                    if (flaglist.Where (x=>x.Id==combEntry.Materialid).Count()>0)
                    {
                        var temp = entryDatas.Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace() && combEntry.SuitCombNumber.EqualsIgnoreCase(x[item.Key].ToString())).ToList();
                        //var temps = entryDatas.Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace() && combEntry.SuitCombNumber.EqualsIgnoreCase(Convert.ToString(x[item.Key])) && combEntry.Suiteflag!="1" &&(combEntry.CloseStatus == "0" || combEntry.CloseStatus == "2"));
                        if(temp.Count<1) continue;
                        bool change = true;
                        foreach (var t in temp)
                        {
                            if ((t["fclosestatus_e"].ToString() == "0" || t["fclosestatus_e"].ToString() == "2") && t["fmaterialid"].ToString() != combEntry.Materialid)
                            {
                                change = false;
                            }
                        }
                        if (!change)
                        {
                            foreach (var tem in temp)
                            {
                                if (tem["fsuitcombnumber"].ToString() == combEntry.SuitCombNumber && tem["fmaterialid"].ToString() == combEntry.Materialid)
                                {
                                    tem["fclosestatus_e"] = CloseStatusConst.Default;
                                }
                            }
                        }
                        else
                        {
                            foreach (var tem in temp)
                            {
                                if (tem["fsuitcombnumber"].ToString() == combEntry.SuitCombNumber && tem["fmaterialid"].ToString() == combEntry.Materialid)
                                {
                                    tem["fclosestatus_e"] = CloseStatusConst.Auto;
                                }
                            }

                        }
                    }
                }
            }
            #endregion

            // 计算单据头的【关闭状态】
            CalcOrderBillHeadCloseStatus(purOrderData, entryDatas);
        }

        /// <summary>
        /// 计算销售合同的关闭状态字段值，包括单据头的关闭状态和单据体明细行的关闭状态。
        /// </summary>
        /// <param name="orderData">销售合同数据包</param>
        public static void CalcOrderCloseStatus(DynamicObject orderData, UserContext context, string opCode = "", bool isCalcEntryClose = false)
        {
            if (orderData == null) return;

            // 如果关闭状态为空，则设置一个默认值
            if (orderData["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
            {
                orderData["fclosestatus"] = CloseStatusConst.Default;
            }
            if (orderData["fclosestate"].IsNullOrEmptyOrWhiteSpace())
            {
                orderData["fclosestate"] = CloseStatusConst.Default;
            }

            #region 计算单据体明细行的【关闭状态】

            // 系统参数服务
            var profileService = context.Container.GetService<ISystemProfile>();
            // 销售退换货允许变更合同
            var returnmodifyorder = profileService.GetSystemParameter(context, "bas_storesysparam", "freturnmodifyorder", false);

            var entryDatas = orderData["fentry"] as DynamicObjectCollection;

            List<DynamicObject> productlist = new List<DynamicObject>();
            foreach (var entry in entryDatas)
            {
                // 如果行关闭状态为空，则设置一个默认值
                if (entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace())
                {
                    entry["fclosestatus_e"] = CloseStatusConst.Default;
                }

                // 行关闭状态
                int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out var entryCloseStatus);

                // 销售数量
                var qty = Convert.ToDecimal(entry["fbizqty"]);

                // 销售出库数量
                var outQty = Convert.ToDecimal(entry["fbizoutqty"]);

                // 销售已退换数量
                var returnQty = Convert.ToDecimal(entry["fbizreturnqty"]);

                // 销售退换中数量
                var returningQty = Convert.ToDecimal(entry["fbizreturningqty"]);
                if (returnmodifyorder)
                {
                    //当参数开启时，行关闭状态计算需考虑【销售退货中数量】
                    returnQty += returningQty;
                }

                // 满足该条件时，【行关闭状态】设置为“自动关闭”
                // 行关闭状态不是自动关闭 且 行关闭状态不是手动关闭 且 出库数量>0 且 销售数量-(出库数量-退换数量)=0
                if (entryCloseStatus != (int)CloseStatus.Auto &&
                        entryCloseStatus != (int)CloseStatus.Manual &&
                        outQty > 0 &&
                        qty - (outQty - returnQty) == 0)
                {
                    entry["fclosestatus_e"] = CloseStatusConst.Auto;
                }

                // 满足该条件时，【行关闭状态】设置为“手动关闭”
                // 行关闭状态不是手动关闭 且 销售数量=0 且 出库数量-退换数量<=0
                if (entryCloseStatus != (int)CloseStatus.Manual &&
                    qty == 0 &&
                    outQty - returnQty <= 0)
                {
                    entry["fclosestatus_e"] = CloseStatusConst.Manual;
                }

                // 满足该条件时，【行关闭状态】设置为“部分关闭”
                // 行关闭状态不是部分关闭 且 行关闭状态不是手动关闭 且 出库数量>0 且 销售数量-(出库数量-退换数量)>0 且 销售数量-(出库数量-退换数量)<销售数量
                if (entryCloseStatus != (int)CloseStatus.Part &&
                    entryCloseStatus != (int)CloseStatus.Manual &&
                    outQty > 0 &&
                    qty - (outQty - returnQty) > 0 &&
                    qty - (outQty - returnQty) < qty)
                {
                    entry["fclosestatus_e"] = CloseStatusConst.Part;
                }

                // 满足该条件时，【行关闭状态】设置为“正常”
                // 行关闭状态不是正常 且 行关闭状态不是手动关闭 且 (出库数量=0 或 (出库数量>0 且 出库数量-退换数量<=0))
                if (entryCloseStatus != (int)CloseStatus.Default &&
                    entryCloseStatus != (int)CloseStatus.Manual &&
                    !opCode.EqualsIgnoreCase("bizclose") &&
                    (outQty == 0 || (outQty > 0 && outQty - returnQty <= 0)))
                {
                    entry["fclosestatus_e"] = CloseStatusConst.Default;
                }

                if (!entry["fsuitcombnumber"].IsNullOrEmptyOrWhiteSpace())
                {
                    var product = context.LoadBizDataById("ydj_product", entry["fproductid"].ToString(), true);
                    if (product["fsuiteflag"].ToString().ToLower() == "true")
                    {
                        productlist.Add(product);
                    }
                }
            }

            //如果明细行手动关闭不需要走下面的逻辑，兼容 廖东毅 47688 需求。手动关闭、自动关闭子件联动套件头
            if (!isCalcEntryClose)
            {
                //判断商品明细是否为套件
                //子套件全部关闭
                var combFieldKv = new Dictionary<string, string>()
                    {
                        { "fsuitcombnumber", "fsuitcombnumber"}, //套件商品组合号
                        //{ "fsofacombnumber", "fsofacombnumber"}, //沙发商品组合
                    };
                var combFieldKvv = new Dictionary<string, string>()
                    {
                        { "Id", "Id"}, //套件商品组合号
                        //{ "fsofacombnumber", "fsofacombnumber"}, //沙发商品组合
                    };
                var flaglist = productlist.Select(x => new
                {
                    Id = Convert.ToString(x["Id"])
                }).ToList();
                foreach (var item in combFieldKv)
                {
                    // 套件商品组合号
                    var combEntrys = entryDatas
                        .Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace())
                        .Select(x => new
                        {
                            SuitCombNumber = Convert.ToString(x[item.Key]),
                            BizQty = Convert.ToDecimal(x["fbizqty"]),
                            CloseStatus = Convert.ToString(x["fclosestatus_e"]),
                            Productid = Convert.ToString(x["fproductid"])
                        })
                        .ToList()
                        .GroupBy(x => x)
                        .Select(x => new
                        {
                            SuitCombNumber = x.Key.SuitCombNumber,
                            BizQty = x.Key.BizQty,
                            CloseStatus = x.Key.CloseStatus,
                            Productid = x.Key.Productid
                        })
                        .ToList();

                    foreach (var combEntry in combEntrys)
                    {
                        // 同组套件关闭状态改成一样
                        if (flaglist.Where(x => x.Id == combEntry.Productid).Count() > 0)
                        {
                            var temp = entryDatas.Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace() && combEntry.SuitCombNumber.EqualsIgnoreCase(x[item.Key].ToString())).ToList();
                            //var temps = entryDatas.Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace() && combEntry.SuitCombNumber.EqualsIgnoreCase(Convert.ToString(x[item.Key])) && combEntry.Suiteflag!="1" &&(combEntry.CloseStatus == "0" || combEntry.CloseStatus == "2"));
                            if (temp.Count <= 1) continue;
                            bool change = true;
                            foreach (var t in temp)
                            {
                                if ((t["fclosestatus_e"].ToString() == "0" || t["fclosestatus_e"].ToString() == "2") && t["fproductid"].ToString() != combEntry.Productid)
                                {
                                    change = false;
                                }
                            }
                            if (!change)
                            {
                                foreach (var tem in temp)
                                {
                                    if (tem["fsuitcombnumber"].ToString() == combEntry.SuitCombNumber && tem["fproductid"].ToString() == combEntry.Productid)
                                    {
                                        tem["fclosestatus_e"] = CloseStatusConst.Default;
                                    }
                                }
                            }
                            else
                            {
                                foreach (var tem in temp)
                                {
                                    if (tem["fsuitcombnumber"].ToString() == combEntry.SuitCombNumber && tem["fproductid"].ToString() == combEntry.Productid && Convert.ToDecimal(tem["fbizqty"]) != 0)//变更状态下修改套件头数量，保持原关闭状态：手工关闭
                                    {
                                        tem["fclosestatus_e"] = CloseStatusConst.Auto;
                                    }
                                }

                            }
                        }
                    }
                }
            }


                #endregion

                // 计算单据头的【关闭状态】
                CalcOrderBillHeadCloseStatus(orderData, entryDatas);
        }

        /// <summary>
        /// 上下样合同 调拨单审核后更新关闭状态
        /// </summary>
        /// <param name="orderData"></param>
        /// <param name="opCode"></param>
        /// <param name="isCalcEntryClose"></param>
        public static void CalcOrderCloseStatus_SXY(DynamicObject orderData,List<string> entryIds, string opcode) 
        {
            if (orderData == null) return;

            var close = CloseStatusConst.Auto;
            if (opcode.EqualsIgnoreCase("unaudit")) 
            {
                close = CloseStatusConst.Default;
            }

            var entryDatas = orderData["fentry"] as DynamicObjectCollection;
            foreach (var entry in entryDatas)
            {
                if (!entryIds.Contains(Convert.ToString(entry["id"]))) continue;

                // 如果行关闭状态为空，则设置一个默认值
                if (entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace())
                {
                    entry["fclosestatus_e"] = CloseStatusConst.Default;
                }

                entry["fclosestatus_e"] = close;
                
            }
            // 计算单据头的【关闭状态】
            CalcOrderBillHeadCloseStatus(orderData, entryDatas);
        }

        /// <summary>
        /// 计算单据头的关闭状态字段值。
        /// </summary>
        /// <param name="orderData">业务单据数据包</param>
        /// <param name="entryDatas">业务单据体明细数据包</param>
        public static void CalcOrderBillHeadCloseStatus(DynamicObject orderData, DynamicObjectCollection entryDatas)
        {
            // 单据头【关闭日期】根据单据头【关闭状态】的变化而变化。
            // 1、部分关闭、正常 时【关闭日期】为空。
            // 2、整单关闭、手动关闭 时【关闭日期】为当前系统日期。

            // 单据头关闭状态
            int.TryParse(Convert.ToString(orderData["fclosestatus"]), out var closeStatus);

            // 有效（行关闭状态不为空）的明细总行数
            var entryCount = entryDatas
                .Where(o => !o["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace())
                .Count();

            // 【行关闭状态】为“正常”的总行数
            var defEntryCount = entryDatas
                .Where(o => Convert.ToString(o["fclosestatus_e"]).EqualsIgnoreCase(CloseStatusConst.Default))
                .Count();

            // 【行关闭状态】为“部分关闭”的总行数
            var partEntryCount = entryDatas
                .Where(o => Convert.ToString(o["fclosestatus_e"]).EqualsIgnoreCase(CloseStatusConst.Part))
                .Count();

            // 【行关闭状态】为“自动关闭”的总行数
            var autoEntryCount = entryDatas
                .Where(o => Convert.ToString(o["fclosestatus_e"]).EqualsIgnoreCase(CloseStatusConst.Auto))
                .Count();

            // 【行关闭状态】为“手动关闭”的总行数
            var manualEntryCount = entryDatas
                .Where(o => Convert.ToString(o["fclosestatus_e"]).EqualsIgnoreCase(CloseStatusConst.Manual))
                .Count();

            // 满足该条件时，【单据头关闭状态】设置为“部分关闭”
            if (closeStatus != (int)CloseStatus.Part &&
                (defEntryCount > 0 || partEntryCount > 0) &&
                defEntryCount < entryCount)
            {
                orderData["fclosestatus"] = CloseStatusConst.Part;
                orderData["fclosedate"] = null;
            }

            // 如果【单据头关闭状态】为“部分关闭”，则清空单据头【关闭日期】
            if (closeStatus == (int)CloseStatus.Part)
            {
                orderData["fclosedate"] = null;
            }

            // 满足该条件时，【单据头关闭状态】设置为“整单关闭”
            if (closeStatus != (int)CloseStatus.Whole &&
                autoEntryCount > 0 &&
                autoEntryCount + manualEntryCount == entryCount &&
                manualEntryCount < entryCount)
            {
                orderData["fclosestatus"] = CloseStatusConst.Whole;
                orderData["fclosedate"] = DateTime.Now;
            }

            // 满足该条件时，【单据头关闭状态】设置为“正常”
            if (defEntryCount > 0 && defEntryCount == entryCount)
            {
                orderData["fclosestatus"] = CloseStatusConst.Default;
                orderData["fclosedate"] = null;
            }

            // 满足该条件时，【单据头关闭状态】设置为“手动关闭”
            if (manualEntryCount > 0 && manualEntryCount == entryCount)
            {
                orderData["fclosestatus"] = CloseStatusConst.Manual;
                orderData["fclosedate"] = DateTime.Now;
            }
        }

        /// <summary>
        /// 处理（套件组合、沙发组合）商品关闭状态。
        /// #36178 套件与沙发组合的同组商品一起关闭或反关闭
        /// </summary>
        /// <param name="isPdk">套件与沙发商品是否成套采购</param>
        /// <param name="orderForm">业务单据模型，采购订单、销售合同</param>
        /// <param name="orderData">业务单据数据包，采购订单、销售合同</param>
        /// <param name="entryDatas">业务单据商品明细数据包</param>
        /// <param name="selEntryDatas">业务单据选中行商品明细数据包</param>
        /// <param name="context">登录上下文环境</param>
        public static void SetSuitCombProductCloseStatus(
            bool isPdk,
            HtmlForm orderForm,
            DynamicObject orderData,
            DynamicObjectCollection entryDatas,
            List<DynamicObject> selEntryDatas
            , UserContext context
           )
        {
            string ProductKey = "fproductid";
            if (orderForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                ProductKey = "fproductid";
            }
            else if (orderForm.Id.EqualsIgnoreCase("ydj_purchaseorder"))
            {
                ProductKey = "fmaterialid";
            }
            if (!isPdk) return;

            var needProcCloseStatus = false;

            var combFieldKv = new Dictionary<string, string>()
            {
                { "fsuitcombnumber", "fsuitcombnumber"}, //套件商品组合号
                { "fsofacombnumber", "fsofacombnumber"}, //沙发商品组合
            };

            List<DynamicObject> productlist = new List<DynamicObject>();
            foreach (var entry in entryDatas) 
            {
                if (!entry["fsuitcombnumber"].IsNullOrEmptyOrWhiteSpace())
                {
                    var product = context.LoadBizDataById("ydj_product", entry[ProductKey].ToString());
                    if (product["fsuiteflag"].ToString().ToLower() == "true")
                    {
                        productlist.Add(product);
                    }
                }
            }
            //获取套件头商品
            var flaglist = productlist.Select(x => new
            {
                Id = Convert.ToString(x["Id"])
            }).ToList();

            foreach (var item in combFieldKv)
            {
                // 套件商品组合号
                var combEntrys = selEntryDatas
                    .Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace())
                    .Select(x => new
                    {
                        SuitCombNumber = Convert.ToString(x[item.Key]),
                        BizQty = Convert.ToDecimal(x["fbizqty"]),
                        Productid = Convert.ToString(x[ProductKey]),
                        CloseStatus = Convert.ToString(x["fclosestatus_e"])
                    })
                    .ToList()
                    .GroupBy(x => x)
                    .Select(x => new
                    {
                        SuitCombNumber = x.Key.SuitCombNumber,
                        BizQty = x.Key.BizQty,
                        ProductId = x.Key.Productid,
                        CloseStatus = x.Key.CloseStatus
                    })
                    .ToList();
                
                foreach (var combEntry in combEntrys)
                {
                    var temps = entryDatas.Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace() && combEntry.SuitCombNumber.EqualsIgnoreCase(x[item.Key].ToString())).ToList();
                    //to do 判断子件的关闭状态是否全部一致，如果更新完则头跟随子件的状态
                    var closestatues = temps
                        .Where(o => flaglist.Any(x => x.Id != Convert.ToString(o[ProductKey])))
                        .Select(o => Convert.ToString(o["fclosestatus_e"])).Distinct().ToList();
                    //套件的处理
                    if (flaglist.Where(x => x.Id == combEntry.ProductId).Count() > 0)
                    {
                        //to do  如果是套件头操作 不去联动子件关闭状态，因为套件头不做出库。只有子件才去联动套件

                        //var temps = entryDatas.Where(x => !Convert.ToString(x[item.Key]).IsNullOrEmptyOrWhiteSpace() && combEntry.SuitCombNumber.EqualsIgnoreCase(Convert.ToString(x[item.Key])) && combEntry.Suiteflag!="1" &&(combEntry.CloseStatus == "0" || combEntry.CloseStatus == "2"));
                        if (temps.Count <= 1) continue;

                        if (closestatues.Count() == 1)
                        {
                            foreach (var tem in temps)
                            {
                                if (tem["fsuitcombnumber"].ToString() == combEntry.SuitCombNumber && tem[ProductKey].ToString() == combEntry.ProductId && Convert.ToDecimal(tem["fbizqty"]) != 0)//变更状态下修改套件头数量，保持原关闭状态：手工关闭
                                {
                                    tem["fclosestatus_e"] = combEntry.CloseStatus;
                                }
                            }
                        }
                    }
                    //子件的处理
                    else
                    {
                        //同组套件关闭状态改成一样
                        //var temps = entryDatas.Where(x => combEntry.SuitCombNumber.EqualsIgnoreCase(Convert.ToString(x[item.Key]))).ToList();

                        //to do 只有最后一行子件操作完后才去 联动套件头。即：存在其它行关闭状态不等于当前关闭状态时，不去联动套件头
                        if (temps.Any(o => Convert.ToString(o["fclosestatus_e"]) != combEntry.CloseStatus && flaglist.Any(x => x.Id != Convert.ToString(o[ProductKey])))) continue;

                        foreach (var temp in temps)
                        {
                            if (Convert.ToString(temp["fclosestatus_e"]) == "3")//自动关闭行跳过
                                continue;
                            //子件联动只联动套件头，不联动其它子件
                            if (flaglist.Where(x => x.Id == Convert.ToString(temp[ProductKey])).Count() == 0)
                                continue;

                            if (closestatues.Count() != 1) 
                                continue;

                                temp["fclosestatus_e"] = combEntry.CloseStatus;
                            if (combEntry.BizQty == 0)
                            {
                                temp["fbizqty"] = 0;
                                //基本单位数量也要同步变  bug: 28572
                                temp["fqty"] = 0;
                            }
                            needProcCloseStatus = true;
                        }
                    } 

                } 
            }

            // 如果有设置同组商品的关闭状态，则重新计算一遍整个单据的关闭状态
            if (needProcCloseStatus)
            {
                switch (orderForm.Id.ToLowerInvariant())
                {
                    case "ydj_order":
                        CalcOrderCloseStatus(orderData,context, isCalcEntryClose: true);
                        break;
                    case "ydj_purchaseorder":
                        CalcPurchaseOrderCloseStatus(orderData);
                        break;
                }
            }
        }

        /// <summary>
        /// 跟采购订单 PC保存时根据采购数量 更新行关闭状态逻辑一致（防止接口没有走插件保存，没及时更新状态为正常）
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="context"></param>
        public static void DoCloseOrUnClose(DynamicObject dataEntity,UserContext context) {
            List<SelectedRow> bizCloseIds = new List<SelectedRow>();
            List<SelectedRow> unCloseIds = new List<SelectedRow>();
            var svc = context.Container.GetService<IDBService>();
            var invoke = context.Container.GetService<IHttpServiceInvoker>();
            //Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(dataEntity, string.Empty, !IsNotMgrInv);
            var entryDatas = dataEntity["fentity"] as DynamicObjectCollection;
            foreach (var entry in entryDatas)
            {
                // 采购数量
                var qty = Convert.ToDecimal(entry["fbizqty"]);
                // 行关闭状态
                int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out var entryCloseStatus);
                if (qty == 0 && entryCloseStatus != (int)CloseStatus.Manual)
                {
                    bizCloseIds.Add(new SelectedRow
                    {
                        PkValue = Convert.ToString(dataEntity["id"]),
                        EntryPkValue = Convert.ToString(entry["id"]),
                        EntityKey = "fentity"
                    });
                }
                else if (entryCloseStatus == (int)CloseStatus.Manual && qty != 0)
                {
                    unCloseIds.Add(new SelectedRow
                    {
                        PkValue = Convert.ToString(dataEntity["id"]),
                        EntryPkValue = Convert.ToString(entry["id"]),
                        EntityKey = "fentity"
                    });
                }
            }
            

            List<string> oldZeroList = new List<string>();
            if (unCloseIds.Any())
            {
                oldZeroList = svc.ExecuteDynamicObject(context, "select fentryid from t_ydj_poorderentry where fentryid in('" + string.Join("','", unCloseIds.Select(x => x.EntryPkValue).ToList()) + "')")?.Select(x => Convert.ToString(x["fentryid"])).ToList();
            }
            //this.Container.GetService<IRepairService>().RepairBillHeadSourceNum(context,e.DataEntitys,"fsourcenumber","","fentity", "fsourcebillno","");
            context.SaveBizData("ydj_purchaseorder", dataEntity);
            if (bizCloseIds.Any())
            {
                List<string> rowIds = bizCloseIds.Select(x => x.EntryPkValue).ToList();
                var invokeClose = invoke.InvokeListOperation(context,
                    "ydj_purchaseorder",
                    bizCloseIds,
                    "bizclose",
                    new Dictionary<string, object>() {
                        { "rowIds",rowIds.ToJson() },
                        { "IgnoreCheckPermssion","true" }//去掉权限校验
                    });
                invokeClose?.ThrowIfHasError(true, $"自动关闭数量为0商品行操作异常！");
            }
            if (unCloseIds.Any())
            {
                List<string> rowIds = unCloseIds.Select(x => x.EntryPkValue).ToList();
                if (!oldZeroList.IsNullOrEmpty() || oldZeroList.Any())
                {
                    rowIds = rowIds.Where(x => oldZeroList.Contains(x)).ToList();
                    unCloseIds = unCloseIds.Where(x => oldZeroList.Contains(x.EntryPkValue)).ToList();
                    var invokeUnClose = invoke.InvokeListOperation(context,
                   "ydj_purchaseorder",
                    unCloseIds,
                    "unclose",
                    new Dictionary<string, object>() {
                        { "rowIds",rowIds.ToJson() },
                        { "IgnoreCheckPermssion","true" },//去掉权限校验
                        { "modifyCloseStatus","1"}
                    });
                    invokeUnClose?.ThrowIfHasError(true, $"自动反关闭手工关闭商品行操作异常！");
                }
            }
        }
    }
}
