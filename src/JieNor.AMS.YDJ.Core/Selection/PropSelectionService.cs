using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Core.Interface;
using System.Data.SqlClient;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework.Interface.QueryBuilder;
using System.Globalization;

namespace JieNor.AMS.YDJ.Core
{
    /// <summary>
    /// 属性选配服务
    /// </summary>
    [InjectService]
    public class PropSelectionService : IPropSelectionService
    {
        /// <summary>
        /// 是否启用选配约束条件新逻辑（目的是为了只让特定环境生效，等测试通过后即可删除该判断）
        /// </summary>
        private bool EnableSelConstraintNewLogic { get; set; }

        /// <summary>
        /// 定义一个不存在的属性值ID
        /// </summary>
        private const string NoExistPropValueId = "000000000000000000";

        /// <summary>
        /// 构建选配范围属性值ID集合
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="selCategoryId">选配类别ID</param>
        /// <param name="selPropId">属性ID</param>
        /// <returns>返回构建好的属性值ID集合</returns>
        public List<string> BuildSelRangePropValueIds(UserContext userCtx, string selCategoryId, string selPropId,out string rangFilter)
        {
            rangFilter = " or 1 = 2 ";
            var propValueIds = new List<string>();

            if (selCategoryId.IsNullOrEmptyOrWhiteSpace()
                || selPropId.IsNullOrEmptyOrWhiteSpace())
            {
                return propValueIds;
            }

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "r.");

            var sqlText = $@"
            select distinct re.fpropvalueid,re.fmin,re.fmax from t_sel_range r with(nolock) 
            inner join t_sel_rangeentry re with(nolock) on re.fid=r.fid 
            where r.fselcategoryid=@fselcategoryid 
            and re.fpropid=@fpropid and r.fforbidstatus='0'{aclFilter}";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fselcategoryid", System.Data.DbType.String, selCategoryId),
                new SqlParam("@fpropid", System.Data.DbType.String, selPropId)
            };

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (dynObjs == null || !dynObjs.Any()) return propValueIds;

            var propObj = userCtx.LoadBizBillHeadDataById("sel_prop", selPropId, "fname,fnumber,fdatatype");
            var fdatatype = Convert.ToString(propObj?["fdatatype"]);
            rangFilter = " 1 = 2 ";
            foreach (var dynObj in dynObjs)
            {
                var _propValueIds = Convert.ToString(dynObj?["fpropvalueid"])
                    .Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                propValueIds.AddRange(_propValueIds);
                //如果是数值类型，还要判断是否设置范围值
                if (fdatatype.EqualsIgnoreCase("2")) 
                {
                    var min = Convert.ToDecimal(dynObj?["fmin"]);
                    var max = Convert.ToDecimal(dynObj?["fmax"]);
                    if (min > 0 || max > 0) 
                    {
                        rangFilter += $" or (convert(DECIMAL(18,2),fnumber) between {min} and {max})";
                    }
                }
            }
            rangFilter = $"/*dialect*/ or ( {rangFilter} )";
            //var RangIds = userCtx.LoadBizBillHeadDataByACLFilter("sel_propvalue", rangFilter,"fid").Select(o=> Convert.ToString(o?["fid"])).ToList();
            //propValueIds.AddRange(RangIds);
            propValueIds = propValueIds.Distinct().ToList();

            return propValueIds;
        }

        /// <summary>
        /// 构建选配范围过滤条件
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="selCategoryId">选配类别ID</param>
        /// <param name="selPropId">属性ID</param>
        /// <returns>返回构建好的选配范围过滤条件字符串</returns>
        public string BuildSelRangeFilter(UserContext userCtx, string selCategoryId, string selPropId)
        {
            var filter = $" fid='{NoExistPropValueId}'";
            var propValueIds = this.BuildSelRangePropValueIds(userCtx, selCategoryId, selPropId,out string rangFilter);
            if (propValueIds.Count == 1)
            {
                filter = $" fid='{propValueIds[0]}'";
            }
            else if (propValueIds.Count > 1)
            {
                filter = $" fid in('{string.Join("','", propValueIds)}')";
            }
            //追加范围值过滤，取并集。
            filter = $"({ filter + rangFilter })" ;
            return filter;
        }

        /// <summary>
        /// 通过商品的非标类别 在【非标库类别映射】中找到新的商品类别映射
        /// </summary>
        /// <param name="funstdcategory"></param>
        /// <returns></returns>
        private string GetNewUndcategory(UserContext userCtx, string funstdcategory, string fieldName = "", string separator = "and")
        {
            var categoryFilter = " and 1=2 ";
            var sql = $"select fcategoryid_new from t_sel_unstdtypemappingentry mx with(nolock)" +
                $" inner join t_sel_unstdtypemapping main with(nolock) on mx.fid = main.fid and fforbidstatus = 0 " +
                $" where fcategoryid_old = '{funstdcategory}' ";
            var dbService = userCtx.Container.GetService<IDBService>();
            var categoryid_new = dbService.ExecuteDynamicObject(userCtx, sql).Select(o => Convert.ToString(o["fcategoryid_new"])).FirstOrDefault();
            var productService = userCtx.Container.GetService<IProductService>();
            if (!categoryid_new.IsNullOrEmptyOrWhiteSpace())
            {
                //获取此商品类别及其上级类别
                //var categoryIds = productService.LoadProductParentCategoryIdsByID(userCtx, categoryid_new);
                //if (!categoryIds.Any()) return categoryFilter;

                //if (categoryIds.Count == 1)
                //{
                //    return $" {separator} {fieldName}='{categoryIds[0]}'";
                //}
                //categoryFilter = $" { separator} {fieldName} in ('{string.Join("','", categoryIds)}')";
                //43832 非标属性值匹配非标库映射后不需要找上级类别，直接匹配当前类别即可
                return $" {separator} {fieldName}='{categoryid_new}'";
            }
            return categoryFilter;
        }

        /// <summary>
        /// 构建非标商品属性的可选属性值ID集合
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="selPropIds">属性ID集合</param>
        /// <returns>返回构建好的以属性ID为键，属性值ID为值的键值对</returns>
        public Dictionary<string, List<string>> BuildNonStandardPropValueIds(UserContext userCtx, string productId, List<string> selPropIds, List<string> Filter_NOTIN = null)
        {
            var propValueIdKv = new Dictionary<string, List<string>>();

            if (productId.IsNullOrEmptyOrWhiteSpace()
                || selPropIds == null
                || !selPropIds.Any())
            {
                return propValueIdKv;
            }

            var aclFilter1 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "m.");
            var aclFilter2 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "p.");

            //商品类别过滤条件
            var productService = userCtx.Container.GetService<IProductService>();
            var categoryIdFilter = productService.BuildProductParentCategoryIdFilter(userCtx, productId, "p.fcategoryid");
            //http://dmp.jienor.com:81/zentao/task-view-35171.html
            //这里获取商品类别的方式要做调整 要根据商品的非标类别去非标库类别映射表匹配，找到新的商品类别映射；如果没有匹配到则 返回 1=2
            var products = userCtx.LoadBizBillHeadDataById("ydj_product", productId, "funstdcategory");
            //获取商品的 非标类别字段
            var funstdcategory = Convert.ToString(products?["funstdcategory"]);
            if (!funstdcategory.IsNullOrEmptyOrWhiteSpace())
            {
                categoryIdFilter = GetNewUndcategory(userCtx, funstdcategory, "p.fcategoryid");
            }
            else
            {
                categoryIdFilter = "and 1=2 ";
            }

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fproductid", System.Data.DbType.String, productId)
            };

            var propIdWhere = " and p.fnosuit='1' and p.fforbidstatus='0'";
            if (selPropIds.Count == 1)
            {
                propIdWhere += " and p.fpropid=@fpropid";
                sqlParam.Add(new SqlParam("@fpropid", System.Data.DbType.String, selPropIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < selPropIds.Count; i++)
                {
                    paramNames.Add($"@fpropid{i}");
                    sqlParam.Add(new SqlParam($"@fpropid{i}", System.Data.DbType.String, selPropIds[i]));
                }
                propIdWhere += $" and p.fpropid in({string.Join(",", paramNames)})";
            }

            //匹配属性值为非标的值，并且根据商品中的三个字段匹配非标值里面的这三个字段（品牌、系列、商品类型）时，
            //非标值里面的这三个字段有值时才匹配，并且多个值是并且的关系，
            //所以这三个字段的组合查询条件最多有以下7种
            var sqlText = $@"
            select distinct p.fpropid, p.fid fpropvalueid from t_sel_propvalue p with(nolock)
            inner join t_bd_material m with(nolock) on m.fbrandid=p.fbrandid 
            where m.fid=@fproductid and p.fseriesid=''{categoryIdFilter}{aclFilter1}{aclFilter2}{propIdWhere}
            union 
            select distinct p.fpropid, p.fid fpropvalueid from t_sel_propvalue p with(nolock)
            inner join t_bd_material m with(nolock) on m.fseriesid=p.fseriesid 
            where m.fid=@fproductid and p.fbrandid=''{categoryIdFilter}{aclFilter1}{aclFilter2}{propIdWhere}
            union 
            select distinct p.fpropid, p.fid fpropvalueid from t_sel_propvalue p with(nolock)
            where p.fbrandid='' and p.fseriesid=''{categoryIdFilter}{aclFilter2}{propIdWhere}
            union
            select distinct p.fpropid, p.fid fpropvalueid from t_sel_propvalue p with(nolock)
            inner join t_bd_material m with(nolock) on m.fbrandid=p.fbrandid and m.fseriesid=p.fseriesid
            where m.fid=@fproductid  {categoryIdFilter}{aclFilter1}{aclFilter2}{propIdWhere}
            union
            select distinct p.fpropid, p.fid fpropvalueid from t_sel_propvalue p with(nolock)
            inner join t_bd_material m with(nolock) on m.fbrandid=p.fbrandid 
            where m.fid=@fproductid and p.fseriesid=''{categoryIdFilter}{aclFilter1}{aclFilter2}{propIdWhere}
            union
            select distinct p.fpropid, p.fid fpropvalueid from t_sel_propvalue p with(nolock)
            inner join t_bd_material m with(nolock) on m.fseriesid=p.fseriesid 
            where m.fid=@fproductid and p.fbrandid=''{categoryIdFilter}{aclFilter1}{aclFilter2}{propIdWhere}
            union
            select distinct p.fpropid, p.fid fpropvalueid from t_sel_propvalue p with(nolock)
            inner join t_bd_material m with(nolock) on m.fbrandid=p.fbrandid and m.fseriesid=p.fseriesid
            where m.fid=@fproductid {categoryIdFilter}{aclFilter1}{aclFilter2}{propIdWhere}";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (dynObjs == null || !dynObjs.Any()) return propValueIdKv;

            foreach (var dynObj in dynObjs)
            {
                var propId = Convert.ToString(dynObj["fpropid"]);
                var propValueId = Convert.ToString(dynObj["fpropvalueid"]);

                List<string> propValueIds = null;
                propValueIdKv.TryGetValue(propId, out propValueIds);
                if (propValueIds == null)
                {
                    propValueIds = new List<string>();
                    propValueIdKv[propId] = propValueIds;
                }

                propValueIds.Add(propValueId);
            }
            foreach (var pk in propValueIdKv.GroupBy(o => o.Key)) 
            {
                propValueIdKv.TryGetValue(pk.Key, out List<string> propValueIds);

                if (!propValueIds.Any()) continue;

                if (Filter_NOTIN != null && Filter_NOTIN.Any())
                {
                    propValueIds = GetPropValueIdsByNotIn(userCtx, pk.Key, propValueIds, Filter_NOTIN);
                }
                propValueIdKv[pk.Key] = propValueIds;
            }

            return propValueIdKv;
        }

        /// <summary>
        /// 构建非标商品属性的可选属性值过滤条件
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="selPropId">属性ID</param>
        /// <returns>返回构建好的过滤条件字符串</returns>
        public string BuildNonStandardPropValueFilter(UserContext userCtx, string productId, string selPropId,List<string> Filter_NOTIN = null)
        {
            var filter = "";
            var propValueIdKv = this.BuildNonStandardPropValueIds(userCtx, productId, new List<string> { selPropId }, Filter_NOTIN);

            List<string> propValueIds = null;
            propValueIdKv.TryGetValue(selPropId, out propValueIds);

            if (propValueIds == null) return filter;
            //if (Filter_NOTIN.Any())
            //{
            //    propValueIds = GetPropValueIdsByNotIn(userCtx, propValueIds, Filter_NOTIN);
            //}

            if (propValueIds.Count == 1)
            {
                filter = $" fid='{propValueIds[0]}'";
            }
            else if (propValueIds.Count > 1)
            {
                filter = $" fid in('{string.Join("','", propValueIds)}')";
            }

            return filter;
        }

        public List<string> GetPropValueIdsByNotIn(UserContext userCtx, string PropId, List<string> propValueIds_Old, List<string> Filter_NOTIN = null) 
        {
            List<string> propValueIds = null;
            var propValueIds_filter = string.Empty;
            if (propValueIds_Old.Count == 1)
            {
                propValueIds_filter = $" pv.fid='{propValueIds_Old[0]}'";
            }
            else if (propValueIds_Old.Count > 1)
            {
                propValueIds_filter = $" pv.fid in('{string.Join("','", propValueIds_Old)}')";
            }
            var NotIn_filter = " 1 = 1 ";
            Filter_NOTIN = Filter_NOTIN?.Where(o => o.Contains(PropId)).ToList();
            if (Filter_NOTIN != null && Filter_NOTIN.Any())
            {
                NotIn_filter = string.Join(" and ", Filter_NOTIN);
            }
            string sql = $@" select distinct pv.fid from dbo.t_sel_propvalue pv with(nolock)
                            inner join t_sel_prop p with(nolock) on p.fid = pv.fpropid
                            where {propValueIds_filter} and {NotIn_filter} ";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sql);
            if (dynObjs == null || !dynObjs.Any()) return propValueIds;

            propValueIds = dynObjs.Select(o => Convert.ToString(o["fid"])).ToList();

            return propValueIds;
        }

        /// <summary>
        /// 加载选配约束条件
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="selCategoryId">选配类别ID</param>
        /// <returns>返回选配约束条件集合</returns>
        public DynamicObjectCollection LoadSelConstraint(UserContext userCtx, string selCategoryId)
        {
            if (selCategoryId.IsNullOrEmptyOrWhiteSpace()) return null;

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "c.");

            var sqlText = $@"
            select distinct cg.fname fselcategoryname,c.fname fconstraintname,ce.fconstraintcondition,ce.fconstraintval from t_sel_constraint c with(nolock) 
            inner join t_sel_constraintentry ce with(nolock) on ce.fid=c.fid 
            inner join t_sel_category cg with(nolock) on cg.fid=c.fselcategoryid 
            where c.fselcategoryid=@fselcategoryid and c.fforbidstatus='0'{aclFilter}";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fselcategoryid", System.Data.DbType.String, selCategoryId)
            };

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);

            return dynObjs;
        }

        /// <summary>
        /// 构建选配约束条件的可选属性值ID集合
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="param">参数对象</param>
        /// <returns>返回构建好的属性值ID集合</returns>
        public List<string> BuildSelConstraintPropValueIds(UserContext userCtx, SelectionConstraintMatchParam param)
        {
            param?.OutputFormulas?.Clear();

            //定义一个不存在的属性值ID
            var noExistPropValueIds = new List<string> { NoExistPropValueId };

            //如果选配范围中都没有定义属性值，则直接返回一个不存在的属性值ID，也就是当前属性没有任何可选值
            if (param.SelRangePropValueIds == null || !param.SelRangePropValueIds.Any())
            {
                return noExistPropValueIds;
            }

            //本次被约束的属性，如果没有则不用处理，如果有则必须只能有一个，不能同时存在多个
            var restrictedPropList = param.PropList?.Where(o => o.IsRestricted)?.ToList();
            if (restrictedPropList == null || !restrictedPropList.Any())
            {
                return param.SelRangePropValueIds;
            }
            if (restrictedPropList.Count > 1)
            {
                var restrictedMsgs = new List<string>();
                foreach (var item in restrictedPropList)
                {
                    restrictedMsgs.Add($"{item.PropNumber}/{item.PropName}");
                }
                throw new BusinessException($"不允许同时存在多个被约束的属性【{string.Join("，", restrictedMsgs)}】，请确保接口参数中只有一个属性对象的 isRestricted 为 true");
            }
            var restrictedProp = restrictedPropList?.FirstOrDefault();
            var restrictedPropName = restrictedProp?.PropName?.Trim();
            var restrictedPropNumber = restrictedProp?.PropNumber?.Trim();
            if (restrictedPropNumber.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"被约束的属性编码参数 propNumber 不能为空，请检查！");
            }

            //用于匹配约束条件的属性值集合，如果不存在则不用处理
            var conditionPropList = param.PropList.Where(o => !o.IsRestricted).ToList();
            if (conditionPropList == null || !conditionPropList.Any())
            {
                return param.SelRangePropValueIds;
            }

            //如果没有约束条件，则不用处理
            if (param.Constraints == null)
            {
                param.Constraints = this.LoadSelConstraint(userCtx, param.SelCategoryId);
            }
            //如果是标准定制排除掉选配约束中Sap下发的Not in条件,如果是非标定制则只需要Not in条件
            if (param.Constraints != null) 
            {
                if (!param.isNonStandard)
                {
                    DynamicObjectCollection data = new DynamicObjectCollection(param.Constraints.DynamicCollectionItemPropertyType);
                    param.Constraints.ForEach(o => { if (Convert.ToString(o["fconstraintval"]).ToUpper().IndexOf("NOT") <= -1) { data.Add(o); } });
                    param.Constraints = data;
                }
                else 
                {
                    DynamicObjectCollection data = new DynamicObjectCollection(param.Constraints.DynamicCollectionItemPropertyType);
                    param.Constraints.ForEach(o => { if (Convert.ToString(o["fconstraintval"]).ToUpper().IndexOf("NOT") > -1) { data.Add(o); } });
                    param.Constraints = data;
                }
            }

            if (param.Constraints == null || !param.Constraints.Any())
            {
                return param.SelRangePropValueIds;
            }

            var formulaService = userCtx.Container.GetService<IFormulaService>();
            if (param.PropDatas == null)
            {
                param.PropDatas = formulaService.ParseSelConstraintFormulaPropDatas(userCtx, param.Constraints);
            }

            /*
             * 选配约束条件匹配完整逻辑：每个【属性】的可选【属性值】会根据以下两个基础资料进行过滤显示可选择的值，是且的关系。
             * 1、根据单据头【选配类别】匹配找到对应的《选配范围》基础资料。
             * 2、根据单据头【选配类别】匹配找到对应的《选配约束条件》基础资料里的【选配类别】, 而匹配的逻辑为将《选配约束条件》单据体-约束明细的【约束条件】进行解析。
             * 
             *   2.1、将其他【属性】对应的【属性值】带入到【约束条件】公式中去匹配，如果能匹配到则取对应的【约束值】公式来筛选当前在选的【属性】的【属性值】，
             *   注意：其他属性如果不在【约束值】公式中，则该属性不需要参与到公式中。
             *   
             *   2.2、将其他【属性】对应的【属性值】带入到【约束值】公式中去匹配，如果能匹配到则取对应的【约束条件】公式来筛选当前在选的【属性】的【属性值】，
             *   前提条件是要先满足2.1的【约束条件】公式 并且
             *   （当前属性存在于【约束值公式】中并且【约束值公式】中包含有其他属性 或者 当前属性不存在于【约束值公式】中并且【约束值公式】中包含有其他属性），
             *   注意：其他属性如果不在【约束条件】公式中，则该属性不需要参与到公式中。
             *   
             *   2.3、
             *   如果2.1和2.2逻辑存在已确定的可选值，
             *      则将当前【属性】已确定的可选【属性值】（2.1和2.2逻辑确定的属性值）中的每个属性值 + 其他【属性】对应的【属性值】分别带入到【约束条件】公式中去匹配。
             *   如果2.1和2.2逻辑不存在已确定的可选值，
             *      则将当前【属性】未确定的可选【属性值】（也就是选配范围中定义的所有属性值）中的每个属性值 + 其他【属性】对应的【属性值】分别带入到【约束条件】公式中去匹配。
             *      
             *   匹配的前提条件是当前属性必须存在于【约束条件公式】中。
             *      
             *   如果能匹配到则取对应的【约束值】公式里的属性和属性值，来匹配其他【属性】对应的【属性值】，
             *   判断【约束值】公式里的属性和属性值是否能满足当前的其他【属性】对应的【属性值】。
             *   
             *      2.3.1、如果不满足，就表示当前【属性】的这个【属性值】要过滤掉，不允许被选到。
             *      2.3.2、如果满足，就表示当前【属性】的这个【属性值】可以被选到。
             *      2.3.3、如果连【约束条件】公式都没找到，就默认当前【属性】的这个【属性值】可以被选到。
             */

            if (!this.EnableSelConstraintNewLogic)
            {
                this.EnableSelConstraintNewLogic = "".GetAppConfig<bool>("enableSelConstraintNewLogic");
            }

            //确定不允许被选择的属性值
            var notAllowUsePropValueIds = new List<string>();

            //2.1和2.2的逻辑
            var propValueIds = this.ParseConstraintPropValueIds(
                userCtx, param, formulaService, conditionPropList, restrictedPropNumber, restrictedPropName,
                notAllowUsePropValueIds, out var formulaNotMatchPropValue);

            //当前属性最终确定的可选值
            var certainPropValueIds = new List<string>(propValueIds);

            //当前属性未确定的可选值（需要经过2.3逻辑的最终确定）
            var uncertainPropValueIds = new List<string>();

            if (certainPropValueIds.Any())
            {
                //如果2.1和2.2逻辑存在已确定的可选值，则拿已确定的可选值执行2.3逻辑
                uncertainPropValueIds.AddRange(certainPropValueIds);
            }
            else
            {
                if (notAllowUsePropValueIds.Any())
                {
                    //确定不允许被选择的属性值，不需要执行2.3逻辑
                    foreach (var propValueId in param.SelRangePropValueIds)
                    {
                        if (!notAllowUsePropValueIds.Contains(propValueId))
                        {
                            uncertainPropValueIds.Add(propValueId);
                        }
                    }
                }
                else
                {
                    //如果2.1和2.2逻辑不存在已确定的可选值，则拿未确定的可选值（也就是选配范围中定义的所有属性值）执行2.3逻辑
                    uncertainPropValueIds.AddRange(param.SelRangePropValueIds);
                }
            }

            //2.3的逻辑
            var _propValueIds = this.ParseConstraintPropValueIds(
                userCtx, param, formulaService, conditionPropList, restrictedPropNumber, restrictedPropName,
                restrictedProp, uncertainPropValueIds);
            if (_propValueIds.Any())
            {
                certainPropValueIds.AddRange(_propValueIds);
                certainPropValueIds = certainPropValueIds.Distinct().ToList();
            }

            //如果存在确定的可选值，则返回确定的可选值与选配范围中定义的属性值的交集值，
            //因为最终确定的属性值也必须是选配范围中定义的属性值
            if (certainPropValueIds.Any())
            {
                var intersectPropValueIds = param.SelRangePropValueIds.Intersect(certainPropValueIds).ToList();
                if (!intersectPropValueIds.Any())
                {
                    return noExistPropValueIds;
                }
                return intersectPropValueIds;
            }

            //有设置约束条件但是没有匹配到属性值ID时，则返回一个不存在的属性值ID，
            //目的是为了让前端选不到属性值（因为根据约束值条件查不到可选的属性值）
            if (formulaNotMatchPropValue && !propValueIds.Any())
            {
                return noExistPropValueIds;
            }

            //没有设置任何约束条件时，直接返回选配范围中定义的属性值ID
            return param.SelRangePropValueIds;
        }

        /// <summary>
        /// 解析选配约束条件公式或约束值公式中的属性值ID
        /// </summary>
        private List<string> ParseConstraintPropValueIds(
            UserContext userCtx,
            SelectionConstraintMatchParam param,
            IFormulaService formulaService,
            List<PropEntity> conditionPropList,
            string restrictedPropNumber,
            string restrictedPropName,
            List<string> notAllowUsePropValueIds,
            out bool formulaNotMatchPropValue)
        {
            formulaNotMatchPropValue = false;

            var propValueIds = new List<string>();

            var selCategoryNames = new List<string>();
            var constraintNames = new List<string>();

            //约束值公式列表
            var valueFormulas = new List<string>();

            //约束值公式对应的SQL条件列表
            var sqlWheres = new List<string>();

            //对约束条件和约束值公式进行解析
            foreach (var dynObj in param.Constraints)
            {
                var selCategoryName = Convert.ToString(dynObj?["fselcategoryname"]);
                var constraintName = Convert.ToString(dynObj?["fconstraintname"]);
                var conditionFormula = Convert.ToString(dynObj?["fconstraintcondition"]);
                var valueFormula = Convert.ToString(dynObj?["fconstraintval"]);
                if (conditionFormula.IsNullOrEmptyOrWhiteSpace()
                    || valueFormula.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                if (!selCategoryName.IsNullOrEmptyOrWhiteSpace())
                {
                    selCategoryNames.Add(selCategoryName);
                }
                if (!constraintName.IsNullOrEmptyOrWhiteSpace())
                {
                    constraintNames.Add(constraintName);
                }

                //调用公式服务：判断传入的【属性】和【属性值】是否满足公式
                var isOk = formulaService.TryParseAndExecutePropFormulaForMusiSap<bool>(
                    userCtx, conditionFormula, conditionPropList, out var errorMessage, param.PropDatas);
                if (!isOk)
                {
                    if (!errorMessage.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException($"" +
                            $"选配类别【{selCategoryName}】" +
                            $"关联的选配约束条件【{constraintName}】的约束条件公式【{conditionFormula}】" +
                            $"解析失败：{errorMessage}");
                    }
                    //公式不满足，不处理
                    continue;
                }

                //解析约束值公式中符合条件的子约束值公式
                var parsedValueFormula = this.ParseValueFormula(
                    userCtx, param, formulaService, conditionPropList,
                    restrictedPropNumber, restrictedPropName, selCategoryName,
                    constraintName, "约束值", valueFormula);
                if (parsedValueFormula.IsNullOrEmptyOrWhiteSpace()) continue;

                //调用公式服务：将公式转成SQL条件
                var sqlWhere = formulaService.TryMusiSapPropFormulaConvertToSqlWhere(
                    userCtx, parsedValueFormula, restrictedPropNumber, param.PropDatas);
                if (!sqlWhere.IsNullOrEmptyOrWhiteSpace())
                {
                    //如果包含 Not in的选配约束需要返回给调入方
                    if (parsedValueFormula.ToUpper().IndexOf("NOT") > -1)
                    {
                        param.Filter_NOTIN.Add(sqlWhere);
                    }
                    valueFormulas.Add(valueFormula);
                    sqlWheres.Add(sqlWhere);

                    //将公式翻译后输出给调用方
                    this.SetOutputFormula(userCtx, param, formulaService, restrictedPropName, restrictedPropNumber, conditionFormula, valueFormula);
                }

                //约束条件满足的情况下再2.2的逻辑
                var propNumberKv = formulaService.ParseMusiSapFormulaPropNumbers(valueFormula);
                var needProc =
                    (!propNumberKv.ContainsKey(restrictedPropNumber) && propNumberKv.Count == 1) ||
                    (propNumberKv.ContainsKey(restrictedPropNumber) && propNumberKv.Count >= 2);
                if (needProc)
                {
                    //2.2的逻辑

                    //调用公式服务：判断传入的【属性】和【属性值】是否满足公式
                    var isOk2 = formulaService.TryParseAndExecutePropFormulaForMusiSap<bool>(
                        userCtx, valueFormula, conditionPropList, out var errorMessage2, param.PropDatas);
                    if (!isOk2)
                    {
                        if (!errorMessage2.IsNullOrEmptyOrWhiteSpace())
                        {
                            throw new BusinessException($"" +
                                $"选配类别【{selCategoryName}】" +
                                $"关联的选配约束条件【{constraintName}】的约束值公式【{valueFormula}】" +
                                $"解析失败：{errorMessage2}");
                        }
                        //公式不满足，不处理
                        continue;
                    }

                    //解析约束值公式中符合条件的子约束值公式
                    var parsedValueFormula2 = this.ParseValueFormula(
                        userCtx, param, formulaService, conditionPropList,
                        restrictedPropNumber, restrictedPropName, selCategoryName,
                        constraintName, "约束条件", conditionFormula);
                    if (parsedValueFormula2.IsNullOrEmptyOrWhiteSpace()) continue;

                    //调用公式服务：将公式转成SQL条件
                    var sqlWhere2 = formulaService.TryMusiSapPropFormulaConvertToSqlWhere(
                        userCtx, parsedValueFormula2, restrictedPropNumber, param.PropDatas);
                    if (!sqlWhere2.IsNullOrEmptyOrWhiteSpace())
                    {
                        valueFormulas.Add(conditionFormula);
                        sqlWheres.Add(sqlWhere2);

                        //将公式翻译后输出给调用方
                        this.SetOutputFormula(userCtx, param, formulaService, restrictedPropName, restrictedPropNumber, valueFormula, conditionFormula);
                    }
                }
            }

            try
            {
                if (sqlWheres.Any())
                {
                    //多个约束条件对应的约束值做互斥（也就是取多个约束值的交集值，如果没有交集值，则说明该属性不存在可选值）
                    //按照条件查询属性值ID
                    propValueIds = formulaService.LoadPropValueIdsByWheres(userCtx, sqlWheres, "and");
                    if (!propValueIds.Any())
                    {
                        //约束值公式没有匹配到属性值
                        formulaNotMatchPropValue = true;
                    }

                    //如果存在多个满足约束条件的约束值公式并且多个约束值公式没有交集值时，则说明这多个约束值公式中的属性值是确定不能被选择的
                    if (sqlWheres.Count > 1 && !propValueIds.Any())
                    {
                        //找出确定不能被选择的属性值
                        var _notAllowUsePropValueIds = formulaService.LoadPropValueIdsByWheres(userCtx, sqlWheres);
                        notAllowUsePropValueIds.AddRange(_notAllowUsePropValueIds);
                    }
                }
            }
            catch (SqlException ex)
            {
                throw new BusinessException($"" +
                    $"选配类别【{string.Join("，", selCategoryNames.Distinct())}】" +
                    $"关联的选配约束条件【{string.Join("，", constraintNames.Distinct())}】的约束值公式或约束条件公式错误或者公式中设置的属性不存在，" +
                    $"无法解析：{ex.Message} 请检查以下约束值公式或约束条件公式：{string.Join("，", valueFormulas)}");
            }

            return propValueIds;
        }

        /// <summary>
        /// 解析约束值公式中符合条件的子约束值公式
        /// </summary>
        private string ParseValueFormula(
            UserContext userCtx,
            SelectionConstraintMatchParam param,
            IFormulaService formulaService,
            List<PropEntity> conditionPropList,
            string restrictedPropNumber,
            string restrictedPropName,
            string selCategoryName,
            string constraintName,
            string _valueFormulaName,
            string _valueFormula)
        {
            /*
                对约束值公式做特殊解析
                
                简单的约束值公式示例：S013=180 AND S012=200    

                复杂的约束值公式示例：
                (S005='C1411' AND S006 IN (17-38)) 
                OR (S005='C1413' AND S006 IN (10-28)) 
                OR (S005='C1414' AND S006 IN (13-30)) 
                OR (S005='C1415' AND S006 IN (11-29)) 
                OR (S005='C1412' AND S006 IN (10-28)) 
                OR (S005 IN ('C1416','C1417','C1418','C1419','C1420') AND S006 IN (10-26)) 
                OR (S005='C1421' AND S006 IN (10-25)) 
                OR (S005='C5819' AND S006 IN (17-38))
            */

            //约束值公式集合，多个值公式是或者的关系
            var parsedValueFormulas = new List<string>();

            //按 OR 拆分出公式组
            var formulaGroups = _valueFormula.Split(new string[] { "OR" }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var groupItem in formulaGroups)
            {
                //每个公式组，比如：(S005='C1411' AND S006 IN (17-38)) 
                var formulaGroup = groupItem.Trim();
                var lbIndex = formulaGroup.IndexOf("(");
                if (lbIndex == 0)
                {
                    formulaGroup = formulaGroup.Remove(lbIndex, 1); //去除第一个(

                    var rbIndex = formulaGroup.LastIndexOf(")");
                    if (rbIndex == formulaGroup.Length - 1)
                    {
                        formulaGroup = formulaGroup.Remove(rbIndex, 1); //去除最后一个)
                    }
                }

                //按 AND 拆分出子公式
                var formulas = formulaGroup.Split(new string[] { "AND" }, StringSplitOptions.RemoveEmptyEntries);
                if (formulas.Length >= 2)
                {
                    //找出公式组中的【约束条件公式】和【约束值公式】
                    var _valueFormulas = new List<string>();
                    var _conditionFormulas = new List<string>();
                    foreach (var item in formulas)
                    {
                        //这里只做简单判断，如果当前被约束的属性编码存在于公式中，则说明该公式就是约束值公式
                        if (item.Contains(restrictedPropNumber))
                        {
                            //比如：S005='C1411'
                            _valueFormulas.Add(item);
                        }
                        else
                        {
                            //比如：S006 IN (17-38)
                            _conditionFormulas.Add(item);
                        }
                    }

                    //如果没有找到约束值公式或约束条件公式，则忽略当前条件组
                    if (!_valueFormulas.Any() || !_conditionFormulas.Any()) continue;

                    //执行条件公式，检查条件是否成立
                    var _conditionFormula = string.Join(" AND ", _conditionFormulas);

                    //调用公式服务：判断传入的【属性】和【属性值】是否满足公式
                    var isOk = formulaService.TryParseAndExecutePropFormulaForMusiSap<bool>(
                        userCtx, _conditionFormula, conditionPropList, out var errorMessage, param.PropDatas);
                    //如果是多个组合公式的适合 公式不满足也需要处理。
                    if (!isOk && _conditionFormula.ToUpper().IndexOf("NOT") <= -1)
                    {
                        if (!errorMessage.IsNullOrEmptyOrWhiteSpace())
                        {
                            throw new BusinessException($"" +
                                $"选配类别【{selCategoryName}】" +
                                $"关联的选配约束条件【{constraintName}】的{_valueFormulaName}公式【{_valueFormula}】" +
                                $"解析失败：{errorMessage}");
                        }
                        //公式不满足，不处理
                        continue;
                    }
                    parsedValueFormulas.AddRange(_valueFormulas);
                }
                else
                {
                    //如果只有一个子公式，那么该子公式就是约束值公式，不需要条件
                    if (!formulas[0].IsNullOrEmptyOrWhiteSpace())
                    {
                        parsedValueFormulas.Add(formulas[0]);
                    }
                }
            }

            var parsedValueFormula = string.Join(" OR ", parsedValueFormulas);
            return parsedValueFormula;
        }

        /// <summary>
        /// 解析选配约束条件公式或约束值公式中的属性值ID
        /// </summary>
        private List<string> ParseConstraintPropValueIds(
            UserContext userCtx,
            SelectionConstraintMatchParam param,
            IFormulaService formulaService,
            List<PropEntity> conditionPropList,
            string restrictedPropNumber,
            string restrictedPropName,
            PropEntity restrictedProp,
            List<string> uncertainPropValueIds)
        {
            var propValueIds = new List<string>();

            if (!uncertainPropValueIds.Any()) return propValueIds;

            //存在属性值时，才需要做进一步的匹配，否则不处理
            var uncertainPropValueDatas = userCtx.LoadBizBillHeadDataById("sel_propvalue", uncertainPropValueIds, "fname,fnumber,fpropid");
            if (uncertainPropValueDatas == null || !uncertainPropValueDatas.Any()) return propValueIds;

            //分别将当前属性的每个未确定的可选值 + 其他属性值带入到公式中匹配
            foreach (var item in uncertainPropValueDatas)
            {
                //如果不是当前属性的属性值，则跳过
                if (!Convert.ToString(item["fpropid"]).EqualsIgnoreCase(restrictedProp.PropId))
                {
                    continue;
                }

                var _conditionPropList = new List<PropEntity>();

                //当前属性未确定的可选值
                var valueId = Convert.ToString(item["id"]);
                _conditionPropList.Add(new PropEntity
                {
                    PropId = restrictedProp.PropId,
                    PropName = restrictedProp.PropName,
                    PropNumber = restrictedProp.PropNumber,
                    PropValueDataType = restrictedProp.PropValueDataType,
                    ValueId = valueId,
                    ValueName = Convert.ToString(item["fname"]),
                    ValueNumber = Convert.ToString(item["fnumber"]),
                });

                //其他属性的属性值
                _conditionPropList.AddRange(conditionPropList);

                //检查当前属性未确定的可选值是否可以被选择
                var isCanSelected = this.CheckPropValueIsCanSelected(
                    userCtx, param, formulaService, _conditionPropList, conditionPropList, restrictedPropNumber, restrictedPropName, true);
                if (isCanSelected)
                {
                    //可以被选择
                    propValueIds.Add(valueId);
                }
            }

            return propValueIds;
        }

        /// <summary>
        /// 检查属性值是否可以被选择
        /// </summary>
        private bool CheckPropValueIsCanSelected(
            UserContext userCtx,
            SelectionConstraintMatchParam param,
            IFormulaService formulaService,
            List<PropEntity> conditionPropList,
            List<PropEntity> otherPropList,
            string restrictedPropNumber,
            string restrictedPropName,
            bool matchCondition)
        {
            // conditionPropList 参数是否匹配到【约束条件公式 或 约束值公式】
            var isMatchPropValue = false;

            // otherPropList 参数是否匹配到【约束条件公式 或 约束值公式】
            var isMatchOtherPropValue = false;

            var _conditionFormulaName = matchCondition ? "约束条件" : "约束值";
            var _valueFormulaName = matchCondition ? "约束值" : "约束条件";

            //对约束条件和约束值公式进行解析
            foreach (var dynObj in param.Constraints)
            {
                var selCategoryName = Convert.ToString(dynObj?["fselcategoryname"]);
                var constraintName = Convert.ToString(dynObj?["fconstraintname"]);
                var conditionFormula = Convert.ToString(dynObj?["fconstraintcondition"]);
                var valueFormula = Convert.ToString(dynObj?["fconstraintval"]);
                if (conditionFormula.IsNullOrEmptyOrWhiteSpace()
                    || valueFormula.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var _conditionFormula = matchCondition ? conditionFormula : valueFormula;
                var _valueFormula = matchCondition ? valueFormula : conditionFormula;

                //匹配的前提条件是当前属性必须存在于【约束条件公式】中，否则不用处理
                var propNumberKv = formulaService.ParseMusiSapFormulaPropNumbers(_conditionFormula);
                if (!propNumberKv.ContainsKey(restrictedPropNumber)) continue;

                //调用公式服务：判断传入的【属性】和【属性值】是否满足公式
                var isOk = formulaService.TryParseAndExecutePropFormulaForMusiSap<bool>(
                    userCtx, _conditionFormula, conditionPropList, out var errorMessage, param.PropDatas);
                if (!isOk)
                {
                    if (!errorMessage.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException($"" +
                            $"选配类别【{selCategoryName}】" +
                            $"关联的选配约束条件【{constraintName}】的{_conditionFormulaName}公式【{_conditionFormula}】" +
                            $"解析失败：{errorMessage}");
                    }
                    //公式不满足，不处理
                    continue;
                }
                isMatchPropValue = true;

                var isOk2 = formulaService.TryParseAndExecutePropFormulaForMusiSap<bool>(
                    userCtx, _valueFormula, otherPropList, out var errorMessage2, param.PropDatas);
                if (!isOk2)
                {
                    if (!errorMessage2.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException($"" +
                            $"选配类别【{selCategoryName}】" +
                            $"关联的选配约束条件【{constraintName}】的{_valueFormulaName}公式【{_valueFormula}】" +
                            $"解析失败：{errorMessage2}");
                    }
                    //公式不满足，不处理
                    continue;
                }
                isMatchOtherPropValue = true;
            }

            //2.3.1、如果连【约束条件】或【约束值】都没找到，就默认当前【属性】的这个【属性值】可以被选到。
            if (!isMatchPropValue) return true;

            //2.3.2、如果不满足，就表示当前【属性】的这个【属性值】要过滤掉，不允许被选到。
            //2.3.3、如果满足，就表示当前【属性】的这个【属性值】可以被选到。
            return isMatchOtherPropValue;
        }

        /// <summary>
        /// 将公式翻译后输出给调用方
        /// </summary>
        private void SetOutputFormula(
            UserContext userCtx,
            SelectionConstraintMatchParam param,
            IFormulaService formulaService,
            string restrictedPropName,
            string restrictedPropNumber,
            string conditionFormula,
            string valueFormula)
        {
            if (!param.IsOutputFormula) return;

            var conditionFormulaDesc = formulaService.TryTranslateMusiSapPropFormula(userCtx, conditionFormula, param.PropDatas);
            var valueFormulaDesc = formulaService.TryTranslateMusiSapPropFormula(userCtx, valueFormula, param.PropDatas, restrictedPropNumber);

            //将公式翻译后输出给调用方
            param.OutputFormulas.Add(new ConstraintFormulaInfo
            {
                ConditionFormula = conditionFormula,
                ConditionFormulaDesc = $"当 {conditionFormulaDesc} 时",
                ValueFormula = valueFormula,
                ValueFormulaDesc = $"{restrictedPropName} 只能为 {valueFormulaDesc}"
            });
        }

        /// <summary>
        /// 检查选配属性值是否符合约束条件
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="param">参数对象</param>
        /// <returns>检查结果</returns>
        public IOperationResult CheckSelectionPropValue(UserContext userCtx, SelectionPropValueCheckParam param)
        {
            var result = new OperationResult();
            result.IsSuccess = true;

            //验证选配属性值检查参数
            this.ValidateSelectionPropValueCheckParam(userCtx, param, result);
            if (!result.IsSuccess) return result;

            this.IgnoreAIBedPropValueCheck(userCtx, param);

            //选配范围中定义的属性值ID
            var rangePropValueIdKv = this.LoadSelRangePropValueIds(userCtx, param.SelCategoryId);

            if (param.IsNonStandard)
            {
                var constraints = this.LoadSelConstraint(userCtx, param.SelCategoryId);
                DynamicObjectCollection data = new DynamicObjectCollection(constraints.DynamicCollectionItemPropertyType);
                constraints.ForEach(o => { if (Convert.ToString(o["fconstraintval"]).ToUpper().IndexOf("NOT") > -1) { data.Add(o); } });
                constraints = data;
                var formulaService = userCtx.Container.GetService<IFormulaService>();
                var propDatas = formulaService.ParseSelConstraintFormulaPropDatas(userCtx, constraints);

                var selPropIds = param.PropList.Select(o => o.PropId).ToList();
                var matchParam = new SelectionConstraintMatchParam
                {
                    Constraints = constraints,
                    PropDatas = propDatas
                };
                matchParam.isNonStandard = true;

                //加载非标属性值ID
                var nonStandardPropValueIdKv = this.BuildNonStandardPropValueIds(userCtx, param.ProductId, selPropIds, matchParam.Filter_NOTIN);

                //加载指定属性的手工录入时自动创建的非标属性值ID
                var autoNonStandardPropValueIdKv = this.LoadAutoCreateNonStandardPropValueIds(userCtx, selPropIds);

                //检查选配属性值ID是否符合要求（选配范围属性值、自定义非标属性值、手工录入时自动创建的非标属性值，这三种选配属性值满足其一即可）
                foreach (var item in param.PropList)
                {
                    //选配范围属性值ID集合
                    var rangePropValueIds = rangePropValueIdKv?.GetValue(item.PropId);
                    matchParam.SelRangePropValueIds = rangePropValueIds;
                    //构建属性实体类集合，用于匹配约束条件
                    var propEntitys = BuildPropEntitys(param.PropList, item);
                    matchParam.PropList = propEntitys;
                    //构建选配约束过滤条件
                    var selConstraintFilter = this.BuildSelConstraintFilter(userCtx, matchParam);
                    //非标不需要校验选配范围，只需要校验选配约束中的not in
                    //var isOk = rangePropValueIdKv?.GetValue(item.PropId)?.Contains(item.ValueId) ?? false;
                    //if (!isOk)
                    //{
                    //    isOk = nonStandardPropValueIdKv?.GetValue(item.PropId)?.Contains(item.ValueId) ?? false;
                    //}
                    //if (!isOk)
                    //{
                    //    isOk = autoNonStandardPropValueIdKv?.GetValue(item.PropId)?.Contains(item.ValueId) ?? false;
                    //}
                    //if (!isOk)
                    //{ 
                    //        result.ComplexMessage.ErrorMessages.Add(
                    //        $"属性值【{item.ValueNumber}/{item.ValueName}】不是属性【{item.PropNumber}/{item.PropName}】的可选值，" +
                    //        $"请检查选配范围中是否有该属性值或检查该属性值是否满足非标条件！");
                    //        result.IsSuccess = false; 
                    //}
                    //放到最后
                    List<string> nonStandardPropValue = nonStandardPropValueIdKv?.GetValue(item.PropId);

                    if (nonStandardPropValue == null || !nonStandardPropValue.Any()) continue;

                    if (matchParam.Filter_NOTIN != null && matchParam.Filter_NOTIN.Any())
                    {
                        var propValueIds_filter = GetPropValueIdsByNotIn(userCtx, item.PropId, nonStandardPropValue, matchParam.Filter_NOTIN);

                        if (propValueIds_filter.IsNullOrEmptyOrWhiteSpace()) continue;

                        var propValueIds_notin = nonStandardPropValue.Except(propValueIds_filter);
                        if (propValueIds_notin != null && propValueIds_notin.Any())
                        {
                            //找到被过滤的属性值,要提示出来
                            var unable = propValueIds_notin.Contains(item.ValueId);
                            if (unable)
                            {
                                result.ComplexMessage.ErrorMessages.Add(
                                $"{item.PropName}:{item.ValueName}不是可选值，" +
                                //$"属性值【{item.ValueNumber}/{item.ValueName}】不是属性【{item.PropNumber}/{item.PropName}】的可选值，" +
                                $"请更换{item.PropName}或联系总部跟单！");
                                result.IsSuccess = false;
                                continue;
                            }
                        }
                    }
                }
            }
            else
            {
                var constraints = this.LoadSelConstraint(userCtx, param.SelCategoryId);
                DynamicObjectCollection data = new DynamicObjectCollection(constraints.DynamicCollectionItemPropertyType);
                constraints.ForEach(o => { if (Convert.ToString(o["fconstraintval"]).ToUpper().IndexOf("NOT") <= -1) { data.Add(o); } });
                constraints = data;
                var formulaService = userCtx.Container.GetService<IFormulaService>();
                var propDatas = formulaService.ParseSelConstraintFormulaPropDatas(userCtx, constraints);
                var matchParam = new SelectionConstraintMatchParam
                {
                    Constraints = constraints,
                    PropDatas = propDatas,
                    IsOutputFormula = true
                };
                var selPropIds = param.PropList.Select(o => o.PropId).ToList();
                //加载前端限定范围手工录入时自动创建的非标属性值ID
                var autoNonStandardPropValueIdKv = this.LoadAutoCreateNonStandardPropValueIds(userCtx, selPropIds);

                //针对每一个属性都匹配一遍选配约束条件（通过其他属性值来过滤当前属性的可选值）
                foreach (var item in param.PropList)
                {
                    //选配范围属性值ID集合
                    var rangePropValueIds = rangePropValueIdKv?.GetValue(item.PropId);
                    var isOk = rangePropValueIdKv?.GetValue(item.PropId)?.Contains(item.ValueId) ?? false;
                    if (!isOk)
                    {
                        isOk = autoNonStandardPropValueIdKv?.GetValue(item.PropId)?.Contains(item.ValueId) ?? false;
                    }
                    if (!isOk)
                    {
                        result.ComplexMessage.ErrorMessages.Add(
                            $"属性值【{item.ValueNumber}/{item.ValueName}】不是属性【{item.PropNumber}/{item.PropName}】的可选值，" +
                            $"请检查选配范围中是否有该属性值！");
                        result.IsSuccess = false;
                        continue;
                    }
                    matchParam.SelRangePropValueIds = rangePropValueIds;

                    //构建属性实体类集合，用于匹配约束条件
                    var propEntitys = BuildPropEntitys(param.PropList, item);
                    matchParam.PropList = propEntitys;

                    //构建选配约束条件的可选属性值ID集合（这里目前只能是循环匹配，因为要动态解析约束条件公式）
                    var propValueIds = this.BuildSelConstraintPropValueIds(userCtx, matchParam);

                    //取多个属性值ID集合的交集
                    var intersectPropValueIds = rangePropValueIds.Intersect(propValueIds).ToList();

                    //如果选配属性值存在于交集属性值中，则认为是符合要求的选配属性值，否则认为是不符合要求
                    isOk = intersectPropValueIds.Contains(item.ValueId);
                    if (!isOk)
                    {
                        if (matchParam.OutputFormulas.Any())
                        {
                            foreach (var formula in matchParam.OutputFormulas)
                            {
                                result.ComplexMessage.ErrorMessages.Add(
                                    $"【{formula.ConditionFormulaDesc}，{formula.ValueFormulaDesc}】，" +
                                    $"【约束条件为 {formula.ConditionFormula}，约束值为 {formula.ValueFormula}】");
                            }
                        }
                        else
                        {
                            result.ComplexMessage.ErrorMessages.Add(
                                $"属性值【{item.ValueNumber}/{item.ValueName}】不是属性【{item.PropNumber}/{item.PropName}】的可选值，" +
                                $"请检查约束条件和约束值！");
                        }
                        result.IsSuccess = false;
                    }
                }

                //数据隔离规则SQL
                var authPara = new DataQueryRuleParaInfo();
                var allProductView = userCtx.GetAuthProductDataPKID(authPara);
                //选配配件服务
                var selPartsService = userCtx.Container.GetService<ISelectionPartsService>();
                var partsMapingEntrys = selPartsService.LoadPartsMapingEntryList(userCtx, param.ProductId, param.PropList, allProductView);

                //检查匹配到的配件映射明细
                var checkResult = selPartsService.CheckPartsMapingEntrys(userCtx, param.PropList, partsMapingEntrys);
                if (!checkResult.IsSuccess)
                {
                    result.ComplexMessage.ErrorMessages.Add(checkResult.SimpleMessage);
                    result.IsSuccess = false;
                }
            }

            return result;
        }

        /// <summary>
        /// 检查选配属性值中的床架高度是否符合要求
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="param">参数对象</param>
        /// <returns>检查结果</returns>
        public IOperationResult CheckSelectionBedstockHeight(UserContext userCtx, SelectionPropValueCheckParam param)
        {
            return this.CheckSelectionBedstockHeight(userCtx, new List<SelectionPropValueCheckParam> { param });
        }

        /// <summary>
        /// 检查选配属性值中的床架高度是否符合要求
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="paramList">参数对象集合</param>
        /// <returns>检查结果</returns>
        public IOperationResult CheckSelectionBedstockHeight(UserContext userCtx, List<SelectionPropValueCheckParam> paramList)
        {
            var result = new OperationResult();
            result.IsSuccess = true;

            /*
             * 3.选配界面 点击<确定> 并且 选配的商品 对应的 商品基础资料 勾选了"是否校验高度"时, 系统会执行 "选配高度校验逻辑", 不论是否对应商品有没有关联型号, 不论型号里面的高度有没有维护好, 都会直接执行 "选配高度校验逻辑"  (非标选配的界面不需要该逻辑, 非标都不需要校验高度)
             * 1) 如果是商品没有找到对应的型号, 报错提示 "当前商品没有关联型号, 无法校验高度 !" 但是不会终止数据返回
             * 2) 如果是商品有对应型号, 就执行之前已开发的高度校验来报错终止数据返回
             * 任务链接：http://dmp.jienor.com:81/zentao/task-view-29499.html
             */

            // 非标选配的界面不需要该逻辑, 非标都不需要校验高度：过滤非标的
            var _paramList = paramList?.Where(o => !o.IsNonStandard)?.ToList();
            if (_paramList == null || !_paramList.Any()) return result;

            //如果选配的【属性】为”床架类别”对应的【属性值】=”标准气压床架”或“可调标准床架”或“标准床架”才需要进行校验 (非标选配不需要该校验)

            //找出需要处理的数据：是否存在【选配类别】属性
            _paramList = _paramList
                ?.Where(o => o?.PropList?.Any(oo => oo.PropName.Trim().EqualsIgnoreCase("床架类别")) ?? false)
                ?.ToList();
            if (_paramList == null || !_paramList.Any()) return result;

            //加载商品的型号信息
            var productIds = _paramList.Select(o => o.ProductId).ToList();
            var products = userCtx.LoadBizDataById("ydj_product", productIds);

            // 选配的商品 对应的 商品基础资料 勾选了"是否校验高度"时, 系统会执行 "选配高度校验逻辑"
            _paramList = _paramList.Where(param =>
            {
                var product = products.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(param.ProductId));

                if (product == null) return false;

                bool isCheckHeight = Convert.ToBoolean(product["fischeckheight"]);

                return isCheckHeight;
            }).ToList();
            if (_paramList == null || !_paramList.Any()) return result;

            var productService = userCtx.Container.GetService<IProductService>();
            var productTypes = productService.LoadProductSelType(userCtx, productIds);

            var typePropValues = new string[] { "标准气压床架", "可调标准床架", "标准床架" };

            foreach (var param in _paramList)
            {
                //商品对应的型号信息
                var productType = productTypes?.FirstOrDefault(o =>
                    Convert.ToString(o["fmaterialid"]).EqualsIgnoreCase(param.ProductId));

                // 如果是商品没有找到对应的型号, 报错提示 "当前商品没有关联型号, 无法校验高度 !" 但是不会终止数据返回
                if (productType == null)
                {
                    result.ComplexMessage.WarningMessages.Add("当前商品没有关联型号, 无法校验高度 ! ");
                    continue;
                }

                var typePropValue = (param
                    ?.PropList
                    ?.FirstOrDefault(o => o.PropName.Trim().EqualsIgnoreCase("床架类别"))
                    ?.ValueName ?? "").Trim();
                if (!typePropValues.Contains(typePropValue)) continue;

                var studdlePropValue = (param
                    ?.PropList
                    ?.FirstOrDefault(o => o.PropName.Trim().EqualsIgnoreCase("支撑杆"))
                    ?.ValueName ?? "").Trim();

                var sertaHeightPropValueStr = param
                    ?.PropList
                    ?.FirstOrDefault(o => o.PropName.Trim().EqualsIgnoreCase("床垫高"))
                    ?.ValueName ?? "";
                decimal.TryParse(sertaHeightPropValueStr, out var sertaHeightPropValue);

                //解析排骨架属性值里面的“排骨架”到“cm”之间的数值，比如：FCK1-001标准排骨架4cm 中的 4
                var paiSkeletonPropValue = param
                    ?.PropList
                    ?.FirstOrDefault(o => o.PropName.Trim().EqualsIgnoreCase("排骨架"))
                    ?.ValueName ?? "";
                var cmValue = 0M;
                var temps = paiSkeletonPropValue.Split(new string[] { "排骨架", "cm" }, StringSplitOptions.None);
                if (temps.Length == 3) decimal.TryParse(temps[1], out cmValue);

                //床垫高属性值 + 排骨架的cm值
                var heightValue = sertaHeightPropValue + cmValue;

                var maxHeight = 0M;
                var minHeight = 0M;

                if (typePropValue.EqualsIgnoreCase("标准气压床架"))
                {
                    maxHeight = Convert.ToDecimal(productType?["fgasbedmaxheight"] ?? 0);
                    minHeight = Convert.ToDecimal(productType?["fgasbedminheight"] ?? 0);
                }
                else if ((typePropValue.EqualsIgnoreCase("可调标准床架") || typePropValue.EqualsIgnoreCase("标准床架")) && studdlePropValue.EqualsIgnoreCase("有"))
                {
                    maxHeight = Convert.ToDecimal(productType?["felectricmaxheight"] ?? 0);
                    minHeight = Convert.ToDecimal(productType?["felectricminheight"] ?? 0);
                }
                else
                {
                    maxHeight = Convert.ToDecimal(productType?["fstandardmaxheight"] ?? 0);
                    minHeight = Convert.ToDecimal(productType?["fstandardminheight"] ?? 0);
                }

                if (heightValue > maxHeight)
                {
                    //去掉后面多余的零和点
                    var maxHeightStr = maxHeight.ToString();
                    if (maxHeightStr.Contains("."))
                    {
                        maxHeightStr = maxHeightStr.TrimEnd('0').TrimEnd('.');
                    }
                    result.ComplexMessage.ErrorMessages.Add(
                        $"当前选配商品床垫高【{heightValue}】，大于最高高度【{maxHeightStr}】不允许返回选配数据！");
                    result.IsSuccess = false;
                }

                if (heightValue < minHeight)
                {
                    var minHeightStr = minHeight.ToString();
                    if (minHeightStr.Contains("."))
                    {
                        minHeightStr = minHeightStr.TrimEnd('0').TrimEnd('.');
                    }
                    result.ComplexMessage.ErrorMessages.Add(
                        $"当前选配商品床垫高【{heightValue}】，小于最低高度【{minHeightStr}】不允许返回选配数据！");
                    result.IsSuccess = false;
                }
            }

            return result;
        }

        /// <summary>
        /// 验证选配属性值检查参数
        /// </summary>
        private void ValidateSelectionPropValueCheckParam(UserContext userCtx, SelectionPropValueCheckParam param, IOperationResult result)
        {
            if (param == null)
            {
                result.ComplexMessage.ErrorMessages.Add($"参数 param 为空，请检查！");
                result.IsSuccess = false;
                return;
            }

            if (param.ProductId.IsNullOrEmptyOrWhiteSpace())
            {
                result.ComplexMessage.ErrorMessages.Add($"参数 productId 为空，请检查！");
                result.IsSuccess = false;
            }

            if (param.SelCategoryId.IsNullOrEmptyOrWhiteSpace())
            {
                //获取商品的选配类别ID
                var productObj = userCtx.LoadBizBillHeadDataById("ydj_product", param.ProductId, "fname,fnumber,fselcategoryid");
                if (productObj == null)
                {
                    result.ComplexMessage.ErrorMessages.Add($"商品ID【{param.ProductId}】不存在或已被删除，请检查！");
                    result.IsSuccess = false;
                }
                else
                {
                    param.SelCategoryId = Convert.ToString(productObj?["fselcategoryid"]);
                    if (param.SelCategoryId.IsNullOrEmptyOrWhiteSpace())
                    {
                        result.ComplexMessage.ErrorMessages.Add($"商品【{productObj["fnumber"]}/{productObj["fname"]}】没有设置选配类别，请检查！");
                        result.IsSuccess = false;
                    }
                }
            }

            if (param.PropList == null || !param.PropList.Any())
            {
                result.ComplexMessage.ErrorMessages.Add($"参数 propList 为空，请检查！");
                result.IsSuccess = false;
            }
        }

        /// <summary>
        /// 构建属性实体类集合
        /// </summary>
        public List<PropEntity> BuildPropEntitys(List<PropEntity> propList, PropEntity currentProp)
        {
            var propEntitys = new List<PropEntity>();

            foreach (var item in propList)
            {
                //当前的属性被认为是被约束的属性（主要用于匹配约束条件中的约束值公式）
                var isRestricted = item.PropId.EqualsIgnoreCase(currentProp.PropId);

                propEntitys.Add(new PropEntity
                {
                    PropId = item.PropId,
                    PropName = item.PropName,
                    PropNumber = item.PropNumber,
                    ValueId = item.ValueId ?? "",
                    ValueName = item.ValueName ?? "",
                    ValueNumber = item.ValueNumber ?? "",
                    IsRestricted = isRestricted,
                    PropValueDataType = (PropValueDataTypeEnum)Convert.ToInt32(item.PropValueDataType)
                });
            }

            return propEntitys;
        }

        /// <summary>
        /// 加载指定选配类别关联的选配范围属性值ID
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="selCategoryId">选配类别ID</param>
        /// <returns>返回以属性ID为键，属性值ID集合为值的键值对</returns>
        private Dictionary<string, List<string>> LoadSelRangePropValueIds(UserContext userCtx, string selCategoryId)
        {
            var propValueIdKv = new Dictionary<string, List<string>>();

            if (selCategoryId.IsNullOrEmptyOrWhiteSpace()) return propValueIdKv;

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "r.");

            var sqlText = $@"
            select distinct re.fpropid,re.fpropvalueid from t_sel_range r with(nolock) 
            inner join t_sel_rangeentry re with(nolock) on re.fid=r.fid 
            where r.fselcategoryid=@fselcategoryid and r.fforbidstatus='0'{aclFilter}";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fselcategoryid", System.Data.DbType.String, selCategoryId)
            };

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (dynObjs == null || !dynObjs.Any()) return propValueIdKv;

            foreach (var dynObj in dynObjs)
            {
                var propId = Convert.ToString(dynObj?["fpropid"]);
                var _propValueIds = Convert.ToString(dynObj?["fpropvalueid"])
                    .Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                if (propId.IsNullOrEmptyOrWhiteSpace() || !_propValueIds.Any()) continue;

                List<string> propValueIds = null;
                propValueIdKv.TryGetValue(propId, out propValueIds);
                if (propValueIds == null)
                {
                    propValueIds = new List<string>();
                    propValueIdKv[propId] = propValueIds;
                }
                propValueIds.AddRange(_propValueIds);
            }

            return propValueIdKv;
        }

        /// <summary>
        /// 加载指定属性的手工录入时自动创建的非标属性值ID
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="selPropIds">属性ID集合</param>
        /// <returns>返回以属性ID为键，属性值ID集合为值的键值对</returns>
        private Dictionary<string, List<string>> LoadAutoCreateNonStandardPropValueIds(UserContext userCtx, List<string> selPropIds)
        {
            var propValueIdKv = new Dictionary<string, List<string>>();

            if (selPropIds == null || !selPropIds.Any()) return propValueIdKv;

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "");

            var sqlParam = new List<SqlParam>();

            var where = $" where fpropid<>'' and fnosuitcreate='1'{aclFilter}";
            if (selPropIds.Count == 1)
            {
                where += " and fpropid=@fpropid";
                sqlParam.Add(new SqlParam("@fpropid", System.Data.DbType.String, selPropIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < selPropIds.Count; i++)
                {
                    paramNames.Add($"@fpropid{i}");
                    sqlParam.Add(new SqlParam($"@fpropid{i}", System.Data.DbType.String, selPropIds[i]));
                }
                where += $" and fpropid in({string.Join(",", paramNames)})";
            }

            var sqlText = $@"select distinct fpropid,fid fpropvalueid from t_sel_propvalue with(nolock) {where}";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam);
            if (dynObjs == null || !dynObjs.Any()) return propValueIdKv;

            foreach (var dynObj in dynObjs)
            {
                var propId = Convert.ToString(dynObj["fpropid"]);
                var propValueId = Convert.ToString(dynObj["fpropvalueid"]);

                List<string> propValueIds = null;
                propValueIdKv.TryGetValue(propId, out propValueIds);
                if (propValueIds == null)
                {
                    propValueIds = new List<string>();
                    propValueIdKv[propId] = propValueIds;
                }

                propValueIds.Add(propValueId);
            }

            return propValueIdKv;
        }

        /// <summary>
        /// 忽略AI床垫属性值校验
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="param"></param>
        private void IgnoreAIBedPropValueCheck(UserContext userCtx, SelectionPropValueCheckParam param)
        {
            var muSiAiService = userCtx.Container.GetService<IMuSiAIService>();
            var prop_CID = muSiAiService.GetProp_CID();
            var prop_CustomParam = muSiAiService.GetProp_CustomParam();

            var isAIBed = false;

            foreach (var prop in param.PropList)
            {
                if (muSiAiService.IsProp(userCtx, prop.PropId, prop_CID))
                {
                    isAIBed = true;
                    break;
                }
            }

            if (isAIBed)
            {
                var beRemove = new List<PropEntity>();

                foreach (var prop in param.PropList)
                {
                    if (muSiAiService.IsProp(userCtx, prop.PropId, prop_CID))
                    {
                        beRemove.Add(prop);
                    }
                    if (muSiAiService.IsProp(userCtx, prop.PropId, prop_CustomParam))
                    {
                        beRemove.Add(prop);
                    }
                }

                if (beRemove.Any())
                {
                    foreach (var prop in beRemove)
                    {
                        param.PropList.Remove(prop);
                    }
                }
            }
        }

        /// <summary>
        /// 构建选配约束条件的可选属性值过滤条件
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="selCategoryId">选配类别ID</param>
        /// <param name="propList">属性实体列表</param>
        /// <returns>返回构建好的过滤条件字符串</returns>
        public string BuildSelConstraintFilter(UserContext userCtx, SelectionConstraintMatchParam param)
        {
            var filter = "";
            var propValueIds = this.BuildSelConstraintPropValueIds(userCtx, param);
            if (propValueIds.Count == 1)
            {
                filter = $" fid='{propValueIds[0]}'";
            }
            else if (propValueIds.Count > 1)
            {
                filter = $" fid in('{string.Join("','", propValueIds)}')";
            }
            return filter;
        }

        /// <summary>
        /// 根据定制商品ID和定制商品属性值匹配标准商品ID
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">定制商品ID</param>
        /// <param name="propList">定制商品属性值</param>
        /// <returns>标准商品ID</returns>
        public string LoadStandardProductId(UserContext userCtx, string productId, List<PropEntity> propList)
        {
            var standardProductId = "";

            //匹配加载指定商品的标准品转换属性配置属性明细列表
            //var standardPropEntrys = this.LoadStandardPropEntryList(userCtx, productId);

            //如果选配的属性不在配置表中 或者 在配置表中但是没有勾选【是否参与标准品计算】，则该属性不参与标准品的匹配
            //找出本次需要参与标准品匹配的所有属性
            //var _propList = new List<PropEntity>();
            //if (standardPropEntrys != null && standardPropEntrys.Any())
            //{
            //    foreach (var item in propList)
            //    {
            //        var existsObj = standardPropEntrys.FirstOrDefault(o =>
            //            Convert.ToString(o["fpropid"]).EqualsIgnoreCase(item.PropId) &&
            //            Convert.ToBoolean(o["fispartincalc"]));
            //        if (existsObj != null)
            //        {
            //            _propList.Add(item);
            //        }
            //    }
            //}

            //var _propList = propList;
            //var sqlParam = new List<SqlParam>
            //{
            //    new SqlParam("@fcustmaterialid", System.Data.DbType.String, productId)
            //};

            ////此处匹配辅助属性时，不按严格模式匹配，只要参与匹配的属性值能够匹配到标准品的辅助属性即可，当匹配到多个标准品时，只取第一个
            ////禅道任务：http://dmp.jienor.com:81/zentao/task-view-29949.html
            //var auxPropWhere = this.GetAuxPropWhere(_propList, "sme.fattrinfo", false);

            //var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "sm.");

            ////http://dmp.jienor.com:81/zentao/task-view-31733.html
            ////【慕思现场】定制品转标准品增加条件判断是否禁用：1、如果标准品的商品已禁用不能通过定制品转标准品。2、产品销售组织必须存在一条已启用的销售组织。销售组织都禁用或者空则不允许转标准品
            //var Filter = "and exists (select 1 from T_BD_MATERIAL pd with(nolock) where pd.fid= sme.fmaterialid and fforbidstatus=0 and exists (select 1 from t_bd_materialsaleorg with(nolock) where t_bd_materialsaleorg.fid = pd.fid and  fdisablestatus =1))";

            //var sqlText = $@"
            //select top 1 sme.fmaterialid from t_sel_suitemap sm with(nolock) 
            //inner join t_sel_suitemapentry sme with(nolock) on sme.fid=sm.fid 
            //where sm.fforbidstatus='0' and sme.fcustmaterialid=@fcustmaterialid {auxPropWhere}{aclFilter} {Filter}";

            //var dbService = userCtx.Container.GetService<IDBService>();
            //using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            //{
            //    if (reader.Read())
            //    {
            //        standardProductId = reader.GetValueToString("fmaterialid");
            //    }
            //}
            //弃用标准品转换属性配置属性，直接根据标准品映射获取标准品
            standardProductId = GetStandard(userCtx, productId, propList);
            return standardProductId;
        }
        /// <summary>
        /// 前端的选配ABCD， 标准品映射属性ABC 也算满足条件
        //1、先获取所有标准品映射明细及其属性、属性值组合。
        //2、根据标准品映射fentryid分组后
        //3、匹配前端传过来的属性、属性值，只要当前映射的属性值都匹配则算满足条件；
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productId"></param>
        /// <param name="propList"></param>
        private string GetStandard(UserContext userCtx, string productId, List<PropEntity> propList) 
        {
            var standardId = string.Empty;
            var TemppropList = new List<PropEntity>();
            string sql = $@"select sme.fentryid,sme.fmaterialid,sme.fattrinfo,apve.fvalueid,apve.fauxpropid from t_sel_suitemap sm with(nolock) 
                            inner join t_sel_suitemapentry sme with(nolock) on sme.fid = sm.fid
                            inner join t_bd_auxpropvalueentry as apve on apve.fid = sme.fattrinfo
                            where sm.fforbidstatus = '0' and sme.fcustmaterialid = '{productId}'
                            ";
            var dbService = userCtx.Container.GetService<IDBService>();

            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sql);
            if (dynObjs == null || !dynObjs.Any()) return standardId;

            var data_gp = dynObjs.GroupBy(o => Convert.ToString(o["fentryid"])).ToList();
            foreach (var gp in data_gp) 
            {
                bool IsSuit = true;
                var data = gp.Select(o => 
                new {
                        propvalue = Convert.ToString(o["fvalueid"]), 
                        propid = Convert.ToString(o["fauxpropid"]),
                        fmaterialid = Convert.ToString(o["fmaterialid"]) 
                    }).ToList();

                if (propList != null && propList.Any())
                {
                    for (int i = 0; i < propList.Count; i++)
                    {
                        var attrInfo = propList[i];
                        var fpropid = attrInfo.PropId;
                        var fpropvalueid = attrInfo.ValueId;
                        var propvalue = data.Where(o => o.propid == attrInfo.PropId).Select(o => o.propvalue).FirstOrDefault();
                        if (propvalue.IsNullOrEmptyOrWhiteSpace()) continue;
                        //如果存在属性值对不上
                        if (!propvalue.EqualsIgnoreCase(fpropvalueid))
                        {
                            IsSuit = false;
                        }
                    }
                }
                else 
                { 
                    break;
                } 

                if (IsSuit) 
                {
                    standardId = data.FirstOrDefault().fmaterialid;
                    break;
                }
            }
            return standardId;
        }
             

        /// <summary>
        /// 匹配加载指定商品的标准品转换属性配置属性明细列表
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <returns>返回匹配到的标准品转换属性配置属性明细列表</returns>
        private DynamicObjectCollection LoadStandardPropEntryList(UserContext userCtx, string productId)
        {
            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");

            //商品类别过滤条件
            var productService = userCtx.Container.GetService<IProductService>();
            var categoryIds = productService.LoadProductParentCategoryIds(userCtx, productId);
            if (!categoryIds.Any()) return null;

            //根据商品类别查询标准品属性配置数据
            var categoryIdFilter = "";
            if (categoryIds.Count == 1)
            {
                categoryIdFilter = $" and fcategoryid='{categoryIds[0]}'";
            }
            else
            {
                categoryIdFilter = $" and fcategoryid in ('{string.Join("','", categoryIds)}')";
            }
            var sqlText = $@"select fid,fcategoryid from t_sel_standardprop with(nolock) where {aclFilter}{categoryIdFilter}";
            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText);
            if (dynObjs == null || !dynObjs.Any()) return null;

            //如果有多个商品类别都匹配到了标准品属性配置，则取最底层商品类别对应的标准品属性配置
            var pkid = "";

            //循环从最底层的商品类别开始匹配
            for (var i = 0; i < categoryIds.Count; i++)
            {
                var dynObj = dynObjs.FirstOrDefault(o => Convert.ToString(o["fcategoryid"]).EqualsIgnoreCase(categoryIds[i]));
                if (dynObj != null)
                {
                    //只要匹配到一个就算OK
                    pkid = Convert.ToString(dynObj["fid"]);
                    break;
                }
            }
            if (pkid.IsNullOrEmptyOrWhiteSpace()) return null;

            //加载标准品属性配置明细
            var standardProp = userCtx.LoadBizDataById("sel_standardprop", pkid);
            var entrys = standardProp?["fentity"] as DynamicObjectCollection;
            return entrys;
        }

        /// <summary>
        /// 根据属性ID和属性值名称，批量加载或创建属性值基础资料
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="propList">属性信息集合</param>
        /// <returns>返回属性值数据包集合</returns>
        public List<DynamicObject> LoadOrCreatePropValue(UserContext userCtx, List<PropEntity> propList, string productId = "",bool isrange =false)
        {
            var propValueObjs = new List<DynamicObject>();

            if (propList == null) return propValueObjs;

            //只处理有效的数据
            var _propList = propList
                .Where(o => !o.PropId.IsNullOrEmptyOrWhiteSpace() && !o.ValueName.IsNullOrEmptyOrWhiteSpace())
                .ForEach(o =>
                {
                    o.ValueId = o.ValueId.Trim();
                    o.ValueName = o.ValueName.Trim();
                })
                .Distinct(o => o.PropId + o.PropName) //按属性ID+属性值名称去重
                .ToList();
            if (!_propList.Any()) return propValueObjs;

            //属性ID集合
            var propIds = _propList
                .Where(o => !o.PropId.IsNullOrEmptyOrWhiteSpace())
                .Select(o => o.PropId.Trim())
                .Distinct()
                .ToList();

            //属性值名称集合
            var valueNames = _propList
                .Where(o => !o.ValueName.IsNullOrEmptyOrWhiteSpace())
                .Select(o => o.ValueName.Trim())
                .Distinct()
                .ToList();

            if (!propIds.Any() || !valueNames.Any()) return propValueObjs;

            //根据属性ID和属性值名称加载属性值信息
            var sqlWhere = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");
            var sqlParam = new List<SqlParam>();

            //属性条件
            if (propIds.Count == 1)
            {
                sqlWhere += " and fpropid=@fpropid";
                sqlParam.Add(new SqlParam("@fpropid", System.Data.DbType.String, propIds[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < propIds.Count; i++)
                {
                    paramNames.Add($"@fpropid{i}");
                    sqlParam.Add(new SqlParam($"@fpropid{i}", System.Data.DbType.String, propIds[i]));
                }
                sqlWhere += $" and fpropid in({string.Join(",", paramNames)})";
            }

            //属性值名称条件
            if (valueNames.Count == 1)
            {
                sqlWhere += " and fname=@fname";
                sqlParam.Add(new SqlParam("@fname", System.Data.DbType.String, valueNames[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < valueNames.Count; i++)
                {
                    paramNames.Add($"@fname{i}");
                    sqlParam.Add(new SqlParam($"@fname{i}", System.Data.DbType.String, valueNames[i]));
                }
                sqlWhere += $" and fname in({string.Join(",", paramNames)})";
            }
            var products = userCtx.LoadBizBillHeadDataById("ydj_product", productId, "funstdcategory");
            //获取商品的 非标类别字段(限定范围生成属性值不需要走非标类别过滤)
            var funstdcategory = Convert.ToString(products?["funstdcategory"]);
            if (!funstdcategory.IsNullOrEmptyOrWhiteSpace() && !isrange)
            {
                var cateWhere = GetNewUndcategory(userCtx, funstdcategory, "fcategoryid");
                cateWhere = $" and (fcategoryid='' OR (1=1 {cateWhere}) )";
                sqlWhere += cateWhere;
            }

            //属性数据包
            var propDatas = userCtx.LoadBizBillHeadDataById("sel_prop", propIds, "fdatatype");

            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var propValueForm = metaService.LoadFormModel(userCtx, "sel_propvalue");
            var formDt = propValueForm.GetDynamicObjectType(userCtx);

            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, formDt);

            var reader = userCtx.GetPkIdDataReader(propValueForm, sqlWhere, sqlParam);
            var dynObjs = dm.SelectBy(reader).OfType<DynamicObject>();
            var saveObjs = new List<DynamicObject>();

            //List<string> propValueIds = new List<string>();
            //var nonStandardPropValueIdKv = BuildNonStandardPropValueIds(userCtx, productId, propIds);

            foreach (var item in _propList)
            {
                //var PropValueIds = new List<string>();
                //if (nonStandardPropValueIdKv.Any())
                //{
                //    nonStandardPropValueIdKv.TryGetValue(item.PropId, out PropValueIds);
                //    if (PropValueIds.Count() > 0)
                //    {
                //        dynObjs = dynObjs.Where(o => PropValueIds.Contains(Convert.ToString(o["id"]).Trim())).ToList();
                //    }
                //}
                var dynObj = dynObjs?.FirstOrDefault(o =>
                    Convert.ToString(o["fpropid"]).Trim().EqualsIgnoreCase(item.PropId)
                    //&& Convert.ToString(o["fname"]).Trim().EqualsIgnoreCase(item.ValueName)
                    && CultureInfo.InvariantCulture.CompareInfo.Compare(Convert.ToString(o["fname"]).Trim().ToUpper(), item.ValueName.ToUpper(), CompareOptions.IgnoreSymbols) == 0
                    );
                if (dynObj == null)
                {
                    var propData = propDatas?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(item.PropId));
                    if (propData == null)
                    {
                        var txt = isrange == true ? "" : "非标";
                        throw new BusinessException($"创建" + txt + "属性值时属性【{item.PropId}】不存在，请检查！");
                    }
                    int.TryParse(Convert.ToString(propData["fdatatype"]), out var dataType);

                    //不存在，则自动新增一个属性值
                    dynObj = new DynamicObject(formDt);
                    dynObj["fname"] = item.ValueName;
                    dynObj["fpropid"] = item.PropId;
                    dynObj["fnosuitcreate"] = true; //标记由非标录入时生成\限定范围生成的都打上标识，前端不必看到此属性值
                    saveObjs.Add(dynObj);

                    //如果属性的数据类型是数值，则属性值编码与属性值名称保持一致
                    if (dataType == (int)PropValueDataTypeEnum.Numeric)
                    {
                        dynObj["fnumber"] = item.ValueName;
                    }

                    //始终以总部的身份来创建非标值（因为属性值是所有经销商共用的基础数据）
                    dynObj["fmainorgid"] = userCtx.IsTopOrg ? userCtx.Company : userCtx.TopCompanyId;
                } 

                propValueObjs.Add(dynObj);
            }

            //保存
            if (saveObjs.Any())
            {
                var saveArray = saveObjs.ToArray();

                var preSerivce = userCtx.Container.GetService<IPrepareSaveDataService>();
                preSerivce.PrepareDataEntity(userCtx, propValueForm, saveArray, OperateOption.Create());

                var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                var invokeSave = gateway.InvokeBillOperation(userCtx,
                    propValueForm.Id,
                    saveArray,
                    "save",
                    new Dictionary<string, object>
                    {
                        { "saveBeFrom", "nonStandardAutoCreate" }
                    });
                if (!invokeSave.IsSuccess)
                {
                    var loger = userCtx.Container.GetService<ILogService>();
                    if (invokeSave.ComplexMessage.HasMessage)
                    {
                        loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【非标属性下发失败日志记录】，内容:{4}".Fmt(userCtx.UserName,
                                userCtx.UserId, userCtx.Company,
                             DateTime.Now.ToString("HH:mm:ss"), "ComplexMessage：" + invokeSave.ComplexMessage.ToJson() + "SimpleMessage：" + invokeSave.SimpleMessage),
                            "PropSelectionService");
                    }
                }
                invokeSave?.ThrowIfHasError(true, $"非标{propValueForm.Caption}【自动生成】失败！");
            }

            return propValueObjs;
        }

        /// <summary>
        /// 获取辅助属性值过滤条件，按照提供的属性值匹配
        /// </summary>
        /// <param name="propList">属性列表</param>
        /// <param name="auxPropFieldName">辅助属性字段条件字符串</param>
        /// <param name="isStrictMatch">
        /// 是否严格匹配：默认为严格匹配
        /// 1、严格匹配时，按照提供的属性值匹配，并且属性的个数也必须完全相等
        /// 2、非严格匹配时，按照提供的属性值匹配，不考虑属性的个数是否完全相等
        /// </param>
        /// <returns>过滤条件</returns>
        private string GetAuxPropWhere(List<PropEntity> propList = null, string auxPropFieldName = "te.fattrinfo", bool isStrictMatch = true)
        {
            if (auxPropFieldName.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数 auxPropFieldName 不能为空！");
            }
            var sqlWhere = new StringBuilder();
            if (propList != null && propList.Any())
            {
                for (int i = 0; i < propList.Count; i++)
                {
                    var attrInfo = propList[i];
                    sqlWhere.Append($@" and exists (select top 1 1 from t_bd_auxpropvalueentry apve with(nolock) 
                    inner join t_sel_prop ap with(nolock) on ap.fid = apve.fauxpropid 
                    where ap.fid='{attrInfo.PropId}' and apve.fvaluename='{attrInfo.ValueName}' and apve.fid={auxPropFieldName})");
                }
                if (isStrictMatch)
                {
                    //增加一个属性个数的过滤条件（属性个数必须相同）
                    sqlWhere.Append($@" and (select count(1) from t_bd_auxpropvalueentry with(nolock) where fid={auxPropFieldName} and fauxpropid<>'')={propList.Count}");
                }
            }
            else
            {
                //没有属性值时，根据空匹配，目的是为了精确匹配
                sqlWhere.Append($" and {auxPropFieldName}=''");
            }
            return sqlWhere.ToString();
        }

        /// <summary>
        /// 根据属性和属性值组合，获取辅助属性id
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="propList"></param>
        /// <returns></returns>
        public DynamicObject GetAuxPropId(UserContext userCtx, string productId, List<PropEntity> propList)
        {
            DynamicObject AttrObj = null;
 
            if (propList == null || propList.Count == 0)
            {
                return AttrObj;
            }

            var where = GetAuxPropWhere(propList, "t.fid");

            string sql = $"select top 1 t.fid as fattrinfo,attrext.fid as fattrinfo_e from t_bd_auxpropvalue t with(nolock)  " +
            $"left join t_bd_auxpropvalue_ext as attrext with(nolock) on t.fname_e = attrext.fname_e  " +
            $"where  fmaterialid='{productId}' {where}";

            //using (var reader = userCtx.Container.GetService<IDBService>().ExecuteReader(userCtx, sql))
            //{
            //    if (reader.Read())
            //    {
            //        AttrDic.Add("fattrinfo", Convert.ToString(reader.GetString("fattrinfo")));
            //        AttrDic.Add("fattrinfo_e", Convert.ToString(reader.GetString("fattrinfo_e"))); 
            //    }
            //}

            return userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx,sql)?.FirstOrDefault();
        } 
    }
}
