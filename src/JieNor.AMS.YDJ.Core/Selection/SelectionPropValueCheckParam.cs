using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core
{
    /// <summary>
    /// 选配属性值检查参数对象
    /// </summary>
    public class SelectionPropValueCheckParam
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { get; set; } = string.Empty;

        /// <summary>
        /// 选配类别ID，如果为空时，则自动获取商品对应的选配类别ID
        /// </summary>
        public string SelCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// 是否非标选配
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 属性信息集合
        /// </summary>
        public List<PropEntity> PropList { get; set; } = new List<PropEntity>();
    }
}
