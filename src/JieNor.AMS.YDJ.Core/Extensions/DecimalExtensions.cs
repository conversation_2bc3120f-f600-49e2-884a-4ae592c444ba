using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Extensions
{
    /// <summary>
    /// 数值扩展类
    /// </summary>
    public static class DecimalExtensions
    {
        /// <summary>
        /// 将指定的数值格式化为字符串并且去掉尾部无效的零和点
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="decimals">要保留的小数位</param>
        /// <returns>格式化后的字符串</returns>
        public static string Format(this decimal value, int decimals = 2)
        {
            // 默认保留两位小数
            if (decimals < 0) decimals = 2;

            // 去掉尾部无效的零和点
            var valueStr = value.ToString($"f{decimals}");
            if (valueStr.Contains("."))
            {
                valueStr = valueStr.TrimEnd('0').TrimEnd('.');
            }
            return valueStr;
        }
    }
}
