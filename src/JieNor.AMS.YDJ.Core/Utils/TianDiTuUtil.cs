using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Core.Utils
{
    /// <summary>
    /// 天地图
    /// </summary>
    public class TianDiTuUtil
    {
        private static string _url = "http://api.tianditu.gov.cn/geocoder";

        private static string _appKey = "".GetAppConfig("fw.tianditu.appKey");

        /// <summary>
        /// 根据经纬度获取详细地址
        /// </summary>
        /// <param name="latitude">纬度</param>
        /// <param name="longitude">经度</param>
        /// <returns>
        /// {
        ///     "formatted_address": "北京市西城区西什库大街19号院",
        ///     "location": {
        ///         "lon": 116.37304,
        ///         "lat": 39.92594
        ///     },
        ///     "addressComponent": {
        ///         "address": "西什库大街19号院",
        ///         "city": "北京市",
        ///         "county_code": "156110102",
        ///         "nation": "中国",
        ///         "poi_position": "西南",
        ///         "county": "西城区",
        ///         "city_code": "156110000",
        ///         "address_position": "西南",
        ///         "poi": "西什库大街19号院",
        ///         "province_code": "156110000",
        ///         "province": "北京市",
        ///         "road": "大红罗厂街",
        ///         "road_distance": 49,
        ///         "poi_distance": 21,
        ///         "address_distance": 21
        ///     }
        /// }
        /// </returns>
        public static JObject GetLocation(decimal latitude, decimal longitude)
        {
            var postStr = new JObject();
            postStr["lon"] = longitude;
            postStr["lat"] = latitude;
            postStr["ver"] = 1;

            var param = new Dictionary<string, string>
            {
                { "postStr", postStr.ToString(Newtonsoft.Json.Formatting.None) },
                { "type", "geocode" },
                { "tk", _appKey }
            };

            //string url = $"{_url}?postStr={postStr.ToString(Newtonsoft.Json.Formatting.None)}&type=geocode&tk={_appKey}";

            var userAgent = "Mozilla/5.0 (Windows NT 5.2; rv:12.0) Gecko/20100101 Firefox/12.0";

            var resp = SendWebRequest<TianDiTuResponse>(_url, param: param, userAgent: userAgent);

            return resp.result;
        }

        public class TianDiTuResponse
        {
            /// <summary>
            /// 状态（0：正确，1：错误，404：出错。）
            /// </summary>
            public string status { get; set; }

            /// <summary>
            /// 响应信息
            /// </summary>
            public string msg { get; set; }

            /// <summary>
            /// 结果
            /// </summary>
            public JObject result { get; set; }
        }


        /// <summary>
        /// 向指定的服务器地址发送Web请求，并建请求结果序列化为指定类型的实体对象
        /// </summary>
        /// <typeparam name="T">实体对象类型</typeparam>
        /// <param name="url">请求地址</param>
        /// <param name="method">请求方式：GET/POST 默认为GET</param>
        /// <param name="param">请求参数：只有 POST 方式才需要，GET 方式直接在 url 后面拼接参数即可</param>
        /// <param name="header">请求头</param>
        /// <returns>实体对象</returns>
        private static T SendWebRequest<T>(
            string url,
            Enu_HttpMethod method = Enu_HttpMethod.Get,
            Dictionary<string, string> param = null,
            Dictionary<string, string> header = null,
            string userAgent = null) where T : class
        {
            if (param != null && param.Keys.Count > 0 && method == Enu_HttpMethod.Get)
            {
                // 拼接URL参数
                var hasParam = url.Contains("?");
                foreach (var key in param)
                {
                    if (hasParam)
                    {
                        url += $"&{key.Key}={key.Value}";
                    }
                    else
                    {
                        url += $"?{key.Key}={key.Value}";
                        hasParam = true;
                    }
                }
            }

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = method.ToString();
            request.ContentType = "text/html;charset=UTF-8";
            if (!userAgent.IsNullOrEmptyOrWhiteSpace())
            {
                request.UserAgent = userAgent;
            }

            //设置请求头
            if (header != null)
            {
                foreach (var key in header.Keys)
                {
                    request.Headers.Add(key, header[key]);
                }
            }

            if (param != null && param.Keys.Count > 0 && method == Enu_HttpMethod.Post)
            {
                //设置POST请求参数
                var reqJson = JsonConvert.SerializeObject(param);
                byte[] buffer = Encoding.UTF8.GetBytes(reqJson);
                request.ContentType = "application/json;charset=utf-8";
                request.ContentLength = buffer.Length;
                var reqStream = request.GetRequestStream();
                reqStream.Write(buffer, 0, buffer.Length);
                reqStream.Close();
            }


            var response = (HttpWebResponse)request.GetResponse();
            var resStream = response.GetResponseStream();
            var resReader = new StreamReader(resStream, Encoding.UTF8);
            var resJson = resReader.ReadToEnd();
            resReader.Close();
            resStream.Close();

            var obj = JsonConvert.DeserializeObject<T>(resJson);
            return obj;
        }
    }
}
