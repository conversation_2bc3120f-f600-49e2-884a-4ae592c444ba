using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Log;
using ServiceStack;
using System.Diagnostics;
using System.Runtime.Serialization;
using System.Text;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework.Interface.Cache;
using System.Data;

namespace JieNor.AMS.YDJ.Core
{


    /// <summary>
    /// 【商品】数据权限隔离辅助类
    /// </summary> 
    [InjectService]
    public partial class ProductDataIsolateHelper : IReceivedPubMessage
    {
        

        public ProductDataIsolateHelper()
        {
            this.EnableTimeConsumingLog = "".GetAppConfig<bool>("enableTimeConsumingLog");
            if (this.EnableTimeConsumingLog)
            {
                Stopwatch = new Stopwatch();
                this.Stopwatch.Start();
            }
        }



        /// <summary>
        /// 获取经销商或门店用户户可以看到的系列信息= 总部授权系列 + 自己建的系列
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns>临时表或视图：可以看到的系列Id，总部用户返回空，由外层通用接口处理</returns>
        public Tuple<string, string> GetAgenAuthSeriDataPKID(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            TryCacheDatas(ctx);
            if (rulePara == null)
            {
                rulePara = new DataQueryRuleParaInfo();
            }

            //用户所属组织是总部，则可以看到总部的系列信息（经销商自己新建的系列不看？？？） 
            if (ctx.IsTopOrg)
            {
                return Tuple.Create("", "");
            }

            var orgInfos = GetCurrentUserAgentInfos(ctx);
            // 用户所属组织是分公司 
            if (orgInfos.Any(f => "2".EqualsIgnoreCase(f.OrgType)))
            {
                return Tuple.Create("", "");
            }

            var retAutSeriesIds = GetAutSeriesIds(ctx, rulePara, orgInfos);
            if (retAutSeriesIds.Count == 1)
            {
                return Tuple.Create("=", retAutSeriesIds.FirstOrDefault());
            }
            else if (retAutSeriesIds.Count <= 30)
            {
                return Tuple.Create("in", retAutSeriesIds.JoinEx(",", true));
            }
            else
            {
                var dbService = ctx.Container.GetService<IDBService>();
                var tempTbl = dbService.CreateTempTableWithDataList(ctx, retAutSeriesIds);

                dbService.DeleteTempTableByName(ctx, tempTbl, 5);

                retAutSeriesIds.Clear();
                retAutSeriesIds = null;

                return Tuple.Create("exists", "select {0}.fid as FPKId from {0}".Fmt(tempTbl));
            }

        }





        /// <summary>
        /// 获取经销商或门店用户户可以看到的品牌信息= 总部授权品牌 + 自己建的品牌
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns>临时表或视图：可以看到的品牌Id，总部用户返回空，由外层通用接口处理</returns>
        public Tuple<string, string> GetAgenAuthBrandDataPKID(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            if (rulePara == null)
            {
                rulePara = new DataQueryRuleParaInfo();
            }

            //用户所属组织是总部，则可以看到总部的品牌信息（经销商自己新建的品牌不看？？？） 
            if (ctx.IsTopOrg)
            {
                return Tuple.Create("", ""); ;
            }

            var retAutBrandIds = new List<string>();
            retAutBrandIds.Add("#####");//避免没有数据时不会产生临时表，导致授权错误

            var orgInfos = GetCurrentUserOrgInfos(ctx);
            // 用户所属组织是分公司，按 商品上维护的产品销售组织进行隔离，同时自己建的品牌可见
            if (orgInfos.Any(f => "2".EqualsIgnoreCase(f.OrgType)))
            {
                return Tuple.Create("", ""); ;
            }

            if (orgInfos.Any())
            {
                //TODO 用户是经销商或门店的，可以看到的品牌信息 = 总部授权品牌 + 自己建的品牌
                TryCacheDatas(ctx);

                var prdAuths = new List<AgentProductAuthInfor>();

                //门店看到的品牌=给经销商授权的品牌，
                prdAuths = ProductAuthInfo.Where(f =>
                {
                    return orgInfos.Any(x => x.OrgId == f.forgid);
                }).ToList();

                HashSet<string> brandIds, seriIds, excludeProdIds;

                ParseBrandAndSerieInfo(prdAuths, out brandIds, out seriIds, out excludeProdIds);

                //品牌授权+自建品牌
                var prdIds = GetAutBrandInfo(ctx, orgInfos, brandIds, seriIds);
                retAutBrandIds.AddRange(prdIds);

                //按商品授权部分对应的品牌信息
                var includeProdIds = GetIncludeProdBrandIds(prdAuths);
                retAutBrandIds.AddRange(includeProdIds);

                ReleaseProductAuth(prdAuths);
            }

            if (retAutBrandIds.Count == 1)
            {
                return Tuple.Create("=", retAutBrandIds.FirstOrDefault());
            }
            else if (retAutBrandIds.Count <= 30)
            {
                return Tuple.Create("in", retAutBrandIds.JoinEx(",", true));
            }
            else
            {
                var dbService = ctx.Container.GetService<IDBService>();
                var tempTbl = dbService.CreateTempTableWithDataList(ctx, retAutBrandIds);
                dbService.DeleteTempTableByName(ctx, tempTbl, 5);

                retAutBrandIds.Clear();
                retAutBrandIds = null;

                return Tuple.Create("exists", "select {0}.fid as FPKId from {0}".Fmt(tempTbl));
            }
        }




        /// <summary>
        /// 依据登录用户信息，获取用户可以查看到的商品信息。
        /// 用户所属组织是总部，则可以看到总部的商品信息;
        /// 用户所属组织是分公司，按 商品上维护的使用组织进行隔离
        /// 用户是经销商或门店的，可以看到的商品信息= 总部授权商品 + 自己建的商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns>临时表或视图：可以看到的商品Id</returns>
        public string GetAuthProductDataPKID(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            TryCacheDatas(ctx);
             
            if (rulePara == null)
            {
                rulePara = new DataQueryRuleParaInfo();
            }

            //用户所属组织是总部，则可以看到总部的商品信息（经销商自己新建的商品不看？？？） 
            if (ctx.IsTopOrg)
            {
                return " select t_bd_material.fid as FPKId from t_bd_material with(nolock) where t_bd_material.fmainorgid='{0}' ".Fmt(ctx.TopCompanyId);
            }

            var orgInfos = GetCurrentUserAgentInfos(ctx);

            if (orgInfos.Any(f => "2".EqualsIgnoreCase(f.OrgType)))
            {
                // 用户所属组织是分公司，按 商品上维护的产品销售组织进行隔离，同时自己建的商品可见
                var org = orgInfos.FirstOrDefault(f => "2".EqualsIgnoreCase(f.OrgType));
                if (org != null)
                {
                    return @" select t_bd_materialsaleorg.fid as FPKId from t_bd_materialsaleorg  with(nolock) where t_bd_materialsaleorg.fsaleorgid='{0}' 
                              union all	 
                              select t_bd_material.fid as FPKId from t_bd_material  with(nolock) where t_bd_material.fmainorgid='{1}' ".Fmt(org.OrgId, ctx.Company);
                }
            }

            var tempTbl = "";
            var cacheKey = GetTempTableCacheKey(ctx, rulePara);
            var tempTblInfo = GetCacheTempTable(ctx, cacheKey);
            if (tempTblInfo == null || tempTblInfo.TempTbl.IsNullOrEmptyOrWhiteSpace())
            {
                tempTbl = BuildTempTableDatas(ctx, rulePara, orgInfos, cacheKey);
            }
            else
            {
                tempTbl = tempTblInfo.TempTbl;
                if (!TempTableExists(ctx, tempTbl))
                {
                    tempTbl = BuildTempTableDatas(ctx, rulePara, orgInfos, cacheKey);
                }
                 
            }

            // 自建商品不走缓存
            string ret = $@"select {tempTbl}.fid as FPKId from {tempTbl}";
            if (orgInfos.Any())
            {
                ret += $@"
                        union all
                        select t_bd_material.fid as FPKId from t_bd_material with(nolock) where t_bd_material.fmainorgid in ({orgInfos.Select(s => s.OrgId).JoinEx(",", true)})";
            }

            orgInfos?.Clear();
            orgInfos = null;

            return ret;
        }

        


        public HashSet<string> GetAuthProductDataPKID(UserContext ctx, DataQueryRuleParaInfo rulePara, List<UserAgentInfo> orgInfos)
        {
            var result = new HashSet<string>();
            if (orgInfos.Any())
            {
                TryCacheDatas(ctx);

                //获取经销商对应的商品授权清单
                List<AgentProductAuthInfor> prdAuths = GetProductAuthInfo(ctx, rulePara, orgInfos);

                //第一层过滤：按商品授权清单获取对应的商品id
                var autPrdIds = GetFirstFilterProduct(ctx, rulePara, orgInfos, prdAuths);

                //第二层过滤：按商品销售组织进行过滤
                var autPrdInfo = GetSecondFilterProduct(ctx, rulePara, orgInfos, autPrdIds);

                //第三层过滤：【系列】的编码为”Z2” 时 (慕思经典-新渠道)是否可见
                autPrdInfo = GetThirdFilterProduct(ctx, rulePara, orgInfos, autPrdInfo);

                //第四层过滤：商品授权清单中设置了不允许采购的商品，排除在外
                autPrdInfo = GetCanPurFilterProduct(ctx, rulePara, autPrdInfo, prdAuths);

                //第五层过滤：大客户订单，只能选择特定系列商品
                autPrdInfo = GetVipCustomerFilterProduct(ctx, orgInfos, rulePara, autPrdInfo);

                result = new HashSet<string>(autPrdInfo.Select(f => f.MatId));

                ReleaseProductAuth(prdAuths);
                ReleaseProductSalOrg(autPrdIds);
                ReleaseProductSalOrg(autPrdInfo);
            }

            result.Add("#####");//避免没有数据时不会产生临时表，导致授权错误

            return result;
        }




        /// <summary>
        /// 获取经销商对应的商品授权清单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="orgInfos"></param>
        /// <returns></returns>
        public List<AgentProductAuthInfor> GetProductAuthInfo(UserContext ctx, DataQueryRuleParaInfo rulePara, List<UserAgentInfo> orgInfos)
        {
            TryCacheDatas(ctx);

            var prdAuths = new List<AgentProductAuthInfor>();

            //门店看到的商品=给经销商授权的商品，
            prdAuths = ProductAuthInfo.Where(f =>
            {
                return orgInfos.Any(x => x.OrgId == f.forgid && x.City == f.fcityid);
            }
            ).ToList();

            //采购订单或采购申请单，如果是摆场订单，按门店授权商品过滤（只能查看总部授予门店的摆件商品）
            var storeOrgId = "";
            if (IsPurchaseOrder(ctx, rulePara, out storeOrgId))
            {
                prdAuths = ProductAuthInfo.Where(f => storeOrgId.EqualsIgnoreCase(f.forgid)).ToList();
            }

            //销售合同或销售出库单的，看对应门店的授权情况来确定可看到的商品授权清单
            if (rulePara.SrcFormId.EqualsIgnoreCase("ydj_order") || rulePara.SrcFormId.EqualsIgnoreCase("stk_sostockout"))
            {
                var stroeProxyType = GetStoreProxyType(ctx, rulePara, out storeOrgId);
                if ("byM1".EqualsIgnoreCase(stroeProxyType) || "byA1".EqualsIgnoreCase(stroeProxyType) || "byY1".EqualsIgnoreCase(stroeProxyType))
                {
                    //门店仅代理【助眠】或【HOME】或【美居】的，按门店进行授权
                    prdAuths = ProductAuthInfo.Where(f => storeOrgId.EqualsIgnoreCase(f.forgid)).ToList();
                }
            }

            return prdAuths;
        }




        /// <summary>
        /// 获取当前登录用户可以看到的业务数据的所有组织信息；
        /// 注：按组织架构返回对应的组织信息（返回对应组织及下级组织信息）；
        /// 用于做数据的组织隔离；
        /// 比如部门列表，只能看到本组织及下级组织的部门信息；
        /// 比如用户列表，只能看到本组织及下级组织的用户信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns> 用户的授权组织信息 </returns>
        public static List<UserOrgInfo> GetCurrentUserOrgInfos(UserContext ctx)
        {
            UpdateSchema(ctx);

            var agentInfo = AgentInfo;
            var filter = new List<UserOrgInfo>();
            var sql = @"/*dialect*/select a.fid as forgid,a.fnumber,a.fname, a.forgtype,b.fbizorgid,
                            c.forgtype fbizorgtype,a.FTopCompanyId, b.fid as fuserid 
                        from t_bas_organization a  with(nolock) 
                        inner join t_sec_user b  with(nolock) on a.fid=b.fmainorgid or a.fpath like '%/' + b.fbizorgid + '%' 
                        inner join t_bas_organization c  with(nolock) on b.fbizorgid=c.fid
                         where b.fid ='{0}'  ".Fmt(ctx.UserId);
            var data = ctx.Container.GetService<IDBService>().ExecuteDynamicObject(ctx, sql);
            if (data == null || data.Count == 0)
            {
                return filter;
            }

            foreach (var item in data)
            {
                filter.Add(new UserOrgInfo()
                {
                    BizOrgId = item["fbizorgid"]?.ToString(),
                    OrgId = item["forgid"]?.ToString(),
                    OrgName = item["fname"]?.ToString(),
                    OrgNo = item["fnumber"]?.ToString(),
                    OrgType = item["forgtype"]?.ToString(),
                    BizOrgType = item["fbizorgtype"]?.ToString(),
                    TopCompanyId = item["FTopCompanyId"]?.ToString(),
                });
            }

            GetMainSubAgentMapInfo(ctx, agentInfo, filter);

            data.Clear();
            data = null;

            return filter;
        }





        /// <summary>
        /// 通过部门Id，获取部门对应门店的组织id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="deptId">部门Id</param>
        /// <returns>对应门店的组织id</returns>
        public static string GetStoreOrgIdByDept(UserContext ctx, string deptId)
        {
            if (!deptId.IsNullOrEmptyOrWhiteSpace())
            {
                var dbService = ctx.Container.GetService<IDBService>();
                //fstoreid 为门店的编码，组织的编码跟门店编码保持一致
                var sql = @"select a.fid as forgid,b.fid as fdeptid 
                            from T_BAS_ORGANIZATION a with(nolock) 
                            inner join t_bd_department b  with(nolock) on b.fstore=a.fid 
                            where b.fid='{0}' and a.ftopcompanyid='{1}' ".Fmt(deptId, ctx.TopCompanyId);
                var data = dbService.ExecuteDynamicObject(ctx, sql);
                if (data != null && data.Count > 0)
                {
                    var storeOrgId = data[0]["forgid"]?.ToString();

                    return storeOrgId;
                }
            }

            return "";
        }


        /// <summary>
        /// 通过部门Id，获取部门对应门店的经销商组织id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="deptId">部门Id</param>
        /// <returns>对应门店的经销商组织id</returns>
        public static string GetAgentOrgIdByDept(UserContext ctx, string deptId)
        {
            if (!deptId.IsNullOrEmptyOrWhiteSpace())
            {
                var dbService = ctx.Container.GetService<IDBService>();
                var sql = @"select a.fid from T_BAS_ORGANIZATION a with(nolock) 
                            inner join t_bas_agent  b  with(nolock) on b.fid=a.fid /*经销商生成组织时，保持id一样*/
                            inner join t_bas_store  c  with(nolock) on c.fagentid=b.fid 
                            inner join t_bd_department d  with(nolock) on d.fstoreid=c.fnumber /*fstoreid 为门店的编码*/
                            where d.fid='{0}' and a.ftopcompanyid='{1}' ".Fmt(deptId, ctx.TopCompanyId);
                var data = dbService.ExecuteDynamicObject(ctx, sql);
                if (data != null && data.Count > 0)
                {
                    var storeOrgId = data[0]["fid"]?.ToString();

                    return storeOrgId;
                }
            }

            return "";
        }





        /// <summary>
        /// 按 城市 + 实控人 获取当前用户对应的所有经销商组织
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public List<UserAgentInfo> GetCurrentUserAgentInfos(UserContext ctx)
        {
            TryCacheDatas(ctx);

            var agentInfo = AgentInfo;
            var result = new List<UserAgentInfo>();
            var agents = agentInfo.Where(f => f.Id == ctx.Company && f.City.IsNullOrEmptyOrWhiteSpace() == false).ToList();
            if (agents != null && agents.Count > 0)
            {
                foreach (var agent in agents)
                {
                    //按 城市 + 实控人ID 获取所有经销商组织
                    var otherAgents = agentInfo.Where(f => f.TopCompanyId == ctx.TopCompanyId
                                            && f.ActualOwnerNo.EqualsIgnoreCase(agent.ActualOwnerNo)
                                            && f.City.EqualsIgnoreCase(agent.City)).ToList();

                    if (otherAgents.Any())
                    {
                        foreach (var item in otherAgents)
                        {
                            result.Add(new UserAgentInfo()
                            {
                                Id = item.Id,
                                OrgId = item.OrgId,
                                OrgName = item.OrgName,
                                OrgNo = item.OrgNo,
                                OrgType = item.OrgType,
                                TopCompanyId = item.TopCompanyId,
                                ActualOwnerName = item.ActualOwnerName,
                                ActualOwnerNo = item.ActualOwnerNo,
                                City = item.City,
                            });
                        }
                    }

                    otherAgents?.Clear();
                    otherAgents = null;
                }

                GetMainSubAgentMapInfo(ctx, agentInfo, agents, result);
            }

            agents?.Clear();
            agents = null;

            return result;
        }

        /// <summary>
        /// 按 实控人ID 获取当前用户对应的所有经销商组织
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public List<UserAgentInfo> GetCurrentUserAgentInfosByActualOwner(UserContext ctx)
        {
            TryCacheDatas(ctx);

            var agentInfo = AgentInfo;
            var result = new List<UserAgentInfo>();
            var agents = agentInfo.Where(f => f.Id == ctx.Company && f.City.IsNullOrEmptyOrWhiteSpace() == false).ToList();
            if (agents != null && agents.Count > 0)
            {
                foreach (var agent in agents)
                {
                    //按 实控人ID 获取当前经销商组织
                    var otherAgents = agentInfo.Where(f => f.TopCompanyId == ctx.TopCompanyId
                                            && f.ActualOwnerNo.EqualsIgnoreCase(agent.ActualOwnerNo) && f.Id == ctx.Company).ToList();

                    if (otherAgents.Any())
                    {
                        foreach (var item in otherAgents)
                        {
                            result.Add(new UserAgentInfo()
                            {
                                Id = item.Id,
                                OrgId = item.OrgId,
                                OrgName = item.OrgName,
                                OrgNo = item.OrgNo,
                                OrgType = item.OrgType,
                                TopCompanyId = item.TopCompanyId,
                                ActualOwnerName = item.ActualOwnerName,
                                ActualOwnerNo = item.ActualOwnerNo,
                                City = item.City,
                            });
                        }
                    }

                    otherAgents?.Clear();
                }

                GetMainSubAgentMapInfo(ctx, agentInfo, agents, result);
            }

            agents?.Clear();

            return result;
        }


        /// <summary>
        /// 通过主子经销商匹配得到对应经商商ID
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public HashSet<string> GetAgentAllByBasMac(UserContext ctx)
        {
            var cid = ctx.IsTopOrg ? ctx.TopCompanyId : ctx.Company;
            string strSql = $@"
            select fagentid from t_bas_deliver d with(nolock) 
            where exists 
            (
	            select top 1 1 from t_bas_mac m with(nolock) 
	            where m.fmainagentid=d.fagentid and fmainagentid='{cid}' and fforbidstatus='0'
            ) 
            or exists 
            (
	            select top 1 1 from t_bas_mac m with(nolock) 
	            join t_bas_macentry me with(nolock) on m.fid = me.fid 
	            where me.fsubagentid=d.fagentid and fmainagentid='{cid}' and fforbidstatus='0'
            )";
            HashSet<string> list = new HashSet<string>();
            using (var dr = ctx.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    list.Add(Convert.ToString(dr["fagentid"]));
                }
            }
            return list;
        }

        /// <summary>
        /// 按实控人 获取当前用户对应的所有经销商组织
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public List<UserAgentInfo> GetCurrentUserAgentAll(UserContext ctx)
        {
            TryCacheDatas(ctx);

            var agentInfo = AgentInfo;
            var result = new List<UserAgentInfo>();
            var agents = agentInfo.Where(f => f.Id == ctx.Company && f.City.IsNullOrEmptyOrWhiteSpace() == false).ToList();
            if (agents != null && agents.Count > 0)
            {
                foreach (var agent in agents)
                {
                    //按实控人ID 获取所有经销商组织
                    var otherAgents = agentInfo.Where(f => f.TopCompanyId == ctx.TopCompanyId
                                            && f.ActualOwnerNo.EqualsIgnoreCase(agent.ActualOwnerNo)).ToList();
                    if (otherAgents.Any())
                    {
                        foreach (var item in otherAgents)
                        {
                            result.Add(new UserAgentInfo()
                            {
                                Id = item.Id,
                                OrgId = item.OrgId,
                                OrgName = item.OrgName,
                                OrgNo = item.OrgNo,
                                OrgType = item.OrgType,
                                TopCompanyId = item.TopCompanyId,
                                ActualOwnerName = item.ActualOwnerName,
                                ActualOwnerNo = item.ActualOwnerNo,
                                City = item.City,
                            });
                        }
                    }
                }
            }
            agents?.Clear();
            agents = null;

            return result;
        }





    }

}