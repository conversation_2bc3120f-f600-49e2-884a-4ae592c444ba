using JieNor.AMS.YDJ.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.IoC;

namespace JieNor.AMS.YDJ.Store.AppService
{
    [InjectService]
    public class ResultBrandService :IResultBrandService
    {
        public string GetFilter(string fproductid, UserContext context, string fbilltype, string seriesid, string fauxseriesid, string deptid, string formid, DynamicObject[] Entitys, string fdeliverid = "", string fattrinfo = "", string fcustomdes = "", bool fisoutspot= false, bool fisfromfirstinventory = false)
        {
            string filter = " 1=1 ";
            GetResultBrandData Data = new GetResultBrandData();
            filter = Data.GetFilterData(fproductid,context, fbilltype, seriesid, fauxseriesid, deptid, formid, Entitys, fdeliverid, fattrinfo, fcustomdes, fisoutspot, fisfromfirstinventory);
            return filter;
        }
    }
}
