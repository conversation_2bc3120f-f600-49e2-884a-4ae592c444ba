using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity.Weixin;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc.DTO;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 微信服务实现
    /// </summary>
    [InjectService]
    public class WeixinService : IWeixinService
    {
        private static string _WxaCode_Cache_Key = "wxacode:{0}:{1}:{2}:{3}";

        /// <summary>
        /// 获取缓存key
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="qywxCustomerId"></param>
        /// <param name="wxaType">小程序类型（consumer：消费者版；merchant：商户版）</param>
        /// <param name="scene"></param>
        /// <returns></returns>
        private static string GetWxaCodeCacheKey(string companyId, string qywxCustomerId, string wxaType, string scene)
        {
            return string.Format(_WxaCode_Cache_Key, companyId, qywxCustomerId, wxaType, scene);
        }

        /// <summary>
        /// 生成消费者小程序的小程序码（base64）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="data">数据，必要</param>
        /// <param name="page">小程序页面</param>
        /// <returns>小程序码</returns>
        public WeixinWxaCode GenerateConsumerWxaCode(UserContext userCtx, string data, string page)
        {
            if (data.IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("data不能为空！");

            string wxaType = "consumer";

            string scene = Guid.NewGuid().ToString("N"); //(userCtx.Company + userCtx.QyWxCustomerId + wxaType + data + page).HashString();

            WeixinWxaCode wxaCode = GetWeixinWxaCodeByCache(userCtx, wxaType, scene);

            // 缓存没有
            if (wxaCode == null)
            {
                // 先去数据库找
                wxaCode = GetWeixinWxaCodeByDB(userCtx, wxaType, scene);

                // 数据库没有，则远程获取
                if (wxaCode == null)
                {
                    wxaCode = GetWeixinWxaCodeByRemote(userCtx, wxaType, scene, page, data);

                    if (wxaCode == null)
                    {
                        return null;
                    }

                    // 保存到数据库
                    var htmlForm = userCtx.Container.GetService<IMetaModelService>()
                        .LoadFormModel(userCtx, "mp_wxacode");

                    var dt = htmlForm.GetDynamicObjectType(userCtx);

                    var dataEntity = new DynamicObject(dt);
                    dataEntity["fcreatedate"] = BeiJingTime.Now;
                    dataEntity["fmainorgid"] = userCtx.Company;
                    dataEntity["fqywxcustomerid"] = userCtx.QyWxCustomerId;
                    dataEntity["fscene"] = scene;
                    dataEntity["fdata"] = data;
                    dataEntity["fpage"] = page ?? string.Empty;
                    dataEntity["fwxatype"] = wxaType;    // 消费者
                    dataEntity["fwxacodebase64"] = wxaCode.WxaCodeBase64;

                    //保存至本地并上传至服务器
                    wxaCode.ImageId = Base64ToImage(userCtx, wxaCode.WxaCodeBase64);
                    //写入数据库字段
                    dataEntity["fimage"] = wxaCode.ImageId;

                    var dm = userCtx.Container.GetService<IDataManager>();
                    dm.InitDbContext(userCtx, dt);

                    var preService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    preService.PrepareDataEntity(userCtx, htmlForm, new DynamicObject[] { dataEntity }, OperateOption.Create());

                    dm.Save(dataEntity);
                }

                // 保存到缓存
                string value = JsonConvert.SerializeObject(wxaCode);
                string cacheKey = GetWxaCodeCacheKey(userCtx.Company, userCtx.QyWxCustomerId, wxaType, scene);

                userCtx.Container.GetService<IRedisCache>().Set(userCtx, cacheKey, value);
            }

            return wxaCode;
        }



        /// <summary>
        /// 获取消费者小程序的小程序码（base64）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="scene">数据，必要</param>
        /// <returns>小程序码</returns>
        public WeixinWxaCode GetConsumerWxaCode(UserContext userCtx, string scene)
        {
            if (scene.IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("scene不能为空！");

            string wxaType = "consumer";

            WeixinWxaCode wxaCode = GetWeixinWxaCodeByCache(userCtx, wxaType, scene);

            if (wxaCode == null)
            {
                // 去DB找
                wxaCode = GetWeixinWxaCodeByDB(userCtx, wxaType, scene);
            }
            return wxaCode;
        }

        /// <summary>
        /// 从缓存获取小程序码
        /// </summary>
        /// <param name="userCtx"></param> 
        /// <param name="wxaType"></param>
        /// <param name="scene"></param>
        /// <returns></returns>
        private WeixinWxaCode GetWeixinWxaCodeByCache(UserContext userCtx, string wxaType, string scene)
        {
            string cacheKey = GetWxaCodeCacheKey(userCtx.Company, userCtx.QyWxCustomerId, wxaType, scene);

            // 判断缓存是否存在
            var cacheService = userCtx.Container.GetService<IRedisCache>();
            var value = cacheService.Get<string>(userCtx, cacheKey);

            WeixinWxaCode wxaCode = null;

            try
            {
                wxaCode = JsonConvert.DeserializeObject<WeixinWxaCode>(value);
            }
            catch (Exception e)
            {
            }

            return wxaCode;
        }

        /// <summary>
        /// 从数据库获取小程序码
        /// </summary>
        /// <param name="userCtx"></param> 
        /// <param name="wxaType"></param>
        /// <param name="scene"></param>
        /// <returns></returns>
        private WeixinWxaCode GetWeixinWxaCodeByDB(UserContext userCtx, string wxaType, string scene)
        {
            var wxaCode = userCtx.LoadBizDataByFilter("mp_wxacode",
                  $" fscene='{scene}' and fwxatype='{wxaType}' ").LastOrDefault();

            if (wxaCode == null) return null;

            return new WeixinWxaCode
            {
                Page = Convert.ToString(wxaCode["fpage"]).Trim(),
                Scene = Convert.ToString(wxaCode["fscene"]),
                WxaCodeBase64 = Convert.ToString(wxaCode["fwxacodebase64"]),
                Data = Convert.ToString(wxaCode["fdata"])
            };
        }

        /// <summary>
        /// 从远程获取小程序码
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="wxaType"></param>
        /// <param name="scene"></param>
        /// <param name="page"></param>
        /// <param name="data">自定义数据</param>
        /// <returns></returns>
        private WeixinWxaCode GetWeixinWxaCodeByRemote(UserContext userCtx, string wxaType, string scene, string page, string data)
        {
            WeixinWxaCode result = null;

            switch (wxaType?.ToLowerInvariant())
            {
                case "consumer":

                    var dto = new EwcGetConsumerWxaCodeDTO()
                    {
                        Url = "/consumer/wxopen/getwxacode",
                        Code = userCtx.QyWxCustomerId,
                        Page = page,
                        Scene = scene
                    };

                    // 发送消息
                    var ewcResp = EwcJsonClient.Invoke<EwcGetConsumerWxaCodeData>(dto);
                    if (!ewcResp.Success)
                    {
                        var errMsg = $"调用微信API失败：{ewcResp.Code}-{ewcResp.Msg}";
                        userCtx.Container.GetService<ILogServiceEx>().Error(errMsg);

                        throw new BusinessException(ewcResp.Msg);
                    }

                    result = new WeixinWxaCode
                    {
                        Page = page,
                        Scene = scene,
                        WxaCodeBase64 = ewcResp.Data.Base64Img,
                        Data = data
                    };
                    break;
                default:
                    throw new BusinessException($"不支持获取'{wxaType}'小程序码的方法");
            }

            return result;
        }

        private static string uploadFilePath = PathUtils.GetStartupPath() + @"\uploadfiles";

        /// <summary>
        /// base64 转 png图片 保存至本地并上传
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="base64"></param>
        /// <returns>文件fileId</returns>
        private string Base64ToImage(UserContext userCtx, string base64)
        {
            base64 = base64.Replace("data:image/png;base64,", "").Replace("data:image/jgp;base64,", "").Replace("data:image/jpg;base64,", "").Replace("data:image/jpeg;base64,", "");//将base64头部信息替换
            byte[] bytes = Convert.FromBase64String(base64);
            MemoryStream memStream = new MemoryStream(bytes);
            Image mImage = Image.FromStream(memStream);
            Bitmap bp = new Bitmap(mImage);
            string imageName = $"{DateTime.Now:yyyyMMddHHmmssfff}.png";
            string filePath = Path.Combine(uploadFilePath, imageName);
            bp.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);//注意保存路径
            var result = userCtx.Container.GetService<IFileInfoService>().PostFile(filePath, imageName);
            return result["fileId"];
        }
    }
}
