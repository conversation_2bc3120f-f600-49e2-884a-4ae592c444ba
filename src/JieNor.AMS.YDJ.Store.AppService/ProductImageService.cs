using System;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Utils;
using System.Text;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 商品图库服务
    /// </summary>
    [InjectService]
    public class ProductImageService : IProductImageService
    {
        /// <summary>
        /// 元数据服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据读取服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 获取商品图片的Url
        /// </summary>
        /// <param name="userCtx">用户登录上下文</param>
        /// <param name="productId">商品Id</param>
        /// <param name="attrGroupValue">辅助属性组合值：json字符串，比如：["大人床","长两米","宽一米八"]</param>
        /// <returns>商品图片集合</returns>
        public List<Dictionary<string, object>> GetImageUrl(UserContext userCtx, string productId, string attrGroupValue)
        {
            if (productId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("商品Id为空，请检查！");
            }
            List<string> attrGroupValueList = null;
            if (!attrGroupValue.IsNullOrEmptyOrWhiteSpace())
            {
                attrGroupValueList = JsonConvert.DeserializeObject<List<string>>(attrGroupValue);
            }

            List<Dictionary<string, object>> productImageList = new List<Dictionary<string, object>>();
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            var onlyProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fonlyproductimg", false);

            if (onlyProductImg)
            {
                return productImageList;
            }

            var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "ydj_commoditygallery");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fproductid", System.Data.DbType.String, productId)
            };

            //唯一匹配到的商品图库
            DynamicObject commodityGallery = null;

            //没有传递辅助属性组合值，则根据商品Id商品图片
            if (attrGroupValueList == null || attrGroupValueList.Count <= 0)
            {
                var dataReader = userCtx.GetPkIdDataReader(htmlForm, "fmainorgid=@fmainorgid and fproductid=@fproductid and fattrinfo=''", sqlParam);
                commodityGallery = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            }
            else
            {
                //根据辅助属性组合值匹配商品图片
                List<string> whereIn = new List<string>();
                for (int i = 0; i < attrGroupValueList.Count; i++)
                {
                    whereIn.Add($"@fvalueid{i}");
                    sqlParam.Add(new SqlParam($"@fvalueid{i}", System.Data.DbType.String, attrGroupValueList[i]));
                }

                var strSql = $@"
                select top 1 fid,fproductid,fattrinfo,count(fvalueid) as vcount from 
                (
	                select t4.fid,t4.fproductid,t4.fattrinfo,t2.fvalueid from t_ydj_commoditygallery t4 
	                inner join t_bd_auxpropvalue t1 on t1.fid=t4.fattrinfo 
	                inner join t_bd_auxpropvalueentry t2 on t1.fid=t2.fid 
	                inner join t_bd_auxpropvaluemap t3 on t2.fauxpropid=t3.fauxpropid 
	                where t4.fmainorgid=@fmainorgid and t4.fproductid=@fproductid and t2.fvalueid in({string.Join(",", whereIn)}) 
	                group by t4.fid,t4.fproductid,t4.fattrinfo,t2.fvalueid
                ) te1 
                group by fid,fproductid,fattrinfo
                having count(fvalueid)={attrGroupValueList.Count}";

                var commodityGalleryId = "";
                using (var reader = this.DBService.ExecuteReader(userCtx, strSql, sqlParam))
                {
                    if (reader.Read())
                    {
                        commodityGalleryId = reader.GetString("fid");
                    }
                }
                if (!commodityGalleryId.IsNullOrEmptyOrWhiteSpace())
                {
                    commodityGallery = dm.Select(commodityGalleryId) as DynamicObject;
                }
            }

            if (commodityGallery != null)
            {
                var strImageId = Convert.ToString(commodityGallery["fimage"]);
                if (!strImageId.IsNullOrEmptyOrWhiteSpace())
                {
                    var imageIds = strImageId.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var imageId in imageIds)
                    {
                        if (imageId.IsNullOrEmptyOrWhiteSpace()) continue;

                        productImageList.Add(new Dictionary<string, object>()
                        {
                            { "fileId", imageId },
                            { "url", imageId.GetSignedFileUrl(false) }
                        });
                    }
                }
            }

            return productImageList;
        }

        /// <summary>
        /// 获取辅助属性值过滤条件
        /// </summary>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <returns>过滤条件</returns>
        private string GetAuxPropWhere(JArray auxPropVals = null)
        {
            var sqlWhere = new StringBuilder();
            if (auxPropVals != null && auxPropVals.Any())
            {
                for (int i = 0; i < auxPropVals.Count; i++)
                {
                    var attrInfo = auxPropVals[i];
                    sqlWhere.AppendFormat(@" and exists (select top 1 1 from t_bd_auxpropvalueentry apve with(nolock) 
                    inner join t_sel_prop ap with(nolock) on ap.fid = apve.fauxpropid 
                    where ap.fid='{0}' and apve.fvalueid='{1}' and apve.fid=te.fattrinfo)",
                        Convert.ToString(attrInfo["auxPropId"]), Convert.ToString(attrInfo["valueId"]));
                }

                sqlWhere.AppendFormat($" and (select COUNT(1) from t_bd_auxpropvalueentry apve with(nolock) where apve.fid=te.fattrinfo) = {auxPropVals.Count}");
            }
            else
            {
                //没有辅助属性值时，根据空匹配，目的是为了精确匹配
                sqlWhere.Append(" and te.fattrinfo=''");
            }
            return sqlWhere.ToString();
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">用户登录上下文</param>
        /// <param name="productInfos">
        /// 商品数据包
        /// 格式：
        /// {
        ///     "clientId": "",     // 唯一标识
        ///     "productId": "",    // 商品id
        ///     "attrInfo": {       // 辅助属性，id、entities二选一
        ///         "id":  "",      // 辅助属性值id
        ///         "entities": [   // 辅助属性值结合
        ///             "auxPropId": "740943490174816262",
        ///             "valueId": "咖啡色",
        ///             "valueName": "咖啡色"
        ///         ]
        ///     }
        /// }
        /// </param>
        /// <returns>
        /// 商品数据包
        /// 格式：
        /// {
        ///     "clientId": "",     // 唯一标识
        ///     "productId": "",    // 商品id
        ///     "attrInfo": {       // 辅助属性，id、entities二选一
        ///         "id":  "",      // 辅助属性值id
        ///         "entities": [   // 辅助属性值结合
        ///             "auxPropId": "740943490174816262",
        ///             "valueId": "咖啡色",
        ///             "valueName": "咖啡色"
        ///         ]
        ///     },
        ///     "image": "",        // 图片ids
        ///     "imageTxt": ""      // 图片txts
        /// }
        /// </returns>
        public List<JToken> GetImages(UserContext userCtx, JArray productInfos)
        {
            List<JToken> result = new List<JToken>();

            if (productInfos == null || productInfos.Count == 0) return result;

            /*
             * 3.根据麦浩后端销售管理参数[只取主图]、[先取图库后取主图]来获取库存商品的图片
             * a)当选择[只取主图]，直取商品主图；
             * b)当选择[先取图库后取主图]，则按以下业务逻辑来取图：
             * 先以商品最细的辅助属性组合值（辅助属性组合值不包括空值）来匹配商品图库的商品辅助属性组合值，即按“商品编码+辅助属性（所有的辅助属性维度值）”规则条件匹配商品图库获取商品图册的图片；
             * 反之，按第1-a)点的规则条件匹配不到商品图册的图片，则根据《辅助属性》的【主属性】=是的商品辅助属性组合值来匹配商品图库的商品辅助属性组合值（辅助属性组合值不包括空值），即按“商品编码+辅助属性（部分的辅助属性维度值）” 规则条件匹配商品图库获取商品图册的图片；
             * 最后，当按第1-a)、1-b)点的规格条件完全匹配不到商品图库，则直接取商品主图。
             */

            var profileService = userCtx.Container.GetService<ISystemProfile>();

            //只取主图
            var onlyProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fonlyproductimg", false);
            if (onlyProductImg)
            {
                return GetImageFromProduct(userCtx, productInfos);
            }

            JArray notMatchProductInfos = null;

            //先取图库再取主图
            var enableProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fenableproductimg", false);
            if (enableProductImg)
            {
                // 精准匹配
                result.AddRange(GetImagesFromCommodityGallery(userCtx, productInfos));

                // 主属性组合匹配
                notMatchProductInfos = FetchNotMatch(productInfos, result);
                if (notMatchProductInfos.Any())
                {
                    #region 主属性组合匹配

                    // 1. 获取所有为主属性的辅助属性
                    var allMainProps = userCtx.LoadBizDataByFilter("bd_auxproperty", "fmainprop=1");
                    var allMainPropIds = allMainProps?.Select(s => Convert.ToString(s["id"]));

                    // 2. 当有主属性的辅助属性，则进入判断
                    if (allMainPropIds != null && allMainPropIds.Count() > 0)
                    {
                        foreach (var productInfo in notMatchProductInfos)
                        {
                            JObject attrInfo = productInfo["attrInfo"] as JObject;

                            if (attrInfo != null)
                            {
                                string attrInfoId = (string)attrInfo["id"];
                                if (attrInfoId.IsNullOrEmptyOrWhiteSpace())
                                {
                                    JArray copyedAttrInfoEntities = new JArray();
                                    JArray attrInfoEntities = attrInfo["entities"] as JArray;
                                    if (attrInfoEntities != null && attrInfoEntities.Count > 0)
                                    {
                                        foreach (var attrInfoEntity in attrInfoEntities)
                                        {
                                            // 排除非主属性的辅助属性
                                            if (!allMainPropIds.Contains((string)attrInfoEntity["auxPropId"]))
                                            {
                                                continue;
                                            }

                                            copyedAttrInfoEntities.Add(attrInfoEntity);
                                        }

                                        attrInfo["entities"] = copyedAttrInfoEntities;
                                    }
                                }
                                else
                                {
                                    // 获取辅助属性组合
                                    var auxPropValueSet = userCtx.LoadBizDataById("bd_auxpropvalueset", attrInfoId);
                                    if (auxPropValueSet != null)
                                    {
                                        // 判断辅助属性组合里有没有非主属性的辅助属性
                                        var entrys = auxPropValueSet["FEntity"] as DynamicObjectCollection;
                                        if (entrys != null && entrys.Any(entry =>
                                                !allMainPropIds.Contains(Convert.ToString(entry["fauxpropid"]))))
                                        {
                                            JArray attrInfoEntities = new JArray();
                                            foreach (var entry in entrys)
                                            {
                                                var jToken = JToken.FromObject(new
                                                {
                                                    auxPropId = entry["fauxpropid"],
                                                    valueId = entry["fvalueid"]
                                                });

                                                // 排除非主属性的辅助属性
                                                if (!allMainPropIds.Contains(Convert.ToString(entry["fauxpropid"])))
                                                {
                                                    continue;
                                                }

                                                attrInfoEntities.Add(jToken);
                                            }

                                            attrInfo["entities"] = attrInfoEntities;
                                            // 清空辅助属性组合id
                                            attrInfo["id"] = string.Empty;
                                        }
                                    }
                                }
                            }

                        }

                        result.AddRange(GetImagesFromCommodityGallery(userCtx, notMatchProductInfos));
                    }

                    #endregion

                    // 单个主属性匹配
                    notMatchProductInfos = FetchNotMatch(productInfos, result);
                    if (notMatchProductInfos.Any())
                    {
                        #region 单个主属性匹配

                        // 2. 当有主属性的辅助属性，则进入判断
                        if (allMainPropIds != null && allMainPropIds.Count() > 0)
                        {
                            // 遍历所有主属性
                            foreach (var propId in allMainPropIds)
                            {
                                // 从未匹配的商品里找到有此主属性的商品
                                JArray notMatchProductInfos2 = new JArray();

                                foreach (var productInfo in notMatchProductInfos)
                                {
                                    JObject attrInfo = productInfo["attrInfo"] as JObject;

                                    JArray copyedAttrInfoEntities = new JArray();
                                    JArray attrInfoEntities = attrInfo["entities"] as JArray;

                                    if (attrInfoEntities == null) continue;

                                    foreach (var attrInfoEntity in attrInfoEntities)
                                    {
                                        if (((string)attrInfoEntity["auxPropId"]).EqualsIgnoreCase(propId))
                                        {
                                            copyedAttrInfoEntities.Add(attrInfoEntity);
                                            attrInfo["entities"] = copyedAttrInfoEntities;
                                            notMatchProductInfos2.Add(productInfo);
                                            break;
                                        }
                                    }
                                }

                                result.AddRange(GetImagesFromCommodityGallery(userCtx, notMatchProductInfos2));

                                notMatchProductInfos = FetchNotMatch(productInfos, result);
                            }
                        }

                        #endregion
                    }
                }
            }

            notMatchProductInfos = FetchNotMatch(productInfos, result);

            result.AddRange(GetImageFromProduct(userCtx, notMatchProductInfos));

            return result;
        }

        /// <summary>
        /// 未匹配图片的数据
        /// </summary>
        /// <param name="productInfos"></param>
        /// <param name="matched">已匹配的</param>
        /// <returns></returns>
        private JArray FetchNotMatch(JArray productInfos, List<JToken> matched)
        {
            JArray notMatchProductInfos = new JArray();
            foreach (var productInfo in productInfos)
            {
                string clientId = productInfo.GetJsonValue("clientId", "");
                var match = matched.FirstOrDefault(s => s.GetJsonValue("clientId", "").EqualsIgnoreCase(clientId));

                if (match == null)
                {
                    notMatchProductInfos.Add(productInfo);
                }
            }

            return notMatchProductInfos;
        }

        /// <summary>
        /// 取商品主图
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productInfos"></param>
        /// <returns></returns>
        private List<JToken> GetImageFromProduct(UserContext userCtx, JArray productInfos)
        {
            var result = new List<JToken>();

            if (productInfos == null || productInfos.Count == 0) return result;

            var productIds = productInfos.Select(s => s.GetJsonValue("productId", ""));

            var products = userCtx.LoadBizDataById("ydj_product", productIds);

            foreach (var productInfo in productInfos)
            {
                string productId = productInfo.GetJsonValue("productId", "");
                var product = products.FirstOrDefault(s => Convert.ToString(s["Id"]).EqualsIgnoreCase(productId));

                var item = new JObject();
                item["clientId"] = productInfo["clientId"];
                item["productId"] = productInfo["productId"];

                if (product != null)
                {
                    item["image"] = Convert.ToString(product["fimage"]).Trim();
                    item["imageTxt"] = Convert.ToString(product["fimage_txt"]).Trim();
                }
                else
                {
                    item["image"] = string.Empty;
                    item["imageTxt"] = string.Empty;
                }

                result.Add(item);
            }

            return result;
        }

        /// <summary>
        /// 取商品图库
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productInfos"></param>
        /// <returns></returns>
        private List<JToken> GetImagesFromCommodityGallery(UserContext userCtx, JArray productInfos)
        {
            var result = new List<JToken>();

            if (productInfos == null || productInfos.Count == 0) return result;

            List<string> sqls = new List<string>();

            foreach (var productInfo in productInfos)
            {
                JObject attrInfo = productInfo["attrInfo"] as JObject;
                if (attrInfo == null) continue;

                string attrInfoId = attrInfo.GetJsonValue("id", "");

                string auxPropWhere = attrInfoId.IsNullOrEmptyOrWhiteSpace()
                    ? GetAuxPropWhere(attrInfo["entities"] as JArray)
                    : "";

                string clientId = productInfo.GetJsonValue("clientId", "");
                string productId = productInfo.GetJsonValue("productId", "");

                string sqlWhere = $@"
            te.fmainorgid='{userCtx.Company}' and te.fproductid='{productId}' {auxPropWhere}";
                string sql = $@"select top 1 '{clientId}' as fclientid,fimage,fimage_txt from t_ydj_commoditygallery te with(nolock) where {sqlWhere}";

                sqls.Add(sql);
            }

            string sqlText = string.Join(" union all ", sqls);

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText))
            {
                while (reader.Read())
                {
                    string clientId = reader.GetValueToString("fclientid");
                    string image = reader.GetValueToString("fimage");
                    string imageTxt = reader.GetValueToString("fimage_txt");

                    var productInfo =
                        productInfos.First(s => s.GetJsonValue("clientId", "").EqualsIgnoreCase(clientId));
                    if (productInfo != null)
                    {
                        var item = new JObject();
                        item["clientId"] = productInfo["clientId"];
                        item["productId"] = productInfo["productId"];
                        item["image"] = image;
                        item["imageTxt"] = imageTxt;
                        result.Add(item);
                    }
                }
            }

            return result;
        }
    }
}