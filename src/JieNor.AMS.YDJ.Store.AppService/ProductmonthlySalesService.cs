using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    ///月均销售数量表
    /// </summary>
    [InjectService]
    public class ProductmonthlySalesService
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        public IOperationResult ProductMonthlySales(UserContext userCtx, DynamicObject dynamic, OperateOption option)
        {
            var dbService = userCtx.Container.GetService<IDBService>();
            var result = userCtx.Container.GetService<IOperationResult>();
            result.IsSuccess = true;
            var tmp = dbService.CreateTemporaryTableName(userCtx);
            int monthCount = Convert.ToInt32(dynamic["MonthCount"].ToString());

            // 获取当前日期
            DateTime today = DateTime.Now;

            // 获取本月的第一天
            DateTime firstDayOfCurrentMonth = new DateTime(today.Year, today.Month, 1);
            DateTime lastDayMaxTime = firstDayOfCurrentMonth.AddTicks(-1);
            DateTime firstDayMinTime = new DateTime(lastDayMaxTime.Year, 1, 1, 0, 0, 0);
            var dtTableName = string.Empty;
            DataTable dt = new DataTable();
            try
            {
                //先备份数据
                var deleteSql = $@"/*dialect*/select * into {tmp} from  t_ydj_productmonthlysales where fmainorgid='{userCtx.Company}'";
                dbService.ExecuteDynamicObject(userCtx, deleteSql);

                deleteSql = $@"/*dialect*/delete from t_ydj_productmonthlysales where fmainorgid='{userCtx.Company}'";
                dbService.ExecuteDynamicObject(userCtx, deleteSql);
                string datasql = $@"/*dialect*/select a.fmainorgid, b.fproductid,b.fattrinfo_e,b.fcustomdes_e, sum(b.fbizqty) as fsumqty, 
               CASE 
                WHEN {monthCount} = 0 THEN 0
                ELSE SUM(b.fbizqty)  / {monthCount}
                 END AS monthlyaverage
                from t_ydj_order a
                inner join T_BD_BILLTYPE  c (nolock) on a.fbilltype=c.fid
                inner join T_YDJ_ORDERENTRY b (nolock) on  a.fid=b.fid
                inner join T_BD_MATERIAL m (nolock) on b.fproductid=m.fid and m.fmainorgid='{userCtx.TopCompanyId}'
                where a.fcreatedate>='{firstDayMinTime}' and a.fcreatedate<='{lastDayMaxTime}' and c.fname not in ('门店上样','门店下样','v6定制柜合同','V6全屋定制合同','上样销售合同')
                and a.fcancelstatus='0'
                and m.fnumber<>'VFZ1-M001'
                and m.fsuiteflag=0
                and a.fmainorgid='{userCtx.Company}'
                and b.fclosestatus <>'4'
                and (a.fneedtransferorder=0 or (a.fneedtransferorder=1 and b.fisoutspot=1 ))
                group by a.fmainorgid, b.fproductid,b.fattrinfo_e,b.fcustomdes_e";
                var resultDatas = userCtx.ExecuteDynamicObject(datasql, null);

                dt.Columns.Add("fproductid");
                dt.Columns.Add("fattrinfo_e");
                dt.Columns.Add("fcustomdes");
                dt.Columns.Add("fsumqty");
                dt.Columns.Add("favgsaleqty");
                dt.BeginLoadData();
                foreach (var item in resultDatas)
                {
                    string fproductid = item["fproductid"].ToString();
                    string fattrinfo_e = item["fattrinfo_e"].ToString();
                    string fcustomdes = item["fcustomdes_e"].ToString();
                    string fsumqty = item["fsumqty"].ToString();
                    decimal avg = Convert.ToDecimal(item["monthlyaverage"]);
                    string favgsalecount = avg < 1 ? "1" : Math.Round(avg, 2, MidpointRounding.AwayFromZero).ToString();
                    dt.Rows.Add(fproductid, fattrinfo_e, fcustomdes, fsumqty, favgsalecount);
                }
                dt.EndLoadData();
                dtTableName = dbService.CreateTempTableWithDataTable(userCtx, dt, 1000);
                string sql = $@"/*dialect*/insert into t_ydj_productmonthlysales(fid,fmainorgid,fagentid,fproductid,fattrinfo_e,fcustomdes,fsumqty,fmonthqty,favgsaleqty,fupdatetime,fmodifierid)
                                                           select LOWER(REPLACE(LTRIM(NEWID()),'-','')) fid, '{userCtx.Company}' as 'fmainorgid','{userCtx.Company}' as 'fagentid',fproductid, fattrinfo_e,fcustomdes,fsumqty, {monthCount}  'fmonthqty',favgsaleqty,getdate() as 'fupdatetime'  ,'{userCtx.UserId}' as fmodifierid
                                                            from {dtTableName}";

                this.DBServiceEx.Execute(userCtx, sql);
                result.ComplexMessage.SuccessMessages.Add($"月均销售数量更新成功！");
            }
            catch (Exception ex)
            {
                string insertSql = $@"/*dialect*/insert into t_ydj_productmonthlysales select * from  {tmp} ";
                dbService.ExecuteDynamicObject(userCtx, insertSql);
                result.ComplexMessage.ErrorMessages.Add($"月均销售数量更新失败！{ex.Message})");
                result.IsSuccess = false;
            }
            finally
            {
                dbService.DeleteTempTableByName(userCtx, tmp, true);
                if (!dtTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(userCtx, dtTableName, true);
                }
                dt.Clear();
                dt.Dispose();
            }
            return result;
        }
    }
}
