using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 收支记录服务
    /// </summary>
    [InjectService]
    public class IncomeDisburseService : IIncomeDisburseService
    {
        /// <summary>
        /// 检查是否存在指定的关联源单（用于上游单据删除时校验）
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="sourceForm"></param>
        /// <param name="sourceEntity"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        public bool CheckIsExistsSourceOrder(UserContext userCtx, HtmlForm sourceForm, DynamicObject sourceEntity, out string errorMessage)
        {
            errorMessage = "";

            //收支记录
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "coo_incomedisburse");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

            string where = $@"fmainorgid=@fmainorgid and fsourceid=@fsourceid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("fsourceid", System.Data.DbType.String, sourceEntity["id"])
            };
            var dataReader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);
            var stockOutIn = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            if (stockOutIn != null)
            {
                errorMessage = $"{sourceForm.Caption}【{sourceEntity["fbillno"]}】已存在下游收支记录，不允许删除！";
                return true;
            }

            return false;
        }

        public void InitIncomeDisburse(UserContext userCtx, IEnumerable<DynamicObject> dataEntities)
        {
            if (userCtx == null || dataEntities == null || false == dataEntities.Any())
            {
                return;
            }
            ////设置客户手机号码
            //SetCustomerPhone(userCtx, dataEntities);
            //更新卖场对账状态
            UpdateStatementStatus(userCtx, dataEntities);
            //更新经销商对账状态
            UpdateDealerStatementStatus(userCtx, dataEntities);
        }

        /// <summary>
        /// 设置客户手机号码
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void SetCustomerPhone(UserContext userCtx, IEnumerable<DynamicObject> dataEntities)
        {
            var emptyPhoneEntities = dataEntities.Where(x => false == Convert.ToString(x["fcustomerid"]).IsNullOrEmptyOrWhiteSpace() &&
                                                             Convert.ToString(x["fcustomerphone"]).IsNullOrEmptyOrWhiteSpace())
                                                 .ToList();
            if (emptyPhoneEntities == null || emptyPhoneEntities.Count <= 0)
            {
                return;
            }
            var customerIds = emptyPhoneEntities.Select(x => Convert.ToString(x["fcustomerid"])).Distinct().ToList();
            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userCtx, "ydj_customer");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var customerEntities = dm.Select(customerIds).OfType<DynamicObject>().ToList();
            if (customerEntities == null || customerEntities.Count <= 0)
            {
                return;
            }
            foreach (var customerEntity in customerEntities)
            {
                var phone = Convert.ToString(customerEntity["fphone"]);
                var customerId = Convert.ToString(customerEntity["id"]);
                if (string.IsNullOrWhiteSpace(phone))
                {
                    continue;
                }
                foreach (var emptyPhoneEntity in emptyPhoneEntities)
                {
                    if (Convert.ToString(emptyPhoneEntity["fcustomerid"]).EqualsIgnoreCase(customerId))
                    {
                        emptyPhoneEntity["fcustomerphone"] = phone;
                    }
                }
            }
        }

        /// <summary>
        /// 更新卖场对账状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void UpdateStatementStatus(UserContext userCtx, IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var fway = Convert.ToString(dataEntity["fway"]);
                var fsourceformid = Convert.ToString(dataEntity["fsourceformid"]);
                if (fway.EqualsIgnoreCase("payway_01") && fsourceformid.EqualsIgnoreCase("ydj_order"))
                {
                    //【支付方式】=“账户支付”且【源单类型】=“销售合同”，【对账状态】默认无需对账 2023-04-07
                    dataEntity["fstatementstatus"] = "0";
                    continue;
                }

                var statementStatus = Convert.ToString(dataEntity["fstatementstatus"]);
                if (false == string.IsNullOrWhiteSpace(statementStatus) && statementStatus != "0")
                {
                    continue;
                }

                var contactUnitType = Convert.ToString(dataEntity["fcontactunittype"]);
                var contactUnitId = Convert.ToString(dataEntity["fcontactunitid"]);
                if (contactUnitType.EqualsIgnoreCase("contactunittype_02") && false == string.IsNullOrWhiteSpace(contactUnitId))
                {
                    dataEntity["fstatementstatus"] = "1";
                }
                else
                {
                    dataEntity["fstatementstatus"] = "0";
                }
            }
        }

        /// <summary>
        /// 更新经销商对账状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void UpdateDealerStatementStatus(UserContext userCtx, IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var statementStatus = Convert.ToString(dataEntity["fdealerstatus"]);
                if (false == string.IsNullOrWhiteSpace(statementStatus))
                {
                    continue;
                }

                dataEntity["fdealerstatus"] = "1";
            }
        }
    }
}