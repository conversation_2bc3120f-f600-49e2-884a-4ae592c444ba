using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Core.Metadata;
using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.BizState;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.OperationService
{
    /// <summary>
    /// 审批详情
    /// </summary>
    [InjectService("ApprovalDetail")]
    public class ApprovalDetail : AbstractOperationService
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OperationName { get { return ""; } }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem { get { return ""; } }

        /// <summary>
        /// 业务表单
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities">数据包</param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var sourceId = this.GetQueryOrSimpleParam<string>("sourceId");
            if (sourceId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("参数 sourceId 为空，请检查！");
            }

            Dictionary<string, object> data = new Dictionary<string, object>();

            bool isStartFlow = this.IsStartFlow(this.UserCtx, HtmlForm);

            data.Add("isStartFlow", isStartFlow);

            if (isStartFlow)
            {
                // 获取源单
                var sourceObj = this.UserCtx.LoadBizDataById(HtmlForm.Id, sourceId);
                if (sourceObj == null)
                {
                    this.OperationContext.Result.IsSuccess = false;
                    this.OperationContext.Result.SimpleMessage = $"请求对象不存在或已删除，不允许操作！";
                    return;
                }

                // 记录源单信息
                data.Add("sourceId", sourceObj["id"]);
                data.Add("sourceType", HtmlForm.Id);
                data.Add("sourceNumber", sourceObj["fbillno"]);

                string flowinstanceid = Convert.ToString(sourceObj["fflowinstanceid"]);
                if (flowinstanceid.IsNullOrEmptyOrWhiteSpace())
                {
                    data.Add("executionList", new List<object>());
                    data.Add("flowinstance", (object)null);
                    data.Add("content", "");

                    this.OperationContext.Result.IsSuccess = true;
                    this.OperationContext.Result.SrvData = data.ToJson();
                    return;
                }

                var flowInstance = this.UserCtx.LoadBizDataById("bpm_flowinstance", flowinstanceid);

                data.Add("flowinstance", flowInstance);

                var executionList = GetExecutionList(flowinstanceid);
                data.Add("executionList", executionList);

                var executionId = Convert.ToString(executionList?.LastOrDefault()?["id"]);
                data.Add("content", GetContent(executionId));

                data.Add("createdate", executionList?.FirstOrDefault()?["fcreatedate"]);
                data.Add("creatorid", executionList?.FirstOrDefault()?["fcreatorid"]);
                switch (HtmlForm.Id.ToLower())
                {
                    case "ydj_saleintention":
                    case "ydj_order":
                        data.Add("staffid", sourceObj["fstaffid"]);
                        data.Add("deptid", sourceObj["fdeptid"]);
                        break;
                    default:
                        data.Add("staffid", "");
                        data.Add("deptid", "");
                        break;
                }

                // 当前节点是否审批模式（会签/非会签）
                data.Add("currentNodeFlowModel", GetCurrentNodeFlowModel(flowInstance));
            }

            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SrvData = data.ToJson(); //srvData;
        }

        /// <summary>
        /// 获取任务描述
        /// </summary>
        /// <param name="executionId"></param>
        /// <returns></returns>
        private string GetContent(string executionId)
        {
            string content = string.Empty;

            if (executionId.IsNullOrEmptyOrWhiteSpace())
            {
                return content;
            }

            string sql =
                $@"select top 1 fcontent from T_BPM_SECTIONMSG where fsectionid ='{executionId}'";
            using (var reader = this.DBService.ExecuteReader(this.UserCtx, sql))
            {
                if (reader.Read())
                {
                    content = Convert.ToString(reader["fcontent"]);
                }
            }

            return content;
        }

        /// <summary>
        /// 获取审批执行
        /// </summary>
        /// <param name="flowinstanceid"></param>
        /// <returns></returns>
        private List<DynamicObject> GetExecutionList(string flowinstanceid)
        {
            var formMeta = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.UserCtx, "bpm_execution");
            var formDt = formMeta.GetDynamicObjectType(this.UserCtx);
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserCtx, formDt);

            string sqlWhere = "finstanceid = @finstanceid";
            List<SqlParam> sqlParams = new List<SqlParam> { new SqlParam("@finstanceid", DbType.String, flowinstanceid) };

            var reader = this.UserCtx.GetPkIdDataReader(formMeta, sqlWhere, sqlParams);

            var list = dm.SelectBy(reader).OfType<DynamicObject>().ToList();
            if (list.Any())
            {
                //加载引用数据
                var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
                refMgr.Load(this.UserCtx, formDt, list, false);
            }

            return list;
        }

        /// <summary>
        /// 系统是否已经启用审批流
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        private bool IsStartFlow(UserContext userCtx, HtmlForm bizForm)
        {
            if (!bizForm.ApprovalFlow)
            {
                return false;
            }

            var bpmMeta = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "bpm_businessapproval");
            var bpmnDm = userCtx.Container.GetService<IDataManager>();
            bpmnDm.InitDbContext(userCtx, bpmMeta.GetDynamicObjectType(userCtx));

            var sql = @"select fbizformid from t_bpm_businessapproval where (fmainorgid='0' or fmainorgid='{0}') and  fbizformid = '{1}'".Fmt(userCtx.Company, bizForm.Id);
            var dbSvc = userCtx.Container.GetService<IDBService>();
            var allDatas = dbSvc.ExecuteDynamicObject(userCtx, sql);

            return allDatas.Any();
        }

        /// <summary>
        /// 获取当前节点的审批模式
        /// </summary>
        /// <param name="flowInstanceObj"></param>
        /// <returns></returns>
        private string GetCurrentNodeFlowModel(DynamicObject flowInstanceObj)
        {
            string stateMachine = Convert.ToString(flowInstanceObj?["fstatemachine"]);
            if (stateMachine.IsNullOrEmptyOrWhiteSpace())
            {
                return string.Empty;
            }

            JObject stateMachineJObj = JObject.Parse(stateMachine);

            string data = stateMachineJObj["currentState"]?.GetJsonValue<string>("data");
            if (data.IsNullOrEmptyOrWhiteSpace())
            {
                return string.Empty;
            }

            JObject dataJObj = JObject.Parse(data);

            return dataJObj["param"]?["costData"]?["famodel"]?.GetJsonValue<string>("id");
        }

        #region 获取节点参与人用户列表

        /// <summary>
        /// 获取节点参与人用户列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="flowInstance"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetNodeParticipantsUserList(UserContext userCtx, BusinessState state, DynamicObject flowInstance)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            var node = JsonConvert.DeserializeObject<BPMSectionModel>(state.Data as string);

            //节点类型
            switch ((SectionTypes)node?.stepType)
            {
                case SectionTypes.Start:
                case SectionTypes.Finished:
                case SectionTypes.InterProcess:
                    //开始节点，结束节点，内部节点
                    userList = this.GetInnerNodeParticipantsUserList(userCtx, state, node, flowInstance);
                    break;
                case SectionTypes.ExterProcess:
                    //外部节点

                    break;
                default:
                    break;
            }
            return userList;
        }

        /// <summary>
        /// 获取（开始节点，结束节点，内部节点）参与人用户列表
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="node"></param>
        /// <param name="flowInstance"></param>
        /// <returns></returns>
        private Dictionary<string, string> GetInnerNodeParticipantsUserList(UserContext userCtx, BusinessState state, BPMSectionModel node, DynamicObject flowInstance)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            #region 以前使用的单个字段存储参与人时的取数逻辑（兼容历史数据，保证用户在不使用多字段参与人的情况下能正常工作）
            //参与人对象
            string participant = node?.param?.costData?.fbizformid?.id ?? "";
            if (!participant.IsNullOrEmptyOrWhiteSpace())
            {
                string[] userNumbers = participant.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                //参与人类型
                string joinType = (node?.param?.costData?.fjointype?.id ?? "").ToLowerInvariant().Trim();
                switch (joinType)
                {
                    //用户
                    case "fjointype_001":
                        var strUserNames = node?.param?.costData?.fbizformid?.name ?? "";
                        if (!strUserNames.IsNullOrEmptyOrWhiteSpace())
                        {
                            string[] userNames = strUserNames.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                            if (userNumbers.Length == userNames.Length)
                            {
                                for (int i = 0; i < userNumbers.Length; i++)
                                {
                                    userList.Add(userNumbers[i], userNames[i]);
                                }
                            }
                        }
                        break;

                    //部门
                    case "fjointype_002":
                        userList = this.GetUserListByDeptIds(userCtx, participant);
                        break;

                    //角色
                    case "fjointype_003":
                        userList = this.GetUserListByRoleIds(userCtx, participant);
                        break;

                    //参与人变量
                    case "fjointype_004":

                        var formId = flowInstance["fbizformid"] as string;
                        var billPkid = flowInstance["fbizbillpkid"] as string;
                        userList = this.GetUserListByVars(userCtx, formId, billPkid, participant);

                        //解析出特定的用户
                        foreach (var userNumber in userNumbers)
                        {
                            switch (userNumber.ToLowerInvariant().Trim())
                            {
                                //流程发布人，其实就是流程实例的初始人
                                case "@publisher":
                                    var distributor = flowInstance["finitiator"] as string;
                                    var distributorNumber = flowInstance["finitiatornumber"] as string;
                                    if (!distributor.IsNullOrEmptyOrWhiteSpace()
                                        && !distributorNumber.IsNullOrEmptyOrWhiteSpace()
                                        && !userList.ContainsKey(distributorNumber))
                                    {
                                        userList.Add(distributorNumber, distributor);
                                    }
                                    break;

                                //上一步处理人
                                case "@prevhandler":
                                    //加载指定节点的上一节点处理人（严格来说应该是上一个节点的参与人）
                                    this.LoadPrevSectionProcessor(userCtx, state.PrevState?.Value, userList);
                                    break;

                                default:
                                    break;
                            }
                        }
                        break;

                    default:
                        break;
                }
            }
            #endregion

            //现在使用多个字段存储参与人时的取数逻辑
            this.LoadParticipantsInfo(userCtx, state, node, flowInstance, userList);

            return userList;
        }

        /// <summary>
        /// 获取指定部门下的所有用户
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="deptIds">部门ID串，多个用逗号“,”隔开</param>
        /// <returns>用户列表：编码为键，名称为值</returns>
        private Dictionary<string, string> GetUserListByDeptIds(UserContext userCtx, string deptIds)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            var deptIdArray = deptIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (deptIdArray != null && deptIdArray.Length > 0)
            {
                var strSql = @"select distinct t3.fnumber,t3.fname from t_bd_department t1 
                inner join t_bd_staff t2 on t2.fdeptid = t1.fid 
                inner join t_sec_user t3 on t3.fphone = t2.fphone or t3.fnumber = t2.fphone 
                where t1.fmainorgid=@fmainorgid and t2.fmainorgid=@fmainorgid and t3.fmainorgid=@fmainorgid";

                StringBuilder sbWhere = new StringBuilder(256);
                List<SqlParam> paramList = new List<SqlParam>

                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                for (int i = 0; i < deptIdArray.Length; i++)
                {
                    var paramName = "@deptid" + i;
                    sbWhere.Append(paramName).Append(",");
                    paramList.Add(new SqlParam(paramName, DbType.String, deptIdArray[i]));
                }
                strSql += $" and t1.fid in({sbWhere.ToString().TrimEnd(',')})";

                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
                {
                    while (reader.Read())
                    {
                        var number = reader.GetString("fnumber");
                        if (!number.IsNullOrEmptyOrWhiteSpace())
                        {
                            var name = reader.GetString("fname");
                            userList.Add(number, name);
                        }
                    }
                }
            }

            return userList;
        }

        /// <summary>
        /// 获取指定角色下的所有用户
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="roleIds">角色ID串，多个用逗号“,”隔开</param>
        /// <returns>用户列表：编码为键，名称为值</returns>
        private Dictionary<string, string> GetUserListByRoleIds(UserContext userCtx, string roleIds)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            var roleIdArray = roleIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (roleIdArray != null && roleIdArray.Length > 0)
            {
                var strSql = @"select distinct t2.fnumber,t2.fname from t_sec_roleuser t1 with(nolock)
                inner join t_sec_user t2 with(nolock) on t2.fid = t1.fuserid 
                where t2.fmainorgid=@fmainorgid";

                StringBuilder sbWhere = new StringBuilder(512);
                List<SqlParam> paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                for (int i = 0; i < roleIdArray.Length; i++)
                {
                    var paramName = "@roleid" + i;
                    sbWhere.Append(paramName).Append(",");
                    paramList.Add(new SqlParam(paramName, DbType.String, roleIdArray[i]));
                }
                strSql += $" and t1.froleid in({sbWhere.ToString().TrimEnd(',')})";

                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
                {
                    while (reader.Read())
                    {
                        var number = reader.GetString("fnumber");
                        if (!number.IsNullOrEmptyOrWhiteSpace())
                        {
                            var name = reader.GetString("fname");
                            userList.Add(number, name);
                        }
                    }
                }
            }

            return userList;
        }

        /// <summary>
        /// 获取指定单据下的指定字段值（一般是用户类型字段）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="formId">表单ID</param>
        /// <param name="billPkid">单据主键ID</param>
        /// <param name="fieldIds">字段ID串，多个用逗号“,”隔开</param>
        /// <returns>用户列表：编码为键，名称为值</returns>
        private Dictionary<string, string> GetUserListByVars(UserContext userCtx, string formId, string billPkid, string fieldIds)
        {
            Dictionary<string, string> userList = new Dictionary<string, string>();

            Dictionary<string, object> fieldValues = this.GetBillFieldValueByFieldIds(userCtx, formId, billPkid, fieldIds);
            if (fieldValues.Count > 0)
            {
                var strSql = @"
                select distinct fnumber,fname from t_sec_user 
                where fmainorgid=@fmainorgid";

                StringBuilder sbWhere = new StringBuilder(256);
                List<SqlParam> paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };

                int index = 0;
                foreach (var key in fieldValues.Keys)
                {
                    var paramName = "@fid" + index;
                    sbWhere.Append(paramName).Append(",");
                    paramList.Add(new SqlParam(paramName, DbType.String, fieldValues[key] as string));

                    index++;
                }
                strSql += $" and fid in({sbWhere.ToString().TrimEnd(',')})";

                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
                {
                    while (reader.Read())
                    {
                        var number = reader.GetString("fnumber");
                        if (!number.IsNullOrEmptyOrWhiteSpace())
                        {
                            var name = reader.GetString("fname");
                            userList.Add(number, name);
                        }
                    }
                }
            }

            return userList;
        }

        /// <summary>
        /// 获取指定业务单据中指定字段值，目前只支持获取单据头的字段值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formId"></param>
        /// <param name="billPkid"></param>
        /// <param name="fieldIds"></param>
        /// <param name="bdFetchName"></param>
        /// <returns></returns>
        public Dictionary<string, object> GetBillFieldValueByFieldIds(UserContext userCtx, string formId, string billPkid, string fieldIds, bool bdFetchName = false)
        {
            Dictionary<string, object> fieldValues = new Dictionary<string, object>();

            //表单模型
            var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, formId);
            if (htmlForm == null) throw new BusinessException($"业务表单{formId}不存在，请检查！");

            //字段ID数组
            var fieldIdArray = fieldIds.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fieldIdArray == null || fieldIdArray.Length <= 0) throw new BusinessException($"业务字段ID为空，请检查！");

            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            var dataObj = dm.Select(billPkid) as DynamicObject;
            if (dataObj == null) throw new BusinessException($"业务单据{billPkid}不存在，请检查！");

            //基础资料/辅助资料 字段是否取名称
            if (bdFetchName)
            {
                //加载引用数据
                var refObjMrg = userCtx.Container.GetService<LoadReferenceObjectManager>();
                refObjMrg?.Load(userCtx, htmlForm.GetDynamicObjectType(userCtx), dataObj, false);
            }

            //当前业务表单的所有表头字段
            List<HtmlField> fieldList = htmlForm.GetFieldList().Where(t => t.IsBillHeadField).ToList();

            //提取字段值
            foreach (var fieldId in fieldIdArray)
            {
                HtmlField field = fieldList.FirstOrDefault(t => t.Id.EqualsIgnoreCase(fieldId));
                if (field == null) continue;

                object fieldValue = null;

                //基础资料/辅助资料 字段是否取名称
                if (bdFetchName && field is HtmlBaseDataField)
                {
                    var baseField = field as HtmlBaseDataField;
                    var refObjData = baseField?.RefDynamicProperty?.GetValue(dataObj) as DynamicObject;
                    if (refObjData != null)
                    {
                        var refNameField = baseField?.GetRefNameField(userCtx);
                        fieldValue = refNameField?.DynamicProperty?.GetValue<string>(refObjData)?.Trim();
                    }
                }
                else
                {
                    fieldValue = field?.DynamicProperty?.GetValue(dataObj) ?? "";

                    //简单下拉框字段
                    if (field is HtmlSimpleSelectField)
                    {
                        var simpleSelectField = field as HtmlSimpleSelectField;
                        fieldValue = simpleSelectField.GetDisplayValue(userCtx, htmlForm, dataObj, OperateOption.Create());
                    }
                }

                fieldValues.Add(fieldId, fieldValue);
            }

            return fieldValues;
        }

        /// <summary>
        /// 加载指定节点的上一节点处理人
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="designSectionId"></param>
        /// <param name="userList"></param>
        private void LoadPrevSectionProcessor(UserContext userCtx, string designSectionId, Dictionary<string, string> userList)
        {
            var dbService = userCtx.Container.GetService<IDBService>();
            var metaService = userCtx.Container.GetService<IMetaModelService>();

            string strSql = @"
            select t2.fexecutors,t2.fexecutors_txt from t_bpm_execution t1 
            inner join t_bpm_executionentry t2 on t1.fid=t2.fid 
            where t1.fmainorgid=@fmainorgid and t1.fsectionid=@fsectionid and t2.fexec<>' ' and t2.fexecnumber<>' '";

            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("@fsectionid", DbType.String, designSectionId)
            };

            var dicExecutor = new Dictionary<string, string>();

            using (var reader = dbService.ExecuteReader(userCtx, strSql, paramList))
            {
                while (reader.Read())
                {
                    var executors = reader.GetValue<string>("fexecutors");
                    var executors_txt = reader.GetValue<string>("fexecutors_txt");
                    if (executors.IsNullOrEmptyOrWhiteSpace() || executors_txt.IsNullOrEmptyOrWhiteSpace()) continue;

                    dicExecutor[executors] = executors_txt;
                }
            }
            foreach (var key in dicExecutor.Keys)
            {
                var numbers = key.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                var names = dicExecutor[key].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < numbers.Length; i++)
                {
                    if (!userList.ContainsKey(numbers[i]))
                    {
                        userList.Add(numbers[i], names[i]);
                    }
                }
            }
        }

        /// <summary>
        /// 加载参与人信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="state"></param>
        /// <param name="node"></param>
        /// <param name="flowInstance"></param>
        /// <param name="users"></param>
        private void LoadParticipantsInfo(UserContext userCtx, BusinessState state, BPMSectionModel node, DynamicObject flowInstance, Dictionary<string, string> users)
        {
            var nodeData = node?.param?.costData;
            string bizFormId = flowInstance["fbizformid"] as string;
            if (bizFormId.IsNullOrEmptyOrWhiteSpace()) return;

            var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();
            var staffIds = new List<string>();
            var staffs = new List<BaseDataSummary>();

            #region 参与人（用户）
            string joinUserIdStr = nodeData?.fjoinuser?.id ?? "";
            if (!joinUserIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var userIds = joinUserIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                this.LoadUserByUserIds(userCtx, userIds, users, true);
            }
            #endregion

            #region 参与人（部门）
            string joinDeptIdStr = nodeData?.fjoindept?.id ?? "";
            if (!joinDeptIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var _deptIds = joinDeptIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                if (_deptIds.Count > 0)
                {
                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                    if (_staffs != null) staffs.AddRange(_staffs);
                }
            }
            #endregion

            #region 参与人（角色）
            string joinRoleIdStr = nodeData?.fjoinrole?.id ?? "";
            if (!joinRoleIdStr.IsNullOrEmptyOrWhiteSpace())
            {
                var paramNames = new List<string>();
                var paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
                };
                var roleIds = joinRoleIdStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                for (int i = 0; i < roleIds.Length; i++)
                {
                    if (roleIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                    var paramName = $"@froleid{i}";
                    paramNames.Add(paramName);
                    paramList.Add(new SqlParam(paramName, DbType.String, roleIds[i]));
                }
                if (paramNames.Count > 0)
                {
                    var sqlText = $@"
                    select distinct u.fid,u.fname,u.fnumber from t_sec_roleuser ru 
                    inner join t_sec_user u on u.fid=ru.fuserid  
                    where u.fmainorgid=@fmainorgid and ru.froleid in({string.Join(",", paramNames)})";
                    using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, paramList))
                    {
                        while (reader.Read())
                        {
                            var number = reader.GetString("fnumber");
                            if (number.IsNullOrEmptyOrWhiteSpace()) continue;
                            users[number] = reader.GetString("fname");
                        }
                    }
                }
            }
            #endregion

            #region 参与人（变量）
            string joinVarStr = nodeData?.fjoinvar?.id ?? "";
            if (!joinVarStr.IsNullOrEmptyOrWhiteSpace())
            {
                var vars = joinVarStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                //特定变量
                foreach (var userNumber in vars)
                {
                    switch (userNumber.ToLowerInvariant().Trim())
                    {
                        //流程发布人，其实就是流程实例的初始人
                        case "@publisher":
                            var distributor = flowInstance["finitiator"] as string;
                            var distributorNumber = flowInstance["finitiatornumber"] as string;
                            if (!distributor.IsNullOrEmptyOrWhiteSpace()
                                && !distributorNumber.IsNullOrEmptyOrWhiteSpace()
                                && !users.ContainsKey(distributorNumber))
                            {
                                users.Add(distributorNumber, distributor);
                            }
                            break;
                        //上一步处理人
                        case "@prevhandler":
                            //加载指定节点的上一节点处理人（严格来说应该是上一个节点的参与人）
                            this.LoadPrevSectionProcessor(userCtx, state.PrevState?.Value, users);
                            break;

                        default:
                            break;
                    }
                }

                //其他变量
                var billPkid = flowInstance["fbizbillpkid"] as string;
                var otherVars = vars.Where(o => !o.EqualsIgnoreCase("@publisher") && !o.EqualsIgnoreCase("@prevhandler")).ToList();
                if (!billPkid.IsNullOrEmptyOrWhiteSpace() && otherVars.Count > 0)
                {
                    var bizForm = this.MetaModelService.LoadFormModel(userCtx, bizFormId);
                    var dm = userCtx.Container.GetService<IDataManager>();
                    dm.InitDbContext(userCtx, bizForm.GetDynamicObjectType(userCtx));
                    var dataEntity = dm.Select(billPkid) as DynamicObject;
                    if (dataEntity != null)
                    {
                        ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
                        dataEntitySet.Parse(userCtx, new DynamicObject[] { dataEntity }, bizForm);

                        var staffFormId = baseFormProvider.GetStaffFormObject(userCtx);
                        var deptFormId = baseFormProvider.GetDeptFormObject(userCtx);

                        for (int i = 0; i < otherVars.Count; i++)
                        {
                            if (otherVars[i].IsNullOrEmptyOrWhiteSpace()) continue;
                            var fieldKeys = otherVars[i].Split(new char[] { '$' }, StringSplitOptions.RemoveEmptyEntries);
                            var fieldKey = fieldKeys[0];
                            var field = bizForm.GetField(fieldKey) as HtmlBaseDataField;
                            if (field == null) continue;

                            if (field.RefFormId.EqualsIgnoreCase("sec_user"))
                            {
                                var _userIds = new List<string>();
                                this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _userIds);
                                if (_userIds != null)
                                {
                                    if (fieldKeys.Length == 2)
                                    {
                                        //获取指定用户所在的部门信息
                                        var _depts = baseFormProvider.GetUserDepts(userCtx, _userIds);
                                        var _deptIds = _depts?.Select(o => o.Id)?.ToList();
                                        if (_deptIds != null)
                                        {
                                            switch (fieldKeys[1].ToLowerInvariant())
                                            {
                                                //用户所属部门负责人
                                                case "deptmain":
                                                    var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                                    if (_leaders != null) staffs.AddRange(_leaders);
                                                    break;
                                                //用户所属部门所有员工
                                                case "deptall":
                                                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                                    if (_staffs != null) staffs.AddRange(_staffs);
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }
                                    }
                                    else
                                    {
                                        //根据用户Id加载用户信息
                                        this.LoadUserByUserIds(userCtx, _userIds, users);
                                    }
                                }
                            }
                            else if (field.RefFormId.EqualsIgnoreCase(staffFormId))
                            {
                                if (fieldKeys.Length == 2)
                                {
                                    var _staffIds = new List<string>();
                                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _staffIds);
                                    if (_staffIds != null)
                                    {
                                        //获取指定员工所在的部门信息
                                        var _depts = baseFormProvider.GetStaffDepts(userCtx, _staffIds);
                                        var _deptIds = _depts?.Select(o => o.Id)?.ToList();
                                        if (_deptIds != null)
                                        {
                                            switch (fieldKeys[1].ToLowerInvariant())
                                            {
                                                //员工所属部门负责人
                                                case "deptmain":
                                                    var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                                    if (_leaders != null) staffs.AddRange(_leaders);
                                                    break;
                                                //员工所属部门所有员工
                                                case "deptall":
                                                    var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                                    if (_staffs != null) staffs.AddRange(_staffs);
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    //查找员工Id字段值
                                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, staffIds);
                                }
                            }
                            else if (field.RefFormId.EqualsIgnoreCase(deptFormId))
                            {
                                if (fieldKeys.Length == 2)
                                {
                                    var _deptIds = new List<string>();
                                    this.FindFieldValue(bizForm, dataEntitySet, fieldKey, _deptIds);
                                    switch (fieldKeys[1].ToLowerInvariant())
                                    {
                                        //部门负责人
                                        case "main":
                                            var _leaders = baseFormProvider.GetDeptLeaders(userCtx, _deptIds);
                                            if (_leaders != null) staffs.AddRange(_leaders);
                                            break;
                                        //部门内所有员工
                                        case "all":
                                            var _staffs = baseFormProvider.GetDeptAllStaffs(userCtx, _deptIds);
                                            if (_staffs != null) staffs.AddRange(_staffs);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            foreach (var staff in staffs)
            {
                if (!staff.Id.IsNullOrEmptyOrWhiteSpace() && !staffIds.Contains(staff.Id))
                {
                    staffIds.Add(staff.Id);
                }
            }

            if (staffIds.Count > 0)
            {
                var _users = baseFormProvider.GetStaffUsers(userCtx, staffIds);
                foreach (var _user in _users)
                {
                    users[_user.Number] = _user.Name;
                }
            }

            //加载流程代理人
            this.LoadFlowProxy(userCtx, flowInstance, users);
        }

        /// <summary>
        /// 加载流程代理人
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="flowInstance"></param>
        /// <param name="users"></param>
        private void LoadFlowProxy(UserContext userCtx, DynamicObject flowInstance, Dictionary<string, string> users)
        {
            if (users == null && users.Count <= 0) return;

            //构建流程代理表结构
            var flowProxyForm = this.MetaModelService.LoadFormModel(userCtx, "bpm_flowproxy");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, flowProxyForm.GetDynamicObjectType(userCtx));

            var sqlText = $@"select distinct u.fnumber,u.fname from t_bpm_flowproxy f
            inner join t_bpm_flowproxyentry pe on pe.fid=f.fid 
            inner join t_sec_user u on u.fid=f.fproxyid 
            inner join t_sec_user u2 on u2.fid=f.fbyproxyid and u2.fnumber in('{string.Join("','", users.Keys)}')
            where f.fmainorgid=@fmainorgid and pe.fverid=@fverid and pe.fisenabled='1' 
            and @fdate>=fbegindate and @fdate<dateadd(d,1,fenddate)";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", DbType.String, userCtx.Company),
                new SqlParam("fverid", DbType.String, flowInstance["fflowid"]),
                new SqlParam("fdate", DbType.DateTime, DateTime.Now)
            };

            using (var reader = this.DBService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    var userNumber = reader.GetString("fnumber");
                    if (userNumber.IsNullOrEmptyOrWhiteSpace()) continue;
                    users[userNumber] = reader.GetString("fname");
                }
            }
        }

        /// <summary>
        /// 根据用户Id加载用户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="userIds"></param>
        /// <param name="users"></param>
        /// <param name="inNumber"></param>
        private void LoadUserByUserIds(UserContext userCtx, List<string> userIds, Dictionary<string, string> users, bool inNumber = false)
        {
            if (userIds == null || userIds.Count <= 0) return;

            var paramNames = new List<string>();
            var paramList = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, userCtx.Company)
            };
            for (int i = 0; i < userIds.Count; i++)
            {
                if (userIds[i].IsNullOrEmptyOrWhiteSpace()) continue;
                var paramName = $"@fid{i}";
                paramNames.Add(paramName);
                paramList.Add(new SqlParam(paramName, DbType.String, userIds[i]));
            }
            if (paramNames.Count <= 0) return;

            var sqlText = @"
            select fid,fnumber,fname from t_sec_user 
            where fmainorgid=@fmainorgid";
            if (userIds.Count > 1)
            {
                //为了兼容历史参与人字段中存用户编码的情况
                if (inNumber)
                {
                    sqlText += $" and (fid in({string.Join(",", paramNames)}) or fnumber in({string.Join(",", paramNames)}))";
                }
                else
                {
                    sqlText += $" and fid in({string.Join(",", paramNames)})";
                }
            }
            else
            {
                //为了兼容历史参与人字段中存用户编码的情况
                if (inNumber)
                {
                    sqlText += $" and (fid={paramNames[0]} or fnumber={paramNames[0]})";
                }
                else
                {
                    sqlText += $" and fid={paramNames[0]}";
                }
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, paramList))
            {
                while (reader.Read())
                {
                    var number = reader.GetString("fnumber");
                    if (number.IsNullOrEmptyOrWhiteSpace()) continue;
                    if (users.ContainsKey(number)) continue;
                    users[number] = reader.GetString("fname");
                }
            }
        }

        /// <summary>
        /// 根据字段标识在指定的数据包中查找字段值（包括表体中的字段值）
        /// </summary>
        /// <param name="bizForm"></param>
        /// <param name="dataEntitySet"></param>
        /// <param name="fieldKey"></param>
        /// <param name="values"></param>
        private void FindFieldValue(HtmlForm bizForm, ExtendedDataEntitySet dataEntitySet, string fieldKey, List<string> values)
        {
            var field = bizForm.GetField(fieldKey);
            if (field == null) return;

            var extendeds = dataEntitySet.FindByEntityKey(field.EntityKey);
            if (extendeds == null) return;

            foreach (var extended in extendeds)
            {
                var value = field?.DynamicProperty?.GetValue<string>(extended.DataEntity);
                if (!value.IsNullOrEmptyOrWhiteSpace() && !values.Contains(value))
                {
                    values.Add(value);
                }
            }
        }

        #endregion
    }
}
