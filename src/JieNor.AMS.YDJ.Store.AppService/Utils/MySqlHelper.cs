using System.Data;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MySql.Data;
using MySql.Data.MySqlClient;
using System;
using System.Globalization;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using MySql.Data.Types;

namespace JieNor.AMS.YDJ.Store.AppService.Utils
{
    /// <summary>
    ///MySql工具类
    /// </summary>
    public class MySqlHelper : System.IDisposable
    {

        public static MySqlHelper Instance { get; protected set; }

        //MySql 数据库连接
        private static MySqlConnection conn = null;

        //连接Mysql相关
        private static string ConnString = string.Empty;

        private static string DataBase = string.Empty;


        /// <summary>
        ///  初始化禅道相关配置
        /// </summary>

        static MySqlHelper()
        {
            Instance = new MySqlHelper();
        }

        private MySqlHelper()
        {
            var dataBase = this.GetAppConfig("ms.zt.database");
            var dataSource = this.GetAppConfig("ms.zt.datasource");
            var userId = this.GetAppConfig("ms.zt.userid");
            var passWord = this.GetAppConfig("ms.zt.password");
            var port = this.GetAppConfig("ms.zt.port");
            if (!dataBase.IsNullOrEmptyOrWhiteSpace() && !dataSource.IsNullOrEmptyOrWhiteSpace() && !userId.IsNullOrEmptyOrWhiteSpace() &&
                !passWord.IsNullOrEmptyOrWhiteSpace() && !port.IsNullOrEmptyOrWhiteSpace())
            {
                DataBase = dataBase;
                ConnString = $@"Database='{dataBase}';Data Source='{dataSource}';User Id='{userId}';Password='{passWord}';charset='utf8';pooling=true;port={port};allow zero datetime=true";
            }
        }


        /// <summary>
        ///  给定连接的数据库用假设参数执行一个sql命令（不返回数据集）
        /// </summary>
        /// <param name="cmdText">存储过程名称或者sql命令语句</param>
        /// <param name="commandParameters">执行命令所用参数的集合</param>
        /// <returns>执行命令所影响的行数</returns>
        public static int ExecuteNonQuery( string cmdText, params MySqlParameter[] commandParameters)
        {

            //MySqlCommand cmd = new MySqlCommand();
            using (conn = new MySqlConnection(ConnString))
            {
                using (MySqlCommand cmd = new MySqlCommand(cmdText, conn))
                {
                    //异常捕获
                    try
                    {
                        conn.Open();
                        //返回
                        cmd.Parameters.Clear();
                        return cmd.ExecuteNonQuery();
                    }
                    catch (MySql.Data.MySqlClient.MySqlException e)
                    {
                        throw e;
                    }
                    finally
                    {
                        if (conn != null && conn.State == ConnectionState.Open)
                        {
                            conn.Close();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 用执行的数据库连接执行一个返回数据集的sql命令
        /// </summary>
        /// <param name="cmdText">存储过程名称或者sql命令语句</param>
        /// <param name="commandParameters">执行命令所用参数的集合</param>
        /// <returns>包含结果的读取器</returns>
        public static MySqlDataReader ExecuteReader(string cmdText, params MySqlParameter[] commandParameters)
        {
            using (conn = new MySqlConnection(ConnString))
            {
                using (MySqlCommand cmd = new MySqlCommand(cmdText, conn))
                {
                    //异常捕获
                    try
                    {
                        //返回
                        cmd.Parameters.Clear();
                        return cmd.ExecuteReader(CommandBehavior.CloseConnection);
                    }
                    catch (MySql.Data.MySqlClient.MySqlException e)
                    {
                        throw e;
                    }
                    finally
                    {
                        if (conn != null && conn.State == ConnectionState.Open)
                        {
                            conn.Close();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 返回DataSet
        /// </summary>
        /// <param name="cmdText">存储过程名称或者sql命令语句</param>
        /// <param name="commandParameters">执行命令所用参数的集合</param>
        /// <returns></returns>
        public static DataSet GetDataSet(string cmdText, params MySqlParameter[] commandParameters)
        {
            using (conn = new MySqlConnection(ConnString))
            {
                using (MySqlCommand cmd = new MySqlCommand(cmdText, conn))
                {
                    //异常捕获
                    try
                    {
                        //调用 MySqlCommand  的 ExecuteReader 方法
                        MySqlDataAdapter adapter = new MySqlDataAdapter();
                        adapter.SelectCommand = cmd;
                        DataSet ds = new DataSet();
                        adapter.Fill(ds);
                        //清除参数
                        cmd.Parameters.Clear();
                        conn.Close();
                        return ds;
                    }
                    catch (MySql.Data.MySqlClient.MySqlException e)
                    {
                        throw e;
                    }
                    finally
                    {
                        if (conn != null && conn.State == ConnectionState.Open)
                        {
                            conn.Close();
                        }
                    }
                }
            }
        }



        /// <summary>
        /// 用指定的数据库连接字符串执行一个命令并返回一个数据集的第一列
        /// </summary>
        /// <param name="cmdText">存储过程名称或者sql命令语句</param>
        /// <param name="commandParameters">执行命令所用参数的集合</param>
        /// <returns>用 Convert.To{Type}把类型转换为想要的 </returns>
        public static object ExecuteScalar(string cmdText, params MySqlParameter[] commandParameters)
        {
            using (conn = new MySqlConnection(ConnString))
            {
                using (MySqlCommand cmd = new MySqlCommand(cmdText, conn))
                {
                    //异常捕获
                    try
                    {
                        //返回
                        cmd.Parameters.Clear();
                        return cmd.ExecuteScalar();
                    }
                    catch (MySql.Data.MySqlClient.MySqlException e)
                    {
                        throw e;
                    }
                    finally
                    {
                        if (conn != null && conn.State == ConnectionState.Open)
                        {
                            conn.Close();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 准备执行一个命令
        /// </summary>
        /// <param name="cmd">sql命令</param>
        /// <param name="conn">OleDb连接</param>
        /// <param name="trans">OleDb事务</param>
        /// <param name="cmdType">命令类型例如 存储过程或者文本</param>
        /// <param name="cmdText">命令文本,例如:Select * from Products</param>
        /// <param name="cmdParms">执行命令的参数</param>
        private static void PrepareCommand(MySqlCommand cmd, MySqlConnection conn, MySqlTransaction trans, CommandType cmdType, string cmdText, MySqlParameter[] cmdParms)
        {

            if (conn.State != ConnectionState.Open)
                conn.Open();

            cmd.Connection = conn;
            cmd.CommandText = cmdText;

            if (trans != null)
                cmd.Transaction = trans;

            cmd.CommandType = cmdType;

            if (cmdParms != null)
            {
                foreach (MySqlParameter parm in cmdParms)
                    cmd.Parameters.Add(parm);
            }
        }

        public void Dispose()
        {
            // 链接还不为空
            if (conn != null && conn.State == ConnectionState.Open)
            {
                try
                {
                    conn.Close();
                    conn.Dispose();
                }
                catch (Exception ex)
                {

                }
                finally
                {
                    conn = null;
                }
            }
        }


        /// <summary>
        /// 用执行的数据库连接执行一个返回数据集的sql命令
        /// </summary>
        /// <param name="cmdText">sql命令语句</param>
        /// <returns>包含结果的禅道任务的数据集（个性化）</returns>
        public static List<DataModel> ExecuteBatchQuery(string query)
        {

            List<DataModel> result = new List<DataModel>();
            try
            {
                using (MySqlConnection conn = new MySqlConnection(ConnString))
                {
                    conn.Open();

                    MySqlCommand cmd = new MySqlCommand(query, conn);


                    using (MySqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            // 处理查询结果
                            string id = string.Empty;
                            string openedDate = string.Empty;
                            string deadLine = string.Empty;
                            string finishedDate = string.Empty;
                            string closedDate = string.Empty;
                            string lastEditedDate = string.Empty;
                            string status = string.Empty;
                            string assignedTo = string.Empty;

                            if (!reader.IsDBNull(reader.GetOrdinal("id")))
                                id = reader.GetString("id");

                            if (!reader.IsDBNull(reader.GetOrdinal("openedDate")))
                            {
                                var openedDateTemp = reader.GetMySqlDateTime(column: "openedDate");
                                if (!openedDateTemp.ToString().Equals("0000/0/0 0:00:00"))
                                    openedDate = openedDateTemp.GetDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            }


                            if (!reader.IsDBNull(reader.GetOrdinal("deadline")))
                            {
                                var deadLineTemp = reader.GetMySqlDateTime(column: "deadline");

                                if (!deadLineTemp.ToString().Equals("0000/0/0"))
                                {
                                    deadLine = deadLineTemp.GetDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                                    DateTime originalDateTime = DateTime.Parse(deadLine);
                                    DateTime newDateTime = originalDateTime.Date.AddDays(1).AddSeconds(-1);
                                    deadLine = newDateTime.ToString("yyyy-MM-dd HH:mm:ss");
                                }
                            }


                            if (!reader.IsDBNull(reader.GetOrdinal("finishedDate")))
                            {
                                var finishedDateTemp = reader.GetMySqlDateTime(column: "finishedDate");
                                if (!finishedDateTemp.ToString().Equals("0000/0/0 0:00:00"))
                                    finishedDate = finishedDateTemp.GetDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            }

                            if (!reader.IsDBNull(reader.GetOrdinal("closedDate")))
                            {
                                var closedDateTemp = reader.GetMySqlDateTime(column: "closedDate");
                                if (!closedDateTemp.ToString().Equals("0000/0/0 0:00:00"))
                                    closedDate = closedDateTemp.GetDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            }

                            if (!reader.IsDBNull(reader.GetOrdinal("lastEditedDate")))
                            {
                                var lastEditedDateTemp = reader.GetMySqlDateTime(column: "lastEditedDate");
                                if (!lastEditedDateTemp.ToString().Equals("0000/0/0 0:00:00"))
                                    lastEditedDate = lastEditedDateTemp.GetDateTime().ToString("yyyy-MM-dd HH:mm:ss");
                            }

                            if (!reader.IsDBNull(reader.GetOrdinal("status")))
                                status = reader.GetString("status");

                            if (!reader.IsDBNull(reader.GetOrdinal("assignedTo")))
                                assignedTo = reader.GetString("assignedTo");

                            DataModel data = new DataModel
                            {
                                id = id,
                                openedDate = openedDate,
                                deadLine = deadLine,
                                finishedDate = finishedDate,
                                status = status,
                                closedDate = closedDate,
                                lastEditedDate = lastEditedDate,
                                assignedTo = assignedTo
                            };

                            result.Add(data);
                        }
                    }
                }


            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {

                if (conn != null && conn.State == ConnectionState.Open)
                {
                    conn.Close();
                }
            }

            return result;
        }
    }
}

public  class DataModel
{
    public string id { get; set; }
    public string openedDate { get; set; }
    public string deadLine { get; set; }
    public string finishedDate { get; set; }
    public string status { get; set; }
    public string closedDate { get; set; }
    public string lastEditedDate { get; set; }

    public string assignedTo { get; set; }
}
