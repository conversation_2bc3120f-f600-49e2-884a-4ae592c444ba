<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AlibabaCloud.EndpointUtil" version="0.1.1" targetFramework="net461" />
  <package id="AlibabaCloud.GatewaySpi" version="0.0.1" targetFramework="net461" />
  <package id="AlibabaCloud.OpenApiClient" version="0.1.3" targetFramework="net461" />
  <package id="AlibabaCloud.OpenApiUtil" version="1.0.15" targetFramework="net461" />
  <package id="AlibabaCloud.RPCClient" version="1.0.1" targetFramework="net461" />
  <package id="AlibabaCloud.SDK.Common" version="0.2.5" targetFramework="net461" />
  <package id="AlibabaCloud.SDK.Dysmsapi20170525" version="2.0.10" targetFramework="net461" />
  <package id="AlibabaCloud.TeaUtil" version="0.1.13" targetFramework="net461" />
  <package id="AlibabaCloud.TeaXML" version="0.0.3" targetFramework="net461" />
  <package id="Aliyun.Credentials" version="1.3.1" targetFramework="net461" />
  <package id="Autofac" version="4.6.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.SignalR.Core" version="2.2.2" targetFramework="net461" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net461" />
  <package id="Microsoft.Bcl.Build" version="1.0.14" targetFramework="net461" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net461" />
  <package id="Microsoft.Owin" version="3.1.0" targetFramework="net461" />
  <package id="Microsoft.Owin.Security" version="3.1.0" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net461" />
  <package id="Owin" version="1.0" targetFramework="net461" />
  <package id="Tea" version="1.0.11" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net461" />
</packages>