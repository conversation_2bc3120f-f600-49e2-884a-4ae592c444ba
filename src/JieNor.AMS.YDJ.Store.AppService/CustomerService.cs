using JieNor.AMS.YDJ.Core.DataEntity.Customer;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService
{
    [InjectService]
    public class CustomerService : ICustomerService
    {
        /// <summary>
        /// 根据当前用户上下文获取客户id
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public string GetCustomerId(UserContext userCtx)
        {
            var customer = GetCustomerInfo(userCtx);
            if (customer == null)
            {
                throw new BusinessException("当前用户没有找到关联的客户信息!");
            }
            return Convert.ToString(customer["id"]);
        }

        /// <summary>
        /// 根据公司id和手机号码获取客户id
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="company"></param>
        /// <param name="phone"></param>
        /// <returns></returns>
        public string GetCustomerId(UserContext userCtx, string company, string phone)
        {
            var customer = GetCustomerInfo(userCtx, company, phone);
            if (customer == null)
            {
                throw new BusinessException("根据当前公司id和手机号码未能找到关联的客户信息!");
            }
            return Convert.ToString(customer["id"]);
        }

        /// <summary>
        /// 根据当前用户上下文获取客户信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public DynamicObject GetCustomerInfo(UserContext userCtx)
        {
            var company = userCtx.Company;
            var phone = userCtx.UserPhone;

            if (string.IsNullOrWhiteSpace(phone))
            {
                IMetaModelService metaModelService = userCtx.Container.GetService<IMetaModelService>();
                HtmlForm userHtmlForm = metaModelService.LoadFormModel(userCtx, "sec_user");
                var dm = userCtx.Container.GetService<IDataManager>();
                dm.InitDbContext(userCtx, userHtmlForm.GetDynamicObjectType(userCtx));
                var userInfo = dm.Select(userCtx.UserId) as DynamicObject;
                phone = Convert.ToString(userInfo["fphone"]);
            }

            return GetCustomerInfo(userCtx, company, phone);
        }

        /// <summary>
        /// 根据当前用户上下文获取手机号信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public string GetUserPhone(UserContext userCtx)
        {
            var company = userCtx.Company;
            var phone = userCtx.UserPhone;

            if (string.IsNullOrWhiteSpace(phone))
            {
                IMetaModelService metaModelService = userCtx.Container.GetService<IMetaModelService>();
                HtmlForm userHtmlForm = metaModelService.LoadFormModel(userCtx, "sec_user");
                var dm = userCtx.Container.GetService<IDataManager>();
                dm.InitDbContext(userCtx, userHtmlForm.GetDynamicObjectType(userCtx));
                var userInfo = dm.Select(userCtx.UserId) as DynamicObject;
                phone = Convert.ToString(userInfo["fphone"]);
            }
            return phone;
        }

        /// <summary>
        /// 根据公司id和手机号码获取客户
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="company"></param>
        /// <param name="phone"></param>
        /// <returns></returns>
        public DynamicObject GetCustomerInfo(UserContext userCtx, string company, string phone)
        {
            IMetaModelService metaModelService = userCtx.Container.GetService<IMetaModelService>();
            HtmlForm htmlForm = metaModelService.LoadFormModel(userCtx, "ydj_customer");
            IDataManager dataManager = userCtx.Container.GetService<IDataManager>();
            dataManager.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            return GetCustomerInfo(userCtx, htmlForm, company, phone, dataManager);
        }

        /// <summary>
        /// 根据公司id和手机号码获取客户
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="company"></param>
        /// <param name="phone"></param>
        /// <param name="dm"></param>
        /// <returns></returns>
        public DynamicObject GetCustomerInfo(UserContext userCtx, HtmlForm htmlForm, string company, string phone, IDataManager dm)
        {
            if (userCtx == null)
            {
                throw new BusinessException("用户上下文不能为空!");
            }
            if (htmlForm == null)
            {
                throw new BusinessException("ydj_customer模型不存在!");
            }
            if (string.IsNullOrWhiteSpace(company))
            {
                throw new BusinessException("company不能为空!");
            }
            if (string.IsNullOrWhiteSpace(phone))
            {
                throw new BusinessException("phone不能为空!");
            }
            if (dm == null)
            {
                throw new BusinessException("dm不能为空!");
            }
            var pkIdReader = userCtx.GetPkIdDataReader(htmlForm, "fphone=@fmobile and fmainorgid=@fmainorgid and fcustype='customercate_03' ",
                   new SqlParam[] {
                               new SqlParam("fmobile", System.Data.DbType.String, phone),
                               new SqlParam("fmainorgid", System.Data.DbType.String, company)
                   });
            return dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
        }

        /// <summary>
        /// 根据客户唯一因子，判断是否冲突
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerUniqueItem">客户唯一因子</param>
        /// <param name="customerId">存在冲突的customerId</param>
        /// <returns></returns>
        public bool CheckCustomerUnique(UserContext userCtx, CustomerUniqueItem customerUniqueItem, out string customerId)
        {
            //var profileService = userCtx.Container.GetService<ISystemProfile>();
            //var customerUnique = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fcustomerunique", "fphone");
            //if (customerUnique.IndexOf(":") > 0)
            //{
            //    customerUnique = customerUnique.Substring(0, customerUnique.IndexOf(":")).Replace('|', ',');
            //}
            //var customerUniqueFields = customerUnique.Split(new char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
            //if (customerUniqueFields.Any() == false)
            //{
            //    customerUniqueFields = new List<string> { "fphone" };
            //}

            //task#36313:1.1隐藏销售管理参数中<客户报备唯一性规则>参数，固定以“个人手机号”当做客户唯一条件
            var customerUniqueFields = new List<string> { "fphone" };
            var dbService = userCtx.Container.GetService<IDBService>();

            List<SqlParam> lstParam = new List<SqlParam>()
                {
                    new SqlParam("fmainorgid", DbType.String,userCtx.Company),
                    new SqlParam("ftype", DbType.String, customerUniqueItem.Type)
                };

            //27165 【慕思现场-正式问题765/835/779-重要】长沙意悦客户同一手机号不同编码
            //注：需移除类型校验
            //string sqlWhere = "where u1.fmainorgid=@fmainorgid and u1.ftype=@ftype";
            string sqlWhere = "where u1.fmainorgid=@fmainorgid ";

            //27165 【慕思现场-正式问题765/835/779-重要】长沙意悦客户同一手机号不同编码
            //注：需移除类型校验，校验不区分企业或个人
            //if (customerUniqueItem.Type.EqualsIgnoreCase("customertype_01"))
            //{
            //    // 公司客户处理逻辑
            //    bool checkName = customerUniqueFields.Contains("fname") &&
            //                      !customerUniqueItem.Name.IsNullOrEmptyOrWhiteSpace();
            //    if (checkName)
            //    {
            //        lstParam.Add(new SqlParam($"fname", DbType.String, customerUniqueItem.Name));

            //        using (var reader = dbService.ExecuteReader(userCtx,
            //            $"select top 1 fid from t_ydj_customer u1 WITH(NOLOCK) {sqlWhere} and u1.fname=@fname",
            //            lstParam))
            //        {
            //            if (reader.Read())
            //            {
            //                customerId = Convert.ToString(reader[0]);

            //                if (customerId.EqualsIgnoreCase(customerUniqueItem.Id) == false)
            //                    return true;
            //            }
            //        }
            //    }
            //}
            //else
            {
                // 个人客户处理逻辑

                // 先找出所有符合条件的客户
                bool checkPhone = customerUniqueFields.Contains("fphone") &&
                                  !customerUniqueItem.Phone.IsNullOrEmptyOrWhiteSpace();
                bool checkWechat = customerUniqueFields.Contains("fwechat") &&
                                    !customerUniqueItem.Wechat.IsNullOrEmptyOrWhiteSpace();

                if (checkPhone && checkWechat)
                {
                    sqlWhere += " and (u1.fphone=@fphone or u1.fwechat=@fwechat)";
                    lstParam.Add(new SqlParam($"fphone", DbType.String, customerUniqueItem.Phone));
                    lstParam.Add(new SqlParam($"fwechat", DbType.String, customerUniqueItem.Wechat));
                }
                else if (checkPhone)
                {
                    sqlWhere += " and (u1.fphone=@fphone)";
                    lstParam.Add(new SqlParam($"fphone", DbType.String, customerUniqueItem.Phone));
                }
                else if (checkWechat)
                {
                    sqlWhere += " and (u1.fwechat=@fwechat)";
                    lstParam.Add(new SqlParam($"fwechat", DbType.String, customerUniqueItem.Wechat));
                }

                if (checkPhone || checkWechat)
                {
                    using (var reader = dbService.ExecuteReader(userCtx,
                        $"select fid, fphone, fwechat from t_ydj_customer u1 WITH(NOLOCK) {sqlWhere}",
                        lstParam))
                    {
                        while (reader.Read())
                        {
                            customerId = Convert.ToString(reader[0]);
                            string phone = Convert.ToString(reader[1]);
                            string wechat = Convert.ToString(reader[2]);

                            // 优先匹配手机号，当出现手机号相同，但 id 不同时，返回冲突
                            if (checkPhone &&
                                !phone.IsNullOrEmptyOrWhiteSpace() &&
                                customerUniqueItem.Phone.EqualsIgnoreCase(phone) &&
                                !customerUniqueItem.Id.EqualsIgnoreCase(customerId)
                                )
                            {
                                return true;
                            }

                            // 再匹配微信号，当出现微信号相同，但 id 不同时，返回冲突
                            if (checkWechat &&
                                !wechat.IsNullOrEmptyOrWhiteSpace() &&
                                customerUniqueItem.Wechat.EqualsIgnoreCase(wechat) &&
                                !customerUniqueItem.Id.EqualsIgnoreCase(customerId)
                                )
                            {
                                return true;
                            }
                        }
                    }
                }
            }

            customerId = null;
            return false;
        }

        /// <summary>
        /// 根据客户唯一因子，获取系统能关联上的客户数据对象
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerUniqueItems">客户唯一因子</param>
        /// <param name="customerUniqueFields">客户唯一性字段</param>
        /// <returns></returns>
        public Dictionary<string, DynamicObject> GetCustomerInfo(UserContext userCtx, IEnumerable<CustomerUniqueItem> customerUniqueItems, IEnumerable<string> customerUniqueFields = null)
        {
            if (customerUniqueItems == null
                || customerUniqueItems.Any() == false) return new Dictionary<string, DynamicObject>();

            //先初始化成传入对象一样多的结构
            var dctCustomerInfo = new Dictionary<string, DynamicObject>();
            var dctCustomerPkId = new Dictionary<string, string>();
            foreach (var kvpItem in customerUniqueItems)
            {
                dctCustomerInfo[kvpItem.Id] = null;
                dctCustomerPkId[kvpItem.Id] = "";
            }

            // 实现按指定信息获取系统已存在的客户信息

            // 1、根据客户报备唯一性规则去关联客户
            // 2、按优先级匹配，第一个规则匹配中，则直接返回，不再匹配后续的规则。关联的优先级：手机号->微信号（个人），名称（公司）
            // 3、当客户报备唯一性规则没有匹配时，默认值：手机号（个人）、名称（公司）

            var dbService = userCtx.Container.GetService<IDBService>();
            var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();

            if (customerUniqueFields == null)
            {
                var profileService = userCtx.Container.GetService<ISystemProfile>();
                var customerUnique = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fcustomerunique", "fphone");
                if (customerUnique.IndexOf(":") > 0)
                {
                    customerUnique = customerUnique.Substring(0, customerUnique.IndexOf(":")).Replace('|', ',');
                }
                customerUniqueFields = customerUnique.Split(new char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                if (customerUniqueFields.Any() == false)
                {
                    customerUniqueFields = new List<string> { "fphone" };
                }
            }

            if (customerUniqueItems.IsGreaterThan(1))
            {
                var dtTemp = new DataTable();
                dtTemp.Columns.Add("fcustomerid", typeof(string));
                dtTemp.Columns.Add("fid", typeof(string));
                // 新增 ftype，现有客户类型分个人和公司
                dtTemp.Columns.Add("ftype", typeof(string));
                dtTemp.Columns.Add("fphone", typeof(string));
                dtTemp.Columns.Add("fwechat", typeof(string));
                dtTemp.Columns.Add("fname", typeof(string));

                dtTemp.BeginLoadData();
                foreach (var itemRow in customerUniqueItems)
                {
                    List<object> dtRowValues = new List<object>();
                    dtRowValues.Add("");
                    dtRowValues.Add(itemRow.Id);
                    // 新增 ftype，现有客户类型分个人和公司
                    dtRowValues.Add(itemRow.Type);
                    dtRowValues.Add(itemRow.Phone);
                    dtRowValues.Add(itemRow.Wechat);
                    dtRowValues.Add(itemRow.Name);

                    dtTemp.LoadDataRow(dtRowValues.ToArray(), true);
                }
                dtTemp.EndLoadData();

                using (var tran = userCtx.CreateTransaction())
                {
                    var tempTableName = dbService.CreateTempTableWithDataTable(userCtx, dtTemp, 500,null,null,false);

                    // 处理公司客户
                    dbServiceEx.Execute(userCtx, $@"
                                            update {tempTableName} as tmp set (fcustomerid)=(
                                                select u1.fid 
                                                from t_ydj_customer u1 WITH(NOLOCK)
                                                where u1.fmainorgid=@fmainorgid and tmp.ftype=u1.ftype and tmp.fname=u1.fname) where tmp.ftype=@ftype",
                        new List<SqlParam>
                        {
                        new SqlParam("fmainorgid", DbType.String, userCtx.Company),
                        new SqlParam("ftype", DbType.String, "customertype_01")
                        });

                    // 处理个人客户，优先级：手机号 -> 微信号
                    if (customerUniqueFields.Contains("fphone"))
                    {
                        dbServiceEx.Execute(userCtx, $@"
                                            update {tempTableName} as tmp set (fcustomerid)=(
                                                select u1.fid 
                                                from t_ydj_customer u1 WITH(NOLOCK)
                                                where u1.fmainorgid=@fmainorgid and tmp.ftype=u1.ftype and tmp.fphone=u1.fphone) where tmp.ftype=@ftype and tmp.fcustomerid=''",
                            new List<SqlParam>
                            {
                            new SqlParam("fmainorgid", DbType.String, userCtx.Company),
                            new SqlParam("ftype", DbType.String, "customertype_00")
                            });
                    }

                    if (customerUniqueFields.Contains("fwehcat"))
                    {
                        dbServiceEx.Execute(userCtx, $@"
                                                update {tempTableName} as tmp set (fcustomerid)=(
                                                    select u1.fid 
                                                    from t_ydj_customer u1 WITH(NOLOCK)
                                                    where u1.fmainorgid=@fmainorgid and tmp.ftype=u1.ftype and tmp.fwechat=u1.fwechat) where tmp.ftype=@ftype and tmp.fcustomerid=''",
                            new List<SqlParam>
                            {
                            new SqlParam("fmainorgid", DbType.String, userCtx.Company),
                            new SqlParam("ftype", DbType.String, "customertype_00")
                            });
                    }

                    using (var reader = dbService.ExecuteReader(userCtx, $@"select * from {tempTableName}"))
                    {
                        while (reader.Read())
                        {
                            var outerPkId = Convert.ToString(reader["fid"]);
                            var customerId = Convert.ToString(reader["fcustomerid"]);
                            dctCustomerPkId[outerPkId] = customerId;
                        }
                    }
                    tran.Complete();

                    dbService.DeleteTempTableByName(userCtx, tempTableName, false);
                }
            }
            else
            {
                #region 一条记录的处理逻辑

                var customerEntryItem = customerUniqueItems.First();
                List<SqlParam> lstParam = new List<SqlParam>()
                {
                    new SqlParam("fmainorgid", DbType.String,userCtx.Company),
                    //new SqlParam("wechat", DbType.String,customerEntryItem.Wechat)
                    new SqlParam("ftype", DbType.String,customerEntryItem.Type)
                };

                string sqlWhere = "where u1.fmainorgid=@fmainorgid and u1.ftype=@ftype";

                if (customerEntryItem.Type.EqualsIgnoreCase("customertype_01"))
                {
                    // 公司客户处理逻辑
                    bool checkName = customerUniqueFields.Contains("fname") &&
                                      !customerEntryItem.Name.IsNullOrEmptyOrWhiteSpace();
                    if (checkName)
                    {
                        lstParam.Add(new SqlParam($"fname", DbType.String, customerEntryItem.Name));

                        using (var reader = dbService.ExecuteReader(userCtx,
                            $"select top 1 fid from t_ydj_customer u1 WITH(NOLOCK) {sqlWhere} and u1.fname=@fname",
                            lstParam))
                        {
                            if (reader.Read())
                            {
                                var customerId = Convert.ToString(reader[0]);
                                dctCustomerPkId[customerEntryItem.Id] = customerId;
                            }
                        }
                    }
                }
                else
                {
                    // 个人客户处理逻辑

                    // 先找出所有符合条件的客户
                    bool checkPhone = customerUniqueFields.Contains("fphone") &&
                                      !customerEntryItem.Phone.IsNullOrEmptyOrWhiteSpace();
                    bool checkWechat = customerUniqueFields.Contains("fwechat") &&
                                        !customerEntryItem.Wechat.IsNullOrEmptyOrWhiteSpace();

                    if (checkPhone && checkWechat)
                    {
                        sqlWhere += " and (u1.fphone=@fphone or u1.fwechat=@fwechat)";
                        lstParam.Add(new SqlParam($"fphone", DbType.String, customerEntryItem.Phone));
                        lstParam.Add(new SqlParam($"fwechat", DbType.String, customerEntryItem.Wechat));
                    }
                    else if (checkPhone)
                    {
                        sqlWhere += " and (u1.fphone=@fphone)";
                        lstParam.Add(new SqlParam($"fphone", DbType.String, customerEntryItem.Phone));
                    }
                    else if (checkWechat)
                    {
                        sqlWhere += " and (u1.fwechat=@fwechat)";
                        lstParam.Add(new SqlParam($"fwechat", DbType.String, customerEntryItem.Wechat));
                    }

                    if (checkPhone || checkWechat)
                    {
                        using (var reader = dbService.ExecuteReader(userCtx,
                            $"select fid, fphone, fwechat from t_ydj_customer u1 WITH(NOLOCK) {sqlWhere}",
                            lstParam))
                        {
                            while (reader.Read())
                            {
                                string customerId = Convert.ToString(reader[0]);
                                string phone = Convert.ToString(reader[1]);
                                string wechat = Convert.ToString(reader[2]);

                                // 优先匹配手机号
                                if (checkPhone && !phone.IsNullOrEmptyOrWhiteSpace() && customerEntryItem.Phone.EqualsIgnoreCase(phone))
                                {
                                    dctCustomerPkId[customerEntryItem.Id] = customerId;
                                    break;
                                }

                                // 再匹配微信号
                                if (checkWechat && !wechat.IsNullOrEmptyOrWhiteSpace() && customerEntryItem.Wechat.EqualsIgnoreCase(wechat))
                                {
                                    dctCustomerPkId[customerEntryItem.Id] = customerId;
                                    break;
                                }
                            }
                        }
                    }
                }
                #endregion
            }

            var billDataService = userCtx.Container.GetService<IBillDataService>();
            foreach (var kvpItem in dctCustomerPkId)
            {
                if (kvpItem.Value.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                var customerObj = billDataService.Load(userCtx, "ydj_customer", kvpItem.Value);
                dctCustomerInfo[kvpItem.Key] = customerObj;
            }
            return dctCustomerInfo;
        }

        /// <summary>
        /// 客户负责人去重并自动填充（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerForm">客户表单</param>
        /// <param name="customer">客户</param>
        public void CustomerDutyDuplicateAndAutoFillMultipleDuty(UserContext userCtx, HtmlForm customerForm, DynamicObject customer)
        {
            var fdutyentry = (DynamicObjectCollection)customer["fdutyentry"];
            if (fdutyentry.Count == 0)
            {
                customer["fdutyids"] = string.Empty;
                customer["fdutyids_txt"] = string.Empty;
                customer["fdeptids"] = string.Empty;
                customer["fdeptids_txt"] = string.Empty;
                return;
            }

            #region 去重

            HashSet<string> hashSet = new HashSet<string>();

            List<DynamicObject> districtObjs = new List<DynamicObject>();

            foreach (var item in fdutyentry)
            {
                string key = $"{item["fdutyid"]}_{item["fdeptid"]}";
                if (hashSet.Contains(key))
                {
                    continue;
                }

                hashSet.Add(key);
                districtObjs.Add(item);
            }

            fdutyentry.Clear();
            foreach (var item in districtObjs)
            {
                fdutyentry.Add(item);
            }
            #endregion

            if (fdutyentry.Count == 0)
            {
                customer["fdutyids"] = string.Empty;
                customer["fdutyids_txt"] = string.Empty;
                customer["fdeptids"] = string.Empty;
                customer["fdeptids_txt"] = string.Empty;
                return;
            }

            var formDt = customerForm.GetEntryEntity("fdutyentry").DynamicObjectType;
            // 加载引用数据
            var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(userCtx, formDt, fdutyentry, false);

            customer["fdutyids"] = string.Join(",", fdutyentry.Select(s => s["fdutyid"]));
            customer["fdutyids_txt"] = string.Join(",", fdutyentry.Select(s => (s["fdutyid_ref"] as DynamicObject)?["fname"]));
            customer["fdeptids"] = string.Join(",", fdutyentry.Select(s => s["fdeptid"]));
            customer["fdeptids_txt"] = string.Join(",", fdutyentry.Select(s => (s["fdeptid_ref"] as DynamicObject)?["fname"]));
        }

        /// <summary>
        /// 添加客户负责人（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerForm">客户表单</param>
        /// <param name="customer">客户</param>
        /// <param name="dutyId">负责人</param>
        /// <param name="deptId">部门</param>
        public void AddCustomerDuty(UserContext userCtx, HtmlForm customerForm, DynamicObject customer, string dutyId, string deptId)
        {
            if (customer == null) return;

            if (dutyId.IsNullOrEmptyOrWhiteSpace() || deptId.IsNullOrEmptyOrWhiteSpace()) return;

            var dutyEntry = (DynamicObjectCollection)customer["fdutyentry"];
            // 判断负责人是否存在于当前客户负责人中，如有，则返回
            if (dutyEntry.Any(s =>
                Convert.ToString(s["fdutyid"]).EqualsIgnoreCase(dutyId) &&
                Convert.ToString(s["fdeptid"]).EqualsIgnoreCase(deptId)))
            {
                return;
            }

            // 判断是否存在负责人明细行只有部门=deptId，而没有负责人的，如有，则更新
            var duty = dutyEntry.FirstOrDefault(s => Convert.ToString(s["fdeptid"]).EqualsIgnoreCase(deptId) && Convert.ToString(s["fdutyid"]).IsNullOrEmptyOrWhiteSpace());
            if (duty != null)
            {
                duty["fdutyid"] = dutyId;
            }
            else
            {
                // 没有，则插入一新行
                var dutyEntyEntity = customerForm.GetEntryEntity("fdutyentry");
                duty = new DynamicObject(dutyEntyEntity.DynamicObjectType);
                duty["fdutyid"] = dutyId;
                duty["fdeptid"] = deptId;
                duty["fjointime"] = BeiJingTime.Now;

                dutyEntry.Add(duty);

                var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                prepareService.PrepareDataEntity(userCtx, customerForm, new DynamicObject[] { customer }, OperateOption.Create());
            }

            // 去重
            this.CustomerDutyDuplicateAndAutoFillMultipleDuty(userCtx, customerForm, customer);
        }

        /// <summary>
        /// 替换负责人（需要自行调用保存）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerForm">客户表单</param>
        /// <param name="customer">客户</param>  
        /// <param name="sourceDutyId">源负责人</param>
        /// <param name="targetDutyId">目标负责人</param>
        /// <param name="targetDeptId">目标部门</param>
        public void ReplaceCustomerDuty(UserContext userCtx, HtmlForm customerForm, DynamicObject customer, string sourceDutyId,
            string targetDutyId, string targetDeptId)
        {
            if (customer == null) return;

            if (sourceDutyId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("源负责人不能为空！");
            }

            if (targetDutyId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("目标负责人不能为空！");
            }

            if (targetDeptId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("目标部门不能为空！");
            }

            // 获取客户负责人明细
            var dutyEntrys = customer["fdutyentry"] as DynamicObjectCollection;

            // 是否存在目标负责人
            var existsTargetStaff = dutyEntrys.Any(s =>
                Convert.ToString(s["fdutyid"]).EqualsIgnoreCase(targetDutyId) &&
                Convert.ToString(s["fdeptid"]).EqualsIgnoreCase(targetDeptId)
                );

            for (int i = dutyEntrys.Count - 1; i >= 0; i--)
            {
                var dutyObj = dutyEntrys[i];
                // 找到源负责人，并删除
                if (Convert.ToString(dutyObj["fdutyid"]).EqualsIgnoreCase(sourceDutyId))
                {
                    dutyEntrys.RemoveAt(i);
                }
            }

            if (existsTargetStaff)
            {
                CustomerDutyDuplicateAndAutoFillMultipleDuty(userCtx, customerForm, customer);
            }
            else
            {
                AddCustomerDuty(userCtx, customerForm, customer, targetDutyId, targetDeptId);
            }
        }

        ///// <summary>
        ///// 创建客户至部门的链接
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="customerId"></param>
        ///// <param name="shareItem"></param>
        //public void CreateCustomerLink(UserContext userCtx, string customerId, CustomerShareItem shareItem)
        //{
        //    var billDataService = userCtx.Container.GetService<IBillDataService>();
        //    var customerObj = billDataService.Load(userCtx, "ydj_customer", customerId);
        //    if (customerObj != null)
        //    {
        //        this.CreateCustomerLink(userCtx, new Dictionary<DynamicObject, IList<CustomerShareItem>>
        //        {
        //            {customerObj, new List<CustomerShareItem> { shareItem } }
        //        });
        //    }
        //}

        ///// <summary>
        ///// 创建客户至部门的链接
        ///// </summary>
        ///// <param name="userCtx"></param>
        ///// <param name="dctCustomerLink"></param>
        //public void CreateCustomerLink(UserContext userCtx, Dictionary<DynamicObject, IList<CustomerShareItem>> dctCustomerLink)
        //{
        //    if (dctCustomerLink == null
        //        || dctCustomerLink.Any() == false) return;
        //    var sequenceService = userCtx.Container.GetService<ISequenceService>();
        //    List<DynamicObject> lstNeedSaveCustomerObjs = new List<DynamicObject>();
        //    foreach (var kvpItem in dctCustomerLink)
        //    {
        //        if (kvpItem.Value == null
        //            || kvpItem.Value.Any() == false) continue;
        //        var shareEntryObjs = kvpItem.Key["fshareentry"] as DynamicObjectCollection;
        //        if (shareEntryObjs == null) continue;
        //        foreach (var shareItem in kvpItem.Value)
        //        {
        //            if (shareItem.DeptId.IsNullOrEmptyOrWhiteSpace()) continue;
        //            var existLinkObj = shareEntryObjs.FirstOrDefault(o => Convert.ToString(o["fusedeptid"]).EqualsIgnoreCase(shareItem.DeptId));
        //            if (existLinkObj == null)
        //            {
        //                existLinkObj = shareEntryObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
        //                shareEntryObjs.Add(existLinkObj);
        //                existLinkObj["id"] = sequenceService.GetSequence<string>();
        //                existLinkObj["fusedeptid"] = shareItem.DeptId;
        //                if (shareItem.UserId.IsNullOrEmptyOrWhiteSpace())
        //                {
        //                    shareItem.UserId = userCtx.UserId;
        //                }
        //                if (!shareItem.ShareTime.HasValue)
        //                {
        //                    shareItem.ShareTime = DateTime.Now;
        //                }
        //                existLinkObj["fuseuserid"] = shareItem.UserId;
        //                existLinkObj["fusetime"] = shareItem.ShareTime;
        //                existLinkObj["fuseremark"] = shareItem.Remark;
        //            }
        //        }
        //    }

        //    var billPkService = userCtx.Container.GetService<IDataEntityPkService>();
        //    var metaModelService = userCtx.Container.GetService<IMetaModelService>();
        //    var customerMeta = metaModelService.LoadFormModel(userCtx, "ydj_customer");
        //    var dm = userCtx.Container.GetService<IDataManager>();
        //    dm.InitDbContext(userCtx, customerMeta.GetDynamicObjectType(userCtx));

        //    billPkService.AutoSetPrimaryKey(userCtx, dctCustomerLink.Keys.Distinct(), customerMeta.GetDynamicObjectType(userCtx));
        //    dm.Save(dctCustomerLink.Keys.Distinct());
        //}

        /// <summary>
        /// 计算当前会员积分和累计消费金额，当达到会员等级晋级条件，则晋级
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="customerId">客户</param>
        /// <param name="amount">本次确认金额（正数为收款，负数为退款）</param>
        public void CalculateAvailableIntegralAndSumAmount(UserContext userCtx, string customerId, decimal amount)
        {
            // 确认金额 = 0，不计算
            if (amount == 0) return;

            var customer = userCtx.LoadBizDataById("ydj_customer", customerId);
            if (customer == null) return;

            // 一块就相当于一积分
            var favailableintegral = Convert.ToInt32(customer["favailableintegral"]) + Convert.ToInt32(amount);
            if (favailableintegral <= 0) favailableintegral = 0;
            var fsumamount = Convert.ToDecimal(customer["fsumamount"]) + amount;
            if (fsumamount <= 0) fsumamount = 0;

            customer["favailableintegral"] = favailableintegral;
            customer["fsumamount"] = fsumamount;

            // 只有增加，才会触发判断晋级
            if (amount > 0)
            {
                // 会员级别，倒序：符合则跳出
                var customerLevels = userCtx.LoadBizDataByFilter("ydj_customerlevel", " fforbidstatus<>'1' ").OrderByDescending(s => Convert.ToInt32(s["fcondition"]));
                var sumAmount = Convert.ToDecimal(customer["fsumamount"]);
                var currCustomerLevel = Convert.ToString(customer["fcustomerlevel"]);   // 当前会员级别

                foreach (var customerLevel in customerLevels)
                {
                    var condition = Convert.ToInt32(customerLevel["fcondition"]);
                    var customerLevelId = Convert.ToString(customerLevel["Id"]);

                    // 当前会员级别 = 本会员级别，跳出（由于是倒序，如果在本会员级别，不管后面是否符合条件，保留级别）
                    if (currCustomerLevel == customerLevelId)
                    {
                        break;
                    }

                    // 达到晋级条件，晋级，跳出
                    if (sumAmount > condition)
                    {
                        customer["fcustomerlevel"] = customerLevelId;
                        break;
                    }
                }
            }

            // 保存
            var dm = userCtx.Container.GetService<IDataManager>();
            var htmlForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_customer");
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

            //数据包预处理
            var preService = userCtx.Container.GetService<IPrepareSaveDataService>();
            preService.PrepareDataEntity(userCtx, htmlForm, new[] { customer }, OperateOption.Create());

            dm.Save(customer);
        }
    }
}
