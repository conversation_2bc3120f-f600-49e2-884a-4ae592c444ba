using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.AMS.YDJ.Store.AppService;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.Stock.AppService.SchedulerTask
{
    /// <summary>
    /// 服务单逾期自动关闭服务
    /// </summary>
    //[InjectService(AliasName = "Task")]
    [InjectService]
    [TaskSvrId("serviceautoclose")]
    [Caption("服务单逾期自动关闭（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class ServiceAutoClose : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            var ctx = this.UserContext;

            var date = DateTime.Now.AddDays(-30);
            var sql = @"select fid from t_ydj_service where  fserstatus='sersta04' and ffinishdate <= '{0}' ".Fmt (date);
            var ids = this.DBService.ExecuteDynamicObject(ctx, sql);
            if (ids.IsNullOrEmpty() || !ids.Any())
            {
                this.WriteLog("暂无逾期30天的未评价服务单");
                return; 
            }

            var serviceBills = ctx.LoadBizDataById("ydj_service", ids.Select (f=>f["fid"].ToString ()));
            var grpOrgs = serviceBills.GroupBy(f => f["fmainorgid"].ToString()).ToList();
            foreach (var item in grpOrgs)
            {
                var ctxX = ctx.CreateAgentDBContext(item.Key);             
                var serAutoCloseService = ctx.Container.GetService<ServiceAutoCloseService>();
                var resalt=serAutoCloseService?.AutoCloseService(ctxX, item.ToList (), this.Option);

                this.WriteLog("经销商 {0} ：{1}".Fmt(item.Key, resalt.SimpleMessage));
            }
             
        }
    }
}