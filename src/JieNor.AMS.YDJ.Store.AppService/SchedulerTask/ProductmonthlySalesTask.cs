using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    ///月度销售数量计算
    /// </summary>
    [InjectService]
    [TaskSvrId("productmonthlysales")]
    [Caption("月均销售数量计算（在总部执行）")]
    [Browsable(false)]
    public class ProductmonthlySalesTask : AbstractScheduleWorker
    {
        protected override async Task DoExecute()
        {
            var ctx = this.UserContext;
            try
            {
                var profileService = ctx.Container.GetService<ISystemProfile>();
                var finventorywaterlevel = profileService.GetSystemParameter(ctx, "pur_systemparam", "finventorywaterlevel", false);
                if (!finventorywaterlevel) {
                    this.WriteLog("总部未启用库存水位线控制功能（计算+校验）！");
                    this.Result.ComplexMessage.WarningMessages.Add("总部未启用库存水位线控制功能（计算+校验）！");
                    return;
                }
                var orgsql = $@"/*dialect*/select  a.fmainorgid,b.fnumber,b.fname, min(a.fcreatedate) as MinorderTime ,
			                                 CASE 
                                            -- 如果(当前日期-1个月)的年份 > MIN日期的年份
                                            WHEN YEAR(DATEADD(MONTH, -1, GETDATE())) > YEAR(MIN(a.fcreatedate)) 
                                                THEN MONTH(DATEADD(MONTH, -1, GETDATE()))
        
                                            -- 如果(当前日期-1个月)的年份 = MIN日期的年份
                                            WHEN YEAR(DATEADD(MONTH, -1, GETDATE())) = YEAR(MIN(a.fcreatedate)) 
                                                THEN MONTH(DATEADD(MONTH, -1, GETDATE())) - MONTH(MIN(a.fcreatedate)) + 1
        
                                            -- 其他情况（理论上不应该出现）
                                            ELSE 0
                                        END as 'MonthCount'
                                        from t_ydj_order  a  (nolock)
                                        inner join t_bas_agent b (nolock) on a.fmainorgid=b.fid and fisreseller='0' and fagentstatus='1'
                                        where not exists (select 1 from t_bas_mac mac inner join t_bas_macentry macentry  on mac.fid=macentry.fid where macentry.fsubagentid=b.fid and mac.fforbidstatus='0' )
                                         and a.fcancelstatus='0'
                                        group by  a.fmainorgid,b.fnumber,b.fname";
                var allGrpOrgs = this.DBService.ExecuteDynamicObject(ctx, orgsql);
                var _service = ctx.Container.GetService<ProductmonthlySalesService>();
             
                foreach (var item in allGrpOrgs)
                {
                    string fmainorgid = Convert.ToString(item["fmainorgid"]);
                    var ctxX = ctx.CreateAgentDBContext(fmainorgid);
                    try
                    {
                        var result = _service?.ProductMonthlySales(ctxX, item, this.Option);
                        this.WriteLog("经销商 {0}({1}) ：{2}".Fmt(item["fnumber"].ToString(), item["fname"].ToString(), result.IsSuccess? result.ComplexMessage.SuccessMessages.FirstOrDefault() : result.ComplexMessage.ErrorMessages.FirstOrDefault()));
                    }
                    catch (Exception ex) {
                        this.WriteLog("经销商 {0}({1})}计算异常 ：{2}".Fmt(item["fnumber"].ToString(), item["fname"].ToString(), ex.Message));
                    }
                }
            }
            catch(Exception e)
            {
                this.WriteLog("月均销售数量计算异常！" + e.Message);
            }
        }
    }
}
