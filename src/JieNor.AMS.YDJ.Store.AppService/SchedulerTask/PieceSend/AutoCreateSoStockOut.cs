using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using System.Threading;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;
using Newtonsoft.Json;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework.SuperOrm.Drivers;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.E3;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 一件代发销售出库单自动创建并审核
    /// </summary>
    [InjectService]
    [TaskSvrId("autocreatesostockout")]
    [Caption("一件代发销售出库单自动创建并审核（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class AutoCreateSoStockOut : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            var userCtx = this.UserContext;
            var tmpTableName = string.Empty;
            try
            {
                //根据采购入库单找源头的销售合同，通过销售合同下推出库单
                // 获取符合条件的销售合同
                E3Commom e3Commom = new E3Commom(userCtx);
                var result = e3Commom.AutoCreateSoStockOut();
                if (result.SrvData != null)
                {
                    this.WriteLog("更新销售合同物流进度（在总部执行）：" + result.SrvData.ToJson());
                    var srvdata = result.SrvData.ToJson().FromJson<Dictionary<string, List<string>>>();
                    srvdata.TryGetValue("successNo", out List<string> successNo);
                    srvdata.TryGetValue("failNo", out List<string> failNo);
                    srvdata.TryGetValue("message", out List<string> updMessage);
                    this.WriteLog("成功对象编码：" + string.Join(",", successNo));
                    this.WriteLog("失败对象编码：" + string.Join(",", failNo));
                    this.WriteLog("执行消息内容：" + string.Join(",", updMessage));
                    //this.WriteLog(result.SimpleMessage);
                }
                if (!result.IsSuccess)
                    return;

                var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.UserContext, "ms_pulldata");

                var dt = htmlForm.GetDynamicObjectType(this.UserContext);
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, dt);

                List<DynamicObject> dynObjs = new List<DynamicObject>();
                if (result != null && result.IsSuccess)
                {
                    if (result.SrvData != null)
                    {
                        var srvdata = result.SrvData.ToJson().FromJson<Dictionary<string, List<string>>>();
                        srvdata.TryGetValue("successNo", out List<string> successNo);
                        srvdata.TryGetValue("failNo", out List<string> failNo);
                        this.WriteLog("成功对象编码！" + string.Join(",", successNo));
                        this.WriteLog("失败对象编码！" + string.Join(",", failNo));
                        foreach (var dataItem in failNo)
                        {
                            var dynObj = (DynamicObject)dt.CreateInstance();
                            string json = result.ToJson();
                            dynObj["fjson"] = json;
                            dynObj["fmd5"] = SecurityUtil.HashString(json);
                            dynObj["fdatatype"] = "autocreatesostockout";
                            dynObj["fcreatedate"] = BeiJingTime.Now;
                            dynObj["ftranid"] = "";
                            dynObj["fbizformid"] = "ydj_e3logisticsprogress";
                            dynObj["fbizobjno"] = dataItem;
                            dynObj["fbizobjid"] = "";
                            dynObj["fopstatus"] = "0";
                            dynObjs.Add(dynObj);
                        }
                    }
                }
                if (dynObjs.Any())
                {
                    var prepareSaveDataService = this.UserContext.Container.GetService<IPrepareSaveDataService>();
                    prepareSaveDataService.PrepareDataEntity(this.UserContext, htmlForm, dynObjs.ToArray(), this.Option);
                    this.UserContext.SaveBizData(htmlForm.Id, dynObjs);
                }

            }
            catch (Exception e)
            {
                this.WriteLog("一件代发销售出库单自动创建并审核异常！" + e.Message);
            }
        }

        /// <summary>
        /// 获取符合条件的采购入库单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetPendingOrders()
        {
            string sql = $@"select distinct ord.fid,ord.fbillno,a.fid as postockInId,ord.fmainorgid,ordentry.fentryid,ordentry.fproductid,SUM(b.fbizqty) bizqty
                from T_STK_POSTOCKIN  a with(nolock)
                inner join T_STK_POSTOCKINENTRY b with(nolock) on a.fid=b.fid
                inner join T_YDJ_POORDERENTRY poentry with(nolock) on b.fsourceentryid=poentry.fentryid
                inner join T_YDJ_ORDER ord with(nolock) on poentry.fsoorderinterid=ord.fid
                inner join T_YDJ_ORDEREntry ordentry with(nolock) on ord.fid=ordentry.fid and poentry.fsoorderentryid=ordentry.fentryid
                where a.fpiecesendtag=1 and a.fstatus='E' and a.fcancelstatus=0 and a.fisoutstock=0  and ordentry.ftransoutqty=0  and ord.fpiecesendtag=1
               -- and a.fbillno='CGRKD100316800000109'
                group by ord.fid,ord.fbillno,a.fid,ord.fmainorgid,ordentry.fentryid,ordentry.fproductid";
            return this.DBService.ExecuteDynamicObject(this.UserContext, sql);
        }
    }
}
