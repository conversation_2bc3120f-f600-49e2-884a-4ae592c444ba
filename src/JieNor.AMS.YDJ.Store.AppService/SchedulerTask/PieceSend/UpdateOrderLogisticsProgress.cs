using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using System.Threading;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.E3;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 更新销售合同物流进度
    /// </summary>
    [InjectService]
    [TaskSvrId("updateorderlogisticsprogress")]
    [Caption("更新销售合同物流进度（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class UpdateOrderLogisticsProgress : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {

            var userCtx = this.UserContext;
            var tmpTableName = string.Empty;

            try
            {
                E3Commom e3Commom = new E3Commom(userCtx);
                var result = e3Commom.UpdateOrderLogisticsProgress();
                if (result.SrvData != null)
                {
                    this.WriteLog("更新销售合同物流进度（在总部执行）：" + result.SrvData.ToJson());
                    var srvdata = result.SrvData.ToJson().FromJson<Dictionary<string, List<string>>>();
                    srvdata.TryGetValue("successNo", out List<string> successNo);
                    srvdata.TryGetValue("failNo", out List<string> failNo);
                    srvdata.TryGetValue("message", out List<string> updMessage);
                    this.WriteLog("成功对象编码：" + string.Join(",", successNo));
                    this.WriteLog("失败对象编码：" + string.Join(",", failNo));
                    this.WriteLog("执行消息内容：" + string.Join(",", updMessage));
                    //this.WriteLog(result.SimpleMessage);
                }
                if (!result.IsSuccess)
                    return;

                var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.UserContext, "ms_pulldata");

                var dt = htmlForm.GetDynamicObjectType(this.UserContext);
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, dt);

                List<DynamicObject> dynObjs = new List<DynamicObject>();
                if (result != null && result.IsSuccess)
                {
                    if (result.SrvData != null)
                    {
                        var srvdata = result.SrvData.ToJson().FromJson<Dictionary<string, List<string>>>();
                        srvdata.TryGetValue("successNo", out List<string> successNo);
                        srvdata.TryGetValue("failNo", out List<string> failNo);
                        foreach (var dataItem in failNo)
                        {
                            var dynObj = (DynamicObject)dt.CreateInstance();
                            string json = result.ToJson();
                            dynObj["fjson"] = json;
                            dynObj["fmd5"] = SecurityUtil.HashString(json);
                            dynObj["fdatatype"] = "updateorderlogisticsprogress";
                            dynObj["fcreatedate"] = BeiJingTime.Now;
                            dynObj["ftranid"] = "";
                            dynObj["fbizformid"] = "ydj_e3logisticsprogress";
                            dynObj["fbizobjno"] = dataItem;
                            dynObj["fbizobjid"] = "";
                            dynObj["fopstatus"] = "0";
                            dynObjs.Add(dynObj);
                        }
                    }
                }
                if (dynObjs.Any())
                {
                    var prepareSaveDataService = this.UserContext.Container.GetService<IPrepareSaveDataService>();
                    prepareSaveDataService.PrepareDataEntity(this.UserContext, htmlForm, dynObjs.ToArray(), this.Option);
                    this.UserContext.SaveBizData(htmlForm.Id, dynObjs);
                }
            }
            catch (Exception e)
            {
                this.WriteLog("更新销售合同物流进度失败！原因：" + e.Message);
            }
        }

    }
}
