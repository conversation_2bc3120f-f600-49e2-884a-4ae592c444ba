using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask.Fin
{
    /// <summary>
    /// 拉取开户网点数据
    /// </summary>
    [InjectService]
    [TaskSvrId("getopeningbankdata")]
    [Caption("拉取开户网点数据（在总部执行）")]
    [Browsable(false)]
    public class GetOpeningBankData : AbstractScheduleWorker
    {
        private HtmlForm ophtml;
        protected override async Task DoExecute()
        {
            try
            {
                // 2. 加载表单元数据
                var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
                this.ophtml = metaModelService.LoadFormModel(this.UserContext, "ydj_openingbank");
                // 1. 调用外部接口获取开户网点数据
                var apiResult = GetBankDataFromApi();
                if (string.IsNullOrWhiteSpace(apiResult))
                {
                    this.WriteLog("开户网点接口返回为空");
                    return;
                }

                JObject resultObj = JObject.Parse(apiResult);
                JArray itms = resultObj.GetValue("itms") as JArray;

                if (itms == null || !itms.Any())
                {
                    this.WriteLog("开户网点接口无数据");
                    return;
                }
                // 2. 批量获取所有联行号
                var bankNoList = itms.Select(itm => itm["BANKL"]?.ToString()).Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();
                if (!bankNoList.Any())
                {
                    this.WriteLog("接口数据无有效联行号");
                    return;
                }

                var dt = ophtml.GetDynamicObjectType(this.UserContext);
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, dt);
                // 构造批量查询条件
                //string inClause = string.Join(",", bankNoList.Select(x => $"'{x.Replace("'", "''")}'"));
                var opBanks = this.UserContext.LoadBizDataByNo(ophtml.Id,"fnumber", bankNoList);


                List<DynamicObject> toSave = new List<DynamicObject>();
                List<DynamicObject> toDelete = new List<DynamicObject>();

                foreach (var itm in itms)
                {
                    string bankNo = itm["BANKL"]?.ToString();//联行号（编号
                    string bankName = itm["BANKA"]?.ToString();//银行名称（所属银行
                    //string bankCountry = itm["BANKS"]?.ToString();
                    string branch = itm["BRNCH"]?.ToString();//开户网点
                    string itemStatus = itm["STATUS"]?.ToString();

                    // 查询本地是否已存在
                    var exists = opBanks.Where(a => Convert.ToString(a["fnumber"]).Equals(bankNo)).FirstOrDefault();

                    if (itemStatus == "删除")
                    {
                        if (exists != null)
                        {
                            toDelete.Add(exists);
                        }
                        continue;
                    }

                    DynamicObject obj = exists ?? (DynamicObject)dt.CreateInstance();
                    obj["fnumber"] = bankNo;
                    obj["fname"] = branch;
                    obj["fbankname"] = bankName;
                    obj["fjointno"] = bankNo;
                    //obj["fstatus"] = itemStatus;
                    toSave.Add(obj);
                }

                // 3. 执行保存/删除
                if (toSave.Any())
                {
                    //var saveOption = new Dictionary<string, object>
                    //                {
                    //                    { "NotRefreshNumber", true },
                    //                    { "IsMuSiSync", true },
                    //                    { "IgnoreCheckPermssion", true },
                    //                    { "IsReturnBillUniqueValidationDetailErrorMessage", true }
                    //                };
                    //var agentCtx = this.UserContext.CreateTopOrgDBContext();

                    //var Gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();
                    //var result = Gateway.InvokeBillOperation(agentCtx, htmlForm.Id, toSave.ToList(), "save", saveOption);

                    //var prepareSaveDataService = this.UserContext.Container.GetService<IPrepareSaveDataService>();
                    //prepareSaveDataService.PrepareDataEntity(this.UserContext, htmlForm, toSave.ToArray(), this.Option);
                    //this.UserContext.SaveBizData(htmlForm.Id, toSave);
                    dm.Save(toSave);
                    this.WriteLog($"开户网点保存/更新成功：{toSave.Count}条");
                }
                if (toDelete.Any())
                {
                    dm.Delete(toDelete.Select(x => x["id"]).ToList());
                    this.WriteLog($"开户网点删除成功：{toDelete.Count}条");
                }
            }
            catch (Exception ex)
            {
                this.WriteLog("拉取开户网点数据异常：" + ex.Message);
            }
        }

        /// <summary>
        /// 模拟接口调用，实际请替换为真实HTTP请求
        /// </summary>
        private string GetBankDataFromApi()
        {
            Dictionary<string, object> dic = new Dictionary<string, object>();
            //dic.Add("ERDAT", DateTime.Now.ToString("yyyyMMdd"));
            dic.Add("ERDAT", "********");
            var result = MuSiApi.ZY_getBanks(this.UserContext, this.ophtml, dic);

            return JsonConvert.SerializeObject(result);
            //// TODO: 替换为实际接口调用
            //return @"{
            //    ""MESSAGE"": ""Success!"",
            //    ""STATUS"": ""S"",
            //    ""ITMS"": [
            //        {
            //            ""BANKL"": *************,
            //            ""STATUS"": ""启用"",
            //            ""BANKA"": ""农业测试1"",
            //            ""BANKS"": ""CN"",
            //            ""BRNCH"": ""中国银行分行测试1""
            //        }
            //    ]
            //}";
        }
    }
}