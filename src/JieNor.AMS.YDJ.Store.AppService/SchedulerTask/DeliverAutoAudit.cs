using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 针对采购订单变更中，总部变更状态=终审，或驳回。需要审核采购订单变更单；
    /// 注意该计划任务在总部执行（会自动按经销商进行处理），不要放经销商组织进行（上万的经销商，调度出问题不好排查）
    /// </summary>
    //[InjectService(AliasName = "Task")]
    [InjectService]
    [TaskSvrId("deliverautoaudit")]
    [Caption("送达方自动审核（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class DeliverAutoAudit : AbstractScheduleWorker
    {

        protected IHttpServiceInvoker Gateway { get; set; }
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            this.ResetSysAdminUserContext();

            var userCtx = this.UserContext;
            this.Gateway = userCtx.Container.GetService<IHttpServiceInvoker>();

            //取所有门店数据（不分组织） 
            var strSql = $@" SELECT fid FROM t_bas_deliver WHERE fstatus = 'D' AND fcreatorid ='sysadmin' and fforbidstatus = 0 ";
            var idObjs = userCtx.ExecuteDynamicObject(strSql, null);
            if (idObjs == null || idObjs.Count == 0)
            {
                this.WriteLog("未找到相关需要自动审核的送达方数据");
                return;
            }

            //1、送达方对应经销商禁用，无需审核
            //2、送达方对应经销商没有销售组织，无需审核
            var Objs = this.UserContext.LoadBizDataByFilter("bas_deliver", $@" fstatus = 'D' AND fcreatorid ='sysadmin' 
                                                                            and exists (select 1 from t_bas_organization as org with(nolock)  where fagentid = org.fid)
                                                                            and exists (select 1 from t_bas_agent as ag with(nolock) where fagentid = ag.fid and ag.fforbidstatus = 0)");
            foreach (var Obj in Objs)
            {
                var result = this.Gateway.InvokeBillOperation(this.UserContext, "bas_deliver", new[] { Obj }, "auditflow", this.Option.ToDictionary()); 
                this.WriteLog("送达方 {0} ：{1}".Fmt(Obj["fnumber"], result.SimpleMessage));
            }
        }

        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext()
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = this.UserContext.Product;
            session.Company = this.UserContext.Company;
            session.BizOrgId = this.UserContext.Company;
            session.TopCompanyId = this.UserContext.TopCompanyId;
            session.ParentCompanyId = this.UserContext.ParentCompanyId;
            session.Companys = this.UserContext.Companys.ToList();
            session.Id = this.UserContext.Id;

            this.UserContext.SetUserSession(session);
        }
    }
}