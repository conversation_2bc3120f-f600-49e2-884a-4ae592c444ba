using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Model
{
    public class SubmitScanData
    {
        /// <summary>
        /// 任务单号
        /// </summary>
        public string taskno { get; set; }
        /// <summary>
        /// pda
        /// </summary>
        public string pda { get; set; }
        /// <summary>
        /// 是否完成提交
        /// </summary>
        public string iscompletesubmit { get; set; }

        public List<SubmitMaterialInfo> materialInfos { get; set; }
    }
    public class SubmitMaterialInfo
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string materialid { get; set; }
        /// <summary>
        /// 商品编码
        /// </summary>
        public string materialcode { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string materialname { get; set; }
        /// <summary>
        /// 扫描数量
        /// </summary>
        public decimal scanqty { get; set; }
        /// <summary>
        /// 待收数量
        /// </summary>
        public decimal waitqty { get; set; }

        /// <summary>
        /// 辅助属性
        /// </summary>
        public string attrinfo { get; set; }

        /// <summary>
        /// 辅助属性
        /// </summary>
        public string attrinfo_e { get; set; }
        /// <summary>
        /// 定制说明
        /// </summary>
        public string customdesc { get; set; }
        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string mtono { get; set; }
        /// <summary>
        /// 批号
        /// </summary>
        public string lotno { get; set; }
        /// <summary>
        /// 货主类型
        /// </summary>
        public string ownertype { get; set; }
        /// <summary>
        /// 货主
        /// </summary>
        public string ownerid { get; set; }
        /// <summary>
        /// 是否手工新增
        /// </summary>
        public string ishandadd { get; set; }
        /// <summary>
        /// 是否忽略已提交商品进行提交
        /// </summary>
        public string iscansubmit { get; set; }
        /// <summary>
        /// 仓库ID
        /// </summary>
        public string storeid { get; set; }
        /// <summary>
        /// 仓位ID
        /// </summary>
        public string storelocationid { get; set; }
        /// <summary>
        /// 库存状态
        /// </summary>
        public string storestatus { get; set; }
        /// <summary>
        /// 条码信息
        /// </summary>
        public List<SubmitBCInfo> barcodeinfos { get; set; }
    }
    public class SubmitBCInfo
    {
        /// <summary>
        /// 条码
        /// </summary>
        public string barcode { get; set; }
        /// <summary>
        /// 仓库id
        /// </summary>
        public string storeid { get; set; }
        /// <summary>
        /// 仓位id
        /// </summary>
        public string storelocationid { get; set; }
        /// <summary>
        /// 扫描人
        /// </summary>
        public string scanuser { get; set; }
        /// <summary>
        /// 扫描时间
        /// </summary>
        public string scantime { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public int qty { get; set; }
    }
}
