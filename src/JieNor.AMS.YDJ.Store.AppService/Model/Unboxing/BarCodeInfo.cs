using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Model.Unboxing
{
    public class BarCodeInfo
    {
        public string mainbarcode { get; set; }
        public List<MaterialInfo> materials { get; set; }
    }
    public class MaterialInfo
    {
        public string subbarcode { get; set; }
        /// <summary>
        /// 商品
        /// </summary>
        public object material { get; set; }
        /// <summary>
        /// 基本单位
        /// </summary>
        public object baseunit { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        public object stockunit { get; set; }
        /// <summary>
        /// 辅助属性
        /// </summary>
        public object attrinfo { get; set; }
        /// <summary>
        /// 定制说明
        /// </summary>
        public string customdesc { get; set; }
        /// <summary>
        /// 批号
        /// </summary>
        public string lotno { get; set; }
        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string mtono { get; set; }
        /// <summary>
        /// 货主类型
        /// </summary>
        public object ownertype { get; set; }
        /// <summary>
        /// 货主
        /// </summary>
        public string ownerid { get; set; }
        /// <summary>
        /// 规格说明
        /// </summary>
        public string mtrlmodel { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        public object brandid { get; set; }
        /// <summary>
        /// 系列
        /// </summary>
        public object seriesid { get; set; }
        /// <summary>
        /// 库存数量
        /// </summary>
        public decimal stockqty { get; set; }
        /// <summary>
        /// 总包数
        /// </summary>
        public int packcount { get; set; }
        /// <summary>
        /// 包序号
        /// </summary>
        public int packindex { get; set; }
    }
}
