using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Model.DeliveryScanTask
{
    public class DeliveryTaskListModel
    {
        public List<DeliveryTask> deliverytasks { get; set; }
    }
    public class DeliveryTask
    {
        /// <summary>
        /// 主键Id
        /// </summary>
        public string mainid { get; set; }
        /// <summary>
        /// 收货任务单号
        /// </summary>
        public string deliverytaskno { get; set; }
        /// <summary>
        /// 来源单据名称
        /// </summary>
        public string sourcetype { get; set; }
        /// <summary>
        /// 来源单据编码
        /// </summary>
        public string sourcebillno { get; set; }
        /// <summary>
        /// 收货单后(也就是采购入库单号)
        /// </summary>
        public string receptionno { get; set; }
        /// <summary>
        /// 物流单号
        /// </summary>
        public string logisticsno { get; set; }
        /// <summary>
        /// 扫描任务状态
        /// </summary>
        public string taskstatus { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public object warehouse { get; set; }
        /// <summary>
        /// 仓位
        /// </summary>
        public object warehouselocation { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string customer { get; set; } 
        /// <summary>
        /// 客户手机号
        /// </summary>
        public string customerphone { get; set; }
        /// <summary>
        /// 收货人
        /// </summary>
        public string deliveryuser { get; set; }
        /// <summary>
        /// 收货人手机号
        /// </summary>
        public string deliveryuserphone { get; set; }
        /// <summary>
        /// 要货日期
        /// </summary>
        public string deliverydate { get; set; }

        /// <summary>
        /// 出库日期
        /// </summary>
        public string stockoutdate { get; set; }
        /// <summary>
        /// 商品总数
        /// </summary>
        public string materialcount { get; set; }
        /// <summary>
        /// 商品列表
        /// </summary>
        public List<MaterialInfo> materialinfos { get; set; }

        /// <summary>
        /// 工厂发货日期
        /// </summary>
        public string senddate { get; set; }

        /// <summary>
        /// 扫码枪商品排序规则
        /// </summary>
        public string sortrule { get; set; }
    }
    public class MaterialInfo
    {
        /// <summary>
        /// id
        /// </summary>
        public string mainid { get; set; }
        /// <summary>
        /// 明细Id
        /// </summary>
        public string entryid { get; set; }
        /// <summary>
        /// 行号
        /// </summary>
        public int seq { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        public object material { get; set; }

        /// <summary>
        /// 基本单位
        /// </summary>
        public object baseunit { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        public object stockunit { get; set; }
        /// <summary>
        /// 应收数量
        /// </summary>
        public decimal needqty { get; set; }
        /// <summary>
        /// 待收数量
        /// </summary>
        public decimal waitrecqty { get; set; }
        /// <summary>
        /// 已收数量
        /// </summary>
        public decimal recqty { get; set; }
        /// <summary>
        /// 待收包数
        /// </summary>
        public decimal waitpackqty { get; set; }
        /// <summary>
        /// 已收包数
        /// </summary>
        public decimal recpackqty { get; set; }   
        /// <summary>
        /// 辅助属性
        /// </summary>
        public object attrinfo { get; set; }
        public string attrinfo_e { get; set; }
        /// <summary>
        /// 定制说明
        /// </summary>
        public string customdesc { get; set; }
        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string mtono { get; set; }

        /// <summary>
        /// 货主类型
        /// </summary>
        public string ownertype { get; set; }
        /// <summary>
        /// 货主
        /// </summary>
        public string ownerid { get; set; }
        /// <summary>
        /// 包装规则
        /// </summary>
        public string packagerule { get; set; }
        /// <summary>
        /// 是否收货完成
        /// </summary>
        public string iscomplete { get; set; }
        /// <summary>
        /// 批号
        /// </summary>
        public string lotno { get; set; }

        /// <summary>
        /// 来源单类型
        /// </summary>
        public string sourcetype { get; set; }

        /// <summary>
        /// 来源单号
        /// </summary>
        public string sourcebillno { get; set; }
        /// <summary>
        /// 来源单行内码
        /// </summary>
        public string sourceentryid { get; set; }        

        public List<BCReturnData> barcodeinfos { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public object storehouseid { get; set; }

        /// <summary>
        /// 仓位
        /// </summary>
        public object storelocationid { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public object customerid { get; set; }
    }
    public class BCReturnData
    {
        /// <summary>
        /// id
        /// </summary>
        public string mainid { get; set; }
        /// <summary>
        /// 明细id
        /// </summary>
        public string entryid { get; set; }

        /// <summary>
        /// 子明细id
        /// </summary>
        public string subentryid { get; set; }
        /// <summary>
        /// 条码
        /// </summary>
        public string barcode { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        public object warehouse { get; set; }
        /// <summary>
        /// 仓位
        /// </summary>
        public object warehouselocation { get; set; }
        /// <summary>
        /// 扫描人
        /// </summary>
        public object scanuser { get; set; }
        /// <summary>
        /// 扫描时间
        /// </summary>
        public string scantime { get; set; }
        /// <summary>
        /// 是否已提交
        /// </summary>
        public string issubmit { get; set; }
    }
}
