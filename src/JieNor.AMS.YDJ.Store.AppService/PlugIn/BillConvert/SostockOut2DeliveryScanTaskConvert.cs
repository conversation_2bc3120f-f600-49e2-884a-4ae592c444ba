using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售出库单->发货扫描任务的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("bcm_deliveryscantask")]
    [OperationNo("stk_sostockout2bcm_deliveryscantask")]
    public class SostockOut2DeliveryScanTaskConvert : AbstractConvertServicePlugIn
    {

        public override void AfterGetSourceBillData(AfterGetSourceBillDataEventArgs e)
        {
            base.AfterGetSourceBillData(e);

            var fbillnos = e.SourceDataEntities.ToArray().Select(x => (string)x["fbillno"]).Distinct().ToList();
            //if (fbillnos == null || fbillnos.Count() == 0) return;
            ////判断下游销售出库单是否有单据
            //var sql = @"SELECT fid FROM t_stk_postockin 
            //            WHERE fmainorgid = '{0}' AND fsourcetype='t_stk_sostockout' and fsourcenumber in ({1}) and fstatus<>'E' and fcancelstatus='0'".Fmt(this.UserContext.Company, fbillnos.JoinEx(",", true));

            //var dbService = this.UserContext.Container.GetService<IDBService>();
            //var exsit1 = dbService.ExecuteDynamicObject(this.UserContext, sql);
            //if (exsit1.Count > 0)
            //{
            //    throw new BusinessException("当前订单存在下游库存单据, 不允许生成收货任务!");
            //}


            var dbService = this.UserContext.Container.GetService<IDBService>();
            //销售出库单单据编号列表
            var fbillno = e.SourceDataEntities.ToArray().Select(x => (string)x["fbillno"]).Distinct().ToList();
            //判断是否有发货扫描任务记录
            var sqltext = @"select a.fentryid from t_bcm_descantaskentity a left join t_bcm_deliveryscantask b on a.fid = b.fid where b.fmainorgid = '{0}' and a.flinkformid= 'stk_sostockout' and a.flinkbillno in ({1})".Fmt(this.UserContext.Company, fbillno.JoinEx(",", true));

            var exsit2 = dbService.ExecuteDynamicObject(this.UserContext, sqltext);
            if (exsit2.Count > 0)
            {
                throw new BusinessException("当前订单已经存在对应的发货任务, 不允许重复生成!");
            }

        }

        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            var dataEntities = e.TargetDataEntities.ToArray();

            var datas = dataEntities.SelectMany(x =>
            {
                var fentry = x["ftaskentity"] as DynamicObjectCollection;
                return fentry.Where(z => !Convert.ToString(z["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()).Select(y =>
                {
                    return new
                    {
                        fmaterialid = (string)(y["fmaterialid"])//商品id
                    };
                });
            });

            SetProductInfo(datas.Select(x => x.fmaterialid).Distinct().ToList(), dataEntities);

            SetSourceFromRowInfo(dataEntities);
        }

        /// <summary>
        /// 根据商品打包类型设置打包类型,包件数和待扫描包数
        /// </summary>
        /// <param name="productIds"></param>
        /// <param name="dataEntities"></param>
        private void SetProductInfo(List<string> productIds, DynamicObject[] dataEntities)
        {
            var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));
            var products = dm.Select(productIds).OfType<DynamicObject>().ToList();
            if (products == null || products.Count <= 0)
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["ftaskentity"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {
                    var productId = (string)(fentry["fmaterialid"]);
                    var product = products.FirstOrDefault(x => (string)(x["id"]) == productId);
                    if (product != null)
                    {
                        fentry["fpackagingtype"] = product["fpackagtype"];
                        var fwaitworkqty = fentry["fwaitworkqty"] ?? 0;

                        switch (product["fpackagtype"])
                        {
                            case "1":
                                fentry["fpackagingqty"] = "1";
                                fentry["fwaitscanqty"] = fwaitworkqty;
                                break;
                            case "2":
                                fentry["fpackagingqty"] = product["fbag"];
                                fentry["fwaitscanqty"] = Convert.ToInt32(fwaitworkqty) * Convert.ToInt32(product["fbag"]);
                                break;
                            case "3":
                                fentry["fpackagingqty"] = product["fpiece"];
                                fentry["fwaitscanqty"] = Convert.ToInt32(fwaitworkqty) / Convert.ToInt32(product["fpiece"]);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            
        }

        /// <summary>
        /// 设置来源单行号及内码
        /// </summary>
        private void SetSourceFromRowInfo(DynamicObject[] dataEntities)
        {
            var orderDatas = GetLinkOrderData(dataEntities);

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["ftaskentity"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {
                    var fsourceformid = Convert.ToString(fentry["fsourceformid"]) ?? "";
                    var flinkrowinterid = Convert.ToString(fentry["flinkrowinterid"]) ?? "";

                    if (fsourceformid.IsNullOrEmptyOrWhiteSpace() || fsourceformid != "ydj_order")
                        return;
      
                    var orderData = orderDatas.FirstOrDefault(item=> flinkrowinterid == (Convert.ToString(item["soeFentryid"]) ?? ""));
                    if (orderData == null)
                        return;

                    fentry["flinkrownumber"] = orderData["soeFSeq"];
                    fentry["fsourceinterid"] = orderData["yoeFSeq"];

                }
            }
        }


        /// <summary>
        /// 获取销售出库单行号和上游销售合同商品明细行号和行内码
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetLinkOrderData(DynamicObject[] dataEntities)
        {
            var flinkrowinterids = ValueListbyKey("flinkrowinterid", dataEntities);
            var sql = $"select yoe.FSeq yoeFSeq,yoe.fentryid yoeFentryid,soe.FSeq soeFSeq,soe.fentryid soeFentryid from t_stk_sostockoutentry as soe left join T_YDJ_ORDERENTRY as yoe on soe.fsourceentryid = yoe.fentryid left join t_ydj_order  as yo on yo.fid = yoe.fid where soe.fentryid in ({flinkrowinterids.JoinEx(",",true)})";
            var dbService = this.UserContext.Container.GetService<IDBService>();
            return dbService.ExecuteDynamicObject(this.UserContext, sql);
        }


        private IEnumerable<string> ValueListbyKey(string keyName, DynamicObject[] dataEntities)
        {
            var _list = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["ftaskentity"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {                
                    _list.Add(Convert.ToString(fentry[keyName]) ?? "");
                }
            }
            return _list.Where(item=>!item.IsNullOrEmptyOrWhiteSpace());
        }

    }
}

