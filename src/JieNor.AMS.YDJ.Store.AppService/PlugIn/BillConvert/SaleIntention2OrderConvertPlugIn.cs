using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售意向下推销售合同的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("ydj_saleintention2ydj_order")]
    public class SaleIntention2OrderConvertPlugIn : AbstractConvertServicePlugIn
    {
        const string KEYFORMAT = "productId={0}&attrInfo={1}&orderDate={2}";

        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            //只有下推模式才需要对销售员明细做转换（因为选单前合同本身已经有销售员明细了）
            if (e.ConvertMode == Enu_BillConvertMode.Default)
            {
                //对销售员明细的转换
                var saleIntentionForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_saleintention");
                var dm = this.UserContext.Container.GetService<IDataManager>();
                dm.InitDbContext(this.UserContext, saleIntentionForm.GetDynamicObjectType(this.UserContext));

                var sourceDutyEntity = saleIntentionForm.GetEntryEntity("fdutyentry");
                var targetDutyEntity = this.TargetHtmlForm.GetEntryEntity("fdutyentry");

                foreach (var target in e.TargetDataEntities)
                {
                    var sourceType = target["fsourcetype"] as string;
                    var sourceNumber = target["fsourcenumber"] as string;
                    if (!sourceType.EqualsIgnoreCase(saleIntentionForm.Id) || sourceNumber.IsNullOrEmptyOrWhiteSpace()) continue;

                    var dataReader = this.UserContext.GetPkIdDataReader(saleIntentionForm,
                        "fmainorgid=@fmainorgid and fbillno=@fsourcenumber",
                        new List<SqlParam>
                        {
                            new SqlParam("@fmainorgid", System.Data.DbType.String, this.UserContext.Company),
                            new SqlParam("@fsourcenumber", System.Data.DbType.String, sourceNumber)
                        });
                    var sourceOrder = dm.SelectBy(dataReader)?.OfType<DynamicObject>()?.FirstOrDefault();
                    if (sourceOrder == null) continue;
                    var sourceDutyEntrys = sourceDutyEntity.DynamicProperty.GetValue<DynamicObjectCollection>(sourceOrder);
                    if (sourceDutyEntrys == null) continue;

                    var targetDutyEntrys = targetDutyEntity.DynamicProperty.GetValue<DynamicObjectCollection>(target);
                    foreach (var sourceEntry in sourceDutyEntrys)
                    {
                        var targetEntry = new DynamicObject(targetDutyEntity.DynamicObjectType);
                        targetEntry["fismain"] = sourceEntry["fismain"];
                        targetEntry["fdutyid"] = sourceEntry["fdutyid"];
                        targetEntry["fratio"] = sourceEntry["fratio"];
                        targetEntry["famount"] = sourceEntry["famount"];
                        targetEntry["fdescription"] = sourceEntry["fdescription"];
                        targetDutyEntrys.Add(targetEntry);
                    }

                    //已收 freceivable=freceivedamount+fconfirmedamount
                    decimal sumfreceivable = 0;
                    // 收款金额 fsumreceivable= freceivedamount + fconfirmedamount
                    decimal sumfsumreceivable = 0;
                    sumfreceivable =Convert.ToDecimal(sourceOrder["freceivedamount"])  + Convert.ToDecimal(sourceOrder["fconfirmedamount"]);
                    sumfsumreceivable = Convert.ToDecimal(sourceOrder["freceivedamount"]) + Convert.ToDecimal(sourceOrder["fconfirmedamount"]);
                    target["freceivable"] = sumfreceivable;
                    target["fsumreceivable"] = sumfsumreceivable;
                    //明细关闭状态默认为“正常”
                    var entrys = target["fentry"] as DynamicObjectCollection;
                    foreach (var entry in entrys)
                    {
                        if (entry["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace())
                        {
                            entry["fclosestatus_e"] = "0";
                        }
                    }
                    var sourceentrys = sourceOrder["fentity"] as DynamicObjectCollection;
                    //过滤已成单明细
                    List<DynamicObject> toRemoveList = new List<DynamicObject>();
                    sourceentrys.ForEach(a =>
                    {
                        entrys.ForEach(o =>
                        {
                            if (Convert.ToString(a["fdoorderstatus"]) == "1" && Convert.ToString(o["fsourceentryid_e"]) == Convert.ToString(a["id"]))
                            {
                                toRemoveList.Add(o);
                            }
                        });
                    });
                    //entrys.ForEach(o =>
                    //{
                    //    if (Convert.ToString(o["fdoorderstatus"]) == "1")
                    //    {
                    //        toRemoveList.Add(o);
                    //    }
                    //});
                    //toRemoveList = entrys.Where(o => Convert.ToString(o["fdoorderstatus"])== "1") as DynamicObjectCollection;
                    foreach (var item in toRemoveList)
                    {
                        entrys.Remove(item);
                    }
                }
            }

            //bug[1398]，【智谛】《销售意向》转《销售合同》取价逻辑修改
            //该需求要求意向单下推合同时取消取价功能
            //getPrice(e.TargetDataEntities.ToArray());
            compute(e.TargetDataEntities.ToArray());
        }

        private void getPrice(DynamicObject[] dataEntities)
        {
            var datas = dataEntities.SelectMany(x =>
            {
                var fentry = x["fentry"] as DynamicObjectCollection;
                var orderDate = Convert.ToDateTime(x["forderdate"]).ToString("yyyy-MM-dd");//业务日期
                var customerId = Convert.ToString(x["fcustomerid"]);
                var innerCustomerId = Convert.ToString(x["finnercustomerid"]);

                return fentry.Where(z=> Convert.ToString(z["fsuitid"]).IsNullOrEmptyOrWhiteSpace()).Select(y =>
                {
                    var productId = Convert.ToString(y["fproductid"]);//商品id
                    var attrInfo = Convert.ToString(y["fattrinfo"]);//辅助属性
                    return new
                    {
                        clientId = string.Format(KEYFORMAT, productId, attrInfo, orderDate),
                        entry = y,
                        productId = productId,
                        attrInfo = attrInfo,
                        orderDate = orderDate,
                        price = Convert.ToDecimal(y["fprice"]),
                        customerId = customerId,
                        innerCustomerId= innerCustomerId,
                        stockStatus = Convert.ToString(y["fstockstatus"])
                    };
                });
            }).Where(x => string.IsNullOrWhiteSpace(x.productId) == false).ToList();

            if (datas.Count <= 0)
            {
                compute(dataEntities);
                return;
            }

            setSupplierInfo(datas.Select(x => x.productId).Distinct().ToList(), dataEntities);

            var requestDatas = datas.Distinct(x => x.clientId).Select(x => new
            {
                clientId = x.clientId,
                productId = x.productId,
                bizDate = x.orderDate,
                customerId = x.customerId,
                stockStatus = x.stockStatus,
                innerCustomerId=x.innerCustomerId,
                length = 0,
                width = 0,
                thick = 0,
                attrInfo = new
                {
                    id = x.attrInfo
                }
            }).ToList();

            var gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();

            // 获取价格
            var result = gateway.InvokeBillOperation(this.UserContext,
                            "ydj_price",
                             null,
                            "getprices",
                            new Dictionary<string, object>
                            {
                               { "productInfos",JsonConvert.SerializeObject(requestDatas)},
                                { "priceFlag",3}
                            }
                         );

            if (result == null || !result.IsSuccess || result.SrvData == null)
            {
                compute(dataEntities);
                return;
            }

            JArray array = JArray.FromObject(result.SrvData);

            if (array == null || array.Count <= 0)
            {
                compute(dataEntities);
                return;
            }

            foreach (var data in datas)
            {
                var jObj = array.FirstOrDefault(x => (string)x["clientId"] == data.clientId) as JObject;
                if (jObj != null && ((bool)jObj["success"]))
                {
                    var fprice = Convert.ToDecimal(data.entry["fprice"]);
                    data.entry["fprice"] = fprice <= 0 ? Convert.ToDecimal(jObj["salPrice"]) : fprice;
                    data.entry["fsellprice"] = Convert.ToDecimal(jObj["definedPrice"]);
                }
            }

            compute(dataEntities);
        }

        private void compute(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentry"] as DynamicObjectCollection;
                decimal sum = 0;
                decimal sumdealamount = 0;
                decimal sumdistamount = 0;
                foreach (var fentry in fentries)
                {
                    var fprice = Convert.ToDecimal(fentry["fprice"]);
                    var amount = fprice * Convert.ToDecimal(fentry["fbizqty"]); //计算金额
                    fentry["famount"] = amount; //合同初始化时成交金额等于金额

                    //如果源单有成交价，则以源单为准
                    var fdealprice = Convert.ToDecimal(fentry["fdealprice"]);
                    if (fdealprice == 0)
                    {
                        if (Convert.ToDecimal(fentry["fdistrate"]) == 0)
                        {
                            fentry["fdistrate"] = 10; //合同初始化时折扣等于10;
                            fentry["fdistamount"] = 0; //合同初始化时折扣金额等于0
                        }
                        fdealprice = fprice * Convert.ToDecimal(fentry["fdistrate"]) / 10;
                    }
                    var dealamount = fdealprice * Convert.ToDecimal(fentry["fbizqty"]);//计算成交金额
                    fentry["fdealprice"] = fdealprice;
                    fentry["fdealamount"] = dealamount;
                    var fdistamount = amount - dealamount;
                    fentry["fdistamount"] = fdistamount;
                    sumdistamount += fdistamount;
                    sum += amount;
                    sumdealamount += dealamount;
                }
                dataEntity["fdealamount"] = sumdealamount; //合同初始化时成交总金额等于总金额
                dataEntity["ffaceamount"] = sum; //合同初始化时货品原值等于总金额
                dataEntity["fsumamount"] = sumdealamount; //合同初始化时订单总额等于成交总额
                dataEntity["funreceived"] = sum - Convert.ToDecimal(dataEntity["freceivable"]); //未收金额=成交总金额 + 费用收入 - 已收金额=订单总额 - 已收金额;
                dataEntity["fdistamount"] = sumdistamount;
                dataEntity["fdistsumrate"] = sum == 0 ? 1 : Math.Round((sumdealamount / sum), 2, MidpointRounding.AwayFromZero);
            }
        }

        private void setSupplierInfo(List<string> productIds, DynamicObject[] dataEntities)
        {
            var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));
            var products = dm.Select(productIds).OfType<DynamicObject>().ToList();
            if (products == null || products.Count <= 0)
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {
                    var productId = Convert.ToString(fentry["fproductid"]);
                    var product = products.FirstOrDefault(x => Convert.ToString(x["id"]) == productId);
                    if (product != null)
                    {
                        fentry["fsupplierid"] = Convert.ToString(product["fsupplierid"]);
                    }
                }
            }
        }
    }
}