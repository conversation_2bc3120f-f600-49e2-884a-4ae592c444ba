using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 采购退货通知生成采购退货单
    /// </summary>
    [InjectService]
    [FormId("stk_postockreturn")]
    [OperationNo("pur_returnnotice2stk_postockreturn")]
    public class ReturnNotice2PoStockReturnConvertPlugIn : BaseScanTaskConvertPlugIn
    {
    }
}
