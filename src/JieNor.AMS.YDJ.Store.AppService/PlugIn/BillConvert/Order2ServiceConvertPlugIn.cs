using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售合同下推服务单的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("ydj_service")]
    [OperationNo("ydj_order2ydj_service")]
    public class Order2ServiceConvertPlugIn : AbstractConvertServicePlugIn
    {
        /// <summary>
        /// 单据转换执行完成后接口（目标单数据包将会生成）
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            var targetEntity = e.TargetDataEntities.FirstOrDefault();
            if (targetEntity == null) return;

            if (e.ConvertMode != Enu_BillConvertMode.Default) return;

            //通过销售合同的【销售部门】字段去取关联的客户资料（通过客户的所属门店进行查询）且只查询客户类型为（商户）的客户资料
            var orderForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_order");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, orderForm.GetDynamicObjectType(this.UserContext));

            var sourceType = targetEntity["fsourcetype"] as string;
            var sourceNumber = targetEntity["fsourcenumber"] as string;
            if (!sourceType.EqualsIgnoreCase(orderForm.Id) || sourceNumber.IsNullOrEmptyOrWhiteSpace()) return;

            var sqlText = @"select top 1 c.fid fcustomerid from t_ydj_order o 
            inner join t_ydj_customer c on c.fdeptid = o.fdeptid
            where o.fmainorgid=@fmainorgid and c.fmainorgid=@fmainorgid and o.fbillno = @fbillno and c.fcustype = 'customercate_03'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.UserContext.Company),
                new SqlParam("@fbillno", System.Data.DbType.String, sourceNumber)
            };

            var dbService = this.UserContext.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(this.UserContext, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    targetEntity["fdealerid"] = reader.GetString("fcustomerid");
                }
            }
        }
    }
}