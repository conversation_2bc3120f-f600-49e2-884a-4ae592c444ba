using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 其它入库单->包装清单的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("bcm_packorder")]
    [OperationNo("stk_otherstockin2bcm_packorder")]
    public class OtherStockIn2PackOrderConvertPlugIn : AbstractConvertServicePlugIn
    {
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            var dataEntities = e.TargetDataEntities.ToArray();

            var datas = dataEntities.SelectMany(x =>
            {
                var fentry = x["fentity"] as DynamicObjectCollection;
                return fentry.Where(z => !Convert.ToString(z["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()).Select(y =>
                {
                    return new
                    {
                        fmaterialid = (string)(y["fmaterialid"])//商品id
                    };
                });
            });

            SetProductInfo(datas.Select(x => x.fmaterialid).Distinct().ToList(), dataEntities);
        }

        /// <summary>
        /// 根据商品打包类型设置打包类型,包件数和待扫描包数
        /// </summary>
        /// <param name="productIds"></param>
        /// <param name="dataEntities"></param>
        private void SetProductInfo(List<string> productIds, DynamicObject[] dataEntities)
        {
            var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));
            var products = dm.Select(productIds).OfType<DynamicObject>().ToList();
            if (products == null || products.Count <= 0)
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var fentry in fentries)
                {

                    var productId = (string)(fentry["fmaterialid"]);
                    var product = products.FirstOrDefault(x => (string)(x["id"]) == productId);
                    if (product != null)
                    {
                        fentry["fpacktype"] = product["fpackagtype"];
                        switch (product["fpackagtype"])
                        {
                            case "1":
                                fentry["fpackcount"] = "1";
                                break;
                            case "2":
                                fentry["fpackcount"] = product["fbag"];
                                break;
                            case "3":
                                fentry["fpackcount"] = product["fpiece"];
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
        }

    }
}
