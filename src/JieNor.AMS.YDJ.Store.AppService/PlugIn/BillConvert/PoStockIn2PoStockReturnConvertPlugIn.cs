using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 采购入库单->采购退货的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("stk_postockreturn")]
    [OperationNo("stk_postockin2stk_postockreturn")]
    public class PoStockIn2PoStockReturnConvertPlugIn : AbstractConvertServicePlugIn
    {
        protected IEnumerable<Dictionary<string,object>> InteractReturnData { get; set; }

        protected override void OnInitialized(InitializeServiceEventArgs e)
        {
            base.OnInitialized(e);

            var returnGoodData = new List<Dictionary<string, object>>();
            this.Option.TryGetVariableValue("returnData", out returnGoodData);
            this.InteractReturnData = returnGoodData;
        }
        /// <summary>
        /// 获得来源单数据后事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterGetSourceBillData(AfterGetSourceBillDataEventArgs e)
        {
            base.AfterGetSourceBillData(e);
        }

        /// <summary>
        /// 字段值映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            if (this.InteractReturnData == null
                || this.InteractReturnData.Any()==false) return;
            if (e.SourceDataEntities.Any() == false) return;

            var targetFldKey = e.FieldMapObject?.Id;
            var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
            if (targetField == null) return;
            
            bool isCancel = false;
            object targetValue = null;

            var srcEntryId = e.SourceDataEntities.First().GetString("fentity_id");
            var existReturnObj = this.InteractReturnData.FirstOrDefault(o => o.GetString("entryPkValue").EqualsIgnoreCase(srcEntryId));
            if (existReturnObj == null) return;

            object returnQty = 0m;

            switch (targetField.Id.ToLower())
            {
                case "fqty":
                case "fstockqty":
                    if (existReturnObj.TryGetValue("returnQty", out returnQty))
                    {
                        isCancel = true;
                        targetValue = returnQty;
                    }
                    break;
                case "famount":
                    var dPrice = Convert.ToDecimal(e.SourceDataEntities.First().GetValue("fprice", 0m));
                    if (existReturnObj.TryGetValue("returnQty", out returnQty))
                    {
                        isCancel = true;
                        targetValue = dPrice * Convert.ToDecimal(returnQty);
                    }
                    break;
                case "fpoamount":
                    var dPoPrice = Convert.ToDecimal(e.SourceDataEntities.First().GetValue("fpoprice", 0m));
                    if (existReturnObj.TryGetValue("returnQty", out returnQty))
                    {
                        isCancel = true;
                        targetValue = dPoPrice * Convert.ToDecimal(returnQty);
                    }
                    break;
                case "fentrynote":
                    object entryNote = "";
                    if (existReturnObj.TryGetValue("returnNote", out entryNote))
                    {
                        isCancel = true;
                        targetValue = entryNote;
                    }
                    break;
                case "ffeedbackinterid":
                    object feedbackinterid = "";
                    if (existReturnObj.TryGetValue("feedbackinterid", out feedbackinterid))
                    {
                        isCancel = true;
                        targetValue = feedbackinterid;
                    }
                    break;
                case "ffeedbackentryid":
                    object feedbackentryid = "";
                    if (existReturnObj.TryGetValue("feedbackentryid", out feedbackentryid))
                    {
                        isCancel = true;
                        targetValue = feedbackentryid;
                    }
                    break;
            }

            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
        }

        /// <summary>
        /// 退货单字段计算逻辑处理
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities?.Any() != true)
            {
                throw new BusinessException("退货失败：未能成功生成采购退货单！");
            }

            var actualReturnAmount = 0m;
            var returnReason = "";
            var returnType = "";
            this.Option.TryGetVariableValue("refundAmount", out actualReturnAmount);
            this.Option.TryGetVariableValue("returnReason", out returnReason);
            this.Option.TryGetVariableValue("returnType", out returnType);

            var targetReturnBillObjs = e.TargetDataEntities;
            //将退货单表体金额汇总至表头，同时设置退货业务类型及退货原因
            decimal dTotalReturnAmount = 0m;
            foreach (var returnBillObj in targetReturnBillObjs)
            {
                var entryObjs = returnBillObj["fentity"] as DynamicObjectCollection;
                
                var totalReturnAmount = entryObjs.Sum(o => (decimal)o["famount"]);
                returnBillObj["fplanreturnamount"] = totalReturnAmount;
                dTotalReturnAmount += totalReturnAmount;

                returnBillObj["freturnreason"] = returnReason;
                switch (returnType)
                {
                    case "postockreturn_scenetype_01":
                        returnBillObj["fplanreturnamount"] = 0;
                        returnBillObj["freturntype"] = "postockreturn_biztype_01";
                        break;
                    case "postockreturn_scenetype_02":
                        returnBillObj["freturntype"] = "postockreturn_biztype_02";
                        break;
                }
            }
            //分别设置实退金额（按多单应退货款金额比例进行分摊）
            if (dTotalReturnAmount > 0)
            {
                int totalTargetBills = targetReturnBillObjs.Count();
                decimal dAllocated = 0m;
                for (int i = 0; i < totalTargetBills; i++)
                {
                    var returnBillObj = targetReturnBillObjs.ElementAt(i);
                    var currPlanReturnAmt = (decimal)returnBillObj["fplanreturnamount"];
                    decimal currActualAmt = 0m;
                    if (i == totalTargetBills - 1)
                    {
                        currActualAmt = actualReturnAmount - dAllocated;
                    }
                    else
                    {
                        currActualAmt = MathUtil.Round(actualReturnAmount * currPlanReturnAmt / dTotalReturnAmount, 2, RoundMode.AwayFromZero);
                    }

                    returnBillObj["factualreturnamount"] = currActualAmt;
                    dAllocated += currActualAmt;
                }
            }
        }
    }
}
