using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售合同自动下推库存调拨单的单据转换插件
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("ydj_order2stk_inventorytransfer.auto")]
    public class Order2InventoryTransferAutoConvertPlugIn : AbstractConvertServicePlugIn
    {
        private List<Dictionary<string, object>> OutspotEntries;

        protected override void OnInitialized(InitializeServiceEventArgs e)
        {
            base.OnInitialized(e);

            this.Option.TryGetVariableValue("outspotEntries", out OutspotEntries);

            if (OutspotEntries == null)
            {
                OutspotEntries = new List<Dictionary<string, object>>();
            }
        }

        /// <summary>
        /// 字段值映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeMapFieldValue(BeforeMapFieldValueEventArgs e)
        {
            if (e.SourceDataEntities.Any() == false) return;

            var targetFldKey = e.FieldMapObject?.Id;
            var targetField = this.TargetHtmlForm?.GetField(targetFldKey);
            if (targetField == null) return;

            bool isCancel = false;
            object targetValue = null;

            var srcEntryId = e.SourceDataEntities.First().GetString("fentry_id");
            var existOutspotEntry = this.OutspotEntries.FirstOrDefault(x => x.GetString("entryId").EqualsIgnoreCase(srcEntryId));
            if (existOutspotEntry == null) return;

            object qty = 0m;
            var targetFieldId = targetField.Id.ToLower();
            switch (targetFieldId)
            {
                case "fqty":
                    if (existOutspotEntry.TryGetValue("qty", out qty))
                    {
                        isCancel = true;
                        targetValue = qty;
                    }
                    break;
            }

            if (isCancel)
            {
                e.Cancel = true;
                targetField.DynamicProperty.SetValue(e.TargetEntryDataEntity, targetValue);
            }
        }
    }
}
