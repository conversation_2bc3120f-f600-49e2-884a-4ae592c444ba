using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 销售合同下推销售出库单
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("sal_deliverynotice2stk_sostockout")]
    public class SalDelivery2OutStockConvertPlugIn : BaseScanTaskConvertPlugIn
    {
        /// <summary>
        /// 获取源单数据查询对象接口，允许添加额外需要获取的源单数据字段或提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareQueryBuilderParameter(OnPrepareQueryBuilderParameterEventArgs e)
        {
        }

        /// <summary>
        /// 单据转换执行完成后生成关联数据行接口
        /// </summary>
        /// <param name="e"></param>
        public override void OnCreateLinkData(OnCreateLinkDataEventArgs e)
        {
            if (e.LinkEntryData == null) return;
            if (e.LinkRelation == null) return;

        }

        /// <summary>
        /// 单据转换执行完成后接口（目标单数据包将会生成）
        /// </summary>
        /// <param name="e"></param>
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || e.TargetDataEntities.Count() <= 0) return;

            StockPickSetting setting = new StockPickSetting
            {
                ActiveEntityKey = "fentity",
                QtyFieldKey = "fqty",
                PlanQtyFieldKey = "fplanqty",
                StockQtyFieldKey = "fstockqty",
                PriceFieldKey = "",
                AmountFieldKey = ""
            };

            var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var targetForm = metaModelService.LoadFormModel(this.UserContext, e.TargetFormId);

            var stockPickService = this.UserContext.Container.GetService<IStockPickService>();
            var result = stockPickService.Picking(this.UserContext, setting, targetForm, e.TargetDataEntities, this.Option);

            result.ThrowIfHasError(true, "库存拣货出现意外错误！");
        }
    }
}
