using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 收货扫描任务下推包装清单
    /// </summary>
    [InjectService]
    [FormId("bcm_packorder")]
    [OperationNo("bcm_receptionscantask2bcm_packorder")]
    public class ReceptionScanTask2PackOrderConvertPlugIn : AbstractConvertServicePlugIn
    {
        public override void OnConvertComplete(OnConvertCompleteEventArgs e)
        {
            base.OnConvertComplete(e);

            if (e.TargetDataEntities == null || !e.TargetDataEntities.Any()) return;

            var allMaterialIds = e.TargetDataEntities.SelectMany(f => (f["fentity"] as DynamicObjectCollection)
                                          .Select(x => Convert.ToString(x["fmaterialid"]))).Distinct().ToList();
            var allMaterialObjs = this.UserContext.LoadBizDataById("ydj_product", allMaterialIds);

            foreach (var item in e.TargetDataEntities)
            {
                var packSourceEntrys = item["fentity"] as DynamicObjectCollection;
                foreach (var entry in packSourceEntrys)
                {
                    var materialId = Convert.ToString(entry["fmaterialid"]);
                    var materialObj = allMaterialObjs.Find(t => Convert.ToString(t["id"]) == materialId);
                    var packType = Convert.ToString(materialObj?["fpackagtype"]);
                    entry["fpackcount"] = 1;
                    if (!string.IsNullOrEmpty(packType))
                    {
                        entry["fpacktype"] = packType;
                        var bizremainqty = Convert.ToDecimal(entry["fbizremainqty"]);
                        switch (packType)
                        {
                            case "1":
                                entry["fpackcount"] = 1;
                                entry["fbizqty"] = bizremainqty;
                                break;
                            case "2":
                                var bag= Convert.ToInt32(materialObj?["fbag"]);
                                entry["fpackcount"] = bag;
                                entry["fbizqty"] = bizremainqty * bag;
                                break;
                            case "3":
                                var piece = Convert.ToInt32(materialObj?["fpiece"]);
                                entry["fpackcount"] = piece;
                                entry["fbizqty"] = bizremainqty / piece;
                                break;
                        }
                    }
                }
            }
        }
    }
}
