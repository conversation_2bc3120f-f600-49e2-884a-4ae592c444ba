using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MobileShop
{
    [InjectService]
    [FormId("ydj_msmenu")]
    [OperationNo("addtimes")]
    public class AddTimes: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var id = this.GetQueryOrSimpleParam<string>("id");

            if (string.IsNullOrEmpty(id))
            {
                throw new BusinessException("请提交要保存的菜单id");
            }

            //根据当前用户id获取用户的自定义菜单id
            var userMenuForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_msusermenu");
            var userMenuDm = this.Container.GetService<IDataManager>();
            userMenuDm.InitDbContext(this.Context, userMenuForm.GetDynamicObjectType(this.Context));

            string where = $"fuserid=@fuserid and fmenuid=@fmenuid and fappid=@fappid";
            SqlParam[] sqlParams = new SqlParam[]
            {
                new SqlParam("fuserid",System.Data.DbType.String,this.Context.UserId),
                new SqlParam("fmenuid",System.Data.DbType.String,id),
                new SqlParam("fappid",System.Data.DbType.String,this.Context.AppId)
            };
            var dataReader = this.Context.GetPkIdDataReader(userMenuForm, where, sqlParams);
            var userMenuEntity = userMenuDm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

            if (userMenuEntity == null)
            {
                throw new BusinessException("您无此菜单");
            }

            int ftimes = Convert.ToInt32(userMenuEntity["ftimes"]);
            ftimes++;
            userMenuEntity["ftimes"] = ftimes;

            userMenuDm.Save(userMenuEntity);

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "增加使用频率成功!";
        }
    }
}
