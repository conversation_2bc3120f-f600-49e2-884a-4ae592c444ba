using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MobileShop
{
    [InjectService]
    [FormId("ydj_msmenueditor")]
    [OperationNo("listmenus")]
    public class ListMenus : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            var menuForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_msmenu");
            var menuDm = this.Container.GetService<IDataManager>();
            menuDm.InitDbContext(this.Context, menuForm.GetDynamicObjectType(this.Context));

            //获取全部菜单
            string where = string.Empty;
            var dataReader = this.Context.GetPkIdDataReader(menuForm, where, new List<SqlParam>());
            List<DynamicObject> menuEntities = menuDm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

            List<object> srvData = new List<object>();

            if (menuEntities != null && menuEntities.Count > 0)
            {
                var appIdEntities = menuEntities.GroupBy(x => Convert.ToString(x["fappid"])).Select(x => new { appId = x.Key, groups = x.ToList() }).ToList();

                foreach (var appIdEntity in appIdEntities)
                {
                    var appIdItem = new { appId = appIdEntity.appId, groups = new List<object>() };
                    srvData.Add(appIdItem);

                    var groupEntities = appIdEntity.groups.GroupBy(x => Convert.ToString(x["fgroup"]))
                                             .Select(g => new { group = g.Key, groupSort = g.Min(x => Convert.ToInt32(x["fgroupsort"])), menus = g.ToList() })
                                             .ToList();
                    groupEntities.Sort((x, y) =>
                    {
                        return x.groupSort - y.groupSort;
                    });

                    foreach(var groupEntity in groupEntities)
                    {
                        var groupItem = new { group = groupEntity.group, groupSort = groupEntity.groupSort, menus = new List<object>() };
                        appIdItem.groups.Add(groupItem);

                        var menus = groupEntity.menus;
                        menus.Sort((x, y) => Convert.ToInt32(x["fmenusort"]) - Convert.ToInt32(y["fmenusort"]));

                        foreach(var menu in menus)
                        {
                            groupItem.menus.Add(new
                            {
                                id = menu["id"],
                                name = menu["fname"],
                                menuSort = menu["fmenusort"],
                                isDefault = menu["fisdefault"],
                                icon = menu["ficon"],
                                link = menu["flink"],
                                parameter = menu["fparameter"]
                            });
                        }
                    }
                 }
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "获取全部菜单成功!";
            this.Result.SrvData = srvData;

        }
    }
}
