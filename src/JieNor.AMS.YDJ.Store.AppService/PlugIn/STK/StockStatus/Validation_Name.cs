using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.StockStatus
{
    /// <summary>
    /// 库存状态名称校验
    /// </summary>
    public class Validation_Name : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option,
            string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            string sql = $@"
select fname from {formInfo.HeadEntity.TableName} 
where fmainorgid='0' and fname in ('{string.Join("','", dataEntities.Select(s => s["fname"]))}') and fispreset='1'";

            var dynObjs = this.DBService.ExecuteDynamicObject(userCtx, sql);

            foreach (var dataEntity in dataEntities)
            {
                string fname = Convert.ToString(dataEntity["fname"]);

                if (dynObjs.Any(s => Convert.ToString(s["fname"]).EqualsIgnoreCase(fname)))
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"数据重复：已经存在【名称 为 '{fname}'】的预设库存状态 ！",
                        DataEntity = dataEntity
                    });
                }
            }

            return result;
        }
    }
}
