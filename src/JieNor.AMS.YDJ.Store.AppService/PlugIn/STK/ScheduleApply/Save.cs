using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.ScheduleApply
{
    /// <summary>
    /// 排单计划单申请：保存
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleapply")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var customerMsg = "客户信息不能为空!";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fapplytype = Convert.ToString(newData["fapplytype"]);
                var fcustomerid = Convert.ToString(newData["fcustomerid"]);
                var fstockdeptidto = Convert.ToString(newData["fstockdeptidto"]);
                if (fapplytype == "3")
                {
                    if (string.IsNullOrWhiteSpace(fstockdeptidto) && string.IsNullOrWhiteSpace(fcustomerid))
                    {
                        customerMsg = "客户信息和收货部门不能同时为空!";
                        return false;
                    }
                    return true;
                }
                if (fapplytype == "4")
                {
                    //"收货人，收货部门，收货单位不能同时为空!";
                    //收货单位就是客户，收货人和收货部门下一个规则已校验
                    var fstockstaffidto = Convert.ToString(newData["fstockstaffidto"]);
                    if(string.IsNullOrWhiteSpace(fstockdeptidto) && string.IsNullOrWhiteSpace(fcustomerid) && string.IsNullOrWhiteSpace(fstockstaffidto))
                    {
                        customerMsg = "收货人，收货部门，客户不能同时为空!";
                        return false;
                    }
                    return true;
                }
                return false == string.IsNullOrWhiteSpace(fcustomerid);
            }).WithMessage("{0}", (n, o) => customerMsg));

            var errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fstockdateto = Convert.ToString(newData["fstockdateto"]);
                var fstockdeptidto = Convert.ToString(newData["fstockdeptidto"]);
                var fstockstaffidto = Convert.ToString(newData["fstockstaffidto"]);
                var fstockaddressto = Convert.ToString(newData["fstockaddressto"]);
                var fstockdate = Convert.ToString(newData["fstockdate"]);
                var fstockdeptid = Convert.ToString(newData["fstockdeptid"]);
                var fstockstaffid = Convert.ToString(newData["fstockstaffid"]);
                var fstockaddress = Convert.ToString(newData["fstockaddress"]);
                var fapplytype = Convert.ToString(newData["fapplytype"]);
                var fbillno = Convert.ToString(newData["fbillno"]);
                var msgBuilder = new StringBuilder();
                switch (fapplytype)
                {
                    case "0":
                    case "1":
                        //检查收货人信息必录
                        checkReceiptInfo(fbillno, msgBuilder, fstockdateto, fstockdeptidto, fstockstaffidto, fstockaddressto);
                        break;
                    case "2":
                        //检查发货人信息必录
                        checkSendInfo(fbillno, msgBuilder, fstockdate, fstockdeptid, fstockstaffid, fstockaddress, byte.MaxValue - 8);
                        checkReceiptInfo(fbillno, msgBuilder, fstockdateto, fstockdeptidto, fstockstaffidto, fstockaddressto, byte.MaxValue - 9);
                        break;
                    case "3":
                        checkSendInfo(fbillno, msgBuilder, fstockdate, fstockdeptid, fstockstaffid, fstockaddress);
                        break;
                    case "4":
                        checkSendInfo(fbillno, msgBuilder, fstockdate, fstockdeptid, fstockstaffid, fstockaddress, byte.MaxValue - 8);
                        break;
                }
                var fentities = newData["fentity"] as DynamicObjectCollection;
                var i = 1;
                foreach (var fentity in fentities)
                {
                    var fstorehouseid = Convert.ToString(fentity["fstorehouseid"]);
                    var fstockstatus = Convert.ToString(fentity["fstockstatus"]);
                    var fstorehouseidto = Convert.ToString(fentity["fstorehouseidto"]);
                    var fstockstatusto = Convert.ToString(fentity["fstockstatusto"]);
                    var fplanbackdate = Convert.ToString(fentity["fplanbackdate"]);
                    var fqty = Convert.ToDecimal(fentity["fqty"]);

                    if (fqty <= 0)
                    {
                        msgBuilder.AppendLine($"编号为{fbillno}的单据第{i}的申请明细的数量不能小于等于零");
                    }

                    switch (fapplytype)
                    {
                        case "0":
                        case "1":
                            //检查收货仓和收货仓状态必录
                            //checkReceiptInfo(fbillno, i, msgBuilder, fstorehouseidto, fstockstatusto);
                            break;
                        case "2":
                            //检查预回货信息必录,且预回货日期不能小于调拨日期(即发货日期)
                            checkInfo(fbillno, i, msgBuilder, fplanbackdate, fstockdate);
                            //检查发货仓和发货仓状态必录
                            //checkSendInfo(fbillno, i, msgBuilder, fstorehouseid, fstockstatus);
                            //checkReceiptInfo(fbillno, i, msgBuilder, fstorehouseidto, fstockstatusto);
                            break;
                        case "4":
                            //checkSendInfo(fbillno, i, msgBuilder, fstorehouseid, fstockstatus);
                            //checkReceiptInfo(fbillno, i, msgBuilder, fstorehouseidto, fstockstatusto);
                            break;
                        case "3":
                            //checkSendInfo(fbillno, i, msgBuilder, fstorehouseid, fstockstatus);
                            break;
                    }

                    i++;
                }
                errorMsg = msgBuilder.ToString();
                return errorMsg.Length <= 0;
            }).WithMessage("{0}", (n,o)=>errorMsg));
        }

        private void checkSendInfo(string fbillno, StringBuilder msgBuilder, string fstockdate, string fstockdeptid, string fstockstaffid, string fstockaddress, byte flag = byte.MaxValue)
        {
            checkInfo(fbillno, msgBuilder, fstockdate, fstockdeptid, fstockstaffid, fstockaddress, "发", flag);
        }

        //private void checkSendInfo(string fbillno, int row, StringBuilder msgBuilder, string fstorehouseid, string fstockstatus)
        //{
        //    checkInfo(fbillno, row, msgBuilder, fstorehouseid, fstockstatus, "发");
        //}

        private void checkReceiptInfo(string fbillno, StringBuilder msgBuilder, string fstockdateto, string fstockdeptidto, string fstockstaffidto, string fstockaddressto, byte flag = byte.MaxValue)
        {
            checkInfo(fbillno, msgBuilder, fstockdateto, fstockdeptidto, fstockstaffidto, fstockaddressto, "收", flag);
        }

        //private void checkReceiptInfo(string fbillno, int row, StringBuilder msgBuilder, string fstorehouseidto, string fstockstatusto)
        //{
        //    checkInfo(fbillno, row, msgBuilder, fstorehouseidto, fstockstatusto, "收");
        //}

        private void checkInfo(string fbillno, int row, StringBuilder msgBuilder, string fplanbackdate, string fstockdate)
        {
            if (string.IsNullOrWhiteSpace(fplanbackdate))
            {
                msgBuilder.AppendLine($"编号为{fbillno}的单据第{row}行申请明细的预计回货日期不能为空!");
            }
            else
            {
                if (string.IsNullOrWhiteSpace(fstockdate) == false)
                {
                    var planbackdate = Convert.ToDateTime(fplanbackdate);
                    var stockdate = Convert.ToDateTime(fstockdate);
                    if ((planbackdate - stockdate).Days < 0)
                    {
                        msgBuilder.AppendLine($"编号为{fbillno}的单据第{row}行申请明细的预计回货日期不能小于发货日期!");
                    }
                }
            }
        }

        //private void checkInfo(string fbillno, int row, StringBuilder msgBuilder, string fstorehouseid, string fstockstatus, string typeInfo)
        //{
        //    if (string.IsNullOrWhiteSpace(fstorehouseid))
        //    {
        //        msgBuilder.AppendLine($"编号为{fbillno}的单据第{row}行申请明细的{typeInfo}货仓库不能为空!");
        //    }
        //    if (string.IsNullOrWhiteSpace(fstockstatus))
        //    {
        //        msgBuilder.AppendLine($"编号为{fbillno}的单据第{row}行申请明细的{typeInfo}货仓状态不能为空!");
        //    }
        //}

        private void checkInfo(string fbillno, StringBuilder msgBuilder, string fstockdate, string fstockdeptid, string fstockstaffid, string fstockaddress, string typeInfo, byte flag = byte.MaxValue)
        {
            if (string.IsNullOrWhiteSpace(fstockdate) && (flag & 1) > 0)
            {
                msgBuilder.AppendLine($"编号为{fbillno}的单据的{typeInfo}货日期不能为空!");
            }
            if (string.IsNullOrWhiteSpace(fstockstaffid) && (flag & 2) > 0)
            {
                msgBuilder.AppendLine($"编号为{fbillno}的单据的{typeInfo}货人不能为空!");
            }
            if (string.IsNullOrWhiteSpace(fstockdeptid) && (flag & 4) > 0)
            {
                msgBuilder.AppendLine($"编号为{fbillno}的单据的{typeInfo}货部门不能为空!");
            }
            if (string.IsNullOrWhiteSpace(fstockaddress) && (flag & 8) > 0)
            {
                msgBuilder.AppendLine($"编号为{fbillno}的单据的{typeInfo}货人地址不能为空!");
            }
        }
    }
}
