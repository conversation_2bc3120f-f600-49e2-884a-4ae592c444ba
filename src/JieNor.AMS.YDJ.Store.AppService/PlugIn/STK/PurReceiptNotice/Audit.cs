using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PurReceiptNotice
{
    /// <summary>
    /// 采购收货通知单：审核
    /// </summary>
    [InjectService]
    [FormId("pur_receiptnotice")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);
        }
    }
}