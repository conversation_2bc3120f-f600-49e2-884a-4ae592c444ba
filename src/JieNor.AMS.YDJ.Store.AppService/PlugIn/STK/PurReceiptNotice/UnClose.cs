using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PurReceiptNotice
{
    /// <summary>
    /// 采购收货通知单：反关闭
    /// </summary>
    [InjectService]
    [FormId("pur_receiptnotice")]
    [OperationNo("unclose")]
    public class UnClose: AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                //行反关闭事件
                case "unCloseRowEvent":
                    unCloseRowEvent(e);
                    break;
            }
        }

        private void unCloseRowEvent(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null)
            {
                return;
            }
            var entries = eventData["entries"] as List<DynamicObject>;
            var entryKey = eventData["entryKey"] as string;
            var dataEntity = eventData["dataEntity"] as DynamicObject;

            if (false == string.Equals("fentity", entryKey, StringComparison.OrdinalIgnoreCase) || entries == null || entries.Count <= 0 || dataEntity == null)
            {
                return;
            }

            foreach(var entry in entries)
            {
                entry["fqty"] = Convert.ToDecimal(entry["fcloseqty"]) + Convert.ToDecimal(entry["fqty"]);
                entry["fcloseqty"] = 0;
            }

            var unitConvertService = this.Container.GetService<IUnitConvertService>();
            unitConvertService.ConvertByBasQty(this.Context, this.HtmlForm, new[] { dataEntity }, OperateOption.Create());
        }
    }
}
