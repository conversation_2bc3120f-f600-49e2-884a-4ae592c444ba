using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SchedulePlatform
{
    /// <summary>
    /// 发货排单平台：保存服务明细
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleplatform")]
    [OperationNo("SaveServiceEntity")]
    public class SaveServiceEntity: AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var billDatas = this.GetQueryOrSimpleParam<string>("billDatas");

            if (string.IsNullOrWhiteSpace(billDatas))
            {
                throw new BusinessException("billDatas不能为空");
            }

            var billArray = JArray.Parse(billDatas);

            if (billArray == null || billArray.Count <= 0)
            {
                return;
            }

            var billList = billArray.Where(x =>
              {
                  if (string.IsNullOrWhiteSpace((string)x["id"]))
                  {
                      return false;
                  }
                  var fserviceentity = x["fserviceentity"] as JArray;
                  if (fserviceentity == null || fserviceentity.Count <= 0)
                  {
                      return false;
                  }
                  return !fserviceentity.Any(y => string.IsNullOrWhiteSpace((string)y["fseritemid"]) ||
                                                string.IsNullOrWhiteSpace((string)y["funitid"]) ||
                                                ((decimal)y["fprice"]) > 0 ||
                                                ((decimal)y["fqty"]) > 0 ||
                                                string.IsNullOrWhiteSpace((string)x["fsourceformid_s"]) ||
                                                string.IsNullOrWhiteSpace((string)x["fsourcebillno_s"]) ||
                                                string.IsNullOrWhiteSpace((string)x["fsourceinterid_s"]));
              }).ToList();

            if (billList == null || billList.Count <= 0)
            {
                throw new BusinessException("请传送合法数据");
            }

            var billIds = billList.Select(x => (string)x["id"]).Distinct().ToList();
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "stk_scheduleplanbill");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var dataEntities = dm.Select(billIds).OfType<DynamicObject>().ToList();
            if (dataEntities == null || dataEntities.Count <= 0)
            {
                throw new BusinessException("没有找到相应的排单计划单");
            }

            var ids = dataEntities.Select(x => Convert.ToString(x["id"])).ToList();
            foreach(var billItem in billList)
            {
                var id = (string)billItem["id"];
                if (ids.Contains(id) == false)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"没有找到id为{0}的排单计划单");
                }
            }

            foreach(var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fbizstatus"]) != "1")
                {
                    string billNo = Convert.ToString(dataEntity["fbillno"]);
                    this.Result.ComplexMessage.ErrorMessages.Add($"编号为{billNo}的排单计划单尚未确认，请确认后再增加服务项目");
                }
            }

            if (this.Result.ComplexMessage.ErrorMessages.Count > 0)
            {
                return;
            }

            foreach(var billItem in billList)
            {
                var id = (string)billItem["id"];
                var entryObj = billItem["fserviceentity"] as JArray;
                var dataEntity = dataEntities.FirstOrDefault(x => Convert.ToString(x["id"]) == id);
                var entries = dataEntity["fserviceentity"] as DynamicObjectCollection;

                var groupEntries = entryObj.GroupBy(x => new
                {
                    fsourceformid = Convert.ToString(x["fsourceformid_s"]),
                    fsourceinterid = Convert.ToString(x["fsourceinterid_s"])
                }).ToList();

                //根据源单id和源单formid分组有多个，那么则认为是全保存
                if (groupEntries.Count > 1)
                {
                    entries.Clear();
                }
                else
                {
                    //如果只有一个分组，则认为是只操作该源单的服务项目
                    var firstItem = groupEntries.FirstOrDefault();
                    var fsourceformid = firstItem.Key.fsourceformid;
                    var fsourceinterid = firstItem.Key.fsourceinterid;

                    var removeItems = entries.Where(x => Convert.ToString(x["fsourceformid"]) == fsourceformid && Convert.ToString(x["fsourceinterid"]) == fsourceinterid).ToList();
                    if (removeItems != null && removeItems.Count > 0)
                    {
                        foreach(var removeItem in removeItems)
                        {
                            entries.Remove(removeItem);
                        }
                    }
                }

                foreach(var groupEntry in groupEntries)
                {
                    foreach(var entryItem in groupEntry)
                    {
                        var fprice = Convert.ToDecimal(entryItem["fprice"]);
                        var fqty = Convert.ToDecimal(entryItem["fqty"]);
                        var famount = fprice * fqty;
                        var entry = new DynamicObject(entries.DynamicCollectionItemPropertyType);
                        entry["fseritemid"] = Convert.ToString(entryItem["fseritemid"]);
                        entry["funitid"] = Convert.ToString(entryItem["funitid"]);
                        entry["fprice"] = fprice;
                        entry["fqty"] = fqty;
                        entry["famount"] = famount;
                        entry["fsourceformid"] = Convert.ToString(entryItem["fsourceformid_s"]);
                        entry["fsourcebillno"] = Convert.ToString(entryItem["fsourcebillno_s"]);
                        entry["fsourceinterid"] = Convert.ToString(entryItem["fsourceinterid_s"]);
                        entries.Add(entry);
                    }
                }
            }

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, htmlForm, dataEntities.ToArray(), OperateOption.Create());
            dm.Save(dataEntities);
        }
    }
}
