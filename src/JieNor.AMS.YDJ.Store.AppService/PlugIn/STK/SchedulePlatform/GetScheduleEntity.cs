using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework;
using System.Data;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SchedulePlatform
{
    /// <summary>
    /// 发货排单平台：获取已排单据明细
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleplatform")]
    [OperationNo("GetScheduleEntity")]
    public class GetScheduleEntity : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var billId = this.GetQueryOrSimpleParam<string>("billId");
            if (string.IsNullOrWhiteSpace(billId))
            {
                throw new BusinessException("billId不能为空");
            }
            string sql = @"
select sb.fid as r__id,sb.fbillno as r__fschedulebillno,fdate as r__fdate,sb.fstarttime as r__fstarttime,sb.fendtime as r__fendtime,sb.fschedulestaffid as r__fschedulestaffid,sb.fscheduledeptid as r__fscheduledeptid,
sb.fstockdate as r__fstockdate,sb.fstockdeptid as r__fstockdeptid,sb.fstockstaffid as r__fstockstaffid,sb.fcarid as r__fcarid,
sb.fstockdateto as r__fstockdateto,sb.fstockdeptidto as r__fstockdeptidto,sb.fstockstaffidto as r__fstockstaffidto
from t_stk_scheduleplanbill sb
where sb.fid=@fid;


select se.fentryid as r__fplanentryid,se.fsourceformid as r__fsourceformid,se.fsourcebillno as r__fsourcebillno,se.fsourceinterid as r__fsourceinterid,se.fsourceentryid as r__fsourceentryid,bt.fname as r__fsourcebilltypename,tenum.fenumitem as r__fsourcebiztypename,o.forderdate as r__fsourcebizdate,o.fdeliverydate as r__fplanbizdate,o.fcustomerid as r__fcustomerid,o.fphone as r__fphone,concat(concat(concat(penum.fenumitem,cenum.fenumitem),renum.fenumitem),o.faddress) as r__faddress,o.fdeptid as r__fdeptid,o.fstaffid as r__fstaffid,
oe.fentryid as r__id,oe.fproductid as r__fmaterialid,oe.fattrinfo as r__fattrinfo,oe.fcustomdes_e as r__fcustomdesc,oe.fmtono as r__fmtono,oe.funitid as r__funitid,(oe.fqty-oe.fscheduledqty) as r__fqty,oe.fbizunitid as r__fbizunitid,oe.fbizqty as r__fbizqty,se.fstorehouseidfrom as r__fstorehouseidfrom,se.fstorehouseidto as r__fstorehouseidto,
se.fstockdateto as r__fstockdateto,se.fstockdeptidto as r__fstockdeptidto,se.fstockstaffidto as r__fstockstaffidto,se.fstockdate as r__fstockdate,se.fstockdeptid as r__fstockdeptid,se.fstockstaffid as r__fstockstaffid,se.fscheduleqty as r__fscheduleqty,oe.fexdeliverydate as r__fexdeliverydate
from t_stk_schedulebillentry se
left join t_ydj_orderentry oe on se.fsourceinterid=oe.fid and se.fsourceentryid=oe.fentryid
left join t_ydj_order o on o.fid=se.fsourceinterid 
left join t_bd_billtype bt on o.fbilltype=bt.fid
left join T_BD_ENUMDATAENTRY tenum on tenum.fentryid=o.ftype
left join T_BD_ENUMDATAENTRY penum on penum.fentryid=o.fprovince
left join T_BD_ENUMDATAENTRY cenum on cenum.fentryid=o.fcity
left join T_BD_ENUMDATAENTRY renum on renum.fentryid=o.fregion
where se.fid=@fid and se.fsourceformid='ydj_order'
union all
select se.fentryid as r__fplanentryid,se.fsourceformid as r__fsourceformid,se.fsourcebillno as r__fsourcebillno,se.fsourceinterid as r__fsourceinterid,se.fsourceentryid as r__fsourceentryid,bt.fname as r__fsourcebilltypename,(case sa.fapplytype when N'0' then N'退货换货' when N'1' then N'退货退款' when N'2' then N'销售借货' when N'3' then '其它出库' when N'4' then N'库存调拨' when N'5' then N'其它服务' else '' end) as r__fsourcebiztypename,sa.fdate as r__fsourcebizdate,null as r__fplanbizdate,sa.fcustomerid as r__fcustomerid,sa.fcustphone as r__fphone,sa.faddress as r__faddress,sa.fapplydeptid as r__fdeptid,sa.fapplystaffid as r__fstaffid,
sae.fentryid as r__id,sae.fmaterialid as r__fmaterialid,sae.fattrinfo as r__fattrinfo,sae.fcustomdesc as r__fcustomdesc,sae.fmtono as r__fmtono,sae.funitid as r__funitid,sae.fqty as r__fqty,sae.fbizunitid as r__fbizunitid,sae.fbizqty as r__fbizqty,se.fstorehouseidfrom as r__fstorehouseidfrom,se.fstorehouseidto as r__fstorehouseidto,
se.fstockdateto as r__fstockdateto,se.fstockdeptidto as r__fstockdeptidto,se.fstockstaffidto as r__fstockstaffidto,se.fstockdate as r__fstockdate,se.fstockdeptid as r__fstockdeptid,se.fstockstaffid as r__fstockstaffid,se.fscheduleqty as r__fscheduleqty,null as r__fexdeliverydate
from t_stk_schedulebillentry se
left join t_stk_scheduleapplyentry sae on se.fsourceinterid=sae.fid and se.fsourceentryid=sae.fentryid
left join t_stk_scheduleapply sa on sa.fid=se.fsourceinterid 
left join t_bd_billtype bt on sa.fbilltype=bt.fid
where se.fid=@fid and se.fsourceformid='stk_scheduleapply';
";
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fid",System.Data.DbType.String,billId)
            };
            var dbSerivice = this.Container.GetService<IDBService>();

            DynamicObject dataEntity = null;
            using (var dataReader = dbSerivice.ExecuteReader(this.Context, sql, sqlParams))
            {
                dataEntity = readerDatas(dataReader);
                dataReader.NextResult();
                var fscheduleentity = dataEntity["fscheduleentity"] as DynamicObjectCollection;
                readerDatas(dataReader, fscheduleentity);
            }

            var unitConvertService = this.Container.GetService<IUnitConvertService>();
            unitConvertService.ConvertByBasQty(this.Context, this.HtmlForm, new[] { dataEntity }, Option);

            var loadReferenceObjectManager = this.Container.GetService<LoadReferenceObjectManager>();
            loadReferenceObjectManager.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntity, false);

            var dataConverter = this.Container.GetService<IUiDataConverter>();
            var srvData = dataConverter.CreateUIDataObject(this.Context, this.HtmlForm, dataEntity, Option).GetJsonValue<JObject>("uidata");

            this.Result.SrvData = srvData;
            this.Result.IsSuccess = true;
        }

        private DynamicObject readerDatas(IDataReader dataReader)
        {
            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            if (dataReader.Read())
            {
                return readerDatas(dataReader, dt);
            }
            return new DynamicObject(dt);
        }

        private void readerDatas(IDataReader dataReader, DynamicObjectCollection objectCollection)
        {
            var dt = objectCollection.DynamicCollectionItemPropertyType;
            while (dataReader.Read())
            {
                objectCollection.Add(readerDatas(dataReader, dt));
            }
        }

        private DynamicObject readerDatas(IDataReader dataReader, DynamicObjectType dt)
        {
            var result = new DynamicObject(dt);
            for (var i = 0; i < dataReader.FieldCount; i++)
            {
                var name = dataReader.GetName(i);
                if (name.StartsWith("r__") == false)
                {
                    continue;
                }
                name = name.Substring(3);
                var value = dataReader.GetValue(i);
                if (Convert.IsDBNull(value))
                {
                    value = null;
                }
                result[name] = value;
            }
            return result;
        }
    }
}
