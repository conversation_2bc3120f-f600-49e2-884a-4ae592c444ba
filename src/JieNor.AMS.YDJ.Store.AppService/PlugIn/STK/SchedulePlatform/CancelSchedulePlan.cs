using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SchedulePlatform
{
    /// <summary>
    /// 发货排单平台：取消排单计划
    /// </summary>
    [InjectService]
    [FormId("stk_scheduleplatform")]
    [OperationNo("CancelSchedulePlan")]
    public class CancelSchedulePlan : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = false;
            base.BeginOperationTransaction(e);
            var billIdString = this.GetQueryOrSimpleParam<string>("billIds");
            var billIds = billIdString?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            if (billIds == null || billIds.Length <= 0)
            {
                throw new BusinessException("billIds参数不能为为空");
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "stk_scheduleplanbill");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var dataEntities = dm.Select(billIds).OfType<DynamicObject>().ToArray();
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                throw new BusinessException("没有找到相应的排单计划单");
            }

            var ids = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fbizstatus"]) != "1")
                {
                    string billNo = Convert.ToString(dataEntity["fbillno"]);
                    this.Result.ComplexMessage.WarningMessages.Add($"编号为{billNo}的排单计划单不是已确认");
                }
                ids.Add(Convert.ToString(dataEntity["id"]));
            }

            foreach (var billId in billIds)
            {
                if (ids.Contains(billId) == false)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"id为{billId}的排单计划单没有找到或已被删除");
                }
            }

            if (Result.ComplexMessage.ErrorMessages.Count > 0)
            {
                return;
            }

            if (Result.ComplexMessage.WarningMessages.Count > 0)
            {
                dataEntities = dataEntities.Where(x => Convert.ToString(x["fbizstatus"]) == "1").ToArray();
            }

            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            metaModelService = this.Container.GetService<IMetaModelService>();
            var applyHtmlForm = metaModelService.LoadFormModel(this.Context, "stk_scheduleapply");
            var applyDM = this.Container.GetService<IDataManager>();
            applyDM.InitDbContext(this.Context, applyHtmlForm.GetDynamicObjectType(this.Context));
            foreach (var dataEntity in dataEntities)
            {
                cancelOperation(dataEntity, applyHtmlForm, applyDM, metaModelService);
            }

            if (this.Result.ComplexMessage.ErrorMessages.Count > 0)
            {
                throw new BusinessException("取消排程计划操作失败!");
            }

            //var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            //prepareSaveDataService.PrepareDataEntity(this.Context, htmlForm, dataEntities, OperateOption.Create());

            //dm.Save(dataEntities);
            //反审核计划单
            var option = new Dictionary<string, object>();
            var result = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, dataEntities, "unaudit", option);
            result.ThrowIfHasError(true, "计划单反审核失败!");
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 取消操作
        /// </summary>
        private void cancelOperation(DynamicObject dataEntity, HtmlForm applyHtmlForm, IDataManager applyDM, IMetaModelService metaModelService)
        {
            var fentities = dataEntity["fentity"] as DynamicObjectCollection;
            var formIdList = new List<string>();

            if (fentities != null && fentities.Count > 0)
            {
                //根据表单分组
                var formIds = fentities.Select(x =>
                {
                    var fsourceformid = Convert.ToString(x["fsourceformid"]);
                    var targetFormId = string.Empty;

                    switch (fsourceformid)
                    {
                        case "ydj_order":
                            targetFormId = "sal_deliverynotice";
                            break;
                        case "stk_scheduleapply":
                            var fsourceinterid = Convert.ToString(x["fsourceinterid"]);
                            var applyObject = applyDM.Select(fsourceinterid) as DynamicObject;
                            if (applyObject == null)
                            {
                                throw new BusinessException($"没有找到id为{fsourceinterid}的排单申请单");
                            }
                            var fapplytype = Convert.ToString(applyObject["fapplytype"]);
                            switch (fapplytype)
                            {
                                case "0":
                                case "1":
                                    targetFormId = "sal_returnnotice";
                                    break;
                                case "2":
                                case "4":
                                    targetFormId = "stk_inventorytransferreq";
                                    break;
                                case "3":
                                    targetFormId = "stk_otherstockoutreq";
                                    break;
                            }
                            break;
                    }

                    return targetFormId;
                }).Where(x => string.IsNullOrWhiteSpace(x) == false).Distinct();
                formIdList.AddRange(formIds);
            }

            var cancelResult = true;
            var billNo = Convert.ToString(dataEntity["fbillno"]);
            foreach(var formId in formIdList)
            {
                //根据排程编号获取下游单据
                var htmlForm = metaModelService.LoadFormModel(this.Context, formId);
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                var where = " fschedulebillno = @fschedulebillno";
                var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, new[] { new SqlParam("@fschedulebillno", System.Data.DbType.String, billNo) });
                var dbEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

                if (dbEntities == null || dbEntities.Count <= 0)
                {
                    continue;
                }

                //检查单据是否是创建、重新审核，不是则终止操作
                foreach(var dbEntity in dbEntities)
                {
                    var fstatus = Convert.ToString(dbEntity["fstatus"]);
                    if (string.Equals(fstatus, "D", StringComparison.OrdinalIgnoreCase) || string.Equals(fstatus, "E", StringComparison.OrdinalIgnoreCase))
                    {
                        cancelResult = false;
                        this.Result.ComplexMessage.ErrorMessages.Add($"编号[{billNo}]的排程取消失败!,原因是下游{htmlForm.Caption}[{Convert.ToString(dbEntity[htmlForm.NumberFldKey])}]是已提交或已审核状态");
                    }
                }

                if (cancelResult == false)
                {
                    return;
                }

                //调用删除操作删除下游单据，平台反写引擎自动修改反写数据，故这里不需要修改反写数据
                cancelResult = invokeBillOperation(formId, dbEntities, "delete", new Dictionary<string, object>(), $"编号[{billNo}]的排程取消失败!,原因是下游{htmlForm.Caption}删除失败!");
                if (cancelResult == false)
                {
                    return;
                }
            }

            if (cancelResult == false)
            {
                return;
            }

            //修改排程计划单的状态为计划状态
            dataEntity["fbizstatus"] = "0";
        }

        private bool invokeBillOperation(string formId, List<DynamicObject> dataEntities, string operationNo, Dictionary<string, object> option, string errorMsg)
        {
            var response = this.Gateway.InvokeBillOperation(this.Context, formId, dataEntities, operationNo, option);
            //检查删除结果
            if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
            {
                if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.AddRange(response.ComplexMessage.ErrorMessages);
                }
                if (string.IsNullOrWhiteSpace(errorMsg))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add(errorMsg);
                }
                return false;
            }
            return true;
        }
    }
}
