using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Microsoft.AspNet.SignalR.Hubs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("submitheadquart")]
    public class SubmitHeadQuart : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fstore", "fstockstaffid", "fmaterialid", "funitid" });
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            var agentItem = this.Context.LoadBizDataById("bas_agent", this.Context.Company);
            foreach (var bill in e.DataEntitys)
            {
                var entrys = bill["fentity"] as DynamicObjectCollection;
                if (entrys == null || entrys.Count == 0) continue;

                var warehouseMap = new Dictionary<string, string>(); // CRM仓库Id -> SAP仓库Id
                var sapWarehouseNameMap = new Dictionary<string, string>(); // SAP仓库Id -> SAP仓库名称

                // 获取表头门店
                var headStoreId = bill["fstore"]?.ToString();
                var headStoreNumber = Convert.ToString((bill["fstore_ref"] as DynamicObject)?["fnumber"]);

                if (headStoreId.IsNullOrEmptyOrWhiteSpace())
                {
                    var datas1 = entrys
                         .GroupBy(entry =>
                         {
                             //var crmWarehouse = entry["fstorehouseid_ref"] as DynamicObject;
                             //var crmWarehouseId = crmWarehouse?["id"]?.ToString();
                             //var sapWarehouseId = warehouseMap.ContainsKey(crmWarehouseId) ? warehouseMap[crmWarehouseId] : crmWarehouseId;
                             var material = entry["fmaterialid_ref"] as DynamicObject;
                             var materialNo = material?["fnumber"]?.ToString();
                             var unitid = entry["funitid_ref"] as DynamicObject;
                             var unitNo = unitid?["fnumber"]?.ToString();
                             return new { MaterialNo = materialNo, UnitNo = unitNo };
                         });
                    var datas = entrys
                         .GroupBy(entry =>
                         {
                             //var crmWarehouse = entry["fstorehouseid_ref"] as DynamicObject;
                             //var crmWarehouseId = crmWarehouse?["id"]?.ToString();
                             //var sapWarehouseId = warehouseMap.ContainsKey(crmWarehouseId) ? warehouseMap[crmWarehouseId] : crmWarehouseId;
                             var material = entry["fmaterialid_ref"] as DynamicObject;
                             var materialNo = material?["fnumber"]?.ToString();
                             var unitid = entry["funitid_ref"] as DynamicObject;
                             var unitNo = unitid?["fnumber"]?.ToString();
                             return new { MaterialNo = materialNo, UnitNo = unitNo };
                         })
                         .Select(g => new
                         {
                             //SapWarehouseId = g.Key.SapWarehouseId,
                             //SapWarehouseName = sapWarehouseNameMap.ContainsKey(g.Key.SapWarehouseId) ? sapWarehouseNameMap[g.Key.SapWarehouseId] : "",
                             billno = bill["fbillno"]?.ToString(),
                             saleorgid = agentItem["fnumber"]?.ToString(),
                             store = Convert.ToString((bill["fstore_ref"] as DynamicObject)?["fnumber"])?.ToString(),
                             date = Convert.ToDateTime(bill["fdate"]).ToString("yyyyMMddHHmmss"),
                             staffid = Convert.ToString((bill["fstockstaffid_ref"] as DynamicObject)?["fname"])?.ToString(),
                             description = bill["fdescription"]?.ToString(),
                             zytype = bill["fzytype"]?.ToString(),
                             MaterialNo = g.Key.MaterialNo,
                             UnitNo = g.Key.UnitNo,
                             Qty = g.Sum(x => Convert.ToDecimal(x["fbizqty"] ?? 0)),
                             PYQty = g.Sum(x => Convert.ToDecimal(x["fbizpyqty"] ?? 0)),
                             PKQty = g.Sum(x => Convert.ToDecimal(x["fbizpkqty"] ?? 0)),
                             ZYOnWayQty = g.Sum(x => Convert.ToDecimal(x["fzyonwayqty"] ?? 0)),
                             entrynote = g.Where(a => !Convert.ToString(a["fentrynote"]).IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fentrynote"])).ToList()
                         })
                         .ToList();
                    //var dtos= datas.Select(d => new Dictionary<string, object>
                    // {
                    //     ["item"] = d.entrynote.Select((note, idx) => new Dictionary<string, object>
                    //     {
                    //         ["zyComments"] = note,
                    //         ["zyIntrsQty"] = d.ZYOnWayQty,
                    //         ["zyItemLine"] = (idx + 1),
                    //         ["zyPrdCode"] = d.MaterialNo,
                    //         ["zyQtyBefore"] = d.Qty,
                    //         ["zyQtyVar"] = d.PYQty > 0 ? d.PYQty : -d.PKQty,//传盘亏/盘盈数;盘亏传负数，盘盈传正数
                    //         ["zyQtyAfter"] = (d.Qty - (d.PYQty > 0 ? d.PYQty : -d.PKQty)),
                    //         ["zyStockNum"] = d.billno,
                    //         ["zyStoreNum"] = d.store,
                    //         ["zyUnit"] = d.UnitNo
                    //     }).ToList(),
                    //     ["zyComments"] = d.description,
                    //     ["zyFactory"] = d.saleorgid,
                    //     ["zyInvDate"] = d.date,
                    //     ["zyInvName"] = d.staffid,
                    //     ["zyInvTime"] = d.date,
                    //     ["zyStockNum"] = d.billno,
                    //     ["zyStoreNum"] = d.store,
                    //     ["zyType"] = d.zytype
                    // }).ToList();
                    var pushDto = new Dictionary<string, object>
                    {
                        ["zyInvNum"] = bill["fbillno"]?.ToString(),
                        ["zyType"] = bill["fzytype"].Equals("1") ? "01" : "02",
                        ["zyFactory"] = agentItem["fnumber"]?.ToString(),
                        ["zyInvDate"] = Convert.ToDateTime(bill["fdate"]).ToString("yyyyMMddHHmmss"),
                        ["zyInvTime"] = Convert.ToDateTime(bill["fdate"]).ToString("yyyyMMddHHmmss"),
                        ["zyInvName"] = (bill["fstockstaffid_ref"] as DynamicObject)?["fname"]?.ToString(),
                        ["zyStockNum"] = bill["fbillno"]?.ToString(),
                        ["zyStoreNum"] = (bill["fstore_ref"] as DynamicObject)?["fnumber"]?.ToString(),
                        ["zyComments"] = bill["fdescription"]?.ToString(),
                        ["item"] = datas.Select((d, idx) => new Dictionary<string, object>
                        {
                            ["zyComments"] = d.entrynote,
                            ["zyIntrsQty"] = d.ZYOnWayQty.ToString(),
                            ["zyItemLine"] = (idx + 1).ToString(),
                            ["zyPrdCode"] = d.MaterialNo,
                            ["zyQtyBefore"] = d.PKQty.ToString(),
                            ["zyQtyVar"] = d.PYQty > 0 ? d.PYQty.ToString() : (-d.PKQty).ToString(),
                            ["zyQtyAfter"] = (d.Qty - (d.PYQty > 0 ? d.PYQty : -d.PKQty)).ToString(),
                            ["zyStockNum"] = d.billno,
                            ["zyStoreNum"] = d.store,
                            ["zyUnit"] = d.UnitNo
                        }).ToList()
                    };
                    MuSiApi.ZY_PushInventoryVerify(this.Context, this.HtmlForm, pushDto);

                }
                else
                {
                    // 过滤出仓库的 fmulstore 包含表头门店的明细
                    var filteredEntrys = entrys.Where(entry =>
                    {
                        var crmWarehouse = entry["fstorehouseid_ref"] as DynamicObject;
                        var fmulstore = crmWarehouse?["fmulstore"]?.ToString();
                        return !string.IsNullOrWhiteSpace(fmulstore) && fmulstore.Contains(headStoreId);
                    });

                    var datas = filteredEntrys
                        .GroupBy(entry =>
                        {
                            var material = entry["fmaterialid_ref"] as DynamicObject;
                            var materialNo = material?["fnumber"]?.ToString();
                            var unitid = entry["funitid_ref"] as DynamicObject;
                            var unitNo = unitid?["fnumber"]?.ToString();
                            return new { MaterialNo = materialNo, UnitNo = unitNo };
                        })
                        .Select(g => new
                        {
                            billno = bill["fbillno"]?.ToString(),
                            saleorgid = headStoreNumber, // 门店编码
                            store = headStoreNumber,
                            date = Convert.ToDateTime(bill["fdate"]).ToString("yyyyMMddHHmmss"),
                            staffid = Convert.ToString((bill["fstockstaffid_ref"] as DynamicObject)?["fname"])?.ToString(),
                            description = bill["fdescription"]?.ToString(),
                            zytype = bill["fzytype"]?.ToString(),
                            MaterialNo = g.Key.MaterialNo,
                            UnitNo = g.Key.UnitNo,
                            Qty = g.Sum(x => Convert.ToDecimal(x["fbizqty"] ?? 0)),
                            PYQty = g.Sum(x => Convert.ToDecimal(x["fbizpyqty"] ?? 0)),
                            PKQty = g.Sum(x => Convert.ToDecimal(x["fbizpkqty"] ?? 0)),
                            ZYOnWayQty = g.Sum(x => Convert.ToDecimal(x["fzyonwayqty"] ?? 0)),
                            entrynote = g.Where(a => !Convert.ToString(a["fentrynote"]).IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fentrynote"])).ToList()
                        })
                        .ToList();

                    var pushDto = new Dictionary<string, object>
                    {
                        ["zyInvNum"] = bill["fbillno"]?.ToString(),
                        ["zyType"] = bill["fzytype"].Equals("1") ? "01" : "02",
                        ["zyFactory"] = headStoreNumber, // 门店编码
                        ["zyInvDate"] = Convert.ToDateTime(bill["fdate"]).ToString("yyyyMMddHHmmss"),
                        ["zyInvTime"] = Convert.ToDateTime(bill["fdate"]).ToString("yyyyMMddHHmmss"),
                        ["zyInvName"] = (bill["fstockstaffid_ref"] as DynamicObject)?["fname"]?.ToString(),
                        ["zyStockNum"] = bill["fbillno"]?.ToString(),
                        ["zyStoreNum"] = headStoreNumber,
                        ["zyComments"] = bill["fdescription"]?.ToString(),
                        ["item"] = datas.Select((d, idx) => new Dictionary<string, object>
                        {
                            ["zyComments"] = d.entrynote,
                            ["zyIntrsQty"] = d.ZYOnWayQty.ToString(),
                            ["zyItemLine"] = (idx + 1).ToString(),
                            ["zyPrdCode"] = d.MaterialNo,
                            ["zyQtyBefore"] = d.PKQty.ToString(),
                            ["zyQtyVar"] = d.PYQty > 0 ? d.PYQty.ToString() : (-d.PKQty).ToString(),
                            ["zyQtyAfter"] = (d.Qty - (d.PYQty > 0 ? d.PYQty : -d.PKQty)).ToString(),
                            ["zyStockNum"] = d.billno,
                            ["zyStoreNum"] = d.store,
                            ["zyUnit"] = d.UnitNo
                        }).ToList()
                    };

                    MuSiApi.ZY_PushInventoryVerify(this.Context, this.HtmlForm, pushDto);
                }

                // 预处理：构建仓库映射
                //foreach (var entry in entrys)
                //{
                //    var crmWarehouse = entry["fstorehouseid_ref"] as DynamicObject;
                //    var crmWarehouseId = crmWarehouse?["id"]?.ToString();
                //    var orgId = bill["fmainorgid"]?.ToString();
                //    var storeId = crmWarehouse["fmulstore"]?.ToString(); // 明细仓库的门店

                //    if (!string.IsNullOrWhiteSpace(crmWarehouseId) && !warehouseMap.ContainsKey(crmWarehouseId))
                //    {
                //        var sapWarehouseId = GetSapWarehouseIdByCrmWarehouse(crmWarehouse, orgId, storeId);
                //        warehouseMap[crmWarehouseId] = sapWarehouseId;
                //        sapWarehouseNameMap[sapWarehouseId] = crmWarehouse?["fname"]?.ToString();
                //    }
                //}

                //// 过滤明细
                //IEnumerable<DynamicObject> filteredEntrys = entrys;
                //if (!string.IsNullOrWhiteSpace(headStoreId))
                //{
                //    // 只保留仓库门店包含表头门店的明细
                //    filteredEntrys = entrys.Where(entry =>
                //    {
                //        var entryStoreId = entry["fstore"]?.ToString();
                //        // 包含关系可根据实际业务调整
                //        return !string.IsNullOrWhiteSpace(entryStoreId) && entryStoreId.Contains(headStoreId);
                //    });
                //}

                //// 按商品编码汇总数量
                //var summary = filteredEntrys
                //    .Where(entry => entry["fmaterialid_ref"] != null && entry["fstorehouseid_ref"] != null)
                //    .GroupBy(entry =>
                //    {
                //        var crmWarehouse = entry["fstorehouseid_ref"] as DynamicObject;
                //        var crmWarehouseId = crmWarehouse?["id"]?.ToString();
                //        var sapWarehouseId = warehouseMap.ContainsKey(crmWarehouseId) ? warehouseMap[crmWarehouseId] : crmWarehouseId;
                //        var material = entry["fmaterialid_ref"] as DynamicObject;
                //        var materialNo = material?["fnumber"]?.ToString();
                //        return new { SapWarehouseId = sapWarehouseId, MaterialNo = materialNo };
                //    })
                //    .Select(g => new
                //    {
                //        SapWarehouseId = g.Key.SapWarehouseId,
                //        SapWarehouseName = sapWarehouseNameMap.ContainsKey(g.Key.SapWarehouseId) ? sapWarehouseNameMap[g.Key.SapWarehouseId] : "",
                //        MaterialNo = g.Key.MaterialNo,
                //        Qty = g.Sum(x => Convert.ToDecimal(x["fbizqty"] ?? 0))
                //    })
                //    .ToList();

                //var pushDto = new
                //{
                //    BillNo = bill["fbillno"]?.ToString(),
                //    OrgId = bill["fmainorgid"]?.ToString(),
                //    StoreId = bill["fstoreid"]?.ToString(),
                //    Details = summary.Select(s => new
                //    {
                //        WarehouseId = s.SapWarehouseId,
                //        WarehouseName = s.SapWarehouseName,
                //        MaterialNo = s.MaterialNo,
                //        Qty = s.Qty
                //    }).ToList()
                //};

                //// MuSiApi.PushInventoryVerify(this.Context, pushDto);

                //bill["fsubmithtime"] = DateTime.Now;
                //bill["fchstatus"] = "1";
            }
        }
    }
}