using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Flow;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    /// <summary>
    /// 盘点单:审核
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("auditflow")]
    public class AuditFlow : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            // 创建流程节点信息服务
            var flowNodeInfoService = new FlowNodeInfoService(this.Context);

            var fmainorgids = e.DataEntitys.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
            var agentInfo = GetAgentInfo(this.Context, fmainorgids);

            foreach (var dataEntity in e.DataEntitys)
            {
                // 获取当前审批流节点信息
                var flowNodeInfo = flowNodeInfoService.GetCurrentFlowNodeInfo(dataEntity);

                //创建单据的经销商的经销类型-直营
                var fmainorgid = Convert.ToString(dataEntity["fmainorgid"]);
                var isfmainorgid = agentInfo.GetValue(fmainorgid);

                //完成最后一个业务节点审批,(系统需要触发外部接口调用逻辑，传收款单到中台。
                if (flowNodeInfo.IsStateComplete && isfmainorgid )
                {
                    string syncMessage = "";
                    try
                    {
                        var syncResult = this.Gateway.InvokeBillOperation(this.Context, "stk_inventoryverify", e.DataEntitys, "submitheadquart",
                            new Dictionary<string, object>
                            {
                        { "IgnoreCheckPermssion", true }, { "IgnoreValidateDataEntities", true }
                            });

                        if (syncResult != null && syncResult.IsSuccess)
                        {
                            syncMessage = "数据已提交总部";
                        }
                        else
                        {
                            syncMessage = $"数据提交总部失败：{syncResult?.SimpleMessage ?? "未知错误"}";
                        }
                    }
                    catch (Exception ex)
                    {
                        syncMessage = $"数据提交总部异常：{ex.Message}";
                    }

                    // 无论同步成功还是失败，都抛出异常拦截当前审核单据的逻辑
                    //throw new BusinessException($"{syncMessage}");
                }
            }
        }

        /// <summary>
        /// 获取当前经销商相关字段信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private Dictionary<string, bool> GetAgentInfo(UserContext userCtx, List<string> fmainorgids)
        {
            var dic = new Dictionary<string, bool>();
            if (fmainorgids.Count == 0) return dic;

            var agentInfo = userCtx.LoadBizBillHeadDataById("bas_agent", fmainorgids, "fid,fmainorgid,fname,fnumber,actualownernumber,fmanagemodel").ToList();

            foreach (var item in agentInfo)
            {
                if (!dic.ContainsKey(Convert.ToString(item["fid"])))
                {
                    var fmanagemodel = Convert.ToString(item["fmanagemodel"]) == "1" ? true : false;
                    dic.Add(Convert.ToString(item["fid"]), fmanagemodel);
                }
            }

            return dic;
        }
    }
}
