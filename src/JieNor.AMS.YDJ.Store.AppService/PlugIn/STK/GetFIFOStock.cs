using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.SAL.SaleIntention.PushSaleIntention;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK
{
    /// <summary>
    /// 获取先进先出库位
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout|stk_sostockout|stk_inventorytransfer")]
    [OperationNo("getfifostock")]
    public class GetFIFOStock : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            //是否启用条码管理
            var profileService = this.Container.GetService<ISystemProfile>();
            var fisfifostock = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fisfifostock", false);

            if (!fisfifostock)
            {
                this.Result.IsSuccess = true;
                return;
            }

            var stockBill = GetStockBillData();

            var flag = this.Option.HasInteractionFlag("fifostock");

            this.Container.GetService<IAuxPropService>().FillFlexAuxPropPkValue(this.Context, this.HtmlForm, new[] { stockBill }, OperateOption.Create());

            var inventoryService = this.Container.GetService<IInventoryService>();
            inventoryService.FillFIFOStockInfos(this.Context, this.HtmlForm, new[] { stockBill }, !flag);

            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            refMgr.Load(this.Context, dt, stockBill, false);

            var entityMeta = this.HtmlForm.GetEntity("fentity");
            var fentity = stockBill[entityMeta.PropertyName] as DynamicObjectCollection;
            var uiDataConverter = this.Container.GetService<IUiDataConverter>();

            var entityUiData = new JArray();
            foreach (var entryData in fentity)
            {
                entityUiData.Add(uiDataConverter.PackageEntityData(this.Context, this.HtmlForm, entityMeta, entryData));
            }

            this.Result.SrvData = new
            {
                //rowDatas = fentity.Select(s => new
                //{
                //    fmaterialid = Convert.ToString(s["fmaterialid"]),
                //    fattrinfo = Convert.ToString(s["fattrinfo"]),
                //    fcustomdesc = Convert.ToString(s["fcustomdesc"]),
                //    fownertype = Convert.ToString(s["fownertype"]),
                //    fownerid = Convert.ToString(s["fownerid"]),
                //    funitid = Convert.ToString(s["funitid"]),
                //    fstockunitid = Convert.ToString(s["fstockunitid"]),
                //    fmtono = Convert.ToString(s["fmtono"]),
                //    flotno = Convert.ToString(s["flotno"]),
                //    fqty = Convert.ToDecimal(s["fqty"]),
                //    fstockqty = Convert.ToDecimal(s["fstockqty"]),
                //    fstorehouseid = Convert.ToString(s["fstorehouseid"]),
                //    fstorelocationid = Convert.ToString(s["fstorelocationid"]),
                //    fstockstatus = Convert.ToString(s["fstockstatus"]),
                //}),
                rowDatas = entityUiData,
                rowId = this.GetQueryOrSimpleParam<string>("rowId")
            };
        }

        /// <summary>
        /// 获取库存单数据包
        /// </summary>
        /// <returns></returns>
        private DynamicObject GetStockBillData()
        {
            var rowData = this.GetQueryOrSimpleParam<string>("rowData");
            if (rowData.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数错误，rowData不能为空，请检查！");
            }

            var uiData = "[{\"fentity\": [" + rowData + "]}]";

            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            var billItems = JArray.Parse(uiData);
            List<DynamicObject> lstDataEntities = new List<DynamicObject>();
            var dcSerializer = this.Container.GetService<IDynamicSerializer>();
            dcSerializer.Sync(dt, lstDataEntities, billItems, (propKey) =>
            {
                var el = this.HtmlForm?.GetElement(propKey);
                if (el is HtmlField) return (el as HtmlField).DynamicProperty;
                if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

                return null;
            },
            null,
            null,
            null);

            return lstDataEntities.First();
        }
    }
}