using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockIn
{
    /// <summary>
    /// 其他入库单单：撤销
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockin")]
    [OperationNo("UnSubmit")]
    public class UnSubmit: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var sql = string.Format($@"/*dialect*/SELECT a.fid,a.fbillno
                FROM T_BCM_RECEPTIONSCANTASK a WITH(NOLOCK)
                LEFT JOIN T_BCM_RESCANTASKENTITY b WITH(NOLOCK) ON b.fid=a.fid
                WHERE a.ftaskstatus <>'ftaskstatus_04' AND a.fmainorgid = '{this.Context.Company}'
	                AND b.fsourceformid='stk_otherstockin' AND b.fsourcebillno = '{Convert.ToString(newData["fbillno"])}'");
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                return data == null || !data.Any();
            }).WithMessage("当前其他入库单【{0}】已经生成了收货扫描任务，请您删除后再进行撤销操作！", (newData, oldData) => newData["fbillno"]));
        }
    }
}
