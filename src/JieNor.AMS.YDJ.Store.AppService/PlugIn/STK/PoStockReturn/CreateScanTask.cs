using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockReturn
{
    /// <summary>
    /// 采购退货单创建发货扫描任务
    /// </summary>
    [InjectService]
    [FormId("stk_postockreturn")]
    [OperationNo("createscantask")]
    public class CreateScanTask: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fstatus"]) != "D" || Convert.ToString(newData["fcancelstatus"]) == "1")
                {
                    errorMsg = $"采购退货单【{Convert.ToString(newData["fbillno"])}】必须是提交状态且未作废才能产生发货扫描任务!";
                    return false;
                }

                //判断采购退货单是否存在发货扫描任务
                var sqltext = string.Format($@"/*dialect*/SELECT a.fid,a.fbillno
                FROM T_BCM_DELIVERYSCANTASK a WITH(NOLOCK)
                LEFT JOIN T_BCM_DESCANTASKENTITY b WITH(NOLOCK) ON b.fid=a.fid
                WHERE  a.fmainorgid = '{this.Context.Company}'
	                AND b.flinkformid='stk_postockreturn' AND b.flinkbillno = '{Convert.ToString(newData["fbillno"])}'");

                var exsit2 = this.DBService.ExecuteDynamicObject(this.Context, sqltext);
                if (exsit2 != null && exsit2.Count > 0)
                {
                    errorMsg = $"当前订单已经存在对应的发货任务【{Convert.ToString(exsit2.FirstOrDefault()["fbillno"])}】, 不允许重复生成！";
                    return false;
                }
                return true;

            }).WithMessage("{0}", (billObj, propObj) => errorMsg));
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys.Length == 0 || e.DataEntitys == null) return;

            string ruleId = "stk_postockreturn2bcm_deliveryscantask";//采购退货单生成发货扫描任务
            string targetFormId = "bcm_deliveryscantask";//其他入库单
            var fEntityKey = "fentity";//源单明细标识

            var targetForm = this.MetaModelService.LoadFormModel(this.Context, targetFormId);
            var pushSelectedRows = e.DataEntitys.Where(x =>
            {
                var fentries = x[fEntityKey] as DynamicObjectCollection;
                if (fentries == null || fentries.Count <= 0)
                {
                    return false;
                }
                return fentries.Any();
            }).SelectMany(x =>
            {
                var id = Convert.ToString(x["id"]);
                var fentries = x[fEntityKey] as DynamicObjectCollection;
                return fentries.Select(y => new SelectedRow
                {
                    PkValue = id,
                    EntityKey = fEntityKey,
                    EntryPkValue = Convert.ToString(y["id"])
                });
            }).ToList();

            if (pushSelectedRows == null || pushSelectedRows.Count <= 0)
            {
                return;
            }

            var convertService = this.Container.GetService<IConvertService>();
            var pushResult = convertService.Push(this.Context, new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = this.HtmlForm.Id,
                TargetFormId = targetFormId,
                SelectedRows = pushSelectedRows.ToConvertSelectedRows(),
                Option = OperateOption.Create()
            });

            var convertResult = pushResult.SrvData as ConvertResult;
            var targetDatas = convertResult.TargetDataObjects?.ToList();
            if (targetDatas == null || targetDatas.Count <= 0)
            {
                this.Result.ComplexMessage.ErrorMessages.Add($"下推{targetForm.Caption}失败!");
                return;
            }
            var response = this.Gateway.InvokeBillOperation(this.Context, targetFormId, targetDatas, "save", new Dictionary<string, object>());
            if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
            {
                if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
                {
                    this.Result.ComplexMessage.ErrorMessages.AddRange(response.ComplexMessage.ErrorMessages);
                }
                return;
            }
        }
    }
}
