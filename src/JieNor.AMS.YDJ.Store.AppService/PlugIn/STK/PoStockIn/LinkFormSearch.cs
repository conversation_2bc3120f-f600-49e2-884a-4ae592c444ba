using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Core.Interface; 
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.SaleIntention;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PoStockIn
{
   /// <summary>
    /// 联查
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : LinkFormSearchBase
    {
        //protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        //{
        //    var pkIds = dataEntities.Select(o => o["id"]?.ToString ()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
        //    if (pkIds.Count <= 0) return;

        //    ReceptionScanTaskBillAdd();
        //}

        ///// <summary>
        ///// 添加收货扫描任务
        ///// </summary>
        //private void ReceptionScanTaskBillAdd(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        //{

        //    var fsourceTypes = dataEntities.Select(o => o["fsourcetype"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
        //    var fsourceNumbers = dataEntities.Select(o => o["fsourcenumber"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();

        //    var strSql = $@"select distinct fsourceinterid from t_bcm_rescantaskentity where fid ='{id[0]}'";
        //    List<string> fids = new List<string>();
        //    using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
        //    {
        //        while (dr.Read())
        //        {
        //            fids.Add(dr["fsourceinterid"].ToString());
        //        }
        //    }


        //    //预留单
        //    var metaModelService = userContext.Container.GetService<IMetaModelService>();
        //    var reserveForm = metaModelService.LoadFormModel(this.Context, "bcm_receptionscantask");
        //    var filterStr = fsourceTypes.Count == 1 ? $"fsourcetype='{fsourceTypes[0]}' and fsourcepkid='{fsourceNumbers[0]}'" :
        //                                   $"fsourcetype='stk_sostockout' and fsourcepkid in ({string.Join(",", pkIds.Select(x => $"'{x}'"))})";
        //    linkFormDatas.Add(new Dictionary<string, object>
        //    {
        //        { "formId", reserveForm.Id },
        //        { "formCaption", reserveForm.Caption },
        //        { "flag", "nextForm" },
        //        { "filterString", filterStr },
        //    });
        //}


        /// <summary>
        /// 添加收货扫描任务
        /// </summary>
        protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            //收货扫描任务
            var metaModelService = userContext.Container.GetService<IMetaModelService>();

            var fSourceTypes = FSourceTypes(dataEntities); 
            var fSourceNumbers = FSourceNumbers(dataEntities);

            IEnumerable<string> fids = new List<string>();
            if (fSourceTypes.Count() > 0 && fSourceNumbers.Count() > 0)
            {
                var strSql = $"select distinct rc.fid FROM T_BCM_RESCANTASKENTITY AS rc JOIN T_BCM_RECEPTIONSCANTASK AS re ON re.fid = rc.fid WHERE rc.fsourceformid IN ({ToINSql(fSourceTypes)}) AND rc.fsourcebillno IN ({ToINSql(fSourceNumbers)}) AND re.fmainorgid = '{Context.Company}'";
                fids = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { }).Select(item => Convert.ToString(item["fid"]) ?? "");
            }
                // var orgin = Context.IsTopOrg ? Context.Company : Context.TopCompanyId;
             
            var receptionForm = metaModelService.LoadFormModel(this.Context, "bcm_receptionscantask");
            string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", receptionForm.Id },
                { "formCaption", receptionForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr }
            });
            //联查销售合同
            LinkOrder(dataEntities, metaModelService, linkFormDatas);
        }

        private IEnumerable<string> FSourceTypes(DynamicObject[] dataEntities)
        {
            var _list = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                _list.Add(Convert.ToString(dataEntity["fsourcetype"]));
                var entryObjs = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entryObj in entryObjs)
                {
                    _list.Add(Convert.ToString(entryObj["fsourceformid"]));
                }
            }
            return _list.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
        }
        private IEnumerable<string> FSourceNumbers(DynamicObject[] dataEntities)
        {
            var _list = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                _list.Add(Convert.ToString(dataEntity["fsourcenumber"]));
                var entryObjs = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entryObj in entryObjs)
                {
                    _list.Add(Convert.ToString(entryObj["fsourcebillno"]));
                }
            }
            return _list.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
        }


        /// <summary>
        /// 将集合转为 sql IN查询语句
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private string ToINSql(IEnumerable<string> list)
        {
            return string.Join(",", list.Select(item => $"'{item}'"));
        }

        /// <summary>
        /// 联查销售合同
        /// </summary>
        private void LinkOrder(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            if (!this.Context.IsDirectSale) return;
            var id = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            var strSql = $@"select distinct fsourceinterid from t_stk_postockinentry where fid ='{id[0]}' AND fsourceformid='ydj_order'";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fsourceinterid"].ToString());
                }
            }
            if (fids.Count == 0) return;

            //找到合同下标用于修改
            var purFlag = -1;
            for (int i = 0; i < linkFormDatas.Count; i++)
            {
                foreach (var val in linkFormDatas[i])
                {
                    if (val.Value.ToString() == "ydj_order")
                    {
                        purFlag = i;
                        goto ModifyData;
                    }
                }
            }
        ModifyData:
            var purchaseorder = metaModelService.LoadFormModel(this.Context, "ydj_order");
            string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));
            if (purFlag < 0)
            {
                linkFormDatas.Add(new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "preForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }
            else
            {
                linkFormDatas[purFlag] = (new Dictionary<string, object>
                {
                    { "formId", purchaseorder.Id },
                    { "formCaption", purchaseorder.Caption },
                    { "flag", "preForm" },
                    { "filterString", filterStr },
                    { "visible",1}
                });
            }
        }

    }
}