using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PoStockIn
{
    /// <summary>
    /// 采购入库单：反审核
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("UnAudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !Convert.ToBoolean(newData["fpdainstock"]);
            }).WithMessage("编号为【{0}】的采购入库单为PDA扫码入库，不允许反审核！", (billObj, propObj) => billObj["fbillno"]));
        }
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            BackWriteSourceBill(e.DataEntitys);

            // 反写销售合同【已采购入库数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackPurInQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);

            if (this.Context.IsDirectSale)
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                //是否启用条码管理                    
                var fenablebarcode = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fenablebarcode", false);
                //总部发货后自动生成入库单
                var fautostockinorder = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fautostockinorder", false);
                if (fenablebarcode && fautostockinorder)
                {
                    // 反写收货扫描任务【已作业数量】
                    Core.Helpers.OrderQtyWriteBackHelper.WriteBackRecTaskWorkQty(
                        this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
                }
            }

            AutoUpdateReserve(e.DataEntitys);
        }

        /// <summary>
        /// 更新对应的销售合同的预留信息
        /// </summary>
        private void AutoUpdateReserve(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var reserveService = this.Container.GetService<IReserveUpdateService>();
            var result = reserveService.UpdatePOInStockReserve(this.Context, this.HtmlForm, dataEntities, this.Option);

            this.Result.MergeResult(result);
        }

        /// <summary>
        /// 反写源采购订单
        /// </summary>
        private void BackWriteSourceBill(DynamicObject[] dataEntitys)
        {
            // 源采购订单ID集合
            var purOrderIds = dataEntitys
                .SelectMany(o => o["fentity"] as DynamicObjectCollection)
                .Select(o => Convert.ToString(o["fpoorderinterid"]))
                .Distinct()
                .ToList();

            if (!purOrderIds.Any()) return;

            // 批量加载源采购订单
            var purOrders = this.Context.LoadBizDataById("ydj_purchaseorder", purOrderIds);
            if (!purOrders.Any()) return;

            var purOrderEntrys = purOrders
                .SelectMany(o => o["fentity"] as DynamicObjectCollection)
                .ToList();

            List<DynamicObject> productlist = new List<DynamicObject>();
            foreach (var pur in purOrderEntrys)
            {
                if (!pur["fsuitcombnumber"].IsNullOrEmptyOrWhiteSpace())
                {
                    var product = this.Context.LoadBizDataById("ydj_product", pur["fmaterialid"].ToString(), true);
                    string str = product["fsuiteflag"].ToString();
                    if (product["fsuiteflag"].ToString().ToLower() == "true")
                    {
                        productlist.Add(product);
                    }
                }
            }

            foreach (var purOrder in purOrders)
            {
                // 计算源采购订单关闭状态字段值
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatusWhitfissuitflag(purOrder, productlist);
            }

            this.Context.SaveBizData("ydj_purchaseorder", purOrders);
        }
    }
}