using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockIn
{
    /// <summary>
    /// 采购入库单：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("QuerySelector")]
    public class QuerySelector : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            // 只限于采购订单
            var targetFormId = this.GetQueryOrSimpleParam("targetFormId", "");
            if (!targetFormId.EqualsIgnoreCase("ydj_purchaseorder")) return;

            /*
            1、当采购入库单.供应商不为空时
            a、点<选单>，进入《采购订单-列表》，需要自动匹配筛选 “ 采购订单的【供应商】+【采购员】+【采购部门】（特殊处理：若采购员或采购部门为空，则不参与过滤）等于采购入库单所选择的当前供应商、采购员、采购部门 且 采购订单商品明细行的【采购数量】大于 源单采购订单商品明细已关联采购入库单.入库存明细的【实收数量】汇总 ” 这一类采购入库单数据范围供选择；
            b、当采购入库单重新选择供应商，需要直接清空原已选择的所有源单信息；
            4、当采购入库单.供应商为空时，点<选单>，进入《采购订单-列表》，需要自动匹配筛选 “ 采购订单商品明细行的【采购数量】大于 源单采购订单商品明细已关联采购入库单.入库存明细的【实收数量】汇总 ” 这一类采购入库单数据范围供选择。
            任务链接：http://dmp.jienor.com:81/zentao/task-view-29341.html
             */
            var fsupplierid = this.GetQueryOrSimpleParam("fsupplierid", "");
            var fpostaffid = this.GetQueryOrSimpleParam("fpostaffid", "");
            var fpodeptid = this.GetQueryOrSimpleParam("fpodeptid", "");

            string supplierFilter = string.Empty;
            if (!fsupplierid.IsNullOrEmptyOrWhiteSpace())
            {
                supplierFilter += $"  and po.fsupplierid='{fsupplierid}' ";

                //if (!fpodeptid.IsNullOrEmptyOrWhiteSpace())
                //{
                //    supplierFilter += $" and po.fpodeptid = '{fpodeptid}' ";
                //}

                //if (!fpostaffid.IsNullOrEmptyOrWhiteSpace())
                //{
                //    supplierFilter += $" and po.fpostaffid = '{fpostaffid}' ";
                //}
            }

            string filterString = $@" 
fid in 
(
    select poe.fid from t_ydj_purchaseorder po 
    inner join t_ydj_poorderentry poe on po.fid=poe.fid and po.fcancelstatus='0' {supplierFilter}
    left join (
	    select psie.fpoorderinterid,psie.fpoorderentryid,SUM(fbizqty) as fbizqty from t_stk_postockin psi 
	    inner join t_stk_postockinentry psie on psi.fid=psie.fid
	    where psi.fcancelstatus='0'
	    group by psie.fpoorderinterid,psie.fpoorderentryid
    ) psie on poe.fid=psie.fpoorderinterid and poe.fentryid=psie.fpoorderentryid
    where poe.fbizqty+poe.fbizreturnqty > ISNULL(psie.fbizqty,0)
)";

            this.SimpleData["filterString"] = filterString;

        }
    }
}
