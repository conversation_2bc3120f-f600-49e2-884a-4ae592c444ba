using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.Log;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PoStockIn
{
    /// <summary>
    /// 采购入库单：审核
    /// </summary>
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string errMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entrys = newData["fentity"] as DynamicObjectCollection;
                var purOrderIds = entrys.Select(t => Convert.ToString(t["fpoorderinterid"])).ToList();
                var allPurOrders = this.Context.LoadBizDataById("ydj_purchaseorder", purOrderIds);
                var noAuditSources = allPurOrders.Where(t => Convert.ToString(t["fstatus"]) != "E").ToList();
                if (noAuditSources != null && noAuditSources.Any())
                {
                    errMsg = $"上游采购订单【{string.Join("、", noAuditSources.Select(t => Convert.ToString(t["fbillno"])).ToList())}】未审核，不允许审核！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            // DateTime.Now.ToString("yyyy-mm-DD");
            var nowDate = DateTime.Now;
            foreach (var dataEntity in e.DataEntitys)
            {
                //task 70392,【采购入库单】以当前日期更新至【入库日期】
                dataEntity["fdate"] = nowDate;
            }
        }

        /// <summary>
        /// 执行操作事务后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            // 源采购订单ID集合
            var purOrderIds = e.DataEntitys
                .SelectMany(o => o["fentity"] as DynamicObjectCollection)
                .Select(o => Convert.ToString(o["fpoorderinterid"]))
                .Distinct()
                .ToList();

            if (!purOrderIds.Any()) return;

            // 批量加载源采购订单
            var purOrders = this.Context.LoadBizDataById("ydj_purchaseorder", purOrderIds, true);
            if (!purOrders.Any()) return;
            var purOrderEntrys = purOrders
                .SelectMany(o => o["fentity"] as DynamicObjectCollection)
                .ToList();
            if (purOrderEntrys == null || !purOrderEntrys.Any()) return;

            var metamodelService = Context.Container.GetService<IMetaModelService>();
            var refmg = Context.Container.GetService<LoadReferenceObjectManager>();
            var productFrom = metamodelService.LoadFormModel(Context, "ydj_purchaseorder");
            refmg.Load(Context, productFrom.GetDynamicObjectType(Context), purOrderIds, false);

            List<DynamicObject> productlist = new List<DynamicObject>();
            foreach (var pur in purOrderEntrys)
            {
                if (!pur["fsuitcombnumber"].IsNullOrEmptyOrWhiteSpace())
                {
                    var product = this.Context.LoadBizDataById("ydj_product", pur["fmaterialid"].ToString(), true);
                    string str = product["fsuiteflag"].ToString();
                    if (product["fsuiteflag"].ToString().ToLower() == "true")
                    {
                        productlist.Add(product);
                    }
                }
            }

            // 源采购订单的源销售合同
            var orderIds = purOrderEntrys
                .Where(o => !o["fsoorderinterid"].IsNullOrEmptyOrWhiteSpace())
                .Select(o => Convert.ToString(o["fsoorderinterid"]))
                .Distinct()
                .ToList();
            var orders = new List<DynamicObject>();
            if (orderIds.Any())
            {
                orders = this.Context.LoadBizDataById("ydj_order", orderIds);
            }

            foreach (var dataEntity in e.DataEntitys)
            {
                //生成源单日志
                this.BuildOrderLog(dataEntity);

                // 当前入库单商品明细
                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                var groups = entrys?.GroupBy(x => new
                {
                    fpoorderinterid = Convert.ToString(x["fpoorderinterid"]),
                    fsourceformid = Convert.ToString(x["fsourceformid"])
                });
                if (groups == null || !groups.Any()) continue;

                foreach (var group in groups)
                {
                    if (!group.Key.fsourceformid.EqualsIgnoreCase("ydj_purchaseorder")) continue;

                    var purOrder = purOrders.FirstOrDefault(t => Convert.ToString(t["id"]).EqualsIgnoreCase(group.Key.fpoorderinterid));
                    if (purOrder == null) continue;

                    // 计算源采购订单关闭状态字段值
                    Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatusWhitfissuitflag(purOrder, productlist);

                    // 源采购订单商品明细
                    var _purOrderEntrys = purOrder["fentity"] as DynamicObjectCollection;
                    foreach (var _purOrderEntry in _purOrderEntrys)
                    {
                        // 源采购订单的源销售合同
                        var order = orders.FirstOrDefault(o =>
                            Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(_purOrderEntry["fsoorderinterid"])));
                        if (order == null) continue;

                        if (!Convert.ToString(_purOrderEntry["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder_bc")
                            && !Convert.ToString(_purOrderEntry["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder_bh"))
                        {
                            // 当前入库单商品明细行
                            var entry = entrys
                                .Where(o => Convert.ToString(o["fsourceentryid"]).EqualsIgnoreCase(Convert.ToString(_purOrderEntry["id"])))
                                .FirstOrDefault();
                            if (entry == null) continue;

                            // 源销售合同商品明细
                            var orderEntrys = order["fentry"] as DynamicObjectCollection;

                            if (!orderEntrys.Any(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(_purOrderEntry["fsourceentryid_e"]))))
                            {
                                var productName = (_purOrderEntry["fmaterialid_ref"] as DynamicObject)?["fname"] ?? "";

                                this.Result.ComplexMessage.WarningMessages.Add(
                                    $"因存在销售合同已变更导致采购订单与销售合同取消关联关系，【{dataEntity["fbillno"]}】第{entry["fseq"]}行入库商品【{productName}】没有自动预留，如需预留，请手工预留！");
                            }
                        }
                    }
                }
            }

            this.Context.SaveBizData("ydj_purchaseorder", purOrders);
        }

        /// <summary>
        /// 生成源单日志
        /// </summary>
        /// <param name="dataEntity"></param>
        private void BuildOrderLog(DynamicObject dataEntity)
        {
            //修改为从明细中获取数据
            //var sourceType = Convert.ToString(dataEntity["fsourcetype"]);
            //var sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
            if (dataEntity == null) return;
            var entitys = dataEntity["fentity"] as DynamicObjectCollection;
            if (entitys == null || entitys.Count <= 0) return;
            var groups = entitys.GroupBy(x => new { fsourcebillno = Convert.ToString(x["fsourcebillno"]), fsourceType = Convert.ToString(x["fsourceformid"]) });
            foreach (var group in groups)
            {
                var sourceType = group.Key.fsourceType;
                var sourceNumber = group.Key.fsourcebillno;
                if (sourceType.EqualsIgnoreCase("ydj_purchaseorder") && !sourceNumber.IsNullOrEmptyOrWhiteSpace())
                {
                    var sourceOrder = this.GetSourceOrder(sourceType, sourceNumber);
                    if (sourceOrder == null) return;

                    //向源单写入操作日志
                    var logServiceEx = this.Context.Container.GetService<ILogServiceEx>();
                    logServiceEx.SyncBatchWriteLog(this.Context, new LogEntry[]
                    {
                    new LogEntry()
                    {
                        BillFormId = sourceType,
                        OpCode = "receiv",
                        OpName = "收货",
                        BillIds = sourceOrder["id"] as string,
                        Level = Enu_LogLevel.Info.ToString(),
                        LogType = Enu_LogType.RecordType_03,
                        Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                        Content = "经销商订单已【收货】",
                        Detail = ""
                    }
                    });

                    //生成协同订单日志
                    this.BuildSyncOrderLog(sourceOrder);
                }
            }
        }

        /// <summary>
        /// 生成协同订单日志
        /// </summary>
        /// <param name="sourceOrder"></param>
        private void BuildSyncOrderLog(DynamicObject sourceOrder)
        {
            //如果采购订单时协同采购，则需要向“销售方系统”的“销售意向单”写入操作日志
            var purService = this.Container.GetService<IPurchaseOrderService>();
            var isSync = purService.CheckIsSync(this.Context, sourceOrder);
            if (!isSync) return;

            //单据是否已经发生协同
            var bizStatus = Convert.ToString(sourceOrder["fbizstatus"]);
            if (bizStatus.IsNullOrEmptyOrWhiteSpace() || bizStatus.EqualsIgnoreCase("business_status_01")) return;

            var supplierId = sourceOrder["fsupplierid"] as string;
            if (supplierId.IsNullOrEmptyOrWhiteSpace()) return;

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var supplier = dm.Select(supplierId) as DynamicObject;
            if (supplier == null) return;

            var responseResult = this.Gateway.Invoke(
                this.Context,
                new TargetSEP(supplier["fcoocompanyid"] as string, supplier["fcooproductid"] as string),
                new CommonBillDTO()
                {
                    FormId = "bd_record",
                    OperationNo = "write",
                    BillData = "",
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string>
                    {
                        { "records", (new List<Dictionary<string,string>>{
                            new Dictionary<string, string>
                            {
                                { "formId", "ydj_saleintention" },
                                { "tranId", sourceOrder["ftranid"] as string },
                                { "opCode", "receiv" },
                                { "opName", "收货" },
                                { "opContent", "经销商订单已【收货】" },
                                { "operator", this.Context.DisplayName ?? this.Context.UserName },
                            }
                        }).ToJson() },
                    }
                }.SetOptionFlag((long)Enu_OpFlags.RequestToReply)) as CommonBillDTOResponse;
        }

        /// <summary>
        /// 获取源单数据包
        /// </summary>
        /// <param name="sourceType"></param>
        /// <param name="sourceNumber"></param>
        /// <returns></returns>
        private DynamicObject GetSourceOrder(string sourceType, string sourceNumber)
        {
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, sourceType);
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            string where = $"fmainorgid=@fmainorgid and fbillno=@fbillno";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("fbillno", System.Data.DbType.String, sourceNumber)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            return dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            // 反写销售合同【已采购入库数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackPurInQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);


            if (this.Context.IsDirectSale)
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                //是否启用条码管理                    
                var fenablebarcode = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fenablebarcode", false);
                //总部发货后自动生成入库单
                var fautostockinorder = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fautostockinorder", false);
                if (fenablebarcode&& fautostockinorder)
                {
                    // 反写收货扫描任务【已作业数量】
                    Core.Helpers.OrderQtyWriteBackHelper.WriteBackRecTaskWorkQty(
                        this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
                }
            }


            AutoUpdateReserve(e.DataEntitys);
        }

        /// <summary>
        /// 更新对应的销售合同的预留信息
        /// </summary>
        private void AutoUpdateReserve(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var reserveService = this.Container.GetService<IReserveUpdateService>();
            var result = reserveService.SetOrUpdateReserve(this.Context, this.HtmlForm, dataEntities, this.Option);
            this.Result.MergeResult(result);
        }
    }
}