using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PoStockIn
{
    [InjectService]
    [FormId("stk_postockin")]
    [OperationNo("draft")]
    public class Draft : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //删除前先记录关联的合同ids,避免删除后联查不到数据无法更新。
            base.EndOperationTransaction(e);

            // 反写采购订单和销售合同【总部已发货数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackHqDeliveryQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);

            var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            List<string> ids = entitys.Select(o => Convert.ToString(o?["fsoorderentryid"])).ToList<string>();

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys, ids);
        }
    }
}
