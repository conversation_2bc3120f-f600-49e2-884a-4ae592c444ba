using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK
{
    /// <summary>
    /// 库存单据通用保存（用于处理因复制行引起的交易流水号问题）
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout|stk_sostockout|stk_inventorytransfer|stk_inventoryverify|stk_otherstockin|stk_postockin|stk_postockreturn|stk_sostockreturn")]
    [OperationNo("Save")]
    public class CommonSave : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (this.HtmlForm.EntryList == null || !this.HtmlForm.EntryList.Any())
            {
                return;
            }

            var trandidFldName = "ftranid";

            foreach (var entity in this.HtmlForm.EntryList)
            {
                var allFlds = this.HtmlForm.GetEntryFieldList(entity.Id);
                if (!allFlds.Any(s => s.PropertyName.EqualsIgnoreCase(trandidFldName)))
                {
                    continue;
                }

                foreach (var dataEntity in e.DataEntitys)
                {
                    var entrys = dataEntity[entity.PropertyName] as DynamicObjectCollection;

                    // 过滤不为空，再分组，且取分组数大于1的
                    var needClearTranidGroup = entrys
                        .Where(s => !Convert.ToString(s[trandidFldName]).IsNullOrEmptyOrWhiteSpace())
                        .GroupBy(s => Convert.ToString(s[trandidFldName]))
                        .Where(s => s.Count() > 1);

                    foreach (var group in needClearTranidGroup)
                    {
                        var list = group.Skip(1).ToList();

                        list.ForEach(s =>
                        {
                            s[trandidFldName] = " ";
                            if (allFlds.Any(fld => fld.PropertyName.EqualsIgnoreCase("fparenttranid")))
                            {
                                s["fparenttranid"] = " ";
                            }
                            if (allFlds.Any(fld => fld.PropertyName.EqualsIgnoreCase("ftoptranid")))
                            {
                                s["ftoptranid"] = " ";
                            }
                        });
                    }
                }

            }


        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any())
            {
                return;
            }

            //统一处理商品体积数据
            var allMatIds = e.DataEntitys.SelectMany(t => t["fentity"] as DynamicObjectCollection).Select(p => Convert.ToString(p["fmaterialid"])).Distinct().ToList();
            //加载所有商品的即时库存数据

            System.Collections.Generic.List<DynamicObject> invObjs;
            var dbService = this.Context.Container.GetService<IDBService>();
            using (var tran = Context.CreateTransaction())
            {
                //用临时表关联查询
                var tempTable = dbService.CreateTempTableWithDataList(this.Context, allMatIds, false);
                invObjs = this.Context.LoadBizDataByFilter("stk_inventorylist", $"fmaterialid in (select fid from {tempTable})", false);

                tran.Complete();

                //清理临时表
                dbService.DeleteTempTableByName(this.Context, tempTable, true);
            }

            var productObjList = invObjs.Select(t => new
            {
                fmaterialid = t["fmaterialid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fmaterialid"]).Trim(),
                fattrinfo = t["fattrinfo"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fattrinfo"]).Trim(),
                fcustomdesc = t["fcustomdesc"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fcustomdesc"]).Trim(),
                funitid = t["funitid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["funitid"]).Trim(),
                fsinglevolume = t["fsinglevolume"].IsNullOrEmptyOrWhiteSpace() ? 0M : Convert.ToDecimal(t["fsinglevolume"]),
                fvolumeunit = t["fvolumeunit"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fvolumeunit"]).Trim()
            }).ToList();

            //加载所有商品档案
            var allMaterialObjs = this.Context.LoadBizDataById("ydj_product", allMatIds);

            foreach (var data in e.DataEntitys)
            {
                var entrys = data["fentity"] as DynamicObjectCollection;
                foreach (var ent in entrys)
                {
                    var singleVol = ent["fsinglevolume"].IsNullOrEmptyOrWhiteSpace() ? 0M : Convert.ToDecimal(ent["fsinglevolume"]);
                    if (singleVol > 0)
                    {
                        //不为0的不更新
                        continue;
                    }
                    var currMatId = ent["fmaterialid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["fmaterialid"]).Trim();
                    var currAttrInfo = ent["fattrinfo"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["fattrinfo"]).Trim();
                    var currCusDesc = ent["fcustomdesc"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["fcustomdesc"]).Trim();
                    var currUnitid = ent["funitid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(ent["funitid"]).Trim();
                    var filterObj = productObjList.Where(t =>
                      currMatId.EqualsIgnoreCase(t.fmaterialid)
                      && currAttrInfo.EqualsIgnoreCase(t.fattrinfo)
                      && currCusDesc.EqualsIgnoreCase(t.fcustomdesc)
                      && currUnitid.EqualsIgnoreCase(t.funitid)
                    ).OrderByDescending(p => p.fsinglevolume).FirstOrDefault();
                    var saveSingleVol = 0M;
                    var saveVolUnit = "";
                    if (filterObj != null)
                    {
                        saveSingleVol = filterObj.fsinglevolume;
                        saveVolUnit = filterObj.fvolumeunit;
                    }
                    if (saveSingleVol == 0)
                    {
                        //如果没取到有效值，则使用商品档案上的体积
                        var currMatObj = allMaterialObjs.Find(t => Convert.ToString(t["id"]).EqualsIgnoreCase(currMatId));
                        saveSingleVol = currMatObj["fvolume"].IsNullOrEmptyOrWhiteSpace() ? 0M : Convert.ToDecimal(currMatObj["fvolume"]);
                        saveVolUnit = "M3";
                    }

                    var bizQty = 0M;
                    if (this.HtmlForm.Id == "stk_inventoryverify")
                    {
                        bizQty = Convert.ToDecimal(ent["fbizpdqty"]);
                    }
                    else
                    {
                        bizQty = Convert.ToDecimal(ent["fbizqty"]);
                    }

                    ent["fsinglevolume"] = saveSingleVol;
                    ent["ftotalvolume"] = saveSingleVol * bizQty;
                    ent["fvolumeunit"] = saveVolUnit;
                }
            }
            
        }
    }
}