using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryBase
{
    /// <summary>
    /// 盘点方案：保存
    /// </summary>
    [InjectService]
    [FormId("stk_inventorybase")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.Context.IsDirectSale)
                {
                    string fzytype = Convert.ToString(newData["fzytype"]);
                    return !string.IsNullOrWhiteSpace(fzytype);
                }
                return true;
            }).WithMessage("创建单据组织为“直营”盘点类型必填！"));
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var dataEntities = e.DataEntitys;

            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

        }
    }
}
