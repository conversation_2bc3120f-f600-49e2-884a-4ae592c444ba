using JieNor.AMS.YDJ.Store.AppService.Plugin.STK.PurReceiptNotice;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SalReturnNotice
{
    /// <summary>
    /// 销售退货通知单:下推包装清单
    /// </summary>
    [InjectService]
    [FormId("sal_returnnotice")]
    [OperationNo("push2packorder")]
    public class Push2PackOrder : BasePush2PackOrder
    {
    }
}
