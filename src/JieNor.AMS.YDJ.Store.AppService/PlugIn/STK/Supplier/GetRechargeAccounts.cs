using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Supplier
{
    /// <summary>
    /// 供应商：获取可充值账户
    /// </summary>
    [InjectService]
    [FormId("ydj_supplier")]
    [OperationNo("GetRechargeAccounts")]
    public class GetRechargeAccounts: AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            var dataEntity = e.DataEntitys[0];
            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();
            this.Result.SrvData = synAccountBalanceService.GetRechargeBySupplierId(this.Context, Convert.ToString(dataEntity["id"]));
            this.Result.IsSuccess = true;
        }
    }
}
