using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Helpers;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockReturn
{
    /// <summary>
    /// 销售退货单审核：更新销售合同成本信息
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        private List<DynamicObject> auditBeforeOrder { get; set; }
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);


            var svc = this.Container.GetService<ICostCalulateService>();
            svc.UpdateSalesOrderCostInfo(this.Context, this.HtmlForm, e.DataEntitys?.ToList());
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            //// 退货单只反写合同关联的二级经销商采购订单【一级合同状态】为“订单已出库”，且自动重新计算二级销售合同和采购订单关闭状态
            //var stockInfo = this.Context.LoadBizBillHeadDataById("stk_sostockout", e.DataEntitys.Select(x => Convert.ToString(x["fsourceinterid"]))?.ToList(), "fsourcetype,fsourceinterid");
            //if (stockInfo != null && stockInfo.Any())
            //{
            //    ResellerHelper.WriteBackPurchaseOrdersBySoStockOuts(
            //    this.OperationContext,
            //    stockInfo,
            //    OneLvOrderSratusConst.OutStock);
            //}
            //// 反写销售合同【流程状态】
            //Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
            //    this.Context, this.HtmlForm, e.DataEntitys);

            ////筛选【退货类型】为“正常退换”的单据
            //List<DynamicObject> filterObjs = e.DataEntitys.Where(t => Convert.ToString(t["freturntype"]).EqualsIgnoreCase("sostockreturn_biztype_01")).ToList();

            ////反写销售合同【销售退换中数量】
            //OrderQtyWriteBackHelper.WriteBackReturningQty(this.Context, this.HtmlForm, filterObjs);

            ////获取源头单据销售出库单信息
            //var soStockOutNos = filterObjs.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "stk_sostockout").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            //var soStockOuts = this.Context.LoadBizDataByFilter("stk_sostockout", " fbillno in ('{0}') ".Fmt(string.Join("','", soStockOutNos)));
            ////获取源头单据销售合同信息
            //var fsourcenumbers = soStockOuts.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "ydj_order").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            //var orders = this.Context.LoadBizDataByFilter("ydj_order", " fbillno in ('{0}') ".Fmt(string.Join("','", fsourcenumbers)));
            //foreach (var order in orders)
            //{
            //    DocumentStatusHelper.CalcOrderCloseStatus(order, this.Context);
            //}
            //this.Context.SaveBizData("ydj_order", orders);

            //ProductDelistingHelper.DealDelistingDataByStockOut(this.Context, this.HtmlForm, this.auditBeforeOrder, e.DataEntitys.ToList(), "stockoutreturnaudit");

            if (this.Context.IsDirectSale)
            {
                ZySync(e.DataEntitys);
            }
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            this.auditBeforeOrder = ProductDelistingHelper.GetOrderData(this.Context, e.DataEntitys.ToList(), this.HtmlForm);
        }

        private void ZySync(DynamicObject[] objs)
        {
            foreach (var item in objs)
            {
                var zySalOrderSync = Convert.ToBoolean(item["fzysyncsalorder"]);
                var zyOutStockSync = Convert.ToBoolean(item["fzysyncreturnorder"]);
                bool flga1 = false;
                bool flga2 = false;
                if (!zySalOrderSync)
                {

                    var orderHtml = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                    var orderFormId = "ydj_order";
                    var entity = item["fentity"] as DynamicObjectCollection;
                    var soorderentryids = entity.Select(a => Convert.ToString(a["fsoorderentryid"])).ToList();
                    var entityItem = entity.FirstOrDefault();
                    string soorderinterid = Convert.ToString(entityItem["fsoorderinterid"]);
                    //查询合同信息，修改金额等
                    var orderItem = this.Context.LoadBizDataById(orderFormId, soorderinterid);
                    orderItem["forderbyreturn"] = "1";
                    var orderEntry = orderItem["fentry"] as DynamicObjectCollection;
                    var removeItems = orderEntry.Where(a => soorderentryids.IndexOf(Convert.ToString(a["id"])) < 0).ToList();
                    removeItems.ForEach(a => orderEntry.Remove(a));
                    foreach (var returnEntryItem in entity)
                    {
                        foreach (var orderEntryItem in orderEntry)
                        {
                            string soorderentryid = Convert.ToString(returnEntryItem["fsoorderentryid"]);
                            string soid = Convert.ToString(orderEntryItem["id"]);
                            if (soorderentryid.Equals(soid))
                            {
                                orderEntryItem["fbizqty"] = returnEntryItem["fbizqty"];
                                //orderEntryItem["fdistrate"] = returnEntryItem["frefundrate"];
                                //orderEntryItem["fdistrateraw"] = returnEntryItem["frefundrate"];
                                //orderEntryItem["fdistamount"] = (Convert.ToDecimal(returnEntryItem["frefundrateprice"]) / Convert.ToDecimal(returnEntryItem["fbizqty"])).ToString("0.00");
                                orderEntryItem["fdistamount"] = Convert.ToDecimal(returnEntryItem["frefundrateprice"]).ToString("0.00");

                                orderEntryItem["frounded_fdirectdealamount"] = Convert.ToDecimal(returnEntryItem["frefundtail"]);
                                orderEntryItem["rounded_fdealprice"] = Convert.ToDecimal(returnEntryItem["factualrefundamount"]).ToString("0.00");//实退金额
                                orderEntryItem["fcommissionamount_e"] = returnEntryItem["fcommissionamount"];//抽佣金额
                                orderItem["faddress"] = item["faddress"];//详细退货地址
                                orderItem["fchstatus"] = "1";

                            }
                        }
                    }

                    IMuSiService muSiService = this.Container.GetService<IMuSiService>();
                    var orderSync = muSiService.SyncNormalOrder(this.Context, orderHtml, new DynamicObject[] { orderItem });
                    // 焕新订单提交总部
                    //var orderSync = this.Gateway.InvokeBillOperation(this.Context, orderFormId, new[] { orderItem }, "normalordersubmithq",
                    //        new Dictionary<string, object>
                    //        {
                    //            { "IgnoreCheckPermssion", true }, { "IgnoreValidateDataEntities", true },{ "__IgnoreOpLog__", true }  // 正确的忽略操作日志参数
                    //        });

                    orderSync.ThrowIfHasError();
                    flga1 = true;

                    //var orderDto = BuildSalOrderDTO();
                    //var resp = MuSiApi.SendSTKReturnBySalOrder(this.Context, this.HtmlForm, orderDto);
                    //if (resp.status == 1)
                    //{
                    //    flga1 = true;
                    //}
                }
                else
                {
                    flga1 = true;
                }
                if (!zyOutStockSync)
                {
                    if (flga1)
                    {
                        IMuSiService muSiService = this.Container.GetService<IMuSiService>();
                        var result = muSiService.SyncSoStockReturn(this.Context, this.HtmlForm, objs);
                        //var outOrderDto = BuildSalOrderDTO();
                        //var resp1 = MuSiApi.SendSTKReturnByOutStock(this.Context, this.HtmlForm, outOrderDto);
                        //if (resp1.status == 1)
                        //{
                        if (result.IsSuccess)
                        {
                            flga2 = true;
                        }
                        //}
                    }
                }
                item["fzysyncsalorder"] = flga1;
                item["fzysyncreturnorder"] = flga2;

                item["fhqderdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                item["fhqderstatus"] = "1";//已发送
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(objs);
        }

        /// <summary>
        /// 构建数据包
        /// </summary>
        /// <param name="order"></param>
        /// <param name="fentry"></param>
        /// <returns></returns>
        private Dictionary<string, object> BuildSalOrderDTO()
        {
            return new Dictionary<string, object>();
        }
    }
}