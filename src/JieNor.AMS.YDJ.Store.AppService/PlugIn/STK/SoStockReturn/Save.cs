using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockReturn
{
    /// <summary>
    /// 销售退货单：保存
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fentity", data => data).IsTrue((newData, oldData) =>
            {
                var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                refObjMgr?.Load(this.Context, newData.DynamicObjectType, newData, false);
                var materialObj = newData["fmaterialid_ref"] as DynamicObject;
                if (materialObj != null)
                {
                    if (!Convert.ToString(newData["funitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["funitid"])))
                    {
                        errorMsg = $"第{Convert.ToString(newData["fseq"])}行商品【基本单位】与商品基础信息【基本单位】不一致！";
                        return false;
                    }
                    if (!Convert.ToString(newData["fstockunitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["fstockunitid"])))
                    {
                        errorMsg = $"第{Convert.ToString(newData["fseq"])}行商品【库存单位】与商品基础信息【库存单位】不一致！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMsg = "";
                var freturntype = Convert.ToString(newData["freturntype"]);
                var entity = newData["fentity"] as DynamicObjectCollection;
                var exists01 = entity.Any(a => Convert.ToString(a["fdeliverytype"]).Equals("delivery_type_01"));
                if (exists01 && freturntype.Equals("sostockreturn_biztype_01"))
                {
                    errorMsg = $"当前销售退货单【退货类型】=正常退换，不允许总部直发的商品做退换处理，请选择退货退款处理，谢谢！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (this.Context.IsDirectSale)
            {
                foreach (var dataEntity in e.DataEntitys)
                {
                    var entity = dataEntity["fentity"] as DynamicObjectCollection;
                    if (entity == null || entity.Count == 0)
                        continue;

                    decimal actualReturnAmount = Convert.ToDecimal(dataEntity["factualreturnamount"] ?? 0);
                    decimal planReturnAmount = Convert.ToDecimal(dataEntity["fplanreturnamount"] ?? 0);

                    // 总成交金额
                    decimal totalAmount = entity.Sum(row => Convert.ToDecimal(row["famount"] ?? 0));
                    decimal totalPlanAmount = planReturnAmount == 0 ? totalAmount : planReturnAmount;

                    decimal sumFactualRefund = 0;
                    decimal sumRefundRateAmount = 0;
                    for (int i = 0; i < entity.Count; i++)
                    {
                        var row = entity[i];
                        decimal amount = Convert.ToDecimal(row["famount"] ?? 0);

                        // 1. 新实退金额
                        decimal factualRefund;
                        if (i < entity.Count - 1)
                        {
                            factualRefund = Math.Round(actualReturnAmount * (amount / (totalPlanAmount == 0 ? 1 : totalPlanAmount)), 6, MidpointRounding.AwayFromZero);
                            sumFactualRefund += factualRefund;
                        }
                        else
                        {
                            factualRefund = Math.Round(actualReturnAmount - sumFactualRefund, 6, MidpointRounding.AwayFromZero);
                        }
                        row["factualrefundamount"] = factualRefund;

                        // 2. 退款折率
                        decimal refundRate = amount == 0 ? 0 : Math.Round(factualRefund / amount, 6, MidpointRounding.AwayFromZero);
                        row["frefundrate"] = refundRate;

                        // 3. 退款总折扣额
                        decimal refundRateAmount = Math.Round(amount - factualRefund, 6, MidpointRounding.AwayFromZero);
                        row["frefundrateamount"] = refundRateAmount;
                        sumRefundRateAmount += refundRateAmount;

                        // 4. 退款折扣额
                        decimal qty = Convert.ToDecimal(row["fqty"] ?? 0);
                        decimal refundRatePrice = qty == 0 ? 0 : Math.Round(refundRateAmount / qty, 2, MidpointRounding.AwayFromZero);
                        row["frefundrateprice"] = refundRatePrice;

                        // 5. 直营退款尾差（审核时处理，此处可先置0）
                        row["frefundtail"] = Convert.ToDecimal(row["factualrefundamount"]) - Convert.ToDecimal(Convert.ToDecimal(row["factualrefundamount"]).ToString("0.00"));
                        row["fcommissionproportion"] = 3;
                        // 6. 抽佣比例、抽佣金额（如有源单，需带入或查找）
                        decimal commissionProportion = Convert.ToDecimal(row["fcommissionproportion"] ?? 0);
                        decimal commissionAmount = Math.Round(factualRefund * commissionProportion * 0.01M, 2, MidpointRounding.AwayFromZero);
                        row["fcommissionamount"] = commissionAmount;
                    }
                }
            }
        }
    }
}
