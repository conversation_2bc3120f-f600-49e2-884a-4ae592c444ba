using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockReturn
{
    /// <summary>
    /// 销售退货单：选单
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("Pull")]
    public class Pull : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    this.BeforeGetConvertRule(e);
                    break;
                default:
                    break;
            }
        }

        /*
        2、销售退货单.源单编号为空时，进入《选择-销售出库单列表》，点<确定>时，校验所选择的明细行是否对应多笔销售出库单，若是，返回失败，并提示报错：不允许多笔销售出库单合并出库，请重新选择！
        3、销售退货单.源单编号不为空时，进入《选择-销售出库单列表》，点<确定>后，控制只能返回与当前销售退货单的源单编号一致的出库明细；若所选择的明细行均不属于当前销售退货单的源单编号所对应的出库明细，返回失败，并提示报错：不允许多笔销售出库单合并出库，请重新选择！
        4、销售退货单.源单编号不为空时，进入《选择-销售出库单列表》，点<确定>时，校验所选择的明细行是否只对应一笔出库 且 该笔出库编号不同于当前销售退货单的源单编号，若是，返回成功，并重新覆盖销售退货单；反之，则执行第3点逻辑判断。
        任务链接：http://dmp.jienor.com:81/zentao/task-view-29340.html
        */
        private void BeforeGetConvertRule(OnCustomServiceEventArgs e)
        {
            var dataEntities = e.DataEntities;

            if (dataEntities == null || dataEntities.Length == 0) return;

            var eventData = e.EventData as BeforeGetConvertRuleData;
            if (eventData == null) return;

            var rule = this.MetaModelService.LoadConvertRule(this.Context, eventData.RuleId);
            if (rule == null) return;

            // 源单不是销售出库，跳过
            if (!rule.SourceFormId.EqualsIgnoreCase("stk_sostockout")) return;

            // 如果销售出库单的存在源单，但源单不是销售合同，报错
            if (dataEntities.Any(s =>
            {
                string sourceType = Convert.ToString(s["fsourcetype"]);
                return !sourceType.IsNullOrEmptyOrWhiteSpace() && !sourceType.EqualsIgnoreCase("stk_sostockout");
            }))
            {
                throw new BusinessException("销售退货单存在源单类型不是销售出库单的，请重新选择！");
            }

            var pkids = eventData.SelectedRows?.Where(s => !s.PkValue.IsNullOrEmptyOrWhiteSpace()).Select(s => s.PkValue).Distinct();
            if (pkids == null || pkids.Count() == 0) return;

            var sourceDataEntities = this.Context.LoadBizDataById(rule.SourceFormId, pkids);
            if (sourceDataEntities == null || sourceDataEntities.Count == 0) return;

            var hasDifferent = sourceDataEntities.Any(x => Convert.ToBoolean(x["frenewalflag"])) && sourceDataEntities.Any(x => !Convert.ToBoolean(x["frenewalflag"]));
            if (hasDifferent)
            {
                throw new BusinessException("仅允许全部选焕新单据或者全部选非焕新单据，请重新选择！");
            }

            List<SelectedRow> selectedRows = new List<SelectedRow>();

            foreach (var dataEntity in dataEntities)
            {
                string sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
                if (sourceNumber.IsNullOrEmptyOrWhiteSpace())
                {
                    //2、销售退货单.源单编号为空时，进入《选择 - 销售出库单列表》，点<确定> 时，校验所选择的明细行是否对应多笔合同，若是，返回失败，并提示报错：不允许多笔销售出库单合并退货，请重新选择！
                    if (sourceDataEntities.Count > 1)
                    {
                        throw new BusinessException("不允许多笔销售出库单合并退货，请重新选择！");
                    }
                }
                else
                {
                    if (sourceDataEntities.Count == 1)
                    {
                        //4、销售退货单.源单编号不为空时，进入《选择 - 销售出库单列表》，点<确定> 时，校验所选择的明细行是否只对应一笔出库单 且 该笔出库单编号不同于当前销售退货单的源单编号，若是，返回成功，并重新覆盖销售退货单；反之，则执行第3点逻辑判断。
                        var sourceOrder = sourceDataEntities.First();
                        var orderNo = Convert.ToString(sourceOrder["fbillno"]);
                        if (!orderNo.EqualsIgnoreCase(sourceNumber))
                        {
                            // 删除
                            var entities = dataEntity["fentity"] as DynamicObjectCollection;
                            entities.Clear();

                            string pkid = Convert.ToString(dataEntity["id"]);
                            string billNo = Convert.ToString(dataEntity["fbillno"]);
                            var response = this.Gateway.InvokeLocal<DynamicDTOResponse>(this.Context,
                                new CommonBillDTO()
                                {
                                    FormId = this.HtmlForm.Id,
                                    OperationNo = "deleterow",
                                    SelectedRows = entities.Select(s => new SelectedRow
                                    {
                                        PkValue = pkid,
                                        EntityKey = "fentity",
                                        EntryPkValue = Convert.ToString(s["id"])
                                    })
                                });
                            response.OperationResult.ThrowIfHasError(true, $"{this.HtmlForm.Caption}【{billNo}】清空明细失败！");

                            // 更新源单信息
                            dataEntity["fsourceinterid"] = sourceOrder["id"];
                            dataEntity["fsourcenumber"] = sourceOrder["fbillno"];
                        }

                        selectedRows = eventData.SelectedRows.ToList();
                    }
                    else
                    {
                        //3、销售退货单.源单编号不为空时，进入《选择 - 销售出库列表》，点<确定> 后，控制只能返回与当前销售退货单的源单编号一致的出库明细；若所选择的明细行均不属于当前销售退货单的源单编号所对应的出库明细，返回失败，并提示报错：不允许多笔销售出库单合并出库，请重新选择！

                        // 如果源单里有销售出库单.源单编号
                        var sourceOrder = sourceDataEntities.FirstOrDefault(s =>
                            Convert.ToString(s["fbillno"]).EqualsIgnoreCase(sourceNumber));
                        if (sourceOrder != null)
                        {
                            string sourceOrderId = Convert.ToString(sourceOrder["id"]);
                            string sourceEntityKey = "fentry";

                            // 只返回与当前销售出库单的源单编号一致的 且 排除已添加的明细
                            var sourceOrderEntrys = sourceOrder["fentry"] as DynamicObjectCollection;
                            var sourceOrderEntryIds = sourceOrderEntrys.Select(s => Convert.ToString(s["id"]));

                            var entities = dataEntity["fentity"] as DynamicObjectCollection;
                            var sourceEntityIds = entities
                                .Where(s => Convert.ToString(s["fsourceformid"]).EqualsIgnoreCase("stk_sostockout"))
                                .Select(s => Convert.ToString(s["fsourceentryid"]));

                            var addingEntryIds = sourceOrderEntryIds.Where(s => !sourceEntityIds.Contains(s));

                            foreach (var selectedRow in eventData.SelectedRows.Where(s => s.PkValue.EqualsIgnoreCase(sourceOrderId) && s.EntityKey.EqualsIgnoreCase(sourceEntityKey)))
                            {
                                // 如果单据体id为空，表示整单添加
                                if (selectedRow.EntryPkValue.IsNullOrEmptyOrWhiteSpace())
                                {
                                    selectedRows.Add(selectedRow);
                                }
                                else if (addingEntryIds.Contains(selectedRow.EntryPkValue))
                                {
                                    selectedRows.Add(selectedRow);
                                }
                            }
                        }
                        // 没找到源单，说明选择的都不是当前销售退货单的源单对应的，报错
                        else
                        {
                            throw new BusinessException("不允许多笔销售出库单合并退货，请重新选择！");
                        }

                    }

                    eventData.SelectedRows = selectedRows;
                }
            }

            foreach (var dataEntity in dataEntities)
            {
                var renewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                string sourcenumber = Convert.ToString(dataEntity["fsourcenumber"]);
                
                //仅允许全部选焕新单据或者全部选非焕新单据
                var entities = dataEntity["fentity"] as DynamicObjectCollection;
                var isAllNotEmpty = !sourcenumber.IsNullOrEmptyOrWhiteSpace()
                                   && (entities != null && entities.Any());
                if (!isAllNotEmpty)
                {
                    var sourceDataEntity = sourceDataEntities.FirstOrDefault();
                    renewalflag = Convert.ToBoolean(sourceDataEntity["frenewalflag"]);
                }

                foreach (var sourceDataEntity in sourceDataEntities)
                {
                    var sourcerenewalflag = Convert.ToBoolean(sourceDataEntity["frenewalflag"]);
                    if (renewalflag != sourcerenewalflag)
                    {
                        throw new BusinessException("仅允许全部选焕新单据或者全部选非焕新单据，请重新选择！");
                    }
                }
            }
        }

    }
}
