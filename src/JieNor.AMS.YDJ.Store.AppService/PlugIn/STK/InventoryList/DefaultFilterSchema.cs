using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryList
{
    /// <summary>
    /// 即时库存列表：预设的过滤方案
    /// </summary>
    [InjectService]
    [FormId("stk_inventorylist")]
    public class DefaultFilterSchema : IDynamicFilterSchemeService
    {
        public List<FilterSchemeObject> BuildDynamicFilterScheme(UserContext ctx, HtmlForm htmlForm)
        {
            List<FilterSchemeObject> filterSchemes = new List<FilterSchemeObject>();

            var schemeObj = DisplayNegativeInventory(htmlForm);
            filterSchemes.Add(schemeObj);

            return filterSchemes;
        }


        private static FilterSchemeObject DisplayNegativeInventory(HtmlForm htmlForm)
        {
            var schemeObj = new FilterSchemeObject()
            {
                Id = "dp_nt_inventory",
                Name = "显示负库存",
                BillFormId = htmlForm.Id,
                IsPreset = true,
                Order = 1,
            };
            schemeObj.FilterData.Add(new FilterRowObject()
            {
                Id = "fstockqty",
                Operator = "<",
                Value = "0",
                LeftBracket = "",
                RightBracket = "",
                Logic = "",
                RowIndex = 0,
            });

            return schemeObj;
        }
    }
}
