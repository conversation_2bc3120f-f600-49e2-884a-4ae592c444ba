using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.InventoryList
{
    /// <summary>
    /// 即时库存：清除套件商品库存数据
    /// </summary>
    [InjectService]
    [FormId("stk_inventorylist")]
    [OperationNo("ClearSuiteStock")]
    public class ClearSuiteStock : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

            string sqlText = $@"
            select inv.fid from t_stk_inventorylist inv 
            inner join t_bd_material m on m.fid = inv.fmaterialid and m.fsuiteflag = '1'
            where inv.fmainorgid = @fmainorgid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var inventorys = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParam);
            var ids = inventorys?.Select(o => o["fid"]);
            if (ids == null || !ids.Any())
            {
                this.Result.SimpleMessage = "当前不存在套件商品库存数据！";
                this.Result.IsSuccess = false;
                return;
            }

            
            dm.Delete(ids);

            this.Result.ComplexMessage.SuccessMessages.Add("清除成功！");
            this.Result.IsSuccess = true;
        }
    }
}