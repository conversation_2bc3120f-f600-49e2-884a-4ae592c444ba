using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.StoreHouse
{
    /// <summary>
    /// 仓库即时库存校验
    /// </summary>
    public class Validation_Inv : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            CheckInv(userCtx, formInfo, dataEntities, result, option, operationNo);

            return result;
        }

        /// <summary>
        /// 检验即时库存
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        /// <param name="option"></param>
        private void CheckInv(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option, string operationNo)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;

            /*
             * 1、在《仓库》中增加一控制：当仓库维护仓位值之后操作保存，需要校验当前仓库如果存在【仓位】为空且【库存单位数量】>0的即时库存明细数据，保存失败，并给个警告提示：已存在即时库存数据，不允许中途启用仓位。
             */

            List<string> ids = new List<string>();

            foreach (var dataEntity in dataEntitys)
            {
                var fentity = (DynamicObjectCollection)dataEntity["fentity"];
                if (fentity.Any())
                {
                    ids.Add(Convert.ToString(dataEntity["id"]));
                }
            }

            if (ids.Any())
            {
                string sql =
                    $"select distinct fstorehouseid from t_stk_inventorylist with(nolock) where fstorehouseid in ({ids.JoinEx(",", true)}) and fstorelocationid='' and fqty>0";


                var hasNotLocationStock = this.DBService.ExecuteDynamicObject(this.Context, sql);

                foreach (var item in hasNotLocationStock)
                {
                    var dataEntity = dataEntitys.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(Convert.ToString(item["fstorehouseid"])));
                    if (dataEntity != null)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{formInfo.Caption} {dataEntity["fnumber"]}/{dataEntity["fname"]} 已存在即时库存数据，不允许中途启用仓位。",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

    }
}
