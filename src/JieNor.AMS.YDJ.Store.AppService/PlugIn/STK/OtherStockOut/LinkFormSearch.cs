using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockOut
{
    /// <summary>
    /// 联查
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : LinkFormSearchBase
    {
        protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            LinkReserveBill(userContext, dataEntities, linkFormDatas);
        }
    }
}