using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Contexts;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockOut
{
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fmaterialid", "fstorehouseid", "fstore", "funitid", "fcostcenterid", "fdeptid" });
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var nowDate = DateTime.Now;
            foreach (var dataEntity in e.DataEntitys)
            {
                //task 70392,【其它出库单】以当前日期更新至【出库日期】
                dataEntity["fdate"] = nowDate;
            }
        }


        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmptyOrWhiteSpace() || !e.DataEntitys.Any())
            {
                return;
            }
            SyncOtherStockOut(e.DataEntitys);
        }


        /// <summary>
        /// 其它出库单审核后，调用同步接口
        /// </summary>
        /// <param name="order"></param>
        /// <param name="syncingEntrys"></param>
        private void SyncOtherStockOut(DynamicObject[] order)
        {
            //直营并且不等于跨组织调出，走同步接口
            var zyOrder = order.Where(a => Convert.ToString(a["fmanagemodel"]).Equals("1"));
            if (zyOrder == null || zyOrder.Count() == 0) return;

            var agentItem = this.Context.LoadBizDataById("bas_agent", this.Context.Company);
            var mate = this.Context.Container.GetService<IMetaModelService>();
            var agentForm = mate.LoadFormModel(this.Context, "bas_agent");
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, new DynamicObject[] { agentItem }, true, agentForm, new List<string> { "fsaleorgid" });

            string storehouseNumber = "";
            string _sql = $"select top 1 fid,fnumber from T_YDJ_STOREHOUSE with(nolock) where  fmainorgid='{this.Context.Company}' and fwarehousetype='warehouse_01' and fforbidstatus=0  and fcreatorid='sysadmin' order by fcreatedate asc";
            using (var dr = this.Context.ExecuteReader(_sql, null))
            {
                if (dr.Read())
                {
                    storehouseNumber = Convert.ToString(dr["fnumber"]);
                }
            }
            foreach (var orderItem in zyOrder)
            {
                var dto = BuildData(orderItem, agentItem, storehouseNumber);
                var resp = MuSiApi.SendStkOtherStockOut(this.Context, this.HtmlForm, dto);

                orderItem["fhqderdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                orderItem["fhqderstatus"] = "1";//已发送
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(zyOrder);
        }

        /// <summary>
        /// 按中台字段要求构建数据包
        /// </summary>
        private Dictionary<string, object> BuildData(DynamicObject order, DynamicObject agentItem, string storehouseNumber)
        {
            Dictionary<string, object> dicts = new Dictionary<string, object>();
            // 主表字段
            dicts.Add("zyOutboundNum", Convert.ToString(order["fbillno"])); // 其他出库单号
            string zyType = Convert.ToString(order["ftype"]);

            switch (zyType)
            {
                case "zy_otherstockout_type_01":
                    zyType = "01"; // 赠送商场
                    break;
                case "zy_otherstockout_type_02":
                    zyType = "02"; // 店面活动
                    break;
                case "830859544318181376":
                    zyType = "03"; // 报废
                    break;
                case "zy_otherstockout_type_04":
                    zyType = "04"; // 奖励员工
                    break;
                case "zy_otherstockout_type_05":
                    zyType = "05"; // 门店装修材料
                    break;
                case "zy_otherstockout_type_06":
                    zyType = "06"; // 饰品转出
                    break;
            }
            dicts.Add("zyType", zyType); // 出库类型
            dicts.Add("zySalesCode", Convert.ToString((agentItem["fsaleorgid_ref"] as DynamicObject)?["fnumber"])); // 公司编码
            dicts.Add("zyFkber", "2000"); // 功能范围
                                          // 业务场景
            string zyZywcj = "CJ01";
            if (zyType == "01") zyZywcj = "J002";
            else if (zyType == "02") zyZywcj = "C101";
            dicts.Add("zyZywcj", zyZywcj);
            dicts.Add("zyInvDate", Convert.ToString(order["fdate"])); // 出库日期
                                                                      // 移动类型
            string zyMoveType = zyType == "03" ? "551" : "201";//报废：551 其他：201
            dicts.Add("zyMoveType", zyMoveType);
            dicts.Add("zyStoreNum", Convert.ToString((order["fstore_ref"] as DynamicObject)?["fnumber"])); // 门店编码
            //dicts.Add("zyStockStaffId", Convert.ToString(order["fstockstaffid"])); // 仓管员
            //dicts.Add("zyStockDeptId", Convert.ToString(order["fstockdeptid"])); // 仓管部门
            //dicts.Add("zyStockAddr", Convert.ToString(order["fstockaddr"])); // 仓库地址?
            //dicts.Add("zyPlanDate", Convert.ToString(order["fplandate"])); // 计划出库日期
            //dicts.Add("zyCustName", Convert.ToString(order["fcustname"])); // 客户姓名?
            dicts.Add("zyComments", Convert.ToString(order["fdescription"])); // 备注
            dicts.Add("zyCostCenter", Convert.ToString((order["fdeptid_ref"] as DynamicObject)?["fsapcostcenterno"])); // 成本中心

            // 明细表
            List<Dictionary<string, object>> entityDics = new List<Dictionary<string, object>>();
            var entitys = order["fentity"] as DynamicObjectCollection;
            foreach (var item in entitys)
            {
                var entityDict = new Dictionary<string, object>
                    {
                        { "zyItemLine", Convert.ToString(item["fseq"]) }, // 行号
                        { "zyProdCode", Convert.ToString((item["fmaterialid_ref"] as DynamicObject)?["fnumber"]) }, // 产品编码
                        { "zyQty", Convert.ToDecimal(item["fbizqty"]) }, // 实发数量
                        { "zyUnit", Convert.ToString((item["funitid_ref"] as DynamicObject)?["fnumber"])  }, // 单位
                        { "zySalesOrg", Convert.ToString((agentItem["fsaleorgid_ref"] as DynamicObject)?["fnumber"]) }, // 销售组织
                        { "zyComments", Convert.ToString(item["fentrynote"]) }, // 行备注
                        { "zyTermNum", Convert.ToString(item["ftranid"]) }, // 交易流水号
                        { "zyAttr", Convert.ToString(item["fattrinfo_e"]) }, // 辅助属性
                        { "zyDesc", Convert.ToString(item["fcustomdesc"]) }, // 定制说明
                        { "zyOrdQty", Convert.ToDecimal(item["fbizplanqty"]) }, // 应发数量
                        //{ "zyIsGift", Convert.ToString(item["fisgiveaway"]) }, // 赠品
                        { "zyWarehouse", Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fnumber"]) }, // 仓库
                        { "zyLocation",Convert.ToString((item["fstorelocationid_ref"] as DynamicObject)?["fnumber"])  } // 仓位
                    };

                var warehousetype = Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fwarehousetype"]);
                if (warehousetype.Equals("warehouse_01"))//总仓，传仓库编码
                {
                    entityDict.Add("zyStoreNum", storehouseNumber);
                }
                else if (warehousetype.Equals("warehouse_02"))//门店仓，传门店编码
                {
                    entityDict.Add("zyStoreNum", Convert.ToString((order["fstore_ref"] as DynamicObject)?["fnumber"]));
                }
                else if (warehousetype.Equals("warehouse_04"))//售后仓，传门店编码
                {
                    var mulstore = Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fmulstore"]);
                    if (mulstore.IsNullOrEmptyOrWhiteSpace())
                    {
                        entityDict.Add("zyStoreNum", storehouseNumber);
                    }
                    else
                    {
                        entityDict.Add("zyStoreNum", Convert.ToString((order["fstore_ref"] as DynamicObject)?["fnumber"]));
                    }
                }
                entityDics.Add(entityDict);
            }
            dicts.Add("item", entityDics);
            return dicts;
        }
    }
}