using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Linq;
using System;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.DataTransferObject.Poco;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.Framework.DataTransferObject;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface.CloudChain;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockOut
{
    /// <summary>
    /// 其他出库单：保存
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fmaterialid", "fstorehouseid", "fstore", "funitid", "fdeptid", "fstorehouseid_h" });
        }
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string paramMessage = "";
            bool flag = true;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fentity = newData["fentity"] as DynamicObjectCollection;
                foreach (DynamicObject item in fentity)
                {
                    if (Convert.ToDecimal(item["fbizqty"]) == 0)
                    {
                        paramMessage += $"商品明细第{string.Join(",", item["fseq"])}行的实发数量不允许为0！";
                        flag = false;
                    }
                }
                return flag;
            }).WithMessage("{0}", (billObj, propObj) => paramMessage));

            string errorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fentity", data => data).IsTrue((newData, oldData) =>
            {
                var materialObj = newData["fmaterialid_ref"] as DynamicObject;
                if (materialObj != null)
                {
                    if (!Convert.ToString(newData["funitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["funitid"])))
                    {
                        errorMsg = $"第{Convert.ToString(newData["fseq"])}行商品【基本单位】与商品基础信息【基本单位】不一致！";
                        return false;
                    }
                    if (!Convert.ToString(newData["fstockunitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["fstockunitid"])))
                    {
                        errorMsg = $"第{Convert.ToString(newData["fseq"])}行商品【库存单位】与商品基础信息【库存单位】不一致！";
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!this.Context.IsDirectSale)
                {
                    return true;
                }
                paramMessage = "";
                var fstore = Convert.ToString(newData["fstore"]);
                var fentity = newData["fentity"] as DynamicObjectCollection;
                foreach (DynamicObject item in fentity)
                {
                    var storehouseObj = (item["fstorehouseid_ref"] as DynamicObject);
                    if (storehouseObj != null && Convert.ToString(storehouseObj["fwarehousetype"]).Equals("warehouse_02"))
                    {
                        var mulstore = Convert.ToString(storehouseObj["fmulstore"]).ToString();
                        if (mulstore.IndexOf(fstore) < 0)
                        {
                            paramMessage += $"第{string.Join(",", item["fseq"])}行商品所选仓库关联的门店需与单据头门店名称一致，请修改！";
                            flag = false;
                        }
                    }
                }
                return flag;
            }).WithMessage("{0}", (billObj, propObj) => paramMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                paramMessage = "";
                flag = true;
                if (!this.Context.IsDirectSale)
                    return true;
                var fentity = newData["fentity"] as DynamicObjectCollection;
                var headStoreHouseid = newData["fstorehouseid_h"]?.ToString(); // 单据头.仓库
                if (string.IsNullOrWhiteSpace(headStoreHouseid))
                {
                    throw new BusinessException("请选择出库仓库！");
                }

                foreach (DynamicObject item in fentity)
                {
                    var storehouseObj = Convert.ToString(item["fstorehouseid"]);
                    if (!storehouseObj.Equals(headStoreHouseid))
                    {
                        paramMessage += $"明细第{item["fseq"]}行仓库与出库仓库不一致，请调整！\n";
                        flag = false;
                    }
                }
                return flag;
            }).WithMessage("{0}", (billObj, propObj) => paramMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!this.Context.IsDirectSale)
                {
                    return true;
                }
                paramMessage = "";
                flag = true;
                var store = Convert.ToString(newData["fstore"]);
                var headWarehouseType = Convert.ToString((newData["fstorehouseid_h_ref"] as DynamicObject)?["fwarehousetype"])?.ToString(); // 单据头.仓库类型
                var mulstore = Convert.ToString((newData["fstorehouseid_h_ref"] as DynamicObject)?["fmulstore"])?.ToString(); // 单据头.仓库类型门店
                if (store.IsNullOrEmptyOrWhiteSpace() || headWarehouseType.Equals("warehouse_01") || (headWarehouseType.Equals("warehouse_04") && mulstore.IsNullOrEmptyOrWhiteSpace()))
                {
                    var sapcostcenterno = Convert.ToString((newData["fdeptid_ref"] as DynamicObject)?["fsapcostcenterno"]);
                    if (sapcostcenterno.IsNullOrEmptyOrWhiteSpace())
                    {
                        paramMessage += $"单号：【{newData["fbillno"]}】“SAP成本中心编码”为空，请选择已关联该编码的部门以便完成财务成本做账，请修改！\n";
                        flag = false;
                    }
                }
                return flag;
            }).WithMessage("{0}", (billObj, propObj) => paramMessage));

        }


        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            //先删除预留信息
            DeleteLinkReserveInfo(e.DataEntitys);

            this.Container.GetService<IRepairService>().RepairBillHeadSourceNum(this.Context, e.DataEntitys, "fsourcenumber", "fsourceinterid", "fentity", "fsourcebillno", "fsourceinterid");
        }



        /// <summary>
        /// 先删除关联的预留单信息，后续的预留更新服务会重新生成预留信息
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void DeleteLinkReserveInfo(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length == 0)
            {
                return;
            }

            var datas = dataEntitys.Where(f => !(Convert.ToString(f["fstatus"]).EqualsIgnoreCase("D")
                                                 || Convert.ToString(f["fstatus"]).EqualsIgnoreCase("E")))?.ToList();
            if (datas == null || datas.Count == 0)
            {
                return;
            }

            var svc = this.Container.GetService<IReserveUpdateService>();
            var opResult = svc.DeleteReserve(this.Context, this.HtmlForm, datas, this.Option);
        }


    }
}
