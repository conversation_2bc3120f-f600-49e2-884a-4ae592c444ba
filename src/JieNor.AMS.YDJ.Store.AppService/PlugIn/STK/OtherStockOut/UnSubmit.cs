using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.OtherStockOut
{
    /// <summary>
    /// 其他出库单：撤销
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("UnSubmit")]
    public class UnSubmit: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var sql = @"select a.fentryid from t_bcm_descantaskentity a left join t_bcm_deliveryscantask b on a.fid = b.fid where b.fmainorgid = '{0}' and a.flinkformid= 'stk_otherstockout' and a.flinkbillno = '{1}'".Fmt(this.Context.Company, newData["fbillno"]?.ToString());
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                return data == null || !data.Any();
            }).WithMessage("当前其他出库单【{0}】已经生成了发货扫描任务，请您删除后再进行撤销操作！", (newData, oldData) => newData["fbillno"]));
        }
    }
}
