using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockOut
{
    /// <summary>
    /// 销售出库单：作废
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("UnCancel")]
    public class UnCancel : AbstractOperationServicePlugIn
    {

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            //先删除预留信息
            DeleteLinkReserveInfo(e.DataEntitys);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            var ids = entitys.Select(o => Convert.ToString(o?["Id"])).ToList();

            // 反写销售合同【已推出库数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackTransOutQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);
        }


        /// <summary>
        /// 先删除关联的预留单信息，后续的预留更新服务会重新生成预留信息
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void DeleteLinkReserveInfo(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length == 0)
            {
                return;
            }

            var datas = dataEntitys.Where(f => !(Convert.ToString(f["fstatus"]).EqualsIgnoreCase("D")
                                        || Convert.ToString(f["fstatus"]).EqualsIgnoreCase("E")))?.ToList();
            if (datas == null || datas.Count == 0)
            {
                return;
            }

            var svc = this.Container.GetService<IReserveUpdateService>();
            var opResult = svc.DeleteReserve(this.Context, this.HtmlForm, datas, this.Option);
        }



    }
}
