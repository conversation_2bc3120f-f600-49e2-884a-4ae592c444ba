using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut
{
    /// <summary>
    /// 销售出库单：根据Id查询
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("querybyid")]
    public class QueryById : AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
            e.PermItem = "fw_view";
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var Ids = this.GetQueryOrSimpleParam<string>("Ids");
            if (Ids.IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("参数Ids不能为空！");

            var datas = JsonConvert.DeserializeObject<JArray>(Ids);
            if (datas == null || datas.Count() <= 0)
            {
                throw new BusinessException("请先选择一行商品明细！");
            }

            BuildSqlWhereAndParams(out var param, out var listBuilder);

            var data = datas.Select(x => Convert.ToString(x["fid"])).Distinct();
            if (datas != null && datas.Count > 0)
            {
                param.AppendFilterString($@" t0.fid in ({data.JoinEx(",", true)})");
            }

            param.PageCount = 50;
            param.PageIndex = 1;

            //排序：默认按创建日期降序
            var orderBy = "fcreatedate";
            param.OrderByString = $"{orderBy} {"desc"}";

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            //获取分页数据
            var listData = listBuilder.GetQueryData(this.Context, param, queryObj);
            if (listData == null || listData.Count() == 0)
            {
                this.Result.IsSuccess = false;
            }
        }

        protected void BuildSqlWhereAndParams(out SqlBuilderParameter param, out IListSqlBuilder listBuilder)
        {
            //参数对象
            param = new SqlBuilderParameter(this.Context, this.HtmlForm.Id);
            param.ReadDirty = true;
            param.NoColorSetting = true;

            //当前要查询的字段列表
            var fieldKeys = new string[] { "fid", "fbillno" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(this.Context);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }
            //列表构建器
            listBuilder = this.Container.GetService<IListSqlBuilder>();
            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
            param.SetFilter(accessFilter);
        }
    }
}
