using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Helpers;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockOut
{
    /// <summary>
    /// 销售出库单：审核
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fmaterialid", "fstorehouseid", "fmainorgid" });
        }
        private List<DynamicObject> auditBeforeOrder { get; set; }
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            var errMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var type = newData["fsourcetype"].ToString();
                if (!type.IsNullOrEmptyOrWhiteSpace() && type == "ydj_order")
                {
                    //销售合同允许出库的最低金额比例
                    var profileService = this.Context.Container.GetService<ISystemProfile>();
                    var systemParameter = profileService.GetSystemParameter(this.Context, "bas_storesysparam");
                    var fsourcenumber = newData["fsourcenumber"].ToString();
                    var dm = GetOrderDm(fsourcenumber);
                    if (dm.IsNullOrEmptyOrWhiteSpace())
                    {
                        errMsg = "选单数据不正确！";
                        return false;
                    }


                    //若启用了流程则会走流程的条件，不需要走这段业务条件
                    var fflowinstanceid = Convert.ToString(newData["fflowinstanceid"]);
                    if (fflowinstanceid.IsNullOrEmptyOrWhiteSpace())
                    {
                        //任务 37793 参数改为从合同单据类型配置里读取 ，不再使用销售管理参数配置了。
                        var fproportionoratioamount = 100;
                        //找上游合同的单据类型参数配置
                        var fsourcebillno = Convert.ToString(newData["fsourcenumber"]);
                        if (!fsourcebillno.IsNullOrEmptyOrWhiteSpace())
                        {
                            var billTypeService = this.Container.GetService<IBillTypeService>();
                            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                            var reader = Context.GetPkIdDataReader(orderForm, $@"fbillno='{fsourcebillno}'", new List<SqlParam>() { });
                            var formDt = orderForm.GetDynamicObjectType(Context);
                            var orderdm = Context.Container.GetService<IDataManager>();
                            orderdm.InitDbContext(Context, formDt);
                            var dynObjs = orderdm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();
                            var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, orderForm, Convert.ToString(dynObjs["fbilltype"]));
                            if (paramSetObj != null)
                            {
                                int.TryParse(Convert.ToString(paramSetObj["fproportionoratioamount"]), out fproportionoratioamount);
                            }
                        }

                        var freceivable = dm["freceivable"];
                        var fsumamount = dm["fsumamount"];
                        //#37762改为订单总额-申请退货金额
                        var newfsumamount = (Convert.ToDecimal(fsumamount) - Convert.ToDecimal(dm["frefundamount"])) * fproportionoratioamount / 100;
                        if (Convert.ToDouble(freceivable) < Convert.ToDouble(newfsumamount))
                        {
                            errMsg = "当前订单确认已收金额不足" + newfsumamount + "元，暂不允许出库！";
                            return false;
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fsourcebillno = Convert.ToString(newData["fsourcenumber"]);
                var sysProfile = this.Container.GetService<ISystemProfile>();
                //销售合同超额收款控制出库
                var forderoverpaymentdisout = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "forderoverpaymentdisout", false);

                if (!fsourcebillno.IsNullOrEmptyOrWhiteSpace() && forderoverpaymentdisout)
                {
                    return this.CheckCanOutByFunreceived(newData, out errMsg);
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var nowDate = DateTime.Now;
            foreach (var dataEntity in e.DataEntitys)
            {
                //task 70392,【销售出库单】以当前日期更新至【出库日期】
                dataEntity["fdate"] = nowDate;
            }
        }

        /// <summary>
        /// 销售出库审核后需要反写上游单据状态
        /// 修改人：zpf
        /// 日  期：2022-01-24
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys.Count() == 0) return;
            this.Result.IsSuccess = false;
            var notifyService = this.Context.Container.GetService<IStockOutNotifyService>();
            notifyService.Notify(this.Context, e.DataEntitys, this.HtmlForm, this.OperationNo);

            // 反写销售合同【流程状态】
            //这里先反写流程状态，让下面OrderReviewAuditOrUnAudit同步流程状态到接单方和二级
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys/*, secOrderEntryIds*/);

            /*10.如果发货方经销商将转单的《销售合同》出库生成《销售出库单》时 (经过发货通知单也算), 《销售出库单》审核之后要更新出库数量
            1)更新 接单方 与 发货方《转单申请单》商品明细对应商品的【已发货数量】, 并且如果是完全出库的话更新《转单申请单》基本信息的【发货完成日期】(用出库日期反写)
            2)更新 接单方《销售合同》商品明细的【销售已出库数】
            3)更新 发货方《销售合同》商品明细的【销售已出库数】(已有标准功能)
            4)还要增加 找到 接单方《转单申请单》 所对应 二级分销商的《采购订单》与《销售合同》的数据 进行更新, 让二级分销商的导购或跟单可以知道已经给消费者发货了
                4.1) 更新 二级分销商的《销售合同》商品明细的【销售已出库数】, 【基本单位已出库数】
                4.2) 如果全部发货完毕则还要更新 二级分销商的《销售合同》商品明细的【行关闭状态】= "自动关闭",【流程状态】="已出库"
                4.3) 更新 二级分销商的《采购订单》商品明细的【采购已入库数】,【基本单位已采购数量】
                4.4) 如果全部发货完毕则还要更新 二级分销商的《采购订单》商品明细的【行关闭状态】= "自动关闭"
                4.5) 同样的如果后来 发货经销商 将该《销售出库单》反审核,  成功反审核后要扣减上述所更新的数量, 以及所更新的【行关闭状态】,【流程状态】 (按以前逻辑重新判断)
            5)同样的如果后来该《销售出库单》反审核, 要扣减上述所更新的数量 与 清空发货完成日期
            
            remark:目前只有销售出库单审核会反写二级合同状态，
            如需其他采购等也反写，需和出库单审核插件类似处理下流程反写状态先后顺序逻辑，这里是先计算流程状态后
             */
            //HashSet<string> secOrderEntryIds =null;

            AuditOrUnAuditHelper.OrderReviewAuditOrUnAudit(this.HtmlForm.Id, e.DataEntitys, this.Context, true/*, secOrderEntryIds*/);

            // 反写合同关联的二级经销商采购订单【一级合同状态】为“订单已出库”，且自动重新计算二级销售合同和采购订单关闭状态
            ResellerHelper.WriteBackPurchaseOrdersBySoStockOuts(
                this.OperationContext,
                e.DataEntitys,
                OneLvOrderSratusConst.OutStock);

            ProductDelistingHelper.DealDelistingDataByStockOut(this.Context, this.HtmlForm, this.auditBeforeOrder, e.DataEntitys.ToList(), "stockoutaudit");
            this.Result.IsSuccess = true;
        }




        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            this.auditBeforeOrder = ProductDelistingHelper.GetOrderData(this.Context, e.DataEntitys.ToList(), this.HtmlForm);
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);


            var svc = this.Container.GetService<ICostCalulateService>();
            svc.UpdateSalesOrderCostInfo(this.Context, this.HtmlForm, e.DataEntitys?.ToList());


            SyncSoStockOut(e.DataEntitys);

        }

        /// <summary>
        /// 发送出库单至中台
        /// </summary>
        /// <param name="order"></param>
        /// <param name="syncingEntrys"></param>
        private void SyncSoStockOut(DynamicObject[] order)
        {
            var zyOrder = order.Where(a => Convert.ToString(a["fmanagemodel"]).Equals("1") && Convert.ToBoolean(a["fpiecesendtag"]) == false);
            if (zyOrder == null || zyOrder.Count() == 0) return;
            var agentItem = this.Context.LoadBizDataById("bas_agent", this.Context.Company);
            var mate = this.Context.Container.GetService<IMetaModelService>();
            var agentForm = mate.LoadFormModel(this.Context, "bas_agent");
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, new DynamicObject[] { agentItem }, true, agentForm, new List<string> { "fsaleorgid" });
            List<string> soOrderId = new List<string>();
            zyOrder.ForEach(a =>
           {
               var entity = a["fentity"] as DynamicObjectCollection;
               soOrderId.AddRange(entity.Select(b => Convert.ToString(b["fsoorderinterid"])));
           });
            soOrderId = soOrderId.Distinct().ToList();
            var salOrders = this.Context.LoadBizDataById("ydj_order", soOrderId);
            var salOrderForm = mate.LoadFormModel(this.Context, "ydj_order");
            refObjMgr?.Load(this.Context, salOrders.ToArray(), true, salOrderForm, new List<string> { "fstore" });


            string storehouseNumber = "";
            string _sql = $"select top 1 fid,fnumber from T_YDJ_STOREHOUSE with(nolock) where  fmainorgid='{this.Context.Company}' and fwarehousetype='warehouse_01' and fforbidstatus=0  and fcreatorid='sysadmin' order by fcreatedate asc";
            using (var dr = this.Context.ExecuteReader(_sql, null))
            {
                if (dr.Read())
                {
                    storehouseNumber = Convert.ToString(dr["fnumber"]);
                }
            }


            foreach (var orderItem in zyOrder)
            {
                var dto = BuildData(orderItem, salOrders, agentItem, storehouseNumber);
                var resp = MuSiApi.SendStkSoStockOut(this.Context, this.HtmlForm, dto);

                orderItem["fhqderdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                orderItem["fhqderstatus"] = "1";//已发送
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(zyOrder);
        }
        /// <summary>
        /// 构建数据包
        /// </summary>
        /// <param name="order"></param>
        /// <param name="fentry"></param>
        /// <returns></returns>
        private Dictionary<string, object> BuildData(DynamicObject order, List<DynamicObject> salOrders, DynamicObject agentItem, string storehouseNumber)
        {
            Dictionary<string, object> dicts = new Dictionary<string, object>();
            dicts.Add("zyInvNum", Convert.ToString(order["fbillno"]));
            dicts.Add("zyType", "ZY01");
            List<Dictionary<string, object>> entityDics = new List<Dictionary<string, object>>();
            var entitys = order["fentity"] as DynamicObjectCollection;
            foreach (var item in entitys)
            {
                var orderItem = salOrders.FirstOrDefault(a => Convert.ToString(a["id"]) == Convert.ToString(item["fsoorderinterid"]));
                if (orderItem == null)
                {
                    continue;
                }
                var ordEntry = orderItem["fentry"] as DynamicObjectCollection;
                var ordEntryItem = ordEntry.FirstOrDefault(a => Convert.ToString(a["id"]) == Convert.ToString(item["fsourceentryid"]));
                if (ordEntryItem == null) continue;

                var entityDict = new Dictionary<string, object>
                    {
                        { "zyUnit", Convert.ToString((item["funitid_ref"] as DynamicObject)?["fnumber"]) },
                        { "zyBatchNum", Convert.ToString(item["fmtono"]) },
                        //{ "zyInvLoc", Convert.ToString((orderItem["fstore_ref"] as DynamicObject)?["fnumber"]) },
                        //{ "zyInvLoc", Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fnumber"]) },
                        //{ "zyFactory", Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fnumber"]) },
                        { "zyFactory", agentItem["fnumber"]},
                        { "zyQty", Convert.ToString(item["fbizqty"]) },
                        { "zyProdCode", Convert.ToString((item["fmaterialid_ref"] as DynamicObject)?["fnumber"]) },
                        { "zyOrdLine",Convert.ToString(ordEntryItem["fseq"]) },
                        { "zyOrdId", Convert.ToString(item["fsoorderinterid"]) },
                        //{ "zyOrdNum", Convert.ToString(item["fsoorderno"]) },
                        { "zyItemLine", Convert.ToString(item["fseq"]) },
                        { "zyItemId", Convert.ToString(order["id"]) }
                    };
                //dicts.Add("zyOrdNum", Convert.ToString(orderItem["fheadquartno"]));//SAP合同号
                if (!dicts.ContainsKey("zyOrdNum"))
                {
                    dicts.Add("zyOrdNum", Convert.ToString(orderItem["fbillno"]));//SAP合同号
                }
                var warehousetype = Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fwarehousetype"]);
                if (warehousetype.Equals("warehouse_01"))//总仓，传仓库编码
                {
                    //entityDict.Add("zyInvLoc", Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fnumber"]));
                    entityDict.Add("zyInvLoc", storehouseNumber);
                }
                else if (warehousetype.Equals("warehouse_02"))//门店仓，传门店编码
                {
                    entityDict.Add("zyInvLoc", Convert.ToString((orderItem["fstore_ref"] as DynamicObject)?["fnumber"]));
                }
                else if (warehousetype.Equals("warehouse_04"))//售后仓，传门店编码
                {
                    var mulstore = Convert.ToString((item["fstorehouseid_ref"] as DynamicObject)?["fmulstore"]);
                    if (mulstore.IsNullOrEmptyOrWhiteSpace())
                    {
                        entityDict.Add("zyInvLoc", storehouseNumber);
                    }
                    else
                    {
                        entityDict.Add("zyInvLoc", Convert.ToString((orderItem["fstore_ref"] as DynamicObject)?["fnumber"]));
                    }
                }
                entityDics.Add(entityDict);
            }
            dicts.Add("entity", entityDics);
            return dicts;
        }

        //根据源单ID找到对应合同
        private DynamicObject GetOrderDm(string fbillno)
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));

            var where = "fbillno=@fbillno";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fbillno", System.Data.DbType.String, fbillno)
            };

            var reader = this.Context.GetPkIdDataReader(purForm, where, sqlParam);
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();
            return purOrder;
        }

        private bool CheckCanOutByFunreceived(DynamicObject newData, out string errorMsg)
        {
            bool canout = true;
            errorMsg = "";
            var sourenumber = Convert.ToString(newData?["fsourcenumber"]);
            var orderObj = this.Context.LoadBizDataByACLFilter("ydj_order", $"fbillno = '{sourenumber}' ").FirstOrDefault();
            if (orderObj != null)
            {
                var freceived = Convert.ToDecimal(orderObj?["funreceived"]);
                if (freceived < 0)
                {
                    canout = false;
                    errorMsg = $"对不起，上游销售合同【{sourenumber}】已经超额收款，管理员已控制不允许出库，请核查，谢谢！！";
                }
            }
            return canout;
        }

    }
}