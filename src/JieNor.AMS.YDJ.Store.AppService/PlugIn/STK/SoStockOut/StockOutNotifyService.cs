using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockOut
{
    [InjectService]
    public class StockOutNotifyService: IStockOutNotifyService
    {
        //public void Notify(UserContext userContext, DynamicObject[] argDataEntities, string notifyType)
        //{
        //    if (argDataEntities == null || argDataEntities.Length <= 0)
        //    {
        //        return;
        //    }
        //    var dataEntities = argDataEntities.Where(x => Convert.ToString(x["fsourcetype"]) == "ydj_order").ToList();

        //    var ftranids = dataEntities.Select(x => Convert.ToString(x["ftranid"])).Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();

        //    if (ftranids == null || ftranids.Count <= 0)
        //    {
        //        return;
        //    }

        //    //直营模式销售合同协同数据
        //    List<Dictionary<string, object>> orderSynData = new List<Dictionary<string, object>>();
        //    //独立模式采购订单下推采购入库单协同数据
        //    List<Dictionary<string, object>> purchaseSynData = new List<Dictionary<string, object>>();

        //    getSynDatas(userContext, "ydj_saleintention", "fentity", ftranids, dataEntities, orderSynData, purchaseSynData);
        //    getSynDatas(userContext, "ydj_order", "fentry", ftranids, dataEntities, orderSynData, purchaseSynData);

        //    var gateway = userContext.Container.GetService<IHttpServiceInvoker>();

        //    if (orderSynData != null && orderSynData.Count > 0)
        //    {
        //        //直营模式下，协同到门店，更新门店合同商品明细状态和出库数
        //        foreach (var item in orderSynData)
        //        {
        //            TargetSEP target = new TargetSEP(Convert.ToString(item["publishcid"]), Convert.ToString(item["publishpid"]));

        //            var response = gateway.Invoke(
        //                               userContext,
        //                               target,
        //                               new CommonBillDTO()
        //                               {
        //                                   FormId = "ydj_order",
        //                                   OperationNo = "stockoutnotify",
        //                                   ExecInAsync = false,
        //                                   AsyncMode = (int)Enu_AsyncMode.Background,
        //                                   SimpleData = new Dictionary<string, string>
        //                                   {
        //                                            { "orders",item["orders"].ToJson()},
        //                                            { "notifyType",notifyType}
        //                                   }
        //                               }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
        //                           ) as CommonBillDTOResponse;

        //            response.OperationResult.ThrowIfHasError(true, $"协同出库审核失败！");
        //        }
        //    }

        //    if (purchaseSynData != null && purchaseSynData.Count > 0)
        //    {
        //        //独立运营模式下，异步协同通知采购方下推生成采购入库单
        //        foreach (var item in purchaseSynData)
        //        {
        //            TargetSEP target = new TargetSEP(Convert.ToString(item["publishcid"]), Convert.ToString(item["publishpid"]));

        //            var response = gateway.Invoke(
        //                               userContext,
        //                               target,
        //                               new CommonBillDTO()
        //                               {
        //                                   FormId = "ydj_purchaseorder",
        //                                   OperationNo = "stockoutnotify",
        //                                   ExecInAsync = true,
        //                                   AsyncMode = (int)Enu_AsyncMode.Background,
        //                                   SimpleData = new Dictionary<string, string>
        //                                   {
        //                                            { "orders",item["orders"].ToJson()}
        //                                   }
        //                               }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
        //                           ) as CommonBillDTOResponse;

        //            response.OperationResult.ThrowIfHasError(true, $"协同出库审核失败！");
        //        }
        //    }
        //}

        //        private void getSynDatas(
        //            UserContext userContext,
        //            string formid,
        //            string detailid,
        //            List<string> ftranids,
        //            List<DynamicObject> dataEntities,
        //            List<Dictionary<string, object>> orderSynData,
        //            List<Dictionary<string, object>> purchaseSynData)
        //        {
        //            //根据ftranid找出协同合同或意向单
        //            var metaModelService = userContext.Container.GetService<IMetaModelService>();
        //            var htmlForm = metaModelService.LoadFormModel(userContext, formid);
        //            var dm = userContext.Container.GetService<IDataManager>();
        //            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

        //            StringBuilder sql = new StringBuilder();

        //            sql.AppendFormat(@"
        //select r.fid,c.foperationmode from {0} r
        //left join t_ydj_customer c on r.fmainorgid=c.fmainorgid and r.fpublishcid=c.fcoocompanyid and r.fpublishcid_pid=c.fcooproductid
        //where r.fmainorgid=@fmainorgid and r.fdataorigin='协同' and r.ftranid ", htmlForm.BillHeadTableName);

        //            List<SqlParam> sqlParams = new List<SqlParam>
        //            {
        //                new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company)
        //            };

        //            if (ftranids.Count == 1)
        //            {
        //                sql.Append(" = @ftranid");
        //                sqlParams.Add(new SqlParam("@ftranid", System.Data.DbType.String, ftranids[0]));
        //            }
        //            else
        //            {
        //                sql.Append(" in (");
        //                sql.Append(string.Join(",", ftranids.Select((x, i) => string.Format("@ftranid{0}", i))));
        //                sql.Append(")");
        //                sqlParams.AddRange(ftranids.Select((x, i) => new SqlParam(string.Format("@ftranid{0}", i), System.Data.DbType.String, x)));
        //            }

        //            Dictionary<string, string> operationModes = new Dictionary<string, string>();
        //            var dbService = userContext.Container.GetService<IDBService>();
        //            using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString(), sqlParams))
        //            {
        //                while (dataReader.Read())
        //                {
        //                    operationModes[dataReader.GetValueToString("fid")] = dataReader.GetValueToString("foperationmode");
        //                }
        //            }
        //            var dbEntities = dm.Select(operationModes.Keys).OfType<DynamicObject>().ToList();

        //            if (dbEntities == null || dbEntities.Count <= 0)
        //            {
        //                return;
        //            }

        //            foreach (var dataEntity in dataEntities)
        //            {
        //                var fentities = dataEntity["fentity"] as DynamicObjectCollection;
        //                if (fentities == null || fentities.Count <= 0)
        //                {
        //                    continue;
        //                }

        //                var dbEntity = dbEntities.FirstOrDefault(x => Convert.ToString(dataEntity["ftranid"]) == Convert.ToString(x["ftranid"]));
        //                if (dbEntity == null)
        //                {
        //                    continue;
        //                }

        //                var fentries = dbEntity[detailid] as DynamicObjectCollection;
        //                if (fentries == null && fentries.Count <= 0)
        //                {
        //                    continue;
        //                }

        //                var ls = operationModes[Convert.ToString(dbEntity["id"])] == "1" ? orderSynData : purchaseSynData;
        //                var publishcid = Convert.ToString(dbEntity["fpublishcid"]);
        //                var publishpid = Convert.ToString(dbEntity["fpublishcid_pid"]);
        //                var tranid = Convert.ToString(dbEntity["ftranid"]);

        //                var data = ls.FirstOrDefault(x => Convert.ToString(x["publishcid"]) == publishcid && Convert.ToString(x["publishpid"]) == publishpid);
        //                if (data == null)
        //                {
        //                    data = new Dictionary<string, object>
        //                        {
        //                            { "publishcid",publishcid},
        //                            { "publishpid",publishpid},
        //                            { "orders",new List<Dictionary<string,object>>()}
        //                        };
        //                    ls.Add(data);
        //                }
        //                var orders = data["orders"] as List<Dictionary<string, object>>;
        //                var order = orders.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == tranid);
        //                if (order == null)
        //                {
        //                    order = new Dictionary<string, object>
        //                    {
        //                        { "tranId",tranid},
        //                        { "entries",new List<Dictionary<string,object>>()}
        //                    };
        //                    orders.Add(order);
        //                }
        //                var entries = order["entries"] as List<Dictionary<string, object>>;

        //                foreach (var fentity in fentities)
        //                {
        //                    var fentity_ftrainid = Convert.ToString(fentity["ftranid"]);
        //                    var fentry = fentries.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == fentity_ftrainid);
        //                    if (fentry == null)
        //                    {
        //                        continue;
        //                    }
        //                    entries.Add(new Dictionary<string, object>
        //                        {
        //                            { "tranId",Convert.ToString(fentry["ftranid"]) },
        //                            { "qty",Convert.ToDecimal(fentity["fqty"])},
        //                            { "returnQty",Convert.ToDecimal(fentity["freturnqty"])},
        //                            { "planQty",Convert.ToDecimal(fentity["fplanqty"])}
        //                        });
        //                }
        //            }
        //        }

        public void Notify(UserContext userContext, DynamicObject[] argDataEntities, HtmlForm htmlForm, string notifyType)
        {
            if (argDataEntities == null || argDataEntities.Length <= 0)
            {
                return;
            }

            //出库单也可以由出库通知单上来
            //var ftranids = argDataEntities.Where(x => Convert.ToString(x["fsourcetype"]) == "ydj_order")
            //                              .Select(x => Convert.ToString(x["ftranid"])).Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();

            var ftranids = argDataEntities.Select(x => Convert.ToString(x["ftranid"])).Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();

            if (ftranids == null || ftranids.Count <= 0)
            {
                return;
            }

            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
            StringBuilder where = new StringBuilder("fmainorgid=@fmainorgid and fstatus='E' and ftranid ");
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company)
            };

            if (ftranids.Count == 1)
            {
                where.Append(" = @ftrainid");
                sqlParams.Add(new SqlParam("@ftrainid", System.Data.DbType.String, ftranids[0]));
            }
            else
            {
                where.Append(" in (");
                where.Append(string.Join(",", ftranids.Select((x, i) => string.Format("@ftranid{0}", i))));
                where.Append(")");
                sqlParams.AddRange(ftranids.Select((x, i) => new SqlParam(string.Format("@ftranid{0}", i), System.Data.DbType.String, x)));
            }

            var dataReader = userContext.GetPkIdDataReader(htmlForm, where.ToString(), sqlParams);
            var dataEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();

            //直营模式销售合同协同数据
            List<Dictionary<string, object>> orderSynData = new List<Dictionary<string, object>>();
            //独立模式采购订单下推采购入库单协同数据
            List<Dictionary<string, object>> purchaseSynData = new List<Dictionary<string, object>>();

            getSynDatas(userContext, "ydj_saleintention", "fentity", ftranids, dataEntities, orderSynData, purchaseSynData);
            getSynDatas(userContext, "ydj_order", "fentry", ftranids, dataEntities, orderSynData, purchaseSynData);

            var gateway = userContext.Container.GetService<IHttpServiceInvoker>();

            if (orderSynData != null && orderSynData.Count > 0)
            {
                //直营模式下，协同到门店，更新门店合同商品明细状态和出库数
                foreach (var item in orderSynData)
                {
                    TargetSEP target = new TargetSEP(Convert.ToString(item["publishcid"]), Convert.ToString(item["publishpid"]));

                    var response = gateway.Invoke(
                                       userContext,
                                       target,
                                       new CommonBillDTO()
                                       {
                                           FormId = "ydj_order",
                                           OperationNo = "stockoutnotify",
                                           ExecInAsync = false,
                                           AsyncMode = (int)Enu_AsyncMode.Background,
                                           SimpleData = new Dictionary<string, string>
                                           {
                                                    { "orders",item["orders"].ToJson()},
                                                    { "notifyType",notifyType}
                                           }
                                       }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
                                   ) as CommonBillDTOResponse;

                    response.OperationResult.ThrowIfHasError(true, $"协同出库审核失败！");
                }
            }

            if (purchaseSynData != null && purchaseSynData.Count > 0)
            {
                //独立运营模式下，异步协同通知采购方下推生成采购入库单
                foreach (var item in purchaseSynData)
                {
                    TargetSEP target = new TargetSEP(Convert.ToString(item["publishcid"]), Convert.ToString(item["publishpid"]));

                    var response = gateway.Invoke(
                                       userContext,
                                       target,
                                       new CommonBillDTO()
                                       {
                                           FormId = "ydj_purchaseorder",
                                           OperationNo = "stockoutnotify",
                                           ExecInAsync = true,
                                           AsyncMode = (int)Enu_AsyncMode.Background,
                                           SimpleData = new Dictionary<string, string>
                                           {
                                                    { "orders",item["orders"].ToJson()}
                                           }
                                       }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
                                   ) as CommonBillDTOResponse;

                    //response.OperationResult.ThrowIfHasError(true, $"协同出库审核失败！");
                }
            }
        }

        private void getSynDatas(
            UserContext userContext,
            string formid,
            string detailid,
            List<string> ftranids,
            List<DynamicObject> dataEntities,
            List<Dictionary<string, object>> orderSynData,
            List<Dictionary<string, object>> purchaseSynData)
        {
            //根据ftranid找出协同合同或意向单
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userContext, formid);
            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

            StringBuilder sql = new StringBuilder();

            sql.AppendFormat(@"
        select r.fid,c.foperationmode from {0} r
        left join t_ydj_customer c on r.fmainorgid=c.fmainorgid and r.fpublishcid=c.fcoocompanyid and r.fpublishcid_pid=c.fcooproductid
        where r.fmainorgid=@fmainorgid and r.fdataorigin='协同' and r.ftranid ", htmlForm.BillHeadTableName);

            List<SqlParam> sqlParams = new List<SqlParam>
                    {
                        new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company)
                    };

            if (ftranids.Count == 1)
            {
                sql.Append(" = @ftranid");
                sqlParams.Add(new SqlParam("@ftranid", System.Data.DbType.String, ftranids[0]));
            }
            else
            {
                sql.Append(" in (");
                sql.Append(string.Join(",", ftranids.Select((x, i) => string.Format("@ftranid{0}", i))));
                sql.Append(")");
                sqlParams.AddRange(ftranids.Select((x, i) => new SqlParam(string.Format("@ftranid{0}", i), System.Data.DbType.String, x)));
            }

            Dictionary<string, string> operationModes = new Dictionary<string, string>();
            var dbService = userContext.Container.GetService<IDBService>();
            using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString(), sqlParams))
            {
                while (dataReader.Read())
                {
                    operationModes[dataReader.GetValueToString("fid")] = dataReader.GetValueToString("foperationmode");
                }
            }
            var dbEntities = dm.Select(operationModes.Keys).OfType<DynamicObject>().ToList();

            if (dbEntities == null || dbEntities.Count <= 0)
            {
                return;
            }

            foreach(var dbEntity in dbEntities)
            {
                var fentries = dbEntity[detailid] as DynamicObjectCollection;
                if (fentries == null && fentries.Count <= 0)
                {
                    continue;
                }

                var ls = operationModes[Convert.ToString(dbEntity["id"])] == "1" ? orderSynData : purchaseSynData;
                var publishcid = Convert.ToString(dbEntity["fpublishcid"]);
                var publishpid = Convert.ToString(dbEntity["fpublishcid_pid"]);
                var tranid = Convert.ToString(dbEntity["ftranid"]);

                var data = ls.FirstOrDefault(x => Convert.ToString(x["publishcid"]) == publishcid && Convert.ToString(x["publishpid"]) == publishpid);
                if (data == null)
                {
                    data = new Dictionary<string, object>
                                        {
                                            { "publishcid",publishcid},
                                            { "publishpid",publishpid},
                                            { "orders",new List<Dictionary<string,object>>()}
                                        };
                    ls.Add(data);
                }
                var orders = data["orders"] as List<Dictionary<string, object>>;
                var order = orders.FirstOrDefault(x => Convert.ToString(x["ftranid"]) == tranid);
                if (order == null)
                {
                    order = new Dictionary<string, object>
                                    {
                                        { "tranId",tranid},
                                        { "entries",new List<Dictionary<string,object>>()}
                                    };
                    orders.Add(order);
                }
                var entries = order["entries"] as List<Dictionary<string, object>>;

                var fentities = dataEntities?.Where(x => tranid == Convert.ToString(x["ftranid"])).SelectMany(x=> x["fentity"] as DynamicObjectCollection).ToList();

                foreach(var fentry in fentries)
                {
                    var ftranid = Convert.ToString(fentry["ftranid"]);
                    var tranEntities = fentities?.Where(x => Convert.ToString(x["ftranid"]) == ftranid).ToList();
                    decimal qty = 0;
                    decimal returnQty = 0;
                    decimal planQty = 0;

                    if (tranEntities != null && tranEntities.Count > 0)
                    {
                        qty = tranEntities.Sum(x => Convert.ToDecimal(x["fqty"]));
                        returnQty = tranEntities.Sum(x => Convert.ToDecimal(x["freturnqty"]));
                        planQty = Convert.ToDecimal(tranEntities.FirstOrDefault()["fplanqty"]);
                    }

                    entries.Add(new Dictionary<string, object>
                                        {
                                            { "tranId",ftranid },
                                            { "qty",qty},
                                            { "returnQty",returnQty},
                                            { "planQty",planQty}
                                        });
                }
            }
        }
    }
}
