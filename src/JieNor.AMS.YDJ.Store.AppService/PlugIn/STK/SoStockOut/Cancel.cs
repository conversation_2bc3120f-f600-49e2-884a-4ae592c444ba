using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockOut
{
    /// <summary>
    /// 销售出库单：作废
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("Cancel")]
    public class Cancel : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            DirectHelper.Cancel(this.Context, e.DataEntitys);
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //base.EndOperationTransaction(e);
            this.Result.IsSuccess = false;
            var notifyService = this.Context.Container.GetService<IStockOutNotifyService>();
            notifyService.Notify(this.Context, e.DataEntitys, this.HtmlForm, this.OperationNo);

            var svc = this.Container.GetService<IReserveUpdateService>();
            var opResult = svc.DeleteReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);
            this.Result.MergeResult(opResult);

            this.Result.IsSuccess = true;


            var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            var ids = entitys.Select(o => Convert.ToString(o?["Id"])).ToList();
            // 反写销售合同【已推出库数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackTransOutQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);
        }
    }
}
