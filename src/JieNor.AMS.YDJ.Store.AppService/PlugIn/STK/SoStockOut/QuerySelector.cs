using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut
{
    /// <summary>
    /// 销售出库单：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("QuerySelector")]
    public class QuerySelector : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            // 只限于销售合同
            var targetFormId = this.GetQueryOrSimpleParam("targetFormId", "");
            if (!targetFormId.EqualsIgnoreCase("ydj_order")) return;

            var orderFileter = string.Empty;

            // 当存在源单时，只能选择当前源单的数据
            var sourceNumber = this.GetQueryOrSimpleParam("sourceNumber", "");
            if (!sourceNumber.IsNullOrEmptyOrWhiteSpace())
            {
                orderFileter += $" and o.fbillno='{sourceNumber}' ";
            }

            /*
            1、当销售出库单.客户不为空时
            a、点<选单>，进入《销售合同-列表》，需要自动匹配筛选 “ 销售合同的【客户】等于销售出库单所选择的当前客户 且 合同商品明细行的【销售数量】大于 源单合同商品明细已关联销售出库明细的【实发数量】汇总 ” 这一类销售合同数据范围供选择；
            b、当销售出库单重新选择客户，需要直接清空原已选择的所有源单信息；
            5、当销售出库单.客户为空时，点<选单>，进入《销售合同-列表》，需要自动匹配筛选 “ 合同商品明细行的【销售数量】大于 源单合同商品明细已关联销售出库明细的【实发数量】汇总 ” 这一类销售合同数据范围供选择。 
            6、销售出库单选单合同的时候，列表显示的订单剔除{【焕新订单标记】=是 并且【结算进度】!=已收款}的订单。
            任务链接：http://dmp.jienor.com:81/zentao/task-view-29340.html
            */
            var fcustomerid = this.GetQueryOrSimpleParam("fcustomerid", "");
            if (!fcustomerid.IsNullOrEmptyOrWhiteSpace())
            {
                orderFileter += $" and o.fcustomerid='{fcustomerid}' ";
            }

            orderFileter += $" and (o.frenewalflag='0' or o.fsettlprogress='{Enu_RenewalSettleProgress.已收款}') ";

            if (this.Context.IsDirectSale)
            {
                orderFileter += $" and o.fchstatus='3' AND o.fpiecesendtag='0' ";
            }

            string filterString = $@" 
fid in 
(
    select oe.fid from t_ydj_order o with(nolock)
    inner join t_ydj_orderentry oe with(nolock) on o.fid=oe.fid and o.fcancelstatus='0' {orderFileter}
    left join (
	    select fsoorderinterid,fsoorderentryid,SUM(fbizqty) as fbizqty from t_stk_sostockout so with(nolock)
	    inner join t_stk_sostockoutentry soe with(nolock) on so.fid=soe.fid
	    where so.fcancelstatus='0'
	    group by fsoorderinterid,fsoorderentryid
    ) soe on oe.fid=soe.fsoorderinterid and oe.fentryid=soe.fsoorderentryid
    where oe.fbizqty+ oe.fbizreturnqty> ISNULL(soe.fbizqty,0) and oe.fdeliverytype in ('','delivery_type_02') 
)";

            this.SimpleData["filterString"] = filterString;
        }
    }
}
