using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut
{
    /// <summary>
    /// 销售出库单：新增
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    this.ProcAfterCreateUiData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理UI数据打包逻辑
        /// </summary>
        /// <param name="e"></param>
        private void ProcAfterCreateUiData(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as JObject;
            if (eventData == null) return;

            //根据单据类型参数设置合同交货日期（基于创建日期往后推多少天）
            var billTypeService = this.Container.GetService<IBillTypeService>();
            var billTypeId = eventData["fbilltype"].GetJsonValue<string>("id");
            var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, billTypeId);
            if (paramSetObj != null)
            {
                if (int.TryParse(Convert.ToString(paramSetObj["fday"]), out var deliveryDays))
                {
                    if (!DateTime.TryParse(Convert.ToString(eventData["fcreatedate"]), out var fcreatedate))
                    {
                        fcreatedate = DateTime.Now;
                    }

                    eventData["fdate"] = fcreatedate.Date.AddDays(deliveryDays);
                }
            }
        }
    }
}