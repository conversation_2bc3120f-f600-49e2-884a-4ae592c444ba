using System;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.SoStockOut
{




    /// <summary>
    /// 销售出库单保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary> 
    public class UpdateReserveBill : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if(e.DataEntitys==null)
            {
                return;
            }
            List<string> sql = new List<string>();
            foreach (var item in e.DataEntitys)
            {
                sql.Add(@"/*dialect*/ update t set fsourcestatus = x.fstatus
                            from t_stk_reservebill as t 
                            inner join t_stk_sostockout x on x.fid='{0}' and t.fsourcepkid = x.fid and t.fsourcetype ='stk_sostockout' ".Fmt(item["Id"]));

            }

            var dbSvc = this.Container.GetService<IDBServiceEx>();
            dbSvc.ExecuteBatch(this.Context, sql);
        }


    }



    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("Save")]
    public class OrderSave : UpdateReserveBill
    { 
    }



    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("Submit")]
    public class OrderSubmit : UpdateReserveBill
    {
    }


    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("UnSubmit")]
    public class OrderUnSubmit : UpdateReserveBill
    {
    }


    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("Audit")]
    public class OrderAudit : UpdateReserveBill
    {
    }

    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("stk_sostockout")]
    [OperationNo("UnAudit")]
    public class OrderUnAudit : UpdateReserveBill
    {
    }

}