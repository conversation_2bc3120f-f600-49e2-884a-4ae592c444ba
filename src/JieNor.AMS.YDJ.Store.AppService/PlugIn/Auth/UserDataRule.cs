using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.PermData;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    /// <summary>
    /// 【用户】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    public class UserListTree : IDataQueryRule
    {
        /// <summary>
        /// 用户所属组织是总部，则可以看到所有的系统用户信息;
        /// 其他情况，按组织架构树来过滤
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();
            //总部用户
            if (ctx.IsTopOrg)
            {
                //总部自己建的用户
                filter.Add(new FilterRowObject()
                {
                    Id = "fmainorgid",
                    Operator = "=",
                    Value = ctx.Company,
                });
                
                // 是否来源于角色授权
                var fromAssignright = (rulePara.SrcPara?.GetValue("fromFormId") ?? "").EqualsIgnoreCase("sec_assignright");

                // 是否来源于主控菜单
                var fromMainFw = !(rulePara.SrcPara?.GetValue("mainFwMenuId") ?? "").IsNullOrEmptyOrWhiteSpace();

                // 总部视角下要显示经销商的管理员用户
                if (fromAssignright || fromMainFw)
                {
                    //var sql = @"  select distinct a.fid as FPKId
                    //    from T_SEC_user a with(nolock)
                    //    inner join T_BAS_agent b with(nolock) on a.fnumber =b.fnumber or a.fnumber =b.fcontacterphone 
                    //    where b.fmainorgid='{0}'  ".Fmt( ctx.Company);
                    var sql = @"
SELECT a.fid fpkid FROM t_sec_user a WITH (NOLOCK) 
INNER JOIN t_sec_roleuser b WITH (NOLOCK) ON a.fid=b.fuserid
inner join t_sec_role c with(nolock) on b.froleid=c.fid 
WHERE c.fmainorgid = '{0}' and c.fnumber='{1}'
".Fmt(ctx.Company, "Admin_Agent");
                    filter.Add(new FilterRowObject()
                    {
                        Id = "fid",
                        Operator = "exists",
                        Value = sql,
                        Logic = "or",
                    });
                }
                
                return filter;
            }
              
            //经销商、门店用户
            filter.Add(new FilterRowObject()
            {
                Id = "fmainorgid",
                Operator = "=",
                Value =  ctx.Company ,
            });

            var permiSvc = ctx.Container.GetService<IPermissionService>();
            var isDevOpsUser = permiSvc.IsDevOpsUser(ctx, new PermAuth(ctx));
            if (!isDevOpsUser)
            {
                //非运维人员的，列表不显示运维人员
                filter.Add(new FilterRowObject()
                {
                    Id = "fisdevops",
                    Operator = "=",
                    Value = "0",
                });
            }

            return filter;
        }



    }
}