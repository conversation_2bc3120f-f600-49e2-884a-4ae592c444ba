using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.Interface;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{

    /// <summary>
    /// 【招商经销商】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("ms_crmdistributor")]
    public class CrmAgentDataRule : IDataQueryRule
    {
        /// <summary>
        ///   
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();

            //总部用户，可以看所有数据，不加额外的过滤
            if (ctx.IsTopOrg)
            {
                //filter.Add(new FilterRowObject()
                //{
                //    Id = "ftopcompanyid",
                //    Operator = "=",
                //    Value = ctx.TopCompanyId,
                //});
                return filter;
            }
            var sqlTest = $@"/*dialect*/select b.fid from t_ms_crmdistributor b
                            inner join t_bas_macentry me on b.fagentid like '%'+me.fsubagentid+'%'
                            inner join t_bas_mac m on m.fid=me.fid
                            where m.fmainagentid=@fmainorgid and m.fforbidstatus='0'
                            union all
                            select b.fid from t_ms_crmdistributor b where b.fagentid like '%'+@fmainorgid+'%'";
            List<SqlParam> param = new List<SqlParam>();
            param.Add(new SqlParam("@fmainorgid", DbType.String, ctx.Company));
            List<string> idList = ctx.ExecuteDynamicObject(sqlTest, param)?.Select(x => x["fid"].ToString()).Distinct().ToList();
            if (idList != null && idList.Any())
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fid",
                    Operator = "in",
                    Value = string.Join("','", idList)
                });
            }
            else
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fid",
                    Operator = "in",
                    Value = "''"
                });
            }
            return filter;
        }


    }
}