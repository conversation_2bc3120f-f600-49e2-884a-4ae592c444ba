using JieNor.AMS.YDJ.Core;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    /// <summary>
    /// 【价目表】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("ydj_price|ydj_selfprice")]
    public class PriceDataRule : IDataQueryRule
    {
        
        /// <summary>
        /// 用户所属组织是总部，则可以看到总部的商品信息;
        /// 用户所属组织是分公司，按 商品上维护的使用组织进行隔离
        /// 用户是经销商或门店的，可以看到的商品信息= 总部授权商品 + 自己建的商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();
            filter.Add(new FilterRowObject
            {
                Id = "ftype",
                Operator = "=",
                Value = "quote_type_01"
            });
            var orgInfos = ProductDataIsolateHelper.GetCurrentUserOrgInfos(ctx);
            if (orgInfos == null || orgInfos.Count == 0)
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fid",
                    Operator = "=",
                    Value = "0",
                });
                return filter;
            }

            var tempView = ctx.GetAuthProductDataPKID(rulePara);
            filter.Add(new FilterRowObject()
            {
                Id = "fproductid",
                Operator = "exists",
                Value = tempView,
            });

            //自建商品价目只显示自建商品的销售价目
            if (rulePara.FormId.ToLower().EqualsIgnoreCase("ydj_selfprice"))
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fproductid",
                    Operator = "exists",
                    Value = $"SELECT fid as FPKId  FROM dbo.T_BD_MATERIAL with(nolock) WHERE T_BD_MATERIAL.fmainorgid ='{ctx.Company}'",
                });
            }
            //只显示总部商品的销售价目
            else
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fproductid",
                    Operator = "exists",
                    Value = $"SELECT fid as FPKId  FROM dbo.T_BD_MATERIAL with(nolock) WHERE T_BD_MATERIAL.fmainorgid ='{ctx.TopCompanyId}'",
                });
            }

            return filter;
        }

    }
}