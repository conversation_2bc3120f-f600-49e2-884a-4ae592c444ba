using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{


    [InjectService]
    public   class PermissionEvent : IPermissionEvent
    {
        /// <summary>
        /// 系统运维角色的用户，自动更新到经销商组织下，以便运维人员可以看到所有组织数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleACLDetail"></param>
        public  void OnSaveRolePermission(UserContext ctx, RolePermitInfo roleACLDetail)
        {
            if(roleACLDetail.RoleNumber.IsNullOrEmptyOrWhiteSpace ())
            {
                var dbSvc = ctx.Container.GetService<Framework.Interface.IDBService>();
                var sql = "select fnumber,fname from t_sec_role where fid='{0}' ".Fmt(roleACLDetail.RoleId);
                var roleObj = dbSvc.ExecuteDynamicObject(ctx, sql)?.FirstOrDefault();
                if(roleObj !=null )
                {
                    roleACLDetail.RoleNumber = Convert.ToString(roleObj["fnumber"]);
                    roleACLDetail.RoleName = Convert.ToString(roleObj["fname"]);
                }
            }

            //系统运维人员分发到各个经销商下
            if(roleACLDetail.RoleNumber.EqualsIgnoreCase ("Admin_DevOps"))
            {
                var result = DataAuthHelp.AddOrUpdateDevOpsUserInfo(ctx, roleACLDetail.RoleUsers,true);
            }

            //经销商管理员：之前经销商已经分发给下面角色的权限的，当前不给权限了，则经销商下面的角色的权限要收回来
            if (roleACLDetail.RoleNumber.EqualsIgnoreCase("Admin_Agent"))
            {
                DataAuthHelp.TakeBackPermission(ctx, roleACLDetail.RoleUsers);
            }
            
        }


    }



}
