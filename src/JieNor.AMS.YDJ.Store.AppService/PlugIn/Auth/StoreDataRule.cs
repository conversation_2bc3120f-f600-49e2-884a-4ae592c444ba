using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.AMS.YDJ.Core;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    /// <summary>
    /// 【门店】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("bas_store")]
    public class StoreDataRule : IDataQueryRule
    {


        /// <summary>
        /// 用户所属组织是总部，则可以看到所有的门店信息;
        /// 其他情况，按组织架构树来过滤
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();
            //总部用户，可以看所有数据，不加额外的过滤
            if (ctx.IsTopOrg)
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fmainorgid",
                    Operator = "=",
                    Value = ctx.TopCompanyId,
                }); ;
                return filter;
            }

            if(ctx.UserId == "sysadmin")
            {
                return filter;
            }
            var orgInfos = ProductDataIsolateHelper.GetCurrentUserOrgInfos(ctx);             
            //总部或分公司用户，可以看所有数据，不加额外的过滤
            if (orgInfos.Any (f=>"1".EqualsIgnoreCase ( f.OrgType) || "2".EqualsIgnoreCase(f.OrgType)))
            { 
                return filter;
            }

            //TODO 经销商或门店的用户，看自己的（经销商、门店生成组织时，组织编码跟经销商、门店编码一致，可以用编码做过滤）
            var orgIds = orgInfos.Select(f => f.OrgId).Distinct().ToList();
            orgIds.Add("##");
            filter.Add(new FilterRowObject()
            {
                Id = "fagentid",
                Operator = "in",
                Value = string.Join (",", orgIds),
            });

            return filter;
        }



    }
}