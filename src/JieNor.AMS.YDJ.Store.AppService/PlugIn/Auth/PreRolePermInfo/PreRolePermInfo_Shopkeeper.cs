using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Report;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    public partial class PreRolePermInfo
    {
        /// <summary>
        /// 经销商角色--店长数据权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<DataRowAuthInfo>>> GetAgentDataRowAuth_Shopkeeper()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
             select distinct fbizobjid ,ffiltertype,'"' + fbizobjid + '|' + ffiltertype + '|' + fexpress + '|'+ fdesc + '|' + fsuperiorperm  + '|' + fbdfldfilter  + '|' + fbdfldfilter_txt    +  '",'  
             from t_sec_roledatarowacl 
             where fcompanyid = '企业id' and froleId = '角色id'
             order by  fbizobjid ,ffieldid
             */

            var fldItems = new List<string>()
            {
                "stk_inventorybase|fmyandsubdepartment|fstockdeptid|基本信息.盘点部门|1| | ",
                "stk_inventoryverify|fmyandsubdepartment|fstockdeptid|基本信息.盘点部门|1| | ",
                "stk_otherstockin|fmyandsubdepartment|fstockdeptid|基本信息.收货部门|1| | ",
                "stk_otherstockout|fmyandsubdepartment|fdeptid|基本信息.领用部门|1| | ",
                "stk_reservebill|fcurrentuser|fcreatorid|预留单.创建人|1| | ",
                "stk_sostockout|fmyandsubdepartment|fsodeptid|基本信息.门店名称|1| | ",
                "ydj_customer|fmyandsubdepartment|fdeptid|客户负责人.所属部门|1| | ",
                "ydj_followerrecord|fmyandsubdepartment|fdeptid| |1| | ",
                "ydj_order|fmyandsubdepartment|fdeptid| |1| | ",
                "ydj_order_chg|fmyandsubdepartment|fdeptid_chg| |1| | ",
                "ydj_purchaseorder_chg| | | |0| | ",
                "ydj_productpromotion|fmydepartment|fdeptid|促销门店.门店名称|1| | ",
                "ydj_combopromotion|fmydepartment|fdeptid|促销门店.门店名称|1| | ",
            };
            var result = GetDataRowPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--店长字段权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：字段标识，item3：字段名称，item4：是否可见，item5：是否可修改 </returns>
        public static List<Tuple<string, List<FieldAuthInfo>>> GetAgentRoleFldPerm_Shopkeeper()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct fbizobjid ,ffieldid,'"' + fbizobjid + ';' + ffieldid + ';' + ffieldname + ';'+ fvisible + ';' + fmodify  +  '",'  
            from t_sec_rolefieldacl  where fcompanyid = '企业id' and froleId = '角色id'
            order by  fbizobjid ,ffieldid
            */
            var fldItems = new List<string>()
            {
"rpt_purchaseprice;fpurprice;采购价;0;1",

"ydj_purchaseorder;factrefundamount;实退金额;0;1",
"ydj_purchaseorder;famount;金额;0;1",
"ydj_purchaseorder;fconfirmamount;待确认金额;0;1",
"ydj_purchaseorder;fdealamount;货品原值;0;1",
"ydj_purchaseorder;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder;fdealprice;成交单价;0;1",
"ydj_purchaseorder;fdepthdiscount;深度护理折扣;0;1",
"ydj_purchaseorder;fdistamount;折扣额;0;1",
"ydj_purchaseorder;fdistamount_e;折扣额;0;1",
"ydj_purchaseorder;fdistrate;折扣率;0;1",
"ydj_purchaseorder;fexpenserebate;费用支持返利;0;1",
"ydj_purchaseorder;ffbillamount;成交金额;0;1",
"ydj_purchaseorder;fnewdiscount;新品折扣;0;1",
"ydj_purchaseorder;fotherdiscount;其他折扣;0;1",
"ydj_purchaseorder;fpaidamount;已结算金额;0;1",
"ydj_purchaseorder;fpayamount;待结算金额;0;1",
"ydj_purchaseorder;fpinvoicemount;开票金额;0;1",
"ydj_purchaseorder;fprice;采购单价;0;1",
"ydj_purchaseorder;frefundamount;申请退货金额;0;1",
"ydj_purchaseorder;fsapdiscount;SAP折扣总额;0;1",
"ydj_purchaseorder;fsettleamount;已付金额;0;1",
"ydj_purchaseorder;fstardiscount;星级折扣/上市折扣;0;1",
"ydj_purchaseorder;ftaxprice;含税出厂价;0;1",

"ydj_purchaseorder_chg;factrefundamount;实退金额;0;1",
"ydj_purchaseorder_chg;famount;金额;0;1",
"ydj_purchaseorder_chg;famount_chg;金额(新);0;1",
"ydj_purchaseorder_chg;fconfirmamount;待确认金额;0;1",
"ydj_purchaseorder_chg;fdealamount;货品原值;0;1",
"ydj_purchaseorder_chg;fdealamount_chg;货品原值;0;1",
"ydj_purchaseorder_chg;fdealamount_e;成交金额;0;1",
"ydj_purchaseorder_chg;fdealamount_e_chg;成交金额(新);0;1",
"ydj_purchaseorder_chg;fdealprice;成交单价;0;1",
"ydj_purchaseorder_chg;fdealprice_chg;成交单价(新);0;1",
"ydj_purchaseorder_chg;fdepthdiscount;深度护理折扣;0;1",
"ydj_purchaseorder_chg;fdistamount;折扣额;0;1",
"ydj_purchaseorder_chg;fdistamount_e;折扣额;0;1",
"ydj_purchaseorder_chg;fdistamount_e_chg;折扣额(新);0;1",
"ydj_purchaseorder_chg;fdistrate;折扣率;0;1",
"ydj_purchaseorder_chg;fdistrate_chg;折扣(新);0;1",
"ydj_purchaseorder_chg;fexpenserebate;费用支持返利;0;1",
"ydj_purchaseorder_chg;ffbillamount;成交金额;0;1",
"ydj_purchaseorder_chg;ffbillamount_chg;订单金额(新);0;1",
"ydj_purchaseorder_chg;fnewdiscount;新品折扣;0;1",
"ydj_purchaseorder_chg;fotherdiscount;其他折扣;0;1",
"ydj_purchaseorder_chg;fpaidamount;已结算金额;0;1",
"ydj_purchaseorder_chg;fpayamount;待结算金额;0;1",
"ydj_purchaseorder_chg;fpayamount_chg;待结算金额(新);0;1",
"ydj_purchaseorder_chg;fpinvoicemount;开票金额;0;1",
"ydj_purchaseorder_chg;fprice;采购单价;0;1",
"ydj_purchaseorder_chg;frefundamount;申请退货金额;0;1",
"ydj_purchaseorder_chg;fsapdiscount;SAP折扣总额;0;1",
"ydj_purchaseorder_chg;fsettleamount;已付金额;0;1",
"ydj_purchaseorder_chg;fstardiscount;星级折扣/上市折扣;0;1",
"ydj_purchaseorder_chg;ftaxprice;含税出厂价;0;1",
"ydj_purchaseorder_chg;fprice_chg;单价(新);0;1",

"rpt_stocksynthesize;fcostamt;成本;0;1",
"rpt_stocksynthesize;fcostprice;成本价;0;1",

"stk_inventorybalance;fcostamt;成本;0;1",
"stk_inventorybalance;fcostprice;成本价;0;1",
"stk_inventorybalance;finicostamt;期初总成本;0;1",
"stk_inventorybalance;finicostprice;期初成本价;0;1",

"stk_inventorylist;fcostamt;成本;0;1",
"stk_inventorylist;fcostprice;成本价;0;1",

"stk_inventorytransfer;fcostamt;总成本(加权平均);0;1",
"stk_inventorytransfer;fcostprice;单位成本(加权平均);0;1",

"stk_inventoryverify;fcostamt;总成本(加权平均);0;1",
"stk_inventoryverify;fcostprice;单位成本(加权平均);0;1",
"stk_inventoryverify;fbizpdprice;盘点单价;0;1",
"stk_inventoryverify;fpdprice;基本单位盘点单价;0;1",
"stk_inventoryverify;fbizprice;账存单价;0;1",
"stk_inventoryverify;fprice;基本单位账存单价;0;1",
"stk_inventoryverify;famount;账存金额;0;1",
"stk_inventoryverify;fpdamount;盘点金额;0;1",
"stk_inventoryverify;fpyamount;盘盈金额;0;1",
"stk_inventoryverify;fpkamount;盘亏金额;0;1",
"stk_inventoryverify;fbugunitprice;采购单价(折前);0;1",
"stk_inventoryverify;fpkbuyamount;盈亏采购总额;0;1",

"stk_otherstockin;fcostamt;总成本(加权平均);0;1",
"stk_otherstockin;fcostprice;单位成本(加权平均);0;1",

"stk_otherstockout;fcostamt;总成本(加权平均);0;1",
"stk_otherstockout;fcostprice;单位成本(加权平均);0;1",

"stk_postockin;fcostamt;总成本(加权平均);0;1",
"stk_postockin;fcostprice;单位成本(加权平均);0;1",
"stk_postockin;famount;成交金额;0;1",
"stk_postockin;fpoamount;金额;0;1",
"stk_postockin;fpoprice;采购单价;0;1",
"stk_postockin;fprice;成交单价;0;1",

"stk_postockreturn;fcostamt;总成本(加权平均);0;1",
"stk_postockreturn;fcostprice;单位成本(加权平均);0;1",
"stk_postockreturn;famount;成交金额;0;1",
"stk_postockreturn;fpoamount;金额;0;1",
"stk_postockreturn;fpoprice;采购单价;0;1",
"stk_postockreturn;fprice;成交单价;0;1",

"stk_sostockout;fcostamt;总成本(加权平均);0;1",
"stk_sostockout;fcostprice;单位成本(加权平均);0;1",
"stk_sostockout;fpurfacamount;采购折前金额;0;1",
"stk_sostockout;fpurfacprice;采购单价（折前）;0;1",

"stk_sostockreturn;fcostamt;总成本(加权平均);0;1",
"stk_sostockreturn;fcostprice;单位成本(加权平均);0;1",

"ydj_order;fcost;成本金额;0;1",
"ydj_order;fcostprice;成本价;0;1",
"ydj_order;ftransfercostprice;成本价格;0;1",
"ydj_order;ftargetagentamount;发货结算金额;0;1",
"ydj_order;fotherfee;其他费用;0;1",
"ydj_order;fpurfacamount;采购折前金额;0;1",
"ydj_order;fpurfacprice;采购单价（折前）;0;1",

"ydj_order_chg;fcost;成本金额;0;1",
"ydj_order_chg;fcostprice;成本价;0;1",
"ydj_order_chg;fpurfacamount;采购折前金额;0;1",
"ydj_order_chg;fpurfacamount_chg;采购折前金额（新）;0;1",
"ydj_order_chg;fpurfacprice;采购单价（折前）;0;1",
"ydj_order_chg;fpurfacprice_chg;采购单价（折前）（新）;0;1",
"ydj_order_chg;ftransfercostprice;成本价格;0;1",

"ydj_transferorderapply;fcostprice;成本价格;0;1",

"ydj_purchaseprice;fpurprice;采购价;0;1",

"ydj_stockoutreport;fcost;单位成本;0;1",
"ydj_stockoutreport;fcostamt;总成本;0;1",

"rpt_orderdetail;fcostprice;单位成本;0;1",
"rpt_orderdetail;fcost;总成本;0;1",

"rpt_orderbalance;fcostprice;单位成本;0;1",
"rpt_orderbalance;fcost;总成本;0;1",

"rpt_stocksynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_stocksynthesize;fpurfacamount;采购折前金额;0;1",
"rpt_stocksynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_stocksynthesize;fpurdealamount;采购折后金额;0;1",
"rpt_stocksynthesize;funifysaleprice;统一零售价（折前）;0;1",
"rpt_stocksynthesize;funifysaleamount;统一零售金额;0;1",
"rpt_stocksynthesize;fsellprice;经销价（折前）;0;1",
"rpt_stocksynthesize;fsellamount;经销金额;0;1",
"rpt_stocksynthesize;freprice;分销价（折前）;0;1",
"rpt_stocksynthesize;freamount;分销金额;0;1",
"rpt_stocksynthesize;fterprice;终端零售价（折前）;0;1",
"rpt_stocksynthesize;fteramount;终端零售金额;0;1",

"rpt_pricesynthesize;fpurfacprice;采购单价（折前）;0;1",
"rpt_pricesynthesize;fpurdealprice;采购单价（折后）;0;1",
"rpt_pricesynthesize;freprice;分销价（折前）;0;1",
"rpt_pricesynthesize;funitcostprice;单位成本;0;1",

"rpt_stockageanalysis;fpurfacprice;采购单价(折前);0;1",
"rpt_stockageanalysis;fpurfacamount;采购折前金额;0;1",
"rpt_stockageanalysis;fpurdealprice;采购单价（折后）;0;1",
"rpt_stockageanalysis;fpurdealamount;采购折后金额;0;1",
"rpt_stockageanalysis;freprice;分销价（折前）;0;1",
"rpt_stockageanalysis;freamount;分销金额;0;1",
"rpt_stockageanalysis;funitcostprice;单位成本价;0;1",
"rpt_stockageanalysis;funitcostamount;总成本;0;1",

"rpt_orderbalance;fpurfacprice;采购单价(折前);0;1",
"rpt_orderbalance;fpurfacamount;采购折前金额;0;1",
"rpt_orderbalance;fpurdealprice;采购单价（折后）;0;1",
"rpt_orderbalance;fpurdealamount;采购折后金额;0;1",

"ydj_stockoutreport;fpurfacprice;采购单价(折前);0;1",
"ydj_stockoutreport;fpurfacamount;采购折前金额;0;1",
"ydj_stockoutreport;fpurdealprice;采购单价（折后）;0;1",
"ydj_stockoutreport;fpurdealamount;采购折后金额;0;1",

"rpt_purchasedetail;fdealamount;金额;0;1",
"rpt_purchasedetail;fdealprice;单价;0;1",

"rpt_stockdetail;fincostamt;收入总成本(加权平均);0;1",
"rpt_stockdetail;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stockdetail;fpoprice;采购单价(折前);0;1",
"rpt_stockdetail;fpopriceafter;采购单价(折后);0;1",
"rpt_stockdetail;fstockintotal;收入总成本(折前);0;1",
"rpt_stockdetail;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stockdetail;fstockouttotal;发出总成本(折前);0;1",
"rpt_stockdetail;fstockouttotalafter;发出总成本(折后);0;1",

"rpt_stocksummary;fincostamt;收入总成本(加权平均);0;1",
"rpt_stocksummary;foutcostamt;发出总成本(加权平均);0;1",
"rpt_stocksummary;fpoprice;采购单价(折前);0;1",
"rpt_stocksummary;fpopriceafter;采购单价(折后);0;1",
"rpt_stocksummary;fstockintotal;收入总成本(折前);0;1",
"rpt_stocksummary;fstockintotalafter;收入总成本(折后);0;1",
"rpt_stocksummary;fstockouttotal;发出总成本(折前);0;1",
"rpt_stocksummary;fstockouttotalafter;发出总成本(折后);0;1",

            };
            var result = GetFieldPermItems(fldItems);

            return result;
        }

        /// <summary>
        /// 经销商角色--店长权限
        /// </summary>
        /// <returns>列表： item1：表单标识，item2：权限项 </returns>
        public static List<Tuple<string, List<string>>> GetAgentRolePermItem_Shopkeeper()
        {
            /* 系统中预设角色权限，然后通过下面的这个sql查询到这个预设数据
            select distinct t0.fbizobjid ,fpermititemid , '"' + fbizobjid + ';' + fpermititemid + '",'
            from t_sec_rolefuncacl t0
            where t0.froleId = '角色id'  and fallow = '1'
            order by t0.fbizobjid ,fpermititemid
            */
            var permItems = new List<string>()
            {
                "bas_msglog;fw_view",
"bas_store;fw_view",
"bd_billtype;fw_view",
"bpm_flowmsg;fw_view",
"coo_incomedisburse;fw_delete",
"coo_incomedisburse;fw_edit",
"coo_incomedisburse;fw_export",
"coo_incomedisburse;fw_listattach",
"coo_incomedisburse;fw_modify",
"coo_incomedisburse;fw_new",
"coo_incomedisburse;fw_print",
"coo_incomedisburse;fw_submit",
"coo_incomedisburse;fw_unsubmit",
"coo_incomedisburse;fw_view",
"coo_incomedisburse;fw_viewrecord",
"dashboard_mytask;mobile_view",
"dashboard_mytask;fw_view",
"rpt_ordertrackingform;fw_export",
"rpt_ordertrackingform;fw_view",
"rpt_stockdetail;fw_view",
"rpt_stocksummary;fw_view",
"rpt_stocksynthesize;fw_export",
"rpt_stocksynthesize;fw_presetfilteredit",
"rpt_stocksynthesize;fw_print",
"rpt_stocksynthesize;fw_view",
"rpt_storesalesconversionrate;fw_export",
"rpt_storesalesconversionrate;fw_view",
"sel_propvalue;fw_new",
"sel_propvalue;fw_view",
"ste_channel;addduty",
"ste_channel;changeduty",
"ste_channel;deleteduty",
"ste_channel;followerrecord",
"ste_channel;fw_export",
"ste_channel;fw_forbid",
"ste_channel;fw_listattach",
"ste_channel;fw_modify",
"ste_channel;fw_new",
"ste_channel;fw_print",
"ste_channel;fw_unforbid",
"ste_channel;fw_view",
"ste_channel;fw_viewrecord",
"stk_inventorybase;fw_audit",
"stk_inventorybase;fw_delete",
"stk_inventorybase;fw_export",
"stk_inventorybase;fw_modify",
"stk_inventorybase;fw_new",
"stk_inventorybase;fw_submit",
"stk_inventorybase;fw_unaudit",
"stk_inventorybase;fw_unsubmit",
"stk_inventorybase;fw_view",
"stk_inventorybase;fw_viewrecord",
"stk_inventorytransfer;fw_audit",
"stk_inventorytransfer;fw_delete",
"stk_inventorytransfer;fw_export",
"stk_inventorytransfer;fw_listattach",
"stk_inventorytransfer;fw_modify",
"stk_inventorytransfer;fw_new",
"stk_inventorytransfer;fw_operatelog",
"stk_inventorytransfer;fw_presetfilteredit",
"stk_inventorytransfer;fw_print",
"stk_inventorytransfer;fw_queryinventory",
"stk_inventorytransfer;fw_submit",
"stk_inventorytransfer;fw_unsubmit",
"stk_inventorytransfer;fw_view",
"stk_inventorytransfer;fw_viewrecord",
"stk_inventoryverify;fw_delete",
"stk_inventoryverify;fw_export",
"stk_inventoryverify;fw_modify",
"stk_inventoryverify;fw_new",
"stk_inventoryverify;fw_operatelog",
"stk_inventoryverify;fw_presetfilteredit",
"stk_inventoryverify;fw_print",
"stk_inventoryverify;fw_submit",
"stk_inventoryverify;fw_unsubmit",
"stk_inventoryverify;fw_view",
"stk_inventoryverify;fw_viewrecord",
"stk_otherstockin;fw_delete",
"stk_otherstockin;fw_modify",
"stk_otherstockin;fw_new",
"stk_otherstockin;fw_submit",
"stk_otherstockin;fw_unsubmit",
"stk_otherstockin;fw_view",
"stk_otherstockin;fw_viewrecord",
"stk_otherstockout;fw_delete",
"stk_otherstockout;fw_modify",
"stk_otherstockout;fw_new",
"stk_otherstockout;fw_queryinventory",
"stk_otherstockout;fw_submit",
"stk_otherstockout;fw_unsubmit",
"stk_otherstockout;fw_view",
"stk_otherstockout;fw_viewrecord",
"stk_reservebill;fw_new",
"stk_reservebill;fw_reserveinventory",
"stk_reservebill;fw_view",
"stk_reservebill;fw_viewrecord",
"stk_reservebill;ydj_manualrelease",
"stk_reservebill_history;fw_view",
"stk_sostockout;fw_audit",
"stk_sostockout;fw_delete",
"stk_sostockout;fw_modify",
"stk_sostockout;fw_new",
"stk_sostockout;fw_queryinventory",
"stk_sostockout;fw_submit",
"stk_sostockout;fw_unaudit",
"stk_sostockout;fw_unsubmit",
"stk_sostockout;fw_view",
"stk_sostockout;fw_viewrecord",
"sys_cfunction;fw_view",
"sys_datawidgetlist;fw_view",
"sys_datawidgettype;fw_view",
"sys_reportshell;fw_new",
"ydj_achievement;fw_view",
"ydj_achievement;myself",
"ydj_activity;fw_view",
"ydj_bank;fw_view",
"ydj_banknum;fw_view",
"ydj_brand;fw_view",
"ydj_category;fw_view",
"ydj_combopromotion;fw_view",
"ydj_contactunit;fw_view",
"ydj_customer;followerrecord",
"ydj_customer;fw_export",
"ydj_customer;fw_import",
"ydj_customer;fw_listattach",
"ydj_customer;fw_modify",
"ydj_customer;fw_new",
"ydj_customer;fw_operatelog",
"ydj_customer;fw_presetfilteredit",
"ydj_customer;fw_print",
"ydj_customer;fw_tasktmpl",
"ydj_customer;fw_view",
"ydj_customer;fw_viewrecord",
"ydj_customer;fw_ydj_customer_addduty",
"ydj_customer;fw_ydj_customer_removeduty",
"ydj_customer;fw_ydj_customer_replaceduty",
"ydj_customer;fw_distributeduty",
"ydj_dept;fw_view",
"ydj_followerrecord;fw_new",
"ydj_followerrecord;fw_view",
"ydj_innerpartrank;fw_view",
"ydj_order;followerrecord",
"ydj_order;fw_audit",
"ydj_order;fw_change",
"ydj_order;fw_delete",
"ydj_order;fw_import",
"ydj_order;fw_listattach",
"ydj_order;fw_modify",
"ydj_order;fw_new",
"ydj_order;fw_operatelog",
"ydj_order;fw_print",
"ydj_order;fw_queryinventory",
"ydj_order;fw_submit",
"ydj_order;fw_submitchange",
"ydj_order;fw_unaudit",
"ydj_order;fw_unchange",
"ydj_order;fw_unsubmit",
"ydj_order;fw_view",
"ydj_order;fw_viewrecord",
"ydj_order;ydj_order_addattachment",
"ydj_order;ydj_order_receipt",
"ydj_order;ydj_order_refund",
"ydj_order;fw_renewalreceipt",
"ydj_order_chg;followerrecord",
"ydj_order_chg;fw_change",
"ydj_order_chg;fw_import",
"ydj_order_chg;fw_listattach",
"ydj_order_chg;fw_modify",
"ydj_order_chg;fw_new",
"ydj_order_chg;fw_operatelog",
"ydj_order_chg;fw_print",
"ydj_order_chg;fw_queryinventory",
"ydj_order_chg;fw_submit",
"ydj_order_chg;fw_submitchange",
"ydj_order_chg;fw_unchange",
"ydj_order_chg;fw_unsubmit",
"ydj_order_chg;fw_view",
"ydj_order_chg;fw_viewrecord",
"ydj_order_chg;ydj_order_addattachment",
"ydj_order_chg;ydj_order_receipt",
"ydj_order_chg;ydj_order_refund",
"ydj_price;fw_view",
"ydj_product;fw_view",
"ydj_productpromotion;fw_view",
"ydj_series;fw_view",
"ydj_staff;fw_view",
"ydj_stockstatus;fw_view",
"ydj_storehouse;fw_view",
"ydj_supplier;fw_view",
"ydj_target;fw_view",
"ydj_target;myself",
"ydj_trend;fw_view",
"ydj_trend;mydepartment",
"ydj_trend;myself",
"ydj_unit;fw_view",
"ms_markingassistant;fw_view",
"ydj_renewtype;fw_view",
"rpt_selfbuildproduct;fw_export",
"rpt_selfbuildproduct;fw_presetfilteredit",
"rpt_selfbuildproduct;fw_print",
"rpt_selfbuildproduct;fw_view",
"ydj_openingbank;fw_view",
"bcm_receptionscantask;push2poinstock",
"ydj_purchaseorder;fw_pushfeedback",
            };

            var result = GetPermItems(permItems);

            return result;
        }

        public static List<MPTabbarModel> GetAgentRoleMPMenu_Shopkeeper()
        {
            /*
             select '"' + ftabbar + ';' + fgroup + ';' + fmenuid + ';' + fmenuname  +  '",'  
            from t_mp_rolemenu t0  with(nolock) 
            where t0.froleId=@roleId and fisallow=1
             */

            var menuItems = new List<string>()
            {
                "工作台;销售管理;720326618328993798;商品图册",
                "工作台;销售管理;720561606232248333;购物车",
                "工作台;销售管理;720565024824889424;我的客户",
                "工作台;订单管理;720565024824889427;合同单",
                "工作台;订单管理;720565024824889428;收款单",
                "工作台;订单管理;720565024824889429;退款单",
                "工作台;企业管理;720565024824889431;审批",
                "工作台;企业管理;720565024824889432;渠道伙伴",
                "首页;快捷入口;720590220793352192;录合同",
                "工作台;企业管理;732316900327034886;员工管理",
                "工作台;销售管理;758711323021414406;库存明细查询",
                "工作台;销售管理;994614394863353856;促销活动",
                "统计;客户;997916152440360100;客户概览",
                "统计;客户;997916152440360101;客户来源占比分析",
                "统计;客户;997916152440360103;客户年龄占比分析",
                "统计;销售;997916152440360104;销售概览",
                "统计;销售;997916152440360105;销售趋势分析",
                "统计;销售;997916152440360106;员工业绩排名",
                "统计;销售;997916152440360107;系列销售占比",
                "统计;销售;997916152440360108;品牌销售占比",
                "统计;销售;997916152440360109;品类销售金额TOP10",
                "统计;销售;997916152440360110;品类销售数量TOP10",
                "统计;销售;997916152440360111;型号销售TOP10",
                "工作台;销售管理;994614394863353856;促销活动",
            };


            var mpMenu = GetMPTabbars(menuItems);

            return mpMenu;
        }

    }
}
