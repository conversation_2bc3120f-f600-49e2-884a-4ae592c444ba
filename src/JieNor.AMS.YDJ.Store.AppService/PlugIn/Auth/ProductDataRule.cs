using System;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    /// <summary>
    /// 【商品】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    public class ProductDataRule : IDataQueryRule
    {
        /// <summary>
        /// 用户所属组织是总部，则可以看到总部的商品信息;
        /// 用户所属组织是分公司，按 商品上维护的使用组织进行隔离
        /// 用户是经销商或门店的，可以看到的商品信息= 总部授权商品 + 自己建的商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();
            var orgInfos = ProductDataIsolateHelper.GetCurrentUserOrgInfos(ctx);
            if (orgInfos == null || orgInfos.Count == 0)
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fid",
                    Operator = "=",
                    Value = "0",
                });
                return filter;
            }

            InitDataQueryRuleParaInfo(rulePara);

            var tempView = ctx.GetAuthProductDataPKID(rulePara);
            filter.Add(new FilterRowObject()
            {
                Id = "fid",
                Operator = "exists",
                Value = tempView,
            });

            return filter;
        }


        static Dictionary<string, string> _dicSrcFormIds = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "ydj_productpromotion", "ydj_order" },
            { "ydj_combopromotion", "ydj_order" },
        };


        private void InitDataQueryRuleParaInfo(DataQueryRuleParaInfo rulePara)
        {
            if (rulePara == null || rulePara.SrcFormId.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            if (_dicSrcFormIds.ContainsKey(rulePara.SrcFormId))
            {
                rulePara.SrcFormId = _dicSrcFormIds[rulePara.SrcFormId];
            }
        }
    }
}