using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;  
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{

    /// <summary>
    /// 【选配】相关基础数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("sel_category|sel_component|sel_constraint|sel_fittingsmap|sel_priceformula|sel_range|sel_suitemap|sel_propvalue|sel_prop|sel_type|sel_suite|sel_standardprop|ms_markingassistant")]
    public class ApolegamicDataRule : IDataQueryRule
    {


        /// <summary>
        /// 选配基础数据，经销商、门店下单时需要选到总部创建的相关选配基础数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();

            //总部用户，自己创建的选配基础数据
            if (ctx.IsTopOrg)
            {
                filter.Add(new FilterRowObject()
                {
                    Id = "fmainorgid",
                    Operator = "=",
                    Value = ctx.Company,
                });
                return filter;
            }

            //经销商、门店用户：可看到自己创建的及经销商创建的选配基础数据
            filter.Add(new FilterRowObject()
            {
                Id = "fmainorgid",
                Operator = "in",
                Value = "{0},{1}".Fmt(ctx.Company,ctx.TopCompanyId),
            });
            return filter;
        }



    }
}