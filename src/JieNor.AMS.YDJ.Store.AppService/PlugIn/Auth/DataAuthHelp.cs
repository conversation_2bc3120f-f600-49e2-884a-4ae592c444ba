using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore;
using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    public class DataAuthHelp
    {



        /// <summary>
        /// 设置总部管理员角色的默认权限及经销商角色的默认权限。
        /// 应用场景：在总部企业注册时，给对应管理员设置的默认权限
        /// </summary>
        /// <param name="orgType"></param>
        /// <param name="adminRole">总部管理员角色信息</param>
        /// <param name="users">用户信息</param>
        public static void SetRoleDefaultAccess(UserContext userCtx, string orgType, DynamicObject adminRole, List<DynamicObject> users)
        {
            if (orgType.EqualsIgnoreCase("1"))//总部注册企业时，才需要创建管理员角色及经销商管理员角色，这个时候才需要设置默认权限
            {
                var permService = userCtx.Container.GetService<IPermissionService>();
                var allMdls = permService.GetBizObjPermitItem(userCtx);

                //总部管理员角色的默认权限
                var roleACLDetail = GetAdminRoleDefaultAccess(userCtx, adminRole, users, allMdls);
                permService.SaveRolePermission(userCtx, roleACLDetail);

                //总部管理员角色的小程序默认权限 
                SetAdminRoleMPMenuPermission(userCtx, Convert.ToString(adminRole["Id"]));

                var agentRole = GetAgentRole(userCtx);
                if (agentRole != null)
                {
                    var roleId = Convert.ToString(agentRole["Id"]);
                    Dictionary<string, string> agentUsers = MPPermHelper.GetRoleUser(userCtx, roleId);
                    //经销商管理员角色的默认权限
                    roleACLDetail = GetAgentRoleDefaultAccess(userCtx, agentRole, agentUsers, allMdls);
                    permService.SaveRolePermission(userCtx, roleACLDetail);

                    //经销商理员角色的小程序默认权限
                    SetAgentRoleMPMenuPermission(userCtx, roleId, agentUsers);
                }
            }
        }


        /// <summary>
        /// 总部管理员角色的小程序默认权限
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="roleId">角色Id</param> 
        /// <returns></returns>
        private static void SetAdminRoleMPMenuPermission(UserContext userCtx, string roleId)
        {
            RoleMPMenuPermitInfo auth = new RoleMPMenuPermitInfo();
            auth.RoleId = roleId;
            auth.RoleUsers = MPPermHelper.GetRoleUser(userCtx, roleId); ;
            auth.MPMenuPermission = MPMenuHelper.GetMPTabbars(userCtx, true);

            if (auth.MPMenuPermission == null)
            {
                return;
            }

            foreach (var tabbar in auth.MPMenuPermission)
            {
                foreach (var group in tabbar.groups)
                {
                    foreach (var menu in group.menus)//给所有权限
                    {
                        menu.isAllow = true;
                    }
                }
            }

            MPPermHelper.SaveRolePermission(userCtx, auth);
        }

        /// <summary>
        /// 经销商管理员角色的小程序默认权限
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="roleId">角色Id</param>
        /// <param name="authRoleUsers">角色关联的用户</param>
        /// <returns></returns>
        private static void SetAgentRoleMPMenuPermission(UserContext userCtx, string roleId, Dictionary<string, string> authRoleUsers)
        {
            RoleMPMenuPermitInfo auth = new RoleMPMenuPermitInfo();
            auth.RoleId = roleId;
            auth.RoleUsers = authRoleUsers;
            auth.MPMenuPermission = MPMenuHelper.GetMPTabbars(userCtx, true);

            if (auth.MPMenuPermission == null)
            {
                return;
            }

            //经销商角色的小程序默认权限
            var defMenuPerms = PreRolePermInfo.GetAgentRoleDefaultMPPermItem();

            foreach (var tabbar in auth.MPMenuPermission)
            {
                foreach (var group in tabbar.groups)
                {
                    foreach (var menu in group.menus)
                    {
                        string key = $"{tabbar.tabbar}:{group.group}:{menu.id}";
                        if (defMenuPerms.Any(f => f.EqualsIgnoreCase(key)))
                        {
                            menu.isAllow = true;
                        }
                    }
                }
            }

            MPPermHelper.SaveRolePermission(userCtx, auth);
        }


        /// <summary>
        /// 获取经销商默认权限
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentRole"></param>
        /// <param name="agentUsers"></param>
        /// <param name="allMdls"></param> 
        /// <returns></returns>
        private static RolePermitInfo GetAgentRoleDefaultAccess(UserContext userCtx, DynamicObject agentRole, Dictionary<string, string> agentUsers,
                                                                List<Tuple<string, List<HtmlPermItem>, string, string>> allMdls)
        {
            var defPermItems = PreRolePermInfo.GetAgentRoleDefaultPermItem();

            var roleACLDetail = GetAgentRoleAccess(userCtx, agentRole, agentUsers, allMdls, defPermItems);

            return roleACLDetail;
        }





        /// <summary>
        /// 获取经销商默认权限
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentRole"></param>
        /// <param name="agentUsers"></param>
        /// <param name="allMdls"></param>
        /// <returns></returns>
        public static RolePermitInfo GetAgentRoleAccess(UserContext userCtx, DynamicObject agentRole, Dictionary<string, string> agentUsers,
                                                         List<Tuple<string, List<HtmlPermItem>, string, string>> allMdls, List<Tuple<string, List<string>>> defPermItems,
                                                          List<Tuple<string, List<FieldAuthInfo>>> fldAuths = null, List<Tuple<string, List<DataRowAuthInfo>>> dataRowAuths = null)
        {
            RolePermitInfo roleACLDetail = new RolePermitInfo()
            {
                ProductId = userCtx.Product.IsNullOrEmptyOrWhiteSpace() ? userCtx.ProductId() : userCtx.Product,
                CompanyId = userCtx.Company,
                CompanyName = userCtx.Companys?.FirstOrDefault(f => f.CompanyId == userCtx.Company)?.CompanyName,
                RoleId = agentRole["Id"]?.ToString(),
                RoleName = agentRole["fname"]?.ToString(),
            };
            foreach (var user in agentUsers)
            {
                roleACLDetail.RoleUsers.Add(user.Key, user.Value);
            }

            foreach (var bizMdl in allMdls)
            {
                if (bizMdl.Item4.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var haveForm = defPermItems.FirstOrDefault(f => f.Item1.EqualsIgnoreCase(bizMdl.Item4));
                // 暂时注释该逻辑，因为客户要求即使没有预制【表单权限】，也需要能够预制【字段权限】。
                //if (haveForm == null)
                //{
                //    continue;
                //}

                var module = roleACLDetail.MdlPermission.FirstOrDefault(f => f.ModuleId.EqualsIgnoreCase(bizMdl.Item1));
                if (module == null)
                {
                    module = new MdlPermitInfo();
                    module.ModuleId = bizMdl.Item1;
                    module.ModuleName = bizMdl.Item1;
                    roleACLDetail.MdlPermission.Add(module);
                }

                BizObjPermitInfo bizObjAuth = new BizObjPermitInfo();
                bizObjAuth.BizObjId = bizMdl.Item4;
                bizObjAuth.BizObjName = bizMdl.Item3;

                if (haveForm != null)
                {
                    foreach (var item in bizMdl.Item2)
                    {
                        if (!haveForm.Item2.Any(f => f.EqualsIgnoreCase(item.Id)))
                        {
                            continue;
                        }

                        PermitItemAuthInfo authItem = new PermitItemAuthInfo();
                        authItem.ItemId = item.Id;
                        authItem.ItemName = item.Caption;
                        authItem.IsRefuse = false;
                        authItem.IsAllow = true;
                        bizObjAuth.FucntionACL.Add(authItem);
                    }
                }

                var fldAuth = fldAuths?.FirstOrDefault(f => f.Item1.EqualsIgnoreCase(bizObjAuth.BizObjId));
                if (fldAuth != null)
                {
                    bizObjAuth.FieldACL = fldAuth.Item2;
                }

                var dataRowAuth = dataRowAuths?.FirstOrDefault(f => f.Item1.EqualsIgnoreCase(bizObjAuth.BizObjId));
                if (dataRowAuth != null)
                {
                    foreach (var item in dataRowAuth.Item2)
                    {
                        item.RoleId = roleACLDetail.RoleId;
                    }
                    bizObjAuth.DataRowACL["fw_view"] = dataRowAuth.Item2;
                }

                module.BizObjPermission.Add(bizObjAuth);
            }

            //没有业务对象的模块去掉
            roleACLDetail.MdlPermission = roleACLDetail.MdlPermission.Where(f => f.BizObjPermission.Count > 0).ToList();

            return roleACLDetail;
        }



        /// <summary>
        /// 获取经销商管理员角色信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static DynamicObject GetAgentRole(UserContext userCtx)
        {
            var roles = userCtx.LoadBizDataByFilter("sec_role", " fnumber='Admin_Agent' ");
            return roles.FirstOrDefault();
        }





        /// <summary>
        /// 设置管理员角色的默认权限。
        /// 应用场景：在经销商、门店审核生成对应组织管理员时，给对应管理员设置的默认权限
        /// </summary> 
        /// <param name="role">角色信息</param>
        /// <param name="users">用户信息</param>
        private static RolePermitInfo GetAdminRoleDefaultAccess(UserContext userCtx, DynamicObject role, List<DynamicObject> users,
                                                            List<Tuple<string, List<HtmlPermItem>, string, string>> allMdls)
        {
            RolePermitInfo roleACLDetail = new RolePermitInfo()
            {
                ProductId = userCtx.Product.IsNullOrEmptyOrWhiteSpace() ? userCtx.ProductId() : userCtx.Product,
                CompanyId = userCtx.Company,
                CompanyName = userCtx.Companys?.FirstOrDefault(f => f.CompanyId == userCtx.Company)?.CompanyName,
                RoleId = role["Id"]?.ToString(),
                RoleName = role["fname"]?.ToString(),
            };
            foreach (var user in users)
            {
                roleACLDetail.RoleUsers.Add(user["Id"].ToString(), user["fname"]?.ToString());
            }

            foreach (var bizMdl in allMdls)
            {
                if (bizMdl.Item4.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var module = roleACLDetail.MdlPermission.FirstOrDefault(f => f.ModuleId.EqualsIgnoreCase(bizMdl.Item1));
                if (module == null)
                {
                    module = new MdlPermitInfo();
                    module.ModuleId = bizMdl.Item1;
                    module.ModuleName = bizMdl.Item1;
                    roleACLDetail.MdlPermission.Add(module);
                }

                BizObjPermitInfo bizObjAuth = new BizObjPermitInfo();
                bizObjAuth.BizObjId = bizMdl.Item4;
                bizObjAuth.BizObjName = bizMdl.Item3;

                //给所有权限
                foreach (var item in bizMdl.Item2)
                {
                    PermitItemAuthInfo authItem = new PermitItemAuthInfo();
                    authItem.ItemId = item.Id;
                    authItem.ItemName = item.Caption;
                    authItem.IsRefuse = false;
                    authItem.IsAllow = true;
                    bizObjAuth.FucntionACL.Add(authItem);
                }

                module.BizObjPermission.Add(bizObjAuth);
            }

            //没有业务对象的模块去掉
            roleACLDetail.MdlPermission = roleACLDetail.MdlPermission.Where(f => f.BizObjPermission.Count > 0).ToList();

            return roleACLDetail;
        }






        /// <summary>
        /// 系统运维角色用户禁用
        /// 1、将对应用户从所有的经销商企业中移除
        /// 3、将所有的经销商企业中的对应用户禁用
        /// </summary>
        /// <param name="userCtx"></param>
        public static IOperationResult DevOpsUserForbid(UserContext ctx, List<BaseDataSummary> userInfos, bool forbid)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;

            if (ctx.IsTopOrg == false)
            {
                return result;
            }
            if (userInfos == null || userInfos.Count == 0)
            {
                return result;
            }

            var sql = "";
            var dbSvc = ctx.Container.GetService<IDBService>();
            using (var tran = ctx.CreateTransaction())
            {
                var tempTblUser = dbSvc.CreateTempTableWithDataList(ctx, userInfos.Select(f => f.Number),false);
                sql = @"/*dialect*/
                    update t_sec_user set fforbidstatus ='{2}' 
                    where exists (select 1 from (
                                    select a.fid,a.fnumber,a.fname,a.fmainorgid 
                                    from t_sec_user a 
                                    inner join T_BAS_ORGANIZATION b on a.fmainorgid=b.fid
                                    inner join {1} x on a.fnumber=x.fid
                                    where b.ftopcompanyid='{0}' 
                            ) t where t_sec_user.fid=t.fid ) ".Fmt(ctx.TopCompanyId, tempTblUser, forbid ? "1" : "0");
                dbSvc.ExecuteDynamicObject(ctx, sql);

                tran.Complete();

                dbSvc.DeleteTempTableByName(ctx, tempTblUser, true);
            }

            sql = @"select fid,fname,fnumber from T_BAS_ORGANIZATION  with(nolock) where forgtype='4' and ftopcompanyid='{0}' ".Fmt(ctx.TopCompanyId);
            var agenInfos = dbSvc.ExecuteDynamicObject(ctx, sql);
            if (agenInfos == null || agenInfos.Count == 0)
            {
                return result;
            }
            //var companyInfos = (from p in agenInfos
            //                    select new BaseDataSummary()
            //                    {
            //                        Id = Convert.ToString(p["fid"]),
            //                        Name = Convert.ToString(p["fname"]),
            //                        Number = Convert.ToString(p["fnumber"]),
            //                    }
            //                 ).ToList();
            var companyInfos = (from p in agenInfos
                                select new BaseDataSummary
                                {
                                    Id = Convert.ToString(p["fid"])
                                }
                             ).Distinct().ToList<BaseDataSummary>();
            var targetSEP = TargetSEP.AuthService;
            var para = new CommonBillDTO()
            {
                FormId = "auth_usercompany",
                OperationNo = forbid ? "ForbitDevOpsUser" : "AddDevOpsUser",
            };

            para.SimpleData.Add("productId", ctx.ProductId());
            if (forbid)
            {
                para.SimpleData.Add("companyInfos", companyInfos.ToJson());
                para.SimpleData.Add("userInfos", userInfos.ToJson());
            }
            else
            {
                para.SimpleData.Add("companyInfos", companyInfos.Select(s => s.Id).ToJson());
                para.SimpleData.Add("userInfos", userInfos.Select(s => s.Number).ToJson());
            }

            var gateway = ctx.Container.GetService<IHttpServiceInvoker>();
            var res = gateway.Invoke(ctx, targetSEP, para) as DynamicDTOResponse;
            result = res?.OperationResult;

            return result;
        }


        /// <summary>
        /// 系统运维角色用户更新
        /// 1、查品牌商组织下的系统运维角色关联的用户信息
        /// 2、将查询到的这些用户，加入到所有的经销商企业中
        /// 3、将查询到的这些用户，添加到所有经销商组织下的用户表中
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="roleUsers">当前角色关联的用户</param> 
        /// <param name="byRoleUpdate">true：从角色授权入口调用</param> 
        /// <param name="agentIds">指定执行的经销商，为null或空表示所有组织</param> 
        public static IOperationResult AddOrUpdateDevOpsUserInfo(UserContext ctx, Dictionary<string, string> roleUsers, bool byRoleUpdate, IEnumerable<string> agentIds = null)
        {
            IOperationResult result = new OperationResult();

            var userMetadata = HtmlParser.LoadFormMetaFromCache("sec_user", null);
            var dt = userMetadata.GetDynamicObjectType(ctx);

            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, dt);
            var dbSvc = ctx.Container.GetService<IDBService>();

            //取经销商组织信息
            var sql = @"select fid,fname,fnumber from T_BAS_ORGANIZATION with(nolock) where forgtype='4' and ftopcompanyid='{0}' ".Fmt(ctx.TopCompanyId);

            // 指定经销商
            if (!agentIds.IsNullOrEmpty())
            {
                sql += $" and fid in ({agentIds.JoinEx(",", true)})";
            }
            var agenInfos = dbSvc.ExecuteDynamicObject(ctx, sql);
            if (agenInfos == null || agenInfos.Count == 0)
            {
                return result;
            }

            sql = @"select fid,fname,fnumber from T_sec_role with(nolock) where fmainorgid='{0}' and fnumber='Admin_DevOps' ".Fmt(ctx.TopCompanyId);
            var roleInfo = dbSvc.ExecuteDynamicObject(ctx, sql)?.FirstOrDefault();
            if (roleInfo == null)
            {
                return result;
            }

            //总部运维角色关联的用户信息（之前已经关联的运维人员--旧的关联信息） 
            sql = @"select distinct a.fuserid,a.froleid, c.fnumber,c.fname 
                        from t_sec_roleuser a with(nolock)
                        inner join t_sec_role b with(nolock) on a.froleid =b.fid
                        inner join t_sec_user c with(nolock) on a.fuserid=c.fid
                        where b.fnumber='Admin_DevOps' and c.fforbidstatus ='0' 
                            and b.fmainorgid='{0}' and c.fmainorgid='{0}' ".Fmt(ctx.TopCompanyId);
            var existRoleUserDatas = dm.SelectBy(dbSvc.ExecuteReader(ctx, sql)).OfType<DynamicObject>().ToList();

            //总部运维角色关联的用户信息（当前要关联的运维人员）
            var userDatas = new List<DynamicObject>();
            if (roleUsers == null || roleUsers.Count == 0)
            {
                userDatas = existRoleUserDatas;
            }
            else
            {
                userDatas = dm.Select(roleUsers.Keys.ToList()).OfType<DynamicObject>().ToList();
            }
            if (userDatas == null || userDatas.Count == 0)
            {
                return result;
            }

            //不在运维角色中的用户，要删除掉关联关系（比如之前用户关联的是运维角色，然后有从运维角色中去除）
            if (roleUsers != null && roleUsers.Count > 0)
            {
                DeleteRoleUserLinkInfo(ctx, roleUsers, byRoleUpdate, roleInfo, userDatas);
            }
            //后面还是会查与总部用户编码相同的其它经销商的用户，不如把条件前置，不然查询会很慢。CreateDevOpsUserInfo其实就是处理将非总部同编码的用户同化为系统运维用户。
            var userNum = userDatas.Select(o => Convert.ToString(o["fnumber"])).Distinct().ToList();

            //现有的经销商组织下的运维人员
            sql = @"select a.fid
                    from t_sec_user a  with(nolock)
                    inner join T_BAS_ORGANIZATION b with(nolock) on a.fmainorgid=b.fid
                    where a.fnumber in ({2}) and b.fid<>'{0}' and b.ftopcompanyid='{0}' and exists (select 1 from ({1}
                                                            ) x where x.fnumber=a.fnumber )
                    ".Fmt(ctx.TopCompanyId, sql, userNum.JoinEx(",", true));

            // 指定经销商
            if (!agentIds.IsNullOrEmpty())
            {
                sql += $" and b.fid in ({agentIds.JoinEx(",", true)})";
            }

            var reader = dbSvc.ExecuteReader(ctx, sql);
            var allAgenUserInfos = dm.SelectBy(reader).OfType<DynamicObject>().ToList();  

            CreateDevOpsUserInfo(ctx, userDatas, agenInfos, allAgenUserInfos, roleInfo);

            //更新auth站点的企业用户关联信息
            result = DevOpsUserJoinCompay(ctx, userDatas, agenInfos);

            var perSvc = ctx.Container.GetService<IPermissionService>();
            perSvc.ClearCache(ctx);

            return result;
        }


        private static void DeleteRoleUserLinkInfo(UserContext ctx, Dictionary<string, string> roleUsers, bool byRoleUpdate, DynamicObject roleInfo, List<DynamicObject> userDatas)
        {
            var beDelIds = new List<string>();
            var grpUserDatas = userDatas.GroupBy(f => Convert.ToString(f["Id"])).ToList();

            var join = (from x in roleUsers
                        join y in grpUserDatas on x.Key equals y.Key into temp
                        from z in temp.DefaultIfEmpty()
                        select new
                        {
                            id = x.Key,
                            users = z?.ToList(),
                        }).ToList();

            foreach (var item in join)
            {
                if (item.users != null && item.users.Any())
                {
                    continue;
                }

                beDelIds.Add(item.id);
            }

            //foreach (var item in roleUsers)
            //{
            //    if (grpUserDatas.Any(f => f.Key == item.Key))
            //    {
            //        continue;
            //    }

            //    beDelIds.Add(item.Key);
            //}

            if (byRoleUpdate)
            {
                //从角色授权入口调用：如果总部运维人员删除了，则经销商组织下对应的运维人员也要相应的删除关联关系
                var topOrgUsers = userDatas.Where(f => ctx.TopCompanyId.EqualsIgnoreCase(f["fmainorgid"]?.ToString()))?.ToList();
                var grpTopOrgUsers = topOrgUsers.GroupBy(f => Convert.ToString(f["fnumber"])).ToList();
                var agenOrgUsers = userDatas.Except(topOrgUsers).ToList();

                var joinX = (from x in agenOrgUsers
                             join y in grpTopOrgUsers on Convert.ToString(x["fnumber"]) equals y.Key into temp
                             from z in temp.DefaultIfEmpty()
                             select new
                             {
                                 id = Convert.ToString(x["Id"]),
                                 number = Convert.ToString(x["fnumber"]),
                                 users = z?.ToList(),
                             }).ToList();
                foreach (var item in joinX)
                {
                    if (item.users != null && item.users.Any())
                    {
                        continue;
                    }

                    beDelIds.Add(item.id);
                }

                //foreach (var item in agenOrgUsers)
                //{
                //    var fnumber = Convert.ToString(item["fnumber"]);
                //    if (grpTopOrgUsers.Any(f => f.Key == fnumber))
                //    {
                //        continue;
                //    }

                //    beDelIds.Add(Convert.ToString(item["Id"]));
                //}
            }

            if (beDelIds.Count > 0)
            {
                var roleId = Convert.ToString(roleInfo["fid"]);
                var roleName = Convert.ToString(roleInfo["fname"]);
                var dbSvcEx = ctx.Container.GetService<IDBServiceEx>();
                var grp = beDelIds.GetListGroup(20);
                foreach (var item in grp)
                {
                    var delSql = @"delete from t_sec_roleuser where froleid = '{1}' and fuserid in ({0})".Fmt(item.JoinEx(",", true), roleId);
                    dbSvcEx.Execute(ctx, delSql);
                }

                var beUpdates = ctx.LoadBizDataById("sec_user", beDelIds, true);
                foreach (var item in beUpdates)
                {
                    var roleIds = Convert.ToString(item["froleids"]).SplitKey();
                    var roleNames = Convert.ToString(item["froleids_txt"]).SplitKey();
                    item["froleids"] = roleIds.Where(f => !f.EqualsIgnoreCase(roleId)).Distinct().JoinEx(",", false);
                    item["froleids_txt"] = roleNames.Where(f => !f.EqualsIgnoreCase(roleName)).Distinct().JoinEx(",", false);
                }
                ctx.SaveBizData("sec_user", beUpdates);
            }
        }


        /// <summary>
        /// 系统运维角色中的用户，加入经销商企业用户列表
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleUserInfos"></param>
        /// <param name="agenInfos"></param>
        public static IOperationResult DevOpsUserJoinCompay(UserContext ctx, List<DynamicObject> roleUserInfos, DynamicObjectCollection agenInfos)
        {
            //var userInfos = (from p in roleUserInfos
            //                 select new BaseDataSummary()
            //                 {
            //                     Id = Convert.ToString(p["Id"]),
            //                     Name = Convert.ToString(p["fname"]),
            //                     Number = Convert.ToString(p["fnumber"]),
            //                 }
            //                 ).ToList();
            //var companyInfos = (from p in agenInfos
            //                 select new BaseDataSummary()
            //                 {
            //                     Id = Convert.ToString(p["fid"]),
            //                     Name = Convert.ToString(p["fname"]),
            //                     Number = Convert.ToString(p["fnumber"]),
            //                 }
            //                 ).ToList();
            var userInfos = (from p in roleUserInfos
                             select Convert.ToString(p["fnumber"])).Distinct().ToList();
            var companyInfos = (from p in agenInfos
                                select Convert.ToString(p["fid"])
                                ).Distinct().ToList();
            var targetSEP = TargetSEP.AuthService;
            var para = new CommonBillDTO()
            {
                FormId = "auth_usercompany",
                OperationNo = "AddDevOpsUser",
            };

            para.SimpleData.Add("productId", ctx.ProductId());
            para.SimpleData.Add("companyInfos", companyInfos.ToJson());
            para.SimpleData.Add("userInfos", userInfos.ToJson());

            var gateway = ctx.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.Invoke(ctx, targetSEP, para) as DynamicDTOResponse;

            return result?.OperationResult;
        }


        /// <summary>
        /// 系统运维角色中的用户，加入经销商用户列表中
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleUserInfos">总部运维角色关联的用户信息</param>
        /// <param name="agenInfos">经销商组织信息</param> 
        /// <param name="allAgenUserInfos">现有的经销商组织下的运维人员</param>
        private static void CreateDevOpsUserInfo(UserContext ctx, List<DynamicObject> roleUserInfos, DynamicObjectCollection agenInfos,
                                                List<DynamicObject> allAgenUserInfos, DynamicObject roleInfo)
        {
            if (allAgenUserInfos == null)
            {
                allAgenUserInfos = new List<DynamicObject>();
            }

            //即将关联的总部运维人员
            var topOrgUsers = roleUserInfos.Where(f => ctx.TopCompanyId.EqualsIgnoreCase(f["fmainorgid"]?.ToString()))?.ToList();
            var grpAgenUsers = allAgenUserInfos.GroupBy(f => f["fmainorgid"]?.ToString())?.ToList();

            var joinByOrg = (from x in agenInfos
                             join y in grpAgenUsers on Convert.ToString(x["fid"]) equals y.Key into temp
                             from z in temp.DefaultIfEmpty()
                             select new
                             {
                                 orgId = Convert.ToString(x["fid"]),
                                 agenUsers = z?.ToList(),
                             }).ToList();

            var beUpdate = new List<DynamicObject>();
            foreach (var item in joinByOrg)
            {
                var orgId = item.orgId;
                var agenUsers = item.agenUsers == null ? new List<DynamicObject>() : item.agenUsers;

                var joinByNumber = (from x in topOrgUsers
                                    join y in agenUsers on Convert.ToString(x["fnumber"]) equals y["fnumber"]?.ToString() into temp
                                    from z in temp.DefaultIfEmpty()
                                    select new
                                    {
                                        fnumber = Convert.ToString(x["fnumber"]),
                                        userInfo = x,//总部用户信息
                                        agenUser = z,//对应的经销商用户信息
                                    }).ToList();

                foreach (var itemX in joinByNumber)
                {
                    var userObj = itemX.agenUser;
                    if (userObj == null)
                    {
                        userObj = itemX.userInfo.Clone() as DynamicObject;
                        userObj["fmainorgid"] = orgId;
                        userObj["froleids"] = roleInfo["fid"];
                        userObj["froleids_txt"] = roleInfo["fname"];
                        userObj["fbizorgid"] = orgId;
                        userObj["ftranid"] = string.Empty;
                    }
                    userObj["fisdevops"] = true;//设置为运维人员

                    beUpdate.Add(userObj);
                }
            }

            if (beUpdate.Count > 0)
            {
                //var grpByOrg = beUpdate.GroupBy(f => f["fmainorgid"]?.ToString()).ToList();
                //foreach (var item in grpByOrg)
                //{
                //    var agentCtx = ctx.CreateAgentDBContext(item.Key);
                //    agentCtx.SaveBizData("sec_user", item.ToList());
                //}
                ctx.SaveBizData("sec_user", beUpdate);

                SaveRoleUser(ctx, roleInfo, beUpdate);
            }
        }


        /// <summary>
        /// 新建的运维人员，加入系统运维角色
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleInfo"></param>
        /// <param name="beUpdate"></param>
        private static void SaveRoleUser(UserContext ctx, DynamicObject roleInfo, List<DynamicObject> beUpdate)
        {
            // 过滤用户id为空的
            var userIds = beUpdate?.Select(f => Convert.ToString(f["Id"])).Where(s => !s.IsNullOrEmptyOrWhiteSpace()).Distinct();
            if (userIds == null || !userIds.Any()) return;

            var dbService = ctx.Container.GetService<IDBService>();
            DynamicObjectCollection existMaps = null;
            using (var tran = ctx.CreateTransaction())
            {
                var tempTblUser = dbService.CreateTempTableWithDataList(ctx, userIds,false);

                var sql = @"select distinct a.fuserid,a.froleid
                            from t_sec_roleuser a  with(nolock)
                            where exists ( select 1 from {0} x where  a.fuserid =x.fid  ) 
                            and a.froleid='{1}' ".Fmt(tempTblUser, roleInfo["fid"]);
                existMaps = dbService.ExecuteDynamicObject(ctx, sql);

                tran.Complete();

                dbService.DeleteTempTableByName(ctx, tempTblUser, true);
            }

            var join = (from x in beUpdate
                        join y in existMaps on Convert.ToString(x["Id"]) equals Convert.ToString(y["fuserid"]) into temp
                        from z in temp.DefaultIfEmpty()
                        select new
                        {
                            userId = Convert.ToString(x["Id"]),
                            roleuser = z,
                        }).ToList();
            var metaType = HtmlParser.LoadFormMetaFromCache(AuthFormConst.Sec_RoleUser, ctx).GetDynamicObjectType(ctx);
            var beSave = new List<DynamicObject>();
            foreach (var item in join)
            {
                if (item.roleuser != null)
                {
                    continue;
                }

                var data = metaType.CreateInstance() as DynamicObject;
                data["froleid"] = roleInfo["fid"];
                data["fuserid"] = item.userId;
                data["fformid"] = "sec_roleuser";
                beSave.Add(data);
            }
            //var hash = new HashSet<string>();
            //foreach (var item in existMaps)
            //{
            //    hash.Add(Convert.ToString(item["fuserid"]));
            //}
            //var metaType = HtmlParser.LoadFormMetaFromCache(AuthFormConst.Sec_RoleUser, ctx).GetDynamicObjectType(ctx);
            //var beSave = new List<DynamicObject>();
            //foreach (var user in beUpdate)
            //{
            //    var userId = user["Id"]?.ToString();
            //    if (hash.Any(f => f == userId))
            //    {
            //        continue;
            //    }

            //    var data = metaType.CreateInstance() as DynamicObject;
            //    data["froleid"] = roleInfo["fid"];
            //    data["fuserid"] = userId;
            //    data["fformid"] = "sec_roleuser";
            //    beSave.Add(data);
            //}
            if (beSave.Count > 0)
            {
                ctx.SaveBizData("sec_roleuser", beSave);
            }
        }





        /// <summary>
        /// 经销商管理员：之前经销商已经分发给下面角色的权限的，当前不给权限了，则经销商下面的角色的权限要收回来
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="roleUsers"></param>
        /// <returns></returns>
        internal static void TakeBackPermission(UserContext ctx, Dictionary<string, string> roleUsers)
        {
            var sql = @"
delete  from T_SEC_ROLEFUNCACL 
where not exists (
    select 1 from (
    /*给到经销商管理员的权限*/
        select a.*  from T_SEC_ROLEFUNCACL  a  with(nolock)
        inner join t_sec_role b  with(nolock) on a.froleid =b.fid
        where b.fnumber  ='Admin_Agent' and b.fmainorgid =@topcompanyid
    ) x where x.fbizobjid =T_SEC_ROLEFUNCACL.fbizobjid and x.fpermititemid =T_SEC_ROLEFUNCACL.fpermititemid 
)
and not exists(
    /*系统运维人员的权限*/
    select 1 from t_sec_role y  with(nolock) 
    where T_SEC_ROLEFUNCACL.froleid=y.fid and y.fnumber='Admin_DevOps'
)
and not exists(
	/*排除总部角色*/
	select 1 from t_sec_role r with(nolock)
	where r.fmainorgid=@topcompanyid and T_SEC_ROLEFUNCACL.froleid=r.fid
)
                            ";
            var dbService = ctx.Container.GetService<IDBServiceEx>();
            dbService.Execute(ctx, sql, new SqlParam("@topcompanyid", System.Data.DbType.String, ctx.TopCompanyId));
        }



    }

}