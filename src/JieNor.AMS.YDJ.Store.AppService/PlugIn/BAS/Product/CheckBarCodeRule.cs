using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using Tea.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：校验库存管理参数  【商品条码包装规则维护校验】
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("checkbarcoderule")]
    public class CheckBarCodeRule : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var fpackagtype = this.GetQueryOrSimpleParam<string>("fpackagtype");
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var matbarcoderulecheck = Convert.ToInt32(profileService.GetSystemParameter<string>(this.Context, "stk_stockparam", "fmatbarcoderulecheck"));//参数【商品条码包装规则维护校验】
            if (matbarcoderulecheck == 0) return;

            if (string.IsNullOrWhiteSpace(fpackagtype) || fpackagtype.Equals("0"))
            {
                if (matbarcoderulecheck == 1)
                {
                    this.Result.SrvData = new
                    {
                        code = "101",
                        mes = "当前商品未维护【打包类型】，请确认是否继续保存该商品？"
                    };
                    return;
                }
                if (matbarcoderulecheck == 2)
                {
                    throw new BusinessException("当前商品未维护【打包类型】，不允许保存，请核查！");
                }
            }
        }

    }
}