using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.DataTransferObject.ProductPrice;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：匹配商品价格
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("MatchPrice")]
    public class MatchPrice : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            decimal price = 0;

            string productId = this.GetQueryOrSimpleParam<string>("productId");
            if (!productId.IsNullOrEmptyOrWhiteSpace())
            {
                //商品价格匹配参数对象
                MatchParam matchParam = new MatchParam()
                {
                    PriceType = (Enu_PriceType)this.GetQueryOrSimpleParam<int>("priceType", 0),
                    ProductId = productId,
                    AttrInfo = this.GetQueryOrSimpleParam<string>("attrInfo", ""),
                    Length = this.GetQueryOrSimpleParam<decimal>("length", 0),
                    Width = this.GetQueryOrSimpleParam<decimal>("width", 0),
                    Thick = this.GetQueryOrSimpleParam<decimal>("thick", 0)
                };

                //商品价格服务
                var productPriceService = this.Container.GetService<IProductPriceService>();
                price = productPriceService.GetPrice(this.Context, matchParam);
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = new { price = price };
            this.Result.OptionData.Add("rowId", this.GetQueryOrSimpleParam<string>("rowId"));
        }
    }
}