using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    
    public class SupplierSyncDownloadConfirm : AbstractSyncDownloadConfirmPlugIn
    {
        protected override Dictionary<string, string> GetFormLinkChainData(UserContext userContext, 
                                                                           HtmlForm htmlForm, 
                                                                           JObject dataContent, 
                                                                           Dictionary<string, string> linkChainData)
        {
            var supplierFieldIds = getSupplierFields(htmlForm).Select(x => x.Id).ToList();
            if (supplierFieldIds == null || supplierFieldIds.Count <= 0)
            {
                return linkChainData;
            }
            return linkChainData.Where(x => supplierFieldIds.All(y => x.Key.StartsWith(y) == false)).ToDictionary(k => k.Key, v => v.Value);
        }

        protected sealed override void DealDataEntities(UserContext userContext, 
                                                        HtmlForm htmlForm, 
                                                        List<Dictionary<string, object>> billDatas, 
                                                        List<Dictionary<string, string>> cooOperationModes)
        {
            var publishInfos = billDatas.SelectMany(x => (x["companyInfos"] as List<Dictionary<string, object>>)
                                        .Select(y => new Dictionary<string, string>
                                        {
                                            { "fpublishcid", Convert.ToString(x["publishCompanyId"]) },
                                            { "fpublishpid", Convert.ToString(x["publishProductId"]) },
                                            { "fmainorgid", Convert.ToString(y["companyId"])}
                                        }))
                                        .Distinct(x => string.Join("|", x["fpublishcid"], x["fpublishpid"], x["fmainorgid"])).ToList();

            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var supplierForm = metaModelService.LoadFormModel(userContext, "ydj_supplier");
            var suppliers = GetPublishInfo(userContext, publishInfos, supplierForm, cooOperationModes);
            var supplierFields = getSupplierFields(htmlForm);

            foreach (var billData in billDatas)
            {
                var dataEntities = billData["dataEntities"] as List<DynamicObject>;
                var linkChainData = billData["linkChainData"] as Dictionary<string,string>;
                var dataContent = billData["dataContent"] as JObject;
                billData["auxpropLinkChainData"] = null;
                billData["auxpropId"] = null;

                if (dataEntities == null || dataEntities.Count <= 0)
                {
                    continue;
                }

                var cooDatas = suppliers.Where(x => x["cooCompanyId"] == Convert.ToString(billData["publishCompanyId"]) &&
                                                    x["cooProductId"] == Convert.ToString(billData["publishProductId"])).ToList();
                foreach (var dataEntity in dataEntities)
                {
                    var companyId = Convert.ToString(dataEntity["fmainorgid"]);
                    var supplier = cooDatas.FirstOrDefault(x =>x["companyId"] == companyId);

                    dealSupplierId(userContext, htmlForm, supplier, dataEntity, linkChainData, supplierFields);
                    OnDealSupplierId(supplier, billData, dataEntity, dataContent, linkChainData, supplierFields);
                }
            }
        }

        /// <summary>
        /// 开放给子类处理供应商的信息,修改dataContent和linkChainData的数据
        /// </summary>
        /// <param name="supplier"></param>
        /// <param name="billData"></param>
        /// <param name="dataContent"></param>
        /// <param name="linkChainData"></param>
        /// <param name="supplierFields"></param>
        protected virtual void OnDealSupplierId(Dictionary<string, string> supplier,
                                                Dictionary<string, object> billData,
                                                DynamicObject dataEntity,
                                                JObject dataContent,
                                                Dictionary<string,string> linkChainData,
                                                List<HtmlBaseDataField> supplierFields)
        {
        }

        private void dealSupplierId(UserContext userContext,
                                    HtmlForm htmlForm,
                                    Dictionary<string, string> supplier, 
                                    DynamicObject dataEntity, 
                                    Dictionary<string,string> linkChainData, 
                                    List<HtmlBaseDataField> supplierFields)
        {
            if (supplier == null)
            {
                return;
            }
            //如果是直营模式直接返回
            if (supplier["operationMode"] == "1")
            {
                return;
            }
            var removeProperties = new List<string>();
            foreach (var supplierField in supplierFields)
            {
                removeProperties.AddRange(linkChainData.Where(x => x.Key.StartsWith(supplierField.Id)).Select(x => x.Key));
            }
            foreach (var removeProperty in removeProperties)
            {
                linkChainData.Remove(removeProperty);
            }
            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userContext, new[] { dataEntity }, htmlForm);
            foreach (var supplierField in supplierFields)
            {
                var dataSets = dataEntitySet.FindByEntityKey(supplierField.EntityKey);
                foreach(var dataSet in dataSets)
                {
                    supplierField.DynamicProperty.SetValue(dataSet.DataEntity, supplier["localDataId"]);
                }
            }
        }

        public static List<Dictionary<string, string>> GetPublishInfo(UserContext userContext,
                                                                      List<Dictionary<string, string>> publishInfos,
                                                                      HtmlForm htmlForm,
                                                                      List<Dictionary<string, string>> cooOperationModes)
        {
            if (publishInfos == null || publishInfos.Count <= 0)
            {
                return null;
            }

            var results = new List<Dictionary<string, string>>();
            var grpPublishInfos = publishInfos.GroupBy(f =>new { cooPushInfo = string.Format("{0}||{1}||{2}", f["fpublishcid"], f["fpublishpid"], f["fmainorgid"])}).ToList();
            var grpCooOperationModes = cooOperationModes.GroupBy(x => new { formId = x["formId"], cooPushInfo = string.Format("{0}||{1}||{2}", x["cooCompanyId"], x["cooProductId"], x["companyId"]) }).ToList();
            foreach (var publishInfo in grpPublishInfos)
            {
                var dbResult = grpCooOperationModes.FirstOrDefault(x => x.Key.formId == htmlForm.Id && x.Key.cooPushInfo == publishInfo.Key.cooPushInfo);
                if (dbResult != null)
                {
                    results.Add(dbResult.FirstOrDefault());
                }
            }
            
            return results;
        }

        protected List<HtmlBaseDataField> getSupplierFields(HtmlForm htmlForm)
        {
            var supplierFields = htmlForm.GetFieldList().Where(x =>
            {
                var field = x as HtmlBaseDataField;
                if (field == null)
                {
                    return false;
                }
                return field.RefFormId == "ydj_supplier";
            }).Select(x => x as HtmlBaseDataField).ToList();
            return supplierFields;
        }
    }
}
