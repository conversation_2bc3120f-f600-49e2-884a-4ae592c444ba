using JieNor.AMS.YDJ.Core.DataEntity.Models;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：获取提货方式接口
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("getdeliverymode")]
    public class GetDeliveryMode : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var productMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var deliverymodeField = productMeta.GetField("fdeliverymode") as HtmlSimpleSelectField;
            this.OperationContext.Result.SrvData = new
            {
                ValueList = deliverymodeField.ValueList
            };
        }
    }
}
