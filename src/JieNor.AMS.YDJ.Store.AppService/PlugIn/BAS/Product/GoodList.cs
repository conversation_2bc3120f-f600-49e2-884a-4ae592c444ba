using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品主图更新接口
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("goodlist")]
    public class GoodList : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 日志服务
        /// </summary>
        private ILogServiceEx LogServiceEx { get; set; }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var data = this.GetQueryOrSimpleParam("param", "");
            var jarray = JArray.Parse(data);
            try
            {
                List<string> sql = new List<string>();
                var dbSvc = this.Container.GetService<IDBServiceEx>();
                foreach (var item in jarray)
                {
                    var fnumber = Convert.ToString(item["fnumber"]);
                    var fimage = Convert.ToString(item["fimage"]);
                    sql.Add($@"/*dialect*/UPDATE t_bd_material  set fimage='{fimage}' where fnumber='{fnumber}'");
                    //每200条更新一次
                    if (sql.Count % 200 == 0)
                    {
                        dbSvc.ExecuteBatch(this.Context, sql);
                        sql = new List<string>();
                    }
                }
                if (sql != null && sql.Count > 0)
                {
                    dbSvc.ExecuteBatch(this.Context, sql);
                }

                //异步执行
                Task.Run(() =>
                {
                    this.RelationUpdateSelTypeImage(dbSvc, jarray);
                });

                this.Result.IsSuccess = true;
                this.Result.SimpleMessage = "操作成功！";
            }
            catch (Exception ee)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "操作失败！";
            }
        }

        /// <summary>
        /// 根据【商品编码】查找关联的《型号》，然后覆盖更新【型号.商品主图】字段。
        /// 并且需查找同一《型号》的其他商品，一起更新【商品主图】。
        /// </summary>
        private void RelationUpdateSelTypeImage(IDBServiceEx dbSvc, JArray jarray)
        {
            try
            {
                var fnumbers = jarray.Select(x => Convert.ToString(x["fnumber"])).Distinct();
                if (fnumbers != null && fnumbers.Count() > 0)
                {
                    var sql = $@"/*dialect*/update t2 set t2.fimage=t1.fimage,t2.fimage_txt=t1.fimage_txt 
                                            from t_bd_material t1 with(nolock)
                                            inner join t_sel_type t2 with(nolock) on t1.fseltypeid =t2.fid
                                            where t1.fnumber in ({fnumbers.JoinEx(",", true)})";
                    dbSvc.Execute(this.Context, sql);
                    var sqlstr = $@"/*dialect*/update t1 set t1.fimage=t2.fimage,t1.fimage_txt=t2.fimage_txt
                                            from t_bd_material t1 with(nolock)
                                            inner join t_sel_type t2 with(nolock) on t1.fseltypeid =t2.fid
                                            where t1.fseltypeid in (select distinct(fseltypeid) from t_bd_material where fnumber in ({fnumbers.JoinEx(",", true)}))
                                            and t1.fnumber not in ({fnumbers.JoinEx(",", true)})";
                    dbSvc.Execute(this.Context, sqlstr);
                }
            }
            catch (Exception ex)
            {
                var message = $"{this.HtmlForm.Caption}【fimage - 商品主图】字段值更新时，关联更新【型号.商品主图】时出现错误。";
                this.LogServiceEx.Error(message, ex);
            }
        }
    }
}
