using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 列表页面左侧树形控件取数插件
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    public class QueryListTree : IListTreeDataProvider
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 获取树形根节点对象
        /// </summary>
        /// <param name="userCtx">当前登录用户上下文</param>
        /// <param name="form">表单对象</param>
        /// <returns>返回树形根节点对象，根节点对象中包括子节点列表</returns>
        public    ListTreeNode GetTreeDataSource(UserContext userCtx, HtmlForm form, OperateOption option)
        { 
            //根节点
            ListTreeNode rootNode = new ListTreeNode()
            {
                Id = "all",
                Name = "所有",
                Filter = "",
                Children = new List<ListTreeNode>()
            };

            //所有类别
            IEnumerable<DynamicObject> allCategorys = this.GetMyCategorys(userCtx);
            if (allCategorys == null || allCategorys.Count() <= 0) { return rootNode; }
            var isAppsOnly = false;
            option.TryGetVariableValue <Boolean>("IsAppsOnly",out isAppsOnly);
            if (isAppsOnly)
            {
                allCategorys = allCategorys.Where(x => Convert.ToString(x["fdisplayinapps"]) == "1");
            }

            foreach (var category in allCategorys)
            {
                //所有一级类别，只要是 fparentid 为空的都认为是一级类别
                if (category["fparentid"].IsNullOrEmptyOrWhiteSpace())
                {
                    ListTreeNode parentNode = new ListTreeNode()
                    {
                        Id = category["id"] as string,
                        Name = category["fname"] as string,
                        Filter = $"",
                        Children = new List<ListTreeNode>()
                    };

                    rootNode.Children.Add(parentNode);

                    List<string> childIds = new List<string>();
                    childIds.Add(category["id"] as string);

                    //递归加载子类别
                    this.LoadChildCategorys(allCategorys, category["id"] as string, parentNode, childIds);

                    parentNode.Filter = $"fcategoryid in('" + string.Join("','", childIds) + "')";
                }
            }

            return rootNode;
        }

        /// <summary>
        /// 递归加载子类别
        /// </summary>
        /// <param name="allCategorys">所有类别</param>
        /// <param name="parentId">父级类别ID</param>
        /// <param name="node">父节点对象</param>
        protected  void LoadChildCategorys(IEnumerable<DynamicObject> allCategorys, string parentId, ListTreeNode node, List<string> childIds)
        {
            IEnumerable<DynamicObject> childCategorys = allCategorys.Where(o => o["fparentid"] as string == parentId);
            if (childCategorys != null && childCategorys.Count() > 0)
            {
                foreach (DynamicObject childCategory in childCategorys)
                {
                    ListTreeNode childNode = new ListTreeNode()
                    {
                        Id = childCategory["id"] as string,
                        Name = childCategory["fname"] as string,
                        Filter = $"",
                        Children = new List<ListTreeNode>()
                    };

                    node.Children.Add(childNode);

                    List<string> tempChildIds = new List<string>();
                    tempChildIds.Add(childCategory["id"] as string);

                    //递归加载子类别
                    this.LoadChildCategorys(allCategorys, childCategory["id"] as string, childNode, tempChildIds);

                    //加上所有子类别
                    childIds.AddRange(tempChildIds);

                    childNode.Filter = $"fcategoryid in('" + string.Join("','", tempChildIds) + "')";
                }
            }
        }

        /// <summary>
        /// 获取所有类别
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        protected List<DynamicObject> GetMyCategorys(UserContext userCtx)
        {
            var result = new List<DynamicObject>();
            var all = GetAllCategorys(userCtx);
            var auth = GetAuthCategorys(userCtx);
            foreach (var item in auth)
            {
                result.Add(item);

                var parentId = Convert.ToString(item["fparentid"]);
                if (!parentId.IsNullOrEmptyOrWhiteSpace())
                {
                    var prentObjs = new List<DynamicObject>();
                    LoadParentCategorys(all, parentId, prentObjs);
                    if (prentObjs.Any())
                    {
                        result.AddRange(prentObjs);
                    }
                }
            }

            var grp = result.GroupBy(f => Convert.ToString(f["id"])).ToList();
            var ret = (from p in grp
                       select p.ToList().FirstOrDefault()).ToList();

            return ret;
        }

        /// <summary>
        /// 递归加载父类别
        /// </summary>
        /// <param name="allCategorys">所有类别</param>
        /// <param name="parentId">类别ID</param> 
        private void LoadParentCategorys(IEnumerable<DynamicObject> allCategorys, string parentId, List<DynamicObject> parentObjs)
        {
            var parent = allCategorys.FirstOrDefault(o => o["id"] as string == parentId);
            if (parent == null)
            {
                return;
            }
            else
            {
                parentObjs.Add(parent);

                var xx = Convert.ToString(parent["fparentid"]);
                if (!xx.IsNullOrEmptyOrWhiteSpace())
                {
                    var yy = new List<DynamicObject>();
                    LoadParentCategorys(allCategorys, xx, yy);
                    parentObjs.AddRange(yy);
                }
            }
        }


        /// <summary>
        /// 获取所有类别
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetAuthCategorys(UserContext userCtx)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "ydj_category");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

            var authIds = userCtx.GetAuthProductDataPKID(null);
            var allCategoryPkIdSql = @"  select t1.fcategoryid as fid
                        from T_BD_MATERIAL t1 with(nolock) 
                        inner join ({0}) t2 on t1.fid = t2.FPKId 
                        union all	 
                        select fid as fid from ser_ydj_category with(nolock) where fmainorgid='{1}' ".Fmt(authIds, userCtx.Company);
            var allPkIds = this.DBService.ExecuteDynamicObject(userCtx, allCategoryPkIdSql, null)
                .Select(o => Convert.ToString(o[0]))
                .Distinct()
                .ToList();

            // 按需加载数据库字段
            var dynObjs = userCtx.LoadBizBillHeadDataById("ydj_category", allPkIds, "fname,fparentid,fdisplayinapps")
                ?.OfType<DynamicObject>()
                ?? new List<DynamicObject>();

            return dynObjs;
        }

        /// <summary>
        /// 获取所有类别
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetAllCategorys(UserContext userCtx)
        {
            var htmlForm = this.MetaModelService.LoadFormModel(userCtx, "ydj_category");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

            var allCategoryPkIdSql = @"select {0} from {1} with(nolock) where fmainorgid='{2}' or fmainorgid='{3}'"
                                .Fmt(htmlForm.HeadEntity.PkFieldName, htmlForm.HeadEntity.TableName, userCtx.Company, userCtx.TopCompanyId);

            var allPkIds = this.DBService.ExecuteDynamicObject(userCtx, allCategoryPkIdSql, null)
                .Select(o => Convert.ToString(o[0]))
                .ToList();

            // 按需加载数据库字段
            var dynObjs = userCtx.LoadBizBillHeadDataById("ydj_category", allPkIds, "fname,fparentid,fdisplayinapps")
                ?.OfType<DynamicObject>()
                ?.OrderBy(o => Convert.ToString(o["fname"]))
                ?.ToList()
                ?? new List<DynamicObject>();

            return dynObjs;
        }


    }
}