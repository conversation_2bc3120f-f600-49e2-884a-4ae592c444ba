using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：根据主键和辅助属性查询商品的图片
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("getimages")]
    public class GetImages : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var dataEntity = e.DataEntitys[0];
            var productId = Convert.ToString(dataEntity["id"]);

            if (string.IsNullOrWhiteSpace(productId))
            {
                return;
            }

            var profileService = this.Container.GetService<ISystemProfile>();
            var onlyProductImg = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fonlyproductimg", false);
            var fimage = string.Empty;

            if (onlyProductImg)//取主图
            {
                fimage = getProductImages(productId);
            }
            else//取图库（注意：图库没有取主图）
            {
                StringBuilder sql = new StringBuilder("select c.fimage from T_Ydj_Commoditygallery c where c.fmainorgid = @fmainorgid and c.fproductid = @fproductid ");
                List<SqlParam> sqlParams = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company),
                    new SqlParam("@fproductid",System.Data.DbType.String,productId)
                };

                var isEmptyAttrInfo = buildingAttrInfoSql(sql, sqlParams);
                var isEmptyCustomDesc = buildingCustomDescSql(sql, sqlParams);

                var dbService = this.Container.GetService<IDBService>();
                using (var dataReader = dbService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
                {
                    if (dataReader.Read())
                    {
                        fimage = Convert.ToString(dataReader[0]);
                    }
                }

                //先取图库再取主图（图库没有取到的情况下再取商品主图）
                var enableProductImg = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenableproductimg", false);
                if (string.IsNullOrWhiteSpace(fimage) && ((isEmptyAttrInfo && isEmptyCustomDesc) || enableProductImg))
                {
                   fimage = GetMainPropImages(this.Context, dataEntity);
                    if (fimage.IsNullOrEmptyOrWhiteSpace())
                    {
                        fimage = getProductImages(productId);
                    }                  
                }
            }

            var imageIds = fimage;
            if (string.IsNullOrWhiteSpace(fimage) == false)
            {
                fimage = string.Join(",", fimage.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries).Select(x => x.GetSignedFileUrl(false)));
            }

            this.Result.SimpleData.Add("imageIds", imageIds);
            this.Result.SimpleData.Add("imageUrls", fimage);
            this.Result.IsSuccess = string.IsNullOrWhiteSpace(fimage) == false;
            this.Result.SrvData = fimage;
        }

        private string getProductImages(string productId)
        {
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            var dataEntity = dm.Select(productId) as DynamicObject;
            if (dataEntity == null)
            {
                return string.Empty;
            }
            return Convert.ToString(dataEntity["fimage"]);
        }

        private bool buildingAttrInfoSql(StringBuilder sql, List<SqlParam> sqlParams)
        {
            string attrInfoArg = this.GetQueryOrSimpleParam<string>("attrInfo");           
            string emptyAttrInfoSql = " and c.fattrinfo='' ";            
            string attrInfoSql = @"
select pe.FEntryId from T_BD_AUXPROPVALUEENTRY pe inner join T_BD_AUXPROPVALUE p on p.fid=pe.fid
where p.fmainorgid = c.fmainorgid and p.fmaterialid = c.fproductid and pe.fid=c.fattrinfo and pe.fauxpropid = @fauxpropid{0} and pe.fvalueid = @fvalueid{0}
";
            string attrInfoSqlWrap = @"
and (
select count(1) from (
	{0}
) pt
)=@fauxpropcount 
and (
select count(pe.FEntryId) from T_BD_AUXPROPVALUEENTRY pe inner join T_BD_AUXPROPVALUE p on p.fid=pe.fid
    where p.fmainorgid=c.fmainorgid and p.fmaterialid=c.fproductid and p.fid=c.fattrinfo
)=@fauxpropcount
";
            //辅助属性
            if (string.IsNullOrWhiteSpace(attrInfoArg))
            {
                sql.Append(emptyAttrInfoSql);
                return true;
            }

            JObject attrInfo = JObject.Parse(attrInfoArg);
            if (attrInfo == null)
            {
                sql.Append(emptyAttrInfoSql);
                return true;
            }
            string attrInfoId = (string)attrInfo["id"];
            if (string.IsNullOrWhiteSpace(attrInfoId) == false)
            {
                sql.Append(" and c.fattrinfo=@fattrinfo ");
                sqlParams.Add(new SqlParam("@fattrinfo", System.Data.DbType.String, attrInfoId));
                return false;
            }
            JArray entities = attrInfo["entities"] as JArray;
            if (entities == null || entities.Count <= 0)
            {
                sql.Append(emptyAttrInfoSql);
                return true;
            }           
            var auxPropList = entities.Distinct(x => (string)x["auxPropId"]).ToList();
            var auxPropListSql = string.Join("union all", auxPropList.Select((x, i) => string.Format(attrInfoSql, i)));
            sqlParams.AddRange(auxPropList.SelectMany((x, i) => new[]
            {
                new SqlParam($"fauxpropid{i}", System.Data.DbType.String, (string)x["auxPropId"]),
                new SqlParam($"fvalueid{i}", System.Data.DbType.String, (string)x["valueId"])
            }));
            sqlParams.Add(new SqlParam("@fauxpropcount", System.Data.DbType.Int32, auxPropList.Count));
            sql.AppendFormat(attrInfoSqlWrap, auxPropListSql);
            return false;
        }

        private bool buildingCustomDescSql(StringBuilder sql, List<SqlParam> sqlParams) {
            string emptyCustomDescSql = " and c.fcustomdesc='' ";
            string customDescArg = this.GetQueryOrSimpleParam<string>("customdesc");
            //定制说明
            if (string.IsNullOrWhiteSpace(customDescArg))
            {
                sql.Append(emptyCustomDescSql);
                return true;

            }

            if (string.IsNullOrWhiteSpace(customDescArg) == false)
            {
                sql.Append(" and c.fcustomdesc=@fcustomdesc ");
                sqlParams.Add(new SqlParam("@fcustomdesc", System.Data.DbType.String, customDescArg));
                return false;
            }
            return false;
        }

        /// <summary>
        /// 根据主要属性取商品图库图片
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productObj"></param>
        /// <returns></returns>
        private  string GetMainPropImages(UserContext userCtx, DynamicObject productObj)
        {

            var image = "";//返回的图片地址
            string attrInfoArg = this.GetQueryOrSimpleParam<string>("attrInfo");
            JObject attrInfo = JObject.Parse(attrInfoArg);
            JArray entities = attrInfo["entities"] as JArray;
            var auxPropVals = new List<Dictionary<string, string>>();
            foreach (var item in entities)
            {

                Dictionary<string, string> dic = new Dictionary<string, string>();
                dic.Add("auxPropId", Convert.ToString(item["auxPropId"]));
                dic.Add("valueId", Convert.ToString(item["valueId"]));
                auxPropVals.Add(dic);
            }
            //获取主要属性
            var mainauxPropVals = GetMianAuxProp(userCtx, auxPropVals);
            if (mainauxPropVals == null || mainauxPropVals.Count <= 0)
            {
                return null;
            }
            if (auxPropVals != null && auxPropVals.Any())//根据辅助属性值
            {
                List<SqlParam> pars = new List<SqlParam>();
                pars.Add(new SqlParam("@fproductid", System.Data.DbType.String, Convert.ToString(productObj?["id"])));

                var insql = "(";
                // var index = 0;
                foreach (var item in auxPropVals)
                {
                    insql += $@"'{item["auxPropId"]}',";
                }
                insql = insql.TrimEnd(',') + ")";

                var sql = $@" select b.fid as auxpropid,a.fvalueid,a.fid,a.fimage,a.fimage_txt,comid from (
                                    select a1.fauxpropid,b1.fimage,a1.fvalueid,a1.fid,b1.fimage_txt,b1.fid as comid from T_BD_AUXPROPVALUEENTRY as a1 left join T_Ydj_Commoditygallery as b1 on a1.fid=b1.fattrinfo where b1.fproductid=@fproductid
                                     )a left join (
                                     select fid from t_sel_prop where 
                                    fid in {insql}
                                    and fmainprop='1'
                                    )b on a.fauxpropid=b.fid ";
                var dbService = userCtx.Container.GetService<IDBService>();
                var res = dbService.ExecuteDynamicObject(userCtx, sql, pars);
                if (res != null && res.Any())
                {
                    Dictionary<string, DynamicObjectCollection> objdic = new Dictionary<string, DynamicObjectCollection>();
                    var delcomids = new List<string>();
                    foreach (var item in res)
                    {
                        var fid = Convert.ToString(item["comid"]);
                        var auxpropid = Convert.ToString(item["auxpropid"]);
                        if (!objdic.ContainsKey(fid))
                        {
                            objdic.Add(fid, new DynamicObjectCollection(item.DynamicObjectType));
                        }
                        objdic[fid].Add(item);
                        if (auxpropid.IsNullOrEmpty())
                        {
                            delcomids.Add(fid);
                        }
                    }
                    if (delcomids.Count>0)//去掉含有非主属性的数据
                    {
                        foreach (var item in delcomids)
                        {
                            objdic.Remove(item);
                        }
                    }
                    if (objdic.Count > 0)
                    {

                        foreach (var item in objdic)//按商品图库id循环商品图库分组
                        {
                            var equalscount = 0;//匹配成功次数

                            foreach (var obj in item.Value)//循环每个商品图库的辅助属性，把每个商品图片的辅助属性的主属性值和输入的辅助属性值对比，如果包含在内，则返回对应的图片
                            {
                                var newobj = obj as DynamicObject;
                                var auxpropid = Convert.ToString(newobj[0]);
                                var fvalueid = Convert.ToString(newobj[1]);
                                var resdic = new Dictionary<string, string>();
                                resdic.Add("auxPropId", auxpropid);
                                resdic.Add("valueId", fvalueid);
                                if (!CheckAuxProp(auxPropVals, resdic))//判断输入的商品主属性是否和商品图库的辅助属性匹配。
                                {
                                    break;
                                }
                                equalscount++;
                            }
                            if (equalscount == mainauxPropVals.Count)//如果每个主属性都相等，则返回该商品图库行的图片
                            {
                                var newobj = item.Value[0] as DynamicObject;
                                 image = Convert.ToString(newobj["fimage"]);
                                 return image;
                            }
                        }

                        //如果主要属性取不到值，则通过单个主要属性取值
                        var tuple = GetOneMainPropImages(userCtx, auxPropVals, objdic, image, mainauxPropVals);
                        if (tuple!=null&&!tuple.Item1.IsNullOrEmptyOrWhiteSpace())
                        {
                            image = tuple.Item1;
                            return image;
                        }
                    }
                }
            }


            return image;
        }

        /// <summary>
        /// 根据唯一的主要属于获取图片
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        /// <param name="objdic"></param>
        /// <param name="image"></param>
        /// <returns></returns>
        private static Tuple<string, string> GetOneMainPropImages(UserContext userCtx, List<Dictionary<string, string>> auxPropVals, Dictionary<string, DynamicObjectCollection> objdic, string image, List<Dictionary<string, string>> mainauxPropVals)
        {
            if (image.IsNullOrEmptyOrWhiteSpace())
            {
                //从图库结果筛选出只有一个主要属性的记录
                foreach (var item in objdic)
                {
                    var val = item.Value;
                    if (val.Count == 1)//只计算只有一个主要属性的记录
                    {
                        foreach (var item2 in mainauxPropVals)
                        {
                            if (item2.ContainsKey("auxPropId") && item2.ContainsKey("valueId"))
                            {
                                var resdic = val[0] as DynamicObject;
                                if (item2["auxPropId"] == Convert.ToString(resdic["auxpropid"]) && item2["valueId"] == Convert.ToString(resdic["fvalueid"]))
                                {
                                    image = Convert.ToString(resdic["fimage"]);
                                    string image_txt = Convert.ToString(resdic["fimage_txt"]);
                                    return new Tuple<string, string>(image, image_txt);
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }

        private  bool CheckAuxProp(List<Dictionary<string, string>> auxPropVals, Dictionary<string, string> resdic)
        {
            foreach (var item in auxPropVals)
            {
                if (item.ContainsKey("auxPropId") && item.ContainsKey("valueId"))
                {
                    if (item["auxPropId"] == resdic["auxPropId"] && item["valueId"] == resdic["valueId"])
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 根据请求的属性组合，把非主要属性排除掉
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        /// <returns></returns>
        private static List<Dictionary<string, string>> GetMianAuxProp(UserContext userCtx, List<Dictionary<string, string>> auxPropVals)
        {
            var dbService = userCtx.Container.GetService<IDBService>();
            //筛选主要属性
            var sql2 = "select fid from t_bd_auxproperty where fmainprop='1'";
            var res2 = dbService.ExecuteDynamicObject(userCtx, sql2);
            List<Dictionary<string, string>> mainauxPropVals = new List<Dictionary<string, string>>();
            if (res2 != null && res2.Any())
            {
                List<string> mainpropids = new List<string>();
                foreach (var item in res2)
                {
                    mainpropids.Add(Convert.ToString(item["fid"]));
                }
                foreach (var item in auxPropVals)
                {
                    var auxPropId = item["auxPropId"];
                    if (mainpropids.Contains(auxPropId))
                    {
                        mainauxPropVals.Add(item);
                    }
                }

            }
            return mainauxPropVals;
        }

    }
}
