using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;

namespace JieNor.AMS.YDJ.Stock.AppService.Plugin.BAS.PrintTmpl
{
    [InjectService]
    [FormId("bas_officetmpl")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 增加删除校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return AllowDelte(this.Context,newData);
            }).WithMessage("总部打印模板【{0}】不允许删除!", (billObj, propObj) => propObj["fnumber"]));
        }

        public bool AllowDelte(UserContext ctx,DynamicObject newData) 
        {
            var fmainorgid = Convert.ToString(newData["fmainorgid"]);
            //总部用户可以删除
            if (ctx.IsTopOrg)
            {
                return true;
            }
            else 
            {
                if (fmainorgid.EqualsIgnoreCase(ctx.Company))
                {
                    return true;
                }
                else {
                    return false;
                }
            }
        }
    }
}
