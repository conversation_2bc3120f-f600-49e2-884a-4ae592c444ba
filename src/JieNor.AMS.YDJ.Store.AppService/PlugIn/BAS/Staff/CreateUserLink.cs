using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Staff
{
    [InjectService]
    [FormId("ydj_staff")]
    [OperationNo("createuserlink")]
    public class CreateUserLink : AbstractOperationServicePlugIn
    {
        
        /// <summary>
        /// 将当前企业所有员工默认关联至用户
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)        
        {
            var strSql = $@"
update t_bd_staff as t0 set (flinkuserid)=(
	select t1.fid 
	from t_sec_user t1 
	where t1.fmainorgid=t0.fmainorgid 
            and ((t0.fphone=t1.fphone and t0.fphone<>'') or (t0.femail=t1.femail and t0.femail<>'') or (t0.fwechat=t1.fwechatid and t0.fwechat<>'')) and t0.flinkuserid=''
            and t0.fmainorgid=@companyId
)
";
            var dbServiceEx = this.Container.GetService<IDBServiceEx>();
            dbServiceEx.Execute(this.Context, strSql, new SqlParam("companyId", System.Data.DbType.String, this.Context.Company));
             
            this.Result.IsShowMessage = true;
            this.Result.IsSuccess = true;
            
        }
    }
}
