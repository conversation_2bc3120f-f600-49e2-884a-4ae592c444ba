using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Brand
{
    /// <summary>
    /// 品牌：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_brand")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //监测慕思品牌字段是否改变，
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
            List<string> sql = new List<string>();
            foreach (var item in e.DataEntitys)
            {
                var fid = item["id"] as string;
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(fid));
                if (existSnapshot != null)
                {
                    if (!(item["fmusibrand"] as string).EqualsIgnoreCase(existSnapshot["fmusibrand"] as string))
                    {
                        //联动修改关联品牌的系列（慕思系列标记）
                        sql.Add($@"/*dialect*/update t_ydj_series set fmusiseries='{item["fmusibrand"] as string}',fmodifydate=GETDATE() where fbrandid='{fid}'");
                        //联动修改关联品牌的商品（慕思商品标记）
                        sql.Add($@"/*dialect*/update t_bd_material set fismusiproduct='{item["fmusibrand"] as string}',fmodifydate=GETDATE() where fbrandid='{fid}'");
                    }
                }
            }
            if (sql != null && sql.Count > 0)
            {
                var dbSvc = this.Container.GetService<IDBServiceEx>();
                dbSvc.ExecuteBatch(this.Context, sql);
            }


            ChangWriteLog(e.DataEntitys);
        }


        /// <summary>
        /// 品牌修改记录日志
        /// 操作日志-操作描述记录是否慕思品牌原值和新值。
        /// </summary>
        /// <param name="dataEntities"></param>
        private void ChangWriteLog(DynamicObject[] dataEntities)
        {
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
            foreach (var item in dataEntities)
            {
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                if (existSnapshot != null)
                {
                    if (Convert.ToString(existSnapshot["fmusibrand"]) != Convert.ToString(item["fmusibrand"]))
                    {
                        this.Logger.WriteLog(this.Context, new LogEntry
                        {
                            BillIds = item["id"] as string,
                            BillNos = item["fnumber"] as string,
                            BillFormId = this.HtmlForm.Id,
                            OpName = "品牌保存",
                            OpCode = this.OperationNo,
                            Content = "执行了【保存】操作，【慕思品牌】发生变化，原值：{0}；新值：{1}。".Fmt(GetName(existSnapshot["fmusibrand"]), GetName(item["fmusibrand"])),
                            DebugData = "执行了【保存】操作，【慕思品牌】发生变化，原值：{0}；新值：{1}。".Fmt(GetName(existSnapshot["fmusibrand"]), GetName(item["fmusibrand"])),
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03
                        });
                    }
                }
            }
        }

        private string GetName(object id)
        {
            var name = string.Empty;
            switch (id)
            {
                case "0":
                    name = "否";
                    break;
                case "1":
                    name = "是";
                    break;
                default:
                    break;
            }
            return name;
        }
    }
}
