using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Flow
{
    /// <summary>
    /// 流程节点测试示例
    /// 展示如何在不同场景中判断当前节点是否为倒数第二个节点
    /// </summary>
    public class FlowNodeTestExample
    {
        private readonly UserContext _context;
        private readonly HtmlForm _htmlForm;

        public FlowNodeTestExample(UserContext context, HtmlForm htmlForm)
        {
            _context = context;
            _htmlForm = htmlForm;
        }

        /// <summary>
        /// 测试方法：演示各种节点判断功能
        /// </summary>
        /// <param name="dataEntity">数据实体</param>
        public void TestNodePositionDetection(DynamicObject dataEntity)
        {
            var flowNodeService = new FlowNodeInfoService(_context, _htmlForm);

            Console.WriteLine("=== 流程节点位置检测测试 ===");

            // 测试1：简单判断是否为倒数第二个节点
            TestIsSecondToLastNode(flowNodeService, dataEntity);

            // 测试2：判断是否为最后一个节点
            TestIsLastNode(flowNodeService, dataEntity);

            // 测试3：获取详细的节点位置信息
            TestGetNodePositionInfo(flowNodeService, dataEntity);

            // 测试4：获取完整的流程节点信息
            TestGetFlowNodeInfo(flowNodeService, dataEntity);

            // 测试5：模拟业务逻辑处理
            TestBusinessLogicProcessing(flowNodeService, dataEntity);
        }

        /// <summary>
        /// 测试是否为倒数第二个节点
        /// </summary>
        private void TestIsSecondToLastNode(FlowNodeInfoService service, DynamicObject dataEntity)
        {
            Console.WriteLine("\n--- 测试1：判断是否为倒数第二个节点 ---");
            
            try
            {
                bool isSecondToLast = service.IsCurrentNodeSecondToLast(dataEntity);
                Console.WriteLine($"当前节点是否为倒数第二个节点: {isSecondToLast}");
                
                if (isSecondToLast)
                {
                    Console.WriteLine("✅ 检测到倒数第二个节点，可以执行预处理逻辑");
                }
                else
                {
                    Console.WriteLine("❌ 当前不是倒数第二个节点");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试是否为最后一个节点
        /// </summary>
        private void TestIsLastNode(FlowNodeInfoService service, DynamicObject dataEntity)
        {
            Console.WriteLine("\n--- 测试2：判断是否为最后一个节点 ---");
            
            try
            {
                bool isLastNode = service.IsCurrentNodeLast(dataEntity);
                Console.WriteLine($"当前节点是否为最后一个节点: {isLastNode}");
                
                if (isLastNode)
                {
                    Console.WriteLine("✅ 检测到最后一个节点，可以执行完成逻辑");
                }
                else
                {
                    Console.WriteLine("❌ 当前不是最后一个节点");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试获取节点位置信息
        /// </summary>
        private void TestGetNodePositionInfo(FlowNodeInfoService service, DynamicObject dataEntity)
        {
            Console.WriteLine("\n--- 测试3：获取节点位置信息 ---");
            
            try
            {
                var positionInfo = service.GetNodePositionInfo(dataEntity);
                
                Console.WriteLine($"当前节点名称: {positionInfo.NodeName ?? "未知"}");
                Console.WriteLine($"当前节点ID: {positionInfo.CurrentNodeId ?? "未知"}");
                Console.WriteLine($"下一个节点名称: {positionInfo.NextNodeName ?? "未知"}");
                Console.WriteLine($"下一个节点ID: {positionInfo.NextNodeId ?? "未知"}");
                Console.WriteLine($"节点位置描述: {positionInfo.PositionDescription}");
                Console.WriteLine($"是否需要执行特殊逻辑: {positionInfo.ShouldExecuteSpecialLogic}");
                Console.WriteLine($"完整信息: {positionInfo}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试获取完整流程节点信息
        /// </summary>
        private void TestGetFlowNodeInfo(FlowNodeInfoService service, DynamicObject dataEntity)
        {
            Console.WriteLine("\n--- 测试4：获取完整流程节点信息 ---");
            
            try
            {
                var flowNodeInfo = service.GetCurrentFlowNodeInfo(dataEntity);
                
                Console.WriteLine($"流程实例ID: {flowNodeInfo.FlowInstanceId ?? "未知"}");
                Console.WriteLine($"当前节点ID: {flowNodeInfo.CurrentNodeId ?? "未知"}");
                Console.WriteLine($"节点名称: {flowNodeInfo.NodeName ?? "未知"}");
                Console.WriteLine($"节点状态: {flowNodeInfo.NodeStatus ?? "未知"}");
                Console.WriteLine($"是否为最后节点: {flowNodeInfo.IsLastNode}");
                Console.WriteLine($"下一个节点是否为最后节点: {flowNodeInfo.IsNextNodeLast}");
                Console.WriteLine($"当前节点是否为倒数第二个: {flowNodeInfo.IsCurrentNodeSecondToLast}");
                Console.WriteLine($"状态是否完成: {flowNodeInfo.IsStateComplete}");
                Console.WriteLine($"流程定义ID: {flowNodeInfo.FlowDefinitionId ?? "未知"}");
                Console.WriteLine($"流程版本: {flowNodeInfo.FlowVersion ?? "未知"}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试业务逻辑处理
        /// </summary>
        private void TestBusinessLogicProcessing(FlowNodeInfoService service, DynamicObject dataEntity)
        {
            Console.WriteLine("\n--- 测试5：模拟业务逻辑处理 ---");
            
            try
            {
                var positionInfo = service.GetNodePositionInfo(dataEntity);
                
                // 根据节点位置执行不同的业务逻辑
                if (positionInfo.IsSecondToLastNode)
                {
                    Console.WriteLine("🔄 执行倒数第二个节点的业务逻辑:");
                    Console.WriteLine("  - 数据预处理");
                    Console.WriteLine("  - 发送即将完成通知");
                    Console.WriteLine("  - 准备同步数据");
                    Console.WriteLine("  - 验证数据完整性");
                    
                    // 模拟具体的业务逻辑
                    SimulateSecondToLastNodeLogic(dataEntity, positionInfo);
                }
                else if (positionInfo.IsLastNode)
                {
                    Console.WriteLine("✅ 执行最后一个节点的业务逻辑:");
                    Console.WriteLine("  - 标记为已完成");
                    Console.WriteLine("  - 发送完成通知");
                    Console.WriteLine("  - 归档数据");
                    Console.WriteLine("  - 同步到外部系统");
                    
                    // 模拟具体的业务逻辑
                    SimulateLastNodeLogic(dataEntity, positionInfo);
                }
                else
                {
                    Console.WriteLine("⏳ 执行中间节点的业务逻辑:");
                    Console.WriteLine("  - 常规审核处理");
                    Console.WriteLine("  - 流转到下一个节点");
                    
                    // 模拟具体的业务逻辑
                    SimulateMiddleNodeLogic(dataEntity, positionInfo);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 业务逻辑处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 模拟倒数第二个节点的业务逻辑
        /// </summary>
        private void SimulateSecondToLastNodeLogic(DynamicObject dataEntity, NodePositionInfo positionInfo)
        {
            Console.WriteLine($"    📋 处理单据: {dataEntity["fbillno"] ?? "未知单号"}");
            Console.WriteLine($"    🎯 当前节点: {positionInfo.NodeName}");
            Console.WriteLine($"    ➡️ 下一个节点: {positionInfo.NextNodeName}");
            Console.WriteLine($"    ⚠️ 注意: 下一步将进入最后节点，请确保数据准备完整");
        }

        /// <summary>
        /// 模拟最后一个节点的业务逻辑
        /// </summary>
        private void SimulateLastNodeLogic(DynamicObject dataEntity, NodePositionInfo positionInfo)
        {
            Console.WriteLine($"    📋 完成单据: {dataEntity["fbillno"] ?? "未知单号"}");
            Console.WriteLine($"    🎯 当前节点: {positionInfo.NodeName}");
            Console.WriteLine($"    ✅ 流程即将完成");
        }

        /// <summary>
        /// 模拟中间节点的业务逻辑
        /// </summary>
        private void SimulateMiddleNodeLogic(DynamicObject dataEntity, NodePositionInfo positionInfo)
        {
            Console.WriteLine($"    📋 处理单据: {dataEntity["fbillno"] ?? "未知单号"}");
            Console.WriteLine($"    🎯 当前节点: {positionInfo.NodeName}");
            Console.WriteLine($"    ➡️ 下一个节点: {positionInfo.NextNodeName}");
            Console.WriteLine($"    ⏳ 流程继续进行中");
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        /// <param name="dataEntity">测试数据实体</param>
        public void RunAllTests(DynamicObject dataEntity)
        {
            Console.WriteLine("开始运行流程节点位置检测测试...");
            Console.WriteLine($"测试单据: {dataEntity["fbillno"] ?? "未知单号"}");
            Console.WriteLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            
            TestNodePositionDetection(dataEntity);
            
            Console.WriteLine("\n=== 测试完成 ===");
        }
    }
}
