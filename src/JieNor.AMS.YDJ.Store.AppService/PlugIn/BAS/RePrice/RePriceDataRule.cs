using JieNor.AMS.YDJ.Core;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.RePrice
{
    /// <summary>
    /// 【分销价目表】数据权限隔离
    /// </summary>
    [InjectService]
    [FormId("ydj_reprice")]
    public class RePriceDataRule : IDataQueryRule
    {

        /// <summary>
        /// 用户所属组织是总部，则可以看到总部的商品信息;
        /// 用户所属组织是分公司，按 商品上维护的使用组织进行隔离
        /// 用户是经销商或门店的，可以看到的商品信息= 总部授权商品 + 自己建的商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formId"></param>
        /// <returns></returns>
        public IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var filter = new List<FilterRowObject>();
            filter.Add(new FilterRowObject
            {
                Id = "ftype",
                Operator = "=",
                Value = "'quote_type_04'"
            });
            //如果是二级分销用户，也能看到一级经销针对自己的分销销售价目【根据一级定义的适用客户，且客户的【对应经销商】=当前企业】
            if (ctx.IsSecondOrg)
            {
                var currentAgent = ctx.LoadBizBillHeadDataById("bas_agent", ctx.Company, "forgid");
                if (currentAgent == null)
                {
                    return filter;
                }
                var sql = $@" select p.fid fpkid
                        from t_ydj_price p 
                        inner join t_ydj_customerentry ce on p.fid=ce.fid
                        inner join t_ydj_customer c on ce.fcustomerid=c.fid
                        where p.fmainorgid='{Convert.ToString(currentAgent["forgid"])}' and c.forgid='{ctx.Company}'
                        group by p.fid
                        union all
                        select p.fid fpkid from t_ydj_price p where p.fmainorgid='{ctx.Company}'";
                filter.Add(new FilterRowObject()
                {
                    Id = "fid",
                    Operator = "exists",
                    Value = sql,
                    Logic = "or"
                });
            }
            else
            {
                var orgInfos = ProductDataIsolateHelper.GetCurrentUserOrgInfos(ctx);
                if (orgInfos == null || orgInfos.Count == 0)
                {
                    filter.Add(new FilterRowObject()
                    {
                        Id = "fid",
                        Operator = "=",
                        Value = "0",
                    });
                    return filter;
                }

                var tempView = ctx.GetAuthProductDataPKID(rulePara);
                filter.Add(new FilterRowObject()
                {
                    Id = "fproductid",
                    Operator = "exists",
                    Value = tempView,
                });
            }
            return filter;
        }

    }
}