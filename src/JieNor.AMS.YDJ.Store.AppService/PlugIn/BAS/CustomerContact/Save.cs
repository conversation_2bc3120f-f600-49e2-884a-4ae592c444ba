using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Customer;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.BAS.CustomerContact
{
    /// <summary>
    /// 客户联系人：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_customercontact")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        #region 后端插件常用事件

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {

            var errorMessage = "";
            base.PrepareValidationRules(e);

            ////错误消息
            //var errorMessage = "";

            ////涉及到查询数据库的校验逻辑统一放到校验插件中进行批量校验（性能优化）
            //e.Rules.Add(new SaveValidation());

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var phoneNo =Convert.ToString(newData["fphone"]);
                if (phoneNo.Trim().Length != 11)
                {
                    errorMessage += "手机号填写11位数字！";
                }

                if (phoneNo.IndexOf(" ") > 0)
                {
                    errorMessage += "手机号存在空格不符合规范！";
                }

                if (!CheckPhoneNum.isNumeric(phoneNo.Replace(" ", "")))
                {
                    errorMessage += "手机号存在特殊字符不符合规范！";
                }

                if (!errorMessage.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 已存在勾选的客户联系人 不允许再勾选默认
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckIsExistDef(DynamicObject newData) 
        {
            var customer = this.GetQueryOrSimpleParam<string>("customer", "");
            var customerObj =this.Context.LoadBizDataById("ydj_customer", customer);
            var entry = customerObj["fcuscontacttry"] as DynamicObjectCollection;
            //是否存在已勾选默认的 客户联系人
            bool Isexist = entry.Any(o => Convert.ToBoolean(o["fisdefault"]));
            return Isexist;
        }

        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //if (CheckIsExistDef(e.DataEntitys[0])) {
            //    throw new BusinessException($"对不起，已存在默认地址的联系人！");
            //}

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var customer = this.GetQueryOrSimpleParam<string>("customer", "");
            var entity = e.DataEntitys[0];

             entity["fid"] = customer;
            
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_customer");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            //将当前客户的其它客户联系人取消默认地址
            var sqlParam = new SqlParam[]
            {
                    new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, $"fid = '{customer}'", sqlParam);
            var customerObj = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            var ContactEntry = customerObj["fcuscontacttry"] as DynamicObjectCollection;
            //判断是否首条联系人
            if (ContactEntry.Count == 0)
            {
                entity["fseq"] = 1;
                entity["fcisfirst"] = true;
                entity["fisdefault"] = true;
            }
            else {
                entity["fseq"] = ContactEntry.Count+1;
            }

            //如果当前传过来为默认
            if (Convert.ToBoolean(entity["fisdefault"])) 
            {
                foreach (var entry in ContactEntry) 
                {
                    entry["fisdefault"] = false;
                }
                dm.Save(customerObj);
                //再将当前联系人 处理成默认地址
                entity["fisdefault"] = true;
            }

        }
        #endregion

    }
}