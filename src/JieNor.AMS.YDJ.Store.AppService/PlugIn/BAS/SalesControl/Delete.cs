using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.SalesControl
{
    /// <summary>
    /// 销售人员可销控制：删除
    /// </summary>
    [InjectService]
    [FormId("ydj_salescontrol")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
          


        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //清除缓存 
            var pubSubService = this.Container.GetService<IPubSubService>(); 
            pubSubService.PublishMessage<string>(ConstPubSubChannel.SakesControl, " "); 
        }

    }
}
