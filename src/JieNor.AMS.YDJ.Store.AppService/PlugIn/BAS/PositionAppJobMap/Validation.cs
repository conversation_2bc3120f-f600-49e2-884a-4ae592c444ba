using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PositionAppJobMap
{
    public class Validation : AbstractBaseValidation
    
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option,
            string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            string sql = $@"
                           select fmarkno,fmarkingassistant,fname from {formInfo.HeadEntity.TableName} 
                             where fmainorgid='0'  and fforbidstatus='0'";

            var dynObjs = this.DBService.ExecuteDynamicObject(userCtx, sql);

            foreach (var dataEntity in dataEntities)
            {
                string fmarkno = Convert.ToString(dataEntity["fmarkno"]);
                string fmarkingassistant = Convert.ToString(dataEntity["fmarkingassistant"]);
                if (dynObjs.Any(s => Convert.ToString(s["fmarkno"]).EqualsIgnoreCase(fmarkno)))
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"数据重复：已经存在【名称 为 '{ Convert.ToString(dataEntity["fname"])}'】的岗位 ！",
                        DataEntity = dataEntity
                    });
                }
                var infos=  dynObjs.Where(x => Convert.ToString(x["fmarkingassistant"]) == fmarkingassistant).FirstOrDefault();
                if (infos!=null)
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"此营销助手职位已被【{infos["fname"]}】岗位关联 ！",
                        DataEntity = dataEntity
                    });
                }
            }

            return result;
        }
    }
}
