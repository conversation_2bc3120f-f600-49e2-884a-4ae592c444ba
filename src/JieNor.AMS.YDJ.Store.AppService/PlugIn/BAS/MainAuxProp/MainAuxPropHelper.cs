using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.IO;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.MainAuxProp
{
    /// <summary>
    /// 主辅属性约束帮助类：定义一些通用的逻辑代码块，方便多个地方调用
    /// </summary>
    public class MainAuxPropHelper
    {
        /// <summary>
        /// 辅助属性字段的检查SQL集合
        /// </summary>
        private static List<string> AuxPropertyFieldCheckSqlList = new List<string>();

        /// <summary>
        /// 校验单据头组合字段的唯一性
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="auxPropId">属性类别ID</param>
        /// <param name="refEnumValue">属性值ID（这里指的辅助资料ID）</param>
        /// <param name="message">校验不通过时的错误消息</param>
        /// <param name="pkid">主键ID</param>
        /// <returns>是否校验通过</returns>
        public static bool CheckBillHeadUnique(UserContext userCtx, string auxPropId, string refEnumValue, out string message, string pkid = "")
        {
            message = "";

            var sqlText = new StringBuilder(@"
            select top 1 1 from t_bas_mainauxprop 
            where fmainorgid=@fmainorgid and fauxpropid=@fauxpropid and frefenumvalue=@frefenumvalue");

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fauxpropid", System.Data.DbType.String, auxPropId),
                new SqlParam("@frefenumvalue", System.Data.DbType.String, refEnumValue)
            };

            //是否是编辑
            if (!pkid.IsNullOrEmptyOrWhiteSpace())
            {
                sqlText.Append(" and fid<>@fid");
                sqlParam.Add(new SqlParam("@fid", System.Data.DbType.String, pkid));
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText.ToString(), sqlParam))
            {
                if (reader.Read())
                {
                    //如果能查到数据，说明存在重复
                    message = $"主属性信息已存在相同的属性值，请重新选择！";
                }
            }

            return message.IsNullOrEmptyOrWhiteSpace();
        }

        /// <summary>
        /// 检查指定的辅助属性值是否被业务表单引用
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="auxPropId">辅助属性类别ID</param>
        /// <param name="refEnumValues">辅助属性值集合（辅助资料ID）</param>
        /// <returns>操作结果</returns>
        public static IOperationResult CheckAuxPropValue(UserContext userCtx, string auxPropId, List<string> refEnumValues)
        {
            var result = new OperationResult();
            result.IsSuccess = true;

            if (refEnumValues == null || !refEnumValues.Any()) return result;

            var sqlText = $@"
            select distinct apv.fid from t_bd_auxpropvalue apv 
            inner join t_bd_auxpropvalueentry apve on apve.fid=apv.fid and apv.fmainorgid=@fmainorgid 
            inner join t_bd_enumdataentry ede on ede.fenumitem=apve.fvalueid 
            where apve.fauxpropid=@fauxpropid";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fauxpropid", System.Data.DbType.String, auxPropId)
            };

            if (refEnumValues.Count == 1)
            {
                sqlText += $" and ede.fentryid='{refEnumValues[0]}'";
            }
            else
            {
                sqlText += $" and ede.fentryid in('{string.Join("','", refEnumValues)}')";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            var auxPropValSetIds = dbService.ExecuteDynamicObject(userCtx, sqlText, sqlParam)
                ?.Select(o => Convert.ToString(o["fid"]))
                ?.ToList();
            if (auxPropValSetIds == null || !auxPropValSetIds.Any()) return result;

            var checkSqls = BuildAuxPropertyFieldCheckSqlList(userCtx);
            if (!checkSqls.Any()) return result;

            var whereIn = "";
            if (auxPropValSetIds.Count == 1)
            {
                whereIn = $" ='{auxPropValSetIds[0]}'";
            }
            else
            {
                whereIn = $" in('{string.Join("','", auxPropValSetIds)}')";
            }

            var checkSqls2 = new List<string>();
            foreach (var item in checkSqls)
            {
                checkSqls2.Add(item + whereIn); //拼接条件
            }
            var checkSql = string.Join(" union ", checkSqls2);
            using (var reader = dbService.ExecuteReader(userCtx, checkSql))
            {
                while (reader.Read())
                {
                    var formName = reader.GetValueToString("formName");
                    result.ComplexMessage.ErrorMessages.Add($"辅属性值已被【{formName}】引用，不允许删除！");
                }
            }

            result.IsSuccess = !result.ComplexMessage.ErrorMessages.Any();

            return result;
        }

        /// <summary>
        /// 构建辅助属性字段检查SQL
        /// </summary>
        private static List<string> BuildAuxPropertyFieldCheckSqlList(UserContext userCtx)
        {
            //如果已经分析过，则无需重复分析，直接返回已分析好的sql
            if (AuxPropertyFieldCheckSqlList.Any()) return AuxPropertyFieldCheckSqlList;

            var strMonitorPath = PathUtils.GetStartupPath();
            var strDefMdlDir = strMonitorPath.GetAppConfig("fw.defaultmdldir")?.TrimStart('/');
            if (strMonitorPath.EndsWith(@"\bin"))
            {
                strMonitorPath = strMonitorPath.ReplaceFirst(@"\bin", "");
            }
            if (strDefMdlDir.IsNullOrEmptyOrWhiteSpace()) strDefMdlDir = "mdl";
            strMonitorPath = Path.Combine(strMonitorPath, strDefMdlDir);

            string strFileFilter = "*.mdl.*html";
            var allfiles = Directory.GetFiles(strMonitorPath, strFileFilter, SearchOption.AllDirectories);
            ConcurrentDictionary<string, string> htmlFormFiles = new ConcurrentDictionary<string, string>();
            foreach (var file in allfiles)
            {
                htmlFormFiles.AddOrUpdate(Path.GetFileName(file).ToLower(), file, (fn, fp) =>
                {
                    return fp;
                });
            }

            var metaSrv = userCtx.Container.GetService<IMetaModelService>();

            foreach (var item in htmlFormFiles)
            {
                var formId = item.Key.MdlFormId();
                if (formId.IsNullOrEmptyOrWhiteSpace()) continue;

                try
                {
                    var targetForm = metaSrv.LoadFormModel(userCtx, formId);
                    if (targetForm.ElementType == HtmlElementType.HtmlForm_BaseForm
                        || targetForm.ElementType == HtmlElementType.HtmlForm_BillForm)
                    {
                        var fields = targetForm.GetFieldList();
                        foreach (var field in fields)
                        {
                            //辅助属性字段
                            if (field is HtmlAuxPropertyField 
                                && !field.Entity.TableName.IsNullOrEmptyOrWhiteSpace() 
                                && !field.FieldName.IsNullOrEmptyOrWhiteSpace())
                            {
                                var auxPropertyField = field as HtmlAuxPropertyField;
                                var sqlText = $"select '{targetForm.Caption}' formName from {field.Entity.TableName} where {field.FieldName}";
                                AuxPropertyFieldCheckSqlList.Add(sqlText);
                            }
                        }
                    }
                }
                catch { }
            }

            //把分析好的sql缓存起来
            AuxPropertyFieldCheckSqlList = AuxPropertyFieldCheckSqlList.Distinct().ToList();

            return AuxPropertyFieldCheckSqlList;
        }
    }
}