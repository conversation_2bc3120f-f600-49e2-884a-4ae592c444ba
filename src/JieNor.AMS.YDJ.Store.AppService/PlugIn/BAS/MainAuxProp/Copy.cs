using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.MainAuxProp
{
    /// <summary>
    /// 主辅属性约束：复制
    /// </summary>
    [InjectService]
    [FormId("bas_mainauxprop")]
    [OperationNo("copy")]
    public class Copy : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "aftercreateuidata":
                    New.ProcAfterCreateUiData(this.Context, e);
                    break;
            }
        }
    }
}