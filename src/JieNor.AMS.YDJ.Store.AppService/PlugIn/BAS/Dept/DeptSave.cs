using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;

using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER
{
    /// <summary>
    /// 部门保存
    /// </summary>
    [InjectService]
    [FormId("ydj_dept")]
    [OperationNo("save")]
    public class DeptSave : AbstractOperationServicePlugIn
    { 
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            //所有部门
            List<Dictionary<string, string>> allDepts = this.GetAllDepts();
            foreach (var billData in e.DataEntitys)
            {
                SetPathValue(billData, allDepts);

            }

            //历史原因需将门店对应的编码赋值到门店编码字段
            SetStoreNumber(e.DataEntitys);
        }

        private void SetStoreNumber(DynamicObject[] billDatas)
        {
            var datas = billDatas.Where(x => !Convert.ToString(x["fstore"]).IsNullOrEmptyOrWhiteSpace());
            var storeIds = datas.Select(x => Convert.ToString(x["fstore"]));
            var stores = this.Context.LoadBizDataByFilter("bas_store", "fid in ('{0}')".Fmt(storeIds.JoinEx("','", false)));
            //没有找到部门信息也不能跳走，可能是清空部门的操作，需要清空部门编码
            foreach (var data in billDatas)
            {
                var storeId = Convert.ToString(data["fstore"]);
                var store = stores?.FirstOrDefault(x => Convert.ToString(x["Id"]) == storeId);
                data["fstoreid"] = store == null ? "": store["fnumber"];
                data["fsname"] = store == null ? "" : store["fshortname"];
            }
        }

        private void SetPathValue(DynamicObject billData, List<Dictionary<string, string>> allDepts)
        {
            var pkid = Convert.ToString(billData["id"]);
            var parentId = Convert.ToString(billData["fparentid"]);

            StringBuilder sbPath = new StringBuilder();

            if (allDepts != null && allDepts.Count > 0)
            {
                //如果有上级部门
                if (!parentId.IsNullOrEmptyOrWhiteSpace())
                {
                    //把当前上级部门的所有上级部门找出来
                    List<string> parentIds = new List<string>();
                    this.LoadParentDepts(allDepts, parentId, parentIds);
                    for (int i = parentIds.Count - 1; i >= 0; i--)
                    {
                        sbPath.AppendFormat("/{0}", parentIds[i]);
                    }

                    //加上自己的上级部门
                    sbPath.AppendFormat("/{0}", parentId);
                }

                //加上自己
                sbPath.AppendFormat("/{0}", pkid);
            }
            else
            {
                //加上自己
                sbPath.AppendFormat("/{0}", pkid);
            }

            //重新设值
            billData["fpath"] = sbPath.ToString();
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */ 

            e.Rules.Add(this.RuleFor("fbillhead", data => data["fname"]).NotEmpty().WithMessage("名称不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var IsTop = this.Context.IsTopOrg;
                var fstore = newData["fstore"] as string;
                if (fstore.IsNullOrEmptyOrWhiteSpace())
                {
                    // 针对一级经销商的《部门》的【关联门店】为必录项
                    // 这里只针对一级经销商, 如果是二级分销商则不控制
                    var currentAgent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fisreseller");
                    //总部也不校验【关联门店】必填
                    if (Convert.ToString(currentAgent?["fisreseller"]) != "1" && !IsTop)
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage(@"关联门店必填！"));

            //上级部门不允许是自己
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string pkid = newData["id"] as string;
                string parentId = newData["fparentid"] as string;
                if (!pkid.IsNullOrEmptyOrWhiteSpace() && pkid.Equals(parentId, StringComparison.OrdinalIgnoreCase))
                {
                    return false;
                }
                return true;
            }).WithMessage(@"上级部门不允许是自己，请检查数据！"));

            //上级部门允许是自己的子级部门（包括子级的子级的子级...等等。）
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return CheckParentID(newData);
            }).WithMessage(@"上级部门不允许是自己的子级部门！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var isSettled = Convert.ToBoolean(newData["fissettled"]);
                if (isSettled == false)
                {
                    return true;
                }
                return false == string.IsNullOrWhiteSpace(Convert.ToString(newData["fmarketplace"]));
            }).WithMessage(@"卖场必填！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var isSettled = Convert.ToBoolean(newData["fissettled"]);
                if (isSettled == false)
                {
                    return true;
                }
                var deductRate = Convert.ToDecimal(newData["fdeductrate"]);
                return deductRate >= 0 && deductRate <= 100;
            }).WithMessage(@"商场扣点比例范围必须是0到100！"));
        }

        /// <summary>
        /// 检查“上级部门”是否合法
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckParentID(DynamicObject newData)
        {
            string pkid = newData["id"] as string;
            if (pkid.IsNullOrEmptyOrWhiteSpace()) { return true; }

            List<Dictionary<string, string>> allDepts = this.GetAllDepts();
            if (allDepts == null && allDepts.Count <= 0) { return true; }

            string parentId = newData["fparentid"] as string;
            List<string> childIds = new List<string>();

            //递归加载所有子部门
            this.LoadChildDepts(allDepts, pkid, childIds);
            if (childIds.Contains(parentId))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 递归加载指定部门的所有子部门
        /// </summary>
        /// <param name="allDepts">所有部门</param>
        /// <param name="deptId">部门ID</param>
        /// <param name="childIds">子部门ID集合</param>
        private void LoadChildDepts(List<Dictionary<string, string>> allDepts, string deptId, List<string> childIds)
        {
            var childDepts = allDepts.Where(o => o["fparentid"] == deptId).ToList();
            if (childDepts != null && childDepts.Count > 0)
            {
                foreach (var childDept in childDepts)
                {
                    var fid = childDept["fid"];
                    if (!childIds.Contains(fid))
                    {
                        childIds.Add(fid);
                    }

                    //递归加载所有子部门
                    this.LoadChildDepts(allDepts, fid, childIds);
                }
            }
        }

        /// <summary>
        /// 递归加载指定部门的所有父部门
        /// </summary>
        /// <param name="allDepts">所有部门</param>
        /// <param name="deptId">部门ID</param>
        /// <param name="parentIds">父部门ID集合</param>
        private void LoadParentDepts(List<Dictionary<string, string>> allDepts, string deptId, List<string> parentIds)
        {
            var parentDepts = allDepts.Where(o => o["fid"] == deptId).ToList();
            if (parentDepts != null && parentDepts.Count > 0)
            {
                foreach (var parentDept in parentDepts)
                {
                    var fparentid = parentDept["fparentid"];

                    //如果能找到，则说明还没有到最顶层部门，继续找
                    if (!fparentid.IsNullOrEmptyOrWhiteSpace())
                    {
                        parentIds.Add(fparentid);

                        this.LoadParentDepts(allDepts, fparentid, parentIds);
                    }
                }
            }
        }

        /// <summary>
        /// 获取所有部门（只查询 fid 和 fparentid）
        /// </summary>
        /// <returns></returns>
        private List<Dictionary<string, string>> GetAllDepts()
        {
            List<Dictionary<string, string>> deptList = new List<Dictionary<string, string>>();

            string strSql = @"select fid, fparentid from t_bd_department 
            where fmainorgid='{0}'".Fmt(this.Context.Company);

            using (var reader = this.DBService.ExecuteReader(this.Context, strSql))
            {
                while (reader.Read())
                {
                    Dictionary<string, string> dept = new Dictionary<string, string>();
                    for (int iCol = 0; iCol < reader.FieldCount; iCol++)
                    {
                        var objValue = reader[iCol];
                        var colName = reader.GetName(iCol);
                        dept[colName] = objValue.IsNullOrEmptyOrWhiteSpace() ? "" : objValue as string;
                    }
                    deptList.Add(dept);
                }
            }

            return deptList;
        }



        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            //所有部门
            List<Dictionary<string, string>> allDepts = this.GetAllDepts();
            if (allDepts == null && allDepts.Count <= 0)
            {
                return;
            }
             
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            foreach (var billData in e.DataEntitys)
            {
                string pkid = e.DataEntitys[0]["id"] as string;
                if (pkid.IsNullOrEmptyOrWhiteSpace())
                {
                    return;
                }

                List<DynamicObject> beUpdate = new List<DynamicObject>();
                //递归加载所有子部门
                List<string> childIds = new List<string>();
                this.LoadChildDepts(allDepts, pkid, childIds);
                var subDepts = dm.Select(childIds).OfType<DynamicObject>().ToList ();
                foreach (var item in childIds)
                {
                    var subDept = subDepts.FirstOrDefault(f=>f["Id"].ToString ()== item) as DynamicObject;
                    if (subDept != null)
                    {
                        SetPathValue(subDept, allDepts);
                        beUpdate.Add(subDept);
                    }
                }

                if (beUpdate.Count > 0)
                {
                    dm.Save(beUpdate, null, OperateOption.InstanceBulkCopyAndNoCache); 
                }
            }
        }



    }
}