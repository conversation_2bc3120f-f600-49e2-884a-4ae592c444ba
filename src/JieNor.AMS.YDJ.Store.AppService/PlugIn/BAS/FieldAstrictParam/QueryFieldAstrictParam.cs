using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.FieldAstrictParam
{
    [InjectService]
    [FormId("bas_fieldastrictparam")]
    [OperationNo("queryFieldAstrictParam")]
    public  class QueryFieldAstrictParam : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var data = profileService.GetSystemParameter(this.Context, "bas_fieldastrictparam");
            this.OperationContext.Result.SrvData = data;
        }
    }
}
