using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PromotionScheme
{
    /// <summary>
    /// 动态列基础资料字段弹窗模糊查询操作
    /// </summary>
    [InjectService]
    [FormId("sel_promotionform")]
    [OperationNo("FuzzyQuery")]
    public class FuzzyQuery : AbstractQueryDyn
    {
        //该插件功能逻辑在基类实现
    }
}
