using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PromotionScheme
{
    /// <summary>
    /// 促销活动：列表查询数据插件
    /// </summary>
    [InjectService]
    [FormId("bas_promotionscheme")]
    [OperationNo("querydata")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义服务端事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
            }
        }

        /// <summary>
        /// 列表准备查询过滤参数事件
        /// </summary>
        /// <param name="e"></param>
        public void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var param = e.EventData as SqlBuilderParameter;
            if (param == null) return;

            //提供额外的过滤条件：当前登录用户只能查看自己的活动数据
            if (!this.Context.IsTopOrg)
            {
                var deliverid = this.GetQueryOrSimpleParam<string>("fdeliverid", "");
                string where = "";
                var dymParam = param.DynamicParams;
                if (dymParam.Count > 0)
                {
                    deliverid = Convert.ToString(dymParam[0].Value);
                    //param.FilterString = param.FilterString.JoinFilterString($@"  ( fdeliverid='{deliverid}' or fsaleorgid=(select fsaleorgid from T_BAS_DELIVER where fid='{deliverid}')) ");
                }
                where = !string.IsNullOrWhiteSpace(deliverid) ? $" AND ( fdeliverid='{deliverid}' or fsaleorgid=(select fsaleorgid from T_BAS_DELIVER where fid='{deliverid}'))" : "";
                //else
                {
                    param.FilterString = param.FilterString.JoinFilterString($@" fid in ( select distinct fpromotionid from t_ydj_fcombinerangeentry a with(nolock) join 
																			t_bas_promotioncombine b with(nolock) on a.fid=b.fid where fagentid='{this.Context.Company}' and fisall=0 {where}
                                                                            union
                                                                            select distinct b.fpromotionid from t_ydj_fcombinerangeentry a with(nolock) 
                                                                            join  t_bas_promotioncombine b with(nolock) on a.fid=b.fid
                                                                            join t_bas_macentry c with(nolock) on a.fagentid=c.fsubagentid
                                                                            join t_bas_mac d with(nolock) on c.fid=d.fid
                                                                            join T_BAS_AGENT e  with(nolock) on d.fnumber=e.fnumber
                                                                            where e.fid='{this.Context.Company}' and fisall=0 {where}
                                                                            union 
                                                                            select distinct a.fpromotionid from t_bas_promotioncombine a with(nolock) 
                                                                           	join t_ydj_fcombinerangeentry b with(nolock) on a.fid=b.fid 
                                                                           	where fisall=1 and b.fsaleorgid in ( select  a.fsaleorgid from T_BAS_DELIVER a with(nolock)
                                                                                              join t_bas_organization b with(nolock) on a.fsaleorgid=b.fid
                                                                                              join T_BAS_AGENT c with(nolock) on a.fagentid=c.fid
                                                                                              where  c.fid='{this.Context.Company}' and a.fforbidstatus=0   {(!string.IsNullOrWhiteSpace(deliverid) ? $" AND a.fid='{deliverid}'" : "")}
                                                                                              union
                                                                                              select a.fsaleorgid  from
	                                                                                          T_BAS_DELIVER a with(nolock)
                                                                                              join t_bas_organization b with(nolock) on a.fsaleorgid=b.fid
                                                                                              join T_BAS_AGENT c with(nolock) on a.fagentid=c.fid
	                                                                                          join t_bas_macentry e with(nolock) on c.fid=e.fsubagentid
	                                                                                          join t_bas_mac d with(nolock) on e.fid=d.fid
	                                                                                          join T_BAS_AGENT f with(nolock) on d.fnumber=f.fnumber
                                                                                              where  f.fid='{this.Context.Company}' and a.fforbidstatus=0   {(!string.IsNullOrWhiteSpace(deliverid) ? $" AND a.fid='{deliverid} '" : "")}
                                                                            )
                                                                          )   and GETDATE() between t0.fbegindate and DATEADD(day,1,t0.fenddate)");
                    List<string> entryfiled1 = new List<string>() { "fdeliverid", "fdeliverid.fname", "fdelivername" };
                    if (param.SelectedFieldKeys.Intersect(entryfiled1).Count() > 0 && this.Context.BizOrgId != this.Context.TopCompanyId)
                    {
                        List<string> materials = new List<string>();
                        var pros = this.Context.LoadBizDataByFilter("bas_deliver", string.Format(" fforbidstatus = 0 and fagentid='{0}'", this.Context.BizOrgId));
                        string tmp = this.DBService.CreateTempTableWithDataList(this.Context, pros.Select(a => a["id"]).ToList());
                        param.FilterString = param.FilterString.JoinFilterString(string.Format("  (t1.fdeliverid in (select fid from {0}) OR ISNULL(t1.fdeliverid,'')='') ", tmp));//若只下发了销售组织，那送达方就是空的
                    }
                }
            }
        }

    }
}
