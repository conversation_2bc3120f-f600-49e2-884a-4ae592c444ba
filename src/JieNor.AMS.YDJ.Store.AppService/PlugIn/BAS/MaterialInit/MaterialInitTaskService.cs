using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MaterialInit
{
    /// <summary>
    /// 后台任务Excel导入服务
    /// </summary>
    [InjectService(AliasName = "Task")]
    [TaskSvrId("excel.import.materialinit")]
    [Caption("商品初始化")]
    public class MaterialInitTaskService2 : AbstractScheduleWorker
    {
        /// <summary>
        /// 一个基于BlockingCollection实现的多线程的处理队列
        /// </summary>
        private class ProcessQueue<T>
        {
            private BlockingCollection<T> _queue;
            private CancellationTokenSource _cancellationTokenSource;
            private CancellationToken _cancellToken;
            //内部线程池
            private List<Thread> _threadCollection;

            //队列是否正在处理数据
            private int _isProcessing;
            //有线程正在处理数据
            private const int _processing = 1;
            //没有线程处理数据
            private const int _unProcessing = 0;
            //队列是否可用
            private volatile bool _enabled = true;
            //内部处理线程数量
            private int _internalThreadCount;

            public event Action<T> ProcessItemEvent;
            //处理异常，需要三个参数，当前队列实例，异常，当时处理的数据
            public event Action<dynamic, Exception, T> ProcessExceptionEvent;

            public ProcessQueue()
            {
                _queue = new BlockingCollection<T>();
                _cancellationTokenSource = new CancellationTokenSource();
                _internalThreadCount = 1;
                _cancellToken = _cancellationTokenSource.Token;
                _threadCollection = new List<Thread>();
            }

            public ProcessQueue(int internalThreadCount) : this()
            {
                this._internalThreadCount = internalThreadCount;
            }

            /// <summary>
            /// 队列内部元素的数量 
            /// </summary>
            public int GetInternalItemCount()
            {
                return _queue.Count;
            }

            public void Enqueue(T items)
            {
                if (items == null)
                {
                    throw new ArgumentException("items");
                }

                _queue.Add(items);
                dataAdded();
            }

            public void Flush()
            {
                stopProcess();

                while (_queue.Count != 0)
                {
                    T item = default(T);
                    if (_queue.TryTake(out item))
                    {
                        try
                        {
                            ProcessItemEvent(item);
                        }
                        catch (Exception ex)
                        {
                            onProcessException(ex, item);
                        }
                    }
                }
            }

            private void dataAdded()
            {
                if (_enabled)
                {
                    if (!isProcessingItem())
                    {
                        processRangeItem();
                        startProcess();
                    }
                }
            }

            //判断是否队列有线程正在处理 
            private bool isProcessingItem()
            {
                return !(Interlocked.CompareExchange(ref _isProcessing, _processing, _unProcessing) == _unProcessing);
            }

            private void processRangeItem()
            {
                for (int i = 0; i < this._internalThreadCount; i++)
                {
                    processItem();
                }
            }

            private void processItem()
            {
                Thread currentThread = new Thread((state) =>
                {
                    T item = default(T);
                    while (_enabled)
                    {
                        try
                        {
                            item = _queue.Take(_cancellToken);
                            ProcessItemEvent(item);
                        }
                        catch (Exception ex)
                        {
                            onProcessException(ex, item);
                        }
                    }
                });

                _threadCollection.Add(currentThread);
            }

            private void startProcess()
            {
                foreach (var thread in _threadCollection)
                {
                    thread.Start();
                }
            }

            private void stopProcess()
            {
                this._enabled = false;
                foreach (var thread in _threadCollection)
                {
                    if (thread.IsAlive)
                    {
                        thread.Join(new TimeSpan(0, 0, 1));
                    }
                }
                _threadCollection.Clear();
            }

            private void onProcessException(Exception ex, T item)
            {
                var tempException = ProcessExceptionEvent;
                Interlocked.CompareExchange(ref ProcessExceptionEvent, null, null);

                if (tempException != null)
                {
                    ProcessExceptionEvent(this, ex, item);
                }
            }

        }

        private const string NAMEKEY = "name";
        private const string NUMBERKEY = "number";
        private const string SEQUENCE = "序号";
        private const string SONSEQUENCE = "子结点序号";
        private const string NODEINFO = "nodeInfo";
        private const string SONNODE = "sonNode";
        private const string NODESTATUS = "nodeStatus";
        private const string NEWSTATUS = "new";
        private const string PREFIXPROPERTY = "辅助属性$";
        private const string PURPRICE = "采购价(价目表)";
        private const string ERRORINFO = "errorInfo";
        private const string IMPORTRESULT = "导入结果";
        private const string ERRORREASON = "错误原因";
        private const string SUCCESS = "成功";
        private const string WARNING = "警告";
        private const string FAILURE = "失败";
        private const string IMAGEID = "imageId";

        private ProcessQueue<Tuple<string, int?, Exception, IOperationResult>> _writeLogQueue;
        private ProcessQueue<Tuple<int, string, string>> _writeExcelQueue;
        private IExcelAssistant _excelAssistant;
        private int _importResultColumnIndex;
        private int _errorReasonColumnIndex;
        private bool _isSuccess;
        private StreamWriter _logFile;

        protected override async Task DoExecute()
        {
            Dictionary<string, string> para = JsonConvert.DeserializeObject<Dictionary<string, string>>(this.TaskObject.Parameter as string);
            var fileUrl = para["fileurl"];
            var fileId = para["fileid"];
            IOperationResult result = new OperationResult();
            if (string.IsNullOrWhiteSpace(fileUrl))
            {
                result.ComplexMessage.ErrorMessages.Add("fileUrl不能为空!");
            }
            if (string.IsNullOrWhiteSpace(fileId))
            {
                result.ComplexMessage.ErrorMessages.Add("fileId不能为空!");
            }
            if (result.ComplexMessage.ErrorMessages.Count > 0)
            {
                return;
            }
            try
            {
                result = await Task.Run(() => importData(this.UserContext, fileUrl, fileId));
            }
            catch (AggregateException ex)
            {
                foreach (var single in ex.InnerExceptions)
                {
                    var msg = "导入出错";
                    setLogMsg(msg, 100, single, result);
                }
            }
            catch (Exception ex)
            {
                var msg = "导入出错";
                setLogMsg(msg, 100, ex, result);
            }
            saveLog();
            this.Result.MergeResult(result);
            this.Result.SimpleMessage = "任务执行完毕";
        }

        private IOperationResult importData(UserContext userContext, string fileUrl, string fileId)
        {
            _isSuccess = true;
            var msg = string.Empty;
            IOperationResult result = new OperationResult();
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var form = metaModelService.LoadFormModel(userContext, "ydj_product");
            var progressBase = 0;
            var increment = 10;
            var partIncrement = 0;
            var partCount = 0;

            var excelAssistantFactory = userContext.Container.GetService<IExcelAssistantFactory>();
            _excelAssistant = excelAssistantFactory.CreateExcelAssistant(fileUrl);
            try
            {
                _excelAssistant.HtmlForm = form;
                _writeExcelQueue = new ProcessQueue<Tuple<int, string, string>>();
                _writeExcelQueue.ProcessExceptionEvent += writeExcelQueue_ProcessExceptionEvent;
                _writeExcelQueue.ProcessItemEvent += writeExcelQueue_ProcessItemEvent;
                _importResultColumnIndex = _excelAssistant.ContainFieldCaption(IMPORTRESULT);
                _errorReasonColumnIndex = _excelAssistant.ContainFieldCaption(ERRORREASON);
                var excelPath = excelAssistantFactory.GetDefaultPath(fileId);
                var logPath = excelAssistantFactory.GetDefaultPath(fileId, "txt");
                _logFile = new StreamWriter(File.Open(logPath, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite));

                this.Result.SimpleData["logFileUrl"] = excelAssistantFactory.GetWebUrl(fileId, "txt");

                _writeLogQueue = new ProcessQueue<Tuple<string, int?, Exception, IOperationResult>>();
                _writeLogQueue.ProcessExceptionEvent += writeLogQueue_ProcessExceptionEvent;
                _writeLogQueue.ProcessItemEvent += writeLogQueue_ProcessItemEvent;

                if (_importResultColumnIndex < 0)
                {
                    _importResultColumnIndex = _excelAssistant.AddColumn(IMPORTRESULT);
                }

                if (_errorReasonColumnIndex < 0)
                {
                    _errorReasonColumnIndex = _excelAssistant.AddColumn(ERRORREASON);
                }

                var fields = form.GetFieldList().Where(x => _excelAssistant.ContainFieldId(x.Id) > -1).ToList();

                if (fields == null || fields.Count <= 0)
                {
                    msg = "无法从excel文件中分析出字段";
                    setLogMsg(msg, 100, result);
                    saveExcel(excelPath);
                    return result;
                }

                msg = "加载完Excel文件";
                progressBase += increment;
                setLogMsg(msg, progressBase, null);
                List<List<HtmlField>> parsedRefFieldList = new List<List<HtmlField>>();
                Dictionary<HtmlBaseDataField, List<HtmlBaseDataField>> closureRefList = new Dictionary<HtmlBaseDataField, List<HtmlBaseDataField>>();
                Dictionary<string, Dictionary<HtmlBaseDataField, HtmlBaseDataField>> formRefFieldMaps = new Dictionary<string, Dictionary<HtmlBaseDataField, HtmlBaseDataField>>();
                parseRefFields(userContext, fields, parsedRefFieldList, closureRefList, formRefFieldMaps);
                var formGroupRowEntiyMaps = new ConcurrentDictionary<string, Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>>();
                var formGroupNodeEntityMaps = new ConcurrentDictionary<string, Dictionary<Dictionary<string, string>, DynamicObject>>();

                msg = "分析完Excel文件的字段";
                progressBase += increment;
                setLogMsg(msg, progressBase, null);
                increment = 20;

                if (parsedRefFieldList != null && parsedRefFieldList.Count > 0)
                {
                    partCount = parsedRefFieldList.Count;
                    foreach (var refFields in parsedRefFieldList)
                    {
                        Parallel.ForEach(refFields, x =>
                        {
                            try
                            {
                                var rowEntityMaps = new Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>();
                                var nodeEntityMaps = new Dictionary<Dictionary<string, string>, DynamicObject>();
                                var assistant = _excelAssistant.Clone();

                                if (x is HtmlImageField || x is HtmlMulImageField)
                                {
                                    formGroupRowEntiyMaps.TryAdd(x.Id, rowEntityMaps);
                                    formGroupNodeEntityMaps.TryAdd(x.Id, nodeEntityMaps);
                                    dealImages(userContext, x, rowEntityMaps, nodeEntityMaps, assistant);
                                    return;
                                }

                                var refFiled = x as HtmlBaseDataField;
                                var refHtmlForm = metaModelService.LoadFormModel(userContext, refFiled.RefFormId);
                                var comboField = x as HtmlComboField;
                                var unitField = x as HtmlUnitField;
                                var groupKey = getGroupKey(refFiled);
                                formGroupRowEntiyMaps.TryAdd(groupKey, rowEntityMaps);
                                formGroupNodeEntityMaps.TryAdd(groupKey, nodeEntityMaps);
                                if (comboField != null)
                                {
                                    //处理辅助资料
                                    dealRefDataEntities(userContext, comboField, refHtmlForm, rowEntityMaps, assistant);
                                    return;
                                }
                                if (unitField != null)
                                {
                                    //处理单位
                                    dealUnitDataEntities(userContext, unitField, refHtmlForm, rowEntityMaps, nodeEntityMaps, assistant);
                                    return;
                                }
                                var fparentid = refHtmlForm.GetField("fparentid") as HtmlBaseDataField;
                                if (fparentid != null && fparentid.RefFormId == refHtmlForm.Id)
                                {
                                    //处理有父结点的引用资料
                                    dealRefDataEntities(userContext, refFiled, fparentid, refHtmlForm, rowEntityMaps, nodeEntityMaps, assistant, formGroupRowEntiyMaps, formGroupNodeEntityMaps, closureRefList, formRefFieldMaps);
                                    return;
                                }
                                //处理引用资料
                                dealRefDataEntities(userContext, refFiled, refHtmlForm, rowEntityMaps, nodeEntityMaps, assistant, formGroupRowEntiyMaps, formGroupNodeEntityMaps, closureRefList, formRefFieldMaps);
                                msg = string.Format("处理完{0}", x.Caption);
                                Interlocked.Increment(ref partIncrement);
                                setLogMsg(msg, progressBase + increment * partIncrement / partCount, null);
                            }
                            catch (Exception ex)
                            {
                                msg = string.Format("处理{0}时出错了!", x.Caption);
                                setLogMsg(msg, progressBase + increment * partIncrement / partCount, ex, result);
                            }
                        });

                        //    foreach (var x in refFields)
                        //    {
                        //        try
                        //        {
                        //            var rowEntityMaps = new Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>();
                        //            var nodeEntityMaps = new Dictionary<Dictionary<string, string>, DynamicObject>();
                        //            var assistant = _excelAssistant.Clone();

                        //            if (x is HtmlImageField || x is HtmlMulImageField)
                        //            {
                        //                formGroupRowEntiyMaps.TryAdd(x.Id, rowEntityMaps);
                        //                formGroupNodeEntityMaps.TryAdd(x.Id, nodeEntityMaps);
                        //                dealImages(userContext, x, rowEntityMaps, nodeEntityMaps, assistant, excelAssistantFactory);
                        //                continue;
                        //            }

                        //            var refFiled = x as HtmlBaseDataField;
                        //            var refHtmlForm = metaModelService.LoadFormModel(userContext, refFiled.RefFormId);
                        //            var comboField = x as HtmlComboField;
                        //            var unitField = x as HtmlUnitField;
                        //            var groupKey = getGroupKey(refFiled);
                        //            formGroupRowEntiyMaps.TryAdd(groupKey, rowEntityMaps);
                        //            formGroupNodeEntityMaps.TryAdd(groupKey, nodeEntityMaps);
                        //            if (comboField != null)
                        //            {
                        //                //处理辅助资料
                        //                dealRefDataEntities(userContext, comboField, refHtmlForm, rowEntityMaps, assistant);
                        //                continue;
                        //            }
                        //            if (unitField != null)
                        //            {
                        //                //处理单位
                        //                dealUnitDataEntities(userContext, unitField, refHtmlForm, rowEntityMaps, nodeEntityMaps, assistant);
                        //                continue;
                        //            }
                        //            var fparentid = refHtmlForm.GetField("fparentid") as HtmlBaseDataField;
                        //            if (fparentid != null && fparentid.RefFormId == refHtmlForm.Id)
                        //            {
                        //                //处理有父结点的引用资料
                        //                dealRefDataEntities(userContext, refFiled, fparentid, refHtmlForm, rowEntityMaps, nodeEntityMaps, assistant, formGroupRowEntiyMaps, formGroupNodeEntityMaps, closureRefList, formRefFieldMaps);
                        //                continue;
                        //            }
                        //            //处理引用资料
                        //            dealRefDataEntities(userContext, refFiled, refHtmlForm, rowEntityMaps, nodeEntityMaps, assistant, formGroupRowEntiyMaps, formGroupNodeEntityMaps, closureRefList, formRefFieldMaps);
                        //            msg = string.Format("处理完{0}", x.Caption);
                        //            Interlocked.Increment(ref partIncrement);
                        //            setLogMsg(msg, progressBase + increment * partIncrement / partCount, null);
                        //        }
                        //        catch (Exception ex)
                        //        {
                        //            msg = string.Format("处理{0}时出错了!", x.Caption);
                        //            setLogMsg(msg, progressBase + increment * partIncrement / partCount, ex, result);
                        //        }
                        //    }

                    }
                }

                if (!_isSuccess)
                {
                    msg = "导入未完成，有引用基础资料导入失败，请查看执行日志!";
                    setLogMsg(msg, 100, result);
                    saveExcel(excelPath);
                    return result;
                }

                msg = "处量完引用基础资料";
                progressBase += increment;
                setLogMsg(msg, progressBase, null);
                increment = 10;

                var parseAuxpropertyTask = Task.Factory.StartNew(() =>
                {
                    try
                    {
                        return parseAuxproperty(userContext, _excelAssistant.Clone(), metaModelService);
                    }
                    catch (Exception ex)
                    {
                        setLogMsg("分析辅助属性类别时出现异常", ex, null);
                    }
                    return new Dictionary<int, Tuple<Dictionary<string, string>, DynamicObject>>();
                });
                var parsePurpriceTask = Task.Factory.StartNew(() =>
                {
                    try
                    {
                        return parsePurprice(userContext, _excelAssistant.Clone());
                    }
                    catch (Exception ex)
                    {
                        setLogMsg("分析采购价时出现异常", ex, null);
                    }
                    return new Dictionary<string, decimal>();
                });
                var parseProductsTask = Task.Factory.StartNew(() =>
                {
                    try
                    {
                        return parseProducts(userContext, fields, form, _excelAssistant.Clone(), metaModelService, formGroupRowEntiyMaps);
                    }
                    catch (Exception ex)
                    {
                        setLogMsg("分析商品信息时出现异常", ex, null);
                    }
                    return new Dictionary<Dictionary<string, string>, DynamicObject>();
                });
                var parseAuxPropertyValueTask = Task.Factory.ContinueWhenAll(new[] { parseAuxpropertyTask }, (t) =>
                {
                    try
                    {
                        return parseAuxPropertyValue(_excelAssistant.Clone(), t[0].Result);
                    }
                    catch (Exception ex)
                    {
                        setLogMsg("分析辅助属性值时出现异常", ex, null);
                    }
                    return new Dictionary<int, Dictionary<string, List<string>>>();
                });

                try
                {
                    Task.WaitAll(parsePurpriceTask, parseProductsTask, parseAuxPropertyValueTask);
                }
                catch (Exception ex)
                {
                    msg = "分析Excel文件中的商品、辅助属性和采购价时出错了";
                    setLogMsg(msg, progressBase, ex, null);
                }

                var auxProperties = parseAuxpropertyTask.Result;
                var purprices = parsePurpriceTask.Result;
                var products = parseProductsTask.Result;
                var auxPropertyValues = parseAuxPropertyValueTask.Result;

                products = products?.Where(x => !x.Key.Keys.Contains(ERRORINFO)).ToDictionary(k => k.Key, v => v.Value);
                var ids = products.Values.Select(x => Convert.ToString(x["id"])).ToList();
                var idsCount = ids.Count;
                var disIds = ids.Distinct().ToList();
                var disCount = disIds.Count();

                if (products == null || products.Count <= 0)
                {
                    msg = "无商品导入!";
                    setLogMsg(msg, 100, result);
                    saveExcel(excelPath);
                    return result;
                }
                var productsList = splitDictionary(products);

                msg = "分析完Excel文件中的商品、辅助属性和采购价信息";
                progressBase += increment;
                setLogMsg(msg, progressBase, null);
                increment = 40;
                partIncrement = 0;
                partCount = productsList.Count;

                var fsupplierKey = getGroupKey(form.GetField("fsupplierid") as HtmlBaseDataField);
                var funitKey = getGroupKey(form.GetField("funitid") as HtmlBaseDataField);
                var fcategoryKey = getGroupKey(form.GetField("fcategoryid") as HtmlBaseDataField);
                var fbrandKey = getGroupKey(form.GetField("fbrandid") as HtmlBaseDataField);
                Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> supplierRowEntiyMaps;
                formGroupRowEntiyMaps.TryGetValue(fsupplierKey, out supplierRowEntiyMaps);

                foreach (var x in productsList)
                {
                    try
                    {
                        sendDatas(userContext, form, x);
                        var productAuxpropMaps = parseProductAuxpropMap(x, auxProperties, auxPropertyValues);
                        dealAuxpropMap(userContext, metaModelService, productAuxpropMaps, x);
                        dealAuxpropValueMap(userContext, metaModelService, productAuxpropMaps, x);
                        dealPurprice(userContext, metaModelService, auxProperties, auxPropertyValues, x, purprices, supplierRowEntiyMaps);
                        writeExcelSuccessInfo(x);
                        msg = "正在保存商品、商品辅助属性、商品采购价";
                        Interlocked.Increment(ref partIncrement);
                        setLogMsg(msg, progressBase + increment * partIncrement / partCount, null);
                    }
                    catch (Exception ex)
                    {
                        msg = string.Format("保存商品、商品辅助属性、商品采购价时出错了\r\n{0}", ex.ToString());
                        Interlocked.Increment(ref partIncrement);
                        setLogMsg(msg, progressBase + increment * partIncrement / partCount, ex, result);
                    }
                }

                msg = "正在保存导入结果!";
                progressBase += increment;
                setLogMsg(msg, progressBase, null);

                saveExcel(excelPath);

                if (_isSuccess)
                {
                    msg = "导入完成!";
                }
                else
                {
                    msg = "导入完成，但有错误发生，请查看执行日志!";
                }
                progressBase = 100;
                setLogMsg(msg, progressBase, null);
            }
            catch (Exception ex)
            {
                msg = "导入出现不可预知异常!";
                setLogMsg(msg, 100, ex, result);
            }
            finally
            {
                _excelAssistant.Dispose();
            }


            return result;
        }

        private void saveExcel(string filePath)
        {
            if (_writeExcelQueue != null)
            {
                if (_writeExcelQueue.GetInternalItemCount() > 0)
                {
                    _writeExcelQueue.Flush();
                }
                _writeExcelQueue.ProcessExceptionEvent += writeExcelQueue_ProcessExceptionEvent;
                _writeExcelQueue.ProcessItemEvent += writeExcelQueue_ProcessItemEvent;
            }
            if (_excelAssistant == null)
            {
                return;
            }
            _excelAssistant.Save(filePath);
        }

        private void writeExcelQueue_ProcessItemEvent(Tuple<int, string, string> obj)
        {
            writeExcelQueue(obj.Item1, obj.Item2, obj.Item3, null);
        }

        private void writeExcelQueue_ProcessExceptionEvent(dynamic arg1, Exception arg2, Tuple<int, string, string> arg3)
        {
            writeExcelQueue(arg3.Item1, arg3.Item2, arg3.Item3, arg2);
        }

        private void writeExcelQueue(int rowIndex, string importInfo, string msg, Exception queueException)
        {
            if (_excelAssistant != null)
            {
                var importResult = _excelAssistant.GetCacheValueByColumnIndex(_importResultColumnIndex, rowIndex);
                importResult = importInfo == FAILURE || importResult == FAILURE ? FAILURE : importInfo == WARNING || importResult == WARNING ? WARNING : SUCCESS;
                _excelAssistant.AppendValueByColumnIndex(_importResultColumnIndex, rowIndex, importResult, false);
                var value = string.Concat("\r\n", msg, queueException == null ? string.Empty : string.Concat("\r\n", queueException.ToString()));
                _excelAssistant.AppendValueByColumnIndex(_errorReasonColumnIndex, rowIndex, value);
            }
        }

        private void writeLogQueue_ProcessItemEvent(Tuple<string, int?, Exception, IOperationResult> obj)
        {
            writeLogQueue(obj.Item1, obj.Item2, obj.Item3, obj.Item4, null);
        }

        private void writeLogQueue_ProcessExceptionEvent(dynamic arg, Exception arg2, Tuple<string, int?, Exception, IOperationResult> arg3)
        {
            writeLogQueue(arg3.Item1, arg3.Item2, arg3.Item3, arg3.Item4, arg2);
        }

        private void writeLogQueue(string msg, int? progress, Exception msgException, IOperationResult result, Exception queueException)
        {
            var logInfo = new StringBuilder();
            logInfo.AppendLine(msg);
            if (msgException != null)
            {
                logInfo.AppendLine(msgException.ToString());
            }
            if (queueException != null)
            {
                logInfo.AppendLine(queueException.ToString());
            }
            if (progress != null)
            {
                this.SetTaskProgress(progress.Value, msg);
            }
            result?.ComplexMessage.ErrorMessages.Add(msg);
            this.WriteLog(logInfo.ToString());
            //if (_logFile != null && msgException != null)
            if (_logFile != null)
            {
                _logFile.WriteLine(logInfo.ToString());
            }
        }

        /// <summary>
        /// 分析采购价
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="excelAssistant"></param>
        /// <returns></returns>
        private Dictionary<string, decimal> parsePurprice(UserContext userContext, IExcelAssistant excelAssistant)
        {
            var purpriceIndex = excelAssistant.ContainFieldCaption(PURPRICE);
            var result = new Dictionary<string, decimal>();

            if (purpriceIndex < 0)
            {
                return result;
            }

            while (excelAssistant.NextRow())
            {
                var inputValue = excelAssistant.GetValueByColumnIndex(purpriceIndex);
                if (string.IsNullOrWhiteSpace(inputValue))
                {
                    continue;
                }
                decimal purprice;
                if (!decimal.TryParse(inputValue, out purprice))
                {
                    continue;
                }
                var sequence = excelAssistant.CurrentRow.ToString();
                result.Add(sequence, purprice);
            }
            return result;
        }

        /// <summary>
        /// 分析商品、辅助属性与辅助值的关联关系
        /// </summary>
        /// <param name="products"></param>
        /// <param name="auxProperties"></param>
        /// <param name="auxPropertyValues"></param>
        /// <returns></returns>
        private Dictionary<DynamicObject, Dictionary<DynamicObject, List<string>>> parseProductAuxpropMap(
                                                                                   Dictionary<Dictionary<string, string>, DynamicObject> products,
                                                                                   Dictionary<int, Tuple<Dictionary<string, string>, DynamicObject>> auxProperties,
                                                                                   Dictionary<int, Dictionary<string, List<string>>> auxPropertyValues)
        {
            var productAuxPropertyMaps = new Dictionary<DynamicObject, Dictionary<DynamicObject, List<string>>>();
            if (products == null || products.Count <= 0)
            {
                return productAuxPropertyMaps;
            }
            if (auxProperties == null || auxProperties.Count <= 0)
            {
                return productAuxPropertyMaps;
            }
            if (auxPropertyValues == null || auxPropertyValues.Count <= 0)
            {
                return productAuxPropertyMaps;
            }
            //获取每个商品的辅助属性及值
            foreach (var product in products)
            {
                var auxPropertyMap = new Dictionary<DynamicObject, List<string>>();
                var sequences = product.Key[SEQUENCE].Split(',');
                foreach (var auxPropertyValue in auxPropertyValues)
                {
                    var valueList = new List<string>();
                    var valueMaps = auxPropertyValue.Value;
                    foreach (var sequence in sequences)
                    {
                        foreach (var valueMap in valueMaps)
                        {
                            if (valueMap.Value.Contains(sequence))
                            {
                                if (valueList.Contains(valueMap.Key) == false)
                                {
                                    valueList.Add(valueMap.Key);
                                }
                                break;
                            }
                        }
                    }
                    if (valueList.Count > 0)
                    {
                        var auxProperty = auxProperties[auxPropertyValue.Key];
                        auxPropertyMap.Add(auxProperty.Item2, valueList);
                        addErrorInfo(product.Key, auxProperty.Item1);
                    }
                }
                if (auxPropertyMap.Count > 0)
                {
                    productAuxPropertyMaps.Add(product.Value, auxPropertyMap);
                }
            }
            return productAuxPropertyMaps;
        }

        /// <summary>
        /// 分析辅助属性值
        /// </summary>
        /// <param name="excelAssistant"></param>
        /// <param name="auxProperties"></param>
        /// <returns></returns>
        private Dictionary<int, Dictionary<string, List<string>>> parseAuxPropertyValue(IExcelAssistant excelAssistant, Dictionary<int, Tuple<Dictionary<string, string>, DynamicObject>> auxProperties)
        {
            var result = new Dictionary<int, Dictionary<string, List<string>>>();

            if (auxProperties == null || auxProperties.Count <= 0)
            {
                return result;
            }

            var indexes = auxProperties.Keys;

            foreach (var index in indexes)
            {
                result.Add(index, new Dictionary<string, List<string>>());
            }

            while (excelAssistant.NextRow())
            {
                string sequence = excelAssistant.CurrentRow.ToString();
                foreach (var index in indexes)
                {
                    var value = excelAssistant.GetValueByColumnIndex(index);
                    if (string.IsNullOrWhiteSpace(value))
                    {
                        continue;
                    }
                    var recode = result[index];
                    if (recode.Keys.Contains(value))
                    {
                        recode[value].Add(sequence);
                        continue;
                    }
                    recode.Add(value, new List<string> { sequence });
                }
            }

            return result;
        }

        /// <summary>
        /// 分析商品信息
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="fields"></param>
        /// <param name="form"></param>
        /// <param name="excelAssistant"></param>
        /// <param name="metaModelService"></param>
        /// <param name="formGroupRowEntityMaps"></param>
        /// <returns></returns>
        private Dictionary<Dictionary<string, string>, DynamicObject> parseProducts(UserContext userContext,
                                                        List<HtmlField> fields,
                                                        HtmlForm form,
                                                        IExcelAssistant excelAssistant,
                                                        IMetaModelService metaModelService,
                                                        ConcurrentDictionary<string, Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>> formGroupRowEntityMaps)
        {
            var result = new Dictionary<Dictionary<string, string>, DynamicObject>();
            var list = new List<Dictionary<string, string>>();
            var nameIndex = excelAssistant.ContainFieldId(form.NameFldKey);
            var numberIndex = excelAssistant.ContainFieldId(form.NumberFldKey);
            //var mustInputFields = fields.Where(x => x.Id != form.NameFldKey && x.Id != form.NumberFldKey && x.MustInput == 1).ToList();
            //var commonInputFields = fields.Where(x => x.MustInput != 1).ToList();

            while (excelAssistant.NextRow())
            {
                buildingNode(excelAssistant, nameIndex, numberIndex, list, null, (int)Enu_BuildNodePolicy.ByNumber);
            }
            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, form.GetDynamicObjectType(userContext));
            List<DynamicObject> dbDataEntities = readDataBase(userContext, form, list);

            var count = dbDataEntities.Count;
            var disCount = dbDataEntities.Select(x => Convert.ToString(x["id"])).Distinct().Count();

            foreach (var item in list)
            {
                var sequences = item[SEQUENCE].Split(',');
                DynamicObject dataEntity = findDynamicObjectByNumberOrName(userContext, item, dbDataEntities, form);
                if (dataEntity == null)
                {
                    var msg = "商品名称为空!";
                    writeExcelErrorInfo(sequences, msg);
                    addErrorInfo(item, msg);
                    continue;
                }
                var kvp = result.FirstOrDefault(x => x.Value == dataEntity);
                if (kvp.Value != null)
                {
                    addNodeSequence(kvp.Key, item[SEQUENCE]);
                    if (item.Keys.Contains(SONSEQUENCE))
                    {
                        addNodeInfo(kvp.Key, SONSEQUENCE, item[SONSEQUENCE], true);
                    }
                    addErrorInfo(kvp.Key, item);
                    continue;
                }
                //foreach (var mustInputField in mustInputFields)
                //{
                //    if (!setFieldValue(sequences, mustInputField, item, dataEntity, excelAssistant, formGroupRowEntityMaps))
                //    {
                //        string msg = string.Format("必须输入{0}字段", mustInputField.Caption);
                //        writeExcelErrorInfo(sequences, msg);
                //        addErrorInfo(item, msg);
                //    }
                //}
                //foreach (var commonInputField in commonInputFields)
                //{
                //    setFieldValue(sequences, commonInputField, item, dataEntity, excelAssistant, formGroupRowEntityMaps);
                //}
                var headFields = form.HeadEntity.GetFieldList(fields);
                if (headFields != null && headFields.Count > 0)
                {
                    foreach (var headField in headFields)
                    {
                        if (headField.Id == form.NameFldKey || headField.Id == form.NumberFldKey)
                        {
                            continue;
                        }
                        if (!setFieldValue(sequences, headField, item, dataEntity, excelAssistant, formGroupRowEntityMaps) && headField.MustInput == 1)
                        {
                            string msg = string.Format("必须输入{0}字段", headField.Caption);
                            writeExcelErrorInfo(sequences, msg);
                            addErrorInfo(item, msg);
                        }
                    }
                }
                foreach (var entry in form.EntryList)
                {
                    var entryFields = entry.GetFieldList(fields);
                    if (entryFields == null || entryFields.Count <= 0)
                    {
                        continue;
                    }
                    var entryEntites = dataEntity[entry.Id] as DynamicObjectCollection;
                    var entryEntity = entryEntites.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    var isAddEntry = false;
                    foreach (var entryField in entryFields)
                    {
                        if (!setFieldValue(sequences, entryField, item, entryEntity, excelAssistant, formGroupRowEntityMaps))
                        {
                            if (entryField.MustInput == 1)
                            {
                                string msg = string.Format("必须输入{0}字段", entryField.Caption);
                                writeExcelErrorInfo(sequences, msg);
                                addErrorInfo(item, msg);
                            }
                        }
                        else
                        {
                            isAddEntry = true;
                        }
                    }
                    if (isAddEntry)
                    {
                        entryEntites.Add(entryEntity);
                    }
                }
                result.Add(item, dataEntity);
            }
            if (result.Count > 0)
            {
                var vcount = result.Values.Count;
                var disVCount = result.Values.Distinct().Count();
                var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(userContext, form, result.Values.ToArray(), OperateOption.Create());
            }
            return result;
        }

        /// <summary>
        /// 分析辅助属性
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="excelAssistant"></param>
        private Dictionary<int, Tuple<Dictionary<string, string>, DynamicObject>> parseAuxproperty(UserContext userContext, IExcelAssistant excelAssistant, IMetaModelService metaModelService)
        {
            var result = new Dictionary<int, Tuple<Dictionary<string, string>, DynamicObject>>();
            var auxpropertyNames = excelAssistant.ColumnNames.Select((x, i) => new { name = x, index = i }).Where(x => x.name.StartsWith(PREFIXPROPERTY))
                                                 .Select(x => new { name = x.name.Replace(PREFIXPROPERTY, string.Empty), index = x.index })
                                                 .Where(x => string.IsNullOrWhiteSpace(x.name) == false).Distinct(x => x.name).ToList();
            if (auxpropertyNames == null || auxpropertyNames.Count <= 0)
            {
                return result;
            }
            var form = metaModelService.LoadFormModel(userContext, "sel_prop");

            var dataTable = new DataTable();
            dataTable.Columns.Add("fid");
            dataTable.Columns.Add(form.NameFldKey);
            dataTable.BeginLoadData();
            foreach (var item in auxpropertyNames.Select(x => x.name))
            {
                dataTable.LoadDataRow(new object[]
                {
                    Guid.NewGuid().ToString(),
                    item
                }, true);
            }
            dataTable.EndLoadData();

            List<DynamicObject> dataEntities = null;
            using (var tran = this.UserContext.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbService = this.UserContext.Container.GetService<IDBService>();
                var tableName = dbService.CreateTempTableWithDataTable(this.UserContext, dataTable,0,null,null,false);
                var sql = $@"
                            select t1.fid from {form.BillHeadTableName} as t1
                            inner join {tableName} as t2 on t1.{form.NameFldKey}=t2.{form.NameFldKey} and len(t2.{form.NameFldKey})>0
                            where t1.fmainorgid='{this.UserContext.Company}' and (t1.fpublishcid='' or t1.fpublishcid='{this.UserContext.Company}')
                            ";
                var dataReader = dbService.ExecuteReader(this.UserContext, sql);
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, form.GetDynamicObjectType(userContext));
                dataEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                tran.Complete();

                dbService.DeleteTempTableByName(UserContext, tableName, false);
            }

            if (dataEntities == null)
            {
                dataEntities = new List<DynamicObject>();
            }

            var saveEntities = new Dictionary<Dictionary<string, string>, DynamicObject>();

            foreach (var item in auxpropertyNames)
            {
                var dataEntity = dataEntities.FirstOrDefault(x => Convert.ToString(x[form.NameFldKey]) == item.name);
                if (dataEntity == null)
                {
                    dataEntity = new DynamicObject(form.GetDynamicObjectType(userContext));
                    dataEntity[form.NameFldKey] = item.name;
                    dataEntity["fvaluesource"] = "val_type_03";
                }
                var dataKey = new Dictionary<string, string> { { SEQUENCE, string.Empty } };
                saveEntities.Add(dataKey, dataEntity);
                result.Add(item.index, Tuple.Create(dataKey, dataEntity));
            }

            if (saveEntities.Count > 0)
            {
                var arrEntities = saveEntities.Values.ToArray();
                var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(userContext, form, arrEntities, OperateOption.Create());
                sendDatas(userContext, form, saveEntities);
                //dm.Save(arrEntities);
            }

            return result;
        }

        /// <summary>
        /// 分析基础资料字段间的引用关系
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="fields"></param>
        /// <param name="parsedRefFieldList"></param>
        /// <param name="closureRefList"></param>
        /// <param name="formRefFieldMaps"></param>
        private void parseRefFields(UserContext userContext,
                                    List<HtmlField> fields,
                                    List<List<HtmlField>> parsedRefFieldList,
                                    Dictionary<HtmlBaseDataField, List<HtmlBaseDataField>> closureRefList,
                                    Dictionary<string, Dictionary<HtmlBaseDataField, HtmlBaseDataField>> formRefFieldMaps)
        {
            //获取所有单位字段
            var unitFields = fields.Where(x => x is HtmlUnitField).Select(x => x as HtmlUnitField).ToList();
            if (unitFields != null && unitFields.Count > 0)
            {
                parsedRefFieldList.Add(unitFields.Select(x => x as HtmlField).ToList());
            }
            //获取图片字段
            var imageField = fields.Where(x => x is HtmlImageField || x is HtmlMulImageField).Select(x => x).ToList();
            parsedRefFieldList.Add(imageField);
            //查找除单位外所有的引用字段
            var baseFields = fields.Where(x => (x is HtmlBaseDataField) && !(x is HtmlUnitField)).Select(x => x as HtmlBaseDataField).ToList();
            if (baseFields == null || baseFields.Count <= 0)
            {
                return;
            }
            var comboFields = baseFields.Where(x => x is HtmlComboField).Select(x => x as HtmlComboField).ToList();
            var fieldRefMap = new Dictionary<HtmlBaseDataField, List<HtmlBaseDataField>>();

            //查找所有的引用字段的引用字段
            foreach (var item in baseFields)
            {
                //不需要分析辅助资料
                if (item is HtmlComboField)
                {
                    continue;
                }
                //已分析过的不需要再分析
                if (formRefFieldMaps.Keys.Contains(item.RefFormId))
                {
                    continue;
                }

                //添加已分析的记录
                var parseRefFieldList = new Dictionary<HtmlBaseDataField, HtmlBaseDataField>();
                formRefFieldMaps.Add(item.RefFormId, parseRefFieldList);

                var refForm = item.RefHtmlForm(userContext);
                var refFields = refForm.GetFieldList().Where(x => x is HtmlBaseDataField && x.IsBillHeadField).Select(x => x as HtmlBaseDataField).ToList();

                if (refFields == null || refFields.Count <= 0)
                {
                    continue;
                }

                foreach (var refField in refFields)
                {
                    var refComboField = refField as HtmlComboField;
                    if (refComboField != null)
                    {
                        if (comboFields == null || comboFields.Count <= 0)
                        {
                            continue;
                        }
                        //检查当前引用辅助资料字段是不是在引用辅助资料字段的范围上
                        var comboField = comboFields.FirstOrDefault(x => x.CategoryFilter == refComboField.CategoryFilter) as HtmlBaseDataField;
                        if (comboField != null)
                        {
                            //记录表单有效的引用字段
                            parseRefFieldList.Add(refField, comboField);
                        }
                        continue;
                    }
                    //检查当前引用字段是不是在引用字段的范围上
                    var baseItem = baseFields.FirstOrDefault(x => x.RefFormId == refField.RefFormId);
                    if (baseItem != null && refField.Id != "fparentid")
                    {
                        //记录表单有效的引用字段
                        parseRefFieldList.Add(refField, baseItem);
                        //记录字段间的引用关系
                        List<HtmlBaseDataField> baseItems = null;
                        if (!fieldRefMap.TryGetValue(item, out baseItems))
                        {
                            baseItems = new List<HtmlBaseDataField>();
                            fieldRefMap.Add(item, baseItems);
                        }
                        baseItems.Add(baseItem);
                        continue;
                    }
                    var unitItem = unitFields.FirstOrDefault(x => x.RefFormId == refField.RefFormId && x.Id == refField.Id);
                    if (unitItem != null)
                    {
                        //记录表单有效的引用字段
                        parseRefFieldList.Add(refField, unitItem);
                        //记录字段间的引用关系
                        List<HtmlBaseDataField> baseItems = null;
                        if (!fieldRefMap.TryGetValue(item, out baseItems))
                        {
                            baseItems = new List<HtmlBaseDataField>();
                            fieldRefMap.Add(item, baseItems);
                        }
                        baseItems.Add(unitItem);
                    }
                }
            }
            var fieldListItem = baseFields.Where(x => !fieldRefMap.Keys.Contains(x)).Select(x => x as HtmlField).ToList();
            if (fieldListItem != null && fieldListItem.Count > 0)
            {
                //加载所有未有引用其他字段的引用字段
                parsedRefFieldList.Add(fieldListItem);
            }
            //分析引用其他字段的引用字段
            while (fieldRefMap != null && fieldRefMap.Count > 0)
            {
                var leafNodes = new List<HtmlField>();
                foreach (var fieldRefMapItem in fieldRefMap)
                {
                    //移除已分析过的叶子结点
                    var removeItems = fieldRefMapItem.Value.Where(x => !fieldRefMap.Keys.Contains(x)).ToList();
                    if (removeItems != null && removeItems.Count > 0)
                    {
                        foreach (var removeItem in removeItems)
                        {
                            fieldRefMapItem.Value.Remove(removeItem);
                        }
                    }
                    //如果当前结点没有引用结点，则其是当前层次的叶子结点，所以将其加入叶子结点列表
                    if (fieldRefMapItem.Value.Count <= 0)
                    {
                        leafNodes.Add(fieldRefMapItem.Key);
                    }
                }
                if (leafNodes != null && leafNodes.Count > 0)
                {
                    //如果存在叶子结点，则将叶子从字段引用映射表中移除
                    foreach (var removeItem in leafNodes)
                    {
                        fieldRefMap.Remove(removeItem as HtmlBaseDataField);
                    }
                    //将当前层次叶子加入已分析列表
                    parsedRefFieldList.Add(leafNodes);
                    continue;
                }
                //如果没有叶子结点则存在闭环引用
                //统计当前引用映射表中被引用次数最多的一个结点（如果有多个只取一个）
                var maxCountRefItem = fieldRefMap.Values.SelectMany(x => x).GroupBy(x => x.Id)
                                                 .Select(x => new { Key = x.FirstOrDefault(), Count = x.Count() }).OrderByDescending(x => x.Count).FirstOrDefault();
                var refMaxCountRefItems = fieldRefMap.Where(x => x.Value.Contains(maxCountRefItem.Key));
                //加入闭环引用记录中
                closureRefList.Add(maxCountRefItem.Key, refMaxCountRefItems.Select(x => x.Key).ToList());
                foreach (var refMaxCountRefItem in refMaxCountRefItems)
                {
                    refMaxCountRefItem.Value.Remove(maxCountRefItem.Key);
                }
            }
        }

        /// <summary>
        /// 根据名称或编号查找结点
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="listNode"></param>
        /// <param name="dbEntities"></param>
        /// <param name="form"></param>
        /// <returns></returns>
        private DynamicObject findDynamicObjectByNumberOrName(UserContext userContext, Dictionary<string, string> listNode, List<DynamicObject> dbEntities, HtmlForm form)
        {
            DynamicObject result = null;
            bool hasFindByNameFlag = true;
            bool hasFindByNumberFlag = true;
            switch (form.Id.ToLower())
            {
                case "ydj_product":
                    hasFindByNameFlag = false;
                    break;
                default:
                    break;
            }
            if (result == null && listNode.Keys.Contains(NUMBERKEY) && hasFindByNumberFlag)
            {
                result = dbEntities?.FirstOrDefault(x => Convert.ToString(x[form.NumberFldKey]) == listNode[NUMBERKEY]);
            }
            if (result == null && listNode.Keys.Contains(NAMEKEY) && hasFindByNameFlag)
            {
                result = dbEntities?.FirstOrDefault(x => Convert.ToString(x[form.NameFldKey]) == listNode[NAMEKEY]);
                if (result != null && listNode.Keys.Contains(NUMBERKEY))
                {
                    result = null;
                }
            }
            if (result == null && listNode.Keys.Contains(NAMEKEY))
            {
                result = new DynamicObject(form.GetDynamicObjectType(userContext));
                result[form.NameFldKey] = listNode[NAMEKEY];
                if (listNode.Keys.Contains(NUMBERKEY))
                {
                    result[form.NumberFldKey] = listNode[NUMBERKEY];
                }
            }
            return result;
        }

        /// <summary>
        /// 处理单位
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="field"></param>
        /// <param name="form"></param>
        /// <param name="rowEntityMaps"></param>
        /// <param name="nodeEntityMaps"></param>
        /// <param name="excelAssistant"></param>
        private void dealImages(UserContext userContext,
                                HtmlField field,
                                Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps,
                                Dictionary<Dictionary<string, string>, DynamicObject> nodeEntityMaps,
                                IExcelAssistant excelAssistant)
        {
            //字段id的索引，excel表中填写的是名称字段
            var fieldIdIndex = excelAssistant.ContainFieldId(field.Id);
            var cachePaths = new Dictionary<string, string[]>();

            //按行读取excel表
            while (excelAssistant.NextRow())
            {
                var value = getValueByColumnIndex(excelAssistant, fieldIdIndex);
                if (string.IsNullOrWhiteSpace(value))
                {
                    continue;
                }
                var paths = value.Split(new[] { ',', ';' }, StringSplitOptions.RemoveEmptyEntries);
                if (paths != null && paths.Length > 0)
                {
                    cachePaths.Add(excelAssistant.CurrentRow.ToString(), paths);
                }
            }

            if (cachePaths == null || cachePaths.Count <= 0)
            {
                return;
            }

            var pathList = cachePaths.Values.SelectMany(x => x).ToList();
            var fileInfoService = userContext.Container.GetService<IFileInfoService>();
            var pathResult = fileInfoService.PostFile(pathList);
            foreach (var cachePath in cachePaths)
            {
                var paths = cachePath.Value;
                List<Dictionary<string,string>> imageInfos = new List<Dictionary<string,string>>();
                foreach (var path in paths)
                {
                    var imageInfo = pathResult.FirstOrDefault(x => x["filePathInfo"] == path);
                    if (imageInfo != null)
                    {
                        imageInfos.Add(imageInfo);
                    }
                }
                if (imageInfos.Count > 0)
                {
                    string imageId = string.Join(",", imageInfos.Where(x=>Convert.ToBoolean(x["isSuccess"])).Select(x=>x["fileId"]));
                    string errMsg = string.Join("\r\n", imageInfos.Where(x => false == Convert.ToBoolean(x["isSuccess"])).Select(x => x["errMsg"]).Distinct());
                    var item = new Dictionary<string, string>
                    {
                        { SEQUENCE,cachePath.Key },
                        { NAMEKEY,imageId},
                        { NUMBERKEY,imageId},
                        { IMAGEID,imageId}
                    };
                    if (false == string.IsNullOrWhiteSpace(errMsg))
                    {
                        item[ERRORINFO] = errMsg;
                        writeExcelErrorInfo(cachePath.Key, errMsg);
                    }
                    rowEntityMaps.Add(cachePath.Key, Tuple.Create<Dictionary<string, string>, DynamicObject>(item, null));
                    nodeEntityMaps.Add(item, null);
                }
            }
        }

        /// <summary>
        /// 处理单位
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="field"></param>
        /// <param name="form"></param>
        /// <param name="rowEntityMaps"></param>
        /// <param name="nodeEntityMaps"></param>
        /// <param name="excelAssistant"></param>
        private void dealUnitDataEntities(UserContext userContext,
                                         HtmlBaseDataField field,
                                         HtmlForm form,
                                         Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps,
                                         Dictionary<Dictionary<string, string>, DynamicObject> nodeEntityMaps,
                                         IExcelAssistant excelAssistant)
        {
            //字段id的索引，excel表中填写的是名称字段
            var fieldIdIndex = excelAssistant.ContainFieldId(field.Id);
            //字段编号索引
            var fieldNumberIndex = excelAssistant.ContainFieldNumber(field.Id);
            //excel表行引用信息列表
            var list = new List<Dictionary<string, string>>();

            //按行读取excel表
            while (excelAssistant.NextRow())
            {
                //从当前行中构建结点
                buildingNode(excelAssistant, fieldIdIndex, fieldNumberIndex, list, null);
            }

            if (list == null || list.Count <= 0)
            {
                return;
            }

            List<DynamicObject> dbDataEntities = readDataBase(userContext, form, list);

            foreach (var item in list)
            {
                DynamicObject dataEntity = findDynamicObjectByNumberOrName(userContext, item, dbDataEntities, form);

                if (dataEntity == null)
                {
                    addErrorInfo(item, string.Format("无法分析出{0}", field.Caption));
                }

                var kvp = nodeEntityMaps.FirstOrDefault(x => x.Value == dataEntity);
                if (kvp.Value != null)
                {
                    addNodeSequence(kvp.Key, item[SEQUENCE]);
                    if (item.Keys.Contains(SONSEQUENCE))
                    {
                        addNodeInfo(kvp.Key, SONSEQUENCE, item[SONSEQUENCE], true);
                    }
                    addErrorInfo(kvp.Key, item);
                    continue;
                }

                nodeEntityMaps.Add(item, dataEntity);
                foreach (var sequence in item[SEQUENCE].Split(','))
                {
                    rowEntityMaps.Add(sequence, Tuple.Create(item, dataEntity));
                }
            }
        }

        /// <summary>
        /// 处理普通的引用资料
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="field"></param>
        /// <param name="form"></param>
        /// <param name="rowEntityMaps"></param>
        /// <param name="nodeEntityMaps"></param>
        /// <param name="excelAssistant"></param>
        /// <param name="formGroupRowEntityMaps"></param>
        /// <param name="formGroupNodeEntityMaps"></param>
        /// <param name="closureRefList"></param>
        /// <param name="formRefFieldMaps"></param>
        private void dealRefDataEntities(UserContext userContext,
                                         HtmlBaseDataField field,
                                         HtmlForm form,
                                         Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps,
                                         Dictionary<Dictionary<string, string>, DynamicObject> nodeEntityMaps,
                                         IExcelAssistant excelAssistant,
                                         ConcurrentDictionary<string, Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>> formGroupRowEntityMaps,
                                         ConcurrentDictionary<string, Dictionary<Dictionary<string, string>, DynamicObject>> formGroupNodeEntityMaps,
                                         Dictionary<HtmlBaseDataField, List<HtmlBaseDataField>> closureRefList,
                                         Dictionary<string, Dictionary<HtmlBaseDataField, HtmlBaseDataField>> formRefFieldMaps)
        {
            //字段id的索引，excel表中填写的是名称字段
            var fieldIdIndex = excelAssistant.ContainFieldId(field.Id);
            //字段编号索引
            var fieldNumberIndex = excelAssistant.ContainFieldNumber(field.Id);
            //excel表行引用信息列表
            var list = new List<Dictionary<string, string>>();
            //引用信息列表结点与动态对象的映射
            var dynamicMap = new Dictionary<Dictionary<string, string>, DynamicObject>();

            //按行读取excel表
            while (excelAssistant.NextRow())
            {
                //从当前行中构建结点
                buildingNode(excelAssistant, fieldIdIndex, fieldNumberIndex, list, null);
            }

            if (list == null || list.Count <= 0)
            {
                return;
            }

            readDataBase(userContext, field, form, list, dynamicMap, formGroupRowEntityMaps, formRefFieldMaps, null);
            foreach (var map in dynamicMap)
            {
                nodeEntityMaps.Add(map.Key, map.Value);
                foreach (var sequence in map.Key[SEQUENCE].Split(','))
                {
                    rowEntityMaps.Add(sequence, Tuple.Create(map.Key, map.Value));
                }
            }
            rewriteClosureRefNode(userContext, field, rowEntityMaps, closureRefList, formRefFieldMaps, formGroupNodeEntityMaps);
        }

        /// <summary>
        /// 处理有父结点的引用资料
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="field"></param>
        /// <param name="parentField"></param>
        /// <param name="form"></param>
        /// <param name="rowEntityMaps"></param>
        /// <param name="nodeEntityMaps"></param>
        /// <param name="excelAssistant"></param>
        /// <param name="formGroupRowEntityMaps"></param>
        /// <param name="formGroupNodeEntityMaps"></param>
        /// <param name="closureRefList"></param>
        /// <param name="formRefFieldMaps"></param>
        private void dealRefDataEntities(UserContext userContext,
                                         HtmlBaseDataField field,
                                         HtmlBaseDataField parentField,
                                         HtmlForm form,
                                         Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps,
                                         Dictionary<Dictionary<string, string>, DynamicObject> nodeEntityMaps,
                                         IExcelAssistant excelAssistant,
                                         ConcurrentDictionary<string, Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>> formGroupRowEntityMaps,
                                         ConcurrentDictionary<string, Dictionary<Dictionary<string, string>, DynamicObject>> formGroupNodeEntityMaps,
                                         Dictionary<HtmlBaseDataField, List<HtmlBaseDataField>> closureRefList,
                                         Dictionary<string, Dictionary<HtmlBaseDataField, HtmlBaseDataField>> formRefFieldMaps)
        {
            //字段id的索引，excel表中填写的是名称字段
            var fieldIdIndex = excelAssistant.ContainFieldId(field.Id);
            //字段编号索引
            var fieldNumberIndex = excelAssistant.ContainFieldNumber(field.Id);
            //父级字段id的索引，excel表中填写的是名称字段
            var fieldParentIdIndex = excelAssistant.ContainFieldParentId(field.Id);
            //父级字段编号索引
            var fieldParantNumberIndex = excelAssistant.ContainFieldParantNumber(field.Id);
            //excel表行引用信息列表
            var list = new List<Dictionary<string, string>>();
            //父子关系映射
            var parentMap = new Dictionary<Dictionary<string, string>, Dictionary<string, string>>();
            //引用信息列表结点与动态对象的映射
            var dynamicMap = new Dictionary<Dictionary<string, string>, DynamicObject>();

            //按行读取excel表
            while (excelAssistant.NextRow())
            {
                //从当前行中构建结点
                Dictionary<string, string> fieldValue = buildingNode(excelAssistant, fieldIdIndex, fieldNumberIndex, list, SONNODE);
                //当结点不存在时继续下一行
                if (fieldValue == null)
                {
                    continue;
                }
                //添加子结点序号
                addSonNodeSequence(excelAssistant, fieldValue);

                //父结点名称
                var parentName = getValueByColumnIndex(excelAssistant, fieldParentIdIndex);
                //父结点编号
                var parentNumber = getValueByColumnIndex(excelAssistant, fieldParantNumberIndex);
                //父结点行信息
                Dictionary<string, string> parentValue = null;

                //如果映射已存在，则只需要检查父结点是否包含编号和名称信息
                if (parentMap.Keys.Contains(fieldValue))
                {
                    parentValue = parentMap[fieldValue];
                    //如果父结点不包含名称信息并且父结点的名称信息不为空，则加入名称信息
                    if (!parentValue.Keys.Contains(NAMEKEY) && !string.IsNullOrWhiteSpace(parentName))
                    {
                        parentValue.Add(NAMEKEY, parentName);
                    }
                    //如果父结点不包含编号信息并且父结点的编号信息不为空，则加入编号信息
                    if (!parentValue.Keys.Contains(NUMBERKEY) && !string.IsNullOrWhiteSpace(parentNumber))
                    {
                        parentValue.Add(NUMBERKEY, parentNumber);
                    }
                    //继续一下行
                    continue;
                }

                //如果映射不存在，则需要构建父结点并建立映射

                //根据父结点的编号和名称构建信息结点
                parentValue = buildingNode(parentNumber, parentName, list, excelAssistant, null);
                //如果父结点构建成功，则建立映射
                if (parentValue != null)
                {
                    parentMap.Add(fieldValue, parentValue);
                }
            }

            if (list == null || list.Count <= 0)
            {
                return;
            }

            readDataBase(userContext, field, form, list, dynamicMap, formGroupRowEntityMaps, formRefFieldMaps, () =>
            {
                //设置父结点路径
                foreach (var item in list)
                {
                    //不存在父结点映射，即不存在父结点，则不需要设置父结点路径
                    if (!parentMap.Keys.Contains(item))
                    {
                        continue;
                    }
                    //虽然存在父结点映射，但不存在父结点对应的动态对象也不需要设置，例如用户只录入编号，但该编号在数据库中不存在
                    if (!dynamicMap.Keys.Contains(parentMap[item]))
                    {
                        continue;
                    }
                    //当前结点不存在动态对象，则不需要设置父结点路径
                    if (!dynamicMap.Keys.Contains(item))
                    {
                        continue;
                    }
                    //设置当前结点的父结点路径
                    var currentEntity = dynamicMap[item];
                    var parentEntity = dynamicMap[parentMap[item]];
                    //currentEntity["fpath"] = string.Format("{0}/{1}", Convert.ToString(parentEntity["fpath"]), Convert.ToString(parentEntity["id"]));
                    currentEntity["fparentid"] = parentEntity["id"];
                }
            });

            foreach (var map in dynamicMap)
            {
                if (!map.Key.Keys.Contains(NODEINFO) || map.Key[NODEINFO] != SONNODE)
                {
                    continue;
                }
                nodeEntityMaps.Add(map.Key, map.Value);
                foreach (var sequence in map.Key[SONSEQUENCE].Split(','))
                {
                    rowEntityMaps.Add(sequence, Tuple.Create(map.Key, map.Value));
                }
            }
            rewriteClosureRefNode(userContext, field, rowEntityMaps, closureRefList, formRefFieldMaps, formGroupNodeEntityMaps);
        }

        /// <summary>
        /// 处理辅助资料
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="field"></param>
        /// <param name="from"></param>
        /// <param name="rowEntityMaps"></param>
        /// <param name="excelAssistant"></param>
        private void dealRefDataEntities(UserContext userContext,
                                         HtmlComboField field,
                                         HtmlForm from,
                                         Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps,
                                         IExcelAssistant excelAssistant)
        {
            if (string.IsNullOrWhiteSpace(field.CategoryFilter))
            {
                return;
            }
            var fieldIndex = excelAssistant.ContainFieldId(field.Id);
            var values = new Dictionary<string, string>();

            while (excelAssistant.NextRow())
            {
                string name = excelAssistant.GetValueByColumnIndex(fieldIndex);
                string sequence = excelAssistant.CurrentRow.ToString();

                if (!string.IsNullOrEmpty(name))
                {
                    values.Add(sequence, name);
                }
            }

            if (values == null || values.Count <= 0)
            {
                return;
            }

            var mapKey = new Dictionary<string, string> { { SEQUENCE, string.Join(",", values.Keys) } };
            var metaModelService = userContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userContext, "bd_enumdata");
            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

            StringBuilder where = new StringBuilder("(fmainorgid = '0' or fmainorgid = '' or fmainorgid = ' ' or fmainorgid = @fmainorgid) and fname =@fname");
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,userContext.Company),
                new SqlParam("@fname", System.Data.DbType.String, field.CategoryFilter)
            };

            var dataReader = userContext.GetPkIdDataReader(htmlForm, where.ToString(), sqlParams);
            var saveEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            var entryEntityModel = htmlForm.GetEntryEntity("fentity");
            var saveEntities = new Dictionary<Dictionary<string, string>, DynamicObject>();

            if (saveEntity == null)
            {
                saveEntity = new DynamicObject(htmlForm.GetDynamicObjectType(userContext));
                saveEntity["fname"] = field.CategoryFilter;
                saveEntity["fvisible"] = true;
            }
            saveEntities.Add(mapKey, saveEntity);
            var entryEntities = saveEntity["fentity"] as DynamicObjectCollection;
            if (entryEntities != null)
            {
                foreach (var value in values)
                {
                    var entryEntity = entryEntities.FirstOrDefault(x => Convert.ToString(x["fenumitem"]) == value.Value &&
                                                                       (Convert.ToString(x["fenmainorgid"]) == userContext.Company &&
                                                                                (Convert.ToString(x["fpublishcid"]) == userContext.Company ||
                                                                                 string.IsNullOrWhiteSpace(Convert.ToString(x["fpublishcid"]))) ||
                                                                        Convert.ToBoolean(x["fispreset"])));
                    if (entryEntity == null)
                    {
                        entryEntity = new DynamicObject(entryEntityModel.DynamicObjectType);
                        entryEntities.Add(entryEntity);
                        entryEntity["fenumitem"] = value.Value;
                        entryEntity["fispreset"] = false;
                        entryEntity["fenmainorgid"] = userContext.Company;
                        entryEntity["fpublishcid_txt"] = userContext.Companys.FirstOrDefault(x => userContext.Company == x.CompanyId)?.CompanyName;
                        entryEntity["fpublishcid"] = userContext.Company;
                        entryEntity["fpublishcid_pid"] = userContext.Product;
                        //entryEntity["fdataorigin"] = "本地";
                    }
                    rowEntityMaps.Add(value.Key, Tuple.Create(mapKey, entryEntity));
                }
            }

            if (saveEntities == null || saveEntities.Count <= 0)
            {
                return;
            }

            var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
            var dbEntities = saveEntities.Values.ToArray();
            prepareSaveDataService.PrepareDataEntity(userContext, htmlForm, saveEntities.Values.ToArray(), OperateOption.Create());

            sendDatas(userContext, htmlForm, saveEntities);
             
            var comboSvc = userContext.Container.GetService<IComboDataService>();
            foreach (var name in dbEntities.Select(x => Convert.ToString(x["fname"])))
            {
                comboSvc.ClearCache(userContext, name);
            }
        }

        /// <summary>
        /// 处理商品、辅助属性与辅助属性值的映射关系
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="metaModelService"></param>
        /// <param name="productAuxPropertyMaps"></param>
        /// <param name="products"></param>
        private void dealAuxpropValueMap(UserContext userContext,
                                         IMetaModelService metaModelService,
                                         Dictionary<DynamicObject, Dictionary<DynamicObject, List<string>>> productAuxPropertyMaps,
                                         Dictionary<Dictionary<string, string>, DynamicObject> products)
        {
            if (productAuxPropertyMaps == null || productAuxPropertyMaps.Count <= 0)
            {
                return;
            }
            var htmlForm = metaModelService.LoadFormModel(userContext, "bd_auxpropvaluemap");
            //            var sqlFormat = @"
            //select fid from {0} where fmainorgid=@fmainorgid and fmaterialid=@fmaterialid{1} and fauxpropid=@fauxpropid{1}
            //";
            //            var sqlList = new List<string>();
            //            var sqlParams = new List<SqlParam>
            //            {
            //                new SqlParam("@fmainorgid",DbType.String,userContext.Company)
            //            };

            //            foreach (var productAuxPropertyMap in productAuxPropertyMaps)
            //            {
            //                var auxProperties = productAuxPropertyMap.Value.Keys;
            //                var productId = Convert.ToString(productAuxPropertyMap.Key["id"]);
            //                foreach (var auxProperty in auxProperties)
            //                {
            //                    var auxpropertyId = Convert.ToString(auxProperty["id"]);
            //                    var count = sqlParams.Count;
            //                    sqlList.Add(string.Format(sqlFormat, htmlForm.BillHeadTableName, count));
            //                    sqlParams.Add(new SqlParam(string.Format("@fmaterialid{0}", count), DbType.String, productId));
            //                    sqlParams.Add(new SqlParam(string.Format("@fauxpropid{0}", count), DbType.String, auxpropertyId));
            //                }
            //            }

            //            var dbService = userContext.Container.GetService<IDBService>();
            //            var dm = userContext.Container.GetService<IDataManager>();
            //            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

            //            var dataReader = dbService.ExecuteReader(userContext, string.Join("union all", sqlList), sqlParams);
            //            var dbEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();


            var dataTable = new DataTable();
            dataTable.Columns.Add("fid");
            dataTable.Columns.Add("fmaterialid");
            dataTable.Columns.Add("fauxpropid");
            dataTable.BeginLoadData();
            foreach (var productAuxPropertyMap in productAuxPropertyMaps)
            {
                var auxProperties = productAuxPropertyMap.Value.Keys;
                var productId = Convert.ToString(productAuxPropertyMap.Key["id"]);
                foreach (var auxProperty in auxProperties)
                {
                    var auxpropertyId = Convert.ToString(auxProperty["id"]);
                    dataTable.LoadDataRow(new object[]
                    {
                        Guid.NewGuid().ToString(),
                        productId,
                        auxpropertyId
                    }, true);
                }
            }
            dataTable.EndLoadData();

            List<DynamicObject> dbEntities = null;
            using (var tran = this.UserContext.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbService = this.UserContext.Container.GetService<IDBService>();
                var tableName = dbService.CreateTempTableWithDataTable(this.UserContext, dataTable,0,null,null,false);
                var sql = $@"
                            select t1.fid from {htmlForm.BillHeadTableName} as t1
                            inner join {tableName} as t2 on t1.fmaterialid=t2.fmaterialid and len(t2.fmaterialid)>0 and t1.fauxpropid=t2.fauxpropid and len(t2.fauxpropid)>0
                            where t1.fmainorgid='{this.UserContext.Company}'
                            ";
                var dataReader = dbService.ExecuteReader(this.UserContext, sql);
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
                dbEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                tran.Complete();

                dbService.DeleteTempTableByName(UserContext, tableName, false);
            }


            if (dbEntities == null)
            {
                dbEntities = new List<DynamicObject>();
            }

            var htmlEntity = htmlForm.GetEntryEntity("FEntity");
            var dynamicMps = new Dictionary<Dictionary<string, string>, DynamicObject>();
            var kkMaps = new Dictionary<Dictionary<string, string>, Dictionary<string, string>>();
            foreach (var productAuxPropertyMap in productAuxPropertyMaps)
            {
                var productId = Convert.ToString(productAuxPropertyMap.Key["id"]);
                foreach (var auxPropertyMap in productAuxPropertyMap.Value)
                {
                    var auxpropertyId = Convert.ToString(auxPropertyMap.Key["id"]);
                    var dataEntity = dbEntities.FirstOrDefault(x => Convert.ToString(x["fmaterialid"]) == productId && Convert.ToString(x["fauxpropid"]) == auxpropertyId);
                    if (dataEntity == null)
                    {
                        dataEntity = new DynamicObject(htmlForm.GetDynamicObjectType(userContext));
                        dataEntity["fmaterialid"] = productId;
                        dataEntity["fauxpropid"] = auxpropertyId;
                    }
                    var productDynamicMapKey = products.FirstOrDefault(x => x.Value == productAuxPropertyMap.Key).Key;
                    var mapKey = new Dictionary<string, string> { { SEQUENCE, productDynamicMapKey[SEQUENCE] } };
                    dynamicMps.Add(mapKey, dataEntity);
                    kkMaps.Add(mapKey, productDynamicMapKey);
                    var fentities = dataEntity["FEntity"] as DynamicObjectCollection;
                    if (fentities != null)
                    {
                        var isdefval = true;
                        foreach (var auxPropertyValue in auxPropertyMap.Value)
                        {
                            var fentry = fentities.FirstOrDefault(x => Convert.ToString(x["fvalueid"]) == auxPropertyValue);
                            if (fentry == null)
                            {
                                fentry = new DynamicObject(htmlEntity.DynamicObjectType);
                                fentry["fvalueid"] = auxPropertyValue;
                                fentry["fvaluenumber"] = auxPropertyValue;
                                fentry["fvaluename"] = auxPropertyValue;
                                fentry["fisdisable"] = false;
                                if (isdefval)
                                {
                                    var fdefault = fentities.FirstOrDefault(x => Convert.ToBoolean(x["fisdefval"]));
                                    isdefval = fdefault == null;
                                }
                                fentry["fisdefval"] = isdefval;
                                fentities.Add(fentry);
                                isdefval = false;
                            }
                        }
                    }
                }
            }
            var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(userContext, htmlForm, dbEntities.ToArray(), OperateOption.Create());
            sendDatas(userContext, htmlForm, dynamicMps);
            foreach (var item in kkMaps)
            {
                addErrorInfo(item.Value, item.Key);
            }
        }

        /// <summary>
        /// 处理商品与辅助属性的映射关系
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="metaModelService"></param>
        /// <param name="productAuxPropertyMaps"></param>
        /// <param name="products"></param>
        private void dealAuxpropMap(UserContext userContext,
                                    IMetaModelService metaModelService,
                                    Dictionary<DynamicObject, Dictionary<DynamicObject, List<string>>> productAuxPropertyMaps,
                                    Dictionary<Dictionary<string, string>, DynamicObject> products)
        {
            if (productAuxPropertyMaps == null || productAuxPropertyMaps.Count <= 0)
            {
                return;
            }
            var htmlForm = metaModelService.LoadFormModel(userContext, "bd_auxpropmap");

            var dataTable = new DataTable();
            dataTable.Columns.Add("fid");
            dataTable.Columns.Add("fmaterialid");
            dataTable.BeginLoadData();
            foreach (var item in productAuxPropertyMaps.Keys.Select(x => Convert.ToString(x["id"])))
            {
                dataTable.LoadDataRow(new object[]
                {
                    Guid.NewGuid().ToString(),
                    item
                }, true);
            }
            dataTable.EndLoadData();

            List<DynamicObject> dbEntities = null;
            using (var tran = this.UserContext.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbService = this.UserContext.Container.GetService<IDBService>();
                var tableName = dbService.CreateTempTableWithDataTable(this.UserContext, dataTable,0,null,null,false);
                var sql = $@"
                            select t1.fid from {htmlForm.BillHeadTableName} as t1
                            inner join {tableName} as t2 on t1.fmaterialid=t2.fmaterialid and len(t2.fmaterialid)>0
                            where t1.fmainorgid='{this.UserContext.Company}'
                            ";
                var dataReader = dbService.ExecuteReader(this.UserContext, sql);
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
                dbEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                tran.Complete();

                dbService.DeleteTempTableByName(UserContext, tableName, false);
            }

            if (dbEntities == null)
            {
                dbEntities = new List<DynamicObject>();
            }
            var htmlEntity = htmlForm.GetEntryEntity("FEntity");
            var dynamicMps = new Dictionary<Dictionary<string, string>, DynamicObject>();
            foreach (var productAuxPropertyMap in productAuxPropertyMaps)
            {
                var productId = Convert.ToString(productAuxPropertyMap.Key["id"]);
                var dataEntity = dbEntities.FirstOrDefault(x => productId == Convert.ToString(x["fmaterialid"]));
                if (dataEntity == null)
                {
                    dataEntity = new DynamicObject(htmlForm.GetDynamicObjectType(userContext));
                    dataEntity["fmaterialid"] = productId;
                }
                dynamicMps.Add(products.FirstOrDefault(x => x.Value == productAuxPropertyMap.Key).Key, dataEntity);
                var fentities = dataEntity["FEntity"] as DynamicObjectCollection;

                if (fentities != null)
                {
                    foreach (var auxproperty in productAuxPropertyMap.Value.Keys)
                    {
                        var fentity = fentities.FirstOrDefault(x => Convert.ToString(x["fauxpropid"]) == Convert.ToString(auxproperty["id"]));
                        if (fentity == null)
                        {
                            fentity = new DynamicObject(htmlEntity.DynamicObjectType);
                            fentity["fauxpropid"] = auxproperty["id"];
                            fentities.Add(fentity);
                        }
                    }
                }
            }
            var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(userContext, htmlForm, dbEntities.ToArray(), OperateOption.Create());
            sendDatas(userContext, htmlForm, dynamicMps);
        }

        /// <summary>
        /// 处理采购价
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="metaModelService"></param>
        /// <param name="auxProperties"></param>
        /// <param name="auxPropertyValues"></param>
        /// <param name="products"></param>
        /// <param name="purprices"></param>
        private void dealPurprice(UserContext userContext,
                                  IMetaModelService metaModelService,
                                  Dictionary<int, Tuple<Dictionary<string, string>, DynamicObject>> auxProperties,
                                  Dictionary<int, Dictionary<string, List<string>>> auxPropertyValues,
                                  Dictionary<Dictionary<string, string>, DynamicObject> products,
                                  Dictionary<string, decimal> purprices,
                                  Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> supplierRowEntiyMaps)
        {
            products = products?.Where(x => !x.Key.Keys.Contains(ERRORINFO)).ToDictionary(k => k.Key, v => v.Value);
            if (products == null || products.Count <= 0)
            {
                return;
            }
            if (purprices == null || purprices.Count <= 0)
            {
                return;
            }

            var purpriceForm = metaModelService.LoadFormModel(userContext, "ydj_purchaseprice");
            var auxsetForm = metaModelService.LoadFormModel(userContext, "bd_auxpropvalueset");
            var purpriceHtmlEntry = purpriceForm.GetEntryEntity("fentry");
            var auxsetHtmlEntry = auxsetForm.GetEntryEntity("FEntity");
            var supplierMaps = new Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>();
            var baseFormProvider = userContext.Container.GetService<IBaseFormProvider>();
            var sqlFormat = @"
select p.fid from t_ydj_purchaseprice p
inner join t_ydj_purchasepriceentry e on p.fid=e.fid
where p.fmainorgid=@fmainorgid and p.fsupplierid=@fsupplierid{0} and e.fproductid_e=@fproductid{0} and e.funitid_e=@funitid{0} and DATEDIFF(day,e.fstartdate_e,@fstartdate)=0 
--已禁用的筛选掉
and p.fforbidstatus='0'
 and DATEDIFF(day,e.fexpiredate_e,@fexpiredate)=0
";
            var attrinFormat = @"
select fentryid from T_BD_AUXPROPVALUEENTRY pe inner join T_BD_AUXPROPVALUE pv on pe.fid=pv.fid where pv.fmainorgid=@fmainorgid and pe.fid=e.fattrinfo and pe.fvalueid=@fvalueid{0}{1} and pe.fauxpropid=@fauxpropid{0}{1}
";
            var expireDate = DateTime.MaxValue;
            var startDate = DateTime.Today;
            var i = 0;
            var productInfoTmps = new List<KeyValuePair<Dictionary<string, string>, DynamicObject>>();
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",DbType.String,userContext.Company),
                new SqlParam($"@fstartdate", System.Data.DbType.String, startDate),
                new SqlParam($"@fexpiredate", System.Data.DbType.String, expireDate)
            };
            var sqlList = new List<string>();
            var tokenList = new List<string>();

            foreach (var product in products)
            {
                foreach (var sequence in product.Key[SEQUENCE].Split(','))
                {
                    //检查采购价是否存在
                    if (!purprices.Keys.Contains(sequence))
                    {
                        continue;
                    }
                    var info = new Dictionary<string, string>();
                    var supplierId = getTulpeDataId(sequence, supplierRowEntiyMaps, info);
                    var productEntity = product.Value;
                    var productId = Convert.ToString(productEntity["id"]);
                    var unitId = Convert.ToString(productEntity["funitid"]);
                    var sqlBuilder = new StringBuilder();
                    var tkList = new List<string> { productId, unitId, supplierId };
                    var rowParams = new List<SqlParam>();

                    info["supplierId"] = supplierId;
                    info["productId"] = productId;
                    info["unitId"] = unitId;
                    info[SEQUENCE] = sequence;

                    sqlBuilder.AppendFormat(sqlFormat, i);
                    rowParams.Add(new SqlParam($"@fproductid{i}", System.Data.DbType.String, productId));
                    rowParams.Add(new SqlParam($"@funitid{i}", System.Data.DbType.String, unitId));
                    rowParams.Add(new SqlParam($"@fsupplierid{i}", System.Data.DbType.String, supplierId));

                    //检查辅助属性
                    if (auxPropertyValues != null && auxPropertyValues.Count > 0)
                    {
                        var fattrinfoList = new List<string>();
                        var attrinfoSqlList = new List<string>();
                        var j = 0;
                        foreach (var auxPropertyValue in auxPropertyValues)
                        {
                            foreach (var propertyValue in auxPropertyValue.Value)
                            {
                                if (propertyValue.Value.Contains(sequence))
                                {
                                    var auxProperty = auxProperties[auxPropertyValue.Key];
                                    var attrinfoEntry = new DynamicObject(auxsetHtmlEntry.DynamicObjectType);
                                    var valueId = propertyValue.Key;
                                    var auxPropertyId = Convert.ToString(auxProperty.Item2["id"]);

                                    info[$"auxPropertyId_{auxPropertyId}"] = valueId;

                                    rowParams.Add(new SqlParam($"@fauxpropid{i}{j}", System.Data.DbType.String, auxPropertyId));
                                    rowParams.Add(new SqlParam($"fvalueid{i}{j}", System.Data.DbType.String, valueId));
                                    attrinfoSqlList.Add(string.Format(attrinFormat, i, j));
                                    fattrinfoList.Add(string.Concat(auxPropertyId, "-", valueId));
                                    addErrorInfo(info, auxProperty.Item1);
                                    j++;
                                    break;
                                }
                            }
                        }
                        if (attrinfoSqlList.Count > 0)
                        {
                            tkList.Add(string.Join("-", fattrinfoList));
                            sqlBuilder.Append(" and (select count(1) from T_BD_AUXPROPVALUEENTRY as pe where pe.fid=e.fattrinfo)=");
                            sqlBuilder.AppendLine(attrinfoSqlList.Count.ToString());
                            sqlBuilder.AppendLine($" and {attrinfoSqlList.Count}=(select count(1) from ( ");
                            sqlBuilder.AppendLine(string.Join("union", attrinfoSqlList));
                            sqlBuilder.AppendLine(") t)");
                        }
                        else
                        {
                            sqlBuilder.Append(" and e.fattrinfo=''");
                        }
                    }
                    else
                    {
                        sqlBuilder.Append(" and e.fattrinfo=''");
                    }
                    var token = string.Join("-", tkList);
                    var index = tokenList.IndexOf(token);
                    if (index > -1)
                    {
                        var msg = "不允许重复创建相同维度（供应商+商品+辅助属性+单位+生效日期+失效日期）的价格表";
                        writeExcelErrorInfo(sequence, msg);
                        setLogMsg(string.Concat("行号:", sequence, "价目表重复", msg), null);
                        continue;
                    }
                    productInfoTmps.Add(new KeyValuePair<Dictionary<string, string>, DynamicObject>(info, productEntity));
                    tokenList.Add(token);
                    sqlList.Add(sqlBuilder.ToString());
                    sqlParams.AddRange(rowParams);
                    i++;
                }
            }

            if (productInfoTmps.Count <= 0)
            {
                return;
            }

            var dbEntities = new List<DynamicObject>();
            string sql = string.Empty;
            if (sqlList.Count > 0)
            {
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, purpriceForm.GetDynamicObjectType(userContext));
                var dbService = userContext.Container.GetService<IDBService>();
                sql = string.Join("union", sqlList);
                IDataReader dataReader = dbService.ExecuteReader(userContext, sql, sqlParams); ;
                dbEntities.AddRange(dm.SelectBy(dataReader).OfType<DynamicObject>());
                if (dbEntities.Count > 0)
                {
                    //加载引用数据
                    var fieldIds = new List<string> { "fattrinfo" };
                    userContext.Container.GetService<LoadReferenceObjectManager>()?.Load(
                        userContext,
                        dbEntities.ToArray(),
                        false,
                        purpriceForm,
                        fieldIds);
                    //setLogMsg($"数据库有数据，\r\n {Newtonsoft.Json.JsonConvert.SerializeObject(dbEntities.Select(x => serializePurchaseOrder(x)))}", null);
                }
            }

            var dynamicMaps = new Dictionary<Dictionary<string, string>, DynamicObject>();
            foreach (var productInfos in productInfoTmps.GroupBy(x => x.Key["supplierId"]))
            {
                var supplierId = productInfos.Key;
                //找出供应商已存在的价目表
                var existEntities = dbEntities.Where(x => Convert.ToString(x["fsupplierid"]).Trim() == supplierId).SelectMany(x =>
                {
                    var fentries = x["fentry"] as DynamicObjectCollection;
                    return fentries.Select(y =>
                    {
                        Dictionary<string, string> attrInfo = null;
                        var refAttrInfo = y["fattrinfo_ref"] as DynamicObject;
                        if (refAttrInfo == null)
                        {
                            attrInfo = new Dictionary<string, string>();
                        }
                        else
                        {
                            var attrinfoEntries = refAttrInfo["FEntity"] as DynamicObjectCollection;
                            attrInfo = attrinfoEntries.ToDictionary(k => $"auxPropertyId_{Convert.ToString(k["fauxpropid"])}",
                                                                    v => Convert.ToString(v["fvalueid"]));
                        }
                        return new
                        {
                            headEntity = x,
                            entryEntity = y,
                            productId = Convert.ToString(y["fproductid_e"]),
                            unitId = Convert.ToString(y["funitid_e"]),
                            attrInfo = attrInfo
                        };
                    });
                });
                DynamicObject currentEntity = null;
                Dictionary<string, string> currentKey = null;
                //对比当前输入值，如果已存在价目表，更新原有值，如果不存在创建新的价目表
                foreach (var productInfo in productInfos)
                {
                    var sequence = productInfo.Key[SEQUENCE];
                    var purprice = purprices[sequence];
                    var attrInfos = productInfo.Key.Where(x => x.Key.StartsWith("auxPropertyId_")).ToDictionary(k => k.Key, v => v.Value);
                    var exists = existEntities.Where(x => x.productId == productInfo.Key["productId"] &&
                                                          x.attrInfo.Count == attrInfos.Count &&
                                                          (x.attrInfo.Count == 0 || x.attrInfo.All(y => attrInfos.Keys.Contains(y.Key) &&
                                                                                                        attrInfos[y.Key] == y.Value))).ToList();
                    if (exists != null && exists.Count > 0)
                    {
                        foreach (var exist in exists)
                        {
                            var dynamicMap = dynamicMaps.FirstOrDefault(x => x.Value == exist.headEntity);
                            if (dynamicMap.Value == null)
                            {
                                dynamicMap = new KeyValuePair<Dictionary<string, string>, DynamicObject>(new Dictionary<string, string>(), exist.headEntity);
                                dynamicMaps.Add(dynamicMap.Key, dynamicMap.Value);
                            }
                            addNodeSequence(dynamicMap.Key, sequence);
                            addErrorInfo(dynamicMap.Key, productInfo.Key);
                            //商品
                            //var productEntity = productInfo.Value;
                            //采购价
                            exist.entryEntity["fpurprice"] = purprice;
                            //检查产品类别
                            //exist.entryEntity["fcategoryid_e"] = productEntity["fcategoryid"];
                            //检查品牌信息
                            //exist.entryEntity["fbrandid_e"] = productEntity["fbrandid"];

                            //赋值其他信息
                            exist.entryEntity["fconfirmstatus"] = "1";
                            exist.entryEntity["fstartdate_e"] = startDate;
                            exist.entryEntity["fexpiredate_e"] = expireDate;
                        }
                        continue;
                    }
                    //setLogMsg("创建新的单据", null);
                    if (currentEntity == null)
                    {
                        currentEntity = new DynamicObject(purpriceForm.GetDynamicObjectType(userContext));
                        currentKey = new Dictionary<string, string>();
                        dynamicMaps.Add(currentKey, currentEntity);
                        //赋值其他信息
                        currentEntity["fsupplierid"] = supplierId;
                        currentEntity["ftype"] = "quote_type_01";
                        //currentEntity["fstaffid"] = baseFormProvider.GetMyStaff(userContext)?.Id;
                        //currentEntity["fdeptid"] = baseFormProvider.GetMyDepartment(userContext)?.Id;
                    }
                    var fentries = currentEntity["fentry"] as DynamicObjectCollection;
                    var fentry = new DynamicObject(purpriceHtmlEntry.DynamicObjectType);
                    fentries.Add(fentry);
                    //商品
                    var productEntity = productInfo.Value;
                    var productId = productEntity["id"];
                    fentry["fproductid_e"] = productId;
                    addNodeSequence(currentKey, sequence);
                    addErrorInfo(currentKey, productInfo.Key);
                    //采购价
                    fentry["fpurprice"] = purprice;
                    //检查单位是否存在
                    fentry["funitid_e"] = productEntity["funitid"];
                    //检查产品类别
                    //fentry["fcategoryid_e"] = productEntity["fcategoryid"];
                    //检查品牌信息
                    //fentry["fbrandid_e"] = productEntity["fbrandid"];
                    //检查辅助属性
                    if (attrInfos != null && attrInfos.Count > 0)
                    {
                        var fattrinfo = new DynamicObject(auxsetForm.GetDynamicObjectType(userContext));
                        fentry["fattrinfo_ref"] = fattrinfo;
                        fattrinfo["fmaterialid"] = productId;
                        var attrinfoEntries = fattrinfo["FEntity"] as DynamicObjectCollection;
                        foreach (var attrInfo in attrInfos)
                        {
                            var attrinfoEntry = new DynamicObject(auxsetHtmlEntry.DynamicObjectType);
                            attrinfoEntry["fvalueid"] = attrInfo.Value;
                            attrinfoEntry["fvaluenumber"] = attrInfo.Value;
                            attrinfoEntry["fvaluename"] = attrInfo.Value;
                            attrinfoEntry["fauxpropid"] = attrInfo.Key.Replace("auxPropertyId_", string.Empty);
                            attrinfoEntries.Add(attrinfoEntry);
                        }
                    }
                    //赋值其他信息
                    fentry["fconfirmstatus"] = "1";
                    fentry["fstartdate_e"] = startDate;
                    fentry["fexpiredate_e"] = expireDate;
                }

                //if (currentEntity != null)
                //{
                //    setLogMsg("currentEntity不为null", null);
                //    string currentString = Newtonsoft.Json.JsonConvert.SerializeObject(serializePurchaseOrder(currentEntity));
                //    string existString = Newtonsoft.Json.JsonConvert.SerializeObject(existEntities.Select(x => serializePurchaseOrder(x.headEntity)));
                //    string productString = Newtonsoft.Json.JsonConvert.SerializeObject(products.Values.Select(x => serializeDynamicObject(x)));
                //    setLogMsg($"当前要发送的采购价目为: \r\n {currentString} \r\n 查出的价目表为: \r\n {existString} \r\n 商品信息为: \r\n {productString}", null);
                //}
            }
            sendDatas(userContext, purpriceForm, dynamicMaps, (ex) =>
            {
                setLogMsg("采购价保存出现异常", null);
                string sqlParamString = Newtonsoft.Json.JsonConvert.SerializeObject(sqlParams);
                string dbEntitiesString = Newtonsoft.Json.JsonConvert.SerializeObject(dbEntities.Select(x => serializePurchaseOrder(x)));
                string saveDatas = Newtonsoft.Json.JsonConvert.SerializeObject(dynamicMaps.Values.Select(x => serializePurchaseOrder(x)));
                string msg = $"执行的sql为\r\n {sql} \r\n 参数为: \r\n {sqlParamString} \r\n 查询结果为: \r\n {dbEntitiesString} \r\n 当前保存的数据为: \r\n {saveDatas}";
                setLogMsg(msg, null);
            });
        }

        private object serializeDynamicObject(DynamicObject dynamicObject)
        {
            return new
            {
                fid = Convert.ToString(dynamicObject["id"]),
                fname = Convert.ToString(dynamicObject["fname"]),
                fnumber = Convert.ToString(dynamicObject["fnumber"])
            };
        }

        private object serializePurchaseOrder(DynamicObject dynamicObject)
        {
            var fentries = dynamicObject["fentry"] as DynamicObjectCollection;
            return new
            {
                fid = Convert.ToString(dynamicObject["id"]),
                fsupplierid = Convert.ToString(dynamicObject["fsupplierid"]),
                ftype = Convert.ToString(dynamicObject["ftype"]),
                //fstaffid = Convert.ToString(dynamicObject["fstaffid"]),
                //fdeptid = Convert.ToString(dynamicObject["fdeptid"]),
                fentries = fentries.Select(x =>
                {
                    var fattrinfo = x["fattrinfo_ref"] as DynamicObject;
                    var attrinfoEntries = fattrinfo?["FEntity"] as DynamicObjectCollection;
                    return new
                    {
                        fentryid = Convert.ToString(x["id"]),
                        fproductid = Convert.ToString(x["fproductid_e"]),
                        funitid = Convert.ToString(x["funitid_e"]),
                        //fcategoryid = Convert.ToString(x["fcategoryid_e"]),
                        //fbrandid = Convert.ToString(x["fbrandid_e"]),
                        //fprice = Convert.ToString(x["fprice"]),
                        //ftaxrate = Convert.ToString(x["ftaxrate"]),
                        fconfirmstatus = Convert.ToString(x["fconfirmstatus"]),
                        fpurprice = Convert.ToString(x["fpurprice"]),
                        fstartdate = Convert.ToString(x["fstartdate_e"]),
                        fexpiredate = Convert.ToString(x["fexpiredate_e"]),
                        fattrinfo = new
                        {
                            fid = Convert.ToString(x["fattrinfo"]),
                            fmaterialid = Convert.ToString(fattrinfo?["fmaterialid"]),
                            fentries = attrinfoEntries?.Select(y =>
                            {
                                return new
                                {
                                    fentryid = Convert.ToString(y["id"]),
                                    fvalueid = Convert.ToString(y["fvalueid"]),
                                    fauxpropid = Convert.ToString(y["fauxpropid"])
                                };
                            }).ToList()
                        }
                    };
                }).ToList()
            };
        }

        /// <summary>
        /// 根据字段id索引和字段编号索引从excel表中构建列表结点，当结点存在于列表时返回该结点，否则构建并加入列表
        /// </summary>
        /// <param name="excelAssistant"></param>
        /// <param name="fieldIdIndex"></param>
        /// <param name="fieldNumberIndex"></param>
        /// <param name="list"></param>
        /// <param name="nodeInfo"></param>
        /// <param name="policy"><seealso cref="Enu_BuildNodePolicy"/></param>
        /// <returns></returns>
        private Dictionary<string, string> buildingNode(IExcelAssistant excelAssistant,
            int fieldIdIndex,
            int fieldNumberIndex,
            List<Dictionary<string, string>> list,
            string nodeInfo,
            int policy = -1)
        {
            var name = getValueByColumnIndex(excelAssistant, fieldIdIndex);
            var number = fieldNumberIndex < 0 ? string.Empty : excelAssistant.GetValueByColumnIndex(fieldNumberIndex);
            return buildingNode(number, name, list, excelAssistant, nodeInfo, policy);
        }

        /// <summary>
        /// 根据编号和名称构建列表结点,当结点存在于列表时返回该结点，否则构建并加入列表
        /// </summary>
        /// <param name="number"></param>
        /// <param name="name"></param>
        /// <param name="list"></param>
        /// <param name="excelAssistant"></param>
        /// <param name="nodeInfo"></param>
        /// <param name="policy"><seealso cref="Enu_BuildNodePolicy"/></param>
        /// <returns></returns>
        private Dictionary<string, string> buildingNode(string number,
            string name,
            List<Dictionary<string, string>> list,
            IExcelAssistant excelAssistant,
            string nodeInfo,
            int policy = -1)
        {
            Dictionary<string, string> result = null;
            //如果编号和名称都为空时，直接返回
            if (string.IsNullOrWhiteSpace(number) && string.IsNullOrWhiteSpace(name))
            {
                return result;
            }
            var sequence = excelAssistant.CurrentRow.ToString();

            //编码不为空时，并且指定按编码寻找唯一性策略时
            if (result == null
                && !string.IsNullOrWhiteSpace(number)
                && (policy & (int)Enu_BuildNodePolicy.ByNumber) == (int)Enu_BuildNodePolicy.ByNumber)
            {
                result = buildingNode(NUMBERKEY, number, list, sequence, nodeInfo);
            }

            //名称不为空时，并且指定按编码寻找唯一性策略时
            if (result == null
                && !string.IsNullOrWhiteSpace(name)
                && (policy & (int)Enu_BuildNodePolicy.ByName) == (int)Enu_BuildNodePolicy.ByName)
            {
                result = buildingNode(NAMEKEY, name, list, sequence, nodeInfo);
            }

            //上述任意根据编码或名称找到了节点，则判断节点数据完整性，将缺失的number与name补充进来
            //反之，如果构建节点时，已存在节点时，则与本行相比后决定是否再生成新节点需要根据策略
            //  1.如果以编码为准，则需要以编码为判断依据
            //  2.如果以名称为准，则需要以名称为判断依据
            //  3.如果两者皆可，则以编码为准
            if (result != null)
            {
                //如果通过名称查找到的结点不包含编号，则将当前编号加入并返回
                if (!result.Keys.Contains(NUMBERKEY) && !number.IsNullOrEmptyOrWhiteSpace())
                {
                    result.Add(NUMBERKEY, number);
                }
                if (!result.Keys.Contains(NAMEKEY) && !name.IsNullOrEmptyOrWhiteSpace())
                {
                    result.Add(NAMEKEY, name);
                }

                //如果以编码为准的，则根据编码判断
                if (result.ContainsKey(NUMBERKEY)
                    && result[NUMBERKEY] != number
                    && (policy & (int)Enu_BuildNodePolicy.ByNumber) == (int)Enu_BuildNodePolicy.ByNumber)
                {
                    result = new Dictionary<string, string>
                                {
                                    { NUMBERKEY,number},
                                    { NAMEKEY,name},
                                    { SEQUENCE,sequence}
                                };
                    if (number.IsNullOrEmptyOrWhiteSpace())
                    {
                        result.Remove(NUMBERKEY);
                    }
                    if (name.IsNullOrEmptyOrWhiteSpace())
                    {
                        result.Remove(NAMEKEY);
                    }
                    list.Add(result);
                    addNodeInfo(result, nodeInfo);
                }

                //如果以名称为准的，则要求不包含编码判断的，统一用名称判断
                if (result.ContainsKey(NAMEKEY)
                    && result[NAMEKEY] != name
                    && (policy & (int)Enu_BuildNodePolicy.ByName) == (int)Enu_BuildNodePolicy.ByName
                    && (policy & (int)Enu_BuildNodePolicy.ByNumber) != (int)Enu_BuildNodePolicy.ByNumber)
                {
                    result = new Dictionary<string, string>
                                {
                                    { NUMBERKEY,number},
                                    { NAMEKEY,name},
                                    { SEQUENCE,sequence}
                                };
                    if (number.IsNullOrEmptyOrWhiteSpace())
                    {
                        result.Remove(NUMBERKEY);
                    }
                    if (name.IsNullOrEmptyOrWhiteSpace())
                    {
                        result.Remove(NAMEKEY);
                    }
                    list.Add(result);
                    addNodeInfo(result, nodeInfo);
                }
            }

            return result;
        }

        /// <summary>
        /// 根据编号或名称构建列表结点,当结点存在于列表时返回该结点，否则构建并加入列表
        /// </summary>
        /// <param name="key"></param>
        /// <param name="value"></param>
        /// <param name="list"></param>
        /// <param name="sequence"></param>
        /// <param name="nodeInfo"></param>
        /// <returns></returns>
        private Dictionary<string, string> buildingNode(string key, string value, List<Dictionary<string, string>> list, string sequence, string nodeInfo)
        {
            var result = list.FirstOrDefault(x => x.Keys.Contains(key) && x[key] == value);
            if (result == null)
            {
                result = new Dictionary<string, string>
                    {
                        { key,value},
                        { SEQUENCE,sequence}
                    };
                list.Add(result);
            }
            else
            {
                result[key] = value;
                addNodeSequence(result, sequence);
            }
            addNodeInfo(result, nodeInfo);
            return result;
        }

        /// <summary>
        /// 添加结点信息
        /// </summary>
        /// <param name="node"></param>
        /// <param name="nodeInfo"></param>
        /// <param name="isAppendModel"></param>
        private void addNodeInfo(Dictionary<string, string> node, string nodeInfo, bool isAppendModel = false)
        {
            addNodeInfo(node, NODEINFO, nodeInfo, isAppendModel);
        }

        /// <summary>
        /// 添加结点信息
        /// </summary>
        /// <param name="node"></param>
        /// <param name="infoKey"></param>
        /// <param name="infoValue"></param>
        /// <param name="isAppendModel"></param>
        private void addNodeInfo(Dictionary<string, string> node, string infoKey, string infoValue, bool isAppendModel = false)
        {
            if (string.IsNullOrWhiteSpace(infoKey) || string.IsNullOrWhiteSpace(infoValue))
            {
                return;
            }
            if (node.Keys.Contains(infoKey) && isAppendModel)
            {
                node[infoKey] = string.Concat(node[infoKey], ',', infoValue);
                return;
            }
            node[infoKey] = infoValue;
        }

        /// <summary>
        /// 添加结点的序号
        /// </summary>
        /// <param name="node"></param>
        /// <param name="sequence"></param>
        private void addNodeSequence(Dictionary<string, string> node, string sequence)
        {
            if (node.Keys.Contains(SEQUENCE))
            {
                node[SEQUENCE] = string.Concat(node[SEQUENCE], ",", sequence);
            }
            else
            {
                node[SEQUENCE] = sequence;
            }
        }

        /// <summary>
        /// 添加结点的序号
        /// </summary>
        /// <param name="node"></param>
        /// <param name="sequence"></param>
        private void addSonNodeSequence(IExcelAssistant excelAssistant, Dictionary<string, string> node)
        {
            string sequence = excelAssistant.CurrentRow.ToString();
            addNodeInfo(node, SONSEQUENCE, sequence, true);
        }

        /// <summary>
        /// 按列序号读取excel表
        /// </summary>
        /// <param name="excelAssistant"></param>
        /// <param name="columnIndex"></param>
        /// <returns></returns>
        private static string getValueByColumnIndex(IExcelAssistant excelAssistant, int columnIndex)
        {
            return columnIndex < 0 ? string.Empty : excelAssistant.GetValueByColumnIndex(columnIndex);
        }

        /// <summary>
        /// 根据list列表提供的编号或名称读取数据库，并构建list列表结点与动态对象的映射，但数据库不存在对应的list结点信息时新建动态对象并建立映射
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="form"></param>
        /// <param name="list"></param>
        /// <param name="dynamicMap"></param>
        /// <param name="formGroupRowEntityMaps"></param>
        /// <param name="formRefFieldMaps"></param>
        /// <param name="callback"></param>
        private void readDataBase(UserContext userContext,
                                  HtmlField field,
                                  HtmlForm form,
                                  List<Dictionary<string, string>> list,
                                  Dictionary<Dictionary<string, string>, DynamicObject> dynamicMap,
                                  ConcurrentDictionary<string, Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>> formGroupRowEntityMaps,
                                  Dictionary<string, Dictionary<HtmlBaseDataField, HtmlBaseDataField>> formRefFieldMaps,
                                  Action callback)
        {
            List<DynamicObject> dbDataEntities = readDataBase(userContext, form, list);

            foreach (var item in list)
            {
                DynamicObject dataEntity = findDynamicObjectByNumberOrName(userContext, item, dbDataEntities, form);
                if (dataEntity == null)
                {
                    var msg = $"{field.Caption}名称为空!";
                    writeExcelErrorInfo(item[SEQUENCE].Split(','), msg);
                    addErrorInfo(item, msg);
                    continue;
                }
                var kvp = dynamicMap.FirstOrDefault(x => x.Value == dataEntity);
                if (kvp.Value != null)
                {
                    addNodeSequence(kvp.Key, item[SEQUENCE]);
                    if (item.Keys.Contains(SONSEQUENCE))
                    {
                        addNodeInfo(kvp.Key, SONSEQUENCE, item[SONSEQUENCE], true);
                    }
                    addErrorInfo(kvp.Key, item);
                    continue;
                }
                dynamicMap.Add(item, dataEntity);
            }

            if (dynamicMap == null || dynamicMap.Count <= 0)
            {
                return;
            }

            Dictionary<HtmlBaseDataField, HtmlBaseDataField> formRefFieldMap = null;
            formRefFieldMaps.TryGetValue(form.Id, out formRefFieldMap);

            if (formRefFieldMap != null && formRefFieldMap.Count > 0)
            {
                foreach (var item in dynamicMap)
                {
                    foreach (var map in formRefFieldMap)
                    {
                        string key = getGroupKey(map.Value);
                        Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps = null;
                        if (formGroupRowEntityMaps.TryGetValue(key, out rowEntityMaps))
                        {
                            foreach (string sequence in item.Key[SEQUENCE].Split(','))
                            {
                                Tuple<Dictionary<string, string>, DynamicObject> tuple;
                                rowEntityMaps.TryGetValue(sequence, out tuple);
                                DynamicObject sequenceEntity = tuple?.Item2;
                                if (sequenceEntity == null)
                                {
                                    continue;
                                }
                                map.Key.DynamicProperty.SetValue(item.Value, sequenceEntity["id"]);
                                if (!item.Key.Keys.Contains(NODESTATUS))
                                {
                                    item.Key.Add(NODESTATUS, NEWSTATUS);
                                }
                                //传递引用错误信息
                                addErrorInfo(item.Key, tuple.Item1);
                            }
                        }
                    }
                }
            }

            var saveList = dynamicMap.Values.ToArray();
            var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(userContext, form, saveList, OperateOption.Create());

            callback?.Invoke();

            sendDatas(userContext, form, dynamicMap);
        }

        /// <summary>
        /// 根据list列表提供的编号或名称读取数据库
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="form"></param>
        /// <param name="list"></param>
        /// <param name="dm"></param>
        /// <returns></returns>
        private List<DynamicObject> readDataBase(UserContext userContext, HtmlForm form, List<Dictionary<string, string>> list)
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("fid");
            dataTable.Columns.Add("fname");
            dataTable.Columns.Add("fnumber");
            dataTable.BeginLoadData();
            foreach (var item in list)
            {
                dataTable.LoadDataRow(new object[]
                {
                    Guid.NewGuid().ToString(),
                    item.Keys.Contains(NAMEKEY)?item[NAMEKEY]:string.Empty,
                    item.Keys.Contains(NUMBERKEY)?item[NUMBERKEY]:string.Empty
                }, true);
            }
            dataTable.EndLoadData();

            List<DynamicObject> results = null;
            using (var tran = this.UserContext.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
            {
                var dbService = this.UserContext.Container.GetService<IDBService>();
                var tableName = dbService.CreateTempTableWithDataTable(this.UserContext, dataTable,0,null,null,false);
                var sql = $@"
                            select t1.fid from {form.BillHeadTableName} as t1
                            inner join {tableName} as t2 on (t1.fnumber=t2.fnumber and len(t2.fnumber)>0) or (t1.fname=t2.fname and len(t2.fname)>0)
                            where t1.fmainorgid='{this.UserContext.Company}'
                            ";
                var dataReader = dbService.ExecuteReader(this.UserContext, sql);
                var dm = userContext.Container.GetService<IDataManager>();
                dm.InitDbContext(userContext, form.GetDynamicObjectType(userContext));
                results = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                tran.Complete();

                dbService.DeleteTempTableByName(UserContext, tableName, false);
            }
            return results;
        }

        /// <summary>
        /// 反写闭环引用字段
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="field"></param>
        /// <param name="rowEntityMaps"></param>
        /// <param name="closureRefList"></param>
        /// <param name="formRefFieldMaps"></param>
        /// <param name="formGroupNodeEntityMaps"></param>
        private void rewriteClosureRefNode(UserContext userContext,
                                           HtmlBaseDataField field,
                                           Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps,
                                           Dictionary<HtmlBaseDataField, List<HtmlBaseDataField>> closureRefList,
                                           Dictionary<string, Dictionary<HtmlBaseDataField, HtmlBaseDataField>> formRefFieldMaps,
                                           ConcurrentDictionary<string, Dictionary<Dictionary<string, string>, DynamicObject>> formGroupNodeEntityMaps)
        {
            //处理闭环引用的反写
            if (closureRefList != null && closureRefList.Count > 0)
            {
                var prepareSaveDataService = userContext.Container.GetService<IPrepareSaveDataService>();
                //获取引用当前字段的结点
                List<HtmlBaseDataField> baseItems = null;
                closureRefList.TryGetValue(field, out baseItems);
                if (baseItems != null && baseItems.Count > 0)
                {
                    foreach (var baseItem in baseItems)
                    {
                        //检查表单引用字段映射表
                        if (formRefFieldMaps == null || formRefFieldMaps.Count <= 0)
                        {
                            break;
                        }
                        Dictionary<HtmlBaseDataField, HtmlBaseDataField> formMaps = null;
                        formRefFieldMaps.TryGetValue(baseItem.RefFormId, out formMaps);
                        if (formMaps == null || formMaps.Count <= 0)
                        {
                            continue;
                        }
                        //检查表单序号表单数据映射表
                        if (formGroupNodeEntityMaps == null || formGroupNodeEntityMaps.Count <= 0)
                        {
                            break;
                        }
                        Dictionary<Dictionary<string, string>, DynamicObject> nodeEntityMaps = null;
                        formGroupNodeEntityMaps.TryGetValue(getGroupKey(baseItem), out nodeEntityMaps);
                        if (nodeEntityMaps == null)
                        {
                            continue;
                        }
                        //获取引用结点的引用字段
                        var refBaseField = formMaps.FirstOrDefault(x => x.Value == field).Key;
                        if (refBaseField == null)
                        {
                            continue;
                        }
                        //获取表单新增的数据
                        var newStatusList = nodeEntityMaps.Where(x => x.Key.Keys.Contains(NEWSTATUS)).ToList();
                        if (newStatusList == null || newStatusList.Count <= 0)
                        {
                            continue;
                        }
                        //反写数据并保存
                        var saveDatas = new Dictionary<Dictionary<string, string>, DynamicObject>();
                        foreach (var newStatusItem in newStatusList)
                        {
                            foreach (string sequence in newStatusItem.Key[SEQUENCE].Split(','))
                            {
                                Tuple<Dictionary<string, string>, DynamicObject> map = null;
                                rowEntityMaps.TryGetValue(sequence, out map);
                                DynamicObject dataEntity = map?.Item2;
                                if (dataEntity == null)
                                {
                                    continue;
                                }
                                refBaseField.DynamicProperty.SetValueFast(newStatusItem.Value, dataEntity["id"]);
                                saveDatas.Add(newStatusItem.Key, newStatusItem.Value);
                                addErrorInfo(newStatusItem.Key, map.Item1);
                            }
                        }
                        if (saveDatas != null && saveDatas.Count > 0)
                        {
                            var refHtmlForm = baseItem.RefHtmlForm(userContext);
                            prepareSaveDataService.PrepareDataEntity(userContext, refHtmlForm, saveDatas.Values.ToArray(), OperateOption.Create());
                            sendDatas(userContext, refHtmlForm, saveDatas);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取表单分组键值
        /// </summary>
        /// <param name="field"></param>
        /// <returns></returns>
        private string getGroupKey(HtmlBaseDataField field)
        {
            return string.Format("{0}-{1}", field.RefFormId, field.Id);
        }

        /// <summary>
        /// 获取采购价表的唯一性标识
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="fsupplierid"></param>
        /// <returns></returns>
        private string getPurpriceToken(DynamicObject dataEntity, string fsupplierid)
        {
            string fproductid = Convert.ToString(dataEntity["fproductid_e"]);
            string fstartdate = Convert.ToDateTime(dataEntity["fstartdate_e"]).ToString("yyyy-MM-dd");
            string fexpiredate = Convert.ToDateTime(dataEntity["fexpiredate_e"]).ToString("yyyy-MM-dd");
            string flengthmax = Convert.ToString(dataEntity["flengthmax"]);
            string flengthmin = Convert.ToString(dataEntity["flengthmin"]);
            string fwidthmax = Convert.ToString(dataEntity["fwidthmax"]);
            string fwidthmin = Convert.ToString(dataEntity["fwidthmin"]);
            string fthickmax = Convert.ToString(dataEntity["fthickmax"]);
            string fthickmin = Convert.ToString(dataEntity["fthickmin"]);

            List<string> result = new List<string> { fsupplierid, fproductid, fstartdate, fexpiredate, flengthmax, flengthmin, fwidthmax, fwidthmin, fthickmax, fthickmin };

            var fattrinfo = dataEntity["fattrinfo_ref"] as DynamicObject;
            if (fattrinfo != null)
            {
                var fentities = fattrinfo["FEntity"] as DynamicObjectCollection;
                if (fentities != null && fentities.Count > 0)
                {
                    foreach (var item in fentities.OrderBy(x => Convert.ToString(x["fauxpropid"])))
                    {
                        result.Add(Convert.ToString(item["fauxpropid"]));
                        result.Add(Convert.ToString(item["fvalueid"]));
                    }
                }
            }

            return string.Join("-", result);
        }

        /// <summary>
        /// 设置商品字段的值
        /// </summary>
        /// <param name="sequences"></param>
        /// <param name="field"></param>
        /// <param name="dataEntity"></param>
        /// <param name="excelAssistant"></param>
        /// <param name="formGroupRowEntityMaps"></param>
        /// <returns></returns>
        private bool setFieldValue(IEnumerable<string> sequences,
                                   HtmlField field,
                                   Dictionary<string, string> item,
                                   DynamicObject dataEntity,
                                   IExcelAssistant excelAssistant,
                                   ConcurrentDictionary<string, Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>>> formGroupRowEntityMaps)
        {
            var isCommonFiled = !(field is HtmlBaseDataField || field is HtmlImageField || field is HtmlMulImageField);
            if (isCommonFiled)
            {
                object value = null;
                foreach (var sequence in sequences)
                {
                    value = excelAssistant.GetValueByFieldId(field.Id, int.Parse(sequence));
                    if (value != null)
                    {
                        break;
                    }
                }
                if (value == null)
                {
                    return false;
                }
                field.DynamicProperty.SetValueFast(dataEntity, value);
                return true;
            }
            var baseField = field as HtmlBaseDataField;
            var groupKey = baseField != null ? getGroupKey(baseField) : field.Id;
            if (formGroupRowEntityMaps == null || formGroupRowEntityMaps.Count <= 0)
            {
                return false;
            }
            Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntityMaps = null;
            formGroupRowEntityMaps.TryGetValue(groupKey, out rowEntityMaps);
            if (rowEntityMaps == null || rowEntityMaps.Count <= 0)
            {
                return false;
            }
            foreach (var sequence in sequences)
            {
                Tuple<Dictionary<string, string>, DynamicObject> tuple;
                rowEntityMaps.TryGetValue(sequence, out tuple);

                if (tuple == null)
                {
                    continue;
                }

                if (baseField != null)
                {
                    DynamicObject rowEntity = tuple.Item2;
                    if (rowEntity == null)
                    {
                        continue;
                    }
                    field.DynamicProperty.SetValueFast(dataEntity, rowEntity["id"]);
                    addErrorInfo(item, tuple.Item1);
                }
                else
                {
                    //图片信息
                    if (tuple.Item1.Keys.Contains(IMAGEID))
                    {
                        addErrorInfo(item, tuple.Item1);
                        field.DynamicProperty.SetValueFast(dataEntity, tuple.Item1[IMAGEID]);
                    }
                }


                return true;
            }
            return false;
        }

        /// <summary>
        /// 分割字典数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="U"></typeparam>
        /// <param name="datas"></param>
        /// <returns></returns>
        private List<Dictionary<T, U>> splitDictionary<T, U>(Dictionary<T, U> datas, int perCount = 0)
        {
            var result = new List<Dictionary<T, U>>();

            if (perCount <= 1)
            {
                perCount = datas.Count / 10;

                if (datas.Count > 1000)
                {
                    perCount = 100;
                }
                else if (perCount > 100)
                {
                    perCount = 50;
                }
                else if (datas.Count <= 10)
                {
                    perCount = 1;
                }
                else
                {
                    perCount = 10;
                }
            }

            Dictionary<T, U> item = new Dictionary<T, U>();
            result.Add(item);

            foreach (var data in datas)
            {
                if (item.Count >= perCount)
                {
                    item = new Dictionary<T, U>();
                    result.Add(item);
                }
                item.Add(data.Key, data.Value);
            }

            return result;
        }

        /// <summary>
        /// 发送数据并保存
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="form"></param>
        /// <param name="datas"></param>
        /// <param name="exceptionCallBack"></param>
        private void sendDatas(UserContext userContext, HtmlForm form, Dictionary<Dictionary<string, string>, DynamicObject> datas, Action<Exception> exceptionCallBack = null)
        {
            datas = datas?.Where(x => !x.Key.Keys.Contains(ERRORINFO)).ToDictionary(k => k.Key, v => v.Value);
            if (datas == null || datas.Count <= 0)
            {
                return;
            }
            var draftTableNames = new string[] { "ydj_supplier" };
            if (draftTableNames.Contains(form.Id))
            {
                sendDatas(userContext, form, datas, "draft", "暂存", exceptionCallBack);
                return;
            }
            sendDatas(userContext, form, datas, "save", "保存", exceptionCallBack);
        }

        /// <summary>
        /// 发送数据并保存
        /// </summary>
        /// <param name="userContext"></param>
        /// <param name="form"></param>
        /// <param name="datas"></param>
        /// <param name="opCode"></param>
        /// <param name="opName"></param>
        /// <param name="exceptionCallBack"></param>
        /// <param name="sendCount"></param>
        private void sendDatas(UserContext userContext, HtmlForm form, Dictionary<Dictionary<string, string>, DynamicObject> datas, string opCode, string opName, Action<Exception> exceptionCallBack = null, int sendCount = 0)
        {
            if (datas == null || datas.Count <= 0)
            {
                return;
            }
            try
            {
                var numberItem = datas.FirstOrDefault(x => x.Value.DataEntityState.FromDatabase == false && x.Key.Keys.Contains(NUMBERKEY)).Key;
                //parseDataState(form, datas.Values, sendCount);
                var gateWay = userContext.Container.GetService<IHttpServiceInvoker>();
                var result = gateWay.InvokeBillOperation(userContext, form.Id, datas.Values, opCode, new Dictionary<string, object> { { "ImportNumber", numberItem != null } });
                result?.ThrowIfHasError(true, string.Format("{0}{1}失败!", opName, form.Caption));
            }
            catch (Exception ex)
            {
                if (datas.Count > 1)
                {
                    sendCount++;
                    var splitDatas = splitDictionary(datas);
                    foreach (var item in splitDatas)
                    {
                        sendDatas(userContext, form, item, opCode, opName, exceptionCallBack, sendCount);
                    }
                    return;
                }
                var msg = "{0}{1}时出错，{2}".Fmt(opName, form.Caption, ex.Message);
                var dataKey = datas.Keys.FirstOrDefault();
                var sequences = dataKey[SEQUENCE].Split(',');
                addErrorInfo(dataKey, msg);
                writeExcelErrorInfo(sequences, msg);
                setLogMsg(msg, ex, null);
                if (exceptionCallBack != null)
                {
                    exceptionCallBack.Invoke(ex);
                }
            }
        }

        private void parseDataState(HtmlForm form, IEnumerable<DynamicObject> dataEntities, int sendCount)
        {
            foreach (var dataEntity in dataEntities)
            {
                parseDataState(form, form.HeadEntity, dataEntity, sendCount);
                foreach (var htmlEntry in form.EntryList)
                {
                    var entryCollection = dataEntity[htmlEntry.Id] as DynamicObjectCollection;
                    if (entryCollection == null || entryCollection.Count <= 0)
                    {
                        continue;
                    }
                    foreach (var entryEntity in entryCollection)
                    {
                        parseDataState(form, htmlEntry, entryEntity, sendCount);
                        foreach (var subHtmlEntry in htmlEntry.SubEntryList)
                        {
                            var subEntryCollection = entryEntity[subHtmlEntry.Id] as DynamicObjectCollection;
                            if (subEntryCollection != null && subEntryCollection.Count > 0)
                            {
                                foreach (var subEntryEntity in subEntryCollection)
                                {
                                    parseDataState(form, subHtmlEntry, subEntryEntity, sendCount);
                                }
                            }
                        }
                    }
                }
            }
        }

        private void parseDataState(HtmlForm form, HtmlEntity entity, DynamicObject dataEntity, int sendCount)
        {
            if (dataEntity.DataEntityState.FromDatabase == false)
            {
                string msg = $"新记录：{form.Id}|{entity.Id}|Id:{Convert.ToString(dataEntity["id"])}|第{sendCount}次";
                setLogMsg(msg, null);
            }
        }

        private void addErrorInfo(Dictionary<string, string> target, Dictionary<string, string> source)
        {
            if (source.Keys.Contains(ERRORINFO))
            {
                addErrorInfo(target, source[ERRORINFO]);
            }
        }

        private void addErrorInfo(Dictionary<string, string> dataKey, string msg)
        {
            if (dataKey.Keys.Contains(ERRORINFO))
            {
                dataKey[ERRORINFO] = string.Concat(dataKey[ERRORINFO], "\r\n", msg);
            }
            else
            {
                dataKey.Add(ERRORINFO, msg);
            }
        }

        /// <summary>
        /// 写excel文件的错误信息
        /// </summary>
        /// <param name="sequences"></param>
        /// <param name="errorInfo"></param>
        private void writeExcelErrorInfo(IEnumerable<string> sequences, string errorInfo, string importResult = FAILURE)
        {
            if (sequences == null)
            {
                return;
            }
            foreach (var sequence in sequences)
            {
                writeExcelErrorInfo(sequence, errorInfo, importResult);
            }
        }

        private void writeExcelErrorInfo(string sequence, string errorInfo, string importResult = FAILURE)
        {
            if (_writeExcelQueue == null)
            {
                return;
            }
            if (string.IsNullOrWhiteSpace(sequence))
            {
                return;
            }
            int rowIndex;
            if (int.TryParse(sequence, out rowIndex))
            {
                _writeExcelQueue.Enqueue(Tuple.Create(rowIndex, importResult, errorInfo));
            }
        }

        private void writeExcelSuccessInfo(Dictionary<Dictionary<string, string>, DynamicObject> datas)
        {
            var sequences = datas?.Where(x => !x.Key.Keys.Contains(ERRORINFO)).SelectMany(x => x.Key[SEQUENCE].Split(','));
            writeExcelErrorInfo(sequences, string.Empty, SUCCESS);
        }

        /// <summary>
        /// 设置日志消息
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="progress"></param>
        /// <param name="ex"></param>
        /// <param name="result"></param>
        private void setLogMsg(string msg, int progress, Exception ex, IOperationResult result)
        {
            if (ex != null)
            {
                _isSuccess = false;
            }
            _writeLogQueue.Enqueue(Tuple.Create<string, int?, Exception, IOperationResult>(msg, progress, ex, result));
        }

        /// <summary>
        /// 设置日志消息
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="progress"></param>
        /// <param name="result"></param>
        private void setLogMsg(string msg, int progress, IOperationResult result)
        {
            _writeLogQueue.Enqueue(Tuple.Create<string, int?, Exception, IOperationResult>(msg, progress, null, result));
        }

        /// <summary>
        /// 设置日志消息
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="ex"></param>
        /// <param name="result"></param>
        private void setLogMsg(string msg, Exception ex, IOperationResult result)
        {
            if (ex != null)
            {
                _isSuccess = false;
            }
            _writeLogQueue.Enqueue(Tuple.Create<string, int?, Exception, IOperationResult>(msg, null, ex, result));
        }

        /// <summary>
        /// 设置日志消息
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="result"></param>
        private void setLogMsg(string msg, IOperationResult result)
        {
            _writeLogQueue.Enqueue(Tuple.Create<string, int?, Exception, IOperationResult>(msg, null, null, result));
        }

        private void saveLog()
        {
            if (_writeLogQueue != null)
            {
                if (_writeLogQueue.GetInternalItemCount() > 0)
                {
                    _writeLogQueue.Flush();
                }
                _writeLogQueue.ProcessExceptionEvent -= writeLogQueue_ProcessExceptionEvent;
                _writeLogQueue.ProcessItemEvent -= writeLogQueue_ProcessItemEvent;
            }
            if (_logFile != null)
            {
                _logFile.Close();
            }
        }

        private string getTulpeDataId(string sequence, Dictionary<string, Tuple<Dictionary<string, string>, DynamicObject>> rowEntiyMaps, Dictionary<string, string> key)
        {
            Tuple<Dictionary<string, string>, DynamicObject> entityTuple = null;
            rowEntiyMaps?.TryGetValue(sequence, out entityTuple);
            var entity = entityTuple?.Item2;
            var entityId = string.Empty;
            if (entity != null)
            {
                entityId = Convert.ToString(entity["id"]);
                addErrorInfo(key, entityTuple.Item1);
            }
            return entityId;
        }

        /// <summary>
        /// 数据节点构建策略
        /// </summary>
        [Flags]
        internal enum Enu_BuildNodePolicy : int
        {
            /// <summary>
            /// 默认先按编码再按名称
            /// </summary>
            Default = -1,
            /// <summary>
            /// 仅按编码
            /// </summary>
            ByNumber = 1,
            /// <summary>
            /// 仅按名称
            /// </summary>
            ByName = 2,
        }
    }


























    ///// <summary>
    ///// 后台任务Excel导入服务
    ///// </summary>
    //[InjectService(AliasName = "Task")]
    //[TaskSvrId("excel.import.materialinit")]
    //public class MaterialInitTaskService : AbstractScheduleWorker
    //{
    //    /// <summary>
    //    /// 一个基于BlockingCollection实现的多线程的处理队列
    //    /// </summary>
    //    private class ProcessQueue<T>
    //    {
    //        private BlockingCollection<T> _queue;
    //        private CancellationTokenSource _cancellationTokenSource;
    //        private CancellationToken _cancellToken;
    //        //内部线程池
    //        private List<Thread> _threadCollection;

    //        //队列是否正在处理数据
    //        private int _isProcessing;
    //        //有线程正在处理数据
    //        private const int _processing = 1;
    //        //没有线程处理数据
    //        private const int _unProcessing = 0;
    //        //队列是否可用
    //        private volatile bool _enabled = true;
    //        //内部处理线程数量
    //        private int _internalThreadCount;

    //        public event Action<T> ProcessItemEvent;
    //        //处理异常，需要三个参数，当前队列实例，异常，当时处理的数据
    //        public event Action<dynamic, Exception, T> ProcessExceptionEvent;

    //        public ProcessQueue()
    //        {
    //            _queue = new BlockingCollection<T>();
    //            _cancellationTokenSource = new CancellationTokenSource();
    //            _internalThreadCount = 1;
    //            _cancellToken = _cancellationTokenSource.Token;
    //            _threadCollection = new List<Thread>();
    //        }

    //        public ProcessQueue(int internalThreadCount) : this()
    //        {
    //            this._internalThreadCount = internalThreadCount;
    //        }

    //        /// <summary>
    //        /// 队列内部元素的数量 
    //        /// </summary>
    //        public int GetInternalItemCount()
    //        {
    //            return _queue.Count;
    //        }

    //        public void Enqueue(T items)
    //        {
    //            if (items == null)
    //            {
    //                throw new ArgumentException("items");
    //            }

    //            _queue.Add(items);
    //            dataAdded();
    //        }

    //        public void Flush()
    //        {
    //            stopProcess();

    //            while (_queue.Count != 0)
    //            {
    //                T item = default(T);
    //                if (_queue.TryTake(out item))
    //                {
    //                    try
    //                    {
    //                        ProcessItemEvent(item);
    //                    }
    //                    catch (Exception ex)
    //                    {
    //                        onProcessException(ex, item);
    //                    }
    //                }
    //            }
    //        }

    //        private void dataAdded()
    //        {
    //            if (_enabled)
    //            {
    //                if (!isProcessingItem())
    //                {
    //                    processRangeItem();
    //                    startProcess();
    //                }
    //            }
    //        }

    //        //判断是否队列有线程正在处理 
    //        private bool isProcessingItem()
    //        {
    //            return !(Interlocked.CompareExchange(ref _isProcessing, _processing, _unProcessing) == _unProcessing);
    //        }

    //        private void processRangeItem()
    //        {
    //            for (int i = 0; i < this._internalThreadCount; i++)
    //            {
    //                processItem();
    //            }
    //        }

    //        private void processItem()
    //        {
    //            Thread currentThread = new Thread((state) =>
    //            {
    //                T item = default(T);
    //                while (_enabled)
    //                {
    //                    try
    //                    {
    //                        item = _queue.Take(_cancellToken);
    //                        ProcessItemEvent(item);
    //                    }
    //                    catch (Exception ex)
    //                    {
    //                        onProcessException(ex, item);
    //                    }
    //                }
    //            });

    //            _threadCollection.Add(currentThread);
    //        }

    //        private void startProcess()
    //        {
    //            foreach (var thread in _threadCollection)
    //            {
    //                thread.Start();
    //            }
    //        }

    //        private void stopProcess()
    //        {
    //            this._enabled = false;
    //            foreach (var thread in _threadCollection)
    //            {
    //                if (thread.IsAlive)
    //                {
    //                    thread.Join(new TimeSpan(0, 0, 1));
    //                }
    //            }
    //            _threadCollection.Clear();
    //        }

    //        private void onProcessException(Exception ex, T item)
    //        {
    //            var tempException = ProcessExceptionEvent;
    //            Interlocked.CompareExchange(ref ProcessExceptionEvent, null, null);

    //            if (tempException != null)
    //            {
    //                ProcessExceptionEvent(this, ex, item);
    //            }
    //        }

    //    }

    //    private class ExcelHelper : IDisposable
    //    {
    //        private const string IMPORTRESULT = "导入结果";
    //        private const string ERRORREASON = "错误原因";

    //        private IExcelAssistant _excelAssistant;
    //        private string _excelPath;
    //        private int _importResultColumnIndex;
    //        private int _errorReasonColumnIndex;
    //        private HtmlForm _htmlForm;
    //        private List<HtmlField> _fields;
    //        private bool isCheckedColumnHead;

    //        public ExcelHelper(IExcelAssistantFactory excelAssistantFactory, string fileUrl, string fileId, HtmlForm htmlForm)
    //        {
    //            _htmlForm = htmlForm;
    //            _excelAssistant = excelAssistantFactory.CreateExcelAssistant(fileUrl);
    //            _excelAssistant.HtmlForm = htmlForm;
    //            _excelPath = excelAssistantFactory.GetDefaultPath(fileId);
    //            isCheckedColumnHead = false;
    //        }

    //        /// <summary>
    //        /// 检查列头信息
    //        /// </summary>
    //        public void CheckColumnHeadInfo()
    //        {
    //            if (isCheckedColumnHead)
    //            {
    //                return;
    //            }
    //            isCheckedColumnHead = true;
    //            _importResultColumnIndex = _excelAssistant.ContainFieldCaption(IMPORTRESULT);
    //            _errorReasonColumnIndex = _excelAssistant.ContainFieldCaption(ERRORREASON);

    //            if (_importResultColumnIndex < 0 || _errorReasonColumnIndex < 0)
    //            {
    //                throw new BusinessException($"缺少[{IMPORTRESULT}]或者[{ERRORREASON}]列，请补全");
    //            }

    //            var _fields = _htmlForm.GetFieldList().Where(x => _excelAssistant.ContainFieldId(x.Id) > -1).ToList();

    //            if (_fields == null || _fields.Count <= 0)
    //            {
    //                throw new BusinessException("无法从excel文件中分析出字段");
    //            }
    //        }

    //        public void LoadExistedBaseDatas()
    //        {
    //            var fields = GetField().Where(x => x is HtmlBaseDataField);
    //            if (false == fields.Any())
    //            {
    //                return;
    //            }

    //        }

    //        public List<HtmlField> GetField()
    //        {
    //            CheckColumnHeadInfo();
    //            return _fields;
    //        }

    //        public void Dispose()
    //        {
    //            if (_excelAssistant != null)
    //            {
    //                _excelAssistant.Dispose();
    //            }
    //        }
    //    }

    //    private class LogHelper : IDisposable
    //    {
    //        private StreamWriter _logFile;
    //        private string _url;

    //        public LogHelper(IExcelAssistantFactory excelAssistantFactory, string fileId)
    //        {
    //            var logPath = excelAssistantFactory.GetDefaultPath(fileId, "txt");
    //            _logFile = new StreamWriter(File.Open(logPath, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite));
    //            _url = excelAssistantFactory.GetWebUrl(fileId, "txt");
    //        }

    //        public string Url
    //        {
    //            get { return _url; }
    //        }

    //        public void Dispose()
    //        {
    //            if (_logFile != null)
    //            {
    //                _logFile.Dispose();
    //            }
    //        }
    //    }

    //    private class BaseDataReader
    //    {
    //        private IExcelAssistant _excelAssistant;
    //        public HtmlForm HtmlForm { get; private set; }

    //        public BaseDataReader(IExcelAssistant excelAssistant, HtmlForm htmlForm)
    //        {
    //            this._excelAssistant = excelAssistant.Clone();
    //            this.HtmlForm = htmlForm;
    //        }
    //    }

    //    private class ProductBaseFieldReader : BaseDataReader
    //    {
    //        private HtmlField _htmlField;

    //        public ProductBaseFieldReader(IExcelAssistant excelAssistant, HtmlForm htmlForm, HtmlField htmlField) : base(excelAssistant, htmlForm)
    //        {
    //            this._htmlField = htmlField;
    //        }
    //    }

    //    private class ProductReader : BaseDataReader
    //    {
    //        public ProductReader(IExcelAssistant excelAssistant, HtmlForm htmlForm) : base(excelAssistant, htmlForm)
    //        {
    //        }
    //    }

    //    private class AuxPropertyReader : BaseDataReader
    //    {
    //        public AuxPropertyReader(IExcelAssistant excelAssistant, HtmlForm htmlForm) : base(excelAssistant, htmlForm)
    //        {
    //        }
    //    }

    //    private const string NAMEKEY = "name";
    //    private const string NUMBERKEY = "number";
    //    private const string SEQUENCE = "序号";
    //    private const string SONSEQUENCE = "子结点序号";
    //    private const string NODEINFO = "nodeInfo";
    //    private const string SONNODE = "sonNode";
    //    private const string NODESTATUS = "nodeStatus";
    //    private const string NEWSTATUS = "new";
    //    private const string PREFIXPROPERTY = "辅助属性$";
    //    private const string PURPRICE = "采购价(价目表)";
    //    private const string ERRORINFO = "errorInfo";
    //    private const string IMPORTRESULT = "导入结果";
    //    private const string ERRORREASON = "错误原因";
    //    private const string SUCCESS = "成功";
    //    private const string WARNING = "警告";
    //    private const string FAILURE = "失败";
    //    private const string IMAGEID = "imageId";

    //    private bool _isSuccess;
    //    private ExcelHelper _excelHelper;
    //    private LogHelper _logHelper;

    //    protected override async Task DoExecute()
    //    {
    //        Dictionary<string, string> para = JsonConvert.DeserializeObject<Dictionary<string, string>>(this.TaskObject.Parameter as string);
    //        var fileUrl = para["fileurl"];
    //        var fileId = para["fileid"];
    //        IOperationResult result = new OperationResult();
    //        if (string.IsNullOrWhiteSpace(fileUrl))
    //        {
    //            result.ComplexMessage.ErrorMessages.Add("fileUrl不能为空!");
    //        }
    //        if (string.IsNullOrWhiteSpace(fileId))
    //        {
    //            result.ComplexMessage.ErrorMessages.Add("fileId不能为空!");
    //        }
    //        if (result.ComplexMessage.ErrorMessages.Count > 0)
    //        {
    //            return;
    //        }
    //        try
    //        {
    //            result = await Task.Run(() => importData(this.UserContext, fileUrl, fileId));
    //        }
    //        catch (AggregateException ex)
    //        {
    //            foreach (var single in ex.InnerExceptions)
    //            {
    //                //var msg = "导入出错";
    //                //setLogMsg(msg, 100, single, result);
    //            }
    //        }
    //        catch (Exception ex)
    //        {
    //            //var msg = "导入出错";
    //            //setLogMsg(msg, 100, ex, result);
    //        }
    //        //saveLog();
    //        this.Result.MergeResult(result);
    //        this.Result.SimpleMessage = "任务执行完毕";
    //    }

    //    private IOperationResult importData(UserContext userContext, string fileUrl, string fileId)
    //    {
    //        _isSuccess = true;
    //        var msg = string.Empty;
    //        IOperationResult result = new OperationResult();
    //        var metaModelService = userContext.Container.GetService<IMetaModelService>();
    //        var form = metaModelService.LoadFormModel(userContext, "ydj_product");
    //        var progressBase = 0;
    //        var increment = 10;
    //        var partIncrement = 0;
    //        var partCount = 0;

    //        var excelAssistantFactory = userContext.Container.GetService<IExcelAssistantFactory>();
    //        _excelHelper = new ExcelHelper(excelAssistantFactory, fileUrl, fileId, form);
    //        _logHelper = new LogHelper(excelAssistantFactory, fileId);
    //        try
    //        {
    //            this.Result.SimpleData["logFileUrl"] = _logHelper.Url;

    //            //检查excel文件列头信息
    //            _excelHelper.CheckColumnHeadInfo();
    //            //加载所有已存在的引用信息

    //        }
    //        catch (Exception ex)
    //        {
    //            msg = "导入出现不可预知异常!";
    //        }
    //        finally
    //        {
    //        }


    //        return result;
    //    }


    //}
}
