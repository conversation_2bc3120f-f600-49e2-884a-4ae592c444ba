using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MaterialInit
{
    /// <summary>
    /// 导出错误结果
    /// </summary>
    [InjectService]
    [FormId("sys_materialinit")]
    [OperationNo("importerror")]
    public class ImportError: AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            var dataEntity = e.DataEntitys[0];
            var fileId = Convert.ToString(dataEntity["ffileid"]);

            if (string.IsNullOrWhiteSpace(fileId))
            {
                throw new BusinessException("文件id不能为空!");
            }

            var excelAssistantFactory = this.Container.GetService<IExcelAssistantFactory>();
            var filePath = excelAssistantFactory.GetDefaultPath(fileId);
            var excelAssistant = excelAssistantFactory.CreateExcelAssistant(filePath);
            var emptyAssistant = excelAssistantFactory.CreateExcelAssistant();
            var importResultIndex = excelAssistant.ContainFieldCaption("导入结果");

            try
            {
                if (importResultIndex < 0)
                {
                    throw new BusinessException("您要读取的文件没有导入结果列");
                }

                foreach (var item in excelAssistant.ColumnNames)
                {
                    emptyAssistant.AddColumn(item);
                }
                var emptyRowIndex = 1;
                while (excelAssistant.NextRow())
                {
                    var importResult = excelAssistant.GetValueByColumnIndex(importResultIndex);
                    if (importResult == "成功")
                    {
                        continue;
                    }
                    for (int i = 0; i < excelAssistant.MaxColumns; i++)
                    {
                        emptyAssistant.SetValueByColumnIndex(i, emptyRowIndex, excelAssistant.GetValueByColumnIndex(i));
                    }
                    emptyRowIndex++;
                }

                var result = emptyAssistant.ToBase64String();

                this.Result.IsSuccess = !string.IsNullOrWhiteSpace(result);
                this.Result.SrvData = result;
            }
            finally
            {
                excelAssistant?.Dispose();
                emptyAssistant?.Dispose();
            }
            
        }
    }
}
