using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Schedule
{
    [InjectService]
    [FormId("sys_schedule")]
    [OperationNo("delSchedule")]
    public class Delete : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            string Id = this.GetQueryOrSimpleParam<string>("Id");
            if (Id.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentException("参数错误！");
            }
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "sys_schedule");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            DynamicObject dyObj = dm.Select(Id) as DynamicObject;
            if (!dyObj["fbizobjnumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(dyObj["fbizobject"]) == "ydj_service")
            {
                var sMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_service");
                var sdm = this.Container.GetService<IDataManager>();
                sdm.InitDbContext(this.Context, sMeta.GetDynamicObjectType(this.Context));
                DynamicObject sdyObj = sdm.SelectBy($"fbillno='{dyObj["fbizobjnumber"]}'").OfType<DynamicObject>().FirstOrDefault();
                if (dyObj == null)
                {
                    sdyObj["fisschedule"] = false;
                }
                sdm.Save(sdyObj);
            }
            dm.Delete(new string[] { Id });
        }
    }
}