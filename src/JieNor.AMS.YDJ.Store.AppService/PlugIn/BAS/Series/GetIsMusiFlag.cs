using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Series
{
    /// <summary>
    /// 品牌/系列：获取是否慕思品牌/系列
    /// </summary>
    [InjectService]
    [FormId("ydj_brand|ydj_series")]
    [OperationNo("getismusiflag")]
    public class GetIsMusiFlag : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var pkid = this.GetQueryOrSimpleParam("pkid", "");
            if (!string.IsNullOrWhiteSpace(pkid))
            {
                DynamicObject data = null;
                if (this.HtmlForm.Id == "ydj_brand")
                {
                    var sql = $@"/*dialect*/select fmusibrand as musiflag from t_Ydj_Brand with(nolock) where fid='{pkid}'";
                    data = this.DBService.ExecuteDynamicObject(this.Context, sql)?.FirstOrDefault();
                }
                else if (this.HtmlForm.Id == "ydj_series")
                {
                    var sql = $@"/*dialect*/select fmusiseries as musiflag from t_ydj_series with(nolock) where fid='{pkid}'";
                    data = this.DBService.ExecuteDynamicObject(this.Context, sql)?.FirstOrDefault();
                }

                if (data != null)
                {
                    this.Result.IsSuccess = true;
                    this.Result.SrvData = data["musiflag"];
                }
            }
        }
    }
}
