using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Price
{


    /// <summary>
    /// 销售价目表保存的校验：经销商自定义的价目表，不能包含总部商品
    /// </summary>
    public class SaveValidation : AbstractBaseValidation
    {
         

        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }
         
        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities==null || dataEntities.Length ==0)
            {
                return result;
            }

            if(userCtx.IsTopOrg )
            {
                return result;
            }

            userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, formInfo.GetDynamicObjectType(userCtx), dataEntities, true);
            foreach (var item in dataEntities)
            {
                var ftype = Convert.ToString(item["ftype"]);
                // 允许保存【报价类型】=“二级分销报价”的总部商品价目表，不校验。
                if (ftype.EqualsIgnoreCase("quote_type_04")) continue;

                var fbizorgid = item["fbizorgid"]?.ToString ();
                var fnumber = item["fnumber"]?.ToString();
                var fname = item["fname"]?.ToString();
                var enRows = item["fentry"] as DynamicObjectCollection;
                foreach (var enRow in enRows)
                {
                    if (ftype.EqualsIgnoreCase("quote_type_03"))
                    {
                        var mateObj = enRow["fproductid_ref"] as DynamicObject;
                        if (!Convert.ToString(mateObj["fmainorgid"]).EqualsIgnoreCase(this.Context.TopCompanyId))
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = @"经销商价目表【{0} {1}】中，商品【{2} {3}】不为总部商品, 不允许进行定经销价, 如果是自建产品请通过销售价目进行定价 !".Fmt(fnumber, fname, mateObj["fnumber"], mateObj["fname"]),
                                DataEntity = item,
                            });
                        }
                        if (Convert.ToBoolean(mateObj["fispresetprop"]) && Convert.ToString(enRow["fattrinfo"]).IsNullOrEmptyOrWhiteSpace()) {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = @"经销商价目表【{0} {1}】中，商品【{2} {3}】属于定制品,辅助属性必须维护 !".Fmt(fnumber, fname, mateObj["fnumber"], mateObj["fname"]),
                                DataEntity = item,
                            });
                        }
                    }

                    var matObj = enRow["fproductid"] as DynamicObject;
                    if(matObj ==null )
                    {
                        continue;
                    }

                    var orgId = matObj["fmainorgid"]?.ToString();
                    if ( orgId.EqualsIgnoreCase(userCtx.TopCompanyId))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"经销商价目表【{0} {1}】中，商品【{2} {3}】是总部授权的商品，不允许自定义价目 ！".Fmt(fnumber, fname, matObj["fnumber"], matObj["fname"]),
                            DataEntity = item,
                        });
                    }
                }                
            }
            return result;
        }
    }


}
