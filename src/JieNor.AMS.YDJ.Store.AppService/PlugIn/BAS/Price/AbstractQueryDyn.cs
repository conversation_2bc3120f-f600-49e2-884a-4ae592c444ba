using System;
using System.Collections.Generic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Price
{
    /// <summary>
    /// 属性选配：动态列基础资料字段（模糊查询、弹窗查询）操作抽象基类
    /// </summary>
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理基础资料字段过滤条件解析后事件逻辑
        /// </summary>
        /// <param name="e"></param>
        private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData.Item1?.ToLowerInvariant(); //基础资料字段标识
            var fieldFilter = eventData.Item2; //基础资料字段过滤条件
            var formid = this.HtmlForm.Id;
            var filter = "1!=1 ";
            var sql = string.Empty;
            switch (fieldKey)
            {
                case "fproductid":
                    {
                        filter = "fmainorgid='"+this.Context.TopCompanyId+"'";
                        e.Result = filter;
                        e.Cancel = true;
                    }
                    break;
            }
        }
    }
}
