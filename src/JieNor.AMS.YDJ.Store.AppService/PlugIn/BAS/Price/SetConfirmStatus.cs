using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Price
{
    public abstract class SetConfirmStatus : AbstractOperationServicePlugIn
    {
        protected abstract string ConfirmStatus { get; }
        protected abstract string PromptFieldId { get; }

        protected abstract string ProductIdKey { get; }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            if (string.IsNullOrWhiteSpace(ConfirmStatus))
            {
                throw new BusinessException("操作确认状态不能为空");
            }
            if (string.IsNullOrWhiteSpace(PromptFieldId))
            {
                throw new BusinessException("提示字段id不能为空");
            }
            HtmlField promptField = this.HtmlForm.GetField(PromptFieldId);
            if (promptField == null)
            {
                throw new BusinessException("提示字段不存在");
            }

            string selectRowErrorMsg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return checkSelectRow(newData, promptField, out selectRowErrorMsg);
            }).WithMessage("{0}", (billObj, propObj) => selectRowErrorMsg));
        }

        private bool checkSelectRow(DynamicObject dataEntity, HtmlField promptField, out string msg)
        {
            msg = string.Empty;
            //获取fid
            var id = Convert.ToString(dataEntity["id"]);

            //根据fid获取选择行fentry
            var selectIds = this.SelectedRows.Where(x => x.PkValue == id && x.EntityKey == "fentry").Select(x => x.EntryPkValue).Where(x => string.IsNullOrWhiteSpace(x) == false).Distinct().ToList();
            //var pkvalue = this.SelectedRows.FirstOrDefault()?.PkValue;
            //if ( string.IsNullOrWhiteSpace(pkvalue))
            //{
            //    msg = string.Format("没有选择行,请至少选择一条!");
            //    return false;
            //}

            var fentries = dataEntity["fentry"] as DynamicObjectCollection;
            if (fentries == null || fentries.Count <= 0)
            {
                msg = string.Format("{0}为{1}的价目表明细行的数据为空!", promptField.Caption, dataEntity[promptField.PropertyName]);
                return false;
            }
            foreach (var fentry in fentries)
            {
                var fentryId = Convert.ToString(fentry["id"]);
                if (selectIds == null || selectIds.Count <= 0)
                {
                    fentry["fconfirmstatus"] = ConfirmStatus;
                    switch (ConfirmStatus)
                    {
                        case "1":
                            fentry["fconfirmdate"] = null;
                            break;
                        case "2":
                            fentry["fconfirmdate"] = DateTime.Now;
                            break;
                        default:
                            break;
                    }
                    continue;
                }
                if (selectIds.Contains(fentryId))
                {
                    fentry["fconfirmstatus"] = ConfirmStatus;
                    switch (ConfirmStatus)
                    {
                        case "1":
                            fentry["fconfirmdate"] = null;
                            break;
                        case "2":
                            fentry["fconfirmdate"] = DateTime.Now;
                            break;
                        default:
                            break;
                    }
                }
            }
            return true;
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                throw new BusinessException("没有选择行,请至少选择一条!");
               // return;
            }
            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);
            var priceService = this.Container.GetService<IPriceService>();
            priceService.ClearPriceCache(this.Context, e.DataEntitys, ProductIdKey);
        }
    }
}
