using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Organization
{
    /// <summary>
    /// 组织：禁用
    /// 作者：zpf
    /// 日期：2022-05-27
    /// </summary>
    [InjectService]
    [FormId("bas_organization")]
    [OperationNo("Forbid")]
    public class Forbid : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="e"></param>
        public virtual void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys.Count() == 0) return;

            //获取当前组织下正常的用户信息
            var companyIds = e.DataEntitys.Select(t => Convert.ToString(t["id"])).ToList();
            List<DynamicObject> users = Helpers.UserInformationHelper.GetUsableUsersByCompanyIds(this.Context, companyIds);
            if (users.Count > 0)
            {
                users.ForEach(t => 
                { 
                    t["fforbidstatus"] = 1; 
                    t["fdescription"] += $"组织禁用，自动禁用该用户"; 
                });
                this.Context.SaveBizData("sec_user", users);
            }
        }
    }
}
