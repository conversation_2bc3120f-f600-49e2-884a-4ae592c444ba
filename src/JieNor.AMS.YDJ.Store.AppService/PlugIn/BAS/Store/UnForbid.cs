using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Store
{
    /// <summary>
    /// 门店：反禁用
    /// </summary>
    [InjectService]
    [FormId("bas_store")]
    [OperationNo("UnForbid")]
    public class UnForbid : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToBoolean(newData["fistransfer"]))
                {
                    errorMessage = $"{this.HtmlForm.Caption}【{newData["fnumber"]}】已转让，不允许反禁用！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="e"></param>
        public virtual void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys.Count() == 0) return;

            UnforbidPrdAuth(e.DataEntitys);
        }

        /// <summary>
        /// 禁用商品授权清单
        /// </summary>
        /// <param name="dataEntities"></param>
        private void UnforbidPrdAuth(IEnumerable<DynamicObject> dataEntities)
        {
            var orgIds = dataEntities.Select(s => Convert.ToString(s["id"]));

            var result = this.Container.GetService<IProductAuthService>().Unforbid(this.Context, this.HtmlForm, orgIds);
            this.Result.MergeResult(result);

            //var prdAuths = this.Context.LoadBizDataByNo("ydj_productauth", "forgid", orgIds);

            //foreach (var prdAuth in prdAuths)
            //{
            //    prdAuth["fdescription"] = "门店反禁用，自动反禁用该商品授权清单";
            //}

            //var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_productauth", prdAuths, "UnForbid", new Dictionary<string, object>());
            //this.Result.MergeResult(result);
        }
    }
}
