using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Store
{
    /// <summary>
    /// 门店：删除
    /// </summary>
    [InjectService]
    [FormId("bas_store")]
    [OperationNo("Delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fsrcstoreid"].IsNullOrEmptyOrWhiteSpace())
                {
                    errorMessage = $"{this.HtmlForm.Caption}【{newData["fnumber"]}】是由一级经销商转让时生成，不允许删除！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }
    }
}
