using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Store
{
    /// <summary>
    /// 关联门店查看权限校验
    /// </summary>
    [InjectService]
    [FormId("ydj_dept")]
    [OperationNo("checkvisitpermission")]
    public class CheckVisitPermission : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.IsSuccess = true;


            //1.点击《部门》的【关联门店】，需根据当前用户登录的经销商，判断是否存在《主经销商配置表》：

            //1.1.如果不存在：如果当前用户登录的【经销商编码】与【 关联门店 】的所属经销商的【经销商编码】不一致，则提示：“对不起，XXX部门关联门店已不归您组织所有，您无权查看!”，否则允许查看。

            //1.2.如果存在：再判断【主经销商编码】、【子经销商编码】是否与【 关联门店 】的所属经销商的【经销商编码】一致，如果都不一致，需提示：“对不起，XXX部门关联门店已不归您组织所有，您无权查看!”，否则允许查看。

            var deptName = this.GetQueryOrSimpleParam<string>("deptName");
            var storeId = this.GetQueryOrSimpleParam<string>("storeId");
            var companyId = this.Context.Company;
            if (storeId.IsNullOrEmptyOrWhiteSpace() || companyId.IsNullOrEmptyOrWhiteSpace()) return;

            var sql = $@"/*dialect*/ select 1 from t_bas_mac t0 with(nolock) inner join T_BAS_ORGANIZATION t1 with(nolock) on t0.fmainagentid=t1.fid where t1.fid='{companyId}'";
            var count = this.DBService.ExecuteDynamicObject(this.Context, sql).Count;
            if (count > 0)
            {
                sql = $@"/*dialect*/  select 1
                                     from t_bas_mac t0 with(nolock)
                                     inner join T_BAS_ORGANIZATION t1 with(nolock) on t0.fmainagentid=t1.fid 
                                     inner join t_bas_macentry t2 with(nolock) on t2.fid=t0.fid
                                     inner join t_bas_store t3 with(nolock) on t3.fagentid=t0.fmainagentid or t3.fagentid=t2.fsubagentid
                                     where t3.fid='{storeId}' and t1.fid='{companyId}'";
                count = this.DBService.ExecuteDynamicObject(this.Context, sql).Count;

                if (count > 0)
                {

                }
                else
                {
                    this.Result.IsSuccess = false;
                    throw new BusinessException($@"对不起，{deptName} 部门关联门店已不归您组织所有，您无权查看!");
                }
            }
            else
            {
                sql = $@"/*dialect*/ select 1 from t_bas_store t0 with(nolock) inner join T_BAS_ORGANIZATION t1 with(nolock) on t0.fagentid=t1.fid where t0.fid='{storeId}' and t1.fid='{companyId}'";
                count = this.DBService.ExecuteDynamicObject(this.Context, sql).Count;

                if (count > 0)
                {

                }
                else
                {
                    this.Result.IsSuccess = false;
                    throw new BusinessException($@"对不起，{deptName} 部门关联门店已不归您组织所有，您无权查看!");
                }
                
            }

        }

    }
}
