using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Store
{
    [InjectService]
    [FormId("bas_store")]
    [OperationNo("QueryData")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    var queryPara = e.EventData as SqlBuilderParameter;
                    if (queryPara != null)
                    {
                        //如果实体门店，可以查看禁用门店数据，queryPara.SrcPara为null表示是选实体门店时
                        if (queryPara.SrcFldId.EqualsIgnoreCase("fstoreactual") && queryPara.SrcPara == null && !queryPara.FilterString.IsNullOrEmptyOrWhiteSpace()) 
                        {
                            queryPara.FilterString = queryPara.FilterString.Replace("FForbidStatus='0'", "1=1");
                            queryPara.IsShowForbidden = true;
                            queryPara.NoIsolation = true;
                        }
                    }
                    break;
            }
        }
    }
}
