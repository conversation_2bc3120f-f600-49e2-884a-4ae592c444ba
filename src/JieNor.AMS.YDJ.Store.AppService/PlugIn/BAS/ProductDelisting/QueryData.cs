//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormModel.List;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using Newtonsoft.Json.Linq;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;


//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.ProductDelisting
//{
//    /// <summary>
//    /// 退市清单：列表查询数据插件
//    /// </summary>
//    [InjectService]
//    [FormId("ydj_productdelisting")]
//    [OperationNo("querydata")]
//    public class QueryData : AbstractOperationServicePlugIn
//    {
//        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
//        {
//            base.OnCustomServiceEvent(e);

//            //事件名称
//            switch (e.EventName)
//            {
//                case "afterListData":
//                    this.afterListData(e);
//                    break;
//                default:
//                    break;
//            }
//        }

//        /// <summary>
//        /// 来源单据打包后事件：可对打包后的数据包进行处理或者直接覆盖整个数据包都是可以的
//        /// </summary>
//        /// <param name="e"></param>
//        private void afterListData(OnCustomServiceEventArgs e)
//        {

//            var datas = e.EventData as List<Dictionary<string, object>>;
//            var gp = datas.GroupBy(a => a["fid"]).ToList();
//            var result = new List<Dictionary<string, object>>();
//            foreach (var item in gp)
//            {
//                var _datas = datas.Where(a => item.Key.ToString() == Convert.ToString(a["fid"])).OrderByDescending(a => Convert.ToDateTime(a["fhqpushtime"])).ToList();
//                result.AddRange(_datas);
//            }
//            e.EventData = result;
//        }
//    }
//}
