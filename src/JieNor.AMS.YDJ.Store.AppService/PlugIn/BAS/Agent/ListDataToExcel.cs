using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Agent
{
    /// <summary>
    /// 库存余额查询数据插件：用来只显示最新一期余额数据，往期不用显示给用户，无意义
    /// </summary>
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("listdatatoexcel")]
    public class ListDataToExcel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义事件中，提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case "afterListData":
                    var data = e.EventData as QueryDataInfo;
                    UpdateDataInfo(data);
                    break;
            }
        }

        /// <summary>
        /// 城市类型是多选的时候，同步更新ctlfk="fcity"并且 el="107"的数据
        /// </summary>
        /// <param name="lookupForm"></param>
        /// <param name="datas"></param>
        private void UpdateDataInfo(QueryDataInfo datas)
        {
            HtmlForm lookupForm = this.OperationContext?.HtmlForm;
            if (datas == null || (datas != null && datas.OfficeDatas == null)) return;
            if (!datas.OfficeDatas.ContainsKey("fcity")) return;

            var havekey = datas.OfficeDatas.Where(c => c.Key == "fcity").FirstOrDefault();
            var commoncity = "";
            foreach (var item in havekey.Value)
            {
                commoncity += item + ",";
            }

            var allCity = string.Join("','", commoncity.Split(',').ToArray());

            var citylist = this.OperationContext.UserContext.LoadBizDataByACLFilter("ydj_city", $"fid in ('{allCity}')", true);

            for (int i = 0; i < havekey.Value.Count; i++)
            {
                var cityids = havekey.Value[i];
                if (cityids == null)
                {
                    continue;
                }

                foreach (HtmlField field in lookupForm.GetFieldList())
                {
                    var basefiled = field as HtmlBasePropertyField;

                    if (basefiled != null && basefiled.ControlFieldKey == "fcity")
                    {
                        var cityidsLst = cityids.ToString().Split(',').ToList();
                        //城市多选的才需要特殊处理
                        if (cityidsLst.Count < 2)
                        {
                            continue;
                        }
                        var citys = citylist.Where(c => cityidsLst.Contains(c["Id"])).ToList();
                        var cityrelation = citys.Select(o => (o[$"{basefiled.Id}_ref"] as DynamicObject)?["fenumitem"].ToString()).Distinct().JoinEx(",", false);
                        if (cityrelation == null)
                        {
                            continue;
                        }

                        if (!datas.OfficeDatas.ContainsKey(basefiled.Id)) continue;

                        var keyValuePairfile = datas.OfficeDatas.Where(c => c.Key == basefiled.Id).FirstOrDefault();
                        if (!keyValuePairfile.Value.IsNullOrEmpty())
                        {
                            for (int j = 0; j < keyValuePairfile.Value.Count; j++)
                            {
                                if (i == j)
                                {
                                    keyValuePairfile.Value[j] = cityrelation;
                                }
                            }
                        }
                        
                    }
                }

            }
        }


    }
}
