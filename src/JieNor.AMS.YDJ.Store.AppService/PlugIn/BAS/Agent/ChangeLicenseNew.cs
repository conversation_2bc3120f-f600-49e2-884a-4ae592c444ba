using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.AMS.YDJ.Store.AppService.Validation.BAS.Agent;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using Newtonsoft.Json;
using JieNor.Framework.MetaCore.Validator;
using ServiceStack;
using System.Threading;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Const;
using JieNor.Framework.SuperOrm;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.BizTask;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Agent
{
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("ChangeLicenseNew")]
    //更新营业执照重构
    public class ChangeLicenseNew : AbstractOperationServicePlugIn
    {

        protected ILogService loger { get; set; }

        private string fnewagentid { get; set; }

        private const string SupplierFormId = "ydj_supplier";

        private OperateOption option = OperateOption.Create();
        /// <summary>
        /// 操作结果
        /// </summary>
        protected IOperationResult Result => this.OperationContext.Result;
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            //if (e.DataEntitys.Length == 0)
            //    return;
            base.BeginOperationTransaction(e);
            this.fnewagentid =  this.GetQueryOrSimpleParam<string>("fnewagentid","");

            var dataStr = this.GetQueryOrSimpleParam("data", "");
            if (dataStr.IsNullOrEmptyOrWhiteSpace()) return;
            JObject agentDataObj;
            try
            {
                agentDataObj = JObject.Parse(dataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数格式不正确！");
            }

            string NewAgentId = agentDataObj.GetJsonValue("NewAgentId", "");
            string OldAgentId = agentDataObj.GetJsonValue("OldAgentId", ""); 

            var AgentDataEntity = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "ftranid", new List<string>() { OldAgentId, NewAgentId });
            var NewAgent = AgentDataEntity.Where(o => Convert.ToString(o["ftranid"]).EqualsIgnoreCase(NewAgentId))?.FirstOrDefault();
            var OldAgent = AgentDataEntity.Where(o => Convert.ToString(o["ftranid"]).EqualsIgnoreCase(OldAgentId))?.FirstOrDefault();
            if (NewAgent.IsNullOrEmptyOrWhiteSpace()) 
            {
                throw new BusinessException($"未找到{NewAgentId}此经销商！");
            }
            if (OldAgent.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"未找到{OldAgentId}此经销商！");
            }
            if (!Convert.ToString(NewAgent["actualownernumber"]).EqualsIgnoreCase(Convert.ToString(OldAgent["actualownernumber"]))) 
            {
                throw new BusinessException($"新经销商与旧经销商实控人不一致，不允许更换营业执照！");
            }
            //更换营业执照 模拟数据包，通过旧经销商加载数据包，将新经销商id 赋值到新营业执照字段中。
            OldAgent["fnewagentid"] = Convert.ToString(NewAgent["Id"]);
            this.fnewagentid = Convert.ToString(NewAgent["Id"]);
            e.DataEntitys = new DynamicObject[] { OldAgent };
        }
        /// <summary>
        /// 更换经销商
        /// </summary>
        /// <param name="e"></param>
        private void AlterAgent(AfterExecuteOperationTransaction e)
        {
            var oldagentItem = this.Context.LoadBizDataById("bas_agent", fnewagentid);
            var newagentItem = this.Context.LoadBizDataById("bas_agent", Convert.ToString(e.DataEntitys[0]["id"]));

            //除id外 互换经销商信息。
            var oldagentItem_After = (DynamicObject)oldagentItem.Clone();
            oldagentItem_After["id"] = Convert.ToString(e.DataEntitys[0]["id"]);
            oldagentItem_After["foldagentid"] = Convert.ToString(fnewagentid);

            var newagentItem_After = (DynamicObject)newagentItem.Clone();
            newagentItem_After["id"] = Convert.ToString(fnewagentid);
            newagentItem_After["foldagentid"] = "";

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Delete(new string[] { e.DataEntitys[0]["id"].ToString(), fnewagentid });
            //可能先下发了 旧营业执照的禁用状态，避免save保存再触发禁用商品授权清单，这里只需要draft保存数据就行。
            var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "bas_agent", new DynamicObject[] { oldagentItem_After, newagentItem_After }, "draft",
            option.ToDictionary(s => s.Key, s => s.Value));

            this.Result.MergeResult(tmpresult);
            AlterSyncState(Convert.ToString(e.DataEntitys[0]["id"]));
            this.Logger.WriteLog(this.Context, new LogEntry
            {
                BillIds = e.DataEntitys[0]["id"] as string,
                BillNos = e.DataEntitys[0]["fnumber"] as string,
                BillFormId = HtmlForm.Id,
                OpName = "更换营业执照",
                OpCode = this.OperationNo,
                Content = "执行了【更换营业执照】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(e.DataEntitys[0]["fnumber"], e.DataEntitys[0]["fname"], oldagentItem_After["fnumber"], oldagentItem_After["fname"]),
                DebugData = "执行了【更换营业执照】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(e.DataEntitys[0]["fnumber"], e.DataEntitys[0]["fname"], oldagentItem_After["fnumber"], oldagentItem_After["fname"]),
                Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                Level = Enu_LogLevel.Info.ToString(),
                LogType = Enu_LogType.RecordType_03
            });
            //this.WriteOpLog("门店审核结果：" + tmpresult.ToString());
            //var obj2 = T1MapToT2_2<DynamicObject, DynamicObject>(oldagentItem); //obj2.i = 1; obj2.str = "ss";
        }

        //切换营业执照后将旧经销商的用户、员工档案同步状态都清空，目的是切换营业执照后重新同步一次
        private void AlterSyncState(string oldagentid)
        {
            var updateSqls = new List<string>();
            updateSqls.Add($"/*dialect*/UPDATE T_SEC_USER SET fmusisyncdate=NULL,fmusisyncstatus='01'  WHERE fmainorgid ='{oldagentid}' AND fmusisyncstatus ='02' and fforbidstatus ='0';");
            updateSqls.Add($"/*dialect*/UPDATE T_BD_STAFF SET fmusisyncdate=NULL,fmusisyncstatus='01'  WHERE fmainorgid ='{oldagentid}' AND fmusisyncstatus ='02'  and fforbidstatus ='0';");
            updateSqls.Add($"/*dialect*/UPDATE T_YDJ_POSITION SET fmusisyncdate=NULL,fmusisyncstatus='01' WHERE fmainorgid ='{oldagentid}' AND fmusisyncstatus ='02' and fforbidstatus ='0';");
            updateSqls.Add($"/*dialect*/UPDATE T_SEC_ROLE SET fmusisyncdate=NULL,fmusisyncstatus='01' WHERE fmainorgid ='{oldagentid}' AND fmusisyncstatus ='02' and fforbidstatus ='0';");
            updateSqls.Add($"/*dialect*/UPDATE T_BD_DEPARTMENT SET fmusisyncdate=NULL,fmusisyncstatus='01' WHERE fmainorgid ='{oldagentid}' AND fmusisyncstatus ='02' and fforbidstatus ='0';");
            //更新同步状态
            var dbServiceEx = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceEx.ExecuteBatch(this.Context, updateSqls);
        }

        /// <summary>
        /// 更换组织
        /// </summary>
        /// <param name="e"></param>
        private void AlterOrg(AfterExecuteOperationTransaction e)
        {
            var data = e.DataEntitys[0];
            //旧经销商
            var oldagent = this.Context.LoadBizDataById("bas_agent", Convert.ToString(data["id"]));
            //新经销商
            var newagent = this.Context.LoadBizDataById("bas_agent", fnewagentid); 

            var newItem = this.Context.LoadBizDataById("bas_organization", Convert.ToString(newagent["id"]));
            var oldItem = this.Context.LoadBizDataById("bas_organization", Convert.ToString(oldagent["id"]));
            var OrgObjs = new List<DynamicObject>();
            //组织更换编码、名称
            if (newItem != null && newagent != null)
            {
                newItem["fnumber"] = newagent["fnumber"];
                newItem["fname"] = newagent["fname"];
                OrgObjs.Add(newItem);
            }
            if (oldItem != null && oldagent != null)
            {
                oldItem["fnumber"] = oldagent["fnumber"];
                oldItem["fname"] = oldagent["fname"];
                OrgObjs.Add(oldItem);
            }
            try
            {
                if (OrgObjs.Any())
                {
                    var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "bas_organization", OrgObjs, "save", new Dictionary<string, object>());
                    this.Result.MergeResult(tmpresult);
                    this.Logger.WriteLog(this.Context, new LogEntry
                    {
                        BillIds = e.DataEntitys[0]["id"] as string,
                        BillNos = e.DataEntitys[0]["fnumber"] as string,
                        BillFormId = HtmlForm.Id,
                        OpName = "更换组织",
                        OpCode = this.OperationNo,
                        Content = "执行了【更换组织】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(newItem["fnumber"], newItem["fname"], oldItem["fnumber"], oldItem["fname"]),
                        DebugData = "执行了【更换组织】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(newItem["fnumber"], newItem["fname"], oldItem["fnumber"], oldItem["fname"]),
                        Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                        Level = Enu_LogLevel.Info.ToString(),
                        LogType = Enu_LogType.RecordType_03
                    });
                }
            }
            catch (Exception ex)
            {
                this.Result.MergeResult(new OperationResult
                {
                    SimpleMessage = ex.Message,
                    IsSuccess = false
                });
                this.Logger.WriteLog(this.Context, new LogEntry
                {
                    BillIds = e.DataEntitys[0]["id"] as string,
                    BillNos = e.DataEntitys[0]["fnumber"] as string,
                    BillFormId = HtmlForm.Id,
                    OpName = "更换组织",
                    OpCode = this.OperationNo,
                    Content = "执行了【更换组织】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    DebugData = "执行了【更换组织】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });
                //this.Result.SimpleMessage = ex.Message;
                this.Result.SrvData = new Dictionary<string, string> { };
            }
            finally
            {
                if (!oldItem.IsNullOrEmpty())
                {
                    //创建供应商
                    CreateSuppler(oldItem);
                }
            }
        }
        /// <summary>
        /// 更换企业
        /// </summary>
        /// <param name="e"></param>
        private void AlterCompany(AfterExecuteOperationTransaction e)
        {
            //清除缓存 
            var pubSubService = this.Container.GetService<IPubSubService>();
            PublishSubDBCenterMsgInfo msg = new PublishSubDBCenterMsgInfo();
            var svc = this.Container.GetService<IDataCenterService>();
            SQLDBServerInfo sqlInfo = svc.GetDataCenterServer();
            var AgentService = this.Container.GetService<IAgentService>();

            var data = e.DataEntitys[0];
            //旧经销商
            var oldagent = this.Context.LoadBizDataById("bas_agent", Convert.ToString(data["id"]));
            //新经销商
            var newagent = this.Context.LoadBizDataById("bas_agent", fnewagentid);

            var comps = DataCenterExtentions.GetAllCompanys(this);
            comps.TryGetValue(Convert.ToString(newagent["id"]), out var compItem);
            comps.TryGetValue(Convert.ToString(oldagent["id"]), out var compItemnew);
            try
            {
                if (compItem != null)
                {
                    compItem.CompanyNumber = Convert.ToString(newagent["fnumber"]);
                    compItem.CompanyName = Convert.ToString(newagent["fname"]);

                    msg = new PublishSubDBCenterMsgInfo() { companyInfo = compItem, dbServerInfo = sqlInfo, Add = false };
                    pubSubService.PublishMessage(PubSubChannel.DatacenterConfig, msg);

                    msg = new PublishSubDBCenterMsgInfo() { companyInfo = compItem, dbServerInfo = sqlInfo, Add = true };
                    pubSubService.PublishMessage(PubSubChannel.DatacenterConfig, msg);
                }
                if (compItemnew != null)
                {
                    compItemnew.CompanyNumber = Convert.ToString(oldagent["fnumber"]);
                    compItemnew.CompanyName = Convert.ToString(oldagent["fname"]);

                    msg = new PublishSubDBCenterMsgInfo() { companyInfo = compItemnew, dbServerInfo = sqlInfo, Add = false };
                    pubSubService.PublishMessage(PubSubChannel.DatacenterConfig, msg);

                    msg = new PublishSubDBCenterMsgInfo() { companyInfo = compItemnew, dbServerInfo = sqlInfo, Add = true };
                    pubSubService.PublishMessage(PubSubChannel.DatacenterConfig, msg);
                }
                if (compItem != null && compItemnew != null)
                {
                    AgentService.UpdateCompany(this.Context, new DynamicObject[] { newagent, oldagent });
                }
                if (msg.IsErrorResponse())
                {
                    this.Result.MergeResult(new OperationResult
                    {
                        SimpleMessage = "企业更新失败！",
                        IsSuccess = false
                    });
                    //this.Logger.WriteLog(this.Context, new LogEntry
                    //{
                    //    BillIds = e.DataEntitys[0]["id"] as string,
                    //    BillNos = e.DataEntitys[0]["fnumber"] as string,
                    //    BillFormId = HtmlForm.Id,
                    //    OpName = "更换企业",
                    //    OpCode = this.OperationNo,
                    //    Content = "执行了【更换企业】操作，操作失败，错误原因：{0}".Fmt(msg),
                    //    DebugData = "执行了【更换企业】操作，操作失败，错误原因：{0}".Fmt(msg),
                    //    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    //    Level = Enu_LogLevel.Info.ToString(),
                    //    LogType = Enu_LogType.RecordType_03
                    //});
                }
                else
                {
                    this.Logger.WriteLog(this.Context, new LogEntry
                    {
                        BillIds = e.DataEntitys[0]["id"] as string,
                        BillNos = e.DataEntitys[0]["fnumber"] as string,
                        BillFormId = HtmlForm.Id,
                        OpName = "更换企业",
                        OpCode = this.OperationNo,
                        Content = "执行了【更换企业】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(newagent["fnumber"], newagent["fname"], oldagent["fnumber"], oldagent["fname"]),
                        DebugData = "执行了【更换企业】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(newagent["fnumber"], newagent["fname"], oldagent["fnumber"], oldagent["fname"]),
                        Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                        Level = Enu_LogLevel.Info.ToString(),
                        LogType = Enu_LogType.RecordType_03
                    });
                    this.Result.MergeResult(new OperationResult
                    {
                        SimpleMessage = "企业更新成功",
                        IsSuccess = true
                    });
                }
            }
            catch (Exception ex)
            {
                this.Result.MergeResult(new OperationResult
                {
                    SimpleMessage = "企业更新失败！",
                    IsSuccess = false
                });
                this.Logger.WriteLog(this.Context, new LogEntry
                {
                    BillIds = e.DataEntitys[0]["id"] as string,
                    BillNos = e.DataEntitys[0]["fnumber"] as string,
                    BillFormId = HtmlForm.Id,
                    OpName = "更换企业",
                    OpCode = this.OperationNo,
                    Content = "执行了【更换企业】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    DebugData = "执行了【更换企业】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });
            }

        }
        /// <summary>
        /// 更换送达方(其实关联的经销商内码不变，业务上需要看到关联的经销商编码、名称变)
        /// </summary>
        /// <param name="e"></param>
        private void AlterDeliver(AfterExecuteOperationTransaction e)
        {
            var data = e.DataEntitys[0];
            //如果旧营业执照无送达方则无须调换
            var newItem = this.Context.LoadBizDataByFilter("bas_deliver", $"fagentid='{fnewagentid}'");
            var oldItem = this.Context.LoadBizDataByFilter("bas_deliver", $"fagentid='{Convert.ToString(data["id"])}'");

            if (oldItem != null && oldItem.Count > 0)
            {
                //不需要走送达方更新，内码不变,但是编码、名称会对应变，即是变的逻辑
                return;
            }

            if (oldItem != null && oldItem.Count > 0)
            {
                oldItem.ForEach(_ => _["fagentid"] = fnewagentid);
                var result1 = this.Gateway.InvokeBillOperation(this.Context, "bas_deliver", oldItem, "draft",
                    new Dictionary<string, object>());
            }
            if (newItem != null && newItem.Count > 0)
            {
                newItem.ForEach(_ => _["fagentid"] = data["id"]);
                var result1 = this.Gateway.InvokeBillOperation(this.Context, "bas_deliver", newItem, "draft",
                    new Dictionary<string, object>());
            }
            newItem.AddRange(oldItem);
            var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "bas_deliver", newItem, "draft",
            new Dictionary<string, object>());

            this.Result.MergeResult(tmpresult);
        }


        /// <summary>
        /// 更换送达方 （默认招商经销商调换过一次）这里要调换下fagentid,因为更换经销商编码名称变了，所以要调换回来。
        /// </summary>
        /// <param name="e"></param>
        private void AlterDeliver_New(AfterExecuteOperationTransaction e)
        {
            var data = e.DataEntitys[0];
            //如果旧营业执照无送达方则无须调换
            var newItem = this.Context.LoadBizDataByFilter("bas_deliver", $"fagentid='{fnewagentid}'");
            var oldItem = this.Context.LoadBizDataByFilter("bas_deliver", $"fagentid='{Convert.ToString(data["id"])}'");

            var deliverService = this.Context.Container.GetService<IDeliverService>();
            //1、oldItem不为空、newItem不为空 说明 新旧经销商 都有对应的送达方，因为送达方存的是内码所以不需要调换送达方内的经销商内码fagentid。
            //2、oldItem为空、newItem不为空 说明 两个经销商共用同一个送达方此时需要调换此送达方的经销商内码fagentid。
            //if (oldItem != null && oldItem.Count > 0)
            //{
            //    //不需要走送达方更新，内码不变,但是编码、名称会对应变，即是变的逻辑
            //    return;
            //}

            //241222  
            //  更换营业执照门店、送达方关联经销商没有更换的问题原因：假如是A（旧）更换为B（新）的场景，门店和送达方关联的经销商为A。
            //1、更换营业执照之前，招商主数据会归档下发最新门店与售达方关系、送达方与售达方关系下来。
            //2、此时 门店与系列、送达方与系列的 售达方会被更新为B 下发下来。
            //3、此时拉取主数据 送达方、门店的经销商会被更新为B。
            //4、如果几小时后 佩玲再去做更换营业执照操作（手动）经销商A、B会调换，那门店、送达方这里看到的 经销商编码、名称又会调换成A。
            //综上的效果就是 门店、送达方的经销商编码、名称没有随更换营业执照改变而改变。

            //方案：更换营业执照前 招商都会更新门店、送达方关系数据，更新售达方。那金蝶这边在处理手动更换营业执照的时候再去特殊处理一下（默认招商经销商调换过一次）

            if (oldItem != null && oldItem.Count > 0)
            {
                oldItem.ForEach(_ => _["fagentid"] = fnewagentid);
            }
            if (newItem != null && newItem.Count > 0)
            {
                newItem.ForEach(_ => _["fagentid"] = data["id"]);
            }
            newItem.AddRange(oldItem);
            //如果是 先下发经销商停用会导致送达方的系列明细、商品授权 相应的清空，在更换营业执照的时候需要再补全这部分的数据。
            deliverService.UpdateBrandEntry(this.Context, newItem);

            var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "bas_deliver", newItem, "save",
            new Dictionary<string, object>());

            deliverService.AddOrUpdateProductAuth(this.Context, newItem, true, option);

            this.Result.MergeResult(tmpresult);
        }

        private void AlterStore_New(AfterExecuteOperationTransaction e)
        {
            var data = e.DataEntitys[0]; 
            var newItem = this.Context.LoadBizDataByFilter("bas_store", $"fagentid='{fnewagentid}'");
            var oldItem = this.Context.LoadBizDataByFilter("bas_store", $"fagentid='{Convert.ToString(data["id"])}'");
            //找到关联的门店 的经销商流水id和门店与系列的流水id不一致的门店数据重新赋值经销商id
            var sql = $@"select store.fid as id,ag_new.fid as fagentid from t_bas_store as store with (nolock) 
                        inner join t_bas_agent as ag with (nolock) on store.fagentid = ag.fid
                        inner join t_ms_store_series as ss with (nolock) on ss.fstoreid = store.ftranid
                        inner join t_bas_agent as ag_new with (nolock) on ag_new.ftranid = ss.fagentid
                        where ag.fnumber in ('{fnewagentid}','{Convert.ToString(data["id"])}')
                         and ag.ftranid != ss.fagentid";
            var storeData = this.Context.Container.GetService<IDBService>()
                .ExecuteDynamicObject(this.Context, sql);

            //做一层补偿逻辑更新门店的经销商编码和上级编码按门店与系列中间表的关系去对应。
            if (storeData.Any())
            {
                var storeIds = storeData.Select(o => Convert.ToString(o["id"])).ToList();
                var storeObjs = this.Context.LoadBizDataById("bas_store", storeIds);

                foreach (var storeObj in storeObjs) 
                {
                    var agentId = storeData.Where(o => Convert.ToString(storeObj["id"]).EqualsIgnoreCase(Convert.ToString(o["id"])))
                        .Select(x => Convert.ToString(x["fagentid"]))?.FirstOrDefault();
                    if (!agentId.IsNullOrEmptyOrWhiteSpace()) 
                    {
                        storeObj["fagentid"] = agentId;
                        storeObj["forgid"] = agentId;
                    } 
                }
                if (storeObjs.Any()) 
                {
                    var gateway = this.Context.Container.GetService<IHttpServiceInvoker>();
                    var result = gateway.InvokeBillOperation(this.Context, "bas_store", storeObjs, "draft",
                        new Dictionary<string, object>());
                    this.Result.MergeResult(result);
                }
            }
            //var storeService = this.Context.Container.GetService<IStoreService>();

            ////241222  
            ////  更换营业执照门店、送达方关联经销商没有更换的问题原因：假如是A（旧）更换为B（新）的场景，门店和送达方关联的经销商为A。
            ////1、更换营业执照之前，招商主数据会归档下发最新门店与售达方关系、送达方与售达方关系下来。
            ////2、此时 门店与系列、送达方与系列的 售达方会被更新为B 下发下来。
            ////3、此时拉取主数据 送达方、门店的经销商会被更新为B。
            ////4、如果几小时后 佩玲再去做更换营业执照操作（手动）经销商A、B会调换，那门店、送达方这里看到的 经销商编码、名称又会调换成A。
            ////综上的效果就是 门店、送达方的经销商编码、名称没有随更换营业执照改变而改变。

            ////方案：更换营业执照前 招商都会更新门店、送达方关系数据，更新售达方。那金蝶这边在处理手动更换营业执照的时候再去特殊处理一下（默认招商经销商调换过一次）

            //if (oldItem != null && oldItem.Count > 0)
            //{
            //    oldItem.ForEach(_ => _["fagentid"] = fnewagentid);
            //}
            //if (newItem != null && newItem.Count > 0)
            //{
            //    newItem.ForEach(_ => _["fagentid"] = data["id"]);
            //}
            //newItem.AddRange(oldItem); 

            //var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "bas_store", newItem, "save",
            //new Dictionary<string, object>());

            //storeService.AddOrUpdateProductAuth(this.Context, newItem);

            //this.Result.MergeResult(tmpresult);
        }

        /// <summary>
        /// 更新主子经销商
        /// </summary>
        /// <param name="e"></param>
        private void AlterMainAgentConfig(AfterExecuteOperationTransaction e)
        {
            var data = e.DataEntitys[0];
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "bas_mainagentconfig");
            var dt = htmlForm.GetDynamicObjectType(this.Context);
            //需判断当前旧经销商是否有配置《主经销商配置表》
            var MainAgentConfigs = this.Context.LoadBizDataByACLFilter("bas_mainagentconfig", $"fforbidstatus =0 and ( fmainagentid = '{Convert.ToString(data["id"])}' or exists (SELECT 1 FROM t_bas_macentry WHERE t_bas_macentry.fid =  t_bas_mac.fid AND fsubagentid ='{Convert.ToString(data["id"])}'))");
            var mainAgentIds = MainAgentConfigs.Select(s => Convert.ToString(s["fmainagentid"])).ToList();
            try
            {
                //无论如何都会取创建 主子经销商配置。区别在于 如果已存在的则禁用历史的，不存在直接创建
                var bas_mainagentconfig = new DynamicObject(dt);
                bas_mainagentconfig["fmainagentid"] = Convert.ToString(data["id"]);
                bas_mainagentconfig["fqywxmainagentid"] = Convert.ToString(data["id"]);
                var crmagent = GetCrmData(Convert.ToString(data["id"]));
                if (!crmagent.IsNullOrEmptyOrWhiteSpace())
                {
                    crmagent.TryGetValue("number", out string numbers);
                    crmagent.TryGetValue("name", out string names);
                    bas_mainagentconfig["fcrmdistributornumber"] = numbers;
                    bas_mainagentconfig["fcrmdistributorname"] = names;
                }
                var entrys = bas_mainagentconfig["fsubagent"] as DynamicObjectCollection;

                var entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                entry["fsubagentid"] = fnewagentid;
                var _crmagent_sub = GetCrmData(fnewagentid);
                if (!_crmagent_sub.IsNullOrEmptyOrWhiteSpace())
                {
                    _crmagent_sub.TryGetValue("number", out string numbers);
                    _crmagent_sub.TryGetValue("name", out string names);
                    entry["fsubcrmdistributornumber"] = numbers;
                    entry["fsubcrmdistributorname"] = names;
                }
                entrys.Add(entry);

                if (mainAgentIds.Any())
                {
                    foreach (var mainAgentConfig in MainAgentConfigs)
                    {
                        mainAgentConfig["fforbidstatus"] = 1;
                        var mainentrys = mainAgentConfig["fsubagent"] as DynamicObjectCollection;
                        foreach (var mainentry in mainentrys)
                        {
                            if (mainentry["fsubagentid"].ToString().EqualsIgnoreCase(Convert.ToString(data["id"]))) continue;
                            //已经存在的不需要添加
                            if (entrys.Any(o => o["fsubagentid"].ToString().EqualsIgnoreCase(mainentry["fsubagentid"].ToString()))) continue;

                            var entrytmp = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                            entrytmp["fsubagentid"] = mainentry["fsubagentid"].ToString();
                            var crmagent_sub = GetCrmData(mainentry["fsubagentid"].ToString());
                            if (!crmagent_sub.IsNullOrEmptyOrWhiteSpace())
                            {
                                crmagent_sub.TryGetValue("number", out string numbers);
                                crmagent_sub.TryGetValue("name", out string names);
                                entrytmp["fsubcrmdistributornumber"] = numbers;
                                entrytmp["fsubcrmdistributorname"] = names;
                            }
                            //将原先的子经销商也要保留下来。如果之前是 A主B子，如果A更换营业执照为C新的话，那效果应该是C主A、B子
                            entrys.Add(entrytmp);
                        }
                    }
                    var dm = this.Context.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    dm.Save(MainAgentConfigs);
                }

                IPrepareSaveDataService prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(this.Context, htmlForm, new DynamicObject[] { bas_mainagentconfig }, OperateOption.Create());

                var saveRes = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, new[] { bas_mainagentconfig }, "draft", new Dictionary<string, object>());
                this.Result.MergeResult(saveRes);
                var result = JArray.FromObject(saveRes.SrvData);
                //自动提交
                saveRes = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, new[] { bas_mainagentconfig }, "submit", new Dictionary<string, object>());
                this.Result.MergeResult(saveRes);
                //自动审核
                saveRes = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, new[] { bas_mainagentconfig }, "audit", new Dictionary<string, object>());
                this.Result.MergeResult(saveRes);

                var MainAgentConfigsNum = MainAgentConfigs.Select(o => Convert.ToString(o["fnumber"])).ToList().JoinEx(",", false);

                if (result == null || result.Count < 1) return;
                var successId = result.FirstOrDefault().GetJsonValue("id", "");
                var successNumber = result.FirstOrDefault().GetJsonValue("number", "");
                var successName = result.FirstOrDefault().GetJsonValue("name", "");

                this.Logger.WriteLog(this.Context, new LogEntry
                {
                    BillIds = e.DataEntitys[0]["id"] as string,
                    BillNos = e.DataEntitys[0]["fnumber"] as string,
                    BillFormId = HtmlForm.Id,
                    OpName = "更新主经销商配置表",
                    OpCode = this.OperationNo,
                    Content = "执行了【更新主经销商配置表】操作，禁用了主经销商配置：{0}，创建了新主经销商配置：新编码：{1}，新名称：{2}".Fmt(MainAgentConfigsNum, successNumber, successName),
                    DebugData = "执行了【更新主经销商配置表】操作，禁用了主经销商配置：{0}，创建了新主经销商配置：新编码：{1}，新名称：{2}".Fmt(MainAgentConfigsNum, successNumber, successName),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });

            }
            catch (Exception ex)
            {
                this.Result.MergeResult(new OperationResult
                {
                    SimpleMessage = ex.Message,
                    IsSuccess = false
                });
                this.Logger.WriteLog(this.Context, new LogEntry
                {
                    BillIds = e.DataEntitys[0]["id"] as string,
                    BillNos = e.DataEntitys[0]["fnumber"] as string,
                    BillFormId = HtmlForm.Id,
                    OpName = "更换主子经销商",
                    OpCode = this.OperationNo,
                    Content = "执行了【更换主子经销商】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    DebugData = "执行了【更换主子经销商】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });
            }
        }

        private Dictionary<string, string> GetCrmData(string fagentid)
        {
            var bas_agent = this.Context.LoadBizDataById("bas_agent", fagentid);
            if (bas_agent == null)
            {
                return null;
            }
            Dictionary<string, string> crmdistributorObj = null;
            var fcrmdistributorids = Convert.ToString(bas_agent["fcrmdistributorid"]);
            if (fcrmdistributorids.IsNullOrEmptyOrWhiteSpace())
            {
                return null;
            }
            var crmdistributoridLst = fcrmdistributorids.Split(',');
            if (crmdistributoridLst.Length > 0)
            {
                var datas = this.Context.LoadBizDataByFilter("ms_crmdistributor", $"fid in ({crmdistributoridLst.JoinEx(",", true)})");
                var ids = string.Join(",", datas.Select(o => Convert.ToString(o["id"])).ToList());
                var numbers = string.Join(",", datas.Select(o => Convert.ToString(o["fnumber"])).ToList());
                var names = string.Join(",", datas.Select(o => Convert.ToString(o["fname"])).ToList());
                crmdistributorObj = new Dictionary<string, string> { { "id", ids }, { "number", numbers }, { "name", names } };
            }

            if (crmdistributorObj == null)
            {
                return null;
            }
            return crmdistributorObj;
        }
        /// <summary>
        /// 更新商品授权清单
        /// </summary>
        /// <param name="e"></param>
        private void AlterProductAuth(AfterExecuteOperationTransaction e)
        {
            //20241225 更换营业执照只需要更新编码、名称。授予组织不需要调换。因为主数据重新拉取送达方与系列时会自动新生成商品授权清单。
            return;
            //var data = e.DataEntitys[0];
            var agentItem = this.Context.LoadBizDataById("bas_agent", Convert.ToString(e.DataEntitys[0]["id"]));
            var oldagentid = Convert.ToString(agentItem["foldagentid"]);

            var oldItem = this.Context.LoadBizDataByFilter("ydj_productauth", $" forgid ='{Convert.ToString(e.DataEntitys[0]["id"])}' and fforbidstatus = '0' ").FirstOrDefault();
            //var Mid = newItem;
            //var newItem  = this.Context.LoadBizDataByFilter("ydj_productauth", $" forgid ='{oldagentid}'"  ).FirstOrDefault();
            //商品授权清单更换编码、名称
            var ProductAuthObjs = new List<DynamicObject>();
            //if (newItem != null)
            //{
            //    newItem["fnumber"] = oldItem["fnumber"];
            //    newItem["fname"] = oldItem["fname"];
            //    newItem["forgid"] = oldItem["forgid"];
            //    ProductAuthObjs.Add(newItem);
            //}
            if (oldItem != null && agentItem != null)
            {
                this.Logger.WriteLog(this.Context, new LogEntry
                {
                    BillIds = e.DataEntitys[0]["id"] as string,
                    BillNos = e.DataEntitys[0]["fnumber"] as string,
                    BillFormId = HtmlForm.Id,
                    OpName = "更新商品授权清单",
                    OpCode = this.OperationNo,
                    Content = "执行了【更新商品授权清单】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(oldItem["fnumber"], oldItem["fname"], agentItem["fnumber"], agentItem["fname"]),
                    DebugData = "执行了【更新商品授权清单】操作，旧编码：{0}，旧名称：{1}，新编码：{2}，新名称：{3}".Fmt(oldItem["fnumber"], oldItem["fname"], agentItem["fnumber"], agentItem["fname"]),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });

                var deliverObj = this.Context.LoadBizDataByFilter("bas_deliver", $"fagentid='{agentItem["id"]}'");
                var cityId = deliverObj.Select(s => s["fcity"]?.ToString()).Distinct().FirstOrDefault();
                var org = this.Context.LoadBizDataById("bas_organization", Convert.ToString(agentItem["id"]));
                var city = this.Context.LoadBizDataById("ydj_city", cityId);
                var number = agentItem["fnumber"];
                var name = agentItem["fname"];
                //参照送达方生成商品授权清单逻辑，编码、名称 根据【授予组织】的编码+【城市】的编码
                if (!org.IsNullOrEmptyOrWhiteSpace() && !city.IsNullOrEmptyOrWhiteSpace())
                {
                    number = $"{org["fnumber"]}{city["fnumber"]}";
                    name = $"{org["fname"]}-{city["fname"]}";
                }

                oldItem["fnumber"] = number;
                oldItem["fname"] = name;
                oldItem["forgid"] = agentItem["id"];
                //oldItem["forgid"] = agentItem["id"];
                ProductAuthObjs.Add(oldItem);
            }
            try
            {
                if (ProductAuthObjs.Any())
                {
                    var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "ydj_productauth", ProductAuthObjs, "save", option.ToDictionary(s => s.Key, s => s.Value));
                    this.Result.MergeResult(tmpresult);
                }
            }
            catch (Exception ex)
            {
                this.Result.MergeResult(new OperationResult
                {
                    SimpleMessage = ex.Message,
                    IsSuccess = false
                });
                this.Logger.WriteLog(this.Context, new LogEntry
                {
                    BillIds = e.DataEntitys[0]["id"] as string,
                    BillNos = e.DataEntitys[0]["fnumber"] as string,
                    BillFormId = HtmlForm.Id,
                    OpName = "更新商品授权清单",
                    OpCode = this.OperationNo,
                    Content = "执行了【更新商品授权清单】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    DebugData = "执行了【更新商品授权清单】操作，操作失败，错误原因：{0}".Fmt(ex.Message),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });
                //this.Result.SimpleMessage = ex.Message;
                this.Result.SrvData = new Dictionary<string, string> { };
            }
        }

        private void AlterProductAuth_New(AfterExecuteOperationTransaction e)
        {
            var data = e.DataEntitys[0];
            //其实在更换营业执照之前商品授权清单、特殊商品授权已经是ok的了，这里只需要处理更换营业执照后id发生调换时
            //要将新旧商品授权的经销商id做调换，那样的话才能实现更换营业执照 编码、组织跟更换营业执照前的效果一致。
            var newItem = this.Context.LoadBizDataByFilter("ydj_productauth", $"forgid='{fnewagentid}'");
            var oldItem = this.Context.LoadBizDataByFilter("ydj_productauth", $"forgid='{Convert.ToString(data["id"])}'");

            if (oldItem != null && oldItem.Count > 0)
            {
                oldItem.ForEach(_ => _["forgid"] = fnewagentid);
            }
            if (newItem != null && newItem.Count > 0)
            {
                newItem.ForEach(_ => _["forgid"] = data["id"]);
            }
            newItem.AddRange(oldItem);
            DeleteForbidProductAuth(ref newItem, "ydj_productauth");
            //var tmpresult = this.Gateway.InvokeBillOperation(this.Context, "ydj_productauth", newItem, "draft",new Dictionary<string, object>());

            //this.Result.MergeResult(tmpresult);

            var newItem_ot = this.Context.LoadBizDataByFilter("ydj_productauth_other", $"forgid='{fnewagentid}'");
            var oldItem_ot = this.Context.LoadBizDataByFilter("ydj_productauth_other", $"forgid='{Convert.ToString(data["id"])}'");
            if (oldItem_ot != null && oldItem_ot.Count > 0)
            {
                oldItem_ot.ForEach(_ => _["forgid"] = fnewagentid);
            }
            if (newItem_ot != null && newItem_ot.Count > 0)
            {
                newItem_ot.ForEach(_ => _["forgid"] = data["id"]);
            }
            newItem_ot.AddRange(oldItem_ot);
            DeleteForbidProductAuth(ref newItem_ot, "ydj_productauth_other");
            //var tmpresult_ot = this.Gateway.InvokeBillOperation(this.Context, "ydj_productauth_other", newItem_ot, "draft", new Dictionary<string, object>());

            //this.Result.MergeResult(tmpresult_ot);
        }


        private void DeleteForbidProductAuth(ref List<DynamicObject> newItem, string formid)
        {
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, formid);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var DelItem = newItem.Where(o => Convert.ToBoolean(o["fforbidstatus"])).ToList();
            //筛选出未禁用，便于后面去保存。
            newItem = newItem.Except(DelItem).ToList();
            //存在历史禁用的商品授权或者特殊商品授权就清空掉。
            if (DelItem.Any())
            {
                var del_ids = DelItem.Select(o => Convert.ToString(o["id"])).ToList();
                dm.Delete(del_ids);
            }
            if (newItem.Any()) 
            {
                dm.Save(newItem);
            }
        }

        private void UpdateCrmAgent(AfterExecuteOperationTransaction e) 
        {
            var data = e.DataEntitys[0]; 
            var AgentIds = new List<string> { fnewagentid, Convert.ToString(data["id"]) };

            var sql = $@"/*dialect*/
            select crm.fid,crm.fnumber,crm.fname,
            stuff((select ','+convert(varchar, ag.fnumber) from t_bas_agent as ag with(nolock) where ag.fid in (select id from fn_split(crm.fagentid,','))  for xml path('')), 1, 1, '') as agnumbers,
            stuff((select ','+convert(varchar, ag.fname) from  t_bas_agent as ag with(nolock) where ag.fid in (select id from fn_split(crm.fagentid,','))  for xml path('')), 1, 1, '') as agname
             from t_ms_crmdistributor as crm
             where (crm.fagentid like '%{fnewagentid}%' or crm.fagentid like '%{Convert.ToString(data["id"])}%') ";

            var dbService = this.Context.Container.GetService<IDBService>();

            var dynObjs = dbService.ExecuteDynamicObject(this.Context, sql);
            if (!dynObjs.Any()) return;

            var crmDistributors = this.Context.LoadBizDataById("ms_crmdistributor", dynObjs.Select(o=> Convert.ToString(o["fid"])).ToList());
            if (crmDistributors.Any())
            {
                foreach (var crm in crmDistributors) 
                {
                    var fagentid_txt = dynObjs.Where(o => Convert.ToString(o["fid"]).EqualsIgnoreCase(Convert.ToString(crm["id"])))
                       .Select(o => Convert.ToString(o["agname"]))?.FirstOrDefault();

                    if (fagentid_txt.IsNullOrEmptyOrWhiteSpace()) continue;

                    crm["fagentid_txt"] = fagentid_txt;
                }

                this.Context.SaveBizData("ms_crmdistributor", crmDistributors);
            }  
        }

        /// <summary>
        /// 同步档案（异步处理）
        /// </summary>
        /// <param name="e"></param>
        private void TransferToMusiSync(AfterExecuteOperationTransaction e)
        {
            try
            {
                List<string> NeedTransferObjId = new List<string> { "sec_user", "ydj_staff", "ydj_dept", "ydj_position", "sec_role" };
                var param = "{\"extAppId\":\"809719087805632513\",\"batchSize\":100,\"syncBizFormId\":\"ydj_dept\",\"startBillDate\":\"\",\"endBillDate\":\"\",\"filter\":\"fstatus!='D'ANDfstatus!='E' AND fmainorgid='822148199987941388'\"}";
                var seqSvc = this.Container.GetService<ISequenceService>();
                var metaModelService = this.Container.GetService<IMetaModelService>();
                //异步更新
                Task.Run(() =>
                {
                    foreach (var obj in NeedTransferObjId)
                    {
                        var htmlForm = metaModelService.LoadFormModel(this.Context, obj);
                        ScheduleTaskObject taskInfo = new ScheduleTaskObject(seqSvc.GetSequence<string>(),
                        "transfertomusi",
                        "同步 {0} 数据到慕思".Fmt(htmlForm.Caption),
                        "tomusi",
                        obj
                        );
                        var jobScheduler = this.Container.TryGetService<IJobScheduleService>();
                        jobScheduler.ScheduleJob(this.Context, taskInfo, "");
                        var progressAction = this.Context.ShowProgressForm(taskInfo.Identity, null);
                        this.OperationContext.Result.IsSuccess = true;
                        //this.OperationContext.Result.SimpleMessage = "导入任务正在启动，请等待处理……";
                        //this.OperationContext.Result.HtmlActions.Add(progressAction);
                    }
                });

                this.Result.MergeResult(new OperationResult
                {
                    SimpleMessage = "部门、岗位、角色、员工、用户异步同步中，同步结果见操作日志",
                    IsSuccess = true
                });
            }
            catch (Exception ex)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.SimpleMessage = ex.Message + Environment.NewLine + ex.StackTrace;
            }
        }

        private void CreateSuppler(DynamicObject org)
        {
            var orgName = org["fname"].ToString();//销售组织名称   
            var orgNumber = org["fnumber"].ToString();//销售组织名称  
            var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            //refMgr.Load(Context, this.HtmlForm.GetDynamicObjectType(Context), e.DataEntitys, false);

            var bizAgentCtx = this.Context.CreateAgentDBContext(Convert.ToString(org["id"]));
            var supplierExists = this.CheckAgentSupplierExists(bizAgentCtx, orgName);
            if (!supplierExists)
            {
                var htmlForm = this.MetaModelService?.LoadFormModel(bizAgentCtx, SupplierFormId);
                var dt = htmlForm.GetDynamicObjectType(bizAgentCtx);
                var supplier = new DynamicObject(dt);
                refMgr.Load(bizAgentCtx, dt, supplier, false); //加载引用数据 
                // 由上下文确定企业id
                supplier["fmainorgid"] = Convert.ToString(org["id"]);
                supplier["fname"] = orgName;
                //supplier["fnumber"] = orgNumber;
                supplier["forgid"] = org["Id"];
                supplier["ftype"] = "suppliertype_01";
                supplier["fprovince"] = org["fprovince"];
                supplier["fcity"] = org["fcity"];
                supplier["fregion"] = org["fregion"];
                supplier["faddress"] = org["fdetailaddress"];
                supplier["fcontacts"] = org["fcontacter"];
                supplier["fphone"] = org["fcontacterphone"];
                supplier["fcorporatename"] = org["fcorporatename"];
                supplier["fcorporatenumber"] = org["fcorporateidcard"];

                try
                {
                    var saveRes = this.Gateway.InvokeBillOperation(this.Context, SupplierFormId, new[] { supplier }, "draft", new Dictionary<string, object>());
                    this.Result.MergeResult(saveRes);
                }
                catch (Exception ex)
                {
                    this.Result.MergeResult(new OperationResult
                    {
                        SimpleMessage = ex.Message,
                        IsSuccess = false
                    });
                }
            }
        }

        /// <summary>
        /// 检查经销商供应商名称是否存在
        /// </summary>
        /// <param name="agentCtx">经销商上下文</param>
        /// <param name="supplierName">供应商名称</param>
        /// <returns>指定的供应商名称是否存在</returns>
        private bool CheckAgentSupplierExists(UserContext agentCtx, string supplierName)
        {
            var sqlText = $@"
            select top 1 fid from t_ydj_supplier with(nolock) 
            where (fmainorgid='{agentCtx.Company}' or fmainorgid='{agentCtx.TopCompanyId}') and fname=@fname";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fname", System.Data.DbType.String, supplierName)
            };

            using (var reader = this.DBService.ExecuteReader(agentCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 如果原经销商为禁用状态，则更换营业执照后反禁用已禁用的商品授权清单
        /// （商品授权清单的【禁用状态】和新的经销商【状态】均为“启用”状态，且授权清单的内容不变）。
        /// </summary>
        /// <param name="oldagent"></param>
        private void UnforbidProductAuth(List<DynamicObject> agents)
        {
            return;
            var UnforbIds = agents
                //.Where(o => Convert.ToString(o["fagentstatus"]).EqualsIgnoreCase("0"))
                .Select(o=> Convert.ToString(o["id"])).ToList();
            //UnforbIds因为这里 组织id已经调换了 所以这里 不启用经销商获取的id并非需要反禁用的经销商
            this.Container.GetService<IProductAuthService>().Unforbid(this.Context, this.HtmlForm, UnforbIds, option);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var dbServiceEx = this.Container.GetService<IDBServiceEx>();
            base.EndOperationTransaction(e);

        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys.Length == 0)
                return; 

            option.SetVariableValue("IsChangeLicenseNew", true);

            loger = this.Context.Container.GetService<ILogService>();
            //放一个事务里处理

            if (e.DataEntitys.Any())
            {
                using (var scope =
                    this.Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                { 
                    //1、替换经销商信息
                    AlterAgent(e);
                    var data = e.DataEntitys[0];

                    //旧经销商
                    var oldagent = this.Context.LoadBizDataById("bas_agent", Convert.ToString(data["id"]));
                    //新经销商
                    var newagent = this.Context.LoadBizDataById("bas_agent", fnewagentid); 
                    //如果找不到经销商则不用接着往下执行。
                    if (newagent["id"].IsNullOrEmptyOrWhiteSpace() || oldagent["id"].IsNullOrEmptyOrWhiteSpace()) return;

                    //2、更换组织
                    AlterOrg(e);
                    //3、更换企业
                    AlterCompany(e);
                    //4、更换送达方
                    AlterDeliver(e);
                    //4.5 更换门店 
                    //AlterStore_New(e);
                    //5、更新主经销商配置表
                    AlterMainAgentConfig(e);
                    //7.如果原经销商为禁用状态，则更换营业执照后反禁用已禁用的商品授权清单-1030改动：提前到更新商品授权清单之前
                    UnforbidProductAuth(new List<DynamicObject>() { oldagent , newagent });
                    //更新商品授权清单
                    //AlterProductAuth(e);
                    AlterProductAuth_New(e);
                    //6、同步部门、岗位、角色、员工、用户 通过异步的方式
                    TransferToMusiSync(e);
                    //7、更新招商经销商 关联 售达方文本 fagentid_txt。因为是多选字段所以需要重新赋值。
                    UpdateCrmAgent(e);
                    scope.Complete();
                }
            } 

            //清除缓存
            ProductDataIsolateHelper.ClearCacheByBiz(this.Context, new PrdDataIsolateChannelMessage
            {
                Message = $"{this.HtmlForm?.Caption}-{this.OperationNo}",
                TopCompanyId = this.Context.TopCompanyId,
            }, new List<string> { fnewagentid, Convert.ToString(e.DataEntitys[0]["id"]) });

            this.AddRefreshPageAction();
        }
    }
}
