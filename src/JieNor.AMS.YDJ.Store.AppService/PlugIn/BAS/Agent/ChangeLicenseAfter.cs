using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.AMS.YDJ.Store.AppService.Validation.BAS.Agent;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using Newtonsoft.Json;
using JieNor.Framework.MetaCore.Validator;
using ServiceStack;
using System.Threading;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Agent
{
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("ChangeLicenseAfter")]
    public class ChangeLicenseAfter : AbstractOperationServicePlugIn
    {

        protected ILogService loger { get; set; }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys.Length == 0)
                return;

        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            var dbServiceEx = this.Container.GetService<IDBServiceEx>();
            base.EndOperationTransaction(e);
            //更新组织和企业
            //return;
            loger = this.Context.Container.GetService<ILogService>();
            var dm = this.GetDataManager();
            var companyHtml = this.MetaModelService.LoadFormModel(Context, "sys_company");
            dm.InitDbContext(this.Context, companyHtml.GetDynamicObjectType(this.Context));
            //var result = this.Gateway.InvokeBillOperation(this.Context, "bas_agent", e.DataEntitys, "save",
            //    new Dictionary<string, object>());
            try
            {
                foreach (var item in e.DataEntitys)
                {
                    var agentItems = this.Context.LoadBizDataByFilter("bas_agent", "fid='" + Convert.ToString(item["foldagentid"])+"'");
                    var agentItem = this.Context.LoadBizDataById("bas_agent", Convert.ToString(item["foldagentid"]));

                    var companyItem = this.DBService.ExecuteDynamicObject(Context, "select  * from v_auth_company where fid='" + Convert.ToString(item["id"]) + "'").FirstOrDefault();
                    var oldcompanyItem = this.DBService.ExecuteDynamicObject(Context, "select  * from v_auth_company where fid='" + Convert.ToString(item["foldagentid"]) + "'").FirstOrDefault();
                    //var companyItem = this.Context.LoadBizDataById("sys_company", Convert.ToString(item["id"]));
                    if (companyItem == null)
                    {
                        companyItem = this.Context.LoadBizDataByNo("sys_company", "fnumber", new string[] { Convert.ToString(item["fnumber"]) }.ToList()).FirstOrDefault();
                    }
                    //var oldcompanyItem = this.Context.LoadBizDataById("sys_company", Convert.ToString(item["foldagentid"]));
                    if (oldcompanyItem == null)
                    {
                        oldcompanyItem = this.Context.LoadBizDataByNo("sys_company", "fnumber", new string[] { Convert.ToString(agentItem["fnumber"]) }.ToList()).FirstOrDefault();
                    }

                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【ChangeLicenseAfter-变更营业执照】，内容:{4}".Fmt(this.Context.UserName,
                            this.Context.UserPhone, this.Context.Company,
                         DateTime.Now.ToString("HH:mm:ss"), string.Format("05-3:企业数据状态companyItem：{0},oldcompanyItem：{1}！", companyItem == null ? true : false, oldcompanyItem == null ? true : false)),
                        "ChangeLicenseAfter");

                    //if (false)
                    if (oldcompanyItem != null)
                    {

                        //string updSql = " Update v_auth_company set fnumber=@fnumber,fname=@fname where fid=@fid";
                        //string updSql1 = " Update v_eis_company set fnumber=@fnumber,fname=@fname where fid=@fid";
                        //string updSql2 = " Update t_sys_company set fnumber=@fnumber,fname=@fname where fid=@fid";

                        //List<SqlParam> sqlParams = new List<SqlParam>();
                        //sqlParams.Add(new SqlParam("@fnumber", System.Data.DbType.String, Convert.ToString(agentItem["fnumber"])));
                        //sqlParams.Add(new SqlParam("@fname", System.Data.DbType.String, Convert.ToString(agentItem["fname"])));
                        //sqlParams.Add(new SqlParam("@fid", System.Data.DbType.String, Convert.ToString(oldcompanyItem["fid"])));
                        //dbServiceEx.Execute(this.Context, updSql, sqlParams);
                        //dbServiceEx.Execute(this.Context, updSql1, sqlParams);
                        //dbServiceEx.Execute(this.Context, updSql2, sqlParams);
                        //发送给云链，并只在云链校验名称的唯一性
                        //oldcompanyItem["fnumber"] = agentItem["fnumber"];
                        //oldcompanyItem["fname"] = agentItem["fname"];

                        //var companySaveResult = this.Gateway.InvokeBillOperation(this.Context, "sys_company", new DynamicObject[] { oldcompanyItem }, "save",
                        //      new Dictionary<string, object>());
                        if (false)
                        {
                            //dm.Save(oldcompanyItem);
                            var comps = DataCenterExtentions.GetAllCompanys(this);
                            var compItem = new Framework.DataTransferObject.DB.CompanyDCInfo();
                            comps.TryGetValue(Convert.ToString(item["foldagentid"]), out compItem);
                            if (compItem != null)
                            {
                                //DataCenterExtentions.RemoveCompanyDCInfo(compItem);
                                //compItem.CompanyNumber = Convert.ToString(agentItem["fnumber"]);
                                //compItem.CompanyName = Convert.ToString(agentItem["fname"]);
                                //DataCenterExtentions.AddCompanyDCInfo(compItem);
                                //DataCenterExtentions.SaveDataCenterConfig();
                                //DataCenterExtentions.RefreshDataCenter(Convert.ToString(agentItem["id"]), Convert.ToString(agentItem["fname"]));
                                string companyNameStr = Convert.ToString(agentItem["fname"]);

                               

                                {
                                    var eisresult = this.Gateway.Invoke(this.Context,
                                TargetSEP.EisService,
                                new CommonFormDTO()
                                {
                                    FormId = "eis_company",
                                    OperationNo = "UpdateCompanyName",
                                    SelectedRows = new DynamicObject[] { oldcompanyItem }.Select(x => new SelectedRow { PkValue = Convert.ToString(x["fid"]) }),
                                    SimpleData = new Dictionary<string, string>
                                    {
                            { "companyName",companyNameStr}
                                    }
                                }) as DynamicDTOResponse;

                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【eis改名】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "eisresult：" + JsonConvert.SerializeObject(eisresult)), "ChangeLicenseAfter");
                                    eisresult?.OperationResult?.ThrowIfHasError(true, "云链更名失败!");

                                    //发送给认证站点
                                    eisresult = this.Gateway.Invoke(this.Context,
                                    TargetSEP.AuthService,
                                    new CommonFormDTO()
                                    {
                                        FormId = "auth_company",
                                        OperationNo = "UpdateCompanyName",
                                        SelectedRows = new DynamicObject[] { oldcompanyItem }.Select(x => new SelectedRow { PkValue = Convert.ToString(x["fid"]) }),
                                        SimpleData = new Dictionary<string, string>
                                        {
                            { "companyName",companyNameStr}
                                        }
                                    }) as DynamicDTOResponse;

                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【auth改名】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "eisresult：" + JsonConvert.SerializeObject(eisresult)), "ChangeLicenseAfter");
                                    eisresult?.OperationResult?.ThrowIfHasError(true, "认证站点更名失败!");
                                }
                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【ChangeLicenseAfter-变更营业执照】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"),
                                     string.Format("05-3-old-step555:变更数据包;companyNameStr：{0},SelectedRows[id]：{1}！", companyNameStr, JsonConvert.SerializeObject(new DynamicObject[] { oldcompanyItem }.Select(x => new SelectedRow { PkValue = Convert.ToString(x["id"]) })))),
                                    "ChangeLicenseAfter");
                                //var opts = new Dictionary<string, object>() { };
                                //opts.Add("companyName", Convert.ToString(item["fname"]));
                                //var companySaveResult1 = this.Gateway.InvokeBillOperation(this.Context, "sys_company", new DynamicObject[] { oldcompanyItem }, "UpdateCompanyName", opts);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【更换营业执照】，内容:{4}".Fmt(this.Context.UserName,
                    this.Context.UserPhone, this.Context.Company,
                 DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace), "ChangeLicenseAfter");
                throw ex;
            }
        }
    }
}
