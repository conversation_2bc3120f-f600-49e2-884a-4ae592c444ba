using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.AMS.YDJ.Store.AppService.Validation.BAS.Agent;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Agent
{
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            // 保存校验器
            e.Rules.Add(new SaveValidation());
        }

        /// <summary>
        /// 初始化服务插件上下文时触发的事件
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="serviceList"></param>
        public new void InitializeOperationContext(OperationContext operCtx, params object[] serviceList)
        {
            // 启用幂等性检查
            var serCtrlOpt = serviceList.FirstOrDefault(o => o is ServiceControlOption) as ServiceControlOption;
            serCtrlOpt.SupportIdemotency = true;

            base.InitializeOperationContext(operCtx, serviceList);
        }

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                if (!dataEntity.DataEntityState.FromDatabase)
                {
                    dataEntity["fisnotprerole"] = true;
                    dataEntity["fismember"] = true;
                    dataEntity["freminddate"] = DateTime.Now.ToShortDateString();
                }
            }
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e); 
            //当 接口下发经销商 状态：禁用时，禁用对应的商品授权清单 
            var orgIds = e.DataEntitys.Where(o=> Convert.ToString(o["fagentstatus"]).EqualsIgnoreCase("0")).Select(s => Convert.ToString(s["id"]));

            var orgIds_UnForbid = e.DataEntitys.Where(o => Convert.ToString(o["fagentstatus"]).EqualsIgnoreCase("1")).Select(s => Convert.ToString(s["id"]));

            if (orgIds.Any())
            {
                this.Container.GetService<IProductAuthService>().Forbid(this.Context, this.HtmlForm, orgIds, this.Option);
            }
            if (orgIds_UnForbid.Any()) 
            {
                this.Container.GetService<IProductAuthService>().Unforbid(this.Context, this.HtmlForm, orgIds_UnForbid, this.Option);
            } 
            //// 更新《主经销商配置表》的【企业微信主体经销商】
            //var agentService = this.Container.GetService<IAgentService>();
            //agentService.UpdateQYWXMainAgent(this.Context, e.DataEntitys);
        }


        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            // 清除指定经销商的是否启用经销价标记缓存
            foreach (var dataEntity in e.DataEntitys)
            {
                var agentId = Convert.ToString(dataEntity["id"]);
                var agentCtx = this.Context.CreateAgentDBContext(agentId);
                agentCtx.ClearEnableSellPriceCache();
            }

            var orgIds = e.DataEntitys.Select(s => Convert.ToString(s["id"]))
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                .ToList();
            if (orgIds.Any())
            {
                //清除缓存
                ProductDataIsolateHelper.ClearCacheByBiz(this.Context, new PrdDataIsolateChannelMessage
                {
                    Message = $"{this.HtmlForm?.Caption}-{this.OperationNo}",
                    TopCompanyId = this.Context.TopCompanyId,
                }, orgIds);
            }
        }



    }
}
