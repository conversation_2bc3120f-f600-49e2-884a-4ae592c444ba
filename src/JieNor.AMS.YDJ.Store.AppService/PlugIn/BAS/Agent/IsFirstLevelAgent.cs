using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Agent
{
    /// <summary>
    /// 判断当前组织是否是一级经销商
    /// </summary>
    [InjectService]
    [FormId("bas_agent")]
    [OperationNo("isFirstLevelAgent")]
    public class IsFirstLevelAgent : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string sql = $"select forgtype from t_bas_organization where fid={this.Context.Company}";
            DynamicObject data= Context.LoadBizBillHeadDataById("bas_organization", Context.Company, "forgtype");
           
            if (!Context.IsSecondOrg && !Context.IsTopOrg && data?["forgtype"].ToString() == "4") {
                this.Result.SrvData = true;
            }
            else
            {
                this.Result.SrvData = false;
            }
        }
    }
}
