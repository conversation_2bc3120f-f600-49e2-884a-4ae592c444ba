using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Building
{
    /// <summary>
    /// 取得渠道的客户信息
    /// </summary>
    [InjectService]
    [FormId("ydj_building")]
    [OperationNo("initbill")]
    public class BuildingServiceInfo : AbstractOperationServicePlugIn
    {

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            var billData = e.EventData as JObject;

            string fbuildingid = billData["id"].ToString();

            IBaseFormProvider baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company));
            sqlParams.Add(new SqlParam("@fdutyid", System.Data.DbType.String, baseFormProvider.GetMyStaff(this.Context)?.Id));
            sqlParams.Add(new SqlParam("@fbuildingid", System.Data.DbType.String, fbuildingid));

            int fhouseholds = 0, recordCount = 0, tradedCount = 0, myRecordCount = 0, caseCount = 0;
            string sql = @"SELECT
	( SELECT fhouseholds FROM t_ydj_building WHERE fid =@fbuildingid ) AS fhouseholds,
	( SELECT ISNULL( COUNT ( FID ), 0 ) FROM t_ydj_customerrecord WHERE fmainorgid =@fmainorgid AND fbuildingid =@fbuildingid ) recordCount,
	( select ISNULL( COUNT ( FID ), 0 ) from t_ydj_customer WHERE fmainorgid =@fmainorgid AND fbuildingid =@fbuildingid) tradedCount,
	( SELECT ISNULL( COUNT ( FID ), 0 ) FROM t_ydj_customerrecord WHERE fmainorgid =@fmainorgid AND fbuildingid =@fbuildingid and fdutyid=@fdutyid  ) myRecordCount,
    (select isnull(count(fid),0) from t_ydj_case where fmainorgid=@fmainorgid and fbuildingid=@fbuildingid and fsenstatus='send_status02') caseCount
";
            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
            {
                if (dataReader.Read())
                {
                    fhouseholds = dataReader.GetValue<int>("fhouseholds");
                    recordCount = dataReader.GetValue<int>("recordCount");
                    tradedCount = dataReader.GetValue<int>("tradedCount");
                    myRecordCount = dataReader.GetValue<int>("myRecordCount");
                    caseCount= dataReader.GetValue<int>("caseCount");
                }
            }

            var resultData = new
            {
                fhouseholds,
                recordCount,
                tradedCount,
                myRecordCount,
                caseCount
            };

            switch (e.EventName)
            {
                case "afterCreateUIData":
                    e.Result = resultData;
                    break;
            }
        }

      
    }
}
