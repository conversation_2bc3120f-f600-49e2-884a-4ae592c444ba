using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Senparc.Weixin.Helpers.Extensions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Enum
{
    /// <summary>
    /// 辅助资料协同接收
    /// </summary>
    [InjectService]
    [FormId("bd_enum")]
    [OperationNo("SyncEnum")]
    public class SyncEnum : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var enumdata = this.GetQueryOrSimpleParam<string>("enumdata");
            var enumdataList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(enumdata);
            if (enumdata.IsNullOrWhiteSpace())
            {
                this.Result.SimpleMessage = "协同失败：没有传入有效数据！";
                this.Result.IsSuccess = false;
                return;
            }
            //添加辅助资料
            AddEnum(enumdataList);
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 添加辅助资料
        /// </summary>
        /// <param name="enumdata"></param>
        private void AddEnum(List<Dictionary<string, string>> enumdataList)
        {
            var fname = enumdataList.Select(p => Convert.ToString(p["fname"])).FirstOrDefault();
            var fid = enumdataList.Select(p => Convert.ToString(p["fid"])).FirstOrDefault();

            var sql = $@"select top 1 fid from t_bd_enumdata where fname = '{fname}' ";
            var dbService = this.Context.Container.GetService<IDBService>();
            var sqlData = dbService.ExecuteDynamicObject(this.Context, sql);

            //先根据名字判断是否存在这个模块的辅助资料，已存在的，删除历史数据，再次添加，
            if (sqlData != null && sqlData.Count() > 0)
            {
                AddOld(enumdataList, sqlData);
            }
            else
            {
                //不存在的，新增
                AddNew(enumdataList);
            }
        }

        /// <summary>
        /// 已存在的,删除单据体数据,添加在已存在类别下
        /// </summary>
        /// <param name="enumdataList"></param>
        /// <param name="enumFid"></param>
        private void AddOld(List<Dictionary<string, string>> enumdataList, DynamicObjectCollection sqlData)
        {
            List<string> lstSqlItems = new List<string>();
            StringBuilder sbSql = new StringBuilder();
            var fid = enumdataList.Select(p => Convert.ToString(p["fid"])).FirstOrDefault();
            var fname = enumdataList.Select(p => Convert.ToString(p["fname"])).FirstOrDefault();
            var fnumber = enumdataList.Select(p => Convert.ToString(p["fnumber"])).FirstOrDefault();
            var fentryidList = enumdataList.Select(p => Convert.ToString(p["fentryid"])).ToList();
            var enumFid = Convert.ToString(sqlData[0]["fid"]);
            //删除单据体数据
            sbSql.Append(@"delete from t_bd_enumdataentry where fentryid in('{0}');".Fmt(string.Join("','", fentryidList)));

            //新增单据体数据
            foreach (var item in enumdataList)
            {
                var fentryid = Convert.ToString(item["fentryid"]);
                var fenumitem = Convert.ToString(item["fenumitem"]);
                var forder = Convert.ToString(item["forder"]);
                var fdisabled = Convert.ToString(item["fdisabled"]);
                sbSql.Append($@"insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('{fentryid}','{enumFid}','{forder}','{fdisabled}','0','','{fenumitem}','0');");
            }
            lstSqlItems.Add(sbSql.ToString());
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.ExecuteBatch(this.Context, lstSqlItems);

            var comboSvc = this.Container.GetService<IComboDataService>();
            comboSvc.ClearCache(this.Context, fname);
        }

        /// <summary>
        /// 按照协同资料新增
        /// </summary>
        /// <param name="enumdataList"></param>
        private void AddNew(List<Dictionary<string, string>> enumdataList)
        {
            List<string> lstSqlItems = new List<string>();
            StringBuilder sbSql = new StringBuilder();
            var fid = enumdataList.Select(p => Convert.ToString(p["fid"])).FirstOrDefault();
            var fname = enumdataList.Select(p => Convert.ToString(p["fname"])).FirstOrDefault();
            var fnumber = enumdataList.Select(p => Convert.ToString(p["fnumber"])).FirstOrDefault();
            var fentryidList = enumdataList.Select(p => Convert.ToString(p["fentryid"])).ToList();

            ////删除单据头数据
            ////var sql = $@"delete from t_bd_enumdata where fid ='{fid}';";
            //sbSql.Append($@"delete from t_bd_enumdata where fid ='{fid}';");

            ////删除单据体数据
            //// var sql1 = @"delete from t_bd_enumdataentry where fentryid in('{0}')".Fmt(string.Join("','", fentryidList));
            //sbSql.Append(@"delete from t_bd_enumdataentry where fentryid in('{0}');".Fmt(string.Join("','", fentryidList)));

            //新增数据
            sbSql.Append($@"insert into t_bd_enumdata(fid, fformid, fname, fnumber, fispreset, fstatus, fforbidstatus, fmodule, fmoduleorder, fvisible, fmainorgid) values('{fid}', 'bd_enumdata', '{fname}','{fnumber}', '1', 'B', '0', '协同资料', 1, '1', '0');");
            foreach (var item in enumdataList)
            {
                var fentryid = Convert.ToString(item["fentryid"]);
                var fenumitem = Convert.ToString(item["fenumitem"]);
                var forder = Convert.ToString(item["forder"]);
                var fdisabled = Convert.ToString(item["fdisabled"]);
                sbSql.Append($@"insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('{fentryid}','{fid}','{forder}','{fdisabled}','0','','{fenumitem}','0');");
            }
            lstSqlItems.Add(sbSql.ToString());
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.ExecuteBatch(this.Context, lstSqlItems);

            var comboSvc = this.Container.GetService<IComboDataService>();
            comboSvc.ClearCache(this.Context, fname);
        }
    }
}
