using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Integration;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Auxproperty
{
    ///// <summary>
    ///// 辅助属性组合值下载代码
    ///// </summary>
    //[InjectService]
    //[FormId("bd_auxpropvalueset")]
    //[OperationNo("SyncDownloadConfirm")]
    //public class SyncDownloadConfirm: AbstractOperationServicePlugIn
    //{
    //    public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
    //    {
    //        base.OnCustomServiceEvent(e);
    //        switch (e.EventName.ToLower())
    //        {
    //            case "undistribute_forbidauxset":
    //                unDistributeForbidAuxSet(e);
    //                break;
    //            default:
    //                return;
    //        }
    //    }

    //    private void unDistributeForbidAuxSet(OnCustomServiceEventArgs e)
    //    {
    //        var eventData = e.EventData as Dictionary<string, object>;
    //        var userContext = eventData["userContext"] as UserContext;
    //        var chainDataIdToLocalDataIdMaps = eventData["chainDataIdToLocalDataIdMaps"] as List<Dictionary<string, object>>;
    //        var htmlForm = eventData["htmlForm"] as HtmlForm;
    //        var allCompanys = eventData["allCompanys"] as Dictionary<string, CompanyDCInfo>;

    //        if (userContext == null || chainDataIdToLocalDataIdMaps == null || chainDataIdToLocalDataIdMaps.Count <= 0 || htmlForm == null)
    //        {
    //            return;
    //        }

    //        var billDatas = eventData["billDatas"] as List<Dictionary<string, object>>;
    //        var chainInfos = billDatas?.Select(x => new KeyValuePair<string, string>(Convert.ToString(x["chainDataId"]), Convert.ToString(x["companyId"])))
    //                                   .Distinct(x => string.Join("|", x.Key, x.Value))
    //                                   .ToList();

    //        if (chainInfos == null || chainInfos.Count <= 0)
    //        {
    //            return;
    //        }

    //        var dataEntities = chainDataIdToLocalDataIdMaps.Where(x => Convert.ToString(x["formId"]) == htmlForm.Id &&
    //                                                                   chainInfos.Any(y => Convert.ToString(x["chainDataId"]) == y.Key &&
    //                                                                                       Convert.ToString(x["companyId"]) == y.Value))
    //                                                       .Select(x => x["dataEntity"] as DynamicObject)
    //                                                       .ToList();

    //        if (dataEntities == null || dataEntities.Count <= 0)
    //        {
    //            return;
    //        }

    //        var dm = userContext.Container.GetService<IDataManager>();
    //        dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));
    //        var callBackChainDataInfos = new List<Dictionary<string, string>>();

    //        foreach (var dataEntity in dataEntities)
    //        {
    //            var companyId = Convert.ToString(dataEntity["fmainorgid"]);
    //            dataEntity["fforbidstatus"] = true;
    //            dataEntity["fforbidid"] = userContext.Company == companyId ? userContext.UserId : "sysadmin";
    //            dataEntity["fforbiddate"] = DateTime.Now;
    //            var chainDataId = Convert.ToString(dataEntity["ffromchaindataid"]);
    //            var companyDCInfo = allCompanys[companyId];
    //            var callBackData = new Dictionary<string, string>
    //                {
    //                    { "localDataId" , Convert.ToString(dataEntity["id"]) },
    //                    { "localDataNumber", Convert.ToString(dataEntity[htmlForm.NumberFldKey]) },
    //                    { "localDataName", Convert.ToString(dataEntity[htmlForm.NameFldKey]) },
    //                    { "chainDataId", Convert.ToString(dataEntity["ffromchaindataid"]) },
    //                    { "companyId", companyId },
    //                    { "companyName", companyDCInfo.CompanyName },
    //                    { "productId", userContext.Product }
    //                };
    //            callBackChainDataInfos.Add(callBackData);
    //        }

    //        dm.Save(dataEntities);
    //        e.Result = callBackChainDataInfos;
    //    }
    //}


    /// <summary>
    /// 辅助属性组合值下载代码
    /// </summary>
    [InjectService]
    [FormId("bd_auxpropvalueset")]
    [OperationNo("SyncDownloadConfirm")]
    public class SyncDownloadConfirm : AbstractSyncDownloadConfirmPlugIn
    {
        protected override List<Dictionary<string, string>> UnDistributeForbidAuxSet(UserContext userContext,
                                                                                     HtmlForm htmlForm,
                                                                                     List<Dictionary<string, object>> billDatas,
                                                                                     List<ChainDataIdToLocalDataIdMapsSet> chainDataIdToLocalDataIdMaps)
        {
            var callBackChainDataInfos = new List<Dictionary<string, string>>();
            var companyIds = billDatas?.Select(x => Convert.ToString(x["companyId"])).Distinct().ToList();
            var chainDataIds = billDatas?.Select(x => Convert.ToString(x["chainDataId"])).Distinct().ToList();
            if (chainDataIds == null || chainDataIds.Count <= 0)
            {
                return callBackChainDataInfos;
            }

            var dataEntities = new List<DynamicObject>();
            var formChainDatas = chainDataIdToLocalDataIdMaps.Where(x => x.formId == htmlForm.Id && companyIds.Contains(x.companyId)).ToList(); 
            foreach (var item in formChainDatas)
            {
                var data = item.ChainDataMaps.Where(f =>f.dataEntity !=null && chainDataIds.Contains ( f.chainDataId ));
                if(data !=null )
                {
                    dataEntities.AddRange (data.Select (f=>f.dataEntity as DynamicObject));
                }
            }
             
            if (dataEntities == null || dataEntities.Count <= 0)
            {
                return callBackChainDataInfos;
            }

            var dm = userContext.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, htmlForm.GetDynamicObjectType(userContext));

            foreach (var dataEntity in dataEntities)
            {
                var companyId = Convert.ToString(dataEntity["fmainorgid"]);
                var companyName = Convert.ToString(dataEntity["fmainorgid_txt"]);
                dataEntity["fforbidstatus"] = true;
                dataEntity["fforbidid"] = userContext.Company == companyId ? userContext.UserId : "sysadmin";
                dataEntity["fforbiddate"] = DateTime.Now;
                var chainDataId = Convert.ToString(dataEntity["ffromchaindataid"]);
                var callBackData = new Dictionary<string, string>
                    {
                        { "localDataId" , Convert.ToString(dataEntity["id"]) },
                        { "localDataNumber", Convert.ToString(dataEntity[htmlForm.NumberFldKey]) },
                        { "localDataName", Convert.ToString(dataEntity[htmlForm.NameFldKey]) },
                        { "chainDataId", Convert.ToString(dataEntity["ffromchaindataid"]) },
                        { "companyId", companyId },
                        { "companyName", companyName },
                        { "productId", userContext.Product }
                    };
                callBackChainDataInfos.Add(callBackData);
            }

            dm.Save(dataEntities);
            return callBackChainDataInfos;
        }
    }
}
