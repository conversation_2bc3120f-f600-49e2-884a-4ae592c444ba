using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auxproperty
{
    /// <summary>
    /// 查看物料辅助属性接口
    /// </summary>
    [InjectService]
    [FormId("bd_auxpropvaluemap")]
    [OperationNo("getauxpropvalues")]
    public class GetAuxPropValues: AbstractOperationServicePlugIn
    {
        //public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        //{
        //    string materialId = this.GetQueryOrSimpleParam<string>("materialId");
        //    if (string.IsNullOrEmpty(materialId))
        //    {
        //        throw new BusinessException("materialId参数不能为空!");
        //    }

        //    var datas = new
        //    {
        //        materialId = materialId,
        //        auxPropInfo = new List<Dictionary<string, object>>()
        //    };

        //    var dm = this.Container.GetService<IDataManager>();
        //    dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
        //    string where = "fmaterialid=@fmaterialid and fmainorgid=@fmainorgid";
        //    using (var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, where,
        //                       new SqlParam[] {
        //                       new SqlParam("fmaterialid", System.Data.DbType.String, materialId),
        //                       new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
        //                       }))
        //    {
        //        var dataEntities = dm.SelectBy(pkIdReader).OfType<DynamicObject>().ToList();
        //        if (dataEntities != null && dataEntities.Count > 0)
        //        {
        //            foreach(var dataEntity in dataEntities)
        //            {
        //                var auxPropInfo = datas.auxPropInfo.FirstOrDefault(x => Convert.ToString(x["auxPropId"]) == Convert.ToString(dataEntity["fauxpropid"]));
        //                if (auxPropInfo == null)
        //                {
        //                    auxPropInfo = new Dictionary<string, object>
        //                    {
        //                        { "auxPropId",Convert.ToString(dataEntity["fauxpropid"]) },
        //                        { "auxPropName",null},
        //                        { "auxPropNumber",null},
        //                        { "order",null},
        //                        { "valueSource",null},
        //                        { "refBaseFormId",null},
        //                        { "refEnumFormId",null},
        //                        { "valueList",new List<Dictionary<string,object>>()}
        //                    };
        //                    datas.auxPropInfo.Add(auxPropInfo);
        //                }

        //                var FEntity = dataEntity["FEntity"] as DynamicObjectCollection;
        //                if (FEntity != null && FEntity.Count > 0)
        //                {
        //                    var valueList = auxPropInfo["valueList"] as List<Dictionary<string, object>>;
        //                    foreach (var fentry in FEntity)
        //                    {
        //                        var isDisable = Convert.ToBoolean(fentry["fisdisable"]);
        //                        if (!isDisable)
        //                        {
        //                            valueList.Add(new Dictionary<string, object>
        //                            {
        //                                { "valueId",fentry["fvalueid"]},
        //                                { "valueName",fentry["fvaluename"]},
        //                                { "valueNumber",fentry["fvaluenumber"]},
        //                                { "isDisable",fentry["fisdisable"]},
        //                                { "isDefVal",fentry["fisdefval"]}
        //                            });
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }

        //    var auxPropIds = datas.auxPropInfo.Select(x => Convert.ToString(x["auxPropId"])).Distinct().ToList();
        //    if (auxPropIds != null && auxPropIds.Count > 0)
        //    {
        //        var metaModelService = this.Container.GetService<IMetaModelService>();
        //        var htmlForm = metaModelService.LoadFormModel(this.Context, "bd_auxproperty");
        //        dm = this.Container.GetService<IDataManager>();
        //        dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
        //        var dataEntities = dm.Select(auxPropIds).OfType<DynamicObject>().ToList();
        //        if (dataEntities == null || dataEntities.Count <= 0)
        //        {
        //            throw new BusinessException("辅助属性数据异常!");
        //        }
        //        foreach(var dataEntity in dataEntities)
        //        {
        //            var auxPropInfo = datas.auxPropInfo.FirstOrDefault(x => Convert.ToString(x["auxPropId"]) == Convert.ToString(dataEntity["Id"]));
        //            auxPropInfo["auxPropName"] = dataEntity["fname"];
        //            auxPropInfo["auxPropNumber"] = dataEntity["fnumber"];
        //            auxPropInfo["order"] = dataEntity["forder"];
        //            auxPropInfo["valueSource"] = dataEntity["fvaluesource"];
        //            auxPropInfo["refBaseFormId"] = dataEntity["frefbaseformid"];
        //            auxPropInfo["refEnumFormId"] = dataEntity["frefenumformid"];
        //        }
        //    }

        //    this.Result.IsSuccess = true;
        //    this.Result.SimpleMessage = "查询所有属性成功!";
        //    this.Result.SrvData = datas;
        //}

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            string materialIdStr = this.GetQueryOrSimpleParam<string>("materialId");
            if (string.IsNullOrEmpty(materialIdStr))
            {
                throw new BusinessException("materialId参数不能为空!");
            }

            var materialIds = materialIdStr.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Distinct().ToList();

            if (materialIds == null || materialIds.Count <= 0)
            {
                throw new BusinessException("materialId参数不能为空!");
            }

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            StringBuilder where = new StringBuilder("fmainorgid=@fmainorgid and fmaterialid ");
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company)
            };

            if (materialIds.Count == 1)
            {
                where.Append(" = @fmaterialid");
                sqlParams.Add(new SqlParam("@fmaterialid", System.Data.DbType.String, materialIds[0]));
            }
            else
            {
                where.Append(" in (");
                where.Append(string.Join(",", materialIds.Select((x, i) => string.Format("@fmaterialid{0}", i))));
                where.Append(")");
                sqlParams.AddRange(materialIds.Select((x, i) => new SqlParam(string.Format("@fmaterialid{0}", i), System.Data.DbType.String, x)));
            }

            var datas = new List<Dictionary<string, object>>();

            var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, where.ToString(), sqlParams);
            var dataEntities = dm.SelectBy(pkIdReader).OfType<DynamicObject>().ToList();
            if (dataEntities != null && dataEntities.Count > 0)
            {
                foreach (var dataEntity in dataEntities)
                {
                    var data = datas.FirstOrDefault(x => Convert.ToString(x["materialId"]) == Convert.ToString(dataEntity["fmaterialid"]));
                    if (data == null)
                    {
                        data = new Dictionary<string, object>
                        {
                            { "materialId",Convert.ToString(dataEntity["fmaterialid"])},
                            { "success",true},
                            { "auxPropInfo",new List<Dictionary<string,object>>()}
                        };
                        datas.Add(data);
                    }

                    var auxPropInfos = data["auxPropInfo"] as List<Dictionary<string, object>>;

                    var auxPropInfo = auxPropInfos.FirstOrDefault(x => Convert.ToString(x["auxPropId"]) == Convert.ToString(dataEntity["fauxpropid"]));
                    if (auxPropInfo == null)
                    {
                        auxPropInfo = new Dictionary<string, object>
                            {
                                { "auxPropId",Convert.ToString(dataEntity["fauxpropid"]) },
                                { "auxPropName",null},
                                { "auxPropNumber",null},
                                { "order",null},
                                { "valueSource",null},
                                { "refBaseFormId",null},
                                { "refEnumFormId",null},
                                { "valueList",new List<Dictionary<string,object>>()}
                            };
                        auxPropInfos.Add(auxPropInfo);
                    }

                    var FEntity = dataEntity["FEntity"] as DynamicObjectCollection;
                    if (FEntity != null && FEntity.Count > 0)
                    {
                        var valueList = auxPropInfo["valueList"] as List<Dictionary<string, object>>;
                        foreach (var fentry in FEntity)
                        {
                            var isDisable = Convert.ToBoolean(fentry["fisdisable"]);
                            if (!isDisable)
                            {
                                valueList.Add(new Dictionary<string, object>
                                    {
                                        { "valueId",fentry["fvalueid"]},
                                        { "valueName",fentry["fvaluename"]},
                                        { "valueNumber",fentry["fvaluenumber"]},
                                        { "isDisable",fentry["fisdisable"]},
                                        { "isDefVal",fentry["fisdefval"]}
                                    });
                            }
                        }
                    }
                }
            }

            var auxPropItems = datas.SelectMany(x => x["auxPropInfo"] as List<Dictionary<string, object>>).ToList();

            var auxPropIds = auxPropItems.Select(y => Convert.ToString(y["auxPropId"])).Distinct().ToList();

            if (auxPropIds != null && auxPropIds.Count > 0)
            {
                var metaModelService = this.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(this.Context, "bd_auxproperty");
                dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                var auxEntities = dm.Select(auxPropIds).OfType<DynamicObject>().ToList();
                if (auxEntities == null || auxEntities.Count <= 0)
                {
                    throw new BusinessException("辅助属性数据异常!");
                }

                //是否存在【材质】主辅属性约束
                var existsMainAuxPropInfo = false;
                var mainPropObj = auxEntities?.FirstOrDefault(o => 
                    Convert.ToBoolean(o["fmainprop"]) && Convert.ToString(o["fname"]).EqualsIgnoreCase("材质"));
                var auxPropId = Convert.ToString(mainPropObj?["Id"]);
                if (!auxPropId.IsNullOrEmptyOrWhiteSpace())
                {
                    //【材质】属性类别默认值
                    var auxPropInfo = auxPropItems?.FirstOrDefault(o => Convert.ToString(o["auxPropId"]).EqualsIgnoreCase(auxPropId));
                    var valueList = auxPropInfo?["valueList"] as List<Dictionary<string, object>>;
                    var auxPropValueId = Convert.ToString(valueList?.FirstOrDefault(o => Convert.ToBoolean(o["isDefVal"]))?["valueId"]);
                    if (!auxPropValueId.IsNullOrEmptyOrWhiteSpace())
                    {
                        //加载【材质】主辅属性约束
                        var mainAuxPropInfo = this.LoadMainAuxPropInfo(auxPropId, auxPropValueId)?.FirstOrDefault();
                        var entrys = mainAuxPropInfo?["fentity"] as DynamicObjectCollection;
                        var _entrys = entrys?.Where(o => !Convert.ToBoolean(o["fisdisable"])); //是否存在有效的辅属性约束
                        if (_entrys != null && _entrys.Any())
                        {
                            existsMainAuxPropInfo = true;
                        }
                    }
                }

                foreach (var dataEntity in auxEntities)
                {
                    var auxPropInfos = auxPropItems.Where(x => Convert.ToString(x["auxPropId"]) == Convert.ToString(dataEntity["Id"]));
                    foreach(var auxPropInfo in auxPropInfos)
                    {
                        auxPropInfo["auxPropName"] = dataEntity["fname"];
                        auxPropInfo["auxPropNumber"] = dataEntity["fnumber"];
                        auxPropInfo["order"] = dataEntity["forder"];
                        auxPropInfo["valueSource"] = dataEntity["fvaluesource"];
                        auxPropInfo["refBaseFormId"] = dataEntity["frefbaseformid"];
                        auxPropInfo["refEnumFormId"] = dataEntity["frefenumformid"];

                        if (existsMainAuxPropInfo)
                        {
                            //如果是【颜色】辅助属性，则需要检查是否存在主辅属性约束，如果存在，则颜色辅助属性不需要默认值
                            if (Convert.ToString(auxPropInfo["auxPropName"]).EqualsIgnoreCase("颜色"))
                            {
                                var valueList = auxPropInfo["valueList"] as List<Dictionary<string, object>>;
                                foreach (var item in valueList)
                                {
                                    item["isDefVal"] = false;
                                }
                            }
                        }
                    }
                }
            }

            datas.AddRange(materialIds.Where(x => !datas.Any(y => Convert.ToString(y["materialId"]) == x))
                                      .Select(x => new Dictionary<string, object> {
                                           { "materialId",x },
                                           { "success",false},
                                           { "auxPropInfo",null}
                                       }));

            this.Result.IsSuccess = true;
            //this.Result.SimpleMessage = "查询所有属性成功!";
            this.Result.SrvData = datas;
        }

        /// <summary>
        /// 加载主辅属性约束信息
        /// </summary>
        /// <param name="auxPropId"></param>
        /// <param name="auxPropValueId"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> LoadMainAuxPropInfo(
            string auxPropId,
            string auxPropValueId)
        {
            //是否存在【主辅属性约束】表单，目前麦浩有这个表单模型
            HtmlForm mainAuxPropForm = null;
            try
            {
                mainAuxPropForm = this.MetaModelService.LoadFormModel(this.Context, "bas_mainauxprop");
            }
            catch { }
            if (mainAuxPropForm == null) return null;

            var sqlText = $@"
            select top 1 map.fid from t_bas_mainauxprop map 
            inner join t_bd_enumdataentry ede on ede.fentryid=map.frefenumvalue and ede.fenumitem=@auxPropValueId 
            where map.fmainorgid=@fmainorgid and map.fforbidstatus='0' and map.fauxpropid='{auxPropId}'";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@auxPropValueId", System.Data.DbType.String, auxPropValueId),
            };

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, mainAuxPropForm.GetDynamicObjectType(this.Context));

            var mainPkids = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParam)?.Select(o => o["fid"]);
            var dynObjs = dm.Select(mainPkids).OfType<DynamicObject>();

            //加载引用数据
            //var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            //refMgr.Load(this.Context, mainAuxPropForm.GetDynamicObjectType(this.Context), dynObjs, false);

            return dynObjs;
        }
    }
}
