using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.PromotionCombine
{
    /// <summary>
    /// 促销活动：列表查询数据插件
    /// </summary>
    [InjectService]
    [FormId("bas_promotioncombine")]
    [OperationNo("querydata")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义服务端事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
            }
        }

        /// <summary>
        /// 列表准备查询过滤参数事件
        /// </summary>
        /// <param name="e"></param>
        public void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var param = e.EventData as SqlBuilderParameter;
            if (param == null) return;

            //提供额外的过滤条件：当前登录用户只能查看自己的活动数据
            if (!this.Context.IsTopOrg)
            {
                //如果有子经销商，那么子经销商的数据也要看到
                //更新促销相关信息
                var commonCombine = new Model.PurchaseOrder.CommonCombine(this.Context, this.Context.Container.GetService<IDBService>());
                string where = commonCombine.GetPromotionSQL(this.Context);
                where = where.Replace("between fbegindate and DATEADD(day,1,fenddate)", "between t0.fbegindate and DATEADD(day,1,t0.fenddate)");
                List<string> entryfiled = new List<string>() { "fproductid", "fproductid.fname", "fproductnumber", "funitid", "funitid.fname", "fgroupnumber", "fgroupdesc", "fbaseqty", "fqty", "fdistrate", "fcolor", "fsize", "fproductbegindate", "fproductenddate" };
                List<string> entryfiled1 = new List<string>() { "fdeliverid", "fdeliverid.fname", "fdelivername" };
                if (param.SelectedFieldKeys.Intersect(entryfiled).Count() > 0 && this.Context.BizOrgId != this.Context.TopCompanyId)
                {
                    List<string> materials = new List<string>();
                    //存在明细行字段，拼接明细行条件
                    var agents = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(this.Context);
                    var para = new DataQueryRuleParaInfo() { SrcFormId = this.HtmlForm.Id };
                    var pros = this.Context.LoadBizDataByFilter("bas_deliver", string.Format(" fforbidstatus = 0 and fagentid='{0}'", this.Context.BizOrgId));
                    foreach (var item in pros)
                    {
                        para.SrcPara["deliverid"] = Convert.ToString(item["id"]);
                        var productIds = new ProductDataIsolateHelper().GetAuthProductDataPKID(Context, para, agents);
                        materials.AddRange(productIds);
                    }
                    string tmp = this.DBService.CreateTempTableWithDataList(this.Context, materials);
                    where += string.Format(" AND t1.fproductid in (select fid from {0})", tmp);
                }
                if (param.SelectedFieldKeys.Intersect(entryfiled1).Count() > 0 && this.Context.BizOrgId != this.Context.TopCompanyId)
                {
                    List<string> materials = new List<string>();
                    var pros = this.Context.LoadBizDataByFilter("bas_deliver", string.Format(" fforbidstatus = 0 and fagentid='{0}'", this.Context.BizOrgId));
                    string tmp = this.DBService.CreateTempTableWithDataList(this.Context, pros.Select(a => a["id"]).ToList());
                    where += string.Format(" AND (t2.fdeliverid in (select fid from {0}) OR ISNULL(t2.fdeliverid,'')='') ", tmp);//若只下发了销售组织，那送达方就是空的
                }
                //var matid = param.HtmlForm.GetField("fproductid");
                param.FilterString = param.FilterString.JoinFilterString(where);
            }
        }
    }
}
