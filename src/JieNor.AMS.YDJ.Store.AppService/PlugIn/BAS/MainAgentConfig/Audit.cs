using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.MainAgentConfig
{
    /// <summary>
    /// 主经销商配置：审核
    /// </summary>
    [InjectService]
    [FormId("bas_mainagentconfig")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            // 审核前有修改【企业微信主体经销商】
            var modifyDataEntities = e.DataEntitys.Where(s =>
                !Convert.ToString(s["fqywxmainagentid"]).EqualsIgnoreCase(Convert.ToString(s["fpreqywxmainagentid"]))).ToList();

            if (modifyDataEntities.Any())
            {
                // 修改【企业微信主体经销商（审核前）】=【企业微信主体经销商】
                foreach (var dataEntity in modifyDataEntities)
                {
                    dataEntity["fpreqywxmainagentid"] = dataEntity["fqywxmainagentid"];
                }
                this.Context.SaveBizData(this.HtmlForm.Id, modifyDataEntities);

                var agentIds = modifyDataEntities.Select(s => Convert.ToString(s["fmainagentid"])).ToList();

                var agentService = this.Container.GetService<IAgentService>();
                agentService.SyncAsync(this.Context, agentIds);
            }
        }
    }
}
