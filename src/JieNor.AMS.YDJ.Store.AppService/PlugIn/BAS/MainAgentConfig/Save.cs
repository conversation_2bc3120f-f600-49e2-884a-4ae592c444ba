using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Senparc.Weixin.Helpers.Extensions;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.MainAgentConfig
{
    [InjectService]
    [FormId("bas_mainagentconfig")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 供应商表单ID
        /// </summary>
        private const string SupplierFormId = "ydj_supplier";
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string errorMessages = string.Empty;

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entrys = newData["fsubagent"] as DynamicObjectCollection;

                return !entrys.IsNullOrEmpty();
            }).WithMessage("关联子经销商至少必须有一行分录, 不允许为空!"));


            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessages = string.Empty;
                //1) 表头不允许存在重复的【主经销商】基础资料, 查找所有《主经销商配置表》表头的【主经销商】与表体的的【子经销商】, 如果重复时提示错误信息, "当前主经销商 XXXX, 已被其他主经销商 XXXX 引用, 不允许重复配置主经销商!"

                //2) 表体不允许存在重复的【子经销商】基础资料, 查找所有《主经销商配置表》表头的【主经销商】与表体的的【子经销商】, 如果重复时提示错误信息, "当前子经销商 XXXX, 已被其他主经销商 XXXX 引用, 不允许重复配置子经销商!"

                var fmainagentid = Convert.ToString(newData["fmainagentid"]);
                var fsubmainagentids =
                    (newData["fsubagent"] as DynamicObjectCollection)?.Select(s => Convert.ToString(s["fsubagentid"]));
                var fid = Convert.ToString(newData["id"]);

                var mainSql = $@"
-- 在表体找
select 
    m.fmainagentid
    ,a.fname as fmainagentname
    ,'{fmainagentid}' as fsubagentid
    ,(select top 1 fname from t_bas_agent where fid='{fmainagentid}') as fsubagentname
from t_bas_mac m 
inner join t_bas_macentry me on m.fid=me.fid
inner join t_bas_agent a on m.fmainagentid=a.fid
where m.fmainorgid='{this.Context.Company}' and m.fforbidstatus='0' and me.fsubagentid='{fmainagentid}'

union

-- 在表头找
select 
    m.fmainagentid
    ,a.fname as fmainagentname
    ,m.fmainagentid as fsubagentid
    ,a.fname as fsubagentname
from t_bas_mac m 
inner join t_bas_agent a on m.fmainagentid=a.fid
where m.fmainorgid='{this.Context.Company}' and m.fforbidstatus='0' and m.fmainagentid='{fmainagentid}' and m.fid<>'{fid}'

";
                using (var reader = this.DBService.ExecuteReader(this.Context, mainSql))
                {
                    if (reader.Read())
                    {
                        errorMessages += $"当前主经销商 {reader.GetValueToString("fsubagentname")}, 已被其他主经销商 {reader.GetValueToString("fmainagentname")} 引用, 不允许重复配置主经销商!\r\n";
                    }
                }

                if (fsubmainagentids.Any())
                {
                    var subSql = $@"
-- 在表体找
select 
    m.fmainagentid
    ,ma.fname as fmainagentname
    ,me.fsubagentid
    ,sa.fname as fsubagentname
from t_bas_mac m 
inner join t_bas_macentry me on m.fid=me.fid
inner join t_bas_agent ma on m.fmainagentid=ma.fid  -- 主经销商
inner join t_bas_agent sa on me.fsubagentid=sa.fid  -- 子经销商
where m.fmainorgid='{this.Context.Company}' and m.fforbidstatus='0' and m.fid<>'{fid}' and me.fsubagentid in ({string.Join(",", fsubmainagentids.Select(s => $"'{s}'"))})

union

-- 在表头找

select 
    m.fmainagentid
    ,a.fname as fmainagentname
    ,m.fmainagentid as fsubagentid
    ,a.fname as fsubagentname
from t_bas_mac m 
inner join t_bas_agent a on m.fmainagentid=a.fid
where m.fmainorgid='{this.Context.Company}' and m.fforbidstatus='0' and m.fmainagentid in ({string.Join(",", fsubmainagentids.Select(s => $"'{s}'"))})

";

                    using (var reader = this.DBService.ExecuteReader(this.Context, subSql))
                    {
                        while (reader.Read())
                        {
                            errorMessages += $"当前子经销商 {reader.GetValueToString("fsubagentname")}, 已被其他主经销商 {reader.GetValueToString("fmainagentname")} 引用, 不允许重复配置子经销商!\r\n";
                        }
                    }
                }

                return errorMessages.IsNullOrEmptyOrWhiteSpace();
            }).WithMessage("{0}", (billObj, propObj) => errorMessages));

        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            //HashSet<string> allAgentIds = new HashSet<string>();
            //foreach (var item in e.DataEntitys)
            //{
            //    allAgentIds.Add(Convert.ToString(item["fmainagentid"]));
            //    var entry = item["fsubagent"] as DynamicObjectCollection;
            //    foreach (var detail in entry)
            //    {
            //        allAgentIds.Add(Convert.ToString(item["fsubagentid"]));
            //    }
            //}

            //if (allAgentIds.Any())
            //{
            //    //找到包含改动经销商的所有已审核的且能匹配到主或子经销商关系的送达方
            //    //如果匹配不到的不考虑，因为送达方审核的时候已经自动生成过一次了
            //    //这里只对匹配到主或子的作处理
            //    var strSql = $@"select t0.fsaleorgid,t1.fname,t1.fprovince,t1.fcity,t1.fregion,t1.fdetailaddress
            //                    ,t1.fcontacter,t1.fcontacterphone,t1.fcorporatename,t1.fcorporateidcard
            //                    ,t0.fagentid,m.fmainagentid from t_bas_deliver t0 with(nolock)
            //                    inner join t_bas_organization t1 with(nolock) on t0.fsaleorgid=t1.fid
            //                    inner join t_bas_macentry me with(nolock) on t0.fagentid=me.fsubagentid
            //                    inner join t_bas_mac m with(nolock) on m.fid=me.fid
            //                    inner join t_bas_organization t3 with(nolock) on m.fmainagentid=t3.fid
            //                    left join t_ydj_supplier t2 with(nolock) on t1.fid=t2.forgid and (t2.fmainorgid=m.fmainagentid or t2.fmainorgid=t3.ftopcompanyid)
            //                    where t0.fagentid in('" + (string.Join("','", allAgentIds)) + $@"') and t0.fstatus='E' and t0.fforbidstatus='0'
            //                    and m.fmainorgid='{this.Context.TopCompanyId}' and m.fforbidstatus='0' and isnull(t2.fid,'')=''
            //                    union
            //                    select t0.fsaleorgid,t1.fname,t1.fprovince,t1.fcity,t1.fregion,t1.fdetailaddress
            //                    ,t1.fcontacter,t1.fcontacterphone,t1.fcorporatename,t1.fcorporateidcard
            //                    ,t0.fagentid,m.fmainagentid from t_bas_deliver t0 with(nolock)
            //                    inner join t_bas_organization t1 with(nolock) on t0.fsaleorgid=t1.fid
            //                    inner join t_bas_mac m with(nolock) on m.fmainagentid=t0.fagentid
            //                    inner join t_bas_organization t3 with(nolock) on m.fmainagentid=t3.fid
            //                    left join t_ydj_supplier t2 with(nolock) on t1.fid=t2.forgid and (t2.fmainorgid=m.fmainagentid or t2.fmainorgid=t3.ftopcompanyid)
            //                    where t0.fagentid in('" + (string.Join("','", allAgentIds)) + $@"') and t0.fstatus='E' and t0.fforbidstatus='0'
            //                    and m.fmainorgid='{this.Context.TopCompanyId}' and m.fforbidstatus='0' and isnull(t2.fid,'')=''";
            //    var result = this.Context.ExecuteDynamicObject(strSql, null);
            //    if (result != null)
            //    {
            //        Dictionary<string, UserContext> ctxDic = new Dictionary<string, UserContext>();
            //        var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            //        foreach (var item in result)
            //        {
            //            var orgName = item["fname"].ToString();//销售组织名称  
            //            //var agentId = item["fagentid"].ToString();
            //            var fmainagentid = item["fmainagentid"].ToString();

            //            UserContext bizAgentCtx;
            //            if (ctxDic.ContainsKey(fmainagentid))
            //            {
            //                bizAgentCtx = ctxDic[fmainagentid];
            //            }
            //            else
            //            {
            //                bizAgentCtx = this.Context.CreateAgentDBContext(fmainagentid);
            //                ctxDic.Add(fmainagentid, bizAgentCtx);
            //            }

            //            //var supplierExists = this.CheckAgentSupplierExists(bizAgentCtx, orgName);
            //            //if (!supplierExists)
            //            //{
            //            var htmlForm = this.MetaModelService?.LoadFormModel(bizAgentCtx, SupplierFormId);
            //            var dt = htmlForm.GetDynamicObjectType(bizAgentCtx);
            //            var supplier = new DynamicObject(dt);
            //            refMgr.Load(bizAgentCtx, dt, supplier, false); //加载引用数据

            //            // 由上下文确定企业id
            //            //supplier["fmainorgid"] = agentId;
            //            supplier["fname"] = orgName;
            //            supplier["forgid"] = item["fsaleorgid"];
            //            supplier["ftype"] = "suppliertype_01";
            //            supplier["fprovince"] = item["fprovince"];
            //            supplier["fcity"] = item["fcity"];
            //            supplier["fregion"] = item["fregion"];
            //            supplier["faddress"] = item["fdetailaddress"];
            //            supplier["fcontacts"] = item["fcontacter"];
            //            supplier["fphone"] = item["fcontacterphone"];
            //            supplier["fcorporatename"] = item["fcorporatename"];
            //            supplier["fcorporatenumber"] = item["fcorporateidcard"];

            //            var saveRes = this.Gateway.InvokeBillOperation(bizAgentCtx, SupplierFormId, new[] { supplier }, "draft", new Dictionary<string, object>());
            //            this.Result.MergeResult(saveRes);
            //            //}
            //        }
            //    }
            //}
        }

        ///// <summary>
        ///// 检查经销商供应商名称是否存在
        ///// </summary>
        ///// <param name="agentCtx">经销商上下文</param>
        ///// <param name="supplierName">供应商名称</param>
        ///// <returns>指定的供应商名称是否存在</returns>
        //private bool CheckAgentSupplierExists(UserContext agentCtx, string supplierName)
        //{
        //    var sqlText = $@"
        //    select top 1 fid from t_ydj_supplier with(nolock) 
        //    where (fmainorgid='{agentCtx.Company}' or fmainorgid='{agentCtx.TopCompanyId}') and fname=@fname";

        //    var sqlParam = new List<SqlParam>
        //    {
        //        new SqlParam("@fname", System.Data.DbType.String, supplierName)
        //    };

        //    using (var reader = this.DBService.ExecuteReader(agentCtx, sqlText, sqlParam))
        //    {
        //        if (reader.Read())
        //        {
        //            return true;
        //        }
        //    }

        //    return false;
        //}
    }
}
