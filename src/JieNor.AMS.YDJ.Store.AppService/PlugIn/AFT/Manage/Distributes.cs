using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.AFT.Manage
{
    /// <summary>
    /// 派发指令
    /// </summary>
    [InjectService]
    [FormId("aft_manage")]
    [OperationNo("distributes")]
    public class Distributes : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            string fdeptid = GetQueryOrSimpleParam<string>("fdeptid");
            string fstaffid = GetQueryOrSimpleParam<string>("fstaffid");
            string fcontent = GetQueryOrSimpleParam<string>("fcontent");

            if (string.IsNullOrEmpty(fdeptid)) {
                throw new BusinessException("部门不能为空！");
            }
            if (string.IsNullOrEmpty(fstaffid))
            {
                throw new BusinessException("处理人不能为空！");
            }
            if (string.IsNullOrEmpty(fcontent))
            {
                throw new BusinessException("售后处理单中处理方案不能为空！");
            }
            var objs = new List<DynamicObject>();
            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "aft_instruction");
            DynamicObject billHead = new DynamicObject(htmlForm.GetDynamicObjectType(this.Context));
            billHead["fdeptid"] = fdeptid;
            billHead["fstaffid"] = fstaffid;
            billHead["fcontent"] = fcontent;
            billHead["fdate"] = DateTime.Now.ToShortDateString() ;
            objs.Add(billHead);
            var result = this.Gateway.InvokeBillOperation(this.Context, htmlForm.Id, objs, "save", null);//发送给供应商
            if (!result.IsSuccess)//审核失败，不再往下执行
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = result.SimpleMessage;
                this.Result.ComplexMessage = result.ComplexMessage;
                return;
            }
            else {
                DynamicObject billData = e.DataEntitys[0];

               
                var uiDataConvert = this.Container.GetService<IUiDataConverter>();
                var billJsonData = uiDataConvert.CreateUIDataObject(this.Context, htmlForm, billHead);
                //打开客户新增页面指令
                object action = this.Context.ShowSpecialForm(htmlForm, null, true,
                    this.CurrentPageId,
                    Enu_OpenStyle.Default,
                    Enu_DomainType.Bill,
                    new Dictionary<string, object>
                    {
                    { "sourceId", billData["id"] },
                    { "sourceFormId", this.HtmlForm.Id },
                    { "sourcePageId", this.CurrentPageId }
                    },
                    (formPara) =>
                    {
                        formPara.FormCaption = "售后处理指令--新增";
                        formPara.Status = Enu_BillStatus.New;
                        formPara.UiData = billJsonData.GetJsonValue("uiData", new JObject());
                    });

                this.Result.HtmlActions.Add(action);
            }

        }


    }
}
