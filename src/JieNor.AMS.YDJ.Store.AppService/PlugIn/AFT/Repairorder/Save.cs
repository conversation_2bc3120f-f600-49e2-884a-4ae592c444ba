using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.AFT.Repairorder
{
    /// <summary>
    /// 维修单保存
    /// </summary>
    [InjectService]
    [FormId("aft_repairorder")]
    [OperationNo("save")]
    public class Save: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //判断商品明细行是否有填商品字段
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Checkfproductid(newData))
                {
                    return false;
                }
                return true;
            }).WithMessage("商品信息的商品，不能为空！"));
        }

        private bool Checkfproductid(DynamicObject newData)
        {
            var res = false;
            var Fentry = newData["fentry"] as DynamicObjectCollection;
            if (Fentry!=null&&Fentry.Any())
            {
                foreach (var item in Fentry)
                {
                    var fproductid = Convert.ToString(item["fproductid"]);
                    if (fproductid.IsNullOrEmptyOrWhiteSpace())
                    {
                        return true;
                    }
                }
            }
            else
            {
                res = true;
            }
            return res;
        }
    }
}
