using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.AFT.Repairorder
{
    /// <summary>
    /// 售后维修单 审核
    /// 作者：zpf
    /// 日期：2022-02-16
    /// </summary>
    [InjectService]
    [FormId("aft_repairorder")]
    [OperationNo("audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys.Length == 0 || e.DataEntitys == null) return;

            ////var operate = this.GetQueryOrSimpleParam<string>("operate");//操作：receivables：应收、payables：应付
            //string ruleId = "aft_repairorder2ydj_payreceipt";//售后维修单下推其他应付单
            //string targetFormId = "ydj_payreceipt";//其他应付单
            //var fEntityKey = "fentry";//源单明细标识

            ////if (operate == "payables")
            ////{
            ////    ruleId = "aft_repairorder2ydj_payreceipt"; //售后维修单下推其他应付单
            ////    targetFormId = "ydj_payreceipt";//其他应付单
            ////}

            //var targetForm = this.MetaModelService.LoadFormModel(this.Context, targetFormId);
            //var pushSelectedRows = e.DataEntitys.Where(x =>
            //{
            //    var fentries = x[fEntityKey] as DynamicObjectCollection;
            //    if (fentries == null || fentries.Count <= 0)
            //    {
            //        return false;
            //    }
            //    return fentries.Any();
            //}).SelectMany(x =>
            //{
            //    var id = Convert.ToString(x["id"]);
            //    var fentries = x[fEntityKey] as DynamicObjectCollection;
            //    return fentries.Select(y => new SelectedRow
            //    {
            //        PkValue = id,
            //        EntityKey = fEntityKey,
            //        EntryPkValue = Convert.ToString(y["id"])
            //    });
            //}).ToList();

            //if (pushSelectedRows == null || pushSelectedRows.Count <= 0)
            //{
            //    return;
            //}

            //var convertService = this.Container.GetService<IConvertService>();
            //var pushResult = convertService.Push(this.Context, new BillConvertContext()
            //{
            //    RuleId = ruleId,
            //    SourceFormId = this.HtmlForm.Id,
            //    TargetFormId = targetFormId,
            //    SelectedRows = pushSelectedRows.ToConvertSelectedRows(),
            //    Option = OperateOption.Create()
            //});

            //var convertResult = pushResult.SrvData as ConvertResult;
            //var targetDatas = convertResult.TargetDataObjects?.ToList();
            //if (targetDatas == null || targetDatas.Count <= 0)
            //{
            //    this.Result.ComplexMessage.ErrorMessages.Add($"下推{targetForm.Caption}失败!");
            //    return;
            //}
            //var response = this.Gateway.InvokeBillOperation(this.Context, targetFormId, targetDatas, "save", new Dictionary<string, object>());
            //if (response == null || response.IsSuccess == false || response.ComplexMessage.ErrorMessages.Count > 0)
            //{
            //    if (response != null && response.ComplexMessage.ErrorMessages.Count > 0)
            //    {
            //        this.Result.ComplexMessage.ErrorMessages.AddRange(response.ComplexMessage.ErrorMessages);
            //    }
            //    return;
            //}
        }
    }
}
