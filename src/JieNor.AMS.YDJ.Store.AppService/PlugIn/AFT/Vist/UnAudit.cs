using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.IncomeDisburse;
using JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Service;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.AFT
{
    /// <summary>
    /// 回访单反审核
    /// </summary>
    [InjectService]
    [FormId("ydj_vist")]
    [OperationNo("unaudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            var sourceTypes = e.DataEntitys.Where(x => !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fsourcetype"])).Distinct();
            var sourceNos = e.DataEntitys.Where(x => !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fsourcenumber"])).Distinct();

            if (sourceNos.Any())
            {
                StringBuilder sqlWhereMain = new StringBuilder();
                List<SqlParam> paramMain = new List<SqlParam>();
                sqlWhereMain.Append(string.Join(",", sourceNos.Select((x, i) => $"@fbillno{i}")));
                paramMain.AddRange(sourceNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                paramMain.AddRange(sourceTypes.Select((x, i) => new SqlParam($"@fsourcetype{i}", System.Data.DbType.String, x)));
                paramMain.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company));
                var oldbills = new List<DynamicObject>();
                if (sourceTypes.Count() == 1)
                {
                    oldbills = this.Context.LoadBizDataByFilter("ydj_vist", "fsourcenumber in(" + sqlWhereMain + ") and fsourcetype=@fsourcetype0 and fmainorgid=@fmainorgid", false, paramMain);
                }
                else
                {
                    oldbills = this.Context.LoadBizDataByFilter("ydj_vist", "fsourcenumber in(" + sqlWhereMain + ") and fsourcetype in(" + string.Join(",", sourceTypes.Select((x, i) => $"@fsourcetype{i}")) + ") and fmainorgid=@fmainorgid", false, paramMain);
                }

                if (oldbills.Any())
                {
                    StringBuilder msg = new StringBuilder();
                    List<string> rewriteSerNos = new List<string>();
                    List<string> rewritefeedNos = new List<string>();
                    List<string> pkNos = e.DataEntitys.Where(x=>!x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace())
                        .GroupBy(x=>new { type=x["fsourcetype"],number=x["fsourcenumber"] })
                        .Where(g=>g.Count()>1).Select(x =>x.Key.number.ToString()).ToList();
                    if (pkNos.Any())
                    {
                        throw new BusinessException("以下源单单号【" + string.Join(",", pkNos) +"】对应的回访单不能多个同时进行反审核！请检查！");
                    }
                    foreach (var item in e.DataEntitys)
                    {
                        var temp = oldbills.Where(x => x["fsourcenumber"].Equals(item["fsourcenumber"])
                        && x["fsourcetype"].Equals(item["fsourcetype"])
                        && !x["fbillno"].Equals(item["fbillno"]));
                        //存在未审核的则不能反审核
                        var tempNoAudt = temp.Where(x => !x["fstatus"].Equals("E")).FirstOrDefault();
                        if (!tempNoAudt.IsNullOrEmpty())
                        {
                            msg.Append("回访单【{0}】对应上游单据存在未审核回访单【{1}】，请等待该单据审核后或删除后再进行此操作！<br/>".Fmt(item["fbillno"], tempNoAudt["fbillno"]));
                        }
                        //否则存在已审核的则不反写
                        else if (temp.Any(x => x["fstatus"].Equals("E")))/* && !pkNos.Contains(x["fbillno"])*/
                        {
                            continue;
                        }
                        //需要反写上游单据
                        else
                        {
                            if (item["fsourcetype"].Equals("ydj_service"))
                            {
                                string number = Convert.ToString(item["fsourcenumber"]);
                                if (!rewriteSerNos.Contains(number))
                                {
                                    rewriteSerNos.Add(number);
                                }
                            }
                            else if (item["fsourcetype"].Equals("ste_afterfeedback"))
                            {
                                string number = Convert.ToString(item["fsourcenumber"]);
                                if (!rewritefeedNos.Contains(number))
                                {
                                    rewritefeedNos.Add(number);
                                }
                            }
                        }
                    }
                    if (msg.Length > 0)
                    {
                        throw new BusinessException(msg.ToString());
                    }
                    var dm = this.GetDataManager();
                    if (rewriteSerNos.Any())
                    {
                        StringBuilder sqlWhere = new StringBuilder();
                        List<SqlParam> param = new List<SqlParam>();
                        sqlWhere.Append(string.Join(",", rewriteSerNos.Select((x, i) => $"@fbillno{i}")));
                        param.AddRange(rewriteSerNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                        var serBills = this.Context.LoadBizDataByFilter("ydj_service", "fbillno in(" + sqlWhere + ")and fmainorgid='" + this.Context.Company + "'", false, param);

                        foreach (var serData in serBills)
                        {
                            serData["fserstatus"] = "sersta04";
                        }
                        if (serBills.Any())
                        {
                            var serForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_service");
                            dm.InitDbContext(this.Context, serForm.GetDynamicObjectType(this.Context));
                            // 保存前预处理
                            var preService = this.Container.GetService<IPrepareSaveDataService>();
                            preService.PrepareDataEntity(this.Context, serForm, serBills.ToArray(), OperateOption.Create());
                            dm.Save(serBills);
                        }
                    }
                    //反写上游售后反馈单
                    if (rewritefeedNos.Any())
                    {
                        StringBuilder sqlWhere = new StringBuilder();
                        List<SqlParam> param = new List<SqlParam>();
                        sqlWhere.Append(string.Join(",", rewritefeedNos.Select((x, i) => $"@fbillno{i}")));
                        param.AddRange(rewritefeedNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                        //排除已关闭的，无需反写and ffeedstatus!='aft_service_06'
                        var feedBills = this.Context.LoadBizDataByFilter("ste_afterfeedback", "fbillno in(" + sqlWhere + ") and fmainorgid='" + this.Context.Company + "'", false, param);
                        //经和需求确认此处暂不管下游服务单对应的状态
                        foreach (var serData in feedBills)
                        {
                            serData["ffeedstatus"] = "aft_service_05";
                            serData["ffinishdate"] = null;
                        }
                        if (feedBills.Any())
                        {
                            var feedForm = this.MetaModelService.LoadFormModel(this.Context, "ste_afterfeedback");
                            dm.InitDbContext(this.Context, feedForm.GetDynamicObjectType(this.Context));
                            // 保存前预处理
                            var preService = this.Container.GetService<IPrepareSaveDataService>();
                            preService.PrepareDataEntity(this.Context, feedForm, feedBills.ToArray(), OperateOption.Create());
                            dm.Save(feedBills);
                        }
                    }
                }
            }
        }
    }
}
