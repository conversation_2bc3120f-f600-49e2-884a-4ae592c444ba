using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.IncomeDisburse;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.AFT
{
    /// <summary>
    /// 回访单审核
    /// </summary>
    [InjectService]
    [FormId("ydj_vist")]
    [OperationNo("audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        private DynamicObjectType followType = null;
        private string deptId;
        private string staffId;
        /// <summary>
        /// //判断是否小程序评价过来的
        /// </summary>
        private bool callerTerminal {
            get
            {
                return this.GetQueryOrSimpleParam("callerTerminal", "").ToLower().Equals("mpapi-detailsave");
            }
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            var serNos = e.DataEntitys.Where(x => x["fsourcetype"].Equals("ydj_service")).Select(x => Convert.ToString(x["fsourcenumber"])).Distinct();
            var feedBackNos = e.DataEntitys.Where(x => !x["fsourcetype"].IsNullOrEmpty()&&x["fsourcetype"].Equals("ste_afterfeedback")).Select(x => Convert.ToString(x["fsourcenumber"])).Distinct();
            List<DynamicObject> addFollow = new List<DynamicObject>();
            var followForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord");
            followType = followForm.GetDynamicObjectType(this.Context);
            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;
            foreach (var item in e.DataEntitys)
            {
                item["ffinishdate"] = DateTime.Now;
            }
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
            prepareSerivce.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            dm.Save(e.DataEntitys);

            //反写上游服务单并增加跟进记录
            if (serNos.Any())
            {
                ReWriteSerInfo(serNos.ToList(), e, addFollow);
            }
            //反写上游售后反馈单，同时要检查是否有上下游服务单，有的话也要反写对应状态
            if (feedBackNos.Any())
            {
                ReWriteFeedInfo(feedBackNos.ToList());
            }
            if (addFollow.Any())
            {
                string operPhone = this.Context.LoadBizBillHeadDataById("sec_user", this.Context.UserId, "fphone")?["fphone"]?.ToString();
                addFollow.ForEach(x =>
                {
                    x["fphone"] = operPhone;
                });
                dm.InitDbContext(this.Context, followType);
                prepareSerivce.PrepareDataEntity(this.Context, followForm, addFollow.ToArray(), OperateOption.Create());
                dm.Save(addFollow);
            }
        }
        /// <summary>
        /// 反写上游服务单并增加跟进记录
        /// </summary>
        /// <param name="serNos"></param>
        private void ReWriteSerInfo(List<string> serNos, EndOperationTransactionArgs e, List<DynamicObject> addFollow)
        {
            string serFormId = "ydj_service";
            var dm = this.GetDataManager();
            StringBuilder sqlWhere = new StringBuilder();
            List<SqlParam> param = new List<SqlParam>();
            sqlWhere.Append(string.Join(",", serNos.Select((x, i) => $"@fbillno{i}")));
            param.AddRange(serNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
            var serBills = this.Context.LoadBizDataByFilter(serFormId, "fbillno in(" + sqlWhere + ")and fmainorgid='" + this.Context.Company + "'", false, param);
            List<DynamicObject> updateObjs = new List<DynamicObject>();
            //方便找出回访单号
            var mapOld = (from x in e.DataEntitys.Where(t => t["fsourcetype"].Equals(serFormId))
                          join y in serBills on x["fsourcenumber"] equals y["fbillno"]
                          select new { key = Convert.ToString(x["fsourcenumber"]), value = Convert.ToString(x["fbillno"]) });
            Dictionary<string, string> mapInfo = new Dictionary<string, string>();
            foreach (var item in mapOld)
            {
                mapInfo.Add(item.key, item.value);
            }
            foreach (var serData in serBills)
            {
                if (!serData["fserstatus"].IsNullOrEmpty()&&!serData["fserstatus"].Equals("sersta05"))
                {
                    serData["fserstatus"] = "sersta05";
                    serData["fclosedate"] = DateTime.Now;
                    updateObjs.Add(serData);
                }
                addFollow.Add(Followerrecord(serData, mapInfo[Convert.ToString(serData["fbillno"])]));
            }
            if (updateObjs.Any())
            {
                var serForm = this.MetaModelService.LoadFormModel(this.Context, serFormId);
                dm.InitDbContext(this.Context, serForm.GetDynamicObjectType(this.Context));
                // 保存前预处理
                var preService = this.Container.GetService<IPrepareSaveDataService>();
                preService.PrepareDataEntity(this.Context, serForm, serBills.ToArray(), OperateOption.Create());
                dm.Save(updateObjs);
            }
        }
        /// <summary>
        /// 反写上游售后反馈单，同时要检查是否有下游服务单，有的话也要反写对应状态
        /// </summary>
        /// <param name="feedBackNos"></param>
        private void ReWriteFeedInfo(List<string> feedBackNos)
        {
            StringBuilder sqlWhere = new StringBuilder();
            List<SqlParam> param = new List<SqlParam>();
            string feedFormID = "ste_afterfeedback";
            string serFormId = "ydj_service";
            var dm = this.GetDataManager();
            sqlWhere.Append(string.Join(",", feedBackNos.Select((x, i) => $"@fbillno{i}")));
            param.AddRange(feedBackNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
            //排除已关闭的，无需反写and ffeedstatus!='aft_service_06'
            var feedBills = this.Context.LoadBizDataByFilter(feedFormID, "fbillno in(" + sqlWhere + ") and ffeedstatus!='aft_service_06' and fmainorgid='" + this.Context.Company + "'", false, param);
            var feedSourceSerNos = feedBills.Where(x => Convert.ToString(x["fsourcetype"]) == "ydj_serivce" && !Convert.ToString(x["fsourcenumber"]).IsNullOrEmptyOrWhiteSpace())?.Select(x => Convert.ToString(x["fsourcenumber"]));

            var serWhere = "(fsourcenumber in(" + sqlWhere + ") and fsourcetype='ste_afterfeedback' and fserstatus!='sersta05' and fserstatus!='sersta06') ";
            if(feedSourceSerNos!=null&& feedSourceSerNos.Any())
            {
                serWhere += $" or (fbillno in('{(string.Join("','", feedSourceSerNos))}'))";
            }
            var serBills = this.Context.LoadBizDataByFilter(serFormId, serWhere, false, param);
            foreach (var feedData in feedBills)
            {
                feedData["ffeedstatus"] = "aft_service_06";
                if (feedData["ffinishdate"].IsNullOrEmptyOrWhiteSpace())
                {
                    feedData["ffinishdate"] = DateTime.Now;
                }
                if (feedData["fclosedate"].IsNullOrEmptyOrWhiteSpace())
                {
                    feedData["fclosedate"] = DateTime.Now;
                } 
            }
            foreach (var serData in serBills)
            {
                serData["fserstatus"] = "sersta05";
                serData["fclosedate"] = DateTime.Now;
            }
            if (feedBills.Any())
            {
                var feedForm = this.MetaModelService.LoadFormModel(this.Context, feedFormID);
                dm.InitDbContext(this.Context, feedForm.GetDynamicObjectType(this.Context));
                // 保存前预处理
                var preService = this.Container.GetService<IPrepareSaveDataService>();
                preService.PrepareDataEntity(this.Context, feedForm, feedBills.ToArray(), OperateOption.Create());
                dm.Save(feedBills);
            }
            if (serBills.Any())
            {
                var serForm = this.MetaModelService.LoadFormModel(this.Context, serFormId);
                //先保存，源单为售后单的服务单可能没生成回访单
                dm.InitDbContext(this.Context, serForm.GetDynamicObjectType(this.Context));
                // 保存前预处理
                var preService = this.Container.GetService<IPrepareSaveDataService>();
                preService.PrepareDataEntity(this.Context, serForm, serBills.ToArray(), OperateOption.Create());
                dm.Save(serBills);
                sqlWhere = new StringBuilder();
                param = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", serBills.Select((x, i) => $"@fbillno{i}")));
                param.AddRange(serBills.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x["fbillno"])));
                //再找出下游回访单，存在则自动审核
                var vistBills = this.Context.LoadBizDataByFilter("ydj_vist", "fsourcenumber in(" + sqlWhere + ") and fstatus!='E' and fmainorgid='" + this.Context.Company + "'", false, param);
                List<DynamicObject> submitObj = new List<DynamicObject>();
                List<DynamicObject> auditObj = new List<DynamicObject>();
                foreach (var item in vistBills)
                {
                    switch (item["fstatus"].ToString().ToUpper())
                    {
                        case "A":
                            throw new ArgumentException("上游单据销售出库单为关联暂存状态，自动审核失败！");
                        case "B":
                        case "C":
                            submitObj.Add(item);
                            break;
                        case "D":
                            auditObj.Add(item);
                            break;
                        default:
                            break;
                    }
                }

                if (submitObj.Any())
                {
                    var invokeSubmit = this.Gateway.InvokeBillOperation(this.Context, "ydj_vist", submitObj, "submit", new Dictionary<string, object>());
                    invokeSubmit?.ThrowIfHasError(true, string.Join("", invokeSubmit.ComplexMessage.ErrorMessages));
                }
                auditObj.AddRange(submitObj);
                if (auditObj.Any())
                {
                    var invokeAudit = this.Gateway.InvokeBillOperation(this.Context, "stk_sostockout", auditObj, "audit", new Dictionary<string, object>());
                    invokeAudit?.ThrowIfHasError(true, string.Join("", invokeAudit.ComplexMessage.ErrorMessages));
                }
            }
        }
        /// <summary>
        /// 跟进记录
        /// </summary>
        /// <param name="data"></param>
        /// <param name="type">跟进记录类型</param>
        private DynamicObject Followerrecord(DynamicObject data, string vistBillNo)
        {
            DynamicObject saveData = new DynamicObject(followType);
            saveData["fcustomerid"] = data["fcustomerid"];//客户ID 新
            saveData["frelatedbilltype"] = "ydj_service";//来源表
            saveData["fsourcetype"] = "ydj_service";//来源表
            saveData["frelatedbillno"] = data["fbillno"];//来源单据编号
            saveData["fsourcenumber"] = data["fbillno"];//来源单据编号
            saveData["ffollowtime"] = DateTime.Now;//跟进时间
            saveData["fobjecttype"] = callerTerminal ? "objecttype33" : "objecttype29";//跟进记录类型--回访
            saveData["fstaffid"] = staffId;//跟进员工id ydj_staff表 员工表
            saveData["fdeptid"] = deptId;//跟进员工部门id ydj_dept表 部门表
            saveData["ffollowerid"] = this.Context.UserId;//跟进人id sec_user表 用户表
            saveData["fcontacts"] = this.Context.DisplayName ?? this.Context.UserName;//当前用户名
            saveData["ftype"] = "6";
            saveData["fdescription"] = callerTerminal ? "客户已主动评价，回访单号：{0}".Fmt(vistBillNo) : "服务已回访，回访单号：{0}".Fmt(vistBillNo);//跟进描述
            return saveData;
        }
    }
}
