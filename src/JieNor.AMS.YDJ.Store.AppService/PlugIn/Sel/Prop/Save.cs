using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.Interface.Log;
using System.Threading;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.Prop
{
    /// <summary>
    /// 属性：保存插件
    /// </summary>
    [InjectService]
    [FormId("sel_prop")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 日志服务
        /// </summary>
        private ILogServiceEx LogServiceEx { get; set; }

        /// <summary>
        /// 是否是通过 Excel 导入调用的保存
        /// </summary>
        private bool IsExcelImportSave
        {
            get
            {
                var topOrperationNo = this.Option.GetVariableValue("TopOrperationNo", string.Empty);
                return topOrperationNo.EqualsIgnoreCase("ExcelImport");
            }
        }

        /// <summary>
        /// 本次保存操作需要关联更新辅助属性组合值的属性ID
        /// 属性名称修改后，需要关联更新辅助属性组合值名称字段
        /// </summary>
        private string NeedRelationUpdatePropId { get; set; }

        /// <summary>
        /// 辅助属性组合值模型
        /// </summary>
        private HtmlForm AuxPropValSetForm { get; set; }

        /// <summary>
        /// 辅助属性组合值ORM实例
        /// </summary>
        private IDataManager AuxPropValSetDm { get; set; }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //校验基础资料和辅助资料
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var valueSource = Convert.ToString(newData["fvaluesource"]);
                switch (valueSource)
                {
                    case "basedata":
                        if (newData["frefbaseformid"].IsNullOrEmptyOrWhiteSpace())
                        {
                            errorMessage = $"{this.HtmlForm.Caption}【{newData["fnumber"]}】的【基础资料】不能为空！";
                            return false;
                        }
                        break;
                    case "enumdata":
                        if (newData["frefenumformid"].IsNullOrEmptyOrWhiteSpace())
                        {
                            errorMessage = $"{this.HtmlForm.Caption}【{newData["fnumber"]}】的【辅助资料】不能为空！";
                            return false;
                        }
                        break;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 执行操作事务前事件，通知插件对要处理的数据进行排序等预处理
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            //初始化本次保存操作需要关联更新辅助属性组合值的属性ID
            this.InitNeedRelationUpdatePropId(e.DataEntitys);
        }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            //自动生成属性值基础资料
            //this.AutoCreatePropValue(e.DataEntitys);

            //异步关联更新辅助属性组合值
            //Task.Run(() =>
            //{
            //    this.RelationUpdateAuxPropValueSet(e.DataEntitys);
            //});

            //另起一个线程 处理辅助属性保存逻辑，确保保存数据能保存进去
            Task task = new Task(() =>
            {
                this.RelationUpdateAuxPropValueSet(e.DataEntitys);
            });
            ThreadWorker.QuequeTask(task, result => { });

            //发送消息，通知其他负载均衡站点更新缓存 
            var pubSubService = this.Container.GetService<IPubSubService>();
            pubSubService.PublishMessage<string>(Framework.DataTransferObject.Const.PubSubChannel.AuxPropTypesChange, "");

            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 自动生成属性值基础资料
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void AutoCreatePropValue(DynamicObject[] dataEntitys)
        {
            if (this.IsExcelImportSave) return;

            //手工保存时，如果基础资料是【属性值】则自动生成一个属性值基础资料
            var dataEntity = dataEntitys
                .Where(o =>
                    Convert.ToString(o["fvaluesource"]).EqualsIgnoreCase("basedata")
                    && Convert.ToString(o["frefbaseformid"]).EqualsIgnoreCase("sel_propvalue"))
                .FirstOrDefault();

            if (dataEntity == null) return;

            var propValueForm = this.MetaModelService.LoadFormModel(this.Context, "sel_propvalue");
            var propValueFormDt = propValueForm.GetDynamicObjectType(this.Context);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, propValueFormDt);

            //根据属性值名称加载属性值信息
            var propValueDatas = this.Context.LoadBizDataByACLFilter("sel_propvalue", $"fname in('是','无')");

            var saveDatas = new List<DynamicObject>();

            //构建属性值数据包
            this.BuildPropValueData(dataEntity, propValueFormDt, propValueDatas, saveDatas, "是");
            this.BuildPropValueData(dataEntity, propValueFormDt, propValueDatas, saveDatas, "无");

            if (saveDatas.Any())
            {
                var saveArray = saveDatas.ToArray();

                var preSerivce = this.Container.GetService<IPrepareSaveDataService>();
                preSerivce.PrepareDataEntity(this.Context, propValueForm, saveArray, OperateOption.Create());

                var invokeSave = this.Gateway.InvokeBillOperation(this.Context,
                    propValueForm.Id,
                    saveArray,
                    "save",
                    new Dictionary<string, object>());
                invokeSave?.ThrowIfHasError(true, $"{propValueForm.Caption}【保存】失败！");
            }
        }

        /// <summary>
        /// 构建属性值数据包
        /// </summary>
        private void BuildPropValueData(
            DynamicObject dataEntity,
            DynamicObjectType propValueFormDt,
            List<DynamicObject> propValueDatas,
            List<DynamicObject> saveDatas,
            string propValueName)
        {
            //是否配件
            var isParts = Convert.ToBoolean(dataEntity["fisparts"]);

            var propValueData = propValueDatas.FirstOrDefault(o => Convert.ToString(o["fname"]).EqualsIgnoreCase(propValueName));
            if (propValueData == null)
            {
                propValueData = new DynamicObject(propValueFormDt);
                propValueData["fname"] = propValueName;
                propValueData["fpropid"] = dataEntity["id"];
                saveDatas.Add(propValueData);

                //始终以总部的身份来创建属性值（因为属性值是所有经销商共用的基础数据）
                propValueData["fmainorgid"] = this.Context.IsTopOrg ? this.Context.Company : this.Context.TopCompanyId;
            }
            else
            {
                if (isParts)
                {
                    var forbidStatus = Convert.ToBoolean(propValueData["fforbidstatus"]);
                    if (forbidStatus)
                    {
                        //自动启用
                        propValueData["fforbidstatus"] = false;
                        propValueData["fforbiddate"] = null;
                        propValueData["fforbidid"] = "";
                        saveDatas.Add(propValueData);
                    }
                }
                else
                {
                    //自动禁用
                    propValueData["fforbidstatus"] = true;
                    propValueData["fforbiddate"] = DateTime.Now;
                    propValueData["fforbidid"] = this.Context.UserId;
                    saveDatas.Add(propValueData);
                }
            }
        }

        /// <summary>
        /// 初始化本次保存操作需要关联更新辅助属性组合值的属性ID
        /// </summary>
        private void InitNeedRelationUpdatePropId(DynamicObject[] dataEntitys)
        {
            if (this.IsExcelImportSave) return;

            //手工修改保存时，如果修改了属性名称，则自动更新辅助属性组合值中的属性名称
            var dataEntity = dataEntitys.FirstOrDefault();
            if (!dataEntity.DataEntityState.FromDatabase) return;

            var propId = Convert.ToString(dataEntity["id"]);
            var propName = Convert.ToString(dataEntity["fname"]).Trim();

            //如果没有修改属性名称，则不用处理
            var dbDataEntity = this.Context.LoadBizBillHeadDataById(this.HtmlForm.Id, propId);
            var dbPropName = Convert.ToString(dbDataEntity?["fname"]).Trim();
            if (dbPropName.IsNullOrEmptyOrWhiteSpace() || dbPropName.EqualsIgnoreCase(propName)) return;

            this.NeedRelationUpdatePropId = propId;
        }

        /// <summary>
        /// 关联更新辅助属性组合值
        /// </summary>
        private void RelationUpdateAuxPropValueSet(DynamicObject[] dataEntitys)
        {
            var needRelationUpdatePropId = this.NeedRelationUpdatePropId;
            if (needRelationUpdatePropId.IsNullOrEmptyOrWhiteSpace()) return;
            
            this.NeedRelationUpdatePropId = "";

            //批量加载辅助属性组合值ID（数据量有可能比较大，需要分批处理，否则会占用大量的系统内存）
            var auxPropValIds = this.LoadAuxPropValueSetIdByPropId(needRelationUpdatePropId);
            if (auxPropValIds == null || !auxPropValIds.Any()) return;

            this.LogServiceEx = this.Container.GetService<ILogServiceEx>();

            //加载辅助属性组合值模型
            this.AuxPropValSetForm = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvalueset");
            this.AuxPropValSetDm = this.GetDataManager();
            this.AuxPropValSetDm.InitDbContext(this.Context, this.AuxPropValSetForm.GetDynamicObjectType(this.Context));

            //批次数
            var batchSize = 5000;

            //分批处理
            var bacheCount = decimal.ToInt32(Math.Ceiling(auxPropValIds.Count * 1.0m / batchSize));
            var batchIndex = 1;
            while (batchIndex <= bacheCount)
            {
                var batchIds = auxPropValIds.Skip((batchIndex - 1) * batchSize).Take(batchSize).ToList();
                if (batchIds.Count < 1) break;

                var auxPropValSets = this.Context.LoadBizDataById("bd_auxpropvalueset", batchIds);

                this.BatchRelationUpdateAuxPropValueSet(auxPropValSets);

                batchIndex++;
            } 
        }

        /// <summary>
        /// 分批关联更新辅助属性组合值
        /// </summary>
        private void BatchRelationUpdateAuxPropValueSet(List<DynamicObject> auxPropValSets)
        {
            if (auxPropValSets == null || !auxPropValSets.Any()) return;
            var saveData_exts = new List<DynamicObject>();

            DynamicObject[] lstAllAuxPropObjs = this.LoadAllAuxProperties(this.Context);

            var Lstattr_e = auxPropValSets.Select(o => Convert.ToString(o["fattrinfo_e"])).Distinct().ToList();
            var AttrExtObjs = this.Context.LoadBizDataById("bd_auxpropvalue_ext", Lstattr_e);
            //批量加载属性数据包
            var propIds = new List<string>();
            foreach (var setObj in auxPropValSets)
            {
                var setEntrys = setObj?["fentity"] as DynamicObjectCollection;
                if (setEntrys == null || !setEntrys.Any()) continue;
                var _propIds = setEntrys?.Select(o => Convert.ToString(o["fauxpropid"]));
                propIds.AddRange(_propIds);
            }
            propIds = propIds.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            DynamicObjectCollection propDatas = null;
            if (propIds.Any()) propDatas = this.Context.LoadBizBillHeadDataById("sel_prop", propIds);
            if (propDatas == null || !propDatas.Any()) return;

            var saveDatas = new List<DynamicObject>();

            //重新组装辅助属性组合值名称字段值
            foreach (var setObj in auxPropValSets)
            {
                var setEntrys = setObj?["fentity"] as DynamicObjectCollection;
                if (setEntrys == null || !setEntrys.Any()) continue;

                //自动根据表体组装的编码及名称回填表头
                var lstAuxSortObjs = setEntrys.Join(lstAllAuxPropObjs,
                      ok => ok["fauxpropid"] as string,
                      ik => ik["id"] as string,
                      (ok, ik) =>
                          new
                          {
                              Item1 = ok,
                              Item2 = ik
                          },
                      StringComparer.OrdinalIgnoreCase);

                //新字段目的是按属性排序固定辅助属性名称的顺序
                var strHeadName_e = string.Join(",", lstAuxSortObjs
                  .OrderBy(o => o.Item1["fauxpropid"])
                  .Select(o =>
                  {
                      return string.Format("{0}:{1}", Convert.ToString(o.Item2["fname"]).Trim(), Convert.ToString(o.Item1["fvaluename"]).Trim());
                  }));

                var setNames = new List<string>();
                var _setEntrys = setEntrys.OrderBy(o => Convert.ToInt32(o["fdisplayseq"])).ToList();
                foreach (var _setEntry in _setEntrys)
                {
                    var propId = Convert.ToString(_setEntry["fauxpropid"]);
                    var propValueId = Convert.ToString(_setEntry["fvalueid"]);
                    if (propId.IsNullOrEmptyOrWhiteSpace() || propValueId.IsNullOrEmptyOrWhiteSpace()) continue;

                    //如果此处有一个属性没有匹配到，则终止整个匹配过程
                    var propData = propDatas.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(propId));
                    var propName = Convert.ToString(propData?["fname"]).Trim();
                    if (propName.IsNullOrEmptyOrWhiteSpace())
                    {
                        setNames.Clear();
                        break;
                    }

                    var propValueName = Convert.ToString(_setEntry["fvaluename"]).Trim();

                    setNames.Add($"{propName}:{propValueName}");
                }
                if (setNames.Any())
                {
                    setObj["fname"] = string.Join(",", setNames);
                    setObj["fname_e"] = strHeadName_e;
                    saveDatas.Add(setObj);

                    if (AttrExtObjs.Any())
                    {
                        var setObj_e = AttrExtObjs.Where(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(setObj["fattrinfo_e"]))).ToList();
                        if (setObj_e.Any())
                        {
                            setObj_e.ForEach(o =>
                            {
                                o["fnumber"] = string.Join(",", setNames);
                                o["fname"] = string.Join(",", setNames);
                                //固定名称内的属性名称也需要更新。
                                o["fname_e"] = strHeadName_e;
                            }); 
                            saveData_exts.AddRange(setObj_e);
                        }
                    }
                }
            }

            if (!saveDatas.Any()) return;
            //批次数
            var batchSize = 1000;
             //分批保存
            var bacheCount = decimal.ToInt32(Math.Ceiling(saveDatas.Count * 1.0m / batchSize));
            var batchIndex = 1;
            while (batchIndex <= bacheCount)
            {
                var batchObjs = saveDatas.Skip((batchIndex - 1) * batchSize).Take(batchSize).ToList();
                if (batchObjs.Count < 1) break;

                try
                {
                    this.AuxPropValSetDm.Save(batchObjs);
                }
                catch (Exception ex)
                {
                    var message = $"{this.HtmlForm.Caption}【fname - 属性名称】字段值手工修改保存时，分批关联更新辅助属性组合值时出现错误。";
                    this.LogServiceEx.Error(message, ex);
                }

                batchIndex++;

                //降低cpu占用，防止cpu过高
                Thread.Sleep(20);
            } 

            if (saveData_exts.Any())
            {
                var flexFormInfo_ext = this.Context.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bd_auxpropvalue_ext");
                var dm = this.Context.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, flexFormInfo_ext.GetDynamicObjectType(this.Context));
 
                var bacheCount_ext = decimal.ToInt32(Math.Ceiling(saveData_exts.Count * 1.0m / batchSize));
                var batchIndex_ext = 1;
                while (batchIndex_ext <= bacheCount_ext)
                {
                    var batchObjs = saveData_exts.Skip((batchIndex_ext - 1) * batchSize).Take(batchSize).ToList();
                    if (batchObjs.Count < 1) break;

                    try
                    {
                        dm.Save(saveData_exts);
                    }
                    catch (Exception ex)
                    {
                        var message = $"{this.HtmlForm.Caption}辅助属性扩展更新错误。";
                        this.LogServiceEx.Error(message, ex);
                    }

                    batchIndex_ext++;
                } 
            }
        }

        /// <summary>
        /// 加载所有属性
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public DynamicObject[] LoadAllAuxProperties(UserContext userCtx)
        {
            var auxPropMeta = this.MetaModelService.LoadFormModel(userCtx, "sel_prop");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, auxPropMeta.GetDynamicObjectType(userCtx));
            userCtx.UpdateMdlSchema(auxPropMeta.Id);

            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");

            var allAuxPropPkIdSql = $"select {auxPropMeta.HeadEntity.PkFieldName} from {auxPropMeta.HeadEntity.TableName} with(nolock) where {aclFilter} and {auxPropMeta.ForbidStatusFldKey}='0'";
            var allPkIds = this.DBService.ExecuteDynamicObject(userCtx, allAuxPropPkIdSql, null)
                .Select(o => o[0])
                .ToArray();

            return dm.Select(allPkIds).OfType<DynamicObject>().ToArray();
        }

        /// <summary>
        /// 根据属性加载辅助属性组合值ID
        /// </summary>
        private List<string> LoadAuxPropValueSetIdByPropId(string propId)
        {
            var sqlText = $"select distinct fid from t_bd_auxpropvalueentry with(nolock) where fauxpropid='{propId}'";

            var auxPropValIds = this.DBService.ExecuteDynamicObject(this.Context, sqlText)
                ?.Select(o => Convert.ToString(o["fid"]))
                ?.ToList();

            return auxPropValIds;
        }
    }
}