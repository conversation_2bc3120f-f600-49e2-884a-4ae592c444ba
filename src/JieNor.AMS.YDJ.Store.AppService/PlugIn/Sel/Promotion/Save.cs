using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework.Interface.Log;
using System.Threading;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.Promotion
{
    [InjectService]
    [FormId("bas_promotioncombine")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e) 
        {
            base.BeginOperationTransaction(e);
            //防止Excel 没设置开始时间、结束时间、捆绑类型 保存的时候根据活动 预生成
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            //加载引用数据
            this.Context.Container.GetService<LoadReferenceObjectManager>()?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);
            foreach (var item in e.DataEntitys)
            {
                var promotionObj = this.Context.LoadBizBillHeadDataById("bas_promotionscheme", item["fpromotionid"].ToString(), "fbegindate,fenddate,ftype,fdescription");

                if (item["fbegindate"].IsNullOrEmptyOrWhiteSpace())
                {
                    item["fbegindate"] = Convert.ToDateTime(promotionObj["fbegindate"]);
                }

                if (item["fenddate"].IsNullOrEmptyOrWhiteSpace())
                {
                    item["fenddate"] = Convert.ToDateTime(promotionObj["fenddate"]);
                }

                if (item["ftype"].IsNullOrEmptyOrWhiteSpace())
                {
                    item["ftype"] = Convert.ToString(promotionObj["ftype"]);
                }

                if (item["fpromotiondescription"].IsNullOrEmptyOrWhiteSpace())
                {
                    item["fpromotiondescription"] = Convert.ToString(promotionObj["fdescription"]);
                }
                var entrys = item["fcombineentry"] as DynamicObjectCollection;
                foreach (var entry in entrys) 
                {
                    entry["funitid"] = (entry["fproductid_ref"] as DynamicObject)?["funitid"];
                }
            }
        }
    }
}
