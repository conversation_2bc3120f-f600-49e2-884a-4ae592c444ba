using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.PropValue
{
    /// <summary>
    /// 属性值：根据属性值ID加载属性图片
    /// </summary>
    [InjectService]
    [FormId("sel_propvalue")]
    [OperationNo("LoadPropValueImage")]
    public class LoadPropEntrys : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var propValueId = this.GetQueryOrSimpleParam<string>("propValueId");
            if (propValueId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数 propValueId 为空，请检查！");
            }

            var propValueObj = this.Context.LoadBizBillHeadDataById(this.HtmlForm.Id, propValueId, "fimage,fimage_txt");
            if (propValueObj == null) return;

            this.Result.SrvData = new
            {
                id = Convert.ToString(propValueObj["fimage"]),
                name = Convert.ToString(propValueObj["fimage_txt"])
            };
            this.Result.IsSuccess = true;
        }
    }
}