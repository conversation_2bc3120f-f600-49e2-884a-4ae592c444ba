using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.Selection
{
    /// <summary>
    /// 采购订单：审核
    /// </summary>
    [InjectService]
    [FormId("sel_selectionform")]
    [OperationNo("showsuiteselection")]
    public class ShowSuiteSelection : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            var rowid = this.GetQueryOrSimpleParam<string>("rowid");
            var fissuitflag = this.GetQueryOrSimpleParam<string>("fissuitflag");
            var fsuitproductid = this.GetQueryOrSimpleParam<string>("fsuitproductid");
            var fsuitcombnumber = this.GetQueryOrSimpleParam<string>("fsuitcombnumber");
            var fproductid = this.GetQueryOrSimpleParam<string>("fproductid");
            var propidlist = this.GetQueryOrSimpleParam<string>("propidlist");
            var canchange = this.GetQueryOrSimpleParam<string>("canchange");

            //this.DBService.ExecuteDynamicObject(this.Context, )
            //套件信息
            var suite = this.Context.LoadBizDataByFilter("sel_suite", "fproductid='{0}'".Fmt(fproductid), true).FirstOrDefault();
            //选配类别
            //var cates = this.DBService.ExecuteDynamicObject(
            //    this.Context,
            //    "select * from "
            //);
            if(suite == null)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage?.ErrorMessages.Add("没有找到相应的套件信息");
                return;
            }

            var fentity = suite["fentity"] as DynamicObjectCollection;
            if (fentity == null)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage?.ErrorMessages.Add("没有找到套件的子件信息");
                return;
            }

            var sql = "select fselcategoryid from t_bd_material where fid in ('{0}')".Fmt(
                   fentity.Select(x => Convert.ToString(x["fpartproductid"])).JoinEx("','", false));
            var ids = this.DBService.ExecuteDynamicObject(this.Context, sql);
             
            var categories = this.Context.LoadBizDataById(
                "sel_category", ids.Select(x => Convert.ToString(x["fselcategoryid"])),true
                );
             
            var pc = new Dictionary<string, Dictionary<string, string>>();             
            var products = this.Context.LoadBizDataById(
                "ydj_product", fentity.Select(x => Convert.ToString(x["fpartproductid"])),true
                );

            var ps = new Dictionary<string, Dictionary<string, string>>();

            foreach (var p in products)
            {
                var c = p["fselcategoryid_ref"] as DynamicObject;
                var cs = new Dictionary<string, string>();
                var pid = Convert.ToString(p["Id"]);
                if (c != null)
                {
                    cs = new Dictionary<string, string> {
                        { "id", Convert.ToString( c["Id"])},
                        { "fname", Convert.ToString(c["fname"])},
                        { "fnumber", Convert.ToString(c["fnumber"])}
                    };
                }
                ps.Add(pid, new Dictionary<string, string> {
                        { "id", Convert.ToString( p["Id"])},
                        { "funstdtype", Convert.ToString(p["funstdtype"])}
                    });
                pc.Add(pid, cs);
            }

            List<DynamicObject> props = new List<DynamicObject>();
            if (!propidlist.IsNullOrEmptyOrWhiteSpace())
            {
                var propIdList = propidlist.Split(',');
                var propIds = propIdList.Distinct();
                if (propIds.Any())
                { 
                    sql = "select fid, fnumber, fname, fpropid, fnosuit from t_sel_propvalue where fid in ('{0}')".Fmt(propIds.JoinEx("','", false));
                    props = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                }
            }

            var propSuiteCount = this.Context.LoadBizDataByFilter("sel_prop", "fname = '套件总件数'").FirstOrDefault();

            var meta = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "sel_selectionform");
            var parentId = this.CurrentPageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : this.CurrentPageId;
            var action = this.Context.ShowSpecialForm(
                meta,
                null,
                true,
                parentId,
                Enu_OpenStyle.Modal,
                Enu_DomainType.Dynamic,
                null,
                (formPara) =>
                {
                    formPara.DomainType = Enu_DomainType.Dynamic;
                    formPara.OpenStyle = Enu_OpenStyle.Modal;
                    formPara.CustomParameter["rowid"] = rowid;
                    formPara.CustomParameter["parentpageid"] = parentId;
                    formPara.CustomParameter["suite"] = JsonConvert.SerializeObject(suite);
                    formPara.CustomParameter["categories"] = JsonConvert.SerializeObject(categories);
                    formPara.CustomParameter["productcategories"] = JsonConvert.SerializeObject(pc);
                    formPara.CustomParameter["products"] = JsonConvert.SerializeObject(ps);
                    formPara.CustomParameter["fsuitcombnumber"] = fsuitcombnumber;
                    formPara.CustomParameter["props"] = JsonConvert.SerializeObject(props);
                    formPara.CustomParameter["propsuitecount"] = propSuiteCount == null ? "" : JsonConvert.SerializeObject(propSuiteCount);
                    formPara.CustomParameter["canchange"] = canchange;
                }
            );
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage?.SuccessMessages.Clear();
            this.Result.HtmlActions.Add(action);

        }
    }
}
