using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.IoC;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.Selection
{
    /// <summary>
    /// 属性选配：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("sel_selectionform")]
    [OperationNo("FuzzyQueryDyn")]
    public class FuzzyQueryDyn : PropSelectionSingle.AbstractQueryDyn
    {
        //该插件功能逻辑在基类实现
    }
}