using JieNor.AMS.YDJ.Store.AppService.Model.PurchaseOrder;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.Selection
{
    /// <summary>
    /// 添加促销方案界面
    /// </summary>
    [InjectService]
    [FormId("sel_promotionform")]
    [OperationNo("addpromotion")]
    public class AddPromotion : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var fpromotionid = this.GetQueryOrSimpleParam<string>("fpromotionid");
            var billtypeNo = this.GetQueryOrSimpleParam<string>("billtypeNo");
            var billtypeName = this.GetQueryOrSimpleParam<string>("billtypeName");
            var deptId = this.GetQueryOrSimpleParam<string>("deptId");
            var deliverid = this.GetQueryOrSimpleParam<string>("deliverid");
            var deliverno = this.GetQueryOrSimpleParam<string>("deliverno");
            var delivername = this.GetQueryOrSimpleParam<string>("delivername");
            var supplierid = this.GetQueryOrSimpleParam<string>("supplierid");
            var supplierNo = this.GetQueryOrSimpleParam<string>("supplierNo");
            var supplierName = this.GetQueryOrSimpleParam<string>("supplierName");
            var orgid = this.GetQueryOrSimpleParam<string>("orgid");
            //if (string.IsNullOrWhiteSpace(deliverid))
            //{
            //    //
            //    this.Result.SimpleMessage = "请先选中送达方！";
            //    this.Result.SrvData = null;
            //    this.Result.IsSuccess = false;
            //    return;
            //}
            //获取活动信息
            var date = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd 00:00:00"));
            //取送达方和如果没有送单方要用组织去匹配
            var sql = $@" fbegindate<='{date}' and fenddate>='{date}' 
                          and fid in ( select b.fid from t_ydj_fcombinerangeentry a join t_bas_promotioncombine b on a.fid = b.fid  
                                       where fpromotionid='{fpromotionid}' and fdeliverid='{deliverid}' 
                                       union
                                       select distinct a.fid from t_bas_promotioncombine a with(nolock) 
                                       join t_ydj_fcombinerangeentry b with(nolock) on a.fid=b.fid 
                                       where fisall=1 and fpromotionid='{fpromotionid}' and b.fsaleorgid in ( select a.fsaleorgid from T_BAS_DELIVER a with(nolock)
                                       join t_bas_organization b with(nolock) on a.fsaleorgid=b.fid
                                       join T_BAS_AGENT c with(nolock) on a.fagentid=c.fid
                                       where  c.fid='{this.Context.Company}' and a.fforbidstatus=0
                                         union
                                       select a.fsaleorgid  from
	                                   T_BAS_DELIVER a with(nolock)
                                       join t_bas_organization b with(nolock) on a.fsaleorgid=b.fid
                                       join T_BAS_AGENT c with(nolock) on a.fagentid=c.fid
	                                   join t_bas_macentry e with(nolock) on c.fid=e.fsubagentid
	                                   join t_bas_mac d with(nolock) on e.fid=d.fid
	                                   join T_BAS_AGENT f with(nolock) on d.fnumber=f.fnumber
                                       where  f.fid='{this.Context.Company}' and a.fforbidstatus=0 
                                        )
                                    ) ";


            var combineDatas = this.Context.LoadBizDataByFilter("bas_promotioncombine", sql, true).ToList();
            if (combineDatas.Count() <= 0)
            {
                this.Result.SrvData = null;
                this.Result.IsSuccess = false;
                return;
            }

            var deliveryModels = GetDeliveryInfo(deliverid);
            if (deliveryModels == null || deliveryModels.Count <= 0)
            {
                this.Result.SrvData = null;
                this.Result.IsSuccess = false;
                return;
            }

            //需要过滤商品
            var authPara = new DataQueryRuleParaInfo() { SrcFormId = "ydj_purchaseorder" };
            authPara.SrcPara["billtypeNo"] = billtypeNo;
            authPara.SrcPara["billtypeName"] = billtypeName;
            authPara.SrcPara["deptId"] = deptId;
            authPara.SrcPara["deliverid"] = deliverid;
            authPara.SrcPara["deliverno"] = deliverno;
            authPara.SrcPara["delivername"] = delivername;
            authPara.SrcPara["supplierid"] = supplierid;
            authPara.SrcPara["supplierNo"] = supplierNo;
            authPara.SrcPara["supplierName"] = supplierName;
            var allProductView = this.Context.GetAuthProductDataPKID(authPara);
            //var dt = this.DBService.ExecuteDataTable(this.Context, allProductView);
            List<string> productids = new List<string>();
            List<DynamicObject> combine = new List<DynamicObject>();
            foreach (var combineData in combineDatas)
            {
                var combineentrys = combineData["fcombineentry"] as DynamicObjectCollection;
                var productid = combineentrys.Select(c => c["fproductid"].ToString()).ToList();
                productids.AddRange(productid);
            }
            string prod = "('" + string.Join("','", productids) + "')";
            var products = this.Context.LoadBizDataByFilter("ydj_product", $" fforbidstatus<>'1' and fid in {prod} and fid in ({allProductView})");
            foreach (var combineData in combineDatas)
            {
                List<string> combineprodentity = new List<string>();
                var combineentrys = combineData["fcombineentry"] as DynamicObjectCollection;
                var expireprod = new List<string>();
                //1 先查看套餐商品是否有过期时间
                foreach (var com in combineentrys)
                {
                    if (date < DateTime.Parse(com["fproductbegindate"].ToString()) || date > DateTime.Parse(com["fproductenddate"].ToString()))
                    {
                        //超出过期时间的，不符合的商品
                        expireprod.Add(com["fproductid"].ToString());
                    }
                }
                foreach (var item in combineentrys)
                {
                    if (products.Where(c => Convert.ToString(c["id"]).Equals(Convert.ToString(item["fproductid"]))).FirstOrDefault() == null)
                    {
                        //不在授权清单里的商品，不返回
                        combineprodentity.Add(Convert.ToString(item["fproductid"]));
                    }
                }
                //combineentrys.Where(a => products.Where(c => Convert.ToString(c["id"]).Equals(Convert.ToString(a["fproductid"]))).FirstOrDefault() != null).ToList();
                combineprodentity.AddRange(expireprod);
                var groupnumer = combineentrys.Select(c => c["fgroupnumber"].ToString()).Distinct();
                var addcombinetrys = new List<DynamicObject>();
                if (combineprodentity.Count() > 0)
                {
                    foreach (var item in combineprodentity)
                    {
                        var entity = combineentrys.Where(c => c["fproductid"].ToString() == item).FirstOrDefault();
                        //combineentrys.Remove(entity);
                        addcombinetrys.Add(entity);
                    }

                    var newgroupnumber = combineentrys.Where(c => !addcombinetrys.Select(m => m["fproductid"].ToString()).Contains(c["fproductid"].ToString())).Select(c => c["fgroupnumber"].ToString()).Distinct();

                    if (groupnumer.Count() > newgroupnumber.Count())
                    {
                        combine.Add(combineData);
                    }
                    foreach (var item in addcombinetrys)
                    {
                        combineentrys.Remove(item);
                    }
                }

            }
            //如果经销商看不到某些商品(没权限),则也需要过滤这个活动
            if (combine.Count() > 0)
            {
                combine.ForEach(c => combineDatas.Remove(c));
            }
            this.Result.SrvData = JsonConvert.SerializeObject(combineDatas);
            this.Result.IsSuccess = true;

        }

        public List<DeliveryModel> GetDeliveryInfo(string deliverid)
        {
            string strSql = @" select b.fserieid,c.fname,c.fnumber,isnull(d.fid,'') fbrandid,isnull(d.fname,'') fbrandname,isnull(d.fnumber,'') fbrandnumber from t_bas_deliver a with(nolock)
                              join t_bas_deliverentry b with(nolock) on a.fid=b.fid
                              left join t_ydj_series c with(nolock) on b.fserieid=c.fid
                              left join t_Ydj_Brand d with(nolock) on c.fbrandid=d.fid
                              where a.fid=@deliverid and b.fenable=1";

            var list = new List<DeliveryModel>();
            IDBService iDBService = this.Context.Container.GetService<IDBService>();
            using (var reader = iDBService.ExecuteReader(this.Context, strSql, new List<SqlParam>() { new SqlParam("@deliverid", System.Data.DbType.String, deliverid) }))
            {
                while (reader.Read())
                {
                    var model = new DeliveryModel
                    {
                        fserieid = reader.GetValueToString("fserieid"),
                        fname = reader.GetValueToString("fname"),
                        fnumber = reader.GetValueToString("fnumber"),
                        fbrandid = reader.GetValueToString("fbrandid"),
                        fbrandname = reader.GetValueToString("fbrandname"),
                        fbrandnumber = reader.GetValueToString("fbrandnumber")
                    };
                    list.Add(model);
                }
            }
            return list;
        }
    }
}
