using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sel.FittingsMap
{
    /// <summary>
    /// 选配配件映射：保存
    /// 作者;zpf
    /// 日期：2022-01-12
    /// </summary>
    [InjectService]
    [FormId("sel_fittingsmap")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            StringBuilder stringBuilder = new StringBuilder();

            /*保存时校验单据体-配件映射【条件】, 是否符合表达式语法解析要求, 如果不符合语法解析要求时报错并提示错误信息 (哪行哪种错误)*/        
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            e.Rules.Add(this.RuleFor("ffittingsmapentity", d => d).IsTrue((n, o) =>
            {
                var fconditions = n["fconditions"].IsNullOrEmptyOrWhiteSpace()?"": n["fconditions"].ToString();
                var isValidationPasses = true;
                if (!string.IsNullOrWhiteSpace(fconditions))
                {
                    var parseResults = StringFormatValidation.StringRegularParsing(this.Context, fconditions, out isValidationPasses);
                    if (!isValidationPasses)
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("【配件映射】明细行{0}【条件】格式有问题，参考格式“[属性名]=属性值”", (dy, dyObj) => dy["fseq"]));
        }
    }
}
