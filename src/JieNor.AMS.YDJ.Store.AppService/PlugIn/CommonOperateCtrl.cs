using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin
{
    /// <summary>
    /// 通用保存插件：如果业务单据或基础资料上有已作废字段，且为【已作废】状态，阻止操作
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("save")]
    public class CommonSaveCtrl : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            if ((this.HtmlForm.ElementType != HtmlElementType.HtmlForm_BillForm && this.HtmlForm.ElementType != HtmlElementType.HtmlForm_BaseForm)
                || this.HtmlForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace())
            {
                //只检查业务单据、基础资料
                return;
            }

            e.Rules.Insert(0, new CancelStatusValidation());
        }
    }

    /// <summary>
    /// 通用提交插件：如果业务单据或基础资料上有已作废字段，且为【已作废】状态，阻止操作
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("submit")]
    public class CommonSubmitCtrl : CommonSaveCtrl
    {
    }

    /// <summary>
    /// 通用审批流提交插件：如果业务单据或基础资料上有已作废字段，且为【已作废】状态，阻止操作
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("SubmitFlow")]
    public class CommonSubmitFlowCtrl : CommonSaveCtrl
    {
    }

    /// <summary>
    /// 通用撤销插件：如果业务单据或基础资料上有已作废字段，且为【已作废】状态，阻止操作
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("UnSubmit")]
    public class CommonUnSubmitCtrl : CommonSaveCtrl
    {
    }

    /// <summary>
    /// 通用审核插件：如果业务单据或基础资料上有已作废字段，且为【已作废】状态，阻止操作
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("Audit")]
    public class CommonAuditCtrl : CommonSaveCtrl
    {
    }

    /// <summary>
    /// 通用反审核插件：如果业务单据或基础资料上有已作废字段，且为【已作废】状态，阻止操作
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("UnAudit")]
    public class CommonUnAuditCtrl : CommonSaveCtrl
    {
    }

    /// <summary>
    /// 如果业务单据或基础资料上有已作废字段，且为【已作废】状态，阻止操作
    /// </summary>
    public class CancelStatusValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            var cancelFilds = formInfo.GetFieldList().Where(t => t.Id.EqualsIgnoreCase("fcancelstatus")).FirstOrDefault();
            if (cancelFilds.IsNullOrEmpty())
            {
                //没有作废状态字段的不检查
                return result;
            }

            var numFild = formInfo.NumberFldKey;

            foreach (var item in dataEntities)
            {
                var currCancelStatus = Convert.ToString(item[cancelFilds.Id]);
                if (currCancelStatus.EqualsIgnoreCase("1") || currCancelStatus.EqualsIgnoreCase("true"))
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = @"编号为【{0}】的{1}，作废状态为【已作废】，不允许进行此操作！".Fmt(item[numFild], formInfo.Caption),
                        DataEntity = item
                    });
                }
            }

            return result;
        }
    }
}
