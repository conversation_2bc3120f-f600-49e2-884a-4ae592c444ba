using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService;




namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.CostCalc
{

    /// <summary>
    /// 出入库成本计算
    /// </summary>
    [InjectService]
    [FormId("ydj_costcalculate")]
    [OperationNo("calccost")]
    public class StockCostCalc : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            var repeat = this.GetQueryOrSimpleParam<string>("repeat", "").ToLowerInvariant();
            if (repeat.EqualsIgnoreCase("1") || repeat.EqualsIgnoreCase("true"))
            {
                //关闭成本重算入口：2023-10-27 By liren
                //ReCalculateCost();
            }
            else
            {
                if (e.DataEntitys[0]["fenddate"].IsNullOrEmptyOrWhiteSpace())
                {
                    throw new Exception("成本计算月份不能为空，请选择要计算的月份！");
                }

                var svc = this.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
                var closedate = svc.GetNearClosePeriod(this.Context);
                if (closedate.HasValue)
                {
                    var endDate = Convert.ToDateTime(e.DataEntitys[0]["fenddate"]);
                    if (endDate.Year * 100 + endDate.Month <= closedate.Value.Year * 100 + closedate.Value.Month)
                    {
                        this.Result.IsSuccess = true;
                        this.Result.IsShowMessage = true;
                        this.Result.SimpleMessage = "成本计算失败";
                        this.Result.ComplexMessage.WarningMessages.Add("对不起，计算月份只能选择已关账月份{0}，及以后的月份。".Fmt(closedate.Value.ToString("yyyy-MM")));

                        return;
                    }
                }

                var para = new Core.DataEntity.CostCalculatePara()
                {
                    EndDate = Convert.ToDateTime(e.DataEntitys[0]["fenddate"]),
                    IsWriteLog = Convert.ToBoolean(e.DataEntitys[0]["fiswritelog"]),
                    StockIn = true,
                    StockOut = true,
                };
                if (!e.DataEntitys[0]["fbegindate"].IsNullOrEmptyOrWhiteSpace())
                {
                    para.BeginDate = Convert.ToDateTime(e.DataEntitys[0]["fbegindate"]);
                }


                try
                {
                    this.Result = svc.CostCalcByEnd(this.Context, para, this.Option);
                }
                catch (Exception ex)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "成本计算失败";
                    throw ex;
                }
            }

            this.AddRefreshPageAction();
        }



        /// <summary>
        /// 重算所有组织的成本信息
        /// </summary>
        private void ReCalculateCost()
        {
            var orgNo = this.GetQueryOrSimpleParam<string>("orgNo", "").ToLowerInvariant();
            var svc = this.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            if (orgNo.IsNullOrEmptyOrWhiteSpace())
            {
                //当前组织是经销商，则只更新当前经销商
                try
                {
                    this.Result = svc.CostCalcByEnd_Repeat(this.Context, this.Option);
                }
                catch (Exception ex)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "成本计算过程有错误发生";
                    this.Result.ComplexMessage.ErrorMessages.Add(ex.Message);
                }
            }
            else
            {
                //总部组织，则重算所有经销商的
                var dbInfo = this.GetAllCompanys().Values.FirstOrDefault(f => f.CompanyNumber == orgNo);
                if (dbInfo == null)
                {
                    return;
                }
                try
                {
                    var ctx = this.CreateContextByCompanyInfo(dbInfo);
                    this.Result = svc.CostCalcByEnd_Repeat(ctx, this.Option);
                }
                catch (Exception ex)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "成本计算过程有错误发生";
                    this.Result.ComplexMessage.ErrorMessages.Add(ex.Message);
                }
            }
        }
    }

}
