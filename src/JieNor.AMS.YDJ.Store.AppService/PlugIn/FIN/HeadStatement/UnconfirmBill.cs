using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Documents;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc.AdvancedAPIs;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.HeadStatement
{
    /// <summary>
    /// 总部对账单：对账不符
    /// </summary>
    [InjectService]
    [FormId("ydj_headstatement")]
    [OperationNo("UnconfirmBill")]
    public class UnconfirmBill : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string billStatus = Convert.ToString(newData["fbillstatus"]);
                return !billStatus.EqualsIgnoreCase("1") && !billStatus.EqualsIgnoreCase("2");

            }).WithMessage("【对账状态】={0}，不允许执行对账不符！",
                (newData, oldData) => this.HtmlForm.GetSimpleSelectItemText("fbillstatus", Convert.ToString(newData["fbillstatus"]))
                )
            );

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fremitagentremark = Convert.ToString(newData["fremitagentremark"]);
                var foutagentremark = Convert.ToString(newData["foutagentremark"]);
                var freturnagentremark = Convert.ToString(newData["freturnagentremark"]);
                var frepairagentremark = Convert.ToString(newData["frepairagentremark"]);
                var fdebitagentremark = Convert.ToString(newData["fdebitagentremark"]);
                var fcertagentremark = Convert.ToString(newData["fcertagentremark"]);

                return !fremitagentremark.IsNullOrEmptyOrWhiteSpace()
                       || !foutagentremark.IsNullOrEmptyOrWhiteSpace()
                       || !freturnagentremark.IsNullOrEmptyOrWhiteSpace()
                       || !frepairagentremark.IsNullOrEmptyOrWhiteSpace()
                       || !fdebitagentremark.IsNullOrEmptyOrWhiteSpace()
                       || !fcertagentremark.IsNullOrEmptyOrWhiteSpace();

            }).WithMessage("至少一个的明细的【经销商备注】不为空！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            var statementService = this.Container.GetService<IStatementService>();

            statementService.UnconfirmBill(this.Context, this.HtmlForm, e.DataEntitys);

            this.AddRefreshPageAction();
        }
    }
}
