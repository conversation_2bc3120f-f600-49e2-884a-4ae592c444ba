using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    /// <summary>
    /// 费用申请：审核
    /// </summary>
    [InjectService]
    [FormId("ste_registfee")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            var isCheck = this.GetQueryOrSimpleParam("ischeckbill", "true");
            var ischeckbill = isCheck.IsNullOrEmptyOrWhiteSpace() ? true : Convert.ToBoolean(isCheck);
            
            e.Rules.Add(new Validation_Check(ischeckbill));
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //反写销售合同的实付佣金
            writeBackSourceBill(e.DataEntitys);
        }

        /// <summary>
        /// 反写销售合同的实付佣金
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void writeBackSourceBill(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0) return;

            //检查是否已开启【合作渠道按比例计算佣金】参数
            var profileService = this.Container.GetService<ISystemProfile>();
            var enableBrokerageRatio = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablebrokerageratio", false);
            //如果没有开启，走原有反写逻辑
            if (false == enableBrokerageRatio)
            {
                UpdateSourceBillInfo(dataEntitys);
                return;
            }
            //如果开启，按往来单位类型分组
            //往来单位是合作渠道的分组
            var channelList = dataEntitys.Where(x => Convert.ToString(x["frelatetype"]).EqualsIgnoreCase("ste_channel")).ToList();
            //往来单位不是合作渠道的分组
            var notChannelList = dataEntitys.Where(x => false == Convert.ToString(x["frelatetype"]).EqualsIgnoreCase("ste_channel"))
                                            .ToArray();
            //往来单位不是合作渠道的分组走原有的反写逻辑
            if (notChannelList != null && notChannelList.Length > 0)
            {
                UpdateSourceBillInfo(dataEntitys);
            }
            //往来单位是合作渠道的分组反写明细行的金额到明细行的相联销售合同
            if (channelList == null || channelList.Count <= 0)
            {
                return;
            }
            var orderIds = channelList.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                                      .Where(x => Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order"))
                                      .Select(x => Convert.ToString(x["fsourceinterid"]))
                                      .Where(x=>false==string.IsNullOrWhiteSpace(x))
                                      .Distinct()
                                      .ToList();

            if (orderIds == null || orderIds.Count <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_order");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var orderEntities = dm.Select(orderIds).OfType<DynamicObject>().ToList();
            if (orderEntities == null || orderEntities.Count <= 0)
            {
                return;
            }

            foreach(var dataEntity in dataEntitys)
            {
                var entries = dataEntity["fentry"] as DynamicObjectCollection;
                if (entries == null || entries.Count <= 0)
                {
                    continue;
                }

                foreach(var entry in entries)
                {
                    if (false == Convert.ToString(entry["fsourceformid"]).EqualsIgnoreCase("ydj_order"))
                    {
                        continue;
                    }
                    var orderId = Convert.ToString(entry["fsourceinterid"]);
                    if (string.IsNullOrWhiteSpace(orderId))
                    {
                        continue;
                    }
                    var orderEntity = orderEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(orderId));
                    if (orderEntity == null)
                    {
                        continue;
                    }
                    orderEntity["fbrokerage"] = entry["ftaxamount"];
                }
            }

            dm.Save(orderEntities);
        }

        /// <summary>
        /// 更新源单信息（销售合同）
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void UpdateSourceBillInfo(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0) return;

            foreach (var dataEntity in dataEntitys)
            {
                var sourceType = Convert.ToString(dataEntity["fsourcetype"]);
                var sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
                if (!sourceType.IsNullOrEmptyOrWhiteSpace() && !sourceNumber.IsNullOrEmptyOrWhiteSpace() && sourceType.EqualsIgnoreCase("ydj_order"))
                {
                    var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, sourceType);

                    var dm = this.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                    string where = $"fmainorgid=@fmainorgid and {htmlForm.NumberFldKey}=@fnumber";
                    var sqlParam = new SqlParam[]
                    {
                            new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                            new SqlParam("fnumber", System.Data.DbType.String, sourceNumber.Trim())
                    };
                    var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
                    var sourceBillData = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
                    if (sourceBillData != null)
                    {
                        sourceBillData["fbrokerage"] = dataEntity["fsumtaxamount"];
                        dm.Save(sourceBillData);
                    }
                }
            }
        }
    }
}
