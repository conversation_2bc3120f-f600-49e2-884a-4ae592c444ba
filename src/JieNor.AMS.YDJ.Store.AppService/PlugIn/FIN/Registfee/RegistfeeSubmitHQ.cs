using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.Registfee
{
    /// <summary>
    /// 费用应付单同步中台
    /// </summary>
    [InjectService]
    [FormId("ste_registfee")]
    [OperationNo("syncregistfeesubmithq")]
    public class RegistfeeSubmitHQ : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            var companyIds = e.DataEntitys.Select(o => Convert.ToString(o["fmainorgid"])).Distinct().ToList();
            var agents = this.Context.LoadBizBillHeadDataById("bas_agent", companyIds, "fid,fmanagemodel");

            //直营经销商
            var directSaleAgentIds = agents.Where(o => Convert.ToString(o["fmanagemodel"]) == "1")
                .Select(x => Convert.ToString(x["fid"])).ToList();

            var registfee = e.DataEntitys
                .Where(x => directSaleAgentIds.Contains(Convert.ToString(x["fmainorgid"]))).ToList();

            this.Result.IsSuccess = true;
            if (registfee.Count() > 0)
            {
                Sync(registfee);
                this.Result.SimpleMessage = "费用应付单已提交至慕思总部！";
            }
            else
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "无费用应付单可提交至总部！";
            }

            this.AddRefreshPageAction();
        }

        /// <summary>
        /// 同步费用应付单到中台
        /// </summary>
        /// <param name="registfeeObj">销售合同</param>
        private void Sync(IEnumerable<DynamicObject> registfeeObj)
        {
            var fieldMapObjs = this.Container.GetService<IMuSiBizObjMapService>()
                .GetBizObjMaps(this.Context, this.HtmlForm, Enu_MuSiSyncDir.CurrentToMuSi,
                    Enu_MuSiSyncTimePoint.SyncManual, "fname='费用应付单同步'");

            if (fieldMapObjs.IsNullOrEmpty())
            {
                throw new BusinessException($"未配置{this.HtmlForm.Caption} 的同步配置！");
            }

            IMuSiService muSiService = this.Container.GetService<IMuSiService>();
            foreach (var obj in registfeeObj)
            {
                try
                {
                    obj["fsubmitsharetime"] = DateTime.Now;
                    obj["fcollaborativesharestatus"] = 0;
                    //同步到中台
                    muSiService.SyncData(this.Context, this.HtmlForm, new DynamicObject[] { obj },"ste_registfee","费用应付单同步");
                }
                catch (Exception e)
                {
                    obj["fsyncmessage"] = e.Message;
                }
            }
            
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(registfeeObj);
        }
    }
}