using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Extensions;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
{
    /// <summary>
    /// 收支记录：反审核校验器
    /// </summary>
    public class UnAuditValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fpurpose"]).EqualsIgnoreCase("bizpurpose_04"))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"{formInfo.Caption}【{dataEntity["fbillno"]}】的用途是“红冲”，不允许反审核！",
                        DataEntity = dataEntity
                    });
                }
            }

            this.CheckOrderAllowUnAudit(userCtx, formInfo, dataEntities);

            //校验 是否焕新订单
            this.CheckOrderIsReNew(userCtx, formInfo, dataEntities);

            //校验【收款单】上游销售合同【确认收款】是否小于 销售合同的【订单总额】x【单据类型.自定义参数.销售合同允许XX的最低金额比例】
            this.CheckOrderAmountLimit(userCtx, formInfo, dataEntities);

            // 检查销售合同是否超额收款
            this.CheckOrderIsExcessReceipt(userCtx, formInfo, dataEntities);

            return this.Result;
        }

        private void CheckOrderIsReNew(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities)
        {
            //已提交、已审核状态的【收款单】才校验
            var validDataEntitys = dataEntities?.Where(x =>
             Convert.ToString(x["fsourceformid"]) == "ydj_order" &&
             Convert.ToString(x["fbizdirection"]) == "bizdirection_01"
            );
            if (validDataEntitys == null || !validDataEntitys.Any()) return;

            var validInfo = from b in validDataEntitys
                            select new
                            {
                                dataEntity = b,
                                isrenewalflag = Convert.ToString(b["frenewalflag"]),
                                billno = Convert.ToString(b["fbillno"])
                            };

            Dictionary<string, decimal> ordMapReceivable = new Dictionary<string, decimal>();

            foreach (var item in validInfo)
            { 
                if (item.isrenewalflag.EqualsIgnoreCase("1") || item.isrenewalflag.ToLower().EqualsIgnoreCase("true"))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"收款单{item.billno}属于焕新订单，不允许<反审核>操作，谢谢！",
                        DataEntity = item.dataEntity
                    });
                    continue;
                }
            }
        }

        /// <summary>
        /// 校验【收款单】上游销售合同【确认收款】是否小于 
        /// 销售合同的【订单总额】x【单据类型.自定义参数.销售合同允许XX的最低金额比例】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderAmountLimit(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities)
        {
            //已提交、已审核状态的【收款单】才校验
            var validDataEntitys = dataEntities?.Where(x =>
             Convert.ToString(x["fsourceformid"]) == "ydj_order" &&
             Convert.ToString(x["fbizdirection"]) == "bizdirection_01"
            );
            if (validDataEntitys == null || !validDataEntitys.Any()) return;

            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var orderIds = validDataEntitys.Select(x => Convert.ToString(x["fsourceid"])).ToList();
            var validStatus = new string[] {"D","E" };
            var orderInfo = userCtx.LoadBizBillHeadDataById(orderForm.Id, orderIds, "fbilltype,fstatus,freceivable,fsumamount")?.Where(x=> validStatus.Contains(Convert.ToString(x["fstatus"])));
            if (orderInfo == null || !orderInfo.Any()) return;

            var billTypeService = userCtx.Container.GetService<IBillTypeService>();
            var paramSetObjs = billTypeService.GetBillTypeParamSet(this.Context, orderForm, new HashSet<string>(from x in orderInfo select Convert.ToString(x["fbilltype"])));

            var validInfo=from a in orderInfo
                          join b in validDataEntitys on Convert.ToString(a["id"]) equals Convert.ToString(b["fsourceid"])
                          select new { 
                              dataEntity =b, 
                              receivable = Convert.ToDecimal(a["freceivable"]),
                              sumamount = Convert.ToDecimal(a["fsumamount"]),
                              status = Convert.ToString(a["fstatus"]),
                              billtype= Convert.ToString(a["fbilltype"]),
                              orderid = Convert.ToString(a["id"])
                          };

            Dictionary<string, decimal> ordMapReceivable = new Dictionary<string, decimal>();

            foreach (var item in validInfo)
            {
                decimal minpercentage = 0;
                var paramSetObj = paramSetObjs[item.billtype];
                //if (item.status.EqualsIgnoreCase("E")) 
                //{
                //    decimal.TryParse(Convert.ToString(paramSetObj?["fauditminpercentage"]), out minpercentage);
                //}
                //else if (item.status.EqualsIgnoreCase("D"))
                //{
                    decimal.TryParse(Convert.ToString(paramSetObj?["fsubmitminpercentage"]), out minpercentage);
                //}

                var receivable = item.receivable;
                if (ordMapReceivable.ContainsKey(item.orderid))
                {
                    receivable = ordMapReceivable[item.orderid];
                }
                if (receivable - Convert.ToDecimal(item.dataEntity["famount"]) < item.sumamount* minpercentage / 100)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"对不起，反审核收款单将导致上游销售合同【确认收款】金额未达到销售合同允许提交的最低金额比例，请先{(item.status.EqualsIgnoreCase("E")?"反审核": "撤销")}销售合同后再执行。",
                        DataEntity = item.dataEntity
                    });
                    continue;
                }
                else
                {
                    decimal.TryParse(Convert.ToString(paramSetObj?["fauditminpercentage"]), out minpercentage);
                    if (receivable - Convert.ToDecimal(item.dataEntity["famount"]) < item.sumamount * minpercentage / 100)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"对不起，反审核收款单将导致上游销售合同【确认收款】金额未达到销售合同允许提交的最低金额比例，请先{(item.status.EqualsIgnoreCase("E") ? "反审核" : "撤销")}销售合同后再执行。",
                            DataEntity = item.dataEntity
                        });
                    }
                }
                //避免同时反审核一个合同下多笔收支记录的情况
                if (!ordMapReceivable.ContainsKey(item.orderid))
                {
                    ordMapReceivable.Add(item.orderid, item.receivable - Convert.ToDecimal(item.dataEntity["famount"]));
                }
                else
                {
                    ordMapReceivable[item.orderid] = ordMapReceivable[item.orderid] - Convert.ToDecimal(item.dataEntity["famount"]);
                }
            }
        }

        /// <summary>
        /// 检查销售合同是否超额收款
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderIsExcessReceipt(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities)
        {
            // 查找退款收支记录的销售合同ID
            var orderIds = dataEntities
                .Where(x =>
                {
                    return Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                        && Convert.ToString(x["fpurpose"]).EqualsIgnoreCase("bizpurpose_06")
                        && !x["fsourceid"].IsNullOrEmptyOrWhiteSpace();
                })
                .Select(x => Convert.ToString(x["fsourceid"]))
                .Distinct()
                .ToList();
            if (!orderIds.Any()) return;

            // 获取是否可以超额收款参数
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            var canexcess = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanexcess", false);
            if (canexcess) return;

            // 如果未勾选超额收款参数，则需要校验单据的收款总金额
            var orderDynObjs = userCtx.LoadBizBillHeadDataById("ydj_order", orderIds, "fbillno,fsumamount");
            if (orderDynObjs == null || !orderDynObjs.Any()) return;

            // 批量加载销售合同关联的收支金额
            var sourceReceiptRefunds = IncomeDisburseHelper.LoadOrderReceiptRefundAmount(
                this.Context,
                "ydj_order",
                orderIds,
                null,
                new List<string>
                {
                    IncomeTypeConsts.receiptConfirmed,
                    IncomeTypeConsts.refundConfirmed
                });

            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                    && Convert.ToString(dataEntity["fpurpose"]).EqualsIgnoreCase("bizpurpose_06"))
                {
                    var sourceId = Convert.ToString(dataEntity["fsourceid"]);
                    var orderDynObj = orderDynObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(sourceId));
                    var orderNo = Convert.ToString(orderDynObj?["fbillno"]);
                    var sumAmount = Convert.ToDecimal(orderDynObj?["fsumamount"] ?? 0); // 订单总额

                    var receiptConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptConfirmed);
                    var refundConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.refundConfirmed);

                    // 与当前收支记录同属一个单据的所有退款收支记录金额汇总
                    var refundSum = dataEntities
                        .Where(o =>
                        {
                            // 必须是统计已审核状态的收支记录，因为不是已审核的不会被反审核
                            return Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_06")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId)
                                && Convert.ToString(o["fstatus"]).EqualsIgnoreCase("E");
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();

                    var _receiptSum = (receiptConfirmedSum - refundConfirmedSum) + refundSum;
                    if (_receiptSum > sumAmount)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"销售合同【{orderNo}】的收款总额 {_receiptSum.Format()} 已大于订单总额 {sumAmount.Format()}，当前系统设置为不允许超额收款！",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检查收款单已确认是否可以反审核
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderAllowUnAudit(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities) {
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            // 合同有出库或采购且已确认收款允许反审核
            var canUnAudit = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fordercounteraudit", false);
            if (canUnAudit) {
                return;
            }
            //业务状态是已确认的【收款单】才校验
            var validDataEntitys = dataEntities?.Where(x =>
             Convert.ToString(x["fsourceformid"]) == "ydj_order" &&
             Convert.ToString(x["fbizdirection"]) == "bizdirection_01"&&
             Convert.ToString(x["fbizstatus"])== "bizstatus_02"
            );
            if (validDataEntitys == null || !validDataEntitys.Any()) return;
         
            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var orderIds = validDataEntitys.Select(x => Convert.ToString(x["fsourceid"])).ToList();
            var orderInfo = userCtx.LoadBizBillHeadDataById(orderForm.Id, orderIds, "fbillno");
            if (orderInfo == null || !orderInfo.Any()) return;
            var purstrSql = $@"select distinct tb0.fid,tb1.fsourceinterid from t_ydj_purchaseorder tb0 with(nolock)  
                                      inner join t_ydj_poorderentry tb1 with(nolock) on tb0.fid=tb1.fid  where tb0.fcancelstatus='0' and  tb1.fsourceinterid  in ({orderIds.JoinEx(",", true)})";
            var strSql = $@"select distinct fid,fsourceinterid from t_stk_sostockout with(nolock) where fcancelstatus='0' and  fsourceinterid  in ({orderIds.JoinEx(",", true)})";
            var purOrder = userCtx.ExecuteDynamicObject(purstrSql,null);
            var sostockout = userCtx.ExecuteDynamicObject(strSql, null);
            var validInfo = from a in orderInfo
                            join b in validDataEntitys on Convert.ToString(a["id"]) equals Convert.ToString(b["fsourceid"])
                            select new
                            {
                                dataEntity = b,
                                status = Convert.ToString(b["fstatus"]),
                                orderid = Convert.ToString(a["id"])
                            };
            foreach (var item in validInfo)
            {
                var purOrderCount = purOrder.Where(p => Convert.ToString(p["fsourceinterid"]) == item.orderid).Count();
                var sostockoutCount = sostockout.Where(p => Convert.ToString(p["fsourceinterid"]) == item.orderid).Count();
                if ((purOrderCount > 0 || sostockoutCount > 0) && item.status == "E") {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"对不起，当前订单存在出库或采购且已确认收款，禁止反审核！如有需要，请删除下游《销售出库单》或《采购订单》，或者请做《收款退款单》！",
                        DataEntity = item.dataEntity
                    });
                    continue;
                }
            }
        }
    }
}