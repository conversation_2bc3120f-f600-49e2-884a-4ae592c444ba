//using System;
//using System.Linq;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.Framework.CustomException;
//using JieNor.AMS.YDJ.DataTransferObject.Enums;
//using JieNor.Framework.MetaCore.FormMeta;
//using Newtonsoft.Json.Linq;
//using JieNor.Framework.SuperOrm.Serialization;
//using System.Collections.Generic;
//using JieNor.Framework.MetaCore.FormOp.FormService;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
//{
//    /// <summary>
//    /// 收支记录：协同确认，接收协同方提交过来的确认请求
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("ConfirmSynergy")]
//    public class ConfirmSynergy : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 调用操作事物前触发的事件
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            var tranId = this.GetQueryOrSimpleParam<string>("tranId");
//            if (tranId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"交易流水号为空，请检查！");

//            //收支记录
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

//            string where = $"fmainorgid=@fmainorgid and ftranid=@ftranid";
//            var sqlParam = new SqlParam[]
//            {
//                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("ftranid", System.Data.DbType.String, tranId)
//            };
//            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParam);
//            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
//            //如果收支记录不存在，则检查确认方是否传递收支记录的数据过来
//            dataEntity = checkIncomeDisburse(dataEntity, tranId);
//            if (dataEntity == null) throw new BusinessException($"收支记录不存在，请检查！");

//            //检查是否已确认过
//            if (Convert.ToString(dataEntity["fbizstatus"]).EqualsIgnoreCase("bizstatus_02")) return;

//            //反写本地订单数据
//            this.UpdateOrderInfo(dataEntity);

//            //协同账户余额服务
//            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();

//            //更新收支记录
//            dataEntity["fbizstatus"] = "bizstatus_02";
//            dataEntity["fbalance"] = synAccountBalanceService.GetAccountBalance(this.Context, dataEntity);
//            dataEntity["fmodifierid"] = this.Context.UserId;
//            dataEntity["fmodifydate"] = DateTime.Now;
//            dataEntity["fconfirmorid"] = this.Context.UserId;
//            dataEntity["fconfirmdate"] = DateTime.Now;
//            dm.Save(dataEntity);

//            //更新账户余额
//            synAccountBalanceService.UpdateAccountBalance(this.Context, dataEntity);

//            //调用服务生操作日志
//            var syncService = this.Container.GetService<ISynergyService>();
//            syncService.WriteLog(this.Context, this.HtmlForm, dataEntity["id"] as string, "confirm", "确认", this.Context.CallerContext);

//            //在对应的业务单据上面也需要记录一笔动态
//            var linkBillId = dataEntity["fsourceid"] as string;
//            var linkFormId = dataEntity["fsourceformid"] as string;
//            if (!linkBillId.IsNullOrEmptyOrWhiteSpace() &&
//                (linkFormId.EqualsIgnoreCase("ydj_purchaseorder") || linkFormId.EqualsIgnoreCase("ydj_saleintention")))
//            {
//                var linkForm = this.MetaModelService.LoadFormModel(this.Context, linkFormId);
//                if (linkForm != null)
//                {
//                    syncService.WriteLog(this.Context, linkForm, linkBillId, "setttlefirm", "结算确认", this.Context.CallerContext);
//                }
//            }

//            this.Result.SimpleMessage = "确认成功！";
//            this.Result.IsSuccess = true;
//        }

//        /// <summary>
//        /// 如果收支记录不存在，则检查确认方是否传递收支记录的数据过来
//        /// </summary>
//        /// <param name="dataEntity"></param>
//        /// <param name="tranId"></param>
//        /// <returns></returns>
//        private DynamicObject checkIncomeDisburse(DynamicObject dataEntity, string tranId)
//        {
//            //如果收支记录存在，则直接返回当前收支记录
//            if (dataEntity != null)
//            {
//                return dataEntity;
//            }
//            //如果收支记录存在不存在，则检查确认方是否传递收支记录过来
//            var billDataString = this.GetQueryOrSimpleParam<string>("billDatas");
//            if (string.IsNullOrWhiteSpace(billDataString))
//            {
//                return null;
//            }
//            var billDatas = JArray.Parse(billDataString);
//            if (billDatas == null || billDatas.Count <= 0)
//            {
//                return null;
//            }
//            var dataEntities = new List<DynamicObject>();
//            var dcSerializer = this.Container.GetService<IDynamicSerializer>();
//            dcSerializer.Sync(this.HtmlForm.GetDynamicObjectType(this.Context), dataEntities, billDatas, (propKey) =>
//            {
//                var el = this.HtmlForm?.GetElement(propKey);
//                if (el is HtmlField) return (el as HtmlField).DynamicProperty;
//                if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;

//                return null;
//            },
//            null,
//            null,
//            null);
//            dataEntities = dataEntities.Where(x => Convert.ToString(x["ftranid"]).EqualsIgnoreCase(tranId)).ToList();
//            if (dataEntities == null || dataEntities.Count <= 0)
//            {
//                return null;
//            }
//            var chainDataJson = this.GetQueryOrSimpleParam<string>("chainDataJson");
//            var option = new Dictionary<string, object> { { "chainDataJson", chainDataJson } };
//            var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, dataEntities, "SaveSynergy", option);
//            result?.ThrowIfHasError(true, $"协同保存{this.HtmlForm.Caption}失败!");
//            return dataEntities.FirstOrDefault();
//        }

//        /// <summary>
//        /// 反写本地订单数据
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        private void UpdateOrderInfo(DynamicObject incomeDisburse)
//        {
//            //如果源单不是“采购订单”或“销售订单”，则无需反写源单数据
//            if (!Convert.ToString(incomeDisburse["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder")
//                && !Convert.ToString(incomeDisburse["fsourceformid"]).EqualsIgnoreCase("ydj_saleintention")
//                && !Convert.ToString(incomeDisburse["fsourceformid"]).EqualsIgnoreCase("ydj_order")) return;

//            var fsourceid = incomeDisburse["fsourceid"] as string;
//            var fsourceformid = incomeDisburse["fsourceformid"] as string;
//            var famount = Convert.ToDecimal(incomeDisburse["famount"]);
//            //账户收支方向
//            var direction = Convert.ToString(incomeDisburse["fdirection"]).Trim().ToLower();
//            //用途
//            var purpose = Convert.ToString(incomeDisburse["fpurpose"]).Trim().ToLower();
//            //是否协同
//            var isSyn = Convert.ToBoolean(incomeDisburse["fissyn"]);
//            //运营模式
//            var operationMode = Convert.ToString(incomeDisburse["foperationmode"]).Trim();

//            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, fsourceformid);
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

//            var sourceOrder = dm.Select(fsourceid) as DynamicObject;
//            if (sourceOrder != null)
//            {
//                switch (fsourceformid.Trim().ToLower())
//                {
//                    case "ydj_purchaseorder":
//                        #region 采购订单
//                        //用途是“订单付款”
//                        if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                        {
//                            //累加已结算金额
//                            sourceOrder["fpaidamount"] = Convert.ToDecimal(sourceOrder["fpaidamount"]) + famount;

//                            //扣减待确认金额
//                            sourceOrder["fconfirmamount"] = Convert.ToDecimal(sourceOrder["fconfirmamount"]) - famount;
//                        }

//                        //用途是“红冲”
//                        else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                        {
//                            //扣减已结算金额
//                            sourceOrder["fpaidamount"] = Convert.ToDecimal(sourceOrder["fpaidamount"]) - famount;

//                            //增加待结算金额
//                            sourceOrder["fpayamount"] = Convert.ToDecimal(sourceOrder["fpayamount"]) + famount;
//                        }

//                        //订单金额
//                        var purbillamount = Convert.ToDecimal(sourceOrder["ffbillamount"]);

//                        //已结算金额
//                        var fpaidamount = Convert.ToDecimal(sourceOrder["fpaidamount"]);

//                        //结算状态
//                        if (fpaidamount == 0)
//                        {
//                            //全款未付
//                            sourceOrder["fpaystatus"] = "paystatus_type_01";
//                        }
//                        else if (fpaidamount < purbillamount)
//                        {
//                            //部分付款
//                            sourceOrder["fpaystatus"] = "paystatus_type_02";
//                        }
//                        else if (fpaidamount == purbillamount)
//                        {
//                            //全款已付
//                            sourceOrder["fpaystatus"] = "paystatus_type_03";
//                        }
//                        #endregion
//                        break;

//                    case "ydj_saleintention":
//                        #region 销售意向单
//                        if (isSyn)
//                        {
//                            if (operationMode.EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == true)
//                            {
//                                #region 总部直营模式（博领）
//                                //用途是“订单付款”
//                                if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                                {
//                                    var sysProfile = this.Container.GetService<ISystemProfile>();
//                                    var enableCollectAmount = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fenablecollectamount", false);
//                                    //如果未启用销售意向定金启用应收控制,允许超额收款
//                                    if (enableCollectAmount && (famount > Convert.ToDecimal(sourceOrder["fcollectamount"]) - Convert.ToDecimal(sourceOrder["fcollectedamount"])))
//                                    {
//                                        throw new BusinessException($"（本次收定额 + 已收定金）不允许大于应收定金！");
//                                    }
//                                    //增加已收定金
//                                    sourceOrder["fcollectedamount"] = Convert.ToDecimal(sourceOrder["fcollectedamount"]) + famount;
//                                }
//                                //用途是“红冲”
//                                else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                                {
//                                    switch (direction)
//                                    {
//                                        case "direction_01":
//                                            //扣减已收定金
//                                            sourceOrder["fcollectedamount"] = Convert.ToDecimal(sourceOrder["fcollectedamount"]) - famount;
//                                            break;
//                                        case "direction_02":
//                                            //增加已收定金
//                                            sourceOrder["fcollectedamount"] = Convert.ToDecimal(sourceOrder["fcollectedamount"]) + famount;
//                                            break;
//                                        default:
//                                            break;
//                                    }
//                                }
//                                //用途是“退款”
//                                else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//                                {
//                                    if (famount > Convert.ToDecimal(sourceOrder["fcollectedamount"]))
//                                    {
//                                        throw new BusinessException($"退款金额不能大于已收定金！");
//                                    }
//                                    //扣减已收定金
//                                    sourceOrder["fcollectedamount"] = Convert.ToDecimal(sourceOrder["fcollectedamount"]) - famount;
//                                }
//                                #endregion
//                            }
//                            else
//                            {
//                                #region 购销协同模式（科凡）
//                                //用途是“订单付款”
//                                if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                                {
//                                    //增加已结算金额
//                                    sourceOrder["freceivedamount"] = Convert.ToDecimal(sourceOrder["freceivedamount"]) + famount;

//                                    //扣减待确认金额
//                                    sourceOrder["fconfirmamount"] = Convert.ToDecimal(sourceOrder["fconfirmamount"]) - famount;
//                                }

//                                //用途是“红冲”
//                                else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                                {
//                                    switch (direction)
//                                    {
//                                        case "direction_01":
//                                            //扣减已结算金额
//                                            sourceOrder["freceivedamount"] = Convert.ToDecimal(sourceOrder["freceivedamount"]) - famount;
//                                            //增加待结算金额
//                                            sourceOrder["freceiptamount"] = Convert.ToDecimal(sourceOrder["freceiptamount"]) + famount;
//                                            break;
//                                        case "direction_02":
//                                            //增加已结算金额
//                                            sourceOrder["freceivedamount"] = Convert.ToDecimal(sourceOrder["freceivedamount"]) + famount;
//                                            //扣减待结算金额
//                                            sourceOrder["freceiptamount"] = Convert.ToDecimal(sourceOrder["freceiptamount"]) - famount;
//                                            break;
//                                        default:
//                                            break;
//                                    }
//                                }

//                                //订单金额
//                                var salbillamount = Convert.ToDecimal(sourceOrder["ffbillamount"]);

//                                //已结算金额
//                                var freceivedamount = Convert.ToDecimal(sourceOrder["freceivedamount"]);

//                                //结算状态
//                                if (freceivedamount == 0)
//                                {
//                                    //全款未收
//                                    sourceOrder["freceiptstatus"] = "receiptstatus_type_01";
//                                }
//                                else if (freceivedamount < salbillamount)
//                                {
//                                    //部分付款
//                                    sourceOrder["freceiptstatus"] = "receiptstatus_type_02";
//                                }
//                                else if (freceivedamount == salbillamount)
//                                {
//                                    //全款已付
//                                    sourceOrder["freceiptstatus"] = "receiptstatus_type_03";
//                                }
//                                #endregion
//                            }
//                        }
//                        #endregion
//                        break;

//                    case "ydj_order":
//                        #region 销售合同

//                        //获取是否可以超额收款参数
//                        var profileService = this.Container.GetService<ISystemProfile>();
//                        string stockParamJson = profileService.GetProfile(this.Context, "fw", "bas_storesysparam_parameter");
//                        JObject stockParam = null;
//                        if (!string.IsNullOrWhiteSpace(stockParamJson))
//                        {
//                            stockParam = JObject.Parse(stockParamJson);
//                        }
//                        var fcanexcess = false;
//                        var property = stockParam?.Property("fcanexcess");
//                        if (property != null)
//                        {
//                            fcanexcess = (bool)property.Value;
//                        }

//                        //用途是“订单付款”
//                        if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                        {
//                            if (!fcanexcess && famount > Convert.ToDecimal(sourceOrder["fsumreceivable"]) - Convert.ToDecimal(sourceOrder["freceivable"]))
//                            {
//                                throw new BusinessException($"当前收支记录的待确认金额不能大于销售合同的待确认金额！");
//                            }
//                            // 增加【已收金额】
//                            sourceOrder["freceivable"] = Convert.ToDecimal(sourceOrder["freceivable"]) + famount;
//                            // 扣除【收款待确认】
//                            sourceOrder["freceivabletobeconfirmed"] =
//                                Convert.ToDecimal(sourceOrder["freceivabletobeconfirmed"]) - famount;
//                        }
//                        //用途是“红冲”
//                        else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                        {
//                            decimal freducedbrokerage = Convert.ToDecimal(incomeDisburse["freducedbrokerage"]);

//                            switch (direction)
//                            {
//                                case "direction_01":
//                                    //扣减已收金额
//                                    sourceOrder["freceivable"] = Convert.ToDecimal(sourceOrder["freceivable"]) - famount;
//                                    //扣减收款金额
//                                    sourceOrder["fsumreceivable"] = Convert.ToDecimal(sourceOrder["fsumreceivable"]) - famount;
//                                    //扣减已扣佣金
//                                    sourceOrder["freducedbrokerage"] = Convert.ToDecimal(sourceOrder["freducedbrokerage"]) - freducedbrokerage;
//                                    break;
//                                case "direction_02":
//                                    //增加已收金额
//                                    sourceOrder["freceivable"] = Convert.ToDecimal(sourceOrder["freceivable"]) + famount;
//                                    //增加收款金额
//                                    sourceOrder["fsumreceivable"] = Convert.ToDecimal(sourceOrder["fsumreceivable"]) + famount;
//                                    //增加已扣佣金
//                                    sourceOrder["freducedbrokerage"] = Convert.ToDecimal(sourceOrder["freducedbrokerage"]) + freducedbrokerage;
//                                    break;
//                                default:
//                                    break;
//                            }
//                        }
//                        //用途是“退款”
//                        else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//                        {
//                            if (famount > Convert.ToDecimal(sourceOrder["freceivable"]))
//                            {
//                                throw new BusinessException($"退款金额不能大于已收金额！");
//                            }
//                            //扣减已收金额
//                            sourceOrder["freceivable"] = Convert.ToDecimal(sourceOrder["freceivable"]) - famount;
//                        }
//                        //确认已收金额
//                        var freceivable = Convert.ToDecimal(sourceOrder["freceivable"]);
//                        //订单总额
//                        var fsumamount = Convert.ToDecimal(sourceOrder["fsumamount"]);

//                        var orderService = this.Container.GetService<IOrderService>();

//                        orderService.CalculateUnreceived(this.Context, new[] { sourceOrder });
//                        orderService.CalculateReceiptStatus(this.Context, new[] { sourceOrder });

//                        var unReceived = Convert.ToDecimal(sourceOrder["funreceived"]);

//                        ////未收金额=订单总额-确认已收-申请退货金额
//                        //var unReceived = fsumamount - freceivable - Convert.ToDecimal(sourceOrder["frefundamount"]);
//                        //sourceOrder["funreceived"] = unReceived;

//                        if (freceivable < 0)
//                        {
//                            throw new BusinessException($"确认已收金额不允许为负数！");
//                        }
//                        if (!fcanexcess && unReceived < 0)
//                        {
//                            throw new BusinessException($"未收金额不允许为负数！");
//                        }

//                        ////结算状态
//                        //if (freceivable == 0)
//                        //{
//                        //    //全款未收
//                        //    sourceOrder["freceiptstatus"] = "receiptstatus_type_01";
//                        //}
//                        //else if (freceivable < fsumamount)
//                        //{
//                        //    //部分收款
//                        //    sourceOrder["freceiptstatus"] = "receiptstatus_type_02";
//                        //}
//                        //else if (freceivable >= fsumamount)
//                        //{
//                        //    //全款已收
//                        //    sourceOrder["freceiptstatus"] = "receiptstatus_type_03";
//                        //}
//                        #endregion
//                        break;

//                    default:
//                        break;
//                }

//                dm.Save(sourceOrder);
//            }
//        }
//    }
//}