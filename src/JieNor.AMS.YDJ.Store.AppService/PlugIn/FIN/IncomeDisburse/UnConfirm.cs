//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.AMS.YDJ.DataTransferObject.Enums;
//using JieNor.AMS.YDJ.DataTransferObject.Poco;
//using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
//using JieNor.Framework;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.Enums;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.MetaCore.FormOp;
//using JieNor.Framework.MetaCore.FormOp.FormService;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm.DataManager;
//using Newtonsoft.Json.Linq;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
//{
//    /// <summary>
//    /// 收支记录：反确认
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("UnConfirm")]
//    public class UnConfirm : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 预处理校验规则
//        /// </summary>
//        /// <param name="e"></param>
//        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
//        {
//            base.PrepareValidationRules(e);

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            false == Convert.ToBoolean(newData["fissyn"])).WithMessage("协同收支记录不允许反确认！请红冲协同收支记录！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                var verificStatus = Convert.ToString(newData["fverificstatus"]);
//                if (verificStatus.EqualsIgnoreCase("verificstatus_02"))
//                {
//                    return false;
//                }
//                return true;
//            }).WithMessage("已核销的收支记录不允许反确认！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                var statementStatus = Convert.ToString(newData["fstatementstatus"]);
//                var dealerStatus = Convert.ToString(newData["fdealerstatus"]);

//                if (statementStatus == "2" || statementStatus == "3" || dealerStatus == "2" || dealerStatus == "3")
//                {
//                    return false;
//                }

//                return true;
//            }).WithMessage("对账中或已对账的收支记录不允许反确认！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("bizstatus_02")).WithMessage("只有已确认的收支记录才能反确认！"));


//        }

//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            this.Result.IsSuccess = false;
//            base.BeginOperationTransaction(e);

//            e.DataEntitys = checkEntityByInnerCustomer(e.DataEntitys);
//            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
//            {
//                return;
//            }

//            updateAccountInfo(e.DataEntitys);
//            updateSourceEntities(e.DataEntitys);

//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
//            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
//            prepareSaveDataService.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
//            dm.Save(e.DataEntitys);

//            new OrderCommon(this.Context).UpdateTransferOrder(e.DataEntitys);

//            //刷新父页面的父页面
//            var formParameter = this?.ParentPageSession?.FormParameter as FormParameter;
//            var ppPageId = formParameter?.ParentPageId;
//            if (!ppPageId.IsNullOrEmptyOrWhiteSpace())
//            {
//                this.AddRefreshPageAction(ppPageId);
//            }

//            this.Result.IsSuccess = true;
//        }

//        /// <summary>
//        /// 根据加盟商检查收支记录，如果它的源收支记录不是待确认状态则不能反确认
//        /// </summary>
//        /// <returns></returns>
//        private DynamicObject[] checkEntityByInnerCustomer(DynamicObject[] dataEntities)
//        {
//            if (dataEntities == null || dataEntities.Length <= 0)
//            {
//                return dataEntities;
//            }

//            //过滤源单为客户的收支记录
//            var cusSourceEntities = dataEntities.Where(x => Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_customer") &&
//                                                            false == string.IsNullOrWhiteSpace(Convert.ToString(x["fcustomerid"])) &&
//                                                            false == string.IsNullOrWhiteSpace(Convert.ToString(x["foldtranid"])))
//                                                .ToList();

//            if (cusSourceEntities == null || cusSourceEntities.Count <= 0)
//            {
//                return dataEntities;
//            }

//            //根据客户id获取加盟商id
//            var customerIds = cusSourceEntities.Select(x => Convert.ToString(x["fcustomerid"])).Distinct().ToList();
//            var customerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");
//            var customerDm = this.Container.GetService<IDataManager>();
//            customerDm.InitDbContext(this.Context, customerForm.GetDynamicObjectType(this.Context));
//            var innerCusIds = customerDm.Select(customerIds)
//                                        .OfType<DynamicObject>()
//                                        //过滤出加盟商
//                                        .Where(x => Convert.ToString(x["fcustype"]).EqualsIgnoreCase("customercate_02") &&
//                                                     string.IsNullOrWhiteSpace(Convert.ToString(x["fcoostate"])))
//                                        .Select(x => Convert.ToString(x["id"]))
//                                        .ToList();


//            if (innerCusIds == null || innerCusIds.Count <= 0)
//            {
//                return dataEntities;
//            }

//            //根据加盟商的收支记录的源流水号查找源收支记录，如果源收支记录是确认状态，那么加盟商的收支记录不能反确认
//            var oldTranIds = cusSourceEntities.Where(x => innerCusIds.Contains(Convert.ToString(x["fcustomerid"])))
//                                             .Select(x => Convert.ToString(x["foldtranid"]))
//                                             .ToList();
//            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
//            var where = "fmainorgid=@fmainorgid";
//            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company) };
//            var invailTranIds = multiValueQueryService.Select(this.Context, where, sqlParams, this.HtmlForm, "ftranid", oldTranIds)
//                                                      .Where(x => Convert.ToString(x["fbizstatus"]) != "bizstatus_01")
//                                                      .Select(x => Convert.ToString(x["ftranid"]))
//                                                      .ToList();

//            if (invailTranIds == null || invailTranIds.Count <= 0)
//            {
//                return dataEntities;
//            }

//            var msg = cusSourceEntities.Where(x => invailTranIds.Contains(Convert.ToString(x["foldtranid"])))
//                                       .Select(x => $"加盟商收支记录[{x["ftranid"]}]的源收支记录[{x["foldtranid"]}]不是待确认状态，因此不能反确认!");
//            this.Result.ComplexMessage.ErrorMessages.AddRange(msg);

//            return dataEntities.Where(x => false == invailTranIds.Contains(Convert.ToString(x["foldtranid"])))
//                               .ToArray();
//        }

//        /// <summary>
//        /// 更新账户信息
//        /// </summary>
//        /// <param name="dataEntities"></param>
//        private void updateAccountInfo(IEnumerable<DynamicObject> dataEntities)
//        {
//            //协同账户余额服务
//            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();
//            //更新账户余额
//            foreach (var dataEntity in dataEntities)
//            {
//                //克隆收支记录数据包，然后调换账户方向，进行账户余额计算
//                var direction = Convert.ToString(dataEntity["fdirection"]).Trim().ToLower();
//                direction = direction.EqualsIgnoreCase("direction_01") ? "direction_02" : "direction_01";
//                var newEntity = dataEntity.Clone() as DynamicObject;
//                newEntity["fdirection"] = direction;

//                //检查账户余额
//                checkSynAccountBalance(newEntity);

//                //更新账户余额
//                synAccountBalanceService.UpdateAccountBalance(this.Context, newEntity);
//            }
//        }

//        /// <summary>
//        /// 更新源单数据
//        /// </summary>
//        /// <param name="dataEntities"></param>
//        private void updateSourceEntities(DynamicObject[] dataEntities)
//        {
//            var groups = dataEntities.Where(x => false == string.IsNullOrWhiteSpace(Convert.ToString(x["fsourceformid"])))
//                                     .GroupBy(x => Convert.ToString(x["fsourceformid"]).ToLower());
//            var innerCustomerInfos = new List<Dictionary<string, string>>();

//            ICustomerService customerService = this.Container.GetService<ICustomerService>();

//            foreach (var group in groups)
//            {
//                var sourceForm = this.MetaModelService?.LoadFormModel(this.Context, group.Key);
//                var dm = this.Container.GetService<IDataManager>();
//                dm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));
//                var sourceKey = group.Key.EqualsIgnoreCase("ydj_customer") ? "fcustomerid" :
//                                group.Key.EqualsIgnoreCase("ydj_supplier") ? "fsupplierid" : "fsourceid";
//                var ids = group.Select(x => Convert.ToString(x[sourceKey]))
//                               .Where(x => false == string.IsNullOrWhiteSpace(x))
//                               .Distinct()
//                               .ToList();
//                if (ids == null || ids.Count <= 0)
//                {
//                    return;
//                }
//                var sourceEntities = dm.Select(ids).OfType<DynamicObject>().ToArray();
//                if (sourceEntities == null || sourceEntities.Length <= 0)
//                {
//                    return;
//                }

//                foreach (var incomeDisburse in group)
//                {
//                    var isValid = true;
//                    switch (group.Key)
//                    {
//                        case "ydj_saleintention":
//                            isValid = updateSaleIntention(incomeDisburse, sourceEntities, innerCustomerInfos, customerService);
//                            break;
//                        case "ydj_order":
//                            isValid = updateOrder(incomeDisburse, sourceEntities, innerCustomerInfos, customerService);
//                            break;
//                        case "ydj_purchaseorder":
//                            isValid = updatePurchaseOrder(incomeDisburse, sourceEntities);
//                            break;
//                    }
//                    if (isValid)
//                    {
//                        clearConfirmInfo(incomeDisburse);
//                    }
//                }

//                deleteInnerCustomerRecord(innerCustomerInfos);

//                var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
//                prepareSaveDataService.PrepareDataEntity(this.Context, sourceForm, sourceEntities, OperateOption.Create());
//                dm.Save(sourceEntities);
//            }
//        }

//        /// <summary>
//        /// 清除收支记录的确认信息
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        private void clearConfirmInfo(DynamicObject incomeDisburse)
//        {
//            //清空确认人、确认时间、业务状态
//            incomeDisburse["fbizstatus"] = "bizstatus_01";
//            incomeDisburse["fbalance"] = 0;
//            incomeDisburse["fconfirmorid"] = string.Empty;
//            incomeDisburse["fconfirmdate"] = null;
//        }

//        /// <summary>
//        /// 删除加盟商收支记录
//        /// </summary>
//        /// <param name="innerCustomerInfos"></param>
//        private void deleteInnerCustomerRecord(List<Dictionary<string, string>> innerCustomerInfos)
//        {
//            if (innerCustomerInfos == null || innerCustomerInfos.Count <= 0)
//            {
//                return;
//            }

//            var oldTranIds = innerCustomerInfos.Select(x => x["oldTranId"]).ToList();
//            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
//            var where = "fmainorgid=@fmainorgid";
//            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company) };
//            var incomeDisburses = multiValueQueryService.Select(this.Context, where, sqlParams, this.HtmlForm, "foldtranid", oldTranIds);

//            if (incomeDisburses == null || innerCustomerInfos.Count <= 0)
//            {
//                return;
//            }

//            var validEntities = new List<DynamicObject>();
//            foreach (var incomeDisburse in incomeDisburses)
//            {
//                var oldTranId = Convert.ToString(incomeDisburse["foldtranid"]);
//                var isSyn = Convert.ToBoolean(incomeDisburse["fissyn"]);
//                var customerId = Convert.ToString(incomeDisburse["fcustomerid"]);
//                var sourceFormId = Convert.ToString(incomeDisburse["fsourceformid"]);
//                var bizStatus = Convert.ToString(incomeDisburse["fbizstatus"]);
//                var innerCustomerInfo = innerCustomerInfos.FirstOrDefault(x => x["oldTranId"].EqualsIgnoreCase(oldTranId));

//                if (isSyn ||
//                    false == sourceFormId.EqualsIgnoreCase("ydj_customer") ||
//                    false == bizStatus.EqualsIgnoreCase("bizstatus_02") ||
//                    innerCustomerInfo == null ||
//                    false == innerCustomerInfo["customerId"].EqualsIgnoreCase(customerId))
//                {
//                    continue;
//                }
//                validEntities.Add(incomeDisburse);
//            }

//            if (validEntities == null || validEntities.Count <= 0)
//            {
//                return;
//            }

//            updateAccountInfo(validEntities);

//            foreach (var validEntity in validEntities)
//            {
//                clearConfirmInfo(validEntity);
//            }

//            var option = new Dictionary<string, object>();
//            var auditEntities = validEntities.Where(x => Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.E.ToString())).ToList();
//            if (auditEntities != null && auditEntities.Count > 0)
//            {
//                var auditResults = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, auditEntities, "unaudit", option);
//                auditResults.ThrowIfHasError(true, "反审核加盟商的收支记录失败!");
//            }
//            var submitEntities = validEntities.Where(x => Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.D.ToString())).ToList();
//            if (submitEntities != null && submitEntities.Count > 0)
//            {
//                var submitResults = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, submitEntities, "unsubmit", option);
//                submitResults.ThrowIfHasError(true, "撤消加盟商的收支记录失败!");
//            }

//            var deleteResults = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, validEntities, "delete", option);
//            deleteResults.ThrowIfHasError(true, "删除加盟商的收支记录失败!");
//        }

//        /// <summary>
//        /// 更新销售意向单
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        /// <param name="sourceEntities"></param>
//        /// <param name="innerCustomerInfos"></param>
//        /// <returns></returns>
//        private bool updateSaleIntention(DynamicObject incomeDisburse,
//                                         DynamicObject[] sourceEntities,
//                                         List<Dictionary<string, string>> innerCustomerInfos,
//                                         ICustomerService customerService)
//        {
//            var sourceId = incomeDisburse["fsourceid"] as string;
//            var amount = Convert.ToDecimal(incomeDisburse["famount"]);
//            var tranId = Convert.ToString(incomeDisburse["ftranid"]);
//            //账户收支方向
//            var direction = Convert.ToString(incomeDisburse["fdirection"]).Trim().ToLower();
//            //用途
//            var purpose = Convert.ToString(incomeDisburse["fpurpose"]).Trim().ToLower();

//            var sourceEntity = sourceEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
//            if (sourceEntity == null)
//            {
//                this.Result.ComplexMessage.ErrorMessages.Add($"流水号为[{tranId}]的收支记录没有找到销售意向单，不可以反确认!");
//                return false;
//            }

//            var orderId = Convert.ToString(sourceEntity["forderid"]);
//            if (false == string.IsNullOrWhiteSpace(orderId))
//            {
//                this.Result.ComplexMessage.ErrorMessages.Add($"流水号为[{tranId}]的收支记录对应的销售意向单已经下推销售合同，不可以反确认!");
//                return false;
//            }

//            // 计算本次确认收款/退款的金额
//            decimal confirmAmount = 0,
//                prevReceivable = Convert.ToDecimal(sourceEntity["fconfirmedamount"]),         // 更新前的确认已收
//                nextReceivable = 0;         // 更新后的确认已收

//            //用途是“订单付款”
//            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//            {
//                //减少确认已收
//                var confirmedAmount = Convert.ToDecimal(sourceEntity["fconfirmedamount"]) - amount;
//                if (confirmedAmount < 0)
//                {
//                    this.Result.ComplexMessage.ErrorMessages.Add($"如果流水号为[{tranId}]的收支记录反确认，它相关的销售意向单的确认已收将为负数，因此不可以反确认!");
//                    return false;
//                }
//                sourceEntity["fconfirmedamount"] = confirmedAmount;
//            }
//            //用途是“红冲”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//            {
//                this.Result.ComplexMessage.ErrorMessages.Add($"流水号为[{tranId}]的收支记录是红冲用途，不可以反确认!");
//                return false;
//            }
//            //用途是“退款”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//            {
//                //增加确认已收
//                sourceEntity["fconfirmedamount"] = Convert.ToDecimal(sourceEntity["fconfirmedamount"]) + amount;
//                //减少实退金额字段
//                sourceEntity["factrefundamount"] = Convert.ToDecimal(sourceEntity["factrefundamount"]) - amount;
//            }

//            var innerCustomerId = Convert.ToString(sourceEntity["finnercustomerid"]);
//            var oldTranId = Convert.ToString(incomeDisburse["ftranid"]);
//            if (false == string.IsNullOrWhiteSpace(innerCustomerId) && false == string.IsNullOrWhiteSpace(oldTranId))
//            {
//                innerCustomerInfos.Add(new Dictionary<string, string>
//                {
//                    { "customerId",innerCustomerId},
//                    { "oldTranId", oldTranId}
//                });
//            }

//            // 计算客户的会员等级和当前会员级别
//            nextReceivable = Convert.ToDecimal(sourceEntity["fconfirmedamount"]);
//            confirmAmount = nextReceivable - prevReceivable;
//            customerService.CalculateAvailableIntegralAndSumAmount(this.Context, Convert.ToString(sourceEntity["fcustomerid"]), confirmAmount);

//            return true;
//        }

//        /// <summary>
//        /// 更新销售合同
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        /// <param name="sourceEntities"></param>
//        /// <param name="innerCustomerInfos"></param>
//        /// <returns></returns>
//        private bool updateOrder(DynamicObject incomeDisburse,
//                                 DynamicObject[] sourceEntities,
//                                 List<Dictionary<string, string>> innerCustomerInfos,
//                                 ICustomerService customerService)
//        {
//            var sourceId = incomeDisburse["fsourceid"] as string;
//            var amount = Convert.ToDecimal(incomeDisburse["famount"]);
//            var tranId = Convert.ToString(incomeDisburse["ftranid"]);
//            //账户收支方向
//            var direction = Convert.ToString(incomeDisburse["fdirection"]).Trim().ToLower();
//            //用途
//            var purpose = Convert.ToString(incomeDisburse["fpurpose"]).Trim().ToLower();

//            var sourceEntity = sourceEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
//            if (sourceEntity == null)
//            {
//                this.Result.ComplexMessage.ErrorMessages.Add($"流水号为[{tranId}]的收支记录没有找到销售意向单，不可以反确认!");
//                return false;
//            }

//            // 计算本次确认收款/退款的金额
//            decimal confirmAmount = 0,
//                prevReceivable = Convert.ToDecimal(sourceEntity["freceivable"]),         // 更新前的确认已收
//                nextReceivable = 0;         // 更新后的确认已收

//            //用途是“订单付款”
//            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//            {
//                //减少确认已收
//                var confirmedAmount = Convert.ToDecimal(sourceEntity["freceivable"]) - amount;
//                if (confirmedAmount < 0)
//                {
//                    this.Result.ComplexMessage.ErrorMessages.Add($"如果流水号为[{tranId}]的收支记录反确认，它相关的销售合同的确认已收将为负数，因此不可以反确认!");
//                    return false;
//                }
//                //扣减确认已收金额
//                sourceEntity["freceivable"] = confirmedAmount;
//                //增加待确认金额
//                sourceEntity["freceivabletobeconfirmed"] = Convert.ToDecimal(sourceEntity["freceivabletobeconfirmed"]) + amount;
//            }
//            //用途是“红冲”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//            {
//                this.Result.ComplexMessage.ErrorMessages.Add($"流水号为[{tranId}]的收支记录是红冲用途，不可以反确认!");
//                return false;
//            }
//            //用途是“退款”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//            {
//                //增加确认已收金额
//                sourceEntity["freceivable"] = Convert.ToDecimal(sourceEntity["freceivable"]) + amount;
//                //扣减实退金额
//                sourceEntity["factrefundamount"] = Convert.ToDecimal(sourceEntity["factrefundamount"]) - amount;
//            }

//            //确认已收金额
//            var freceivable = Convert.ToDecimal(sourceEntity["freceivable"]);
//            //订单总额
//            var fsumamount = Convert.ToDecimal(sourceEntity["fsumamount"]);
//            #region 旧逻辑
//            ////未收金额=订单总额-确认已收-申请退货金额
//            //sourceEntity["funreceived"] = fsumamount - freceivable - Convert.ToDecimal(sourceEntity["frefundamount"]);

//            ////销售合同实退不体现未收
//            //var spService = this.Container.GetService<ISystemProfile>();
//            //var dontReflect = spService.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fdontreflect");
//            ////销售合同实退不体现未收(指的是：退货金额不体现未收)
//            //if (dontReflect)
//            //{
//            //      //反写未收，未收 = 订单总额 - 确认已收-申请退货金额
//            //    sourceEntity["funreceived"] = Convert.ToDecimal(sourceEntity["fsumamount"]) - Convert.ToDecimal(sourceEntity["freceivable"])- Convert.ToDecimal(sourceEntity["frefundamount"]);
//            //}
//            //else
//            //{
//            //    //反写未收，未收 = 订单总额 - 确认已收
//            //    sourceEntity["funreceived"] = Convert.ToDecimal(sourceEntity["fsumamount"]) - Convert.ToDecimal(sourceEntity["freceivable"]);
//            //}
//            #endregion

//            ////结算状态
//            //if (freceivable == 0)
//            //{
//            //    //全款未收
//            //    sourceEntity["freceiptstatus"] = "receiptstatus_type_01";
//            //}
//            //else if (freceivable < fsumamount)
//            //{
//            //    //部分收款
//            //    sourceEntity["freceiptstatus"] = "receiptstatus_type_02";
//            //}
//            //else if (freceivable >= fsumamount)
//            //{
//            //    //全款已收
//            //    sourceEntity["freceiptstatus"] = "receiptstatus_type_03";
//            //}

//            var orderService = this.Container.GetService<IOrderService>();

//            orderService.CalculateUnreceived(this.Context, new[] { sourceEntity });
//            orderService.CalculateReceiptStatus(this.Context, new[] { sourceEntity });

//            var innerCustomerId = Convert.ToString(sourceEntity["finnercustomerid"]);
//            var oldTranId = Convert.ToString(incomeDisburse["ftranid"]);
//            if (false == string.IsNullOrWhiteSpace(innerCustomerId) && false == string.IsNullOrWhiteSpace(oldTranId))
//            {
//                innerCustomerInfos.Add(new Dictionary<string, string>
//                {
//                    { "customerId",innerCustomerId},
//                    { "oldTranId", oldTranId}
//                });
//            }

//            // 计算客户的会员等级和当前会员级别
//            nextReceivable = freceivable;
//            confirmAmount = nextReceivable - prevReceivable;
//            customerService.CalculateAvailableIntegralAndSumAmount(this.Context, Convert.ToString(sourceEntity["fcustomerid"]), confirmAmount);

//            return true;
//        }

//        /// <summary>
//        /// 更新采购订单
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        /// <param name="sourceEntities"></param>
//        /// <returns></returns>
//        private bool updatePurchaseOrder(DynamicObject incomeDisburse, DynamicObject[] sourceEntities)
//        {
//            var sourceId = incomeDisburse["fsourceid"] as string;
//            var amount = Convert.ToDecimal(incomeDisburse["famount"]);
//            var tranId = Convert.ToString(incomeDisburse["ftranid"]);
//            //账户收支方向
//            var direction = Convert.ToString(incomeDisburse["fdirection"]).Trim().ToLower();
//            //用途
//            var purpose = Convert.ToString(incomeDisburse["fpurpose"]).Trim().ToLower();

//            var sourceEntity = sourceEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(sourceId));
//            if (sourceEntity == null)
//            {
//                this.Result.ComplexMessage.ErrorMessages.Add($"流水号为[{tranId}]的收支记录没有找到销售意向单，不可以反确认!");
//                return false;
//            }

//            //用途是“订单付款”
//            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//            {
//                //扣减已结算金额
//                var paidAmount = Convert.ToDecimal(sourceEntity["fpaidamount"]) - amount;
//                if (paidAmount < 0)
//                {
//                    this.Result.ComplexMessage.ErrorMessages.Add($"如果流水号为[{tranId}]的收支记录反确认，它相关的销售合同的确认已收将为负数，因此不可以反确认!");
//                    return false;
//                }
//                sourceEntity["fpaidamount"] = paidAmount;
//                //累加待确认金额
//                sourceEntity["fconfirmamount"] = Convert.ToDecimal(sourceEntity["fconfirmamount"]) + amount;
//            }
//            //用途是“红冲”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//            {
//                this.Result.ComplexMessage.ErrorMessages.Add($"流水号为[{tranId}]的收支记录是红冲用途，不可以反确认!");
//                return false;
//            }
//            //用途是“退款”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//            {
//                //累加已结算金额
//                sourceEntity["fpaidamount"] = Convert.ToDecimal(sourceEntity["fpaidamount"]) + amount;
//                //扣减实退金额
//                sourceEntity["factrefundamount"] = Convert.ToDecimal(sourceEntity["factrefundamount"]) - amount;
//            }

//            return true;
//        }


//        /// <summary>
//        /// 检查协同账户余额
//        /// </summary>
//        /// <param name="dataEntity"></param>
//        private void checkSynAccountBalance(DynamicObject dataEntity)
//        {
//            //账户为空的收支记录不参与余额增减逻辑，因为这类收支记录是由“线下支付”入口生成的，不存在账户的概念
//            var account = Convert.ToString(dataEntity["faccount"]);
//            if (account.IsNullOrEmptyOrWhiteSpace()) return;

//            //如果金额方向不是“支出”，则无需检查余额
//            if (!Convert.ToString(dataEntity["fdirection"]).EqualsIgnoreCase("direction_02")) return;

//            //金额
//            var amount = Convert.ToDecimal(dataEntity["famount"]);

//            var accountSynService = this.Container.GetService<ISynAccountBalanceService>();

//            //如果是客户
//            var supplierId = Convert.ToString(dataEntity["fsupplierid"]);
//            var customerId = Convert.ToString(dataEntity["fcustomerid"]);
//            if (!customerId.IsNullOrEmptyOrWhiteSpace())
//            {
//                #region 如果是客户，那么直接取客户协同账户配置
//                IEnumerable<AccountInfo> lstAllAccount = accountSynService.GetAllAccountByCustomerId(this.Context, customerId);
//                var accountInfo = lstAllAccount.LastOrDefault(t => t.AccountId.EqualsIgnoreCase(account));
//                if (accountInfo == null) return;
//                if (amount > accountInfo.Balance)
//                {
//                    if (accountInfo.CreditLimit > 0 && Math.Abs(accountInfo.Balance - amount) > accountInfo.CreditLimit)
//                    {
//                        throw new BusinessException($"{accountInfo.AccountName}账户信用额度不足，无法完成确认，当前信用额度为：{accountInfo.CreditLimit.ToString("f2")}！");
//                    }
//                    throw new BusinessException($"{accountInfo.AccountName}账户余额不足，无法完成确认，当前余额为：{accountInfo.Balance.ToString("f2")}！");
//                }
//                #endregion
//            }

//            //如果是供应商
//            else if (!supplierId.IsNullOrEmptyOrWhiteSpace())
//            {
//                #region 如果不是协同，那么直接取供应商账户信息
//                IEnumerable<AccountInfo> lstAllAccount = accountSynService.GetAllAccountBySupplierId(this.Context, supplierId);
//                var accountInfo = lstAllAccount.LastOrDefault(t => t.AccountId.EqualsIgnoreCase(account));
//                if (accountInfo == null) return;
//                if (amount > accountInfo.Balance)
//                {
//                    throw new BusinessException($"{accountInfo.AccountName}账户余额不足，无法完成确认，当前余额为：{accountInfo.Balance.ToString("f2")}！");
//                }
//                #endregion
//            }
//            else
//            {
//                if (Convert.ToString(dataEntity["foperationmode"]).EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == false)
//                {
//                    throw new BusinessException("供应商Id 和 客户Id 为空，请检查！");
//                }
//            }
//        }
//    }
//}
