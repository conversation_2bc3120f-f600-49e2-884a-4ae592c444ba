using JieNor.AMS.YDJ.Core.Extensions;
using JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
{
    /// <summary>
    /// 保存《收支记录》时需要校验：当前收款金额 + 合同收款待确认金额 > 合同未收款金额
    /// </summary>
    public class Validation_CheckExcess : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            var dataEntitys = dataEntities
                ?.Where(x=> 
                {
                    return Convert.ToString(x["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                        && Convert.ToString(x["fpurpose"]).EqualsIgnoreCase("bizpurpose_02")
                        && !x["fsourceid"].IsNullOrEmptyOrWhiteSpace();
                })
                ?.ToArray();
            if (dataEntitys == null || !dataEntitys.Any()) return this.Result;

            // 获取是否可以超额收款参数
            var profileService = userCtx.Container.GetService<ISystemProfile>();
            var canexcess = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanexcess", false);
            if (canexcess) return this.Result;

            // 检查销售合同是否超额收款
            this.CheckExcess(userCtx, formInfo, dataEntitys);

            return this.Result;
        }

        /// <summary>
        /// 检查销售合同是否超额收款
        /// </summary>
        private void CheckExcess(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntitys)
        {
            // 查找收款收支记录的销售合同ID
            var orderIds = dataEntitys
                .Select(x => Convert.ToString(x["fsourceid"]))
                .Distinct()
                .ToList();
            if (!orderIds.Any()) return;

            // 如果未勾选超额收款参数，则需要校验单据的收款总金额
            var orderDynObjs = userCtx.LoadBizBillHeadDataById("ydj_order", orderIds, "fbillno,funreceived");
            if (orderDynObjs == null || !orderDynObjs.Any()) return;

            // 批量加载销售合同关联的收支金额
            var sourceReceiptRefunds = IncomeDisburseHelper.LoadOrderReceiptRefundAmount(
                this.Context,
                "ydj_order",
                orderIds,
                null,
                new List<string>
                {
                    IncomeTypeConsts.receiptUnConfirmed
                });

            // 加载收支记录在数据库中的金额
            var incomeDisburseIds = dataEntitys
                .Where(o => !o["id"].IsNullOrEmptyOrWhiteSpace() && o.DataEntityState.FromDatabase)
                .Select(o => Convert.ToString(o["id"]))
                .ToList();
            DynamicObjectCollection incomeDisburses = null;
            if (incomeDisburseIds.Any())
            {
                incomeDisburses = userCtx.LoadBizBillHeadDataById("coo_incomedisburse", incomeDisburseIds, "famount");
            }

            foreach (var dataEntity in dataEntitys)
            {
                var incomeDisburseId = Convert.ToString(dataEntity["id"]);
                var sourceId = Convert.ToString(dataEntity["fsourceid"]);
                var orderDynObj = orderDynObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(sourceId));
                var orderNo = Convert.ToString(orderDynObj?["fbillno"]);
                var unReceived = Convert.ToDecimal(orderDynObj?["funreceived"] ?? 0); // 未收款

                var receiptUnConfirmedSum = IncomeDisburseHelper.FindSourceReceiptRefundAmount(sourceReceiptRefunds, sourceId, IncomeTypeConsts.receiptUnConfirmed);
                var amountSum = 0M;

                // 是否是修改时保存
                if (dataEntity.DataEntityState.FromDatabase)
                {
                    // 先扣减掉原先数据库中已保存过的金额，再比较
                    var incomeDisburse = incomeDisburses?.FirstOrDefault(o => 
                        Convert.ToString(o["id"]).EqualsIgnoreCase(incomeDisburseId));
                    var incomeDisburseAmount = Convert.ToDecimal(incomeDisburse?["famount"] ?? 0);
                    receiptUnConfirmedSum -= incomeDisburseAmount;

                    // 与当前收支记录同属一个单据的所有其它收支记录金额汇总
                    amountSum = dataEntitys
                        .Where(o =>
                        {
                            return Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_02")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId);
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();
                }
                else
                {
                    // 新增时保存

                    // 与当前收支记录同属一个单据的所有其它新增收支记录金额汇总
                    amountSum = dataEntitys
                        .Where(o =>
                        {
                            // 非新增的收支记录
                            if (o.DataEntityState.FromDatabase) return false;

                            return Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order")
                                && Convert.ToString(o["fpurpose"]).EqualsIgnoreCase("bizpurpose_02")
                                && Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(sourceId);
                        })
                        .Select(o => Convert.ToDecimal(o["famount"]))
                        .Sum();
                }

                // 其它总的待确认收款 + 当前金额，如果大于合同未收款，则提示
                var _sumAmount = receiptUnConfirmedSum + amountSum;
                if (_sumAmount > unReceived)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"销售合同【{orderNo}】的收款待确认总额 {_sumAmount.Format()} 已大于未收款 {unReceived.Format()}，当前系统设置为不允许超额收款！",
                        DataEntity = dataEntity
                    });
                }
            }
        }
    }
}
