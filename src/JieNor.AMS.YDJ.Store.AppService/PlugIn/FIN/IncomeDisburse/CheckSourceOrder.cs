using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
{
    /// <summary>
    /// 收支记录：检查源单是否整单关闭
    /// </summary>
    [InjectService]
    [FormId("coo_incomedisburse")]
    [OperationNo("checksourceorder")]
    public class CheckSourceOrder : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var Ids = this.GetQueryOrSimpleParam<string>("Ids");
            if (!Ids.IsNullOrEmptyOrWhiteSpace())
            {
                string[] fids = Ids.Split(',');
                if (fids.Length > 0)
                {
                    var sql = $@"select t1.fbillno,t1.fsourcenumber from t_coo_incomedisburse  t1 with(nolock)
                                        inner join t_ydj_order t2  with(nolock) on t1.fsourcenumber=t2.fbillno and t1.fmainorgid=t2.fmainorgid
                                        where t1.fsourceformid='ydj_order' and t2.fclosestatus='1' and t1.fid in ({fids.JoinEx(",", true)})";
                    var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                    if (data != null && data.Count() > 0)
                    {
                        var msg = $"收支记录{ string.Join("、", data.Select(x => Convert.ToString(x["fbillno"])))}关联的源单销售合同{ string.Join("、", data.Select(x => Convert.ToString(x["fsourcenumber"])))}已是整单关闭状态，确定要反审核吗？";
                        this.Result.SrvData = msg;
                        this.Result.IsSuccess = true;
                    }
                }
            }
        }
    }
}
