using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.FIN.IncomeDisburse
{
    /// <summary>
    /// 收支记录：撤销
    /// </summary>
    [InjectService]
    [FormId("coo_incomedisburse")]
    [OperationNo("UnSubmit")]
    public class UnSubmit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var isCheck = this.GetQueryOrSimpleParam("ischeckbill", "true");
            var ischeckbill = isCheck.IsNullOrEmptyOrWhiteSpace() ? true : Convert.ToBoolean(isCheck);

            e.Rules.Add(new Validation_Check(ischeckbill));
        }
    }
}
