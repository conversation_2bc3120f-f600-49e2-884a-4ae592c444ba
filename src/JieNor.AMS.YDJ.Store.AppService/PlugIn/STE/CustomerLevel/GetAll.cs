using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerLevel
{
    /// <summary>
    /// 会员等级：获取所有可用数据
    /// </summary>
    [InjectService]
    [FormId("ydj_customerlevel")]
    [OperationNo("getall")]
    public class GetAll : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            // 获取所有会员等级
            var customerLevels = this.Context.LoadBizDataByFilter(this.HtmlForm.Id, " fforbidstatus<>'1' ");

            this.Result.IsSuccess = true;
            //this.Result.SimpleMessage = "保存成功！";
            this.Result.SrvData = customerLevels.OrderBy(s => s["fcondition"]).Select(s => new
            {
                Id = s["Id"],
                fname = s["fname"],
                fcondition = s["fcondition"]
            });
        }
    }
}