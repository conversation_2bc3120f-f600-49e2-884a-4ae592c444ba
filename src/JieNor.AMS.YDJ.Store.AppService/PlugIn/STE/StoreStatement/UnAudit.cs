using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.StoreStatement
{
    /// <summary>
    /// 店面结算对账单:反审核
    /// </summary>
    [InjectService]
    [FormId("ydj_storestatement")]
    [OperationNo("UnAudit")]
    public class UnAudit: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            updateIncomeDisburse(e.DataEntitys);
            updateOrder(e.DataEntitys);

            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["fverifydate"] = null;
            }
        }

        private void updateOrder(DynamicObject[] dataEntities)
        {
            var orderEntries = dataEntities.SelectMany(x => x["fincomeentry"] as DynamicObjectCollection).ToList();
            if (orderEntries == null || orderEntries.Count <= 0)
            {
                return;
            }

            var orderIds = orderEntries.Select(x => Convert.ToString(x["forderid"]))
                                       .Where(x => false == string.IsNullOrWhiteSpace(x))
                                       .Distinct()
                                       .ToList();

            if (orderIds == null || orderIds.Count <= 0)
            {
                return;
            }

            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var orderDm = this.Container.GetService<IDataManager>();
            orderDm.InitDbContext(this.Context, orderForm.GetDynamicObjectType(this.Context));
            var orderEntities = orderDm.Select(orderIds).OfType<DynamicObject>().ToArray();

            if (orderEntities == null || orderEntities.Length <= 0)
            {
                return;
            }

            foreach (var orderEntity in orderEntities)
            {
                var orderId = Convert.ToString(orderEntity["id"]);
                var orderItems = orderEntries.Where(x => Convert.ToString(x["forderid"]).EqualsIgnoreCase(orderId)).ToList();
                if (orderItems == null || orderItems.Count <= 0)
                {
                    continue;
                }
                var sumAmount = 0m;
                foreach (var orderItem in orderItems)
                {
                    var verificAmount = Convert.ToDecimal(orderItem["fverificamount"]);
                    verificAmount = verificAmount < 0 ? -verificAmount : verificAmount;
                    sumAmount += verificAmount;
                }
                var verifyAmount = Convert.ToDecimal(orderEntity["fverifyamount"]);
                verifyAmount += sumAmount;
                orderEntity["fverifyamount"] = verifyAmount;
            }

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, orderForm, orderEntities, OperateOption.Create());
            orderDm.Save(orderEntities);
        }

        private void updateIncomeDisburse(DynamicObject[] dataEntities)
        {
            var incomeIds = dataEntities.SelectMany(x => x["fincomeentry"] as DynamicObjectCollection)
                                        .Select(x => Convert.ToString(x["fincomeid"]))
                                        .Where(x => false == string.IsNullOrWhiteSpace(x))
                                        .Distinct()
                                        .ToList();

            if (incomeIds == null || incomeIds.Count <= 0)
            {
                return;
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));
            var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return;
            }

            foreach (var incomeEntity in incomeEntities)
            {
                var dealerStatus = Convert.ToString(incomeEntity["fdealerstatus"]);
                if (dealerStatus == "3")
                {
                    incomeEntity["fdealerstatus"] = "2";
                }
            }

            var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, incomeForm, incomeEntities, OperateOption.Create());
            incomeDm.Save(incomeEntities);
        }
    }
}
