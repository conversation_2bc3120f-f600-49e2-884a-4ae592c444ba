using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：确认收定
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("ConfirmDeposit")]
    public class ConfirmDeposit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            var htmlForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_incomedisburse");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            string where = $@"fmainorgid=@fmainorgid and fsourceid=@fsourceid and fbizstatus='bizstatus_01'";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("fsourceid", System.Data.DbType.String, dataEntity["id"])
            };
            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var incomeDisburses = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (incomeDisburses == null || incomeDisburses.Count() <= 0)
            {
                throw new BusinessException($"{this.HtmlForm.Caption}【{dataEntity["fbillno"]}】没有未确认的收支记录！");
            }

            //调用收支记录的确认操作
            var operationResult = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.Context,
                htmlForm.Id,
                incomeDisburses,
                "Confirm",
                new Dictionary<string, object>());
            operationResult?.ThrowIfHasError(true);

            this.Result.SimpleMessage = "确认成功！";
            this.Result.IsSuccess = true;
        }
    }
}