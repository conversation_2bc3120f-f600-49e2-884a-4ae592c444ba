using JieNor.AMS.YDJ.Core.DataEntity.Models;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 挂单列表
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("GetList")]
    public class GetList : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "buildQueryParameter":
                    buildQueryParameter(e);
                    break;
                case "changeQueryParameter":
                    changeQueryParameter(e);
                    break;
                case "changeResult":
                    changeResult(e);
                    break;
                default:
                    return;
            }
        }

        private void changeResult(OnCustomServiceEventArgs e)
        {
            var srvData = e.EventData as Dictionary<string, object>;
            if (srvData == null || srvData.Count <= 0)
            {
                return;
            }

            var listData = srvData["datas"] as List<Dictionary<string, object>>;
            if (listData == null || listData.Count <= 0)
            {
                return;
            }

            //保护列表客户信息，当前销售意向单并没有微信号，如果今后加了微信号按当前的需求要清空微信号
            var protecteDataService = this.Container.GetService<IProtecteDataService>();
            protecteDataService.Init(this.Context, "bas_storesysparam", "fenableprotectecusinfo");
            protecteDataService.MaskFields(new[] { "fphone_e" }, listData, 3, 4);
        }

        private void buildQueryParameter(OnCustomServiceEventArgs e)
        {
            //string searchText = this.GetQueryOrSimpleParam<string>("searchText");
            //List<SqlParam> dynamicParams = new List<SqlParam>();
            //StringBuilder filterString = new StringBuilder("(case when fispushorder='1' then 'Y' else 'N' end)='N'");

            //if (!string.IsNullOrWhiteSpace(searchText))
            //{
            //    filterString.Append(" and (t0.fname like @searchText or t0.fphone_e like @searchText)");
            //    dynamicParams.Add(new SqlParam("@searchText", System.Data.DbType.String, $"%{searchText}%"));
            //}
            e.Result = new Dictionary<string, object>
            {
                { "filterString","(case when fispushorder='1' then 'Y' else 'N' end)='N'"}
            };

            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }

            var fields = eventData["fields"] as List<string>;
            if (fields == null)
            {
                return;
            }
            fields.AddRange(@"fid|fcustomerid|fcustomerid.fname|fdate|fdeptid|fdeptid.fname|fstaffid|fstaffid.fname|fmodifydate|(select isnull(sum(se.fqty),0) from t_ydj_saleentry se where se.fid=t0.fid) as fqty|(select isnull(sum(se.famount),0) from t_ydj_saleentry se where se.fid=t0.fid) as famount".Split('|'));

            var orderInfos = eventData["orderInfos"] as List<Dictionary<string, string>>;
            orderInfos.Add(new Dictionary<string, string>
            {
                { "id","fmodifydate"},
                { "order","desc"}
            });
        }

        private void changeQueryParameter(OnCustomServiceEventArgs e)
        {
            Filters filters = new Filters();

            filters.Regions.Add(new RegionFilters
            {
                FieldId = "fdate",
                Operator = "between"
            });

            Dictionary<string, string> aliasFields = new Dictionary<string, string>
            {
                { "fbillhead_id","id"},
                { "fcustomerid","customerId"},
                { "fcustomerid_fname","customerName"},
                { "fdate","date"},
                { "fdeptid","deptId"},
                { "fdeptid_fname","deptName"},
                { "fstaffid","staffId"},
                { "fstaffid_fname","statusName"},
                { "fqty","qty"},
                { "famount","amount"}
            };

            e.Result = new Dictionary<string, object>
            {
                { "filters",filters},
                { "aliasFields",aliasFields}
            };
        }

        //        /// <summary>
        //        /// 开始事务前事件
        //        /// </summary>
        //        /// <param name="e"></param>
        //        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        //        {
        //            Filters filters = new Filters();
        //            var dataMetas = this.Option.GetVariableValue<JObject>("dataMetas");

        //            filters.Regions.Add(new RegionFilters
        //            {
        //                FieldId = "fdate",
        //                Operator = "between",
        //                ElementType = (int)(dataMetas["fdate"]["elementType"])
        //            });

        //            this.Option.SetVariableValue("filters", filters);

        //            string searchText = this.GetQueryOrSimpleParam<string>("searchText");
        //            List<SqlParam> dynamicParams = new List<SqlParam>();
        //            StringBuilder filterString = new StringBuilder("(case when s.fispushorder='1' then 'Y' else 'N' end)='N'");

        //            if (!string.IsNullOrWhiteSpace(searchText))
        //            {
        //                filterString.Append(" and (c.fname like @searchText or s.fphone_e like @searchText)");
        //                dynamicParams.Add(new SqlParam("@searchText", System.Data.DbType.String, $"%{searchText}%"));
        //            }

        //            this.Option.SetVariableValue("dynamicParams", dynamicParams);
        //            this.Option.SetVariableValue("filterString", filterString.ToString());
        //            pageData();
        //        }

        //private void pageData()
        //{
        //    int pageIndex = this.GetQueryOrSimpleParam<int>("pageIndex", 0);
        //    int pageSize = this.GetQueryOrSimpleParam<int>("pageSize", 0);
        //    List<Dictionary<string, string>> whereInfos = this.Option.GetVariableValue<List<Dictionary<string, string>>>("whereInfos", null);
        //    List<SqlParam> dynamicParams = this.Option.GetVariableValue<List<SqlParam>>("dynamicParams", null);
        //    string filterString = this.Option.GetVariableValue<string>("filterString", null);
        //    IBaseFormProvider baseFormProvider = this.Container.GetService<IBaseFormProvider>();
        //    List<SqlParam> sqlParams = new List<SqlParam>
        //            {
        //                new SqlParam("@fmainorgid",DbType.String,this.Context.Company),
        //                new SqlParam("@fstaffid",DbType.String,baseFormProvider.GetMyStaff(this.Context)?.Id)
        //            };
        //    StringBuilder where = new StringBuilder();

        //    string countSql = "select count(1) from ({0}) as t";
        //    string pageDataSql = @"
        //select top {0} * from (
        //select row_number() over(order by {1}) as fpageindentity,t.* from (
        //{2}
        //) as t
        //) as tt where tt.fpageindentity>{3}
        //";

        //    string tabelFormat = @"
        //select s.fid,s.fcustomerid,c.fname as fcustomername,s.fdate,s.fdeptid,d.fname as fdeptname,s.fstaffid,st.fname as fstaffname,s.fmodifydate,
        //(select sum(se.fqty) from t_ydj_saleentry se where se.fid=s.fid) as fqty,
        //(select sum(se.famount) from t_ydj_saleentry se where se.fid=s.fid) as famount
        //from t_ydj_saleintention s
        //left join t_ydj_customer c on c.fid=s.fcustomerid
        //left join t_bd_department d on d.fid=s.fdeptid
        //left join t_bd_staff st on st.fid=s.fstaffid
        //where s.fmainorgid=@fmainorgid and s.fstaffid=@fstaffid {0}
        //";

        //    if (pageIndex <= 0)
        //    {
        //        pageIndex = 1;
        //    }

        //    if (pageSize <= 0)
        //    {
        //        pageSize = 10;
        //    }

        //    if (dynamicParams != null || dynamicParams.Count > 0)
        //    {
        //        sqlParams.AddRange(dynamicParams);
        //    }

        //    if (string.IsNullOrWhiteSpace(filterString) == false)
        //    {
        //        where.AppendFormat(" and {0} ", filterString);
        //    }

        //    where.Append(getWhereSql(whereInfos, sqlParams));

        //    string tabelSql = string.Format(tabelFormat, where.ToString());
        //    int currentMaxPages = 0;
        //    int currentRecords = 0;

        //    if (pageIndex > 0 && pageSize > 0)
        //    {
        //        //获取当前表中实际的记录数
        //        countSql = string.Format(countSql, tabelSql);
        //        using (var countReader = this.DBService.ExecuteReader(this.Context, countSql, sqlParams))
        //        {
        //            if (countReader.Read())
        //            {
        //                currentRecords = Convert.ToInt32(countReader[0]);
        //            }
        //        }
        //        //求出当前最大的页面数
        //        currentMaxPages = currentRecords % pageSize == 0 ? currentRecords / pageSize : currentRecords / pageSize + 1;
        //        //校正当前请求的页数
        //        if (pageIndex > currentMaxPages)
        //        {
        //            pageIndex = currentMaxPages > 0 ? currentMaxPages : 1;
        //        }
        //    }


        //    //获取当前请求页面记录
        //    List<Dictionary<string, object>> datas = new List<Dictionary<string, object>>();
        //    if (currentMaxPages > 0)
        //    {
        //        pageDataSql = string.Format(pageDataSql, pageSize, "fmodifydate desc", tabelSql, (pageIndex - 1) * pageSize);
        //        using (var dataReader = this.DBService.ExecuteReader(this.Context, pageDataSql, sqlParams))
        //        {
        //            while (dataReader.Read())
        //            {
        //                Dictionary<string, object> data = new Dictionary<string, object>();
        //                data["id"] = getValueToString(dataReader["fid"]);
        //                data["customerId"] = getValueToString(dataReader["fcustomerid"]);
        //                data["customerName"] = getValueToString(dataReader["fcustomername"]);
        //                data["date"] = getValueToDateTime(dataReader["fdate"]);
        //                data["deptId"] = getValueToString(dataReader["fdeptid"]);
        //                data["deptName"] = getValueToString(dataReader["fdeptname"]);
        //                data["staffId"] = getValueToString(dataReader["fstaffid"]);
        //                data["staffName"] = getValueToString(dataReader["fstaffname"]);
        //                data["qty"] = getValueToDecimal(dataReader["fqty"]);
        //                data["amount"] = getValueToDecimal(dataReader["famount"]);
        //                datas.Add(data);
        //            }
        //        }
        //    }

        //    this.Result.SrvData = new
        //    {
        //        pageInfos = new
        //        {
        //            pageIndex = pageIndex,
        //            pageSize = pageSize,
        //            pageCount = currentMaxPages,
        //            pageRows = currentRecords
        //        },
        //        datas = datas,
        //        dataMetas = getDataMetas(),
        //        filters = this.Option.GetVariableValue<Filters>("filters", null),
        //        colation = this.Option.GetVariableValue<Colation>("colation", null)
        //    };
        //    this.Result.IsSuccess = true;
        //    this.Result.SimpleMessage = "查询成功!";
        //    this.Option.SetVariableValue("notExecute", true);
        //}

        //        private bool isNullOrDBNull(object value)
        //        {
        //            return value == null || value is DBNull;
        //        }

        //        public object getValueToDateTime(object value)
        //        {
        //            if (isNullOrDBNull(value))
        //            {
        //                return null;
        //            }
        //            return Convert.ToDateTime(value).ToString("yyyy-MM-dd");
        //        }

        //        public string getValueToString(object value)
        //        {
        //            if (isNullOrDBNull(value))
        //            {
        //                return string.Empty;
        //            }
        //            return Convert.ToString(value);
        //        }

        //        public decimal getValueToDecimal(object value)
        //        {
        //            if (isNullOrDBNull(value))
        //            {
        //                return 0;
        //            }
        //            return Convert.ToDecimal(value);
        //        }

        //        private JArray getDataMetas()
        //        {
        //            JArray array = new JArray();
        //            var dataMetas = this.Option.GetVariableValue<JObject>("dataMetas", null);
        //            var fields = dataMetas?["fields"] as JArray;
        //            if (fields != null && fields.Count > 0)
        //            {
        //                foreach (var fieldToken in fields)
        //                {
        //                    string field = (string)fieldToken;
        //                    var token = dataMetas[field];
        //                    if (token != null && token.Type == JTokenType.Object)
        //                    {
        //                        array.Add(token);
        //                    }
        //                }
        //            }
        //            return array;
        //        }

        //        private static string getWhereSql(List<Dictionary<string, string>> whereInfos, List<SqlParam> sqlParams)
        //        {
        //            if (whereInfos == null||whereInfos.Count<=0)
        //            {
        //                return string.Empty;
        //            }
        //            var fdateItem = whereInfos.FirstOrDefault(x => x["id"] == "fdate");
        //            if (fdateItem == null)
        //            {
        //                return string.Empty;
        //            }
        //            var value = fdateItem["value"];
        //            if (string.IsNullOrWhiteSpace(value))
        //            {
        //                return string.Empty;
        //            }
        //            value = Regex.Replace(value, @"\b'|'\b|\bN'", string.Empty).ToLower();
        //            var values = value.Split(new[] { "and" }, StringSplitOptions.None);
        //            if (values == null || values.Length != 2)
        //            {
        //                return string.Empty;
        //            }

        //            var beginDate = values[0].Trim();
        //            var endDate = values[1].Trim();
        //            var where = new StringBuilder();

        //            if (string.IsNullOrEmpty(beginDate) == false)
        //            {
        //                where.AppendFormat(" and s.fdate >=@fdate{0}", sqlParams.Count);
        //                sqlParams.Add(new SqlParam(string.Format("@fdate{0}", sqlParams.Count), DbType.DateTime, DateTime.Parse(beginDate)));
        //            }

        //            if (string.IsNullOrEmpty(endDate) == false)
        //            {
        //                where.AppendFormat(" and s.fdate < @fdate{0}", sqlParams.Count);
        //                sqlParams.Add(new SqlParam(string.Format("@fdate{0}", sqlParams.Count), DbType.DateTime, DateTime.Parse(endDate).AddDays(1)));
        //            }

        //            return where.ToString();
        //        }
    }
}
