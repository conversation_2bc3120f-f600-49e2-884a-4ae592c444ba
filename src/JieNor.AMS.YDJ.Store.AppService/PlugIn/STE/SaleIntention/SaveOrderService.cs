using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    //[InjectService("SaveOrder")]
    //[ServiceMetaAttribute("name", "移动端挂单结算服务")]
    //public class SaveOrderService : AbstractBaseService
    //{
    //    public override void ExecuteService(ref DynamicObject[] dataEntities)
    //    {
    //        if (dataEntities == null || dataEntities.Length <= 0)
    //        {
    //            return;
    //        }
    //        var orderInfoStr = this.GetQueryOrSimpleParam<string>("orders");
    //        if (string.IsNullOrWhiteSpace(orderInfoStr))
    //        {
    //            return;
    //        }
    //        var orders = JArray.Parse(orderInfoStr);
    //        if (orders == null || orders.Count <= 0)
    //        {
    //            return;
    //        }
    //        if (dataEntities.Length != orders.Count)
    //        {
    //            throw new BusinessException("挂单的数量与合同的数量不相等");
    //        }

    //        //将销售意向单下推生成销售合同
    //        string targetFormId = "ydj_order";
    //        var convertService = this.Container.GetService<IConvertService>();
    //        DynamicObject[] targetDataObjects = null;

    //        var result = convertService.Push(this.Context, new BillConvertContext()
    //        {
    //            RuleId = "ydj_saleintention2ydj_order",
    //            SourceFormId = this.HtmlForm.Id,
    //            TargetFormId = targetFormId,
    //            SelectedRows = dataEntities.Select(x => new SelectedRow { PkValue = Convert.ToString(x["Id"]) }).ToConvertSelectedRows(),
    //            Option = this.Option
    //        });
    //        targetDataObjects = (result.SrvData as ConvertResult)?.TargetDataObjects?.ToArray();

    //        if (targetDataObjects == null || targetDataObjects.Length <= 0 || targetDataObjects.Length != orders.Count)
    //        {
    //            throw new BusinessException("意向单转销售合同失败!");
    //        }

    //        //将移动端传回的合同数据合并到合同中
    //        var metaModelService = this.Container.GetService<IMetaModelService>();
    //        var dcSerializer = this.Container.GetService<IDynamicSerializer>();
    //        var targetForm = metaModelService.LoadFormModel(this.Context, targetFormId);
    //        var targetType = targetForm.GetDynamicObjectType(this.Context);
    //        var prepareService = this.Container.GetService<IPrepareSaveDataService>();
    //        prepareService?.PrepareDataEntity(this.Context, targetForm, targetDataObjects, OperateOption.Create());

    //        for (var i = 0; i < targetDataObjects.Length; i++)
    //        {
                
    //            JObject orderObject = orders[i] as JObject;
    //            DynamicObject targetEntity = targetDataObjects[i];
    //            orderObject["id"] = Convert.ToString(targetEntity["id"]);
    //            foreach (var entryForm in targetForm.EntryList)
    //            {
    //                if(entryForm is HtmlSubEntryEntity)
    //                {
    //                    continue;
    //                }
    //                var entryEntities = targetEntity[entryForm.Id] as DynamicObjectCollection;
    //                var orderEntries = orderObject[entryForm.Id] as JArray;

    //                if (entryEntities == null || entryEntities.Count <= 0 || orderEntries == null || orderEntries.Count <= 0)
    //                {
    //                    continue;
    //                }

    //                int count = Math.Min(entryEntities.Count, orderEntries.Count);

    //                for (var j = 0; j < count; j++)
    //                {
    //                    var entry = entryEntities[j];
    //                    var orderEntry = orderEntries[j];
    //                    orderEntry["id"] = Convert.ToString(entry["id"]);
    //                }
    //            }
    //        }

    //        dcSerializer.Sync(targetType, targetDataObjects, orders, (propKey) =>
    //        {
    //            var el = targetForm.GetElement(propKey);
    //            if (el is HtmlField) return (el as HtmlField).DynamicProperty;
    //            if (el is HtmlEntryEntity) return (el as HtmlEntryEntity).DynamicProperty;
    //            return null;
    //        },
    //         null,
    //         null,
    //         null);

    //        //保存合同
    //        var saveResult = this.Gateway.InvokeBillOperation(this.Context,
    //                                                            targetFormId,
    //                                                             targetDataObjects,
    //                                                            "save",
    //                                                            null
    //                                                         );

    //        saveResult.ThrowIfHasError(true, "销售合同保存失败!");

    //        this.Option.SetVariableValue("orderInfos", JArray.FromObject(saveResult.SrvData));
    //        this.Result.IsSuccess = true;
    //        this.Result.SimpleMessage = "销售合同保存成功!";
    //    }
    //}
}
