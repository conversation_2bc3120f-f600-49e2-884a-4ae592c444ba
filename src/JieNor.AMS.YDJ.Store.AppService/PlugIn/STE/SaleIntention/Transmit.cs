using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.RegularExpressions;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：报价确认
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("Transmit")]
    public class Transmit : UpdateBizStatus
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public override string OperationName { get { return "报价确认"; } }

        /// <summary>
        /// 是否协同明细
        /// </summary>
        protected override bool IsSyncEntry
        {
            get
            {
                return true;
            }
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
        }
    }


    /// <summary>
    /// 销售意向单：下推 销售合同
    /// </summary>
    //[InjectService]
    //[FormId("ydj_order")]
    //[OperationNo("ydj_saleintention2ydj_order")] //ydj_saleintention2ydj_order
    //public class SaleIntentionToOrderContract : AbstractConvertServicePlugIn
    //{
    //    //const string REPLACEFATTRINFO = @"\w+\.fname\s+as\sfattrinfo";
    //    //const string REMOVELEFTJOINAUXPROPVALUE = @"left\s+join\s+t_bd_auxpropvalue\s+as\s+\w+\s+on\s+\w+\.fid\s*=\s*\w+\.fattrinfo";
    //    //const string MATCHTABLENAME = @"left\s+join\s+t_ydj_saleentry\s+as\s+(\w+)";
    //    const string KEYFORMAT = "productId={0}&attrInfo={1}&orderDate={2}";

    //    public override void OnConvertComplete(OnConvertCompleteEventArgs e)
    //    {
    //        base.OnConvertComplete(e);
    //        var dataEntities = e.TargetDataEntities?.ToList();
    //        if (dataEntities == null || dataEntities.Count <= 0)
    //        {
    //            return;
    //        }

    //        var datas = dataEntities.SelectMany(x =>
    //          {
    //              var fentry = x["fentry"] as DynamicObjectCollection;
    //              var orderDate = Convert.ToDateTime(x["forderdate"]).ToString("yyyy-MM-dd");//业务日期
    //              var customerId = Convert.ToString(x["fcustomerid"]);

    //              return fentry.Select(y =>
    //              {
    //                  var productId = Convert.ToString(y["fproductid"]);//商品id
    //                  var attrInfo = Convert.ToString(y["fattrinfo"]);//辅助属性
    //                  return new
    //                  {
    //                      clientId = string.Format(KEYFORMAT, productId, attrInfo, orderDate),
    //                      entry = y,
    //                      productId = productId,
    //                      attrInfo = attrInfo,
    //                      orderDate = orderDate,
    //                      price = Convert.ToDecimal(y["fprice"]),
    //                      customerId = customerId,
    //                      stockStatus = Convert.ToString(y["fstockstatus"])
    //                  };
    //              });
    //          }).Where(x => string.IsNullOrWhiteSpace(x.productId) == false).ToList();

    //        if (datas.Count <= 0)
    //        {
    //            compute(dataEntities);
    //            return;
    //        }

    //        setSupplierInfo(datas.Select(x => x.productId).Distinct().ToList(), dataEntities);

    //        var requestDatas = datas.Distinct(x => x.clientId).Select(x => new
    //        {
    //            clientId = x.clientId,
    //            productId = x.productId,
    //            bizDate = x.orderDate,
    //            customerId = x.customerId,
    //            stockStatus = x.stockStatus,
    //            length = 0,
    //            width = 0,
    //            thick = 0,
    //            attrInfo = new
    //            {
    //                id = x.attrInfo
    //            }
    //        }).ToList();

    //        var gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();

    //        // 获取价格
    //        var result = gateway.InvokeBillOperation(this.UserContext,
    //                        "ydj_price",
    //                         null,
    //                        "getprices",
    //                        new Dictionary<string, object>
    //                        {
    //                           { "productInfos",JsonConvert.SerializeObject(requestDatas)}
    //                        }
    //                     );

    //        if (result == null || !result.IsSuccess || result.SrvData == null)
    //        {
    //            compute(dataEntities);
    //            return;
    //        }

    //        JArray array = JArray.FromObject(result.SrvData);

    //        if (array == null || array.Count <= 0)
    //        {
    //            compute(dataEntities);
    //            return;
    //        }

    //        foreach(var data in datas)
    //        {
    //            var jObj = array.FirstOrDefault(x => (string)x["clientId"] == data.clientId) as JObject;
    //            if (jObj != null && ((bool)jObj["success"]))
    //            {
    //                var fprice = Convert.ToDecimal(data.entry["fprice"]);
    //                data.entry["fprice"] = fprice <= 0 ? Convert.ToDecimal(jObj["salPrice"]) : fprice;
    //                data.entry["fsellprice"] = Convert.ToDecimal(jObj["definedPrice"]);
    //            }
    //        }

    //        compute(dataEntities);
    //    }

    //    private void compute(List<DynamicObject> dataEntities)
    //    {
    //        foreach(var dataEntity in dataEntities)
    //        {
    //            var fentries = dataEntity["fentry"] as DynamicObjectCollection;
    //            decimal sum = 0;
    //            decimal sumdealamount = 0;
    //            decimal sumdistamount = 0;
    //            foreach (var fentry in fentries)
    //            {
    //                var fprice = Convert.ToDecimal(fentry["fprice"]);
    //                var amount = fprice * Convert.ToDecimal(fentry["fqty"]); //计算金额
    //                fentry["famount"] = amount; //合同初始化时成交金额等于金额
                                       
    //                //如果源单有成交价，则以源单为准
    //                var fdealprice = Convert.ToDecimal(fentry["fdealprice"]);
    //                if (fdealprice == 0)
    //                {
    //                    if (Convert.ToDecimal(fentry["fdistrate"]) == 0)
    //                    {
    //                        fentry["fdistrate"] = 10; //合同初始化时折扣等于10;
    //                        fentry["fdistamount"] = 0; //合同初始化时折扣金额等于0
    //                    }
    //                    fdealprice = fprice * Convert.ToDecimal(fentry["fdistrate"]) / 10;
    //                }
    //                var dealamount = fdealprice * Convert.ToDecimal(fentry["fqty"]);//计算成交金额
    //                fentry["fdealprice"] = fdealprice;
    //                fentry["fdealamount"] = dealamount;
    //                var fdistamount = amount - dealamount;
    //                fentry["fdistamount"] = fdistamount;
    //                sumdistamount += fdistamount;
    //                sum += amount;
    //                sumdealamount += dealamount;
    //            }
    //            dataEntity["fdealamount"] = sumdealamount; //合同初始化时成交总金额等于总金额
    //            dataEntity["ffaceamount"] = sum; //合同初始化时货品原值等于总金额
    //            dataEntity["fsumamount"] = sumdealamount; //合同初始化时订单总额等于成交总额
    //            dataEntity["funreceived"] = sum - Convert.ToDecimal(dataEntity["freceivable"]); //未收金额=成交总金额 + 费用收入 - 已收金额=订单总额 - 已收金额;
    //            dataEntity["fdistamount"] = sumdistamount;
    //        }
    //    }

    //    private void setSupplierInfo(List<string> productIds, List<DynamicObject> dataEntities)
    //    {
    //        var productForm = this.MetaModelService.LoadFormModel(this.UserContext, "ydj_product");
    //        var dm = this.UserContext.Container.GetService<IDataManager>();
    //        dm.InitDbContext(this.UserContext, productForm.GetDynamicObjectType(this.UserContext));
    //        var products = dm.Select(productIds).OfType<DynamicObject>().ToList();
    //        if (products == null || products.Count <= 0)
    //        {
    //            return;
    //        }

    //        foreach (var dataEntity in dataEntities)
    //        {
    //            var fentries = dataEntity["fentry"] as DynamicObjectCollection;
    //            foreach (var fentry in fentries)
    //            {
    //                var productId = Convert.ToString(fentry["fproductid"]);
    //                var product = products.FirstOrDefault(x => Convert.ToString(x["id"]) == productId);
    //                if (product!=null)
    //                {
    //                    fentry["fsupplierid"] = Convert.ToString(product["fsupplierid"]);
    //                }
    //            }
    //        }
    //    }

    //    //public override void OnPrepareQuerySourceObject(OnPrepareQuerySourceObjectEventArgs e)
    //    //{
    //    //    if (e == null || e.SourceQueryObjects == null)
    //    //    {
    //    //        return;
    //    //    }

    //    //    foreach (var item in e.SourceQueryObjects)
    //    //    {
    //    //        var queryObject = item.QueryObject;
    //    //        string sqlForm = queryObject.SqlFrom;
    //    //        var match = Regex.Match(sqlForm, MATCHTABLENAME, RegexOptions.IgnoreCase);
    //    //        if (match.Success)
    //    //        {
    //    //            var tableName = match.Groups[1].Value;
    //    //            string fattrinfoExpression = string.Format("{0}.fattrinfo As fattrinfo", tableName);
    //    //            queryObject.SqlFrom = Regex.Replace(sqlForm, REMOVELEFTJOINAUXPROPVALUE, string.Empty, RegexOptions.IgnoreCase);
    //    //            queryObject.SqlSelect = Regex.Replace(queryObject.SqlSelect, REPLACEFATTRINFO, fattrinfoExpression, RegexOptions.IgnoreCase);
    //    //        }
    //    //    }
    //    //}



    //    /// <summary>
    //    /// 获取源单数据后接口，允许自定义部分来源数据
    //    /// </summary>
    //    /// <param name="e"></param>
    //    public override void AfterGetSourceBillData(AfterGetSourceBillDataEventArgs e)
    //    {
    //        base.AfterGetSourceBillData(e);

    //        //if (e.SourceDataEntities == null || !e.SourceDataEntities.Any())
    //        //{
    //        //    throw new BusinessException("下推销售合同失败：数据实体不能为空！");
    //        //}
    //    }
    //}


    /// <summary>
    /// 销售意向单：生成销售合同
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("tocontractorder")]
    public class IntentToContract : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            #region 参数处理
            var fiswholedis = this.GetQueryOrSimpleParam<string>("fiswholedis");
            var fdisopt = this.GetQueryOrSimpleParam<string>("fdisopt");
            var fdiscscale = this.GetQueryOrSimpleParam<decimal>("fdiscscale", 10);// 折扣
            var fdiscscale_temp = this.GetQueryOrSimpleParam<decimal>("fdiscscale_temp");
            var fisfavor = this.GetQueryOrSimpleParam<string>("fisfavor");
            var ffavoropt = this.GetQueryOrSimpleParam<string>("ffavoropt");
            var ffavorway = this.GetQueryOrSimpleParam<string>("ffavorway");
            var ffixedprice = this.GetQueryOrSimpleParam<decimal>("ffixedprice");
            var fid = this.GetQueryOrSimpleParam<string>("opId");

            if (fid.IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("请求参数错误！");

            var billForm = this.MetaModelService?.LoadFormModel(this.Context, this.HtmlForm.Id);
            var dmIntent = this.Container.GetService<IDataManager>();
            dmIntent.InitDbContext(this.Context, billForm.GetDynamicObjectType(this.Context));
            var dataEntity = dmIntent.Select(fid) as DynamicObject;
            if (dataEntity == null)
            {
                throw new BusinessException($"Id为：{fid} 的 {billForm.Caption} 不存在或者已被删除，请检查！");
            }
            if (Convert.ToString(dataEntity["fstaffid"]).IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("生成销售合同请指派导购员！");

            #region 成单时判断下，要求已收定金等于应收定金
            if (Convert.ToDecimal(dataEntity["fcollectamount"]) > 0 && Convert.ToDecimal(dataEntity["fcollectedamount"]) <= 0)
            {
                throw new BusinessException("此单已收定金不允许小于等于0！");
            }
            //if (Convert.ToString(dataEntity["fcollectamount"]).EqualsIgnoreCase(Convert.ToString(dataEntity["fcollectedamount"])) == false)
            //{
            //    // 是否已收定金（从未收取，已收但未确认在收支记录里，未收全）
            //    var incomeForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_incomedisburse");
            //    var dmIncome = this.Context.Container.GetService<IDataManager>();
            //    dmIncome.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));
            //    var sqlParam = new SqlParam[]
            //    {
            //        new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
            //        new SqlParam("fsourceid", System.Data.DbType.String, dataEntity["id"])
            //    };
            //    var dataReader = this.Context.GetPkIdDataReader(incomeForm, "fmainorgid=@fmainorgid and fsourceid=@fsourceid and fbizstatus='bizstatus_01'", sqlParam);
            //    var existIncomeDyo = dmIncome.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            //    if (existIncomeDyo != null)
            //    {
            //        throw new BusinessException("此单存在未确认的已收款，请在结算确认后再成单！");
            //    }
            //}
            #endregion

            #region 是否已存在此单的 下推销售合同
            var orderFormId = "ydj_order";
            var orderForm = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, orderFormId);
            var dmService = this.Container.GetService<IDataManager>();
            dmService.InitDbContext(this.Context, orderForm.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(orderForm, "fsourcenumber=@fbillno and fmainorgid=@fmainorgid",
                 new SqlParam[]
                 {
                        new SqlParam("fbillno", System.Data.DbType.String, Convert.ToString(dataEntity["fbillno"])),
                        new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
                 });
            var existOrderDyo = dmService.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (existOrderDyo != null)
            {
                throw new BusinessException("此销售意向单已成单！请勿重复提交");
            }
            #endregion

            if (fdiscscale > 0)
            {
                //fdiscscale_temp = fdiscscale;
                //fdisopt = "1";
                ffavoropt = "";
                ffavorway = "";
                ffixedprice = 0;
                fisfavor = "0";
                //fiswholedis = "0";
            }
            else if (fdiscscale <= 0 || fdiscscale > 50)
            {
                throw new BusinessException("折扣输入不合理！");
            }
            #endregion

            #region 组装销售合同单数据
            DynamicObjectCollection intentEntry = dataEntity["fentity"] as DynamicObjectCollection;// 销售意向单  商品明细
            //if (intentEntry == null || !intentEntry.Any())
            //    throw new BusinessException("必须有至少一条商品明细数据！");
            //加载引用数据
            this.Container.GetService<LoadReferenceObjectManager>()?.Load(
                this.Context,
                this.HtmlForm.GetDynamicObjectType(this.Context),
                dataEntity,
                false);
            var dm = this.Container.GetService<IDataManager>();
            List<DynamicObject> contractOrders = new List<DynamicObject>();
            var prepairServ = this.Container.GetService<IPrepareSaveDataService>();
            var orderMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, orderFormId);
            dm.InitDbContext(this.Context, orderMeta.GetDynamicObjectType(this.Context));
            DynamicObject orderDyo = new DynamicObject(orderMeta.GetDynamicObjectType(this.Context));
            #region 基本信息
            orderDyo["ftype"] = "order_type_01";
            orderDyo["forderdate"] = dataEntity["fdate"];
            orderDyo["fdeliverydate"] = dataEntity["fpickdate"];
            orderDyo["fcustomerid"] = dataEntity["fcustomerid"];
            orderDyo["fphone"] = dataEntity["fphone_e"];
            orderDyo["fprovince"] = dataEntity["fprovince"];
            orderDyo["fcity"] = dataEntity["fcity"];
            orderDyo["fregion"] = dataEntity["fregion"];
            orderDyo["fprovince_ref"] = dataEntity["fprovince_ref"];
            orderDyo["fcity_ref"] = dataEntity["fcity_ref"];
            orderDyo["fregion_ref"] = dataEntity["fregion_ref"];
            orderDyo["faddress"] = dataEntity["faddress_e"];
            orderDyo["fdeptid"] = dataEntity["fdeptid"];
            orderDyo["fstaffid"] = dataEntity["fstaffid"];
            //orderDyo["fstylistid"] = dataEntity["fstylist"];
            orderDyo["fdesignscheme"] = dataEntity["fdesignscheme"];
            orderDyo["fscalerecord"] = dataEntity["fscalerecord"];
            orderDyo["fsourcetype"] = "ydj_saleintention";
            orderDyo["fsourcenumber"] = dataEntity["fbillno"];
            // 折扣设置
            orderDyo["fiswholedis"] = fiswholedis == "1" || fiswholedis.EqualsIgnoreCase("true");
            orderDyo["fdisopt"] = fdisopt;
            orderDyo["fdiscscale"] = fdiscscale;
            orderDyo["fdiscscale_temp"] = fdiscscale_temp;
            // 优惠设置
            orderDyo["fisfavor"] = fisfavor == "1" || fisfavor.EqualsIgnoreCase("true");
            orderDyo["ffavorway"] = ffavorway;
            orderDyo["ffavoropt"] = ffavoropt;
            //orderDyo["ffixedprice"] = ffixedprice;
            orderDyo["fchannel"]= dataEntity["fchannel"];//外部带单
            orderDyo["fdescription"] = dataEntity["fdescription"];//备注
            // 电商订单号、品牌
            orderDyo["fcommercebillno"] = dataEntity["fcommercebillno"];
            orderDyo["fbrandid"] = dataEntity["fbrandid"];

            // 客户、品牌、外部带单、所属门店、导购员
            orderDyo["fstatus"] = "B";
            orderDyo["fcustomerid_ref"] = dataEntity["fcustomerid_ref"];
            orderDyo["fbrandid_ref"] = dataEntity["fbrandid_ref"];
            orderDyo["fchannel_ref"] = dataEntity["fchannel_ref"];
            orderDyo["fdeptid_ref"] = dataEntity["fdeptid_ref"];
            orderDyo["fstaffid_ref"] = dataEntity["fstaffid_ref"];
            #endregion

            #region 商品明细
            var pro_entrydata = orderDyo["fentry"] as DynamicObjectCollection;
            var productEntity = orderMeta.GetEntryEntity("fentry");// 销售合同 商品明细
            foreach (var entry in intentEntry)
            {
                if (Convert.ToString(entry["fmaterialid"]).IsNullOrEmptyOrWhiteSpace()) continue;
                var proDyo = new DynamicObject(productEntity.DynamicObjectType);
                proDyo["fproductid"] = entry["fmaterialid"];
                proDyo["fproductid_ref"] = entry["fmaterialid_ref"];
                proDyo["funitid"] = entry["funitid"];
                proDyo["funitid_ref"] = entry["funitid_ref"];
                //proDyo["fattrinfo"] = entry["funitid"];
                proDyo["fqty"] = entry["fqty"];
                proDyo["fbizqty"] = entry["fbizqty"];
                proDyo["fprice"] = entry["fprice"];
                //出现货，提货方式、仓库、仓位、库存状态
                //proDyo["fisoutspot"] = entry["fisoutspot"];
                //proDyo["fdeliverymode"] = entry["fdeliverymode"];
                //proDyo["fstockstatus"] = entry["fstockstatus"];
                //proDyo["fstorehouseid"] = entry["fstorehouseid"];
                //proDyo["fstorelocationid"] = entry["fstorelocationid"];

                proDyo["famount"] = Convert.ToDecimal(entry["fprice"]) * Convert.ToDecimal(entry["fbizqty"]) * fdiscscale / 10;
                proDyo["fdistrate"] = fdiscscale <= 0 ? 10.0M : fdiscscale;
                proDyo["fdistamount"] = Convert.ToDecimal(entry["fprice"]) * Convert.ToDecimal(entry["fbizqty"]) * (10 - fdiscscale) / 10;
                proDyo["fdealprice"] = Convert.ToDecimal(entry["fprice"]) * fdiscscale / 10;
                proDyo["fdealamount"] = Convert.ToDecimal(entry["fprice"]) * Convert.ToDecimal(entry["fbizqty"]) * fdiscscale / 10;
                proDyo["fdescription"] = entry["fnote"];
                pro_entrydata.Add(proDyo);
            }

            // 财务信息
            orderDyo["fdistsumamount"] = pro_entrydata.Sum(p => Convert.ToDecimal(p["fdistamount"]));//货款总折扣额
            orderDyo["fdistsumrate"] = fdiscscale;//货款总折扣率
            orderDyo["fdistamount"] = pro_entrydata.Sum(p => Convert.ToDecimal(p["fdistamount"]));// 折扣金额
            orderDyo["ffaceamount"] = dataEntity["ffbillamount"];//货品原值
            orderDyo["fdealamount"] = pro_entrydata.Sum(p => Convert.ToDecimal(p["fdealamount"]));// 成交金额
            orderDyo["freceivable"] = dataEntity["fcollectedamount"];//已收
            orderDyo["funreceived"] = pro_entrydata.Sum(p => Convert.ToDecimal(p["famount"])) - Convert.ToDecimal(dataEntity["fcollectedamount"]);// 未收
            orderDyo["fexpense"] = 0.00M;
            orderDyo["fsumamount"] = pro_entrydata.Sum(p => Convert.ToDecimal(p["fdealamount"]));
            orderDyo["fcollectamount"] = dataEntity["fcollectamount"];// 应收订金
            orderDyo["fcollectedamount"] = dataEntity["fcollectedamount"];// 已收定金
            // 导购员信息
            var duty_entrydata = orderDyo["fdutyentry"] as DynamicObjectCollection;
            var staffEntity = orderMeta.GetEntryEntity("fdutyentry");
            var staffDyo = new DynamicObject(staffEntity.DynamicObjectType);
            staffDyo["fismain"] = true;
            staffDyo["fdutyid"] = dataEntity["fstaffid"];
            staffDyo["fratio"] = 100.00M;
            staffDyo["famount"] = pro_entrydata.Sum(p => Convert.ToDecimal(p["famount"]));
            staffDyo["fdescription"] = " ";
            duty_entrydata.Add(staffDyo);

            contractOrders.Add(orderDyo);
            #endregion
          //  prepairServ.PrepareDataEntity(this.Context, orderMeta, contractOrders.ToArray(), this.Option);
            #endregion

            #region 打包成前端的数据结构
            var uiDataConvert = this.Container.GetService<IUiDataConverter>();
            var billJsonData = uiDataConvert.CreateUIDataObject(this.Context, orderMeta, orderDyo);

            var action = this.Context.ShowSpecialForm(orderMeta,
                        orderDyo,
                        false,
                        Guid.NewGuid().ToString(),
                        Enu_OpenStyle.Default,
                        Enu_DomainType.Bill,
                        new Dictionary<string, object>
                {
                    { "sourceId", "" },
                    { "sourceFormId", this.HtmlForm.Id },
                    { "sourcePageId", this.GetQueryOrSimpleParam<string>("sourcePageId", "") }
                },
                (formPara) =>
                {
                    //formPara.FormCaption = "销售合同-新增";
                    formPara.Status = Enu_BillStatus.Push;
                    formPara.UiData = billJsonData.GetJsonValue("uiData", new JObject());
                },
                GetQueryOrSimpleParam<string>("containerId"));
            #endregion

            this.Result.HtmlActions.Add(action);
            this.Result.IsSuccess = true;
        }
    }

    /// <summary>
    /// 销售意向单:判断销售意向单是否已生成销售合同
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("SaleContract")]
    public class SaleContract: AbstractOperationServicePlugIn
    {
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);
            e.OpCtlParam.IgnoreOpMessage = true;
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var dataEntity = e.DataEntitys[0];

            this.Result.IsSuccess = true;
            this.Result.SrvData = new
            {
                saleId = Convert.ToString(dataEntity["forderid"])
            };

        }
    }
}