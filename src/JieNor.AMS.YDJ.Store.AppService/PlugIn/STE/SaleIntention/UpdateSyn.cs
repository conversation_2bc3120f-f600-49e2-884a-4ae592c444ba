using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：更新协同信息，该操作主要用于接收采购订单发起的协同更新
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("UpdateSyn")]
    public class UpdateSyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string tranId = this.GetQueryOrSimpleParam<string>("tranId");
            string synFiles = this.GetQueryOrSimpleParam<string>("synFiles");
            if (tranId.IsNullOrEmptyOrWhiteSpace() || synFiles.IsNullOrEmptyOrWhiteSpace()) return;

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

            var htmlDrawEntity = this.HtmlForm.GetEntryEntity("fdrawentity");

            string where = $"fmainorgid=@fmainorgid and ftranid=@ftranid";
            var sqlParam = new SqlParam[]
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("ftranid", System.Data.DbType.String, tranId)
            };
            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, sqlParam);
            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
            if (dataEntity != null)
            {
                //已有的协同文件明细
                List<DynamicObject> toRemoveList = new List<DynamicObject>();

                //本次要协同的文件明细
                var synFileList = synFiles?.FromJson<List<Dictionary<string, object>>>();

                DynamicObjectCollection drawEntitys = dataEntity["fdrawentity"] as DynamicObjectCollection;
                if (drawEntitys != null)
                {
                    //1.先删除上一次协同过来的文件明细
                    foreach (var drawEntity in drawEntitys)
                    {
                        if (!drawEntity["fsourceentryid"].IsNullOrEmptyOrWhiteSpace())
                        {
                            toRemoveList.Add(drawEntity);
                        }
                    }
                    foreach (var item in toRemoveList)
                    {
                        drawEntitys.Remove(item);
                    }

                    //2.再新增本次协同过来的文件明细
                    if (synFileList != null)
                    {
                        foreach (var synFile in synFileList)
                        {
                            DynamicObject synEntry = new DynamicObject(htmlDrawEntity.DynamicObjectType);
                            synEntry["ffilename"] = synFile["ffilename"];
                            synEntry["ffileid"] = synFile["ffileid"];
                            synEntry["fnote"] = synFile["fnote"];
                            synEntry["fuploader"] = synFile["fuploader"];
                            synEntry["fuptime"] = Convert.ToDateTime(synFile["fuptime"]);
                            synEntry["fsourceentryid"] = synFile["Id"];
                            if (synFile.ContainsKey("ffileformat")) synEntry["ffileformat"] = synFile["ffileformat"];
                            if (synFile.ContainsKey("ffilesize")) synEntry["ffilesize"] = synFile["ffilesize"];
                            drawEntitys.Add(synEntry);
                        }
                    }
                }

                //如果本次没有需要更新的协同明细，则不需要更新
                if (!toRemoveList.Any() && (synFileList == null || !synFileList.Any())) return;

                //生成主键ID
                var pkService = this.Container.GetService<IDataEntityPkService>();
                pkService.AutoSetPrimaryKey(this.Context, dataEntity, dm.DataEntityType);

                //保存
                dm.Save(dataEntity);

                //标记成功
                this.Result.IsSuccess = true;
            }
        }
    }
}