using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.SaleIntention
{
    /// <summary>
    /// 销售意向单：库存查询
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("QueryInventory")]
    public class QueryInventory : AbstractOperationServicePlugIn
    {
        /// <summary>
		/// 服务端自定义事件
		/// </summary>
		/// <param name="e"></param>
		public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "onafterqueryinventory":
                    var eventData = e.EventData as Tuple<HtmlEntryEntity, DynamicObjectCollection, List<string>>;
                    if (eventData != null)
                    {
                        if (eventData.Item2 != null)
                        {
                            //指定要打包的其他字段
                            eventData.Item3.Add("fisoutspot");

                            eventData.Item3.Add("fcostprice");//单位成本 

                            foreach (var entry in eventData.Item2)
                            {
                                entry["fisoutspot"] = true;
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }
}