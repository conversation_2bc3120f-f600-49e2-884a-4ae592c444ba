using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SaleIntention
{
    /// <summary>
    /// 销售订单：提交
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var saleService = this.Container.GetService<ISaleIntentionService>();
            var billNo = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if(!saleService.CheckIsSync(this.Context, newData))
                {
                    return true;
                }
                var fbizstatus = Convert.ToString(newData["fbizstatus"]);
                if (string.IsNullOrWhiteSpace(fbizstatus) || fbizstatus == "business_status_01" || fbizstatus == "business_status_02")
                {
                    billNo = Convert.ToString(newData["fbillno"]);
                    return false;
                }
                return true;
            }).WithMessage("编号为{0}的单据只有受理完成才能提交!", (billObj, propObj) => billNo));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !string.IsNullOrWhiteSpace(Convert.ToString(newData["fdeptid"]));
            }).WithMessage("销售部门不能为空，请选择销售部门并保存后再提交!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !string.IsNullOrWhiteSpace(Convert.ToString(newData["fstaffid"]));
            }).WithMessage("销售员不能为空，请选择销售员并保存后再提交!"));
        }
    }
}
