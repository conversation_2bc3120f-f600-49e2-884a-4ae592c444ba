using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.StoreSysParam
{
    /// <summary>
    /// 门店系统参数：获取参数
    /// </summary>
    [InjectService]
    [FormId("bas_storesysparam")]
    [OperationNo("LoadStoreSysParam")]
    public class LoadStoreSysParam : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var spService = this.Container.GetService<ISystemProfile>();
            string sysProfileValue = spService.GetProfile(this.Context, "fw", $"{this.HtmlForm.Id}_parameter");
            if (!sysProfileValue.IsNullOrEmptyOrWhiteSpace())
            {
                //为空，则读取总部的配置
                //var topContext = this.Context.CreateTopOrgDBContext();
                //string topSysProfileValue = spService.GetProfile(topContext, "fw", $"{this.HtmlForm.Id}_parameter");
                //JObject obj = JObject.Parse(sysProfileValue);
                //string fhomestaysd = obj.GetJsonValue("fhomestaysd", "");
                //if (string.IsNullOrWhiteSpace(fhomestaysd))
                //{
                //    if (!topSysProfileValue.IsNullOrEmptyOrWhiteSpace())
                //    {
                //        JObject topObj = JObject.Parse(topSysProfileValue);
                //        string topfopenomsservice = topObj.GetJsonValue("fhomestaysd", "");
                //        if (!string.IsNullOrWhiteSpace(topfopenomsservice))
                //        {
                //            obj.Add("fhomestaysd", topfopenomsservice);
                //        }
                //    }
                //}
                //string fhomestayzdzp = obj.GetJsonValue("fhomestayzdzp", "");
                //if (string.IsNullOrWhiteSpace(fhomestayzdzp))
                //{
                //    if (!topSysProfileValue.IsNullOrEmptyOrWhiteSpace())
                //    {
                //        JObject topObj = JObject.Parse(topSysProfileValue);
                //        string topfopenomsservice = topObj.GetJsonValue("fhomestayzdzp", "");
                //        if (!string.IsNullOrWhiteSpace(topfopenomsservice))
                //        {
                //            obj.Add("fhomestayzdzp", topfopenomsservice);
                //        }
                //    }
                //}
                //string fhomestaydjj = obj.GetJsonValue("fhomestaydjj", "");
                //if (string.IsNullOrWhiteSpace(fhomestaydjj))
                //{
                //    if (!topSysProfileValue.IsNullOrEmptyOrWhiteSpace())
                //    {
                //        JObject topObj = JObject.Parse(topSysProfileValue);
                //        string topfopenomsservice = topObj.GetJsonValue("fhomestaydjj", "");
                //        if (!string.IsNullOrWhiteSpace(topfopenomsservice))
                //        {
                //            obj.Add("fhomestaydjj", topfopenomsservice);
                //        }
                //    }
                //}
                this.Result.SrvData = JObject.Parse(sysProfileValue);
            }
            this.Result.IsSuccess = true;
        }
    }
}