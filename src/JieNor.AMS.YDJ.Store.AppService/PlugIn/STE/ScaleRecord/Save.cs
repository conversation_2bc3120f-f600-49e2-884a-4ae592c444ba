using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Consts;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.ScaleRecord
{
    /// <summary>
    /// 量尺记录：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_scalerecord")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fscaledate"]).NotEmpty().WithMessage("量尺日期不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fcustomerid"]).NotEmpty().WithMessage("客户不能为空！"));

            //验证量尺日期
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return this.CheckScaleDate(newData);
            }).WithMessage("量尺日期填写不合理！"));

            //检查预算
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((now, old) =>
            {
                return CheckBudget(now);
            }).WithMessage("预算必须填入数值：大于 0 、小于 999999999"));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            var sourceId = this.GetQueryOrSimpleParam<string>("sourceId", "");
            var sourceFormId = this.GetQueryOrSimpleParam<string>("sourceFormId", "");
            var sourcePageId = this.GetQueryOrSimpleParam<string>("sourcePageId", "");
            if (!sourceId.IsNullOrEmptyOrWhiteSpace()
                && !sourceFormId.IsNullOrEmptyOrWhiteSpace()
                && !sourcePageId.IsNullOrEmptyOrWhiteSpace())
            {
                //源单
                var sourceForm = this.MetaModelService?.LoadFormModel(this.Context, sourceFormId);
                if (sourceForm != null)
                {
                    //源单量尺记录字段
                    var designSchemeField = sourceForm.GetField("fscalerecord");
                    if (designSchemeField != null)
                    {
                        var dm = this.Container.GetService<IDataManager>();
                        dm.InitDbContext(this.Context, sourceForm.GetDynamicObjectType(this.Context));

                        var sourceBill = dm.Select(sourceId) as DynamicObject;
                        if (sourceBill != null)
                        {
                            //量尺记录编码字段值
                            var designSchemeValue = this.HtmlForm.GetNumberField()?.DynamicProperty?.GetValue(dataEntity);

                            //反写源单量尺记录字段值
                            designSchemeField?.DynamicProperty?.SetValue(sourceBill, designSchemeValue);
                            dm.Save(sourceBill);

                            //更新源单前端页面视图字段值
                            //this.AddSetValueAction("fscalerecord", designSchemeValue, null, sourcePageId);
                        }
                    }
                }
            }




        }

        /// <summary>
        /// 检查日期
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private bool CheckScaleDate(DynamicObject dataEntity)
        {
            string fscaledate = Convert.ToString(dataEntity["fscaledate"]);
            DateTime scaleDt;
            if (fscaledate.IsNullOrEmptyOrWhiteSpace() || !DateTime.TryParse(fscaledate, out scaleDt)
                || scaleDt.CompareTo(DateTime.Now) > 0 || DateTime.Now.Year - scaleDt.Year > 30)
                return false;
            return true;
        }

        /// <summary>
        /// 检查预算金额
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private bool CheckBudget(DynamicObject value)
        {
            decimal MaxBudgetMoney = 999999999;// 预算最大 maxlength="9"
            var fbudget = Convert.ToString(value["fbudget"]);
            decimal budgetMoney = 0M;
            if (fbudget.IsNullOrEmptyOrWhiteSpace()) return true;
            if (!fbudget.IsNullOrEmptyOrWhiteSpace() && !decimal.TryParse(fbudget, out budgetMoney))
            {
                return false;
            }
            return !(budgetMoney < 0 || budgetMoney > MaxBudgetMoney);
        }
    }
}