using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 客流记录--删除
    /// </summary>
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("Delete")]
    public class CustomerRecordDelete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //e.Rules.Add(this.RuleFor("fbillhead", (dataObj) => dataObj["fchancestatus"] as string)
            //    .IsTrue((dataObj, leadStatus) => leadStatus.EqualsIgnoreCase("chance_status_01"))
            //    .WithMessage("机会删除失败：{0}，业务状态必须为【未分配】才可删除！", (dataObj, leadStatus) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    return Convert.ToString(newData["fintentionno"]).IsNullOrEmptyOrWhiteSpace() &&
                           Convert.ToString(newData["forderno"]).IsNullOrEmptyOrWhiteSpace();
                }).WithMessage("已有意向单或已下合同，不允许删除！"));
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            var dataEntities = e.DataEntitys;
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var leadIds = dataEntities.Select(x => Convert.ToString(x["fleadssourceid"])).Where(x => !string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            if (leadIds == null && leadIds.Count <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_leads");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var dbEntities = dm.Select(leadIds).OfType<DynamicObject>().ToList();

            if (dbEntities == null || dbEntities.Count <= 0)
            {
                return;
            }

            foreach (var dbEntity in dbEntities)
            {
                dbEntity["fleadsstatus"] = "leads_status_02";
            }

            dm.Save(dbEntities);
        }
    }
}
