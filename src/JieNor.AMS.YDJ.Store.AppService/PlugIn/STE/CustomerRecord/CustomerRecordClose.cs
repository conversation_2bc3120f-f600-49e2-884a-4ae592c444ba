using System;
using System.Collections.Generic;
using System.Data.SqlTypes;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 销售机会：关闭
    /// </summary>
    /// 
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("customerrecordclose")]
    public class CustomerRecordClose : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 验证时间：业务状态不为已转商机才可关闭
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {

            e.Rules.Add(this.RuleFor("fbillhead", (dataObj) => dataObj["fchancestatus"] as string)
                .IsTrue((dataObj, leadStatus) => !leadStatus.EqualsIgnoreCase("chance_status_05"))
                .WithMessage("机会关闭失败：{0}，业务状态不为【已转客户】才可关闭！", (dataObj, leadStatus) => dataObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", (dataObj) => dataObj["fphase"] as string)
                .IsTrue((dataObj, phase) => !phase.EqualsIgnoreCase("customerrecord_phase_05"))
                .WithMessage("机会关闭失败：{0}，合同已成交，不能关闭！", (dataObj, phase) => dataObj["fbillno"]));
        }

        /// <summary>
        /// 开始事务前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                throw new BusinessException("请至少选择一条有效机会进行此操作！");
            }

            base.BeforeExecuteOperationTransaction(e);

            //关闭原因
            var closeReason = this.GetQueryOrSimpleParam<string>("closeReason");
            var closeEnum = this.GetQueryOrSimpleParam<string>("closeEnum");
            var closeEnumName = this.GetQueryOrSimpleParam<string>("closeEnumName");

            if (closeEnum.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请选择商机关闭原因！");
            }

            ICustomerRecordService customerRecordService = this.Container.GetService<ICustomerRecordService>();
            customerRecordService.Close(this.Context, e.DataEntitys, closeReason, closeEnum);

            // 数据读写引擎
            var dm = this.GetDataManager();
            //初始化上下文
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            //将选中的数据存放在读写引擎中
            dm.Save(e.DataEntitys);

            // 添加跟进记录
            AddFollowerRecord(e.DataEntitys, closeReason, closeEnum, closeEnumName);

            // 作废意向单和合同单
            CancelSaleIntentionAndOrder(e.DataEntitys);

            SyncCloseMusi(e.DataEntitys,closeEnumName);

            //添加刷新页面指令 页面刷新
            this.AddRefreshPageAction();
            //操作成功
            this.Result.IsSuccess = true;
            //提示关闭成功
            this.Result.SimpleMessage = "关闭成功!";

        }

        /// <summary>
        /// 同步关闭
        /// </summary>
        /// <param name="purchaseOrders">同步关闭</param>
        private void SyncCloseMusi(IEnumerable<DynamicObject> purchaseOrders,string closeEnumName)
        {
            Task.Run(() =>
            {
                foreach (var item in purchaseOrders)
                {
                    Dictionary<string, object> DataParam = new Dictionary<string, object>();
                    DataParam["opportunityId"] = item["oppid"];
                    DataParam["status"] = "Close";
                    DataParam["closeUserId"] = item["fcloseid"];
                    DataParam["closeReason"] = closeEnumName;
                    DataParam["type"] = "";
                    DataParam["closeTime"] = item["fclosedate"];

                    MuSiSaleApi.SendCustomerRecord(this.Context, MuSiSaleApi.saveOrUpdate, this.HtmlForm, DataParam);
                }
            }); 
        }

        /// <summary>
        /// 添加跟进记录
        /// </summary>
        /// <param name="dataEntitys"></param>
        /// <param name="closeReason"></param>
        private void AddFollowerRecord(DynamicObject[] dataEntitys, string closeReason, string closeEnum, string closeEnumName)
        {
            //var dm = this.GetDataManager();
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord");
            var formDt = htmlForm.GetDynamicObjectType(this.Context);
            //dm.InitDbContext(this.Context, formDt);

            List<DynamicObject> followerRecordObjs = new List<DynamicObject>();

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            string deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            string staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;

            foreach (var item in dataEntitys)
            {
                var followerRecordObj = new DynamicObject(formDt);

                followerRecordObj["fcustomerid"] = item["fcustomerid"];
                followerRecordObj["fcontacts"] = item["fcustomername"];
                followerRecordObj["fphone"] = item["fphone"];
                followerRecordObj["ffollowtime"] = DateTime.Now;
                followerRecordObj["ffollowerid"] = this.Context.UserId;
                followerRecordObj["fdeptid"] = deptId;
                followerRecordObj["fstaffid"] = staffId;
                followerRecordObj["ftype"] = "6";       // 默认是其他
                followerRecordObj["fdescription"] = string.Format("商机已关闭，原因：{0}，备注：{1}", closeEnumName, closeReason);
                followerRecordObj["fobjecttype"] = "objecttype18";  // 失效关闭
                followerRecordObj["fobjectid"] = item["id"];
                followerRecordObj["fobjectno"] = item["fbillno"];
                followerRecordObj["frelatedbilltype"] = this.HtmlForm.Id;
                followerRecordObj["frelatedbillno"] = item["fbillno"];
                followerRecordObj["fsourcetype"] = this.HtmlForm.Id;
                followerRecordObj["fsourcenumber"] = item["fbillno"];
                followerRecordObj["ftranid"] = item["ftranid"];

                followerRecordObjs.Add(followerRecordObj);
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", followerRecordObjs, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "添加跟进记录失败！");
        }

        /// <summary>
        /// 作废销售意向和销售合同
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void CancelSaleIntentionAndOrder(DynamicObject[] dataEntitys)
        {
            var saleIntentionNos = dataEntitys.Select(s => Convert.ToString(s["fintentionno"]))
                .Where(s => s.IsNullOrEmptyOrWhiteSpace() == false);

            if (saleIntentionNos.Any())
            {
                var saleIntentionObjs = this.Context.LoadBizDataByNo("ydj_saleintention", "fbillno", saleIntentionNos);
                if (saleIntentionObjs != null && saleIntentionObjs.Any())
                {
                    var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_saleintention", saleIntentionObjs, "cancel", new Dictionary<string, object>());
                    result.ThrowIfHasError(true, "销售意向作废失败！");
                }
            }

            var orderNos = dataEntitys.Select(s => Convert.ToString(s["forderno"]))
                .Where(s => s.IsNullOrEmptyOrWhiteSpace() == false);

            if (orderNos.Any())
            {
                var orderObjs = this.Context.LoadBizDataByNo("ydj_order", "fbillno", orderNos);
                if (orderObjs != null && orderObjs.Any())
                {
                    var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_order", orderObjs, "cancel", new Dictionary<string, object>());
                    result.ThrowIfHasError(true, "销售合同作废失败！");
                }
            }
        }
    }
}
