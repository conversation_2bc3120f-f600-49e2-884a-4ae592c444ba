//using JieNor.AMS.YDJ.Store.AppService.MuSi;
//using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.SuperOrm.DataEntity;
//using Newtonsoft.Json.Linq;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
//{
//    public class MuSiSyncData : AbstractOperationServicePlugIn
//    {

//        public static MuSiClient msClient { get; set; }


//        private static MuSiSyncData msData = null;

//        private static string apiNo;
//        private static string appKey;


//        public static void Send(UserContext context,DynamicObject customerrecord)
//        {
//            //业务对象映射关系
//            var fieldMapObj = context.LoadBizDataByNo("si_musibizobjmap", "fnumber", new string[] { "MSZTYWDXYS000000000015" }).FirstOrDefault()?.Clone(false, false) as DynamicObject;

//            if (fieldMapObj == null) return;

//            var ExtAppId = Convert.ToString(fieldMapObj["fextappid"]);
//            var server = context.Container.GetService<IMuSiService>();
//            //外部应用
//            var ExtAppObj = server.GetExternalAppObject(context, ExtAppId);
//            apiNo = Convert.ToString(fieldMapObj["fname"]); //接口编码

//            appKey = Convert.ToString(ExtAppObj["fappkey"]);

//            var appConfig = JObject.Parse(appKey);

//            var appId = appConfig.GetJsonValue("appId", "");
//            var appSecret = appConfig.GetJsonValue("appSecret", "");
//            var url = Convert.ToString(ExtAppObj["fserverurl"]);

//            msClient = new MuSiClient(appId, appSecret, url);
//            var result = msClient.Send<Dictionary<string, object>>(apiNo, LoadParamData(customerrecord), appKey);

//            string flagKey = "";
//            string messageKey = "";
//            //获取标记位
//            result.TryGetValue(flagKey, out var flag);
//            //获取消息
//            result.TryGetValue(messageKey, out var message);

//        }

//        /// <summary>
//        /// 数据封装
//        /// </summary>
//        /// <param name="customerrecord"></param>
//        public static Dictionary<string, object> LoadParamData(DynamicObject customerrecord)
//        {
//            //请求参数
//            Dictionary<string, object> DataParam = new Dictionary<string, object>();
//            DataParam.Add("id", Convert.ToString(customerrecord["oppid"]).Trim());
//            DataParam.Add("crmId", customerrecord["fbillno"]);
//            DataParam.Add("opportunityName", customerrecord["fcustomername"]);

//            switch (Convert.ToString(customerrecord["fchancestatus"]))
//            {
//                //商机状态 0:待下发,1:待分配,Approved:已下单,Closed:关闭(战败),New:新建(跟进中),Wait Follow:待跟进 【必填】"
//                case "chance_status_01":
//                    DataParam.Add("status", "1");
//                    break;
//                case "chance_status_02":
//                    DataParam.Add("status", "Wait Follow");
//                    break;
//                case "chance_status_03":
//                    DataParam.Add("status", "New");
//                    break;
//                case "chance_status_04":
//                    DataParam.Add("status", "Closed");
//                    break;
//                case "chance_status_06":
//                    DataParam.Add("status", "Approved");
//                    break;
//                default:
//                    DataParam.Add("status", "0");
//                    break;
//            }
//            DataParam.Add("source", "1");
//            DataParam.Add("phone", customerrecord["fphone"]);
//            DataParam.Add("bossId", customerrecord["fmainorgid"]);
//            DataParam.Add("headquartersId", customerrecord["fdeptid"]);
//            DataParam.Add("headquartersName", (customerrecord["fdeptid_ref"] as DynamicObject)?["fname"]);
//            DataParam.Add("followPerson", customerrecord["ffollowerid"]);
//            DataParam.Add("followPersonCrmId", customerrecord["ffollowerid"]);
//            DataParam.Add("followPersonName", (customerrecord["ffollowerid_ref"] as DynamicObject)?["fname"]);
//            DataParam.Add("recommendAccountId", customerrecord["freferrer"]);
//            DataParam.Add("closeReason", customerrecord["fclosereason"]);
//            DataParam.Add("stylePref", (customerrecord["fstyle_ref"] as DynamicObject)?["fenumitem"]);
//            DataParam.Add("progress", customerrecord["frequirement_type_ref"]);
//            DataParam.Add("budgetRegion", customerrecord["fbudget_scope"]);
//            DataParam.Add("urgency", customerrecord["furgency_type"]);
//            DataParam.Add("createBy", customerrecord["fcreatorid"]);
//            DataParam.Add("createName", (customerrecord["fcreatorid_ref"] as DynamicObject)?["fname"]);
//            DataParam.Add("remark", customerrecord["fdescription"]);
//            DataParam.Add("goodsDimensions", customerrecord["fproduct_size"]);
//            DataParam.Add("spaceRequirements", (customerrecord["fspace_ref"] as DynamicObject)?["fenumitem"]);
//            DataParam.Add("deliveredHouse", Convert.ToBoolean(customerrecord["fdelivery_type"]) ? 1 : 0);
//            DataParam.Add("addWechat", Convert.ToBoolean(customerrecord["fadd_wechat_type"]) ? 1 : 0);
//            DataParam.Add("intention", Convert.ToBoolean(customerrecord["fintention_sign"]) ? 1 : 0);
//            DataParam.Add("enterTheStore", Convert.ToBoolean(customerrecord["fvisit_type"]) ? 1 : 0);
//            DataParam.Add("willingTheStore", Convert.ToBoolean(customerrecord["fwill_visit_type"]) ? 1 : 0);
//            DataParam.Add("apartmentSize", customerrecord["house_type"]);
//            DataParam.Add("updateBy", customerrecord["fcreatorid"]);
//            //客户信息
//            Dictionary<string, object> customerDetailsUpdate = new Dictionary<string, object>();
//            customerDetailsUpdate.Add("phone", customerrecord["fphone"]);
//            customerDetailsUpdate.Add("username", customerrecord["fcustomername"]);
//            customerDetailsUpdate.Add("sex", "Mr");
//            customerDetailsUpdate.Add("nationality", "中国");
//            customerDetailsUpdate.Add("provinceName", (customerrecord["fprovince_ref"] as DynamicObject)?["fenumitem"]);
//            customerDetailsUpdate.Add("cityName", (customerrecord["fcity_ref"] as DynamicObject)?["fenumitem"]);
//            customerDetailsUpdate.Add("address", customerrecord["faddress"]);
//            DataParam.Add("customerDetailsUpdate", customerDetailsUpdate);
//            //商机地址
//            Dictionary<string, object> cusAddress = new Dictionary<string, object>();
//            cusAddress.Add("country", (customerrecord["fcountry_ref"] as DynamicObject)?["fenumitem"]);
//            cusAddress.Add("province", customerrecord["fprovince"]);
//            cusAddress.Add("provinceName", (customerrecord["fprovince_ref"] as DynamicObject)?["fenumitem"]);
//            cusAddress.Add("city", customerrecord["fcity"]);
//            cusAddress.Add("cityName", (customerrecord["fcity_ref"] as DynamicObject)?["fenumitem"]);
//            cusAddress.Add("address", customerrecord["faddress"]);
//            DataParam.Add("cusAddress", cusAddress);
//            //商机地址
//            List<Dictionary<string, object>> itemList = new List<Dictionary<string, object>>();

//            var prod_entry = customerrecord["fentry"] as DynamicObjectCollection;
//            foreach (var item in prod_entry)
//            {
//                Dictionary<string, object> prodDic = new Dictionary<string, object>();
//                prodDic.Add("goodsId", (item["goods_id_ref"] as DynamicObject)?["fnumber"]);
//                prodDic.Add("quantity", item["fattention_num"]);
//                itemList.Add(prodDic);
//            }
//            DataParam.Add("itemList", itemList);
//            return DataParam;

//        }
//    }

//}
