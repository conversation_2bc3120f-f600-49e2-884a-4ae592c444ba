using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：接口附件处理
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("OrderFileSave")]
    public class OrderFileSave : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string mes = "";
            base.BeginOperationTransaction(e);
            string param = this.GetQueryOrSimpleParam<string>("dto", "");

            if (string.IsNullOrWhiteSpace(param)) return;
            Model dto = JsonConvert.DeserializeObject<Model>(param);
            var obj = this.Context.LoadBizDataById(this.HtmlForm.Id, dto.fid);
            if (obj == null)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "未查询到当前销售合同！";
                return;
            }
            var attrEntrys = this.GetAttachmentList(obj);
            if (attrEntrys == null)
            {
                return;
            }
            var entrys = attrEntrys["fdrawentity"] as DynamicObjectCollection;
            var entryItem = entrys.Where(a => Convert.ToString(a["id"]).Equals(dto.attachmentId)).FirstOrDefault();

            string fileName = dto.fileAddr.Substring(dto.fileAddr.LastIndexOf("/") + 1, dto.fileAddr.Length - dto.fileAddr.LastIndexOf("/") - 1);
            var fileResult = PostFile(dto.fileAddr, fileName);
            if (!Convert.ToBoolean(fileResult["isSuccess"]))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "附件上传失败，请重试！";
                return;
            }
            if (dto.isDel)
            {
                if (entryItem == null)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "未查询到需要删除的明细行，请检查数据！";
                    return;
                }
                else
                {
                    entrys.Remove(entryItem);
                }
            }
            else
            {
                if (entryItem == null)
                {
                    //新增行
                    DynamicObject fileDetail = entrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    fileDetail["id"] = dto.attachmentId;
                    fileDetail["ffilename"] = fileName;
                    fileDetail["ffileid"] = fileResult["fileId"];
                    fileDetail["ffileformat"] = Path.GetExtension(fileName)?.TrimStart('.');
                    fileDetail["ffilesize"] = fileResult["fileSize"];
                    fileDetail["fuploader"] = this.Context.UserName;
                    fileDetail["fuploaderid"] = this.Context.UserId;
                    fileDetail["ffilegrouping"] = dto.fileGroup;
                    fileDetail["ftabledrawing"] = dto.isTableDrawing;
                    fileDetail["fuploadtime"] = string.IsNullOrWhiteSpace(dto.modifydate) ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : DateTimeUtil.GetBeiJingTime(Convert.ToInt64(dto.modifydate)).ToString("yyyy-MM-dd HH:mm:ss");
                    entrys.Add(fileDetail);
                }
                else
                {
                    //编辑行
                    //entryItem
                    //entryItem["id"] = dto.attachmentId;
                    entryItem["ffilename"] = fileName;
                    entryItem["ffileid"] = fileResult["fileId"];
                    entryItem["ffileformat"] = Path.GetExtension(fileName)?.TrimStart('.');
                    entryItem["ffilesize"] = fileResult["fileSize"];
                    entryItem["fuploadtime"] = string.IsNullOrWhiteSpace(dto.modifydate) ? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") : DateTimeUtil.GetBeiJingTime(Convert.ToInt64(dto.modifydate)).ToString("yyyy-MM-dd HH:mm:ss");
                }
            }
            var gateway = this.Context.Container.GetService<IHttpServiceInvoker>();
            //重新下发的，需要删除附件
            var attachlist = gateway.InvokeBillOperation(this.Context, "bd_attachlist", new[] { attrEntrys }, "save",
                new Dictionary<string, object>());
        }



        /// <summary>
        /// 时间戳转时间
        /// </summary>
        /// <param name="timestamp">时间戳</param>
        /// <returns></returns>
        private static DateTime TimeStampToDateTime(string timestamp)
        {
            if (!long.TryParse(timestamp, out var ts))
            {
                return new DateTime(1900, 1, 1);
            }

            return DateTimeOffset.FromUnixTimeMilliseconds(ts).DateTime;
        }


        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private Dictionary<string, string> PostFile(string filePath, string fileName)
        {
            Dictionary<string, string> json = new Dictionary<string, string>();
            try
            {
                string newFilePath = filePath;
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", $"文件路径不能为空!"}
                };
                }


                var startupPath = PathUtils.GetStartupPath();
                filePath = Path.Combine(startupPath, "");
                bool flag = FileLoad(newFilePath, fileName, filePath + "\\swjTmpFile\\");
                if (!flag)
                {
                    WebResponse response = null;
                    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(newFilePath);
                    response = request.GetResponse();
                    var stream = response.GetResponseStream();
                    //保存操作
                    var Value = SaveBinaryFile(response, filePath + "\\swjTmpFile\\" + fileName);
                }
                FileInfo t = new FileInfo(filePath + "\\swjTmpFile\\" + fileName);//获取文件
                var fileSize = GetFileSize(t.Length);
                var fileServerInfo = this.GetFirstWorkFileServer();
                if (fileServerInfo == null)
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器信息"}
                };
                }
                string baseUrl = fileServerInfo.Url;
                if (string.IsNullOrWhiteSpace(baseUrl))
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器的地址信息"}
                };
                }

                if (baseUrl.EndsWith("/"))
                {
                    baseUrl = baseUrl.Substring(0, baseUrl.Length - 1);
                }

                string url = $"{baseUrl}/FileInfo/AjaxUpload";

                Dictionary<string, string> headers = new Dictionary<string, string>();

                Dictionary<string, string> keyValues = new Dictionary<string, string>();

                Dictionary<string, string> fileList = new Dictionary<string, string>();
                fileList.Add("file", filePath + "\\swjTmpFile\\" + fileName);

                headers = new Dictionary<string, string>();
                headers.Add("sysCode", fileServerInfo.SysCode);
                headers.Add("authCode", fileServerInfo.AuthCode);
                headers.Add("isThumbnail", "true");
                headers.Add("isPrintText", "false");
                //headers.Add("Content-Type", "application/octet-stream");
                headers.Add("Content-Disposition", "form-data; name=\"file\"; filename=\"" + fileName + "\"");
                keyValues = new Dictionary<string, string>();
                keyValues.Add("id", "WU_FILE_0");
                keyValues.Add("name", fileName);
                keyValues.Add("type", System.Web.MimeMapping.GetMimeMapping(fileName));
                //var result1 = this.Context.Container.GetService<IFileInfoService>().SendHttpRequestPost(url, keyValues, fileList, Encoding.UTF8, headers);


                var result = this.Container.GetService<IFileInfoService>().SendHttpRequestPost(url, keyValues, fileList, Encoding.UTF8, headers);
                if (string.IsNullOrWhiteSpace(result))
                {
                    return new Dictionary<string, string>
                        {
                            { "isSuccess", "false"},
                            { "errMsg", "文件服务无应答消息"}
                        };
                }
                json = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(result);
                var kvp = json?.FirstOrDefault(x => string.Equals("fileId", x.Key, StringComparison.OrdinalIgnoreCase));
                if (kvp == null || string.IsNullOrWhiteSpace(kvp.Value.Value))
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "文件服务没有正确返回文件id"}
                };
                }
                Stream stream1 = new FileStream(filePath + "\\swjTmpFile\\" + fileName, FileMode.Open);
                var hWCloudFile = this.Context.Container.GetService<IHWCloudFileService>();
                string hwresult = hWCloudFile.UploadFile(json["fileId"], stream1);
                json["fileSize"] = fileSize;
                json["isSuccess"] = "true";
                return json;
            }
            catch (Exception ex)
            {
                //loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，文件上传，Error】，内容:{4}".Fmt(this.Context.UserName,
                //        this.Context.UserPhone, this.Context.Company,
                //     DateTime.Now.ToString("HH:mm:ss"), "filePath" + filePath + "fileName" + fileName + "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                //    "SWJLogFile");
                json["isSuccess"] = "false";
                return json;
            }
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="interAddress">在线地址</param>
        /// <param name="fileName">客户端保存的文件名</param>
        /// <param name="filePath">保存的文件夹路径</param>
        /// <returns></returns>
        public bool FileLoad(string interAddress, string fileName, string filePath)
        {
            try
            {
                //判断保存的文件夹是否存在
                if (!Directory.Exists(filePath))
                {
                    //不存在则创建
                    Directory.CreateDirectory(filePath);
                }
                //System.Net 中的验证和下载方法
                WebClient client = new WebClient();
                client.Credentials = CredentialCache.DefaultCredentials;
                client.DownloadFile(interAddress, filePath + "\\" + fileName);
                return true;
            }
            catch (Exception ex)
            {
                //loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，下载文件，Error】，内容:{4}".Fmt(this.Context.UserName,
                //        this.Context.UserPhone, this.Context.Company,
                //     DateTime.Now.ToString("HH:mm:ss"), "interAddress：【" + interAddress + "】filePath【" + filePath + "】fileName【" + fileName + "】ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                //    "SWJLogFile");
            }
            return false;
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="filesize">文件传入大小</param>
        /// <returns></returns>
        private static string GetFileSize(long filesize)
        {
            try
            {
                if (filesize < 0)
                {
                    return "0";
                }
                else if (filesize >= 1024 * 1024 * 1024)  //文件大小大于或等于1024MB    
                {
                    return string.Format("{0:0.00} GB", (double)filesize / (1024 * 1024 * 1024));
                }
                else if (filesize >= 1024 * 1024) //文件大小大于或等于1024KB    
                {
                    return string.Format("{0:0.00} MB", (double)filesize / (1024 * 1024));
                }
                else if (filesize >= 1024) //文件大小大于等于1024bytes    
                {
                    return string.Format("{0:0.00} KB", (double)filesize / 1024);
                }
                else
                {
                    return string.Format("{0:0.00} bytes", filesize);
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }


        /// <summary>
        /// 将二进制文件保存到磁盘
        /// </summary>
        /// <param name="response"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private static bool SaveBinaryFile(WebResponse response, string fileName)
        {
            bool Value = true;
            byte[] buffer = new byte[1024];

            try
            {
                if (File.Exists(fileName)) File.Delete(fileName);

                Stream outStream = File.Create(fileName);
                Stream inStream = response.GetResponseStream();

                int l;
                do
                {
                    l = inStream.Read(buffer, 0, buffer.Length);
                    if (l > 0)
                        outStream.Write(buffer, 0, l);
                }
                while (l > 0);

                outStream.Close();
                inStream.Close();
            }
            catch (Exception ex)
            {
                Value = false;
            }
            return Value;
        }


        private DynamicObject GetAttachmentList(DynamicObject orderObj)
        {
            var attachFormMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_attachlist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, attachFormMeta.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(attachFormMeta, "flinkformid=@linkFormId and flinkbillinterid=@linkBillId", new SqlParam[]
            {
                new SqlParam("linkFormId", System.Data.DbType.String, "ydj_order"),
                new SqlParam("linkBillId", System.Data.DbType.String, orderObj["id"]),
            });

            var linkAttachBillObj = dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (linkAttachBillObj == null)
            {
                DynamicObject fileObj = new DynamicObject(attachFormMeta.GetDynamicObjectType(this.Context));
                var fileItem = fileObj.DynamicObjectType.CreateInstance() as DynamicObject;
                fileItem["FFormId"] = "bd_attachlist";
                fileItem["flinkformid"] = "ydj_order";
                fileItem["flinkbillno"] = orderObj["fbillno"];
                fileItem["flinkbillinterid"] = orderObj["id"];
                fileItem["flinkbilltranid"] = orderObj["ftranid"];
                return fileItem;

            }


            //var entrys = (DynamicObjectCollection)linkAttachBillObj["fdrawentity"];
            //var entrys = linkAttachBillObj["fdrawentity"] as DynamicObjectCollection;
            //var copyItem = linkAttachBillObj.Clone() as DynamicObject;

            //entrys.Clear();
            ////linkAttachBillObj["fdrawentity"]. = entrys.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["ffilegrouping"])));
            //var isnull = (copyItem["fdrawentity"] as DynamicObjectCollection).Where(a => string.IsNullOrWhiteSpace(Convert.ToString(a["ffilegrouping"])));
            //foreach (var item in isnull)
            //{
            //    entrys.Add(item);
            //}
            return linkAttachBillObj;
        }

        private class Model
        {

            public string fnumber { get; set; }
            public string fmainorgid { get; set; }
            public string fid { get; set; }
            /// <summary>
            /// 合同流水号
            /// </summary>
            public string tranid { get; set; }
            /// <summary>
            /// 合同编号
            /// </summary>
            public string fbillno { get; set; }
            /// <summary>
            /// 附件明细行行内码
            /// </summary>
            public string entryid { get; set; }

            /// <summary>
            /// 附件id
            /// </summary>
            public string attachmentId { get; set; }
            /// <summary>
            /// 文件在线地址
            /// </summary>
            public string fileAddr { get; set; }

            /// <summary>
            /// 是否删除附件
            /// </summary>
            public bool isDel { get; set; }

            /// <summary>
            /// 是否台面图纸
            /// </summary>
            public bool isTableDrawing { get; set; }
            /// <summary>
            /// 订单附件分组
            /// </summary>
            public string fileGroup { get; set; }

            /// <summary>
            /// 备注
            /// </summary>
            public string remark { get; set; }

            /// <summary>
            /// 附件更新时间
            /// </summary>
            public string modifydate { get; set; }
        }
    }
}
