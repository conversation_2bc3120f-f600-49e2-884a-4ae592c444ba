using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同反审核：检查是否存在预留，存在预留，不允许反审核
    /// </summary>
    public class Validation_ReserveInfo : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }
            var fmainorgids = dataEntities.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
            var agentInfo = GetAgentInfo(userCtx, fmainorgids);
            CheckOrderData(userCtx, dataEntities, agentInfo);

            CheckReserveInfo(userCtx, formInfo, dataEntities, result, option);
            CheckOrderIsSubmitToTopOrg(userCtx, dataEntities,result);
            return result;
        }

        private void CheckReserveInfo(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option)
        {
            var sql = "";
            var tempTable = "";
            DynamicObjectCollection datas;
            var ids = dataEntitys.Select(f => Convert.ToString(f["Id"])).ToList();
            using (var tran = ctx.CreateTransaction())
            {
                if (dataEntitys.Length < 20)
                {
                    sql = @" select  a.fid,c.fbillno
			                            from t_ydj_orderentry a
			                            inner join t_stk_reservebillentry b on a.fentryid=b.fsourceentryid  and a.fid=b.fsourceinterid
			                            inner join t_stk_reservebill c on b.fid=c.fid 
			                            where  a.fid in ({0}) and c.fsourcetype='ydj_order'  and b.fqty>0 ".Fmt(ids.JoinEx(",", true));
                }
                else
                {
                    tempTable = this.DBService.CreateTempTableWithDataList(ctx, ids, false);
                    sql = @" select  a.fid,c.fbillno
			                            from t_ydj_orderentry a
			                            inner join t_stk_reservebillentry b on a.fentryid=b.fsourceentryid  and a.fid=b.fsourceinterid
			                            inner join t_stk_reservebill c on b.fid=c.fid 
                                        inner join {0} x on a.fid=x.fid 
			                            where  c.fsourcetype='ydj_order' and b.fqty>0 ".Fmt(tempTable);
                }
                datas = this.DBService.ExecuteDynamicObject(ctx, sql);

                tran.Complete();

                this.DBService.DeleteTempTableByName(ctx, tempTable, true);
            }

            if (!datas.Any())
            {
                return;
            }

            foreach (var item in dataEntitys)
            {
                var orderId = Convert.ToString(item["Id"]);
                var exist = datas.FirstOrDefault(f => Convert.ToString(f["fid"]) == orderId);

                if (exist == null)
                {
                    continue;
                }

                result.Errors.Add(new ValidationResultEntry()
                {
                    ErrorMessage = @"销售合同{0}对应的预留单{1}存在预留未释放，请先手工释放预留后再反审核！".Fmt(item["fbillno"], exist["fbillno"]),
                    DataEntity = item,
                });
            }

        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="agentInfo"></param>
        /// <param name="getRenewType"></param>
        private void CheckOrderData(UserContext userCtx, IEnumerable<DynamicObject> dataEntities, Dictionary<string, bool> agentInfo)
        {
            foreach (var dataEntity in dataEntities)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]);
                //创建单据的经销商的经销类型-直营
                var fmainorgid = Convert.ToString(dataEntity["fmainorgid"]);
                var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);//勾选焕新订单标记
                var fchstatus = Convert.ToString(dataEntity["fchstatus"]);//协同总部状态
                var isfmainorgid = agentInfo.GetValue(fmainorgid);
                if (isfmainorgid && frenewalflag && !fchstatus.IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"操作失败，订单编号：{fbillno}当勾选了【焕新订单标记】且创建该单据的经销商经营类型=【直营】且【协同总部状态＝已锁单、已驳回、已终审】时,不允许操作！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 检查订单是否提交到总部(直营)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderIsSubmitToTopOrg(UserContext userCtx, IEnumerable<DynamicObject> dataEntities,ValidationResult result)
        {
            var isDirectSale = userCtx.IsDirectSale();
            if (isDirectSale)
            {
                foreach (var dataEntity in dataEntities)
                {
                    //提交总部状态
                    var chStatus = Convert.ToString(dataEntity["fchstatus"]);

                    if (!chStatus.IsNullOrEmptyOrWhiteSpace())
                    {
                        // ①当【创建该单据的经销商.经营类型=直营】，并且【协同SAP状态=提已交总部 或是 已终审】，不允许操作该按钮。
                        // ②当【创建该单据的经销商.经营类型=直营】并且【协同SAP状态= 空值 或是 已驳回】，允许操作该按钮。
                        //'1':'已提交总部','2':'已驳回','3':'已终审'
                        if (chStatus.Equals("1") || chStatus.Equals("3"))
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"操作失败，订单编号：{dataEntity["fbillno"]}当【创建该单据的经销商.经营类型=直营】，并且【协同SAP状态=已锁单 或是 已终审】，不允许操作反审核按钮。",
                                DataEntity = dataEntity
                            });
                        }
                    }
                    
                }
            }
        }

        /// <summary>
        /// 获取当前经销商相关字段信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private Dictionary<string, bool> GetAgentInfo(UserContext userCtx, List<string> fmainorgids)
        {
            var dic = new Dictionary<string, bool>();
            if (fmainorgids.Count == 0) return dic;

            var agentInfo = userCtx.LoadBizBillHeadDataById("bas_agent", fmainorgids, "fmainorgid,fname,fnumber,actualownernumber,fmanagemodel").ToList();

            foreach (var item in agentInfo)
            {
                if (!dic.ContainsKey(Convert.ToString(item["fmainorgid"])))
                {
                    var fmanagemodel = Convert.ToString(item["fmanagemodel"]) == "1" ? true : false;
                    dic.Add(Convert.ToString(item["fmainorgid"]), fmanagemodel);
                }
            }

            return dic;
        }
    }
}
