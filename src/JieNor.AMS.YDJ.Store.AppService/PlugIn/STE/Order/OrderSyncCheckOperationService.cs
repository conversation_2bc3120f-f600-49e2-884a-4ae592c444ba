using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    public class OrderSyncCheckOperationService : IOrderSyncCheckOperationService
    {
        public DynamicObject[] SyncCheckOperation(UserContext userContext, string opCode, DynamicObject[] dataEntities, List<string> msg)
        {
            if (userContext == null)
            {
                throw new BusinessException("用户上下文不能为空!");
            }
            if (string.IsNullOrWhiteSpace(opCode))
            {
                throw new BusinessException("opCode操作码不能为空!");
            }
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return dataEntities;
            }

            //获取合同商品明细的商品id并去重
            var productIds = dataEntities.SelectMany(x =>
            {
                var fentries = x["fentry"] as DynamicObjectCollection;
                if (fentries == null || fentries.Count <= 0)
                {
                    return new string[0];
                }
                return fentries.Select(y => Convert.ToString(y["fproductid"])).Where(y => !string.IsNullOrWhiteSpace(y));
            }).Distinct().ToList();

            if (productIds == null || productIds.Count <= 0)
            {
                return dataEntities;
            }

            //根据所有商品id查找直营模式的供应商信息
            StringBuilder sql = new StringBuilder(@"
select m.fid as productid,m.fpublishcid as publishcid,m.fpublishcid_pid as publishpid from t_bd_material m
inner join t_ydj_supplier s on m.fpublishcid=s.fcoocompanyid and m.fpublishcid_pid=s.fcooproductid and m.fmainorgid=s.fmainorgid
where m.fmainorgid=@fmainorgid and s.foperationmode='1' and m.fid 
");
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",DbType.String,userContext.Company)
            };

            if (productIds.Count == 1)
            {
                sql.Append(" = @fproductid");
                sqlParams.Add(new SqlParam("@fproductid", DbType.String, productIds[0]));
            }
            else
            {
                sql.Append(" in (");
                sql.Append(string.Join(",", productIds.Select((x, i) => string.Format("@fproductid{0}", i))));
                sql.Append(")");
                sqlParams.AddRange(productIds.Select((x, i) => new SqlParam(string.Format("@fproductid{0}", i), DbType.String, x)));
            }

            List<Dictionary<string, string>> publishInfos = new List<Dictionary<string, string>>();
            IDBService dbService = userContext.Container.GetService<IDBService>();

            using (var dataReader = dbService.ExecuteReader(userContext, sql.ToString(), sqlParams))
            {
                while (dataReader.Read())
                {
                    publishInfos.Add(new Dictionary<string, string>
                    {
                        { "productId",dataReader.GetValue<string>("productid") },
                        { "publishCId",dataReader.GetValue<string>("publishcid")},
                        { "publishPId",dataReader.GetValue<string>("publishpid")}
                    });
                }
            }

            if (publishInfos == null || publishInfos.Count <= 0)
            {
                return dataEntities;
            }

            List<Dictionary<string, string>> synDatas = new List<Dictionary<string, string>>();

            //根据运营模式分离商品明细行
            foreach (var dataEntity in dataEntities)
            {
                var fentries = dataEntity["fentry"] as DynamicObjectCollection;
                if (fentries == null || fentries.Count <= 0)
                {
                    continue;
                }

                var ftranid = Convert.ToString(dataEntity["ftranid"]);

                foreach (var fentry in fentries)
                {
                    var productId = Convert.ToString(fentry["fproductid"]);
                    var publishInfo = publishInfos.FirstOrDefault(x => x["productId"] == productId);

                    if (publishInfo == null)
                    {
                        //非直营模式
                        continue;
                    }

                    //直营模式
                    var synData = synDatas.FirstOrDefault(x => x["tranId"] == ftranid &&
                                                               x["publishCId"] == publishInfo["publishCId"] &&
                                                               x["publishPId"] == publishInfo["publishPId"]);

                    if (synData == null)
                    {
                        synDatas.Add(new Dictionary<string, string>
                        {
                            { "tranId",ftranid},
                            { "publishCId",publishInfo["publishCId"]},
                            { "publishPId",publishInfo["publishPId"]}
                        });
                    }
                }
            }

            if (synDatas == null || synDatas.Count <= 0)
            {
                return dataEntities;
            }

            List<Dictionary<string, string>> result = new List<Dictionary<string, string>>();
            IHttpServiceInvoker gateway = userContext.Container.GetService<IHttpServiceInvoker>();
            foreach (var group in synDatas.GroupBy(x => new { publishCId = x["publishCId"], publishPId = x["publishPId"] }))
            {
                var tranIds = group.Select(x => x["tranId"]).ToList();
                TargetSEP target = new TargetSEP(group.Key.publishCId, group.Key.publishPId);
                var response = gateway.Invoke(
                                       userContext,
                                       target,
                                       new CommonBillDTO()
                                       {
                                           FormId = "ydj_order",
                                           OperationNo = "SyncCheckOperation",
                                           ExecInAsync = false,
                                           AsyncMode = (int)Enu_AsyncMode.Background,
                                           SimpleData = new Dictionary<string, string>
                                           {
                                                    { "opCode",opCode},
                                                    { "tranids",tranIds.ToJson()}
                                           }
                                       }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
                                   ) as CommonBillDTOResponse;

                response.OperationResult.ThrowIfHasError(true, $"协同检查操作失败！");
                var srvData = response.OperationResult.SrvData.ToString();
                result.AddRange(Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(srvData));
            }

            if (result == null || result.Count <= 0)
            {
                return dataEntities;
            }

            var failItems = result.Where(x => string.Equals("false", x["result"], StringComparison.OrdinalIgnoreCase)).Distinct(x => x["tranid"]).ToList();
            var failTranIds = failItems.Select(x => x["tranid"]).ToArray();
            msg.AddRange(failItems.Select(x => string.Format("流水号为[{0}]的合同操作失败,原因:{1}", x["tranid"], x["msg"])));

            return dataEntities.Where(x => !failTranIds.Contains(Convert.ToString(x["ftranid"]))).ToArray();
        }
    }
}
