
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：判断是否三维家合同
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("CheckSwjOrder")]
    public class CheckSwjOrder : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            this.Result.SrvData = "";
            this.Result.IsSuccess = false;
            var fbillnos = this.GetQueryOrSimpleParam<string>("fbillno",",");
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds", "");

            var rows = string.IsNullOrWhiteSpace(rowIds) ? new List<Dictionary<string, string>>() : JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(rowIds);
            if (fbillnos.Length <= 0) return;
            var billnos = fbillnos.Split(',').Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            if (billnos.Count == 0 && rows.Count == 0)
                return;

            List<DynamicObject> DataEntity = new List<DynamicObject>();
            if (billnos.Count > 0)
            {
                DataEntity.AddRange(this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", billnos, true));
            }
            if (rows.Count > 0)
            {
                StringBuilder sbWhere = new StringBuilder();
                List<SqlParam> paramList = new List<SqlParam>();
                for (int i = 0; i < rows.Count; i++)
                {
                    var paramName = "@pid" + i;
                    sbWhere.Append(paramName).Append(",");
                    paramList.Add(new SqlParam(paramName, DbType.String, rows[i].GetValue("id")));
                }

                var ids = this.Context.ExecuteDynamicObject($"select distinct fid from T_YDJ_ORDERENTRY with(nolock) where fentryid in({sbWhere.ToString().TrimEnd(',')})", paramList);

                DataEntity.AddRange(this.Context.LoadBizDataById(this.HtmlForm.Id, ids.Select(a => Convert.ToString(a["fid"])).ToList(), true));
            }

            if (DataEntity.Count() <= 0) return;

            //二级分销商销售合同转采购时 当多个销售合同转采购生成一个采购订单时：判断这些合同的【客户】【合作渠道】是否均是同一个
            if (Context.IsSecondOrg) {
                var groups = DataEntity.GroupBy(p => new { fcustomerid = p["fcustomerid"].ToString(), fchannel = p["fchannel"].ToString() });
                if (groups.Count() > 1)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SrvData = "所选合同存在客户与合作渠道不一致的情况，请核查，谢谢！";
                    return;
                }
            }
            //var refMgr = this.Container.GetService<L"oadReferenceObjectManager>();
            //var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
            //refMgr.Load(this.Context, dt, e.DataEntitys, false);

            int swjordernum = 0;
            bool swjorder = false;
            foreach (var item in DataEntity)
            {
                var billtypeObj = item["fbilltype_ref"] as DynamicObject;
                if (Convert.ToString(billtypeObj["fname"]).Equals("v6定制柜合同"))
                {
                    swjordernum++;
                }
            }
            if (swjordernum == DataEntity.Count())
            {
                swjorder = true;
            }
            else if (swjordernum > 0)
            {
                this.Result.SrvData = "不允许同时选择v6定制柜合同和其他销售合同同时下推采购订单！";
            }
            this.Result.IsSuccess = swjorder;
        }
    }
}
