using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.E3
{
    /// <summary>
    /// E3预设的员工，部门，仓库不允许删除
    /// </summary>
    [InjectService]
    [FormId("ydj_staff|ydj_dept|ydj_storehouse")]
    [OperationNo("Delete")]
    public class E3Delete : AbstractOperationServicePlugIn
    {
        private readonly List<string> defaultNumber = new List<string>
        {
            "YJDFZBCK",
            "YJDFXNBM",
            "YJDFXNYG",
            "ZFCK",
            "ZFTHCK"
        };

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string number = Convert.ToString(newData["fnumber"]);

                if (defaultNumber.IndexOf(number) >= 0)
                {
                    return false;

                }
                return true;
            }).WithMessage("预置数据不允许删除！"));
        }
    }
    /// <summary>
    /// E3预设的不允许编辑
    /// </summary>
    [InjectService]
    [FormId("ydj_storehouse")]
    [OperationNo("save")]
    public class E3Save : AbstractOperationServicePlugIn
    {
        private readonly List<string> defaultNumber = new List<string>
        {
            "YJDFZBCK",
            "ZFCK"
        };

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string number = Convert.ToString(newData["fnumber"]);

                if (defaultNumber.IndexOf(number) >= 0)
                {
                    return false;

                }
                return true;
            }).WithMessage("预置数据不允许编辑！"));
        }
    }

    /// <summary>
    /// E3预设的不允许禁用
    /// </summary>
    [InjectService]
    [FormId("ydj_storehouse")]
    [OperationNo("forbid")]
    public class E3Forbid : AbstractOperationServicePlugIn
    {
        private readonly List<string> defaultNumber = new List<string>
        {
            "YJDFZBCK",
            "ZFCK"
        };

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string number = Convert.ToString(newData["fnumber"]);

                if (defaultNumber.IndexOf(number) >= 0)
                {
                    return false;

                }
                return true;
            }).WithMessage("预置数据不允许禁用！"));
        }
    }

    /// <summary>
    /// 采购入库，销售出库单，其它出库，不允许选择虚拟仓（自动下推除外
    /// </summary>
    [InjectService]
    [FormId("stk_postockin|stk_sostockout|stk_otherstockout")]
    [OperationNo("save")]
    public class E3StockSave : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string e3Auto = this.GetQueryOrSimpleParam<string>("e3Auto", "");
            base.PrepareValidationRules(e);
            string errorMsg = "";
            if (e3Auto != null && !e3Auto.Equals("true"))
            {
                // 加载数据
                e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
                {
                    if (Convert.ToString(newData["fmanagemodel"]).Equals("1"))
                    {
                        var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
                        refObjMgr?.Load(this.Context, new[] { newData }, true, this.HtmlForm, new List<string> { "fstorehouseid" });
                        bool flag = true;
                        errorMsg = "";
                        var entries = newData["fentity"] as DynamicObjectCollection;
                        foreach (var entry in entries)
                        {
                            var storehouse = entry["fstorehouseid_ref"] as DynamicObject;
                            decimal dealAmount = 0;
                            if (Convert.ToString(storehouse?["fwarehousetype"]).Equals("warehouse_03"))
                            {
                                errorMsg += $"第{Convert.ToString(entry["fseq"])}行商品,直营经销商不允许选择虚拟仓！";
                                flag = false;
                            }
                        }
                        return flag;
                    }
                    return true;
                }).WithMessage("{0}", (billObj, propObj) => errorMsg));
            }
        }
    }

    /// <summary>
    /// 一件代发的采购入库和销售出库不允许提交，审核，删除，反审核
    /// </summary>
    [InjectService]
    [FormId("stk_postockin|stk_sostockout")]
    [OperationNo("submit")]
    public class E3Submit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string e3Auto = this.GetQueryOrSimpleParam<string>("e3Auto", "");
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_piecesendtag(e3Auto, this.OperationName));
        }
    }


    /// <summary>
    /// 一件代发的采购入库和销售出库不允许提交，审核，删除，反审核
    /// </summary>
    [InjectService]
    [FormId("stk_postockin|stk_sostockout")]
    [OperationNo("audit")]
    public class E3audit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string e3Auto = this.GetQueryOrSimpleParam<string>("e3Auto", "");
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_piecesendtag(e3Auto, this.OperationName));
        }
    }



    /// <summary>
    /// 一件代发的采购入库和销售出库不允许提交，审核，删除，反审核
    /// </summary>
    [InjectService]
    [FormId("stk_postockin|stk_sostockout")]
    [OperationNo("unaudit")]
    public class E3unaudit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string e3Auto = this.GetQueryOrSimpleParam<string>("e3Auto", "");
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_piecesendtag(e3Auto, this.OperationName));
        }
    }




    /// <summary>
    /// 一件代发的采购入库和销售出库不允许提交，审核，删除，反审核
    /// </summary>
    [InjectService]
    [FormId("stk_postockin|stk_sostockout")]
    [OperationNo("delete")]
    public class E3stockdelete : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            string e3Auto = this.GetQueryOrSimpleParam<string>("e3Auto", "");
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_piecesendtag(e3Auto, this.OperationName));
        }
    }
    /// <summary>
    /// 
    /// </summary>
    public class Validation_piecesendtag : AbstractBaseValidation
    {

        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }
        public Validation_piecesendtag(string e3Auto, string opName)
        {
            this._e3Auto = e3Auto;
            this._opName = opName;
        }
        /// <summary>
        /// 保存提交
        /// </summary>
        private string _e3Auto { get; set; }
        private string _opName { get; set; }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }
            if (this.Context.IsDirectSale)
            {
                if (!_e3Auto.Equals("true"))
                {
                    ZYCheckChangeInfo(userCtx, formInfo, dataEntities, result, option, operationNo);
                }
            }
            else
            {
                if (!_e3Auto.Equals("true"))
                {
                    CheckChangeInfo(userCtx, formInfo, dataEntities, result, option, operationNo);
                }
            }
            return result;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        /// <param name="option"></param>
        private void CheckChangeInfo(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option, string operationNo)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;
            foreach (var item in dataEntitys)
            {
                var piecesendtag = Convert.ToBoolean(item["fpiecesendtag"]);
                if (piecesendtag)
                {
                    bool flag = true;
                    if (formInfo.Id.Equals("stk_sostockout"))
                    {
                        var entitys = item["fentity"] as DynamicObjectCollection;
                        if (entitys.All(a => Convert.ToString(a["fdeliverytype"]).Equals("delivery_type_02")))
                        {
                            flag = false;
                        }
                    }
                    if (flag)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"该单据【{item["fbillno"]}】审核通过后已自动同步总部，不允许该操作！",
                            DataEntity = item,
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        /// <param name="option"></param>
        private void ZYCheckChangeInfo(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option, string operationNo)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;
            foreach (var item in dataEntitys)
            {
                var piecesendtag = Convert.ToBoolean(item["fpiecesendtag"]);
                if (piecesendtag)
                {
                    bool flag = true;
                    if (formInfo.Id.Equals("stk_sostockout"))
                    {
                        var entitys = item["fentity"] as DynamicObjectCollection;
                        if (entitys.All(a => Convert.ToString(a["fdeliverytype"]).Equals("delivery_type_02")))
                        {
                            flag = false;
                        }
                    }
                    if (flag)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"该单据【{item["fbillno"]}】为总部一件代发，不允许该操作！",
                            DataEntity = item,
                        });
                    }
                }
            }
        }

    }
}
