using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：下推
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("push")]
    public class Push: AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "getExistedTargetEntities":
                    getExistedTargetEntities(e);
                    break;
            }
        }

        /// <summary>
        /// 获取已下推的目标实体列表
        /// </summary>
        /// <param name="e"></param>
        private void getExistedTargetEntities(OnCustomServiceEventArgs e)
        {
            if (e.DataEntities == null || e.DataEntities.Length <= 0)
            {
                return;
            }

            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null)
            {
                return;
            }

            var ruleId = eventData["ruleId"] as string;
            var targetForm = eventData["targetForm"] as HtmlForm;
            var userContext = eventData["userContext"] as UserContext;

            if (false == "ydj_order2ste_registfee".EqualsIgnoreCase(ruleId) || targetForm == null || userContext == null)
            {
                return;
            }

            var htmlEntry = targetForm.GetEntryEntity("fentry");
            var sqlFormat = @"
select t.fid as ffeeid from {0} t
where exists(select 1 from {1} te inner join {2} tmp on tmp.fid=te.fsourceinterid and te.fsourceformid='{3}' and te.fid=t.fid)
and t.fmainorgid='{4}'
";
            var orderIds = e.DataEntities
                            .Select(x => Convert.ToString(x["id"]))
                            .Distinct()
                            .ToList();
            var dbService = this.Container.GetService<IDBService>();
            List<DynamicObject> targetEntities = null;
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(userContext, targetForm.GetDynamicObjectType(userContext));

            using (var tran = userContext.CreateTransaction())
            {
                var tableName = dbService.CreateTempTableWithDataList(userContext, orderIds,false);
                var sql = string.Format(sqlFormat,
                                        targetForm.BillHeadTableName,
                                        htmlEntry.TableName,
                                        tableName,
                                        this.HtmlForm.Id,
                                        userContext.Company);
                using (var dataReader = dbService.ExecuteReader(userContext, sql))
                {
                    targetEntities = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                }
                tran.Complete();

                dbService.DeleteTempTableByName(Context, tableName, true);
            }

            if (targetEntities == null)
            {
                targetEntities = new List<DynamicObject>();
            }

            var idMaps = targetEntities.SelectMany(x =>
              {
                  var entries = x["fentry"] as DynamicObjectCollection;
                  return entries.Select(y => new
                  {
                      targetId = Convert.ToString(x["id"]),
                      sourceFormId = Convert.ToString(y["fsourceformid"]),
                      sourceId = Convert.ToString(y["fsourceinterid"])
                  });
              })
            .Where(x => false == x.sourceFormId.EqualsIgnoreCase("ydj_order") && false == string.IsNullOrWhiteSpace(x.sourceId))
            .Distinct(x => x.sourceId)
            .ToDictionary(k => k.sourceId, v => v.targetId);

            var hasNotTargetEntities = e.DataEntities.Where(x => false == idMaps.Keys.Contains(Convert.ToString(x["id"]))).ToList();

            var result = new Dictionary<string, object>
            {
                { "targetEntities",targetEntities},
                { "idMaps",idMaps},
                { "hasNotTargetEntities",hasNotTargetEntities}
            };
            e.Cancel = true;
            e.Result = result;
        }
    }
}
