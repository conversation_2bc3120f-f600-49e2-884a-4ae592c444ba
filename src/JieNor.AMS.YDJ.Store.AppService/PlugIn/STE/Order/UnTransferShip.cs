using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("untransfership")]
    public class UnTransferShip : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys.Length == 0)
                return;
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            if (selectRowIds.Count == 0)
            {
                throw new BusinessException("至少选择一行商品!");
            }

            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context,
                this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false, new Dictionary<string, string> { { "bd_billtype", "bd_billtype" },{ "fproductid", "fproductid" }
        });
            var entry = e.DataEntitys.FirstOrDefault();

            //验证数据
            VerificationData(entry, selectRowIds);

            //修改单据头状态
            ModifyRowStatus(entry, selectRowIds);
        }

        /// <summary>
        /// 修改单据头状态
        /// </summary>
        /// <param name="entry"></param>
        private void ModifyRowStatus(DynamicObject entry, List<Row> rows)
        {
            var listid = rows.Select(y => y.Id.ToString()).ToList();
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_order");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fid", DbType.String, entry["id"].ToString()));
            string sqlWhere = $@"fid=@fid ";
            var reader = this.Context.GetPkIdDataReader(htmlForm, sqlWhere, sqlParams);
            var orderObj = dm.SelectBy(reader).OfType<DynamicObject>().FirstOrDefault();

            var entrys = orderObj["fentry"] as DynamicObjectCollection;

            //更新选中行状态
            foreach (var item in entrys.Where(x => listid.Contains(Convert.ToString(x["id"]))))
            {
                item["fclosestatus_e"] = (int)CloseStatus.Default;
            }

            //明细整行数
            var count = entrys.Count;
            //所有明细行正常状态行数
            var norstateCount = entrys.Count(x => Convert.ToString(x["fclosestatus_e"]) == ((int)CloseStatus.Default).ToString());
            //所有明细行自动关闭状态行数
            var autoCount = entrys.Count(x => Convert.ToString(x["fclosestatus_e"]) == ((int)CloseStatus.Auto).ToString());
            //所有明细行部份关闭状态行数
            var partCount = entrys.Count(x => Convert.ToString(x["fclosestatus_e"]) == ((int)CloseStatus.Part).ToString());
            //所有明细行只有自动关闭"与"手工关闭
            var autoAndManualCount = entrys.Count(x => Convert.ToString(x["fclosestatus_e"]) == ((int)CloseStatus.Auto).ToString() ||
            Convert.ToString(x["fclosestatus_e"]) == ((int)CloseStatus.Manual).ToString());

            if (norstateCount == count)//更新单据头的【关闭状态】="正常"
                orderObj["fclosestatus"] = (int)CloseStatus.Default;
            else if (autoCount == count || autoAndManualCount == count)// 更新单据头的【关闭状态】="整单关闭"
                orderObj["fclosestatus"] = (int)CloseStatus.Whole;
            else if (partCount == count) //更新单据头的【关闭状态】= "部份关闭"
                orderObj["fclosestatus"] = (int)CloseStatus.Part;
            else //不满足以上条件 更新单据头的【关闭状态】="部份关闭"
                orderObj["fclosestatus"] = (int)CloseStatus.Part;
            //返回下游戏找到的采购明细源单ID
            var purentrysourceids = GetPurOrderEntryid(listid);
            //返回下游戏找到的出库存单明细源单ID
            var sostockoutsourceids = GetSostockoutEntryid(listid);

            //将状态返回给前端更新
            List<object> list = new List<object>();

            //修改状态,匹配不到就清空,优先级高的放下面去覆盖状态
            foreach (var item in entrys.Where(x => listid.Contains(Convert.ToString(x["id"]))))
            {
                //找到下游采购订单明细对应数据改流程状态为已采购，否则改为空
                if (purentrysourceids.Contains(Convert.ToString(item["id"])))
                    item["flinkpro"] = "已采购";
                else
                    item["flinkpro"] = "";

                //找到下游采购订单明细对应数据改流程状态为已入库
                if (sostockoutsourceids.Contains(Convert.ToString(item["id"])))
                    item["flinkpro"] = "已入库";

                list.Add(new { id = Convert.ToString(item["id"]), status = Convert.ToString(item["flinkpro"]), fclosestatus_e = item["fclosestatus_e"] });
            }

            dm.Save(orderObj);

            this.Result.SrvData = list;
            this.Result.ComplexMessage.SuccessMessages.Add($"已反执行转单发货 ! ");
        }

        /// <summary>
        /// 返回查找到的采购订单明细源单ID
        /// </summary>
        /// <returns></returns>
        public List<string> GetPurOrderEntryid(List<string> ids)
        {
            string strSql = @"select t2.fsourceentryid
from t_ydj_purchaseorder t1 with(nolock) 
join t_ydj_poorderentry t2 with(nolock)  on t1.fid=t2.fid
where t1.fstatus='E' and t2.fsourceentryid in ('{0}')".Fmt(string.Join("','", ids));
            List<string> list = new List<string>();

            using (var data = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                if (data != null)
                {
                    while (data.Read())
                    {
                        list.Add(Convert.ToString(data["fsourceentryid"]));
                    }
                }
            };
            return list;
        }

        /// <summary>
        /// 返回查找到的采购订单明细源单ID
        /// </summary>
        /// <returns></returns>
        public List<string> GetSostockoutEntryid(List<string> ids)
        {
            string strSql = @"select t2.fsourceentryid
from t_stk_sostockout t1 with(nolock) 
join t_stk_sostockoutentry t2 with(nolock)  on t1.fid=t2.fid
where t1.fstatus='E' and t2.fsourceentryid in ('{0}')".Fmt(string.Join("','", ids));
            List<string> list = new List<string>();

            using (var data = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                if (data != null)
                {
                    while (data.Read())
                    {
                        list.Add(Convert.ToString(data["fsourceentryid"]));
                    }
                }
            };
            return list;
        }

        /// <summary>
        /// 过滤数据
        /// </summary>
        /// <param name="dic"></param>
        /// <param name="entrys"></param>
        private void VerificationData(DynamicObject entry, List<Row> rows)
        {
            //发货经销商ID
            Dictionary<string, string> agentids = new Dictionary<string, string>();
            var listid = rows.Select(y => y.Id.ToString()).ToList();
            var entrys = entry["fentry"] as DynamicObjectCollection;
            #region 任务36645 开始验证 5.1 到5.3 

            var typeMdl = entry["fbilltype_ref"] as DynamicObject;
            var Isoffline = new OrderCommon(this.Context).CheckIsofflineTrans(entrys.Where(x => listid.Contains(x["id"].ToString())).Select(x => Convert.ToString(x["id"])).ToList());//是否线下转单
            var fneedtransferorder = Convert.ToString(entry["fneedtransferorder"]).EqualsIgnoreCase("true");//是否选中需转单
            var istrens = Convert.ToString(typeMdl["fname"]) == "销售转单";//是否销售转单
            if (
                (!istrens && fneedtransferorder && !Isoffline)          //-------验证5.1-------
                || (istrens && !fneedtransferorder && !Isoffline)       //-------验证5.2-------
                || (istrens && fneedtransferorder)                      //-------验证5.2-------
                )
            {
                throw new BusinessException("当前商品不为线下转单, 不允许手工取消已发货 !");
            }

            #endregion

            #region 任务36645 开始验证 5.4  5.5

            foreach (var item in entrys.Where(x => listid.Contains(x["id"].ToString())))
            {
                var prodMdl = item["fproductid_ref"] as DynamicObject;
                var prodName = Convert.ToString(prodMdl["fname"]);
                if (Convert.ToString(item["fclosestatus_e"]) != ((int)CloseStatus.Auto).ToString())  //-------验证 5.5-------
                {
                    throw new BusinessException($"当前商品不等于自动关闭, 不允许手工标记取消已发货 ! ");
                }
                agentids[Convert.ToString(item["fshipperagentno"])] = prodName;
            }
            var orginlist = agentids.Keys.ToList();

            //查主经销商
            string sqlStr_mac = @"select t2.fnumber 'orgid' from t_bas_mac t1
            join t_bas_agent t2 on t1.fmainagentid =t2.fid 
            where t2.fnumber in ('" + string.Join("','", orginlist) + "')  and t1.fmainagentid='" + Context.Company + "'";
            //查子经销商
            string sqlStr_macentry = @"select t2.fnumber 'orgid' from t_bas_macentry t1
            join t_bas_mac t3 on t1.fid=t3.fid
            join t_bas_agent t2 on t1.fsubagentid =t2.fid
            where t2.fnumber in ('" + string.Join("','", orginlist) + "') and t3.fmainagentid='" + Context.Company + "'";

            var _dbService = this.Context.Container.GetService<IDBService>();
            var data_mac = _dbService.ExecuteDataTable(this.Context, sqlStr_mac);
            //验证主经销商
            if (data_mac != null || data_mac.Rows.Count > 0)
            {
                foreach (DataRow row in data_mac.Rows)
                {
                    var orgin = row["orgid"].ToString();
                    throw new BusinessException($"当前商品{agentids[orgin]}需要自行发货, 不允许手工标记取消已发货 !");
                }
            }
            data_mac = null;

            //验证子经销商
            var data_macentry = _dbService.ExecuteDataTable(this.Context, sqlStr_mac);
            if (data_macentry != null || data_macentry.Rows.Count > 0)
            {
                foreach (DataRow row in data_macentry.Rows)
                {
                    var orgin = row["orgid"].ToString();
                    throw new BusinessException($"当前商品{agentids[orgin]}需要自行发货, 不允许手工标记取消已发货 !");
                }
            }
            data_macentry = null;

            #endregion

        }
    }
}
