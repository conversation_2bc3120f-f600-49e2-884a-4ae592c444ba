//using JieNor.AMS.YDJ.Store.AppService.MuSi;
//using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
//using JieNor.Framework;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.Utils;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
//{
//    /// <summary>
//    /// 销售合同：更新物流进度
//    /// </summary>
//    [InjectService]
//    [FormId("ydj_order")]
//    [OperationNo("updatelogisticsprogress")]
//    public class UpdateLogisticsProgress : AbstractOperationServicePlugIn
//    {
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            base.BeginOperationTransaction(e);
//            string tmpName = this.DBService.CreateTemporaryTableName(this.Context, true);
//            GetPendingPurchaseOrders(tmpName);
//            string sql = $@"select a.*,b.pobizqty,b.fentryid from t_ydj_e3logisticsprogress a  
//                                inner join {tmpName} b on a.fposeq=b.fseq_e and a.fpoid=b.poId ";
//            var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
//            if (data == null || data.Count == 0)
//            {
//                this.WriteLog("没有符合条件的销售合同物流进度数据");
//                return;
//            }
//            var groupedData = data
//               .GroupBy(d => new { forderid = d["forderid"], fentryid = d["fentryid"] })
//               .Select(g => new
//               {
//                   forderid = g.Key.forderid,
//                   fentryid = g.Key.fentryid,
//                   TotalPobizqty = g.Sum(x => Convert.ToDecimal(x["pobizqty"])),//采购订单数量
//                   TotalFbizqty = g.Sum(x => Convert.ToDecimal(x["fbizqty"]))//签收数量
//               })
//               .ToList();
//            List<DynamicObject> needSaveObjs = new List<DynamicObject>();
//            List<string> orderIds = data.Select(f => f["forderid"].ToString()).Distinct().ToList();
//            List<DynamicObject> orders = this.Context.LoadBizDataById("ydj_order", orderIds);
//            foreach (var item in groupedData)
//            {
//                var orderItem = orders.Where(a => Convert.ToString(a["id"]).Equals(item.forderid)).FirstOrDefault();
//                if (orderItem == null) continue;
//                var orderEntry = orderItem["fentry"] as DynamicObjectCollection;
//                var orderEntryItem = orderEntry.Where(a => Convert.ToString(a["id"]).Equals(item.fentryid)).FirstOrDefault();
//                if (orderEntryItem == null) continue;
//                // 判断行签收状态
//                if (item.TotalPobizqty == item.TotalFbizqty)
//                {
//                    orderEntryItem["fentrysignstatus"] = "entrysign_type_01"; // 已签收
//                }
//                else if (item.TotalPobizqty > item.TotalFbizqty)
//                {
//                    orderEntryItem["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
//                }
//                else
//                {
//                    orderEntryItem["fentrysignstatus"] = "entrysign_type_03"; // 未签收
//                }
//                if (needSaveObjs.Where(a => Convert.ToString(a["id"]).Equals(orderItem["id"])).FirstOrDefault() != null)
//                {
//                    needSaveObjs.Remove(needSaveObjs.Where(a => Convert.ToString(a["id"]).Equals(orderItem["id"])).FirstOrDefault());
//                }
//                needSaveObjs.Add(orderItem);
//            }
//            foreach (var order in needSaveObjs)
//            {
//                var orderEntries = order["fentry"] as DynamicObjectCollection;
//                if (orderEntries == null || orderEntries.Count == 0) continue;

//                // 判断明细签收状态
//                bool allSigned = orderEntries.All(e => Convert.ToString(e["fentrysignstatus"]) == "entrysign_type_01");
//                bool hasPartialSign = orderEntries.Any(e => Convert.ToString(e["fentrysignstatus"]) == "entrysign_type_02");
//                bool allNotSigned = orderEntries.All(e => Convert.ToString(e["fentrysignstatus"]) == "entrysign_type_03");

//                // 更新表头签收状态
//                if (allSigned)
//                {
//                    order["fordersignstatus"] = "ordersign_type_01"; // 已签收
//                }
//                else if (hasPartialSign)
//                {
//                    order["fordersignstatus"] = "ordersign_type_02"; // 部分签收
//                }
//                else if (allNotSigned)
//                {
//                    order["fordersignstatus"] = "ordersign_type_03"; // 未签收
//                }
//            }
//            var grpOrgDatas = needSaveObjs.GroupBy(f => f["fmainorgid"].ToString()).ToList();
//            foreach (var item in grpOrgDatas)
//            {
//                var ctx = this.Context.CreateAgentDBContext(item.Key);
//                var objs = needSaveObjs.Where(a => Convert.ToString(a["fmainorgid"]).Equals(item.Key)).ToList();
//                var saveResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, "ydj_order", objs, "save", null);

//                var billnos = objs.Select(o => Convert.ToString(o["fbillno"])).ToList();
//                if (saveResult.IsSuccess)
//                {
//                    this.WriteLog($" 销售合同签收状态{billnos.JoinEx(",", false)} 保存成功；");
//                }
//                else
//                {
//                    this.WriteLog($" 销售合同签收状态{billnos.JoinEx(",", false)} 保存失败；");
//                }
//            }




//        }

//        /// <summary>
//        /// 获取符合条件的订单
//        /// </summary>
//        /// <returns></returns>
//        private DynamicObjectCollection GetPendingPurchaseOrders(string tmpName)
//        {
//            string sql = $@"select a.fid,a.fbillno,a.fmainorgid,b.fentryid,poentry.fseq_e,poentry.fentryid poentryid,poentry.fid as poId,poentry.fbizqty pobizqty 
//                        into {tmpName}  
//                        from t_ydj_order a with(nolock)
//                        inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid
//                        inner join t_ydj_poorderentry poentry with(nolock) on b.fentryid=poentry.fsoorderentryid
//                        inner join t_ydj_purchaseorder po with(nolock) on poentry.fid=po.fid
//                        where a.fpiecesendtag=1 and fordersignstatus<>'entrysign_type_01' and b.fbizpurinqty>0 and b.fdeliverytype='delivery_type_01'";

//            return this.DBService.ExecuteDynamicObject(this.Context, sql);
//        }

//    }
//}
