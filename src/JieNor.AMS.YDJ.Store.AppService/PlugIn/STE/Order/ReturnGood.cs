using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order|ste_afterfeedback")]
    [OperationNo("push2returngood")]
    public class ReturnGood : AbstractOperationServicePlugIn
    {
        ///// <summary>
        ///// 预处理规则校验
        ///// </summary>
        ///// <param name="e"></param>
        //public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        //{
        //    string errorMsg = string.Empty;

        //    var profileService = this.Context.Container.GetService<ISystemProfile>();
        //    var outboundreturn = profileService.GetSystemParameter(this.Context, "stk_stockparam", "foutboundreturn", true);
        //    e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
        //    {
        //        errorMsg = string.Empty;
        //        var entrys = newData["fentry"] as DynamicObjectCollection;
        //        foreach (var entry in entrys)
        //        {
        //            if (Convert.ToDouble(entry["fqty"])<Convert.ToDouble(entry["freturnqty"]) && outboundreturn)
        //            {
        //                errorMsg += $"销售合同明细行{entry["fseq"]}选择的退货明细必须满足：出库数量>退货数量（有货可退）";
        //                throw new BusinessException(errorMsg);
        //            }
        //        }

        //        return true;

        //    }).WithMessage("{0}", (billObj, propObj) => errorMsg));
        //}

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    var eventData = e.EventData as BeforeGetConvertRuleData;
                    this.PrepareConvertRuleData(e.DataEntities, eventData);
                    break;
            }
        }

        /// <summary>
        /// 准备下推操作参数
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="eventData"></param>
        private void PrepareConvertRuleData(DynamicObject[] dataEntities, BeforeGetConvertRuleData eventData)
        {
            if (eventData == null
                || (!eventData.RuleId.EqualsIgnoreCase("stk_sostockout2stk_sostockreturn")
                    && !eventData.RuleId.EqualsIgnoreCase("stk_sostockout2sal_returnnotice"))) return;
            //获取退货方式
            var returnType = this.GetQueryOrSimpleParam<string>("returnType", "");
            var actualReturnAmount = this.GetQueryOrSimpleParam<decimal>("refundAmount", 0m);
            var returnReason = this.GetQueryOrSimpleParam<string>("returnReason", "");

            var returnGoodData = this.GetQueryOrSimpleParam<string>("returnData", "")
                .FromJson<IEnumerable<Dictionary<string, object>>>(true)
                .ToList();
            if (returnType.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("退货失败：请先指定退货方式（正常退换/退货退款）！");
            }
            if (returnGoodData.IsNullOrEmpty())
            {
                throw new BusinessException("退货失败：请至少选择一行商品明细进行退货！");
            }

            eventData.Option.SetVariableValue("returnType", returnType);
            eventData.Option.SetVariableValue("returnReason", returnReason);
            eventData.Option.SetVariableValue("returnData", returnGoodData);
            eventData.Option.SetVariableValue("refundAmount", actualReturnAmount);

            var lstSelPoEntryRows = new List<SelectedRow>();
            Dictionary<string, Dictionary<string, object>> dctActualReturnInfo = new Dictionary<string, Dictionary<string, object>>();
            foreach (var mtrlObj in returnGoodData)
            {
                var poId = mtrlObj.GetString("pkValue");
                var poEntryId = mtrlObj.GetString("entryPkValue");
                var entityKey = mtrlObj.GetString("entityKey");
                if (poId.IsNullOrEmptyOrWhiteSpace()) continue;

                lstSelPoEntryRows.Add(new SelectedRow()
                {
                    PkValue = poId,
                    EntityKey = entityKey,
                    EntryPkValue = poEntryId,
                });
            }
            if (lstSelPoEntryRows.Count == 0)
            {
                throw new BusinessException("退货失败：所退商品明细数据不明确！");
            }

            eventData.RuleId = "stk_sostockout2stk_sostockreturn";
            eventData.SelectedRows = lstSelPoEntryRows;

            if (this.HtmlForm.Id.Equals("ydj_order") && (dataEntities != null && dataEntities.Any()))
            {
                var isEnableNotice = Convert.ToBoolean(dataEntities[0]["fenablenotice"]);
                if (isEnableNotice)
                {
                    eventData.RuleId = "stk_sostockout2sal_returnnotice";
                }
            }

            var systemProfileService = this.Container.GetService<ISystemProfile>();
            var isEnableSchedulePlatform = systemProfileService.GetSystemParameter(this.Context, "stk_stockparam", "fenablescheduleplatform", false);
            if (isEnableSchedulePlatform)
            {
                eventData.RuleId = "stk_sostockout2stk_scheduleapply";
            }
        }

        /// <summary>
        /// 操作前处理事件
        /// </summary>
        /// <param name="e"></param>
        //public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        //{
        //    if (e.DataEntitys == null)
        //    {
        //        throw new BusinessException("退货失败：销售合同不存在，尝试重新打开此单据再操作！");
        //    }

        //    //获取退货方式
        //    var returnType = this.GetQueryOrSimpleParam<string>("returnType", "");
        //    var actualReturnAmount = this.GetQueryOrSimpleParam<decimal>("refundAmount", 0m);
        //    var returnReason = this.GetQueryOrSimpleParam<string>("returnReason", "");

        //    var returnGoodData = this.GetQueryOrSimpleParam<string>("returnData", "")
        //        .FromJson<IEnumerable<Dictionary<string, object>>>(true)
        //        .ToList();
        //    if (returnType.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        throw new BusinessException("退货失败：请先指定退货方式（正常退换/退货退款）！");
        //    }
        //    if (returnGoodData.IsNullOrEmpty())
        //    {
        //        throw new BusinessException("退货失败：请至少选择一行商品明细进行退货！");
        //    }

        //    this.Option.SetVariableValue("returnType", returnType);
        //    this.Option.SetVariableValue("returnReason", returnReason);
        //    this.Option.SetVariableValue("returnData", returnGoodData);
        //    this.Option.SetVariableValue("refundAmount", actualReturnAmount);

        //    var lstSelPoEntryRows = new List<SelectedRow>();
        //    Dictionary<string, Dictionary<string, object>> dctActualReturnInfo = new Dictionary<string, Dictionary<string, object>>();
        //    foreach (var mtrlObj in returnGoodData)
        //    {
        //        var poId = mtrlObj.GetString("pkValue");
        //        var poEntryId = mtrlObj.GetString("entryPkValue");
        //        var entityKey = mtrlObj.GetString("entityKey");
        //        if (poId.IsNullOrEmptyOrWhiteSpace()) continue;

        //        lstSelPoEntryRows.Add(new SelectedRow()
        //        {
        //            PkValue = poId,
        //            EntityKey = entityKey,
        //            EntryPkValue = poEntryId,
        //        });
        //    }
        //    if (lstSelPoEntryRows.Count == 0)
        //    {
        //        throw new BusinessException("退货失败：所退商品明细数据不明确！");
        //    }

        //    //检查目标单新增操作权限
        //    var permSrv = this.Container.GetService<IPermissionService>();
        //    permSrv?.CheckPermission(this.Context, new PermAuth(this.Context)
        //    {
        //        FormId = "stk_sostockreturn",
        //        OperationName = "退货",
        //        PermId = PermConst.PermssionItem_New,
        //    });

        //    var pushService = this.Container.GetService<IConvertService>();
        //    var pushResult = pushService.Push(this.Context, new BillConvertContext()
        //    {
        //        SourceFormId = "stk_sostockout",
        //        TargetFormId = "stk_sostockreturn",
        //        RuleId = "stk_sostockout2stk_sostockreturn",
        //        Option = this.Option,
        //        SelectedRows = lstSelPoEntryRows.ToConvertSelectedRows()
        //    });

        //    pushResult.ThrowIfHasError(true, "退货失败：生成退货单过程出现未知错误！");

        //    var convertResult = pushResult.SrvData as ConvertResult;

        //    var dctOption = this.Option.ToDictionary(k => k.Key, v => v.Value);
        //    convertResult.Option.Merge(dctOption ?? new Dictionary<string, object>());
        //    convertResult.NoSaveRelation = true;
        //    convertResult.MockUISave = true;
        //    var saveResult = pushService.SaveConvertData(this.Context, convertResult, true);
        //    saveResult.ThrowIfHasError(true, "退货失败：生成的销售退货单保存失败！");

        //    this.Result.MergeResult(saveResult);
        //}
    }
}
