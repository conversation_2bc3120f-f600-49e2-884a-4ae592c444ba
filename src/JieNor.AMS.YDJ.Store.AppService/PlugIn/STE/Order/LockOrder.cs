using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using Senparc.Weixin.Helpers.Extensions;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：锁定订单
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("lockorder")]
    public class LockOrder : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            //加载量尺模型数据
            var orderFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            //操作数据库
            var dm = this.Container.GetService<IDataManager>();
            //初始化上下文
            dm.InitDbContext(this.Context, orderFrom.GetDynamicObjectType(this.Context));

            //根据当前用户id  员工信息.关联账号(t_bd_staff.flinkuserid)= 用户.fid 
            //员工信息.关联账号无关联 需绑定
            var sql = $@"select top 1 t1.fid fid 
                         from t_sec_user t
                         left join t_bd_staff t1 on t.fid =t1.flinkuserid
                         where t.fid = '{this.Context.UserId}'";
            var flockpeople = string.Empty;
            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString()))
            {
                while (dataReader.Read())
                {
                    flockpeople = Convert.ToString(dataReader["fid"]).Trim();
                }
            }
            //无关联 需绑定
            if (flockpeople.IsNullOrEmpty())
            {
                throw new BusinessException($"当前账户还未关联员工，请先前往员工新增员工绑定关联账号！");
            }

            //用户点击锁定订单
            //锁定整张单据不可编辑, 
            //只有对应【销售员】的用户可以操作, 记录【锁定日期】= “当前日期”, 【锁定状态】=”已锁定”, 【锁定人】=当前用户
            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["flockstate"] = "1";//锁定状态 '0':'未锁定','1':'已锁定'
                dataEntity["flockdate"] = DateTime.Now; ;//锁定日期
                dataEntity["flockpeople"] = flockpeople;//锁定人 
            }
            dm.InitDbContext(this.Context, orderFrom.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);
            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "锁定订单成功！";
        }

    }
}