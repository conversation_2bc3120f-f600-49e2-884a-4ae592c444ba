using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 根据单据类型得到合同自定义配置
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getbilltypeparam")]
    public class GetBillTypeParam : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string fbilltype = this.GetQueryOrSimpleParam("fbilltype", "");
            decimal notOutSpotRatio = 0; //非现货首款比例
            decimal outspotratio = 0; //现货首款比例
            if (!outspotratio.IsNullOrEmptyOrWhiteSpace())
            {
                var billTypeService = Context.Container.GetService<IBillTypeService>();
                var paramSetObj = billTypeService.GetBillTypeParamSet(Context, this.HtmlForm, fbilltype);
                if (paramSetObj != null)
                {
                    decimal.TryParse(Convert.ToString(paramSetObj["fnotoutspotratio"]), out notOutSpotRatio);
                    decimal.TryParse(Convert.ToString(paramSetObj["foutspotratio"]), out outspotratio);
                }
            }
            this.Result.SrvData = new { notOutSpotRatio = notOutSpotRatio, outspotratio = outspotratio };
            this.Result.IsSuccess = true;
        }

    }
}
