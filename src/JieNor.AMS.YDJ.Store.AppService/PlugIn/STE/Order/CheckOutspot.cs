using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：检查非出现货库存情况
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("CheckOutspot")]
    public class CheckOutspot: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var DataEntity = this.SimpleData["entry"].FromJson<List<SimpleProduct>>();
            var entrys = DataEntity ;
            if (entrys==null||!entrys.Any())
            {
                return;
            }
            //检查非出现货库存
            var profileService2 = this.Context.Container.GetService<ISystemProfile>();
            var foutspotnotice = profileService2.GetSystemParameter(this.Context, "bas_storesysparam", "foutspotnotice", false);
            if (foutspotnotice)
            {
                var outpotmsg = "";              
                Dictionary<string, decimal> qtys = new Dictionary<string, decimal>();

                var index = 0;               
                foreach (var item in entrys)
                {
                    index++;//记录行数
                    var isout =item.fisoutspot;///是否出现货
                    if (isout)
                    {
                        continue;
                    }
                    var productid = item.fproductid;
                    if (productid.IsNullOrEmptyOrWhiteSpace())//排除空行
                    {
                        continue;
                    }
                    var material = this.Context.LoadBizDataById("bd_material", productid);//查找商品对应的明细信息
                    var materialname = Convert.ToString(material["fname"]);//商品名称
                    var target = new TargetSEP(Convert.ToString(material["fpublishcid"]), Convert.ToString(material["fpublishcid_pid"]));//目标地址
                    List<Dictionary<string, object>> postData = new List<Dictionary<string, object>>();//要查询的商品信息
                    postData.Add(
                        new Dictionary<string, object>
                        {
                                    { "productId", productid },
                                    { "lot",""}
                        }
                    );
                    var products = new List<string>();
                    products.Add(productid);
                    var res = Getinventory(products) as Dictionary<string, int>;                 
                    if (res.ContainsKey(productid))
                    {
                        if (outpotmsg.IsNullOrEmptyOrWhiteSpace())
                        {
                            outpotmsg += "第";
                        }
                        outpotmsg += $@"{index},";
                    }
                    //调用本地接口，获取库存信息
                    //var response = this.Gateway.Invoke(
                    //                        this.Context,
                    //                        target,
                    //                        new CommonBillDTO()
                    //                        {
                    //                            FormId = "stk_inventorylist",
                    //                            OperationNo = "getinventory",
                    //                            ExecInAsync = false,
                    //                            AsyncMode = (int)Enu_AsyncMode.Background,
                    //                            SimpleData = new Dictionary<string, string>
                    //                            {
                    //                            { "productInfos", Newtonsoft.Json.JsonConvert.SerializeObject(postData) },
                    //                            //{ "hopCount",(++hopCount).ToString()},
                    //                            { "idType","chainDataId"},
                    //                            { "modeType","1"}
                    //                            }
                    //                        }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
                    //                    ) as CommonBillDTOResponse;

                    //var srvData = response.OperationResult.SrvData;
                    //if (srvData != null)
                    //{
                    //    JToken result;
                    //    if (srvData is string)
                    //    {
                    //        result = JArray.Parse(srvData.ToString());
                    //    }
                    //    else
                    //    {
                    //        result = JArray.FromObject(srvData);
                    //    }
                    //    foreach (var resitem in result)
                    //    {
                    //        var qty = Convert.ToInt32(resitem["qty"]);
                    //        if (qty > 0)//如果有库存
                    //        {
                    //            if (outpotmsg.IsNullOrEmptyOrWhiteSpace())
                    //            {
                    //                outpotmsg += "第";
                    //            }
                    //            outpotmsg += $@"{index},";
                    //            break;
                    //        }
                    //    }
                    //}
                }

                if (outpotmsg.IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.IsSuccess = true;
                }
                else
                {
                    outpotmsg= outpotmsg.TrimEnd(',');
                    outpotmsg += "行目前有库存，请确认是否出现货";
                    this.Result.SimpleData.Add("msg", outpotmsg);
                    this.Result.IsSuccess = false;
                }
            }
        }

        public object Getinventory(List<string> productInfos)
        {
            var filter = "";
            for (var i = 0; i < productInfos.Count; i++)
            {
                var productId = productInfos[i];
                //找出物料编码 
                var product = this.Context.LoadBizDataById("ydj_product", productId);
                if (product.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                //查询 库存综合查询报表 过滤条件 
                filter = buildQueryReportParameter(product, filter, i);
            }
            var parentid = this.OperationContext.PageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : this.OperationContext.PageId;
            var listDto = new CommonListDTO()
               //.SetPageId(parentid)
               .SetFormId("rpt_stocksynthesize")
               .SetOperationNo("querydata")
               .SetSimpleData("filterString", filter);
            var resp = this.Gateway.InvokeLocal<CommonListDTOResponse>(this.Context, listDto, Enu_HttpMethod.Post);

            JArray dataList = null;
            var srvDataJson = resp?.OperationResult?.SrvData?.ToJson();
            if (!srvDataJson.IsNullOrEmptyOrWhiteSpace())
            {
                var srvData = JObject.Parse(srvDataJson);
                dataList = srvData?["data"] as JArray;
            }
            // return dataList;
            Dictionary<string, int> productqtydic = new Dictionary<string, int>();
            if (dataList != null)
            {               
                foreach (var data in dataList)
                {
                    var key = Convert.ToString(data["fmaterialid"]);
                    var value= Convert.ToInt32(data["fqty"]);
                    if (!productqtydic.ContainsKey(key))
                    {
                        productqtydic.Add(key, value);
                    }                    
                }
            }
            return productqtydic;
        }


        private String buildQueryReportParameter(DynamicObject product, string sql, int i)
        {
            if (i > 0)
            {
                sql += " union all ";
            }
            var fmaterialid = Convert.ToString(product["id"]);
            sql += "fmaterialid = '{0}' ".Fmt(fmaterialid);
            sql += " and fcustomdesc = '' and fmtono = '' and fstockusableqty >0";
            return sql;
        }
    }

    public class SimpleProduct
    {
        public string  fproductid { get; set; }

        public bool fisoutspot { get; set; }
    }


}
