using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：暂存
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("draft")]
    public class Draft : AbstractOperationServicePlugIn
    {

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            // 处理有问题的商品促销id
            var entrys = e.DataEntitys.SelectMany(s => (s["fentry"] as DynamicObjectCollection));
            foreach (var entry in entrys)
            {
                var fpromotionid = Convert.ToString(entry["fpromotionid"]);
                if (fpromotionid.EqualsIgnoreCase("{}") || fpromotionid.EqualsIgnoreCase("{id:,fnumber:,fname:}"))
                {
                    entry["fpromotionid"] = " ";
                }
            }
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context);
            }
        }
    }
}
