using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model;
using System.Collections.Generic;
using JieNor.Framework;
using System;
using JieNor.Framework.MetaCore.FormModel.Office;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Store.AppService.Model.Unboxing;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.Framework.DataTransferObject.Print;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.STE.Order
{
    /// <summary>
    /// 转单申请单打印
    /// </summary>
    [InjectService]
    [FormId("ydj_transferorderapply")]
    [OperationNo("print_trans")]
    public class GetPrintFileUrl : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var data = this.GetQueryOrSimpleParam<string>("fbillno");//打印信息
            if (data.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"打印信息不能为空！");
                return;
            }
            var fbillnos = data.Split(',').ToList<string>();

           // var bizdatas = this.Context.LoadBizDataByFilter(this.HtmlForm.Id, $" fnumber in ('{string.Join("','", fbillnos)}') ");

            var TransferOrderApplys = this.Context.LoadBizDataByFilter("ydj_transferorderapply", " fsourcetype='ydj_order' and fcancelstatus='0' and ftransferstatus<>'3' and  freceivercontractnumber in ('{0}')".Fmt(string.Join("','", fbillnos)));
            var TransferOrderApplyEntrys = (TransferOrderApplys.SelectMany(d => d["fentry"] as DynamicObjectCollection)).ToList();

            if (TransferOrderApplys != null && TransferOrderApplys.Count <= 0)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"未找到对应的转单申请单信息！");
                return;
            }

            //var orderids = bizdatas.SelectMany(x => x["fentry"] as DynamicObjectCollection)
            //                 .Select(x => Convert.ToString(x["id"]))
            //                 .ToList();

            var prints = new List<PrintData>();

            //根据接单方合同找到相同的送货方合同，合并其明细
            var TransOrderMerge = new List<DynamicObject>();
            var TransOrderGroup = TransferOrderApplys.GroupBy(o => Convert.ToString(o["fshippercontractnumber"]));
            foreach (var group in TransOrderGroup)
            {
                var groupentrys = group.SelectMany(o => o["fentry"] as DynamicObjectCollection);

                var TransOrderGroup_first = group.FirstOrDefault();
                var shipperApply = (DynamicObject)TransOrderGroup_first.Clone();

                var shipperApplyEntry = shipperApply["fentry"] as DynamicObjectCollection;
                shipperApplyEntry.Clear();

                foreach (var entry in groupentrys)
                {
                    shipperApplyEntry.Add(entry);
                }
                var freceiveragentamounts = group.Select(o => Convert.ToDecimal(o["freceiveragentamount"])).Sum();
                var ftargetagentamounts = group.Select(o => Convert.ToDecimal(o["ftargetagentamount"])).Sum();
                var fdistributionmethods = group.Select(o => Convert.ToString(o["fdistributionmethod"])).Distinct();
                shipperApply["freceiveragentamount"] = freceiveragentamounts;
                shipperApply["ftargetagentamount"] = ftargetagentamounts;
                shipperApply["fdistributionmethod"] = string.Join(",", fdistributionmethods);

                TransOrderMerge.Add(shipperApply);
            }

            foreach (var barcodeinfo in TransOrderMerge)
            { 
                var print = new PrintData();
                print.BizData = barcodeinfo;
                print.PrintCount = 1;
                prints.Add(print);
            }
            var templateid = GetTemplateData(this.HtmlForm.Id,"1","1")?.FirstOrDefault()?.id;

            if (templateid.IsNullOrEmptyOrWhiteSpace()) 
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"未找到对应的转单申请单打印模板！");
                return;
            }

            try
            {
                var printdatasource = printtmpl.CreatePrintData(this.Context, this.HtmlForm, prints);
                var svc = this.Container.GetService<IPrintService>();
                PrintOption fprintOption = svc.GetPrintTmpl(this.Context, this.HtmlForm.Id, templateid);
                if (fprintOption == null)
                {
                    throw new Exception("未找到对应的打印模板！");
                }
                var offCtx = svc.BuildPrintContext(this.Context, this.HtmlForm, printdatasource, fprintOption);
                var url = svc.PrintWithTemplete(offCtx);

                url = url.Replace("\\", "/");
                url = url.Substring(url.LastIndexOf("/prints"));
                //var host = "".GetCurrentAppServer()?.ToString();
                //url = host + url;
            
                this.Result.IsSuccess = true;
                this.Result.SrvData = url;
                this.Result.ComplexMessage.SuccessMessages.Add($"执行成功!");
            }
            catch (Exception ex)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add("发生异常了，请联系管理员："+ex.Message);
                return;
            }

        }

        private List<BaseDataModel> GetTemplateData(string formid, string isExportExcel, string IsDefault)
        {
            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fforbidstatus", System.Data.DbType.String, "0")
            };

            var filter = "";
            if (!string.IsNullOrWhiteSpace(formid))
            {
                filter += $" and fsrcformid='{formid}' ";
            }
            //if (isExportExcel == "1")
            //{
            //    filter += $" and ffiletype='xlsx' ";
            //}
            //if (IsDefault == "1")
            //{
            //    filter += $" and fdefault='1' ";
            //}

            var sqlText = $@"select fid,fnumber,fname from t_bas_officetmpl where (fmainorgid=@fmainorgid OR fmainorgid = '{this.Context.TopCompanyId}') and fforbidstatus=@fforbidstatus {filter} order by fdefault ";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            var models = new List<BaseDataModel>();
            foreach (var item in list)
            {
                var model = new BaseDataModel(item["fid"], item["fnumber"], item["fname"]);

                models.Add(model);
            }
            return models;
        }
    }
}
