//using JieNor.AMS.YDJ.MP.API.Utils;
//using JieNor.AMS.YDJ.Store.AppService.MuSi;
//using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
//using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
//using JieNor.Framework;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.Framework.SuperOrm;
//using JieNor.Framework.SuperOrm.DataEntity;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
//{
//    /// <summary>
//    /// 销售合同:发起售后
//    /// </summary>
//    [InjectService]
//    [FormId("ydj_order")]
//    [OperationNo("submitaftersale")]
//    public class SubmitAfterSale : AbstractOperationServicePlugIn
//    {
//        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
//        {
//            base.InitializeOperationDataEntity(e);

//            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

//            // 加载数据
//            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
//            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fproductid" });
//        }

//        /// <summary>
//        /// 预处理校验规则
//        /// </summary>
//        /// <param name="e"></param>
//        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
//        {
//            base.PrepareValidationRules(e);
//            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
//            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
//            //if (selectRowIds == null || selectRowIds.Count == 0) return;

//            string errorMsg = "";
//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                var entry = newData["fentry"] as DynamicObjectCollection;
//                foreach (var item in entry)
//                {
//                    if (selectRowIds.Any(a => a.Id == Convert.ToString(item["id"])))
//                    {
//                        if (Convert.ToDecimal(item["fbizpurinqty"])==0)
//                        {
//                            errorMsg = $"第{Convert.ToString(item["fseq"])}行商品【{Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fname"])}】当前商品暂未收货入库，不允许发起售后，谢谢！";
//                            return false;
//                        }
//                    }
//                }
//                return true;
//            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

//        }

//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            base.BeginOperationTransaction(e);
//            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

//            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
//            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
//            if (selectRowIds == null || selectRowIds.Count == 0) return;
//            var entry = e.DataEntitys[0]["fentry"] as DynamicObjectCollection;
//            string sourceEntryId = string.Empty;
//            foreach (var item in entry)
//            {
//                if (selectRowIds.Any(a => a.Id == Convert.ToString(item["id"])))
//                {
//                    sourceEntryId = Convert.ToString(item["id"]);

//                }
//            }
//            var param = new List<SqlParam>();
//            param.Add(new SqlParam("fentryid", System.Data.DbType.String, sourceEntryId));
//            param.Add(new SqlParam("fbillno", System.Data.DbType.String, e.DataEntitys[0]["fbillno"]));
//            //this.Context.LoadBizDataByFilter(this.HtmlForm.Id, " fsourceentryid_e=@fentryid and fcancelstatus=0 and fsourcenumber_e=@fbillno", false, param);
//            List<object> objs = new List<object>();
//            var orders = this.Context.LoadBizDataByFilter(this.HtmlForm.Id, " exists (select 1 from t_ydj_orderentry odmx where odmx.fid = t_ydj_order.fid and odmx.fsourceentryid_e =@fentryid and fsourcenumber_e=@fbillno) and fcancelstatus=0 ", false, param);
//            if (orders.Count > 0)
//            {
//                //存在下游数据
//                foreach (var item in orders)
//                {
//                    var _entry = item["fentry"] as DynamicObjectCollection;
//                    foreach (var entryIten in _entry)
//                    {
//                        var obj = new
//                        {
//                            fbillno = item["fbillno"],
//                            ffactorybillno = entryIten["ffactorybillno"],
//                            fomsprogress = entryIten["fomsprogress"]
//                        };
//                        objs.Add(obj);
//                    }
//                }
//            }
//            if (objs.Count > 0)
//            {
//                this.Result.SrvData = objs;
//                this.Result.IsSuccess = false;
//                return;
//            }




//            var prepareSaveDataService = this.Context.Container.GetService<IPrepareSaveDataService>();

//            foreach (var dataEntity in e.DataEntitys)
//            {

//                //创建这个销售合同
//                var orderFormType = this.HtmlForm.GetDynamicObjectType(this.Context);
//                var seqSrv = this.Context.Container.GetService<IDataEntityPkService>();
//                var dm = this.GetDataManager();
//                dm.InitDbContext(this.Context, orderFormType);
//                var orderData = new DynamicObject(orderFormType);
//                seqSrv.AutoSetPrimaryKey(this.Context, orderData, dm.DataEntityType);

//                orderData["forderdate"] = dataEntity["forderdate"];
//                orderData["fcustomerid"] = dataEntity["fcustomerid"];
//                orderData["fcustomertype"] = dataEntity["fcustomertype"];
//                orderData["fphone"] = dataEntity["fphone"];
//                orderData["fdeptid"] = dataEntity["fdeptid"];
//                orderData["fstaffid"] = dataEntity["fstaffid"];
//                orderData["fdeliverydate"] = dataEntity["fdeliverydate"];
//                orderData["fcustomercontactid"] = dataEntity["fcustomercontactid"];

//                // 加载经销商【标准销售合同】单据类型
//                var btxshtBillType = this.Context.GetNotForbidBillTypeByBizObject(this.HtmlForm.Id, "ydj_order_vsix_swj");
//                //this.BtxshtBillTypeId = btxshtBillType?.fid;
//                orderData["fbilltype"] = dataEntity["fbilltype"];
//                orderData["ftype"] = dataEntity["ftype"];
//                orderData["fprovince"] = dataEntity["fprovince"];
//                orderData["fcity"] = dataEntity["fcity"];
//                orderData["fregion"] = dataEntity["fregion"];
//                orderData["faddress"] = dataEntity["faddress"];
//                orderData["fstore"] = dataEntity["fstore"];
//                orderData["fcommercebillno"] = dataEntity["fcommercebillno"];
//                orderData["flogisticsitems"] = dataEntity["flogisticsitems"];
//                orderData["fwithin"] = dataEntity["fwithin"];
//                orderData["fmallorderno"] = dataEntity["fmallorderno"];
//                orderData["fmemberdesc"] = dataEntity["fmemberdesc"];
//                orderData["fdescription"] = dataEntity["fdescription"];
//                orderData["fbuildingid"] = dataEntity["fbuildingid"];
//                orderData["factivityid"] = dataEntity["factivityid"];
//                orderData["fchannel"] = dataEntity["fchannel"];
//                orderData["flinkindenttype"] = dataEntity["findenttype"];
//                orderData["fswjdesignerid"] = dataEntity["fswjdesignerid"];
                

//                var dutyentry = orderData["fdutyentry"] as DynamicObjectCollection;
//                var _dutyentry = dataEntity["fdutyentry"] as DynamicObjectCollection;
//                foreach (var _dutyentryItem in _dutyentry)
//                {
//                    var detailItem = dutyentry.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
//                    detailItem["fismain"] = _dutyentryItem["fismain"];
//                    detailItem["fdutyid"] = _dutyentryItem["fdutyid"];
//                    detailItem["fdeptid"] = _dutyentryItem["fdeptid"];
//                    detailItem["fratio"] = _dutyentryItem["fratio"];
//                    detailItem["famount"] = 0;
//                    detailItem["fdescription"] = _dutyentryItem["fdescription"];
//                    dutyentry.Add(detailItem);
//                }
//                var tagentry = orderData["fentry"] as DynamicObjectCollection;
//                var sourcedutyentry = dataEntity["fentry"] as DynamicObjectCollection;
//                foreach (var item in sourcedutyentry)
//                {
//                    var detailItem = tagentry.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
//                    detailItem["fproductid"] = item["fproductid"];
//                    detailItem["funitid"] = item["funitid"];
//                    detailItem["fqty"] = item["fqty"];
//                    detailItem["fbizqty"] = item["fbizqty"];
//                    detailItem["fattrinfo"] = item["fattrinfo"];
//                    detailItem["fsourceentryid_e"] = item["id"];
//                    detailItem["fsourcenumber_e"] = dataEntity["fbillno"];
//                    detailItem["fsourcetype_e"] = this.HtmlForm.Id;
//                    detailItem["fsfactorybillno"] = item["ffactorybillno"];
//                    detailItem["fsfactorybillid"] = item["ffactorybillid"];

//                    tagentry.Add(detailItem);
//                }
//                //因为没有明细行，所以只能做暂存
//                //prepareSaveDataService.PrepareDataEntity(this.Context, this.HtmlForm, new[] { orderData }, OperateOption.Create());
//                //var res = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new DynamicObject[] { orderData }, "draft", new Dictionary<string, object>() { });
//                //dm.Save(dataEntity);
//                //if (res.IsSuccess)
//                //{
//                //    JArray array = JsonConvert.DeserializeObject<JArray>(JsonConvert.SerializeObject(res.SrvData));
//                //    string number = array.Count > 0 ? Convert.ToString(array[0]["number"]) : "";
//                //    this.Result.ComplexMessage.SuccessMessages.Add("销售合同生成成功！");
//                //    this.Result.ComplexMessage.SuccessMessages.Add($"销售合同编号【{number}】");
//                //}



//                var action = this.OperationContext.UserContext.ShowSpecialForm(this.HtmlForm,
//                        orderData,
//                        false,
//                        this.CurrentPageId,
//                        Enu_OpenStyle.Modal,
//                        Enu_DomainType.Bill,
//                        new Dictionary<string, object>(),
//                        formPara =>
//                        {
//                            formPara.Status = Enu_BillStatus.Push;
//                        });
//                this.OperationContext.Result.HtmlActions.Add(action);
//                //this.
//            }


//        }
//    }
//}
