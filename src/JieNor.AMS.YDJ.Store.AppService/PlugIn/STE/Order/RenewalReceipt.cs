using JieNor.AMS.YDJ.Store.AppService.MuSi.Api;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：焕新订单发起收款
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("RenewalReceipt")]
    public class RenewalReceipt : AbstractOperationServicePlugIn
    {
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            var renewalflagFld = this.HtmlForm.GetField("frenewalflag");
            if (renewalflagFld == null)
            {
                throw new BusinessException("销售合同模型没有【焕新订单标记】字段，请检查");
            }
        }

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fproductid" });
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(new RenewalReceiptValidation());
        }


        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            base.BeginOperationTransaction(e);

            foreach (var dataEntity in e.DataEntitys)
            {
                //类型默认收款
                var type = 0;
                //如果是总部已退款，则焕新订单发起退款
                var fiszbrefund = Convert.ToBoolean(dataEntity["fiszbrefund"]);
                if (fiszbrefund)
                {
                    type = 1;
                }

                var dto = new PushRenewalOrderDTO(this.Context, dataEntity, type);

                dataEntity["fsettlprogress"] = Enu_RenewalSettleProgress.收款中; //收款中
                if (Convert.ToBoolean(dataEntity["frenewalrectag"]))
                {
                    dataEntity["frecdealamount_gb"] = dataEntity["fdealamount"];
                }

                MuSiMemberApi.PushRenewalOrder(this.Context, dto);
            }

            this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);

            this.Result.IsSuccess = true;
            this.AddRefreshPageAction();
        }
    }



    /// <summary>
    /// 焕新订单：发起收款
    /// </summary>
    public class RenewalReceiptValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        protected ValidationResult Result { get; set; }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities,
            OperateOption option,
            string operationNo)
        {
            this.Result = new ValidationResult();

            if (dataEntities == null || !dataEntities.Any()) return this.Result;

            var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, dataEntities, true, formInfo, new List<string> { "frenewtype" });

            CheckRenewalFlag(dataEntities);
            CheckRenewTyepe(dataEntities);
            CheckOrderAddress(dataEntities);
            CheckResellOrder(dataEntities);
            CheckSettlProgress(dataEntities);
            CheckTransferOrder(dataEntities);
            CheckBillType(dataEntities);
            CheckDeliver(userCtx, dataEntities);
            CheckCustomCategory(dataEntities);

            return this.Result;
        }

        private void CheckRenewalFlag(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                if (!frenewalflag)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"销售合同【{dataEntity["fbillno"]}】的【焕新订单标记】为否，不允许操作。",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        /// <summary>
        /// 判断焕新订单类型是否 勾选 【允许发起收款】,勾选了 才允许发起收款操作。
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CheckRenewTyepe(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["frenewtype"]).IsNullOrEmptyOrWhiteSpace()) continue;

                var fisincome = Convert.ToBoolean((dataEntity["frenewtype_ref"] as DynamicObject)?["fisincome"] ?? false);
                if (!fisincome)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"销售合同【{dataEntity["fbillno"]}】的 焕新订单类型【允许发起收款】未勾选，不允许操作。",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        private void CheckOrderAddress(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToString(dataEntity["frenewtype"]).IsNullOrEmptyOrWhiteSpace()) continue;
                var fprovince = Convert.ToString(dataEntity?["fprovince"]) ?? "";
                var fcity = Convert.ToString(dataEntity?["fcity"]) ?? "";
                var fregion = Convert.ToString(dataEntity?["fregion"]) ?? "";
                var faddress = Convert.ToString(dataEntity?["faddress"]) ?? "";
                if (string.IsNullOrWhiteSpace(fprovince) || string.IsNullOrWhiteSpace(fcity) || string.IsNullOrWhiteSpace(fregion) || string.IsNullOrWhiteSpace(faddress))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"当前焕新合同【{dataEntity["fbillno"]}】的【省】【市】【区】【详细地址】均必录，请确认！",
                        DataEntity = dataEntity,
                    });
                }
                var RenewtypeObj = (dataEntity["frenewtype_ref"] as DynamicObject);
                var fenableaddress = Convert.ToBoolean(RenewtypeObj?["fenableaddress"] ?? false);
                var fenableaddress_gb = Convert.ToBoolean(RenewtypeObj?["fenableaddress_gb"] ?? false);
                //如果启用地址的话
                if (fenableaddress)
                {
                    var IsMatch = AddressMatch(RenewtypeObj, dataEntity, "");
                    if (!IsMatch)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"当前焕新合同【{dataEntity["fbillno"]}】的地址，不符合要求，请确认！",
                            DataEntity = dataEntity,
                        });
                    }
                }
                //启用国补地址
                if (fenableaddress_gb)
                {
                    //var exist = SetGbAddressData(this.Context,dataEntity);
                    var exist = this.Context.Container.GetService<IOrderService>().SetGbAddressData(this.Context, dataEntity);
                    if (!exist) 
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"当前经销商不符合参与国补条件,请联系总部人员确认！",
                            DataEntity = dataEntity,
                        });
                    }
                    //var IsMatch = AddressMatch(RenewtypeObj, dataEntity, "_gb");
                    //if (!IsMatch)
                    //{
                    //    this.Result.Errors.Add(new ValidationResultEntry()
                    //    {
                    //        ErrorMessage = $"当前焕新合同【{dataEntity["fbillno"]}】的国补地址，不符合[{RenewtypeObj["fname"]}]要求，请确认！",
                    //        DataEntity = dataEntity,
                    //    });
                    //}
                }
            }
        }
        private bool AddressMatch(DynamicObject RenewtypeObj, DynamicObject order, string GbStr)
        {
            bool isMatch = true;
            var fprovince = Convert.ToString(RenewtypeObj?["fprovince"]) ?? "";
            var fcity = Convert.ToString(RenewtypeObj?["fcity"]) ?? "";
            var fregion = Convert.ToString(RenewtypeObj?["fregion"]) ?? "";
            var fprovince_od = Convert.ToString(order?[$"fprovince{GbStr}"]) ?? "";
            var fcity_od = Convert.ToString(order?[$"fcity{GbStr}"]) ?? "";
            var fregion_od = Convert.ToString(order?[$"fregion{GbStr}"]) ?? "";
            //先取焕新配置里最细维度。如果设置到了区，那合同只能以按区去匹配是否满足
            if (!fregion.IsNullOrEmptyOrWhiteSpace())
            {
                return fregion.EqualsIgnoreCase(fregion_od);
            }
            //如果焕新配置只设置到了市，则合同只匹配市即可
            if (!fcity.IsNullOrEmptyOrWhiteSpace())
            {
                return fcity.EqualsIgnoreCase(fcity_od);
            }
            //如果焕新配置只设置到了省，则合同只匹配省即可
            if (!fprovince.IsNullOrEmptyOrWhiteSpace())
            {
                return fprovince.EqualsIgnoreCase(fprovince_od);
            }

            return isMatch;
        }

        private void CheckSettlProgress(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var fsettlprogress = Convert.ToString(dataEntity["fsettlprogress"]);
                if (!fsettlprogress.EqualsIgnoreCase(Enu_RenewalSettleProgress.待发起))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"销售合同【{dataEntity["fbillno"]}】的【结算进度】不是“待发起”，不允许操作。",
                        DataEntity = dataEntity,
                    });
                }
            }
        }


        /// <summary>
        /// 校验二级分销
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CheckResellOrder(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var fisresellorder = Convert.ToBoolean(dataEntity["fisresellorder"]);
                if (fisresellorder)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"二级分销生成的焕新标记订单，不允许发起收款，谢谢！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }


        /// <summary>
        /// 校验转单
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CheckTransferOrder(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var billTypeObj = dataEntity["fbilltype_ref"] as DynamicObject;
                if (billTypeObj != null && Convert.ToString(billTypeObj["fname"]).EqualsIgnoreCase("销售转单"))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"销售转单生成的焕新标记订单，不允许发起收款，谢谢！",
                        DataEntity = dataEntity,
                    });
                }
            }
        }

        private void CheckBillType(IEnumerable<DynamicObject> dataEntities)
        {
            /*
             * 4、按单据类型区分，校验处理：
             * ①若【单据类型】≠’v6定制柜合同’：{当【焕新订单标记】=是 and 【数据状态】=已审核 and 【结算进度】=“待发起”}，才允许操作此按钮。
             * ②若【单据类型】=’v6定制柜合同’：{当【焕新订单标记】=是  and (所有商品明细【定制订单进度】=’流程完成’ 或者’单据作废’) and 【结算进度】=’待发起’} ，才允许操作此按钮。--注意：至少要有1行商品【定制订单进度】=’流程完成’。
             */

            foreach (var dataEntity in dataEntities)
            {
                var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                var fstatus = Convert.ToString(dataEntity["fstatus"]);
                var fsettlprogress = Convert.ToString(dataEntity["fsettlprogress"]);
                var renewalrectag = Convert.ToBoolean(dataEntity["frenewalrectag"]);

                var billTypeObj = dataEntity["fbilltype_ref"] as DynamicObject;

                if (billTypeObj != null && !Convert.ToString(billTypeObj["fname"]).EqualsIgnoreCase("v6定制柜合同"))
                {
                    if (!(frenewalflag && fstatus.EqualsIgnoreCase("E") &&
                          fsettlprogress.EqualsIgnoreCase(Enu_RenewalSettleProgress.待发起)))
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"非定制订单，只允许【焕新订单标记】=是，【数据状态】=已审核且【结算进度】=“待发起”，发起收款，谢谢！",
                            DataEntity = dataEntity,
                        });
                    }
                }
                else
                {
                    var fentry = dataEntity["fentry"] as DynamicObjectCollection;
                    // 所有行“流程完成”或“单据作废”
                    var allFinish = fentry.All(s =>
                        Convert.ToString(s["fomsprogress"]).EqualsIgnoreCase(Enu_OMSProgress.流程完成) || Convert.ToString(s["fomsprogress"]).EqualsIgnoreCase(Enu_OMSProgress.单据作废));
                    // 至少一行“流程完成”
                    var oneFinish = fentry.Any(s =>
                        Convert.ToString(s["fomsprogress"]).EqualsIgnoreCase(Enu_OMSProgress.流程完成));

                    if (renewalrectag)
                    {
                        if (!(frenewalflag && fsettlprogress.EqualsIgnoreCase(Enu_RenewalSettleProgress.待发起)))
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"定制订单，只允许【焕新订单标记】=是，且【结算进度】=“待发起”，发起收款，谢谢！",
                                DataEntity = dataEntity,
                            });
                        }
                    }
                    else
                    {
                        if (!(frenewalflag && allFinish && oneFinish && fsettlprogress.EqualsIgnoreCase(Enu_RenewalSettleProgress.待发起)))
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"定制订单，只允许【焕新订单标记】=是，所有商品明细【定制订单进度】=“流程完成”或“单据作废”且【结算进度】=“待发起”，发起收款，谢谢！",
                                DataEntity = dataEntity,
                            });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 校验送达方
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CheckDeliver(UserContext userCtx, IEnumerable<DynamicObject> dataEntities)
        {
            /*
             * 9.1.2.1.一级经销商
             * <1>.开发需梳理后台合同转采购时，每个商品匹配送达方的逻辑，迁移至<发起收款>节点。
             * <2>.当合同<发起收款>时：
             * ①当存在未获取到送达方的商品时，需要校验拦截并提醒：“第XX行商品[商品编码][商品名称]无匹配的送达方，请检查！”
             * ②当能获取到送达方，但是采购订单【供应商】无法获取到时，需要校验拦截并提醒：“第XX行商品[商品编码][商品名称]能匹配到送达方[送达方编码][送达方名称]，但无法匹配到供应商，请检查！”
             * 9.1.2.2.二级经销商
             * 二级经销商的合同<发起收款>时，依然增加上述校验。但是需要依据对应一级经销商合同转采购匹配送达方的逻辑，判断对应商品最终在一级经销商转采购时能否匹配到送达方，来加以判断。
             * 最终：
             * ①当存在未获取到送达方的商品时，需要校验拦截并提醒：“第XX行商品[商品编码][商品名称]无匹配的送达方，请检查！”
             * ②当能获取到送达方，但是采购订单【供应商】无法获取到时，需要校验拦截并提醒：“第XX行商品[商品编码][商品名称]能匹配到一级送达方[送达方编码][送达方名称]，但无法匹配到一级供应商，请检查！”
             */
            IPurchaseOrderService purchaseOrderService = userCtx.Container.GetService<IPurchaseOrderService>();

            foreach (var dataEntity in dataEntities)
            {
                var deptId = dataEntity["fdeptid"]?.ToString();
                //得到城市
                var cityId = GetStoreCity(userCtx, deptId);

                var fentries = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var entry in fentries)
                {
                    var fsaleorgid = entry["fshipperdeliver"]?.ToString();
                    if (fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var fresultbrandid = entry["fresultbrandid"]?.ToString();
                        var dm = GetDeliverByBrandidAndCity(userCtx, fresultbrandid, cityId);
                        if (dm != null)
                        {
                            //送达方赋值
                            fsaleorgid = dm["id"]?.ToString();
                        }
                    }

                    if (fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var product = entry["fproductid_ref"] as DynamicObject;

                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"第{entry["fseq"]}行商品[{product?["fnumber"]}][{product?["fname"]}]无匹配的送达方，请检查！",
                            DataEntity = dataEntity,
                        });
                        continue;
                    }

                    var fsupplierid = Convert.ToString(entry["fsupplierid"]);
                    if (fsupplierid.IsNullOrEmptyOrWhiteSpace())
                    {
                        if (!fsaleorgid.IsNullOrEmptyOrWhiteSpace())
                        {
                            fsupplierid = purchaseOrderService.GetSupplierIdByDeliverId(userCtx, fsaleorgid);

                            if (fsupplierid.IsNullOrEmptyOrWhiteSpace())
                            {
                                var product = entry["fproductid_ref"] as DynamicObject;

                                var deliver =
                                    userCtx.LoadBizBillHeadDataById("bas_deliver", fsaleorgid, "fnumber,fname");

                                if (userCtx.IsSecondOrg)
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"第{entry["fseq"]}行商品[{product?["fnumber"]}][{product?["fname"]}]能匹配到一级送达方[{deliver?["fnumber"]}][{deliver?["fname"]}]，但无法匹配到一级供应商，请检查！",
                                        DataEntity = dataEntity,
                                    });
                                }
                                else
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"第{entry["fseq"]}行商品[{product?["fnumber"]}][{product?["fname"]}]能匹配到送达方[{deliver?["fnumber"]}][{deliver?["fname"]}]，但无法匹配到供应商，请检查！",
                                        DataEntity = dataEntity,
                                    });
                                }
                            }
                        }
                    }
                }


            }
        }

        /// <summary>
        /// 得到门店城市
        /// </summary>
        /// <param name="deptId"></param>
        /// <returns></returns>
        private string GetStoreCity(UserContext userCtx, string deptId)
        {
            string strSql = @"select top 1 t2.fmycity from t_bd_department t1 with(nolock)
                            join t_bas_store t2 with(nolock) on t1.fstore = t2.fid where t1.fid =@fid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fid", System.Data.DbType.String, deptId)
            };
            string fmycity = "";
            using (var dr = userCtx.ExecuteReader(strSql, sqlParam))
            {
                if (dr.Read())
                {
                    fmycity = Convert.ToString(dr["fmycity"]);
                }
            }

            return fmycity;
        }



        /// <summary>
        /// 根据城市和品牌找到送达方
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        private DynamicObject GetDeliverByBrandidAndCity(UserContext userCtx, string fresultbrandid, string fcity)
        {
            if (fresultbrandid.IsNullOrEmptyOrWhiteSpace() || fcity.IsNullOrEmptyOrWhiteSpace())
                return null;
            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(userCtx, "bas_deliver");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));
            //// 按 实控人 获取当前用户对应的所有经销商组织
            //var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentAll(UserContext);
            ////组织
            //var Agents = AgentInfos.Select(o => o.OrgId).ToList();
            //var cid = UserContext.IsTopOrg ? UserContext.TopCompanyId : UserContext.Company;
            //Agents.Add(cid);
            var Agents = new List<string>() { userCtx.Company };
            //获取当前用户登录经销商的《主经销商配置表》
            var topCtx = userCtx.CreateTopOrgDBContext();
            var mainAgentConfigs = topCtx.LoadBizDataByACLFilter("bas_mainagentconfig", $" fmainagentid = '{userCtx.Company}'  AND fforbidstatus='0' ").FirstOrDefault();
            if (mainAgentConfigs != null)
            {
                //存在配置，则需要将所有子经销商也包含进来
                var subAgentEntrys = mainAgentConfigs["fsubagent"] as DynamicObjectCollection;
                if (subAgentEntrys != null && subAgentEntrys.Any())
                {
                    Agents.AddRange(subAgentEntrys.Select(t => Convert.ToString(t["fsubagentid"])));
                }
            }

            var where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}'".Fmt(string.Join("','", Agents), fcity);

            var sqlParam = new List<SqlParam>
            {
            };
            var reader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);
            var data = dm.SelectBy(reader).OfType<DynamicObject>();
            if (data != null && data.Count() > 0)
            {
                //35321 【慕思现场-正式区问题 526】调整 销售转采购 送达方生成逻辑, 如果匹配到多个送达方时, 系统不进行赋值让用户手动选择
                var count = data.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                if (count >= 1)
                {
                    foreach (var item in data)
                    {
                        var fentry = item["fentry"] as DynamicObjectCollection;
                        var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                        if (isexists)
                            return item;
                    }
                }
                //如果城市 +系列没匹配到 还是要走上级城市逻辑
                if (count == 0)
                {
                    var parentcity = userCtx.Container.GetService<IOrderService>().GetParentCity(userCtx, fcity);
                    if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                    where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' ".Fmt(string.Join("','", Agents), parentcity);
                    var deliver = userCtx.LoadBizDataByFilter("bas_deliver", where);
                    if (!deliver.IsNullOrEmptyOrWhiteSpace())
                    {
                        var count_temp = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                        if (count_temp >= 1)
                        {
                            foreach (var item in deliver)
                            {
                                var fentry = item["fentry"] as DynamicObjectCollection;
                                var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                                if (isexists)
                                    return item;
                            }
                        }
                    }
                }
            }
            //通过深圳市宝安区匹配不到，尝试通过深圳市匹配
            else
            {
                var parentcity = userCtx.Container.GetService<IOrderService>().GetParentCity(userCtx, fcity);

                if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' ".Fmt(string.Join("','", Agents), parentcity);
                var deliver = userCtx.LoadBizDataByFilter("bas_deliver", where);
                if (!deliver.IsNullOrEmptyOrWhiteSpace())
                {
                    var count = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                    if (count >= 1)
                    {
                        foreach (var item in deliver)
                        {
                            var fentry = item["fentry"] as DynamicObjectCollection;
                            var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                            if (isexists)
                                return item;
                        }
                    }
                }
            }
            return null;
        }


        /// <summary>
        /// 校验当合同【单据类型】！=v6定制柜合同  and 【焕新订单标记】=是，校验【焕新定制柜类别】必须为空。
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CheckCustomCategory(IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var billTypeObj = dataEntity["fbilltype_ref"] as DynamicObject;
                if (billTypeObj != null && !Convert.ToString(billTypeObj["fname"]).EqualsIgnoreCase("v6定制柜合同"))
                {
                    var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                    var customcategory = Convert.ToString(dataEntity["fcustomcategory"]);
                    if (frenewalflag && !customcategory.IsNullOrEmptyOrWhiteSpace())
                    {

                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"焕新订单非v6定制柜合同,【焕新定制柜类别】必须为空！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }
    }
}
