using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    public class Validation_SubmitHeadQuart : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        /// <summary>
        /// 是否是列表进来的
        /// </summary>
        private bool IsList { set; get; }

        private HtmlForm htmlForm { set; get; }

        private bool IsPriceCanZeroParam { set; get; }

        public Validation_SubmitHeadQuart()
        {

        }

        public Validation_SubmitHeadQuart(bool isList)
        {
            this.IsList = isList;
        }

        /// <summary>
        /// 校验逻辑处理单元
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        /// <returns></returns>
        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return this.Result;
            }

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();

            this.htmlForm = metaModelService.LoadFormModel(userCtx, "ydj_order");

            var agentInfoDy = userCtx.LoadBizBillHeadDataById("bas_agent", userCtx.Company, "fdirectsalesgiveawaynotzero");

            if (agentInfoDy != null)
            {
                this.IsPriceCanZeroParam = Convert.ToBoolean(Convert.ToInt32(agentInfoDy["fdirectsalesgiveawaynotzero"]));
            }
            //焕新订单的
            var reNewOrderDys = dataEntities.Where(x => Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            //销售合同的
            var normalOrderDys = dataEntities.Where(x => !Convert.ToBoolean(Convert.ToInt32(x["frenewalflag"]))).ToList();

            //焕新类型的销售合同提交总部
            if (reNewOrderDys != null && reNewOrderDys.Any())
            {
                ValidateReNewBillType(userCtx, formInfo, reNewOrderDys.ToArray(), option, operationNo);
            }

            //校验正常的销售合同提交总部
            if (normalOrderDys != null && normalOrderDys.Any())
            {
                ValidateNormalBillType(userCtx, formInfo, normalOrderDys.ToArray(), option, operationNo);
            }

            return this.Result;
        }

        /// <summary>
        /// 校验正常的销售合同提交总部
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        private void ValidateNormalBillType(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            CheckOrderBillStatus(userCtx, dataEntities);
            CheckCanSubmitTopOrg(userCtx, dataEntities);
            CheckBillTypeIsStandardTransfer(userCtx, dataEntities);
            CheckOrderChannel(userCtx, dataEntities);
            //CheckZYOrder(dataEntities);
            CheckProductEntryGiveWayPriceCanZero(this.IsPriceCanZeroParam, userCtx, dataEntities);
            
            CheckDirectOrderBillTypeIsStoreSampleAndCanSubmitHq(userCtx,dataEntities,this.Result);
        }

        /// <summary>
        /// 焕新类型的销售合同提交总部
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntities"></param>
        /// <param name="option"></param>
        /// <param name="operationNo"></param>
        private void ValidateReNewBillType(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            var MSRenewalNotify = option.GetVariableValue("MSRenewalNotify", false);

            var fmainorgids = dataEntities.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
            var agentInfo = GetAgentInfo(userCtx, fmainorgids);

            ///焕新订单类型
            var frenewtypes = dataEntities.Select(x => Convert.ToString(x["frenewtype"])).ToList();
            var getRenewType = GetRenewType(userCtx, frenewtypes);

            //允许发起收款的焕新流程，慕思会员小程序收款完成回传杰诺时，自动触发提交总部，不需要考虑此校验。
            if (!MSRenewalNotify)
            {
                CheckOrderData(userCtx, dataEntities, agentInfo, getRenewType);
            }
            CheckOrderBillStatus(userCtx, dataEntities);
            CheckRenewType(userCtx, dataEntities, getRenewType);
            CheckIsRepeat(userCtx, dataEntities);
            //CheckZYOrder(dataEntities);
            CheckCanSubmitTopOrg(userCtx, dataEntities);
            CheckDataFromListForm(userCtx, dataEntities);
            CheckBillTypeIsStandardTransfer(userCtx, dataEntities);
            CheckOrderChannel(userCtx, dataEntities);
            CheckProductEntryGiveWayPriceCanZero(this.IsPriceCanZeroParam, userCtx, dataEntities);

            CheckDirectOrderBillTypeIsStoreSampleAndCanSubmitHq(userCtx,dataEntities,this.Result);
        }

        /// <summary>
        /// 当【焕新订单标记】勾选，并且【焕新订单类型.允许发起收款=否】，针对以下字段进行必录校验：合同附件、会员商城交易流水号、会员商城支付日期、补贴总金额。
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="getRenewType"></param>
        private void CheckRenewType(UserContext userCtx, IEnumerable<DynamicObject> dataEntities, Dictionary<string, int> getRenewType)
        {
            foreach (var dataEntity in dataEntities)
            {
                //勾选焕新订单标记
                var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                var fbillno = Convert.ToString(dataEntity["fbillno"]);
                //焕新订单类型.允许发起收款=否
                var frenewtype = Convert.ToString(dataEntity["frenewtype"]);
                var isfrenewtype = getRenewType.GetValue(frenewtype);


                if (frenewalflag)
                {
                    //合同附件
                    //改到提交节点校验
                    //var fimage = Convert.ToString(dataEntity["fimage"]);
                    //if (fimage.IsNullOrEmptyOrWhiteSpace())
                    //{
                    //    this.Result.Errors.Add(new ValidationResultEntry()
                    //    {
                    //        ErrorMessage = $"提交失败，焕新订单{fbillno}【合同附件】必填!",
                    //        DataEntity = dataEntity,
                    //    });
                    //}
                    //商城交易流水号
                    var fmembershiptranid = Convert.ToString(dataEntity["fmembershiptranid"]);
                    if (fmembershiptranid.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"提交失败，焕新订单{fbillno}【会员商城交易流水号】必填!",
                            DataEntity = dataEntity,
                        });
                    }
                    //商城支付日期
                    var fmembershippaydate = Convert.ToString(dataEntity["fmembershippaydate"]);
                    if (fmembershippaydate.IsNullOrEmptyOrWhiteSpace())
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"提交失败，焕新订单{fbillno}【会员商城支付日期】必填!",
                            DataEntity = dataEntity,
                        });
                    }

                    //当单据头.【补贴总金额】 不等于所有商品明细行【补贴金额】之和时，拦截。
                    var fsubsidyamount = Convert.ToDecimal(dataEntity["fsubsidyamount"]);
                    var fentry = dataEntity["fentry"] as DynamicObjectCollection;
                    var sumSubsidyAmount = fentry != null ? fentry.ToList().Sum(x => Convert.ToDecimal(x["fsubsidyamount"])) : 0;
                    if (fsubsidyamount != sumSubsidyAmount)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"提交失败，订单编号：{fbillno}【补贴总金额】需要等于商品明细行【补贴金额】之和时,才允许锁单！",
                            DataEntity = dataEntity,
                        });
                    }

                    //补贴总金额
                    if (fsubsidyamount == 0M)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"提交失败，焕新订单{fbillno}【补贴总金额】必填!",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 当【创建该单据的经销商.经营类型=直营】，并且【焕新订单标记】勾选，并且【焕新订单类型.允许发起收款=否】，并且【结算进度=待发起】才允许操作此按钮
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        /// <param name="agentInfo"></param>
        /// <param name="getRenewType"></param>
        private void CheckOrderData(UserContext userCtx, IEnumerable<DynamicObject> dataEntities, Dictionary<string, bool> agentInfo, Dictionary<string, int> getRenewType)
        {
            foreach (var dataEntity in dataEntities)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]);
                //创建单据的经销商的经销类型-直营
                var fmainorgid = Convert.ToString(dataEntity["fmainorgid"]);
                var isfmainorgid = agentInfo.GetValue(fmainorgid);
                if (!isfmainorgid)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"提交失败，订单编号：{fbillno}【经销商经销类型】需为【直营】,才允许直营锁单！",
                        DataEntity = dataEntity,
                    });
                }
                //勾选焕新订单标记
                var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                if (!frenewalflag)
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"提交失败，订单编号：{fbillno}需要勾选【焕新订单标记】,才允许直营锁单！",
                        DataEntity = dataEntity,
                    });
                }
                //焕新订单类型.允许发起收款=否
                var frenewtype = Convert.ToString(dataEntity["frenewtype"]);
                var isfrenewtype = getRenewType.GetValue(frenewtype);
                if (isfrenewtype.IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"提交失败，订单编号：{fbillno}【焕新订单类型】允许发起收款为否,才允许提交！",
                        DataEntity = dataEntity,
                    });
                }

                //结算进度=待发起
                var fsettlprogress = Convert.ToString(dataEntity["fsettlprogress"]);
                //协同总部状态
                var fchstatus = Convert.ToString(dataEntity["fchstatus"]);
                if (fchstatus.Equals("2")|| fchstatus.Equals("4"))
                {
                    //不做校验
                }
                else if (fchstatus.Equals("1"))
                {
                    //不做校验（则无需校验【结算进度】，只需要报错提醒“XXXX已经直营锁单，不能再次提交！”）该校验在下面有
                }
                else if (fchstatus.Equals("3"))
                {
                    //不做校验（则无需校验【结算进度】，只需要报错提醒“XXXX已经终审，不能再次直营锁单！）该校验在下面有
                }
                //if (fsettlprogress != "0" && fchstatus != "2")
                //{
                //    this.Result.Errors.Add(new ValidationResultEntry()
                //    {
                //        ErrorMessage = $"提交失败，订单编号：{fbillno}【结算进度】需要为【待发起】,才允许直营锁单！",
                //        DataEntity = dataEntity,
                //    });
                //}
            }
        }

        /// <summary>
        /// 重复性校验
        /// 3、当【焕新订单标记】勾选 并且【结算进度】不等于“已退款” ，依据{【焕新订单类型】+【会员商城交易流水号】}做重复性校验。
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        private void CheckIsRepeat(UserContext userCtx, IEnumerable<DynamicObject> dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                if (Convert.ToBoolean(dataEntity["frenewalflag"])
            && !Convert.ToString(dataEntity["fsettlprogress"]).EqualsIgnoreCase("3")
            && !Convert.ToString(dataEntity["frenewtype"]).IsNullOrEmptyOrWhiteSpace()
            )
                {
                    //会员商城交易流水号为空不校验
                    if (Convert.ToString(dataEntity["fmembershiptranid"]).IsNullOrEmptyOrWhiteSpace()) return;

                    var htmlForm = userCtx.Container.GetService<IMetaModelService>()?.LoadFormModel(userCtx, "ydj_order");
                    var dm = userCtx.Container.GetService<IDataManager>();
                    dm.InitDbContext(userCtx, htmlForm.GetDynamicObjectType(userCtx));

                    string where = $@" fmainorgid = @fmainorgid and frenewtype=@frenewtype and fsettlprogress != '3' and fmembershiptranid = @fmembershiptranid and fid != @fid ";
                    var sqlParam = new SqlParam[]
                    {
                    new SqlParam("fid", System.Data.DbType.String, dataEntity["id"]),
                    new SqlParam("frenewtype", System.Data.DbType.String, dataEntity["frenewtype"]),
                    new SqlParam("fmainorgid", System.Data.DbType.String, dataEntity["fmainorgid"]),
                    new SqlParam("fmembershiptranid", System.Data.DbType.String, dataEntity["fmembershiptranid"])
                    };
                    var dataReader = userCtx.GetPkIdDataReader(htmlForm, where, sqlParam);
                    var order = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                    if (order != null)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"对不起，当前焕新订单{dataEntity["fbillno"]}的会员商城交易流水号 与{order["fbillno"]}冲突，请检查！",
                            DataEntity = dataEntity,
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检查是否可以提交总部(已经提交总部的就不能再次提交，除非是驳回状态)
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="dataEntities">销售合同数据包</param>
        private void CheckCanSubmitTopOrg(UserContext userCtx, DynamicObject[] dataEntities)
        {
            foreach (var orderDy in dataEntities)
            {
                //总部提交状态
                var chStatus = Convert.ToString(orderDy["fchstatus"]);
                if (!chStatus.IsNullOrEmptyOrWhiteSpace())
                {
                    //'1':'已提交总部','2':'已驳回','3':'已终审'，‘4’:锁单失败
                    //已提交状态总部，这时候需要先判断
                    switch (chStatus.ToLower())
                    {
                        case "1":
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"提交失败，订单编号：【{orderDy["fbillno"]}】已经直营锁单，不能再次提交！",
                                DataEntity = orderDy
                            });
                            break;
                        case "2":
                            break;
                        case "3":
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"提交失败，订单编号：【{orderDy["fbillno"]}】已经终审，不能再次直营锁单！",
                                DataEntity = orderDy
                            });
                            break;
                        default:
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// 检查销售合同的数据状态是否为【已审核】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckOrderBillStatus(UserContext userCtx, DynamicObject[] dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var status = Convert.ToString(dataEntity["fstatus"]);
                if (!status.IsNullOrEmptyOrWhiteSpace() && !status.EqualsIgnoreCase("E"))
                {
                    this.Result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"销售合同【{Convert.ToString(dataEntity["fbillno"])}】的数据状态不为【已审核】，不能直营锁单",
                        DataEntity = dataEntity
                    });
                }

            }
        }

        /// <summary>
        /// 检查数据是否来自列表(如果是列表的话，焕新类型的订单需要在详情页里去提交总部)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckDataFromListForm(UserContext userCtx, DynamicObject[] dataEntities)
        {
            if (this.IsList)
            {
                foreach (var dataEntity in dataEntities)
                {
                    var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                    if (frenewalflag)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"销售合同【{Convert.ToString(dataEntity["fbillno"])}】为焕新订单类型的销售合同，请在详情页里直营锁单！",
                            DataEntity = dataEntity
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 检查单据类型是否为标准销售合同，且有转单标记，要完成转单才能提交总部
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntities"></param>
        private void CheckBillTypeIsStandardTransfer(UserContext userCtx, DynamicObject[] dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                var billTypeDy = dataEntity["fbilltype_ref"] as DynamicObject;
                if (billTypeDy != null)
                {
                    var billTypeName = Convert.ToString(billTypeDy["fname"]);

                    var needTransferOrder = Convert.ToBoolean(Convert.ToInt32(dataEntity["fneedtransferorder"]));

                    if (billTypeName.EqualsIgnoreCase("标准销售合同") && needTransferOrder)
                    {
                        var productEntrys = dataEntity["fentry"] as DynamicObjectCollection;
                        //'1':'审批中','2':'审批通过','3':'审批驳回','4':'已结算'
                        var isHasNotTransferOrderStatus = productEntrys.Any(x => !Convert.ToString(x["ftransferorderstatus"]).Equals("2"));
                        if (isHasNotTransferOrderStatus)
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"销售合同【{Convert.ToString(dataEntity["fbillno"])}】需要完成转单才能直营锁单！",
                                DataEntity = dataEntity
                            });
                        }
                    }

                }
            }

        }

        /// <summary>
        /// 获取当前经销商相关字段信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private Dictionary<string, bool> GetAgentInfo(UserContext userCtx, List<string> fmainorgids)
        {
            var dic = new Dictionary<string, bool>();
            if (fmainorgids.Count == 0) return dic;

            var agentInfo = userCtx.LoadBizBillHeadDataById("bas_agent", fmainorgids, "fid,fmainorgid,fname,fnumber,actualownernumber,fmanagemodel").ToList();

            foreach (var item in agentInfo)
            {
                if (!dic.ContainsKey(Convert.ToString(item["fid"])))
                {
                    var fmanagemodel = Convert.ToString(item["fmanagemodel"]) == "1" ? true : false;
                    dic.Add(Convert.ToString(item["fid"]), fmanagemodel);
                }
            }

            return dic;
        }

        /// <summary>
        /// 获取焕新订单类型
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private Dictionary<string, int> GetRenewType(UserContext userCtx, List<string> frenewtypes)
        {
            var dic = new Dictionary<string, int>();
            if (frenewtypes.Count == 0) return dic;

            var renewtypeObjs = userCtx.LoadBizBillHeadDataById("ydj_renewtype", frenewtypes, "fid,fisincome").ToList();

            foreach (var item in renewtypeObjs)
            {
                if (!dic.ContainsKey(Convert.ToString(item["fid"])))
                {
                    dic.Add(Convert.ToString(item["fid"]), Convert.ToInt32(item["fisincome"]));
                }
            }

            return dic;
        }
        private void CheckZYOrder(IEnumerable<DynamicObject> dataEntities)
        {
            if (this.Context.IsDirectSale)
            {
                foreach (var dataEntity in dataEntities)
                {
                    var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                    // 判断所有商品行是否都为赠品（fisgiveaway=1）
                    bool allDirectGiveaway = entrys.All(x => Convert.ToBoolean(Convert.ToInt32(x["fisgiveaway"])));
                    if (allDirectGiveaway)
                    {
                        var relatedOrderNo = Convert.ToString(dataEntity["frelevanceorderno"]);
                        if (string.IsNullOrWhiteSpace(relatedOrderNo))
                        {
                            this.Result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = "直营销售订单商品行全部为赠品时，关联合同号必填",
                                DataEntity = dataEntity
                            });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查销售合同关联的渠道是否合规(直营)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderDys"></param>
        private void CheckOrderChannel(UserContext userCtx, DynamicObject[] orderDys)
        {
            if (userCtx.IsDirectSale())
            {
                //有关联合作渠道的销售合同数据包
                var hasChannelDys = orderDys.Where(x => !Convert.ToString(x["fchannel"]).IsNullOrEmptyOrWhiteSpace());

                if (hasChannelDys != null && hasChannelDys.Any())
                {
                    var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();

                    refMgr.Load(userCtx, hasChannelDys.ToArray(), false, this.htmlForm, new List<string>() { "fchannel" });
                    foreach (var hasChannelDy in hasChannelDys)
                    {
                        //合作渠道数据包
                        var channelDy = hasChannelDy["fchannel_ref"] as DynamicObject;
                        if (channelDy != null)
                        {
                            //这里交易流水号是传给慕思中台，当作外部供应商id,这里需要判断是否为空，不为空就行
                            var tranid = Convert.ToString(channelDy["ftranid"]);
                            var billNo = Convert.ToString(hasChannelDy["fbillno"]);
                            var channelName = Convert.ToString(channelDy["fname"]);
                            if (tranid.IsNullOrEmptyOrWhiteSpace())
                            {
                                this.Result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $"销售合同【{billNo}】关联的合作渠道【{channelName}】的信息待完善，请前往《合作渠道》档案补充完整。",
                                    DataEntity = hasChannelDy
                                });
                                continue;
                            }

                            var channelEntrys = channelDy["fentry"] as DynamicObjectCollection;
                            if (channelEntrys != null && channelEntrys.Any())
                            {
                                //flowerlimit 金额下限 fratiofupperlimit 提成比例 fupperlimit 金额上限
                                var isRowAny = channelEntrys.Any(x => Convert.ToDecimal(x["flowerlimit"]) > 0 || Convert.ToDecimal(x["fratio"]) > 0 || Convert.ToDecimal(x["fupperlimit"]) > 0);
                                if (!isRowAny)
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"销售合同【{billNo}】关联的合作渠道【{channelName}】的返佣比例待完善，请前往《合作渠道》档案补充完整。",
                                        DataEntity = hasChannelDy
                                    });
                                    continue;
                                }
                            }
                            else
                            {
                                this.Result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = $"销售合同【{billNo}】关联的合作渠道【{channelName}】的返佣比例待完善，请前往《合作渠道》档案补充完整。",
                                    DataEntity = hasChannelDy
                                });
                                continue;
                            }
                        }
                    }
                }

            }


        }

        /// <summary>
        /// 检查商品明细中的成交单价是否可以为0
        /// </summary>
        /// <param name="isPriceCanZeroParam"></param>
        /// <param name="userCtx"></param>
        /// <param name="orderDys"></param>
        private void CheckProductEntryGiveWayPriceCanZero(bool isPriceCanZeroParam, UserContext userCtx, DynamicObject[] orderDys)
        {
            //如果经销商下的成交单价不允许为0，那么就要校验成交单价不呢个为0
            if (isPriceCanZeroParam)
            {
                var orderProductEntrys = orderDys.SelectMany(x => x["fentry"] as DynamicObjectCollection).ToList();
                var productIdSet = new HashSet<string>();
                if (orderProductEntrys != null && orderProductEntrys.Any())
                {
                    orderProductEntrys.ForEach(x => productIdSet.Add(Convert.ToString(x["fproductid"])));
                }

                var productDys = new List<DynamicObject>();
                if (productIdSet != null && productIdSet.Any())
                {
                    productDys = this.Context.LoadBizBillHeadDataById("ydj_product", productIdSet.ToList(), "fnumber,fname,fsuiteflag").ToList();
                }
                foreach (var orderDy in orderDys)
                {
                    var productEntrys = orderDy["fentry"] as DynamicObjectCollection;
                    if (productEntrys != null && productEntrys.Any())
                    {
                        // 找出成交单价为0的明细行
                        var findDealPriceProductEntry = productEntrys.Where(x => Convert.ToDecimal(x["fdealprice"]) == 0M).ToList();
                        if (findDealPriceProductEntry != null && findDealPriceProductEntry.Any())
                        {
                            // 过滤掉套件头的商品（fsuiteflag=1的商品）
                            var nonSuiteHeaderEntries = new List<DynamicObject>();
                            foreach (var entry in findDealPriceProductEntry)
                            {
                                string productId = Convert.ToString(entry["fproductid"]);
                                var product = productDys.FirstOrDefault(p => Convert.ToString(p["id"]) == productId);

                                // 只有找到商品信息且不是套件头商品时，才添加到需要校验的列表中
                                if (product != null && !Convert.ToBoolean(Convert.ToInt32(product["fsuiteflag"])))
                                {
                                    nonSuiteHeaderEntries.Add(entry);
                                }
                            }

                            // 只对非套件头的商品进行校验
                            if (nonSuiteHeaderEntries.Any())
                            {
                                var fseqList = nonSuiteHeaderEntries.Select(x => Convert.ToString(x["fseq"])).ToList();
                                string errorMsg = $"销售合同【{Convert.ToString(orderDy["fbillno"])}】的商品明细第{string.Join("、", fseqList.Select(x => $"【{x}】"))}行的成交单价不允许为0，请检查！";
                                this.Result.Errors.Add(new ValidationResultEntry()
                                {
                                    ErrorMessage = errorMsg,
                                    DataEntity = orderDy
                                });
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="formInfo"></param>
        /// <param name="result"></param>
        /// <param name="noOrders"></param>
        private void CheckPackQty(UserContext userCtx, DynamicObject dataEntity, HtmlForm formInfo, ValidationResult result)
        {
            if (Convert.ToBoolean(dataEntity["fpiecesendtag"]))
            {
                var entry = dataEntity["fentry"] as DynamicObjectCollection;
                if (entry != null)
                {
                    foreach (var item in entry)
                    {
                        var bizqty = Convert.ToDecimal(item["fbizqty"]);
                        var fseq = Convert.ToDecimal(item["fseq"]);
                        var fdeliverytype = Convert.ToString(item["fdeliverytype"]);
                        if (fdeliverytype.Equals("delivery_type_01"))
                        {
                            var matNumber = Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fnumber"]);
                            var packqty = Convert.ToDecimal((item["fproductid_ref"] as DynamicObject)?["fpackqty"]);
                            if (packqty > 1)
                            {
                                if (bizqty % packqty > 0)
                                {
                                    result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $@"{formInfo.Caption}{dataEntity["fbillno"]},第[{fseq}]行商品[{matNumber}]属于箱类商品，交货方式为“总部直发”，但是销售数量不满足整箱倍数，不允许一件代发操作，请核查！",
                                        DataEntity = dataEntity,
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查直营销售合同是否是门店上样以及门店上样的是否能提交总部
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="directOrderDys"></param>
        public void CheckDirectOrderBillTypeIsStoreSampleAndCanSubmitHq(UserContext ctx, DynamicObject[] directOrderDys,ValidationResult result)
        {
            if (ctx.IsDirectSale)
            {
				var billTypeIdSet = new HashSet<string>();
				var storeSampleDys = new List<DynamicObject>();
				var notHasBillTypeRefOrderDys = new List<DynamicObject>();
				foreach (var directOrderDy in directOrderDys)
				{
					var billTypeId = Convert.ToString( directOrderDy["fbilltype"]);
					var billTypeDy = directOrderDy["fbilltype_ref"] as DynamicObject;
					if (billTypeDy == null)
					{
						billTypeIdSet.Add(billTypeId);
						notHasBillTypeRefOrderDys.Add(directOrderDy);
					}
					else
					{
						var billTypeName = Convert.ToString(billTypeDy["fname"]);
						if (billTypeName.EqualsIgnoreCase("门店上样"))
						{
							storeSampleDys.Add(directOrderDy);
						}
					}
				}

				if (billTypeIdSet != null && billTypeIdSet.Any())
				{
					var billTypeDys = ctx.LoadBizBillHeadDataById("bd_billtype",billTypeIdSet.ToList(),"fname");
					if (billTypeDys != null && billTypeDys.Any())
					{
						foreach (var tempDy in notHasBillTypeRefOrderDys)
						{
							var notHasBillTypeRefId = Convert.ToString(tempDy["fbilltype"]);
							var findBillTypeDy = billTypeDys.FirstOrDefault(x=>Convert.ToString(x["id"]).EqualsIgnoreCase(notHasBillTypeRefId));
							if (findBillTypeDy != null)
							{
								var findBillTypeName = Convert.ToString(findBillTypeDy["fname"]);
								if (findBillTypeName.EqualsIgnoreCase("门店上样"))
								{
									storeSampleDys.Add(tempDy);
								}
							}
						}
					}
				}

				if (storeSampleDys != null && storeSampleDys.Any())
				{
					var currentAgentDy = ctx.LoadBizBillHeadDataById("bas_agent",ctx.Company,"fnumber,ftoppiecesendtag");

					if (currentAgentDy != null)
					{
						var isPieceSendTag = Convert.ToBoolean(Convert.ToInt32(currentAgentDy["ftoppiecesendtag"]));
						// (2)非一件代发的经销商不允许点击，判断条件如下：
						//     ①当【创建该单据的经销商.经营类型=直营】，并且【经销商档案.一件代发标记≠勾选】，并且【单据类型=门店上样】，该按钮置灰。
						if (!isPieceSendTag)
						{
							foreach (var storeSampleDy in storeSampleDys)
							{
								result.Errors.Add(new ValidationResultEntry()
								{
									ErrorMessage = $"销售合同【{Convert.ToString(storeSampleDy["fbillno"])}】为门店上样的销售合同，当【经销商档案.一件代发标记≠勾选】时，不允许直营锁单",
									DataEntity = storeSampleDy
								});
							}
						}
                        else
                        {
                            // (1)一件代发的经销商允许点击，判断条件如下：
                            //     ①当【创建该单据的经销商.经营类型=直营】，并且【经销商档案.一件代发标记=勾选】，并且【单据类型=门店上样】，并且【协同总部状态=空值】时，允许操作该按钮。（小程序涉及改造，显示按钮）
                            //     ②其他情况下，点击后，系统提示：“当前单据已存在下游采购订单，不允许该操作”
                            var findChstatusNotEmptyDys = storeSampleDys.Where(x=>!Convert.ToString(x["fchstatus"]).IsNullOrEmptyOrWhiteSpace()).ToList();
                            if (findChstatusNotEmptyDys != null && findChstatusNotEmptyDys.Any())
                            {
                                var orderNums = findChstatusNotEmptyDys.Select(x => Convert.ToString(x["fbillno"])).ToList();
                                var sqlStr = $@" select typ.fid as 'billhead_id',
                                                    typ.fbillno as 'purchaseorderno',
                                                    typoe.fsourcebillno as 'sourceorderno'
                                               from t_ydj_purchaseorder typ with(nolock )
                                                inner join t_ydj_poorderentry typoe with(nolock ) on typ.fid = typoe.fid
                                                    where typ.fmainorgid = '{ctx.Company}' 
                                                        and typ.fcancelstatus = '0'
                                                        and typoe.fsourceformid = 'ydj_order'
                                                        and typoe.fsourcebillno in ({string.Join(",", orderNums.Select(x => $"'{x}'"))})
                                                ";
                                var tempPurChaseOrderIdDys = DBService.ExecuteDynamicObject(ctx, sqlStr);
                                if (tempPurChaseOrderIdDys != null && tempPurChaseOrderIdDys.Any())
                                {
                                    foreach (var findChstatusNotEmptyDy in findChstatusNotEmptyDys)
                                    {
                                        var findChstatusNotEmptyDyBillNo = Convert.ToString(findChstatusNotEmptyDy["fbillno"]);
                                        var findPurChaseOrderDy = tempPurChaseOrderIdDys.FirstOrDefault(x=>Convert.ToString(x["sourceorderno"]).Equals(findChstatusNotEmptyDyBillNo));
                                        if (findPurChaseOrderDy != null)
                                        {
                                            result.Errors.Add(new ValidationResultEntry()
                                            {
                                                ErrorMessage = $"销售合同【{findChstatusNotEmptyDy["fbillno"]}】已存在下游采购订单【{findPurChaseOrderDy["purchaseorderno"]}】，不允许直营锁单",
                                                DataEntity = findChstatusNotEmptyDy
                                            });
                                        }
                                        
                                    }
                                }
                            }
                            
                        }
                    }
					
				}
            }
            
        }

    }
}
