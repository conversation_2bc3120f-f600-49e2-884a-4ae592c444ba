using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 采购订单：标准品根据选配配件映射商品带出配件商品
    /// </summary>
    [InjectService]
    [FormId("ydj_order|ydj_purchaseorder")]
    [OperationNo("TgcPartByStandard")]
    public class TgcPartByStandard : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string fproductid = this.GetQueryOrSimpleParam("fproductid", "");
            string sql = $@"SELECT t_sel_fittingsmapentry.fid,t_sel_fittingsmapentry.fmaterialid,t_sel_fittingsmapentry.fpropid AS 'fattrinfo',fqty FROM t_sel_fittingsmap 
                            INNER JOIN t_sel_fittingsmapentry ON t_sel_fittingsmapentry.fid = t_sel_fittingsmap.fid
                            INNER JOIN SER_YDJ_CATEGORY ON SER_YDJ_CATEGORY.fid =t_sel_fittingsmap.fcategoryid
                            WHERE SER_YDJ_CATEGORY.fname ='铁架床' AND fmatchbyproduct = '1' AND fmaterialidnew  = '{fproductid}' AND fdisable != 1 ";
            var partsMapingEntryList = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
            var partsList = new List<Dictionary<string, object>>();
            GetResultBrandData re = new GetResultBrandData();
            var HaveTGC = false;
            //如果查到已经被配置到选配配件映射中的配件商品，则不能单独下单
            foreach (var partsMapingEntrys in partsMapingEntryList) {
                if (partsMapingEntrys != null)
                {
                    var fittingsmapobj = this.Context.LoadBizDataById("sel_fittingsmap", Convert.ToString(partsMapingEntrys?["fid"]));
                    var categoryObj = this.Context.LoadBizDataById("ydj_category", Convert.ToString(fittingsmapobj["fcategoryid"]));
                    var productid = Convert.ToString(partsMapingEntrys?["fmaterialid"]);

                    ////铁架床逻辑：判断是否停产，如果停产则需要给前端提示
                    if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_purchaseorder") && Convert.ToString(categoryObj["fname"]).EqualsIgnoreCase("铁架床"))
                    {
                        //to do 判断商品是否停产
                        if (IsTC(productid))
                        {
                            HaveTGC = true;
                            continue;
                        }
                    }
                    //配件商品是否允许定制
                    var Iscustom = re.GetBaseDataNameById(this.Context, "ydj_product", productid, "fcustom");
                    var attrinfoobj = this.Context.LoadBizDataById("bd_auxpropvalueset", Convert.ToString(partsMapingEntrys?["fattrinfo"]));
                    var setEntrys = attrinfoobj?["fentity"] as DynamicObjectCollection;
                    //辅助属性组合值键值对
                    List<Dictionary<string, string>> auxPropKv = new List<Dictionary<string, string>>();
                    if (setEntrys != null && setEntrys.Any())
                    {
                        foreach (var item in setEntrys)
                        {
                            var auxPropKv1 = new Dictionary<string, string>();
                            var proplist = auxPropKv.Select(o => o["fauxpropid"]).ToList();
                            if (proplist.Contains(item["fauxpropid"])) continue;
                            var sel_propobj = this.Context.LoadBizDataById("sel_prop", Convert.ToString(Convert.ToString(item["fauxpropid"])));
                            auxPropKv1["fauxpropid"] = Convert.ToString(item["fauxpropid"]);
                            auxPropKv1["fname"] = Convert.ToString(sel_propobj["fname"]);
                            auxPropKv1["fnumber"] = Convert.ToString(sel_propobj["fnumber"]);
                            auxPropKv1["fvalueid"] = Convert.ToString(item["fvalueid"]);
                            auxPropKv1["fvaluenumber"] = Convert.ToString(item["fvaluenumber"]);
                            auxPropKv1["fvaluename"] = Convert.ToString(item["fvaluename"]);
                            auxPropKv.Add(auxPropKv1);
                        }
                    }
                    partsList.Add(new Dictionary<string, object>
                    {
                        { "fmaterialid", Convert.ToString(partsMapingEntrys?["fmaterialid"]) },
                        { "Iscustom",Convert.ToBoolean(Iscustom)},
                        { "parttype","铁架床" },
                        { "attrinfo", auxPropKv },
                        //方便小程序处理
                        { "attrinfoid",Convert.ToString(partsMapingEntrys?["fattrinfo"])},
                        { "id",this.GetQueryOrSimpleParam<string>("rowId")},
                        { "fqty", Convert.ToString(partsMapingEntrys?["fqty"]) }
                    });
                }
            }

            //如果选配配件映射有满足条件的铁架床配件，但是已停产此时需要抛给前端
            if (this.HtmlForm.Id.EqualsIgnoreCase("ydj_purchaseorder") && HaveTGC && !partsList.Any(o => Convert.ToString(o["parttype"]) == "铁架床"))
            {
                partsList.Add(new Dictionary<string, object>
                                    {
                                        { "fmaterialid", "" },
                                        { "Iscustom","" },
                                        { "parttype","铁架床" },
                                        { "attrinfo", "" },
                                        { "id",this.GetQueryOrSimpleParam<string>("rowId")},
                                        { "fqty", "0" },
                                        { "Isunstdtype" ,"0"}
                                    });
            }
            this.Result.SrvData = partsList;
            this.Result.IsSuccess = true;
            //this.Result.IsSuccess = this.DBService.ExecuteDynamicObject(this.Context, sql).Count == 0;
        }

        /// <summary>
        /// 判断商品是否停产
        /// </summary>
        /// <param name="product"></param>
        /// <returns></returns>
        private bool IsTC(string productId)
        {
            //停产定义:
            //1)  首先根据 商品 对应的【系列】, 找到《销售组织与业绩品牌关系》单据体 - 销售组织与业绩品牌关系 的【系列】
            //2)  如果在《销售组织与业绩品牌关系》里匹配不到【系列】时, 就认为没有停产, 因为有可能就是通配品牌或慕思助眠
            //3)  如果匹配到【系列】时, 获取《销售组织与业绩品牌关系》单据体 - 销售组织与业绩品牌关系, 对应行的【销售组织】, 将获取到的【销售组织】匹配《商品》基础资料里 单据体-产品销售组织【销售组织】
            //4) 如果匹配上的【销售组织】对应行的【禁用状态】=”已启用”时, 则不为停产
            //5) 如果匹配上的【销售组织】对应行的【禁用状态】=”已禁用”时, 则认为这商品在总部已经停产了
            //6) 如果匹配不到【销售组织】, 也要走停购的逻辑判断(已有停购的逻辑), 因为我认为这商品在总部已经停产了
            bool IsTc = false;
            string sql = $@" select 1 FROM t_bd_material t1
                              INNER JOIN t_ydj_orgresultbrandentry t3 on t1.fseriesid = t3.fseriesid
                              where t1.fid in('{productId}') ";
            //如果销售组织与业绩品牌关系匹配不到 则直接返回未停产
            if (this.DBService.ExecuteDynamicObject(this.Context, sql).Count == 0) return false;

            sql = $@"select distinct t2.fsaleorgid,t2.fdisablestatus FROM t_bd_material t1
                            join t_bd_materialsaleorg t2 on t1.fid=t2.fid
                            join t_ydj_orgresultbrandentry t3 on t2.fsaleorgid = t3.forganizationid
                            join t_ydj_series t4 on t3.fseriesid = t4.fid
                            join t_bas_organization t5 on t3.forganizationid = t5.fid
                            where t1.fid in('{productId}')";
            var saleorg = this.DBService.ExecuteDynamicObject(this.Context, sql);
            if (saleorg.Count == 0)
            {
                //如果没有匹配的销售组织则为停产
                IsTc = true;
            }
            else
            {
                //fdisablestatus 1：已启用，2：已禁用 存在一个已启用的即不为停产
                if (saleorg.Any(o => Convert.ToString(o["fdisablestatus"]).EqualsIgnoreCase("1")))
                {
                    IsTc = false;
                }
                else
                {
                    IsTc = true;
                }
            }
            return IsTc;
        }
    }




}
