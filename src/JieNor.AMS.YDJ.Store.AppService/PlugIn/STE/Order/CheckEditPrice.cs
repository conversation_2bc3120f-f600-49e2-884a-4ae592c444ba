using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：零售价可编辑
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("CheckEditPrice")]
    public class CheckEditPrice : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            this.Result.IsSuccess = false;
            this.Result.SrvData = false;
            /* 【销售合同.数据状态】 =“创建”或“重新审核”。
             * 系统实现定义一变量【零售价可编辑】字段，布尔类型，需满足以下任一条件则为”是”：
             * 【商品.创建组织】=“慕思总部组织” 且【商品.商品类别.零售价可编辑】=“是”。
             * 【商品.创建组织】=【销售合同.当前组织】且销售管理参数【自建商品零售价可编辑】=“是”
             * 销售管理参数【二级分销合同零售价可编辑】=“是” 且【销售合同.二级分销合同】=“是”。
             * .当【单据类型】=“v6定制柜合同”，且订单为经销商自建时(创建人非系统管理员)，【零售价】需放开编辑。
             */
            string productId = this.GetQueryOrSimpleParam("productId", "");
            string status = this.GetQueryOrSimpleParam("status", "");//合同状态
            var isresellorder = this.GetQueryOrSimpleParam("isresellorder", false);//二级分销合同
            var fcreatorid = this.GetQueryOrSimpleParam("fcreatorid", "");//创建人
            var fbilltypename = this.GetQueryOrSimpleParam("fbilltypename", "");//合同类型
            if (status != "D" && status != "E")
            { 
                //检查当前经销商是否已被禁用
                var agentforbid = this.DBService.ExecuteDynamicObject(
                    this.Context,
                    "select top 1 fforbidstatus from t_bas_agent where fid=@fmainorgid ",
                    new SqlParam[]
                    {
                    new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
                    })?.FirstOrDefault();
                //获取经销商禁用状态，如果经销商禁用取价取不到，则要放开编辑
                if (agentforbid != null && Convert.ToString(agentforbid["fforbidstatus"]) == "1")
                {
                    this.Result.IsSuccess = true;
                    this.Result.SrvData = new { AgentForbid = true };
                    return;
                } 

                var profileService = this.Context.Container.GetService<ISystemProfile>();
                var isorderusable = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fisorderusable", false);//参数【二级分销合同零售价可编辑】
                if (isresellorder && isorderusable)
                {
                    this.Result.IsSuccess = true;
                    this.Result.SrvData = true;
                }
                else if (fcreatorid != "sysadmin" && fbilltypename == "v6定制柜合同")
                {
                    this.Result.IsSuccess = true;
                    this.Result.SrvData = true;
                }
                else if (!productId.IsNullOrEmptyOrWhiteSpace())
                {
                    var sql = $@"select t1.fid,t1.fnumber,t1.fmainorgid,t2.feditprice from t_bd_material t1 with(nolock)
                        left join ser_ydj_category t2 with(nolock) on t1.fcategoryid=t2.fid
                        where t1.fid='{productId}'";
                    var result = this.DBService.ExecuteDynamicObject(this.Context, sql)?.FirstOrDefault();
                    if (result != null)
                    {
                        var fmainorgid = Convert.ToString(result["fmainorgid"]);//商品数据来源
                        //判断商品是自建商品还是总部商品
                        if (fmainorgid == this.Context.Company)
                        {
                            //是自建商品，则获取经销商参数【自建商品零售价可编辑】判断
                            var owneditprice = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fowneditprice", false);//参数【自建商品零售价可编辑】
                            if (owneditprice)
                            {
                                this.Result.IsSuccess = true;
                                this.Result.SrvData = true;
                            }
                        }
                        else
                        {
                            //是总部商品，则判断商品.商品类别.零售价可编辑=“是”
                            var feditprice = Convert.ToString(result["feditprice"]);
                            if (feditprice == "1")
                            {
                                this.Result.IsSuccess = true;
                                this.Result.SrvData = true;
                            }
                        }
                    }
                }
            }
        }
    }
}
