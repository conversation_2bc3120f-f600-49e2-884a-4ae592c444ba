using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：查询合同单据类型
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("queryorderbilltype")]
    public class QueryOrderBillType: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            bool isRefresh = false;//是否需要刷新
            string fbilltype = this.GetQueryOrSimpleParam<string>("fbilltype");
            string existsql = @" select fprimitiveid,fid from T_BD_BILLTYPE where fmainorgid='{0}' and fprimitiveid = '{1}'".Fmt(this.Context.BizOrgId, fbilltype);
            var datas = this.DBService.ExecuteDynamicObject(this.Context, existsql).ToList();
            if (datas.Any())
            {
                isRefresh = true;
            }
            var billTypeObj = this.Context.LoadBizDataByFilter("bd_billtype", $"fbizobject = 'ydj_order' and fmainorgid = '0'");
            var Ids = billTypeObj.Select(o=>o["Id"]?.ToString()).Distinct().ToList();
            List<BillType> billtypes = new List<BillType>();
            string sql = @" select fprimitiveid,fid,fname from T_BD_BILLTYPE where fmainorgid='{0}' and fprimitiveid in ('{1}')".Fmt(this.Context.BizOrgId, string.Join("','", Ids));
            var BillTypeObjs = this.DBService.ExecuteDynamicObject(this.Context, sql);
            foreach (var item in billTypeObj)
            {
                var BillTypeObj = BillTypeObjs.FirstOrDefault(o => o["fprimitiveid"]?.ToString() == item["Id"]?.ToString());
                if (BillTypeObj!=null)
                {
                    var billtype = new BillType
                    {
                        id= BillTypeObj["fid"]?.ToString(),
                        name = BillTypeObj["fname"]?.ToString()
                    };
                    billtypes.Add(billtype);
                }
                else
                {
                    var billtype = new BillType
                    {
                        id = item["Id"]?.ToString(),
                        name = item["fname"]?.ToString()
                    };
                    billtypes.Add(billtype);
                }  
            }
            this.Result.SrvData = new
            {
                billtypes = billtypes,
                isRefresh = isRefresh
            };
            this.Result.IsSuccess = true;
            return;
        }
    }

    public class BillType
    {
        public string id { get; set; }

        public string name { get; set; }
    }
}
