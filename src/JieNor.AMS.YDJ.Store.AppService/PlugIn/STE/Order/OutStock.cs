using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("push2outstock")]
    public class OutStock : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理规则校验
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            //选中商品行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            /*
              1. 把以下销售合同操作<出库>的条件检查代码（现在是一次性提示所有的可能性）调整为：检查的代码按循环模式执行，检查到一组条件不满足出库，立马报错，并只提示不满足出库的当前条件检验的报错（等处理当前报错后，继续操作，发现还有不满足条件就提示其他的报错，就这样进入循环模式）。
                  a. 销售合同必须是已审核状态！
                  b. 至少要有一行商品明细行没有完全出库（数量-出库数量+退货数量>0）！
                  c. 至少要有一行商品明细行不是出现货且不是自提！
                  d. 至少要有一行商品明细行的关闭状态是正常或部分关闭状态！
             */
            string errorMsg = string.Empty;

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //当焕新订单标记=是，【结算进度】!=已收款，不允许点<出库>按钮。
                var frenewalflag = Convert.ToBoolean(newData["frenewalflag"]);
                var fsettlprogress = Convert.ToString(newData["fsettlprogress"]);
                if (frenewalflag && fsettlprogress != Enu_RenewalSettleProgress.已收款)
                {
                    throw new BusinessException("焕新订单结算进度不是[已收款]，不允许出库");
                }
                return true;
            }).WithMessage("焕新订单结算进度不是[已收款]，不允许出库！", (billObj, propObj) => billObj["fbillno"]));

            // 销售退换货允许变更合同
            var profileService = this.Container.GetService<ISystemProfile>();
            var returnmodifyorder = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "freturnmodifyorder", false);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMsg = string.Empty;

                if (newData["fstatus"].ToString() != "E")
                {
                    errorMsg += "销售合同必须是已审核状态";
                    throw new BusinessException("销售合同必须是已审核状态");
                    //return false;
                }
                var entrys = newData["fentry"] as DynamicObjectCollection;
                //if (entrys.Any(t => Convert.ToString(t["fdeliverymode"]) != "1" && !Convert.ToBoolean(t["fisoutspot"])))
                //{
                //    errorMsg += "至少要有一行商品明细行不是出现货且不是自提";
                //    throw new BusinessException("至少要有一行商品明细行不是出现货且不是自提");
                //    //return false;
                //}
                if (!entrys.Any(t => Convert.ToString(t["fclosestatus_e"]) == "0" || Convert.ToString(t["fclosestatus_e"]) == "2" || t["fclosestatus_e"].IsNullOrEmptyOrWhiteSpace()))
                {
                    errorMsg += "至少要有一行商品明细行的关闭状态是正常或部分关闭状态";
                    throw new BusinessException("至少要有一行商品明细行的关闭状态是正常或部分关闭状态");
                    //return false;
                }
                //优化按选中明细行来判断
                var err = "";
                foreach (var entry in entrys)
                {
                    var id = Convert.ToString(entry["id"]);
                    if (!selectRowIds.Select(x => x.Id).ToList().Contains(id)) continue;
                    // "fqty+freturnqty>foutqty and fstatus='E' and (fisoutspot!='1' or fisoutspot='1' and fdeliverymode!='1') and (fclosestatus_e='0' or fclosestatus_e='2') ",
                    //if (Convert.ToDouble(entry["fqty"]) + Convert.ToDouble(entry["freturnqty"]) < Convert.ToDouble(entry["foutqty"]))
                    var flag = Convert.ToDouble(entry["fqty"]) + Convert.ToDouble(entry["freturnqty"]) < Convert.ToDouble(entry["foutqty"]);
                    if (returnmodifyorder)
                    {
                        //如果开启参数，则将【销售退货中数量】纳入公式
                        flag = Convert.ToDouble(entry["fqty"]) + Convert.ToDouble(entry["freturnqty"]) + Convert.ToDouble(entry["fbizreturningqty"]) < Convert.ToDouble(entry["foutqty"]);
                    }
                    if (flag)
                    {
                        err += $"销售合同明细行{entry["fseq"]}商品已完全出库. <br>";
                        //return false;
                    }
                }
                if (err != "")
                {
                    throw new BusinessException(err);
                }

                return true;

            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(new Validation_InventoryTransfer(selectRowIds.Select(s => s.Id).ToList(), true));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fpiecesendtag = Convert.ToBoolean(newData["fpiecesendtag"]);
                if (!fpiecesendtag) return true;
                var entrys = newData["fentry"] as DynamicObjectCollection;

                //优化按选中明细行来判断
                var err = "";
                foreach (var entry in entrys)
                {
                    var id = Convert.ToString(entry["id"]);
                    if (!selectRowIds.Select(x => x.Id).ToList().Contains(id)) continue;
                    // "fqty+freturnqty>foutqty and fstatus='E' and (fisoutspot!='1' or fisoutspot='1' and fdeliverymode!='1') and (fclosestatus_e='0' or fclosestatus_e='2') ",
                    //if (Convert.ToDouble(entry["fqty"]) + Convert.ToDouble(entry["freturnqty"]) < Convert.ToDouble(entry["foutqty"]))
                    var flag = Convert.ToString(entry["fdeliverytype"])== "delivery_type_01";
                    if (flag)
                    {
                        err += $"销售合同明细行{entry["fseq"]}交货方式为总部直发的商品行，不允许手工下推出库，请选择非“总部直发”的商品出库，谢谢！<br>";
                        //return false;
                    }
                }
                if (err != "")
                {
                    throw new BusinessException(err);
                }

                return true;
            }).WithMessage("一件代发合同，交货方式为总部直发的商品行，不允许手工下推出库，请选择非“总部直发”的商品出库，谢谢！", (billObj, propObj) => billObj["fbillno"]));
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //调用《库存综合查询报表》查询方法，更新扩展数据
            var filterValueMap = new List<Dictionary<string, string>>();
            var allEntrys = e.DataEntitys.SelectMany(t => t["fentry"] as DynamicObjectCollection).ToList();
            foreach (var ent in allEntrys)
            {
                var item = new Dictionary<string, string>();

                item["fmaterialid"] = Convert.ToString(ent["fproductid"]);
                item["fattrinfo"] = Convert.ToString(ent["fattrinfo"]);
                item["fcustomdesc"] = Convert.ToString(ent["fcustomdes_e"]);

                filterValueMap.Add(item);
            }

            var selectFlds = new List<string>();
            selectFlds.Add("fmaterialid");
            selectFlds.Add("fstorehouseid");
            selectFlds.Add("fstorelocationid");
            selectFlds.Add("fstockstatus");
            selectFlds.Add("fqty");
            selectFlds.Add("freserveqty");
            selectFlds.Add("fusableqty");
            selectFlds.Add("fintransitqty");

            CommonListDTO listDto = new CommonListDTO()
                .SetFormId("rpt_stocksynthesize")
                .SetOperationNo("querydata")
                .SetSelectFields(selectFlds.ToArray())
                .SetSimpleData("FilterValueMap", filterValueMap.ToJson())
                .SetPageIndex(1)
                .SetPageCount(1000);

            var resp = this.Gateway.InvokeLocal<CommonListDTOResponse>(this.Context, listDto);
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var order = e.DataEntitys[0];
            //校验销售合同允许出库
            var freceivable = order["freceivable"];
            var fsumamount = order["fsumamount"];
            //销售合同允许出库的最低金额比例  备注：改为在提交按钮判断-2022/04/15
            //var profileService = this.Context.Container.GetService<ISystemProfile>();
            //var systemParameter = profileService.GetSystemParameter(this.Context, "bas_storesysparam");
            //var fproportionoratioamount = Convert.ToInt32(systemParameter["fproportionoratioamount"]);

            var orderCommon = new OrderCommon(this.Context);
            if (!orderCommon.IsSaleTransferOrder(order))
            {
                //判断是否允许出库  备注：改为在提交按钮判断-2022/04/15
                //var newfsumamount = Convert.ToDouble(fsumamount) * fproportionoratioamount / 100;
                //if (Convert.ToDouble(freceivable) < newfsumamount)
                //    throw new BusinessException("当前订单确认已收金额不足" + newfsumamount + "元,暂不允许出库!");
            }
            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            orderCommon.CheckTransferOrderApprovingAndHasShipperAgentByEntry(e.DataEntitys, selectRowIds.Select(x => x.Id).ToArray());
        }

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    var eventData = e.EventData as BeforeGetConvertRuleData;
                    this.PrepareConvertRuleData(e.DataEntities, eventData);
                    break;
            }
        }

        /// <summary>
        /// 准备下推操作参数
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="eventData"></param>
        private void PrepareConvertRuleData(DynamicObject[] dataEntities, BeforeGetConvertRuleData eventData)
        {
            if (dataEntities.IsNullOrEmpty())
            {
                return;
            }

            if (eventData == null
                || (!eventData.RuleId.EqualsIgnoreCase("ydj_order2stk_sostockout")
                    && !eventData.RuleId.EqualsIgnoreCase("ydj_order2sal_deliverynotice"))) return;


            eventData.RuleId = "ydj_order2stk_sostockout";
            //选中商品行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            string errorMsg = "";
            //过滤实收数量大于等于销售数量的明细行
            List<string> filterList = new List<string>();

            if (selectRowIds.Count > 0)
            {
                var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                refObjMgr?.Load(this.Context, dataEntities.First().DynamicObjectType, dataEntities, false);
                //将同组套件强行选中 需求 5698 不需要强制出整个套件
                //CheckSuitcombnumberGroup(dataEntities, selectRowIds);
                //过滤方法
                CheckFbizqty(filterList, dataEntities, out errorMsg);
            }
            if (dataEntities != null && dataEntities.Any())
            {
                var isEnableNotice = Convert.ToBoolean(dataEntities[0]["fenablenotice"]);
                if (isEnableNotice)
                {
                    eventData.RuleId = "ydj_order2sal_deliverynotice";
                }

                //获取主组织经销商
                var fentrys = dataEntities.SelectMany(t => t["fentry"] as DynamicObjectCollection);
                var agentIds = fentrys.Where(t => Convert.ToString(t["fshipperagentid"]).Trim().Length > 0).Select(t => Convert.ToString(t["fshipperagentid"]).Trim()).ToList();
                var mainAgents = (new AppService.Service.AgentService()).GetMainAgentIds(this.Context, agentIds);
                var orderCommon = new OrderCommon(this.Context);
                var hasShipperEntry = new OrderCommon(this.Context).HasShipperEntry(dataEntities[0]/*, selectRowIds.Select(x => x.Id)*/);
                var lstSelPoEntryRows = new List<SelectedRow>();

                foreach (var dataEntity in dataEntities)
                {
                    var poId = dataEntity["id"] as string;
                    var billNo = dataEntity["fbillno"] as string;
                    if (poId.IsEmptyPrimaryKey()) continue;

                    var entries = dataEntity["fentry"] as DynamicObjectCollection;
                    var allMaterial = entries.Select(t => t["fproductid_ref"] as DynamicObject).ToList();//全部商品
                    var suiteHeadIds = allMaterial.Where(t => Convert.ToString(t["fsuiteflag"]).ToLower() == "true" || Convert.ToString(t["fsuiteflag"]) == "1").Select(m => Convert.ToString(m["id"])).ToList();//套件头商品ID
                    var pushEntries = entries.Where(t => !suiteHeadIds.Contains(Convert.ToString(t["fproductid"])));//过滤掉套件头商品
                    if (Convert.ToBoolean(dataEntity["fpiecesendtag"]))
                    {
                        pushEntries = pushEntries.Where(a => Convert.ToString(a["fdeliverytype"]).Equals("delivery_type_02")).ToList();
                        //一件代发的情况下，总部直发由系统自动下推
                    }
                    var selectedRows = pushEntries.Where(t => selectRowIds.Any(s => !filterList.Contains(Convert.ToString(t["id"]))
                     && s.Id.Contains(Convert.ToString(t["id"])))).Select(x => new SelectedRow
                     {
                         PkValue = poId,
                         BillNo = billNo,
                         EntityKey = "fentry",
                         EntryPkValue = Convert.ToString(x["id"])
                     }).ToList();
                    eventData.SelectedRows = selectedRows;
                    if (hasShipperEntry)
                    {
                        foreach (var item in pushEntries.Where(t => selectRowIds.Select(s => s.Id).Contains(Convert.ToString(t["id"]))))
                        {
                            var agentId = Convert.ToString(item["fshipperagentid"]).Trim();
                            var mainAgentId = "";
                            mainAgents.TryGetValue(agentId, out mainAgentId);
                            mainAgentId = mainAgentId.IsNullOrEmptyOrWhiteSpace() ? agentId : mainAgentId;
                            if ((mainAgents.ContainsKey(item["fshipperagentid"].ToString()) ||
                                mainAgentId == this.Context.Company)
                                )
                            {
                                selectedRows.Add(
                                    new SelectedRow()
                                    {
                                        PkValue = poId,
                                        BillNo = billNo,
                                        EntityKey = "fentity",
                                        EntryPkValue = Convert.ToString(item["id"])
                                    });
                            }
                        }
                        lstSelPoEntryRows.AddRange(selectedRows);
                        eventData.SelectedRows = lstSelPoEntryRows;
                        eventData.RuleId = "ydj_order2stk_sostockout_shipper";
                    }
                }

                if (eventData.SelectedRows.Count() == 0
                    && !errorMsg.IsNullOrEmptyOrWhiteSpace()
                    && selectRowIds.Count != 0)
                {
                    throw new BusinessException(errorMsg);
                }
            }
        }

        /// <summary>
        ///需求39075， 验证同组套件组合号是否都选中，同组非选中时强行选中
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="rows"></param>
        private void CheckSuitcombnumberGroup(DynamicObject[] dataEntities, List<Row> rows)
        {
            //全选时不验证
            if (rows.Count == 0) return;
            //选中的ID
            var selids = rows.Select(x => x.Id).ToList();
            //需要验证的分组字段
            Dictionary<string, string> reportDic = new Dictionary<string, string>() {
                    { "fsuitcombnumber","fsuitcombnumber"}//套件组合号
                    };

            foreach (var item in dataEntities)
            {
                //记录已选中的分组
                Dictionary<string, List<string>> seldic = new Dictionary<string, List<string>>();
                var entries = item["fentry"] as DynamicObjectCollection;
                var selEnter = entries.Where(x => selids.Contains(x["id"])).ToList();
                //先将已选择的字段加入分组
                foreach (var valEnt in selEnter)
                {
                    //按分组字段来找对应ID
                    foreach (var rdic in reportDic)
                    {
                        var gval = Convert.ToString(valEnt[rdic.Key]).Trim();
                        if (seldic.ContainsKey(gval) || gval.IsNullOrEmptyOrWhiteSpace())
                            continue;

                        //把同组的套件找出来加入分组
                        var grData = entries.Where(x => Convert.ToString(x[rdic.Key]) == gval).ToList();
                        foreach (var drval in grData)
                        {
                            var key = Convert.ToString(drval[rdic.Key]).Trim();
                            string vid = Convert.ToString(drval["id"]);
                            AddGroup(key, vid, seldic);
                        }
                    }
                }

                //开始对字段分组进行验证
                foreach (var vField in seldic)
                {
                    //按组查找是否对应商品行都选中
                    foreach (var rdic in reportDic)
                    {
                        //查出选中的值
                        var selcount = entries.Where(x => Convert.ToString(x[rdic.Key]) == vField.Key && selids.Contains(x["id"])).Count();
                        //数量对不上，说明分组字段验证没有全部选上，将ID 强行加入选中缓存 List<Row>
                        if (selcount != vField.Value.Count)
                        {
                            var listids = vField.Value.Except(entries.Where(x => Convert.ToString(x[rdic.Key]) == vField.Key && selids.Contains(x["id"])).Select(x => Convert.ToString(x["id"])).ToList());

                            listids.ForEach(x => rows.Add(new Row() { Id = x }));
                        }
                    }

                    //校验仓库一致性 
                    var entdata = entries.Where(x => vField.Value.Contains(Convert.ToString(x["id"])) && !Convert.ToString(x["fstorehouseid"]).Trim().IsNullOrEmptyOrWhiteSpace()).ToList();
                    //先验证仓库和套件数量 是否相同
                    if (entdata.Count > 0 && entdata.Count != vField.Value.Count)
                    {
                        var defaultData = entries.Where(x => vField.Value.Contains(Convert.ToString(x["id"])) && Convert.ToString(x["fstorehouseid"]).Trim().IsNullOrEmptyOrWhiteSpace()).FirstOrDefault();
                        throw new BusinessException($@"第{Convert.ToString(defaultData["fseq"])}行的套件 {Convert.ToString(defaultData["fsuitcombnumber"])} 与 子件商品需配套出库, 请确保套件填入与子件商品相同的仓库后再进行出库 !");
                    }

                    Dictionary<string, string> dicstore = new Dictionary<string, string>();
                    //记录仓库行数
                    foreach (var val in entdata)
                    {
                        var fstorehouseid = Convert.ToString(val["fstorehouseid"]).Trim();
                        if (fstorehouseid.IsNullOrEmptyOrWhiteSpace())
                            continue;
                        //校验同一批套件的仓库是否一致
                        if (dicstore.Count == 0 || dicstore.ContainsKey(fstorehouseid))
                        {
                            dicstore[fstorehouseid] = fstorehouseid;
                        }
                        else
                        {
                            throw new BusinessException($@"第{Convert.ToString(val["fseq"])}行的套件 {Convert.ToString(val["fsuitcombnumber"])} 与 子件商品需配套出库, 请确保套件填入与子件商品相同的仓库后再进行出库 !");
                        }
                    }

                }
            }
        }

        /// <summary>
        /// 记录KEY组中的ID数
        /// </summary>
        /// <param name="key"></param>
        /// <param name="val"></param>
        /// <param name=""></param>
        private void AddGroup(string key, string val, Dictionary<string, List<string>> dic)
        {
            if (dic.ContainsKey(key))
            {
                List<string> list = dic[key];
                list.Add(val);
                dic[key] = list;
            }
            else
            {
                List<string> list = new List<string>();
                list.Add(val);
                dic[key] = list;
            }
        }

        /// <summary>
        /// 校验销售数量是否大于出库明细的【实收数量】汇总
        /// </summary>
        /// <param name="dic"></param>
        /// <param name="entityids"></param>
        private Dictionary<string, QtpInstock> CheckFbizqty(List<string> filterlist, DynamicObject[] dataEntities, out string errorMsg)
        {
            errorMsg = "";
            var ids = new List<string>();
            var fsourceentryid = "id";
            //存储 出库明细的【实收数量】汇总
            Dictionary<string, QtpInstock> dic = new Dictionary<string, QtpInstock>();

            foreach (var val in dataEntities)
            {
                var entries = val["fentry"] as DynamicObjectCollection;
                ids.AddRange(entries.Where(x => !x[fsourceentryid].IsNullOrEmptyOrWhiteSpace()).Select(x => x[fsourceentryid].ToString()).ToList());
            }

            if (!ids.Any())
            {
                return dic;
            }

            string strSql = $@"
select t2.fproductid as fmaterialid, t2.fseq, t2.fbizqty, t2.fbizreturnqty, t1.sumfbizqty, t1.fsourceentryid
from
(

	SELECT t1.fsourceentryid, SUM(t1.fbizqty) as sumfbizqty
	FROM t_stk_sostockoutentry t1 with(nolock)
	INNER JOIN t_stk_sostockout t3 with(nolock) ON t1.fid = t3.fid 
	WHERE (t3.fcancelstatus = 0 AND t1.fsourceentryid IN ({ids.JoinEx(",", true)})) 
	GROUP BY t1.fsourceentryid
) t1
INNER JOIN t_ydj_orderentry t2 with(nolock) ON t1.fsourceentryid = t2.fentryid ";

            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam> { }))
            {
                while (dr.Read())
                {
                    dic[dr["fsourceentryid"].ToString()] = new QtpInstock()
                    {
                        fseq = Convert.ToInt32(dr["fseq"]),
                        fbizqty = Convert.ToInt32(dr["fbizqty"]),
                        fbizreturnqty = Convert.ToInt32(dr["fbizreturnqty"]),
                        sumfbizqty = Convert.ToInt32(dr["sumfbizqty"]),
                        fsourceentryid = Convert.ToString(dr["fsourceentryid"]),
                        fmaterialid = Convert.ToString(dr["fmaterialid"]),
                    };
                }
            }

            foreach (var val in dataEntities)
            {
                var entries = (val["fentry"] as DynamicObjectCollection).OrderBy(s => Convert.ToInt32(s["fseq"]));
                foreach (var item in entries)
                {
                    var seq = Convert.ToInt32(item["fseq"]);
                    var id = Convert.ToString(item[fsourceentryid]);
                    //商品
                    var matObj = item["fproductid_ref"] as DynamicObject;
                    //if (Convert.ToString(item["fisoutspot"]) == "True" && Convert.ToString(item["fdeliverymode"]) == "1"
                    //    )
                    ////过滤出现货 + 自提
                    //{
                    //    errorMsg += "第" + i + "行商品【" + matObj["fname"] + "】为出现货 + 自提 ,转为出库自动处理。 \r\n";
                    //    filterlist.Add(id);
                    //    continue;
                    //}

                    //自动关闭和手动关闭不下推
                    var fclosestatus_e = Convert.ToString(item["fclosestatus_e"]);
                    if (fclosestatus_e == "3" || fclosestatus_e == "4")
                    {
                        errorMsg += $"第{seq}行商品【{matObj["fname"]}】 行关闭状态已关闭，下推失败！ \r\n";
                        filterlist.Add(id);
                    }
                    else if (dic.ContainsKey(id))
                    {
                        var mdl = dic[id];
                        //销售数量
                        var order_fbizqty = mdl.fbizqty;
                        //出库实收数量汇总
                        var sumfbizqty = mdl.sumfbizqty;
                        if (matObj == null)
                        {
                            continue;
                        }
                        if (order_fbizqty + mdl.fbizreturnqty - sumfbizqty <= 0)
                        {
                            errorMsg += $"第{seq}行商品【{matObj["fname"]}】的销售数量[{order_fbizqty}]+销售已退换数量[{mdl.fbizreturnqty}]应大于已下推的销售出库单的累计实发数量[{sumfbizqty}]，才允许出库！ \r\n";
                            filterlist.Add(id);
                        }
                    }
                }
            }
            return dic;
        }


    }
    public class QtpInstock
    {
        public QtpInstock() { }

        public QtpInstock(int fseq, int fbizqty, int fbizreturnqty, int sumfbizqty, string fsourceentryid, string fmaterialid)
        {
            this.fmaterialid = fmaterialid;
            this.fseq = fseq;
            this.fbizqty = fbizqty;
            this.fbizreturnqty = fbizreturnqty;
            this.sumfbizqty = sumfbizqty;
            this.fsourceentryid = fsourceentryid;
        }

        public string fmaterialid { get; set; }
        public int fseq { get; set; }
        public int fbizqty { get; set; }
        public int fbizreturnqty { get; set; }
        public int sumfbizqty { get; set; }
        public string fsourceentryid { get; set; }
    }
}
