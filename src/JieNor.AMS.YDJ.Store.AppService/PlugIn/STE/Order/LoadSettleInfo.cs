using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：收款或退款
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("LoadSettleInfo")]
    public class LoadSettleInfo : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                throw new BusinessException($"请先保存后再执行该操作！");
            }
            var dataEntity = e.DataEntitys[0];

            // 客户（按需加载，性能优化）
            var customerId = Convert.ToString(dataEntity["fcustomerid"]);
            var customer = this.Context.LoadBizBillHeadDataById("ydj_customer", customerId, "fnumber,fname,fsrcstoreid");
            if (customer == null)
            {
                throw new BusinessException($"客户不存在或已被删除，无法收款！");
            }

            Dictionary<string, object> dicSettle = new Dictionary<string, object>();
            dicSettle["fsourceformid"] = this.HtmlForm.Id;
            dicSettle["fsourceid"] = dataEntity?["id"] as string;
            dicSettle["fsourcenumber"] = dataEntity?["fbillno"] as string;
            dicSettle["fsourcetranid"] = dataEntity?["ftranid"] as string;
            dicSettle["fsettlemaintype"] = "ydj_customer";
            dicSettle["fsettlemainid"] = customer["id"];
            dicSettle["fsettlemainname"] = customer["fname"];
            dicSettle["fcustomerid"] = new { id = customer["id"], fnumber = customer["fnumber"], fname = customer["fname"] };
            dicSettle["freceivable"] = Convert.ToDecimal(dataEntity?["freceivable"]).ToString("f2");
            dicSettle["fissyn"] = false;
            //客户来源门店
            dicSettle["fsrcstoreid"] = customer["fsrcstoreid"];

            dicSettle["fstaffid"] = dataEntity?["fstaffid"] as string;

            //焕新订单标记
            dicSettle["frenewalflag"] = dataEntity?["frenewalflag"];
            dicSettle["fbillno"] = dataEntity?["fbillno"];

            // 设置代收单位信息
            this.SetContactUnitInfo(dataEntity, dicSettle);

            var spService = this.Context.Container.GetService<ISystemProfile>();

            //销售参数的“销售单据（意向及合同）不审核可退款”:  是否勾选
            var fcanrefund = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanrefund", false);

            //销售参数的“销售单据（意向及合同）不审核可退款”:  是否勾选
            var ftransferallowrefund = spService.GetSystemParameter(this.Context, "bas_storesysparam", "ftransferallowrefund", false);

            //销售参数的“销售单据（意向及合同）不审核可收款”:  是否勾选
            var fcanreceipt = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanreceipt", false);

            //结算类型：收款，退款
            var settleType = this.GetQueryOrSimpleParam<string>("settleType", "");
            switch (settleType.ToLower())
            {
                //收款
                case "receipt":

                    // 获取是否可以超额收款参数
                    var canexcess = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanexcess", false);
                    if (!canexcess && Convert.ToDecimal(dataEntity?["funreceived"]) <= 0)
                    {
                        throw new BusinessException($"未收金额小于等于0，无法收款！");
                    }

                    // 如果未勾择 |销售参数的“销售单据（意向及合同）不审核可收款”| => 则需要审核后才可收款
                    if (!fcanreceipt)
                    {
                        if (!Convert.ToString(dataEntity?["fstatus"]).Equals("E"))
                        {
                            throw new BusinessException($"当前单据未审核，无法收款！");
                        }
                    }

                    // 已结算金额
                    dicSettle["fsettledamount"] = Convert.ToDecimal(dataEntity?["fsumreceivable"]).ToString("f2");

                    var orderService = this.Container.GetService<IOrderService>();

                    // 待结算金额
                    dicSettle["funsettleamount"] = orderService.GetUnsettleAmount(this.Context, dataEntity);

                    // 确认已收
                    dicSettle["fconfirmedamount"] = Convert.ToDecimal(dataEntity?["freceivable"]).ToString("f2");

                    //收款待确认
                    dicSettle["freceivabletobeconfirmed"] = Convert.ToDecimal(dataEntity?["freceivabletobeconfirmed"]).ToString("f2");

                    break;

                //退款
                case "refund":
                    var fneedtransferorder = Convert.ToBoolean(dataEntity["fneedtransferorder"]);
                    if (Convert.ToInt32(dataEntity["fclosestatus"]) == 1 && Convert.ToDecimal(dataEntity["funreceived"]) >= 0 && !(ftransferallowrefund && fneedtransferorder))
                    {
                        throw new BusinessException("对不起，当前订单未收款为大于等于0且已整单关闭，禁止退款！如有需要，请使用《其它应收单》退款！");
                    }

                    if (Convert.ToDecimal(dataEntity?["freceivable"]) <= 0)
                    {
                        throw new BusinessException($"确认已收金额小于等于0，无法退款！");
                    }

                    // 如果未勾择 |销售参数的“销售单据（意向及合同）不审核可退款”| => 则需要审核后才可退款
                    if (!fcanrefund)
                    {
                        if (!Convert.ToString(dataEntity?["fstatus"]).Equals("E"))
                        {
                            throw new BusinessException($"当前单据未审核，无法退款！");
                        }
                    }

                    dicSettle["fwithin"] = dataEntity?["fwithin"];
                    dicSettle["fdirection"] = "direction_01";
                    dicSettle["fbizdirection"] = "bizdirection_02";
                    dicSettle["fpurpose"] = "bizpurpose_06";
                    dicSettle["fsettletype"] = "退款";
                    dicSettle["fsettledamount"] = Convert.ToDecimal(dataEntity?["freceivable"]).ToString("f2");

                    var profileService = this.Context.Container.GetService<ISystemProfile>();
                    // 获取订单超额收款自动转账户余额参数
                    var fistransfer = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fistransfer", false);
                    if (fistransfer && Convert.ToDecimal(dataEntity?["funreceived"]) < 0)
                    {
                        //如果【未收款】< 0时，则退款弹框界面的【结算金额】字段默认填充为【未收款】正数金额
                        //dicSettle["amount"] = -Convert.ToDecimal(dataEntity?["funreceived"]);
                        var svc = this.Container.GetService<IComboDataService>();
                        var datas = svc.GetFormComboDatas(this.Context, "coo_settledyn", "paymentdesc");
                        dicSettle["paymentdesc"] = datas.Where(x => x.Name == "合同款").Select(x => x.Id).FirstOrDefault();
                    }

                    //销售合同退款时，未收款<0时，退款弹框界面的【结算金额】字段默认填充为【未收款】正数金额，不受”订单超额收款自动转账户余额参数“控制
                    if (Convert.ToDecimal(dataEntity?["funreceived"]) < 0)
                    {
                        //如果【未收款】< 0时，则退款弹框界面的【结算金额】字段默认填充为【未收款】正数金额
                        dicSettle["amount"] = -Convert.ToDecimal(dataEntity?["funreceived"]);
                    }

                    var _orderService = this.Container.GetService<IOrderService>();

                    // 确认已收
                    dicSettle["fconfirmedamount"] = Convert.ToDecimal(dataEntity?["freceivable"]).ToString("f2");

                    // 待结算金额
                    dicSettle["funsettleamount"] = _orderService.GetUnsettleAmount(this.Context, dataEntity);


                    var coos = this.Context.LoadBizDataByFilter("coo_incomedisburse", "fsourcenumber=@fsourcenumber and fstatus<>'E' and fcancelstatus=0", false, new List<SqlParam>() { new SqlParam("fsourcenumber", System.Data.DbType.String, Convert.ToString(dataEntity?["fbillno"])) });
                    decimal refundAmount = coos.Select(a => Convert.ToDecimal(a["famount"])).Sum();
                    //收款待确认
                    dicSettle["freceivabletobeconfirmed"] = refundAmount.ToString("f2");

                    break;
            }

            // 订单金额
            dicSettle["fsumamount"] = Convert.ToDecimal(dataEntity?["fsumamount"]).ToString("f2");

            //获取客户账户信息
            var accountSynService = this.Container.GetService<ISynAccountBalanceService>();
            dicSettle["fallaccounts"] = accountSynService.GetAllAccountByCustomerId(this.Context, customerId).ToList();

            this.Result.SrvData = dicSettle;
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 设置代收单位信息
        /// </summary>
        private void SetContactUnitInfo(DynamicObject dataEntity, Dictionary<string, object> dicSettle)
        {
            var deptId = Convert.ToString(dataEntity?["fdeptid"]);
            if (deptId.IsNullOrEmptyOrWhiteSpace()) return;

            dicSettle["fdeptid"] = deptId;

            // 部门（按需加载，性能优化）
            var deptEntity = this.Context.LoadBizBillHeadDataById("ydj_dept", deptId, "fisunifiedcashier,fmarketplace");
            if (deptEntity == null) return;

            var isUnifiedCashier = Convert.ToString(deptEntity["fisunifiedcashier"]) == "1";
            if (!isUnifiedCashier) return;

            dicSettle["fcontactunittype"] = new { id = "contactunittype_02", fnumber = "contactunittype_02", fname = "供货商" };
            dicSettle["fcontactunitid"] = deptEntity["fmarketplace"];
            dicSettle["fisunifiedcashier"] = isUnifiedCashier;
        }
    }
}