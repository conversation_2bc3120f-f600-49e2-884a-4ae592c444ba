using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.SchedulerTask;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.AMS.YDJ.Store.AppService.Validation.STE.Order;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static JieNor.AMS.YDJ.Core.Helpers.ProductDelistingHelper;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 是否是从采购订单【提交一级经销】触发的保存
        /// </summary>
        private bool FromPurchaseOrderSubmitAgent
        {
            get
            {
                return this.Option.GetVariableValue("FromPurchaseOrderSubmitAgent", false);
            }
        }

        /// <summary>
        /// 当前二级经销商是否不管理库存
        /// </summary>
        private bool IsNotMgrInv { get; set; }
        private int ftoppiecesendtag { get; set; }
        private int fmanagemodel { get; set; }

        /// <summary>
        /// 保存之前的数据
        /// </summary>
        private List<DynamicObject> BeforeSaveDatas { get; set; }

        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            var preService = this.Container.GetService<IPrepareSaveDataService>();
            preService.RemoveEmptyOrInvalidRow(this.Context, this.OperationContext.HtmlForm, e.DataEntitys);

            // 处理有问题的商品促销id
            var entrys = e.DataEntitys.SelectMany(s => (s["fentry"] as DynamicObjectCollection));
            foreach (var entry in entrys)
            {
                var fpromotionid = Convert.ToString(entry["fpromotionid"]);
                if (fpromotionid.EqualsIgnoreCase("{}") || fpromotionid.EqualsIgnoreCase("{id:,fnumber:,fname:}"))
                {
                    entry["fpromotionid"] = " ";
                }
            }

            RefillReserveTransfer(e.DataEntitys);

            LogDealAmountDiff(e.DataEntitys, "InitializeOperationDataEntity");

            var orderService = this.Container.GetService<IOrderService>();
            orderService.FillUnstdInfo(this.Context, e.DataEntitys);
            orderService.CalculateEntrySubsidyamount(this.Context, e.DataEntitys);
            foreach (var item in e.DataEntitys)
            {
                //重算结算信息
                orderService.CalculateSettlement(this.Context, item, this.HtmlForm);
                //如果明细成交单价*数量!=成交金额，则重算成交单价
                orderService.CalculateDealPrice(this.Context, item, this.HtmlForm);
            }

            orderService.CalculateUnreceived(this.Context, e.DataEntitys);
            orderService.CalculateReceiptStatus(this.Context, e.DataEntitys);

            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fdrawstatus", "fproductid", "fstockstatus", "fbilltype", "fcustomercontactid", "frenewtype", "fstorehouseid" });
        }

        /// <summary>
        /// 表头与表体成交金额不一致日志
        /// </summary>
        /// <param name="content"></param>
        private void LogDealAmountDiff(IEnumerable<DynamicObject> dataEntities, string content)
        {
            if (dataEntities.IsNullOrEmpty()) return;

            ThreadWorker.QuequeTask(() =>
                {
                    List<DynamicObject> diffOrders = new List<DynamicObject>();

                    foreach (var dataEntity in dataEntities)
                    {
                        var fdealamount_h = Convert.ToDecimal(dataEntity["fdealamount"]);

                        var fdealamount_e = (dataEntity["fentry"] as DynamicObjectCollection)?.Sum(s => Convert.ToDecimal(s["fdealamount"])) ?? 0M;


                        if (fdealamount_h != fdealamount_e)
                        {
                            diffOrders.Add(dataEntity);
                        }
                    }

                    if (!diffOrders.Any()) return;

                    StringBuilder builder = new StringBuilder();

                    builder.AppendLine(content);

                    // 记录请求数据
                    builder.AppendLine("request data:");

                    var requestDto = (CommonBillDTO)this.Context.CurrentRequestObject;
                    builder.AppendLine(requestDto.ToJson(false));

                    // 记录当前数据包
                    foreach (var diffOrder in diffOrders)
                    {
                        var entrys = (diffOrder["fentry"] as DynamicObjectCollection);

                        var fdealamount_h = Convert.ToDecimal(diffOrder["fdealamount"]);
                        var fdealamount_e = entrys?.Sum(s => Convert.ToDecimal(s["fdealamount"])) ?? 0M;

                        builder.AppendLine($"金额差异：{fdealamount_h} - {fdealamount_e} = {fdealamount_h - fdealamount_e}");

                        builder.AppendLine("单据头：");

                        builder.AppendLine(new Dictionary<string, object>
                        {
                            { "id", diffOrder["id"] },
                            { "fbillno", diffOrder["fbillno"] },
                            { "fdealamount", diffOrder["fdealamount"] },
                            { "freceivable", diffOrder["freceivable"] },
                            { "funreceived", diffOrder["funreceived"] },
                            { "freceiptstatus", diffOrder["freceiptstatus"] },
                            { "fdontreflect", diffOrder["fdontreflect"] },
                            { "fdistsumamount", diffOrder["fdistsumamount"] },
                            { "fdistsumrate", diffOrder["fdistsumrate"] },
                            { "fdistamount", diffOrder["fdistamount"] },
                            { "ffaceamount", diffOrder["ffaceamount"] },
                            { "freceivabletobeconfirmed", diffOrder["freceivabletobeconfirmed"] },
                            { "fexpense", diffOrder["fexpense"] },
                            { "frefundamount", diffOrder["frefundamount"] },
                            { "factrefundamount", diffOrder["factrefundamount"] },
                            { "fsumamount", diffOrder["fsumamount"] },
                        }.ToJson(false));

                        builder.AppendLine("单据体：");

                        builder.AppendLine(entrys?
                            .Select(s => new Dictionary<string, object>
                            {
                                { "id", s["id"] },
                                { "fseq", s["fseq"] },
                                { "fproductid", s["fproductid"] },
                                { "fprice", s["fprice"] },
                                { "famount", s["famount"] },
                                { "fbizqty", s["fbizqty"] },
                                { "fdistrate", s["fdistrate"] },
                                { "fdistrateraw", s["fdistrateraw"] },
                                { "fdistamount", s["fdistamount"] },
                                { "fdealprice", s["fdealprice"] },
                                { "fdealamount", s["fdealamount"] },
                                { "fisoutspot", s["fisoutspot"] },
                            }).ToJson(false));
                    }

                    DebugUtil.WriteLogToFile(builder.ToString(), "OrderDealAmountDiff");
                },
                (asynResult) =>
                {
                    asynResult.HandleError();
                });
        }


        /// <summary>
        /// 重新填充《预留转移记录》
        /// </summary>
        private void RefillReserveTransfer(DynamicObject[] dataEntitys)
        {
            var orderIds = dataEntitys.Where(s => s.DataEntityState.FromDatabase).Select(s => Convert.ToString(s["id"]))
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace());
            if (!orderIds.Any()) return;

            // 填充预留转移数据
            var newDataEntities = this.Context.LoadBizDataById(this.HtmlForm.Id, orderIds);

            foreach (var dataEntity in dataEntitys)
            {
                var id = Convert.ToString(dataEntity["id"]);
                var newDataEntity = newDataEntities.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));
                if (newDataEntity == null)
                {
                    continue;
                }

                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                var newEntrys = newDataEntity["fentry"] as DynamicObjectCollection;

                foreach (var entry in entrys)
                {
                    var entryId = Convert.ToString(entry["id"]);
                    var newEntry = newEntrys.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(entryId));
                    if (newEntry == null)
                    {
                        continue;
                    }

                    var reserveTransferEntrys = entry["freservetransfer"] as DynamicObjectCollection;
                    var newReserveTransferEntrys = newEntry["freservetransfer"] as DynamicObjectCollection;
                    if (reserveTransferEntrys.Count == 0 && newReserveTransferEntrys.Count == 0)
                    {
                        continue;
                    }

                    reserveTransferEntrys.Clear();

                    foreach (var reserveTransferEntry in newReserveTransferEntrys)
                    {
                        reserveTransferEntrys.Add(reserveTransferEntry);
                    }
                }
            }
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            bool isAutoSubmitAfterSave = this.GetQueryOrSimpleParam<bool>("autosubmit");
            bool isAutoAuditAfterSave = this.GetQueryOrSimpleParam<bool>("autoaudit");
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string temp = Convert.ToString(newData["fbilltype"]);

                if (temp.IsNullOrEmptyOrWhiteSpace())
                {
                    return CheckIsV6Order(temp);
                }
                return true;
            }).WithMessage("业务类型不能为空！"));

            //e.Rules.Add(this.RuleFor("fbillhead", data => data["ftype"]).NotEmpty().WithMessage("业务类型不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["forderdate"]).NotEmpty().WithMessage("业务日期不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fdeliverydate"]).NotEmpty().WithMessage("交货日期不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fcustomer = Convert.ToString(newData["fcustomerid"]);
                bool isnull = this.CheckIsNull(newData);
                if (!isnull)
                {
                    if (fcustomer.IsNullOrEmptyOrWhiteSpace())
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("客户不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fphone = Convert.ToString(newData["fphone"]);
                bool isnull = this.CheckIsNull(newData);
                if (!isnull)
                {
                    if (fphone.IsNullOrEmptyOrWhiteSpace())
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("手机号不能为空！"));

            //错误消息
            var errorMessage = "";

            //涉及到查询数据库的校验逻辑统一放到校验插件中进行批量校验（性能优化）
            e.Rules.Add(new SaveValidation(isAutoSubmitAfterSave, isAutoAuditAfterSave));
            //保存是否来自销售合同变更申请单
            var fromOrderApplyBill = this.GetQueryOrSimpleParam<string>("__fromOrderApplyBill__");
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //如果是来自销售合同变更申请单的保存，这个校验就不需要进行校验
                if (!fromOrderApplyBill.IsNullOrEmptyOrWhiteSpace() && fromOrderApplyBill.EqualsIgnoreCase("__fromOrderApplyBill__"))
                    return true;
                errorMessage = "";
                var fchangestatus = Convert.ToString(newData["fchangestatus"]);
                if ((fchangestatus == "0" || fchangestatus.IsNullOrEmpty()))
                {
                    var fentrys = newData["fentry"] as DynamicObjectCollection;
                    if (!fentrys.IsNullOrEmpty() && fentrys.Count > 0)
                    {
                        if (fentrys.Any(x => Convert.ToDecimal(x["fqty"]) == 0))
                        {
                            errorMessage = $"非变更状态下，销售合同【{newData["fbillno"]}】明细行中基本单位数量必填！";
                            return false;
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            //校验设计图纸
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
        {
            errorMessage = "";

            if (Convert.ToString(newData["fbilltype"]).EqualsIgnoreCase("ydj_order_vsix"))
            {
                if (newData["fdesignerid"].IsNullOrEmptyOrWhiteSpace())
                {
                    errorMessage = "请选择设计师！";
                    return false;
                }
                else if (newData["fhqdesignerid"].IsNullOrEmptyOrWhiteSpace())
                {
                    errorMessage = "总部暂未对该经销商配置总部设计师，请联系总部进行配置！";
                    return false;
                }
                var fentrys = newData["fentry"] as DynamicObjectCollection;
                if (!fentrys.IsNullOrEmpty() && fentrys.Count() > 1)
                {
                    errorMessage = "对不起，当前为V6定制柜订单，有且仅能选择一行商品！";
                    return false;
                }
                else if (!fentrys.IsNullOrEmpty() && fentrys.Any(x => !x["fproductid"].IsNullOrEmpty() && (Convert.ToDecimal(x["fprice"]) <= 0 || Convert.ToDecimal(x["fdealprice"]) <= 0)))
                {
                    errorMessage = "对不起，当前订单需手工填写【统一零售价】和【成交价】（需满足>0）！";
                    return false;
                }
                var drawFentry = newData["fdrawentity"] as DynamicObjectCollection;
                var oldDrawFentry = oldData["fdrawentity"] as DynamicObjectCollection;
                var errStatus = new string[] { "draw_status_03", "draw_status_05", "draw_status_06" };
                if (!drawFentry.IsNullOrEmpty() && drawFentry.Any(x => !x["ffilename"].ToString().Contains(newData["fbillno"].ToString())))
                {
                    errorMessage = "对不起，当前图纸文件名称必须包含合同编号！";
                    return false;
                }
                if (!oldDrawFentry.IsNullOrEmpty() && !drawFentry.IsNullOrEmpty()
                && drawFentry.Any(x => !oldDrawFentry.Select(o => o["id"]).Contains(x["id"]))
                && oldDrawFentry.Any(x => errStatus.Contains(x["fdrawstatus"])))
                {
                    errorMessage = "对不起，当前订单包含【审核中/审核通过/已确认】状态图纸，禁止继续添加！";
                    return false;
                }
                var editData = (from x in drawFentry
                                join y in oldDrawFentry
                                on x["id"] equals y["id"]
                                where x["ffileid"] != y["ffileid"]
                                select new { x = x, y = y }).ToList();
                var errEditStatus = new string[] { "draw_status_02", "draw_status_03", "draw_status_05", "draw_status_06" };
                foreach (var item in editData)
                {
                    if (errEditStatus.Contains(item.y["fdrawstatus"]))
                    {
                        errorMessage = $"对不起，第{item.x["Fseq"]}行上传文件状态为\"{(item.y["fdrawstatus_ref"] as DynamicObject)?["fenumitem"]}\"，不允许修改！";
                        return false;
                    }
                }
            }
            return true;
        }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //加载引用数据
                //var refObjMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                //refObjMgr?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), newData, false);
                var entry = newData["fentry"] as DynamicObjectCollection;
                if (CheckIsexistLargeSuitNumber(entry))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起当前订单商品行存在排序异常，请点击重新排序后保存！"));

            //如果“业务类型”是“增补单”，则“源合同号”不能为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (CheckIsSubjoin(newData))
                {
                    return !newData["foldorderno"].IsNullOrEmptyOrWhiteSpace();
                }
                newData["foldorderno"] = string.Empty;
                return true;
            }).WithMessage("源合同号不能为空！"));

            //如果“业务类型”是“增补单”，则“责任方”不能为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (CheckIsSubjoin(newData))
                {
                    return !newData["fduty"].IsNullOrEmptyOrWhiteSpace();
                }
                return true;
            }).WithMessage("增补详情【责任方】不能为空！"));

            //如果“责任方”是“我公司”，则“增补详情明细”必须符合以下要求
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fduty"]).Trim().ToLower() == "duty_03")
                {
                    return CheckSubjoinEntry(newData);
                }
                return true;
            }).WithMessage(@"增补详情【销售员、销售部门/部门、承担比例、金额】不能为空，且金额汇总必须等于货品原值！"));

            //“销售员信息”必须符合以下要求
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return CheckDutyEntry2(newData);
            }).WithMessage(@"销售员信息【比例】不能为负数，且【比例】大于0时【销售员、销售部门】不能为空！"));

            //“销售员信息”必须符合以下要求
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fbillno = Convert.ToString(newData["fbillno"]);
                //销售合同的单据类型是销售转单，并且下游有转单申请单且状态审批已经通过,不验证【销售员、比例、金额】不能为空
                if (GetTransferorderBill(fbillno))
                    return true;
                else
                    return CheckDutyEntry(newData);
            }).WithMessage(@"销售员信息【销售员、比例、金额】不能为空，且最多只能有一个主要销售员，且比例必须等于100！"));

            //销售员信息中的销售员不允许重复出现多个
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                DynamicObjectCollection dutyEntrys = newData["fdutyentry"] as DynamicObjectCollection;
                if (dutyEntrys != null && dutyEntrys.Count > 1)
                {
                    List<string> dutyIds = new List<string>();
                    foreach (var dutyEntry in dutyEntrys)
                    {
                        string dutyId = dutyEntry["fdutyid"] as string;
                        if (dutyIds.Contains(dutyId))
                        {
                            return false;
                        }
                        dutyIds.Add(dutyId);
                    }
                }
                return true;
            }).WithMessage("销售员信息【销售员】不允许重复！"));

            //“商品明细”必须符合以下要求
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return CheckProductEntry(newData);
            }).WithMessage(@"商品明细【商品】不能为空，商品明细【销售单位】不能为空，且【数量】必须是大于0，且【单价、金额、折扣率、成交单价、成交金额】不允许为负数！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var fchangestatus = Convert.ToString(newData["fchangestatus"]);
                if (fchangestatus == "1")//变更中
                {
                    var entrys = (newData["fentry"] as DynamicObjectCollection).Where(t => Convert.ToDecimal(t["ftransoutqty"]) > 0).ToList();
                    if (entrys != null && entrys.Count() > 0)
                    {
                        var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
                        var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(newData["id"] as string));
                        foreach (var entry in entrys)
                        {
                            var oldentry = (existSnapshot["fentry"] as DynamicObjectCollection).Where(t => Convert.ToString(t["id"]) == Convert.ToString(entry["id"])).FirstOrDefault();
                            //此处保留两位小数比较冗余0.01，避免尾差情况
                            var newdealPrice = Math.Round(Convert.ToDecimal(entry?["fdealprice"]), 2);
                            var olddealPrice = Math.Round(Convert.ToDecimal(oldentry?["fdealprice"]), 2);
                            if (oldentry != null && newdealPrice - olddealPrice > 0.01M || newdealPrice - olddealPrice < -0.01M)
                            {
                                errorMessage += $"第{entry["fseq"]}行商品已出库,成交单价禁止修改！";
                                return false;
                            }
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            //var unstdData = this.GetQueryOrSimpleParam("unstdData", "");
            //e.Rules.Add(new UnstdPriceValidation(unstdData));
            DynamicObject agentDy = null;
            if (this.Context.IsDirectSale)
            {
                agentDy = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fdirectsalesgiveawaynotzero");
            }
            string msg = string.Empty;
            e.Rules.Add(this.RuleFor("fentry", data => data).IsTrue((newData, oldData) =>
            {
                var materialObj = newData["fproductid_ref"] as DynamicObject;
                if (materialObj != null)
                {
                    if (!Convert.ToString(newData["funitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["funitid"])))
                    {
                        msg = $"第{Convert.ToString(newData["fseq"])}行商品【基本单位】与商品基础信息【基本单位】不一致！";
                        return false;
                    }
                    if (!Convert.ToString(newData["fbizunitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["fsalunitid"])))
                    {
                        msg = $"第{Convert.ToString(newData["fseq"])}行商品【销售单位】与商品基础信息【销售单位】不一致！";
                        return false;
                    }
                }
                //不是直营才校验
                //成交金额可以不为0
                bool isGiveAwayPriceCanBeNotZero = false;
                if (this.Context.IsDirectSale)
                {
                    if (agentDy != null)
                    {
                        isGiveAwayPriceCanBeNotZero = Convert.ToBoolean(Convert.ToInt32(agentDy["fdirectsalesgiveawaynotzero"]));
                    }
                }
                //如果是直营的话，且经销商那里的参数不为0，那么就不需要校验下面的
                if (Convert.ToBoolean(newData["fisgiveaway"]) && !isGiveAwayPriceCanBeNotZero && Math.Round(Convert.ToDecimal(newData["fdealprice"]), 2) > 0)
                {
                    msg = $"第{Convert.ToString(newData["fseq"])}行商品是赠品,成交金额不能大于0！";
                    return false;
                }

                //如果是直营且开启了，且在经销商档案中【直营销售合同赠品成交金额不能为0】勾选了，那么需要校验赠品的成交单价不能为0
                if (Convert.ToBoolean(newData["fisgiveaway"]) && isGiveAwayPriceCanBeNotZero && this.Context.IsDirectSale && Math.Round(Convert.ToDecimal(newData["fdealprice"]), 2) <= 0)
                {
                    var headParentDy = newData.Parent as DynamicObject;
                    msg = $"销售合同【{Convert.ToString(headParentDy["fbillno"])}】,第{Convert.ToString(newData["fseq"])}行商品是赠品,直营销售合同赠品成交单价不能为0！";
                    return false;
                }

                return true;
            }).WithMessage("{0}", (billObj, propObj) => msg));

            //校验库存
            e.Rules.Add(new Validation_Inv());
            //焕新逻辑
            e.Rules.Add(new Validation_Renew());


            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var fchangestatus = Convert.ToString(newData["fchangestatus"]);
                var fpiecesendtag = Convert.ToBoolean(newData["fpiecesendtag"]);
                if (fchangestatus == "1" && fpiecesendtag)//变更中，一件代发
                {
                    bool isPusPurOrder =
                     (newData["fentry"] as DynamicObjectCollection).Any(t => Convert.ToDecimal(t["fbizpurqty"]) > 0 || Convert.ToDecimal(t["ftranspurqty"]) > 0);
                    var entrys = (newData["fentry"] as DynamicObjectCollection).ToList();
                    if (entrys != null && entrys.Count() > 0 && isPusPurOrder)
                    {
                        //存在下游采购，才走校验
                        var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
                        var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(newData["id"] as string));
                        foreach (var entry in entrys)
                        {
                            var newEntry = (existSnapshot["fentry"] as DynamicObjectCollection).Where(t => Convert.ToString(t["id"]) == Convert.ToString(entry["id"])).FirstOrDefault();
                            if (newEntry == null)
                            {
                                //快照不存在，单据存在，新增的行。不允许新增总部直发的行
                                if (Convert.ToString(entry["fdeliverytype"]).Equals("delivery_type_01"))
                                {
                                    errorMessage += $"销售合同【{newData["fbillno"]}】已转采购，不允许新增【交货方式】=总部直发的商品行；第{entry["fseq"]}行为新增的总部直发行！";
                                    return false;
                                }
                            }
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var frenewalflag = Convert.ToBoolean(newData["frenewalflag"]);
                var fpiecesendtag = Convert.ToBoolean(newData["fpiecesendtag"]);
                if (frenewalflag && fpiecesendtag)
                {
                    var entrys = (newData["fentry"] as DynamicObjectCollection).ToList();
                    if (entrys != null && entrys.Count() > 0)
                    {
                        foreach (var entry in entrys)
                        {
                            if (Convert.ToString(entry["fdeliverytype"]).Equals("delivery_type_02"))
                            {
                                errorMessage += $"销售合同【{newData["fbillno"]}】国补订单做一件代发业务时，所有商品行【交货方式】均必须为“总部直发”，请核查！";
                                return false;
                            }
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var fchangestatus = Convert.ToString(newData["fchangestatus"]);
                var fpiecesendtag = Convert.ToBoolean(newData["fpiecesendtag"]);
                if (fchangestatus == "1" && fpiecesendtag)//变更中，一件代发
                {
                    var entrys = (newData["fentry"] as DynamicObjectCollection).Where(t => Convert.ToDecimal(t["fbizpurqty"]) > 0 || Convert.ToDecimal(t["ftranspurqty"]) > 0).ToList();
                    if (entrys != null && entrys.Count() > 0)
                    {
                        //存在下游采购，才走校验
                        var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
                        var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(newData["id"] as string));
                        foreach (var entry in entrys)
                        {
                            var snapEntry = (existSnapshot["fentry"] as DynamicObjectCollection).Where(t => Convert.ToString(t["id"]) == Convert.ToString(entry["id"])).FirstOrDefault();
                            if (snapEntry != null)
                            {
                                //快照存在，单据存在，编辑。不允许新增总部直发的行
                                if (Convert.ToString(entry["fdeliverytype"]).Equals("delivery_type_01"))
                                {
                                    if (Convert.ToDecimal(entry["fbizqty"]) > (Convert.ToDecimal(snapEntry["fbizqty"])))
                                    {
                                        errorMessage += $"销售合同【{newData["fbillno"]}】已转采购，不允许第{entry["fseq"]}行【交货方式】=总部直发的商品行增加数量！";
                                        return false;
                                    }
                                    else if (Convert.ToDecimal(entry["fbizqty"]) < (Convert.ToDecimal(entry["ftranspurqty"])))
                                    {
                                        errorMessage += $"销售合同【{newData["fbillno"]}】已转采购，不允许第{entry["fseq"]}行【交货方式】=总部直发的商品行减少数量小于已转采购数！";
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                var fchangestatus = Convert.ToString(newData["fchangestatus"]);
                var fpiecesendtag = Convert.ToBoolean(newData["fpiecesendtag"]);
                if (fchangestatus == "1" && fpiecesendtag)//变更中，一件代发
                {
                    //已转采购
                    var entrys = (newData["fentry"] as DynamicObjectCollection).Where(t => Convert.ToDecimal(t["fbizpurqty"]) > 0 || Convert.ToDecimal(t["ftranspurqty"]) > 0).ToList();
                    if (entrys != null && entrys.Count() > 0)
                    {
                        //存在下游采购，才走校验
                        var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
                        var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(newData["id"] as string));
                        var snapAddress = Convert.ToString(existSnapshot["faddress"]);
                        var dataAddress = Convert.ToString(newData["faddress"]);
                        var snapfcustomerid = Convert.ToString(existSnapshot["fcustomerid"]);
                        var datafcustomerid = Convert.ToString(newData["fcustomerid"]);
                        if (!dataAddress.Equals(snapAddress))
                        {
                            errorMessage += $"销售合同【{newData["fbillno"]}】已转采购，详细地址不允许编辑！";
                            return false;
                        }
                        if (!datafcustomerid.Equals(snapfcustomerid))
                        {
                            errorMessage += $"销售合同【{newData["fbillno"]}】已转采购，客户不允许编辑！";
                            return false;
                        }
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToBoolean(newData["frenewalflag"]))
                {
                    var entrys = (newData["fentry"] as DynamicObjectCollection).Where(t => Convert.ToBoolean(t["fisfromfirstinventory"])).ToList();
                    if (entrys.Count > 0)
                    {
                        foreach (var item in entrys)
                        {
                            msg += $"第{Convert.ToString(item["fseq"])}行商品,已勾选【是否一级库存携带】，不允许勾选焕新订单标记！";
                        }
                        return false;
                    }
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => msg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fmanagemodel"]).Equals("1"))
                {
                    bool flag = true;
                    msg = "";
                    var entries = newData["fentry"] as DynamicObjectCollection;
                    foreach (var entry in entries)
                    {
                        var isOutSpot = Convert.ToBoolean(entry["fisoutspot"]);
                        var storehouse = entry["fstorehouseid_ref"] as DynamicObject;
                        decimal dealAmount = 0;
                        if (isOutSpot && Convert.ToString(storehouse?["fwarehousetype"]).Equals("warehouse_03"))
                        {
                            msg += $"第{Convert.ToString(entry["fseq"])}行商品,直营经销商出现货不允许选择虚拟仓！";
                            flag = false;
                        }
                    }
                    return flag;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => msg));
        }


        private bool CheckIsNull(DynamicObject data)
        {
            string fphone = Convert.ToString(data["fphone"]);
            string temp = Convert.ToString(data["fbilltype"]);
            string findenttype = Convert.ToString(data["findenttype"]);
            if (CheckIsV6Order(temp))
            {
                if (findenttype.Equals("Y") || findenttype.Equals("S") || findenttype.Equals("H"))
                {
                    //可以为空
                    return true;
                }
                else return false;//不可以为空
            }
            else
                return false;//不可以为空
        }

        /// <summary>
        /// 销售合同的单据类型是销售转单，并且下游有转单申请单且状态审批已经通过
        /// </summary>
        /// <param name="fbillid"></param>
        /// <returns></returns>
        private bool GetTransferorderBill(string fbillno)
        {
            string strSql = $@"select count(1) count from t_ydj_order t1 with(nolock)
join t_bd_billtype t2 with(nolock) on t1.fbilltype = t2.fid
join t_ydj_transferorderapply t3 with(nolock) on t1.fbillno = t3.fsourcenumber
where t1.fbillno = '{fbillno}' and t2.fname = '销售转单' and t3.ftransferstatus = '2'";
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return Convert.ToInt32(data.FirstOrDefault()["count"]) > 0;
        }

        /// <summary>
        /// 是否存在同一组套件商品中，子件在套件之前的情况
        /// </summary>
        /// <returns></returns>
        private bool CheckIsexistLargeSuitNumber(DynamicObjectCollection fentry)
        {
            var productIds = fentry
                ?.Select(o => Convert.ToString(o["fproductid"]))
                ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                ?.Distinct()
                ?.ToList();
            if (productIds == null || !productIds.Any()) return false;

            var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fname,fsuiteflag");
            if (productObjs == null || !productObjs.Any()) return false;

            //套件组合号
            var suitcombnumberLst = fentry
                .Where(f => Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fproductid"])))?["fsuiteflag"]).EqualsIgnoreCase("1"))
                .Select(o => Convert.ToString(o["fsuitcombnumber"])).Distinct().ToList<string>();

            //配件组合号
            var partscombnumberLst = fentry.Where(o => !Convert.ToString(o["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fpartscombnumber"])).Distinct().ToList<string>();
            //沙发组合号
            var sofacombnumberLst = fentry.Where(o => !Convert.ToString(o["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fsofacombnumber"])).Distinct().ToList<string>();

            int Mainseq_part = int.MaxValue;
            foreach (var partscombnumber in partscombnumberLst)
            {
                //获取配件主商品
                Mainseq_part = fentry.Where(f => Convert.ToBoolean(f["fiscombmain"]) && partscombnumber == Convert.ToString(f["fpartscombnumber"]))
                .Select(o => Convert.ToInt32(o["fseq"])).FirstOrDefault();

                var FseqLst = fentry.Where(f => partscombnumber == Convert.ToString(f["fpartscombnumber"]))
                     .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();
                //判断配件组合号是否连续
                if (!IsContinuous(FseqLst)) return true;

                foreach (var entry in fentry)
                {
                    if (partscombnumber.EqualsIgnoreCase(Convert.ToString(entry["fpartscombnumber"])))
                    {
                        var fseq = Convert.ToInt32(entry["fseq"]);
                        if (fseq < Mainseq_part)
                        {
                            return true;
                        }
                    }
                }
            }

            foreach (var sofacombnumber in sofacombnumberLst)
            {
                var FseqLst = fentry.Where(f => sofacombnumber == Convert.ToString(f["fsofacombnumber"]))
                                        .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();
                //判断沙发组合号是否连续
                if (!IsContinuous(FseqLst)) return true;
            }

            //var entrys = fentry.GroupBy(o=>Convert.ToString(o["fsuitcombnumber"]));
            int Mainseq = int.MaxValue;
            foreach (var suitcombnumber in suitcombnumberLst)
            {
                Mainseq = fentry
                    .Where(f => Convert.ToString(productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fproductid"])))?["fsuiteflag"]).EqualsIgnoreCase("1") && suitcombnumber == Convert.ToString(f["fsuitcombnumber"]))
                    .Select(o => Convert.ToInt32(o["fseq"])).FirstOrDefault();

                var FseqLst = fentry.Where(f => suitcombnumber == Convert.ToString(f["fsuitcombnumber"]))
                    .Select(o => Convert.ToInt32(o["fseq"])).ToList<int>();

                //判断是否连续
                if (!IsContinuous(FseqLst)) return true;

                foreach (var entry in fentry)
                {
                    if (suitcombnumber.EqualsIgnoreCase(Convert.ToString(entry["fsuitcombnumber"])))
                    {
                        var fseq = Convert.ToInt32(entry["fseq"]);
                        if (fseq < Mainseq)
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 判断数字集合是否是连续的
        /// </summary>
        /// <returns></returns>
        public bool IsContinuous(List<int> numList)
        {
            if (numList.Count() == 1) return true;

            numList.Sort((x, y) => -x.CompareTo(y));//降序
            bool result = false;

            for (int i = 0; i < numList.Count() - 1; i++)
            {
                if (numList[i] - numList[i + 1] == 1)
                    result = true;
                else
                {
                    result = false;
                    break;
                }
            }
            return result;
        }

        //计算首款额
        private void CalculateFirstAmount(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }
            // 是否是小程序API发起的保存操作
            var callerTerminal = this.Option.GetVariableValue("callerTerminal", "");
            var spService = this.Container.GetService<ISystemProfile>();
            //任务 37793 第2点 注意：【首款比例控制】【销售合同按收款金额控制出库】弃用
            var firstRatioCtrl = true;//spService.GetSystemParameter(Context, "bas_storesysparam", "ffirstratioctrl", false);
            //var notoutSpotRatio = spService.GetSystemParameter(Context, "bas_storesysparam", "fnotoutspotratio", 0m);
            //var outSpotRatio = spService.GetSystemParameter(Context, "bas_storesysparam", "foutspotratio", 0m);
            var billTypeService = Context.Container.GetService<IBillTypeService>();
            foreach (var dataEntity in dataEntities)
            {
                //任务 37793 参数改为从合同单据类型配置里读取 ，不再使用销售管理参数配置了。
                decimal notoutSpotRatio = 0;
                decimal outSpotRatio = 0;
                var paramSetObj = billTypeService.GetBillTypeParamSet(Context, this.HtmlForm, Convert.ToString(dataEntity["fbilltype"]));
                if (paramSetObj != null)
                {
                    decimal.TryParse(Convert.ToString(paramSetObj["fnotoutspotratio"]), out notoutSpotRatio);
                    decimal.TryParse(Convert.ToString(paramSetObj["foutspotratio"]), out outSpotRatio);
                }

                var fbilltype = dataEntity["fbilltype_ref"] as DynamicObject;
                //当没有启用首款比例控制参数时，首款额等于0
                if (false == firstRatioCtrl || Convert.ToString(fbilltype["fname"]) == "销售转单")
                {
                    dataEntity["ffirstamount"] = 0;
                    continue;
                }

                //当启用首款比例控制参数时，按明细行是否出现货按不同的比例计算首款款
                var firstAmount = 0m;
                var entries = dataEntity["fentry"] as DynamicObjectCollection;

                foreach (var entry in entries)
                {
                    var isOutSpot = Convert.ToBoolean(entry["fisoutspot"]);
                    decimal dealAmount = 0;
                    if (callerTerminal.Equals("MPAPI"))//是否是由小程序发起的请求
                    {
                        dealAmount = Convert.ToDecimal(entry["fdealprice"]);
                        entry["fdealamount"] = decimal.Round(Convert.ToDecimal(entry["fdealamount"]), 2);
                    }
                    else
                    {
                        dealAmount = Convert.ToDecimal(entry["fdealamount"]);
                    }
                    firstAmount += isOutSpot ? dealAmount * outSpotRatio / 100 : dealAmount * notoutSpotRatio / 100;
                }
                dataEntity["ffirstamount"] = Math.Round(firstAmount, 2, MidpointRounding.AwayFromZero);
                //处理小程序尾差问题
                if (callerTerminal.Equals("MPAPI"))//是否是由小程序发起的请求
                {
                    var fdealamount = Convert.ToDecimal(dataEntity["fdealamount"]);
                    var sumdealamount = entries.Select(x => Convert.ToDecimal(x["fdealamount"])).Sum();
                    if (fdealamount - sumdealamount == -0.01M && sumdealamount > 0)
                    {
                        var lastentry = entries.Where(x => Convert.ToDecimal(x["fdealamount"]) > 0).OrderByDescending(x => x["fseq"]).FirstOrDefault();
                        foreach (var entry in entries)
                        {
                            if (Convert.ToString(entry["Id"]) == Convert.ToString(lastentry["Id"]))
                            {
                                entry["fdealamount"] = Convert.ToDecimal(entry["fdealamount"]) - 0.01M;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 计算应付佣金
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CalculatePlanBrokerage(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            //检查是否已开启【合作渠道按比例计算佣金】参数
            var profileService = this.Container.GetService<ISystemProfile>();
            var enableBrokerageRatio = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablebrokerageratio", false);
            //如果没有开启，直接返回
            if (false == enableBrokerageRatio)
            {
                return;
            }

            //获取合作渠道
            var channelIds = dataEntities.Select(x => Convert.ToString(x["fchannel"])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();
            if (channelIds == null || channelIds.Count <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ste_channel");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            var channelEntities = dm.Select(channelIds).OfType<DynamicObject>().ToList();

            if (channelEntities == null || channelEntities.Count <= 0)
            {
                return;
            }

            var channelRatioes = new Dictionary<string, decimal>();
            //按合作渠道的累计带单金额获取提成比例
            foreach (var channelEntity in channelEntities)
            {
                var entries = channelEntity["fentry"] as DynamicObjectCollection;
                if (entries == null || entries.Count <= 0)
                {
                    continue;
                }

                var sumBillAmount = Convert.ToDecimal(channelEntity["fsumbillamount"]);
                foreach (var entry in entries)
                {
                    var lowerLimit = Convert.ToDecimal(entry["flowerlimit"]);
                    var upperLimit = Convert.ToDecimal(entry["fupperlimit"]);

                    if (lowerLimit <= sumBillAmount && upperLimit >= sumBillAmount)
                    {
                        var ratio = Convert.ToDecimal(entry["fratio"]);
                        var channelId = Convert.ToString(channelEntity["id"]);
                        channelRatioes.Add(channelId, ratio);
                        break;
                    }
                }
            }

            if (channelRatioes == null || channelRatioes.Count <= 0)
            {
                return;
            }

            //根据合作渠道提成比例计算合同的应付佣金
            foreach (var channelRatioe in channelRatioes)
            {
                var channelId = channelRatioe.Key;
                var ratio = channelRatioe.Value;

                foreach (var dataEntity in dataEntities)
                {
                    if (false == Convert.ToString(dataEntity["fchannel"]).EqualsIgnoreCase(channelId))
                    {
                        continue;
                    }

                    //应付佣金=订单总额*合作渠道的提成比例/100
                    dataEntity["fplanbrokerage"] = Convert.ToDecimal(dataEntity["fsumamount"]) * ratio / 100;
                }
            }

        }

        /// <summary>
        /// 计算经销商待对账金额
        /// </summary>
        /// <param name="dataEntities"></param>
        private void CalculateVerifyAmount(DynamicObject[] dataEntities)
        {
            dataEntities = dataEntities?.Where(x => false == x.DataEntityState.FromDatabase &&
                                                   Convert.ToString(x["fsourcetype"]).EqualsIgnoreCase("ydj_saleintention"))
                                        .ToArray();
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }
            var sourceNumbers = dataEntities.Select(x => Convert.ToString(x["fsourcenumber"]))
                                            .Where(x => false == string.IsNullOrWhiteSpace(x))
                                            .Distinct()
                                            .ToList();
            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
            var where = "fmainorgid=@fmainorgid and fissyn='0' and fsourceformid='ydj_saleintention' and fverificstatus='verificstatus_02' ";
            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", DbType.String, this.Context.Company) };
            var incomeEntities = multiValueQueryService.Select(this.Context, where, sqlParams, incomeForm, "fsourcenumber", sourceNumbers);
            if (incomeEntities == null || incomeEntities.Count <= 0)
            {
                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
                var incomeItems = incomeEntities.Where(x => Convert.ToString(x["fsourcenumber"]).EqualsIgnoreCase(sourceNumber)).ToList();
                if (incomeItems == null || incomeItems.Count <= 0)
                {
                    dataEntity["fverifyamount"] = 0;
                    return;
                }
                var sumAmount = incomeItems.Sum(x => Convert.ToDecimal(x["famount"]));
                dataEntity["fverifyamount"] = sumAmount;
            }
        }

        /// <summary>
        /// 根据套件商品做排序
        /// </summary>
        /// <param name="fentry"></param>
        private void SortBysuit(DynamicObject[] DataEntitys)
        {
            foreach (var DataEntity in DataEntitys)
            {
                DynamicObjectCollection fentry = DataEntity["fentry"] as DynamicObjectCollection;
                if (!fentry.IsNullOrEmpty())
                {
                    var entrys = fentry.OrderByDescending(f => Convert.ToInt32((f["fproductid_ref"] as DynamicObject)?["fsuiteflag"]))
                        .OrderBy(g => Convert.ToString(g["fsuitcombnumber"]))
                        .OrderBy(g => Convert.ToString(g["fpartscombnumber"]))
                        .OrderBy(g => Convert.ToString(g["fsofacombnumber"]))
                        .ToList();
                    fentry.Clear();
                    for (int i = 0; i < entrys.Count; i++)
                    {
                        var entry = entrys[i] as DynamicObject;
                        //传过去的行也做排序
                        entry["fseq"] = i + 1;
                        fentry.Add(entry);
                    }
                }
            }
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            this.OrderSaveValidation(e.DataEntitys);

            //校验合同保存的时候，客户的来源门店与合同部门不一致，则无法保存;2023-06-12
            var paramSer = this.Container.GetService<ISystemProfile>();
            var fcustomerunique = paramSer.GetSystemParameter<string>(this.Context, "bas_storesysparam", "fcustomerunique");//获取客户报备唯一性控制规则
            if (fcustomerunique != null && fcustomerunique.IndexOf("store") > 0)
            {
                var dataentitys = e.DataEntitys.ToList();
                int count = e.DataEntitys.ToList().Count();
                for (int i = e.DataEntitys.Length - 1; i >= 0; i--)
                {
                    var allowisnull = CheckIsNull(dataentitys[i]);
                    if (allowisnull) continue;
                    var fneedtransferorder = Convert.ToBoolean(dataentitys[i]["fneedtransferorder"]);
                    var fisresellorder = Convert.ToBoolean(dataentitys[i]["fisresellorder"]);
                    //二级分销整体改造：二级销售合同不校验来源门店。
                    if (fisresellorder) continue;
                    //45373 针对二级分销的销售合同转单，勾选复选框需转单和勾选二级分销，不校验来源门店
                    if (fneedtransferorder && fisresellorder) continue;
                    //已和产品确认，无需验证状态，只要开启不一致，就校验部门和客户门店不一致。
                    var fdeptid = Convert.ToString(dataentitys[i]["fdeptid"]);
                    var customerId = Convert.ToString(dataentitys[i]["fcustomerid"]);
                    var customerObjs = this.Context.LoadBizBillHeadDataById("ydj_customer", customerId, "fsrcstoreid");
                    if (fdeptid != Convert.ToString(customerObjs?["fsrcstoreid"]))
                    {
                        dataentitys.Remove(dataentitys[i]);
                        this.Result.ComplexMessage.WarningMessages.Add($"对不起，客户档案的来源门店与当前合同的销售部门不一致，禁止保存。");
                    }

                }

                e.DataEntitys = dataentitys.ToArray();
            }
            if (this.Context.IsSecondOrg)
            {
                // 当前二级经销商信息
                var currentAgent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fisnotmgrinv");
                this.IsNotMgrInv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
            }
            var agent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "ftoppiecesendtag,fmanagemodel");
            this.ftoppiecesendtag = Convert.ToInt32(agent["ftoppiecesendtag"]);
            this.fmanagemodel = Convert.ToInt32(agent["fmanagemodel"]);
            //保存时对套件子件商品进行排序
            //SortBysuit(e.DataEntitys);

            //var productInfoService = this.Container.GetService<IProductInfoService>();
            //productInfoService.CheckProductIsPullOffShelves(this.Context, "fproductid", this.HtmlForm, e.DataEntitys);

            var systemProfileService = this.Container.GetService<ISystemProfile>();
            var orderCodeRule = systemProfileService.GetProfile(this.Context, "ydj", "HeaderSoBillNoCodeRule")?
                .FromJson<UniqueCodeRuleBaseDb>();

            CalculateFirstAmount(e.DataEntitys);
            CalculatePlanBrokerage(e.DataEntitys);
            CalculateVerifyAmount(e.DataEntitys);

            var orderService = this.Container.GetService<IOrderService>();

            //根据销售管理参数计算未收金额字段值
            orderService.CalculateUnreceived(this.Context, e.DataEntitys);
            orderService.CalculateReceiptStatus(this.Context, e.DataEntitys);
            orderService.CalculatePaymentRatios(this.Context, e.DataEntitys);

            List<SelectedRow> bizCloseIds = new List<SelectedRow>();
            List<SelectedRow> unCloseIds = new List<SelectedRow>();

            // 销售退换货允许变更合同
            var returnmodifyorder = paramSer.GetSystemParameter(this.Context, "bas_storesysparam", "freturnmodifyorder", false);

            var billDataService = this.Context.Container.GetService<IBillDataService>();
            var stockStatusObjs = billDataService.LoadBySql(this.Context, "ydj_stockstatus", "fname='可用'");
            foreach (var dataEntity in e.DataEntitys)
            {
                ////已收金额
                //var freceivable = Convert.ToDecimal(dataEntity["freceivable"]);
                ////订单总额
                //var fsumamount = Convert.ToDecimal(dataEntity["fsumamount"]);
                ////结算状态
                //if (freceivable == 0)
                //{
                //    //全款未收
                //    dataEntity["freceiptstatus"] = "receiptstatus_type_01";
                //}
                //else if (freceivable < fsumamount)
                //{
                //    //部分收款
                //    dataEntity["freceiptstatus"] = "receiptstatus_type_02";
                //}
                //else if (freceivable >= fsumamount)
                //{
                //    //全款已收
                //    dataEntity["freceiptstatus"] = "receiptstatus_type_03";
                //}

                //var provider = Context?.Container.GetService<IBaseFormProvider>();
                //var currStaff = provider?.GetMyStaff(Context);


                //保存时根据客户自动带出客户类型
                if (!dataEntity["fcustomerid"].IsNullOrEmptyOrWhiteSpace())
                {
                    var customerid = Convert.ToString(dataEntity["fcustomerid"]).Trim();
                    var customerObj = this.Context.LoadBizDataById("ydj_customer", customerid);
                    dataEntity["fcustomertype"] = customerObj["ftype"];
                }

                //表体预计交货日期为空时，取表头交货日期填充到表体
                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    if (entry["fexdeliverydate"].IsNullOrEmptyOrWhiteSpace())
                    {
                        entry["fexdeliverydate"] = dataEntity["fdeliverydate"];
                    }
                    //如果负责人为空时，去表头负责人填充
                    if (Convert.ToString(entry["fstaffid"]).IsNullOrEmptyOrWhiteSpace())
                    {
                        entry["fstaffid"] = dataEntity["fstaffid"]; // currStaff?.Id;
                    }
                    //将合同单明细状态更新为已成单
                    //entry["fdoorderstatus"] = "1";
                    // 行关闭状态
                    int.TryParse(Convert.ToString(entry["fclosestatus_e"]), out var entryCloseStatus);

                    // 销售数量
                    var qty = Convert.ToDecimal(entry["fbizqty"]);
                    if (qty == 0 && entryCloseStatus != (int)CloseStatus.Manual)
                    {
                        bizCloseIds.Add(new SelectedRow
                        {
                            PkValue = Convert.ToString(dataEntity["id"]),
                            EntryPkValue = Convert.ToString(entry["id"]),
                            EntityKey = "fentry"
                        });
                    }
                    // 4888:手动关闭后 因为数量大于0 然后保存 状态又会变回正常 需要更新逻辑：如果手动关闭即便数量大于0，状态也不做调整。
                    //else if (entryCloseStatus == (int)CloseStatus.Manual && qty != 0)
                    //{
                    //    unCloseIds.Add(new SelectedRow
                    //    {
                    //        PkValue = Convert.ToString(dataEntity["id"]),
                    //        EntryPkValue = Convert.ToString(entry["id"]),
                    //        EntityKey = "fentry"
                    //    });
                    //}

                    //当库存状态为空时，默认为可用
                    if (entry["fstockstatus"].IsNullOrEmptyOrWhiteSpace())
                    {
                        if (!stockStatusObjs.IsNullOrEmptyOrWhiteSpace() && stockStatusObjs.Any())
                        {
                            entry["fstockstatus"] = Convert.ToString(stockStatusObjs.FirstOrDefault()["id"]);
                        }
                    }
                }
                if (!IsNotMgrInv)
                {
                    Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(dataEntity, this.Context, string.Empty);
                }

                if (returnmodifyorder)
                {
                    //检查记录的关闭行，在计算之后是否为手动关闭
                    List<SelectedRow> draftCloseIds = new List<SelectedRow>();
                    foreach (var closeIdObj in bizCloseIds)
                    {
                        var closeRow = entrys.Where(t => Convert.ToString(t["id"]).EqualsIgnoreCase(closeIdObj.EntryPkValue)).FirstOrDefault();

                        // 销售数量
                        var qty = Convert.ToDecimal(closeRow["fbizqty"]);
                        int.TryParse(Convert.ToString(closeRow["fclosestatus_e"]), out var entryCloseStatus);
                        if (qty == 0 && entryCloseStatus == (int)CloseStatus.Manual)
                        {
                            draftCloseIds.Add(closeIdObj);
                        }
                    }
                    bizCloseIds = draftCloseIds;
                }

                //处理总部合同号逻辑
                if (orderCodeRule != null && dataEntity["fhqderno"].IsNullOrEmptyOrWhiteSpace())
                {
                    dataEntity["fhqderno"] = this.GetHeaderContractNo(this.Context, dataEntity, orderCodeRule);
                }

                //保存时自动赋值明细
                if (Convert.ToString(dataEntity["fbilltype"]).EqualsIgnoreCase("v6定制柜合同"))
                {
                    foreach (var entry in entrys)
                    {
                        entry["forderattr"] = dataEntity["forderattr"];
                    }

                }
                if (fmanagemodel == 1 && ftoppiecesendtag == 1)
                {
                    var billTypeName = Convert.ToString((dataEntity["fbilltype_ref"] as DynamicObject)?["fname"]);
                    // 判断所有商品明细行【出现货】都未勾选
                    bool allNotOutSpot = entrys.All(x => !Convert.ToBoolean(x["fisoutspot"]));
                    bool allOutSpot = entrys.All(x => Convert.ToBoolean(x["fisoutspot"]));
                    if (billTypeName.Equals("门店上样")) continue;

                    if (allOutSpot == false && allNotOutSpot == false)
                    {
                        throw new BusinessException("直营销售订单商品行需按交货方式分开录单，门店仓出现货与总部直发不可混录。");
                    }
                }
            }

            if (orderCodeRule != null)
            {
                if (orderCodeRule.CodeRuleIndex != null)
                {
                    foreach (var kvpItem in orderCodeRule.CodeRuleIndex.ToArray())
                    {
                        //只保留 90天的流水序列
                        if ((DateTime.Now - kvpItem.Value.LastUpdateTime).TotalDays > 90)
                        {
                            orderCodeRule.CodeRuleIndex.Remove(kvpItem.Key);
                        }
                    }
                }
                systemProfileService.CreateOrUpdateProfile(this.Context, "ydj", "HeaderSoBillNoCodeRule", orderCodeRule.ToJson());
            }
            //更新源单信息（销售意向单）
            this.UpdateSourceBillInfo(e.DataEntitys);

            //生成商品明细订单跟踪号
            //#33911 子 【慕思现场】取消 销售合同 保存自动生成【物流跟踪号】逻辑 / 销售合同保存取消以下逻辑：若定制说明有值，自动生成物流跟踪号
            //this.BuildProductEntryMtoNo(e.DataEntitys);
            //更新商品明细供应商id和销售部门id
            UpdateSupplierInfo(e.DataEntitys);
            //更新图片上传者信息
            UpdateUploader(e.DataEntitys);
            //反写销售机会的合同编号
            RewriteCustomerRecord(e.DataEntitys);
            ////反写客户基础资料的信息
            //RewriteCustomer(e.DataEntitys);
            /*
            //根据默认单据类型 处理 启用通知，启用申购，严格跟单等
            var handleBillType = this.GetQueryOrSimpleParam<string>("handleBillType");
            if (!handleBillType.IsNullOrEmptyOrWhiteSpace())
            {
                RewriteMsgByBillType(e.DataEntitys);
            }
            //根据默认单据类型 处理 启用通知
            RewriteNoticByBillType(e.DataEntitys);
            */

            HandleCustomerRecord(e.DataEntitys);

            List<string> oldZeroList = new List<string>();
            if (unCloseIds.Any())
            {
                oldZeroList = this.DBService.ExecuteDynamicObject(this.Context, "select fentryid from t_ydj_orderentry where fentryid in('" + string.Join("','", unCloseIds.Select(x => x.EntryPkValue).ToList()) + "')")?.Select(x => Convert.ToString(x["fentryid"])).ToList();
            }
            this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);
            if (bizCloseIds.Any())
            {
                List<string> rowIds = bizCloseIds.Select(x => x.EntryPkValue).ToList();
                var invokeClose = this.Gateway.InvokeListOperation(this.Context,
                    this.HtmlForm.Id,
                    bizCloseIds,
                    "bizclose",
                    new Dictionary<string, object>() {
                        { "rowIds",rowIds.ToJson() },
                        { "IgnoreCheckPermssion","true" }//去掉权限校验
                    });
                invokeClose?.ThrowIfHasError(true, $"自动关闭数量为0商品行操作异常！");
            }
            if (unCloseIds.Any())
            {
                List<string> rowIds = unCloseIds.Select(x => x.EntryPkValue).ToList();
                if (!oldZeroList.IsNullOrEmpty() || oldZeroList.Any())
                {
                    rowIds = rowIds.Where(x => oldZeroList.Contains(x)).ToList();
                    unCloseIds = unCloseIds.Where(x => oldZeroList.Contains(x.EntryPkValue)).ToList();
                    var invokeUnClose = this.Gateway.InvokeListOperation(this.Context,
                    this.HtmlForm.Id,
                    unCloseIds,
                    "unclose",
                    new Dictionary<string, object>() {
                        { "rowIds",rowIds.ToJson() },
                        { "IgnoreCheckPermssion","true" },//去掉权限校验
                        { "modifyCloseStatus","1"}
                    });
                    invokeUnClose?.ThrowIfHasError(true, $"自动反关闭手工关闭商品行操作异常！");
                }
            }

            RewriteAIBedOrder(e.DataEntitys);


            LogDealAmountDiff(e.DataEntitys, "BeginOperationTransaction");

            foreach (var item in e.DataEntitys)
            {
                var flag = this.CheckIsV6Order(Convert.ToString(Convert.ToString(item["fbilltype"])));
                var entrys = item["fentry"] as DynamicObjectCollection;
                var omsDeliver = Convert.ToString(entrys.FirstOrDefault()?["fomsdeliver"]);
                var omsDeliverQty = Convert.ToString(entrys.FirstOrDefault()?["fomsdeliverqty"]);
                //是三维家单据，并且oms送达方是空的，才做最新赋值
                if (flag && string.IsNullOrWhiteSpace(omsDeliver))
                {
                    this.UpdateOMSDeliver(new DynamicObject[] { item });
                }
                else
                {
                    entrys.ForEach(a => a["fomsdeliver"] = omsDeliver);
                    entrys.ForEach(a => a["fomsdeliverqty"] = omsDeliverQty);
                }

                string swjordernumber = Convert.ToString(item["fswjordernumber"]);

                if (flag && string.IsNullOrWhiteSpace(swjordernumber))
                {
                    foreach (var entryItem in entrys)
                    {
                        string omsdeliver = Convert.ToString(entryItem["fomsdeliver"]);
                        int omsdeliverqty = Convert.ToInt32(entryItem["fomsdeliverqty"]);
                        if (omsdeliverqty == 0 && string.IsNullOrWhiteSpace(omsdeliver))
                        {
                            throw new BusinessException("该门店无法找到v6送达方，无法新建v6定制柜合同，请检查门店是否正确，谢谢！");
                        }
                    }
                }
            }
        }

        /// <summary>
        ///  根据默认单据类型 处理 启用通知
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void RewriteNoticByBillType(DynamicObject[] dataEntitys)
        {
            var billTypeService = this.Container.GetService<IBillTypeService>();
            var billType = billTypeService.GetDefaultBillTypeId(this.Context, this.HtmlForm);
            var paramSet = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, billType);
            foreach (var data in dataEntitys)
            {
                if (!billType.IsNullOrEmptyOrWhiteSpace())
                {
                    if (paramSet.IsNullOrEmptyOrWhiteSpace())
                    {
                        data["fenablenotice"] = false;//默认不启用通知
                    }
                    else if (Convert.ToBoolean(paramSet["fenablenotice"]))
                    {
                        data["fenablenotice"] = true;
                    }
                    else
                    {
                        data["fenablenotice"] = false;
                    }
                }
                else//默认不启用通知
                {
                    data["fenablenotice"] = false;
                }
            }
        }

        /// <summary>
        /// 根据默认单据类型 处理 启用通知，启用申购，严格跟单
        /// </summary>
        /// <param name="dataEntities"></param>
        private void RewriteMsgByBillType(DynamicObject[] dataEntities)
        {
            foreach (var data in dataEntities)
            {
                var billTypeService = this.Container.GetService<IBillTypeService>();
                var billType = billTypeService.GetDefaultBillTypeId(this.Context, this.HtmlForm);
                if (!billType.IsNullOrEmptyOrWhiteSpace())
                {
                    data["fbilltype"] = billType;
                    var paramSet = billTypeService.GetBillTypeParamSet(this.Context, this.HtmlForm, billType);
                    if (paramSet.IsNullOrEmptyOrWhiteSpace())
                    {
                        return;
                    }
                    if (Convert.ToBoolean(paramSet["fstricttrack"]))
                    {
                        data["fstricttrack"] = true;
                        //默认表体也加上 
                        var fentry = data["fentry"] as DynamicObjectCollection;
                        foreach (var entry in fentry)
                        {
                            entry["fstricttrack"] = true;
                        }
                    }
                    if (Convert.ToBoolean(paramSet["fisapplypur"]))
                    {
                        data["fisapplypur"] = true;
                    }
                    if (Convert.ToBoolean(paramSet["fenablenotice"]))
                    {
                        data["fenablenotice"] = true;
                    }
                }
            }
        }

        /// <summary>
        /// 获取总部合同号
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntity"></param>
        /// <param name="rule"></param>
        /// <returns></returns>
        private string GetHeaderContractNo(UserContext userCtx, DynamicObject dataEntity, UniqueCodeRuleBaseDb rule)
        {
            if (rule == null) return string.Empty;
            if (rule.SeqLength <= 0) return string.Empty;
            string value = "";
            var field = this.HtmlForm.GetField(rule.FieldKey);
            if (field != null && field.Entity is HtmlHeadEntity)
            {
                var fieldValue = field.DynamicProperty.GetValue(dataEntity);
                if (fieldValue != null)
                {
                    if (field is HtmlDateField
                        || field is HtmlDateTimeField)
                    {
                        if (rule.FieldValueFormat.IsNullOrEmptyOrWhiteSpace())
                        {
                            rule.FieldValueFormat = "yyyyMMdd";
                        }
                        value = ((DateTime)fieldValue).ToString(rule.FieldValueFormat);
                    }
                    else
                    {
                        value = Convert.ToString(fieldValue);
                    }
                }
            }

            var prefix = $"{rule.Prefix}{value}";

            var redisCache = this.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<IRedisCache>();
            (redisCache as IPreInitCache).Init(null);
            var seqVal = redisCache.Increment(prefix, 1);

            if (rule.CodeRuleIndex == null) rule.CodeRuleIndex = new Dictionary<string, UniqueCodeRuleIndex>(StringComparer.OrdinalIgnoreCase);
            UniqueCodeRuleIndex uniqueCodeRuleIndex = null;
            if (!rule.CodeRuleIndex.TryGetValue(prefix, out uniqueCodeRuleIndex))
            {
                uniqueCodeRuleIndex = new UniqueCodeRuleIndex()
                {
                    LastUpdateTime = DateTime.Now,
                    LastSeq = seqVal
                };
                rule.CodeRuleIndex[prefix] = uniqueCodeRuleIndex;
            }

            if (uniqueCodeRuleIndex.LastSeq > seqVal)
            {
                //修复redis序列
                for (long i = seqVal; i <= uniqueCodeRuleIndex.LastSeq + 1; i++)
                {
                    redisCache.Increment(prefix, 1);
                }

                seqVal = uniqueCodeRuleIndex.LastSeq + 1;
            }
            else
            {
                uniqueCodeRuleIndex.LastSeq = seqVal;
                uniqueCodeRuleIndex.LastUpdateTime = DateTime.Now;
            }

            return $"{prefix}{seqVal.ToString(new string('0', rule.SeqLength))}";

        }


        /// <summary>
        /// 反写客户基础资料的信息
        /// </summary>
        /// <param name="dataEntities"></param>
        private void RewriteCustomer(DynamicObject[] dataEntities)
        {
            //转单生成的销售合同已经自动生成了【客户】，不需要走这里的反写逻辑
            var customerIds = dataEntities.Where(x => Convert.ToBoolean(x["fissaletransferorder"]) == false
            && !string.IsNullOrWhiteSpace(Convert.ToString(x["fcustomerid"])))
                .Select(x => Convert.ToString(x["fcustomerid"])).Distinct().ToList();

            if (customerIds == null || customerIds.Count <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var customerForm = metaModelService.LoadFormModel(this.Context, "ydj_customer");
            var customerDm = this.Container.GetService<IDataManager>();
            customerDm.InitDbContext(this.Context, customerForm.GetDynamicObjectType(this.Context));

            var customerEntities = customerDm.Select(customerIds).OfType<DynamicObject>().ToList();
            if (customerEntities == null || customerEntities.Count <= 0)
            {
                return;
            }

            var savedCustomers = new List<DynamicObject>();

            foreach (var customerEntity in customerEntities)
            {
                var customerId = Convert.ToString(customerEntity["id"]);
                var orderEntity = dataEntities.FirstOrDefault(x => Convert.ToString(x["fcustomerid"]) == customerId);

                if (orderEntity == null)
                {
                    continue;
                }

                /*
                 * 客户资料完整判断逻辑：联系人、手机号、省市区、地址，都不为空则算完整。
                 */

                string newContacts = Convert.ToString(orderEntity["flinkstaffid"]);
                string newPhone = Convert.ToString(orderEntity["fphone"]);
                string newProvince = Convert.ToString(orderEntity["fprovince"]);
                string newCity = Convert.ToString(orderEntity["fcity"]);
                string newRegion = Convert.ToString(orderEntity["fregion"]);
                string newAddress = Convert.ToString(orderEntity["faddress"]);

                string oldContacts = Convert.ToString(customerEntity["fcontacts"]);
                string oldPhone = Convert.ToString(customerEntity["fphone"]);
                string oldProvince = Convert.ToString(customerEntity["fprovince"]);
                string oldCity = Convert.ToString(customerEntity["fcity"]);
                string oldRegion = Convert.ToString(customerEntity["fregion"]);
                string oldAddress = Convert.ToString(customerEntity["faddress"]);

                // 信息不完整
                bool notComplete = oldContacts.IsNullOrEmptyOrWhiteSpace() ||
                                   oldPhone.IsNullOrEmptyOrWhiteSpace() || oldProvince.IsNullOrEmptyOrWhiteSpace() ||
                                   oldCity.IsNullOrEmptyOrWhiteSpace() || oldRegion.IsNullOrEmptyOrWhiteSpace() ||
                                   oldAddress.IsNullOrEmptyOrWhiteSpace();

                if (notComplete)
                {
                    // 2.客户资料不完整时，则保存时，则反写客户联系人。
                    //customerEntity["fcontacts"] = newContacts;
                    //customerEntity["fphone"] = newPhone;
                    customerEntity["fprovince"] = newProvince;
                    customerEntity["fcity"] = newCity;
                    customerEntity["fregion"] = newRegion;
                    customerEntity["faddress"] = newAddress;

                    // 在客户联系人信息新增一行联系人
                    var contacts = (DynamicObjectCollection)customerEntity["fcuscontacttry"];
                    //var firstContanct = contacts.FirstOrDefault(s => Convert.ToBoolean(s["fcisfirst"]));
                    var contact = new DynamicObject(contacts.DynamicCollectionItemPropertyType);
                    if (contact != null)
                    {
                        contact["fcontacter"] = newContacts;
                        contact["fphone"] = newPhone;
                        contact["fprovince"] = newProvince;
                        contact["fcity"] = newCity;
                        contact["fregion"] = newRegion;
                        contact["faddress"] = newAddress;
                        contacts.Add(contact);
                    }

                    savedCustomers.Add(customerEntity);
                }
                else
                {
                    // 1.客户资料是完整时，则保存时，判断是否有一致的联系人，有则不处理，无则新增联系人。

                    var contacts = (DynamicObjectCollection)customerEntity["fcuscontacttry"];

                    // 不存在，则新增
                    if (!contacts.Any(s =>
                        Convert.ToString(s["fcontacter"]).EqualsIgnoreCase(newContacts) &&
                        Convert.ToString(s["fphone"]).EqualsIgnoreCase(newPhone) &&
                        Convert.ToString(s["fprovince"]).EqualsIgnoreCase(newProvince) &&
                        Convert.ToString(s["fcity"]).EqualsIgnoreCase(newCity) &&
                        Convert.ToString(s["fregion"]).EqualsIgnoreCase(newRegion) &&
                        Convert.ToString(s["faddress"]).EqualsIgnoreCase(newAddress)))
                    {
                        var contact = new DynamicObject(customerForm.GetEntryEntity("fcuscontacttry").DynamicObjectType);
                        contact["fcontacter"] = newContacts;
                        contact["fphone"] = newPhone;
                        contact["fprovince"] = newProvince;
                        contact["fcity"] = newCity;
                        contact["fregion"] = newRegion;
                        contact["faddress"] = newAddress;
                        contacts.Add(contact);

                        savedCustomers.Add(customerEntity);
                    }
                }
            }

            // 反写客户信息
            if (savedCustomers.Count > 0)
            {
                var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_customer", savedCustomers, "save",
                         new Dictionary<string, object>());
                result.ThrowIfHasError(true, "反写客户信息失败！");
            }

            foreach (var dataEntity in customerEntities)
            {
                //将协同客户到K3Cloud
                DirectSynergyHelper.SyncCustomerToK3Cloud(this.Context, customerForm, dataEntity);
            }
        }

        /// <summary>
        /// 反写销售机会的合同编号 和 最后跟进时间
        /// </summary>
        /// <param name="dataEntities"></param>
        private void RewriteCustomerRecord(DynamicObject[] dataEntities)
        {
            var metaModelService = this.Container.GetService<IMetaModelService>();
            var recordForm = metaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var salForm = metaModelService.LoadFormModel(this.Context, "ydj_saleintention");
            var recordEntities = new List<DynamicObject>();
            var recordDm = this.Container.GetService<IDataManager>();
            recordDm.InitDbContext(this.Context, recordForm.GetDynamicObjectType(this.Context));

            var datas = dataEntities.Where(x => Convert.ToString(x["fsourcetype"]) == recordForm.Id).ToList();
            if (datas != null && datas.Count > 0)
            {
                var recordNumbers = datas.Select(x => Convert.ToString(x["fsourcenumber"])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();

                if (recordNumbers != null && recordNumbers.Count > 0)
                {
                    var recordReader = this.Context.GetPkIdDataReaderWithNumber(recordForm, recordNumbers);
                    var recordDBEntities = recordDm.SelectBy(recordReader).OfType<DynamicObject>().ToList();

                    if (recordDBEntities != null && recordDBEntities.Count > 0)
                    {
                        foreach (var recordItem in recordDBEntities)
                        {
                            var recordNumber = Convert.ToString(recordItem[recordForm.NumberFldKey]);
                            var orderItem = datas.FirstOrDefault(x => Convert.ToString(x["fsourcenumber"]) == recordNumber);
                            if (orderItem != null)
                            {
                                recordItem["forderno"] = orderItem[this.HtmlForm.NumberFldKey];
                                recordItem["ffollowtime"] = DateTime.Now;//客户的最后跟进时间更新
                            }
                        }
                        recordEntities.AddRange(recordDBEntities);
                    }
                }

            }

            datas = dataEntities.Where(x => Convert.ToString(x["fsourcetype"]) == salForm.Id).ToList();
            if (datas != null && datas.Count > 0)
            {
                var salNumbers = datas.Select(x => Convert.ToString(x["fsourcenumber"])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();

                if (salNumbers != null && salNumbers.Count > 0)
                {
                    var salDm = this.Container.GetService<IDataManager>();
                    salDm.InitDbContext(this.Context, salForm.GetDynamicObjectType(this.Context));
                    var salReader = this.Context.GetPkIdDataReaderWithNumber(salForm, salNumbers);
                    var salDBEntities = salDm.SelectBy(salReader).OfType<DynamicObject>().Where(x => Convert.ToString(x["fsourcetype"]) == recordForm.Id).ToList();

                    if (salDBEntities != null && salDBEntities.Count > 0)
                    {
                        var recordNumbers = salDBEntities.Select(x => Convert.ToString(x["fsourcenumber"])).Where(x => false == string.IsNullOrWhiteSpace(x)).Distinct().ToList();

                        if (recordNumbers != null && recordNumbers.Count > 0)
                        {
                            var recordReader = this.Context.GetPkIdDataReaderWithNumber(recordForm, recordNumbers);
                            var recordDbEntities = recordDm.SelectBy(recordReader).OfType<DynamicObject>().ToList();

                            if (recordDbEntities != null && recordDbEntities.Count > 0)
                            {
                                foreach (var recordItem in recordDbEntities)
                                {
                                    var recordNumber = Convert.ToString(recordItem[recordForm.NumberFldKey]);
                                    var salItem = salDBEntities.FirstOrDefault(x => Convert.ToString(x["fsourcenumber"]) == recordNumber);
                                    if (salItem == null)
                                    {
                                        continue;
                                    }
                                    var salNumber = Convert.ToString(salItem[salForm.NumberFldKey]);
                                    var orderItem = datas.FirstOrDefault(x => Convert.ToString(x["fsourcenumber"]) == salNumber);
                                    if (orderItem != null)
                                    {
                                        recordItem["forderno"] = orderItem[this.HtmlForm.NumberFldKey];
                                        recordItem["fintentionno"] = salItem[this.HtmlForm.NumberFldKey];
                                    }
                                }
                                recordEntities.AddRange(recordDbEntities);
                            }
                        }
                    }
                }
            }

            if (recordEntities != null && recordEntities.Count > 0)
            {
                var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(this.Context, recordForm, recordEntities.ToArray(), OperateOption.Create());
                recordDm.Save(recordEntities);
            }
        }

        /// <summary>
        /// 处理关闭状态
        /// </summary>
        /// <param name="dataEntity"></param>
        private void ProcCloseStatus(DynamicObject dataEntity)
        {
            //如果是变更合同数量，则处理行关闭状态
            /*
             * 新需求：32383
             4. 《采购订单》与《销售合同》点击"变更完成" 时,增加更新单据头的【关闭状态】与明细行的所有【行关闭状态】 , 以下拿《销售合同》为例, 如果是 《采购订单》则用【采购数量】【采购入库数量】【采购退换数量】
               1)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "正常"
               2)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】> 0 且 不等于明细行的【销售数量】 时, 明细行的【行关闭状态】= "部份关闭"
               3)  如果 明细行的【销售数量】-明细行的【销售已出库数】+明细行的【销售已退换数量】= 0  时, 明细行的【行关闭状态】= "自动关闭"

               4) 如果所有明细行的【行关闭状态】="正常"时, 更新单据头的【关闭状态】="正常"
               5) 如果所有明细行的【行关闭状态】="自动关闭"时, 更新单据头的【关闭状态】="整单关闭"
               6) 如果所有明细行的【行关闭状态】="部份关闭"时, 更新单据头的【关闭状态】="部份关闭"
               7) 如果所有明细行的【行关闭状态】同时存"自动关闭"与"手工关闭" 且 不存在"部份关闭"时, 更新单据头的【关闭状态】="整单关闭"
               8) 如果所有明细行的【行关闭状态】不为上述情况时, 更新单据头的【关闭状态】="部份关闭"
            */
            if (Convert.ToString(dataEntity["fchangestatus"]) == "3")
            {
                var entrys = dataEntity["fentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var fbizqty = Convert.ToDecimal(entry["fbizqty"]);//销售数量
                    var fbizoutqty = Convert.ToDecimal(entry["fbizoutqty"]);//销售已出库数                    
                    var fbizreturnqty = Convert.ToDecimal(entry["fbizreturnqty"]);//销售已退换数量
                    var orderQty = fbizqty - fbizoutqty + fbizreturnqty;
                    if (orderQty > 0 && orderQty - fbizreturnqty == fbizqty)
                    {
                        entry["fclosestatus_e"] = (int)CloseStatus.Default;
                    }
                    else if (orderQty > 0 && orderQty - fbizreturnqty != fbizqty)
                    {
                        entry["fclosestatus_e"] = (int)CloseStatus.Part;
                    }
                    else if (orderQty == 0)
                    {
                        entry["fclosestatus_e"] = (int)CloseStatus.Auto;
                    }
                }

                var totalNormalCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == "0").Count();//正常状态总数
                var totalAutoCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Auto).ToString()).Count();//自动关闭状态总数
                var totalPartCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Part).ToString()).Count();//部分关闭状态总数
                var totalManuallyCloseCount = entrys.Where(t => Convert.ToString(t["fclosestatus_e"]) == ((int)CloseStatus.Manual).ToString()).Count();//手动关闭状态总数

                if (totalNormalCount == entrys.Count)
                {
                    dataEntity["fclosestatus"] = (int)CloseStatus.Default;
                }
                else if (totalAutoCloseCount == entrys.Count || ((totalPartCloseCount > 0 || totalManuallyCloseCount > 0) && totalPartCloseCount == 0))
                {
                    dataEntity["fclosestatus"] = (int)CloseStatus.Whole;
                }
                else
                {
                    dataEntity["fclosestatus"] = (int)CloseStatus.Part;
                }
            }
            if (dataEntity["fclosestatus"].IsNullOrEmptyOrWhiteSpace())
            {
                dataEntity["fclosestatus"] = (int)CloseStatus.Default;
            }


        }

        /// <summary>
        /// 调用操作事物后触发的时间
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            OrderQtyWriteBackHelper.WriteBackUnStockOutQty(this.Context, e.DataEntitys);

            //切换收货人手机号及地址
            OrderChangeStaffLog(e.DataEntitys);

            /*
            //选择设计方案
            SaveDTypeAndNumber(e.DataEntitys);
            //选择量尺
            SaveSTypeAndNumber(e.DataEntitys);
            */

            //// 异步同步慕思中台
            //IMuSiService muSiService = this.Container.GetService<IMuSiService>();

            //var customerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");
            //var customers = this.Context.LoadBizDataById(customerForm.Id,
            //    e.DataEntitys.Select(s => Convert.ToString(s["fcustomerid"])));

            //muSiService.SyncAsync(this.Context, customerForm, customers).
            //    ContinueWith(antecedent =>
            //    {
            //        muSiService.SyncAsync(this.Context, this.HtmlForm, e.DataEntitys);
            //    });



            //已经审核的合同，如果修改了交货日期，则需要更新预留的预留日期
            var beUpdate = e.DataEntitys.Where(f => "E".EqualsIgnoreCase(f["fstatus"]?.ToString()))?.ToArray();
            if (beUpdate != null && beUpdate.Length > 0)
            {
                this.Option.SetVariableValue("UpdateReserveDate", true);
                ReserveUtil.UpdateReserveDate(this.Context, this.HtmlForm, beUpdate, this.Option);
            }
            DealDelistingData(e.DataEntitys.ToList());
        }

        /// <summary>
        /// 销售合同【结算进度】不等于“待发起”，不允许修改补贴总金额、会员商城交易流水号、会员商城支付日期、合同附件！
        /// </summary>
        /// <param name="dataEntities"></param>
        private void OrderSaveValidation(DynamicObject[] dataEntities)
        {
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot();

            foreach (var item in dataEntities)
            {
                var renewalflag = Convert.ToBoolean(item["frenewalflag"]);
                if (renewalflag)
                {
                    var fsettlprogress = Convert.ToString(item["fsettlprogress"]);
                    if (!fsettlprogress.EqualsIgnoreCase(Enu_RenewalSettleProgress.待发起))
                    {
                        var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                        if (existSnapshot != null)
                        {
                            if (Convert.ToDecimal(existSnapshot["fsubsidyamount"]) != Convert.ToDecimal(item["fsubsidyamount"])
                           || !Convert.ToString(existSnapshot["fmembershiptranid"]).EqualsIgnoreCase(Convert.ToString(item["fmembershiptranid"]))
                            || !Convert.ToString(existSnapshot["faddress"]).EqualsIgnoreCase(Convert.ToString(item["faddress"]))
                           || Convert.ToDateTime(existSnapshot["fmembershippaydate"]) != Convert.ToDateTime(item["fmembershippaydate"])
                           || !Convert.ToString(existSnapshot["fimage"]).EqualsIgnoreCase(Convert.ToString(item["fimage"])))
                            {
                                throw new BusinessException("销售合同【结算进度】不等于“待发起”，不允许修改详细地址、补贴总金额、会员商城交易流水号、会员商城支付日期、合同附件！");
                            }
                        }
                    }
                    //补偿低版本浏览器保存时，焕新订单的结算进度为空。
                    if (fsettlprogress.IsNullOrEmptyOrWhiteSpace())
                    {
                        item["fsettlprogress"] = Enu_RenewalSettleProgress.待发起;
                    }
                }
                var renewalrectag = Convert.ToBoolean(item["frenewalrectag"]);
                if (renewalrectag == false)
                {
                    var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                    if (existSnapshot != null)
                    {
                        var snaprenewalrectag = Convert.ToBoolean(existSnapshot["frenewalrectag"]);
                        if (snaprenewalrectag)
                        {
                            throw new BusinessException("销售合同已经勾选【焕新预收标记】，不允许取消勾选");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 销售合同若切换收货人手机号及地址，
        /// 操作日志-操作描述需记录收货人、手机号及地址的原值和新值。
        /// </summary>
        /// <param name="dataEntities"></param>
        private void OrderChangeStaffLog(DynamicObject[] dataEntities)
        {
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot();

            foreach (var item in dataEntities)
            {
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                if (item.DataEntityState.FromDatabase && existSnapshot != null)
                {
                    if (!(existSnapshot["fcustomercontactid"] as string).EqualsIgnoreCase(item["fcustomercontactid"] as string)
                        || !(existSnapshot["fphone"] as string).EqualsIgnoreCase(item["fphone"] as string)
                        || !(existSnapshot["fprovince"] as string).EqualsIgnoreCase(item["fprovince"] as string)
                        || !(existSnapshot["fcity"] as string).EqualsIgnoreCase(item["fcity"] as string)
                        || !(existSnapshot["fregion"] as string).EqualsIgnoreCase(item["fregion"] as string))
                    {
                        var newflinkstaffid = (item["fcustomercontactid_ref"] as DynamicObject)?["fcontacter"];
                        var oldflinkstaffid = newflinkstaffid;
                        if (!(existSnapshot["fcustomercontactid"] as string).EqualsIgnoreCase(item["fcustomercontactid"] as string))
                        {
                            //快照里面没有引用数据，故需要查询记录
                            var sqlText = $@"select fcuscontacttryid,fcontacter from t_ydj_fcuscontacttry with(nolock) 
                                        where fcuscontacttryid='{existSnapshot["fcustomercontactid"]}'";
                            var dbService = this.Context.Container.GetService<IDBService>();
                            var dynObj = dbService.ExecuteDynamicObject(this.Context, sqlText)?.FirstOrDefault();
                            if (dynObj != null) oldflinkstaffid = dynObj["fcontacter"];
                        }

                        var oldValue = "{0}，{1}，{2}".Fmt(oldflinkstaffid, existSnapshot["fphone"], this.HtmlForm.GetDistrictFullText_NotRef(this.Context, existSnapshot));
                        var newValue = "{0}，{1}，{2}".Fmt(newflinkstaffid, item["fphone"], this.HtmlForm.GetDistrictFullText(this.Context, item));
                        this.Logger.WriteLog(this.Context, new LogEntry
                        {
                            BillIds = item["id"] as string,
                            BillNos = item["fbillno"] as string,
                            BillFormId = this.HtmlForm.Id,
                            OpName = "销售合同保存",
                            OpCode = this.OperationNo,
                            Content = "执行了【保存】操作，销售合同更换了收货人手机号及地址，原值：{0}；新值：{1}。".Fmt(oldValue, newValue),
                            DebugData = "执行了【保存】操作，销售合同更换了收货人手机号及地址，原值：{0}；新值：{1}".Fmt(oldValue, newValue),
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03
                        });
                    }
                }
            }
        }

        /// <summary>
        /// 选择设计方案
        /// </summary>
        /// <param name="dataEntities"></param>
        private void SaveDTypeAndNumber(DynamicObject[] dataEntities)
        {
            //获取表单模型
            var metaModelService = this.Container.GetService<IMetaModelService>();
            //加载设计方案模型数据
            var designFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_designscheme");
            //操作数据库
            var dm = this.Container.GetService<IDataManager>();
            //初始化上下文
            dm.InitDbContext(this.Context, designFrom.GetDynamicObjectType(this.Context));
            //方案编号
            foreach (var dataEntity in dataEntities)
            {
                var responsorFdesignscheme = Convert.ToString(dataEntity["fdesignscheme"]);
                var responsFbillno = dataEntity["fbillno"];

                //根据设计方案表单模型及编码信息返回设计方案信息datareader
                var dataReader = this.Context.GetPkIdDataReaderWithNumber(designFrom, new List<string> { responsorFdesignscheme });

                //查询设计方案数据               
                var selectFdesignscheme = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                //判断是否存在方案编号
                if (!responsorFdesignscheme.IsNullOrEmptyOrWhiteSpace())
                {
                    var responsFsourcetype = Convert.ToString(selectFdesignscheme?["fsourcetype"]);
                    var responsFnumber = Convert.ToString(selectFdesignscheme?["fsourcenumber"]);

                    //如果不存在，源单类型为销售合同，源单编号为合同编号
                    if (responsFsourcetype.IsNullOrEmptyOrWhiteSpace() && responsFnumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        selectFdesignscheme["fsourcetype"] = "ydj_order";
                        selectFdesignscheme["fsourcenumber"] = responsFbillno;

                    }
                    //初始化上下文
                    dm.InitDbContext(this.Context, designFrom.GetDynamicObjectType(this.Context));
                    dm.Save(selectFdesignscheme);
                    // this.AddRefreshPageAction();
                    this.Result.IsSuccess = true;
                    this.Result.SimpleMessage = "保存成功";
                }
            }


        }

        /// <summary>
        /// 选择量尺
        /// </summary>
        /// <param name="dataEntities"></param>
        private void SaveSTypeAndNumber(DynamicObject[] dataEntities)
        {
            //获取表单模型
            var motaModelService = this.Container.TryGetService<IMetaModelService>();
            //加载量尺模型数据
            var scalerFrom = this.MetaModelService.LoadFormModel(this.Context, "ydj_scalerecord");
            //操作数据库
            var dm = this.Container.GetService<IDataManager>();
            //初始化上下文
            dm.InitDbContext(this.Context, scalerFrom.GetDynamicObjectType(this.Context));
            foreach (var dataEntity in dataEntities)
            {
                var responsorFbillno = dataEntity["fbillno"];
                //量尺编号
                var responsorFscalere = Convert.ToString(dataEntity["fscalerecord"]);
                //根据量尺表单模型及编码信息返回量尺信息datareader
                var dataReader = this.Context.GetPkIdDataReaderWithNumber(scalerFrom, new List<string> { responsorFscalere });
                //查询量尺数据
                var selectFscalere = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                //判断是否存在量尺编号
                if (!responsorFscalere.IsNullOrEmptyOrWhiteSpace())
                {
                    var responsFsourcetype = Convert.ToString(selectFscalere?["fsourcetype"]);
                    var responsFnumber = Convert.ToString(selectFscalere?["fsourcenumber"]);
                    //如果不存在源单类型和源单编号，源单类型为销售合同，源单编号为合同编号
                    if (responsFsourcetype.IsNullOrEmptyOrWhiteSpace() && responsFnumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        selectFscalere["fsourcetype"] = "ydj_order";
                        selectFscalere["fsourcenumber"] = responsorFbillno;
                    }
                    dm.InitDbContext(this.Context, scalerFrom.GetDynamicObjectType(this.Context));
                    dm.Save(selectFscalere);
                    // this.AddRefreshPageAction();
                    this.Result.IsSuccess = true;
                    this.Result.SimpleMessage = "保存成功";
                }
            }
        }

        /// <summary>
        /// 更新图片上传者信息
        /// </summary>
        /// <param name="dataEntities"></param>
        private void UpdateUploader(DynamicObject[] dataEntities)
        {
            foreach (var dataEntity in dataEntities)
            {
                DynamicObjectCollection drawEntitys = dataEntity["fdrawentity"] as DynamicObjectCollection;
                if (drawEntitys != null)
                {
                    foreach (var drawEntity in drawEntitys)
                    {
                        if (drawEntity["fuploader"].IsNullOrEmptyOrWhiteSpace())
                        {
                            var uploader = this.Context.DisplayName;
                            if (uploader.IsNullOrEmptyOrWhiteSpace())
                            {
                                uploader = this.Context.UserName;
                            }
                            drawEntity["fuploader"] = uploader;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查“商品明细”是否合法
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckProductEntry(DynamicObject newData)
        {
            DynamicObjectCollection entry = newData["fentry"] as DynamicObjectCollection;

            //至少要有一行商品明细
            if (entry == null || entry.Count <= 0) return false;

            bool isVlid = true;

            string productId = "", fbizunitid = "";
            decimal length = 0, width = 0, thick = 0, area = 0,
            nsc = 0, qty = 0, price = 0, amount = 0,
            distRate = 0, distAmount = 0, dealPrice = 0, dealAmount = 0;

            //要移除的明细列表
            List<DynamicObject> toRemoveList = new List<DynamicObject>();

            foreach (DynamicObject item in entry)
            {
                productId = Convert.ToString(item["fproductid"]);
                length = Convert.ToDecimal(item["flength"]);
                width = Convert.ToDecimal(item["fwidth"]);
                thick = Convert.ToDecimal(item["fthick"]);
                area = Convert.ToDecimal(item["farea"]);
                nsc = Convert.ToDecimal(item["fnsc"]);
                qty = Convert.ToDecimal(item["fqty"]);
                price = Convert.ToDecimal(item["fprice"]);
                amount = Convert.ToDecimal(item["famount"]);
                distRate = Convert.ToDecimal(item["fdistrate"]);
                distAmount = Convert.ToDecimal(item["fdistamount"]);
                dealPrice = Convert.ToDecimal(item["fdealprice"]);
                dealAmount = Convert.ToDecimal(item["fdealamount"]);
                fbizunitid = Convert.ToString(item["fbizunitid"]);

                //商品为空 并且 数量小于1，则认为是空行
                if (productId.IsNullOrEmptyOrWhiteSpace() && qty < 0)
                {
                    toRemoveList.Add(item);
                    continue;
                }

                //商品不能为空，且数量必须大于0，且所有数字字段不允许为负数
                if (productId.IsNullOrEmptyOrWhiteSpace() || qty < 0
                    || fbizunitid.IsNullOrEmptyOrWhiteSpace()
                    || price < 0 || amount < 0
                    || dealPrice < 0 || dealAmount < 0)
                //|| distRate < 0 || distAmount < 0 || dealPrice < 0 || dealAmount < 0)
                {
                    isVlid = false;
                    break;
                }

                ////数量必须为正整数
                //if (qty.ToString().TrimEnd('0').TrimEnd('.').Contains("."))
                //{
                //    isVlid = false;
                //    break;
                //}
            }

            foreach (var item in toRemoveList)
            {
                entry.Remove(item);
            }

            return isVlid;
        }

        /// <summary>
        /// 检查“销售员信息明细”是否合法
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckDutyEntry2(DynamicObject newData)
        {
            // 不校验
            if (this.FromPurchaseOrderSubmitAgent) return true;

            // 没有明细行时，不校验
            var entry = newData["fdutyentry"] as DynamicObjectCollection;
            if (entry == null || entry.Count <= 0) return true;

            foreach (DynamicObject item in entry)
            {
                var ratio = Convert.ToDecimal(item["fratio"]);
                var dutyId = Convert.ToString(item["fdutyid"]);
                var deptId = Convert.ToString(item["fdeptid"]);

                if (ratio < 0)
                {
                    return false;
                }

                if (ratio > 0 && (dutyId.IsNullOrEmptyOrWhiteSpace() || deptId.IsNullOrEmptyOrWhiteSpace()))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 检查“销售员信息明细”是否合法
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckDutyEntry(DynamicObject newData)
        {
            // 不校验
            if (this.FromPurchaseOrderSubmitAgent) return true;

            DynamicObjectCollection entry = newData["fdutyentry"] as DynamicObjectCollection;

            //至少要有一行销售员信息
            if (entry == null || entry.Count <= 0) return false;

            bool isVlid = true;

            string dutyId = "";
            decimal ratio = 0, amount = 0, ratioSum = 0;
            int dutyCount = 0;
            bool isMain = false;

            //要移除的明细列表
            List<DynamicObject> toRemoveList = new List<DynamicObject>();

            foreach (DynamicObject item in entry)
            {
                isMain = Convert.ToBoolean(item["fismain"]);
                dutyId = Convert.ToString(item["fdutyid"]);
                ratio = Convert.ToDecimal(item["fratio"]);
                amount = Convert.ToDecimal(item["famount"]);

                if (dutyId.IsNullOrEmptyOrWhiteSpace() && ratio <= 0)
                {
                    toRemoveList.Add(item);
                    continue;
                }

                //主销售员如果不是空行，则以下字段不能为空
                //if (dutyId.IsNullOrEmptyOrWhiteSpace() || ratio <= 0 || amount < 0)
                if (isMain && (dutyId.IsNullOrEmptyOrWhiteSpace() || ratio <= 0 || amount < 0))
                {
                    isVlid = false;
                    break;
                }

                if (isMain)
                {
                    dutyCount++;
                }

                ratioSum += ratio;
            }

            foreach (var item in toRemoveList)
            {
                entry.Remove(item);
            }

            if (!isVlid) { return isVlid; }

            //至少要有一个主要销售员，且最多只能有一个主要销售员
            //if (dutyCount == 0 || dutyCount > 1)

            //至少要有一个主要销售员，其它为带单员
            if (dutyCount == 0)
            {
                return false;
            }

            //明细比例汇总必须 = 100
            return ratioSum == 100;
        }

        /// <summary>
        /// 检查“增补详情明细”是否合法
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckSubjoinEntry(DynamicObject newData)
        {
            DynamicObjectCollection entry = newData["fsubjoinentry"] as DynamicObjectCollection;
            if (entry != null && entry.Count > 0)
            {
                string dutyId = "", deptId = "";
                decimal ratio = 0, amount = 0, amountSum = 0;

                bool isVlid = true;

                //要移除的明细列表
                List<DynamicObject> toRemoveList = new List<DynamicObject>();

                foreach (DynamicObject item in entry)
                {
                    dutyId = Convert.ToString(item["fdutyid"]);
                    deptId = Convert.ToString(item["fdeptid"]);
                    ratio = Convert.ToDecimal(item["fratio"]);
                    amount = Convert.ToDecimal(item["famount"]);

                    //如果是空行，则跳过
                    if (dutyId.IsNullOrEmptyOrWhiteSpace() && deptId.IsNullOrEmptyOrWhiteSpace() && ratio <= 0 && amount <= 0)
                    {
                        toRemoveList.Add(item);
                        continue;
                    }

                    //如果不是空行，则以下字段不能为空
                    if (dutyId.IsNullOrEmptyOrWhiteSpace() || deptId.IsNullOrEmptyOrWhiteSpace() || ratio <= 0 || amount <= 0)
                    {
                        isVlid = false;
                        break;
                    }

                    amountSum += amount;
                }

                foreach (var item in toRemoveList)
                {
                    entry.Remove(item);
                }

                if (!isVlid) { return isVlid; }

                //明细金额汇总必须 = 货品原值
                return amountSum == Convert.ToDecimal(newData["ffaceamount"]);
            }

            return true;
        }

        /// <summary>
        /// 检查“业务类型”是否是“增补单”
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckIsSubjoin(DynamicObject newData)
        {
            //此处根据辅助资料主键ID进行判断
            return Convert.ToString(newData["ftype"]).Trim().ToLower() == "order_type_02" || Convert.ToBoolean(newData["fissubjoin"]);
        }

        /// <summary>
        /// 更新源单信息（销售意向单）
        /// </summary>
        /// <param name="dataEntitys"></param>
        public void UpdateSourceBillInfo(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0) return;

            foreach (var dataEntity in dataEntitys)
            {
                //反写销售意向“已下推合同”字段值  dataEntity.DataEntityState.FromDatabase 修改保存时是true,新增才是false
                //if (dataEntity.DataEntityState.FromDatabase == false && false == CheckIsSubjoin(dataEntity))
                if (false == CheckIsSubjoin(dataEntity))
                {
                    var sourceType = Convert.ToString(dataEntity["fsourcetype"]);
                    var sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
                    var orderentrys = dataEntity["fentry"] as DynamicObjectCollection;
                    var sourceNumberList = orderentrys.Select(o => Convert.ToString(o["fsourcenumber_e"])).Distinct().ToList();
                    var sourceTypeList = orderentrys.Select(o => Convert.ToString(o["fsourcetype_e"])).Distinct().ToList();
                    //if (!sourceNumber.IsNullOrEmptyOrWhiteSpace() && sourceType.EqualsIgnoreCase("ydj_saleintention"))
                    if (sourceNumberList.Count > 0 && sourceTypeList.Contains("ydj_saleintention"))
                    {
                        var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, sourceTypeList[0].ToString());

                        var dm = this.Container.GetService<IDataManager>();
                        dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                        string where = $"fmainorgid=@fmainorgid and {htmlForm.NumberFldKey} in ('{string.Join("','", sourceNumberList)}')";
                        var sqlParam = new SqlParam[]
                        {
                            new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company)
                        };
                        var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
                        var sourceBillDataList = dm.SelectBy(dataReader).OfType<DynamicObject>().ToList();
                        foreach (var sourceBillData in sourceBillDataList)
                        {
                            if (sourceBillData != null)
                            {
                                sourceBillData["fispushorder"] = true;
                                sourceBillData["forderid"] = dataEntity["id"];
                                //合同保存反写销售意向 成单状态=“已成单”；
                                //sourceBillData["fdoorderstatus"] = "1";
                                var sourceentrys = sourceBillData["fentity"] as DynamicObjectCollection;
                                var listorderentrys = orderentrys.Select(o => Convert.ToString(o["fsourceentryid_e"])).ToList();
                                sourceentrys.ForEach(a =>
                                {
                                    //明细删除再做保存，删除的明细反写为0，依然存在的反写为1,但是会与多明细分批下推合同的场景冲突
                                    if (listorderentrys.Contains(a["Id"]))
                                    {
                                        a["fdoorderstatus"] = "1";
                                    }
                                    else
                                    {
                                        //如果同一意向的不同明细不断下推生成的合同是新的，由此区别于对修改状态的合同明细删除“已成单”商品的场景
                                        //true表示修改状态，是删除明细的操作 需要将源单明细置为“未成单”
                                        if (dataEntity.DataEntityState.FromDatabase == true)
                                        {
                                            a["fdoorderstatus"] = "0";
                                        }
                                    }
                                });
                                dm.Save(sourceBillData);

                                var sourcePageId = this.GetQueryOrSimpleParam<string>("sourcePageId", "");
                                if (!sourcePageId.IsNullOrEmptyOrWhiteSpace())
                                {
                                    //更新源单前端页面视图（销售意向单）字段值
                                    this.AddSetValueAction("fispushorder", true, null, sourcePageId);
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 生成商品明细订单跟踪号
        /// </summary>
        /// <param name="dataEntitys"></param>
        /// <remarks>
        /// 生成规则：单据编号 + 三位数的序号，比如：HT20180503_001
        /// </remarks>
        private void BuildProductEntryMtoNo(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0) return;

            foreach (var dataEntity in dataEntitys)
            {
                var productEntrys = dataEntity["fentry"] as DynamicObjectCollection;
                if (productEntrys == null) continue;

                var mtonoSeq = 1;
                if (dataEntity.DataEntityState.FromDatabase)
                {
                    //找出最大的订单跟踪号
                    var mtonoEntrys = productEntrys?.Where(o => !o["fmtono"].IsNullOrEmptyOrWhiteSpace());
                    if (mtonoEntrys.Any())
                    {
                        var maxMtoNo = mtonoEntrys.Select(o => Convert.ToString(o["fmtono"]))?.OrderByDescending(o => o)?.First();
                        if (!maxMtoNo.IsNullOrEmptyOrWhiteSpace())
                        {
                            var maxMtoNos = maxMtoNo.Split('_');
                            if (maxMtoNos.Length == 2)
                            {
                                var strMaxMtono = maxMtoNos[1].TrimStart('0');
                                if (!strMaxMtono.IsNullOrEmptyOrWhiteSpace())
                                {
                                    var maxMtonoSeq = 0;
                                    int.TryParse(strMaxMtono, out maxMtonoSeq);
                                    if (maxMtonoSeq > 0)
                                    {
                                        mtonoSeq = maxMtonoSeq + 1;
                                    }
                                }
                            }
                        }
                    }
                }

                foreach (var productEntry in productEntrys)
                {
                    if (!productEntry["fcustomdes_e"].IsNullOrEmptyOrWhiteSpace() || Convert.ToBoolean(productEntry["fstricttrack"]))
                    {
                        if (productEntry["fmtono"].IsNullOrEmptyOrWhiteSpace())
                        {
                            StringBuilder sbMtono = new StringBuilder();
                            var startLength = 3 - mtonoSeq.ToString().Length;
                            for (int i = 0; i < startLength; i++)
                            {
                                sbMtono.Append("0");
                            }
                            sbMtono.Append(mtonoSeq);

                            productEntry["fmtono"] = $"{dataEntity["fbillno"]}_{sbMtono}";

                            mtonoSeq++;
                        }
                    }
                    else
                    {
                        //如果未填写“定制说明”则清空订单跟踪号字段值
                        productEntry["fmtono"] = "";
                    }
                }
            }
        }

        /// <summary>
        /// 更新商品明细供应商id和销售部门id
        /// </summary>
        /// <param name="dataEntitys"></param>
        private void UpdateSupplierInfo(DynamicObject[] dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0)
            {
                return;
            }

            //List<Dictionary<string, string>> caches = new List<Dictionary<string, string>>();

            foreach (var dataEntity in dataEntitys)
            {
                var fentries = dataEntity["fentry"] as DynamicObjectCollection;
                if (fentries == null || fentries.Count <= 0)
                {
                    continue;
                }

                /*
                if (caches != null && caches.Count > 0)
                {
                    foreach (var cache in caches)
                    {
                        foreach (var fentry in fentries.Where(x => Convert.ToString(x["fproductid"]) == cache["fproductid"]))
                        {
                            fentry["fsupplierid"] = cache["fsupplierid"];
                            fentry["foperationmode"] = cache["foperationmode"];
                            fentry["fdeptid"] = cache["fcoodeptid"];
                        }
                    }
                }

                var productids = fentries.Where(x => !string.IsNullOrWhiteSpace(Convert.ToString(x["fproductid"])) &&
                                                      string.IsNullOrWhiteSpace(Convert.ToString(x["fsupplierid"])))
                                         .Select(x => Convert.ToString(x["fproductid"])).Distinct().ToList();

                if (productids != null && productids.Count > 0)
                {
                    StringBuilder sql = new StringBuilder(@"select s.fid as fsupplierid,s.fcoodeptid,m.fid as fproductid,s.foperationmode from t_ydj_supplier s
  inner join t_bd_material m on s.fcoocompanyid=m.fpublishcid and s.fmainorgid=m.fmainorgid
  where m.fmainorgid=@fmainorgid and m.fdataorigin='下载'  and m.fid ");

                    List<SqlParam> sqlParams = new List<SqlParam>
                    {
                        new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company)
                    };

                    if (productids.Count == 1)
                    {
                        sql.Append("=@fproductid");
                        sqlParams.Add(new SqlParam("@fproductid", System.Data.DbType.String, productids[0]));
                    }
                    else
                    {
                        sql.Append(" in (");
                        sql.Append(string.Join(",", productids.Select((x, i) => string.Format("@fproductid{0}", i))));
                        sql.Append(" )");
                        sqlParams.AddRange(productids.Select((x, i) => new SqlParam(string.Format("@fproductid{0}", i), System.Data.DbType.String, x)));
                    }

                    List<Dictionary<string, string>> results = new List<Dictionary<string, string>>();
                    using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
                    {
                        while (dataReader.Read())
                        {
                            results.Add(new Dictionary<string, string>
                            {
                                { "fproductid",dataReader.GetValueToString("fproductid")},
                                { "fsupplierid",dataReader.GetValueToString("fsupplierid")},
                                { "fcoodeptid",dataReader.GetValueToString("fcoodeptid")},
                                { "foperationmode",dataReader.GetValueToString("foperationmode")}
                            });
                        }
                    }

                    if (results != null && results.Count > 0)
                    {
                        foreach (var result in results)
                        {
                            foreach (var fentry in fentries.Where(x => Convert.ToString(x["fproductid"]) == result["fproductid"]))
                            {
                                fentry["fsupplierid"] = result["fsupplierid"];
                                fentry["foperationmode"] = result["foperationmode"];
                                fentry["fdeptid"] = result["fcoodeptid"];
                            }
                        }

                        caches.AddRange(results);
                    }

                }
                */

                //更新销售部门id
                var fdeptid = Convert.ToString(dataEntity["fdeptid"]);
                if (string.IsNullOrWhiteSpace(fdeptid) == false)
                {
                    var status = dataEntity["fstatus"];
                    //已审核、已提交的历史数据不更新部门关联门店
                    if (status != "D" && status != "E")
                    {
                        //修改根据部门加载其最新关联门店                     
                        var fstoreid = Convert.ToString(this.Context.LoadBizBillHeadDataById("ydj_dept", fdeptid, "fstore")?["fstore"]);
                        if (!fstoreid.IsNullOrEmptyOrWhiteSpace())
                        {
                            dataEntity["fstore"] = fstoreid;
                        }
                    }

                    foreach (var fentry in fentries)
                    {
                        var eDefptId = Convert.ToString(fentry["fdeptid"]);
                        if (string.IsNullOrWhiteSpace(eDefptId))
                        {
                            fentry["fdeptid"] = fdeptid;
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "onloadsyncinfo":
                    IOrderLoadSyncInfoService service = this.Container.GetService<IOrderLoadSyncInfoService>();
                    var result = service.LoadSyncInfo(this.Context, e.EventData as Tuple<string, DynamicObject>, this.Option);
                    e.Cancel = result.Item1;
                    e.Result = result.Item2;
                    break;
                //让插件返回源单数据
                case "getsourcedatas":
                    GetSourceDatas(e);
                    break;
                default:
                    break;
            }
        }

        private void GetSourceDatas(OnCustomServiceEventArgs e)
        {
            //设计方案数据包
            var dataEntities = e.DataEntities;
            var eventData = e.EventData as Dictionary<string, object>;
            var sourceForm = eventData["sourceForm"] as HtmlForm;
            //销售机会数据包
            var sourceEntities = new List<DynamicObject>();
            //处理逻辑
            switch (sourceForm.Id)
            {
                //来源单据为销售机会
                case "ydj_customerrecord":
                    var fsourcetype = Convert.ToString(dataEntities[0]["fsourcetype"]);
                    var fsourcenumber = Convert.ToString(dataEntities[0]["fsourcenumber"]);
                    if (fsourcetype == "ydj_saleintention" && !fsourcenumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        var scaleModelService = this.Container.GetService<IMetaModelService>();
                        var scaleHtmForm = scaleModelService.LoadFormModel(this.Context, fsourcetype);
                        var scaleDm = this.Container.GetService<IDataManager>();
                        scaleDm.InitDbContext(this.Context, scaleHtmForm.GetDynamicObjectType(this.Context));
                        var scalrReader = this.Context.GetPkIdDataReader(scaleHtmForm, " fbillno=@fsourcenumber",
                         new SqlParam[] {
                             new SqlParam("@fsourcenumber",System.Data.DbType.String,fsourcenumber)
                         }
                         );
                        var scalObj = scaleDm.SelectBy(scalrReader).OfType<DynamicObject>().FirstOrDefault();
                        var saleType = Convert.ToString(scalObj["fsourcetype"]);
                        var salenumber = Convert.ToString(scalObj["fsourcenumber"]);
                        if (saleType == "ydj_customerrecord" && !salenumber.IsNullOrEmptyOrWhiteSpace())
                        {
                            var saleModelService = this.Container.GetService<IMetaModelService>();
                            var saleHtmForm = saleModelService.LoadFormModel(this.Context, saleType);
                            var saleDm = this.Container.GetService<IDataManager>();
                            saleDm.InitDbContext(this.Context, saleHtmForm.GetDynamicObjectType(this.Context));
                            var saleReader = this.Context.GetPkIdDataReader(saleHtmForm, " fbillno=@fsourcenumber",
                             new SqlParam[] {
                                 new SqlParam("@fsourcenumber",System.Data.DbType.String,salenumber)
                             }
                             );
                            var saleObj = saleDm.SelectBy(saleReader).OfType<DynamicObject>().FirstOrDefault();
                            sourceEntities.Add(saleObj);
                        }
                        var result = new Dictionary<string, object>();
                        result["sourceEntities"] = sourceEntities;
                        //一个销售机会只有一个销售合同，所以同源数据包只有它自己了
                        result["isogenyEntities"] = dataEntities.ToList();
                        e.Result = result;
                        e.Cancel = true;
                    }
                    break;
            }
        }

        /// <summary>
        /// 处理销售机会
        /// 1. 反写商机阶段为签订合同
        /// 2. 添加跟进记录
        /// </summary>
        /// <param name="dataEntities"></param>
        private void HandleCustomerRecord(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var newDataEntities = dataEntities.Where(s => s.DataEntityState.FromDatabase == false).ToArray();

            // 获取关联商机
            var customerRecordObjs = GetCustomerRecords(newDataEntities);
            if (customerRecordObjs.Count == 0)
            {
                return;
            }

            var followerRecordFormDt = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord").GetDynamicObjectType(this.Context);
            var customerRecordFormDt = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord").GetDynamicObjectType(this.Context);

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, customerRecordFormDt);

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            string deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            string staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;

            List<DynamicObject> saveObjs = new List<DynamicObject>();
            List<DynamicObject> followerRecordObjs = new List<DynamicObject>();

            foreach (var item in customerRecordObjs)
            {
                #region 反写商机阶段
                string fphase = Convert.ToString(item["fphase"]);
                if (fphase != "customerrecord_phase_04" && fphase != "customerrecord_phase_05")
                {
                    // 签订合同
                    item["fphase"] = "customerrecord_phase_04";
                    saveObjs.Add(item);
                }
                #endregion

                #region 添加跟进记录
                var followerRecordObj = new DynamicObject(followerRecordFormDt);

                followerRecordObj["fcustomerid"] = item["fcustomerid"];
                followerRecordObj["fcontacts"] = item["fcustomername"];
                followerRecordObj["fphone"] = item["fphone"];
                followerRecordObj["ffollowtime"] = DateTime.Now;
                followerRecordObj["ffollowerid"] = this.Context.UserId;
                followerRecordObj["fdeptid"] = deptId;
                followerRecordObj["fstaffid"] = staffId;
                followerRecordObj["ftype"] = "6";       // 默认是其他
                followerRecordObj["fdescription"] = $"合同已签订";
                followerRecordObj["fobjecttype"] = "objecttype12";  // 签订合同  
                //followerRecordObj["ftranid"] = item["ftranid"];

                // 关联到商机
                followerRecordObj["frelatedbilltype"] = "ydj_customerrecord";
                followerRecordObj["frelatedbillno"] = item["fbillno"];

                // 操作对象是合同
                var orderObj = newDataEntities.FirstOrDefault(s =>
                    (Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase("ydj_customerrecord") && Convert.ToString(s["fsourcenumber"]).EqualsIgnoreCase(Convert.ToString(item["fbillno"])))
                    || (Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase("ydj_saleintention") && Convert.ToString(s["fsourcenumber"]).EqualsIgnoreCase(Convert.ToString(item["fintentionno"])))
                    );
                if (orderObj != null)
                {
                    followerRecordObj["fobjectid"] = orderObj["id"];
                    followerRecordObj["fobjectno"] = orderObj["fbillno"];
                }

                followerRecordObjs.Add(followerRecordObj);
                #endregion
            }

            dm.Save(saveObjs);

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", followerRecordObjs, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "添加跟进记录失败！");
        }

        private List<DynamicObject> GetCustomerRecords(DynamicObject[] dataEntitys)
        {
            // 判断源单类型
            var customerRecordNos = dataEntitys.Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase("ydj_customerrecord")).Select(s => Convert.ToString(s["fsourcenumber"])).ToList();
            var saleIntentionNos = dataEntitys.Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase("ydj_saleintention")).Select(s => Convert.ToString(s["fsourcenumber"])).ToList();

            if (customerRecordNos.Count == 0 && saleIntentionNos.Count == 0)
            {
                return new List<DynamicObject>();
            }

            var htmlForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "ydj_customerrecord");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fmainorgid", DbType.String, this.Context.Company));

            string sqlWhere = $@"fmainorgid=@fmainorgid";
            if (customerRecordNos.Any())
            {
                sqlWhere += $" and fbillno in ({string.Join(",", customerRecordNos.Select(s => $"'{s}'"))})";
            }
            if (saleIntentionNos.Any())
            {
                sqlWhere += $" and fintentionno in ({string.Join(",", saleIntentionNos.Select(s => $"'{s}'"))})";
            }

            var reader = this.Context.GetPkIdDataReader(htmlForm, sqlWhere, sqlParams);

            return dm.SelectBy(reader).OfType<DynamicObject>().ToList();
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            string fstatus = this.GetQueryOrSimpleParam<string>("fstatus");
            var allIds = e.DataEntitys.Where(t => !Convert.ToString(t["id"]).IsNullOrEmptyOrWhiteSpace()).Select(t => Convert.ToString(t["id"]));
            BeforeSaveDatas = new List<DynamicObject>();
            if (allIds != null && allIds.Any())
            {
                BeforeSaveDatas = this.Context.LoadBizDataById(this.HtmlForm.Id, allIds, true);
            }
            foreach (var dbObj in BeforeSaveDatas)
            {
                if (!fstatus.IsNullOrEmptyOrWhiteSpace() && !dbObj["fstatus"].ToString().EqualsIgnoreCase(fstatus))
                {
                    throw new BusinessException($"销售合同【{dbObj?["fbillno"].ToString()}】数据状态已被修改,请刷新或关闭重新打开,谢谢！");
                }

            }
            //DealOrderBillType(this.Context, e.DataEntitys.ToList());
            //http://dmp.jienor.com:81/zentao/task-view-52845.html
            //foreach (var item in e.DataEntitys)
            //{
            //    if (!item.DataEntityState.FromDatabase)
            //    {
            //        string billtype = Convert.ToString(item["fbilltype"]);
            //        if (CheckIsV6Order(billtype))
            //        {
            //            string billno = item["fbillno"].ToString();
            //            if (!string.IsNullOrWhiteSpace(billno) && !billno.StartsWith("DZ", true, null))
            //            {
            //                item["fbillno"] = "DZ" + item["fbillno"];
            //            }
            //        }
            //    }
            //}
        }

        /// <summary>
        /// 处理销售合同单据类型不是当前经销商视角下的问题
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="OrderObjs"></param>
        private void DealOrderBillType(UserContext userCtx, List<DynamicObject> OrderObjs)
        {
            //var fbilltypes = OrderObjs.Select(o => Convert.ToString(o["fbilltype"])).Distinct().ToList();
            //string sql = @" select fprimitiveid,fid from T_BD_BILLTYPE where fmainorgid='{0}' and fprimitiveid in ('{1}')".Fmt(userCtx.BizOrgId, string.Join("','", fbilltypes));
            //var BillTypeObjs = this.DBService.ExecuteDynamicObject(this.Context, sql);

            var svc = Context.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(Context, "ydj_order");
            if (billTypeInfos.Any())
            {
                foreach (var item in OrderObjs)
                {
                    var id = item["fbilltype"]?.ToString();
                    var billTypeInfo = billTypeInfos.FirstOrDefault(o => o.fprimitiveid.EndsWithIgnoreCase(id));
                    if (billTypeInfo != null)
                    {
                        item["fbilltype"] = billTypeInfo.fid;
                        this.AddSetValueAction("fbilltype", billTypeInfo.fid);
                    }
                }
            }
        }


        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //new MuSiMQEnqueueHelper().PushDataToMQAsync(this.Context, this.HtmlForm.Id, e.DataEntitys, "musimqenqueue");

            // 是否是小程序API发起的保存操作
            var callerTerminal = this.Option.GetVariableValue("callerTerminal", "");
            if (callerTerminal.EqualsIgnoreCase("MPAPI"))
            {
                var svc = this.Container.GetService<ICostCalulateService>();
                svc.UpdateSalesOrderCostInfo(this.Context, e.DataEntitys?.ToList());
            }

            LogDealAmountDiff(e.DataEntitys, "AfterExecuteOperationTransaction");

            //合同保存时再计算一遍总部折扣率,二级分销合同除外
            foreach (var item in e.DataEntitys)
            {
                var fisresellorder = Convert.ToBoolean(item["fisresellorder"]);

                if (!fisresellorder)
                {
                    var fenties = item["fentry"] as DynamicObjectCollection;

                    foreach (var fentiry in fenties)
                    {
                        var fdealprice = Convert.ToDecimal(fentiry["fdealprice"]);
                        var fhqprice = Convert.ToDecimal(fentiry["fhqprice"]);

                        if (fhqprice != 0)
                            fentiry["fhqdistrate"] = fdealprice / fhqprice;
                    }
                }
            }

        }


        private void DealDelistingData(List<DynamicObject> dataEntitys)
        {
            if (dataEntitys == null || dataEntitys.Count <= 0) return;

            bool isAutoSubmitAfterSave = this.GetQueryOrSimpleParam<bool>("autosubmit");
            bool isAutoAuditAfterSave = this.GetQueryOrSimpleParam<bool>("autoaudit");

            var entryEntities = ProductDelistingHelper.PrepareFilter(this.HtmlForm.Id, dataEntitys, isAutoSubmitAfterSave, isAutoAuditAfterSave);
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot().ToList();
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, billSnapshotObjs.ToArray(), true, this.HtmlForm, new List<string> { "fproductid" });
            var changeDataModel = ProductDelistingHelper.GetSnapExceptData(this.HtmlForm, billSnapshotObjs, entryEntities);

            changeDataModel = changeDataModel.Where(t => t.Material != null && Convert.ToBoolean(t.Material?["fdelisting"])).ToList();

            List<DynamicObject> allValidateEntrys = new List<DynamicObject>();
            if (changeDataModel != null && changeDataModel.Any())
            {
                var allProductIds = changeDataModel.Select(t => Convert.ToString(t.Material?["id"])).ToList();
                var allSelTypeIds = changeDataModel.Select(t => Convert.ToString(t.Material?["fseltypeid"])).ToList();

                List<string> allDelistingIds = ProductDelistingHelper.PrepareValidationDelisting(this.Context, allProductIds, allSelTypeIds);

                if (allDelistingIds.Count > 0)
                {
                    //Task task = Task.Run(() =>
                    //{
                    //    //ProductDelistingHelper.UpdateDelistingQty(this.Context, this.HtmlForm.Id, allValidateEntrys, allProductIds, allSelTypeIds, allDelistingIds);
                    ProductDelistingLogger logger = new ProductDelistingLogger(this.HtmlForm.Id);
                    logger.AppendLine("changeDataModel：");
                    logger.AppendLine(JsonConvert.SerializeObject(changeDataModel));
                    logger.AppendLine("allDelistingIds：" + string.Join(",", allDelistingIds));
                    logger.Write();
                    ProductDelistingHelper.UpdateDelistingQtyNew(this.Context, this.HtmlForm.Id, changeDataModel, allDelistingIds);
                    //});
                    //ThreadWorker.QuequeTask(task, x =>
                    //{
                    //    if (x?.Exception != null)
                    //    {
                    //        this.OperationContext?.Container.GetService<ILogServiceEx>().Error("异常执行退市清单计算错误：" + x.Exception.Message);
                    //    }
                    //});
                }

            }
        }

        /// <summary>
        /// 反写床垫选配单
        /// </summary>
        /// <param name="orders"></param>
        private void RewriteAIBedOrder(IEnumerable<DynamicObject> orders)
        {
            var faibedorderids = orders.SelectMany(s => s["fentry"] as DynamicObjectCollection)
                .Where(s => !Convert.ToString(s["faibedorderid"]).IsNullOrEmptyOrWhiteSpace())
                .Select(s => Convert.ToString(s["faibedorderid"])).Distinct().ToList();

            if (faibedorderids.Any())
            {
                var sql = $"update t_ms_aibedorder set fispushorder=1 where fid in ({faibedorderids.JoinEx(",", true)})";

                this.Container.GetService<IDBServiceEx>().Execute(this.Context, sql);
            }
        }

        /// <summary>
        /// 是否为v6定制柜合同
        /// </summary>
        /// <returns></returns>
        private bool CheckIsV6Order(string id)
        {
            bool v6OrNot = false;
            var svc = Context.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(Context, "ydj_order");
            var billTypeInfo = billTypeInfos.FirstOrDefault(f => f.fid == id);
            if (billTypeInfo == null)
            {
                return v6OrNot;
            }

            v6OrNot = billTypeInfo.fname.EndsWithIgnoreCase("v6定制柜合同") || (!billTypeInfo.fprimitivename.IsNullOrEmptyOrWhiteSpace() && billTypeInfo.fprimitivename.EndsWithIgnoreCase("v6定制柜合同"));

            //bool v6OrNot = false;
            //var sqlText = string.Format(@"select fname from t_bd_billtype with(nolock) where fid='{0}' ", id);
            //using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
            //{
            //    while (reader.Read())
            //    {
            //        string str = reader.GetValueToString("fname");
            //        if (str.EndsWith("v6定制柜合同"))
            //        {
            //            v6OrNot = true;
            //            break;
            //        }
            //    }
            //}
            return v6OrNot;
        }

        /// <summary>
        /// 更新生成OMS送达方
        /// </summary>
        /// <param name="objs"></param>
        private void UpdateOMSDeliver(DynamicObject[] objs)
        {
            var delivers = this.GetDeliverid(objs.ToList());
            foreach (var item in objs)
            {
                var _deliverqty = delivers.Where(a => a.Key.Equals(Convert.ToString(item["fbillno"]))).Count();
                if (_deliverqty > 1)
                {
                    var entrys = item["fentry"] as DynamicObjectCollection;
                    entrys.ForEach(a => a["fomsdeliverqty"] = _deliverqty);
                }
                else if (_deliverqty == 1)
                {
                    var _deliverItem = delivers.Where(a => a.Key.Equals(Convert.ToString(item["fbillno"]))).FirstOrDefault();
                    if (_deliverItem.IsNullOrEmptyOrWhiteSpace()) continue;
                    var entrys = item["fentry"] as DynamicObjectCollection;
                    entrys.ForEach(a => a["fomsdeliver"] = _deliverItem.Value);
                    entrys.ForEach(a => a["fomsdeliverqty"] = _deliverqty);
                }
                else
                {
                    var entrys = item["fentry"] as DynamicObjectCollection;
                    entrys.ForEach(a => a["fomsdeliver"] = "");
                    entrys.ForEach(a => a["fomsdeliverqty"] = 0);
                }
            }
            //foreach (var item in delivers)
            //{
            //    int qty = objs.Where(a => Convert.ToString(a["fbillno"]).Equals(item.Key)).Count();
            //    if (qty > 1)
            //    {
            //        //var entrys = objItem["fentry"] as DynamicObjectCollection;
            //        //entrys.ForEach(a => a["fomsdeliverqty"] = qty);
            //    }
            //    else
            //    {
            //        var objItem = objs.Where(a => Convert.ToString(a["fbillno"]).Equals(item.Key)).FirstOrDefault();
            //        if (objItem != null)
            //        {
            //            var entrys = objItem["fentry"] as DynamicObjectCollection;
            //            entrys.ForEach(a => a["fomsdeliver"] = item.Value);
            //            entrys.ForEach(a => a["fomsdeliverqty"] = qty);
            //            //entrys.ForEach(a => a["fomsdeliver"] = objItem["Id"]);
            //        }
            //    }
            //}
        }

        /// <summary>
        /// 更新送达方
        /// 对于批量，这里可以优化，不需要重复查，待优化
        /// </summary>
        /// <param name="dataEntities"></param>
        private List<KeyValuePair<string, string>> GetDeliverid(List<DynamicObject> dataEntities)
        {
            List<KeyValuePair<string, string>> deliver = new List<KeyValuePair<string, string>>();
            foreach (var item in dataEntities)
            {
                //得到城市
                var cityid = GetStoreCity(item["fdeptid"].ToString());
                string fresultbrandid = "";

                using (var dr = this.Context.ExecuteReader("select fid from T_YDJ_SERIES with(nolock) where fname='V6家居-传统渠道' and fmainorgid='" + this.Context.TopCompanyId + "' and fforbidstatus=0 and fisresultbrand=1", new List<SqlParam>() { }))
                {
                    if (dr.Read())
                    {
                        fresultbrandid = Convert.ToString(dr["fid"]);
                    }
                }

                var dm = GetDeliverByBrandidAndCity(fresultbrandid, cityid);
                if (dm != null && dm.Count > 0)
                {
                    foreach (var dmItem in dm)
                    {
                        deliver.Add(new KeyValuePair<string, string>(Convert.ToString(item["fbillno"]), dmItem["id"].ToString()));
                    }
                }
            }
            return deliver;


        }

        /// <summary>
        /// 得到门店城市
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        private string GetStoreCity(string deptid)
        {
            string strSql = @"select top 1 t2.fmycity from t_bd_department t1 with(nolock)
                            join t_bas_store t2 with(nolock) on t1.fstore = t2.fid where t1.fid =@fid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fid", System.Data.DbType.String, deptid)
            };
            string fmycity = "";
            using (var dr = this.Context.ExecuteReader(strSql, sqlParam))
            {
                if (dr.Read())
                {
                    fmycity = Convert.ToString(dr["fmycity"]);
                }
            }

            return fmycity;
        }
        /// <summary>
        /// 根据城市和品牌找到送达方
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        private List<DynamicObject> GetDeliverByBrandidAndCity(string fresultbrandid, string fcity)
        {
            if (fresultbrandid.IsNullOrEmptyOrWhiteSpace() || fcity.IsNullOrEmptyOrWhiteSpace())
                return null;
            var metaModelService = this.Context.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "bas_deliver");
            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            //// 按 实控人 获取当前用户对应的所有经销商组织
            //var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentAll(UserContext);
            ////组织
            //var Agents = AgentInfos.Select(o => o.OrgId).ToList();
            //var cid = UserContext.IsTopOrg ? UserContext.TopCompanyId : UserContext.Company;
            //Agents.Add(cid);
            var Agents = new List<string>() { this.Context.Company };
            //获取当前用户登录经销商的《主经销商配置表》
            var topCtx = this.Context.CreateTopOrgDBContext();
            var mainAgentConfigs = topCtx.LoadBizDataByACLFilter("bas_mainagentconfig", $" fmainagentid = '{this.Context.Company}'  AND fforbidstatus='0' ").FirstOrDefault();
            if (mainAgentConfigs != null)
            {
                //存在配置，则需要将所有子经销商也包含进来
                var subAgentEntrys = mainAgentConfigs["fsubagent"] as DynamicObjectCollection;
                if (subAgentEntrys != null && subAgentEntrys.Any())
                {
                    Agents.AddRange(subAgentEntrys.Select(t => Convert.ToString(t["fsubagentid"])));
                }
            }

            var where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' and fsaleorgid=(select fid from T_BAS_ORGANIZATION where fnumber='2111' and fforbidstatus=0 and fmainorgid='{2}') ".Fmt(string.Join("','", Agents), fcity, this.Context.TopCompanyId);

            //var dt = this.DBService.ExecuteDataTable(this.Context, "select  a.fid from T_BAS_DELIVER a with(nolock) inner join t_bas_organization b with(nolock) on a.fsaleorgid=b.fid where b.fnumber='2111'");
            //var tmpTable = this.DBService.CreateTempTableWithDataTable(this.Context, dt);

            var sqlParam = new List<SqlParam>
            {
            };
            var reader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
            var data = dm.SelectBy(reader).OfType<DynamicObject>();
            if (data != null && data.Count() > 0)
            {
                //35321 【慕思现场-正式区问题 526】调整 销售转采购 送达方生成逻辑, 如果匹配到多个送达方时, 系统不进行赋值让用户手动选择
                var count = data.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                if (count == 1)
                {
                    foreach (var item in data)
                    {
                        var fentry = item["fentry"] as DynamicObjectCollection;
                        var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                        if (isexists)
                            return new List<DynamicObject>() { item };
                    }
                }
                else if (count > 1)
                {
                    List<DynamicObject> result = new List<DynamicObject>();
                    foreach (var item in data)
                    {
                        var fentry = item["fentry"] as DynamicObjectCollection;
                        var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                        if (isexists)
                            result.Add(item);
                    }
                    return result;
                }
                //如果城市 +系列没匹配到 还是要走上级城市逻辑
                else if (count == 0)
                {
                    var parentcity = this.Context.Container.GetService<IOrderService>().GetParentCity(this.Context, fcity);
                    if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                    where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}'  and fsaleorgid=(select fid from T_BAS_ORGANIZATION where fnumber='2111' and fforbidstatus=0 and fmainorgid='{2}')  ".Fmt(string.Join("','", Agents), parentcity, this.Context.TopCompanyId);
                    var deliver = this.Context.LoadBizDataByFilter("bas_deliver", where);
                    if (!deliver.IsNullOrEmptyOrWhiteSpace())
                    {
                        var count_temp = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                        if (count_temp == 1)
                        {
                            foreach (var item in deliver)
                            {
                                var fentry = item["fentry"] as DynamicObjectCollection;
                                var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                                if (isexists)
                                    return new List<DynamicObject>() { item };
                            }
                        }
                    }
                }
            }
            //通过深圳市宝安区匹配不到，尝试通过深圳市匹配
            else
            {
                var parentcity = this.Context.Container.GetService<IOrderService>().GetParentCity(this.Context, fcity);

                if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}'  and fsaleorgid=(select fid from T_BAS_ORGANIZATION where fnumber='2111'  and fforbidstatus=0 and fmainorgid='{2}') ".Fmt(string.Join("','", Agents), parentcity, this.Context.TopCompanyId);
                var deliver = this.Context.LoadBizDataByFilter("bas_deliver", where);
                if (!deliver.IsNullOrEmptyOrWhiteSpace())
                {
                    var count = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                    if (count == 1)
                    {
                        foreach (var item in deliver)
                        {
                            var fentry = item["fentry"] as DynamicObjectCollection;
                            var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                            if (isexists)
                                return new List<DynamicObject>() { item };
                        }
                    }
                }
            }
            return null;
        }
    }


    public class CheckUnstdPriceDTO
    {
        public string id { get; set; }

        public List<CheckUnstdPriceEntry> fentry { get; set; }

        public class CheckUnstdPriceEntry
        {
            public string id { get; set; }

            public bool funstdtype { get; set; }

            public string funstdtypestatus { get; set; }

            public decimal fprice { get; set; }
        }
    }
}