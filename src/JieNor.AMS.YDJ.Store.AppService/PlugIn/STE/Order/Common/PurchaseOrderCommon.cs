using JieNor.AMS.YDJ.Store.AppService.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common
{
    /// <summary>
    /// 采购订单公共部分
    /// </summary>
    public class PurchaseOrderCommon
    {
        private const string PurchaseOrderFormId = "ydj_purchaseorder";
        private const string BCBillTypeId = "ydj_purchaseorder_bc"; //系统预设摆场单据类型ID
        private UserContext Context;
        public PurchaseOrderCommon(UserContext context)
        {
            this.Context = context;
        }

        /// <summary>
        /// 若单据类型为摆场订单则采购部部门为必填
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        public bool CheckDeptByBillType(DynamicObject purchase)
        {
            string deptId = Convert.ToString(purchase["fpodeptid"]);
            if (IsBCBillType(purchase) && deptId.IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }
            return true;
        }

        public bool CheckCityByBillType(DynamicObject purchase) {

            if (!IsBCBillType(purchase) )
            {
                return true;
            }
            var fcity = (purchase["fdeliverid_ref"] as DynamicObject)?["fcity"];
            var stroeid = (purchase["fpodeptid_ref"] as DynamicObject)?["fstore"];
            var storeobj = this.Context.LoadBizBillHeadDataById("bas_store", stroeid.ToString(), "fmycity");
            if (storeobj != null && fcity != null) {
                if (!storeobj["fmycity"].ToString().EqualsIgnoreCase(fcity.ToString())) {
                    return false;
                }
            
            }
            return true;

        }

        public bool IsBCBillType(DynamicObject purchase)
        {
            var billTypeObj = Context.GetBillTypeByBizObject(PurchaseOrderFormId, BCBillTypeId);
            if (billTypeObj != null && billTypeObj.fid.EqualsIgnoreCase(Convert.ToString(purchase["fbilltypeid"])))
            {
                return true;
            }
            return false;
        }
    }
}
