using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common
{
    /// <summary>
    /// 合同通用处理类
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("orderashx")]
    public class OrderASHX : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 方法操作前
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string action = this.GetQueryOrSimpleParam<string>("action", null);
            if (action.IsNullOrEmptyOrWhiteSpace())
                return;
            ResultFmt res = new ResultFmt();
            switch (action)
            {
                case "searchStockstatus"://返回名称对应的ID
                    string stockname = this.GetQueryOrSimpleParam<string>("stockname", null);
                    string row = this.GetQueryOrSimpleParam<string>("row", null);
                    res.LoadData(action, new { fid = GetStockstatusIdByStatus(stockname), row = row });
                    break;
                case "existsSostockout":
                    string billno = this.GetQueryOrSimpleParam<string>("billno", null);
                    res.LoadData(action, ExistsSostockout(billno));
                    break;
                case "getAgentmember"://任务40453 第5点
                    string jsonp = this.GetQueryOrSimpleParam<string>("jsonp", null);
                    bool flag = false;
                    bool.TryParse(this.GetQueryOrSimpleParam<string>("tipflag", null), out flag);
                    res.LoadData(action, new { data = GetAgentmember(jsonp), tipflag = flag });
                    break;
            }
            this.Result.SrvData = res.ToJson();
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 获取下游出找到的明细行源单ID
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        private List<string> ExistsSostockout(string billno)
        {
            string strSql = $@"select t2.fentryid from t_ydj_order t1 with(nolock)
join t_ydj_orderentry t2 with(nolock) on t1.fid=t2.fid
join t_stk_sostockoutentry t3 with(nolock) on t3.fsourceentryid = t2.fentryid
where fbillno='{billno}'";

            var list = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    list.Add(Convert.ToString(dr["fentryid"]));
                }
            }

            return list;
        }

        /// <summary>
        /// 根据名称得到库存对应ID
        /// </summary>
        /// <returns></returns>
        private string GetStockstatusIdByStatus(string name)
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_stockstatus");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));

            var where = "fname=@fname";
            var lparas = new List<SqlParam> {
            new SqlParam("@fname",System.Data.DbType.String,name)
         };
            var reader = this.Context.GetPkIdDataReader(purForm, where, lparas);
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>();
            var fid = "";
            if (purOrder != null)
            {
                fid = purOrder.FirstOrDefault()["id"].ToString();
            }
            return fid;
        }

        /// <summary>
        ///查部门是否关联门店，并查出客户是否绑定会员 ID，条件满足找到会员ID 为空返回true 前端提示， 否则返回false 不提示
        /// </summary>
        /// <returns></returns>
        private bool GetAgentmember(string jsonp)
        {
            IOrderService orderService = this.Container.GetService<IOrderService>();
            return orderService.GetAgentmember(this.Context, jsonp);
        }
    }
}
