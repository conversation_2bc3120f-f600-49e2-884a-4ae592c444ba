using JieNor.AMS.YDJ.Core.DataEntity.Models;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：查询列表
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getList")]
    public class GetList : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "buildQueryParameter":
                    buildQueryParameter(e);
                    break;
                case "changeQueryParameter":
                    changeQueryParameter(e);
                    break;
                case "changeResult":
                    changeResult(e);
                    break;
                default:
                    return;
            }
        }

        private void changeResult(OnCustomServiceEventArgs e)
        {
            var srvData = e.EventData as Dictionary<string, object>;
            if (srvData == null || srvData.Count <= 0)
            {
                return;
            }

            var listData = srvData["datas"] as List<Dictionary<string, object>>;
            if (listData == null || listData.Count <= 0)
            {
                return;
            }

            //保护列表客户信息，当前销售合同并没有微信号，如果今后加了微信号按当前的需求要清空微信号
            var protecteDataService = this.Container.GetService<IProtecteDataService>();
            protecteDataService.Init(this.Context, "bas_storesysparam", "fenableprotectecusinfo");
            protecteDataService.MaskFields(new[] { "fphone" }, listData, 3, 4);
        }

        private void buildQueryParameter(OnCustomServiceEventArgs e)
        {
            string searchText = this.GetQueryOrSimpleParam<string>("searchText");
            List<SqlParam> dynamicParams = new List<SqlParam>();
            StringBuilder filterString = new StringBuilder();

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                filterString.Append("(fcustomerid.fname like @searchText or fphone like @searchText or fbillno like @searchText)");
                dynamicParams.Add(new SqlParam("@searchText", System.Data.DbType.String, $"%{searchText}%"));
            }
            e.Result = new Dictionary<string, object>
            {
                { "dynamicParams",dynamicParams},
                { "filterString",filterString.ToString()}
            };

            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null || eventData.Count <= 0)
            {
                return;
            }

            var fields = eventData["fields"] as List<string>;
            if (fields == null)
            {
                return;
            }
            fields.AddRange(@"fid|fcustomerid|fcustomerid.fname|fbillno|fsumamount|forderdate|fstatus|fstatus.fenumitem|(select isnull(sum(oe.fqty),0) from t_ydj_orderentry oe where t0.fid=oe.fid) as fqty|fdeliverydate|funreceived|freceivable|(select isnull(sum(oe.foutqty),0) from t_ydj_orderentry oe where t0.fid=oe.fid) as fsumoutqty".Split('|'));
        }

        private void changeQueryParameter(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null)
            {
                return;
            }
            var para = eventData["para"] as SqlBuilderParameter;

            if (para == null)
            {
                return;
            }

            Filters filters = new Filters();
            filters.Enums.Add(new EnumFilters
            {
                FieldId = "fstatus",
                MultiSelect = false,
                Operator = "="
            });
            filters.Regions.Add(new RegionFilters
            {
                FieldId = "forderdate",
                Operator = "between"
            });
            filters.Regions.Add(new RegionFilters
            {
                FieldId = "fdeliverydate",
                Operator = "between"
            });

            Colation colation = new Colation();
            colation.Options = new List<ColationOption>
            {
                new ColationOption { Id="myOrders",Name="我的合同" },
                new ColationOption { Id="allOrders",Name="全部合同"}
            };
            colation.Default = "myOrders";
            colation.Selected = this.GetQueryOrSimpleParam<string>("colationOptionId");

            string s = null;
            if (string.IsNullOrWhiteSpace(colation.Selected))
            {
                colation.Selected = colation.Default;
            }
            if (colation.Selected == colation.Default)
            {
                IBaseFormProvider baseFormProvider = this.Container.GetService<IBaseFormProvider>();
                s = $"fstaffid='{baseFormProvider.GetMyStaff(this.Context)?.Id}'";
                para.FilterString = string.IsNullOrWhiteSpace(para.FilterString) ? s : para.FilterString.JoinFilterString(s);
            }

            Dictionary<string, string> aliasFields = new Dictionary<string, string>
            {
                { "fbillhead_id","id"},
                { "fcustomerid","customerId"},
                { "fcustomerid_fname","customerName"},
                { "fbillno","billNo"},
                { "fsumamount","sumAmount"},
                { "forderdate","orderDate"},
                { "fstatus","status"},
                { "fstatus_fenumitem","statusName"},
                { "fqty","qty"},
                { "fdeliverydate","deliveryDate"},
                { "funreceived","unreceived"},
                { "freceivable","receivable"},
            };

            e.Result = new Dictionary<string, object>
            {
                { "filters",filters},
                { "colation",colation},
                { "aliasFields",aliasFields}
            };
        }
    }
}
