using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：根据售达方与订单号查询v6定制合同
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("QueryVSixOrder")]
    public  class QueryVSixOrder : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string foriginalagentno = this.GetQueryOrSimpleParam("foriginalagentno", "");
            string foriginalorderbillno = this.GetQueryOrSimpleParam("foriginalorderbillno", "");
            if (string.IsNullOrWhiteSpace(foriginalagentno)) {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "原售达方编号不能为空!";
                return;
            }
            if (string.IsNullOrWhiteSpace(foriginalagentno))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "原工厂订单号不能为空!";
                return;
            }
           
            string querySql = $@"select a.fid as orderid,b.fomsbillno, b.ffactorybillid,b.ffactorybillno,b.ftranspurqty,a.fbillno,a.findenttype,billtype.fname as billtypename,d.fhqderstatus as fhqderstatus from t_ydj_order a with(nolock) 
                                inner join t_bas_agent org with (nolock) on a.fmainorgid=org.fid  and org.fnumber='{foriginalagentno}'
                                inner join t_bd_billtype billtype with (nolock) on a.fbilltype=billtype.fid
                                inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid  and b.ffactorybillno='{foriginalorderbillno}'
                                inner  join t_ydj_poorderentry c with(nolock) on c.fsourceentryid=b.fentryid
                                inner join  t_ydj_purchaseorder d with(nolock) on c.fid=d.fid and d.fcancelstatus='0'
                                where a.fcancelstatus='0' ";
            var data = this.Context.ExecuteDynamicObject(querySql, null).FirstOrDefault();
            if (data == null) {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "无有效数据，请核查是否录入正确，谢谢！";
                return;
            }
            if (data["billtypename"].ToString().EqualsIgnoreCase("v6定制柜合同")&& !data["findenttype"].ToString().EqualsIgnoreCase("H"))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "原定制柜商品行对应的订单不属于正单！";
                return;
            }
            if (!data["fhqderstatus"].ToString().EqualsIgnoreCase("03"))
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "下游无有效已终审采购订单，无法发起售后，请核查！";
                return;
            }
            if (Convert.ToDecimal(data["ftranspurqty"]) !=1M)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "原定制柜商品行已转采购数量不为1！";
                return;
            }
            this.Result.IsSuccess = true;
            this.Result.SrvData = data;
        }
    }
}
