using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：通过员工获取主岗位对应部门
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getdeptbystaff")]
    public class GetDeptByStaff : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string fstaffid = this.GetQueryOrSimpleParam("fstaffid", "");
            if (!fstaffid.IsNullOrEmptyOrWhiteSpace())
            {
                var sql = $@" select t_bd_staffentry.fdeptid as fdeptid from T_BD_STAFF inner join t_bd_staffentry on t_bd_staffentry.fid = T_BD_STAFF.fid
                                where T_BD_STAFF.fid = '{fstaffid}' and t_bd_staffentry.fismain = 1 ";
                var Data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                this.Result.SrvData = Data.FirstOrDefault();
                this.Result.IsSuccess = Data.Count > 0;
            }
            else {
                this.Result.IsSuccess = false;
            }
        }
    }
}
