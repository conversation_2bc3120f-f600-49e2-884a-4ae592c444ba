using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("orderrecrefund")]
    public class OrderRecRefund : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype" });
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            var dm = this.Context.Container.GetService<IDataManager>();
            foreach (var OrderData in e.DataEntitys)
            {
                var resp_pushod = PushReNewOrder(OrderData, this.Context);
                if (resp_pushod.IsSuccess)
                {
                    //var RefundResult = this.CreateRefund(OrderData, this.Context);
                    //if (!RefundResult.IsSuccess)
                    //{
                    //    this.Result.IsSuccess = false;
                    //    throw new BusinessException(RefundResult.ComplexMessage?.ToString());
                    //}
                    OrderData["fiszbrefund"] = 1;
                    //结算进度：已退款
                    OrderData["fsettlprogress"] = JieNor.AMS.YDJ.DataTransferObject.Enums.Enu_RenewalSettleProgress.已退款;
                    //已收
                    OrderData["freceivable"] = 0;
                    //未收款金额：成交总额
                    OrderData["funreceived"] = OrderData["fdealamount"];
                    //结算状态：全款未收
                    OrderData["freceiptstatus"] = "receiptstatus_type_01";
                    SetCloseState(OrderData);

                    dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                    dm.Save(OrderData);
                }
            }
            var swjOrders = e.DataEntitys.Where(a => Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("v6定制柜合同"));
            if (swjOrders != null && swjOrders.Count() > 0)
            {
                IMuSiService muSiService = this.Container.GetService<IMuSiService>();
                var orderFormMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                //实时调用接口将数据传到定制OMS中台。
                muSiService.OMSSyncAsync(this.Context, orderFormMeta, swjOrders);
            }

        }
        /// <summary>
        /// 焕新订单发起退款
        /// </summary>
        /// <param name="order"></param>
        /// <param name="ctx"></param>
        /// <returns></returns>
        private IOperationResult PushReNewOrder(DynamicObject order, UserContext ctx)
        {
            //调用 焕新订单发起退款
            var operationResult = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx,
            "ydj_order",
            new List<DynamicObject> { order },
            "RenewalRefund",
            new Dictionary<string, object>());
            return operationResult;
        }



        private IOperationResult CreateRefund(DynamicObject Order, UserContext ctx)
        {
            // 基于合同生成退款单，系统自动提交审核。（字段映射逻辑请看下方单据转换：销售合同->退款单）
            var incomeDisburseMeta = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var payOrder = incomeDisburseMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;

            var svc = this.Container.GetService<IComboDataService>();
            var datas = svc.GetFormComboDatas(this.Context, "coo_settledyn", "paymentdesc");

            payOrder["frenewalflag"] = true;
            payOrder["fdate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            payOrder["fway"] = "payway_15"; //焕新合同退款
            payOrder["fpurpose"] = "bizpurpose_06";  //订单退
            payOrder["fdirection"] = "direction_01";    //增
            payOrder["fbizdirection"] = "bizdirection_02";  //支出
            payOrder["fsourceformid"] = "ydj_order";
            payOrder["fsourcenumber"] = Order["fbillno"];
            payOrder["fsourceid"] = Order["id"];
            payOrder["fstaffid"] = Order["fstaffid"];
            payOrder["fdeptid"] = Order["fdeptid"];
            payOrder["fcustomerid"] = Order["fcustomerid"];
            // payOrder["fphone"] = order["fphone"]; 
            payOrder["famount"] = Order["fdealamount"];
            payOrder["paymentdesc"] = datas.Where(x => x.Name == "合同款").Select(x => x.Id).FirstOrDefault();   //合同款

            var result = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, incomeDisburseMeta.Id, new[] { payOrder }, "save",
                new Dictionary<string, object> { { "IgnoreValidateDataEntities", true }, { "NotAutoSubmit", true } });
            if (!result.IsSuccess) return result;

            result = this.Container.GetService<IHttpServiceInvoker>()
            .InvokeBillOperation(ctx, incomeDisburseMeta.Id, new[] { payOrder }, "submit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true } });

            if (!result.IsSuccess) return result;

            result = this.Container.GetService<IHttpServiceInvoker>()
                .InvokeBillOperation(ctx, incomeDisburseMeta.Id, new[] { payOrder }, "audit", new Dictionary<string, object> { { "IgnoreValidateDataEntities", true } });

            if (!result.IsSuccess) return result;

            return result;
        }



        //设置关闭状态
        private void SetCloseState(DynamicObject order)
        {
            var typename = Convert.ToString((order["fbilltype_ref"] as DynamicObject)?["fname"]);
            //V6 ⑤更新所有商品明细【定制订单进度】= 单据作废。
            var IsV6 = Convert.ToString(order["fbilltype"]) == "ydj_order_vsix" || typename == "v6定制柜合同";
            order["fclosestatus"] = CloseStatusConst.Manual;
            var fentrys = order["fentry"] as DynamicObjectCollection;
            foreach (var entry in fentrys)
            {
                entry["fclosestatus_e"] = CloseStatusConst.Manual;
                if (IsV6)
                {
                    entry["fomsprogress"] = "-1";
                }
            }
        }
    }

    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("synctooms")]
    public class SyncToOms : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype" });
        }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            var swjOrders = e.DataEntitys.Where(a => Convert.ToString((a["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("v6定制柜合同"));
            if (swjOrders != null && swjOrders.Count() > 0)
            {
                IMuSiService muSiService = this.Container.GetService<IMuSiService>();
                var orderFormMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                //实时调用接口将数据传到定制OMS中台。
                muSiService.OMSSyncAsync(this.Context, orderFormMeta, swjOrders);
            }
        }
    }
}
