using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：自动匹配价格
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getmatchprice")]
    public class GetAutoMatchPrice : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件  
        /// 销售价目 = 销售价目.销售价 where 销售部门=[销售合同].[销售部门],and 失效日在[销售合同][业务日期]内,and 销售价目=已审核未失效
        /// 成本价 = 采购价目.采购价 where 失效日在[销售合同].[业务日期]内, and 采购价目表=已审核未失效, 
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //获取商品id
            var productId = this.GetQueryOrSimpleParam<string>("productId", "");
            //获取请求JSON数据包 单据头-销售部门 业务日期
            var requestJsonStr = this.GetQueryOrSimpleParam<string>("requestData", "");
            JArray jData = null;
            if (string.IsNullOrWhiteSpace(requestJsonStr) == false)
            {
                jData = JArray.Parse(requestJsonStr);
            }
            if (jData == null || jData.Count <= 0 || productId.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            //单据头 - 销售部门
            var fdeptid = jData.Select(p => Convert.ToString(p["fdeptid"]["id"]))?.Where(p => !string.IsNullOrWhiteSpace(p))?.FirstOrDefault()?.ToString();
            //单据头 - 业务日期
            var stringdate = jData.Select(p => Convert.ToString(p["forderdate"]))?.FirstOrDefault();
            var forderdate = string.IsNullOrWhiteSpace(stringdate) ? null : (object)Convert.ToDateTime(stringdate);
            var sql = $@"select  fproductid =@fproductid, 
                               fsalprice = (
                               select top 1 t1.fsalprice from   t_ydj_price t
                               left join t_ydj_priceentry t1 on t.fid = t1.fid
                               where t.fforbidstatus='0'  --已禁用的筛选掉
                               and t.fdeptid = @fdeptid--销售价目 单据头 部门
                               and t1.fproductid = @fproductid --销售价目 单据体 商品
                               and t1.fstartdate <= @forderdate --销售价目 单据体 生效日期
                               and t1.fexpiredate >= @forderdate --销售价目 单据体 失效日期
                               and (t.fmainorgid = @fmainorgid or t.fmainorgid = @ftoporgid )--销售价目 企业标识
                               and t1.fconfirmstatus = 2),
							   fpurprice = (
                               select top 1 t3.fpurprice fpurprice from
                               t_ydj_purchaseprice t2 --on t.fproductid = t2.fproductid--采购价目表
                               left join t_ydj_purchasepriceentry t3 on t2.fid = t3.fid
                               where t2.fforbidstatus='0' --已禁用的筛选掉
                               and t2.fdeptid = @fdeptid --采购价目 单据头 部门
                               and t3.fproductid_e = @fproductid --采购价目 单据体 商品
                               and  t3.fstartdate_e <= @forderdate --采购价目 单据体 生效日期
                               and t3.fexpiredate_e >= @forderdate --采购价目 单据体 失效日期
                               and (t2.fmainorgid = @fmainorgid or t2.fmainorgid = @ftoporgid ) --采购价目 企业标识
                               and t3.fconfirmstatus = 2)";
            var sqlParam = new List<SqlParam>
                {
                    new SqlParam("ftoporgid", System.Data.DbType.String, this.Context.TopCompanyId),
                    new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                    new SqlParam("fproductid", System.Data.DbType.String, productId),
                    new SqlParam("fdeptid", System.Data.DbType.String, fdeptid),
                    new SqlParam("forderdate", System.Data.DbType.DateTime, forderdate),
                };
            List<Dictionary<string, string>> datas = new List<Dictionary<string, string>>();
            using (var dataReader = this.DBService.ExecuteReader(this.Context, sql.ToString(), sqlParam))
            {
                while (dataReader.Read())
                {
                    Dictionary<string, string> data = new Dictionary<string, string>();
                    data["fproductid"] = Convert.ToString(dataReader["fproductid"]);
                    data["fsalprice"] = Convert.ToString(dataReader["fsalprice"]);
                    data["fpurprice"] = Convert.ToString(dataReader["fpurprice"]);
                    datas.Add(data);
                }
            }
            this.Result.SrvData = datas;
            this.Result.IsSuccess = true;
        }
    }
}
