using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockPick;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("submitaftersale")]
    public class Push2Order : AbstractOperationServicePlugIn
    {
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fbilltype", "fproductid" });
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            //if (selectRowIds == null || selectRowIds.Count == 0) return;

            string errorMsg = "";
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    var entry = newData["fentry"] as DynamicObjectCollection;
            //    foreach (var item in entry)
            //    {
            //        if (selectRowIds.Any(a => a.Id == Convert.ToString(item["id"])))
            //        {
            //            if (Convert.ToDecimal(item["fbizpurinqty"]) == 0)
            //            {
            //                errorMsg = $"第{Convert.ToString(item["fseq"])}行商品【{Convert.ToString((item["fproductid_ref"] as DynamicObject)?["fname"])}】当前商品暂未收货入库，不允许发起售后，谢谢！";
            //                throw new BusinessException(errorMsg);
            //                return false;
            //            }
            //        }
            //    }
            //    return true;
            //}).WithMessage("{0}", (billObj, propObj) => errorMsg));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var iszbrefund = Convert.ToBoolean(newData["fiszbrefund"]);
                if (iszbrefund)
                {
                    errorMsg = $"当前合同总部已退款，不允许发起售后，请核查！";
                    throw new BusinessException(errorMsg);
                    return false;
                }
                else
                {
                    var entry = newData["fentry"] as DynamicObjectCollection;
                    foreach (var item in entry)
                    {
                        if (selectRowIds.Any(a => a.Id == Convert.ToString(item["id"])))
                        {
                            IDBService dbService = this.Container.GetService<IDBService>();
                            string sql = "select 1 from t_ydj_purchaseorder a with(nolock) inner join t_ydj_poorderentry b with(nolock) on a.fid=b.fid where a.fhqderstatus='03' AND b.fbizqty>0 AND b.fsourceentryid='" + Convert.ToString(item["id"]) + "'";
                            using (var dr = dbService.ExecuteReader(this.Context, sql))
                            {
                                if (!dr.Read())
                                {
                                    errorMsg = $"下游无有效已终审采购订单，无法发起售后，请核查！";
                                    throw new BusinessException(errorMsg);
                                    return false;
                                }
                            }
                        }
                    }
                }

                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var order = e.DataEntitys[0];

            string s1 = "";
        }

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    var eventData = e.EventData as BeforeGetConvertRuleData;
                    this.PrepareConvertRuleData(e.DataEntities, eventData);
                    break;
            }
        }

        /// <summary>
        /// 准备下推操作参数
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="eventData"></param>
        private void PrepareConvertRuleData(DynamicObject[] dataEntities, BeforeGetConvertRuleData eventData)
        {
            if (dataEntities.IsNullOrEmpty())
            {
                return;
            }


            eventData.RuleId = "ydj_order2ydj_order";
            //选中商品行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            string errorMsg = "";
            //过滤实收数量大于等于销售数量的明细行
            List<string> filterList = new List<string>();

            foreach (var dataEntity in dataEntities)
            {
                var poId = dataEntity["id"] as string;
                var billNo = dataEntity["fbillno"] as string;
                if (poId.IsEmptyPrimaryKey()) continue;

                var entries = dataEntity["fentry"] as DynamicObjectCollection;
                var selectedRows = entries.Where(t =>
                selectRowIds.Any(s => s.Id.Contains(Convert.ToString(t["id"])))).Select(x => new SelectedRow
                {
                    PkValue = poId,
                    BillNo = billNo,
                    EntityKey = "fentry",
                    EntryPkValue = Convert.ToString(x["id"])
                }).ToList();
                eventData.SelectedRows = selectedRows;
            }

        }



    }
}
