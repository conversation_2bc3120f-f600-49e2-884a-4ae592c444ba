using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("push2borrowgood")]
    public class BorrowGood : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntities, false);
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            new OrderCommon(this.Context).CheckTransferOrderApprovingAndHasShipperAgentByEntry(e.DataEntities, selectRowIds.Select(x => x.Id).ToArray());
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    var eventData = e.EventData as BeforeGetConvertRuleData;
                    this.PrepareConvertRuleData(e.DataEntities, eventData);
                    break;
            }
        }

        private void PrepareConvertRuleData(DynamicObject[] dataEntities, BeforeGetConvertRuleData eventData)
        {

            //获取主组织经销商
            var fentrys = dataEntities.SelectMany(t => t["fentry"] as DynamicObjectCollection);
            var agentIds = fentrys.Select(t => Convert.ToString(t["fshipperagentid"]).Trim()).ToList();
            var mainAgents = (new AppService.Service.AgentService()).GetMainAgentIds(this.Context, agentIds);
            var orderCommon = new OrderCommon(this.Context);
            var hasShipperEntry = orderCommon.HasShipperEntry(dataEntities[0]);
            if (eventData == null
                || (!eventData.RuleId.EqualsIgnoreCase("ydj_order2stk_inventorytransfer")
                    && !eventData.RuleId.EqualsIgnoreCase("ydj_order2stk_inventorytransferreq"))) return;

            if (dataEntities.IsNullOrEmpty())
            {
                throw new BusinessException("借货失败：请选择有效合同后重新操作！");
            }
            var lstSelPoEntryRows = new List<SelectedRow>();
            //优化按选中行来下推
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);
            foreach (var dataEntity in dataEntities)
            {
                var poId = dataEntity["id"] as string;
                var billNo = dataEntity["fbillno"] as string;
                if (poId.IsEmptyPrimaryKey()) continue;

                if (hasShipperEntry)
                {
                    var entries = dataEntity["fentry"] as DynamicObjectCollection;
                    var selectedRows = new List<SelectedRow>();
                    foreach (var item in entries)
                    {
                        var product = item["fproductid_ref"] as DynamicObject;
                        if (Convert.ToString(product["fsuiteflag"]).ToLower() == "false" || Convert.ToString(product["fsuiteflag"]) == "0")
                        {
                            var agentId = Convert.ToString(item["fshipperagentid"]).Trim();
                            var entryid = Convert.ToString(item["id"]);
                            var mainAgentId = "";
                            mainAgents.TryGetValue(agentId, out mainAgentId);
                            mainAgentId = mainAgentId.IsNullOrEmptyOrWhiteSpace() ? agentId : mainAgentId;
                            if ((mainAgents.ContainsKey(item["fshipperagentid"].ToString()) || mainAgentId == this.Context.Company)
                                && selectRowIds.Select(x => x.Id).ToList().Contains(entryid)
                                )
                            {
                                selectedRows.Add(
                                    new SelectedRow()
                                    {
                                        PkValue = poId,
                                        BillNo = billNo,
                                        EntityKey = "fentry",
                                        EntryPkValue = Convert.ToString(item["id"])
                                    });
                            }
                        }
                    }
                    lstSelPoEntryRows.AddRange(selectedRows);
                }
                else
                {
                    var entries = dataEntity["fentry"] as DynamicObjectCollection;
                    var selectedRows = new List<SelectedRow>();
                    foreach (var item in entries)
                    {
                        var product = item["fproductid_ref"] as DynamicObject;
                        if (Convert.ToString(product["fsuiteflag"]).ToLower() == "false" || Convert.ToString(product["fsuiteflag"]) == "0")
                        {
                            var entryid = Convert.ToString(item["id"]);
                            if (selectRowIds.Select(x => x.Id).ToList().Contains(entryid))
                            {
                                selectedRows.Add(
                                    new SelectedRow()
                                    {
                                        PkValue = poId,
                                        BillNo = billNo,
                                        EntityKey = "fentry",
                                        EntryPkValue = Convert.ToString(item["id"])
                                    });
                            }
                        }
                    }
                    lstSelPoEntryRows.AddRange(selectedRows);
                }
                if (lstSelPoEntryRows.Count == 0)
                {
                    throw new BusinessException("借货失败：请选择有效合同后重新操作！");
                }

                eventData.RuleId = hasShipperEntry ? "ydj_order2stk_inventorytransfer_shipper" : "ydj_order2stk_inventorytransfer";
                eventData.SelectedRows = lstSelPoEntryRows;

                var systemProfileService = this.Container.GetService<ISystemProfile>();
                var isEnabledTransferNotice = systemProfileService.GetSystemParameter(this.Context, "stk_stockparam", "fenabletransfernotice", false);
                if (isEnabledTransferNotice)
                {
                    eventData.RuleId = hasShipperEntry ? "ydj_order2stk_inventorytransferreq_shipper" : "ydj_order2stk_inventorytransferreq";
                }

                var isEnableSchedulePlatform = systemProfileService.GetSystemParameter(this.Context, "stk_stockparam", "fenablescheduleplatform", false);
                if (isEnableSchedulePlatform)
                {
                    eventData.RuleId = hasShipperEntry ? "ydj_order2stk_scheduleapply_shipper" : "ydj_order2stk_scheduleapply";
                }
            }
        }
    }
}
