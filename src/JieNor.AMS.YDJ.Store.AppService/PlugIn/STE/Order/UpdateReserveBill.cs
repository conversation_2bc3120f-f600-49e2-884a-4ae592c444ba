using System;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{




    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单信息（源单信息可能有变更，预留信息没变导致数据不一致）
    /// </summary> 
    public class UpdateReserveBill : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if(e.DataEntitys==null)
            {
                return;
            }

            List<string> sql = new List<string>();
            foreach (var item in e.DataEntitys)
            {
                sql.Add(@"/*dialect*/ update t set fsourcestatus = x.fstatus
                            from t_stk_reservebill as t with(nolock) 
                            inner join t_ydj_order x with(nolock) on x.fid='{0}' and t.fsourcepkid = x.fid and t.fsourcetype ='ydj_order' ".Fmt(item["Id"]));

                sql.Add(@"/*dialect*/ update t set fmaterialid=x.fproductid, fattrinfo=x.fattrinfo,fresultbrandid=x.fresultbrandid,fcustomdesc=x.fcustomdes_e
                            from t_stk_reservebillentry as t with(nolock) 
                            inner join (
			                            select   d.fname,c.fcreatedate, c.fbillno,c.fsourcenumber ,a.fattrinfo,b.fentryid,a.fproductid,a.fresultbrandid, a.fcustomdes_e
			                            from t_ydj_orderentry a with(nolock)
			                            inner join t_stk_reservebillentry b with(nolock) on a.fentryid=b.fsourceentryid  and a.fid=b.fsourceinterid
			                            inner join t_stk_reservebill c with(nolock) on b.fid=c.fid
			                            inner join T_BAS_ORGANIZATION d with(nolock) on c.fmainorgid=d.fid
			                            where c.fsourcetype='ydj_order'  and  a.fid='{0}' 
			                            ) x on t.fentryid = x.fentryid  ".Fmt(item["Id"]));
                 
            }

            var dbSvc = this.Container.GetService<IDBServiceEx>();
            dbSvc.ExecuteBatch(this.Context, sql);
        }


    }



    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("Save")]
    public class OrderSave : UpdateReserveBill
    { 
    }



    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("Submit")]
    public class OrderSubmit : UpdateReserveBill
    {
    }


    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("UnSubmit")]
    public class OrderUnSubmit : UpdateReserveBill
    {
    }


    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("Audit")]
    public class OrderAudit : UpdateReserveBill
    {
    }

    /// <summary>
    /// 销售合同保存、审核等操作：更新其对应的预留单的源单状态
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("UnAudit")]
    public class OrderUnAudit : UpdateReserveBill
    {
    }

}