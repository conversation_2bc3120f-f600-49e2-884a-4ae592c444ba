using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.OrderSettleDyn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.OrderSettleDyn
{
    /// <summary>
    /// 销售合同：收款或退款结算
    /// </summary>
    [InjectService]
    [FormId("ydj_ordersettledyn")]
    [OperationNo("Settle")]
    public class Settle : AbstractOperationServicePlugIn
    {
        public override void CreateObjectIdemotency(CreateObjectIdemotencyEventArgs e)
        {
            base.CreateObjectIdemotency(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            e.IdemotencyIds = new List<string>();

            foreach (var dataEntity in e.DataEntitys)
            {
                e.IdemotencyIds.Add(SecurityUtil.HashString(dataEntity.ToJson()));
            }
        }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var spService = this.Container.GetService<ISystemProfile>();

            e.Rules.Add(this.RuleFor("fbillhead", data => data["fsourceid"]).NotEmpty().WithMessage("fsourceid不能为空！"));

            //“销售员信息”必须符合以下要求
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return CheckDutyEntry2(newData);
            }).WithMessage(@"销售员信息【比例】不能为负数，且【比例】大于0时【销售员、销售部门】不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);

                if ((Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_06") ||
                    enableMustInputBankId && Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_11"))
                     && false == Convert.ToBoolean(newData["fissyn"]) //直营方式协同时不需要关注我方银行账号
                    && newData["fmybankid"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择我方银行！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);

                if ((Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_06") ||
                     enableMustInputBankId && Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_11"))
                    && Convert.ToBoolean(newData["fissyn"])
                    && newData["fsynbankid"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择收款账号！")); //直营方式时收款账号就是对方银行账号

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                decimal fsettleamount = Convert.ToDecimal(newData["fsettleamount"]);
                if (fsettleamount < 0.01M)
                {
                    return false;
                }
                return true;
            }).WithMessage("本次结算额必须大于0！"));

            //支付方式为“商场代收”，“代收单位”不能为空
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fway = Convert.ToString(newData["fway"]);
                var fcontactunitid = Convert.ToString(newData["fcontactunitid"]);
                if (fway.EqualsIgnoreCase("payway_13") && fcontactunitid.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("支付方式为“商场代收”，“代收单位”不能为空!"));

            var wayIds = new List<string>() { "payway_01", "payway_05" };
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fcontactunitid"].IsNullOrEmptyOrWhiteSpace() && newData["fimage"].IsNullOrEmptyOrWhiteSpace() && !wayIds.Contains(newData["fway"].ToString()))
                {
                    return false;
                }
                return true;
            }).WithMessage("选择了代收单位，必须上传凭证！"));

            string receiptMsg = "【收款小票号】必填！";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var mustinvoicenumber = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fmustinvoicenumber", false); //收款必填小票号
                var receiptNo = Convert.ToString(newData["freceiptno"]);
                var way = Convert.ToString(newData["fway"]);//支付方式
                if (receiptNo.IsNullOrEmptyOrWhiteSpace() && way != "payway_01")
                {
                    //收款小票号为空且支付方式不为：账户支付
                    if (mustinvoicenumber)
                    {
                        receiptMsg = "【收款小票号】必填！";
                        return false;
                    }
                }
                //else
                //{
                //    decimal money = Convert.ToDecimal(newData["fsettleamount"]);
                //    if (money > 0.01M)
                //    {
                //        var incomeObjs = this.Context.LoadBizDataByFilter("coo_incomedisburse", $" freceiptno='{receiptNo}' AND famount='{money}'");
                //        if (incomeObjs != null && incomeObjs.Any())
                //        {
                //            receiptMsg = "该笔收款金额的“收款小票号”录入重复，请检查！";
                //            return false;
                //        }
                //    }
                //}
                return true;
            }).WithMessage(receiptMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                decimal famount = Convert.ToDecimal(newData["famount"]);
                if (famount > 0 && famount < 0.01M)
                {
                    return false;
                }
                return true;
            }).WithMessage("结算金额必须大于0！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                decimal famount = Convert.ToDecimal(newData["famount"]);
                if (famount > 0 && newData["fway"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("如果填写了结算金额，则支付方式不能为空！"));

            e.Rules.Add(new Validation_Settle());
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var settleInfo = e.DataEntitys[0];

            //源单主键ID
            string fsourceid = Convert.ToString(settleInfo["fsourceid"]);
            decimal fsettleamount = Convert.ToDecimal(settleInfo["fsettleamount"]);
            decimal famount = Convert.ToDecimal(settleInfo["famount"]);
            string fcontactunitid = Convert.ToString(settleInfo["fcontactunitid"]);

            //源单信息（按需加载，性能优化）
            var sourceForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_order");
            var sourceOrder = this.Context.LoadBizBillHeadDataById("ydj_order", fsourceid,
                "fbillno,fphone,fstaffid,fcustomerid,fstatus,freceiptstatus,funreceived,fwithin,ftranid,flinkstaffid,fbilltype,forderdate");
            if (sourceOrder == null) throw new BusinessException($"{sourceForm.Caption}【{fsourceid}】不存在！");

            var spService = this.Container.GetService<ISystemProfile>();

            //销售单据（意向及合同）不审核可收款
            var canreceipt = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanreceipt", false);
            if (!canreceipt)
            {
                if (Convert.ToString(sourceOrder["fstatus"]) != "E")
                {
                    throw new BusinessException("所属销售合同并未审核，请审核后再结算！");
                }
            }

            // 获取是否可以超额收款参数
            var canexcess = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanexcess", false);
            if (!canexcess)
            {
                if (Convert.ToString(sourceOrder["freceiptstatus"]).EqualsIgnoreCase("receiptstatus_type_03"))
                {
                    throw new BusinessException($"{sourceForm.Caption}【{sourceOrder["fbillno"]}】的结算状态是【全款已收】，无法结算！");
                }
                if (fsettleamount > Convert.ToDecimal(sourceOrder["funreceived"]))
                {
                    throw new BusinessException($"本次结算额不允许大于{sourceForm.Caption}的未收金额，当前系统设置为不允许超额收款！");
                }
            }

            // 客户（按需加载，性能优化）
            var customerId = Convert.ToString(sourceOrder["fcustomerid"]);
            var customer = this.Context.LoadBizBillHeadDataById("ydj_customer", customerId, "fcoocompanyid,fcooproductid,fcoostate");
            if (customer == null) throw new BusinessException($"客户信息不存在！");

            //协同账户余额服务
            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();

            //代收单位
            var contactUnit = synAccountBalanceService.GetContactUnitById(this.Context, fcontactunitid);
            if (contactUnit != null)
            {
                settleInfo["fcontactunittype"] = contactUnit["ftype"];
            }

            //收支记录模型
            var htmlForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_incomedisburse");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            List<DynamicObject> incomeDisburses = new List<DynamicObject>();
            
            //超额收款转账金额，默认为0
            var transferamount = this.GetQueryOrSimpleParam<decimal>("transferamount", 0);

            //账户结算金额
            List<AccountInfo> accountSettle = new List<AccountInfo>();
            string accounts = this.GetQueryOrSimpleParam<string>("accounts");
            if (!accounts.IsNullOrEmptyOrWhiteSpace())
            {
                accountSettle = accounts.FromJson<List<AccountInfo>>() ?? new List<AccountInfo>();
            }
            var account = accountSettle.FirstOrDefault();
            var way = Convert.ToString(settleInfo["fway"]);
            if (way.EqualsIgnoreCase("payway_01"))
            {
                if (account == null || account.AccountId.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("请选择支付账号!");
                }

                //将本次的钱赋给账号
                account.SettleAmount = famount - transferamount;

                //检查账户余额
                synAccountBalanceService.CheckAccountBalance(this.Context, customer, true, new List<AccountInfo> { account });

                //账户支付
                incomeDisburses.Add(this.GetIncomeDisburseObject(htmlForm,
                    sourceForm, sourceOrder, customer, settleInfo, famount, account.AccountId));
            }
            else
            {
                incomeDisburses.Add(this.GetIncomeDisburseObject(htmlForm,
                    sourceForm, sourceOrder, customer, settleInfo, famount, ""));
            }

            //获取订单超额收款自动转账户余额参数
            var istransfer = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fistransfer", false);
            if (canexcess && istransfer)
            {
                var isalltransfer = this.GetQueryOrSimpleParam<bool>("isalltransfer", false);
                if (transferamount > 0)
                {
                    //当不是账户收款但超额收款时是否全部转入账户，是只用产生账户收款收支记录
                    if (!way.EqualsIgnoreCase("payway_01") && isalltransfer)
                    {
                        incomeDisburses.Clear();
                    }
                    else
                    {
                        incomeDisburses[0]["famount"] = famount - transferamount;
                    }
                    //支付方式!=账户支付,生成账户收款的收支记录
                    if (!way.EqualsIgnoreCase("payway_01"))
                    {
                        //新增一条账户收款的收支记录，支付方式、收款日期、代收单位、银行账号、款项说明、销售部门、备注等都与订单收款保持一致。
                        var accountId = way.EqualsIgnoreCase("payway_01") ? account.AccountId : "";
                        var adddIncome = this.GetIncomeDisburseObject(htmlForm, sourceForm, sourceOrder, customer, settleInfo, famount, accountId);
                        adddIncome["faccount"] = "settleaccount_type_01";
                        adddIncome["fdirection"] = "direction_01";
                        adddIncome["fpurpose"] = "bizpurpose_01";
                        adddIncome["famount"] = transferamount;
                        adddIncome["fsourcetranid"] = "";
                        adddIncome["fsourceid"] = "";
                        adddIncome["fsourcenumber"] = "";
                        adddIncome["fsourceformid"] = "ydj_customer";//源单类型为客户
                        incomeDisburses.Insert(0, adddIncome);
                    }
                }
            }

            //保存前预处理
            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.Context, htmlForm, incomeDisburses.ToArray(), OperateOption.Create());

            //模拟正常表单保存操作流程
            var saveResult = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.Context,
                htmlForm.Id,
                incomeDisburses,
                "save",
                new Dictionary<string, object>());
            saveResult?.ThrowIfHasError(true, "收支记录保存出现错误，请检查当前用户的权限！");

            // 处理销售机会
            HandleCustomerRecord(incomeDisburses, sourceOrder);

            this.Result.SimpleMessage = "结算成功，等待确认！";
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 检查“销售员信息明细”是否合法
        /// </summary>
        /// <param name="newData"></param>
        /// <returns></returns>
        private bool CheckDutyEntry2(DynamicObject newData)
        {
            // 不校验
            //if (this.FromPurchaseOrderSubmitAgent) return true;

            // 没有明细行时，不校验
            var entry = newData["fdutyentry"] as DynamicObjectCollection;
            if (entry == null || entry.Count <= 0) return true;

            foreach (DynamicObject item in entry)
            {
                var ratio = Convert.ToDecimal(item["fratio"]);
                var dutyId = Convert.ToString(item?["fdutyid"]);
                var deptId = Convert.ToString(item?["fdeptid_ed"]);

                if (ratio < 0)
                {
                    return false;
                }

                if (ratio > 0 && (dutyId.IsNullOrEmptyOrWhiteSpace() || deptId.IsNullOrEmptyOrWhiteSpace()))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 获取本地收支记录数据包
        /// </summary>
        private DynamicObject GetIncomeDisburseObject(HtmlForm htmlForm,
            HtmlForm sourceForm,
            DynamicObject sourceOrder,
            DynamicObject customer,
            DynamicObject settleInfo,
            decimal amount,
            string account = "")
        {
            var way = "";
            //如果是账户结算，则支付方式统一为“账户支付”
            if (!account.IsNullOrEmptyOrWhiteSpace())
            {
                way = "payway_01";
            }
            else
            {
                //如果是非账户结算，则以前端用户选择的支付方式为准
                way = Convert.ToString(settleInfo["fway"]);
                if (this.Context.IsDirectSale)
                {
                    way = Convert.ToString(settleInfo["frefundway"]);
                }
            }

            var companyId = customer["fcoocompanyid"] as string;
            var productId = customer["fcooproductid"] as string;

            DynamicObject billHead = new DynamicObject(htmlForm.GetDynamicObjectType(this.Context));

            //销售员取合同销售员
            billHead["fstaffid"] = sourceOrder["fstaffid"];

            billHead["fwithin"] = sourceOrder["fwithin"];
            billHead["paymentdesc"] = settleInfo["paymentdesc"];
            billHead["fdate"] = settleInfo["fdate"];
            billHead["fway"] = way;
            billHead["faccount"] = account;
            billHead["fsalecompany"] = this.Context?.Companys?.FirstOrDefault(t => t.CompanyId.EqualsIgnoreCase(this.Context.Company))?.CompanyName;
            billHead["fsalecompanyid"] = this.Context.Company;
            billHead["fcreatecompany"] = billHead["fsalecompany"]; //创建方默认等于销售方
            billHead["fcreatecompanyid"] = billHead["fsalecompanyid"];
            billHead["fpurpose"] = "bizpurpose_02";
            billHead["fbizstatus"] = "bizstatus_01";
            billHead["forderno"] = sourceOrder["fbillno"];
            billHead["fdirection"] = "direction_02";
            billHead["fbizdirection"] = "bizdirection_01";
            billHead["fcontactunittype"] = settleInfo["fcontactunittype"];
            billHead["fcontactunitid"] = settleInfo["fcontactunitid"];
            billHead["fverificstatus"] = "verificstatus_01";
            billHead["famount"] = amount;
            billHead["fimage"] = settleInfo["fimage"];
            billHead["fdescription"] = settleInfo["fdescription"];
            billHead["fbankcard"] = settleInfo["fbankcard"];
            billHead["fdeptid"] = settleInfo["fdeptid"];
            billHead["fmarketsettleno"] = settleInfo["fmarketsettleno"];
            billHead["fpayeraccount"] = settleInfo["fpayeraccount"];//付款方账号

            billHead["fmybankid"] = settleInfo["fmybankid"];
            billHead["fsynbankid"] = settleInfo["fsynbankid"];
            billHead["fsynbankname"] = settleInfo["fsynbankname"];
            billHead["fsynbanknum"] = settleInfo["fsynbanknum"];
            billHead["fsynaccountname"] = settleInfo["fsynaccountname"];

            //收支记录手机号 = 销售合同手机号
            //billHead["fcustomerphone"] = sourceOrder["fphone"];

            //后台字段
            billHead["fcustomerid"] = sourceOrder["fcustomerid"];
            billHead["fcoocompanyid"] = companyId;
            billHead["fcooproductid"] = productId;
            billHead["fsourcetranid"] = sourceOrder["ftranid"];
            billHead["fsourceid"] = sourceOrder["id"];
            billHead["fsourcenumber"] = sourceOrder["fbillno"];
            billHead["fsourceformid"] = sourceForm.Id;
            billHead["fisself"] = true;
            billHead["fissyn"] = false;
            billHead["foperationmode"] = (int)Enu_OperateMode.None;
            //收款小票号
            billHead["freceiptno"] = settleInfo["freceiptno"];

            var orderBillTypeid = sourceOrder["fbilltype"];
            if (!orderBillTypeid.IsNullOrEmptyOrWhiteSpace())
            {
                //var sqlParam = new List<SqlParam>
                //{
                //    new SqlParam("@fid", System.Data.DbType.String, orderBillTypeid)
                //};
                //var sqlText = $@"select fname from t_bd_billtype where fid=@fid";
                //var dbService = this.Context.Container.GetService<IDBService>();
                //using (var reader = dbService.ExecuteReader(this.Context, sqlText, sqlParam))
                //{
                //    if (reader.Read())
                //    {
                //        //单据类型
                //        billHead["forderbilltype"] = reader.GetValueToString("fname");
                //    }
                //}

                var svc = Context.Container.GetService<IBillTypeService>();
                var billTypeInfo = svc.GetBillTypeInfor(Context, orderBillTypeid.ToString ());
                //单据类型
                billHead["forderbilltype"] = billTypeInfo.fname;
            }
            //业务日期
            billHead["forderdate"] = sourceOrder["forderdate"]; 

            // 防重复提交增加交流流水号
            string tranId = Convert.ToString(settleInfo["ftranid"]);
            if (!tranId.IsNullOrEmptyOrWhiteSpace())
            {
                billHead["ftranid"] = tranId;
            }

            // 设置费用明细
            SetExpenseEntry(settleInfo, billHead, htmlForm);

            // 设置联合开单信息
            SetDutyEntry(settleInfo, billHead, htmlForm);

            return billHead;
        }

        /// <summary>
        /// 设置费用明细
        /// </summary>
        private void SetExpenseEntry(DynamicObject source, DynamicObject target, HtmlForm targetForm)
        {
            DynamicObjectCollection sourceEntryCollection = source["fexpenseentry"] as DynamicObjectCollection;
            DynamicObjectCollection targetEntryCollection = target["fexpenseentry"] as DynamicObjectCollection;
            var targetEntryEntity = targetForm.GetEntryEntity("fexpenseentry");

            if (sourceEntryCollection == null || sourceEntryCollection.Count <= 0 || targetEntryEntity == null || targetEntryCollection == null)
            {
                return;
            }

            var sourceList = sourceEntryCollection.Where(x => !string.IsNullOrWhiteSpace(Convert.ToString(x["fexpenseitemid"])) && Convert.ToDecimal(x["famount"]) > 0)
                                                 .ToList();

            if (sourceList == null || sourceList.Count <= 0)
            {
                return;
            }

            foreach (var sourceItem in sourceList)
            {
                DynamicObject targetItem = new DynamicObject(targetEntryEntity.DynamicObjectType);
                targetItem["fexpenseitemid"] = sourceItem["fexpenseitemid"];
                targetItem["famount"] = sourceItem["famount"];
                targetItem["fdescription"] = sourceItem["fdescription"];
                targetEntryCollection.Add(targetItem);
            }
        }

        /// <summary>
        /// 设置联合开单信息
        /// </summary>
        private void SetDutyEntry(DynamicObject source, DynamicObject target, HtmlForm targetForm)
        {
            DynamicObjectCollection sourceEntryCollection = source["fdutyentry"] as DynamicObjectCollection;
            DynamicObjectCollection targetEntryCollection = target["fdutyentry"] as DynamicObjectCollection;
            var targetEntryEntity = targetForm.GetEntryEntity("fdutyentry");

            if (sourceEntryCollection == null || sourceEntryCollection.Count <= 0 || targetEntryEntity == null || targetEntryCollection == null)
            {
                return;
            }

            var sourceList = sourceEntryCollection.Where(x => !string.IsNullOrWhiteSpace(Convert.ToString(x["fdutyid"]))).ToList();

            if (sourceList == null || sourceList.Count <= 0)
            {
                return;
            }

            foreach (var sourceItem in sourceList)
            {
                DynamicObject targetItem = new DynamicObject(targetEntryEntity.DynamicObjectType);
                targetItem["fismain"] = sourceItem["fismain"];
                targetItem["fdutyid"] = sourceItem["fdutyid"];
                targetItem["fdeptid_ed"] = sourceItem["fdeptid_ed"];
                targetItem["fratio"] = sourceItem["fratio"];
                targetItem["famount_ed"] = sourceItem["famount_ed"];
                targetItem["fdescription_ed"] = sourceItem["fdescription_ed"];
                var isContainsDeptPerfRatio = sourceItem.DynamicObjectType.Properties.ContainsKey("fdeptperfratio");
                if (isContainsDeptPerfRatio)
                {
                    targetItem["fdeptperfratio"] = sourceItem["fdeptperfratio"];
                }
                else
                {
                    targetItem["fdeptperfratio"] = 0M;
                }
                //如果只有一个销售员，当首款比例不等于100%，处理默认等于100%,分成金额不等于结算金额，处理默认等于结算金额
                if (sourceList.Count() == 1)
                {
                    if (Convert.ToDecimal(targetItem["fratio"]) != 100) targetItem["fratio"] = 100;
                    if (Convert.ToDecimal(targetItem["famount_ed"]) != Convert.ToDecimal(target["famount"]))
                    {
                        targetItem["famount_ed"] = Convert.ToDecimal(target["famount"]);
                    }
                }

                targetEntryCollection.Add(targetItem);
            }
        }

        /// <summary>
        /// 处理销售机会
        /// </summary>
        /// <param name="incomeDisburses">收支记录</param>
        /// <param name="sourceOrder">销售合同</param>
        private void HandleCustomerRecord(List<DynamicObject> incomeDisburses, DynamicObject sourceOrder)
        {
            if (incomeDisburses == null || !incomeDisburses.Any()) return;

            var orderNo = Convert.ToString(sourceOrder["fbillno"]);

            // 获取关联商机（按需加载，性能优化）
            var customerRecord = this.DBService.ExecuteDynamicObject(
                this.Context,
                "select top 1 fbillno from t_ydj_customerrecord with(nolock) where forderno=@forderno",
                new List<SqlParam>
                {
                    new SqlParam("@forderno", System.Data.DbType.String, orderNo)
                })
                ?.FirstOrDefault();
            if (customerRecord == null) return;

            var followerRecordFormDt = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord").GetDynamicObjectType(this.Context);

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            var deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            var staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;

            var followerRecordObjs = new List<DynamicObject>();

            foreach (var item in incomeDisburses)
            {
                // 添加跟进记录
                var followerRecordObj = new DynamicObject(followerRecordFormDt);

                // 客户、联系人、手机号从意向单获取
                followerRecordObj["fcustomerid"] = sourceOrder["fcustomerid"];
                followerRecordObj["fcontacts"] = sourceOrder["flinkstaffid"];
                followerRecordObj["fphone"] = sourceOrder["fphone"];

                followerRecordObj["ffollowtime"] = DateTime.Now;
                followerRecordObj["ffollowerid"] = this.Context.UserId;
                followerRecordObj["fdeptid"] = deptId;
                followerRecordObj["fstaffid"] = staffId;
                followerRecordObj["ftype"] = "6"; // 默认是其他

                // 金额从收支记录获取
                followerRecordObj["fdescription"] = $"发起收货款，金额￥{item["famount"]}";
                followerRecordObj["fobjecttype"] = "objecttype13"; // 收货款  

                // 关联到商机
                followerRecordObj["frelatedbilltype"] = "ydj_customerrecord";
                followerRecordObj["frelatedbillno"] = customerRecord["fbillno"];

                // 操作对象为收支记录
                followerRecordObj["fobjectid"] = item["id"];
                followerRecordObj["fobjectno"] = item["fbillno"];

                followerRecordObjs.Add(followerRecordObj);
            }

            this.Gateway.InvokeBillOperation(this.Context, "ydj_followerrecord", followerRecordObjs, "save", new Dictionary<string, object>());
        }
    }
}