using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Core.DataEntity.Customer;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Customer
{
    /// <summary>
    /// 客户：报备唯一性规则验证
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("validate")]
    public class Validate : BaseCustomerPlugin
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string phone = this.GetQueryOrSimpleParam("phone", "");
            string wechat = this.GetQueryOrSimpleParam("wechat", "");
            string type = this.GetQueryOrSimpleParam("type", "customertype_00");
            string name = this.GetQueryOrSimpleParam("name", "");
            string id = this.GetQueryOrSimpleParam("id", "");

            string errorMsg = null;

            this.Result.IsSuccess = CheckCustomerUniqueRule(id, type, phone, wechat, name, out errorMsg);
            this.Result.SimpleMessage = errorMsg;
        }
    }
}
