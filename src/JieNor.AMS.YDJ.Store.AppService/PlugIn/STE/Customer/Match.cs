using JieNor.AMS.YDJ.Core.DataEntity.Customer;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Customer
{
    /// <summary>
    /// 客户：匹配
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("match")]
    public class Match : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            string phone = this.GetQueryOrSimpleParam("phone", "");
            string wechat = this.GetQueryOrSimpleParam("wechat", "");
            string type = this.GetQueryOrSimpleParam("type", "customertype_00");
            string name = this.GetQueryOrSimpleParam("name", "");

            switch (type)
            {
                case "customertype_01":
                    if (name.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException("公司名称不能为空！");
                    }

                    break;
                case "customertype_00":
                    if (phone.IsNullOrEmptyOrWhiteSpace() && wechat.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException("手机号和微信号至少有一项不能为空！");
                    }

                    break;
                default:
                    throw new BusinessException("商机类型只能是个人或公司！");
            }

            var customerService = this.Container.GetService<ICustomerService>();

            var customerUniqueItem = new CustomerUniqueItem("test")
            {
                Name = name,
                Phone = phone,
                Wechat = wechat,
                Type = type
            };

            var customerInfo = customerService.GetCustomerInfo(this.Context,
                    new CustomerUniqueItem[] { customerUniqueItem },
                    new string[] { "fname", "fphone", "fwechat" });

            var item = new Dictionary<string, object>();

            var customerObj = customerInfo["test"];
            if (customerObj != null)
            {
                var formDt = this.HtmlForm.GetDynamicObjectType(this.Context);
                // 加载引用数据
                var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
                refMgr.Load(this.Context, formDt, customerObj, false);
                item = new Dictionary<string, object>
                {
                    {"id", customerObj["id"]},
                    {
                        "fcustomerid", new Dictionary<string, object>
                        {
                            {"id", customerObj["id"]},
                            {"fname", customerObj["fname"]},
                            {"fcontacts", customerObj["fcontacts"]},
                            {"fphone", customerObj["fphone"]}
                        }
                    },
                    {"fcontacts", customerObj["fcontacts"]},
                    {"fwechat", customerObj["fwechat"]},
                    {"fphone", customerObj["fphone"]},
                    {"fname", customerObj["fname"]},
                    {"faddress", customerObj["faddress"]},
                };

                var fchannelid = customerObj["fchannelid_ref"] as DynamicObject;
                item["fchannelid"] = new Dictionary<string, object>
                {
                    { "id", fchannelid?["id"] },
                    { "fname", fchannelid?["fname"] },
                    { "fnumber", fchannelid?["fnumber"] }
                };
                var fbuildingid = customerObj["fbuildingid_ref"] as DynamicObject;
                item["fbuildingid"] = new Dictionary<string, object>
                {
                    { "id", fbuildingid?["id"] },
                    { "fname", fbuildingid?["fname"] },
                    { "fnumber", fbuildingid?["fnumber"] }
                };
                var fsource = customerObj["fsource_ref"] as DynamicObject;
                item["fsource"] = new Dictionary<string, object>
                {
                    { "id", fsource?["id"] },
                    { "fname", fsource?["fenumitem"] },
                    { "fnumber", fsource?["id"] }
                };
                var fprovince = customerObj["fprovince_ref"] as DynamicObject;
                item["fprovince"] = new Dictionary<string, object>
                {
                    { "id", fprovince?["id"] },
                    { "fname", fprovince?["fenumitem"] },
                    { "fnumber", fprovince?["id"] }
                };
                var fcity = customerObj["fcity_ref"] as DynamicObject;
                item["fcity"] = new Dictionary<string, object>
                {
                    { "id", fcity?["id"] },
                    { "fname", fcity?["fenumitem"] },
                    { "fnumber", fcity?["id"] }
                };
                var fregion = customerObj["fregion_ref"] as DynamicObject;
                item["fregion"] = new Dictionary<string, object>
                {
                    { "id", fregion?["id"] },
                    { "fname", fregion?["fenumitem"] },
                    { "fnumber", fregion?["id"] }
                };
                var fgender = customerObj["fgender_ref"] as DynamicObject;
                item["fgender"] = new Dictionary<string, object>
                {
                    { "id", fgender?["id"] },
                    { "fname", fgender?["fenumitem"] },
                    { "fnumber", fgender?["id"] }
                };
                var fage = customerObj["fage_ref"] as DynamicObject;
                item["fage"] = new Dictionary<string, object>
                {
                    {"id", fage?["id"]},
                    {"fname", fage?["fenumitem"]},
                    {"fnumber", fage?["id"]}
                };
                item["fimage"] = customerObj["fimage"];
                item["fimage_txt"] = customerObj["fimage_txt"];
            }

            this.Result.SrvData = item;
            this.Result.IsSuccess = true;
        }
    }
}
