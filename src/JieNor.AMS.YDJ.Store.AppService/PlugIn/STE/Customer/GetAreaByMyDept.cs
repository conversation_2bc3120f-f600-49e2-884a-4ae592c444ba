using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Customer
{
    /// <summary>
    /// 销售机会：根据部门获取省市区
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("getareabymydept")]
    public class GetAreaByMyDept: AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            //获取当前员工所属部门
            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            string mydeptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
            ICustomerRecordService customerRecordService = this.Container.GetService<ICustomerRecordService>();
            var areaobj = customerRecordService.GetAreaByDept(this.Context, mydeptId);
            this.Result.IsSuccess = true;
            this.Result.SrvData = areaobj;
        }
    }
}
