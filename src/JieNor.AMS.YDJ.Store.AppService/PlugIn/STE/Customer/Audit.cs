using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Customer
{
    /// <summary>
    /// 客户：审核
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length < 1) return;

            ////this.SyncToK3Cloud(e.DataEntitys);
        }

        /// <summary>
        /// 同步到K3Cloud
        /// </summary>
        /// <param name="dataEntitys"></param>
        public void SyncToK3Cloud(DynamicObject[] dataEntitys)
        {
            var target = this.GetSyncTargetSEP();
            if (target == null) return;

            //加载引用数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), dataEntitys, false);

            var billData = this.GetCustomerSyncBillData(dataEntitys);

            //发起协同请求
            var responseResult = this.Gateway.Invoke(
                this.Context,
                target,
                new CommonBillDTO()
                {
                    FormId = "ydj_customer",
                    OperationNo = "SaveSynergy",
                    BillData = billData,
                    ExecInAsync = false,
                    AsyncMode = (int)Enu_AsyncMode.Background,
                    SimpleData = new Dictionary<string, string> { }
                }.SetOptionFlag((long)Enu_OpFlags.RequestToReply)) as CommonBillDTOResponse;

            responseResult?.OperationResult?.ThrowIfHasError(true, $"客户协同失败，对方系统未返回任何响应！");
        }

        /// <summary>
        /// 获取客户协同数据包
        /// </summary>
        /// <param name="dataEntitys"></param>
        /// <returns></returns>
        protected string GetCustomerSyncBillData(DynamicObject[] dataEntitys)
        {
            var billDatas = new List<Dictionary<string, object>>();

            foreach (var dataEntity in dataEntitys)
            {
                var citys = new List<string>();
                var province = Convert.ToString((dataEntity["fprovince_ref"] as DynamicObject)?["fenumitem"]);
                var city = Convert.ToString((dataEntity["fcity_ref"] as DynamicObject)?["fenumitem"]);
                var region = Convert.ToString((dataEntity["fregion_ref"] as DynamicObject)?["fenumitem"]);
                if (!province.IsNullOrEmptyOrWhiteSpace()) citys.Add(province);
                if (!city.IsNullOrEmptyOrWhiteSpace()) citys.Add(city);
                if (!region.IsNullOrEmptyOrWhiteSpace()) citys.Add(region);

                var billData = new Dictionary<string, object>();
                billData["ftranid"] = dataEntity["ftranid"];
                billData["fname"] = dataEntity["fname"];
                billData["fnumber"] = dataEntity["fnumber"];
                billData["fprovincecityregion"] = string.Join("/", citys);
                billData["fphone"] = dataEntity["fphone"];
                billData["faddress"] = dataEntity["faddress"];
                billDatas.Add(billData);
            }

            return billDatas.ToJson();
        }

        /// <summary>
        /// 获取唯一的总部直营模式的供应商协同目标地址
        /// </summary>
        /// <returns></returns>
        public TargetSEP GetSyncTargetSEP()
        {
            var supplierForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, supplierForm.GetDynamicObjectType(this.Context));

            var where = "fmainorgid=@fmainorgid and foperationmode='1'";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
            };
            var dataReader = this.Context.GetPkIdDataReader(supplierForm, where, sqlParam);
            var suppliers = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (suppliers == null || !suppliers.Any()) return null;

            if (suppliers.Count() > 1)
            {
                throw new BusinessException("当前存在多个总部直营的供应商，无法确定协同目标！");
            }

            var supplier = suppliers.FirstOrDefault();
            var cooCompanyId = Convert.ToString(supplier["fcoocompanyid"]);
            var cooProductId = Convert.ToString(supplier["fcooproductid"]);
            if (cooCompanyId.IsNullOrEmptyOrWhiteSpace() || cooProductId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"总部直营供应商【{supplier["fname"]}】的 fcoocompanyid 或 fcooproductid 为空，无法协同！");
            }

            return new TargetSEP(cooCompanyId, cooProductId);
        }
    }
}