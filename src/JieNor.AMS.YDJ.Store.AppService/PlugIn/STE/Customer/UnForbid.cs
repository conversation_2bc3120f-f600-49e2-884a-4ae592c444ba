using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Customer
{
    /// <summary>
    /// 客户:反禁用
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("unforbid")]
    public class UnForbid : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return CheckCustomerPhoneUnique(Convert.ToString(newData["id"]), Convert.ToString(newData["fphone"]), Convert.ToString(newData["fsrcstoreid"]));
            }).WithMessage("对不起，该手机号客户已存在，禁止操作！"));
        }

        /// <summary>
        /// 验证手机号是否存在
        /// </summary>
        /// <param name="id"></param> 
        /// <param name="phone"></param> 
        /// <returns></returns>
        public bool CheckCustomerPhoneUnique(string id, string phone,string storeid)
        {
            if (phone.IsNullOrEmptyOrWhiteSpace()) return true;
            string sql = $"select COUNT(1) from t_ydj_customer WITH(NOLOCK) where fmainorgid='{this.Context.Company}' and fforbidstatus='0' and fphone='{phone}' and fid<>'{id}'";

            var paramSer = this.Container.GetService<ISystemProfile>();
            var fcustomerunique = paramSer.GetSystemParameter<string>(this.Context, "bas_storesysparam", "fcustomerunique");//获取客户报备唯一性控制规则
            var uniqueFlag = fcustomerunique != null && fcustomerunique.IndexOf("store") > 0 && !storeid.IsNullOrEmptyOrWhiteSpace();
            if (uniqueFlag)
            {
                sql += $" and fsrcstoreid='{storeid}' ";
            }

            using (var reader = Container.GetService<IDBService>().ExecuteReader(this.Context, sql))
            {
                if (reader.Read())
                {
                    return Convert.ToInt32(reader[0]) == 0;
                }
            }
            return true;
        }
    }
}
