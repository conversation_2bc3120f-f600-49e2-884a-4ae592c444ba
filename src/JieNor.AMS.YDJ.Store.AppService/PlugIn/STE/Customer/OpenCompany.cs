using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Company;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Customer
{
    /// <summary>
    /// 开通企业
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("opencompany")]
    public class OpenCompany : AbstractOperationServicePlugIn
    {
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.DisableTransaction = true;
            e.OpCtlParam.IgnoreOpMessage = true;            
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            e.Rules.Add(this.RuleFor("fbillhead", billObj => billObj["fcontacts"] as string)
                .NotEmpty()
                .WithMessage("客户【{0}】创建企业失败：联系人不可以为空！",(billobj,contactName)=>billobj["fname"]));

            e.Rules.Add(this.RuleFor("fbillhead", billObj => billObj["fphone"] as string)
                .NotEmpty()
                .WithMessage("客户【{0}】创建企业失败：手机号不可以为空！", (billobj, phoneNo) => billobj["fname"]));


            e.Rules.Add(this.RuleFor("fbillhead", billObj => billObj["fcoostate"] as string)
                .IsTrue((billObj,cooState)=>!cooState.EqualsIgnoreCase("已协同"))
                .WithMessage("客户【{0}】创建企业失败：协同状态不能为已协同！", (billobj, phoneNo) => billobj["fname"]));
        }


        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {            
            if (HostConfigView.Site.Gateway.IsNullOrEmptyOrWhiteSpace()
                || HostConfigView.Site.GatewayTokenId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("当前系统未加入云链，无法创建企业！");
            }

            var serviceLineId = this.GetQueryOrSimpleParam<string>("serviceLineId");
            var initAdminPwd = this.GetQueryOrSimpleParam<string>("initAdminPwd");
            if (serviceLineId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("服务线路不能为空！");
            }

            if (initAdminPwd.IsNullOrEmptyOrWhiteSpace()
                || initAdminPwd.Length<6)
            {
                throw new BusinessException("初始化管理员密码不可以为空且长度不可以小于6个字符！");
            }

            int allCustomerObjs = e.DataEntitys.Length;
            int flag = 1;
            bool hasSuccess = false;
            foreach(var dataEntity in e.DataEntitys)
            {
                this.TaskProgressService.SetTaskProgressMessage(this.Context, this.TaskId, $"正在创建企业:{dataEntity["fname"]}……({flag}/{allCustomerObjs})");
                this.TaskProgressService.SetTaskProgressValue(this.Context, this.TaskId, flag * 1.0m * 100 / allCustomerObjs - 1);

                string companyId, productId;
                if (this.RegisterCompany(dataEntity,out companyId, out productId))
                {
                    hasSuccess = true;
                    //发起协同
                    Dictionary<string, object> dctOption = new Dictionary<string, object>();
                    dctOption["opmode"] = "2";
                    dctOption["customerid"] = dataEntity["id"];
                    dctOption["companyid"] = companyId;
                    dctOption["productid"] = productId;
                    var synResult = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new DynamicObject[] { dataEntity }, "synCustomer", dctOption);
                    if (synResult?.IsSuccess == true)
                    {
                        this.Result.ComplexMessage.SuccessMessages.Add($"客户【{dataEntity["fname"]}】发送协同邀请成功！");
                    }
                    this.Result.MergeResult(synResult);
                }
            }
            this.Result.IsSuccess = hasSuccess;
        }


        private bool RegisterCompany(DynamicObject dataEntity, out string companyId, out string productId)
        {
            companyId = "";
            productId = "";

            RegisterCompanyDTO dto = new RegisterCompanyDTO();
            dto.CompanyName = dataEntity["fname"] as string;
            dto.Province = dataEntity["fprovince"] as string;
            dto.City = dataEntity["fcity"] as string;
            dto.Region = dataEntity["fregion"] as string;
            dto.CompanyScale = "";
            dto.UserName = dataEntity["fcontacts"] as string;
            dto.UserNumber = dataEntity["fphone"] as string;
            dto.ProductHostId = this.GetQueryOrSimpleParam<string>("serviceLineId");
            dto.Password = this.GetQueryOrSimpleParam<string>("initAdminPwd");
            
            
            var targetSEP = TargetSEP.EisService;
            var result = this.Gateway.Invoke(null, targetSEP, new CommonFormDTO()
            {
                FormId = "eis_company",
                OperationNo = "register",
                OperationName = "注册企业",
                SimpleData = new Dictionary<string, string>()
                {
                    {
                        "companyInfo",dto.ToJson()
                    }
                }
            });

            if (result is DynamicDTOResponse)
            {
                var regResult = (result as DynamicDTOResponse).OperationResult;
                regResult.SimpleData?.TryGetValue("companyId", out companyId);
                regResult.SimpleData?.TryGetValue("productId", out productId);
                if (regResult.IsSuccess)
                {
                    this.Result.ComplexMessage.SuccessMessages.Add($"客户【{dataEntity["fname"]}】创建协同企业成功！");
                }
                this.Result.MergeResult(regResult);
                return (result as DynamicDTOResponse).OperationResult?.IsSuccess == true;
            }
            return false;
        }
    }
}
