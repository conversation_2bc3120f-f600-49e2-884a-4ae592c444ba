using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Customer
{
    /// <summary>
    /// 公海客户列表
    /// </summary>
    [InjectService]
    [FormId("ydj_customer")]
    [OperationNo("commoncus")]
    public class CommonCus : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //自定义弹出框名称，拿系统封装前的代码指定formName
            var metaSrv = this.Context.Container?.TryGetService<IMetaModelService>();
            var formMeta = metaSrv.LoadFormModel(this.Context, this.HtmlForm.Id);
            List<FilterSchemeObject> filterSchemes = new List<FilterSchemeObject>();
            var schemeService = this.Context.Container.GetService<IFilterSchemeService>();
            filterSchemes = schemeService.LoadFilterScheme(this.Context, formMeta?.Id);
            List<ColumnObject> lstColumns = new List<ColumnObject>();
            foreach (var field in formMeta.GetFieldList())
            {
                lstColumns.AddRange(field.ToListColumn(this.Context));
            }
            var filterString = "fispublish=1";

            var action = this.Context.ShowForm(formMeta,
                null,
                this.CurrentPageId,
                Enu_DomainType.List,
                Enu_OpenStyle.Modal,
                null,
                (formPara) =>
                {
                    var listFormPara = formPara as ListShowParameter;
                    if (listFormPara == null) return;
                    listFormPara.ListColumns = lstColumns.OrderBy(k => k.ListTabIndex).ToList();
                    listFormPara.FilterSchemes = filterSchemes;
                    listFormPara.FilterViewStyle = "Default";
                    listFormPara.FormCaption = "公海客户";
                    if (!filterString.IsNullOrEmptyOrWhiteSpace())
                    {
                        listFormPara.FilterString = " ( {0} ) ".Fmt(filterString);
                    }
                    listFormPara.DynamicParam = "";
                    listFormPara.ListMode = Enu_ListMode.Default;
                });
            this.Result.HtmlActions.Add(action);
            this.Result.IsShowMessage = false;
        }
    }
}
