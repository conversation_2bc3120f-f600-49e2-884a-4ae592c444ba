using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.CashBill
{
    /// <summary>
    /// 不存在直接的单据关联规则关系，但又需要向单据转换一样的原理去反写某个信息
    /// 例如这里的，收银单与客户资料上账户余额的关系。
    /// </summary>
    [InjectService]
    [FormId("ydj_cash")]
    [OperationNo("#")]
    public class WritebackAccountBalance : AbstractWritebackServicePlugIn
    {
        /// <summary>
        /// 处理关联反写动作逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeWriteback(BeforeWritebackEventArgs e)
        {
            //能进到这里的，说明当前单据一定不是关联生成的，一定是手工新增的
            //取消引擎内置处理
            e.Cancel = true;
            Dictionary<string, decimal> dctCustBalanceData = new Dictionary<string, decimal>();
            //todo:收集当前单据的数据包余额变化信息
            var custField = this.HtmlForm.GetField("fcustomerid");
            var allCustId = this.DataEntities.Select(o => custField.DynamicProperty.GetValue<string>(o));

            //统一采用重算机制，如果一个客户的一年消费记录在300笔以下，适用此算法
            //否则需要进行优化算法。
            var customerService = this.Context.Container.GetService<ICustomerBalanceService>();
            customerService.CorrectAccountBalance(this.Context, allCustId);
        }
    }
}
