using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Leads
{
    ///// <summary>
    /// 销售线索：转为销售机会
    /// </summary>
    [InjectService]
    [FormId("ydj_leads")]
    [OperationNo("leadstochance")]
    public class LeadsToChance: AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 过滤不符合条件的数据
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            e.Rules.Add(this.RuleFor("fbillhead", (dataObj) => dataObj["fleadsstatus"] as string)
                .IsTrue((dataObj, leadStatus) => 
                    !leadStatus.EqualsIgnoreCase("leads_status_01") &&
                    !leadStatus.EqualsIgnoreCase("leads_status_03") &&
                    !leadStatus.EqualsIgnoreCase("leads_status_04"))
                .WithMessage("线索转商机失败：{0}，业务状态必须为【已分配】才可转换！", (dataObj, leadStatus) => dataObj["fbillno"]));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null)
            {
                throw new BusinessException("请至少选中一条有效的线索进行转销售机会操作！");
            }
            if (e.DataEntitys.Length == 0) return;

            List<SelectedRow> lstSelRows = new List<SelectedRow>();
            foreach (var dataEntity in e.DataEntitys)
            {
                lstSelRows.Add(new SelectedRow()
                {
                    PkValue = Convert.ToString(dataEntity["Id"]),
                    BillNo = Convert.ToString(dataEntity["fbillno"]),
                });
            }
            var convertService = this.Container.GetService<IConvertService>();

            var result = convertService.Push(this.Context, new BillConvertContext()
            {
                RuleId = "ydj_leads2ydj_customerrecord",
                SourceFormId = this.HtmlForm.Id,
                TargetFormId = "ydj_customerrecord",
                SelectedRows = lstSelRows.ToConvertSelectedRows(),
                Option = this.Option
            });

            var targetDataObjects = (result.SrvData as ConvertResult)?.TargetDataObjects?.ToArray();

            if (targetDataObjects != null && targetDataObjects.Length > 0)
            {
                var metaModelService = this.Container.GetService<IMetaModelService>();
                var crHtmlForm = metaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
                var crdm = this.Container.GetService<IDataManager>();
                crdm.InitDbContext(this.Context, crHtmlForm.GetDynamicObjectType(this.Context));

                var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(this.Context, crHtmlForm, targetDataObjects, OperateOption.Create());

                crdm.Save(targetDataObjects);

                List<DynamicObject> lstSuccessConvertLeadObjs = new List<DynamicObject>();
                foreach(var targetDataObj in targetDataObjects)
                {
                    var sourceLeadBillNo = targetDataObj["fleadssource"] as string;
                    var sourceLeadObj = e.DataEntitys.FirstOrDefault(o => Convert.ToString(o["fbillno"]).EqualsIgnoreCase(sourceLeadBillNo));
                    if (sourceLeadObj != null)
                    {
                        sourceLeadObj["fleadsstatus"] = "leads_status_03";
                        lstSuccessConvertLeadObjs.Add(sourceLeadObj);
                    }
                }

                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                dm.Save(lstSuccessConvertLeadObjs);
                this.Result.IsSuccess = true;
                this.Result.SimpleMessage = "转换成功!";
                this.Result.SrvData = new { id = targetDataObjects[0]["id"] };
            }
            else
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = "转换失败!";
            }
        }
    }
}
