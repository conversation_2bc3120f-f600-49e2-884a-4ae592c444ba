using JieNor.AMS.YDJ.Core.DataEntity.Models;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Leads
{
    /// <summary>
    /// 销售线索：查询列表
    /// </summary>
    [InjectService]
    [FormId("ydj_leads")]
    [OperationNo("getList")]
    public class GetLeads : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "buildQueryParameter":
                    buildQueryParameter(e);
                    break;
                case "changeQueryParameter":
                    changeQueryParameter(e);
                    break;
                case "changeResult":
                    changeResult(e);
                    break;
                default:
                    return;
            }
        }

        private void changeResult(OnCustomServiceEventArgs e)
        {
            var srvData = e.EventData as Dictionary<string, object>;
            if (srvData == null || srvData.Count <= 0)
            {
                return;
            }

            var listData = srvData["datas"] as List<Dictionary<string, object>>;
            if (listData == null || listData.Count <= 0)
            {
                return;
            }

            //保护列表客户信息
            var protecteDataService = this.Container.GetService<IProtecteDataService>();
            protecteDataService.Init(this.Context, "bas_storesysparam", "fenableprotectecusinfo");
            protecteDataService.EmptyFields(new[] { "fwechat" }, listData);
            protecteDataService.MaskFields(new[] { "fphone" }, listData, 3, 4);
        }

        private void buildQueryParameter(OnCustomServiceEventArgs e)
        {
            string searchText = this.GetQueryOrSimpleParam<string>("searchText");
            List<SqlParam> dynamicParams = new List<SqlParam>();
            string filterString = null;

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                filterString = "(fname like @searchText or fphone like @searchText)";
                dynamicParams.Add(new SqlParam("searchText", System.Data.DbType.String, $"%{searchText}%"));
            }
            e.Result = new Dictionary<string, object>
            {
                { "dynamicParams",dynamicParams},
                { "filterString",filterString}
            };
        }

        private void changeQueryParameter(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Dictionary<string, object>;
            if (eventData == null)
            {
                return;
            }
            var para = eventData["para"] as SqlBuilderParameter;

            if (para == null)
            {
                return;
            }

            Filters filters = new Filters();

            filters.Enums.Add(new EnumFilters
            {
                FieldId = "fcustomersource",
                MultiSelect = false,
                Operator = "="
            });

            Colation colation = new Colation();
            colation.Options = new List<ColationOption>
            {
                new ColationOption { Id="myLeads",Name="我的销售线索" },
                new ColationOption { Id="leadsPool",Name="线索池"},
                new ColationOption { Id="allLeads",Name="全部销售线索"}
            };
            colation.Default = "myLeads";
            colation.Selected = this.GetQueryOrSimpleParam<string>("colationOptionId");

            string s = null;
            if (string.IsNullOrWhiteSpace(colation.Selected))
            {
                colation.Selected = colation.Default;
            }
            IBaseFormProvider baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            var staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;
            if (string.IsNullOrWhiteSpace(staffId))
            {
                throw new BusinessException("对不起，您还没绑定员工，请用手机号码绑定员工后再使用!");
            }
            switch (colation.Selected)
            {
                //我的线索
                case "myLeads":
                    s = $"(fdutyid='{staffId}')";
                    break;
                    //线索池
                case "leadsPool":
                    s = "fleadsstatus='leads_status_01'";
                    break;
                    //全部销售线索
                case "allLeads":
                    s = $"(fdutyid='{staffId}' or fleadsstatus = 'leads_status_01')";
                    break;
                default:
                    throw new BusinessException("colation非法输入!");
            }
            para.FilterString = string.IsNullOrWhiteSpace(para.FilterString) ? s : para.FilterString.JoinFilterString(s);

            e.Result = new Dictionary<string, object>
            {
                { "filters",filters},
                { "colation",colation}
            };
        }
    }
}
