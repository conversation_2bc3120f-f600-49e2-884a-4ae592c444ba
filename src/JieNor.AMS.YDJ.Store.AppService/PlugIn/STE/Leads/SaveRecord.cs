using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Leads
{
    /// <summary>
    /// 表单记录保存
    /// </summary>
    [InjectService]
    [FormId("bd_record")]
    [OperationNo("savelog")]
    public class SaveLog : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var allDataEntityObjs = e.DataEntitys.Where(o => Convert.ToString(o["fbizformid"]).EqualsIgnoreCase("ydj_leads"))
                .ToArray();
            if (allDataEntityObjs.Any() == false) return;

            var allLinkBillIds = allDataEntityObjs.Select(o => o["fbizobjid"] as string)
                .Where(o => !o.IsEmptyPrimaryKey())
                .Distinct();

            var linkFormMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_leads");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, linkFormMeta.GetDynamicObjectType(this.Context));
            var allLinkBillObjs = dm.Select(allLinkBillIds)
                .OfType<DynamicObject>();

            //var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            //var myStaffInfo = baseFormProvider.GetMyStaff(this.Context);
            var lastTime = DateTime.Now;
            foreach(var linkBillObj in allLinkBillObjs)
            {
                linkBillObj["ffollowtime"] = lastTime;
                linkBillObj["ffollowerid"] = this.Context.UserId;
            }
            dm.Save(allLinkBillObjs);
            //this.AddRefreshPageAction();
            this.Context.SetValue(this.Result, linkFormMeta, "ffollowerid", this.Context.UserId, null, this.CurrentPageId);
            this.Context.SetValue(this.Result, linkFormMeta, "ffollowtime", lastTime, null, this.CurrentPageId);
        }

    }
}
