using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Leads
{
    ///// <summary>
    /// 销售线索：列表查询数据
    /// </summary>
    [InjectService]
    [FormId("ydj_leads")]
    [OperationNo("QueryData")]
    public class QueryData: AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);
            switch (e.EventName)
            {
                case "afterListData":
                    afterListData(e);
                    break;
            }
        }

        /// <summary>
        /// 处理列表查询的数据
        /// </summary>
        /// <param name="e"></param>
        private void afterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;
            if (listData == null || listData.Count <= 0)
            {
                return;
            }

            //保护列表客户信息
            var protecteDataService = this.Container.GetService<IProtecteDataService>();
            protecteDataService.Init(this.Context, "bas_storesysparam", "fenableprotectecusinfo");
            protecteDataService.EmptyFields(new[] { "fwechat" }, listData);
            protecteDataService.MaskFields(new[] { "fphone" }, listData, 3, 4);
        }
    }
}
