using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Leads
{
    /// <summary>
    /// 销售线索：获取移动端详情页面按钮
    /// </summary>
    [InjectService]
    [FormId("ydj_leads")]
    [OperationNo("GetMenuMeta")]
    public class GetMenuMeta: AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var results = this.Result.SrvData as List<JObject>;


            if (results == null || results.Count <= 0 || e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            //获取详情数据
            var dataEntity = e.DataEntitys[0];

            //未分配且有权限的保留删除按钮(注：权限已在GetMenuMeta的OperationService中处理)
            if (Convert.ToString(dataEntity["fleadsstatus"]) == "leads_status_01")
            {
                return;
            }

            var deleteOperation = results.FirstOrDefault(x => x.GetJsonValue("opcode", string.Empty).EqualsIgnoreCase("delete"));
            if (deleteOperation != null)
            {
                results.Remove(deleteOperation);
            }
        }
    }
}
