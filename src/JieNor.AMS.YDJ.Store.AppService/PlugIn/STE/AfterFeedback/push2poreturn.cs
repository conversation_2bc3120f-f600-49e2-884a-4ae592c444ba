using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.IoC;
using JieNor.Framework.CustomException;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.Consts;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 售后反馈退货操作插件
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("push2poreturn")]
    public class ReturnGood : AbstractOperationServicePlugIn
    {
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    var eventData = e.EventData as BeforeGetConvertRuleData;
                    this.PrepareConvertRuleData(e.DataEntities, eventData);
                    break;
            }
        }

        /// <summary>
        /// 准备下推操作参数
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="eventData"></param>
        private void PrepareConvertRuleData(DynamicObject[] dataEntities, BeforeGetConvertRuleData eventData)
        {
            if (eventData == null
                || (!eventData.RuleId.EqualsIgnoreCase("stk_postockin2stk_postockreturn")
                    && !eventData.RuleId.EqualsIgnoreCase("stk_postockin2pur_returnnotice"))) return;
            //获取退货方式
            var returnType = this.GetQueryOrSimpleParam<string>("returnType", "");
            var actualReturnAmount = this.GetQueryOrSimpleParam<decimal>("refundAmount", 0m);
            var returnReason = this.GetQueryOrSimpleParam<string>("returnReason", "");

            var returnGoodData = this.GetQueryOrSimpleParam<string>("returnData", "")
                .FromJson<IEnumerable<Dictionary<string, object>>>(true)
                .ToList();
            if (returnType.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("退货失败：请先指定退货方式（正常退换/退货退款）！");
            }
            if (returnGoodData.IsNullOrEmpty())
            {
                throw new BusinessException("退货失败：请至少选择一行商品明细进行退货！");
            }

            eventData.Option.SetVariableValue("returnType", returnType);
            eventData.Option.SetVariableValue("returnReason", returnReason);
            eventData.Option.SetVariableValue("returnData", returnGoodData);
            eventData.Option.SetVariableValue("refundAmount", actualReturnAmount);

            var lstSelPoEntryRows = new List<SelectedRow>();
            Dictionary<string, Dictionary<string, object>> dctActualReturnInfo = new Dictionary<string, Dictionary<string, object>>();
            foreach (var mtrlObj in returnGoodData)
            {
                var poId = mtrlObj.GetString("pkValue");
                var poEntryId = mtrlObj.GetString("entryPkValue");
                var entityKey = mtrlObj.GetString("entityKey");
                if (poId.IsNullOrEmptyOrWhiteSpace()) continue;

                lstSelPoEntryRows.Add(new SelectedRow()
                {
                    PkValue = poId,
                    EntityKey = entityKey,
                    EntryPkValue = poEntryId,
                });
            }
            if (lstSelPoEntryRows.Count == 0)
            {
                throw new BusinessException("退货失败：所退商品明细数据不明确！");
            }

            eventData.RuleId = "stk_postockin2stk_postockreturn";
            eventData.SelectedRows = lstSelPoEntryRows;

            if (this.HtmlForm.Id.Equals("ydj_purchaseorder") && (dataEntities != null && dataEntities.Any()))
            {
                var isEnableNotice = Convert.ToBoolean(dataEntities[0]["fenablenotice"]);
                if (isEnableNotice)
                {
                    eventData.RuleId = "stk_postockin2pur_returnnotice";
                }
            }
        }

        /// <summary>
        /// 操作前处理事件
        /// </summary>
        /// <param name="e"></param>
        //public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        //{
        //    if (e.DataEntitys == null)
        //    {
        //        throw new BusinessException("退货失败：采购订单不存在，尝试重新打开此单据再操作！");
        //    }

        //    //获取退货方式
        //    var returnType = this.GetQueryOrSimpleParam<string>("returnType", "");
        //    var actualReturnAmount = this.GetQueryOrSimpleParam<decimal>("refundAmount", 0m);
        //    var returnReason = this.GetQueryOrSimpleParam<string>("returnReason", "");

        //    var returnGoodData = this.GetQueryOrSimpleParam<string>("returnData", "")
        //        .FromJson<IEnumerable<Dictionary<string, object>>>(true)
        //        .ToList();
        //    if (returnType.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        throw new BusinessException("退货失败：请先指定退货方式（正常退换/退货退款）！");
        //    }
        //    if (returnGoodData.IsNullOrEmpty())
        //    {
        //        throw new BusinessException("退货失败：请至少选择一行商品明细进行退货！");
        //    }

        //    this.Option.SetVariableValue("returnType", returnType);
        //    this.Option.SetVariableValue("returnReason", returnReason);
        //    this.Option.SetVariableValue("returnData", returnGoodData);
        //    this.Option.SetVariableValue("refundAmount", actualReturnAmount);

        //    var lstSelPoEntryRows = new List<SelectedRow>();
        //    Dictionary<string, Dictionary<string, object>> dctActualReturnInfo = new Dictionary<string, Dictionary<string, object>>();
        //    foreach(var mtrlObj in returnGoodData)
        //    {
        //        var poId = mtrlObj.GetString("pkValue");
        //        var poEntryId = mtrlObj.GetString("entryPkValue");
        //        var entityKey = mtrlObj.GetString("entityKey");
        //        if (poId.IsNullOrEmptyOrWhiteSpace()) continue;

        //        lstSelPoEntryRows.Add(new SelectedRow()
        //        {
        //            PkValue = poId,                    
        //            EntityKey = entityKey,
        //            EntryPkValue = poEntryId,
        //        });
        //    }
        //    if (lstSelPoEntryRows.Count == 0)
        //    {
        //        throw new BusinessException("退货失败：所退商品明细数据不明确！");
        //    }

        //    //检查目标单新增操作权限
        //    var permSrv = this.Container.GetService<IPermissionService>();
        //    permSrv?.CheckPermission(this.Context, new PermAuth(this.Context)
        //    {
        //        FormId = "stk_postockreturn",
        //        OperationName = "退货",
        //        PermId = PermConst.PermssionItem_New,
        //    });

        //    var pushService = this.Container.GetService<IConvertService>();
        //    var pushResult = pushService.Push(this.Context, new BillConvertContext()
        //    {
        //        SourceFormId = "stk_postockin",
        //        TargetFormId = "stk_postockreturn",
        //        RuleId = "stk_postockin2stk_postockreturn",
        //        Option = this.Option,
        //        SelectedRows = lstSelPoEntryRows.ToConvertSelectedRows()
        //    });

        //    pushResult.ThrowIfHasError(true, "退货失败：生成退货单过程出现未知错误！");

        //    var convertResult = pushResult.SrvData as ConvertResult;

        //    var dctOption = this.Option.ToDictionary(k => k.Key, v => v.Value);
        //    convertResult.Option.Merge(dctOption ?? new Dictionary<string, object>());
        //    convertResult.NoSaveRelation = true;
        //    convertResult.MockUISave = true;
        //    var saveResult = pushService.SaveConvertData(this.Context, convertResult, true);
        //    saveResult.ThrowIfHasError(true, "退货失败：生成的采购退货单保存失败！");

        //    this.Result.MergeResult(saveResult);
        //}
    }
}
