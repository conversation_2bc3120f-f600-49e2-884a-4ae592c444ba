using JieNor.AMS.YDJ.Core.DataEntity.Models;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 销售合同：联查
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("LinkFormSearch")]
    public class LinkFormSearch : LinkFormSearchBase
    {
        protected override void DealLinkForm(UserContext userContext, DynamicObject[] dataEntities, List<Dictionary<string, object>> linkFormDatas)
        {
            base.DealLinkForm(userContext, dataEntities, linkFormDatas);
            var pkids = dataEntities.Select(o => o["id"]?.ToString()).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (pkids.Count <= 0) return;
            //过滤条件
            var filterStr = "";
            var metaModelService = userContext.Container.GetService<IMetaModelService>();


            //收支记录
            var incomeDisburseForm = metaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            filterStr = pkids.Count == 1 ? $"fsourceformid='{this.HtmlForm.Id}' and fsourceid='{pkids[0]}'" :
                                               $"fsourceformid='{this.HtmlForm.Id}' and fsourceid in ({string.Join(",", pkids.Select(x => $"'{x}'"))})";
            linkFormDatas.Add(new Dictionary<string, object>
            {
                { "formId", incomeDisburseForm.Id },
                { "formCaption", incomeDisburseForm.Caption },
                { "flag", "nextForm" },
                { "filterString", filterStr },
                { "visible",1}
            });

            //联查采购退货单
            LinkPoStockReturn(dataEntities, metaModelService, linkFormDatas);

            //联查销售退货单
            LinkSoStockReturn(dataEntities, metaModelService, linkFormDatas);



        }


        /// <summary>
        /// 联查采购退货单
        /// </summary>
        private void LinkPoStockReturn(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var id = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            var strSql = $@"select distinct fid from t_stk_postockreturnentry with(nolock) where ffeedbackinterid ='{id[0]}'";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fid"].ToString());
                }
            }
            var purFlag = -1;
            if (fids.Count == 0)
            {
                for (int i = 0; i < linkFormDatas.Count; i++)
                {
                    foreach (var val in linkFormDatas[i])
                    {
                        if (val.Value.ToString() == "stk_postockreturn")
                        {
                            purFlag = i;
                            linkFormDatas[purFlag]["filterString"] = " 1=2";
                            return;
                        }
                    }
                }
                return;
            }

            //找到采购订单下标用于修改
            for (int i = 0; i < linkFormDatas.Count; i++)
            {
                foreach (var val in linkFormDatas[i])
                {
                    if (val.Value.ToString() == "stk_postockreturn")
                    {
                        purFlag = i;
                        goto ModifyData;
                    }
                }
            }
        ModifyData:
            if (purFlag < 0)
            {
                var purchaseorder = metaModelService.LoadFormModel(this.Context, "stk_postockreturn");
                string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));

                linkFormDatas.Add(new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "nextForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }
            else
            {
                var purchaseorder = metaModelService.LoadFormModel(this.Context, "stk_postockreturn");
                string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));

                linkFormDatas[purFlag] = (new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "nextForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }

        }

        /// <summary>
        /// 联查销售退货单
        /// </summary>
        private void LinkSoStockReturn(DynamicObject[] dataEntities, IMetaModelService metaModelService, List<Dictionary<string, object>> linkFormDatas)
        {
            var id = dataEntities.Select(o => o["id"]).Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            var strSql = $@"select distinct fid from t_stk_sostockreturnentry with(nolock) where ffeedbackinterid ='{id[0]}'";
            List<string> fids = new List<string>();
            using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            {
                while (dr.Read())
                {
                    fids.Add(dr["fid"].ToString());
                }
            }
            var purFlag = -1;
            if (fids.Count == 0)
            {
                for (int i = 0; i < linkFormDatas.Count; i++)
                {
                    foreach (var val in linkFormDatas[i])
                    {
                        if (val.Value.ToString() == "stk_sostockreturn")
                        {
                            purFlag = i;
                            linkFormDatas[purFlag]["filterString"] = " 1=2";
                            return;
                        }
                    }
                }
                return;
            }

            //找到采购订单下标用于修改
            for (int i = 0; i < linkFormDatas.Count; i++)
            {
                foreach (var val in linkFormDatas[i])
                {
                    if (val.Value.ToString() == "stk_sostockreturn")
                    {
                        purFlag = i;
                        goto ModifyData;
                    }
                }
            }
        ModifyData:
            if (purFlag < 0)
            {
                var purchaseorder = metaModelService.LoadFormModel(this.Context, "stk_sostockreturn");
                string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));

                linkFormDatas.Add(new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "nextForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }
            else
            {
                var purchaseorder = metaModelService.LoadFormModel(this.Context, "stk_sostockreturn");
                string filterStr = "fid in ('{0}')".Fmt(string.Join("','", fids));

                linkFormDatas[purFlag] = (new Dictionary<string, object>
                    {
                        { "formId", purchaseorder.Id },
                        { "formCaption", purchaseorder.Caption },
                        { "flag", "nextForm" },
                        { "filterString", filterStr },
                        { "visible",1}
                    });
            }

        }

    }
}
