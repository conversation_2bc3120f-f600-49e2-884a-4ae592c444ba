using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Contexts;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 保存《售后反馈单》时校验
    /// </summary>
    public class Validation_Save : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }
        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }
            foreach (var item in dataEntities)
            {
                CheckPurData(userCtx, item, result, formInfo);
            }
            return result;
        }


        private void CheckPurData(UserContext ctx, DynamicObject dataEntitys, ValidationResult result, HtmlForm formInfo)
        {
            if (Convert.ToString(dataEntitys["fmanagemodel"]).Equals("1"))
            {
                var entrys = dataEntitys["fproductentry"] as DynamicObjectCollection;
                if (entrys == null) return;

                foreach (var entry in entrys)
                {
                    // 判断是否为采购订单源单（假设fsourcebilltype为采购订单单据类型）
                    var sourceBillType = Convert.ToString(entry["fsourceformid"]);
                    var sourceentryid = Convert.ToString(entry["fsourceentryid"]);
                    if (sourceBillType != "ydj_purchaseorder") continue;

                    DynamicObject entryItem = null;
                    var purOrderObj = ctx.LoadBizDataById("ydj_purchaseorder", entry["fsourceinterid"].ToString());
                    if (purOrderObj != null)
                    {
                        var entitys = purOrderObj["fentity"] as DynamicObjectCollection;
                        entryItem = entitys.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(sourceentryid))).FirstOrDefault();
                    }
                    if (entryItem == null) return;
                    // 获取各数量字段（请根据实际字段名调整）
                    decimal stockInQty = Convert.ToDecimal(entryItem["fbizinstockqty"] ?? 0);   // 采购入库数量
                    decimal returnQty = Convert.ToDecimal(entryItem["fbizreturnqty"] ?? 0);     // 采购退换数量
                    decimal refundQty = Convert.ToDecimal(entryItem["fbizrefundqty"] ?? 0);     // 采购退款数量
                    decimal problemQty = Convert.ToDecimal(entry["fqty"] ?? 0);   // 问题商品数量

                    decimal canProblemQty = stockInQty - returnQty - refundQty;
                    if (problemQty > canProblemQty)
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"第{entry["fseq"]}行：问题商品数量（{problemQty.ToString("0.00")}）不能大于可用数量（{canProblemQty.ToString("0.00")}）！",
                            DataEntity = dataEntitys,
                        });
                    }
                }
            }
        }
    }
}
