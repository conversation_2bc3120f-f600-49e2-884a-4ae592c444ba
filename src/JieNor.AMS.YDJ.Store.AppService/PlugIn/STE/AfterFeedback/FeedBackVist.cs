using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Consts;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.DataTransferObject.Poco;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin
{
    /// <summary>
    /// 售后反馈单：回访
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("feedbackvist")]
    public class FeedBackVist : AbstractOperationServicePlugIn
    {
        ///// <summary>
        ///// 预处理校验规则
        ///// </summary>
        ///// <param name="e"></param>
        //public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        //{
        //    base.PrepareValidationRules(e);
        //    e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
        //    {
        //        if (!newData["fserstatus"].Equals("sersta04") && !newData["fserstatus"].Equals("sersta05"))
        //        {
        //            return false;
        //        }
        //        return true;
        //    }).WithMessage(@"服务状态为待评价或已关闭才能进行回访！"));
        //}
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys.IsNullOrEmpty()) return;
            if (!e.DataEntitys.IsNullOrEmpty() && e.DataEntitys.Count() > 1) throw new BusinessException("暂只允许一对一进行回访！");

            base.EndOperationTransaction(e);
            var newbill = this.Context.LoadBizDataByFilter("ydj_vist",
                "fsourcenumber=@fsourcenumber and fmainorgid='" + this.Context.Company + "' and fsourcetype='ste_afterfeedback' and fstatus!='E'", false,
                new List<SqlParam> {
                    new SqlParam("@fsourcenumber", System.Data.DbType.String, e.DataEntitys[0]["fbillno"])
                }).FirstOrDefault();
            if (!newbill.IsNullOrEmpty())
            {
                var action = this.OperationContext.UserContext.ShowSpecialForm(this.MetaModelService.LoadFormModel(this.Context, "ydj_vist"),
                    newbill,
                    false,
                    this.OperationContext.PageId,
                    Enu_OpenStyle.Modal,
                    Enu_DomainType.Bill,
                    new Dictionary<string, object>(),
                    formPara =>
                    {
                        formPara.Status = Enu_BillStatus.Modify;
                    });
                //this.Result.SimpleMessage = "该服务单存在下游未关闭售后反馈单【{0}】，请等待该单据售后状态为待关闭后再进行此操作！<br/>".Fmt(newbill["fbillno"]);
                this.Result.HtmlActions.Add(action);
                this.Result.IsShowMessage = false;
            }
            else
            {
                var convertService = this.Container.GetService<IConvertService>();
                var result = convertService.Push(Context, new BillConvertContext()
                {
                    RuleId = "ste_afterfeedback2ydj_vist",
                    SourceFormId = "ste_afterfeedback",
                    TargetFormId = "ydj_vist",
                    SelectedRows = new List<SelectedRow>{
                        new SelectedRow
                    {
                        PkValue = e.DataEntitys[0]["id"].ToString()
                    } }.ToConvertSelectedRows(),
                    Option = OperateOption.Create()
                });
                var convertResult = result.SrvData as ConvertResult;
                if (convertResult != null)
                {
                    //设置页面打开方式（如果前端有传递，则用传递的方式打开，否则按默认的方式打开）
                    foreach (var targetDataEntity in convertResult.TargetDataObjects)
                    {
                        var action = this.OperationContext.UserContext.ShowSpecialForm(convertResult.HtmlForm,
                            targetDataEntity,
                            false,
                            this.OperationContext.PageId,
                            Enu_OpenStyle.Modal,
                            Enu_DomainType.Bill,
                            new Dictionary<string, object>(), formPara =>
                            {
                                formPara.Status = Enu_BillStatus.Push;
                            });

                        this.OperationContext.Result.HtmlActions.Add(action);
                    }
                    this.Result.IsShowMessage = false;
                }
                else
                {
                    this.OperationContext.Result.IsSuccess = false;
                    this.OperationContext.Result.SimpleMessage = "下推过程出现未知错误，请查看系统日志！";
                }
            }
        }
    }
}