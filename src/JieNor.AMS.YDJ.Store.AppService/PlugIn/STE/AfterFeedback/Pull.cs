using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 售后反馈单：选单
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("Pull")]
    public class Pull : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.BeforeGetConvertRule:
                    this.BeforeGetConvertRule(e);
                    break;
                default:
                    break;
            }
        }

        /*
        2、销售出库单.源单编号为空时，进入《选择-销售合同列表》，点<确定>时，校验所选择的明细行是否对应多笔合同，若是，返回失败，并提示报错：不允许多笔销售合同合并出库，请重新选择！
        3、销售出库单.源单编号不为空时，进入《选择-销售合同列表》，点<确定>后，控制只能返回与当前销售出库单的源单编号一致的合同明细；若所选择的明细行均不属于当前销售出库单的源单编号所对应的合同明细，返回失败，并提示报错：不允许多笔销售合同合并出库，请重新选择！
        4、销售出库单.源单编号不为空时，进入《选择-销售合同列表》，点<确定>时，校验所选择的明细行是否只对应一笔合同 且 该笔合同编号不同于当前销售出库单的源单编号，若是，返回成功，并重新覆盖销售出库单；反之，则执行第3点逻辑判断。
        任务链接：http://dmp.jienor.com:81/zentao/task-view-29340.html
        */
        private void BeforeGetConvertRule(OnCustomServiceEventArgs e)
        {
            var dataEntities = e.DataEntities;

            if (dataEntities == null || dataEntities.Length == 0) return;

            var eventData = e.EventData as BeforeGetConvertRuleData;
            if (eventData == null) return;

            var rule = this.MetaModelService.LoadConvertRule(this.Context, eventData.RuleId);
            if (rule == null) return;

            // 源单不是销售合同，跳过
            if (!rule.SourceFormId.EqualsIgnoreCase("ydj_purchaseorder")) return;

            // 如果销售出库单的存在源单，但源单不是销售合同，报错
            if (dataEntities.Any(s =>
            {
                string sourceType = Convert.ToString(s["fsourcetype"]);
                return !sourceType.IsNullOrEmptyOrWhiteSpace() && !sourceType.EqualsIgnoreCase("ydj_purchaseorder");
            }))
            {
                throw new BusinessException("售后反馈单存在源单类型不是采购的，请重新选择！");
            }

            var pkids = eventData.SelectedRows?.Where(s => !s.PkValue.IsNullOrEmptyOrWhiteSpace()).Select(s => s.PkValue).Distinct();
            var entrypkids = eventData.SelectedRows?.Where(s => !s.EntryPkValue.IsNullOrEmptyOrWhiteSpace()).Select(s => s.EntryPkValue).Distinct();
            if (pkids == null || pkids.Count() == 0) return;
            if (entrypkids.Count() == 0) throw new BusinessException("需要选择指定明细行！");
            if (entrypkids.Count()>1) throw new BusinessException("只允许选择一行明细！");
            var sourceDataEntities = this.Context.LoadBizDataById(rule.SourceFormId, pkids);
            if (sourceDataEntities == null || sourceDataEntities.Count == 0) return;

            //筛选出销售合同商品明细中的套件头
            // 所有的商品ID
            var allEntrys = sourceDataEntities?.SelectMany(t => t["fentity"] as DynamicObjectCollection);
            var materialIds = allEntrys.Select(entry => Convert.ToString(entry["fmaterialid"])).Where(productId => !productId.IsNullOrEmptyOrWhiteSpace())?.Distinct()?.ToList();

            foreach (var item in allEntrys)
            {
                if (item["Id"].Equals(eventData.SelectedRows.FirstOrDefault().EntryPkValue))
                {
                    if (Convert.ToDecimal(item["fbizinstockqty"]) <= 0) throw new BusinessException("当前选中商品行的入库数量要大于0！");
                }
            }

            var suiteHeadEntryIds = new List<string>();
            if (materialIds != null && materialIds.Any())
            {
                var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", materialIds, "fsuiteflag");
                var suiteHeadIds = productObjs.Where(t => Convert.ToString(t["fsuiteflag"]).ToLower() == "true" || Convert.ToString(t["fsuiteflag"]) == "1")
                                              .Select(m => Convert.ToString(m["id"])).ToList();//套件头商品ID
                suiteHeadEntryIds = allEntrys.Where(t => suiteHeadIds.Contains(Convert.ToString(t["fmaterialid"])))
                                             .Select(f => Convert.ToString(f["id"])).ToList();//查询套件头商品所在行的明细行内码
                //将选中行中为套件头的数据清除
                var unHeadSelect = eventData.SelectedRows.Where(t => string.IsNullOrWhiteSpace(t.EntryPkValue) || (!string.IsNullOrWhiteSpace(t.EntryPkValue) && !suiteHeadEntryIds.Contains(t.EntryPkValue)));
                eventData.SelectedRows = unHeadSelect;
            }
            if (!eventData.SelectedRows.Any())
            {
                throw new BusinessException("售后反馈单选单无需选择套件头商品，请重新选择！");
            }

            List<SelectedRow> selectedRows = new List<SelectedRow>();

            foreach (var dataEntity in dataEntities)
            {
                string sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
                if (sourceNumber.IsNullOrEmptyOrWhiteSpace())
                {
                    //2、销售出库单.源单编号为空时，进入《选择 - 销售合同列表》，点<确定> 时，校验所选择的明细行是否对应多笔合同，若是，返回失败，并提示报错：不允许多笔销售合同合并出库，请重新选择！
                    if (sourceDataEntities.Count > 1)
                    {
                        throw new BusinessException("不允许多笔采购订单合并售后，请重新选择！");
                    }
                    foreach (var selectedRow in eventData.SelectedRows)
                    {
                        if (!selectedRow.EntryPkValue.IsNullOrEmptyOrWhiteSpace())
                        {
                            selectedRows.Add(selectedRow);
                        }
                    }
                    //sourceDataEntities.Where(a=>)
                    eventData.SelectedRows = selectedRows;
                }
                else
                {
                    if (sourceDataEntities.Count == 1)
                    {
                        //4、销售出库单.源单编号不为空时，进入《选择 - 销售合同列表》，点<确定> 时，校验所选择的明细行是否只对应一笔合同 且 该笔合同编号不同于当前销售出库单的源单编号，若是，返回成功，并重新覆盖销售出库单；反之，则执行第3点逻辑判断。
                        var sourceOrder = sourceDataEntities.First();
                        var orderNo = Convert.ToString(sourceOrder["fbillno"]);
                        if (!orderNo.EqualsIgnoreCase(sourceNumber))
                        {
                            // 删除
                            var entities = dataEntity["fproductentry"] as DynamicObjectCollection;
                            entities.Clear();

                            string pkid = Convert.ToString(dataEntity["id"]);
                            string billNo = Convert.ToString(dataEntity["fbillno"]);
                            var response = this.Gateway.InvokeLocal<DynamicDTOResponse>(this.Context,
                                new CommonBillDTO()
                                {
                                    FormId = this.HtmlForm.Id,
                                    OperationNo = "deleterow",
                                    SelectedRows = entities.Select(s => new SelectedRow
                                    {
                                        PkValue = pkid,
                                        EntityKey = "fproductentry",
                                        EntryPkValue = Convert.ToString(s["id"])
                                    })
                                });
                            response.OperationResult.ThrowIfHasError(true, $"{this.HtmlForm.Caption}【{billNo}】清空明细失败！");

                            // 更新源单信息
                            dataEntity["fsourceinterid"] = sourceOrder["id"];
                            dataEntity["fsourcenumber"] = sourceOrder["fbillno"];
                        }

                        selectedRows = eventData.SelectedRows.ToList();
                    }
                    else
                    {
                        throw new BusinessException("不允许选择多行采购订单做售后，请重新选择！");
                        //3、销售出库单.源单编号不为空时，进入《选择 - 销售合同列表》，点<确定> 后，控制只能返回与当前销售出库单的源单编号一致的合同明细；若所选择的明细行均不属于当前销售出库单的源单编号所对应的合同明细，返回失败，并提示报错：不允许多笔销售合同合并出库，请重新选择！

                        // 如果源单里有销售出库单.源单编号
                        var sourceOrder = sourceDataEntities.FirstOrDefault(s =>
                            Convert.ToString(s["fbillno"]).EqualsIgnoreCase(sourceNumber));
                        if (sourceOrder != null)
                        {
                            string sourceOrderId = Convert.ToString(sourceOrder["id"]);
                            string sourceEntityKey = "fentry";

                            // 只返回与当前销售出库单的源单编号一致的 且 排除已添加的明细
                            var sourceOrderEntrys = sourceOrder["fentry"] as DynamicObjectCollection;
                            var sourceOrderEntryIds = sourceOrderEntrys.Select(s => Convert.ToString(s["id"]));

                            var entities = dataEntity["fentity"] as DynamicObjectCollection;
                            var sourceEntityIds = entities
                                .Where(s => Convert.ToString(s["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder"))
                                .Select(s => Convert.ToString(s["fsourceentryid"]));

                            var addingEntryIds = sourceOrderEntryIds.Where(s => !sourceEntityIds.Contains(s));

                            foreach (var selectedRow in eventData.SelectedRows.Where(s => s.PkValue.EqualsIgnoreCase(sourceOrderId) && s.EntityKey.EqualsIgnoreCase(sourceEntityKey)))
                            {
                                // 如果单据体id为空，表示整单添加
                                if (selectedRow.EntryPkValue.IsNullOrEmptyOrWhiteSpace())
                                {
                                    selectedRows.Add(selectedRow);
                                }
                                else if (addingEntryIds.Contains(selectedRow.EntryPkValue))
                                {
                                    selectedRows.Add(selectedRow);
                                }
                            }
                        }
                        // 没找到源单，说明选择的都不是当前销售出库单的源单对应的，报错
                        else
                        {
                            throw new BusinessException("不允许多笔销售合同合并出库，请重新选择！");
                        }

                    }

                    eventData.SelectedRows = selectedRows;
                }
            }
        }

    }
}
