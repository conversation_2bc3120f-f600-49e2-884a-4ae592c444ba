using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement
{
    /// <summary>
    /// 卖场对账单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_marketstatement")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fentry", data => data).IsTrue((n, o) =>
            {
                var deductrate = Convert.ToDecimal(n["fdeductrate"]);
                return !(deductrate < 0 || deductrate > 100);
            }).WithMessage("收支记录行{0}：卖场扣点比例不允许超出 0-100 范围值。", (dy, dyObj) => dy["fseq"]));
            e.Rules.Add(this.RuleFor("fentry", data => data).IsTrue((n, o) =>
            {
                var incomeObj = this.Context.LoadBizDataById("coo_incomedisburse", Convert.ToString(n["fincomeid"]));
                var amount = Convert.ToDecimal(incomeObj["famount"]);
                var deductamount = Convert.ToDecimal(n["fdeductamount"]);
                return !(deductamount < 0) && !(deductamount > amount);
            }).WithMessage("收支记录行{0}：卖场扣点比例不允许超出 0-收款金额 范围值。", (dy, dyObj) => dy["fseq"]));


            string errMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((n, o) =>
            {
                var fotherdeduct = n["fotherdeduct"] as DynamicObjectCollection;

                if (fotherdeduct != null && fotherdeduct.Count > 0 && this.Context.IsDirectSale)
                {
                    foreach (var item in fotherdeduct)
                    {
                        var fexpensetype = Convert.ToString(item["fexpensetype"]);
                        var fstartdate = Convert.ToString(item["fstartdate"]);
                        var fenddate = Convert.ToString(item["fenddate"]);
                        var fotherdeductamount = Convert.ToDecimal(item["fotherdeductamount"]);
                        if (fexpensetype.IsNullOrEmptyOrWhiteSpace() || fstartdate.IsNullOrEmptyOrWhiteSpace() ||
                            fenddate.IsNullOrEmptyOrWhiteSpace() || fotherdeductamount == 0)
                        {
                            errMsg = $"其他扣款行{Convert.ToInt32(item["fseq"])}：请完善其他扣款明细区，填写完整后再保存。";
                            return false;
                        }
                    }
                }

                return true;
            }).WithMessage("{0}", (billObj, propObj) => errMsg));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            //清理没有实际数据的【其他扣款】
            foreach (var entity in e.DataEntitys)
            {
                if (entity["fotherdeduct"] != null)
                {
                    var otherdeducts = entity["fotherdeduct"] as DynamicObjectCollection;
                    var removeRows = new List<DynamicObject>();
                    foreach (var otDedect in otherdeducts)
                    {
                        if (Convert.ToString(otDedect["fotherdeductitem"]).IsNullOrEmptyOrWhiteSpace()
                            && (Convert.ToString(otDedect["fotherdeductamount"]).IsNullOrEmptyOrWhiteSpace() ||
                                Convert.ToDecimal(otDedect["fotherdeductamount"]) == 0))
                        {
                            removeRows.Add(otDedect);
                        }
                    }

                    removeRows.ForEach(t => otherdeducts.Remove(t));
                }
            }

            //获取已存在的收支记录
            var existedIncomeIds = getExistedIncomeDisburseId(e.DataEntitys);
            //更新收支记录的对账状态为对账中, 得到明细行返款金额
            var verifyAmountInfos = updateIncomeStatementStatus(e.DataEntitys);
            //计算表头总的返款金额
            calculateSumVerifyAmount(e.DataEntitys, verifyAmountInfos);
            //回滚已删除的收支记录
            updateIncomeStatementStatus(e.DataEntitys, existedIncomeIds);
        }

        /// <summary>
        /// 获取已存在的收支记录
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private List<string> getExistedIncomeDisburseId(DynamicObject[] dataEntities)
        {
            var results = new List<string>();
            var ids = dataEntities.Where(x => x.DataEntityState.FromDatabase).Select(x => Convert.ToString(x["id"]))
                .ToList();
            if (ids == null || ids.Count <= 0)
            {
                return results;
            }

            var dbService = this.Container.GetService<IDBService>();
            if (ids.Count <= 50)
            {
                var sql = new StringBuilder("select fincomeid from t_ydj_marketstatemententry where fid ");
                var sqlParams = new List<SqlParam>();
                if (ids.Count == 1)
                {
                    sql.Append("=@fid");
                    sqlParams.Add(new SqlParam("@fid", System.Data.DbType.String, ids[0]));
                }
                else
                {
                    sql.Append(" in (");
                    sql.Append(string.Join(",", ids.Select((x, i) => $"@fid{i}")));
                    sqlParams.AddRange(ids.Select((x, i) => new SqlParam($"@fid{i}", System.Data.DbType.String, x)));
                    sql.Append(")");
                }

                using (var dataReader = dbService.ExecuteReader(this.Context, sql.ToString(), sqlParams))
                {
                    while (dataReader.Read())
                    {
                        results.Add(Convert.ToString(dataReader.GetValue(0)));
                    }
                }

                return results;
            }

            using (var tran = this.Context.CreateTransaction())
            {
                var tableName = dbService.CreateTempTableWithDataList(this.Context, ids, false);
                var sql = $"select fincomeid from t_ydj_marketstatemententry e inner join {tableName} t on t.fid=e.fid";
                using (var dataReader = dbService.ExecuteReader(this.Context, sql))
                {
                    while (dataReader.Read())
                    {
                        results.Add(Convert.ToString(dataReader.GetValue(0)));
                    }
                }

                tran.Complete();

                dbService.DeleteTempTableByName(Context, tableName, true);
            }

            return results;
        }

        /// <summary>
        /// 回滚已删除的收支记录
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="existedIncomeIds"></param>
        private void updateIncomeStatementStatus(DynamicObject[] dataEntities, List<string> existedIncomeIds)
        {
            if (existedIncomeIds == null || existedIncomeIds.Count <= 0)
            {
                return;
            }

            var currentIds = dataEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection)
                .Select(x => Convert.ToString(x["fincomeid"]))
                .Where(x => false == string.IsNullOrWhiteSpace(x))
                .Distinct()
                .ToList();

            var deleteIds = existedIncomeIds.Where(x => false == currentIds.Contains(x)).ToList();
            if (deleteIds == null || deleteIds.Count <= 0)
            {
                return;
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));

            var incomeEntities = incomeDm.Select(deleteIds).OfType<DynamicObject>().ToArray();
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return;
            }

            foreach (var incomeEntity in incomeEntities)
            {
                var statementStatus = Convert.ToString(incomeEntity["fstatementstatus"]);
                var tranId = Convert.ToString(incomeEntity["ftranid"]);
                if (statementStatus == "3")
                {
                    throw new BusinessException("卖场已对账的收支记录不可以删除!");
                }

                if (statementStatus == "2")
                {
                    incomeEntity["fstatementstatus"] = "1";
                }
            }

            var prepareSaveService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveService.PrepareDataEntity(this.Context, incomeForm, incomeEntities, OperateOption.Create());
            incomeDm.Save(incomeEntities);
        }

        /// <summary>
        /// 更新收支记录的对账状态为对账中
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns>返回明细行包含方向的返款金额</returns>
        private List<Dictionary<string, object>> updateIncomeStatementStatus(DynamicObject[] dataEntities)
        {
            var incomeAmountInfos = new List<Dictionary<string, object>>();
            var entries = dataEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection).ToList();
            var incomeIds = entries.Select(x => Convert.ToString(x["fincomeid"]))
                .Where(x => false == string.IsNullOrWhiteSpace(x))
                .Distinct()
                .ToList();

            if (incomeIds == null || incomeIds.Count <= 0)
            {
                return calculateEntryAmount(entries, incomeAmountInfos);
            }

            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var incomeDm = this.Container.GetService<IDataManager>();
            incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));

            var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();
            if (incomeEntities == null || incomeEntities.Length <= 0)
            {
                return calculateEntryAmount(entries, incomeAmountInfos);
            }

            foreach (var incomeEntity in incomeEntities)
            {
                var statementStatus = Convert.ToString(incomeEntity["fstatementstatus"]);
                var tranId = Convert.ToString(incomeEntity["ftranid"]);
                var sourceFormId = Convert.ToString(incomeEntity["fsourceformid"]);
                //if (false == sourceFormId.EqualsIgnoreCase("ydj_order") && 
                //    false == sourceFormId.EqualsIgnoreCase("ydj_saleintention"))
                //{
                //    throw new BusinessException($"保存失败！流水号为[{ tranId }]的收支记录来源单不是销售意向单或销售合同!");
                //}

                if (statementStatus == "1")
                {
                    incomeEntity["fstatementstatus"] = "2";
                }

                var incomeId = Convert.ToString(incomeEntity["id"]);
                var amount = Convert.ToDecimal(incomeEntity["famount"]);
                //账户收支方向
                var direction = Convert.ToString(incomeEntity["fdirection"]).Trim().ToLower();
                //用途
                var purpose = Convert.ToString(incomeEntity["fpurpose"]).Trim().ToLower();

                switch (purpose)
                {
                    //账户充值
                    case "bizpurpose_01":
                    //付款
                    case "bizpurpose_02":
                        incomeAmountInfos.Add(new Dictionary<string, object>
                        {
                            { "incomeId", incomeId },
                            { "amount", amount },
                            { "direction", 1 }
                        });
                        break;
                    //红冲
                    case "bizpurpose_04":
                        switch (direction)
                        {
                            case "direction_01":
                                incomeAmountInfos.Add(new Dictionary<string, object>
                                {
                                    { "incomeId", incomeId },
                                    { "amount", amount },
                                    { "direction", -1 }
                                });
                                break;
                            case "direction_02":
                                incomeAmountInfos.Add(new Dictionary<string, object>
                                {
                                    { "incomeId", incomeId },
                                    { "amount", amount },
                                    { "direction", 1 }
                                });
                                break;
                        }

                        break;
                    //退款
                    case "bizpurpose_06":
                    //其他扣款
                    case "bizpurpose_03":
                        incomeAmountInfos.Add(new Dictionary<string, object>
                        {
                            { "incomeId", incomeId },
                            { "amount", amount },
                            { "direction", -1 }
                        });
                        break;
                }
            }

            var prepareSaveService = this.Container.GetService<IPrepareSaveDataService>();
            prepareSaveService.PrepareDataEntity(this.Context, incomeForm, incomeEntities, OperateOption.Create());
            incomeDm.Save(incomeEntities);

            return calculateEntryAmount(entries, incomeAmountInfos);
        }

        /// <summary>
        /// 计算表头总的返款金额
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="verifyAmountInfos"></param>
        private void calculateSumVerifyAmount(DynamicObject[] dataEntities,
            List<Dictionary<string, object>> verifyAmountInfos)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            if (verifyAmountInfos == null || verifyAmountInfos.Count <= 0)
            {
                foreach (var dataEntity in dataEntities)
                {
                    dataEntity["fverifysumamount"] = 0;
                }

                return;
            }

            foreach (var dataEntity in dataEntities)
            {
                var id = Convert.ToString(dataEntity["id"]);
                decimal sumVerifyAmount = 0;
                foreach (var verifyAmountInfo in verifyAmountInfos.Where(x =>
                             Convert.ToString(x["id"]).EqualsIgnoreCase(id)))
                {
                    var verifyAmount = Convert.ToDecimal(verifyAmountInfo["verifyAmount"]);
                    var direction = Convert.ToDecimal(verifyAmountInfo["direction"]);
                    if (verifyAmount < 0 && direction < 0) direction = 1;
                    sumVerifyAmount += direction * verifyAmount;
                }

                dataEntity["fverifysumamount"] = sumVerifyAmount;
            }
        }

        /// <summary>
        /// 计算对账单收支记录明细金额
        /// </summary>
        /// <param name="entries"></param>
        /// <param name="incomeAmountInfos"></param>
        /// <returns>返回明细行包含方向的返款金额</returns>
        private List<Dictionary<string, object>> calculateEntryAmount(List<DynamicObject> entries,
            List<Dictionary<string, object>> incomeAmountInfos)
        {
            var results = new List<Dictionary<string, object>>();
            if (entries == null && entries.Count <= 0)
            {
                return results;
            }

            if ((incomeAmountInfos == null || incomeAmountInfos.Count <= 0))
            {
                foreach (var entry in entries)
                {
                    entry["fdeductamount"] = 0;
                    entry["fverifyamount"] = 0;
                }

                return results;
            }

            foreach (var entry in entries)
            {
                var incomeId = Convert.ToString(entry["fincomeid"]);
                var incomeAmountInfo =
                    incomeAmountInfos.FirstOrDefault(x => Convert.ToString(x["incomeId"]).EqualsIgnoreCase(incomeId));
                var deductRate = Math.Round(Convert.ToDecimal(entry["fdeductrate"]), 2, MidpointRounding.AwayFromZero);
                entry["fdeductrate"] = deductRate;

                if (incomeAmountInfo == null)
                {
                    entry["fdeductamount"] = 0;
                    entry["fverifyamount"] = 0;
                    continue;
                }

                var direction = Convert.ToDecimal(incomeAmountInfo["direction"]);
                var verifyAmount = Convert.ToDecimal(entry["fverifyamount"]);
                if (verifyAmount < 0 && direction < 0) direction = 1;
                entry["fverifyamount"] = direction * verifyAmount;
                var id = Convert.ToString((entry.Parent as DynamicObject)["id"]);
                incomeAmountInfo.Add("id", id);
                incomeAmountInfo.Add("verifyAmount", verifyAmount);
                results.Add(incomeAmountInfo);
            }

            return results;
        }
    }
}