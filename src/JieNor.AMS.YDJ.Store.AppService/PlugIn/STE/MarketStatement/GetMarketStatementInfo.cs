using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement
{
    [InjectService]
    [FormId("ydj_marketstatement")]
    [OperationNo("getmarketstatementinfo")]
    public class GetMarketStatementInfo: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var fsupplierid = this.GetQueryOrSimpleParam<string>("fsupplierid", "");

            if (fsupplierid.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var sqlParams = new List<SqlParam>();
            sqlParams.Add(new SqlParam("@fid", DbType.String, fsupplierid));
            var querySql = $"select faddress from t_ydj_supplier where fid = @fid";
            var entity = this.Context.ExecuteDynamicObject(querySql, sqlParams).FirstOrDefault();
            if (entity == null)
            {
                return;
            }
            this.Result.SrvData = entity;
            this.Result.IsSuccess = true;
            this.Result.IsShowMessage = false;
        }
    }
}