using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Flow;
using JieNor.Framework;
using JieNor.Framework.AppService.OperationService.ApprovalFlow;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.MarketStatement
{
    /// <summary>
    /// 卖场对账单：提交
    /// </summary>
    [InjectService]
    [FormId("ydj_marketstatement")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            //更新收支记录
            updateIncomeStatementStatus(e.DataEntitys);
        }

        /// <summary>
        /// 更新收支记录的对账状态为对账中
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns>返回明细行包含方向的返款金额</returns>
        private void updateIncomeStatementStatus(DynamicObject[] dataEntities)
        {
            var incomeAmountInfos = new List<Dictionary<string, object>>();
            var entries = dataEntities.SelectMany(x => x["fentry"] as DynamicObjectCollection).ToList();
            var incomeIds = entries.Select(x => Convert.ToString(x["fincomeid"]))
                .Where(x => false == string.IsNullOrWhiteSpace(x))
                .Distinct()
                .ToList();

            if (incomeIds != null && incomeIds.Count > 0)
            {
                var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
                var incomeDm = this.Container.GetService<IDataManager>();
                incomeDm.InitDbContext(this.Context, incomeForm.GetDynamicObjectType(this.Context));

                var incomeEntities = incomeDm.Select(incomeIds).OfType<DynamicObject>().ToArray();
                if (incomeEntities != null && incomeEntities.Length > 0)
                {
                    var ischange = false;
                    foreach (var incomeEntity in incomeEntities)
                    {
                        var statementStatus = Convert.ToString(incomeEntity["fstatementstatus"]);
                        if (statementStatus == "1")
                        {
                            incomeEntity["fstatementstatus"] = "2";
                            ischange = true;
                        }
                    }

                    if (ischange)
                    {
                        var prepareSaveService = this.Container.GetService<IPrepareSaveDataService>();
                        prepareSaveService.PrepareDataEntity(this.Context, incomeForm, incomeEntities,
                            OperateOption.Create());
                        incomeDm.Save(incomeEntities);
                    }
                }
            }
        }
        
    }
}