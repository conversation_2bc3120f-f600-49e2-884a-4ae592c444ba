using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseSettleDyn
{
    /// <summary>
    /// 采购订单：付款结算
    /// </summary>
    [InjectService]
    [FormId("ydj_purchasesettledyn")]
    [OperationNo("Settle")]
    public class Settle : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var spService = this.Container.GetService<ISystemProfile>();

            e.Rules.Add(this.RuleFor("fbillhead", data => data["fsourceid"]).NotEmpty().WithMessage("fsourceid不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fway"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("支付方式不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var enableMustInputBankId = spService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablemustinputbankid", true);

                if ((Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_06") ||
                    enableMustInputBankId && Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_11"))
                    && Convert.ToBoolean(newData["fissyn"])
                    && newData["fsynbankid"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择对方银行！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                decimal fsettleamount = Convert.ToDecimal(newData["fsettleamount"]);
                if (fsettleamount < 0.01M)
                {
                    return false;
                }
                return true;
            }).WithMessage("本次结算额必须大于0！"));

            var wayIds = new List<string>() { "payway_01", "payway_05" };
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!newData["fcontactunitid"].IsNullOrEmptyOrWhiteSpace() && newData["fimage"].IsNullOrEmptyOrWhiteSpace() && !wayIds.Contains(newData["fway"].ToString()))
                {
                    return false;
                }
                return true;
            }).WithMessage("选择了代收单位，必须上传凭证！"));

            string receiptMsg = "【收款小票号】必填！";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var profileService = this.Container.GetService<ISystemProfile>();
                var mustinvoicenumber = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fmustinvoicenumber", false); //收款必填小票号
                var receiptNo = Convert.ToString(newData["freceiptno"]);
                var way = Convert.ToString(newData["fway"]);//支付方式
                if (receiptNo.IsNullOrEmptyOrWhiteSpace() && way != "payway_01")
                {
                    //收款小票号为空且支付方式不为：账户支付
                    if (mustinvoicenumber)
                    {
                        receiptMsg = "【收款小票号】必填！";
                        return false;
                    }
                }
                //else
                //{
                //    decimal money = Convert.ToDecimal(newData["fsettleamount"]);
                //    if (money > 0.01M)
                //    {
                //        var incomeObjs = this.Context.LoadBizDataByFilter("coo_incomedisburse", $" freceiptno='{receiptNo}' AND famount='{money}'");
                //        if (incomeObjs != null && incomeObjs.Any())
                //        {
                //            receiptMsg = "该笔收款金额的“收款小票号”录入重复，请检查！";
                //            return false;
                //        }
                //    }
                //}
                return true;
            }).WithMessage(receiptMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                decimal famount = Convert.ToDecimal(newData["famount"]);
                if (famount > 0 && famount < 0.01M)
                {
                    return false;
                }
                return true;
            }).WithMessage("结算金额必须大于0！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var settleInfo = e.DataEntitys[0];

            //源单主键ID
            string fsourceid = Convert.ToString(settleInfo["fsourceid"]);
            decimal fsettleamount = Convert.ToDecimal(settleInfo["fsettleamount"]);
            decimal famount = Convert.ToDecimal(settleInfo["famount"]);
            string fcontactunitid = Convert.ToString(settleInfo["fcontactunitid"]);
            if (famount < 0.01M)
            {
                throw new BusinessException("本次结算额必须大于0！");
            }

            //源单信息
            var purForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_purchaseorder");
            var purchaseOrder = this.Context.LoadBizDataById("ydj_purchaseorder", fsourceid);
            if (purchaseOrder == null) throw new BusinessException($"{purForm.Caption}【{fsourceid}】不存在！");

            if (!Convert.ToString(purchaseOrder["fstatus"]).EqualsIgnoreCase("E"))
            {
                throw new BusinessException($"{purForm.Caption}【{purchaseOrder["fbillno"]}】未审核，无法结算！");
            }
            if (Convert.ToString(purchaseOrder["fpaystatus"]).EqualsIgnoreCase("paystatus_type_03"))
            {
                throw new BusinessException($"{purForm.Caption}【{purchaseOrder["fbillno"]}】的结算状态是【全款已付】，无法结算！");
            }
            if (fsettleamount > Convert.ToDecimal(purchaseOrder["fpayamount"]))
            {
                throw new BusinessException($"本次结算额不允许大于{purForm.Caption}的待结算金额！");
            }

            // 供应商（按需加载，性能优化）
            var supplierId = Convert.ToString(purchaseOrder["fsupplierid"]);
            var supplier = this.Context.LoadBizBillHeadDataById("ydj_supplier", supplierId, "fcoocompany,fcoocompanyid,fcooproductid");
            if (supplier == null) throw new BusinessException($"供应商信息不存在！");

            //协同账户余额服务
            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();

            //代收单位
            var contactUnit = synAccountBalanceService.GetContactUnitById(this.Context, fcontactunitid);
            if (contactUnit != null)
            {
                settleInfo["fcontactunittype"] = contactUnit["ftype"];
            }

            //收支记录模型
            var htmlForm = this.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "coo_incomedisburse");

            List<DynamicObject> incomeDisburses = new List<DynamicObject>();

            //账户结算金额
            List<AccountInfo> accountSettle = new List<AccountInfo>();
            string accounts = this.GetQueryOrSimpleParam<string>("accounts");
            if (!accounts.IsNullOrEmptyOrWhiteSpace())
            {
                accountSettle = accounts.FromJson<List<AccountInfo>>() ?? new List<AccountInfo>();
            }

            var way = Convert.ToString(settleInfo["fway"]);
            if (way.EqualsIgnoreCase("payway_01"))
            {
                //新增支持多账户结算
                var accountentity = settleInfo["faccountentity"] as DynamicObjectCollection;

                //当单据体总支付金额 != 单据头支付金额
                if (famount != accountentity.Select(p => Convert.ToDecimal(p["fthisamount"])).Sum())
                {
                    throw new BusinessException("本次结算金额不一致,请检查!");
                }

                //当单据体结算金额 = 0
                if (accountentity.Where(p => Convert.ToDecimal(p["fthisamount"]) == 0).Count() > 0)
                {
                    throw new BusinessException("结算账户中本次结算金额必须大于0!");
                }

                foreach (var account in accountSettle)
                {
                    if (account == null || account.AccountId.IsNullOrEmptyOrWhiteSpace())
                    {
                        throw new BusinessException("请选择支付账号!");
                    }

                    //多账户结算，当前生成的收款金额 = 单据体的本次结算金额
                    famount = accountentity.Where(p => Convert.ToString(p["faccount"]).EqualsIgnoreCase(account.AccountId)).Select(p => Convert.ToDecimal(p["fthisamount"])).FirstOrDefault();

                    //将本次的钱赋给账号
                    account.SettleAmount = famount;

                    //检查账户余额
                    synAccountBalanceService.CheckAccountBalance(this.Context, supplier, false, new List<AccountInfo> { account });

                    //账户支付
                    incomeDisburses.Add(this.GetIncomeDisburseObject(htmlForm, purchaseOrder, supplier, settleInfo, famount, account.AccountId));
                }
            }
            else
            {
                //线下支付
                incomeDisburses.Add(this.GetIncomeDisburseObject(htmlForm,
                    purchaseOrder, supplier, settleInfo, famount));
            }

            //保存前预处理
            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService?.PrepareDataEntity(this.Context, htmlForm, incomeDisburses.ToArray(), OperateOption.Create());

            //模拟正常表单保存操作流程
            var saveResult = this.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(this.Context,
                htmlForm.Id,
                incomeDisburses,
                "save",
                new Dictionary<string, object>());
            saveResult?.ThrowIfHasError(true, "收支记录保存出现错误，请检查当前用户的权限！");

            //反写采购订单数据
            this.UpdatePurchaseOrder(purchaseOrder, fsettleamount);

            this.Result.SimpleMessage = "结算成功，等待确认！";
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 获取本地收支记录数据包
        /// </summary>
        private DynamicObject GetIncomeDisburseObject(HtmlForm htmlForm,
            DynamicObject purchaseOrder,
            DynamicObject supplier,
            DynamicObject settleInfo,
            decimal amount,
            string account = "")
        {
            var way = "";
            //如果是账户结算，则支付方式统一为“账户支付”
            if (!account.IsNullOrEmptyOrWhiteSpace())
            {
                way = "payway_01";
            }
            else
            {
                //如果是非账户结算，则以前端用户选择的支付方式为准
                way = Convert.ToString(settleInfo["fway"]);
            }

            DynamicObject billHead = new DynamicObject(htmlForm.GetDynamicObjectType(this.Context));
            billHead["fdate"] = settleInfo["fdate"];
            billHead["fway"] = way;
            billHead["faccount"] = account;
            billHead["fpurcompany"] = this.Context?.Companys?.FirstOrDefault(t => t.CompanyId.EqualsIgnoreCase(this.Context.Company))?.CompanyName;
            billHead["fpurcompanyid"] = this.Context.Company;
            billHead["fsalecompany"] = supplier["fcoocompany"];
            billHead["fsalecompanyid"] = supplier["fcoocompanyid"];
            billHead["fcreatecompany"] = billHead["fpurcompany"]; //创建方默认等于采购方
            billHead["fcreatecompanyid"] = billHead["fpurcompanyid"];
            billHead["fpurpose"] = "bizpurpose_02";
            billHead["fbizstatus"] = "bizstatus_01";
            billHead["foldtranid"] = "";
            billHead["forderno"] = purchaseOrder["fbillno"];
            billHead["fdirection"] = "direction_02";
            billHead["fbizdirection"] = "bizdirection_02";
            billHead["fcontactunittype"] = settleInfo["fcontactunittype"];
            billHead["fcontactunitid"] = settleInfo["fcontactunitid"];
            billHead["fverificstatus"] = "verificstatus_01";
            billHead["famount"] = amount;
            billHead["fimage"] = settleInfo["fimage"];
            billHead["fdescription"] = settleInfo["fdescription"];
            billHead["fbankcard"] = settleInfo["fbankcard"];
            billHead["fdeptid"] = settleInfo["fdeptid"];

            billHead["fmybankid"] = settleInfo["fmybankid"];
            billHead["fsynbankid"] = settleInfo["fsynbankid"];
            billHead["fsynbankname"] = settleInfo["fsynbankname"];
            billHead["fsynbanknum"] = settleInfo["fsynbanknum"];
            billHead["fsynaccountname"] = settleInfo["fsynaccountname"];

            //后台字段
            billHead["fsupplierid"] = purchaseOrder["fsupplierid"];
            billHead["fcustomerid"] = "";
            billHead["fcoocompanyid"] = supplier["fcoocompanyid"];
            billHead["fcooproductid"] = supplier["fcooproductid"];
            billHead["fsourcetranid"] = purchaseOrder["ftranid"];
            billHead["fsourceid"] = purchaseOrder["id"];
            billHead["fsourcenumber"] = purchaseOrder["fbillno"];
            billHead["fsourceformid"] = "ydj_purchaseorder";
            billHead["fisself"] = true;
            billHead["fissyn"] = false;
            //收款小票号
            billHead["freceiptno"] = settleInfo["freceiptno"];

            return billHead;
        }

        /// <summary>
        /// 反写采购订单数据
        /// </summary>
        private void UpdatePurchaseOrder(DynamicObject purchaseOrder, decimal fsettleamount)
        {
            //增加待确认金额
            var confirmamount = Convert.ToDecimal(purchaseOrder["fconfirmamount"]) + fsettleamount;
            purchaseOrder["fconfirmamount"] = confirmamount;
            //【待结算金额】= 【成交金额】-【已付金额】-【待确认金额】
            purchaseOrder["fpayamount"] = Convert.ToDecimal(purchaseOrder["ffbillamount"]) - Convert.ToDecimal(purchaseOrder["fsettleamount"]) - confirmamount;

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_purchaseorder");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            dm.Save(purchaseOrder);
        }
    }
}