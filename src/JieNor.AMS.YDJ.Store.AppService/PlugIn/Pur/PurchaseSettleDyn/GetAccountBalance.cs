//using System;
//using System.Linq;
//using System.Collections.Generic;
//using Newtonsoft.Json.Linq;
//using JieNor.Framework;
//using JieNor.Framework.Interface;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm.DataManager;
//using JieNor.Framework.DataTransferObject.Poco;
//using JieNor.Framework.DataTransferObject;
//using JieNor.Framework.CustomException;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseSettleDyn
//{
//    /// <summary>
//    /// 采购订单：获取采购方（采购方在供应商中的）账户余额
//    /// </summary>
//    [InjectService]
//    [FormId("ydj_purchasesettledyn")]
//    [OperationNo("GetAccountBalance")]
//    public class GetAccountBalance : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 调用操作事物前触发的事件
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            //货款，返利，保证金
//            decimal loan = 0, rebate = 0, deposit = 0;

//            //供应商ID
//            string supplierId = this.GetQueryOrSimpleParam<string>("supplierId");
//            if (!supplierId.IsNullOrEmptyOrWhiteSpace())
//            {
//                var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
//                var dm = this.Container.GetService<IDataManager>();
//                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
//                var supplier = dm.Select(supplierId) as DynamicObject;
//                if (supplier != null)
//                {
//                    //同步发送：向销售方获取协同账户设置信息
//                    var responseResult = this.Gateway.Invoke(
//                        this.Context,
//                        new TargetSEP(supplier["fcoocompanyid"] as string, supplier["fcooproductid"] as string),
//                        new CommonBillDTO()
//                        {
//                            FormId = "ydj_customer",
//                            OperationNo = "PurGetSynAccount",
//                            BillData = "",
//                            ExecInAsync = false,
//                            SimpleData = new Dictionary<string, string>
//                            {

//                            }
//                        }) as CommonBillDTOResponse;

//                    if (responseResult == null) throw new BusinessException("获取销售方的账户设置信息失败！");

//                    //对方系统是否有返回错误信息
//                    var errorMessages = responseResult?.OperationResult?.ComplexMessage?.ErrorMessages;
//                    if (errorMessages != null && errorMessages.Any())
//                    {
//                        throw new BusinessException(errorMessages[0]);
//                    }
//                    var srvData = responseResult?.OperationResult?.SrvData as string;
//                    if (!srvData.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        var joSrvData = JObject.Parse(srvData);
//                        if (joSrvData != null)
//                        {
//                            this.Result.SrvData = joSrvData;
//                            return;
//                        }
//                    }

//                    //var entrys = supplier["fentry"] as DynamicObjectCollection;
//                    //if (entrys != null)
//                    //{
//                    //    //货款余额
//                    //    var loanObj = entrys.LastOrDefault(t => Convert.ToString(t["fpurpose"]).EqualsIgnoreCase("settleaccount_type_01"));
//                    //    if (loanObj != null)
//                    //    {
//                    //        loan = Convert.ToDecimal(loanObj["fbalance_e"]);
//                    //    }
//                    //    //返利余额
//                    //    var rebateObj = entrys.LastOrDefault(t => Convert.ToString(t["fpurpose"]).EqualsIgnoreCase("settleaccount_type_02"));
//                    //    if (rebateObj != null)
//                    //    {
//                    //        rebate = Convert.ToDecimal(rebateObj["fbalance_e"]);
//                    //    }
//                    //    //保证金余额
//                    //    var depositObj = entrys.LastOrDefault(t => Convert.ToString(t["fpurpose"]).EqualsIgnoreCase("settleaccount_type_03"));
//                    //    if (depositObj != null)
//                    //    {
//                    //        deposit = Convert.ToDecimal(depositObj["fbalance_e"]);
//                    //    }
//                    //}
//                }
//            }

//            this.Result.SrvData = new { loan = loan, rebate = rebate, deposit = deposit };
//        }
//    }
//}