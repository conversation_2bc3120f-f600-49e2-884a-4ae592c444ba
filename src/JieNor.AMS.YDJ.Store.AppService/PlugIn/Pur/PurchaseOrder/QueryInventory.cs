using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：库存查询
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("QueryInventory")]
    public class QueryInventory : AbstractOperationServicePlugIn
    {
        /// <summary>
		/// 服务端自定义事件
		/// </summary>
		/// <param name="e"></param>
		public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName.ToLower())
            {
                case "onafterqueryinventory":
                  
                    break;
                default:
                    break;
            }
        }
    }
}
