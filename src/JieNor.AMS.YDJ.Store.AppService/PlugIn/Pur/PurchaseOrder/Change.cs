using System;
using System.Linq;
using System.Collections.Generic;
using System.Net.Configuration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Enums;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：变更
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("Change")]
    public class Change : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            // 当前二级经销商信息
            DynamicObject currentAgent = null;
            if (this.Context.IsSecondOrg)
            {
                currentAgent = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fisnotmgrinv");
            }
            var isNotmgrinv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
            string msg = string.Empty;
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return SynergyFinish(newData, oldData, out msg);
            }).WithMessage("{0}", (billObj, propObj) => msg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                msg = string.Empty;
                var entrys = newData["fentity"] as DynamicObjectCollection;
                //5. 《采购订单》点击"变更" 时,增加校验如果单据头的【关闭状态】= "整单关闭"或"手工关闭"时, 提示 "单据已关闭，不允许变更！" 关联任务：32383
                if (!isNotmgrinv && !newData["fclosestatus"].IsNullOrEmptyOrWhiteSpace() && (Convert.ToInt32(newData["fclosestatus"]) == (int)CloseStatus.Whole || Convert.ToInt32(newData["fclosestatus"]) == (int)CloseStatus.Manual))
                {
                    msg += $"单据已关闭，不允许变更！";
                    return false;
                }
                else
                {
                    return true;
                }
            }).WithMessage("{0}", (billObj, propObj) => msg));

            e.Rules.Add(this.RuleFor("fbillhead",data=>data).IsTrue((newData, oldData) =>
            {
                var isTrue = CheckPurchaseOrderLeaveOneOrderStatus(newData);
                if (!isTrue)
                {
                    msg = "采购已提交至一级经销商，不允许变更！";
                }

                return isTrue;
            }).WithMessage("{0}", (billObj, propObj) => msg));
        }

        private bool SynergyFinish(DynamicObject newData, DynamicObject oldData, out string msg)
        {
            string bizstatus = newData["fbizstatus"].ToString();

            if (bizstatus == "business_status_11")
            {
                msg = "非协同完成，不允许变更";
                return false;
            }

            if (bizstatus == "business_status_09")
            {
                string changestatus = newData["fchangestatus"].ToString();

                if (!(changestatus == "2" || changestatus == "0"))
                {
                    msg = "协同完成时，变更状态不是正常或变更完成，不允许变更！";
                    return false;
                }

                string cancelstatus = newData["fcancelstatus"].ToString();
                if (cancelstatus == "True" || cancelstatus == "1")
                {
                    msg = "协同完成时，作废，不允许变更！";
                    return false;
                }

                if (HasSourceBill(newData))
                {
                    msg = "协同完成时，采购订单存在下游单据，不允许变更！";
                    return false;
                }
            }

            msg = string.Empty;
            return true;
        }

        /// <summary>
        /// 有下游单据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        private bool HasSourceBill(DynamicObject data)
        {
            string[] bills = new[] { "t_stk_postockin", "t_pur_receiptnotice", "t_ydj_purchaseorder" };
            string billno = data["fbillno"] as string;

            List<string> sqls = new List<string>();

            foreach (string bill in bills)
            {
                var sql = @"SELECT top 1 fid
                            FROM {0} with(nolock)
                            WHERE fmainorgid = '{1}' AND fsourcetype='ydj_purchaseorder' and fsourcenumber= '{2}' and fstatus<>'A' and fcancelstatus='0'".Fmt(bill, this.Context.Company, billno);

                sqls.Add(sql);
            }

            if (sqls.Any())
            {
                string sql = sqls.JoinEx(" union all ", false);

                var list = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                if (list.Any())
                {
                    return true;
                }
            }

            return false;
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            foreach (var dataEntity in e.DataEntitys)
            {
                var entrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    //已关闭、驳回的情况不允许清空变更行状态
                    if (entry["fhqderchgstatus"].ToString().EqualsIgnoreCase("02")) continue;
                    //将总部变更状态设为空
                    entry["fhqderchgstatus"] = "";
                }
            }
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);
        }

        ///// <summary>
        ///// K3《订购意向书》未审核
        ///// </summary>
        ///// <param name="newData"></param>
        ///// <param name="oldData"></param>
        ///// <returns></returns>
        //private bool IsIntentOrderNotAudited(DynamicObject newData, DynamicObject oldData)
        //{
        //    return true;
        //}

        /// <summary>
        /// 检查二级采购订单的一级合同状态
        /// </summary>
        /// <param name="dataEntity">采购订单数据包</param>
        /// <returns></returns>
        public bool CheckPurchaseOrderLeaveOneOrderStatus(DynamicObject dataEntity)
        {
            bool isTrue = true;

            //当采购订单【一级合同状态】不等于“空”或者“订单已驳回”时

            //'01':'提交至一级','02':'订单已审核','03':'订单已驳回','04':'订单已出库'
            var leaveOneOrderStatus = Convert.ToString(dataEntity["fonelvoderstatus"]);

            if (!leaveOneOrderStatus.IsNullOrEmptyOrWhiteSpace() || leaveOneOrderStatus.Equals("03"))
            {
                isTrue = false;
            }

            return isTrue;
        }
    }
}