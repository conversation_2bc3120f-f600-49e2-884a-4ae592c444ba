using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Helpers;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using System.Data;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Store.AppService.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：审核
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("searchdeliverids")]
    public class SearchdDeliverids : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(this.Context);
            //获取组织ID
            var Agents = AgentInfos.Select(o => o.OrgId).ToList();

            this.Result.SrvData = GetDeliverIds(Agents);
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 返回送达方ids
        /// </summary>
        /// <param name="orgid"></param>
        /// <returns></returns>
        private List<string> GetDeliverIds(List<string> orgid)
        {
            var customerForm = this.MetaModelService.LoadFormModel(this.Context, "bas_deliver");
            var strWhere = "fsaleorgid in ('{0}')".Fmt(string.Join("','", orgid));
            var customer = customerForm.GetBizDataByWhere(this.Context, strWhere, new List<SqlParam>
            {
            }, true);
            if (customer != null)
            {
                return customer.Select(x => x["id"].ToString()).ToList();
            }
            return new List<string>();
        }
    }
}
