using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Enums;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：更新业务状态
    /// </summary>
    public abstract class UpdateBizStatus : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public abstract string OperationName { get; }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */

            //确认
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.OperationNo.EqualsIgnoreCase("pricefirm") && !Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("business_status_05"))
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】业务状态不是【待确认】，无法确认！", (billObj, propObj) => propObj["fbillno"]));

            //取消确认
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.OperationNo.EqualsIgnoreCase("cancelconfirm") && !Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("business_status_09"))
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】业务状态不是【协同完成】，无法取消确认！", (billObj, propObj) => propObj["fbillno"]));

            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (this.OperationNo.EqualsIgnoreCase("cancelconfirm") && Convert.ToDecimal(newData["fpayamount"]) < Convert.ToDecimal(newData["ffbillamount"]))
            //    {
            //        return false;
            //    }
            //    return true;
            //}).WithMessage("采购订单【{0}】待结算金额小于订单金额，无法取消确认！", (billObj, propObj) => propObj["fbillno"]));

            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    return this.CheckIsExistsSettle(newData);
            //}).WithMessage("采购订单【{0}】已经存在下游收支明细，无法取消确认！", (billObj, propObj) => propObj["fbillno"]));

            //确认收货
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.OperationNo.EqualsIgnoreCase("confirmreceiv") && !Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("business_status_09"))
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】业务状态不是【协同完成】，无法确认收货！", (billObj, propObj) => propObj["fbillno"]));

            //取消收货
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.OperationNo.EqualsIgnoreCase("cancelreceiv") && !Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("business_status_10"))
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】业务状态不是【已收货】，无法取消收货！", (billObj, propObj) => propObj["fbillno"]));

            //协同关系校验
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var supplier = this.GetSupplierById(newData);
                if (supplier["fcoocompanyid"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("采购订单【{0}】所选供应商没有建立协同关系，无法执行该操作！", (billObj, propObj) => propObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return Convert.ToString(newData["fstatus"]).EqualsIgnoreCase(BillStatus.E.ToString());
            }).WithMessage("单据必须先审核才能操作!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return Convert.ToString(newData["fchargebackstatus"]) != "1";
            }).WithMessage("单据已退单不能操作!"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //采购订单模型
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

            //操作后要更新的状态
            var operatedBizStatus = this.GetOperatedBizStatus();

            this.Option.SetIgnoreOpLogFlag();
            var syncService = this.Container.GetService<ISynergyService>();

            foreach (var dataEntity in e.DataEntitys)
            {
                //供应商信息
                var supplier = this.GetSupplierById(dataEntity);

                //数据发送时采用异步消息模式发送，消息中指定回调类型
                var responseResult = this.Gateway.Invoke(
                    this.Context,
                    new TargetSEP(supplier["fcoocompanyid"] as string, supplier["fcooproductid"] as string),
                    new CommonBillDTO()
                    {
                        FormId = "ydj_saleintention",
                        OperationNo = "UpdateSynBizStatus",
                        BillData = "",
                        ExecInAsync = false,
                        AsyncMode = (int)Enu_AsyncMode.Background,
                        SimpleData = new Dictionary<string, string>
                        {
                            {
                                "orderParam", new List<Dictionary<string, string>>
                                {
                                    new Dictionary<string, string>
                                    {
                                        { "tranId", dataEntity["ftranid"] as string },
                                        { "type",dataEntity["ftype"] as string},
                                        { "bizStatus", operatedBizStatus },
                                        { "opCode", this.OperationNo },
                                        { "opName", this.OperationName },
                                    }
                                }.ToJson()
                            }
                        }
                    }) as CommonBillDTOResponse;

                responseResult?.OperationResult?.ThrowIfHasError(true, $"协同更新失败，对方系统未返回任何响应！");


                //更新状态
                dataEntity["fbizstatus"] = operatedBizStatus;
                dm.Save(dataEntity);

                //调用服务生成协同日志
                syncService.WriteLog(this.Context, this.HtmlForm, dataEntity["id"] as string, this.OperationNo, this.OperationName);
            }

            this.Result.SimpleMessage = this.OperationName+ "成功！";
            this.Result.IsSuccess = true;
            this.AddSetValueAction("fbizstatus", operatedBizStatus);
        }

        /// <summary>
        /// 获取操作后要更新的状态
        /// </summary>
        private string GetOperatedBizStatus()
        {
            switch (this.OperationNo.ToLower())
            {
                //确认 => 协同完成
                case "pricefirm":
                    return "business_status_09";
                //取消确认 => 待确认
                case "cancelconfirm":
                    return "business_status_05";
                //确认收货 => 已收货
                case "confirmreceiv":
                    return "business_status_10";
                //取消收货 => 协同完成
                case "cancelreceiv":
                    return "business_status_09";
                default:
                    throw new BusinessException("无效的操作码，请检查！");
            }
        }

        /// <summary>
        /// 获取供应商信息
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private DynamicObject GetSupplierById(DynamicObject dataEntity)
        {
            string supplierId = Convert.ToString(dataEntity["fsupplierid"]);
            if (supplierId.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException($"供应商ID为空！");

            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_supplier");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            var supplier = dm.Select(supplierId) as DynamicObject;
            if (supplier == null) throw new BusinessException($"供应商不存在！");

            return supplier;
        }

        /// <summary>
        /// 检查采购订单是否存在下游收支明细
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        private bool CheckIsExistsSettle(DynamicObject dataEntity)
        {
            if (this.OperationNo.EqualsIgnoreCase("cancelconfirm"))
            {
                var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "coo_incomedisburse");
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                string where = $"fmainorgid=@fmainorgid and fsourcetranid=@fsourcetranid";
                var sqlParam = new SqlParam[]
                {
                    new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
                    new SqlParam("fsourcetranid", System.Data.DbType.String, dataEntity["ftranid"] as string)
                };
                var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
                var dynObj = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();

                return dynObj == null;
            }
            return true;
        }
    }
}