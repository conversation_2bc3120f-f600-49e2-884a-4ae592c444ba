using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Pur;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.MS.API.Utils;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrder;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单，提交总部前，校验活动有效性
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("checkpromotion")]
    public class CheckPromotion : AbstractOperationServicePlugIn
    {

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //是否是《采购单重新提交总部》菜单过来的操作，此菜单允许重新下推数据至中台
            bool isTopOper = Convert.ToString(this.GetQueryOrSimpleParam<string>("istopoper")) == "1";

            var formPara = this.PageSession?.FormParameter;
            var errorMsg = "";
            Enu_DomainType domainType = Enu_DomainType.Bill;
            if (formPara is FormParameter)
            {
                domainType = ((FormParameter)formPara).DomainType;
            }

            e.Rules.Add(new SubmitHQValidation(domainType));

            string entryErrorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (this.Context.IsSecondOrg)
                {
                    return false;
                }
                return true;
            }).WithMessage("当前用户是二级分销商, 不允许提交总部！"));

            //http://dmp.jienor.com:81/zentao/task-view-34489.html 变更中的可以提交到总部
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string status = Convert.ToString(newData["fstatus"]);
                return (status.EqualsIgnoreCase("E") || Convert.ToString(newData["fchangestatus"]) == "1");
            }).WithMessage("采购订单【{0}】未审核不允许提交至总部 !", (billObj, propObj) => propObj["fbillno"]));

            //如果【变更状态】= "变更中" 且 【总部合同状态】不等于 "已终审" 时 , 要报错提示 "当前采购订单未提交过总部, 不允许向总部进行变更 !"
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return !(Convert.ToString(newData["fhqderstatus"]) != "03" && Convert.ToString(newData["fchangestatus"]) == "1");
            }).WithMessage("采购订单【{0}】未提交过总部, 不允许向总部进行变更!", (billObj, propObj) => propObj["fbillno"]));

            //增加判断如果 【总部变更状态】="提交至总部"或"驳回"时, 点击<提交变更>要报错提示"总部变更状态等于"提交至总部"或"驳回", 不允许提交变更!"
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMsg = string.Empty;
                var entry = newData["fentity"] as DynamicObjectCollection;
                //判断明细 是否存在 【总部变更状态】="提交至总部"或"驳回"
                bool exist = entry.Any(o => Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("01") || Convert.ToString(o["fhqderchgstatus"]).EqualsIgnoreCase("03"));
                if (!isTopOper && exist)
                {
                    errorMsg += $"总部变更状态等于'提交至总部'或'驳回', 不允许提交变更!";
                    return false;
                }
                else
                {
                    return true;
                }
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fhqderstatus = Convert.ToString(newData["fhqderstatus"]);
                string fchangestatus = Convert.ToString(newData["fchangestatus"]);
                //string fhqderchgstatus= Convert.ToString(newData["fhqderchgstatus"]);
                if (/*isTopOper||*/fhqderstatus.IsNullOrEmptyOrWhiteSpace())
                {
                    return true;
                }
                // 【总部合同状态】=【驳回】可【提交总部】
                //注意：下处条件改动注意更改BeginOperationTransaction中dataEntity_CHG中where判断条件
                // 变更状态=变更中 且【总部合同状态】=【已终审】且
                // （fhqderchgstatus【总部变更状态】为空 或 等于"驳回"(03) 或 等于“关闭”（02）时，后续需求已要求放开，这里暂屏蔽总部变更状态判断）可【提交总部】，因为变更单触发也是在此
                if ((isTopOper && fhqderstatus.EqualsIgnoreCase("02")
                //&& (fhqderchgstatus.IsNullOrEmptyOrWhiteSpace() || fhqderchgstatus.EqualsIgnoreCase("03") || fhqderchgstatus.EqualsIgnoreCase("02"))
                )
                || (fhqderstatus.EqualsIgnoreCase("05") || (fchangestatus.EqualsIgnoreCase("1") && fhqderstatus.EqualsIgnoreCase("03"))))
                {
                    return true;
                }

                return false;
            }).WithMessage("采购订单【{0}】已提交至总部, 不允许重复提交!", (billObj, propObj) => propObj["fbillno"]));

            // 41307 采购订单的供应商与送达方不匹配，提交至总部校验，提示：当前供应商与送达方不匹配，请修改后再提交
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                IPurchaseOrderService purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
                var flag = true;
                // 根据送达方带出供应商
                string deliverId = Convert.ToString(newData["fdeliverid"]);
                string fsupplierid = Convert.ToString(newData["fsupplierid"]);

                var supplierId = purchaseOrderService.GetSupplierIdByDeliverId(Context, deliverId);

                if (fsupplierid != supplierId) { flag = false; }

                return flag;
            }).WithMessage("当前经销商与送达方不匹配，请修改后再提交总部!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //当采购订单【第三方来源】=“三维家”且【第三方单号】不为空时，则点击<提交总部>按钮时，
                //需要校验（【启用定制OMS】=“是”）或者（【启用定制OMS】=“否”且【门店已审核】=TRUE）才允许提交总部，
                //否则需提示：“当前采购订单三维家中未进行门店审核，不允许提交总部。”
                if (!newData["fthirdsource"].IsNullOrEmptyOrWhiteSpace() && newData["fthirdsource"].Equals("三维家") && !newData["fthirdbillno"].IsNullOrEmptyOrWhiteSpace())
                {
                    if (!Convert.ToBoolean(newData["fomsservice"]) && !Convert.ToBoolean(newData["fswjorderstate"]))
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("当前采购订单三维家中未进行门店审核，不允许提交总部。"));

        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            //var simpleData = this.SimpleData["simpleData"].FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
            //if (e.DataEntitys == null || e.DataEntitys.Count() == 0)
            //{
            //    return;
            //}
            string domainType = this.GetQueryOrSimpleParam("domainType", "");
            string id = this.GetQueryOrSimpleParam("id", "");

            List<DynamicObject> purOrderItem = new List<DynamicObject>();
            if (domainType == "list")
            {
                purOrderItem = this.Context.LoadBizDataById(this.HtmlForm.Id, id.Split(',').ToList(), true);
            }
            else
            {
                purOrderItem.Add(this.Context.LoadBizDataById(this.HtmlForm.Id, id, true));
            }
            if (purOrderItem == null || purOrderItem.Count == 0)
            {
                this.Result.IsSuccess = false;
                if (domainType == "list")
                {
                    this.Result.ComplexMessage.ErrorMessages.Add("未查询到采购订单！");
                }
                else
                {
                    this.Result.SrvData = new { code = "10001", mes = new List<string>() { "未查询到采购订单！" } };//这个不做确认的提示，直接提示。拒绝提交总部操作
                }
                //this.Result.SimpleMessage = "未查询到采购订单！";
                return;
            }
            //需要过滤掉总部已终审，并且在变更中的数据
            var objs = purOrderItem.Where(a => (!Convert.ToString(a["fhqderstatus"]).Equals("03")) || (Convert.ToBoolean(a["frenewalflag"]) || Convert.ToBoolean(a["frenewalrectag"]))).ToList();
            if (objs == null || objs.Count == 0)
            {
                this.Result.SrvData = new
                {
                    code = "10000",
                    mes = new List<string>() { "校验成功，数据正常" },
                    successid = purOrderItem.Select(a => Convert.ToString(a["id"])).ToList()
                };
                this.Result.IsSuccess = true;//数据正常，正常提交总部
                return;
            }
            UpdatePurOrderProd(objs, domainType);
        }

        /// <summary>
        ///新增需求 --要优先判断活动商品是否有效，如果有效就执行原逻辑，
        ///先校验活动=》再校验套餐=》在校验商品
        //存在过期的商品，如果还满足子组号的比例，则把原过期的商品去掉活动标签，
        //不存在过期的商品，则还是执行原逻辑。
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        public void UpdatePurOrderProd(List<DynamicObject> dynamicObjects, string domainType)
        {
            var date = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd"));//.AddYears(1)
            List<string> combinemessage = new List<string>();
            List<string> proScheme = new List<string>();
            List<string> successbillno = new List<string>();//正常提交总部的单据
            List<string> failbillno = new List<string>();//不符合提交总部的单据
            List<string> failmes = new List<string>();//不符合的原因
            string mes = "";
            bool flag = false;
            foreach (var dynamicObject in dynamicObjects)
            {
                string billno = Convert.ToString(dynamicObject["fbillno"]);
                string billtype = Convert.ToString(dynamicObject["fbilltypeid"]);
                DynamicObjectCollection fentry = dynamicObject["fentity"] as DynamicObjectCollection;
                var combinenumberList = fentry.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["fcombinenumber"]))).Select(c => Convert.ToString(c["fcombinenumber"]).Trim()).Distinct();//已经匹配到套餐
                if ((combinenumberList == null || combinenumberList.Count() <= 0) || billtype == "ydj_purchaseorder_zb")
                {
                    //可以正常提交总部的数据
                    successbillno.Add(billno);
                    continue;
                }
                #region 先判断活动有效期
                var promotioncombine = this.Context.LoadBizDataByFilter("bas_promotioncombine", $"fnumber in('{string.Join("','", combinenumberList)}')");
                var promotionscheme = this.Context.LoadBizDataById("bas_promotionscheme", promotioncombine.Select(a => Convert.ToString(a["fpromotionid"])).ToList());
                //先校验活动，活动过期了就直接返回
                foreach (var item in promotionscheme)
                {
                    if (date < DateTime.Parse(item["fbegindate"].ToString()) || date > DateTime.Parse(item["fenddate"].ToString()))
                    {
                        proScheme.Add(Convert.ToString(item["fnumber"]));
                    }
                }
                if (proScheme.Count > 0)
                {
                    flag = true;
                    clearcombine(fentry);
                    if (domainType == "list")
                    {
                        mes = string.Format("采购订单{0}参与的促销活动时间已过期，已取消优惠折扣，未提交总部！", billno);
                        this.Result.ComplexMessage.ErrorMessages.Add(mes);
                    }
                    else
                    {
                        mes = string.Format("促销活动“{0}”活动已过期，已取消优惠折扣，是否继续提交总部！", string.Join("”，“", proScheme));
                        failmes.Add(mes);
                    }
                    failbillno.Add(Convert.ToString(dynamicObject["fbillno"]));
                    continue;
                }
                #endregion
                if (flag && domainType != "list")
                {
                    break;
                }
                List<string> expiredcombine = new List<string>();
                #region 再判断套餐有效期
                foreach (var item in combinenumberList)
                {
                    List<string> expireprod = new List<string>();
                    var combine = promotioncombine.Where(c => c["fnumber"].ToString() == item).FirstOrDefault();
                    if (combine == null)
                    {
                        flag = true;
                        //未查到套餐明细，清空
                        if (domainType == "list")
                        {
                            mes = string.Format("采购订单{0}参与的促销活动{1}时间未查询到，未提交总部！", billno, item);
                            this.Result.ComplexMessage.ErrorMessages.Add(mes);
                        }
                        else
                        {
                            mes = string.Format("促销活动{0}未查询到，是否继续提交总部！", item);
                            failmes.Add(mes);
                            //this.Result.SimpleMessage = mes;

                        }
                        failbillno.Add(Convert.ToString(dynamicObject["fbillno"]));
                        clearcombine(fentry.Where(a => Convert.ToString(a["fcombinenumber"]) == item).ToList());
                        continue;
                    }
                    if (date < DateTime.Parse(combine["fbegindate"].ToString()) || date > DateTime.Parse(combine["fenddate"].ToString()))
                    {
                        flag = true;
                        clearcombine(fentry);
                        if (domainType == "list")
                        {
                            mes = string.Format("采购订单{0}参与的组合促销套餐时间已过期，已取消优惠折扣，未提交总部！", billno);
                            this.Result.ComplexMessage.ErrorMessages.Add(mes);
                        }
                        else
                        {
                            //mes = string.Format("采购订单{0}参与的组合促销套餐时间已过期，已取消优惠折扣，未提交总部！", billno);
                            expiredcombine.Add(item);
                            //failmes.Add(mes);
                            //this.Result.SimpleMessage = mes;

                        }
                        failbillno.Add(Convert.ToString(dynamicObject["fbillno"]));
                        continue;
                    }
                }

                #endregion
                //记录过期商品集合

                if (flag && domainType != "list")
                {
                    mes = string.Format("组合促销套餐“{0}”时间已过期，已取消优惠折扣，是否继续提交总部！", string.Join("”、“", expiredcombine));
                    failmes.Add(mes);
                    break;
                }
                #region 再判断商品有效期
                foreach (var item in combinenumberList)
                {
                    List<string> expireprod = new List<string>();
                    var combine = promotioncombine.Where(c => c["fnumber"].ToString() == item).FirstOrDefault();
                    if (combine == null) continue;
                    //套餐明细商品
                    var fcombineentry = combine["fcombineentry"] as DynamicObjectCollection;
                    //订单明细商品
                    var orderprod = fentry.Where(c => fcombineentry.Select(m => m["fproductid"].ToString()).Contains(c["fmaterialid"].ToString())).ToList();
                    //1 先查看套餐商品是否有过期时间
                    foreach (var com in fcombineentry)
                    {
                        if (date < DateTime.Parse(com["fproductbegindate"].ToString()) || date > DateTime.Parse(com["fproductenddate"].ToString()))
                        {
                            expireprod.Add(com["fproductid"].ToString());
                        }
                    }
                    //没有过期商品,说明套餐正常，进行下次循环判断
                    if (expireprod.Count <= 0) continue;

                    //有过期商品的话，则需要
                    //1 判断子组号是否满足套餐，不满足套餐就要去掉套餐标签
                    var combinefeoupnumber = fcombineentry.Select(c => c["fgroupnumber"].ToString()).Distinct().ToList();
                    //过期商品的子组号
                    //var expireprodgroupnumber = fcombineentry.Where(c => expireprod.Contains(c["fproductid"].ToString())).Select(c => c["fgroupnumber"].ToString()).Distinct();

                    var orderentry = fentry.Where(c => Convert.ToString(c["fcombinenumber"]).Equals(item)).ToList();
                    //判断订单里面打标签的商品去掉过期商品 是否还满足套餐
                    var orderotherprod = orderprod.Where(c => !expireprod.Contains(c["fmaterialid"].ToString())).ToList();
                    var expireprodgroupnumber = fcombineentry.Where(c => orderotherprod.Select(m => m["fmaterialid"].ToString()).Contains(c["fproductid"].ToString())).Select(c => c["fgroupnumber"].ToString()).Distinct().ToList();

                    if (combinefeoupnumber.Count() != expireprodgroupnumber.Count())
                    {
                        var entrys = orderentry.Where(c => expireprod.IndexOf(c["fmaterialid"].ToString()) >= 0).ToList();
                        if (entrys.Count == 0)
                        {
                            continue;
                        }
                        string matids = string.Join("\"、\"", entrys.Select(a => Convert.ToString((a["fmaterialid_ref"] as DynamicObject)["fnumber"])).ToList());
                        mes = string.Format("采购订单{0}商品\"{1}\"，参与组合促销套餐有效时间已过期，已取消优惠折扣，未提交总部！", billno, matids);
                        failbillno.Add(Convert.ToString(dynamicObject["fbillno"]));
                        if (domainType == "list")
                        {
                            this.Result.ComplexMessage.ErrorMessages.Add(mes);
                        }
                        else
                        {
                            mes = string.Format("商品\"{0}\"，参与组合促销套餐“{1}”有效时间已过期，已取消优惠折扣，是否继续提交总部！", matids, item);
                            failmes.Add(mes);
                            //this.Result.SimpleMessage = mes;

                        }
                        this.clearcombine(orderentry);
                        combinemessage.Add(combine["fdescription"].ToString());
                    }
                    //2 满足子组号的话，则需要去掉过期商品的套餐标签
                    else
                    {
                        var entrys = orderentry.Where(c => expireprod.IndexOf(c["fmaterialid"].ToString()) >= 0).ToList();
                        if (entrys.Count == 0)
                        {
                            continue;
                        }
                        //string matids = string.Join("\",\"", entrys.Select(a => Convert.ToString(a["fmaterialid"])).ToList());
                        string matids = string.Join("\",\"", entrys.Select(a => Convert.ToString((a["fmaterialid_ref"] as DynamicObject)["fnumber"])).ToList());
                        failbillno.Add(Convert.ToString(dynamicObject["fbillno"]));
                        if (domainType == "list")
                        {
                            mes = string.Format("采购订单{0}商品\"{1}\"，参与组合促销套餐有效时间已过期，已取消优惠折扣，未提交总部！", billno, matids);
                            this.Result.ComplexMessage.ErrorMessages.Add(mes);
                        }
                        else
                        {
                            mes = string.Format("商品\"{0}\"，参与组合促销套餐“{1}”有效时间已过期，已取消优惠折扣，是否继续提交总部！", matids, item);
                            failmes.Add(mes);
                            //this.Result.SimpleMessage = string.Format("采购订单{0}商品\"{1}\"，参与组合促销套餐有效时间已过期，已取消优惠折扣，未提交总部！", billno, matids);
                        }
                        failbillno.Add(Convert.ToString(dynamicObject["fbillno"]));
                        clearcombine(entrys);
                    }
                }
                #endregion

            }
            {
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                //dm.Save(purOrderItem);//保存采购订单
                successbillno = dynamicObjects.Where(a => failbillno.IndexOf(Convert.ToString(a["fbillno"])) < 0).Select(a => Convert.ToString(a["id"])).ToList();
                if (failbillno.Count == 0)
                {
                    //符合条件，正常提价总部
                    this.Result.SrvData = new { code = "10000", mes = new List<string>() { "校验成功，数据正常" }, successid = successbillno };
                    this.Result.IsSuccess = true;//数据正常，正常提交总部
                }
                else
                {
                    this.Result.SrvData = new { code = "10002", mes = failmes, successid = successbillno };//做提示，让用户决定是否提交
                    this.Result.IsSuccess = false;//数据正常，正常提交总部
                }
            }


        }
        private void clearcombine(DynamicObject purItem)
        {
            //促销活动
            purItem["fpromotion"] = "";
            //组合促销编号
            purItem["fcombinenumber"] = "";
            //描述
            purItem["fcombineremark"] = "";
            //折扣率
            purItem["fcombinerate"] = 0;
            //套餐组合基数
            purItem["fcombineqty"] = 0;
        }
        private void clearcombine(DynamicObjectCollection purItems)
        {
            foreach (var item in purItems)
            {
                //促销活动
                item["fpromotion"] = "";
                //组合促销编号
                item["fcombinenumber"] = "";
                //描述
                item["fcombineremark"] = "";
                //折扣率
                item["fcombinerate"] = 0;
                //套餐组合基数
                item["fcombineqty"] = 0;
            }
        }
        private void clearcombine(IEnumerable<DynamicObject> purItems)
        {
            foreach (var item in purItems)
            {
                //促销活动
                item["fpromotion"] = "";
                //组合促销编号
                item["fcombinenumber"] = "";
                //描述
                item["fcombineremark"] = "";
                //折扣率
                item["fcombinerate"] = 0;
                //套餐组合基数
                item["fcombineqty"] = 0;
            }
        }
    }
}
