using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System.Linq;
using System.Collections.Generic;
using System.Data;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 根据对应表单和主键id得到采购订单总部订单号集合
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("gethqordernobyforminfo")]
    public class GetHqordernoByFormInfo : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var formid = this.GetQueryOrSimpleParam<string>("hformid", "");
            var fid = this.GetQueryOrSimpleParam<string>("id", "");
            var fmaterid = this.GetQueryOrSimpleParam<string>("materid", "");
            bool success = false;
            DynamicObject result = null;
            string sql = string.Empty;
            List<SqlParam> param = new List<SqlParam>();
            if (!formid.IsNullOrEmptyOrWhiteSpace() && !fid.IsNullOrEmptyOrWhiteSpace() && !fmaterid.IsNullOrEmptyOrWhiteSpace())
            {
                switch (formid)
                {
                    case "ydj_order":
                        sql = @"select top 1 p.fhqderno from t_ydj_order o
						inner join t_ydj_orderentry oe on o.fid=oe.fid
                        inner join T_BD_MATERIAL d on oe.fproductid = d.fid  
						inner join t_ydj_purchaseorder p on p.fsourcetype='ydj_order'and p.fsourcenumber=o.fbillno
                        inner join t_ydj_poorderentry pd on pd.fid = p.fid and oe.fproductid=pd.fmaterialid and oe.fentryid=pd.fsourceentryid
                        where o.fid =@fid and oe.fproductid=@fmaterid
                        and o.fstatus='E' and p.fstatus='E' order by p.fmodifydate desc";
                        param.Add(new SqlParam("@fid", DbType.String, fid));
                        param.Add(new SqlParam("@fmaterid", DbType.String, fmaterid));
                        result = this.Context.ExecuteDynamicObject(sql, param).FirstOrDefault();
                        success = true;
                        break;
                    default:
                        break;
                }
            }
            this.Result.IsSuccess = success;
            this.Result.SrvData = new Dictionary<string, string> {
                { "fhqderno",result?["fhqderno"].ToString() }
            };
        }
    }
}
