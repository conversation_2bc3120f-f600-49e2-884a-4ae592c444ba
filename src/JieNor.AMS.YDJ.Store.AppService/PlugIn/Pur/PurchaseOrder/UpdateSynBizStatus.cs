using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Data;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.Serialization;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Interface.Log;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：更新业务状态，该操作主要用于接收销售订单发起的协同业务状态更新
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("UpdateSynBizStatus")]
    public class UpdateSynBizStatus : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //单据参数，json格式字符串（对象数组）
            var orderParamStr = this.GetQueryOrSimpleParam<string>("orderParam", "");
            if (orderParamStr.IsNullOrEmptyOrWhiteSpace()) return;

            //根据多个流水号批量查询订单信息
            StringBuilder sbInTranId = new StringBuilder();
            List<SqlParam> paramList = new List<SqlParam>();
            List<Dictionary<string, string>> orderParamList = orderParamStr?.FromJson<List<Dictionary<string, string>>>() ?? new List<Dictionary<string, string>>();
            for (int i = 0; i < orderParamList.Count; i++)
            {
                var paramName = "@tranid" + i;
                sbInTranId.Append(paramName).Append(",");
                paramList.Add(new SqlParam(paramName, System.Data.DbType.String, orderParamList[i]["tranId"]));
            }
            if (sbInTranId.IsNullOrEmptyOrWhiteSpace()) return;

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            string where = $"fmainorgid=@fmainorgid and ftranid in({sbInTranId.ToString().TrimEnd(',')})";
            paramList.Add(new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company));

            var dataReader = this.Context.GetPkIdDataReader(this.HtmlForm, where, paramList);
            var dataEntitys = dm.SelectBy(dataReader).OfType<DynamicObject>();
            if (dataEntitys != null && dataEntitys.Count() > 0)
            {
                this.Option.SetIgnoreOpLogFlag();
                var syncService = this.Container.GetService<ISynergyService>();

                foreach (var dataEntity in dataEntitys)
                {
                    string tranId = Convert.ToString(dataEntity["ftranid"]);
                    Dictionary<string, string> orderParam = orderParamList.FirstOrDefault(t => t["tranId"].EqualsIgnoreCase(tranId));

                    string bizStatus = orderParam["bizStatus"];
                    string opCode = orderParam["opCode"];
                    string opName = orderParam["opName"];
                    string pickDate = orderParam["pickDate"];
                    DateTime opDate = DateTime.Now;
                    string opDateStr = orderParam.GetValue("opDate", "");
                    if (!DateTime.TryParse(opDateStr, out opDate))
                    {
                        opDate = DateTime.Now;
                    }
                    if (bizStatus.IsNullOrEmptyOrWhiteSpace()) continue;

                    //销售方受理时更新采购订单的供货方订单号
                    if (opCode.EqualsIgnoreCase("accase") && orderParam.ContainsKey("supplierOrderNo"))
                    {
                        dataEntity["fsupplierorderno"] = orderParam["supplierOrderNo"];
                    }
                    else if (opCode.EqualsIgnoreCase("cancelaccase"))
                    {
                        //销售方取消受理时清空采购订单的供货方订单号
                        dataEntity["fsupplierorderno"] = "";
                    }else if (opCode.EqualsIgnoreCase("Chargeback"))
                    {
                        //销售方退单，设置退单状态为已退单
                        dataEntity["fchargebackstatus"] = "1";
                        dataEntity["fchargebackreason"] = orderParam["chargebackReason"];
                    }else if(opCode.EqualsIgnoreCase("transmit") && orderParam.ContainsKey("supplierOldOrderNo"))
                    {
                        //销售方传送时,更新销售方传回的供货方正单号
                        dataEntity["fsupplieroldorderno"] = orderParam["supplierOldOrderNo"];
                    }

                    //设置业务状态
                    dataEntity["fbizstatus"] = bizStatus;

                    //设置交货日期
                    if (false == string.IsNullOrWhiteSpace(pickDate))
                    {
                        dataEntity["fpickdate"] = Convert.ToDateTime(pickDate);
                    }

                    //处理商品明细
                    string jsonEntrys = "";
                    if (orderParam.ContainsKey("entrys")) jsonEntrys = orderParam["entrys"];
                    this.ProcProductEntrys(opCode, jsonEntrys, dataEntity);

                    //转换辅助属性字段值
                    this.ChainDataIdToLocalId(dataEntity, orderParam["chainDataJson"]);

                    //处理商品子明细
                    string jsonDetails = "";
                    if (orderParam.ContainsKey("details")) jsonDetails = orderParam["details"];
                    this.ProcProductEntryDetails(opCode, jsonDetails, dataEntity);

                    //调用服务生成协同日志
                    syncService.WriteLog(this.Context, this.HtmlForm, dataEntity["id"] as string, opCode, opName, this.Context.CallerContext, opDate);
                }

                var prepareService = this.Container.GetService<IPrepareSaveDataService>();
                prepareService.PrepareDataEntity(this.Context, this.HtmlForm, dataEntitys.ToArray(), OperateOption.Create());

                //保存
                dm.Save(dataEntitys);

                //标记成功
                this.Result.IsSuccess = true;
            }
        }

        /// <summary>
        /// 处理商品明细
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <param name="synProducts"></param>
        /// <returns></returns>
        private void ProcProductEntrys(string opCode, string jsonEntrys, DynamicObject dataEntity)
        {
            DynamicObjectCollection entitys = null;
            
            switch (opCode.ToLower())
            {
                //销售方报价确认：更新商品明细
                case "transmit":
                //销售方退单:刷新商品明细
                case "chargeback":

                    if (jsonEntrys.IsNullOrEmptyOrWhiteSpace()) return;

                    var synEntrys = jsonEntrys?.FromJson<List<Dictionary<string, object>>>();
                    if (synEntrys == null || synEntrys.Count <= 0) return;

                    entitys = dataEntity["fentity"] as DynamicObjectCollection;

                    //本地商品集合
                    var products = this.GetLocalDataByChainDataIds(synEntrys, "fmaterialid");

                    //本地单位集合
                    var units = this.GetLocalDataByChainDataIds(synEntrys, "funitid");

                    //本地业务单位集合
                    var bizUnits = this.GetLocalDataByChainDataIds(synEntrys, "fbizunitid");

                    //处理销售方新增的商品明细
                    var htmlEntry = this.HtmlForm.GetEntryEntity("fentity");
                    foreach (var synEntry in synEntrys)
                    {
                        //存在则更新
                        var entity = entitys.FirstOrDefault(o =>
                            {
                                //销售方新增行二次修改回写的行
                                bool existRowObj = Convert.ToString(synEntry["fentryid"]).EqualsIgnoreCase(Convert.ToString(o["ftranid"]));

                            existRowObj = existRowObj || !o["id"].IsNullOrEmptyOrWhiteSpace()
                            && !synEntry["fsourceentryid"].IsNullOrEmptyOrWhiteSpace()
                            && Convert.ToString(synEntry["fsourceentryid"]).EqualsIgnoreCase(Convert.ToString(o["id"]));

                            return existRowObj;
                        });
                        if (entity != null)
                        {
                            entity["fmaterialid"] = synEntry["fmaterialid"];
                            entity["funitid"] = synEntry["funitid"];
                            entity["fqty"] = synEntry["fqty"];
                            entity["fdistrate"] = synEntry["fdistrate"];
                            entity["fcustomdes_e"] = synEntry["fcustomdes"];
                            entity["fmtono"] = synEntry.GetValue("fmtono","");
                            entity["factualspec"] = synEntry.GetValue("factualspec", "");

                            if (!Convert.ToString(synEntry["fentryid"]).EqualsIgnoreCase(Convert.ToString(entity["ftranid"])))
                            {
                                //设为总部修改
                                entity["fcooeditstatus"] = "1";
                            }

                            //如果传递了价格，则以传递的价格为准
                            decimal synPrice = 0M;
                            if (synEntry.ContainsKey("fprice")) synPrice = Convert.ToDecimal(synEntry["fprice"]);
                            if (synPrice > 0)
                            {
                                entity["fprice"] = synPrice;
                            }
                        }
                        else
                        {
                            //不存在则新增
                            if (synEntry["fsourceentryid"].IsNullOrEmptyOrWhiteSpace() && !synEntry["fentryid"].IsNullOrEmptyOrWhiteSpace())
                            {
                                entity = new DynamicObject(htmlEntry.DynamicObjectType);
                                entity["fmaterialid"] = synEntry["fmaterialid"];
                                entity["funitid"] = synEntry["funitid"];
                                entity["fqty"] = synEntry["fqty"];
                                entity["fprice"] = synEntry["fprice"];
                                entity["famount"] = 0;
                                entity["fdistrate"] = synEntry["fdistrate"];
                                entity["fdistamount"] = 0;
                                entity["fdealprice"] = 0;
                                entity["fdealamount"] = 0;
                                entity["fcustomdes_e"] = synEntry["fcustomdes"];
                                entity["fmtono"] = synEntry.GetValue("fmtono", "");
                                entity["factualspec"] = synEntry.GetValue("factualspec", "");
                                //entity["fsourceentryid_e"] = synEntry["fentryid"];
                                entity["ftranid"] = synEntry["fentryid"];
                                //设为总部新增
                                entity["fcooeditstatus"] = "2";
                                //entity["ftranid"] = synEntry["ftranid"];
                                entitys.Add(entity);
                            }
                        }

                        if (entity != null)
                        {
                            //采购单位和采购数量默认等于基本单位和基本单位数量
                            entity["fbizunitid"] = synEntry["funitid"];
                            entity["fbizqty"] = synEntry["fqty"];

                            //如果传递了采购单位和采购数量，则以传递的为准
                            object bizUnitId = "";
                            synEntry.TryGetValue("fbizunitid", out bizUnitId);
                            if (!bizUnitId.IsNullOrEmptyOrWhiteSpace())
                            {
                                entity["fbizunitid"] = bizUnitId;
                            }
                            object bizQty = 0;
                            synEntry.TryGetValue("fbizqty", out bizQty);
                            decimal bizQtyNum = 0;
                            decimal.TryParse(Convert.ToString(bizQty), out bizQtyNum);
                            if (bizQtyNum > 0)
                            {
                                entity["fbizqty"] = bizQtyNum;
                            }
                        }

                        this.CalculateProductEntry(entity, products, units, bizUnits, synEntry);
                    }

                    //处理总部删除的行
                    foreach(var entity in entitys)
                    {
                        //存在则更新
                        var synObjRow = synEntrys.FirstOrDefault(synEntry =>
                        {
                            //销售方新增行二次修改回写的行
                            bool existRowObj = Convert.ToString(synEntry["fentryid"]).EqualsIgnoreCase(Convert.ToString(entity["ftranid"]));

                            existRowObj = existRowObj || !entity["id"].IsNullOrEmptyOrWhiteSpace()
                            && !synEntry["fsourceentryid"].IsNullOrEmptyOrWhiteSpace()
                            && Convert.ToString(synEntry["fsourceentryid"]).EqualsIgnoreCase(Convert.ToString(entity["id"]));

                            return existRowObj;
                        });
                        if (synObjRow == null)
                        {
                            //总部已删除
                            entity["fcooeditstatus"] = "3";
                        }
                    }

                    break;

                //销售方取消报价确认
                case "canceltransmit":

                    //删除销售方同步过来的商品明细
                    entitys = dataEntity["fentity"] as DynamicObjectCollection;
                    if (entitys != null)
                    {
                        List<DynamicObject> toRemoveList = new List<DynamicObject>();
                        foreach (var entity in entitys)
                        {
                            //如果是总部新增的行则删除还原
                            if (Convert.ToString(entity["fcooeditstatus"]) == "2")
                            {
                                toRemoveList.Add(entity);
                            }
                            //如果是总部删除的行则还原为正常
                            if (Convert.ToString(entity["fcooeditstatus"]) == "3")
                            {
                                entity["fcooeditstatus"] = "0";
                            }
                        }
                        foreach (var item in toRemoveList)
                        {
                            entitys.Remove(item);
                        }
                    }
                    break;
                case "accase":
                    if (jsonEntrys.IsNullOrEmptyOrWhiteSpace()) return;

                    var syncEntrys = jsonEntrys?.FromJson<List<Dictionary<string, object>>>();
                    if (syncEntrys == null || syncEntrys.Count <= 0) return;

                    entitys = dataEntity["fentity"] as DynamicObjectCollection;
                    if (entitys != null)
                    {
                        for (var i = 0; i < entitys.Count; i++)
                        {
                            var entity = entitys[i];
                            var product = this.Context.LoadBizDataById("ydj_product", entity["fmaterialid"].ToString());
                            if (Convert.ToBoolean(product["fissuit"]))
                            {
                                var fornumber = syncEntrys[i]["FPXPM"].ToString(); //返回的套件选配码
                                var orinumber = entity["fselectionnumber"].ToString();
                                if (fornumber == orinumber) continue;

                                var fromchaindataid = syncEntrys[i]["fmaterialid"].ToString();

                                //更新套件选配码为K3的选配码
                                //var sql = "UPDATE t_mt_suiteselection SET fnumber = @fornumber, fname = @fornumber WHERE fnumber = @orinumber";
                                var sql = @"/*dialect*/UPDATE t_mt_suiteselection SET fnumber = @fornumber, fname = @fornumber
FROM t_mt_suiteselection a
INNER JOIN t_bd_material b ON a.fproductid = b.fid
WHERE a.fnumber = @orinumber AND b.ffromchaindataid = @fromchaindataid AND a.fmainorgid = @mainorgid";
                                this.DBService.ExecuteDynamicObject(
                                    this.Context, 
                                    sql, 
                                    new[] { 
                                        new SqlParam("fornumber", DbType.String, fornumber),
                                        new SqlParam("orinumber", DbType.String, orinumber),
                                        new SqlParam("fromchaindataid", DbType.String, fromchaindataid),
                                        new SqlParam("mainorgid", DbType.String, this.Context.Company)
                                    }
                                );
                                entity["fselectionnumber"] = fornumber;

                                if (!syncEntrys[i]["partsselection"].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var dyobjs = syncEntrys[i]["partsselection"].ToString().FromJson<List<Dictionary<string,string>>>();
                                    foreach (var ps in dyobjs)
                                    {
                                        //suite_xpm,suite_materialid,parts_xpm, parts_materialid
                                        sql = @"/*dialect*/UPDATE t_mt_partsselection SET fnumber = @parts_xpm, fname = @parts_xpm
FROM t_mt_partsselection c 
INNER JOIN t_bd_material cm ON c.fproductid = cm.fid
INNER JOIN t_mt_suiteselectionentry b ON b.fpartsselectionid = c.fid
INNER JOIN t_mt_suiteselection a ON a.fid = b.fid
INNER JOIN t_bd_material am ON a.fproductid = am.fid
WHERE a.fnumber = @suite_xpm AND am.ffromchaindataid = @suite_materialid AND cm.ffromchaindataid = @parts_materialid AND c.fmainorgid = @mainorgid";
                                        this.DBService.ExecuteDynamicObject(
                                            this.Context,
                                            sql,
                                            new[] {
                                                new SqlParam("parts_xpm", DbType.String, ps["parts_xpm"]),
                                                new SqlParam("suite_xpm", DbType.String, ps["suite_xpm"]),
                                                new SqlParam("suite_materialid", DbType.String, ps["suite_materialid"]),
                                                new SqlParam("parts_materialid", DbType.String, ps["parts_materialid"]),
                                                new SqlParam("mainorgid", DbType.String, this.Context.Company)
                                            }
                                        );
                                    }
                                }
                            }
                            else
                            {
                                var fornumber = syncEntrys[i]["FXPM"].ToString(); //返回的子件选配码
                                var orinumber = entity["fselectionnumber"].ToString();
                                if (fornumber == orinumber) continue;
                                var fromchaindataid = syncEntrys[i]["fmaterialid"].ToString();
                                //更新子件选配码为K3的选配码
                                //var sql = "UPDATE t_mt_partsselection SET fnumber = @fornumber, fname = @fornumber WHERE fnumber = @orinumber";
                                var sql = @"/*dialect*/UPDATE t_mt_partsselection SET fnumber = @fornumber, fname = @fornumber
FROM t_mt_partsselection a
INNER JOIN t_bd_material b ON a.fproductid = b.fid
WHERE a.fnumber = @orinumber AND b.ffromchaindataid = @fromchaindataid AND a.fmainorgid = @mainorgid";
                                this.DBService.ExecuteDynamicObject(
                                    this.Context,
                                    sql,
                                    new[] {
                                        new SqlParam("fornumber", DbType.String, fornumber),
                                        new SqlParam("orinumber", DbType.String, orinumber),
                                        new SqlParam("fromchaindataid", DbType.String, fromchaindataid),
                                        new SqlParam("mainorgid", DbType.String, this.Context.Company)
                                    }
                                );
                                entity["fselectionnumber"] = fornumber;

                                var pfornumber = syncEntrys[i]["FPXPM"].ToString(); //返回的套件选配码
                                if(!pfornumber.IsNullOrEmptyOrWhiteSpace() && pfornumber != fornumber) //有套件选配码
                                {
                                    //更新套件选配码
                                    sql = @"/*dialect*/UPDATE t_mt_suiteselection SET fnumber = @fornumber, fname = @fornumber
FROM t_mt_suiteselection t
INNER JOIN t_mt_suiteselectionentry te ON t.fid = te.fid
INNER JOIN t_mt_partsselection a ON te.fpartsselectionid = a.fid
INNER JOIN t_bd_material b ON a.fproductid = b.fid
WHERE a.fnumber = @orinumber AND b.ffromchaindataid = @fromchaindataid AND a.fmainorgid = @mainorgid";
                                    this.DBService.ExecuteDynamicObject(
                                        this.Context,
                                        sql,
                                        new[] {
                                            new SqlParam("fornumber", DbType.String, pfornumber),
                                            new SqlParam("orinumber", DbType.String, orinumber),
                                            new SqlParam("fromchaindataid", DbType.String, fromchaindataid),
                                            new SqlParam("mainorgid", DbType.String, this.Context.Company)
                                        }
                                    );

                                }
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            if (entitys != null)
            {
                //成交金额
                var billAmount = entitys.Where(o=>Convert.ToString(o["fcooeditstatus"])!="3")
                    .Select(o => (decimal)o["fdealamount"])
                    .Sum();
                dataEntity["ffbillamount"] = billAmount;

                //货品原值
                var amount = entitys.Where(o => Convert.ToString(o["fcooeditstatus"]) != "3")
                    .Select(o => (decimal)o["famount"])
                    .Sum();
                dataEntity["fdealamount"] = amount;

                //折扣额
                var distAmount = entitys.Where(o => Convert.ToString(o["fcooeditstatus"]) != "3")
                    .Select(o => (decimal)o["fdistamount"])
                    .Sum();
                dataEntity["fdistamount"] = distAmount;

                //待结算金额 = 订单金额 - 已结算金额 - 待确认金额
                dataEntity["fpayamount"] = billAmount - Convert.ToDecimal(dataEntity["fpaidamount"]) - Convert.ToDecimal(dataEntity["fconfirmamount"]);
            }
        }

        /// <summary>
        /// 计算商品明细
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="products"></param>
        /// <param name="units"></param>
        /// <param name="bizUnits"></param>
        /// <param name="dctSyncEntry"></param>
        private void CalculateProductEntry(DynamicObject entity,IEnumerable<DynamicObject> products, 
            IEnumerable<DynamicObject> units, 
            IEnumerable<DynamicObject> bizUnits,
            Dictionary<string,object> dctSyncEntry)
        {
            if (entity == null) return;

            //根据云链Id转换成对应的本地基础资料Id
            var product = products?.FirstOrDefault(t => (t["ffromchaindataid"] as string).EqualsIgnoreCase(entity["fmaterialid"] as string));
            if (product != null)
            {
                entity["fmaterialid"] = product["id"];
            }
            else
            {
                entity["fmaterialid"] = "";
            }

            var unit = units?.FirstOrDefault(t => (t["ffromchaindataid"] as string).EqualsIgnoreCase(entity["funitid"] as string));
            if (unit != null)
            {
                entity["funitid"] = unit["id"];
            }
            else
            {
                entity["funitid"] = "";
            }

            var bizUnit = bizUnits?.FirstOrDefault(t => (t["ffromchaindataid"] as string).EqualsIgnoreCase(entity["fbizunitid"] as string));
            if (bizUnit != null)
            {
                entity["fbizunitid"] = bizUnit["id"];
            }
            else
            {
                entity["fbizunitid"] = entity["funitid"];
                entity["fbizqty"] = entity["fqty"];
            }

            decimal price = Convert.ToDecimal(entity["fprice"]);
            decimal qty = Convert.ToDecimal(entity["fqty"]);

            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;

            //防止再次运算产生尾差，直接使用协同方算好的数据
            if (dctSyncEntry.ContainsKey("famount"))
            {
                amount = Convert.ToDecimal(dctSyncEntry["famount"]);
            }

            //折扣率
            var distRate = Convert.ToDecimal(entity["fdistrate"]);
            distRate = distRate >= 0 ? distRate : 10;

            //表体成交单价默认等于单价
            decimal dealPrice = price, dealAmount = 0;
            
            //表体成交单价 =单价*折扣率*10/100;
            if (qty != 0)
            {
                dealPrice = price * distRate * 10 / 100.0m;
            }
            //防止再次运算产生尾差，直接使用协同方算好的数据
            if (dctSyncEntry.ContainsKey("fdealprice"))
            {
                dealPrice = Convert.ToDecimal(dctSyncEntry["fdealprice"]);
            }

            //表体成交金额 = 表体数量 * 表体成交单价
            dealAmount = qty * dealPrice;

            //防止再次运算产生尾差，直接使用协同方算好的数据
            if (dctSyncEntry.ContainsKey("fdealamount_e"))
            {
                dealAmount = Convert.ToDecimal(dctSyncEntry["fdealamount_e"]);
            }
            
            //表体折扣额 = 表体金额 - 成交金额
            decimal distAmount = amount - dealAmount;

            //防止再次运算产生尾差，直接使用协同方算好的数据
            if (dctSyncEntry.ContainsKey("fdistamount_e"))
            {
                distAmount = Convert.ToDecimal(dctSyncEntry["fdistamount_e"]);
            }

            entity["fqty"] = qty;
            entity["fprice"] = price;
            entity["famount"] = amount;
            entity["fdistrate"] = distRate;
            entity["fdistamount"] = distAmount;
            entity["fdealprice"] = dealPrice;
            entity["fdealamount"] = dealAmount;

            
        }

        /// <summary>
        /// 转换辅助属性字段值
        /// </summary>
        /// <param name="dataEntity"></param>
        private void ChainDataIdToLocalId(DynamicObject dataEntity, string chainDataJson)
        {
            var field = this.HtmlForm.GetField("fattrinfo");
            if (field == null || !(field is HtmlAuxPropertyField)) return;
            var auxPropField = field as HtmlAuxPropertyField;

            if (chainDataJson.IsNullOrEmptyOrWhiteSpace()) return;

            var jaChainData = JArray.Parse(chainDataJson);
            if (jaChainData == null || jaChainData.Count <= 0) return;

            var unPackService = this.Container.GetService<IUnPackUpdateLocalIdService>();
            var chainMapList = unPackService.BindData(this.Context, this.HtmlForm, jaChainData);
            var chainEntitys = chainMapList[0]?.GetJsonValue<JArray>("fentity");
            if (chainEntitys == null || chainEntitys.Count <= 0) return;

            var auxPropValueSetForm = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvalueset");
            var dcSerializer = this.Container.GetService<IDynamicSerializer>();

            var entitys = dataEntity["fentity"] as DynamicObjectCollection;
            foreach (var entity in entitys)
            {
                foreach (var chainEntity in chainEntitys)
                {
                    if (Convert.ToString(entity["ftranid"]).EqualsIgnoreCase(Convert.ToString(chainEntity?["fentity_ftranid"])))
                    {
                        var attrInfos = new JArray();
                        attrInfos.Add(chainEntity?.GetJsonValue<JObject>("fattrinfo"));

                        List<DynamicObject> targetDataObjects = new List<DynamicObject>();
                        dcSerializer.Sync(auxPropValueSetForm.GetDynamicObjectType(this.Context), targetDataObjects, attrInfos);
                        if (targetDataObjects.Count > 0)
                        {
                            //清掉多余的字段值
                            targetDataObjects[0]["ftranid"] = "";
                            var _entitys = targetDataObjects[0]["fentity"] as DynamicObjectCollection;
                            foreach (var _entity in _entitys)
                            {
                                _entity["ftranid"] = "";
                            }
                            //设置辅助属性组合值
                            auxPropField?.RefDynamicProperty?.SetValue(entity, targetDataObjects[0]);
                        }
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 根据云链Id获取本地基础资料集合
        /// </summary>
        /// <param name="synEntrys">协同明细数据包</param>
        /// <param name="fieldId">基础资料字段Id</param>
        /// <returns>基础资料集合</returns>
        private IEnumerable<DynamicObject> GetLocalDataByChainDataIds(List<Dictionary<string, object>> synEntrys, string fieldId)
        {
            var field = this.HtmlForm.GetField(fieldId);
            if (!(field is HtmlBaseDataField)) throw new BusinessException($"{fieldId}不是基础资料字段，请检查！");
            var baseDataField = field as HtmlBaseDataField;
            var baseDataForm = baseDataField.RefHtmlForm(this.Context);

            //去重后的云链Id集合（其实提交过来的是 fchaindataid 字段值，这里是为了利用已有的 fmaterialid 字段进行传值）
            var chainDataIds = synEntrys
                .Select(t => 
                {
                    if (!t.ContainsKey(fieldId)) return string.Empty;
                    return Convert.ToString(t[fieldId]);
                })
                .Where(t => !t.IsNullOrEmptyOrWhiteSpace())
                .Distinct()
                .ToList();
            if (chainDataIds == null || chainDataIds.Count <= 0)
                //throw new BusinessException($"报价确认的协同{baseDataField.Caption}为空，请检查！");
                return null;

            StringBuilder sbWhere = new StringBuilder();
            List<SqlParam> paramList = new List<SqlParam>();
            paramList.Add(new SqlParam("@fmainorgid", DbType.String, this.Context.Company));
            for (int i = 0; i < chainDataIds.Count; i++)
            {
                var paramName = "@pid" + i;
                sbWhere.Append(paramName).Append(",");
                paramList.Add(new SqlParam(paramName, DbType.String, chainDataIds[i]));
            }
            var where = $"fmainorgid=@fmainorgid and ffromchaindataid in({sbWhere.ToString().TrimEnd(',')})";

            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, baseDataForm.GetDynamicObjectType(this.Context));

            return dm.SelectBy(this.Context.GetPkIdDataReader(baseDataForm, where, paramList)).OfType<DynamicObject>();
        }

        /// <summary>
        /// 处理商品子明细
        /// </summary>
        /// <param name="opCode"></param>
        /// <param name="jsonEntrys"></param>
        /// <param name="jsonDetails"></param>
        /// <param name="dataEntity"></param>
        private void ProcProductEntryDetails(string opCode, string jsonDetails, DynamicObject dataEntity)
        {
            DynamicObjectCollection entitys = null;

            switch (opCode.ToLower())
            {
                //销售方报价确认：添加商品子明细
                case "transmit":
                //销售方退单:刷新商品子明细
                case "chargeback":

                    if (jsonDetails.IsNullOrEmptyOrWhiteSpace()) return;

                    var synDetails = jsonDetails?.FromJson<List<Dictionary<string, object>>>();
                    if (synDetails == null || synDetails.Count <= 0) return;

                    entitys = dataEntity["fentity"] as DynamicObjectCollection;
                    if (entitys == null || entitys.Count <= 0) return;

                    var htmlDetail = this.HtmlForm.GetEntryEntity("fdetail");

                    foreach (var entity in entitys)
                    {
                        var details = entity["fdetail"] as DynamicObjectCollection;
                        if (details == null) continue;

                        //先清空
                        details.Clear();

                        var seq = 1;

                        foreach (var synDetail in synDetails)
                        {
                            if (Convert.ToString(entity["id"]).EqualsIgnoreCase(Convert.ToString(synDetail["fsourceentryid"])))
                            {
                                var detail = new DynamicObject(htmlDetail.DynamicObjectType);
                                detail["FSeq"] = seq;
                                detail["fproductid"] = synDetail["fproductid"];
                                detail["funitid"] = synDetail["funitid"];
                                detail["fattrinfo"] = synDetail["fattrinfo"];
                                detail["flength"] = synDetail["flength"];
                                detail["fwidth"] = synDetail["fwidth"];
                                detail["fthick"] = synDetail["fthick"];
                                detail["fqty"] = synDetail["fqty"];
                                detail["fprice"] = synDetail["fprice"];
                                detail["famount"] = synDetail["famount"];
                                detail["fdescription"] = synDetail["fdescription"];
                                detail["fsourcedetailid"] = synDetail["fsourcedetailid"];
                                details.Add(detail);

                                seq++;
                            }
                        }
                    }

                    //生成主键ID
                    var pkService = this.Container.GetService<IDataEntityPkService>();
                    pkService.AutoSetPrimaryKey(this.Context, dataEntity, this.HtmlForm.GetDynamicObjectType(this.Context));

                    break;

                //销售方取消报价确认：删除商品子明细
                case "canceltransmit":
                    entitys = dataEntity["fentity"] as DynamicObjectCollection;
                    if (entitys != null && entitys.Count > 0)
                    {
                        foreach (var entity in entitys)
                        {
                            (entity["fdetail"] as DynamicObjectCollection).Clear();
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }
}