using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService.CustomEventData;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：转售后反馈单
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("pushfeedback")]
    public class PushFeedback : AbstractOperationServicePlugIn
    {

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        /// <summary>
        /// 执行操作事务后事件，通知插件对象执行其它事务无关的业务逻辑
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);


        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys.IsNullOrEmpty()) return;
            if (!e.DataEntitys.IsNullOrEmpty() && e.DataEntitys.Count() > 1) throw new BusinessException("暂只允许一对一转服务单！");
            base.EndOperationTransaction(e);

            string feedbackFormId = "ste_afterfeedback";

            var rowIds = string.Empty;
            rowIds = this.GetQueryOrSimpleParam<string>("rowIds", "");

            var rowId = JArray.Parse(rowIds);


            var currentfbillno = string.Empty; //当前选中商品下游服务单号 

            var datas = this.Context.LoadBizDataByFilter(feedbackFormId,
                "fsourcenumber=@fsourcenumber and fmainorgid='" + this.Context.Company + "' and fsourcetype='" + this.HtmlForm.Id + "'", false,
                new List<SqlParam> { new SqlParam("@fsourcenumber", System.Data.DbType.String, e.DataEntitys[0]?["fbillno"]) });

            var hasPushOrNot = false;
            foreach (var item in datas)
            {
                var entrys = item["fproductentry"] as DynamicObjectCollection;
                foreach (var entry in entrys)
                {
                    var fsourceentryid = Convert.ToString(entry["fsourceentryid"]);
                    var selectrowid = Convert.ToString(rowId[0]["Id"]);
                    if (fsourceentryid == selectrowid)
                    {
                        hasPushOrNot = true;
                        currentfbillno = Convert.ToString(item["fbillno"]);
                        break;
                    }
                }
            }

            var newbill = this.Context.LoadBizDataByFilter(feedbackFormId,
                "fsourcenumber=@fsourcenumber and fmainorgid='" + this.Context.Company + "' and fsourcetype='" + this.HtmlForm.Id + "' and fbillno='" + currentfbillno + "'", false,
                new List<SqlParam> { new SqlParam("@fsourcenumber", System.Data.DbType.String, e.DataEntitys[0]?["fbillno"]) }).FirstOrDefault();

            if (!newbill.IsNullOrEmpty() && hasPushOrNot)
            {
                var action = this.OperationContext.UserContext.ShowSpecialForm(this.MetaModelService.LoadFormModel(this.Context, feedbackFormId),
                    newbill,
                    false,
                    this.OperationContext.PageId,
                    Enu_OpenStyle.Modal,
                    Enu_DomainType.Bill,
                    new Dictionary<string, object>(),
                    formPara =>
                    {
                        formPara.Status = Enu_BillStatus.Modify;
                    });
                this.Result.HtmlActions.Add(action);
                this.Result.IsShowMessage = false;
            }
            else
            {
                var selectRowIds = rowIds.IsNullOrEmptyOrWhiteSpace() ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);  //按选中行商品明细下推
                if (selectRowIds.Count <= 0) throw new BusinessException("请勾选一行商品转总部！");
                var lstSelPoEntryRows = new List<SelectedRow>();

                if (e.DataEntitys != null && e.DataEntitys.Any())
                {
                    foreach (var dataEntity in e.DataEntitys)
                    {
                        var poId = dataEntity["id"] as string;
                        var billNo = dataEntity["fbillno"] as string;
                        if (poId.IsEmptyPrimaryKey()) continue;

                        var entries = dataEntity["fentity"] as DynamicObjectCollection;

                        foreach (var item in entries)
                        {
                            if (item["Id"].Equals(selectRowIds[0].Id))
                            {
                                if (Convert.ToDecimal(item["fbizinstockqty"]) <= 0) throw new BusinessException("当前选中商品行的入库数量要大于0！");
                            }
                        }

                        var selectedRows = entries.Where(t => selectRowIds.Any(s => s.Id.Contains(Convert.ToString(t["id"])))).Select(x => new SelectedRow
                        {
                            PkValue = poId,
                            BillNo = billNo,
                            EntityKey = "fentity",
                            EntryPkValue = Convert.ToString(x["id"])
                        }).ToList();

                        lstSelPoEntryRows.AddRange(selectedRows);
                    }
                }

                var convertService = this.Container.GetService<IConvertService>();
                var result = convertService.Push(Context, new BillConvertContext()
                {
                    RuleId = "ydj_purchaseorder2ste_afterfeedback",
                    SourceFormId = this.HtmlForm.Id,
                    TargetFormId = feedbackFormId,
                    SelectedRows = lstSelPoEntryRows.IsNullOrEmptyOrWhiteSpace() ? e.DataEntitys.Select(x => new SelectedRow
                    {
                        PkValue = Convert.ToString(x["id"])
                    }).ToConvertSelectedRows() : lstSelPoEntryRows.ToConvertSelectedRows(),
                    //SelectedRows = e.DataEntitys.Select(x => new SelectedRow
                    //{
                    //    PkValue = Convert.ToString(x["id"])
                    //}).ToConvertSelectedRows(),
                    Option = OperateOption.Create()
                });


                var convertResult = result.SrvData as ConvertResult;
                if (convertResult != null)
                {
                    //查找符合条件的慕思经销商数据
                    var crmAgentService = this.Container.GetService<CrmDistributorService>();
                    var idList = crmAgentService.GetBizCrmAgentIdDic(this.Context);
                    var fagentid = string.Empty;
                    var fauthcity = string.Empty;
                    foreach (var data in e.DataEntitys)
                    {
                        if (idList != null && idList.Count == 1)
                        {
                            fagentid = idList.First().Key;
                            fauthcity = idList.First().Value;
                        }
                    }
                    foreach (var data in convertResult.TargetDataObjects)
                    {
                        if (this.Context.IsDirectSale)
                        {
                            var entrys = data["fproductentry"] as DynamicObjectCollection;
                            if (entrys == null) return;

                            foreach (var entry in entrys)
                            {
                                // 判断是否为采购订单源单（假设fsourcebilltype为采购订单单据类型）
                                var sourceBillType = Convert.ToString(entry["fsourceformid"]);
                                var sourceentryid = Convert.ToString(entry["fsourceentryid"]);
                                if (sourceBillType != "ydj_purchaseorder") continue;

                                DynamicObject entryItem = null;
                                var purOrderObj = this.Context.LoadBizDataById("ydj_purchaseorder", entry["fsourceinterid"].ToString());
                                if (purOrderObj != null)
                                {
                                    var entitys = purOrderObj["fentity"] as DynamicObjectCollection;
                                    entryItem = entitys.Where(a => Convert.ToString(a["id"]).Equals(Convert.ToString(sourceentryid))).FirstOrDefault();
                                }
                                if (entryItem == null) return;
                                // 获取各数量字段（请根据实际字段名调整）
                                decimal stockInQty = Convert.ToDecimal(entryItem["fbizinstockqty"] ?? 0);   // 采购入库数量
                                decimal returnQty = Convert.ToDecimal(entryItem["fbizreturnqty"] ?? 0);     // 采购退换数量
                                decimal refundQty = Convert.ToDecimal(entryItem["fbizrefundqty"] ?? 0);     // 采购退款数量
                                decimal problemQty = Convert.ToDecimal(entry["fqty"] ?? 0);   // 问题商品数量

                                decimal canProblemQty = stockInQty - returnQty - refundQty;
                                entry["fqty"] = canProblemQty;
                            }
                        }
                    }

                    var temp = convertResult.TargetDataObjects;

                    foreach (var item in temp)
                    {
                        item["fagentid"] = fagentid;//设置招商经销商
                        item["fauthcity"] = fauthcity;
                    }

                    //设置页面打开方式（如果前端有传递，则用传递的方式打开，否则按默认的方式打开）
                    //foreach (var targetDataEntity in convertResult.TargetDataObjects)
                    //{
                    var action = this.OperationContext.UserContext.ShowSpecialForm(convertResult.HtmlForm,
                        convertResult.TargetDataObjects.FirstOrDefault(),
                        false,
                        this.OperationContext.PageId,
                        Enu_OpenStyle.Modal,
                        Enu_DomainType.Bill,
                        new Dictionary<string, object>(), formPara =>
                        {
                            formPara.Status = Enu_BillStatus.Push;
                        });
                    //}
                    this.OperationContext.Result.HtmlActions.Add(action);
                    this.Result.IsShowMessage = false;
                }
                else
                {
                    this.OperationContext.Result.IsSuccess = false;
                    this.OperationContext.Result.SimpleMessage = "下推过程出现未知错误，请查看系统日志！";
                }
            }
        }

    }

}