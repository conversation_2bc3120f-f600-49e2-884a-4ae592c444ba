using JieNor.Framework;
using JieNor.Framework.DataTransferObject.FileServer;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 响应文件服务器组件上传后保存文件的事件
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("fsw_uploadfile")]
    public class SyncFSWidgetUploadData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var tranId = this.GetQueryOrSimpleParam<string>("CooTranId");

            var fsData = this.GetQueryOrSimpleParam<string>("fsData")?
                .FromJson<IEnumerable<FSWidgetFileInfo>>() ?? new FSWidgetFileInfo[] { };

            if (tranId.IsNullOrEmptyOrWhiteSpace()
                || fsData.Any() == false) return;

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, "ftranid=@tranid",
                new SqlParam[]
                {
                    new SqlParam("tranid", System.Data.DbType.String, tranId)
                });
            var purchaseOrderObj = dm.SelectBy(pkIdReader).OfType<DynamicObject>()
                .FirstOrDefault();
            if (purchaseOrderObj.IsNullOrEmpty()) return;

            var fileAttachEntity = this.HtmlForm.GetEntryEntity("FDrawEntity");
            var fileEntryObjs = fileAttachEntity.DynamicProperty.GetValue<DynamicObjectCollection>(purchaseOrderObj);
            var originalFileEntryObjs = fileEntryObjs.ToList();
            fileEntryObjs.Clear();
            foreach (var fileInfo in fsData)
            {
                DynamicObject existFileObj = null;
                for (int i = originalFileEntryObjs.Count - 1; i >= 0; i--)
                {
                    if (StringUtils.EqualsIgnoreCase(originalFileEntryObjs[i]["ffileid"] as string, fileInfo.FileId)
                      || StringUtils.EqualsIgnoreCase(originalFileEntryObjs[i]["id"] as string, fileInfo.CooSrcId))
                    {
                        existFileObj = originalFileEntryObjs[i];
                        originalFileEntryObjs.RemoveAt(i);
                        break;
                    }
                }
                if (existFileObj == null)
                {
                    existFileObj = fileEntryObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                }

                existFileObj["ffileid"] = fileInfo.FileId;
                existFileObj["ffilename"] = fileInfo.FileName;
                existFileObj["ffileformat"] = fileInfo.FileFormat;
                existFileObj["ffilesize"] = fileInfo.FileSize;
                existFileObj["fuploader"] = fileInfo.Uploader;
                existFileObj["fuploaderid"] = fileInfo.UploaderId;
                existFileObj["fuptime"] = fileInfo.UploadTime;
                existFileObj["fnote"] = fileInfo.Description;
                fileEntryObjs.Add(existFileObj);

            }
            var pkService = this.Container.GetService<IDataEntityPkService>();
            pkService.AutoSetPrimaryKey(this.Context, purchaseOrderObj, dm.DataEntityType);
            dm.Save(purchaseOrderObj);
        }
    }

    /// <summary>
    /// 响应文件组件初始化时的文件详情数据准备
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("fsw_initfile")]
    public class SyncFSWidgetInitData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 执行操作事务
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var tranId = this.GetQueryOrSimpleParam<string>("CooTranId");

            if (tranId.IsNullOrEmptyOrWhiteSpace()) return;

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(this.HtmlForm, "ftranid=@tranid",
                new SqlParam[]
                {
                    new SqlParam("tranid", System.Data.DbType.String, tranId)
                });
            var purchaseOrderObj = dm.SelectBy(pkIdReader).OfType<DynamicObject>()
                .FirstOrDefault();
            if (purchaseOrderObj.IsNullOrEmpty()) return;

            var fileAttachEntity = this.HtmlForm.GetEntryEntity("FDrawEntity");
            var entryRowObjs = fileAttachEntity.DynamicProperty.GetValue<DynamicObjectCollection>(purchaseOrderObj);
            List<FSWidgetFileInfo> lstFileData = new List<FSWidgetFileInfo>();
            foreach (var fileInfo in entryRowObjs)
            {
                FSWidgetFileInfo fsFile = new FSWidgetFileInfo();
                fsFile.Id = "";
                fsFile.FileId = fileInfo["ffileid"] as string;
                fsFile.FileName = fileInfo["ffilename"] as string;
                fsFile.FileFormat = fileInfo["ffileformat"] as string;
                fsFile.FileSize = fileInfo["ffilesize"] as string;
                fsFile.Uploader = fileInfo["fuploader"] as string;
                fsFile.UploaderId = fileInfo["fuploaderid"] as string;
                fsFile.UploadTime = (DateTime)fileInfo["fuptime"];
                fsFile.Description = fileInfo["fnote"] as string;
                fsFile.CooSrcId = fileInfo["fsourceentryid"] as string;
                lstFileData.Add(fsFile);
            }

            this.Result.SrvData = lstFileData.ToJson();
        }
    }
}
