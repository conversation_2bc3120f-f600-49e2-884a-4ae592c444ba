using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：付款和退款
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("LoadSettleInfo")]
    public class LoadSettleInfo : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!Convert.ToString(newData["fstatus"]).EqualsIgnoreCase("E"))
                {
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => $"{this.HtmlForm.Caption}【{billObj["fbillno"]}】未审核！"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var dataEntity = e.DataEntitys[0];

            // 供应商（按需加载，性能优化）
            var supplierId = Convert.ToString(dataEntity["fsupplierid"]);
            var supplier = this.Context.LoadBizBillHeadDataById("ydj_supplier", supplierId, "fnumber,fname");

            Dictionary<string, object> dicSettle = new Dictionary<string, object>();
            dicSettle["fsourceformid"] = this.HtmlForm.Id;
            dicSettle["fsourceid"] = dataEntity?["id"] as string;
            dicSettle["fsourcenumber"] = dataEntity?["fbillno"] as string;
            dicSettle["fsourcetranid"] = dataEntity?["ftranid"] as string;
            dicSettle["fbilltypeid"] = dataEntity?["fbilltypeid"] as string;
            dicSettle["fpaystatus"] = dataEntity?["fpaystatus"] as string;
            dicSettle["fbizstatus"] = dataEntity?["fbizstatus"] as string;
            dicSettle["ffbillamount"] = dataEntity?["ffbillamount"];//成交金额
            dicSettle["fsupplierid"] = new { id = supplier?["id"] ?? "", fnumber = supplier?["fnumber"] ?? "", fname = supplier?["fname"] ?? "" };
            dicSettle["funconfirmamount"] = dataEntity?["fconfirmamount"];
            dicSettle["fallaccounts"] = new List<AccountInfo>();
            dicSettle["fdeptid"] = dataEntity["fpodeptid"];
            dicSettle["fsettledamount"] = dataEntity?["fpaidamount"];
            dicSettle["funsettleamount"] = dataEntity?["fpayamount"];
            dicSettle["fsettlemainname"] = supplier?["fname"];
            dicSettle["fsettlemaintype"] = "ydj_supplier";
            dicSettle["fsettlemainid"] = supplier?["id"];
            dicSettle["fissyn"] = false;

            //结算类型：收款，退款
            var settleType = this.GetQueryOrSimpleParam<string>("settleType", "");

            switch (settleType.ToLower())
            {
                //付款
                case "payment":
                    if (Convert.ToDecimal(dataEntity?["fpayamount"]) <= 0)
                    {
                        throw new BusinessException($"待结算金额小于等于0，无法付款！");
                    }
                    break;

                //退款
                case "refund":
                    if (Convert.ToDecimal(dataEntity?["fpaidamount"]) <= 0)
                    {
                        throw new BusinessException($"已结算金额小于等于0，无法退款！");
                    }
                    dicSettle["fdirection"] = "direction_01";
                    dicSettle["fbizdirection"] = "bizdirection_02";
                    dicSettle["fpurpose"] = "bizpurpose_06";
                    dicSettle["fsettletype"] = "退款";
                    break;
            }

            this.Result.SrvData = dicSettle;
            this.Result.IsSuccess = true;
        }
    }
}