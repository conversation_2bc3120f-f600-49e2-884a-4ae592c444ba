using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.PurchaseOrder
{
    /// <summary>
    /// 采购订单：重新发送
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("SubmitAgain")]
    public class SubmitAgain: SubmitBase
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        protected override string OpName { get { return "重新发送"; } }

        /// <summary>
        /// 销售意向单协同接口操作码
        /// </summary>
        protected override string SyncSaleOpCode { get { return "SyncSubmitAgain"; } }

        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //1.检查当前系统是否加入云链
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return Convert.ToString(newData["fchargebackstatus"]) == "1";
            }).WithMessage("只有退单状态的订单才能重新发送"));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            this.Result.IsSuccess = true;
            this.AddSetValueAction("fchargebackstatus", "2"); //重新发送后，将退单状态更改为退单已重发。
        }

        protected override void UpdatePurchaseOrder(DynamicObject dataEntity)
        {
            dataEntity["fchargebackstatus"] = "0";
            dataEntity["fchargebackreason"] = string.Empty;
        }

        protected override void BuildingSaleBillData(Dictionary<string, object> billData)
        {
            //覆盖父方法，重新发送时无需重置销售意向单的状态
            //重置意向单的状态为正常状态
            billData["fchargebackstatus"] = "0";
            billData["fchargebackreason"] = string.Empty;
        }

        /// <summary>
        /// 打包发送协同数据
        /// </summary>
        /// <param name="saleBillData"></param>
        /// <param name="simpleData"></param>
        /// <param name="billData"></param>
        protected override void BuildingSendData(string saleBillData, Dictionary<string, string> simpleData, out string billData)
        {
            billData = string.Empty;
            simpleData["billData"] = saleBillData;
        }
    }
}
