using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：反作废
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("UnCancel")]
    public class UnCancel : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(new Validation_DropShipUnCalcel());
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            var ids = entitys.Select(o => Convert.ToString(o?["fsoorderentryid"])).ToList();
            // 反写销售合同【已转采购数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackTransPurQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);

        }
    }

    /// <summary>
    /// 【一件代发】用，反作废校验器
    /// </summary>
    public class Validation_DropShipUnCalcel : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        private HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            this.HtmlForm = formInfo;

            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            CheckTranspurqty(userCtx, dataEntities, result);

            return result;
        }


        private void CheckTranspurqty(UserContext ctx, DynamicObject[] dataEntitys, ValidationResult result)
        {
            var entitys = dataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            var orderEntryIds = entitys.Select(a => Convert.ToString(a["fsoorderentryid"])).ToList();

            var sql = $@"   SELECT fseq,fentryid,ftranspurqty,fbizqty
                from t_ydj_orderentry oe
                where oe.fentryid in ('{string.Join("','", orderEntryIds)}')";
            using (var dr = this.DBService.ExecuteReader(this.Context, sql))
            {
                while (dr.Read())
                {
                    foreach (var purOrder in dataEntitys)
                    {
                        foreach (var item in entitys)
                        {
                            string fentryid = Convert.ToString(dr["fentryid"]);
                            string fseq = Convert.ToString(dr["fseq"]);
                            decimal transpurqty = Convert.ToDecimal(dr["ftranspurqty"]);
                            decimal saleQty = Convert.ToDecimal(dr["fbizqty"]);
                            string fsoorderentryid = Convert.ToString(item["fsoorderentryid"]);
                            string fsourcebillno = Convert.ToString(item["fsourcebillno"]);
                            decimal bizqty = Convert.ToDecimal(item["fbizqty"]);


                            if (fsoorderentryid.Equals(fentryid))
                            {
                                // 4. 校验
                                if (transpurqty + bizqty > saleQty)
                                {
                                    result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"采购订单【{purOrder["fbillno"]}】上游销售合同【{fsourcebillno}】,第{fseq}行反作废后已采购数{Convert.ToInt32(transpurqty)+ Convert.ToInt32(bizqty)}已超出销售数量{Convert.ToInt32(saleQty)}，不允许反作废，请检查数据！",
                                        DataEntity = item,
                                    });
                                }
                            }
                        }
                    }
                }
            }


        }



    }
}
