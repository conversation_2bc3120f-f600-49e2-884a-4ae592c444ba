using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：暂存
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("draft")]
    public class Draft : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(dataEntity);
            }
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //删除前先记录关联的合同ids,避免删除后联查不到数据无法更新。
            base.EndOperationTransaction(e);

            var entitys = e.DataEntitys.SelectMany(o => o["fentity"] as DynamicObjectCollection).ToList();
            var ids = entitys.Select(o => Convert.ToString(o?["fsoorderentryid"])).ToList();
            // 反写销售合同【已转采购数】
            Core.Helpers.OrderQtyWriteBackHelper.WriteBackTransPurQty(
                this.Context, this.HtmlForm, e.DataEntitys, this.OperationNo);
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys, ids);
        }
    }
}
