using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchasePrice
{


    /// <summary>
    /// 采购价目表保存的校验：经销商自定义的价目表，不能包含总部商品
    /// </summary>
    public class SaveValidation : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            if (userCtx.IsTopOrg)
            {
                return result;
            }

            userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, formInfo.GetDynamicObjectType(userCtx), dataEntities, false);

            foreach (var item in dataEntities)
            {
                // 允许保存【报价类型】=“二级分销报价”的总部商品价目表，不校验。
                if (Convert.ToString(item["ftype"]).EqualsIgnoreCase("quote_type_04")) continue;

                var fmainorgid = item["fmainorgid"]?.ToString();
                var fnumber = item["fnumber"]?.ToString();
                var fname = item["fname"]?.ToString();
                var enRows = item["fentry"] as DynamicObjectCollection;
                foreach (var enRow in enRows)
                {
                    var matObj = enRow["fproductid_e_ref"] as DynamicObject;
                    if (matObj == null)
                    {
                        continue;
                    }

                    var orgId = matObj["fmainorgid"]?.ToString();
                    if (orgId.EqualsIgnoreCase(userCtx.TopCompanyId))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $@"经销商{formInfo.Caption}【{fnumber} {fname}】中，商品【{matObj["fnumber"]} {matObj["fname"]}】是总部授权的商品，不允许自定义价目 ！",
                            DataEntity = item,
                        });
                    }
                }
            }
            return result;
        }
    }


}
