using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sec
{
    /// <summary>
    /// 用户导入校验
    /// </summary>
    public class Validation_Import : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            CheckChangeInfo(userCtx, formInfo, dataEntities, result, option, operationNo);

            return result;
        }



        /// <summary>
        /// 导入时判断，防止用户修改登录账号和手机号
        /// 导致旧登录账号号登录后，根据fnumber和fphone找不到用户，自动建新的账号（t_auth_user会存在fnumber为旧手机号，fphone为新手机号，导致后续登录的是新账号问题）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="formInfo"></param>
        /// <param name="dataEntitys"></param>
        /// <param name="result"></param>
        /// <param name="option"></param>
        private void CheckChangeInfo(UserContext ctx, HtmlForm formInfo, DynamicObject[] dataEntitys, ValidationResult result, OperateOption option, string operationNo)
        {
            if (dataEntitys == null || !dataEntitys.Any()) return;

            var nameArray = dataEntitys.Select(x => Convert.ToString(x["fphone"]));
            var orgArray = dataEntitys.Select(x => Convert.ToString(x["fmainorgid"]));
            StringBuilder sqlWhere = new StringBuilder();
            List<SqlParam> sqlParams = new List<SqlParam>();
            sqlWhere.Append("fphone in(");
            sqlWhere.Append(string.Join(",", nameArray.Select((x, i) => $"@fphone{i}")));
            sqlWhere.Append(") and fmainorgid in(");
            sqlWhere.Append(string.Join(",", orgArray.Select((x, i) => $"@forgid{i}")));
            sqlWhere.Append(")");
            sqlParams.AddRange(nameArray.Select((x, i) => new SqlParam($"@fphone{i}", System.Data.DbType.String, x)));
            sqlParams.AddRange(orgArray.Select((x, i) => new SqlParam($"@forgid{i}", System.Data.DbType.String, x)));

            var existsUsers = ctx.ExecuteDynamicObject("select fnumber,fname,fmainorgid,fphone from t_sec_user where " + sqlWhere, sqlParams);
            if(existsUsers!=null&& existsUsers.Any())
            {
                foreach (var item in dataEntitys)
                {
                    var existsUserList = existsUsers.Where(x => Convert.ToString(x["fphone"]) == Convert.ToString(item["fphone"])
                    && Convert.ToString(x["fmainorgid"]) == Convert.ToString(item["fmainorgid"]));
                    if (existsUserList != null&&existsUserList.Any())
                    {
                        /*如果同组织内存在重名的但是不存在登录账号且手机号相同的
                        那么认定是修改登录账号或手机号，不允许
                        */
                        if (!existsUserList.Any(x=> Convert.ToString(x["fnumber"]) == Convert.ToString(item["fnumber"])
                        && Convert.ToString(x["fphone"]) == Convert.ToString(item["fphone"])))
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = @"用户【{0}】禁止通过导入修改登录账号或手机号，请检查，如需新增，请前往新增页面添加，如需更换手机号请前往用户界面【解绑手机】！".Fmt(Convert.ToString(item["fname"])),
                                DataEntity = item,
                            });
                        }
                    }
                }
            }
        }

    }
}
