using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sec
{
    /// <summary>
    /// 用户编辑页面锁定【关联角色】
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    public class UserBizRules : IDynamicBusinessRule
    {
        public List<UIBizCtrlRuleInfo> BuildBusinessRule(UserContext ctx, FormShowParameter showPara)
        {
            UIBizCtrlRuleInfo dynRule = new UIBizCtrlRuleInfo();
            dynRule.lockRules.Add(new UICtrlRule()
            {
                id = "__lock_roles",
                expression = "field:froleids$|fnumber!='' and fmainorgid!='' ",
            });

            List<UIBizCtrlRuleInfo> lstRules = new List<UIBizCtrlRuleInfo>();
            if (!(showPara.Status == Enu_BillStatus.View || showPara.Status == Enu_BillStatus.Modify))
            {
                return lstRules;
            }

            var perSvc = ctx.Container.GetService<Framework.Interface.UsrMgr.IPermissionService>();
            var hasPer = perSvc.HasPermission(ctx, new Framework.MetaCore.PermData.PermAuth(ctx) { FormId = "sec_assignright", PermId = "fw_allotrole" });

            if (!hasPer)
            {
                //如果当前用户没有角色授权的权限，则锁定
                lstRules.Add(dynRule);

                return lstRules;
            }
            else
            {
                //如果当前修改的用户关联了【经销商角色】【系统管理员】【系统运维】，则锁定，避免被删除了经销商角色导致经销商组织下的所有权限错乱
                if (ctx.IsTopOrg == false)
                {
                    var sql = @"select fid,fname,fnumber from T_sec_role with(nolock) where fmainorgid='{0}' and fnumber in ('Admin_Agent','Administrator','Admin_DevOps') ".Fmt(ctx.TopCompanyId);
                    var roleInfo = ctx.Container.GetService<IDBService>().ExecuteDynamicObject(ctx, sql);
                    if (roleInfo == null)
                    {
                        return lstRules;
                    }
                    var froleids = showPara.UiData["froleids"];
                    var ids = froleids?.GetJsonValue<string>("id", "");
                    foreach (var item in roleInfo)
					{
                        var roleId = Convert.ToString(item["fid"]);
                        if (ids != null && ids.SplitKey().Any(f => f.EqualsIgnoreCase(roleId)))
                        {
                            lstRules.Add(dynRule);
                            break;
                        }
                    }
                }
            }
            return lstRules;
        }
    }
}



