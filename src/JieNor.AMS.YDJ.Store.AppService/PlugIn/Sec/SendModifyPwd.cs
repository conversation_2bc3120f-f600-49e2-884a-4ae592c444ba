using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Api;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sec
{
    /// <summary>
    /// 修改当前用户密码
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("sendmodifypwd")]
    public class SendModifyPwd : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var newPwd = this.GetQueryOrSimpleParam<string>("newpwd");
            var account = this.GetQueryOrSimpleParam<string>("fnumber");
            var phone = this.GetQueryOrSimpleParam<string>("fphone");
            var userid = this.GetQueryOrSimpleParam<string>("userid");
            var dto = new AMS.YDJ.Store.AppService.MuSi.DTO.MsAuthModifyPwdDto();
            var externalApp = MusiAuthService.GetExtApp(this.Context);
            if (externalApp != null)
            {
                string appKey = Convert.ToString(externalApp["fappkey"]);
                if (!appKey.IsNullOrEmptyOrWhiteSpace())
                {
                    JObject configObj = JObject.Parse(appKey);
                    dto.systemCode = configObj.GetJsonValue("api_client_code", "");
                }
            }

            dto.password = AMS.YDJ.MS.API.Utils.MusiAuthHelper.Encrypt(newPwd);
            dto.optUserName = this.Context.UserName;
            if (string.IsNullOrWhiteSpace(userid))
            {
                var secUser = this.Context.LoadBizDataById("sec_user", this.Context.UserId );
                if (secUser == null)
                    return;
                dto.account = Convert.ToString(secUser["fnumber"]);
                dto.phone = Convert.ToString(secUser["fphone"]);
            }
            else
            {
                dto.account = account;
                dto.phone = phone;
                dto.optUserName = account;
            }
            var resp = MuSiApi.SendModifyPwdInfo(this.Context, this.HtmlForm, dto);
            //if (resp.Code != 200)//回滚
            //{
            //    throw new BusinessException($"同步认证中心：{this.HtmlForm.Caption}【修改当前用户密码】 同步失败，失败原因：" + resp.Msg);
            //}

        }
    }
}
