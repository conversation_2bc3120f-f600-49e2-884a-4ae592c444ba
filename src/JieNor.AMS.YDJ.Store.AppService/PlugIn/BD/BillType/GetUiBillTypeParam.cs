using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BD.BillType
{
    // <summary>
    /// 根据单据类型得到单据类型参数设置
    /// </summary>
    [InjectService]
    [FormId("bd_billtype")]
    [OperationNo("getuibillTypeparam")]
    public class GetUiBillTypeParam : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string billTypeId = this.GetQueryOrSimpleParam("billTypeId", "");

            if (billTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SimpleMessage = "对不起，单据类型Id不能为空！";
                this.Result.IsSuccess = false;
            }

            // 取默认
            var result = this.Context.Container.GetService<IHttpServiceInvoker>()
                .InvokeBillOperation(this.Context, "sys_mainfw", null, "getbilltypedata", new Dictionary<string, object>
                    {
                        { "billTypeId", billTypeId }
                    }
                );

            var srvData = result.SrvData;

            Dictionary<string, GetUiBillTypeParam.UiBillTypeParam> jsonObject = new Dictionary<string, GetUiBillTypeParam.UiBillTypeParam>();
            if (!srvData.IsNullOrEmptyOrWhiteSpace())
            {
                jsonObject = JsonConvert.DeserializeObject<Dictionary<string, GetUiBillTypeParam.UiBillTypeParam>>(srvData.ToJson());
                GetUiBillTypeParam.UiBillTypeParam uiBillTypeParams = jsonObject["uiBillTypeParam"];

                // 获取单据类型数据包
                var billTypeObj = this.Context.LoadBizDataById("bd_billtype", billTypeId);

                if (!billTypeObj.IsNullOrEmptyOrWhiteSpace())
                {
                    var fentity = billTypeObj["fentity"] as DynamicObjectCollection;

                    if (!fentity.IsNullOrEmptyOrWhiteSpace() && fentity.Count > 0)
                    {
                        foreach (var uiFentity in uiBillTypeParams.Fentity)
                        {
                            foreach (var item in fentity)
                            {
                                if (uiFentity.Ffieldid.Id.Equals(item["ffieldid"]))
                                {
                                    uiFentity.Ffieldid.Name = Convert.ToString(item["ffieldid_txt"]);        //字段名称
                                    uiFentity.Flock.Id = Convert.ToString(item["flock"]);                    //锁定性
                                    uiFentity.Flock.Name = Convert.ToString(item["flock_txt"]);              //锁定性别名
                                    uiFentity.Fmustinput.Id = Convert.ToString(item["fmustinput"]);          //必录性


                                    //必录性别名
                                    //"1:'保存必录',2:'提交必录',3:'审核必录',4:'作废必录'"
                                    switch (uiFentity.Fmustinput.Id)
                                    {
                                        case "1":
                                            uiFentity.Fmustinput.Fname = "保存必录";
                                            break;
                                        case "2":
                                            uiFentity.Fmustinput.Fname = "提交必录";
                                            break;
                                        case "3":
                                            uiFentity.Fmustinput.Fname = "审核必录";
                                            break;
                                        case "4":
                                            uiFentity.Fmustinput.Fname = "作废必录";
                                            break;

                                    }
                                }
                            }
                        }

                    }
                }
            }

            this.Result.SrvData = jsonObject.ToJson();
            this.Result.IsSuccess = true;
        }

        public class SimpleModel
        {
            public string Id { get; set; }
            public string Name { get; set; }
        }

        public class FmustInputModel
        {
            public string Id { get; set; }
            public string Fnumber { get; set; }
            public string Fname { get; set; }
        }

        public class FentityModel
        {
            public SimpleModel Ffieldid { get; set; }
            public SimpleModel Flock { get; set; }
            public FmustInputModel Fmustinput { get; set; }
        }

        public class UiBillTypeParam
        {
            public string Id { get; set; }
            public string Fnumber { get; set; }
            public string Fname { get; set; }
            public List<FentityModel> Fentity { get; set; }
        }
    }
}
