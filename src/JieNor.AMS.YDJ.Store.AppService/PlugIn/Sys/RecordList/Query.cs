using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sys.RecordList
{
    [InjectService]
    [FormId("bd_recordlist")]
    [OperationNo("query")]
    public class Query : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            //var action = this.OperationContext.Result.HtmlActions.FirstOrDefault(s => s is HtmlViewAction) as HtmlViewAction;

            //if (action != null)
            //{
            //    if (action.ActionParams.ContainsKey("fsp"))
            //    {
            //        var param = action.ActionParams["fsp"] as FormShowParameter;

            //        if (param != null)
            //        {
            //            var sqlStr =
            //                "select fid,fname from t_bas_filterscheme with (nolock ) where (fuserid='' or fuserid=' ') and fispreset = '1' and fbillformid = 'bd_recordlist' and (fmainorgid='' or fmainorgid = '0' or fmainorgid=@mainorgid) ";
            //            var sqlParams = new List<SqlParam>();
            //            sqlParams.Add(new SqlParam("@mainorgid",DbType.String,this.Context.Company));
                        
            //            var FilterDys = DBService.ExecuteDynamicObject(this.Context,sqlStr,sqlParams).ToList();

            //            if (FilterDys != null && FilterDys.Any())
            //            {

            //                //找出包含过滤方案名称中有【今天】的集合
            //                var Dys = FilterDys.Where(x=>(Convert.ToString(x["fname"]).Equals("今天") || Convert.ToString(x["fname"]).Contains("今天"))).ToList();

            //                if (Dys.Any())
            //                {
            //                    var getDy = Dys.OrderBy(x=>Convert.ToString(x["fid"])).FirstOrDefault();

            //                    if (getDy != null)
            //                    {
            //                        var Id = Convert.ToString(getDy["fid"]);

            //                        param.FormUserProfile.FilterSchemeId = Id;
            //                    }

            //                }

                            
            //            }

                        
            //        }
            //    } 
            //}
        }
    }
}