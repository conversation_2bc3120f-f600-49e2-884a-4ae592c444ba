using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sys
{
    /// <summary>
    /// 根据采购订单明细行交易流水号，获取明细附件信息
    /// </summary>
    [InjectService]
    [FormId("sys_detailsynergism")]
    [OperationNo("addordeletefile")]
    public class AddOrDeleteFile : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var strFiles = this.GetQueryOrSimpleParam<string>("addFiles");
            List<Dictionary<string, string>> listFile = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(strFiles);
            var fileIds = listFile.Select(p => Convert.ToString(p["fileId"])).ToList();
            var fileNames = listFile.Select(p => Convert.ToString(p["fileName"])).ToList();
            string fileId = string.Join(",", fileIds.ToArray());
            var fileName = string.Join(",", fileNames.ToArray());
            var ftranid = this.GetQueryOrSimpleParam<string>("ftranid");
            if (ftranid.IsNullOrEmptyOrWhiteSpace() || strFiles.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var sql = @"update t_ydj_poorderentry set fmulfile='{0}' ,fmulfile_txt ='{1}' 
                        where ftranid =(select top 1 t1.ftranid
				                        from t_ydj_purchaseorder t
				                        left join t_ydj_poorderentry t1 on t.fid = t1.fid 
				                        where t1.ftranid='{2}' and t.fmainorgid = @fmainorgid)"
                        .Fmt(fileId, fileName, ftranid);
            var dbService = this.Context.Container.GetService<IDBServiceEx>();
            List<SqlParam> lstParams = new List<SqlParam>()
            {
              new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company)
            };
            var i = dbService.Execute(this.Context, sql, lstParams);
            this.OperationContext.Result.IsSuccess = true;
        }
    }
}
