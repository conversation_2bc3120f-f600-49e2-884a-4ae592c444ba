using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sys
{
    /// <summary>
    /// 根据采购订单明细行交易流水号，获取明细附件信息
    /// </summary>
    [InjectService]
    [FormId("sys_detailsynergism")]
    [OperationNo("getdetailline")]
    public class GetDetailLine : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var ftranid = this.GetQueryOrSimpleParam<string>("data");
            if (ftranid.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.ComplexMessage.ErrorMessages.Add("明细行流水号不能为空！");
                this.Result.IsSuccess = false;
                return;
            }
            var sql = @"select t.fid,t1.fmulfile,t1.fmulfile_txt,t1.ftranid,
                        t.fcreatorid,t1.fmulfile_source
                        from t_ydj_purchaseorder t
                        left join t_ydj_poorderentry t1 on t.fid = t1.fid 
                        where t1.ftranid='{0}'".Fmt(ftranid);
            var dbService = this.Context.Container.GetService<IDBService>();
            var sqlData = dbService.ExecuteDynamicObject(this.Context, sql);
            if (sqlData.Count() == 0)
            {
                this.Result.ComplexMessage.ErrorMessages.Add("当前明细行未查询到附件信息");
                this.Result.IsSuccess = false;
                return;
            }
            var result = new Dictionary<string, string>();

            //当上传方为空的时候，默认上传方为渠道
            var fmulfile = Convert.ToString(sqlData[0]["fmulfile"]);
            var fmulfile_source = Convert.ToString(sqlData[0]["fmulfile_source"]);
            if (fmulfile_source.IsNullOrEmptyOrWhiteSpace())
            {
                List<string> fmulfile_sourceList = new List<string>();
                var index = fmulfile.Split(',').ToList().Count();
                for (int i = 0; i < index; i++)
                {
                    fmulfile_sourceList.Add("渠道");
                }
                fmulfile_source = string.Join(",", fmulfile_sourceList.ToArray());
            }
            result.Add("fmulfile", fmulfile);
            result.Add("fmulfile_source", fmulfile_source);
            this.Result.SrvData = result;
            this.OperationContext.Result.IsSuccess = true;
        }
    }
}