using JieNor.Framework;
using JieNor.Framework.CustomException;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sys.SmsTemplate
{
    public class AlibabaCloudUtil
    {
        /**
         * 使用AK&SK初始化账号Client
         * @param accessKeyId
         * @param accessKeySecret
         * @return Client
         * @throws Exception
         */
        public static AlibabaCloud.SDK.Dysmsapi20170525.Client CreateClient(UserContext userCtx)
        {
            var smsConfig = Framework.Interface.BizSms.SmsConfigService.GetSmsGatwayConfig(userCtx);
            var smsAccessId = smsConfig.Item1;
            var smsAccessKey = smsConfig.Item2;

            if (smsAccessId.IsNullOrEmptyOrWhiteSpace()
                || smsAccessKey.IsNullOrEmptyOrWhiteSpace()
               )
            {
                throw new BusinessException("您站点的短消息配置不正确，无法发送短信！");
            }
            AlibabaCloud.OpenApiClient.Models.Config config = new AlibabaCloud.OpenApiClient.Models.Config
            {
                // 必填，您的 AccessKey ID
                AccessKeyId = smsAccessId,
                // 必填，您的 AccessKey Secret
                AccessKeySecret = smsAccessKey,
            };
            // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
            config.Endpoint = "dysmsapi.aliyuncs.com";
            return new AlibabaCloud.SDK.Dysmsapi20170525.Client(config);
        }
    }
}
