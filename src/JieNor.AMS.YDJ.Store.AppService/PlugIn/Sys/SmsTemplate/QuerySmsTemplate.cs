using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.CustomException;
using Tea;
using Tea.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sys.SmsTemplate
{
    /// <summary>
    /// 查询短信模板的审核状态
    /// </summary>
    [InjectService]
    [FormId("sys_smstemplate")]
    [OperationNo("querysmstemplate")]
    public class QuerySmsTemplate : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var effectStatus = new string[] { "0", "20" };
                if (!effectStatus.Contains(Convert.ToString(newData["ftemplatestatus"])))
                {
                    return false;
                }
                return true;
            }).WithMessage("模版名称为【{0}】的送审状态非【审核中、重审中】，暂不支持查询状态！", (billObj, propObj) => billObj["fname"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data["fcode"]).NotEmpty()
                .WithMessage("模版名称为【{0}】的模板Code为空，暂不支持查询状态！", (billObj, propObj) => billObj["fname"]));
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (this.Context.Company != this.Context.TopCompanyId)
            {
                throw new BusinessException("您暂无该操作权限，请联系管理员统一配置短信模版！");
            }
            if (e.DataEntitys == null) return;

            AlibabaCloud.SDK.Dysmsapi20170525.Client client = AlibabaCloudUtil.CreateClient(this.Context);
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime = new AlibabaCloud.TeaUtil.Models.RuntimeOptions();

            foreach (var item in e.DataEntitys)
            {
                QuerySmsTemplateRequest(item, client, runtime);
            }

            this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);

            this.AddRefreshPageAction();
        }

        /// <summary>
        /// 查询短信模板的审核状态
        /// </summary>
        /// <param name="item"></param>
        /// <param name="client"></param>
        /// <param name="runtime"></param>
        private void QuerySmsTemplateRequest(DynamicObject item,
            AlibabaCloud.SDK.Dysmsapi20170525.Client client,
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime)
        {
            AlibabaCloud.SDK.Dysmsapi20170525.Models.QuerySmsTemplateRequest querySmsTemplateRequest = new AlibabaCloud.SDK.Dysmsapi20170525.Models.QuerySmsTemplateRequest();
            querySmsTemplateRequest.TemplateCode = Convert.ToString(item["fcode"]);
            try
            {
                // 复制代码运行请自行打印 API 的返回值
                var result = client.QuerySmsTemplateWithOptions(querySmsTemplateRequest, runtime);

                if (result.Body.Code == "OK")
                {
                    item["ftemplatestatus"] = result.Body.TemplateStatus;
                    item["freason"] = result.Body.Reason;
                    this.Result.ComplexMessage.SuccessMessages.Add($"名称为【{item["fname"]}】的模版审核状态查询成功！");
                }
                else
                {
                    item["freason"] = result.Body.Reason;
                    this.Result.ComplexMessage.ErrorMessages.Add($"名称为【{item["fname"]}】的审核状态查询失败，请查看【送审备注】结合分析！");
                }
            }
            catch (TeaException error)
            {
                item["freason"] = error.Message;
                this.Result.ComplexMessage.ErrorMessages.Add($"名称为【{item["fname"]}】的审核状态查询失败，请查看【送审备注】结合分析！");
                //// 错误 message
                //Console.WriteLine(error.Message);
                //// 诊断地址
                //Console.WriteLine(error.Data["Recommend"]);
                //AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            catch (Exception _error)
            {
                TeaException error = new TeaException(new Dictionary<string, object>
                    {
                        { "message", _error.Message }
                    });
                item["freason"] = error.Message;
                this.Result.ComplexMessage.ErrorMessages.Add($"名称为【{item["fname"]}】的审核状态查询失败，请查看【送审备注】结合分析！");
                //// 错误 message
                //Console.WriteLine(error.Message);
                //// 诊断地址
                //Console.WriteLine(error.Data["Recommend"]);
                //AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
        }
    }
}
