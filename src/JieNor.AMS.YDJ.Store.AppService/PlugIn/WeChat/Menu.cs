using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.WeiXin.AppService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.WeChat
{
    [InjectService]
    [FormId("wx_menu")]
    [OperationNo("putmenu")]
    public class PutMenu : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            List<MenuModel> list = new List<MenuModel>();
            using (var reader = this.DBService.ExecuteReader(this.Context, "select * from t_wx_menu"))
            {
                while (reader.Read())
                {
                    MenuModel model = new MenuModel();
                    model.id = reader["fid"].ToString();
                    model.name = reader["fname"].ToString();
                    model.level = Convert.ToInt32(reader["flevel"].ToString().Replace("meun_level_0", ""));
                    model.responseType = reader["fresponsetype"].ToString() == "click" ? 0 : 1;
                    model.menuType = Convert.ToInt32(reader["fmenutype"].ToString().Replace("menu_type_0", "")) == 1 ? menuType.普通菜单 : menuType.个性化菜单;
                    model.parentid = reader["fparent"].ToString();
                    model.url = reader["fmenuurl"].ToString();
                    model.group.id = reader["fgroupid"].ToString();
                    list.Add(model);
                }
                this.Result.SrvData = new { log = WeiXinHelper.CreateMenu(list) };
            }
        }
    }
}
