using System;
using System.Linq;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using System.Text;
using JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Service;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc.AdvancedAPIs;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.DataTransferObject.Poco;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Service
{
    /// <summary>
    /// 服务单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_service")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 是否来源于中台下发
        /// </summary>
        private bool isMuSiSync { get; set; }
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            isMuSiSync = this.GetQueryOrSimpleParam<string>("IsMuSiSync", string.Empty).EqualsIgnoreCase("true");
            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!isMuSiSync && !oldData["fserstatus"].IsNullOrEmptyOrWhiteSpace() && (oldData["fserstatus"].Equals("sersta05") || oldData["fserstatus"].Equals("sersta06")))
                {
                    return false;
                }
                return true;
            }).WithMessage("该单据服务状态为已关闭或已取消，禁止保存操作！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fbilltypeid"].Equals("ydj_service_billtype_04") && newData["fquestiontype"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("问题类别不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fbilltypeid"].Equals("ydj_service_billtype_04") && newData["fquestiondesc"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("问题描述不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fbilltypeid"].Equals("ydj_service_billtype_04") && newData["fquestiontype"].Equals("afterquestion_type_01") && newData["fquestionimage"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("问题图片不能为空！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fsourcecancl"]) == "0" || Convert.ToString(newData["fservicetype"]) == "fres_type_03")
                {
                    return true;
                }

                DynamicObjectCollection entry = newData["fserviceentry"] as DynamicObjectCollection;
                //至少要有一行服务明细
                if (entry == null || entry.Count <= 0)
                {
                    return false;
                }
                if (!entry.IsNullOrEmpty() && entry.Any(x => !x["fseritemid"].IsNullOrEmptyOrWhiteSpace()))
                {
                    return true;
                }
                return false;
            }).WithMessage("请至少添加一行服务项目！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                DynamicObjectCollection entry = newData["fserviceentry"] as DynamicObjectCollection;
                if (!entry.IsNullOrEmpty() && entry.Any(x => Convert.ToDecimal(x["fqty"]) <= 0))
                {
                    return false;
                }
                return true;
            }).WithMessage("《服务项目》的【数量】必须大于0才能保存！"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data["fcustomerid"]).NotEmpty().WithMessage("客户不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fcollectrel"]).NotEmpty().WithMessage("收货人不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fcollectpho"]).NotEmpty().WithMessage("联系电话不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fprovince"]).NotEmpty().WithMessage("区域中的省份不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fcity"]).NotEmpty().WithMessage("区域中的城市不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fregion"]).NotEmpty().WithMessage("区域中的区县不能为空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data["fcollectadd"]).NotEmpty().WithMessage("详细地址不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //if (newData["fzbcollectadd"].IsNullOrEmptyOrWhiteSpace() && (newData["fprovince"].IsNullOrEmptyOrWhiteSpace() || newData["fcity"].IsNullOrEmptyOrWhiteSpace()
                //|| newData["fregion"].IsNullOrEmptyOrWhiteSpace() || newData["fcollectadd"].IsNullOrEmptyOrWhiteSpace()))
                //{
                //    return false;
                //}

                if (!newData["fbilltypeid"].Equals("ydj_service_billtype_04") && !isMuSiSync)
                {
                    if (newData["fcollectadd"].IsNullOrEmptyOrWhiteSpace())
                    {
                        return false;
                    }
                }

                return true;
            }).WithMessage("区域中的详细地址不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fservicetype"]).NotEmpty().WithMessage("服务类型不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fservicedate"]).NotEmpty().WithMessage("预约时间不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fagentid"]).NotEmpty().WithMessage("招商经销商不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data["fserstatus"]).NotEmpty().WithMessage("服务状态不能为空！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fimageLen = 0; var fztimageLen = 0;
                if (!newData["fimage"].IsNullOrEmptyOrWhiteSpace())
                {
                    fimageLen = newData["fimage"].ToString().Split(',').Length;
                }
                if (!newData["fztimage"].IsNullOrEmptyOrWhiteSpace())
                {
                    fztimageLen = newData["fztimage"].ToString().Split('|').Length;
                }
                if (fimageLen + fztimageLen > 9)
                {
                    return false;
                }
                return true;
            }).WithMessage("图片附件(总部图片+经销商图片总张数)最多9张！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fserstatus"]) != "sersta06" && (newData["fmasterid"].IsNullOrEmptyOrWhiteSpace() || newData["fteamid"].IsNullOrEmptyOrWhiteSpace()) && !newData["fserstatus"].Equals("sersta01"))
                {
                    return false;
                }
                return true;
            }).WithMessage("服务状态非派单状态下，服务人员和服务团队不可空！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (Convert.ToString(newData["fsourcecancl"]) == "0" || Convert.ToString(newData["fservicetype"]) == "fres_type_03")
            //    {
            //        return true;
            //    }
            //    DynamicObjectCollection entry = newData["fserviceentry"] as DynamicObjectCollection;
            //    //至少要有一行服务明细
            //    if (entry == null || entry.Count <= 0)
            //    {
            //        return false;
            //    }
            //    return true;
            //}).WithMessage(@"至少要有一行服务信息！"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                DynamicObjectCollection entry = newData["fserviceentry"] as DynamicObjectCollection;
                if (!entry.IsNullOrEmpty() && entry.Any(x => Convert.ToDecimal(x["fprice"]) < 0 || Convert.ToDecimal(x["fqty"]) < 0 || Convert.ToDecimal(x["famount"]) < 0))
                {
                    return false;
                }
                return true;
            }).WithMessage(@"服务信息中单价/数量/金额不能小于0！"));
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (newData["fservicetype"] != null && newData["fservicetype"].Equals("fres_type_03") && newData["fsourcetype"] != null && !newData["fsourcetype"].Equals("ste_afterfeedback"))
            //    {
            //        return false;
            //    }
            //    return true;
            //}).WithMessage(@"服务类型为【售后】，请保证当前单据源单类型为【售后反馈单】！"));

            ////“商品明细”必须符合以下要求
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    return CheckProductEntry(newData);
            //}).WithMessage(@"至少要有一行商品信息;商品信息【服务、计价单位、单价、数量、金额】不允许为空;【单价、数量、金额】不能为负数;【数量】必须是大于等于 1 的整数！"));

            ////验证服务项目是否重复
            //var itemUniqueRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BillUniqueValidation);
            //if (itemUniqueRule != null)
            //{
            //    itemUniqueRule.Initialize(this.Context, "fbillno,fseritemid");
            //    e.Rules.Add(itemUniqueRule);
            //}
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newdata, olddata) =>
            {
                //源单类型为销售出库单并且当前保存的服务单类型为送装
                if (Convert.ToString(newdata["fsourcetype"]) == "stk_sostockout" && Convert.ToString(newdata["fservicetype"]).Equals("fres_type_01"))
                {
                    //获取服务单
                    var allService = this.Context.LoadBizDataByFilter("ydj_service", " fsourcenumber in ('{0}') and fcancelstatus='0' and fservicetype='fres_type_01' and fid <>'{1}' ".Fmt(string.Join("','", newdata["fsourcenumber"]), newdata["id"]));
                    if (allService.Count() >= 1)
                    {
                        return false;
                    }
                }
                return true;
            }).WithMessage("对不起，当前上游销售出库单已存在其它送装服务单，禁止保存！"));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            string errMsg = string.Empty;
            //if (!CheckCanSave(e.DataEntitys, out errMsg))//6220需求 不要这个校验了
            //{
            //    throw new BusinessException(errMsg);
            //}
            HashSet<object> zbCancelData = new HashSet<object>();

            foreach (var data in e.DataEntitys)
            {
                var serviceitem = data["fserviceentry"] as DynamicObjectCollection;
                if (!serviceitem.IsNullOrEmpty() && serviceitem.Any())
                {
                    data["fexpectamount"] = serviceitem.Sum(x => Convert.ToDecimal(x["famount"]));
                }
                var fsourcecancl = Convert.ToString(data["fsourcecancl"]);

                if (fsourcecancl == "0" && Convert.ToString(data["fserstatus"]).EqualsIgnoreCase("sersta06"))
                {
                    //data["fserstatus"] = Convert.ToString(data["fserstatus"]).Replace("cancelservice","");
                    zbCancelData.Add(data["id"]);
                }
                if (fsourcecancl.IsNullOrEmptyOrWhiteSpace())
                {
                    data["fsourcecancl"] = "1";
                }
                var fvistzzentry = data["fvistzzentry"] as DynamicObjectCollection;
                var fvistszentry = data["fvistszentry"] as DynamicObjectCollection;
                var fvistshentry = data["fvistshentry"] as DynamicObjectCollection;
                for (int i = 0; i < fvistzzentry.Count; i++)
                {
                    fvistzzentry.Remove(fvistzzentry[i]);
                    i--;
                }
                for (int i = 0; i < fvistszentry.Count; i++)
                {
                    fvistszentry.Remove(fvistszentry[i]);
                    i--;
                }
                for (int i = 0; i < fvistshentry.Count; i++)
                {
                    fvistshentry.Remove(fvistshentry[i]);
                    i--;
                }
                //if (data["fagentid"].IsNullOrEmptyOrWhiteSpace())
                //{
                //    data["fagentid"] = this.Context.Company;
                //}
            }
            BuildEntryRowNum(e.DataEntitys, "fserviceentry");
            BuildEntryRowNum(e.DataEntitys, "fproductentry");
            // 填充字段默认值
            var defCalulator = this.Container.GetService<IDefaultValueCalculator>();
            defCalulator.Execute(this.Context, this.HtmlForm, e.DataEntitys);


            if (zbCancelData.Any())
            {
                var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, e.DataEntitys.Where(x => zbCancelData.Contains(x["id"])), "cancelservice", new Dictionary<string, object>
                {
                    { "IsMuSiSync","true"}
                });
                if (!result.IsSuccess)
                {
                    throw new BusinessException(string.Join(",", result.ComplexMessage.ErrorMessages));
                }
            }

            //金蝶接收到【来源渠道】=“总部下发”的服务单时，需发送小程序通知给通知给所有有服务单<派单>操作权限的人员
            sendQyVxMsg(e.DataEntitys);

            // 保存前预处理
            var preService = this.Container.GetService<IPrepareSaveDataService>();
            preService.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, Framework.SuperOrm.OperateOption.Create());

            var dm = this.GetDataManager();
            dm.InitDbContext(Context, this.HtmlForm.GetDynamicObjectType(Context));
            dm.Save(e.DataEntitys);

            if (e.DataEntitys.Any(x => !x["fsourcetype"].IsNullOrEmptyOrWhiteSpace() && x["fsourcetype"].Equals("stk_sostockout")))
            {
                var stockBills = this.Context.LoadBizDataByNo("stk_sostockout", "fbillno", e.DataEntitys.Where(x => !x["fsourcetype"].IsNullOrEmptyOrWhiteSpace() && x["fsourcetype"].Equals("stk_sostockout"))?.Select(x => x["fsourcenumber"].ToString()));
                foreach (var item in e.DataEntitys.Where(x => !x["fsourcetype"].IsNullOrEmptyOrWhiteSpace() && x["fsourcetype"].Equals("stk_sostockout")))
                {
                    var bill = stockBills.FirstOrDefault(x => x["fbillno"].Equals(item["fsourcenumber"]));
                    if (!bill.IsNullOrEmpty())
                    {
                        bill["finstallerid"] = item["fmasterid"];

                        //将员工名字也反写，否则上游销售出库单显示会有问题
                        var finstallerid = Convert.ToString(bill["finstallerid"]).Trim();
                        if (!finstallerid.IsNullOrEmptyOrWhiteSpace())
                        {
                            var staff = this.Context.LoadBizDataById("ydj_staff", finstallerid);
                            if (staff != null)
                            {
                                bill["finstallerid_txt"] = staff["fname"];
                            }
                        }


                    }
                }
                if (stockBills.Any())
                {
                    var stockForm = this.MetaModelService.LoadFormModel(this.Context, "stk_sostockout");
                    dm.InitDbContext(Context, stockForm.GetDynamicObjectType(Context));
                    preService.PrepareDataEntity(this.Context, stockForm, stockBills.ToArray(), OperateOption.Create());
                    dm.Save(stockBills);
                }
            }

        }
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            //【小程序功能：逻辑已在小程序处理，不到PC处理】0、正常保存，1、调整服务(调整服务将只修改服务信息)，2、转单
            string otherOpera = this.GetQueryOrSimpleParam("OtherOpera", "");
            bool callerTerminal = this.GetQueryOrSimpleParam("callerTerminal", "").ToUpper().Equals("MPAPI");
            //小程序转单后
            if (callerTerminal && otherOpera.Equals("2"))
            {
                Transfers(e);
            }
            List<ConvertSelectedRow> convertRows = new List<ConvertSelectedRow>();
            foreach (var dataEntity in e.DataEntitys)
            {
                convertRows.Add(new ConvertSelectedRow() { PkValue = dataEntity["id"] as string });
            }
            if (convertRows != null && convertRows.Any())
            {
                var vistBills = new List<DynamicObject>();
                var convertService = this.Container.GetService<IConvertService>();

                var result = convertService.Push(this.Context, new BillConvertContext()
                {
                    RuleId = "ydj_service2ydj_vist",
                    SourceFormId = "ydj_service",
                    TargetFormId = "ydj_vist",
                    SelectedRows = convertRows
                });
                var convertResult = result.SrvData as ConvertResult;
                if (convertResult.TargetDataObjects != null && convertResult.TargetDataObjects.Count() > 0)
                {
                    vistBills.AddRange(convertResult.TargetDataObjects);
                }
                if (vistBills.Any())
                {
                    var logger = this.Context.Container.GetService<ILogService>();
                    try
                    {
                        var invokeSave = this.Gateway.InvokeBillOperation(this.Context, "ydj_vist", vistBills, "save", new Dictionary<string, object>
                                        {
                                            { "IgnoreCheckPermssion", "true" }
                                        });
                        if (!invokeSave.IsSuccess)
                        {
                            logger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【回访单保存失败】，内容:{4}".Fmt(this.Context.UserName,
                                    this.Context.UserPhone, this.Context.Company,
                                 DateTime.Now.ToString("HH:mm:ss"), "SimpleMessage:" + invokeSave.SimpleMessage + ";ComplexMessage:" + JsonConvert.SerializeObject(invokeSave.ComplexMessage)),
                                "Service2Vist-Save");
                        }
                        invokeSave?.ThrowIfHasError(true, $"自动生成回访单失败！");
                    }
                    catch (Exception ex)
                    {
                        logger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【回访单保存失败】，内容:{4}".Fmt(this.Context.UserName,
                                this.Context.UserPhone, this.Context.Company,
                             DateTime.Now.ToString("HH:mm:ss"), JsonConvert.SerializeObject(ex)),
                            "Service2Vist-Save");
                        throw new BusinessException(ex.Message);
                    }
                }
            }
        }

        /// <summary>
        /// 金蝶接收到【来源渠道】=“总部下发”的服务单时，需发送小程序通知给通知给所有有服务单<派单>操作权限的人员
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntitys"></param>
        private void sendQyVxMsg(DynamicObject[] dataEntitys)
        {
            IEnumerable<DynamicObject> new2MsgData = dataEntitys.Where(x => !x.DataEntityState.FromDatabase && Convert.ToString(x["fsourcecancl"]) == "0");
            if (new2MsgData != null && new2MsgData.Any())
            {
                var permSvc = Context.Container.GetService<IPermissionService>();
                //有服务单<派单>操作权限的人员
                Framework.MetaCore.PermData.PermAuth serPermAuth = new Framework.MetaCore.PermData.PermAuth(Context);
                serPermAuth.FormId = "ydj_service";
                serPermAuth.OperationName = this.OperationContext.OperationName;
                serPermAuth.PermId = "ydj_setstatus01";//派单权限id
                List<string> userids = permSvc.HaveBizObPermitUsers(Context, serPermAuth);
                if (userids != null && userids.Any())
                {
                    var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                    //只加载需要用到的引用数据
                    refMgr.Load(this.Context, new2MsgData.ToArray(), false, this.HtmlForm, new List<string> { });
                    List<MsgContent> message = new List<MsgContent>();
                    var saveObjs = new List<DynamicObject>();
                    foreach (var item in new2MsgData)
                    {
                        message.Add(new MsgContent
                        {
                            //title = "慕思门店助手",
                            title = "已收到来自总部下发的服务单，请及时跟进！",
                            items = new Dictionary<string, string>
                            {
                                //{$"{DateTime.Now.Month}月{DateTime.Now.Day}日", string.Empty},
                                //{" ", "已收到来自总部下发的服务单，请及时跟进!"},
                                {"单据编号", Convert.ToString(item["fbillno"])},
                                {"客户名称", Convert.ToString((item["fcustomerid_ref"] as DynamicObject)?["fname"])},
                                {"下发时间", Convert.ToDateTime(item["fcreatedate"]).ToString("yyyy-MM-dd HH:mm:ss")},
                            },
                            page = "/page/serviceManage/pages/service/detail/detail?id=" + item["id"],
                            userids = userids
                        });
                    }
                    if (message.Any())
                    {
                        foreach (var item in message)
                        {
                            //企业微信发送小程序通知消息
                            MassApi.EwcSendMiniProgramMsgByUserIdsAsync(this.Context, item.title, item.content, item.items, item.page, item.userids);
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 判断是否能新增
        /// </summary>
        /// <param name="dataEntitys"></param>
        /// <param name="errorMsg"></param>
        /// <returns></returns>
        private bool CheckCanSave(DynamicObject[] dataEntitys, out string errorMsg)
        {
            errorMsg = string.Empty;
            //首先过滤当前保存集合中的服务状态非已取消项中源单类型和编号已重复项
            var distinctNos = dataEntitys.Where(x => !x["fserstatus"].Equals("sersta06") && !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace())
                .GroupBy(x => new { fsourcetype = x["fsourcetype"], fsourcenumber = x["fsourcenumber"] })
                .Where(g => g.Count() > 1).Select(x => x.Key.fsourcenumber.ToString());
            if (distinctNos.Any())
            {
                errorMsg = "当前提交的以下非已审核保存单据中，对应源单有重复项，请检查！【" + string.Join(",", distinctNos) + "】";
                return false;
            }
            var sourceTypes = dataEntitys.Where(x => !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fsourcetype"])).Distinct();
            var sourceNos = dataEntitys.Where(x => !x["fsourcenumber"].IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fsourcenumber"])).Distinct();

            if (sourceNos.Any())
            {
                StringBuilder sqlWhere = new StringBuilder();
                List<SqlParam> param = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", sourceNos.Select((x, i) => $"@fbillno{i}")));
                param.AddRange(sourceNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                param.AddRange(sourceTypes.Select((x, i) => new SqlParam($"@fsourcetype{i}", System.Data.DbType.String, x)));
                param.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company));
                var oldbills = new List<DynamicObject>();
                if (sourceTypes.Count() == 1)
                {
                    oldbills = this.Context.LoadBizDataByFilter("ydj_service", "fsourcenumber in(" + sqlWhere + ")  and fsourcetype=@fsourcetype0 and fmainorgid=@fmainorgid and fserstatus!='sersta06'", false, param);
                }
                else
                {
                    oldbills = this.Context.LoadBizDataByFilter("ydj_service", "fsourcenumber in(" + sqlWhere + ")  and fsourcetype in(" + string.Join(",", sourceTypes.Select((x, i) => $"@fsourcetype{i}")) + ") and fmainorgid=@fmainorgid and fserstatus!='sersta06'", false, param);
                }
                if (oldbills.Any())
                {
                    StringBuilder msg = new StringBuilder();
                    foreach (var item in dataEntitys)
                    {
                        if (!item["fserstatus"].Equals("sersta06"))
                        {
                            var temp = oldbills.Where(x => x["fsourcenumber"].Equals(item["fsourcenumber"]) && x["fsourcetype"].Equals(item["fsourcetype"]) && !x["fbillno"].Equals(item["fbillno"])).FirstOrDefault();
                            //已取消状态的不受影响
                            if (!temp.IsNullOrEmpty())
                            {
                                msg.Append("服务单【{0}】对应上游单据存在未取消的服务单【{1}】，请等待该单据取消或删除后再进行此操作！<br/>".Fmt(item["fbillno"], temp["fbillno"]));
                            }
                        }
                    }
                    if (msg.Length > 0)
                    {
                        errorMsg = msg.ToString();
                        return false;
                    }
                }
            }
            return true;
        }
        private void Transfers(AfterExecuteOperationTransaction e)
        {
            List<MsgContent> message = new List<MsgContent>();
            var auxPropValueMapForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_followerrecord");
            var auxPropValueMapFormDt = auxPropValueMapForm.GetDynamicObjectType(this.Context);
            var saveObjs = new List<DynamicObject>();
            foreach (var item in e.DataEntitys)
            {
                message.Add(new MsgContent
                {
                    title = "您有新的服务单任务待处理",
                    //content = $@"单据编号：{item["fbillno"]}，派单人：{this.Context.DisplayName ?? this.Context.UserName}",
                    items = new Dictionary<string, string>
                            {
                                {"单据编号", Convert.ToString(item["fbillno"])},
                                {"派单人", this.Context.DisplayName ?? this.Context.UserName},
                                {"派单时间", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                            },
                    page = "/page/serviceManage/pages/service/detail/detail?id=" + item["id"],
                    userids = new List<string> { Convert.ToString(item["fmasterid"]) }
                });
                saveObjs.Add(Followerrecord(item, auxPropValueMapFormDt));
            }
            if (message.Any())
            {
                foreach (var item in message)
                {
                    //企业微信发送小程序通知消息
                    MassApi.EwcSendMiniProgramMsgByUserIdsAsync(this.Context, item.title, item.content, item.items, item.page, item.userids);
                }
            }
            if (saveObjs.Any())
            {
                var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
                string deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
                string staffId = baseFormProvider.GetMyStaff(this.Context)?.Id;
                string operPhone = this.Context.LoadBizBillHeadDataById("sec_user", this.Context.UserId, "fphone")?["fphone"]?.ToString();
                saveObjs.ForEach(x =>
                {
                    x["fstaffid"] = staffId;//跟进员工id ydj_staff表 员工表
                    x["fdeptid"] = deptId;//跟进员工部门id ydj_dept表 部门表
                    x["fphone"] = operPhone;
                });
                var dm = this.GetDataManager();
                dm.InitDbContext(this.Context, auxPropValueMapFormDt);
                var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
                prepareSerivce.PrepareDataEntity(this.Context, auxPropValueMapForm, saveObjs.ToArray(), OperateOption.Create());
                dm.Save(saveObjs);
            }
        }
        /// <summary>
        /// 同步一条跟进记录
        /// </summary>
        /// <param name="data"></param>
        /// <param name="type">跟进记录类型</param>
        private DynamicObject Followerrecord(DynamicObject data, DynamicObjectType auxPropValueMapFormDt)
        {
            DynamicObject saveData = new DynamicObject(auxPropValueMapFormDt);
            saveData["fcustomerid"] = data["fcustomerid"];//客户ID 新
            saveData["frelatedbilltype"] = "ydj_service";//来源表
            saveData["fsourcetype"] = "ydj_service";//来源表
            saveData["frelatedbillno"] = data["fbillno"];//来源单据编号
            saveData["fsourcenumber"] = data["fbillno"];//来源单据编号
            saveData["ffollowtime"] = DateTime.Now;//跟进时间
            saveData["fobjecttype"] = "objecttype31";//跟进记录类型--转单
            saveData["ffollowerid"] = this.Context.UserId;//跟进人id sec_user表 用户表
            string operationUser = this.Context.DisplayName ?? this.Context.UserName;//当前用户名
            saveData["fcontacts"] = operationUser;
            saveData["fphone"] = this.Context.UserPhone;
            saveData["ftype"] = "6";
            saveData["fdescription"] = $"服务已转单，转单人：{operationUser}";//跟进描述
            return saveData;
        }

        /// <summary>
        /// 生成明细行标记
        /// </summary>
        /// <param name="dataEntitys"></param>
        /// <remarks>
        /// 生成规则：ZD（终端）+tranid，比如：ZD886907999616237575
        /// </remarks>
        private void BuildEntryRowNum(DynamicObject[] dataEntitys, string entryId)
        {
            if (dataEntitys == null || dataEntitys.Length <= 0) return;

            foreach (var dataEntity in dataEntitys)
            {
                var entrys = dataEntity[entryId] as DynamicObjectCollection;
                if (entrys == null) continue;
                foreach (var entry in entrys)
                {
                    if (entry["fnumber"].IsNullOrEmptyOrWhiteSpace())
                    {
                        entry["fnumber"] = $"ZD{entry["ftranid"]}";
                    }
                }
            }
        }
    }
}