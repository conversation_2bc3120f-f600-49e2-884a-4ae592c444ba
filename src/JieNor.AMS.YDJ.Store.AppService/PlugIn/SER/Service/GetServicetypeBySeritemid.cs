using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Service
{
    /// <summary>
    /// 根据服务项目ID获取服务类型
    /// </summary>
    [InjectService]
    [FormId("ydj_service")]
    [OperationNo("getservicetypebyseritemid")]
    public class GetServicetypeBySeritemid : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            //服务项目ID
            var seritemid = this.GetQueryOrSimpleParam<string>("seritemid");
            if (seritemid.IsNullOrEmptyOrWhiteSpace()) return;

            this.Result.SrvData = this.Context.LoadBizBillHeadDataByACLFilter("ydj_serviceitem",
                "fid=@fseritemid",
                "fservicetype",
                new List<SqlParam> { new SqlParam("@fseritemid", DbType.String, seritemid),});
        }
    }
}
