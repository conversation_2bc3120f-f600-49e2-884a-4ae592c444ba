using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.OpData;
using System.Collections;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using System.Text.RegularExpressions;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.Consts;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.DealerInfo
{
    /// <summary>
    /// 商/用户资料
    /// </summary>
    [InjectService]
    [FormId("ydj_dealerinfo")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            string strValidMsg = "";
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                return ValiMobile(n, out strValidMsg);
            }).WithMessage("{0}", (billData, pData) => strValidMsg));

            //验证手机号码是否重复
            var phoneUniqueRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BillUniqueValidation);
            if (phoneUniqueRule != null)
            {
                phoneUniqueRule.Initialize(this.Context, "fphone");
                e.Rules.Add(phoneUniqueRule);
            }
        }

        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            DynamicObject dataEntity = e.DataEntitys[0];
            if (dataEntity != null)
            {
                //如果该实体不是从数据库中加载的，则认为是新增
                if (!dataEntity.DataEntityState.FromDatabase)
                {
                    //商户用户注册
                    var userRegisterService = this.Container.GetService<IUserRegisterService>();
                    userRegisterService.RegisterUser(this.Context, new List<UserRegistry>() {
                        new UserRegistry() {
                            UserName = dataEntity["fphone"] as string,
                            Password = "123456",
                            DisplayName = dataEntity["fname"] as string,
                            Company = this.Context.Company,
                            UserType = (long)Enu_LoginUserType.EndCustomerUser,
                            TopCompanyId=this.Context.TopCompanyId,
                        }
                    });
                }
            }
        }

        //public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        //{
        //    base.BeforeExecuteOperationTransaction(e);
        //    if (e.DataEntitys == null || e.DataEntitys.Count() == 0)
        //    {
        //        return;
        //    }
        //    DynamicObject DO = e.DataEntitys.ToList().FirstOrDefault();
        //    string Str = Convert.ToString(DO["fphone"]);
        //    var regService = this.Container.GetService<IUserRegisterService>();
        //    var result = regService.RegisterUser(this.Context, new UserRegistry[] { new UserRegistry { UserName = Str, Password = "666666" } });
        //    Dictionary<string, Tuple<bool, string>> h = (Dictionary<string, Tuple<bool, string>>)result.SrvData;
        //    if (!(h[Str] as Tuple<bool, string>).Item1)
        //    {
        //        this.Result.SimpleMessage = "该手机号码已注册";
        //    }
        //    //#region 反写商户管理员
        //    //if (!DO["fdealerid"].IsNullOrEmptyOrWhiteSpace())
        //    //{
        //    //    var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_dealer");
        //    //    var dm = this.Container.GetService<IDataManager>();
        //    //    dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
        //    //    DynamicObject dyObj = dm.Select(Convert.ToString(DO["fdealerid"])) as DynamicObject;
        //    //    dyObj["fadminid"] = DO["fid"];
        //    //    dm.Save(dyObj);
        //    //}
        //    //#endregion
        //}

        private bool ValiMobile(DynamicObject newData, out string strErrMsg)
        {
            bool result = true;
            strErrMsg = "";
            string mobile = (newData["fphone"] ?? "").ToString();//联系方式
            //校验商户联系电话唯一性
            if (string.IsNullOrWhiteSpace(mobile))
            {
                strErrMsg = "请输入手机号";
            }
            else if (mobile.IsRegMatch(RegExPattern.Regex_Pattern_MobilePhone) == false)
            {
                strErrMsg = "手机号码输入不合法！";
                return false;
            }
            else
            {
                string sql = @"select fid,fname from t_ydj_dealerinfo where fphone = @mobile";
                List<SqlParam> parms = new List<SqlParam> {
                    new SqlParam ("@mobile", System.Data.DbType.String, mobile)
                };
                DynamicObjectCollection doc = this.DBService.ExecuteDynamicObject(this.Context, sql, parms);
                if (doc != null && doc.Count() > 0) {
                    strErrMsg = "已存在此联系方式的商户！";
                    return false;
                }
            }
            return result;
        }
    }


}
