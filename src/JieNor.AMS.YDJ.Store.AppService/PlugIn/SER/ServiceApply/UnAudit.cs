using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.ServiceApply
{
    /// <summary>
    /// 服务申请单：反审核
    /// </summary>
    [InjectService]
    [FormId("ser_apply")]
    [OperationNo("UnAudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            var scheduleBillService = this.Container.GetService<IScheduleBillService>();
            e.DataEntitys = scheduleBillService.CheckHasScheduleBill(this.Context, e.DataEntitys, this.HtmlForm, "fserviceentity", this.Result.ComplexMessage.ErrorMessages);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                this.Result.IsSuccess = false;
            }
        }
    }
}
