using System;
using System.Collections.Generic;
using System.Linq;

using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using System.Text.RegularExpressions;
using JieNor.Framework.Consts;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Dealer
{
    /// <summary>
    /// 商户保存
    /// </summary>
    [InjectService]
    [FormId("ydj_dealer")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //string strValidMsg = "";
            //e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            //{
            //    return ValiMobile(n, out strValidMsg);
            //}).WithMessage("{0}", (billData, pData) => strValidMsg));

            //验证手机号码是否重复
            var phoneUniqueRule = this.Container.GetValidRuleService(HtmlElementType.HtmlValidator_BillUniqueValidation);
            if (phoneUniqueRule != null)
            {
                phoneUniqueRule.Initialize(this.Context, "fphone,fmainorgid");
                e.Rules.Add(phoneUniqueRule);
            }
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            DynamicObject dataEntity = e.DataEntitys[0];
            if (dataEntity != null)
            {
                //如果该实体不是从数据库中加载的，则认为是新增
                if (!dataEntity.DataEntityState.FromDatabase)
                {
                    //自动为新增的商户，创建管理员商户资料
                    var dealerInfoMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_dealerinfo");

                    //单据头字段
                    DynamicObject headObj = new DynamicObject(dealerInfoMeta.GetDynamicObjectType(this.Context));
                    headObj["fdealerid"] = dataEntity["id"];
                    headObj["fphone"] = dataEntity["fphone"];
                    headObj["fname"] = dataEntity["fduty"];
                    headObj["fisadmin"] = true;
                    
                    //模拟正常表单保存操作流程
                    //var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_dealerinfo", new DynamicObject[] { headObj }, "save", new Dictionary<string, object>());
                    //if (result == null || !result.IsSuccess)
                    //{
                    //    if (result.ComplexMessage.HasMessage)
                    //    {
                    //        throw new BusinessException("创建管理员商户资料失败！");
                    //    }
                    //}
                }
            }
        }

        private bool ValiMobile(DynamicObject newData, out string strErrMsg)
        {
            bool result = true;
            strErrMsg = "";
            string mobile = (newData["fphone"] ?? "").ToString();//联系方式
            //校验商户联系电话唯一性
            if (string.IsNullOrWhiteSpace(mobile))
            {
                strErrMsg = "请输入手机号";
            }
            else if (mobile.IsRegMatch(RegExPattern.Regex_Pattern_MobilePhone) == false)
            {
                strErrMsg = "手机号码输入不合法！";
                return false;
            }
            else
            {
                string sql = @"select fid,fname from ser_ydj_dealer where fphone = @mobile";
                List<SqlParam> parms = new List<SqlParam> {
                    new SqlParam ("@mobile", System.Data.DbType.String, mobile)
                };
                DynamicObjectCollection doc = this.DBService.ExecuteDynamicObject(this.Context, sql, parms);
                if (doc != null && doc.Count() > 0)
                {
                    strErrMsg = "已存在此联系方式的商户！";
                    return false;
                }
            }
            return result;
        }
    }
}