using System;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MerchantOrder
{
    /// <summary>
    /// 商户订单：删除
    /// </summary>
    [InjectService]
    [FormId("ydj_merchantorder")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!Convert.ToString(newData["fserstatus"]).EqualsIgnoreCase("sht_serstatus00"))
                {
                    return false;
                }
                return true;
            }).WithMessage("商户订单【{0}】的订单状态不是【草稿】，不允许删除！", (billObj, propObj) => propObj["fbillno"]));
        }
    }
}