using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.Validator;
using System.Text.RegularExpressions;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Team
{
    /// <summary>
    /// 删除指定团队的队员
    /// </summary>
    [InjectService]
    [FormId("ydj_team")]
    [OperationNo("delmember")]
    public class TeamMemberDel : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            string Id = this.GetQueryOrSimpleParam<string>("Id");//团队id
            string Mid = this.GetQueryOrSimpleParam<string>("Mid");//队员id
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_team");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            DynamicObject dyObj = dm.Select(Id) as DynamicObject;
            if (dyObj != null)
            {
                var member = dyObj["fstaffentry"] as DynamicObjectCollection;
                member.Remove(member.Where(t => Convert.ToString(t["fstaffid"]) == Mid).FirstOrDefault());//删除队员
                dm.Save(dyObj);
            }
        }
    }
}