using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.ServiceFeed
{
    /// <summary>
    /// 添加服务反馈
    /// </summary>
    [InjectService]
    [FormId("ser_servicefeed")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length < 1) return;

            var dataEntity = e.DataEntitys[0];
            string fsourceser = Convert.ToString(dataEntity["fsourceser"]);
            string ffeedstatus = Convert.ToString(dataEntity["ffeedstatus"]);

            if (string.IsNullOrWhiteSpace(ffeedstatus))
            {
                dataEntity["ffeedstatus"] = "feedback_status01";
            }

            var dm = this.GetDataManager();
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_service");
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(formMeta, "fbillno = @fsouceno",
                new SqlParam[]
                {
                    new SqlParam("fsouceno", System.Data.DbType.String,fsourceser),
                });
            DynamicObject servDyo = dm.SelectBy(pkIdReader).OfType<DynamicObject>().FirstOrDefault();
            if (servDyo == null)
            {
                throw new BusinessException("服务单信息不存在!");
            }
            dataEntity["fsprotype"] = dataEntity["fsprotype"];// 问题类别 fsprotype
            dataEntity["fservicetype"] = servDyo["fservicetype"];// 订单类型
            dataEntity["fexpectamount"] = servDyo["fexpectamount"]; // 订单金额
            dataEntity["fname"] = servDyo["fname"];// 业主名称
            dataEntity["fphone"] = servDyo["fphone"];// 联系电话
            dataEntity["faddress"] = servDyo["faddress"];// 详细地址
            dataEntity["fmerbill"] = servDyo["fmerbill"];// 商户单号
            //问题反馈同时 将服务单关联部门、关联用户带到反馈单
            dataEntity["fstaffid_link"] = servDyo["fstaffid_link"];// 服务单关联用户
            dataEntity["fdeptid_link"] = servDyo["fdeptid_link"];// 服务单关联部门

            if ((this.Context.ClientType & (long)Enu_ClientType.Mobile) == (long)Enu_ClientType.Mobile)
            {
                #region 生成操作日志
                var data = e.DataEntitys[0];
                string operationUser = this.Context.DisplayName ?? this.Context.UserName;
                if (!data["fsourceser"].IsNullOrEmptyOrWhiteSpace())
                {
                    string Id = "";
                    using (var reader = this.DBService.ExecuteReader(this.Context, $"select top 1 fid from t_ydj_service  where fmainorgid='{this.Context.Company}' and fbillno= '{data["fsourceser"]}'"))
                    {
                        if (reader.Read())
                        {
                            Id = Convert.ToString(reader["fid"]);
                        }
                    }
                    if (Id.IsNullOrEmptyOrWhiteSpace())
                    {
                        return;
                    }

                    this.Option.SetIgnoreOpLogFlag();
                    //然后调用服务生成自己的日志：
                    this.Logger?.WriteLog(this.Context, new LogEntry()
                    {
                        BillFormId = "ydj_service",
                        OpCode = "App",
                        OpName = "APP端反馈",
                        Level = Enu_LogLevel.Info.ToString(),
                        Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),   //这个很关键，必须用这个值，平台才会帮你写入记录表
                        Content = $"师傅{operationUser}进行了问题反馈",//这个改成你的拒单原因
                        BillIds = Id,    //这个改成你实际被拒的服务单主键，多个用“,”分隔
                        Detail = ""      //这个可以不传，平台上用这个是为了后续便于分析本次请求中的明细信息，以便定位问题所在。
                    });
                }
                #endregion

            }
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (n["fprograph"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("问题反馈图至少上传一张！"));

            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (n["fprodesript"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请填写问题描述！"));

            e.Rules.Add(this.RuleFor("fbillhead", d => d).IsTrue((n, o) =>
            {
                if (n["fsprotype"].IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("请选择问题类别！"));
        }
    }
}