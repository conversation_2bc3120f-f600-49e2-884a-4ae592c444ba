using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.ComplaintRecord
{
    /// <summary>
    /// 投诉记录单更新状态
    /// </summary>
    [InjectService]
    [FormId("ser_complaintrecord")]
    [OperationNo("updstatus")]
    public class UpdateStatus : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length < 1) return;

            DynamicObject getData = e.DataEntitys[0];
            string complainstatus = Convert.ToString(getData["fcomplainstatus"]);
            string[] states = new string[] { "complain_status01", "complain_status02" };
            string fid = Convert.ToString(getData["Id"]);
            if (fid.IsNullOrEmptyOrWhiteSpace() || complainstatus.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("请求参数不能为空！");
            }
            else if (!states.Contains(complainstatus))
            {
                throw new BusinessException("请求状态参数错误！");
            }
            var formMeta = this.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.Context, "ser_complaintrecord");
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formMeta.GetDynamicObjectType(this.Context));
            var dyObj = dm.Select(fid) as DynamicObject;
            if (dyObj == null)
            {
                throw new BusinessException("投诉记录单不存在！");
            }
            if (complainstatus.EqualsIgnoreCase(Convert.ToString(dyObj["fcomplainstatus"])) == false)
            {
                throw new BusinessException("当前单据状态不是最新，请刷新页面重试！");
            }
            if (complainstatus == "complain_status01")
            {
                getData["fcomplainstatus"] = "complain_status02";
                this.Result.SimpleMessage = "撤诉成功！";
            }
            else if (complainstatus == "complain_status02")
            {
                getData["fcomplainstatus"] = "complain_status01";
                this.Result.IsSuccess = true;
                this.Result.SimpleMessage = "取消撤诉成功";
            }
            dm.Save(getData);
            this.Result.IsSuccess = true;
        }
    }
}
