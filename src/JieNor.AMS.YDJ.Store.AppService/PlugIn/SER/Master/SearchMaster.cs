using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SER.Master
{
    /// <summary>
    /// 师傅搜索
    /// </summary>
    [InjectService]
    [FormId("ydj_master")]
    [OperationNo("AddBySearch")]
    public class SearchMaster : AbstractOperationServicePlugIn
    {

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            string Phone = this.GetQueryOrSimpleParam<string>("Phone");
            if (string.IsNullOrWhiteSpace(Phone)) return;
            Phone = Phone.Replace(";", "").Replace("'", "").Replace("or ", "").Replace("--", "").Replace(" ", "");
            List<Dictionary<string, string>> list = new List<Dictionary<string, string>>();
            using (var reader = this.DBService.ExecuteReader(this.Context, $"SELECT a.[fid], (select fname from t_ser_ydj_team where fid = b.fid) as tname,a.[fphone],a.[fname],a.[fimage] FROM T_YDJ_MASTER as a left join [T_YDJ_STAFFTEAM] as b on a.fid=b.fstaffid where a.fmainorgid='{this.Context.Company}' and a.fapprovestatus='auth2' and (a.fphone like '%{Phone}%' or a.fname like '%{Phone}%')"))
            {
                while (reader.Read())
                {
                    Dictionary<string, string> outData = new Dictionary<string, string>();
                    outData.Add("id", Convert.ToString(reader["fid"]));
                    outData.Add("tname", Convert.ToString(reader["tname"]));
                    outData.Add("phone", Convert.ToString(reader["fphone"]));
                    outData.Add("name", Convert.ToString(reader["fname"]));
                    outData.Add("fimage", Convert.ToString(reader["fimage"]));//师傅头像
                    list.Add(outData);
                }
            }
            this.Result.SrvData = list;
        }
    }
}