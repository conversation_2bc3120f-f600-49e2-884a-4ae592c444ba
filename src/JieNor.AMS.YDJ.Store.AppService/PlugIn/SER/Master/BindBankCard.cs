using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Master
{
    /// <summary>
    /// 绑定银行卡
    /// </summary>
    [InjectService]
    [FormId("ydj_master")]
    [OperationNo("bindbankcard")]
    public class BindBankCard : AbstractOperationServicePlugIn
    {        
        /// <summary>
        /// 执行前
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction (BeforeExecuteOperationTransaction e)
        {
            this.Result.IsSuccess = false;
            e.Cancel = true;

            if (e.DataEntitys == null
                || e.DataEntitys.Length ==0)
            {
                this.Result.SimpleMessage = "师傅资料已被删除，请重新登录后再试！";                
                return;
            }
            if (e.DataEntitys.Length > 1)
            {
                this.Result.SimpleMessage = "绑定银行卡不支持批量操作！";
                return;
            }

            var bankCardId = this.GetQueryOrSimpleParam<string>("bankCardId");
            if (bankCardId.IsNullOrEmptyOrWhiteSpace()
                || bankCardId.Length<12)
            {
                this.Result.SimpleMessage = "银行卡号不可以为空或长度不得小于12位！";
                return;
            }
            var bankName = this.GetQueryOrSimpleParam<string>("bankName");
            var bankBranch = this.GetQueryOrSimpleParam<string>("bankBranch");
            if(bankName.IsNullOrEmptyOrWhiteSpace()
                || bankBranch.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SimpleMessage = "开户银行和银行网点名称不可以为空！";
                return;
            }
            var bankUserName = this.GetQueryOrSimpleParam<string>("bankUserName");
            if (bankUserName.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SimpleMessage = "开户人名称不可以为空！";
                return;
            }
            var bankMobile = this.GetQueryOrSimpleParam<string>("bankMobile");
            if (bankMobile.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.SimpleMessage = "开户人手机号不可以为空！";
                return;
            }

            var bankEntry = this.HtmlForm.GetEntryEntity("fbankcardentry");
            var bindBankItems = bankEntry.DynamicProperty.GetValue<DynamicObjectCollection>(e.DataEntitys[0]);
            var existBankItem = bindBankItems.FirstOrDefault(o => Convert.ToString(o["fbankcardid"]).EqualsIgnoreCase(bankCardId));
            if(existBankItem!=null)
            {
                this.Result.SimpleMessage = $"此卡号{bankCardId}已绑定到当前账号下，不允许重复绑定！";
                return;
            }

            existBankItem = bankEntry.DynamicObjectType.CreateInstance() as DynamicObject;
            existBankItem["fbankcardid"] = bankCardId;
            existBankItem["fbankname"] = bankName;
            existBankItem["fbankbranch"] = bankBranch;
            existBankItem["fbankusername"] = bankUserName;
            existBankItem["fbankmobile"] = bankMobile;
            bindBankItems.Add(existBankItem);

            var seqSrv = this.Container.GetService<IDataEntityPkService>();
            seqSrv.AutoSetPrimaryKey(this.Context, e.DataEntitys, this.HtmlForm.GetDynamicObjectType(this.Context));
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            this.Result.IsSuccess = true;
        }
    }
}
