using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.ServiceChange
{
    [InjectService]
    [FormId("ydj_servicechange")]
    [OperationNo("unaudit")]
    public class Unaudit : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null ||e.DataEntitys.Length<= 0) return;
            var opReason = this.GetQueryOrSimpleParam<string>("opDesc");
            var fid = string.Empty;
            foreach(var dataEntity in e.DataEntitys)
            {
                dataEntity["funauditreason"] = opReason;
                fid = dataEntity["Id"]?.ToString();
            }

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(e.DataEntitys);

            string sql = string.Empty;
            var fentity = this.HtmlForm.GetEntryEntity("fentity");
            var fentityItems = fentity.DynamicProperty.GetValue<DynamicObjectCollection>(e.DataEntitys[0]);
            if (fentityItems.Any())
            {
                fentityItems.ToList().ForEach(o =>
                {
                    sql += $"UPDATE t_ydj_serviceproduct SET fqty = (SELECT TOP 1 fqty FROM t_ydj_servicechangeentry WHERE fentryid = '{o["Id"]}'),famount = (SELECT TOP 1 fqty FROM t_ydj_servicechangeentry WHERE fentryid = '{o["Id"]}') * fprice WHERE fentryid = (SELECT TOP 1 fsourceentryid FROM t_ydj_servicechangeentry WHERE fentryid = '{o["Id"]}')";
                });
                this.Container.GetService<IDBServiceEx>().Execute(this.Context, sql);
            }

        }
    }
}
