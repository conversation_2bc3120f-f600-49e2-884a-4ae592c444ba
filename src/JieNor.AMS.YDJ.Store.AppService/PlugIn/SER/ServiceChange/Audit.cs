using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.IncomeDisburse;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.ServiceChange
{
    /// <summary>
    /// 服务变更单审核插件
    /// </summary>
    [InjectService]
    [FormId("ydj_servicechange")]
    [OperationNo("audit")]
    public class Audit:AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            //审核成功后，自动调用商户订单进行结算
            var merchantOrderInterIdField = this.HtmlForm.GetField("fmerchantorderinterid");
            if (merchantOrderInterIdField == null) return;

            ExtendedDataEntitySet ds = new ExtendedDataEntitySet();
            ds.Parse(this.Context, e.DataEntitys, this.HtmlForm);
            var linkEntityObjs = ds.FindByEntityKey(merchantOrderInterIdField.EntityKey);

            var allMerchantOrderIds = linkEntityObjs.Select(o => merchantOrderInterIdField.DynamicProperty.GetValue(o.DataEntity))
                .Where(o => !o.IsEmptyPrimaryKey())
                .Distinct();

            var merchantBillMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_merchantorder");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, merchantBillMeta.GetDynamicObjectType(this.Context));
            var allMerchantBillObjs = dm.Select(allMerchantOrderIds)
                .OfType<DynamicObject>();

            foreach (var merchantBillObj in allMerchantBillObjs)
            {
                this.AutoSettleAmount(merchantBillObj);
            }

            dm.Save(allMerchantBillObjs);
        }


        private void AutoSettleAmount(DynamicObject dataEntity)
        {
            decimal dExpectAmount = (decimal)dataEntity["fexpectamount"];
            decimal dSettleAmount = (decimal)dataEntity["fsettleamount"];
            if (Math.Abs(dSettleAmount - dExpectAmount) < 0.01m) return;

            IncomeDisburseRecord tranDto = new IncomeDisburseRecord()
            {
                //收
                Direction = "direction_02",
                BizDirection = "bizdirection_01",
                //订单扣款
                SceneType = "bizpurpose_02",
                SettleDate = DateTime.Now,
                SettleAgentId = "",
                TranFormId = "ydj_customer",
                TranBillId = dataEntity["fmerchantid"] as string,
                LinkFormId = "ydj_merchantorder",
                LinkBillId = dataEntity["id"] as string,
                LinkBillNo = dataEntity["fbillno"] as string,
                LinkTranId = dataEntity["ftranid"] as string,
                AttachId = "",
                Description = "商户审核订单变更时系统自动扣款！",
                MyBankId = "",
                SynBankId = "",
                SynBankName = "",
                SynBankNum = "",
                SynAccountName = "",
                AutoConfirm = true
            };

            List<AccountSettleInfo> accountSettleInfo = new List<AccountSettleInfo>();
            tranDto.AccountSettleInfo = accountSettleInfo;
            accountSettleInfo.Add(new AccountSettleInfo
            {
                AccountName = "货款账户",
                AccountId = "settleaccount_type_01",
                Amount = dExpectAmount - dSettleAmount,
                BankCardNo = "",
                SettleType = "payway_01"
            });

            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();
            var settleResult = synAccountBalanceService.CreateIncomeDisburseRecord(this.Context, tranDto, this.Option);
            settleResult.ThrowIfHasError(true, "确认时结算订单出现异常错误！");

            this.Result.ComplexMessage.SuccessMessages.Add($"本次订单变更关联的商户订单【{dataEntity["fbillno"]}】已自动扣款￥{MathUtil.Round(dExpectAmount - dSettleAmount, 2, RoundMode.AwayFromZero) }，详情可查看账户交易明细！");
            dataEntity["fsettleamount"] = dataEntity["fexpectamount"];
        }
    }
}
