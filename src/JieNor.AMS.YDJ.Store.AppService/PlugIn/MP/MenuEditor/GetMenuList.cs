using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MP.MenuEditor
{
    /// <summary>
    /// 小程序菜单编辑器：获取菜单列表
    /// </summary>
    [InjectService]
    [FormId("mp_menueditor")]
    [OperationNo("GetMenuList")]
    public class GetMenuList : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            this.Result.SrvData = MPMenuHelper.GetMPTabbars(this.Context);
            this.Result.IsSuccess = this.Result.SrvData != null;
        }
    }
}