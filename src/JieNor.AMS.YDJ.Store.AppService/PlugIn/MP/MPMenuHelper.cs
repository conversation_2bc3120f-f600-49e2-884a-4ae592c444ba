using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MP
{
    /// <summary>
    /// 小程序菜单模块服务
    /// </summary>
    [InjectService]
    public class MPMenuHelper : IReceivedPubMessage
    {
        private static MPMenuCache _cache = new MPMenuCache();

        private static object _lockObj = new object();

        public static void Init(UserContext userCtx)
        {
            TryCacheDatas(userCtx);
        }
         
        public static void WriteDebugLog(string content)
        {
            DebugUtil.WriteLogToFile(content, "MPMenuHelper");
        }

        /// <summary>
        /// 获取小程序菜单项
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static List<MPTabbarModel> GetMPTabbars(UserContext userCtx, bool isTopOrg = false)
        {
            var menuObjs = GetMPMenues(userCtx, isTopOrg);
            if (menuObjs == null || !menuObjs.Any()) return null;

            var srvData = new List<MPTabbarModel>();

            var tabbarObjs = menuObjs
                .GroupBy(x => x.ftabbar)
                .Select(x => new { tabbar = x.Key, tabbarSort = x.Min(o => o.ftabbarsort), groups = x.ToList() })
                .ToList();

            tabbarObjs.Sort((x, y) => x.tabbarSort - y.tabbarSort); //排序

            foreach (var tabbarObj in tabbarObjs)
            {
                var tabbarItem = new MPTabbarModel { tabbar = tabbarObj.tabbar, tabbarSort = tabbarObj.tabbarSort, groups = new List<MPGroupModel>() };
                srvData.Add(tabbarItem);

                var groupObjs = tabbarObj.groups
                    .GroupBy(x => x.fgroup)
                    .Select(g => new { group = g.Key, groupSort = g.Min(x => x.fgroupsort), menus = g.ToList() })
                    .ToList();

                groupObjs.Sort((x, y) => x.groupSort - y.groupSort); //排序

                foreach (var groupObj in groupObjs)
                {
                    var groupItem = new MPGroupModel { group = groupObj.group, groupSort = groupObj.groupSort, menus = new List<MPMenuModel>() };
                    tabbarItem.groups.Add(groupItem);

                    var menus = groupObj.menus;
                    menus.Sort((x, y) => x.fsort - y.fsort); //排序

                    foreach (var menu in menus)
                    {
                        groupItem.menus.Add(new MPMenuModel
                        {
                            id = menu.fid,
                            number = menu.fnumber,
                            name = menu.fname,
                            sort = menu.fsort,
                            isShortcut = menu.fisshortcut,
                            icon = menu.ficon,
                            url = menu.furl
                        });
                    }
                }
            }

            return srvData;
        }

        /// <summary>
        /// 清空缓存
        /// </summary>
        /// <param name="userCtx"></param>
        public static void ClearCache(UserContext userCtx)
        {
            ClearCache();
            userCtx.Container.GetService<IPubSubService>().PublishMessage(ConstPubSubChannel.MPMenuChannel, " ");
        }

        private static void ClearCache()
        {
            lock (_lockObj)
            {
                WriteDebugLog("小程序菜单清空缓存");
                _cache = new MPMenuCache();
            }
        }

        private static bool TryCacheDatas(UserContext userCtx)
        {
            if (_cache?.AllMPMenu.Count > 0) return false;

            CacheDatas(userCtx);

            return true;
        }

        private static void CacheDatas(UserContext userCtx)
        {
            lock (_lockObj)
            {
                MPMenuCache cache = new MPMenuCache();

                CacheAdmin_AgentMenuIds(userCtx, cache);
                CacheMenu(userCtx, cache);

                cache.CacheTime = DateTime.Now;

                _cache = cache;
            }
        }

        private static void CacheAdmin_AgentMenuIds(UserContext userCtx, MPMenuCache cache)
        {
            ////经销商用户：只能看总部授予经销商的权限（分级授权）
            //List<SqlParam> lstSqlParas = new List<SqlParam>()
            //{
            //    new SqlParam("@topcompanyid", System.Data.DbType.String,userCtx.TopCompanyId),
            //    new SqlParam("@companyid", System.Data.DbType.String,userCtx.Company),
            //};

            //var strSql = @"SELECT  distinct  t0.fmenuid 
            //             FROM t_mp_rolemenu t0 with(nolock) 
            //             where exists ( select 1 from t_sec_role t1 with(nolock) 
            //                            INNER JOIN t_sec_roleuser t2 with(nolock) ON t0.froleId = t2.froleId 
            //                            INNER JOIN t_sec_user t3 with(nolock) ON t2.fuserid = t3.fid 
            //                            WHERE  t1.fmainorgid = @topcompanyid  /*经销商的管理员角色在总部*/ 
            //                                    AND t3.fmainorgid = @companyid  /*经销商的管理员用户在经销商企业里面*/ 
            //                                  and  t0.froleId = t1.fid  
            //                           )
            //           ";

            // 直接使用【经销商管理员】角色的小程序菜单授权 
            List<SqlParam> lstSqlParas = new List<SqlParam>()
            {
                new SqlParam("@topcompanyid", System.Data.DbType.String, userCtx.TopCompanyId)
            };

            var strSql = @"
select fmenuid from t_mp_rolemenu t0 with(nolock)
where froleid in (select top 1 fid from t_sec_role t2 with(nolock) where fmainorgid=@topcompanyid and fnumber='Admin_Agent')
";

            var svc = userCtx.Container.GetService<IDBService>();
            var datas = svc.ExecuteDynamicObject(userCtx, strSql, lstSqlParas);

            cache.Admin_AgentMenuIds = new HashSet<string>(datas.Select(f => Convert.ToString(f["fmenuid"])));
        }

        private static void CacheMenu(UserContext userCtx, MPMenuCache cache)
        {
            var menuObjs = userCtx.LoadBizDataByFilter("mp_menu", "1=1");

            List<MPMenu> mpMenus = new List<MPMenu>();

            foreach (var menuObj in menuObjs)
            {
                mpMenus.Add(new MPMenu
                {
                    fid = Convert.ToString(menuObj["id"]),
                    fgroup = Convert.ToString(menuObj["fgroup"]),
                    fgroupsort = Convert.ToInt32(menuObj["fgroupsort"]),
                    ficon = Convert.ToString(menuObj["ficon"]),
                    fisshortcut = Convert.ToBoolean(menuObj["fisshortcut"]),
                    fname = Convert.ToString(menuObj["fname"]),
                    fnumber = Convert.ToString(menuObj["fnumber"]),
                    fsort = Convert.ToInt32(menuObj["fsort"]),
                    ftabbar = Convert.ToString(menuObj["ftabbar"]),
                    ftabbarsort = Convert.ToInt32(menuObj["ftabbarsort"]),
                    furl = Convert.ToString(menuObj["furl"]),
                });
            }

            cache.AllMPMenu = mpMenus;
        }



        private static IEnumerable<MPMenu> GetMPMenues(UserContext userCtx, bool isTopOrg = false)
        {
            TryCacheDatas(userCtx);

            //总部可以看到所有菜单权限
            if (isTopOrg || userCtx.IsTopOrg)
            {
                return _cache.AllMPMenu.AsEnumerable();
            }

            var menuIds = _cache.Admin_AgentMenuIds;
            if (menuIds.Any())
            {
                return _cache.AllMPMenu.Where(s => menuIds.Contains(s.fid));
            }

            return new List<MPMenu>();
        }

        public string ChannelName
        {
            get { return ConstPubSubChannel.MPMenuChannel; }
        }

        public void OnReceivedMessage(string msg)
        {
            //var userCtx = msg.FromJson<UserContext>();
            //userCtx.Container = ServiceStack.HostContext.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            ClearCache();
            //TryCacheDatas(userCtx);
        }
    }

    internal class MPMenuCache
    {
        public HashSet<string> Admin_AgentMenuIds = new HashSet<string>();

        public List<MPMenu> AllMPMenu = new List<MPMenu>();

        public DateTime CacheTime { get; set; } = DateTime.MinValue;
    }

    internal class MPMenu
    {
        public string fid { get; set; }

        public string ftabbar { get; set; }

        public int ftabbarsort { get; set; }

        public string fgroup { get; set; }

        public int fgroupsort { get; set; }

        public string fnumber { get; set; }

        public string fname { get; set; }

        public int fsort { get; set; }

        public string ficon { get; set; }

        public bool fisshortcut { get; set; }

        public string furl { get; set; }
    }
}
