using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
    /// 运维问题：根据运维分类获取问题应响应时间
    /// </summary>
    [InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("getshouldresponsetime")]
    public class GetShouldResponseTime : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            this.Result.IsSuccess = true;


            var profileService = this.Context.Container.GetService<ISystemProfile>();
            //获取【运维问题管理参数】补班
            var fadjustment = profileService.GetSystemParameter(this.Context, "bas_maintenancesysparam", "fadjustment", "");
            List<DateTime> adjustMentDateList = new List<DateTime>();

            string[] adjustMentDateArray = fadjustment.Split(';');
            foreach (string dateStrItem in adjustMentDateArray)
            {
                if (DateTime.TryParse(dateStrItem, out DateTime date))
                {
                    adjustMentDateList.Add(date);
                }
                else
                {
                    Console.WriteLine($"【运维问题管理参数】补班参数配置的日期格式不正确：{dateStrItem}");
                }
            }
            //获取【运维问题管理参数】节假日
            var fholidays = profileService.GetSystemParameter(this.Context, "bas_maintenancesysparam", "fholidays", "");
            List<DateTime> holidaysDateList = new List<DateTime>();

            string[] holidaysDateArray = fholidays.Split(';');
            foreach (string dateStrItem in holidaysDateArray)
            {
                if (DateTime.TryParse(dateStrItem, out DateTime date))
                {
                    holidaysDateList.Add(date);
                }
                else
                {
                    Console.WriteLine($"【运维问题管理参数】节假日参数配置的日期格式不正确：{dateStrItem}");
                }
            }

            var maintenanceType = this.GetQueryOrSimpleParam("maintenanceType", "");  //运维分类
            var submitDate = this.GetQueryOrSimpleParam("submitDate", "");  //提单时间

            if (!maintenanceType.IsNullOrEmptyOrWhiteSpace() && !submitDate.IsNullOrEmptyOrWhiteSpace())
            {
                if (DateTime.TryParse(submitDate, out var realSubmitDate))
                {

                    int year = realSubmitDate.Year;
                    int month = realSubmitDate.Month;
                    int day = realSubmitDate.Day;
                    int hour = realSubmitDate.Hour;

                    DateTime shouldresponsetime = new DateTime(year, month, day, 23, 59, 59);

                    var addDays = 0;
                    switch (maintenanceType)
                    {
                        //操作问题(当天18:00前完成)
                        case "maintenancetype_01":
                        //数据问题(当日处理)
                        case "maintenancetype_02":
                        //权限问题(当日处理)
                        case "maintenancetype_03":
                        //1类BUG(16:00前的当日处理其他次日12:00前处理)
                        case "maintenancetype_04":
                            //> 18点 + 1天
                            if (hour >= 18) addDays++;
                            this.Result.SrvData = GetRealeRsponseTime(shouldresponsetime, addDays, adjustMentDateList, holidaysDateList);
                            break;
                        //2类BUG(3天内处理)
                        //BUG或需求存在争议(3天内确认方案)
                        case "maintenancetype_05":
                        case "maintenancetype_06":
                            addDays = 1;
                            //> 18点 + 1天
                            if (hour >= 18) addDays++;
                            this.Result.SrvData = GetRealeRsponseTime(shouldresponsetime, addDays, adjustMentDateList, holidaysDateList);
                            break;
                        default:
                            this.Result.IsSuccess = false;
                            this.Result.SrvData = null;
                            break;
                    }
                }
                else
                {
                    this.Result.IsSuccess = false;
                    this.Result.SrvData = null;
                }

            }
            else
            {
                this.Result.IsSuccess = false;
                this.Result.SrvData = null;
            }
        }


        /// <summary>
        /// 计算实际应响应时间
        /// </summary>

        public DateTime GetRealeRsponseTime(DateTime shouldresponsetime,int days, List<DateTime> adjustMentDateList, List<DateTime> holidaysDateList)
        {
            var ortherDays = 0;
            var tempDate = shouldresponsetime;
            var tempDays = days;
            var isCheck = false;

            for (int i = 0; i <= tempDays; i++)
            {
                isCheck = false;

                if (!isCheck && CheckDateTimeHelper.checkDateTimeIsAdjustMentDate(tempDate, adjustMentDateList))
                {
                    isCheck = true;
                }

                if (!isCheck && CheckDateTimeHelper.checkDateTimeIsHoliday(tempDate, holidaysDateList))
                {
                    ortherDays++;
                    tempDays++;
                    isCheck = true;
                }

                if (!isCheck && CheckDateTimeHelper.checkDateTimeIsWorkDay(tempDate))
                {
                    isCheck = true;
                }

                if (!isCheck && CheckDateTimeHelper.checkDateTimeIsWeeKend(tempDate))
                {
                    ortherDays++;
                    tempDays++;
                    isCheck = true;
                }

                tempDate = tempDate.AddDays(1);
            }

            shouldresponsetime = shouldresponsetime.AddDays(days + ortherDays);
            return shouldresponsetime;
        }
    }
}
