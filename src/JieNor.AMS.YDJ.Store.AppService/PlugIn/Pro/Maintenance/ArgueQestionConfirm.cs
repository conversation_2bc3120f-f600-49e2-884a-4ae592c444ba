using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{

    /// <summary>
    /// 运维问题：争议问题确认
    /// </summary>
    [InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("argueqestionconfirm")]
    public class ArgueQestionConfirm : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!Convert.ToString(newData["fmaintenancetype"]).EqualsIgnoreCase("maintenancetype_06"))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，只有运维分类=BUG/需求存在争议(3天内确认方案)，才允许操作!"));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["fquestionprogress"].Equals("q_progress_07"))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，该运维问题已关闭，不允许操作！"));

        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var item in e.DataEntitys) 
            {
                var maintenanceType = item["fmaintenancetype"];  //运维分类
                var dateTime = DateTime.Now;
                item["fsubmitdate"] = dateTime;  //提单时间
                item["fisdispute"] = "1";  //是否争议

                //获取问题应解决时间
                if (!maintenanceType.IsNullOrEmptyOrWhiteSpace() && !dateTime.IsNullOrEmptyOrWhiteSpace())
                {
                    var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", null, "getshoulddonedate", new Dictionary<string, object>() { { "maintenanceType", maintenanceType }, { "submitDate", dateTime } });

                    res.ThrowIfHasError();

                    if (res.IsSuccess)
                    {
                        item["fshoulddonedate"] = res.SrvData;  //问题应解决时间
                        var actSettleTime = item["factsettletime"];  //开发实际解决时间

                        var shouldDoneDateIsOk = DateTime.TryParse(Convert.ToString(item["fshoulddonedate"]), out var realShouldDoneDate);
                        var actSettleTimeIsOk = DateTime.TryParse(Convert.ToString(actSettleTime), out var realActSettleTime);

                        //先重置
                        item["fisoutdeadline"] = "0"; //是否延期
                        item["foutdeadlinedays"] = ""; //延期天数
                        item["fwarningdelay"] = ""; //预警延期

                        if (shouldDoneDateIsOk)
                        {
                            if (realActSettleTime > realShouldDoneDate)
                            {
                                item["fisoutdeadline"] = "1"; //是否延期
                                if (actSettleTimeIsOk)
                                {
                                    TimeSpan timeDifference = realActSettleTime - realShouldDoneDate;
                                    var days = Math.Round(timeDifference.TotalDays, 5).ToString("0.00000");
                                    item["foutdeadlinedays"] = days; //延期天数

                                }
                            }
                            
                            TimeSpan timeDifference1 = realShouldDoneDate - dateTime;
                            var days1 = Math.Round(timeDifference1.TotalDays, 5).ToString("0.00000");
                            item["fwarningdelay"] = days1; //预警延期
                        }
                    }

                }
            }

            var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

            result.ThrowIfHasError();

            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            //var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
            //prepareSerivce.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            //dm.Save(e.DataEntitys);

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}
