using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
    /// 运维问题：列表查询数据插件
    /// </summary>
    [InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("querydata")]
    public class QueryData : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义服务端事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case OnCustomServiceEventArgs.PrepareQueryBuilderParameter:
                    this.PrepareQueryBuilderParameter(e);
                    break;
                case "afterListData":
                    afterListData(e);
                    break;
            }
        }

        /// <summary>
        /// 列表准备查询过滤参数事件
        /// </summary>
        /// <param name="e"></param>
        public void PrepareQueryBuilderParameter(OnCustomServiceEventArgs e)
        {
            var param = e.EventData as SqlBuilderParameter;
            //if (param == null) return;

        }

        /// <summary>
        /// 处理列表查询的数据
        /// </summary>
        /// <param name="e"></param>
        private void afterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;
            if (listData == null || listData.Count <= 0) return;

            //处理《开发人员》和《运维人员》辅助资料是否匹配问题
            //获取《开发人员》辅助资料
            string strSql1 = "select fentryid,fenumitem from t_bd_enumdataentry with(nolock) where fid='1065220001303232512'";
            var dpdatas = this.Context.ExecuteDynamicObject(strSql1, new List<SqlParam>() { });

            //防止《开发人员》辅助资料fid变了
            if (dpdatas.IsNullOrEmptyOrWhiteSpace() || dpdatas.Count < 1)
            {
                strSql1 = $@"select t0.fentryid,t0.fenumitem
                                             from t_bd_enumdataentry t0 
                                             inner join t_bd_enumdata t1 on t0.fid=t1.fid
                                             where t1.fname='开发人员'";

                dpdatas = this.Context.ExecuteDynamicObject(strSql1, new List<SqlParam>() { });
            }

            //获取《运维人员》辅助资料
            string strSql2 = "select fentryid,fenumitem from t_bd_enumdataentry with(nolock) where fid='1067413633632436224'";
            var mpdatas = this.Context.ExecuteDynamicObject(strSql2, new List<SqlParam>() { });

            //防止《运维人员》辅助资料fid变了
            if (mpdatas.IsNullOrEmptyOrWhiteSpace() || mpdatas.Count < 1)
            {
                strSql2 = $@"select t0.fentryid,t0.fenumitem
                                             from t_bd_enumdataentry t0 
                                             inner join t_bd_enumdata t1 on t0.fid=t1.fid
                                             where t1.fname='运维人员'";

                mpdatas = this.Context.ExecuteDynamicObject(strSql2, new List<SqlParam>() { });
            }

            var fentryid = string.Empty;
            var fenumitem = string.Empty;

            foreach (var item in listData)
            {
                fentryid = string.Empty;
                fenumitem = string.Empty;

                if (item.ContainsKey("fquestiondesc"))
                {
                    item["fquestiondesc"] = Convert.ToString(item["fquestiondesc"]).Replace("%^%", ">");
                }
                if (item.ContainsKey("fdescription"))
                {
                    item["fdescription"] = Convert.ToString(item["fdescription"]).Replace("%^%", ">");
                }
                if (item.ContainsKey("freplyreason"))
                {
                    item["freplyreason"] = Convert.ToString(item["freplyreason"]).Replace("%^%", ">");
                }
                if (item.ContainsKey("fprimaryanalysis"))
                {
                    item["fprimaryanalysis"] = Convert.ToString(item["fprimaryanalysis"]).Replace("%^%", ">");
                }
                //匹配开发人员
                foreach (var item1 in dpdatas)
                {
                    if (Convert.ToString(item1["fentryid"]).Trim().Equals(Convert.ToString(item["fdeveloper"]).Trim()))
                    {
                        fentryid = Convert.ToString(item1["fentryid"]).Trim();
                        fenumitem = Convert.ToString(item1["fenumitem"]).Trim();
                    }
                }

                item["fdeveloper"] = fentryid;
                item["fdeveloper_fenumitem"] = fenumitem;

                fentryid = string.Empty;
                fenumitem = string.Empty;

                //匹配运维人员
                foreach (var item2 in mpdatas)
                {
                    if (Convert.ToString(item2["fentryid"]).Trim().Equals(Convert.ToString(item["fmaintenanceperson"]).Trim()))
                    {
                        fentryid = Convert.ToString(item2["fentryid"]).Trim();
                        fenumitem = Convert.ToString(item2["fenumitem"]).Trim();
                    }
                }

                item["fmaintenanceperson"] = fentryid;
                item["fmaintenanceperson_fenumitem"] = fenumitem;


            }

        }


    }
}
