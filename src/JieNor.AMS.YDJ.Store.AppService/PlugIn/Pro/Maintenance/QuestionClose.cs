
using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
	/// 运维问题：关闭
	/// </summary>
	[InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("questionclose")]
    public class QuestionClose : AbstractOperationServicePlugIn
    {


        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fquestionprogress"]).EqualsIgnoreCase("q_progress_07"))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，只有问题进度≠已关闭，才允许操作!"));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (newData["frepeatquestionno"].ToString().EqualsIgnoreCase(newData["id"].ToString()))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，问题重复单号不能关联自己!!"));
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            List<DynamicObject> updateObjs = new List<DynamicObject>();
            var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            //解决方案
            var solutionid = SimpleData["solutionid"];
            var solutionName = SimpleData["solutionName"];
            var frepeatquestionnoid = SimpleData["frepeatquestionnoid"];
            var frepeatquestionnoNo = SimpleData["frepeatquestionnoNo"];
            //解决方案为空
            if (solutionid.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("对不起，关闭失败,请指定解决方案！");
            if(solutionid=="solution_03"&& frepeatquestionnoid.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("对不起，关闭方案选择重复问题,则重复问题单号不能为空！");
            e.DataEntitys[0]["fsolution"] = solutionid;

            var closetime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            e.DataEntitys[0]["fquestionprogress"] = "q_progress_07";
            e.DataEntitys[0]["fquestionclosetime"] = closetime;

            e.DataEntitys[0]["fcloseid"] = this.Context.UserId;
            e.DataEntitys[0]["fclosestatus"] = "1";
            e.DataEntitys[0]["fclosedate"] = closetime;
            e.DataEntitys[0]["fisrepeatquestion"] = solutionid == "solution_03" ? "1" : "0";
          
            if (!string.IsNullOrWhiteSpace(frepeatquestionnoid))
            {
                var _values = e.DataEntitys[0]["frepeatquestionno"].ToString().Trim().SplitKey(",").Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                var _values_txt = e.DataEntitys[0]["frepeatquestionno_txt"].ToString().Trim().SplitKey(",").Where(x => !string.IsNullOrWhiteSpace(x)).ToList(); 
                var _concatvalues = frepeatquestionnoid.Trim().SplitKey(",").Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                var _concatvalues_txt = frepeatquestionnoNo.Trim().SplitKey(",").Where(x => !string.IsNullOrWhiteSpace(x)).ToList();
                _concatvalues.Remove(e.DataEntitys[0]["id"].ToString());
                _concatvalues_txt.Remove(e.DataEntitys[0]["fbillno"].ToString());
                e.DataEntitys[0]["frepeatquestionno"] = string.Join(",", _concatvalues);
                e.DataEntitys[0]["frepeatquestionno_txt"] = string.Join(",", _concatvalues_txt);
                e.DataEntitys[0]["frepeatquestioncount"] = _concatvalues.Count;
                var result = _values.Except(_concatvalues);
                var maintenances = this.Context.LoadBizDataById("ydj_maintenance", _values.Union(_concatvalues));
                if (maintenances.Any()) {
                    foreach (var _ in maintenances.Where(p => _concatvalues.Contains(p["id"])))
                    {
                        _["fisrepeatquestion"] = 1;
                        if (string.IsNullOrWhiteSpace(_["frepeatquestionno"].ToString()))
                        {
                            _["frepeatquestionno"] = e.DataEntitys[0]["id"];
                            _["frepeatquestionno_txt"] = e.DataEntitys[0]["fbillno"];
                            _["frepeatquestioncount"] = "1";
                        }
                        else
                        {
                            var values = Convert.ToString(_["frepeatquestionno"]).Trim().SplitKey(",");
                            var values_txt = Convert.ToString(_["frepeatquestionno_txt"]).Trim().SplitKey(",");
                            if (!values.Contains(e.DataEntitys[0]["id"]))
                            {
                                ;
                                _["frepeatquestionno"] = string.Join(",", values.Concat(new[] { e.DataEntitys[0]["id"] }));
                                _["frepeatquestionno_txt"] = string.Join(",", values_txt.Concat(new[] { e.DataEntitys[0]["fbillno"] }));
                                _["frepeatquestioncount"] = values.Count + 1;
                            }
                        }
                        updateObjs.Add(_);
                    }
                    foreach (var _ in maintenances.Where(p => result.Contains(p["id"])))
                    {
                        var values = Convert.ToString(_["frepeatquestionno"]).Trim().SplitKey(",").Where(x => !string.IsNullOrWhiteSpace(x)).ToList(); ;
                        var values_txt = Convert.ToString(_["frepeatquestionno_txt"]).Trim().SplitKey(",").Where(x => !string.IsNullOrWhiteSpace(x)).ToList(); ;
                        if (values.Contains(e.DataEntitys[0]["id"].ToString()))
                        {
                            values.Remove(e.DataEntitys[0]["id"].ToString());
                            values_txt.Remove(e.DataEntitys[0]["fbillno"].ToString());
                            _["frepeatquestionno"] = string.Join(",", values);
                            _["frepeatquestionno_txt"] = string.Join(",", values_txt);
                            _["frepeatquestioncount"] = values.Count;
                            if (values.Count == 0)
                            {
                                _["fisrepeatquestion"] = 0;
                            }
                        }
                        updateObjs.Add(_);
                    }
                }
             
            }


            //var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

            //if (!res.IsSuccess)
            //{
            //    throw new BusinessException(res.ComplexMessage.ToString());
            //}
            updateObjs.Add(e.DataEntitys[0]);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
            prepareSerivce.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            dm.Save(updateObjs);

            //运维人员
            var maintenancePerson = Convert.ToString(e.DataEntitys[0]["fmaintenanceperson"]).Trim();

            var zenpathnumber = e.DataEntitys[0]["fzenpathnumber"];

            if (!zenpathnumber.IsNullOrEmptyOrWhiteSpace())
            {
                var dataBase = this.GetAppConfig("ms.zt.database"); //所属数据库
                if (dataBase.IsNullOrEmptyOrWhiteSpace())
                {
                    throw new BusinessException("对不起，未配置运维问题所属禅道数据库！请检查 host.config 配置 ms.zt.database！");
                }

                //更新禅道任务
                string updateMySql = string.Format($@"UPDATE {dataBase}.zt_task SET status='closed',closedDate='{closetime}',lastEditedDate='{closetime}' where id='{zenpathnumber}'");
                var init = MySqlHelper.Instance;
                MySqlHelper.ExecuteNonQuery(updateMySql);

                //更新禅道任务备注
                var msg = solutionName;
                var insertMySql = string.Format($@"insert into  {dataBase}.zt_action(`objectType`,`objectID`,`project`,`execution`,`actor`,`action`,`date`,`comment`)
                select 'task',(select id from {dataBase}.zt_task where id='{zenpathnumber}'),(select project from {dataBase}.zt_task where id='{zenpathnumber}'),
                       (select execution from {dataBase}.zt_task where id='{zenpathnumber}'),'{maintenancePerson}','closed','{currentTime}','{msg}'
                ");
                MySqlHelper.ExecuteNonQuery(insertMySql);
            }

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}
