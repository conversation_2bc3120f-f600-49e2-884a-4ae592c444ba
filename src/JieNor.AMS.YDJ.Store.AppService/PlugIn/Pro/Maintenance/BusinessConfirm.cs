using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
    /// 运维问题：商务确认
    /// </summary>
    [InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("businessconfirm")]

    public class BusinessConfirm : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!Convert.ToString(newData["fmaintenancetype"]).EqualsIgnoreCase("maintenancetype_07"))
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起，只有运维分类=新需求，才允许操作!"));
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //人天
            var humanNature = SimpleData["humanNature"];

            //商务确认时间
            var businessConfirmDate = SimpleData["businessConfirmDate"];

            //人天为空
            if (humanNature.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("对不起，商务确认失败,请指定人天！");

            //商务确认时间
            if (businessConfirmDate.IsNullOrEmptyOrWhiteSpace()) throw new BusinessException("对不起，商务确认失败,请指定商务确认时间！");

            e.DataEntitys[0]["fhumannature"] = humanNature;
            e.DataEntitys[0]["fbusinessconfirmdate"] = businessConfirmDate;
            e.DataEntitys[0]["fisbusinessconfirm"] = "1";

           

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(Context));
            var prepareSerivce = this.Container.GetService<IPrepareSaveDataService>();
            prepareSerivce.PrepareDataEntity(this.Context, this.HtmlForm, e.DataEntitys, OperateOption.Create());
            dm.Save(e.DataEntitys);

            var zenpathnumber = e.DataEntitys[0]["fzenpathnumber"];

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}
