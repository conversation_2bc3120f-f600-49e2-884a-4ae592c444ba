using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pro.Maintenance
{
    /// <summary>
    /// 运维问题：数据修复完成
    /// </summary>
    [InjectService]
    [FormId("ydj_maintenance")]
    [OperationNo("datarepairfinish")]
    public class DataRepairFinish : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var dataConfirmDate = Convert.ToString(newData["fdataconfirmdate"]);
                if (dataConfirmDate.IsNullOrEmptyOrWhiteSpace())
                {
                    return false;
                }
                return true;
            }).WithMessage("对不起,数据修复完成操作失败！数据确认时间为空，请先进行数据确认！"));
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            foreach (var item in e.DataEntitys)
            {
                item["fdatarepairfinishdate"] = currentTime;
            }

            var res = this.Gateway.InvokeBillOperation(this.Context, "ydj_maintenance", e.DataEntitys, "save", new Dictionary<string, object>() { });

            res.ThrowIfHasError();

            //刷新页面
            this.AddRefreshPageAction();
        }
    }
}
