using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IM.Notice
{
    /// <summary>
    /// 公告:刷新
    /// </summary>
    [InjectService]
    [FormId("sys_dashboard")]
    [OperationNo("Renow")]
    public class Renow : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e); 
        } 

    }
}
