using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MT.SuiteSelection
{
    [InjectService]
    [FormId("mt_suiteselection")]
    [OperationNo("Save")]
    public class Save : SelectionBase
    {
        Dictionary<string, string> identities = new Dictionary<string, string>();
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var errorMsg = "";
            //套件数量不能为小于0
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entity = newData["fentity"] as DynamicObjectCollection;
                if (entity != null && entity.Count >= 0)
                {
                    if(entity.Where(x => decimal.TryParse(x["fqty"].ToString(), out var qty) && qty <= 0).Any())
                    {
                        errorMsg = "子件产品的数量不能等于0！";
                        return false;
                    }
                }
                else
                {
                    errorMsg = "子件明细不能为空！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMsg));

            //套件选配内容不能重复
            e.Rules.Add(
                this.RuleFor("fbillhead", data => data)
                    .IsTrue((n, o) =>
                    {
                        var es = n["fentity"] as DynamicObjectCollection;

                        var dmBase = this.Context.Container.GetService<IDataManager>();
                        var metaBase = this.MetaModelService.LoadFormModel(this.Context, "mt_partsselection");
                        dmBase.InitDbContext(this.Context, metaBase.GetDynamicObjectType(this.Context));
                        SortedDictionary<string, string> partsIdentities = new SortedDictionary<string, string>();
                        dmBase.Select(es.Select(x => x["fpartsselectionid"]))
                            .OfType<DynamicObject>().ToList().ForEach(x =>
                            {
                                partsIdentities.Add(x["fproductid"].ToString(), x["fidentity"].ToString());
                            });

                        var productid = n["fproductid"].ToString();
                        var identity = GetSuiteSelectionIdentity(productid, Convert.ToDecimal(n["fploidy"]), es, partsIdentities);
                        identities[productid] = identity;
                        var suiteselection = GetSuiteSelectionByIdentity(n["fproductid"].ToString(), identity, n["id"].ToString());
                        var flag = suiteselection == null;
                        if (!flag)
                        {
                            errorMsg = $"套件选配内容与编号为【{suiteselection["fnumber"]}】的相同，保存失败！";
                        }
                        return flag;
                    })
                    .WithMessage("{0}", (billObj, propObj) => errorMsg)
            );
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            var entity = e.DataEntitys.FirstOrDefault();
            if (entity == null) return;

            this.Context.Container.GetService<LoadReferenceObjectManager>()?.Load(
                this.Context,
                this.HtmlForm.GetDynamicObjectType(this.Context),
                entity,
                false
            );
            var es = entity["fentity"] as DynamicObjectCollection;
            var description = GetSuiteSelectionDescription(es);

            //设置组合说明
            entity["fdescription"] = description;

            //识别码
            entity["fidentity"] = identities[entity["fproductid"].ToString()];
        }
    }
}
