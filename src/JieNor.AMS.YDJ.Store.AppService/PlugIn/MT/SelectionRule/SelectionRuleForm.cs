using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MT.SelectionRule
{
    /// <summary>
    /// 选配规则：显示选配规则框
    /// </summary>
    [InjectService]
    [FormId("mt_selectionrule")]
    [OperationNo("selectionruleform")]
    public class SelectionRuleForm : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var selectionrangeid = this.GetQueryOrSimpleParam<string>("selectionrangeid");

            //选配范围下可用的选配维度
            var sql = @"SELECT a.fid, a.fnumber, a.fname, a.fvaluetype, a.frefbaseformid, a.frefenumformid, a.fdatatype
FROM T_MT_SELECTIONDIMENSION a
INNER JOIN T_MT_SELECTIONCATEGORYENTRY b ON a.fid = b.fselectiondimensionid
INNER JOIN T_MT_SELECTIONRANGE c ON c.fselectioncategoryid = b.fid
WHERE c.fid = @fselectionrangeid AND b.fwhetherinforce = 1";
            var dimensions = this.DBService.ExecuteDynamicObject(this.Context, sql, new[] { new SqlParam("fselectionrangeid", System.Data.DbType.String, selectionrangeid) });

            //选配维度对应的维度值
            var fieldSql = @"SELECT '{0}' AS dname, {0} as dvalue
FROM T_MT_SRDIMENSIONENTRY 
WHERE fid = @fselectionrangeid AND {0} != '' AND fdimensionrowauditstatus = 'A'
GROUP BY {0}";
            sql = dimensions.Select(x =>
            {
                var number = x["fnumber"].ToString().ToLower();
                return fieldSql.Fmt("f" + number);
            }).JoinEx(" UNION ", false);
            var ds = this.DBService.ExecuteDynamicObject(this.Context, sql, new[] { new SqlParam("fselectionrangeid", System.Data.DbType.String, selectionrangeid) });
            /*
             * {
             *     ff000001: "1,2,3,4",
             *     ff000002: "1,2,3,4"
             * }
             */
            //var arr = new JArray();
            var dimensionRange = new JObject();
            ds.GroupBy(x => x["dname"].ToString()).ToList().ForEach(g =>
            {
                dimensionRange[g.Key] = g.Select(x => x["dvalue"].ToString()).JoinEx(",", false);
            }); ;

            var meta = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "mt_selectionruleform");
            var parentId = this.CurrentPageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString() : this.CurrentPageId;

            var action = this.Context.ShowSpecialForm(
                meta,
                null,
                true,
                parentId,
                Enu_OpenStyle.Modal,
                Enu_DomainType.Dynamic,
                null,
                (formPara) =>
                {
                    formPara.DomainType = Enu_DomainType.Dynamic;
                    formPara.OpenStyle = Enu_OpenStyle.Modal;
                    formPara.CustomParameter["dimensions"] = dimensions.ToJson();
                    formPara.CustomParameter["dimensionRanges"] = dimensionRange.ToJson();
                    formPara.CustomParameter["rule"] = this.GetQueryOrSimpleParam<string>("rule", "");
                    formPara.CustomParameter["rowid"] = this.GetQueryOrSimpleParam<string>("rowid", "");
                    formPara.CustomParameter["parentId"] = parentId;
                }
            );
            this.Result.IsSuccess = true;
            this.Result.ComplexMessage?.SuccessMessages.Clear();
            this.Result.HtmlActions.Add(action);
        }
    }
}
