using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Senparc.Weixin.Helpers.Extensions;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MT.SelectionRange
{
    /// <summary>
    /// 选配范围：协同保存
    /// </summary>
    [InjectService]
    [FormId("mt_selectionrange")]
    [OperationNo("SyncSave")]
    public class SyncSave : UpdateMTSchemePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var syncdata = this.GetQueryOrSimpleParam<string>("syncdata");
            var billDatas = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(syncdata);
            if (syncdata.IsNullOrWhiteSpace())
            {
                this.Result.SimpleMessage = "协同失败：没有传入有效数据！";
                this.Result.IsSuccess = false;
                return;
            }

            //物料全部查询出来，内存里面处理
            var product = GetConvertBaseId("t_bd_material");
            //选配类别全部查询出来
            var selectType = GetConvertBaseId("t_mt_selectioncategory");
            //选配范围查出来
            var range = GetConvertBaseId("t_mt_selectionrange");
            //选配维度字段全部查出来
            var fieldsObj = GetDimensionField();

            //抽象处理
            //传入选配维度的基础资料类型，以后存在多种类型（物料，维度物料等），按照传入的基础资料加载数据
            //加载所有基础资料的数据
            Dictionary<string, List<DynamicObject>> allBaseData = new Dictionary<string, List<DynamicObject>>();
            var formIds = fieldsObj.Where(p => Convert.ToString(p["fvaluetype"]).EqualsIgnoreCase("basedata")).Select(p => Convert.ToString(p["frefbaseformid"])).Distinct();
            foreach (var formId in formIds)
            {
                var htmlForm = this.MetaModelService.LoadFormModel(this.Context, formId);
                if (htmlForm != null && !htmlForm.IsNullOrEmptyOrWhiteSpace())
                {
                    var baseData = GetConvertBaseId(htmlForm.BillHeadTableName);
                    allBaseData.Add(formId, baseData);
                }
            }

            foreach (var billData in billDatas)
            {
                var dataContent = billData["dataContent"] as string;
                var jsContent = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(dataContent);

                //直营转经销的时候，云链ID 是存在云链的，还是需要判断云链ID是否存在数据，存在更新

                //已存在则更新
                var chaindataid = jsContent.GetString("fid");
                var dataId = range.Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(chaindataid)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();

                if (!dataId.IsNullOrEmptyOrWhiteSpace())
                {
                    UpdateData(jsContent, product, selectType, fieldsObj, dataId, allBaseData);
                }
                else
                {
                    AddData(jsContent, product, selectType, fieldsObj, allBaseData);
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = "发布成功！";
        }

        /// <summary>
        /// 新增单据
        /// </summary>
        /// <param name="jsContent"></param>
        /// <param name="product"></param>
        /// <param name="selectType"></param>
        /// <param name="fieldsObj">选配维度字段</param>
        /// <param name="allBaseData">所有基础资料的数据</param>
        private void AddData(Dictionary<string, object> jsContent, List<DynamicObject> product, List<DynamicObject> selectType, List<DynamicObject> fieldsObj, Dictionary<string, List<DynamicObject>> allBaseData)
        {
            List<DynamicObject> dataEntrys = new List<DynamicObject>();
            var htmlForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "mt_selectionrange");

            var seqService = this.Container.GetService<ISequenceService>();
            var selectionrange = htmlForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
            //单据头
            selectionrange["id"] = seqService.GetSequence<string>();
            selectionrange["fname"] = jsContent["fname"] as string;
            selectionrange["fnumber"] = jsContent["fnumber"] as string;
            var selectTypeIdChaindataId = jsContent["selectTypeIdChaindataId"] as string;
            selectionrange["fselectioncategoryid"] = selectType.Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(selectTypeIdChaindataId)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();
            //selectionrange["fauditstatus"] = jsContent["fauditstatus"] as string;
            selectionrange["fremark"] = jsContent["fremark"] as string;

            selectionrange["ffromchaindataid"] = jsContent["fid"] as string;//云链ID是K3选配范围id
            selectionrange["fdataorigin"] = "下载";
            selectionrange["fdownloaddate"] = DateTime.Now;
            selectionrange["fmainorgid"] = this.Context.Company;
            selectionrange["fmainorgid_txt"] = this.ProductName();
            selectionrange["fmainorgid_pid"] = this.ProductId();

            //适用产品范围
            var fproductentrys = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(Convert.ToString(jsContent["fproductentry"]));
            var productentrys = selectionrange["fproductentry"] as DynamicObjectCollection;
            foreach (var fproductentry in fproductentrys)
            {
                var productentry = productentrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                var materialChaindataid = fproductentry["materialChaindataid"] as string;
                productentry["id"] = seqService.GetSequence<string>();
                var productID = product.Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(materialChaindataid)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();
                productentry["fproductid"] = productID.IsNullOrEmptyOrWhiteSpace() ? "0" : productID;
                productentry["frowauditstatus"] = fproductentry["frowauditstatus"] as string;
                productentrys.Add(productentry);
            }

            //加载选配范围 选配维度模型 判断模型字段是否存在，不存在抛出异常
            var entryEntityModel = htmlForm.GetEntryEntity("fdimensionentry");

            //选配维度的F+fnumber是字段名
            var fdimensionentrys = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(Convert.ToString(jsContent["fdimensionentry"]));
            var dimensionentrys = selectionrange["fdimensionentry"] as DynamicObjectCollection;
            foreach (var fdimensionentry in fdimensionentrys)
            {
                var dimensionentry = dimensionentrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                dimensionentry["id"] = seqService.GetSequence<string>();
                dimensionentry["fdimensionrowauditstatus"] = fdimensionentry["fdimensionrowauditstatus"] as string;
                foreach (var fieldDim in fieldsObj)
                {
                    var field = Convert.ToString(fieldDim["FNUMBER"]);
                    var formId = Convert.ToString(fieldDim["frefbaseformid"]);//当前基础资料formId

                    //判断选配维度模型字段是否存在，不存在抛出错误
                    var properties = entryEntityModel?.DynamicObjectType?.Properties?.OfType<DynamicProperty>();
                    var refFieldProperty = properties?.FirstOrDefault(p => p.Name.EqualsIgnoreCase(field));
                    if (refFieldProperty == null)
                    {
                        this.Result.SimpleMessage = $"协同失败：在麦浩选配范围单据中，单据体选配维度字段{field}不存在！";
                        this.Result.IsSuccess = false;
                        continue;
                    }

                    //包含云链id的 转换本地ID
                    if (fdimensionentry.ContainsKey($"{field}_ChaindataId"))
                    {
                        //转换基础资料对应的本地ID
                        var fieldChaindataId = fdimensionentry[$"{field}_ChaindataId"] as string;
                        //查询对应的基础资料
                        var baseDataId = allBaseData[formId].Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(fieldChaindataId)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();
                        dimensionentry[$"{field}"] = baseDataId.IsNullOrEmptyOrWhiteSpace() ? "0" : baseDataId;
                        continue;
                    }
                    //传输的数据里面包含当前字段，&& 已存储的数据为空，则认为还没存数据
                    if (fdimensionentry.ContainsKey($"{field}") && Convert.ToString(dimensionentry[$"{field}"]).IsNullOrEmptyOrWhiteSpace())
                    {
                        dimensionentry[$"{field}"] = fdimensionentry[$"{field}"] as string;
                    }
                }
                dimensionentrys.Add(dimensionentry);
            }
            dataEntrys.Add(selectionrange);

            var prepareSaveDataService = this.Context.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, htmlForm, dataEntrys.ToArray(), OperateOption.Create());

            //调用保存
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            dm.Save(dataEntrys);
        }

        /// <summary>
        /// 更新单据
        /// </summary>
        /// <param name="jsContent"></param>
        /// <param name="product"></param>
        /// <param name="selectType"></param>
        /// <param name="fieldsObj">选配维度字段</param>
        /// <param name="dataId">要更新的数据Id</param>
        /// <param name="allBaseData">所有基础资料的数据</param>
        private void UpdateData(Dictionary<string, object> jsContent, List<DynamicObject> product, List<DynamicObject> selectType, List<DynamicObject> fieldsObj, string dataId, Dictionary<string, List<DynamicObject>> allBaseData)
        {
            var selectionrange = this.Context.LoadBizDataById("mt_selectionrange", dataId);
            if (selectionrange.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            var seqService = this.Container.GetService<ISequenceService>();
            var htmlForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "mt_selectionrange");
            //单据头
            selectionrange["fname"] = jsContent["fname"] as string;
            selectionrange["fnumber"] = jsContent["fnumber"] as string;
            var selectTypeIdChaindataId = jsContent["selectTypeIdChaindataId"] as string;
            selectionrange["fselectioncategoryid"] = selectType.Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(selectTypeIdChaindataId)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();
            //selectionrange["fauditstatus"] = jsContent["fauditstatus"] as string;
            selectionrange["fremark"] = jsContent["fremark"] as string;

            selectionrange["ffromchaindataid"] = jsContent["fid"] as string;//云链ID是K3选配范围id
            selectionrange["fdataorigin"] = "下载";
            selectionrange["fdownloaddate"] = DateTime.Now;
            selectionrange["fmainorgid"] = this.Context.Company;
            selectionrange["fmainorgid_txt"] = this.Context.ProductName;
            selectionrange["fmainorgid_pid"] = this.Context.Product;
            selectionrange["fcreatorid"] = this.Context.UserId;
            selectionrange["fcreatedate"] = DateTime.Now;

            //适用产品范围
            var fproductentrys = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(Convert.ToString(jsContent["fproductentry"]));
            var productentrys = selectionrange["fproductentry"] as DynamicObjectCollection; ;

            //移除所有产品范围单据体，重新创建
            foreach (var removeItem in productentrys.ToList())
            {
                productentrys.Remove(removeItem);
            }

            foreach (var fproductentry in fproductentrys)
            {
                var productentry = productentrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                var materialChaindataid = fproductentry["materialChaindataid"] as string;
                var productID = product.Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(materialChaindataid)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();
                productentry["id"] = seqService.GetSequence<string>();
                productentry["fproductid"] = productID.IsNullOrEmptyOrWhiteSpace() ? "0" : productID;
                productentry["frowauditstatus"] = fproductentry["frowauditstatus"] as string;
                productentrys.Add(productentry);
            }

            //选配维度的F+fnumber是字段名
            var fdimensionentrys = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(Convert.ToString(jsContent["fdimensionentry"]));
            var dimensionentrys = selectionrange["fdimensionentry"] as DynamicObjectCollection;

            //加载选配范围 选配维度模型 判断模型字段是否存在，不存在抛出异常
            var entryEntityModel = htmlForm.GetEntryEntity("fdimensionentry");

            //移除所有选配维度单据体，重新创建
            foreach (var removeItem in dimensionentrys.ToList())
            {
                dimensionentrys.Remove(removeItem);
            }

            foreach (var fdimensionentry in fdimensionentrys)
            {
                var dimensionentry = dimensionentrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                dimensionentry["id"] = seqService.GetSequence<string>();
                dimensionentry["fdimensionrowauditstatus"] = fdimensionentry["fdimensionrowauditstatus"] as string;
                foreach (var fieldDim in fieldsObj)
                {
                    var field = Convert.ToString(fieldDim["FNUMBER"]);
                    var formId = Convert.ToString(fieldDim["frefbaseformid"]);//当前基础资料formId
                    //判断选配维度模型字段是否存在，不存在抛出异常
                    var properties = entryEntityModel?.DynamicObjectType?.Properties?.OfType<DynamicProperty>();
                    var refFieldProperty = properties?.FirstOrDefault(p => p.Name.EqualsIgnoreCase(field));
                    if (refFieldProperty == null)
                    {
                        throw new BusinessException($"在麦浩选配范围单据中，单据体选配维度字段{field}不存在！");
                    }

                    //包含云链id的 转换本地ID
                    if (fdimensionentry.ContainsKey($"{field}_ChaindataId"))
                    {
                        //转换基础资料对应的本地ID
                        var fieldChaindataId = fdimensionentry[$"{field}_ChaindataId"] as string;
                        //查询对应的基础资料
                        var baseDataId = allBaseData[formId].Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(fieldChaindataId)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();
                        //var productID = product.Where(p => Convert.ToString(p["ffromchaindataid"]).Equals(fieldChaindataId)).Select(p => Convert.ToString(p["fid"]))?.FirstOrDefault();
                        dimensionentry[$"{field}"] = baseDataId.IsNullOrEmptyOrWhiteSpace() ? "0" : baseDataId;
                        continue;
                    }

                    //传输的数据里面包含当前字段，&& 已存储的数据为空，则认为还没存数据
                    if (fdimensionentry.ContainsKey($"{field}") && Convert.ToString(dimensionentry[$"{field}"]).IsNullOrEmptyOrWhiteSpace())
                    {
                        dimensionentry[$"{field}"] = fdimensionentry[$"{field}"] as string;
                    }
                }
                dimensionentrys.Add(dimensionentry);
            }

            List<DynamicObject> dataObj = new List<DynamicObject>();
            dataObj.Add(selectionrange);
            var prepareSaveDataService = this.Context.Container.GetService<IPrepareSaveDataService>();
            prepareSaveDataService.PrepareDataEntity(this.Context, htmlForm, dataObj.ToArray(), OperateOption.Create());

            //调用保存
            var dm = this.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
            dm.Save(dataObj);
        }

        /// <summary>
        /// 转换基础资料
        /// </summary>
        /// <param name="tableName">表</param>
        /// <returns></returns>
        private List<DynamicObject> GetConvertBaseId(string tableName)
        {
            var dbSvc = this.Context.Container.GetService<IDBService>();
            var sql = $"select fid, ffromchaindataid from {tableName} where fmainorgid = '{this.Context.Company}' and isnull(ffromchaindataid,'') != '';";
            var listObj = dbSvc.ExecuteDynamicObject(this.Context, sql).ToList();
            return listObj;
        }

        /// <summary>
        /// 获取选配维度fnumber，基础资料formID
        /// </summary>
        /// <returns></returns>
        private List<DynamicObject> GetDimensionField()
        {
            var dbSvc = this.Context.Container.GetService<IDBService>();
            var sql = @"select concat('f',FNUMBER) AS FNUMBER,frefbaseformid,fvaluetype from t_mt_selectiondimension where fmainorgid =@fmainorgid";
            var sqlParam = new List<SqlParam>()
                     {
                         new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
                     };
            var fieldsObj = dbSvc.ExecuteDynamicObject(this.Context, sql, sqlParam).ToList();
            return fieldsObj;
        }

    }
}
