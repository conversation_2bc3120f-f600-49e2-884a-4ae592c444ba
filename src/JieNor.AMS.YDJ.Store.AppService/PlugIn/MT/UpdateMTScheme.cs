using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.MT
{
    /// <summary>
    /// 选配范围：新增
    /// </summary>
    [InjectService]
    [FormId("mt_selectiondimension")]
    [OperationNo("buildscheme")]
    public class UpdateMTScheme : UpdateMTSchemePlugIn
    {
    }
}
