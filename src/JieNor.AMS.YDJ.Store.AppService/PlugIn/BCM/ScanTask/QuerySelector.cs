using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.ScanTask
{
    /// <summary>
    /// 收货扫描任务：动态列基础资料字段弹窗查询操作
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask")]
    [OperationNo("QuerySelector")]
    public class QuerySelector : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            // 只限于采购订单
            var targetFormId = this.GetQueryOrSimpleParam("targetFormId", "");
            if (!targetFormId.EqualsIgnoreCase("ydj_purchaseorder")) return;

            var fsupplierid = this.GetQueryOrSimpleParam("fsupplierid", "");

            string supplierFilter = string.Empty;
            if (!fsupplierid.IsNullOrEmptyOrWhiteSpace())
            {
                //控制同一个供应商
                supplierFilter += $" fsupplierid='{fsupplierid}' AND ";
            }

            //下游已经有采购入库单的单据不允许生成收货扫描任务
            string filterString = supplierFilter + $@" NOT EXISTS (SELECT fid FROM t_stk_postockin AS poin WITH(NOLOCK)
                        WHERE poin.fmainorgid = @currentCompanyId AND poin.fsourcetype='ydj_purchaseorder' and poin.fsourcenumber=t0.fbillno and poin.fstatus<>'E' and poin.fcancelstatus='0')";

            this.SimpleData["filterString"] = filterString;

        }
    }
}
