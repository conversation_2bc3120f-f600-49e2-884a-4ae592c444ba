using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.ScanTask
{
    /// <summary>
    /// 收货扫描任务：加载供应商
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask")]
    [OperationNo("loadsupplier")]
    public class LoadSupplier : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string result = "";
            var billno = this.GetQueryOrSimpleParam<string>("currBillNo");
            var bizObjs = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new List<string>() { billno });
            if (bizObjs != null && bizObjs.Any())
            {
                var fentry = bizObjs.FirstOrDefault()["ftaskentity"] as DynamicObjectCollection;

                //取表体扫描任务明细中第一行数据的源单供应商
                var firstEnt = fentry.FirstOrDefault();
                var sourceBill = Convert.ToString(firstEnt["fsourceformid"]);
                var sourceBillNo = Convert.ToString(firstEnt["fsourcebillno"]);
                if (sourceBill.EqualsIgnoreCase("ydj_purchaseorder")) //仅限源单类型为采购订单
                {
                    var sourceDataEntities = this.Context.LoadBizDataByNo(sourceBill, "fbillno", new List<string>() { sourceBillNo });
                    if (sourceDataEntities != null && sourceDataEntities.Any())
                    {
                        result = Convert.ToString(sourceDataEntities.FirstOrDefault()["fsupplierid"]);
                    }
                }
            }

            this.Result.IsSuccess = string.IsNullOrWhiteSpace(result) == false;
            this.Result.SrvData = result;
        }
    }
}
