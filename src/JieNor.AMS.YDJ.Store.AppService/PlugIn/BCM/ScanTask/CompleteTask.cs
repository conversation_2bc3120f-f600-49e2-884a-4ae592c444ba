using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.ScanTask
{
    /// <summary>
    /// 完成作业或继续作业
    /// </summary>
    [InjectService]
    [FormId("bcm_receptionscantask|bcm_countscantask|bcm_deliveryscantask|bcm_transferintask|bcm_transfertask")]
    [OperationNo("completetask")]
    public class CompleteTask : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var datas = e.DataEntitys;
            if(datas==null || datas.Length <= 0)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add("请选择扫描任务!");
                return;
            }
            var optype = this.GetQueryOrSimpleParam<string>("optype");
            if (optype.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("参数 optype 为空，请检查！");
            }

            var taskName = "收货扫描任务";
            switch (this.HtmlForm.Id)
            {
                case "bcm_countscantask":
                    taskName = "盘点扫描任务";
                    break;
                case "bcm_deliveryscantask":
                    taskName = "发货扫描任务";
                    break;
                case "bcm_transferintask":
                    taskName = "调入扫描任务";
                    break;
                case "bcm_transfertask":
                    taskName = "调出扫描任务";
                    break;
            }

            var savebills = new List<DynamicObject>();
            var iserror = false;
            var opname = "";
            foreach (var data in datas)
            {
                var ftaskstatus = Convert.ToString(data["ftaskstatus"]);
                var ftask_type = Convert.ToString(data["ftask_type"]);
                //if (this.HtmlForm.Id.Contains("bcm_receptionscantask") && !ftask_type.Equals("stk_postockin"))
                //{
                //    this.Result.ComplexMessage.ErrorMessages.Add($"目前只允许任务类型为【采购入库】的收货任务调整状态！");
                //    iserror = true;
                //    continue;
                //}
               
                if (optype == "complete")
                {
                    opname = "完成作业";
                    if (ftaskstatus== "ftaskstatus_04")
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"当前{taskName}【{Convert.ToString(data["fbillno"])}】已完成作业,无需手工标记已完成！");
                        iserror = true;
                        continue;
                    }
                    data["ftaskstatus"] = "ftaskstatus_04";
                    data["fdescription"] = $"用户【{this.Context.UserName}】手工标记已完成";
                    savebills.Add(data);
                }
                else
                {
                    opname = "继续作业";
                    if (Convert.ToString(data["ftaskstatus"]) != "ftaskstatus_04")
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"当前{taskName}【{Convert.ToString(data["fbillno"])}】未完成作业,无需手工标记继续作业！");
                        iserror = true;
                        continue;
                    }
                    var entitys = data["ftaskentity"] as DynamicObjectCollection;
                    var entity = entitys.Where(f => Convert.ToDecimal(f["fwaitworkqty"]) > 0).ToList();
                    if (entity == null || entity.Count <= 0)
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"当前{taskName}【{Convert.ToString(data["fbillno"])}】已没有待作业的商品,无需手工标记继续作业！");
                        iserror = true;
                        continue;
                    }
                    data["ftaskstatus"] = "ftaskstatus_03";
                    data["fdescription"] = "";
                    savebills.Add(data);
                }
                
            }
            if (iserror)
            {
                this.Result.IsSuccess = false;
                return;
            }

            if(savebills!=null && savebills.Count > 0)
            {
                this.Context.SaveBizData(this.HtmlForm.Id, savebills);
            }

            this.Result.IsSuccess = true;
            foreach(var bill in savebills)
            {
                this.Result.ComplexMessage.SuccessMessages.Add($"{taskName}【{Convert.ToString(bill["fbillno"])}】执行{opname}操作成功!");
            }
            
        }
    }
}
