using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.PackOrder
{
    /// <summary>
    /// 包装清单：反审核
    /// </summary>
    [InjectService]
    [FormId("bcm_packorder|bcm_packorderinit")]
    [OperationNo("UnAudit")]
    public class UnAudit: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }

            var metaModelService = this.Container.GetService<IMetaModelService>();
            var removeItems = new List<DynamicObject>();
            var barCodeMasterHtmlForm = metaModelService.LoadFormModel(this.Context, "bcm_barcodemaster");
            var barCodeMasterDM = this.Container.GetService<IDataManager>();
            barCodeMasterDM.InitDbContext(this.Context, barCodeMasterHtmlForm.GetDynamicObjectType(this.Context));
            var scanResultHtmlForm = metaModelService.LoadFormModel(this.Context, "bcm_scanresult");
            var scanResultDm = this.Container.GetService<IDataManager>();
            scanResultDm.InitDbContext(this.Context, scanResultHtmlForm.GetDynamicObjectType(this.Context));
            var scanResulWhere = " fmainorgid=@fmainorgid and fpackinterid=@fpackinterid ";
            var unitConvertService = this.Container.GetService<IUnitConvertService>();

            foreach (var dataEntity in e.DataEntitys)
            {
                var fbillno = Convert.ToString(dataEntity["fbillno"]);
                //获取源单，判断源单是否是已提交
                var fpackentities = dataEntity["fpackentity"] as DynamicObjectCollection;

                var groupDatas = fpackentities.Select(x => new
                {
                    fsourceformid = Convert.ToString(x["fsourceformid"]),
                    fsourceinterid = Convert.ToString(x["fsourceinterid"])
                }).GroupBy(x => x.fsourceformid).ToList();

                var sourceInfoCache = new Dictionary<HtmlForm, List<DynamicObject>>();
                foreach(var groupData in groupDatas)
                {
                    var htmlForm = metaModelService.LoadFormModel(this.Context, groupData.Key);
                    var ids = groupData.Select(x => x.fsourceinterid).Distinct().ToList();
                    var dm = this.Container.GetService<IDataManager>();
                    dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

                    var sourceEntities = dm.Select(ids).OfType<DynamicObject>().ToList();

                    foreach(var sourceEntity in sourceEntities)
                    {
                        if (!Convert.ToString(sourceEntity["FFormId"]).EqualsIgnoreCase("ydj_purchaseorder"))
                        {
                            if (Convert.ToString(sourceEntity["fstatus"]) != "D" && !Convert.ToString(sourceEntity["FFormId"]).EqualsIgnoreCase("bcm_receptionscantask"))
                            {
                                addRemoveItem(removeItems, dataEntity);
                                this.Result.ComplexMessage.ErrorMessages.Add($"编号[{fbillno}]包装清单关联的[{Convert.ToString(sourceEntity["fbillno"])}]的{htmlForm.Caption}不是已提交状态");
                            }
                        }
                        else
                        {
                            if (Convert.ToString(sourceEntity["fstatus"]) != "E")
                            {
                                addRemoveItem(removeItems, dataEntity);
                                this.Result.ComplexMessage.ErrorMessages.Add($"编号[{fbillno}]包装清单关联的[{Convert.ToString(sourceEntity["fbillno"])}]的{htmlForm.Caption}不是已审核状态");
                            }
                        }
                    }
                    sourceInfoCache.Add(htmlForm, sourceEntities);
                }
                //获取条形码，判断全是待入库状态
                List<DynamicObject> barCodeMasters = null;
                var fbarcodeentities = dataEntity["fbarcodeentity"] as DynamicObjectCollection;
                var barCodes = fbarcodeentities.Select(x => Convert.ToString(x["fbarcode"])).ToList();
                if (barCodes != null && barCodes.Count > 0)
                {
                    var barCodeReader = this.Context.GetPkIdDataReaderWithNumber(barCodeMasterHtmlForm, barCodes);
                    barCodeMasters = barCodeMasterDM.SelectBy(barCodeReader).OfType<DynamicObject>().ToList();
                    foreach (var barCodeMaster in barCodeMasters)
                    {
                        var fbizstatus = Convert.ToString(barCodeMaster["fbizstatus"]);
                        if (fbizstatus != "4" && fbizstatus != "3")
                        {
                            addRemoveItem(removeItems, dataEntity);
                            this.Result.ComplexMessage.ErrorMessages.Add($"编号[{fbillno}]包装清单关联的编号[{Convert.ToString(barCodeMaster["fnumber"])}]的{barCodeMasterHtmlForm.Caption}不是待入库状态");
                        }
                    }
                }

                if (removeItems.Contains(dataEntity))
                {
                    continue;
                }

                //先删除扫码记录，因为扫码记录引用条码主档
                var sqlParams = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company),
                    new SqlParam("@fpackinterid",System.Data.DbType.String,dataEntity["id"])
                };

                var scanResultReader = this.Context.GetPkIdDataReader(scanResultHtmlForm, scanResulWhere, sqlParams);
                var scanResults = scanResultDm.SelectBy(scanResultReader).OfType<DynamicObject>().ToList();

                if (scanResults != null && scanResults.Count > 0)
                {
                    if (invokeBillOperation(fbillno, scanResultHtmlForm, scanResults, "delete", "删除", removeItems, dataEntity, null) == false)
                    {
                        continue;
                    }
                }

                //删除条码主档
                if (barCodeMasters != null && barCodeMasters.Count > 0)
                {
                    if (invokeBillOperation(fbillno, barCodeMasterHtmlForm, barCodeMasters, "delete", "删除", removeItems, dataEntity, null) == false)
                    {
                        continue;
                    }
                    
                }

                //反写源单数量为0
                //foreach(var sourceInfo in sourceInfoCache)
                //{
                //    var htmlForm = sourceInfo.Key;
                //    var sourceEntities = sourceInfo.Value;
                //    foreach(var sourceEntity in sourceEntities)
                //    {
                //        var fentities = sourceEntity["fentity"] as DynamicObjectCollection;
                //        foreach(var fentity in fentities)
                //        {
                //            fentity["fqty"] = 0;
                //        }
                //    }
                //    var unitResult = unitConvertService.ConvertByBasQty(this.Context, htmlForm, sourceEntities, OperateOption.Create());
                //    if (checkInvokeResult(unitResult, fbillno, htmlForm, "单位换算", removeItems, dataEntity) == false)
                //    {
                //        continue;
                //    }
                //    if (invokeBillOperation(fbillno, htmlForm, sourceEntities, "save", "保存", removeItems, dataEntity, null) == false)
                //    {
                //        continue;
                //    }
                //}

                if (removeItems.Contains(dataEntity))
                {
                    continue;
                }

                //清除包装清单的条形码
                foreach(var fpackentity in fpackentities)
                {
                    fpackentity["fbarcode"] = string.Empty;
                }

                fbarcodeentities.Clear();
            }

            this.Result.IsSuccess = removeItems.Count < e.DataEntitys.Length;

            if (removeItems.Count > 0)
            {
                e.DataEntitys = e.DataEntitys.Where(x => removeItems.Contains(x) == false).ToArray();
            }
        }

        private bool invokeBillOperation(string fbillno, 
                                         HtmlForm form, 
                                         List<DynamicObject> dataEntities, 
                                         string opCode, 
                                         string opName, 
                                         List<DynamicObject> removeItems, 
                                         DynamicObject dataEntity, 
                                         Dictionary<string, object> option)
        {
            if (option == null)
            {
                option = new Dictionary<string, object>();
            }
            var invokeResult = this.Gateway.InvokeBillOperation(this.Context, form.Id, dataEntities, opCode, option);
            return checkInvokeResult(invokeResult, fbillno, form, opName, removeItems, dataEntity);
        }

        private bool checkInvokeResult(IOperationResult invokeResult, string fbillno, HtmlForm form, string opName, List<DynamicObject> removeItems, DynamicObject dataEntity)
        {
            if (invokeResult != null && invokeResult.IsSuccess == false && invokeResult.ComplexMessage.ErrorMessages.Count > 0)
            {
                this.Result.ComplexMessage.ErrorMessages.AddRange(invokeResult.ComplexMessage.ErrorMessages);
                this.Result.ComplexMessage.ErrorMessages.Add($"编号[{ fbillno}]包装清单关联的{form.Caption}{opName}失败!");
                addRemoveItem(removeItems, dataEntity);
                return false;
            }
            return true;
        }

        private void addRemoveItem(List<DynamicObject> removeItems, DynamicObject currentItem)
        {
            if (removeItems.Contains(currentItem) == false)
            {
                removeItems.Add(currentItem);
            }
        }
    }
}
