using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.PackOrder
{
    /// <summary>
    /// 包装清单：保存
    /// </summary>
    [InjectService]
    [FormId("bcm_packorder|bcm_packorderinit")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            //《包装清单》《初始包装清单》保存时，需增加【可包装数量】不允许小于0的校验。
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var entity = newData["fentity"] as DynamicObjectCollection;
                if (entity.Any(a => Convert.ToDecimal(a["fbizremainqty"]) < 0))
                {
                    return false;
                }
                return true;
            }).WithMessage("【可包装数量】不允许小于0的校验！"));


            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    var fpackentities = newData["fpackentity"] as DynamicObjectCollection;
            //    if (fpackentities == null || fpackentities.Count <= 0)
            //    {
            //        return true;
            //    }

            //    var groupDatas = fpackentities.Select(x => new
            //    {
            //        fsourceformid = Convert.ToString(x["fsourceformid"]),
            //        fsourceinterid = Convert.ToString(x["fsourceinterid"])
            //    }).GroupBy(x => x.fsourceformid).ToList();

            //    var metaModelSeriver = this.Container.GetService<IMetaModelService>();
            //    var sourceEntities = new List<DynamicObject>();
            //    foreach (var groupData in groupDatas)
            //    {
            //        var htmlForm = metaModelSeriver.LoadFormModel(this.Context, groupData.Key);
            //        var dm = this.Container.GetService<IDataManager>();
            //        dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

            //        sourceEntities.AddRange(dm.Select(groupData.Select(x => x.fsourceinterid).Distinct()).OfType<DynamicObject>());
            //    }

            //    var result = true;
            //    var i = 1;
            //    foreach (var fpackentity in fpackentities)
            //    {
            //        var sourceEntity = sourceEntities.FirstOrDefault(x => Convert.ToString(x["id"]) == Convert.ToString(fpackentity["fsourceinterid"]) &&
            //                                                              Convert.ToString(x["fformid"]) == Convert.ToString(fpackentity["fsourceformid"]));
            //        if (sourceEntity == null)
            //        {
            //            continue;
            //        }

            //        var fautobcinstock = Convert.ToBoolean(sourceEntity["fautobcinstock"]);
            //        if (fautobcinstock == false)
            //        {
            //            continue;
            //        }

            //        var fstorehouseid = Convert.ToString(fpackentity["fstorehouseid"]);
            //        var fstorelocationid = Convert.ToString(fpackentity["fstorelocationid"]);
            //        var fstockstatus = Convert.ToString(fpackentity["fstockstatus"]);

            //        if (string.IsNullOrWhiteSpace(fstorehouseid))
            //        {
            //            result = false;
            //            this.Result.ComplexMessage.ErrorMessages.Add($"第{i}行包装明细仓库不能为空!");
            //        }

            //        //if (string.IsNullOrWhiteSpace(fstorelocationid))
            //        //{
            //        //    result = false;
            //        //    this.Result.ComplexMessage.ErrorMessages.Add($"第{i}行包装明细仓位不能为空!");
            //        //}

            //        if (string.IsNullOrWhiteSpace(fstockstatus))
            //        {
            //            result = false;
            //            this.Result.ComplexMessage.ErrorMessages.Add($"第{i}行包装明细库存状态不能为空!");
            //        }

            //        i++;
            //    }

            //    return result;
            //}).WithMessage("包装明细的仓库、仓位或库存状态不满足条件!"));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
            {
                return;
            }
            var datas = e.DataEntitys;
            //对包装清单 打包类型为1 但是 包数/件数 不为1的做自动赋值。
            foreach (var data in datas)
            {
                var fpackEntrys = data["fentity"] as DynamicObjectCollection;
                foreach (var entry in fpackEntrys)
                {
                    var fpacktype = Convert.ToString(entry?["fpacktype"] ?? "");
                    if (fpacktype.EqualsIgnoreCase("1"))
                    {
                        entry["fpackcount"] = 1;
                    }
                }
            }
        }
    }
}
