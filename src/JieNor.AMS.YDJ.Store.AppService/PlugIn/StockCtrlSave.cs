using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin
{
    /// <summary>
    /// 通用保存插件：如果业务单据上有仓库字段，按可用仓库控制中的设置校验对应权限
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("save")]
    public class StockCtrlSave : AbstractOperationServicePlugIn
    {

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            if (this.HtmlForm.ElementType != HtmlElementType.HtmlForm_BillForm || this.HtmlForm.BillHeadTableName.IsNullOrEmptyOrWhiteSpace())
            {
                //只检查业务单据
                return;
            }

            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var fallowsalescontrol = profileService.GetSystemParameter(this.Context, "stk_stockparam", "fallowsalescontrol", false);
            if (!fallowsalescontrol)//如果没有配置【仓库可用控制】，则无需判断
            {
                return;
            }

            e.Rules.Insert(0, new StockCtrlValidation()); 
        }


    }

    /// <summary>
    /// 通用提交插件：如果业务单据上有仓库字段，按可用仓库控制中的设置校验对应权限
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("submit")]
    public class StockCtrlSubmit : StockCtrlSave
    {
    }

    /// <summary>
    /// 通用审批流提交插件：如果业务单据上有仓库字段，按可用仓库控制中的设置校验对应权限
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("SubmitFlow")]
    public class StockCtrlSubmitFlow : StockCtrlSave
    {
    }

    /// <summary>
    /// 通用撤销插件：如果业务单据上有仓库字段，按可用仓库控制中的设置校验对应权限
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("UnSubmit")]
    public class StockCtrlUnSubmit : StockCtrlSave
    {
    }

    /// <summary>
    /// 通用审核插件：如果业务单据上有仓库字段，按可用仓库控制中的设置校验对应权限
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("Audit")]
    public class StockCtrlAudit : StockCtrlSave
    {
    }

    /// <summary>
    /// 通用反审核插件：如果业务单据上有仓库字段，按可用仓库控制中的设置校验对应权限
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("UnAudit")]
    public class StockCtrlUnAudit : StockCtrlSave
    {
    }


    /// <summary>
    /// 如果业务单据上有仓库字段，按可用仓库控制中的设置校验对应权限
    /// </summary>
    public class StockCtrlValidation : AbstractBaseValidation
    {


        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public virtual string OperationDesc { get; private set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
            base.InitializeContext(userCtx, validationExpr);
        }


        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (dataEntities == null || dataEntities.Length == 0)
            {
                return result;
            }

            var stockFlds = formInfo.GetFieldList()
                .Where(f => f is HtmlBaseDataField && "ydj_storehouse".EqualsIgnoreCase(((HtmlBaseDataField)f).RefFormId));

            // Feat#4644 库存调拨单的可用仓库只需用于控制《库存调拨单.调拨明细》的【调出仓库】，无需考虑【调入仓库】。
            if (formInfo.Id.EqualsIgnoreCase("stk_inventorytransfer"))
            {
                stockFlds = stockFlds.Where(s => s.Id.EqualsIgnoreCase("fstorehouseid"));
            }

            if (stockFlds.IsNullOrEmpty())
            {
                //没有仓库字段的不检查
                return result;
            }

            var svc = userCtx.Container.GetService<ISalesControl>();
            var ctrlDatas = svc?.GetCanSalHousePerm(Context);
            var currCtrlStkDatas = ctrlDatas?.Where(f => f.Item3.SplitKey().Contains(formInfo.Id, StringComparer.OrdinalIgnoreCase))?.ToList(); //当前单据设置的可用仓控制信息
            if (currCtrlStkDatas == null || currCtrlStkDatas.Count == 0)
            {
                //如果当前业务没有设置可用仓库控制信息，则不做检查
                return result;
            }

            var bdProvide = userCtx.Container.GetService<IBaseFormProvider>();
            var fstaffid = bdProvide.GetMyStaff(userCtx)?.Id;
            fstaffid = fstaffid.IsNullOrEmptyOrWhiteSpace() ? "####" : fstaffid;//如果用户不关联员工，认为无权限

            userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, formInfo.GetDynamicObjectType(userCtx), dataEntities, false);

            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(userCtx, dataEntities, formInfo);

            var modifyFld = formInfo.GetField("fmodifierid");
            foreach (HtmlBaseDataField fld in stockFlds)
            {
                var matFld = formInfo.GetFieldList().FirstOrDefault(f => f is HtmlBaseDataField && f.EntityKey.EqualsIgnoreCase(fld.EntityKey)
                                                                   && "ydj_product".EqualsIgnoreCase((f as HtmlBaseDataField)?.RefFormId));
                var enRows = dataEntitySet.FindByEntityKey(fld.EntityKey);

                foreach (var item in enRows)
                {
                    var stockObj = item.DataEntity[fld.PropertyName + "_ref"] as DynamicObject;
                    if (stockObj == null)
                    {
                        //不填仓库的，不做控制
                        continue;
                    }

                    var houseid = stockObj["Id"].ToString();
                    var rootObj = item.DataEntity.GetRoot();

                    var snapShot = item.DataEntity.GetDataEntitySnapshot();
                    if (item.DataEntity.DataEntityState.FromDatabase && snapShot != null && snapShot.ContainsKey(fld.Id))
                    {
                        var fmodifierid = modifyFld?.GetFieldValue(userCtx, formInfo, rootObj)?.ToString();
                        var oldValue = Convert.ToString(snapShot[fld.Id].InitialValue);
                        if (houseid == oldValue && fmodifierid != userCtx.Id)
                        {
                            //之前别人保存的仓位并且没有修改的，不做检查？？？
                            //continue;
                        }
                    }

                    var hasperm = currCtrlStkDatas.Any(f => f.Item1.EqualsIgnoreCase(fstaffid) && f.Item2.EqualsIgnoreCase(houseid));
                    if (hasperm)
                    {
                        continue;
                    }

                    if (!fld.EntityKey.EqualsIgnoreCase ("fbillhead") && item.DataEntity.DynamicObjectType.Properties.ContainsKey("seq"))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"您没有第{0}行商品的仓库【{1}】的使用权限，请在【仓库可用控制】中设置相应的权限！".Fmt(item.DataEntity["seq"], stockObj["fname"]),
                            DataEntity = rootObj,
                        });
                    }
                    else
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = @"您没有仓库【{0}】的使用权限，请在【仓库可用控制】中设置相应的权限！".Fmt(stockObj["fname"]),
                            DataEntity = item.DataEntity.GetRoot(),
                        });
                    }
                }
                 
            }
            
            return result;
        }


    }
}