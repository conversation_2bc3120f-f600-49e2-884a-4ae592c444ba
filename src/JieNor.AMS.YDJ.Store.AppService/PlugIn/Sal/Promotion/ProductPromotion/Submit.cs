using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Promotion.ProductPromotion
{
    /// <summary>
    /// 促销活动：发布
    /// </summary>
    [InjectService]
    [FormId("ydj_productpromotion")]
    [OperationNo("Submit")]
    public class Submit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fruletype = Convert.ToString(newData["fruletype"]);
                if (fruletype.EqualsIgnoreCase("ruletype_02"))
                {
                    var productEntrys = newData["fproductentry"] as DynamicObjectCollection;
                    if (productEntrys.Any(s => Convert.ToDecimal(s["fprice"]) <= 0))
                    {
                        return false;
                    }
                }

                return true;
            }).WithMessage("特价商品销售价必须大于0。"));
        }
    }
}