using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface.QueryBuilder;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Promotion
{
    [InjectService]
    [FormId("rpt_promotion")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        protected IDBServiceEx DBServiceEx { get; set; }

        protected IListSqlBuilder ListSqlBuilder { get; set; }

        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {
            base.OnPrepareReportQueryParameter(listQueryPara);

            //if (this.Option.TryGetVariableValue<string>("OrderByString", out var orderByString))
            //{
            //    listQueryPara.OrderByString = orderByString;
            //}
            //if (this.Option.TryGetVariableValue<string>("FilterString", out var filterString))
            //{
            //    listQueryPara.AppendFilterString(filterString);
            //}
            //if (this.Option.TryGetVariableValue<int>("PageIndex", out var pageIndex))
            //{
            //    listQueryPara.PageIndex = pageIndex;
            //}
            //if (this.Option.TryGetVariableValue<int>("PageCount", out var pageCount))
            //{
            //    listQueryPara.PageCount = pageCount;
            //}
            //if (this.Option.TryGetVariableValue<string[]>("SelectFields", out var selectFields))
            //{
            //    foreach (var selectField in selectFields)
            //    {
            //        if (!listQueryPara.SelectedFieldKeys.Contains(selectField, StringComparer.OrdinalIgnoreCase))
            //        {
            //            listQueryPara.SelectedFieldKeys.Add(selectField);
            //        }
            //    }

            //}

        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.DBServiceEx = this.Container.GetService<IDBServiceEx>();
            //列表构建器
            this.ListSqlBuilder = this.Container.GetService<IListSqlBuilder>();

            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            foreach (var formId in _FormIds)
            {
                var formMeta = this.MetaModelService.LoadFormModel(this.Context, formId);

                InsertData(formMeta);
            }
        }


        static readonly List<string> _FormIds = new List<string> { "ydj_productpromotion", "ydj_combopromotion" };
        static readonly List<string> _SelectFieldKeys = new List<string> { "fnumber", "fname", "fforbidstatus", "fstartdate", "fenddate", "fdescription", "fdeptscope", "fpublishstatus" };

        private void InsertData(HtmlForm formMeta)
        {
            //参数对象
            var param = new SqlBuilderParameter(this.Context, formMeta.Id);
            param.ReadBillCount = false;
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.IsDistinct = true;
            param.OrderByString = "";

            param.SelectedFieldKeys.AddRange(_SelectFieldKeys);

            //设置数据隔离方案的过滤条件
            var accessFilter = this.ListSqlBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            var queryObject = QueryService.BuilQueryObject(param);
            queryObject.SqlOrderBy = "";

            string sql = $@"
insert into {this.DataSourceTableName}(fid, fnumber, fname, fforbidstatus, fstartdate, fenddate, fdescription, fdeptscope, fpublishstatus, fpromotionformid)
select fbillhead_id as fid, fnumber, fname, fforbidstatus, fstartdate, fenddate, fdescription, fdeptscope, fpublishstatus, '{formMeta.Id}' as fpromotionformid
from 
(
    {queryObject.SqlNoPage}
) t
";
            this.DBServiceEx.Execute(this.Context, sql, param.DynamicParams);
        }
    }
}
