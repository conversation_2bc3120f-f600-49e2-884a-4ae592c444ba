using System;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.Promotion
{
    /// <summary>
    /// 促销活动：撤销发布
    /// </summary>
    [InjectService]
    [FormId("ydj_productpromotion|ydj_combopromotion")]
    [OperationNo("UnPublish")]
    public class UnPublish : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            /*
                定义表头校验规则
            */

            string message = $"{this.HtmlForm.GetNumberField().Caption}为【{{0}}】的{this.HtmlForm.Caption} 不是 已发布，不允许{this.OperationName}！";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var fpublishstatus = Convert.ToString(newData["fpublishstatus"]);

                return fpublishstatus.EqualsIgnoreCase("1");
            }).WithMessage(message, (billObj, propObj) => propObj["fnumber"]));
        }

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var dataEntity in e.DataEntitys)
            {
                dataEntity["fpublishstatus"] = "0";
            }

            this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);
        }
    }
}