using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sal.ClosedAccounts
{
    /// <summary>
    /// 
    /// </summary>
    [InjectService]
    [FormId("ydj_closedaccountslist")]
    [OperationNo("QueryData")]
    public class QueryData : AbstractOperationServicePlugIn
    {

        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            base.OnCustomServiceEvent(e);

            switch (e.EventName)
            {
                case "prepareQueryBuilderParameter":
                    var sqlBuilderPara = e.EventData as SqlBuilderParameter;
                    sqlBuilderPara.OrderByString = "foptime desc ";
                    break;
                case "afterListData":
                    //afterListData(e);
                    break;
            }
        }


        public void afterListData(OnCustomServiceEventArgs e)
        {
            var listData = e.EventData as List<Dictionary<string, object>>;
            e.EventData = listData.OrderByDescending(a=>a["foptime"]).ToList();
        }
    }
}
