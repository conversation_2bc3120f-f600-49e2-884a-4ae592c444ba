using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.DealerSetup
{
    /// <summary>
    /// 资金账户设置：查看
    /// </summary>
    [InjectService]
    [FormId("sal_dealersetup")]
    [OperationNo("ViewParam")]
    public class ViewParam : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "afterCreateUIData":

                    var uiData = e.EventData as JObject;
                    if (uiData == null) uiData = new JObject();

                    var entry = uiData["fentry"] as JArray;
                    if (entry == null) entry = new JArray();

                    //所有账户辅助资料信息
                    var accountSynService = this.Container.GetService<ISynAccountBalanceService>();
                    var allAccountInfo = accountSynService.GetAllAccount(this.Context);

                    foreach (var accountInfo in allAccountInfo)
                    {
                        var existEntry = entry.FirstOrDefault(o => accountInfo.AccountId.EqualsIgnoreCase(Convert.ToString(o["fpurpose"]?["id"])));
                        if (existEntry == null)
                        {
                            //账户辅助资料
                            var joPurpose = new JObject();
                            joPurpose["id"] = accountInfo.AccountId;
                            joPurpose["fnumber"] = accountInfo.AccountName;
                            joPurpose["fname"] = accountInfo.AccountName;

                            var joEntry = new JObject();
                            joEntry["fpurpose"] = joPurpose;
                            joEntry["fisbalance"] = false;
                            joEntry["fispayment"] = true;
                            joEntry["fiscash"] = accountInfo.IsCash;
                            joEntry["fcredit"] = 0;
                            joEntry["fisrecharge"] = true;//accountInfo.AccountId == "settleaccount_type_01" || accountInfo.AccountId == "settleaccount_type_03";

                            entry.Add(joEntry);
                        }
                    }

                    //重新排序
                    var sortEntry = entry.OrderBy(o => o["fpurpose"]?["id"]).ToList();
                    var sortArray = new JArray();
                    for (int i = 0; i < sortEntry.Count; i++)
                    {
                        sortEntry[i]["FSeq"] = i + 1;
                        sortArray.Add(sortEntry[i]);
                    }
                    uiData["fentry"] = sortArray;
                    e.EventData = uiData;

                    break;
                default:
                    break;
            }
        }
    }
}