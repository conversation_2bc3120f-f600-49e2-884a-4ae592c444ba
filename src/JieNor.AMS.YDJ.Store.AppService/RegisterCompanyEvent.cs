using JieNor.AMS.YDJ.Store.AppService.Plugin.Auth;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Company;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Company;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static JieNor.AMS.YDJ.Store.AppService.MuSi.Api.MusiAuthService;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 企业注册成功事件
    /// </summary>
    [InjectService]
    public class RegisterCompanyEvent : IRegisterCompanyEvent
    {
        public void OnCompanyRegistered(UserContext userCtx, RegisterCompanyEventArgs e)
        {
            var orgInfo = CreateOrgInfo(userCtx, e.OpenCompanyInfo);

            //UpdateAgentAccountRole(userCtx, orgInfo);

            var orgType = orgInfo["forgtype"]?.ToString();
            if (orgType != "1")
            {
                return;
            }

            if (e.ChainRoleDataObject == null)
            {
                return;
            }

            //给总部管理员设置所有功能权限
            var chainRoleId = Convert.ToString(e.ChainRoleDataObject["id"]);

            //如果已经配置过权限，则不用再生成
            var permService = userCtx.Container.GetService<IPermissionService>();
            if (permService.IsRolePermissionSet(userCtx, chainRoleId) && !HostConfigView.Develope.DebugMode)
            {
                return;
            }

            DataAuthHelp.SetRoleDefaultAccess(userCtx, orgType, e.ChainRoleDataObject, e.ChainUserDataObject);
        }


        /// <summary>
        /// 创建组织信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="openCompanyInfo"></param>
        private DynamicObject CreateOrgInfo(UserContext userCtx, OpenCompanyInfo openCompanyInfo)
        {
            var orgInfo = userCtx.LoadBizDataById("bas_organization", openCompanyInfo.CompanyId);
            if (orgInfo == null)
            {
                var orgMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "bas_organization");
                var dyType = orgMeta.GetDynamicObjectType(userCtx);
                orgInfo = dyType.CreateInstance() as DynamicObject;
                orgInfo["fmainorgid"] = openCompanyInfo.CompanyId;
                orgInfo["Id"] = openCompanyInfo.CompanyId;//组织id等于fmainorgid
            }

            orgInfo["fnumber"] = openCompanyInfo.CompanyNumber;
            orgInfo["fname"] = openCompanyInfo.CompanyName;
            orgInfo["forgtype"] = openCompanyInfo.CompanyType.IsNullOrEmptyOrWhiteSpace() ? "1" : openCompanyInfo.CompanyType;

            orgInfo["fcontacter"] = openCompanyInfo.ContacterName;
            orgInfo["fcontacterphone"] = openCompanyInfo.ContacterPhone;

            orgInfo["fcorporatename"] = openCompanyInfo.CorporateName;

            orgInfo["fprovince"] = openCompanyInfo.Province;
            orgInfo["fcity"] = openCompanyInfo.City;
            orgInfo["fregion"] = openCompanyInfo.Region;
            orgInfo["fdetailaddress"] = openCompanyInfo.Address;
            orgInfo["fparentid"] = openCompanyInfo.ParentCompanyId;
            orgInfo["ftopcompanyid"] = openCompanyInfo.TopCompanyId;

            orgInfo["fdescription"] = "在进行企业注册时系统自动生成";

            //以管理员的身份进行操作
            var ctx = userCtx.CreateDBContextByCompanyId(openCompanyInfo.CompanyId);
            ctx.SaveBizData("bas_organization", orgInfo);

            //如果组织是经销商类型的，更新经销商所属组织

            return orgInfo;
        }

        /// <summary>
        /// 修改当前经销商生成的账号的角色，如果当前经销商勾选了大客户渠道，需要把这些账号的角色改成大客户渠道管理员
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agents"></param>
        private void UpdateAgentAccountRole(UserContext userCtx, DynamicObject org)
        {
            if (org.IsNullOrEmptyOrWhiteSpace()) { return; }

            var agent = userCtx.LoadBizDataByNo("bas_agent", "fnumber", new string[] { Convert.ToString(org["fnumber"]) }).FirstOrDefault();

            if (!agent.IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(agent["fcustomchannel"]))
            {
                var metaModelService = userCtx.Container.GetService<IMetaModelService>();
                var roleUserMeta = metaModelService.LoadFormModel(userCtx, "sec_roleuser");

                //大客户渠道管理员和经销商管理员信息
                var roles = userCtx.LoadBizDataByNo("sec_role", "fnumber", new string[] { "Admin_BigCustomer", "Admin_Agent" });

                var bigCustomerRole = roles.FirstOrDefault(p => Convert.ToString(p["fnumber"]) == "Admin_BigCustomer");

                var agentRole = roles.FirstOrDefault(p => Convert.ToString(p["fnumber"]) == "Admin_Agent");

                var username = Convert.ToString(agent["fcontacterphone"]);

                var user = userCtx.LoadBizDataByFilter("sec_user", $"fnumber = '{username}' and fmainorgid = '{org["Id"]}'").FirstOrDefault();

                if (user.IsNullOrEmptyOrWhiteSpace()) { return; }

                var roleuser = userCtx.LoadBizDataByFilter("sec_roleuser", $"froleid = '{agentRole["Id"]}'and fuserid = '{user["Id"]}'").FirstOrDefault();

                if (roleuser.IsNullOrEmptyOrWhiteSpace()) { return; }

                roleuser["froleid"] = bigCustomerRole["Id"];

                userCtx.SaveBizData("sec_roleuser", roleuser);
            }
        }
    }

}
