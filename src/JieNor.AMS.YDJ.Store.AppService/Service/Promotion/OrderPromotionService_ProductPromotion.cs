using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Service.Promotion
{
    public partial class OrderPromotionService
    {
        /// <summary>
        /// 使用商品促销
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        /// <param name="productPromotion"></param>
        /// <param name="gifts">赠品
        /// [{
        ///     "fmaterialid",
        ///     "fattrinfo",
        ///     "funitid",
        ///     "fqty"
        /// }]
        /// </param>
        public void UseProductPromotion(UserContext userCtx, DynamicObject order, DynamicObject productPromotion, JArray gifts)
        {
            if (productPromotion == null || gifts == null || gifts.Count == 0)
            {
                return;
            }
            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var productPromotionMeta = metaModelService.LoadFormModel(userCtx, "ydj_productpromotion");

            var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(userCtx, productPromotionMeta.GetDynamicObjectType(userCtx), productPromotion, false);

            var orderService = userCtx.Container.GetService<IOrderService>();
            var defaultValueCalculator = userCtx.Container.GetService<IDefaultValueCalculator>();

            var htmlForm = metaModelService.LoadFormModel(userCtx, "ydj_order");

            var orderEntryDt = htmlForm.GetEntity("fentry").DynamicObjectType;
            var orderEntrys = order["fentry"] as DynamicObjectCollection;


            var matchOrderEntyrs = PromotionUtil.GetMatchOrderEntrysByProductPromotion(userCtx, order, productPromotion);
            if (matchOrderEntyrs == null || matchOrderEntyrs.Count == 0)
            {
                throw new BusinessException("没有符合参与商品促销的商品！");
            }

            List<DynamicObject> newOrderEntrys = new List<DynamicObject>();

            var comboNo = Guid.NewGuid().ToString("N");

            foreach (var orderEntry in matchOrderEntyrs)
            {
                // 促销活动相关
                orderEntry["fpromotionid"] = productPromotion["id"];
                orderEntry["fpromotionrule"] = "商品满赠";
                orderEntry["fpromotioncombono"] = comboNo;
                orderEntry["fisusepromotion"] = true;
                orderEntry["fpromotiongiftamount"] = productPromotion["fgiftamount"];
                orderEntry["fpromotiongiftqty"] = productPromotion["fgiftqty"];
                orderEntry["fisgiftdiscount"] = productPromotion["fisgiftdiscount"];
            }

            var giftProducts = userCtx.LoadBizBillHeadDataById("ydj_product",
                gifts.Select(s => s.GetJsonValue<string>("fmaterialid")).Distinct().ToList(), "fsalunitid,fseriesid");

            var giftEntrys = productPromotion["fgiftentry"] as DynamicObjectCollection;

            foreach (var gift in gifts)
            {
                DynamicObject orderEntry = orderEntryDt.CreateInstance() as DynamicObject;
                newOrderEntrys.Add(orderEntry);

                string fmaterialid = gift.GetJsonValue<string>("fmaterialid");
                string fattrinfo = gift.GetJsonValue<string>("fattrinfo") ?? string.Empty;

                var giftProduct =
                    giftProducts.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(fmaterialid));

                var giftEntry = giftEntrys.FirstOrDefault(s =>
                    Convert.ToString(s["fmaterialid"]).EqualsIgnoreCase(fmaterialid)
                    && Convert.ToString(s["fattrinfo"]).Trim().EqualsIgnoreCase(fattrinfo)
                    );

                if (giftProduct == null || giftEntry == null)
                {
                    continue;
                }

                orderEntry["fproductid"] = fmaterialid;
                orderEntry["fattrinfo"] = fattrinfo;
                orderEntry["funitid"] = gift.GetJsonValue<string>("funitid");
                orderEntry["fbizunitid"] = giftProduct["fsalunitid"];
                orderEntry["fqty"] = gift.GetJsonValue<decimal>("fqty");
                orderEntry["funstockoutqty"] = orderEntry["fqty"];

                // 业绩品牌
                orderEntry["fresultbrandid"] = Convert.ToString(giftProduct["fseriesid"]);

                // 图片
                orderEntry["fmtrlimage"] = giftEntry["fmtrlimage"];

                // 促销活动相关
                orderEntry["fpromotionid"] = productPromotion["id"];
                orderEntry["fpromotionrule"] = "商品满赠";
                orderEntry["fpromotioncombono"] = comboNo;
                orderEntry["fisusepromotion"] = true;
                orderEntry["fpromotiongiftamount"] = productPromotion["fgiftamount"];
                orderEntry["fpromotiongiftqty"] = productPromotion["fgiftqty"];
                orderEntry["fisgiveaway"] = true;
                orderEntry["fisgiftdiscount"] = productPromotion["fisgiftdiscount"];
            }

            foreach (var orderEntry in newOrderEntrys)
            {
                orderEntry["id"] = "";
                orderEntrys.Add(orderEntry);
            }

            // 单位转换
            var unitConvertService = userCtx.Container.GetService<IUnitConvertService>();
            var option = OperateOption.Create();
            unitConvertService.ConvertByBasQty(userCtx, htmlForm, new[] { order }, option);

            LoadPrice(userCtx, order, newOrderEntrys);

            // 处理赠品折扣额和折率
            foreach (var orderEntry in newOrderEntrys)
            {
                var fisgiveaway = Convert.ToBoolean(orderEntry["fisgiveaway"]);
                if (fisgiveaway)
                {
                    orderEntry["fdistrate"] = 0;
                    orderEntry["fdistrateraw"] = 0;
                    orderEntry["fdistamount"] = orderEntry["famount"];
                }
            }

            orderService.ResetDis(userCtx, order);
            orderService.CalculateSettlement(userCtx, order, htmlForm);

            // 设置默认值
            defaultValueCalculator.Execute(userCtx, htmlForm, newOrderEntrys.ToArray(), "fentry");
        }

        /// <summary>
        /// 获取匹配的商品促销
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="order"></param>
        /// <param name="newDialogObj"></param>
        /// <returns></returns>
        public DynamicObject GetSelectProductPromotionDialog(UserContext userCtx, DynamicObject order,
            DynamicObject newDialogObj, bool throwError = false)
        {
            var orderEntrys = order["fentry"] as DynamicObjectCollection;
            // 判断商品促销是否符合条件 
            var matchPromotionOrderEntrys = PromotionUtil.GetMatchPromotionOrderEntrys(orderEntrys);
            if (matchPromotionOrderEntrys == null || matchPromotionOrderEntrys.Count == 0)
            {
                if (throwError)
                {
                    throw new BusinessException("没有符合参与商品促销的商品！");
                }
                return newDialogObj;
            }

            var date = order["forderdate"] == null ? DateTime.Now : Convert.ToDateTime(order["forderdate"]);
            var deptId = Convert.ToString(order["fdeptid"]);
            var productPromotions = PromotionUtil.GetMatchProductPromotions(userCtx, date, deptId);

            List<DynamicObject> matchProductPromotions = new List<DynamicObject>();
            foreach (var productPromotion in productPromotions)
            {
                var matchOrderEntrys = PromotionUtil.GetMatchOrderEntrysByProductPromotion(userCtx, order, productPromotion);
                if (matchOrderEntrys != null && matchOrderEntrys.Count > 0)
                {
                    matchProductPromotions.Add(productPromotion);
                }
            }

            // 加载引用数据
            var productPromotionMeta = userCtx.Container.GetService<IMetaModelService>()
                .LoadFormModel(userCtx, "ydj_productpromotion");
            var productPromotionDt = productPromotionMeta.GetDynamicObjectType(userCtx);
            userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, productPromotionDt, matchProductPromotions, true);

            InitData(newDialogObj, matchProductPromotions);

            return newDialogObj;
        }

        private void InitData(DynamicObject newDialogObj, List<DynamicObject> productPromotions)
        {
            var promotionEntrys = newDialogObj["fpromotionentry"] as DynamicObjectCollection;
            foreach (var productPromotion in productPromotions)
            {
                var promotionEntry =
                    promotionEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                promotionEntrys.Add(promotionEntry);

                promotionEntry["fpromotionid"] = productPromotion["id"];
                promotionEntry["fpromotionid_ref"] = productPromotion;
                promotionEntry["fruletype"] = productPromotion["fruletype"];
                promotionEntry["fdate"] = $"{productPromotion["fstartdate"]:yyyy-MM-dd}-{productPromotion["fenddate"]:yyyy-MM-dd}";
                promotionEntry["fdescription"] = productPromotion["fdescription"];
                promotionEntry["fgiftmultselect"] = productPromotion["fgiftmultselect"];
                promotionEntry["fgiftqtyedit"] = productPromotion["fgiftqtyedit"];
                promotionEntry["fisgiftdiscount"] = productPromotion["fisgiftdiscount"];


                var promotionGiftEntrys = productPromotion["fgiftentry"] as DynamicObjectCollection;
                var giftEntrys = promotionEntry["fgiftentry"] as DynamicObjectCollection;
                foreach (var promotionGiftEntry in promotionGiftEntrys)
                {
                    var giftEntry = giftEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    giftEntrys.Add(giftEntry);

                    giftEntry["fmaterialid"] = promotionGiftEntry["fmaterialid"];
                    giftEntry["fmaterialid_ref"] = promotionGiftEntry["fmaterialid_ref"];
                    giftEntry["fattrinfo"] = promotionGiftEntry["fattrinfo"];
                    giftEntry["fattrinfo_ref"] = promotionGiftEntry["fattrinfo_ref"];
                    giftEntry["funitid"] = promotionGiftEntry["funitid"];
                    giftEntry["funitid_ref"] = promotionGiftEntry["funitid_ref"];
                    giftEntry["fqty"] = promotionGiftEntry["fqty"];
                    giftEntry["fmtrlimage"] = promotionGiftEntry["fmtrlimage"];
                }
            }

        }
    }
}
