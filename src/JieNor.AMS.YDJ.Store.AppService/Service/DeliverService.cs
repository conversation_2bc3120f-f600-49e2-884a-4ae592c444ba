using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.Service
{
    /// <summary>
    /// 送达方服务实现
    /// </summary>
    [InjectService]
    public class DeliverService : IDeliverService
    {
        /// <summary>
        /// 更新品牌单据体
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="delivers"></param>
        public void UpdateBrandEntry(UserContext topCtx, IEnumerable<DynamicObject> delivers)
        {
            if (delivers.IsNullOrEmpty()) return;

            var dbService = topCtx.Container.GetService<IDBService>();

            var numbers = delivers.Select(s => Convert.ToString(s["fnumber"]));

            // 获取《送达方与系列》
            // 扩展获取父级系列（即业绩品牌）
            string sql = $@"
select ds.fid, ds.fdelivernumber, ds.fforbidstatus, ds.fmodifydate, s.fid as fserieid, s.fbrandid, s.fparentid as fparentserieid
from t_ms_deliver_series ds with(nolock) 
inner join t_ydj_series s with(nolock) on ds.fseriesnumber = s.fnumber
where ds.fdelivernumber in ('{string.Join("','", numbers)}')
";

            var deliverSerieses = dbService.ExecuteDynamicObject(topCtx, sql).ToList();

            deliverSerieses = DistinctDeliverSerieses(deliverSerieses);

            foreach (var deliver in delivers)
            {
                var entrys = deliver["fentry"] as DynamicObjectCollection;
                var number = Convert.ToString(deliver["fnumber"]);

                // 取送达方数据
                var matchSeries = deliverSerieses
                    .Where(s => Convert.ToString(s["fdelivernumber"]).EqualsIgnoreCase(number));

                foreach (var item in matchSeries)
                {
                    string parentSeriesId = Convert.ToString(item["fparentserieid"]);
                    string seriesId = Convert.ToString(item["fserieid"]);

                    // 如果存在父系列，使用父系列id
                    if (!parentSeriesId.IsNullOrEmptyOrWhiteSpace())
                    {
                        seriesId = parentSeriesId;
                    }

                    // 使用叠加
                    var entry = entrys.FirstOrDefault(s => Convert.ToString(s["fserieid"]).EqualsIgnoreCase(seriesId));
                    if (entry == null)
                    {
                        entry = entrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                        entry["fserieid"] = seriesId;
                        entrys.Add(entry);
                    }

                    string fforbidstatus = Convert.ToString(item["fforbidstatus"]);
                    bool forbidStatus = fforbidstatus.EqualsIgnoreCase("1") || fforbidstatus.EqualsIgnoreCase("true");

                    // 禁用就删除此行
                    if (forbidStatus)
                    {
                        entrys.Remove(entry);
                    }
                    // 非禁用则勾选
                    else
                    {
                        entry["fenable"] = true;
                    }
                }
            }

            AddDefaultBrandInfo(topCtx, delivers);

            //var deliverMeta = topCtx.Container.GetService<IMetaModelService>().LoadFormModel(topCtx, "bas_deliver");
            //var deliverDt = deliverMeta.GetDynamicObjectType(topCtx);

            //topCtx.Container.GetService<IPrepareSaveDataService>()
            //    .PrepareDataEntity(topCtx, deliverMeta, delivers.ToArray(), OperateOption.Create());

            //var dm = topCtx.Container.GetService<IDataManager>();
            //dm.InitDbContext(topCtx, deliverDt);
            //dm.Save(delivers);


        }

        /// <summary>
        /// 过滤重复的《送达方与系列》
        /// </summary>
        /// <param name="deliverSerieses"></param>
        /// <returns></returns>
        private List<DynamicObject> DistinctDeliverSerieses(List<DynamicObject> deliverSerieses)
        {
            var distinctDeliverSerieses = new List<DynamicObject>();
            var keys = new HashSet<string>();

            // 送达方+系列作主键，按修改时间+id倒序，取第一项
            foreach (var item in deliverSerieses
                .OrderByDescending(s => Convert.ToDateTime(s["fmodifydate"]))
                .ThenByDescending(s => Convert.ToString(s["fid"]))
            )
            {
                string key = $"{item["fdelivernumber"]}_{item["fserieid"]}";

                if (keys.Add(key))
                {
                    distinctDeliverSerieses.Add(item);
                }
            }

            return distinctDeliverSerieses;
        }

        /// <summary>
        /// 添加默认授权行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        private void AddDefaultBrandInfo(UserContext userCtx, IEnumerable<DynamicObject> delivers)
        {
            if (delivers.IsNullOrEmpty()) return;

            var dbService = userCtx.Container.GetService<IDBService>();

            // 增加 2行授权, 一行为【系列】="Z1" (通配品牌), 另一行为【品牌】="R"(慕思助眠)对应行【系列】="M1" (慕思助眠)

            string sql = $@"
select b.fid as fbrandid, b.fnumber as fbrandnumber, b.fname as fbrandname, s.fid as fserieid, s.fnumber as fseriesnumber, s.fname as fseriesname 
from t_ydj_series s with(nolock) 
left join t_ydj_brand b with(nolock) on s.fbrandid=b.fid 
where s.fnumber in ('Z1', 'M1') and s.fforbidstatus='0';
";

            var seriesZ1AndM1 = dbService.ExecuteDynamicObject(userCtx, sql);
            //送达方-品牌
            var brandids = delivers.Select(s => Convert.ToString(s["fbrandid"])); 
            var brands = userCtx.LoadBizDataById("ydj_brand", brandids);

            foreach (var deliver in delivers)
            {
                string fbrandid = Convert.ToString(deliver["fbrandid"]);
                //根据品牌的配置 判断 送达方明细 是否需要默认配置 通配、助眠
                var brandobj = brands.FirstOrDefault(o=>Convert.ToString(o["id"]).EqualsIgnoreCase(fbrandid));
                if (brandobj.IsNullOrEmptyOrWhiteSpace()) continue;

                var fauto_M1 = Convert.ToBoolean(brandobj["fauto_M1"]);
                var fauto_Z1 = Convert.ToBoolean(brandobj["fauto_Z1"]);

                // 授权品牌/系列
                var entrys = (DynamicObjectCollection)deliver["fentry"];

                //如果送达方禁用则清空所有品牌、系列。
                string deliver_fforbidstatus = Convert.ToString(deliver["fforbidstatus"]);
                if (deliver_fforbidstatus.EqualsIgnoreCase("1") || deliver_fforbidstatus.EqualsIgnoreCase("true"))
                {
                    entrys.Clear();
                    continue;
                } 

                // 判断【系列】="Z1" (通配品牌)是否存在
                var z1 = seriesZ1AndM1.FirstOrDefault(s =>
                    Convert.ToString(s["fseriesnumber"]).EqualsIgnoreCase("Z1") &&
                    Convert.ToString(s["fbrandid"]).IsNullOrEmptyOrWhiteSpace());
                if (z1 != null)
                {
                    // 判断【系列】="Z1" 的行是否存在
                    var entry = entrys.FirstOrDefault(s =>
                        Convert.ToString(s["fserieid"]).EqualsIgnoreCase(Convert.ToString(z1["fserieid"])));
                    if (entry == null)
                    {
                        if (fauto_Z1) {
                            //如果勾选了才预置 通配
                            entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                            entry["fserieid"] = z1["fserieid"];
                            entry["fenable"] = true;
                            entrys.Add(entry);
                        }
                    }
                    //暂时先注释清除逻辑，客户需要上线后观察品牌下发情况
                    else
                    {
                        if (!fauto_Z1) {
                            //如果未勾选则需要清除
                            entrys.Remove(entry);
                        }
                    }
                }

                // 判断【品牌】="R"(慕思助眠)对应行【系列】="M1" (慕思助眠)是否存在
                var m1 = seriesZ1AndM1.FirstOrDefault(s =>
                    Convert.ToString(s["fseriesnumber"]).EqualsIgnoreCase("M1") &&
                    Convert.ToString(s["fbrandnumber"]).EqualsIgnoreCase("R"));
                if (m1 != null)
                {
                    // 判断【系列】="M1" 的行是否存在
                    var entry = entrys.FirstOrDefault(s => Convert.ToString(s["fserieid"]).EqualsIgnoreCase(Convert.ToString(m1["fserieid"])));
                    // 不存在，则新增行
                    if (entry == null)
                    {
                        if (fauto_M1) 
                        {
                            //如果勾选了才预置 助眠
                            entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                            entry["fserieid"] = m1["fserieid"];
                            entry["fenable"] = true;
                            entrys.Add(entry);
                        }
                    }
                    //暂时先注释清除逻辑，客户需要上线后观察品牌下发情况
                    else
                    {
                        if (!fauto_M1) 
                        {
                            //如果未勾选则需要清除
                            entrys.Remove(entry);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 反写经销商城市
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        public void RewriteCity(UserContext userCtx, IEnumerable<DynamicObject> delivers)
        {
            if (delivers.IsNullOrEmpty()) return;

            var agentIds = delivers.Select(s => Convert.ToString(s["fagentid"])).Distinct();
            if (agentIds.IsNullOrEmpty()) return;

            var agents = userCtx.LoadBizDataById("bas_agent", agentIds);
            if (agents.IsNullOrEmpty()) return;

            var allCityIds = new List<string>();

            var allDelivers = userCtx.LoadBizDataByNo("bas_deliver", "fagentid", agentIds);
            allDelivers.AddRange(delivers);

            // BUG#28006：不排除禁用的送达方
            //var allNotForbidStatusDelivers = allDelivers.Where(s => !Convert.ToBoolean(s["fforbidstatus"]));

            foreach (var agent in agents)
            {
                var agentId = Convert.ToString(agent["id"]);

                var ds = allDelivers.Where(s => Convert.ToString(s["fagentid"]).EqualsIgnoreCase(agentId));
                var deliverCityIds = ds.Select(s => Convert.ToString(s["fcity"])).Distinct();

                agent["fcity"] = string.Join(",", deliverCityIds);

                allCityIds.AddRange(deliverCityIds);
            }

            // 处理【城市】多选基础资料的文本
            var allCities = userCtx.LoadBizDataById("ydj_city", allCityIds.Distinct());
            foreach (var agent in agents)
            {
                var cityIds = Convert.ToString(agent["fcity"])
                    .Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries).ToList();
                List<string> cityNames = new List<string>();
                foreach (var cityId in cityIds)
                {
                    cityNames.Add(Convert.ToString(allCities.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(cityId))?["fname"]));
                }

                agent["fcity_txt"] = string.Join(",", cityNames);
            }

            var agentMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "bas_agent");
            var agentDt = agentMeta.GetDynamicObjectType(userCtx);

            //var dm = userCtx.Container.GetService<IDataManager>();
            //dm.InitDbContext(userCtx, agentDt);
            //dm.Save(agents);
            //统一改成标准保存
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.InvokeBillOperation(userCtx, "bas_agent", agents, "save", new Dictionary<string, object>());
            result.ThrowIfHasError(true, "城市保存失败！");
        }

        /// <summary>
        /// 更新销售组织
        /// 注：需要自行保存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        public void UpdateSalOrg(UserContext userCtx, IEnumerable<DynamicObject> delivers)
        {
            if (delivers.IsNullOrEmpty()) return;

            List<string> fserieids = new List<string>();

            foreach (var deliver in delivers)
            {
                var entrys = deliver["fentry"] as DynamicObjectCollection;
                fserieids.AddRange(entrys.Select(s => Convert.ToString(s["fserieid"])));
            }
            if (fserieids.IsNullOrEmpty()) return;

            // 由于《送达方与系列》里的【系列】是子系列（非业绩品牌），需要关联查询【系列】的【上级系列】（业绩品牌）
            var seriesSql = $@"
select be.forganizationid, s.fid as fseriesid from t_ydj_orgresultbrand b with(nolock)
inner join t_ydj_orgresultbrandentry be with(nolock) on b.fid=be.fid
inner join t_ydj_series s with(nolock) on be.fseriesid=s.fparentid
where b.fmainorgid='{userCtx.TopCompanyId}' and b.fforbidstatus='0' and s.fid in ('{string.Join("','", fserieids.Distinct())}')";

            // 兼容《送达方》【品牌信息】里的【系列】就是业绩品牌的情况
            var resultBrandSql = $@"
select be.forganizationid, be.fseriesid from t_ydj_orgresultbrand b with(nolock)
inner join t_ydj_orgresultbrandentry be with(nolock) on b.fid=be.fid
where b.fmainorgid='{userCtx.TopCompanyId}' and b.fforbidstatus='0' and be.fseriesid in ('{string.Join("','", fserieids.Distinct())}')";

            var sql = seriesSql + " union all " + resultBrandSql;

            var dynObjs = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql);
            if (dynObjs.IsNullOrEmpty()) return;

            // 默认系列：通配、助眠
            string defaultSeriesSql =
                $"select fid as id from t_ydj_series where fnumber in ('Z1', 'M1') and fmainorgid='{userCtx.Company}'";
            var defaultSerieIds = userCtx.ExecuteDynamicObject(defaultSeriesSql, new List<SqlParam>())
                .Select(s => Convert.ToString(s["id"]));

            foreach (var deliver in delivers)
            {
                // 将通配、助眠的放到后面匹配
                var entrys = ((DynamicObjectCollection)deliver["fentry"])
                    .OrderBy(s => defaultSerieIds.Contains(Convert.ToString(s["fserieid"])));
                foreach (var entry in entrys)
                {
                    var serieid = Convert.ToString(entry["fserieid"]);
                    if (serieid.IsNullOrEmptyOrWhiteSpace()) continue;

                    // 取第一个符合条件的组织
                    var dynObj =
                        dynObjs.FirstOrDefault(s => Convert.ToString(s["fseriesid"]).EqualsIgnoreCase(serieid));
                    if (dynObj != null)
                    {
                        deliver["fsaleorgid"] = dynObj["forganizationid"];
                        break;
                    }
                }
            }
        }
        /// <summary>
        /// 新逻辑：通过《客户销售组织与渠道关系》更新销售组织
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        public void UpdateSalOrgBySales(UserContext userCtx, IEnumerable<DynamicObject> delivers) 
        {
            if (delivers.IsNullOrEmpty()) return;

            var numbers = delivers.Select(s => Convert.ToString(s["fnumber"])).ToList();
            //需自动从《客户销售组织与渠道关系》表取同一【客户编码】且【状态】=“启用中”且【更新时间】最新的那一条数据的【销售组织编码】。
            var sql = $@"
            select distinct org.fid,org.fnumber,s.fmodifydate,s.fcustomernumber AS delivernumber from t_ms_saleschannel_org s
            inner join dbo.t_bas_organization org on org.fnumber = s.forgnumber and org.forgtype in ('1','2') and org.fforbidstatus = 0
            where s.fcustomernumber in ({numbers.JoinEx(",", true)}) and s.fforbidstatus ='0' and (fchannelnumber ='20' OR fchannelnumber ='10' OR fchannelnumber ='50') ";

            var dbService = userCtx.Container.GetService<IDBService>();
            var objs = dbService.ExecuteDynamicObject(userCtx, sql);

            foreach (var deliver in delivers)
            {
                var number = deliver["fnumber"].ToString();

                var obj = objs.Where(s => Convert.ToString(s["delivernumber"]).EqualsIgnoreCase(number)).OrderByDescending(o=>Convert.ToString(o["fmodifydate"])).FirstOrDefault();

                deliver["fsaleorgid"] = Convert.ToString(obj?["fid"]);
            }

        }

        /// <summary>
        /// 生成商品授权清单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        public void AddOrUpdateProductAuth(UserContext userCtx, IEnumerable<DynamicObject> delivers, bool IncludNoAudit = false, OperateOption option = null)
        {
            //if(delivers.IsNullOrEmpty()) return;
            ResetSysAdminUserContext(userCtx);

            var lstValidDelivers = delivers?.Where(s =>
                !Convert.ToString(s["fagentid"]).IsNullOrEmptyOrWhiteSpace() &&
                !Convert.ToString(s["fcity"]).IsNullOrEmptyOrWhiteSpace())?.ToList();

            if (lstValidDelivers.IsNullOrEmpty()) return;

            /*
             * 当每次生成或更新了《送达方》的表体-品牌信息时, 系统要自动生成或更新《商品授权清单》, 按照《送达方》表头的【经销商】+表头的【城市】来生成, 如果已存在时则进行更新覆盖, 并且覆盖时只会针对”单据体-按【品牌/系列】授权”的数据进行增减(因为一个经销商会有多个送达方, 所以每次都是根据所有对应的送达方来全部替换数据), 不能影响到其他的单据体(如: 单据体-按【商品】授权 或 单据体-例外商品)
             * 单据头-基本信息-编码                  = 根据【授予组织】的编码+【城市】的编码
             * 单据头-基本信息-名称                  = 根据【授予组织】的名称+”-”+【城市】的名称
             * 单据头-基本信息-授予组织              = 根据《送达方》表头的【经销商】对应的《组织》
             * 单据头-基本信息-授予组织所属城市      = 根据《送达方》表头的【城市】
             * 单据头-基本信息-授予组织所属上级      = 默认"慕思健康睡眠股份有限公司"
             * 单据头-基本信息-授予组织类型          = 默认"经销商"
             * 单据体-按【品牌/系列】授权-品牌名称   = 根据《送达方》表体-品牌信息的【品牌】
             * 单据体-按【品牌/系列】授权-系列       = 根据《送达方》表体-品牌信息的【系列】
             */

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var productAuthMeta = metaModelService.LoadFormModel(userCtx, "ydj_productauth");
            var productAuthDt = productAuthMeta.GetDynamicObjectType(userCtx);

            var agentIds = lstValidDelivers.Select(s => s["fagentid"]?.ToString()).Distinct().ToList();
            var cityIds = lstValidDelivers.Select(s => s["fcity"]?.ToString()).Distinct().ToList();

            // 只有已审核，非禁用的经销商才生成
            var agents = userCtx.LoadBizBillHeadDataById("bas_agent", agentIds, "fstatus,fforbidstatus");
            if (IncludNoAudit)
            {
                agentIds = agents
                    .Where(s => Convert.ToString(s["fforbidstatus"]).EqualsIgnoreCase("0")
                                || Convert.ToString(s["fforbidstatus"]).EqualsIgnoreCase("false"))
                    .Select(s => Convert.ToString(s["id"]))
                    .ToList();
            }
            else
            {
                agentIds = agents
                    .Where(s => Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E"))
                    .Where(s => Convert.ToString(s["fforbidstatus"]).EqualsIgnoreCase("0")
                                || Convert.ToString(s["fforbidstatus"]).EqualsIgnoreCase("false"))
                    .Select(s => Convert.ToString(s["id"]))
                    .ToList();
            }
            // 【经销商】对应的【组织】id相同
            var orgs = userCtx.LoadBizDataById("bas_organization", agentIds);
            var cities = userCtx.LoadBizDataById("ydj_city", cityIds);

            // 获取已有的《商品授权清单》：按照《送达方》表头的【经销商】+表头的【城市】来匹配
            List<string> productAuthSqls = new List<string>();
            foreach (var item in lstValidDelivers)
            {
                string fagentid = Convert.ToString(item["fagentid"]);
                string fcity = Convert.ToString(item["fcity"]);

                //需要调整：如果经销商状态被更新停用，商品授权清单被禁用时，再次拉取送达方与系列 不需要再生成新的商品授权清单了。
                //string productAuthSql = $@" select fid from t_ydj_productauth where forgid='{fagentid}' and fcityid='{fcity}' and fforbidstatus ='0' ";
                string productAuthSql = $@"select auth.fid  from t_ydj_productauth as auth with (nolock)
                                            inner join t_bas_agent as ag with (nolock) on ag.fid = auth.forgid
                                            where auth.forgid='{fagentid}' and auth.fcityid='{fcity}'  
                                            and ((auth.fforbidstatus ='0' and ag.fagentstatus='1') or (auth.fforbidstatus ='1' and ag.fagentstatus='0')) "; 

                productAuthSqls.Add(productAuthSql);
            }

            var productAuthFullSql = productAuthSqls.JoinEx(" union all ", false);
            var reader = userCtx.ExecuteReader(productAuthFullSql, new List<SqlParam>());
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, productAuthDt);

            var productAuths = dm.SelectBy(reader).OfType<DynamicObject>().ToList();

            // 需要保存的《商品授权清单》
            List<DynamicObject> savingProductAuths = new List<DynamicObject>();

            // 按【经销商】+【城市】分组
            var groups = lstValidDelivers.GroupBy(s => $"{s["fagentid"]}_{s["fcity"]}");

            // 获取相同【经销商】+相同【城市】的《送达方》的【品牌信息】
            List<string> deliverBrandInfoSqls = new List<string>();
            foreach (var group in groups)
            {
                string fagentid = Convert.ToString(group.First()["fagentid"]);
                string fcity = Convert.ToString(group.First()["fcity"]);

                string deliverBrandInfoSql = $@"
select distinct d.fagentid, d.fcity, de.fserieid, s.fbrandid, s.fname as fseriename 
from t_bas_deliver d with(nolock) 
inner join t_bas_deliverentry de with(nolock) on d.fid=de.fid
inner join t_ydj_series s with(nolock) on s.fid=de.fserieid
where d.fforbidstatus='0' and d.fagentid='{fagentid}' and d.fcity='{fcity}' and de.fenable='1'
";
                deliverBrandInfoSqls.Add(deliverBrandInfoSql);
            }

            var deliverBrandInfoFullSql = deliverBrandInfoSqls.JoinEx(" union all ", false);

            // 相同【经销商】+【城市】的【品牌信息】
            var agentCitySeries = userCtx.Container.GetService<IDBService>()
                .ExecuteDynamicObject(userCtx, deliverBrandInfoFullSql);

            foreach (var group in groups)
            {
                string orgId = Convert.ToString(group.First()["fagentid"]);
                string cityId = Convert.ToString(group.First()["fcity"]);

                var org = orgs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(orgId));
                var city = cities.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(cityId));

                if (org == null || city == null) continue;

                var number = $"{org["fnumber"]}{city["fnumber"]}";

                // 针对所有匹配的商品授权清单处理
                var match = productAuths.Where(s =>
                         Convert.ToString(s["forgid"]).EqualsIgnoreCase(orgId)
                         && Convert.ToString(s["fcityid"]).EqualsIgnoreCase(cityId)
                        ).ToList();
                if (!match.Any())
                {
                    var productAuth = (DynamicObject)productAuthDt.CreateInstance();
                    productAuth["fnumber"] = number;                            // 根据【授予组织】的编码+【城市】的编码
                    productAuth["fname"] = $"{org["fname"]}-{city["fname"]}";   // 根据【授予组织】的名称+”-”+【城市】的名称
                    productAuth["forgid"] = orgId;                              // 根据《送达方》表头的【经销商】对应的《组织》
                                                                                //http://dmp.jienor.com:81/zentao/bug-view-23566.html 商品授权清单中组织类型改成跟组织中组织类型字段类型同步，即在保存时需要处理字段逻辑

                    productAuth["forgtype"] = org["forgtype"];                  // 
                    productAuth["fcityid"] = cityId;                            // 根据《送达方》表头的【城市】

                    match.Add(productAuth);
                }

                var matchSeries = agentCitySeries
                    .Where(s => Convert.ToString(s["fagentid"]).EqualsIgnoreCase(orgId) &&
                                Convert.ToString(s["fcity"]).EqualsIgnoreCase(cityId));

                foreach (var productAuth in match)
                {
                    // 授权品牌/系列
                    var entrys = (DynamicObjectCollection)productAuth["fproductauthbs"];
                    entrys.Clear();

                    foreach (var item in matchSeries.GroupBy(s => Convert.ToString(s["fbrandid"])))
                    {
                        var fbrandid = item.Key;

                        // 过滤为空的情况
                        var serieIds = item.Select(s => Convert.ToString(s["fserieid"])).ToList();
                        var serieNames = item.Select(s => Convert.ToString(s["fseriename"])).ToList();

                        // 过滤系列id为空的情况
                        for (int i = serieIds.Count - 1; i >= 0; i--)
                        {
                            var id = serieIds[i];
                            if (id.IsNullOrEmptyOrWhiteSpace())
                            {
                                serieIds.RemoveAt(i);
                                serieNames.RemoveAt(i);
                            }
                        }

                        var fserieid = string.Join(",", serieIds);
                        var fserieid_txt = string.Join(",", serieNames);

                        var entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                        entry["fbrandid"] = fbrandid;       // 根据《送达方》表体-品牌信息的【品牌】
                        entry["fserieid"] = fserieid;       // 根据《送达方》表体-品牌信息的【系列】
                        entry["fserieid_txt"] = fserieid_txt;
                        entrys.Add(entry);
                    }
                }

                savingProductAuths.AddRange(match);
            }

            if (savingProductAuths.Any())
            {

                var productAuthService = userCtx.Container.GetService<IProductAuthService>();
                //送达方同步商品授权清单不需要再添加通配、助眠；和送达方的系列保持一致。送达方排除了通配则商品授权清单也要排除
                //productAuthService.AddDefault(userCtx, savingProductAuths);

                var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                //如果商品授权清单保存失败，不要影响后续的接口拉取逻辑。
                try
                {
                    var result = gateway.InvokeBillOperation(userCtx, "ydj_productauth", savingProductAuths, "save",
                                                              option?.ToDictionary(s => s.Key, s => s.Value));
                    //result.ThrowIfHasError(true, "商品授权清单保存失败！");
                }
                catch (Exception e)
                {

                }
                AddOrUpdateAgentBrandSeriesData(userCtx, savingProductAuths);
                //var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                //prepareService.PrepareDataEntity(userCtx, productAuthMeta, savingProductAuths.ToArray(), OperateOption.Create()); 
                //dm.Save(savingProductAuths);
            }
        }

        public void AddOrUpdateAgentBrandSeriesData(UserContext userCtx,List<DynamicObject> savingProductAuths) 
        {
            List<DynamicObject> savingAgentBrands = new List<DynamicObject>();

            var metaModelService = userCtx.Container.GetService<IMetaModelService>();
            var AgentBrandMeta = metaModelService.LoadFormModel(userCtx, "ydj_agentbrandseries");
            var AgentBrandDt = AgentBrandMeta.GetDynamicObjectType(userCtx);

            var agentids = savingProductAuths.Select(o => Convert.ToString(o["forgid"])).ToList();
            var AgentBrandDatas = userCtx.LoadBizDataByNo("ydj_agentbrandseries", "fagentid", agentids);

            // 针对所有匹配的商品授权清单处理 
            var groups = savingProductAuths.GroupBy(o => Convert.ToString(o["forgid"])).ToList();
            if (groups.Any())
            {
                foreach (var group in groups) 
                {
                    //取商品授权清单授权品牌系列
                    var ProductAuthsentrys = group.SelectMany(o => o["fproductauthbs"] as DynamicObjectCollection);

                    string orgId = Convert.ToString(group.First()["forgid"]);
                    var match = AgentBrandDatas.Where(o => Convert.ToString(o["fagentid"]).EqualsIgnoreCase(orgId)).ToList();
                    if (!match.Any()) 
                    {
                        var AgentBrand = (DynamicObject)AgentBrandDt.CreateInstance();
                        AgentBrand["fagentid"] = orgId;

                        match.Add(AgentBrand);
                    }

                    foreach (var AgentBrand in match)
                    {
                        // 授权品牌/系列
                        var entrys = (DynamicObjectCollection)AgentBrand["ydj_agentbrandseriesentry"];
                        //entrys.Clear();

                        foreach (var ProductAuthsentry in ProductAuthsentrys) 
                        {
                            var brandid = Convert.ToString(ProductAuthsentry["fbrandid"]);
                            var seriesids = Convert.ToString(ProductAuthsentry["fserieid"]);
                            var seriesLst = seriesids.SplitKey(",");
                            foreach (var seriesid in seriesLst) 
                            {
                                //去重
                                if (entrys.Any(o => Convert.ToString(o["fseriesid"]).EqualsIgnoreCase(seriesid))) continue;

                                var entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                                entry["fbrandid"] = brandid;
                                entry["fseriesid"] = seriesid;
                                entry["fmodifydate"] = DateTime.Now;
                                entrys.Add(entry);
                            }
                        } 
                    }

                    savingAgentBrands.AddRange(match);
                }

                if (savingAgentBrands.Any())
                {
                    //var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
                    //prepareService.PrepareDataEntity(userCtx, AgentBrandMeta, savingAgentBrands.ToArray(), OperateOption.Create());

                    //var dm = userCtx.Container.GetService<IDataManager>();
                    //dm.InitDbContext(userCtx, AgentBrandDt);
                    //dm.Save(savingAgentBrands);
                    var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                    var result = gateway.InvokeBillOperation(userCtx, "ydj_agentbrandseries", savingAgentBrands, "draft",null);
                }
            }
        }

        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext(UserContext context)
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = context.Product;
            session.Company = context.Company;
            session.BizOrgId = context.Company;
            session.TopCompanyId = context.TopCompanyId;
            session.ParentCompanyId = context.ParentCompanyId;
            session.Companys = context.Companys.ToList();
            session.Id = context.Id;

            context.SetUserSession(session);
        }

        /// <summary>
        /// 更新【单据头.招商经销商】
        /// 注：需要自行保存
        /// </summary>
        /// <param name="topCtx"></param>
        /// <param name="delivers"></param>
        public void UpdateCrmDistributor(UserContext topCtx, IEnumerable<DynamicObject> delivers)
        {
            if (delivers.IsNullOrEmpty()) return;

            // 在《送达方》接口的【招商经销商】字段是通过 接口清单的 26、售达方与系列  (送达方与系列) 里面根据对应的售达方获取到经销商编号, 带出对应基础资料的【招商经销商】

            var numbers = delivers.Select(s => Convert.ToString(s["fnumber"])).ToList();

            // 排除禁用状态的
            var sql = $@"
select distinct ds.fdelivernumber, d.fid
from t_ms_crmdistributor d with(nolock)
inner join t_ms_deliver_series ds with(nolock) on d.fid=ds.fdistributorid
where d.fforbidstatus='0' and ds.fdelivernumber in ({numbers.JoinEx(",", true)})";

            var dbService = topCtx.Container.GetService<IDBService>();
            var crmDistributors = dbService.ExecuteDynamicObject(topCtx, sql);

            foreach (var deliver in delivers)
            {
                var number = deliver["fnumber"].ToString();

                var crmDistributor = crmDistributors.FirstOrDefault(s => Convert.ToString(s["fdelivernumber"]).EqualsIgnoreCase(number));

                deliver["fcrmdistributorid"] = Convert.ToString(crmDistributor?["fid"]);
            }
        }

        /// <summary>
        /// 更新实控人信息
        /// 注：需要自行保存
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        public void UpdateBoss(UserContext userCtx, IEnumerable<DynamicObject> delivers)
        {
            if (delivers.IsNullOrEmpty()) return;

            // 外部实控人ID
            var bossIds = delivers.Select(s => Convert.ToString(s["actualownerid"]));

            var bosses = userCtx.LoadBizDataById("ms_boss", bossIds);

            foreach (var deliver in delivers)
            {
                // 外部实控人ID
                string actualownerid = Convert.ToString(deliver["actualownerid"]);

                var boss = bosses.FirstOrDefault(
                    s => Convert.ToString(s["id"]).EqualsIgnoreCase(actualownerid));

                if (boss == null) continue;

                // 填充实控人的编码
                deliver["actualownernumber"] = boss["fnumber"];
            }
        }

        /// <summary>
        /// 更新《经销商》【招商经销商】和《招商经销商》的【经销商】
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="delivers"></param>
        public void UpdateAgentAndCrmDistributor(UserContext userCtx, IEnumerable<DynamicObject> delivers)
        {
            var agentIds = new HashSet<string>();
            foreach (var deliver in delivers)
            {
                string agentId = Convert.ToString(deliver["fagentid"]);

                if (agentId.IsNullOrEmptyOrWhiteSpace()) continue;

                agentIds.Add(agentId);
            }

            // 没有经销商，跳过后续处理
            if (agentIds.IsNullOrEmpty())
            {
                return;
            }

            var agents = userCtx.LoadBizDataById("bas_agent", agentIds);
            if (agents.Any())
            {
                var agentService = userCtx.Container.GetService<IAgentService>();

                // 更新经销商
                agentService.UpdateCrmDistributor(userCtx, agents);
                //userCtx.SaveBizData("bas_agent", agents);

                var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
                var result = gateway.InvokeBillOperation(userCtx, "bas_agent", agents, "draft",
                    new Dictionary<string, object>());
                result.ThrowIfHasError(true, "经销商保存失败！");

                // 反写招商经销商
                agentService.RewriteCrmDistributor(userCtx, agents);
            }
        }

        /// <summary>
        /// 根据经销商获取送达方(无组织隔离)
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agentIds"></param>
        /// <returns></returns>
        public List<DynamicObject> GetDelivers(UserContext userCtx, List<string> agentIds)
        {
            if (agentIds.Count <= 0) return new List<DynamicObject>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var meta = HtmlParser.LoadFormMetaFromCache("bas_deliver", userCtx);
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, meta.GetDynamicObjectType(userCtx));

            string sql = $@"select fid from t_bas_deliver  with(nolock) 
                 where fagentid in ({string.Join(",", agentIds.Select(id => $"'{id}'"))}) and fforbidstatus='0'";
            var reader = dbService.ExecuteReader(userCtx, sql, new List<SqlParam>());
            var datas = dm.SelectBy(reader)?.OfType<DynamicObject>()?.ToList();
            userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, meta.GetDynamicObjectType(userCtx), datas, false);
            return datas;
        }
    }
}
