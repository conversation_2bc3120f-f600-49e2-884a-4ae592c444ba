using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Service
{




    /// <summary>
    /// 商品明细信息（用于批量计算）
    /// </summary>
    public class MaterialItemInfo
    {
        public int OrderIndex { get; set; }
        public int ItemIndex { get; set; }
        public string MaterialId { get; set; }

        public string AttrInfo { get; set; }
        public string CustomDesc { get; set; }
        public string value { get; set; }
    }

    /// <summary>
    /// 订单映射信息
    /// </summary>
    public class OrderMapping
    {
        public int OrderIndex { get; set; }
        public string DeliverId { get; set; }

        public List<MaterialItemInfo> MaterialItemInfos { get; set; }

    }

    /// <summary>
    /// 安全库存信息
    /// </summary>
    public class SafetyStockInfo
    {
        public decimal StockToSaleRate { get; set; }
        public string StockWaterline { get; set; }

        public decimal Stockupqty { get; set; }
    }


 
    public class ValidateType
    {

        public string Type { get; set; }
        public string Pkey { get; set; }
        public bool IsValidate { get; set; }
        public Dictionary<string, string> ValidateData { get; set; }
    }
    /// <summary>
    /// 采购订单库存水位线计算服务
    /// </summary>
    [InjectService]
    public class PurchaseOrderStockWaterlineService
    {
        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }
        [InjectProperty]
        protected ISystemProfile _SystemProfile { get; set; }

        private List<string> _CheckSeries {get;set;}

        private Dictionary<string,string> _parentMap { get; set; }

        private Dictionary<string, List<string>> _childrenMap { get; set; }

        private Dictionary<string, SafetyStockInfo> safetyStockData { get; set; }
        #region 批量计算库存水位线状态

        /// <summary>
        /// 批量计算多个采购订单的库存水位线状态
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orderDataList">采购订单数据列表</param>
        /// <returns>计算结果，每个订单的每个商品都会被赋值</returns>
        public void BatchCalculateOrdersStockWaterline(UserContext userCtx, DynamicObject[] orderDataList)
        {
            if (orderDataList == null || orderDataList.Length == 0)
                return;

            try
            {
                var allDeliverIds = orderDataList.Select(p => Convert.ToString(p["fdeliverid"])).Distinct().ToList();
                //1:批量获取送达方库存水位配置
                Dictionary<string, DynamicObject> personalizedConfigs = BatchGetPersonalizedConfigs(userCtx, allDeliverIds);

                // 2. 批量预加载所有安全库存数据
                this.safetyStockData = BatchGetSafetyStockData(userCtx);
                foreach (var item in orderDataList)
                {
                    if (!CheckCalculatePreconditions(userCtx, item))
                    {
                        continue;
                    }
                    string deliverId = item["fdeliverid"].ToString();
                    var personalizedConfig = personalizedConfigs.ContainsKey(deliverId) ? personalizedConfigs[deliverId] : null;
                    if (personalizedConfig != null)
                    {
                        CalculateWithPersonalizedConfig(userCtx, item["fentity"] as DynamicObjectCollection, personalizedConfig, safetyStockData);
                    }
                    else
                    {

                        CalculateWithSafetyTable(item["fentity"] as DynamicObjectCollection, safetyStockData);
                    }
                }


            }
            catch (Exception ex)
            {
                // 异常情况下，设置所有商品为默认状态
                foreach (var orderData in orderDataList)
                {
                    var entityCollection = orderData["fentity"] as DynamicObjectCollection;
                    if (entityCollection != null)
                    {
                        foreach (var entity in entityCollection)
                        {
                            entity["fstockwaterline"] = "0";
                        }
                    }
                }
            }
        }
        /// <summary>
        /// 检查计算前提条件
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orderData">采购订单数据</param>
        /// <param name="result">校验结果</param>
        /// <returns>是否满足前提条件</returns>
        private bool CheckCalculatePreconditions(UserContext userCtx, DynamicObject orderData)
        {
            // 1. 【启用定制OMS】=否
            var enableCustomOms = Convert.ToBoolean(orderData["fomsservice"] ?? false);
            if (enableCustomOms)
            {

                return false;
            }

            // 2. 【焕新订单标记】=否
            var isRenewalOrder = Convert.ToBoolean(orderData["frenewalflag"] ?? false);
            if (isRenewalOrder)
            {
                return false;
            }

            // 3. 【一件代发标记】=否
            var isDropShipping = Convert.ToBoolean(orderData["fpiecesendtag"] ?? false);
            if (isDropShipping)
            {
                return false;
            }
            // 4【是否分销商】=否

            if (userCtx.IsSecondOrg)
            {
                return false;
            }
            // 5【送达方】不为空
            var fdeliverid = orderData["fdeliverid"].ToString();
            if (string.IsNullOrWhiteSpace(fdeliverid))
            {
                return false;
            }
            // 6. 【总部合同状态】≠已终审
            var contractStatus = Convert.ToString(orderData["fhqderstatus"] ?? "");
            if (contractStatus.EqualsIgnoreCase("03"))
            {
                return false;
            }

            return true; //所有前提条件都满足
        }
        /// <summary>
        /// 批量获取库存水位配置配置
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="deliverIds">送达方ID列表</param>
        /// <returns>个性化配置字典</returns>
        private Dictionary<string, DynamicObject> BatchGetPersonalizedConfigs(UserContext userCtx, List<string> deliverIds)
        {
            if (deliverIds == null || deliverIds.Count == 0)
                return new Dictionary<string, DynamicObject>();

            var filter = $"fforbidstatus = '0' and fcontrollevel = '1' and fcalculateenable = 1 and fdeliverid in ('{string.Join("','", deliverIds)}')";
            var configs = userCtx.LoadBizDataByFilter("si_inventorywaterlevel", filter);

            return configs.ToDictionary(x => Convert.ToString(x["fdeliverid"]), x => x);
        }
        /// <summary>
        /// 批量获取安全库存数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="allMaterialItems">所有商品明细</param>
        /// <returns>安全库存数据字典</returns>
        private Dictionary<string, SafetyStockInfo> BatchGetSafetyStockData(UserContext userCtx)
        {
            var results = new Dictionary<string, SafetyStockInfo>();

            try
            {

                var safetyStockData = userCtx.LoadBizDataByFilter("rpt_safetystockwaterlevel", $" fmainorgid='{userCtx.Company}' ");

                // 建立映射关系
                var dataMap = new Dictionary<string, SafetyStockInfo>();
                foreach (var item in safetyStockData)
                {
                    var materialId = Convert.ToString(item["fmaterialid"]);
                    var attrInfo = Convert.ToString(item["fattrinfo_e"]);
                    var customDesc = Convert.ToString(item["fcustomdesc"]);
                    var stockToSaleRate = Convert.ToDecimal(item["fstocktosalerate"] ?? 0);
                    var stockWaterline = Convert.ToString(item["fstockwaterline"] ?? "0");
                    var fstockupqty = Convert.ToDecimal(item["fstockupqty"] ?? 0);
                    var key = $"{materialId.Trim()}_{attrInfo.Trim()}_{customDesc.Trim()}";
                    dataMap[key] = new SafetyStockInfo
                    {
                        StockToSaleRate = stockToSaleRate,
                        StockWaterline = stockWaterline,
                        Stockupqty= fstockupqty
                    };
                }
                return dataMap;


            }
            catch (Exception ex)
            {
                return results;
            }

        }


        /// <summary>
        /// 使用个性化配置计算
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="items">商品明细列表</param>
        /// <param name="personalizedConfig">个性化配置</param>
        /// <param name="safetyStockData">安全库存数据</param>
        /// <param name="results">结果字典</param>
        private void CalculateWithPersonalizedConfig(UserContext userCtx, DynamicObjectCollection dynamicObjects,
            DynamicObject personalizedConfig, Dictionary<string, SafetyStockInfo> safetyStockData)
        {
            var defaultRedLine = Convert.ToDecimal(personalizedConfig["fredline"] ?? 0);
            var defaultGreenLine = Convert.ToDecimal(personalizedConfig["fgreenline"] ?? 0);

            // 获取例外SPU配置
            var exceptionSpuConfigs = new Dictionary<string, DynamicObject>();
            var exceptionModelsCollection = personalizedConfig["fexceptionmodelsentity"] as DynamicObjectCollection;
            if (exceptionModelsCollection != null)
            {
                foreach (var item in exceptionModelsCollection)
                {
                    var spuId = Convert.ToString(item["fseltypeid"]);
                    if (!string.IsNullOrWhiteSpace(spuId))
                    {
                        exceptionSpuConfigs[spuId] = item;
                    }
                }
            }

            // 逐个计算状态
            foreach (var item in dynamicObjects)
            {
                try
                {
                    string fseltypeid = (item["fmaterialid_ref"] as DynamicObject)?["fseltypeid"].ToString() ?? "";
                    decimal redLine = defaultRedLine;
                    decimal greenLine = defaultGreenLine;

                    // 检查是否有例外SPU配置
                    if (exceptionSpuConfigs.ContainsKey(fseltypeid))
                    {
                        var spuConfig = exceptionSpuConfigs[fseltypeid];
                        if (Convert.ToBoolean(spuConfig["fcalculateenable_spu"]))
                        {
                            redLine = Convert.ToDecimal(spuConfig["fredline_spu"] ?? defaultRedLine);
                            greenLine = Convert.ToDecimal(spuConfig["fgrennline_spu"] ?? defaultGreenLine);
                        }
                    }
                    var materialId = Convert.ToString(item["fmaterialid"]);
                    var attrInfo = Convert.ToString(item["fattrinfo_e"]);
                    var customDesc = Convert.ToString(item["fcustomdes_e"]);
                    string key = $"{materialId.Trim()}_{attrInfo.Trim()}_{customDesc.Trim()}";
                    // 获取库销比
                    var safetyInfo = safetyStockData.ContainsKey(key) ? safetyStockData[key] : new SafetyStockInfo();
                    var stockToSaleRate = safetyInfo.StockToSaleRate;

                    // 计算状态
                    item["fstockwaterline"] = CalculateWaterlineStatus(stockToSaleRate, redLine, greenLine);
                }
                catch
                {
                    item["fstockwaterline"] = "0";
                }
            }
        }

        /// <summary>
        /// 使用安全库存水位表计算
        /// </summary>
        /// <param name="items">商品明细列表</param>
        /// <param name="safetyStockData">安全库存数据</param>
        /// <param name="results">结果字典</param>
        private void CalculateWithSafetyTable(DynamicObjectCollection dynamicObjects,
            Dictionary<string, SafetyStockInfo> safetyStockData)
        {
            foreach (var item in dynamicObjects)
            {
                try
                {
                    var materialId = Convert.ToString(item["fmaterialid"]);
                    var attrInfo = Convert.ToString(item["fattrinfo_e"]);
                    var customDesc = Convert.ToString(item["fcustomdes_e"]);
                    if (string.IsNullOrWhiteSpace(item["fmaterialid"].ToString()))
                    {
                        item["fstockwaterline"] = "0";
                        continue;
                    }
                    string key = $"{materialId.Trim()}_{attrInfo.Trim()}_{customDesc.Trim()}";
                    // 直接从安全库存水位表获取状态
                    var safetyInfo = safetyStockData.ContainsKey(key) ? safetyStockData[key] : new SafetyStockInfo();
                    item["fstockwaterline"] = safetyInfo.StockWaterline ?? "0";
                }
                catch
                {
                    item["fstockwaterline"] = "0";
                }
            }
        }

        /// <summary>
        /// 根据库销比和红绿灯线计算水位线状态
        /// </summary>
        /// <param name="stockToSaleRate">库销比</param>
        /// <param name="redLine">红灯线</param>
        /// <param name="greenLine">绿灯线</param>
        /// <returns>库存水位线状态：1-红灯，2-绿灯，3-黄灯</returns>
        private string CalculateWaterlineStatus(decimal stockToSaleRate, decimal redLine, decimal greenLine)
        {
            if (stockToSaleRate > redLine)
            {
                return "1"; // 红灯
            }
            else if (stockToSaleRate > greenLine)
            {
                return "3"; // 黄灯
            }
            else
            {
                return "2"; // 绿灯
            }
        }


        #endregion

        #region 批量校验库水位线

        /// <summary>
        /// 完整的库存水位线校验方法
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orderData">采购订单数据</param>
        /// <returns>校验结果</returns>
        public List<string> ValidateStockWaterlineComplete(UserContext userCtx, DynamicObject[] orderDataList)
        {
            var result = new List<string>();
            var allDeliverIds = new HashSet<string>();
            var allSeriesIds = new HashSet<string>();
            var allCategoryIds = new HashSet<string>();
            foreach (var item in orderDataList)
            {
                allDeliverIds.Add(item["fdeliverid"].ToString());
                var entry = item["fentity"] as DynamicObjectCollection;
                foreach (var entryItem in entry)
                {
                    var fmaterialidObj = entryItem["fmaterialid_ref"] as DynamicObject;
                    allSeriesIds.Add(Convert.ToString(fmaterialidObj?["fseriesid"]));
                    allCategoryIds.Add(Convert.ToString(fmaterialidObj?["fcategoryid"]));
                }
            }
            //1:获取已启用校验的数据
            var validateDatas = GetInventorywaterLevelConfigs(userCtx, allDeliverIds.ToList());
            this._CheckSeries = GetVerifySerise(userCtx, allSeriesIds.ToList());
            (this._parentMap,this._childrenMap) = GetVerifyCategory(userCtx, allCategoryIds.ToList());
            var fsafetystockqty = this._SystemProfile.GetSystemParameter(userCtx.CreateTopOrgDBContext(), "pur_systemparam", "fsafetystockqty", 0M);
            foreach (var orderData in orderDataList)
            {
                orderData["fiswaterredlinefail"] = false;
                string deliverId = Convert.ToString(orderData["fdeliverid"]);
                string agentId = Convert.ToString(orderData["fmainorgid"]);
                // 获取红线商品
                var redLineItems = GetRedLineItems(orderData);
                if (redLineItems.Count == 0)
                {
                    continue; // 没有红线商品，直接通过
                }
                // 第一层判断：校验前提条件
                if (!CheckValidationPreconditions(userCtx, orderData))
                {
                    continue;
                }
               
                var deliverValidateDatas = validateDatas.TryGetValue(deliverId.Trim(),out var deliverValidate);
                var agentValidateDatas = validateDatas.TryGetValue(agentId.Trim(), out var agentValidate);
                var commonValidateDatas = validateDatas.TryGetValue("common", out var commonValidate);
                if (deliverValidate != null && !deliverValidate.IsValidate)
                {
                    continue;
                }
                if (deliverValidate==null&&agentValidate != null && !agentValidate.IsValidate)
                {
                    continue;
                }
                if (deliverValidate == null& agentValidate==null&&commonValidate != null && !commonValidate.IsValidate)
                {
                    continue;
                }
                foreach (var item in redLineItems)
                {
                    //校验商品对应的【系列】勾选【采购下单不校验库存水位线】
                    var producrobj = item["fmaterialid_ref"] as DynamicObject;
                    if (_CheckSeries.Contains(producrobj?["fseriesid"]))
                    {
                        continue;
                    }
                    //校验商品对应的【商品类别】勾选【采购下单不校验库存水位线】
                    if (CheckForCategory(producrobj?["fcategoryid"].ToString()))
                    {
                        continue;
                    }

                    //校验商品在【安全库存水位线.备货数】<总部采购管理参数.【安全库存基数】
                    var materialId = Convert.ToString(item["fmaterialid"]);
                    var attrInfo = Convert.ToString(item["fattrinfo_e"]);
                    var customDesc = Convert.ToString(item["fcustomdes_e"]);
                    string key = $"{materialId.Trim()}_{attrInfo.Trim()}_{customDesc.Trim()}";
                    var safetyInfo = safetyStockData.ContainsKey(key) ? safetyStockData[key] : new SafetyStockInfo();
                    if (safetyInfo.Stockupqty < fsafetystockqty)
                    {
                        continue;
                    }
                    string fseltypeid = producrobj?["fseltypeid"].ToString() ?? "";
                    string key1 = $"{fseltypeid.Trim()}_{deliverId.Trim()}";
                    string key2 = $"{fseltypeid.Trim()}_{agentId.Trim()}";
                    string key3 = $"{fseltypeid.Trim()}_common";
                    if (deliverValidate!=null&&deliverValidate.ValidateData.ContainsKey(key1)) {
                        continue;
                    }
                  
                    if (agentValidate != null && agentValidate.ValidateData.ContainsKey(key2))
                    {
                        continue;
                    }
                   
                    if (commonValidate != null && commonValidate.ValidateData.ContainsKey(key3))
                    {
                        continue;
                    }
                    orderData["fiswaterredlinefail"] = true;
                    result.Add($"第{item["fseq"]}行商品{(item["fmaterialid_ref"] as DynamicObject)?["fnumber"]},超出库存水位线管控，不允许提交总部。若需提交总部，请联系对应区域人员处理，谢谢！");
                }
            }
            return result;
        }

        /// <summary>
        /// 检查校验前提条件（第一层判断）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="orderData">采购订单数据</param>
        /// <param name="result">校验结果</param>
        /// <returns>是否满足前提条件</returns>
        private bool CheckValidationPreconditions(UserContext userCtx, DynamicObject orderData)
        {
            // 1. 【启用定制OMS】=否
            var enableCustomOms = Convert.ToBoolean(orderData["fomsservice"] ?? false);
            if (enableCustomOms)
            {
                return false;
            }

            // 2. 【焕新订单标记】=否
            var isRenewalOrder = Convert.ToBoolean(orderData["frenewalflag"] ?? false);
            if (isRenewalOrder)
            {
                return false;
            }

            // 3. 【一件代发标记】=否
            var isDropShipping = Convert.ToBoolean(orderData["fpiecesendtag"] ?? false);
            if (isDropShipping)
            {
                return false;
            }

            // 4. 【库存水位管控特批状态】≠审批通过
            var specialApprovalStatus = Convert.ToString(orderData["fstockwaterspecialstatus"] ?? "");
            if (specialApprovalStatus.EqualsIgnoreCase("stockwaterspecial_status_success"))
            {
                return false;
            }

            // 5. 【总部合同状态】≠已终审
            var contractStatus = Convert.ToString(orderData["fhqderstatus"] ?? "");
            if (contractStatus.EqualsIgnoreCase("03"))
            {
                return false;
            }
            // 5. 【单据类型】≠摆场订单
            var filltype = orderData["fbilltypeid_ref"] as DynamicObject;
            if (filltype != null&&(filltype["fnumber"].ToString().EqualsIgnoreCase("BCDD_SYS_01")|| filltype["fname"].ToString().EqualsIgnoreCase("摆场订单")))
            {
                return false;
            }
            return true; // 所有前提条件都满足
        }

        /// <summary>
        /// 获取红线商品列表
        /// </summary>
        /// <param name="orderData">采购订单数据</param>
        /// <returns>红线商品列表</returns>
        private List<DynamicObject> GetRedLineItems(DynamicObject orderData)
        {
            var redLineItems = new List<DynamicObject>();
            var entityCollection = orderData["fentity"] as DynamicObjectCollection;

            if (entityCollection != null)
            {
                for (int i = 0; i < entityCollection.Count; i++)
                {
                    var entity = entityCollection[i];
                    var stockWaterline = Convert.ToString(entity["fstockwaterline"] ?? "0");
                    if (stockWaterline == "1") // 红灯状态
                    {
                        redLineItems.Add(entity);
                    }
                }
            }

            return redLineItems;
        }



        /// <summary>
        /// 获取校验数据
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="deliverIds"></param>
        /// <returns></returns>
        private Dictionary<string, ValidateType> GetInventorywaterLevelConfigs(UserContext userCtx, List<string> deliverIds)
        {
            Dictionary<string, ValidateType> result = new Dictionary<string, ValidateType>();
            List<ValidateType> validates = new List<ValidateType>();
            var filter = $"a.fforbidstatus = '0'  and (a.fdeliverid in ('{string.Join("','", deliverIds)}') or fagentid='{userCtx.Company}' or fcontrollevel='3')";
            string sql = $"select a.fdeliverid,a.fagentid,a.fcontrollevel,a.fvalidateenable ,b.fseltypeid,b.fvalidateenable_spu  from t_si_inventorywaterlevel a left join t_si_exceptionmodels b on a.fid=b.fid where {filter}";
            var list = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>() { });
            if (list.Count() <= 0)
            {
                return result;
            }
            var deliverGroup = list.Where(x => !string.IsNullOrWhiteSpace(x["fdeliverid"].ToString())).GroupBy(x=>new { fdeliverid= x["fdeliverid"].ToString() , fvalidateenable= x["fvalidateenable"].ToString() });
            var agent = list.Where(x => !string.IsNullOrWhiteSpace(x["fagentid"].ToString())).FirstOrDefault();
            var all = list.Where(x => Convert.ToString(x["fcontrollevel"]) == "3").FirstOrDefault();
            if (deliverGroup != null)
            {
                foreach (var item in deliverGroup)
                {
                    string fdeliverid = Convert.ToString(item.Key.fdeliverid);

                    var _fseltypeDeliver = item.Where(x => !string.IsNullOrWhiteSpace(x["fdeliverid"].ToString()) && Convert.ToString(x["fvalidateenable_spu"] ?? "") == "0").ToList();
                    ValidateType deliverVa = new ValidateType
                    {
                        IsValidate = item.Key.fvalidateenable == "1" ? true : false,
                        Pkey = fdeliverid.Trim(),
                        Type = "1",
                        ValidateData = _fseltypeDeliver.ToDictionary(x => $"{x["fseltypeid"].ToString().Trim()}_{x["fdeliverid"].ToString().Trim()}", x => x["fvalidateenable_spu"].ToString().Trim())
                    };
                    result.Add(fdeliverid.Trim(), deliverVa);
                }
               
               
            }
            if (agent != null)
            {
                string agentid = Convert.ToString(agent["fagentid"]);
                var _fseltypeAgent = list.Where(x => !string.IsNullOrWhiteSpace(x["fagentid"].ToString()) && Convert.ToString(x["fvalidateenable_spu"] ?? "") == "0").ToList();
                ValidateType agenttype = new ValidateType
                {
                    IsValidate = Convert.ToString(agent["fvalidateenable"]) == "1" ? true : false,
                    Pkey = agentid,
                    Type = "2",
                    ValidateData = _fseltypeAgent.ToDictionary(x => $"{x["fseltypeid"].ToString().Trim()}_{x["fagentid"].ToString().Trim()}", x => x["fvalidateenable_spu"].ToString().Trim())
                };
                result.Add(agentid.Trim(), agenttype);
            }
            if (all != null)
            {
                var _fseltypeCommon = list.Where(x => Convert.ToString(x["fcontrollevel"]) == "3" && Convert.ToString(x["fvalidateenable_spu"] ?? "") == "0").ToList();
                ValidateType common = new ValidateType
                {
                    IsValidate = Convert.ToString(all["fvalidateenable"]) == "1" ? true : false,
                    Pkey = "common",
                    Type = "3",
                    ValidateData = _fseltypeCommon.ToDictionary(x => $"{x["fseltypeid"].ToString().Trim()}_common", x => x["fvalidateenable_spu"].ToString().Trim())
                };
                result.Add("common", common);
            }
            return result;
        }

        /// <summary>
        /// 获取开启不需校验库存水位的线的系列
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="seriesId"></param>
        /// <returns></returns>
        private List<string> GetVerifySerise(UserContext userCtx,List<string>seriesId) {
            string sql = $"select fid from t_ydj_series where fmainorgid ='{userCtx.TopCompanyId}' and fisverifywaterline=1 and fid in ('{string.Join(",", seriesId)}')";
           List<string> series= userCtx.ExecuteDynamicObject(sql, null).Select(x=>Convert.ToString(x["fid"])).ToList();
            return series;
        }
        /// <summary>
        /// 获取开启不需校验库存水位的线的类别
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="categoryId"></param>
        /// <returns></returns>
        private (Dictionary<string, string>, Dictionary<string, List<string>>) GetVerifyCategory(UserContext userCtx, List<string> categoryId)
        {
            string sql = $"select fid,fparentid from ser_ydj_category where  fmainorgid ='{userCtx.TopCompanyId}' and fisverifywaterline=1 and fid in ('{string.Join(",", categoryId)}')";
            var dataList=  userCtx.ExecuteDynamicObject(sql, null).ToList();
            if(dataList.Count<=0) return (new Dictionary<string, string>(), new Dictionary<string, List<string>>());
            var parentMap = dataList.ToDictionary(c => Convert.ToString(c["fid"]), c => Convert.ToString(c["fparentid"]));
            var childrenMap = dataList
                .Where(c => !Convert.ToString(c["fparentid"]).IsNullOrEmptyOrWhiteSpace())
                .GroupBy(c => Convert.ToString(c["fparentid"]))
                .ToDictionary(g => g.Key, g => g.Select(c =>Convert.ToString(c["fid"])).ToList());
            return (parentMap, childrenMap);
        }

        private bool CheckForCategory(string categoryId) {
            if (categoryId.IsNullOrEmptyOrWhiteSpace()) return false;
            if (this._parentMap.ContainsKey(categoryId)) return true;
            // 2. 检查所有父级（向上追溯）
            var current = categoryId;
            while (_parentMap.TryGetValue(current, out var parentId) && !parentId.IsNullOrEmptyOrWhiteSpace())
            {
                if (this._parentMap.ContainsKey(parentId))
                    return true;
                current = parentId;
            }

            // 3. 检查所有子级（向下追溯，广度优先搜索）
            var queue = new Queue<string>();
            if (_childrenMap.TryGetValue(categoryId, out var children))
            {
                foreach (var child in children)
                    queue.Enqueue(child);
            }

            while (queue.Count > 0)
            {
                var childId = queue.Dequeue();
                if (_parentMap.ContainsKey(childId))
                    return true;

                if (_childrenMap.TryGetValue(childId, out var grandChildren))
                {
                    foreach (var grandChild in grandChildren)
                        queue.Enqueue(grandChild);
                }
            }
            return false;
        }
        #endregion
    }
}
