using JieNor.AMS.YDJ.MS.API;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 针对采购订单变更中，总部变更状态=终审，或驳回。需要审核采购订单变更单
    /// </summary>
    [InjectService]
    public class PurOrderChgAutoAuditService /*: IPurOrderChgAutoAuditService*/
    { 
        public IOperationResult AutoAuditPurOrderChg(UserContext userCtx,List <string > purIds, OperateOption option)
        {
            var result = userCtx.Container.GetService<IOperationResult>(); 
            if (purIds != null && purIds.Any())
            {  
                var purOrderList = userCtx.LoadBizDataById("ydj_purchaseorder", purIds);
                if (purOrderList != null && purOrderList.Any())
                { 
                    var submitAuditResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(userCtx, "ydj_purchaseorder", purOrderList, "audit", null);
                    if (submitAuditResult.IsSuccess)
                    {
                        result.SimpleMessage = "变更单自动审批成功！"; 
                    }
                    else
                    {
                        result.SimpleMessage = submitAuditResult.ToString();
                        return result;
                    } 
                }

                return result;
            }
            else
            {
                result.SimpleMessage = "未查询到需要自动审核的采购订单变更单！";
                return result;
            }
        }
    }
}
