using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.Framework.MetaCore.FormOp.ConvertService.PlugIn;
using Newtonsoft.Json;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore;
using JieNor.Framework.Enums;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using System.Data;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataEntity.BillType;

namespace JieNor.AMS.YDJ.Store.AppService.Report.OrdertoBy
{
    /// <summary>
    /// 合同转采购报表：根据供应商拆分下推采购订单 或 采购申请单
    /// </summary>
    [InjectService]
    [FormId("rpt_ordertobuy")]
    [OperationNo("PushPurOrder")]
    public class PushPurOrder : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //e.Rules.Add(new Plugin.Pur.PurchaseOrder.Validation_Save());
            //错误消息
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                if (!Convert.ToString(newData["orderstatus"]).EqualsIgnoreCase("E"))
                {
                    errorMessage = $"销售合同【{newData["fbillno"]}】订单状态不为已审核，不允许下推采购！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                if (Convert.ToDecimal(newData["sugQty"]) == 0)
                {
                    errorMessage = $"销售合同【{newData["fbillno"]}】建议采购量为0，不允许下推采购！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            var IsRenewalPur = this.GetQueryOrSimpleParam<bool>("IsRenewalPur", false);
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!IsRenewalPur && Convert.ToBoolean(newData["frenewalflag"]))
                {
                    return false;
                }
                return true;
            }).WithMessage("销售合同【{0}】属于焕新合同，不允许<采购>操作，谢谢！", (billObj, propObj) => billObj["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var orderObj = this.Context.LoadBizDataByFilter("ydj_order",$" fbillno = '{newData["fbillno"]}'", "fpiecesendtag" ).FirstOrDefault();
                if (orderObj!=null&& (orderObj["fpiecesendtag"].ToString().EqualsIgnoreCase("1") || orderObj["fpiecesendtag"].ToString().EqualsIgnoreCase("true")))
                {
                    return false;
                }

                return true;
            }).WithMessage("当前销售合同【{0}】属于一件代发合同，不允许正常采购操作，请走一件代发流程，谢谢！", (billObj, propObj) => billObj["fbillno"]));
        }

        /// <summary>
        /// 转换数据包
        /// </summary>
        /// <param name="dataEntitys">订单转采购报表数据包</param>
        /// <returns>合同数据包</returns>
        public DynamicObject[] AlterOrderCloseStatus(DynamicObject[] dataEntitys)
        {
            var entryIds = dataEntitys.Select(x => Convert.ToString(x["fenid"]))
                             .ToList();
            //根据明细id获取关联合同
            var orders = this.Context.LoadBizDataByFilter("ydj_order", " exists (select 1 from t_ydj_orderentry odmx where odmx.fid = t_ydj_order.fid and odmx.fentryid in ('{0}') ) ".Fmt(string.Join("','", entryIds)));
            return orders.ToArray();
        }

        Dictionary<string, object> dicoldData = new Dictionary<string, object>();

        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            foreach (var x in e.DataEntitys)
            {
                dicoldData.Add(Convert.ToString(x["fenid"]), x["sugQty"]);
            }
            e.DataEntitys = AlterOrderCloseStatus(e.DataEntitys);
            var formDt = this.HtmlForm.GetDynamicObjectType(this.Context);
            // 加载引用数据
            var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, formDt, e.DataEntitys, false);
            //销售合同允许采购的最低金额比例
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var systemParameter = profileService.GetSystemParameter(this.Context, "bas_storesysparam");
            // var fproportionbuyeramount = Convert.ToInt32(systemParameter["fproportionbuyeramount"]);
            var billTypeService = this.Container.GetService<IBillTypeService>();

            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            //单据类型
            var billData = GetBilltype(orderForm);
            foreach (var dataEntity in e.DataEntitys)
            {
                //任务37793 修改为单据类型里取数
                var fproportionbuyeramount = 0;

                var paramSetObj = billTypeService.GetBillTypeParamSet(this.Context, orderForm, Convert.ToString(dataEntity["fbilltype"]));
                if (paramSetObj != null)
                {
                    int.TryParse(Convert.ToString(paramSetObj["fproportionbuyeramount"]), out fproportionbuyeramount);
                }
                //校验销售合同允许出库
                var freceivable = dataEntity["freceivable"];
                var fsumamount = dataEntity["fsumamount"];

                //判断是否允许采购
                //#37762改为订单总额-申请退货金额
                var newfsumamount = (Convert.ToDouble(fsumamount) - Convert.ToDouble(dataEntity["frefundamount"])) * fproportionbuyeramount / 100;
                ////过滤 单据类型 = "销售转单"
                var orderids = billData.Where(x => x.fname == "销售转单" || (!x.fprimitivename.IsNullOrEmptyOrWhiteSpace() && x.fprimitivename == "销售转单")).Select(x => x.fid).ToList();
                if (Convert.ToDouble(freceivable) < newfsumamount && !orderids.Contains(Convert.ToString(dataEntity["fbilltype"])))
                    throw new BusinessException("当前订单确认已收金额不足" + newfsumamount + "元，暂不允许下采购！");

                //验证合同下推采购
                new OrderCommon(this.Context).CheckPurOrder(profileService, dataEntity);
            }
        }

        /// <summary>
        /// 返回对应单据类型
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private List<BillTypeInfo> GetBilltype(HtmlForm orderForm)
        {
            var svc = this.Context.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(this.Context, orderForm);

            return billTypeInfos;
            //Dictionary<string, string> dic = new Dictionary<string, string>();
            //var purForm = this.MetaModelService.LoadFormModel(this.Context, "bd_billtype");
            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));
            //var reader = this.Context.GetPkIdDataReader(purForm, "", new List<SqlParam>());
            //var purOrder = dm.SelectBy(reader).OfType<DynamicObject>();
            //return purOrder;
        }

        /// <summary>
        /// 过滤商品
        /// </summary>
        private void FilterProduct(DynamicObjectCollection fentry, List<string> purzeor, Dictionary<string, string> errorDic, bool foutspotnopur, List<string> noCanPurProductIds, List<Row> rowIds, string orderBillNo, bool fispdk, bool isfsuperpinpurchase)
        {
            foreach (var item in fentry)
            {
                var key = Convert.ToString(item["id"]);
                var closestatus = Convert.ToString(item["fclosestatus_e"]);

                //过滤掉非勾选明细行
                if (rowIds.Count > 0 && !rowIds.Select(x => x.Id.ToString()).ToList().Contains(key))
                {
                    purzeor.Add(key);
                    continue;
                }
                //if (fentry.Count(x => x["ftransferorderstatus"].ToString() != string.Empty && x["fshipperagentid"].ToString() == Context.Company) > 0
                //    && item["fshipperagentid"].ToString() != this.Context.Company)
                //{
                //    errorDic[Guid.NewGuid().ToString("N")] = "第 " + Convert.ToString(item["fseq"]) + " 行 商品 :" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + " 发货经销商非等于当前登录人,不允许下推！！";
                //    purzeor.Add(key);
                //}
                //isfsuperpinpurchase 允许超销售数量采购时 不需要校验采购数量
                if (Convert.ToInt32(item["fqty"]) <= Convert.ToInt32(item["fpurqty"]) && !isfsuperpinpurchase)
                {
                    errorDic[Guid.NewGuid().ToString("N")] = "商品 :" + JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + " 采购数量大于等于销售数量,不允许下推！";
                    purzeor.Add(key);
                }

                if (!(closestatus == "0" ||
                    closestatus == "2" ||
                    closestatus == " "))//只有正常\部分关闭\空才能下推，其它过滤掉
                {
                    errorDic[Guid.NewGuid().ToString("N")] = JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + "商品" + Convert.ToString(item["fseq"]) + GetCloseStatusStr(closestatus) + "，不能下推采购！";
                    purzeor.Add(key);
                }
                if (foutspotnopur && Convert.ToBoolean(item["fisoutspot"]))//出现货商品无需采购
                {
                    errorDic[Guid.NewGuid().ToString("N")] = JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + "商品出现货，不允许采购！";
                    purzeor.Add(key);
                }
                if (noCanPurProductIds.Contains(item["fproductid"]))//商品授权清单中定义的不可采购商品
                {
                    //errorDic[key] = "商品【{0} {1}】为历史商品，总部未授权，不可采购！ ".Fmt((item["fproductid_ref"] as DynamicObject)?["fnumber"], (item["fproductid_ref"] as DynamicObject)?["fname"]);
                    errorDic[Guid.NewGuid().ToString("N")] = JNConvert.ToStringAndTrim((item["fproductid_ref"] as DynamicObject)?["fname"]) + " 在商品授权清单中定义的不可采购商品，不允许采购！";
                    purzeor.Add(key);
                }
            }

            #region 36178 套件与沙发组合转采购时, 并需成套采购, 要增加校验 / 套件与沙发组合的销售合同转采购时根据参数增加校验
            if (fispdk)
            {
                //需要验证套件的KEY
                Dictionary<string, string> fieldKey = new Dictionary<string, string>() {
                { "fsuitcombnumber","fsuitcombnumber"},//套件商品组合号
                { "fsofacombnumber","fsofacombnumber"},//沙发组合
                };
                //得到选中行
                var selData = fentry.Where(x => rowIds.Count > 0 ? rowIds.Select(j => j.Id.ToString()).ToList().Contains(Convert.ToString(x["id"])) : 1 == 1).ToList();

                //记录验证套件同组是否全部勾选
                Dictionary<string, int> pdkDic = new Dictionary<string, int>();

                foreach (var item in fentry)
                {
                    foreach (var valItem in fieldKey)
                    {
                        var fValue = Convert.ToString(item[valItem.Key]);
                        //需要组合的KEY加入计算并累加并且值要对应选中行的值
                        if (!fValue.Trim().IsNullOrEmptyOrWhiteSpace()
                            && selData.Select(x => Convert.ToString(x[valItem.Key])).ToList().Contains(fValue))
                        {
                            if (pdkDic.ContainsKey(fValue))
                                pdkDic[fValue] += 1;
                            else
                                pdkDic[fValue] = 1;
                        }
                    }

                    var key = Convert.ToString(item["id"]);
                    //过滤掉非勾选明细行，累减时过滤掉非选中行
                    if (rowIds.Count > 0 && !rowIds.Select(x => x.Id.ToString()).ToList().Contains(key))
                    {
                        continue;
                    }

                    //需要组合的KEY加入计算并累减
                    foreach (var valItem in fieldKey)
                    {
                        var fValue = Convert.ToString(item[valItem.Key]);
                        //需要组合的KEY加入计算并累加
                        if (!fValue.Trim().IsNullOrEmptyOrWhiteSpace())
                        {
                            if (pdkDic.ContainsKey(fValue))
                                pdkDic[fValue] -= 1;
                        }
                    }
                }

                //状态是否一致
                Dictionary<string, string> reportDic = new Dictionary<string, string>() {
                    { "fisoutspot",""},//出现货
                    { "fclosestatus_e",""},//行关闭状态
                    };

                //判断组合是否成套
                foreach (var item in pdkDic)
                {
                    if (item.Value > 0)
                    {
                        throw new BusinessException("当前转采购的商品存在 套子件商品 或 沙发组合商品, 必须成套采购, 不允许单独勾选进行采购! ");
                    }

                    //对应套件组
                    foreach (var fVal in fieldKey)
                    {
                        //用于比较套件组每行状态是否一样
                        Dictionary<string, string> groupDic = new Dictionary<string, string>();

                        foreach (var data in selData.Where(x => Convert.ToString(x[fVal.Key]) == item.Key))
                        {
                            //验证套件相关状态是否一致
                            foreach (var rVal in reportDic)
                            {
                                var prodData = (data["fproductid_ref"] as DynamicObject);

                                if (!groupDic.ContainsKey(rVal.Key))
                                {
                                    groupDic[rVal.Key] = Convert.ToString(data[rVal.Key]);
                                }
                                else
                                {

                                    //状态值不一致，报错提示
                                    if (groupDic[rVal.Key] != Convert.ToString(data[rVal.Key]))
                                    {
                                        if (rVal.Key == "fisoutspot")
                                            throw new BusinessException("当前转采购的商品 " + JNConvert.ToStringAndTrim(prodData?["fname"]) + " 为 套子件商品 或 沙发组合商品, 必须成套采购 , 对应成套的商品有部份为出现货, 所以当前商品不允许转采购! 请确认是否全部出现货 或 全部转采购 ! ");
                                        if (rVal.Key == "fclosestatus_e")
                                            throw new BusinessException("当前转采购的商品 " + JNConvert.ToStringAndTrim(prodData?["fname"]) + " 为 套子件商品 或 沙发组合商品, 必须成套采购 , 对应成套的商品有部份行关闭状态存在不一致, 所以当前商品不允许转采购 !");
                                    }
                                }

                                if (JNConvert.ToStringAndTrim(prodData?["fendpurchase"]).EqualsIgnoreCase("true"))
                                {
                                    throw new BusinessException("当前转采购的商品 " + JNConvert.ToStringAndTrim(prodData?["fname"]) + " 为 套子件商品 或 沙发组合商品, 必须成套采购 , 对应成套的商品有部份停购, 所以当前商品不允许转采购 !");
                                }
                            }
                        }
                    }
                }
            }

            #endregion

        }

        /// <summary>
        /// 返回对应状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string GetCloseStatusStr(string status)
        {
            string result = "";
            switch (status)
            {
                case "0":
                    result = "正常";
                    break;
                case "1":
                    result = "整单关闭";
                    break;
                case "2":
                    result = "部分关闭";
                    break;
                case "3":
                    result = "自动关闭";
                    break;
                case "4":
                    result = "手动关闭";
                    break;
                default:
                    result = "关闭";
                    break;
            }
            return result;
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            //选中商品行Id
            var rowIds = this.GetQueryOrSimpleParam<string>("rowIds");
            var selectRowIds = rowIds == null ? new List<Row>() : JsonConvert.DeserializeObject<List<Row>>(rowIds);

            this.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, orderForm.GetDynamicObjectType(this.Context), e.DataEntitys, false);

            var profileService = this.Container.GetService<ISystemProfile>();
            var foutspotnopur = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "foutspotnopur", false); //出现货商品无需采购
            var fispdk = profileService.GetSystemParameter(this.Context, "pur_systemparam", "fispdk", false); //套件与沙发商品是否成套采购
            var isfsuperpinpurchase = profileService.GetSystemParameter(Context, "pur_systemparam", "fsuperpinpurchase", false);//允许超销售数量采购
            //单据类型分组 目前分类 大客户，期初合同，其它 分为三类
            var billtypeGroup = GetBillTypeGroup(orderForm, e.DataEntitys);
            //过滤提示
            Dictionary<string, string> errorDic = new Dictionary<string, string>();
            //记录商品行ID相关过滤
            var topCloseOrg = new List<string>();
            //记录全部商品
            List<string> allpid = new List<string>();
            //商品明细集合
            List<DynamicObjectCollection> allProductList = new List<DynamicObjectCollection>();
            //商品授权清单中定义的不可采购商品
            var noCanPurProductIds = GetNotCanPurProduct();
            var fentrys = e.DataEntitys.SelectMany(t => t["fentry"] as DynamicObjectCollection);
            foreach (var item in e.DataEntitys)
            {
                var entries = item["fentry"] as DynamicObjectCollection;
                //过滤商品相关业务逻辑
                FilterProduct(entries, topCloseOrg, errorDic, foutspotnopur, noCanPurProductIds, selectRowIds, Convert.ToString(item["fbillno"]), fispdk, isfsuperpinpurchase);
                allpid.AddRange(entries.Select(x => x["fproductid"].ToString()).ToList());
                allProductList.Add(entries);
            }
            //待合单合同
            IEnumerable<DynamicObject> packDataEntitys = null;
            //一对一不合并的合同
            IEnumerable<DynamicObject> noPackDataEntitys = null;
            if (this.Context.IsSecondOrg)
            {
                packDataEntitys = e.DataEntitys.Where(x => !Convert.ToBoolean(x["fneedtransferorder"]));
                noPackDataEntitys = e.DataEntitys.Where(x => Convert.ToBoolean(x["fneedtransferorder"]));
            }
            else
            {
                packDataEntitys = e.DataEntitys;
            }

            //将总部和非总部商品进行区分
            var whetherdic = DicMainProdect(allpid);
            //全部未停购的商品
            var validProd = ProductNotStopBuying(allpid, errorDic, allProductList);

            //记录下推的明细行
            int count = 0;

            if (packDataEntitys != null && packDataEntitys.Any())
            {
                var packWhetherDic = whetherdic.Where(f => packDataEntitys.SelectMany(x => x["fentry"] as DynamicObjectCollection).Select(x => Convert.ToString(x["fproductid"])).Contains(f.Key));
                //城市分组
                var groupCity = GetGroupCity(packDataEntitys.Select(x => x["fdeptid"].ToString()).ToList());

                //单据类型分组 目前分类 大客户，期初合同，其它 分为三类
                foreach (var billItem in billtypeGroup)
                {

                    #region 1.1 无送达方的明细行处理

                    foreach (var vcity in groupCity)
                    {
                        //多选行同一城市的合同
                        var entitylist = packDataEntitys.Where(x => vcity.Value.Contains(x["fdeptid"].ToString()));
                        //多选行合同商品明细集合
                        List<DynamicObjectCollection> dlist = new List<DynamicObjectCollection>();
                        foreach (var valorder in entitylist)
                        {
                            var order = valorder;
                            var entries = order["fentry"] as DynamicObjectCollection;
                            dlist.Add(entries);
                        }
                        //将商品明细按送达方进行分组
                        var dic = GetDeliverByBrandidAndCity(vcity.Key, dlist);

                        //得到商品组织已关闭集合，记录的商品ID
                        //http://dmp.jienor.com:81/zentao/story-view-4354.html 取消转采购的停产校验
                        //topCloseOrg.AddRange(SearchCloseOrg(dlist, packWhetherDic.Where(x => x.Value == true).Select(x => x.Key).ToList(), selectRowIds, fispdk, errorDic));

                        //分组送达方商品进行合并
                        foreach (var v in dic)
                        {
                            // 如果是二级分销商的合同转采购，则只按单据类型和送达方来拆分
                            if (this.Context.IsSecondOrg)
                            {
                                var topOrg = packWhetherDic.Select(x => x.Key)?.ToList();
                                if (topOrg != null && topOrg.Any())
                                {
                                    count += PushPurorder(packDataEntitys.ToArray(), dlist, v.Value, topOrg, validProd, noCanPurProductIds, foutspotnopur, topCloseOrg, billItem.Value, errorDic);
                                }
                            }
                            else
                            {
                                var topOrg = packWhetherDic.Where(x => x.Value == true)?.Select(x => x.Key)?.ToList();
                                if (topOrg != null && topOrg.Any())//总部商品
                                {
                                    count += PushPurorder(packDataEntitys.ToArray(), dlist, v.Value, topOrg, validProd, noCanPurProductIds, foutspotnopur, topCloseOrg, billItem.Value, errorDic);
                                }

                                var agentOrg = packWhetherDic.Where(x => x.Value != true)?.Select(x => x.Key)?.ToList();
                                if (agentOrg != null && agentOrg.Any())//非总部商品
                                {
                                    count += PushPurorder(packDataEntitys.ToArray(), dlist, v.Value, agentOrg, validProd, noCanPurProductIds, foutspotnopur, topCloseOrg, billItem.Value, errorDic);
                                }
                            }
                        }
                    }

                    #endregion

                    #region 1.2 有送达方明细行的处理

                    //多选行合同商品明细集合
                    List<DynamicObjectCollection> haveShip = new List<DynamicObjectCollection>();
                    foreach (var valorder in packDataEntitys)
                    {
                        var order = valorder;
                        var entries = order["fentry"] as DynamicObjectCollection;
                        haveShip.Add(entries);
                    }

                    //将商品明细按送达方进行分组
                    var groupshipdic = GreupShip(haveShip);
                    //得到商品组织已关闭集合
                    //http://dmp.jienor.com:81/zentao/story-view-4354.html 取消转采购的停产校验
                    //topCloseOrg.AddRange(SearchCloseOrg(haveShip, packWhetherDic.Where(x => x.Value == true).Select(x => x.Key).ToList(), selectRowIds, fispdk, errorDic));

                    //分组送达方商品进行合并
                    foreach (var v in groupshipdic)
                    {
                        // 如果是二级分销商的合同转采购，则只按单据类型和送达方来拆分
                        if (this.Context.IsSecondOrg)
                        {
                            var topOrg = packWhetherDic.Select(x => x.Key)?.ToList();
                            if (topOrg != null && topOrg.Any())
                            {
                                count += PushPurorder(packDataEntitys.ToArray(), haveShip, v.Value, topOrg, validProd, noCanPurProductIds, foutspotnopur, topCloseOrg, billItem.Value, errorDic);
                            }
                        }
                        else
                        {
                            var topOrg = packWhetherDic.Where(x => x.Value == true)?.Select(x => x.Key)?.ToList();
                            if (topOrg != null && topOrg.Any())//总部商品
                            {
                                count += PushPurorder(packDataEntitys.ToArray(), haveShip, v.Value, topOrg, validProd, noCanPurProductIds, foutspotnopur, topCloseOrg, billItem.Value, errorDic);
                            }

                            var agenOrg = packWhetherDic.Where(x => x.Value != true)?.Select(x => x.Key)?.ToList();
                            if (agenOrg != null && agenOrg.Any())//非总部商品
                            {
                                count += PushPurorder(packDataEntitys.ToArray(), haveShip, v.Value, agenOrg, validProd, noCanPurProductIds, foutspotnopur, topCloseOrg, billItem.Value, errorDic);
                            }
                        }
                    }
                    #endregion
                }
            }

            if (noPackDataEntitys != null && noPackDataEntitys.Any())
            {
                PushPurOrderOneToOne(noPackDataEntitys.ToArray(), validProd, topCloseOrg);
            }

            foreach (var item in errorDic)
            {
                this.Result.ComplexMessage.ErrorMessages.Add(item.Value);
            }
        }

        /// <summary>
        /// 按单据类型 大客户，期初合同，其它 分为三类
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        private Dictionary<string, List<string>> GetBillTypeGroup(HtmlForm orderForm, DynamicObject[] obj)
        {
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();
            var billData = GetBilltype(orderForm);
            foreach (var item in obj)
            {
                var type = item["fbilltype"].ToString();
                var billent = billData.Where(x => x.fid == type || x.fprimitiveid == type).FirstOrDefault();
                var entitys = item["fentry"] as DynamicObjectCollection;
                //将明细ID 进行分类，加入到 PushPurorder 方法分类 
                foreach (var ent in entitys)
                {
                    var fname = Convert.ToString(billent.fname).Trim();
                    if (fname == "大客户销售合同")
                    {
                        AddDicdeliver("大客户销售合", ent["id"].ToString(), dic);
                    }
                    else if (fname == "期初销售合同")
                    {
                        AddDicdeliver("期初销售合同", ent["id"].ToString(), dic);
                    }
                    else
                    {
                        AddDicdeliver("其它单据", ent["id"].ToString(), dic);
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 获取不可采购商品id
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public List<string> GetNotCanPurProduct()
        {
            var result = new List<string>();
            if (this.Context.IsTopOrg)
            {
                return result;
            }

            var agents = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(this.Context);
            if (agents == null || agents.Count == 0)
            {
                return result;
            }
            var orderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
            var rulePara = new DataQueryRuleParaInfo()
            {
                SrcFldId = orderForm.Id,
            };
            var prdAuths = new ProductDataIsolateHelper().GetProductAuthInfo(Context, rulePara, agents);
            var prdAuthLst = prdAuths.SelectMany(f => f.fproductauthlist).ToList();
            if (prdAuthLst == null || prdAuthLst.Count == 0)
            {
                return result;
            }

            result = prdAuthLst.Where(f => f != null && f.fnopurchase)?.ToList()?.Select(f => f.fproductid)?.ToList();

            return result;
        }


        /// <summary>
        /// 销售合同转采购时销售组织校验的逻辑
        /// </summary>
        /// <param name="list"></param>
        /// <param name="ismainprod">商品ID</param>
        /// <param name="errorDic"></param>
        /// <returns></returns>
        private List<string> SearchCloseOrg(List<DynamicObjectCollection> list, List<string> ismainprod, List<Row> rowIds, bool fispdk, Dictionary<string, string> error)
        {

            //存储需要过滤的商品
            List<string> closeProd = new List<string>();
            //存储全部业绩品牌
            List<string> seriesids = new List<string>();
            foreach (var item in list)
            {
                var fresultbrandids = item.Where(x => ismainprod.Contains(x["fproductid"].ToString())).Select(x => x["fresultbrandid"].ToString()).ToList();
                seriesids.AddRange(fresultbrandids);
            }

            // 需求ID 29997
            string strSql = @" select distinct t2.fsaleorgid,t2.fdisablestatus,t1.fname,t4.fname,t5.fname t5fname,t1.fid,t4.fnumber,t4.fid t4fid from t_bd_material t1
			  join t_bd_materialsaleorg t2 on t1.fid=t2.fid
			  join t_ydj_orgresultbrandentry t3 on t2.fsaleorgid = t3.forganizationid
			  join t_ydj_series t4 on t3.fseriesid = t4.fid
			  join t_bas_organization t5 on t3.forganizationid = t5.fid
			  where t1.fid in('{0}') and t4.fid in ('{1}')".Fmt(string.Join("','", ismainprod), string.Join("','", seriesids));
            var dm = Context.Container.GetService<IDBService>();
            var res = dm.ExecuteDynamicObject(Context, strSql);

            if (res == null)//匹配不到的过滤掉
            {
                //记录过滤行ID
                foreach (var item in list)
                {
                    closeProd.AddRange(item.Where(x => ismainprod.Contains(x["fproductid"].ToString())).Select(x => x["id"].ToString()).ToList());
                }
                return closeProd;
            }
            //批量列表
            foreach (var item in list)
            {
                //单据
                foreach (var val in item)
                {
                    var count = res.Where(x => x["fdisablestatus"].ToString() == "1" &&
                        x["fid"].ToString() == val["fproductid"].ToString() &&
                         x["t4fid"].ToString() == val["fresultbrandid"].ToString()).Count();
                    //只校验总部商品
                    if (count == 0 && ismainprod.Contains(val["fproductid"].ToString()))
                    {
                        var fsuitcombnumber = Convert.ToString(val["fsuitcombnumber"]).Trim();
                        var fsofacombnumber = Convert.ToString(val["fsofacombnumber"]).Trim();

                        if (fsuitcombnumber != "" || fsofacombnumber != "")
                        {
                            if (rowIds.Select(x => x.Id.ToString()).ToList().Contains(Convert.ToString(val["id"])) && fispdk)
                            {
                                throw new BusinessException("当前转采购的商品 " + JNConvert.ToStringAndTrim((val["fproductid_ref"] as DynamicObject)?["fname"]) + " 为 套子件商品 或 沙发组合商品, 必须成套采购 , 对应成套的商品有部份停购, 所以当前商品不允许转采购 !");
                            }

                            if (rowIds.Count == 0 && fispdk)
                            {
                                throw new BusinessException("当前转采购的商品 " + JNConvert.ToStringAndTrim((val["fproductid_ref"] as DynamicObject)?["fname"]) + " 为 套子件商品 或 沙发组合商品, 必须成套采购 , 对应成套的商品有部份停购, 所以当前商品不允许转采购 !");
                            }
                        }


                        //不满足的行过滤掉，记录过滤的明细行ID
                        closeProd.Add(val["id"].ToString());
                        error[val["id"].ToString()] = "第 " + Convert.ToString(val["fseq"]) + " 行" + JNConvert.ToStringAndTrim((val["fproductid_ref"] as DynamicObject)?["fname"]) + "商品已停产，不允许采购！";
                    }
                }
            }
            return closeProd;
        }

        /// <summary>
        /// 将明细行的送达方进行分组
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private Dictionary<string, List<string>> GreupShip(List<DynamicObjectCollection> list)
        {
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();

            foreach (var item in list)
            {
                foreach (var data in item)
                {
                    //送达方
                    var key = data["fshipperdeliver"].ToString().Trim();
                    //非等于空的达送方
                    if (key != "")
                    {
                        //明细ID
                        var val = data["id"].ToString();
                        AddDicdeliver(key, val, dic);
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 下推采购
        /// </summary>
        /// <param name="order">单据头</param>
        /// <param name="entries">明细行</param>
        /// <param name="prodlistid">送达方分组商品</param>
        /// <param name="ismainlist">主商品与非主商品</param>
        /// <param name="validProd">有效商品</param>
        /// <param name="validProd">有效商品</param>
        /// <param name="noCanPurProductIds">商品授权清单中定义的不可采购商品</param>
        /// <param name="BillTypeGroup">按单据类型分组</param>
        /// <param name="closeOrg">要过滤的明细行</param>
        /// <param name="mainAgents">著经销商组织</param>
        private int PushPurorder(DynamicObject[] orders,
                                List<DynamicObjectCollection> entries,
                                List<string> prodlistid,
                                List<string> ismainlist,
                                List<string> validProd,
                                List<string> noCanPurProductIds,
                                bool foutspotnopur,
                                List<string> closeOrg,
                                List<string> billTypeGroup,
                                Dictionary<string, string> msg,
                                Dictionary<string, string> mainAgents = null)
        {
            BillConvertContext billCvtCtx = null;
            var ruleId = "ydj_order2ydj_purchaseorder";
            var sourceFormId = "ydj_order";
            var targetFormId = "ydj_purchaseorder";
            var selectedRows = new List<SelectedRow>();
            var entryIds = new List<string>();
            foreach (var order in orders)
            {
                #region 多个合同商品明细合并

                var selRows = new List<SelectedRow>();

                foreach (var ent in entries)
                {
                    var ens = ent.Where(x => billTypeGroup.Contains(x["id"].ToString())
                                    && prodlistid.Contains(x["id"].ToString())
                                    && ismainlist.Contains(x["fproductid"].ToString())
                                    && validProd.Contains(x["fproductid"].ToString())
                                    && !closeOrg.Contains(x["id"].ToString())
                                 ).ToList();
                    entryIds.AddRange(ens.Select(s => s["id"].ToString()).ToList());

                    selRows.AddRange(ens.Select(x =>
                    {
                        var sel = new SelectedRow
                        {
                            PkValue = order["id"].ToString(),
                            BillNo = order["fbillno"].ToString(),
                            EntityKey = "fentry",
                            EntryPkValue = Convert.ToString(x["id"])
                        };
                        sel.FieldValues = new Dictionary<string, object>();
                        sel.FieldValues["matid"] = x["fproductid"];
                        sel.FieldValues["matno"] = (x["fproductid_ref"] as DynamicObject)?["fnumber"];
                        sel.FieldValues["matname"] = (x["fproductid_ref"] as DynamicObject)?["fname"];
                        return sel;
                    }).ToList());
                }

                selectedRows.AddRange(selRows);
                #endregion
            }
            var option = OperateOption.Create();
            option.SetVariableValue("outspotEntries", dicoldData);

            billCvtCtx = new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = sourceFormId,
                TargetFormId = targetFormId,
                SelectedRows = selectedRows.ToConvertSelectedRows(),
                Option = option
            };

            var count = billCvtCtx.SelectedRows.Count();
            if (count == 0)
            {
                return count;
            }

            if (Convert.ToBoolean(orders[0]["fisapplypur"]))
            {
                billCvtCtx.RuleId = "ydj_order2pur_reqorder";
                billCvtCtx.TargetFormId = "pur_reqorder";
            }
            else
            {
                //检查是否已经下推过采购订单
                //this.CheckIsPushPurOrder(dataEntity);
            }

            var convertService = this.Container.GetService<IConvertService>();
            var result = convertService.Push(this.Context, billCvtCtx);
            var convertResult = result.SrvData as ConvertResult;

            if (billCvtCtx.TargetFormId.EqualsIgnoreCase("ydj_purchaseorder"))
            {
                this.DealRenewal(convertResult.TargetDataObjects, orders);
            }

            var invokeResult = this.Gateway.InvokeBillOperation(this.Context,
                billCvtCtx.TargetFormId,
                convertResult.TargetDataObjects,
                "draft",
                new Dictionary<string, object>());

            if (invokeResult.IsSuccess)
            {
                var entryList = new Dictionary<string, object>();
                foreach (var o in entries)
                {
                    foreach (var e in o)
                    {
                        entryList.Add(e["id"].ToString(), e["fbizqty"]);
                    }
                }

                var dbDataList = this.Context.LoadBizDataByFilter("ydj_order", " exists (select 1 from t_ydj_orderentry odmx where odmx.fid = t_ydj_order.fid and odmx.fentryid in ('{0}') ) ".Fmt(string.Join("','", entryIds)));
                if (dbDataList != null && dbDataList.Any())
                {
                    dbDataList.ForEach(d =>
                    {
                        var entry = d["fentry"] as DynamicObjectCollection;

                        this.Logger.WriteLog(this.Context, new LogEntry()
                        {
                            BillIds = d["Id"]?.ToString(),
                            BillNos = d["fbillno"]?.ToString(),
                            BillFormId = "ydj_order",
                            OpName = "采购",
                            OpCode = "Pushpurorder",
                            Content = "执行了【采购】操作！",
                            DebugData = "执行了【采购】操作！",
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03

                        });
                        foreach (var item in entry)
                        {
                            if (entryIds.Contains(item["id"].ToString()))
                            {
                                var entrytranspurqty = Convert.ToDecimal(item["ftranspurqty"]);//销售合同明细行已下推采购数
                                var bizQty = Convert.ToDecimal(item["fbizqty"]);//销售数量
                                //var transpurqty = Convert.ToDecimal(entryList[item["id"].ToString()]); //本次下推采购数
                                var transpurqty = Convert.ToDecimal(dicoldData[item["id"].ToString()]); //本次下推采购数
                                //if (entrytranspurqty + transpurqty > bizQty) //已下推采购数+本次下推采购数必须小于等于销售数
                                //{
                                //    transpurqty = bizQty - entrytranspurqty;
                                //}
                                item["ftranspurqty"] = entrytranspurqty;
                                item["flinkpro"] = "已转采购";
                            }
                        }
                    });
                    this.Context.SaveBizData("ydj_order", dbDataList);
                }
                var targetForm = this.MetaModelService.LoadFormModel(this.Context, billCvtCtx.TargetFormId);
                this.Result.IsSuccess = true;
                this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}生成成功！");
                foreach (var targetData in convertResult.TargetDataObjects)
                {
                    this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}编号【{targetData["fbillno"]}】");
                }
                //更新促销相关信息
                //  ModifyPromotion(convertResult.TargetDataObjects.ToList());
            }
            return count;
        }


        /// <summary>
		/// 合同一堆已下推采购
        /// </summary>
        /// <param name="orders">单据头</param>
        /// <param name="validProd">有效商品</param>
        /// <param name="closeOrg">要过滤的明细行</param>
        private int PushPurOrderOneToOne(DynamicObject[] orders,
                                List<string> validProd,
                                List<string> closeOrg)
        {
            BillConvertContext billCvtCtx = null;
            var ruleId = "ydj_order2ydj_purchaseorder";
            var sourceFormId = "ydj_order";
            var targetFormId = "ydj_purchaseorder";
            var selectedRows = new List<SelectedRow>();
            var entryList = new Dictionary<string, object>();
            var entryIds = new List<string>();
            foreach (var order in orders)
            {
                //修改 已转采购数 字段
                var fentry = order["fentry"] as DynamicObjectCollection;
                foreach (var o in fentry)
                {
                    var transpurqty = Convert.ToDecimal(o["ftranspurqty"]) + Convert.ToDecimal(o["fbizqty"]);
                    entryList.Add(o["id"].ToString(), transpurqty);
                }
                var ens = fentry.Where(x => validProd.Contains(x["fproductid"].ToString())
                                       && !closeOrg.Contains(x["id"].ToString())
                                     );
                entryIds.AddRange(ens.Select(s => s["id"].ToString()).ToList());
                var selRows = new List<SelectedRow>();
                selRows.AddRange(ens.Select(x =>
                {
                    var sel = new SelectedRow
                    {
                        PkValue = order["id"].ToString(),
                        BillNo = order["fbillno"].ToString(),
                        EntityKey = "fentry",
                        EntryPkValue = Convert.ToString(x["id"])
                    };
                    sel.FieldValues = new Dictionary<string, object>();
                    sel.FieldValues["matid"] = x["fproductid"];
                    sel.FieldValues["matno"] = (x["fproductid_ref"] as DynamicObject)?["fnumber"];
                    sel.FieldValues["matname"] = (x["fproductid_ref"] as DynamicObject)?["fname"];
                    return sel;
                }).ToList());

                selectedRows.AddRange(selRows);
            }

            billCvtCtx = new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = sourceFormId,
                TargetFormId = targetFormId,
                SelectedRows = selectedRows.ToConvertSelectedRows()
            };

            var count = billCvtCtx.SelectedRows.Count();
            if (count == 0)
            {
                return count;
            }

            if (Convert.ToBoolean(orders[0]["fisapplypur"]))
            {
                billCvtCtx.RuleId = "ydj_order2pur_reqorder";
                billCvtCtx.TargetFormId = "pur_reqorder";
            }
            else
            {
                //检查是否已经下推过采购订单
                //this.CheckIsPushPurOrder(dataEntity);
            }

            var convertService = this.Container.GetService<IConvertService>();
            var result = convertService.Push(this.Context, billCvtCtx);
            var convertResult = result.SrvData as ConvertResult;



            var invokeResult = this.Gateway.InvokeBillOperation(this.Context,
                billCvtCtx.TargetFormId,
                convertResult.TargetDataObjects,
                "draft",
                new Dictionary<string, object>());

            if (invokeResult.IsSuccess)
            {

                var dbDataList = this.Context.LoadBizDataByFilter("ydj_order", " exists (select 1 from t_ydj_orderentry odmx where odmx.fid = t_ydj_order.fid and odmx.fentryid in ('{0}') ) ".Fmt(string.Join("','", entryIds)));
                if (dbDataList != null && dbDataList.Any())
                {
                    dbDataList.ForEach(d =>
                    {
                        var entry = d["fentry"] as DynamicObjectCollection;
                        foreach (var item in entry)
                        {
                            if (entryIds.Contains(item["id"].ToString()))
                            {
                                var entrytranspurqty = Convert.ToDecimal(item["ftranspurqty"]);//销售合同明细行已下推采购数
                                var bizQty = Convert.ToDecimal(item["fbizqty"]);//销售数量
                                var transpurqty = Convert.ToDecimal(entryList[item["id"].ToString()]); //本次下推采购数
                                if (entrytranspurqty + transpurqty > bizQty) //已下推采购数+本次下推采购数必须小于等于销售数
                                {
                                    transpurqty = bizQty - entrytranspurqty;
                                }
                                item["ftranspurqty"] = transpurqty + entrytranspurqty;
                                item["flinkpro"] = "已转采购";
                            }
                        }


                        this.Logger.WriteLog(this.Context, new LogEntry()
                        {
                            BillIds = d["Id"]?.ToString(),
                            BillNos = d["fbillno"]?.ToString(),
                            BillFormId = "ydj_order",
                            OpName = "采购",
                            OpCode = "Pushpurorder",
                            Content = "执行了【采购】操作！",
                            DebugData = "执行了【采购】操作！",
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03

                        });

                    });
                    this.Context.SaveBizData("ydj_order", dbDataList);
                }


                var targetForm = this.MetaModelService.LoadFormModel(this.Context, billCvtCtx.TargetFormId);
                this.Result.IsSuccess = true;
                this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}转单采购订单生成成功！");
                foreach (var targetData in convertResult.TargetDataObjects)
                {
                    this.Result.ComplexMessage.SuccessMessages.Add($"{targetForm.Caption}转单采购订单编号【{targetData["fbillno"]}】");
                }
            }
            return count;
        }

        /// <summary>
        /// 城市进行分组
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, List<string>> GetGroupCity(List<string> deptids)
        {
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();
            //var orgin = Context.IsTopOrg ? Context.TopCompanyId : Context.Company;
            string strSql = @"select t2.fmycity,t1.fid from t_bd_department t1
                            join t_bas_store t2 on t1.fstore=t2.fid
                            where t1.fid in ('{0}')".Fmt(string.Join("','", deptids)/*, orgin*/);/* and t1.fforbidstatus='0' and t2.fforbidstatus='0'*/ /*and t2.fmainorgid='{1}'*/
            var dm = Context.Container.GetService<IDBService>();
            var res = dm.ExecuteDynamicObject(Context, strSql);
            var list = res.Where(x => !x["fmycity"].ToString().IsNullOrEmptyOrWhiteSpace()).ToList();

            var isexists = false;
            foreach (var v1 in deptids)
            {
                foreach (var v2 in list)
                {
                    if (v1 == v2["fid"].ToString())
                    {
                        isexists = true;
                        if (v2["fmycity"].ToString().IsNullOrEmptyOrWhiteSpace())
                            AddDicdeliver("无城市", v1, dic);
                        else
                            AddDicdeliver(v2["fmycity"].ToString(), v1, dic);
                        break;
                    }
                }
                if (!isexists)
                    AddDicdeliver("无城市", v1, dic);
                isexists = false;
            }
            return dic;
        }

        /// <summary>
        /// 根据送达方分类
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns>Dictionary<string,string>id,明细行的ID</returns>
        private Dictionary<string, List<string>> GetDeliverByBrandidAndCity(string ctid, List<DynamicObjectCollection> prod)
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "bas_deliver");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));

            var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentAll(Context);
            //组织
            var Agents = AgentInfos.Select(o => o.OrgId).ToList();
            var cid = Context.IsTopOrg ? Context.TopCompanyId : Context.Company;
            Agents.Add(cid);
            var where = @"fforbidstatus='0' and  fcity=@fcity and  fagentid in ('{0}')".Fmt(string.Join("','", Agents));
            // 按 实控人 获取当前用户对应的所有经销商组织

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fcity", System.Data.DbType.String, ctid)
               // new SqlParam("@fagentid", System.Data.DbType.String, Context.Company),
            };
            var reader = Context.GetPkIdDataReader(purForm, where, sqlParam);
            var data = dm.SelectBy(reader).OfType<DynamicObject>();

            //先查出上级城市，如果后面有匹配不出送达方的 需要根据上级城市获取送达方。
            var parentcity = this.Context.Container.GetService<IOrderService>().GetParentCity(this.Context, ctid);
            where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' ".Fmt(string.Join("','", Agents), parentcity);
            var parentdeliver = this.Context.LoadBizDataByFilter("bas_deliver", where);
            //如果 通过深圳市宝安区匹配不到，尝试通过深圳市匹配
            if (data.IsNullOrEmptyOrWhiteSpace() || data.Count() == 0)
            {
                data = parentdeliver;
            }

            //保存送达方对应的商品行分组
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();
            if (data != null && data.Count() != 0)
            {
                //遍历多选全部单据商品
                foreach (var pv in prod)
                {
                    //对单据商品明进行遍历
                    foreach (var fval in pv)
                    {
                        //过滤掉有送达方的商品
                        if (!fval["fshipperdeliver"].IsNullOrEmptyOrWhiteSpace())
                            continue;

                        //记录是否找到送达方
                        var notexists = false;
                        //遍历送达方
                        foreach (var item in data)
                        {
                            //送达方的品牌系列明细行
                            var fentry = item["fentry"] as DynamicObjectCollection;
                            //明细里找对应的系列
                            var isexists = fentry.Any(x => Convert.ToBoolean(x["fenable"]) && x["fserieid"].ToString() == fval["fresultbrandid"].ToString()
                            && !x["fserieid"].ToString().IsNullOrEmptyOrWhiteSpace());
                            if (isexists)
                            {
                                notexists = true;
                                AddDicdeliver(item["id"].ToString(), fval["id"].ToString(), dic);
                                break;
                            }
                        }
                        if (!notexists)
                        {
                            //如果前面没匹配出来则再匹配父级城市的送达方系列
                            foreach (var item in parentdeliver)
                            {
                                //送达方的品牌系列明细行
                                var fentry = item["fentry"] as DynamicObjectCollection;
                                //明细里找对应的系列
                                var isexists = fentry.Any(x => Convert.ToBoolean(x["fenable"]) && x["fserieid"].ToString() == fval["fresultbrandid"].ToString()
                                && !x["fserieid"].ToString().IsNullOrEmptyOrWhiteSpace());
                                if (isexists)
                                {
                                    notexists = true;
                                    AddDicdeliver(item["id"].ToString(), fval["id"].ToString(), dic);
                                    break;
                                }
                            }
                            //经过以上再匹配不到的才属于无送达方的情况
                            if (!notexists)
                            {
                                AddDicdeliver("无送达方", fval["id"].ToString(), dic);
                            }
                        }
                        notexists = false;
                    }
                }
            }
            else//找不到送达方
            {
                //遍历多选全部单据商品
                foreach (var pv in prod)
                {
                    //对单据商品明进行遍历
                    foreach (var fval in pv)
                    {
                        //过滤掉有送达方的商品
                        if (!fval["fshipperdeliver"].IsNullOrEmptyOrWhiteSpace())
                            continue;

                        AddDicdeliver("无送达方", fval["id"].ToString(), dic);
                    }
                }
            }
            return dic;
        }

        /// <summary>
        /// 将送达方对应的业绩品牌商品进行分组
        /// </summary>
        /// <param name="key"></param>
        /// <param name="val"></param>
        /// <param name=""></param>
        private void AddDicdeliver(string key, string val, Dictionary<string, List<string>> dic)
        {
            if (dic.ContainsKey(key))
            {
                List<string> list = dic[key];
                list.Add(val);
                dic[key] = list;
            }
            else
            {
                List<string> list = new List<string>();
                list.Add(val);
                dic[key] = list;
            }
        }

        /// <summary>
        /// 总部商品和非总部商品分类
        /// </summary>
        /// <param name="pids">商品ID集合</param>
        /// <returns></returns>
        private Dictionary<string, bool> DicMainProdect(List<string> pids)
        {
            Dictionary<string, bool> prodlist = new Dictionary<string, bool>();
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));
            var orgin = Context.IsTopOrg ? Context.Company : Context.TopCompanyId;
            var where = "fid in ('{0}') and fmainorgid='{1}'".Fmt(string.Join("','", pids), orgin);
            var reader = this.Context.GetPkIdDataReader(purForm, where, new List<SqlParam>() { });
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>();
            //存主商品
            var mainprodids = new List<string>();
            if (purOrder != null)
                mainprodids = purOrder.Select(x => x["id"].ToString()).ToList();

            //将总部商品加入dic
            mainprodids.ForEach(x => prodlist[x] = true);
            //将非总部的商品加入DIC
            var falseMainProd = pids.Where(x => !mainprodids.Contains(x)).ToList();
            falseMainProd.ForEach(x => prodlist[x] = false);
            return prodlist;
        }

        /// <summary>
        /// 返回没有停购的商品
        /// </summary>
        /// <returns></returns>
        private List<string> ProductNotStopBuying(List<string> prodids, Dictionary<string, string> error, List<DynamicObjectCollection> allProductList)
        {
            var purForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, purForm.GetDynamicObjectType(this.Context));
            var where = "fid in ('{0}')".Fmt(string.Join("','", prodids));
            var reader = this.Context.GetPkIdDataReader(purForm, where, new List<SqlParam> { });
            var purOrder = dm.SelectBy(reader).OfType<DynamicObject>();

            var productMap = purOrder.ToDictionary(
                x => x["id"].ToString(),
                x => new {
                    IsActive = !Convert.ToBoolean(x["fendpurchase"]),
                    Name = x["fname"].ToString()
                });
            var notStoppedSet = new HashSet<string>(
                    productMap.Where(kv => kv.Value.IsActive)
                  .Select(kv => kv.Key));
            var productCheckMap = new Dictionary<string, (bool HasFirstInventory, List<string> NonFirstSeqs)>();
            foreach (var id in prodids.Distinct())
            {
                productCheckMap[id] = (false, new List<string>());
            }


            foreach (var collection in allProductList)
            {
                foreach (var item in collection)
                {
                    var pid = Convert.ToString(item["fproductid"]);
                    if (!productCheckMap.TryGetValue(pid, out var status))
                        continue;

                    if (Convert.ToBoolean(item["fisfromfirstinventory"]))
                    {
                        productCheckMap[pid] = (true, status.NonFirstSeqs);
                    }
                    else
                    {
                        // 收集所有非首次库存的行号
                        var seq = Convert.ToString(item["fseq"]);
                        status.NonFirstSeqs.Add(seq);
                        productCheckMap[pid] = status;
                    }
                }
            }

            var resultSet = new HashSet<string>();
            foreach (var id in prodids.Distinct())
            {
                if (notStoppedSet.Contains(id))
                {
                    resultSet.Add(id);
                    continue;
                }

                var (hasFirstInventory, nonFirstSeqs) = productCheckMap[id];
                if (hasFirstInventory)
                {
                    resultSet.Add(id);
                }
                if (nonFirstSeqs.Count > 0 && productMap.TryGetValue(id, out var product))
                {
                    foreach (var seq in nonFirstSeqs)
                    {
                        error[Guid.NewGuid().ToString("N")] =
                            $"第 {seq} 行 {product.Name} 商品已停购，不允许采购！";
                    }
                }
            }
            return resultSet.ToList();
        }

        /// <summary>
        /// 处理下推客户联系人相关数据
        /// </summary>
        /// <param name="targetDataObjects"></param>
        /// <param name="orders"></param>
        private void DealRenewal(IEnumerable<DynamicObject> targetDataObjects, DynamicObject[] orders)
        {
            var billTypeService = this.Container.GetService<IBillTypeService>();
            foreach (var item in targetDataObjects)
            {
                var frenewalflag = Convert.ToBoolean(item["frenewalflag"]);
                if (frenewalflag)
                {
                    var entrys = (item["fentity"] as DynamicObjectCollection)?.FirstOrDefault();
                    var order = orders.Where(x => Convert.ToString(x["fbillno"]) == Convert.ToString(entrys?["fsourcebillno"])).FirstOrDefault();
                    if (order != null)
                    {
                        var billType = billTypeService.GetBillTypeById(this.Context, Convert.ToString(order["fbilltype"]));
                        if (Convert.ToString(billType["fname"]) == "销售转单" || Convert.ToBoolean(order["fisresellorder"]))
                        {
                            item["fcustomerid"] = Convert.ToString(order["fterminalcustomer"]);
                            item["fconsignee"] = Convert.ToString(order["fcontacts_c"]);
                            item["fphone"] = Convert.ToString(order["fcoophone"]);
                            item["fprovince"] = Convert.ToString(order["fprovince_c"]);
                            item["fcity"] = Convert.ToString(order["fcity_c"]);
                            item["fregion"] = Convert.ToString(order["fregion_c"]);
                            item["faddress"] = Convert.ToString(order["fcooaddress"]);
                        }
                        else
                        {
                            item["fcustomerid"] = Convert.ToString(order["fcustomerid"]);
                            item["fconsignee"] = Convert.ToString((order["fcustomercontactid_ref"] as DynamicObject)["fcontacter"]);
                            item["fphone"] = Convert.ToString(order["fphone"]);
                            item["fprovince"] = Convert.ToString(order["fprovince"]);
                            item["fcity"] = Convert.ToString(order["fcity"]);
                            item["fregion"] = Convert.ToString(order["fregion"]);
                            item["faddress"] = Convert.ToString(order["faddress"]);
                        }
                        //一键采购触发合同生成采购订单时，销售合同的【销售员】需携带至下游采购订单的【采购员】。
                        item["fpostaffid"] = Convert.ToString(order["fstaffid"]);
                    }
                }
            }
        }
    }
}