using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CooIncomedisburse
{
    /// <summary>
    /// 分销商收支记录审核报表：撤销
    /// </summary>
    [InjectService]
    [FormId("rpt_incomedisburse")]
    [OperationNo("operunsubmit")]
    public class OperUnSubmit : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;
            var allCooDatas = this.OperationContext.UserContext.LoadBizDataById("coo_incomedisburse", e.DataEntitys.Select(x => Convert.ToString(x["fcooinid"])));
            var datas = e.DataEntitys.GroupBy(x => x["fagentid"]).Select(x => new { orgid = Convert.ToString(x.Key), ids = x.ToList().Select(y => Convert.ToString(y["fcooinid"])) });
            foreach (var item in datas)
            {
                var cooids = item.ids;
                if (cooids == null || !cooids.Any()) continue;
                var orgContext = this.Context.CreateAgentDBContext(item.orgid);
                var invokeSubmit = this.Gateway.InvokeBillOperation(orgContext,
                                "coo_incomedisburse",
                                allCooDatas.Where(x => Convert.ToString(x["fmainorgid"]).EqualsIgnoreCase(item.orgid)),
                                "unsubmit",
                                new Dictionary<string, object>());
                //invokeSubmit?.ThrowIfHasError(true, "撤销提交收支记录失败！");
                MermgeComplexMessage(orgContext, this.OperationContext.Result, invokeSubmit);
            }
            this.AddRefreshPageAction();
        }
        /// <summary>
        /// 合并invoke消息结果
        /// </summary>
        /// <param name="userCtx">当前操作上下文</param>
        /// <param name="result">当前操作结果</param>
        /// <param name="invokeResult">待合并处理的操作结果</param>
        private static void MermgeComplexMessage(UserContext userCtx, IOperationResult result, IOperationResult invokeResult)
        {
            var companyInfo = userCtx.Companys.FirstOrDefault(x => x.CompanyId.EqualsIgnoreCase(userCtx.Company));
            if (companyInfo == null) return;
            string respSuccMsg = string.Empty;
            string respWarnMsg = string.Empty;
            if (!invokeResult.ComplexMessage.IsNullOrEmptyOrWhiteSpace())
            {
                foreach (var item in invokeResult.ComplexMessage.SuccessMessages)
                {
                    respSuccMsg += item + "\r\n";
                }
                foreach (var item in invokeResult.ComplexMessage.ErrorMessages)
                {
                    respWarnMsg += item + "\r\n";
                }
                foreach (var item in invokeResult.ComplexMessage.WarningMessages)
                {
                    respWarnMsg += item + "\r\n";
                }
            }
            else if (invokeResult.IsSuccess && !invokeResult.SimpleMessage.IsNullOrEmptyOrWhiteSpace())
            {
                respSuccMsg += invokeResult.SimpleMessage + "\r\n";
            }
            else if (!invokeResult.IsSuccess && !invokeResult.SimpleMessage.IsNullOrEmptyOrWhiteSpace())
            {
                respWarnMsg += invokeResult.SimpleMessage + "\r\n";
            }
            if (!respSuccMsg.IsNullOrEmptyOrWhiteSpace())
            {
                result.ComplexMessage.SuccessMessages.Add($"分销商【{companyInfo.CompanyName}】操作成功提醒：\r\n【\r\n{respSuccMsg}】");
            }
            if (!respWarnMsg.IsNullOrEmptyOrWhiteSpace())
            {
                result.ComplexMessage.WarningMessages.Add($"分销商【{companyInfo.CompanyName}】操作异常提醒：\r\n【\r\n{respWarnMsg}】");
                throw new InvokeOperationException($"分销商【{companyInfo.CompanyName}】操作异常提醒：\r\n【\r\n{respWarnMsg}】", result, null);
            }
        }
    }
}
