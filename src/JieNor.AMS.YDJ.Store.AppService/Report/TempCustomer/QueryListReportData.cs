using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Report.TempCustomer
{
    /// <summary>
    /// 下发客户报表
    /// </summary>
    [InjectService]
    [FormId("rpt_tempcustomer")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {


        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {

            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];

            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];

            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
            }
            if ((dtDateFrom == null && dtDateTo != null) || (dtDateFrom != null && dtDateTo == null))
            {
                throw new BusinessException("过滤条件【起始日期必须填写完整】！");
            }

            DateTime? dtDateFrom_delivery = (DateTime?)this.CustomFilterObject["fdatefrom_delivery"];

            DateTime? dtDateTo_delivery = (DateTime?)this.CustomFilterObject["fdateto_delivery"];

            if (dtDateTo_delivery < dtDateFrom_delivery)
            {
                throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
            }
            if ((dtDateFrom_delivery == null && dtDateTo_delivery != null) || (dtDateFrom_delivery != null && dtDateTo_delivery == null))
            {
                throw new BusinessException("过滤条件【起始日期必须填写完整】！");
            }

        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd, out DateTime? dtStart_delivery, out DateTime? dtEnd_delivery)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
            dtStart_delivery = (DateTime?)this.CustomFilterObject["fdatefrom_delivery"];
            if (dtStart_delivery.HasValue)
            {
                dtStart_delivery = dtStart_delivery.Value.DayBegin();
            }
            dtEnd_delivery = (DateTime?)this.CustomFilterObject["fdateto_delivery"];
            if (dtEnd_delivery.HasValue)
            {
                dtEnd_delivery = dtEnd_delivery.Value.DayEnd();
            }
        }
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            //this.CheckDataEnvironment();
            this.GetSaleOrderData();
        }


        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {
            base.OnPrepareReportQueryParameter(listQueryPara);
            listQueryPara.OrderByString = "order by forderdate,fsointerid,fsoentryid,fdatatype";
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        private void GetSaleOrderData()
        {
            var strSql = "/*dialect*/";
            strSql += $@"insert into {this.DataSourceTableName} (fphone,fmemberno)
                                select fphone,fmemberno from t_ydj_tempcustomer";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, strSql, new List<SqlParam>() { });
        }
    }
}
