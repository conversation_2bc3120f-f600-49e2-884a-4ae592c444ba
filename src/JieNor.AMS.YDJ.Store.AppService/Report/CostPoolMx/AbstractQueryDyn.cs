using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CostPoolMx
{
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理基础资料字段过滤条件解析后事件逻辑
        /// </summary>
        /// <param name="e"></param>
        private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData.Item1?.ToLowerInvariant(); //基础资料字段标识
            var fieldFilter = eventData.Item2; //基础资料字段过滤条件
            var formid = this.HtmlForm.Id;
            switch (fieldKey)
            {
                //报表高级搜索不支持前端传入参数，换个思路
                //case "org":
                //    {
                //        var agentid = this.GetQueryOrSimpleParam<string>("agentid", "");
                //        var filter = GetFilter(new List<string>() { agentid });

                //        e.Result = filter;
                //        e.Cancel = true;
                //        break;
                //    }
            }
        }

        private string GetFilter(List<string> agentids) 
        {
            var sql = $@"
            select distinct org.fid,org.fnumber,s.fmodifydate from t_ms_saleschannel_org s
            inner join dbo.t_bas_organization org on org.fnumber = s.forgnumber and org.forgtype in ('1','2') and org.fforbidstatus = 0
            where s.fcustomernumber in ({agentids.JoinEx(",", true)}) and s.fforbidstatus ='0' and (fchannelnumber ='20' OR fchannelnumber ='10' OR fchannelnumber ='50') ";

            var dbService = this.Context.Container.GetService<IDBService>();
            var orgids = dbService.ExecuteDynamicObject(this.Context, sql).Select(o => Convert.ToString(o["fid"])).ToList(); 

            if (orgids.IsNullOrEmptyOrWhiteSpace()) 
            {
                return $" fid in ({orgids.JoinEx(",", true)})";
            } 
            return "";
        }
    }
}
