using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using System.Data.Common;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CostPool
{
    /// <summary>
    /// 费用池查询报表
    /// </summary>
    [InjectService]
    [FormId("rpt_costpool")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
              
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {  
            this.GetSaleOrderData();
        }

        protected void TryGetInventoryPeriodDate(out string dtStart, out string dtEnd)
        {
            if (Convert.ToString(this.CustomFilterObject?["fdatefrom"]).IsNullOrEmptyOrWhiteSpace())
            {
                dtStart = "";
            }
            else
            {
                DateTime d1 = DateTime.Parse(Convert.ToString(this.CustomFilterObject?["fdatefrom"]));
                dtStart = d1.ToString("yyyyMMdd");
            }

            if (Convert.ToString(this.CustomFilterObject?["fdateto"]).IsNullOrEmptyOrWhiteSpace())
            {
                dtEnd = "";
            }
            else
            {
                DateTime d2 = DateTime.Parse(Convert.ToString(this.CustomFilterObject?["fdateto"]));
                dtEnd = d2.ToString("yyyyMMdd");
            }
        }


        private void GetSaleOrderData()
        {

            StringBuilder sbOrderSql = new StringBuilder();
            StringBuilder sbInsertSelectSql = new StringBuilder();
            StringBuilder sbOrderSelectSql = new StringBuilder();
            StringBuilder sbOrderFromSql = new StringBuilder();
            StringBuilder sbOrderWhereSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();

            string dtStart;
            string dtEnd;
            DateTime? dtStart_delivery;
            DateTime? dtEnd_delivery;

            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];

            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];

            sbOrderWhereSql.Append(" where 1=1 ");

            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);

            //经销商
            var agent = this.CustomFilterObject["agent"] as string;

            //销售组织
             var org = this.CustomFilterObject["org"] as string;
            //类型
            var type = this.CustomFilterObject["type"] as string;
            var fdatetime_s = Convert.ToString(this.CustomFilterObject?["fdatefrom"]);
            var fdatetime_e = Convert.ToString(this.CustomFilterObject?["fdateto"]);

            var agentLst = agent?.Split(',');
            if (agentLst !=null && agentLst.Length > 1) {
                throw new BusinessException("售达方只支持单选！");
            }

            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
            }
            if ((dtDateFrom == null && dtDateTo != null) || (dtDateFrom != null && dtDateTo == null))
            {
                throw new BusinessException("过滤条件【起始日期必须填写完整】！");
            }


            var sqlParam = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
            };

            var agentNum = this.Context.LoadBizDataById("bas_agent", agent)?["fnumber"].ToString();
            //822159536671559802,822159536671559804,822159536671559805
            if (org.IsNullOrEmptyOrWhiteSpace()) return;
            var orgLst = org?.Split(',');
            var orgNumLst = this.Context.LoadBizDataByFilter("bas_organization",$"fid in ('{string.Join("','", orgLst)}')").Select(o=>Convert.ToString(o?["fnumber"])).ToList();
            var orgNumObjs = this.Context.LoadBizDataByFilter("bas_organization", $"fid in ('{string.Join("','", orgLst)}')");
            //var orgNum = this.Context.LoadBizDataById("bas_organization", org)?["fnumber"].ToString();
            foreach (var orgNumObj in orgNumObjs) 
            {
                var orgNum = orgNumObj["fnumber"].ToString();
                var orgId = orgNumObj["Id"].ToString();
                //var index = orgNumLst.IndexOf(orgNum);
                //var orgId = orgLst[index];
                var param = new Dictionary<string, object>()
                {
                    { "KUNNR", agentNum },
                    { "FILTER",type},
                    { "VKORG", orgNum }
                };

                if (!dtStart.IsNullOrEmptyOrWhiteSpace()) {
                    param.Add("ZJDATES", dtStart);
                }
                if (!dtEnd.IsNullOrEmptyOrWhiteSpace())
                {
                    param.Add("ZJDATET", dtEnd);
                }
                List<string> errMsg = new List<string>();
                var resp = MuSiApi.GetCostPoolByIncoke(this.Context, this.HtmlForm, param);
                string resultdata = Convert.ToString(resp.data);
                if (!resultdata.IsNullOrEmptyOrWhiteSpace())
                {
                    //返回数据先存在datatable 中
                    var fieldKeys = new string[]
                    {
                            "kUNNR","zFYDL","zDLMS","zBXJE","zSQXJE","zSXZE","zDDZY","zSXYE","zYSYJE"
                    };
                    var dataTable = new DataTable();
                    foreach (var fieldKey in fieldKeys)
                    {
                        dataTable.Columns.Add(fieldKey);
                    }
                    dataTable.BeginLoadData();

                    JArray DataArr = JArray.Parse(resultdata);
                    foreach (var data in DataArr)
                    {
                        //经销商编号
                        var kUNNR = data.GetJsonValue("kUNNR", "");
                        //费用大类
                        var zFYDL = data.GetJsonValue("zFYDL", "");
                        //大类描述
                        var zDLMS = data.GetJsonValue("zDLMS", "");
                        //应冲账金额
                        var zBXJE = data.GetJsonValue("zBXJE", "");
                        //调整金额
                        var zSQXJE = data.GetJsonValue("zSQXJE", "");
                        //方案生效总额
                        var zSXZE = data.GetJsonValue("zSXZE", "");
                        //方案占用总额
                        var zDDZY = data.GetJsonValue("zDDZY", "");
                        //实际可用余额
                        var zSXYE = data.GetJsonValue("zSXYE", "");
                        //原剩余金额
                        var zYSYJE = data.GetJsonValue("zYSYJE", "");
                        dataTable.LoadDataRow(new object[] { kUNNR, zFYDL, zDLMS, zBXJE, zSQXJE, zSXZE, zDDZY, zSXYE, zYSYJE }, true);
                    }
                    dataTable.EndLoadData();
                    if (dataTable.Rows.Count > 0)
                    {
                        using (var trans = this.Context.CreateTransaction())
                        {
                            //将数据存到临时表中
                            var tempTable = this.DBService.CreateTempTableWithDataTable(this.Context, dataTable, 2000,null,null,false);
                            var sql = $@"insert into {this.DataSourceTableName}
                                  (fid,fagent,fdatetime,fexpenseclass,ftotalWriteOff,ftotalEffectiveAmt,ftotalEffective,foccupiedAmt,factAvailableBalance,foriginalRemainingAmt,type,org,fdatetime_s,fdatetime_e)
                                select newid(),{agent},getdate(),zDLMS,zBXJE,zSQXJE,zSXZE,zDDZY,zSXYE,zYSYJE,'{type}','{orgId}','{fdatetime_s}','{fdatetime_e}' from {tempTable} {sbOrderWhereSql}";
                            this.DBService.ExecuteDynamicObject(this.Context, sql);
                            this.DBService.DeleteTempTableByName(this.Context, tempTable, true);

                            trans.Complete();
                        }
                    }
                }
            }
        }
    }
}
