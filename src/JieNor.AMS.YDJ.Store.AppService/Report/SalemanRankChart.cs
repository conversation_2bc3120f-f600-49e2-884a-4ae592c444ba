using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using System.Data;
using JieNor.Framework.IoC;
using System.Dynamic;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.Report
{
    /// <summary>
    /// 报表：销售排名
    /// </summary>
    [InjectService("SalemanRankChart")]
    [FormId("rpt_salesrpt")]
    public class SalemanRankChart : OfficeRptDataService // AbstractQueryDataService
    {
        public override Dictionary<string, string> FilterTitle
        {
            get
            {
                var desc = new Dictionary<string, string>();
                desc.Add("last", DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd"));
                desc.Add("now", DateTime.Now.ToString("yyyy-MM-dd"));
                return desc;
            }
        }

        private string StatType = "saleachievement";
        private string statusType = "type_01";

        protected override IDataReader BuildQueryData(out ListDesc desc)
        {
            //按各自的报表取数要求，编写报表取数的业务逻辑，返回数据。
            string dtStart = this.GetQueryOrSimpleParam<string>("dtStart");//（查询）开始时间
            string dtEnd = this.GetQueryOrSimpleParam<string>("dtEnd");// （查询）结束时间
            string depart = this.GetQueryOrSimpleParam<string>("dept");// 部门
            string khObj = this.GetQueryOrSimpleParam<string>("khObj");// 考核对象：type_01 部门间排名、type_02 部门内人员排名
            string khType = this.GetQueryOrSimpleParam<string>("khType");// 考核维度/类型： check_01 销售业绩排名(默认) 、check_02 商机数排名、成交客户排名、开单数排名
            string[] rankTypes = new string[] { "check_01", "check_02", "check_03", "check_04" };
            string dtType = this.GetQueryOrSimpleParam<string>("dtType");
            //审核状态
            statusType = this.GetQueryOrSimpleParam<string>("statusType");
            if (string.IsNullOrWhiteSpace(dtType)) dtType = "本周";
            string[] dtTimes = ObjectUtils.GetDatetimesByName(dtType);
            if (!string.IsNullOrWhiteSpace(dtStart) || !string.IsNullOrWhiteSpace(dtEnd))
            {
                DateTime dtNow = DateTime.Now;
                if (!string.IsNullOrWhiteSpace(dtStart) && DateTime.TryParse(dtStart, out dtNow))
                    dtTimes[0] = dtStart.Length < 11 ? dtNow.ToString("yyyy-MM-dd 00:00:00") : dtStart;
                if (!string.IsNullOrWhiteSpace(dtEnd) && DateTime.TryParse(dtEnd, out dtNow))
                    dtTimes[1] = dtEnd.Length < 11 ? (dtNow.AddDays(1).ToString("yyyy-MM-dd 00:00:00")) : dtEnd;
            }

            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
            var rptFormId = this.GetQueryOrSimpleParam<string>("rptFormId");
            var dataPermId = this.GetQueryOrSimpleParam<string>("dataPermId");
            var permDepartId = string.Empty;
            var permStaffId = string.Empty;

            if (string.IsNullOrWhiteSpace(rptFormId))
            {
                rptFormId = this.OperationContext.HtmlForm.Id;
            }

            baseFormProvider.CheckMyDataPerms(this.UserCtx, rptFormId, dataPermId, null, out permStaffId, out permDepartId);

            if (false == string.IsNullOrWhiteSpace(permDepartId))
            {
                khObj = "type_02";
                depart = permDepartId;
            }


            if (string.IsNullOrWhiteSpace(khObj)) khObj = "type_01";
            if (string.IsNullOrWhiteSpace(khType) || !rankTypes.Contains(khType)) khType = "check_01";

            var statusCondition = string.Empty;
            //已审核
            if (statusType == "type_02")
            {
                statusCondition = "AND od.fstatus = 'E'";
            }
            else if (statusType == "type_03")
            {
                statusCondition = "AND od.fstatus != 'E'";
            }
            else
            {
                statusCondition = "";
            }


            if (khType == "check_02")
            {
                StatType = "customereceipt";
            }
            else if (khType == "check_03")
            {
                StatType = "turnover";
            }
            else if (khType == "check_04")
            {
                StatType = "billingmount";
            }
            else
            {
                StatType = "saleachievement";//销售业绩排名(默认)
            }
            string sqlSelect = string.Format(@"
select max(x.fname) as 'billingunit','' as 'position',sum(famount) as 'saleachievement',
  (
    select count(distinct fbillno) 
    from t_ydj_customerrecord with(nolock) 
    where fdeptid=x.fdeptid and fcancelstatus='0' 
    and fdutyid in 
    (
        select fid from t_bd_staff with(nolock) 
        where fdeptid=x.fdeptid and fforbidstatus='0'
    ) and fgoshopdate>={0} and fgoshopdate<{1} and fmainorgid='{2}'
  ) as 'customereceipt',
    count(distinct fbillno) as 'billingmount',count(distinct x.fcustomerid) as 'turnover' 
from 
( 
    select od.fid,od.FFormId,od.fbillno,od.fdealamount,od.fcustomerid,ou.famount,od.fdeptid,dp.fname 
    from t_ydj_order od with(nolock) 
    inner join t_ydj_orderduty ou with(nolock) on od.fid=ou.fid
    left join T_BD_department dp with(nolock) on od.fdeptid=dp.fid and dp.fmainorgid='{2}'
    where od.forderdate>={0} and od.forderdate<{1} and od.fmainorgid='{2}' and od.fcancelstatus='0' and dp.fforbidstatus='0'
)x 
group by x.fdeptid",
             "{ts '" + dtTimes[0] + "'}",
             "{ts '" + dtTimes[1] + "'}",
             this.OperationContext.UserContext.Company, statusCondition);

            if (khObj == "type_02")
            {
                sqlSelect = string.Format(@"
select max(saleman) as 'billingunit',min(position) as 'position',count(distinct fbillno) as 'billingmount' ,
  (
    select COUNT(distinct fbillno) 
    from t_ydj_customerrecord c with(nolock) 
    left join t_ydj_customerrecordduty ce with(nolock) on ce.fid=c.fid 
    where ce.fdutyid=x.fdutyid and fcancelstatus='0' and fgoshopdate>={0} and fgoshopdate<{1} and fmainorgid='{3}'
  ) as 'customereceipt',
  sum(famount) as 'saleachievement',count(distinct x.fcustomerid) as 'turnover' 
from 
( 
    select od.fid,od.FFormId,od.fbillno,od.fdealamount,od.fcustomerid,ou.fdutyid,ou.famount,sf.fname as 'saleman',se.fdeptid,p.fname as position
    from t_ydj_order od with(nolock)
    left join t_ydj_orderduty ou with(nolock) on ou.fid=od.fid
    left join t_bd_staff sf with(nolock) on sf.fid=ou.fdutyid and sf.fmainorgid='{3}'
    left join t_bd_staffentry se with(nolock) on se.fid=sf.fid
    left join t_ydj_position p with(nolock) on p.fid=se.fpositionid
    where od.fdeptid='{2}' and od.forderdate>={0} and od.forderdate<{1} and od.fmainorgid='{3}' and od.fcancelstatus='0' and sf.fforbidstatus='0' and p.fforbidstatus='0'
)x 
group by x.fdutyid",
             "{ts '" + dtTimes[0] + "'}",
             "{ts '" + dtTimes[1] + "'}",
             depart, this.OperationContext.UserContext.Company, statusCondition);
            }

            string orderSqlSelect = sqlSelect;
            string sqlCount = string.Format("select count(1) from ( {0} ) xa ", sqlSelect);
            desc = new ListDesc();
            var dbSvc = this.UserCtx.Container.GetService<IDBService>();
            using (var reader = dbSvc.ExecuteReader(this.UserCtx, sqlCount))
            {
                while (reader.Read())
                {
                    desc.CurrentRows = Convert.ToInt64(reader[0]);
                    break;
                }
            }
            orderSqlSelect = string.Format("select * from ( {0} ) aa order by {1} desc", orderSqlSelect, StatType);
            var data = dbSvc.ExecuteReader(this.UserCtx, orderSqlSelect);
            return data;
        }

        private List<Dictionary<string, object>> SetData_Dynamic(string Type)
        {
            ListDesc desc;
            List<Dictionary<string, object>> lstRptRowObjs = new List<Dictionary<string, object>>();
            var reader = BuildQueryData_Dynamic(Type, out desc);
            if (reader != null)
            {
                using (reader)
                {
                    var datas = PackQueryData(reader, desc, ColumnHead.AllColumns);
                    if (datas != null)
                    {
                        lstRptRowObjs = datas.Datas ?? new List<Dictionary<string, object>>();
                    }
                }
            }
            return lstRptRowObjs;
        }

        private IDataReader BuildQueryData_Dynamic(string Type, out ListDesc desc)
        {
            //按各自的报表取数要求，编写报表取数的业务逻辑，返回数据。
            string dtStart = this.GetQueryOrSimpleParam<string>("dtStart");//（查询）开始时间
            string dtEnd = this.GetQueryOrSimpleParam<string>("dtEnd");// （查询）结束时间
            string depart = this.GetQueryOrSimpleParam<string>("dept");// 部门
            string khObj = this.GetQueryOrSimpleParam<string>("khObj");// 考核对象：type_01 部门间排名、type_02 部门内人员排名
            string khType = this.GetQueryOrSimpleParam<string>("khType");// 考核维度/类型： check_01 销售业绩排名(默认) 、check_02 商机数排名、成交客户排名、开单数排名
            string[] rankTypes = new string[] { "check_01", "check_02", "check_03", "check_04" };
            string dtType = this.GetQueryOrSimpleParam<string>("dtType");
            //审核状态
            string statusType = this.GetQueryOrSimpleParam<string>("statusType");
            string[] dtTimes = ObjectUtils.GetDatetimesByName(dtType);
            if (!string.IsNullOrWhiteSpace(dtStart) || !string.IsNullOrWhiteSpace(dtEnd))
            {
                DateTime dtNow = DateTime.Now;
                if (!string.IsNullOrWhiteSpace(dtStart) && DateTime.TryParse(dtStart, out dtNow))
                    dtTimes[0] = dtStart.Length < 11 ? dtNow.ToString("yyyy-MM-dd 00:00:00") : dtStart;
                if (!string.IsNullOrWhiteSpace(dtEnd) && DateTime.TryParse(dtEnd, out dtNow))
                    dtTimes[1] = dtEnd.Length < 11 ? (dtNow.AddDays(1).ToString("yyyy-MM-dd 00:00:00")) : dtEnd;
            }
            var statusCondition = string.Empty;
            //已审核
            if (Type == "YSH")
            {
                statusCondition = "AND od.fstatus = 'E'";
            }
            else if (Type == "WSH")
            {
                statusCondition = "AND od.fstatus != 'E'";
            }
            else
            {
                statusCondition = "";
            }

            string sqlSelect = string.Format(@"select max(x.fname) as 'billingunit','' as 'position',sum(famount) as 'saleachievement',
              (select count(distinct fbillno) from t_ydj_customerrecord where fdeptid=x.fdeptid and fcancelstatus='0' and fdutyid in(select fid from t_bd_staff where fdeptid=x.fdeptid and fforbidstatus='0') and fgoshopdate>={0} and fgoshopdate<{1} and fmainorgid='{2}') as 'customereceipt', 
             count(distinct fbillno) as 'billingmount',count(distinct x.fcustomerid) as 'turnover' from ( 
             select od.fid,od.FFormId,od.fbillno,od.fdealamount,od.fcustomerid,ou.famount 
              ,od.fdeptid,dp.fname from t_ydj_order od with(nolock) inner join t_ydj_orderduty ou with(nolock) on od.fid=ou.fid
	              left join T_BD_department dp with(nolock) on od.fdeptid=dp.fid and dp.fmainorgid='{2}'
	              where od.forderdate>={0} and od.forderdate<{1} and od.fmainorgid='{2}' and od.fcancelstatus='0' and dp.fforbidstatus='0' {3}
	            )x group by x.fdeptid",
             "{ts '" + dtTimes[0] + "'}",
             "{ts '" + dtTimes[1] + "'}",
             this.OperationContext.UserContext.Company, statusCondition);
            if (khObj == "type_02")
                sqlSelect = string.Format(@"
select max(saleman) as 'billingunit',min(position) as 'position',count(distinct fbillno) as 'billingmount' ,
  (
    select COUNT(distinct fbillno) 
    from t_ydj_customerrecord c with(nolock) 
    left join t_ydj_customerrecordduty ce with(nolock) on ce.fid=c.fid 
    where ce.fdutyid=x.fdutyid and fcancelstatus='0' and fgoshopdate>={0} and fgoshopdate<{1} and fmainorgid='{3}'
  ) as 'customereceipt',
  sum(famount) as 'saleachievement',count(distinct x.fcustomerid) as 'turnover' 
from 
(
    select od.fid,od.FFormId,od.fbillno,od.fdealamount,od.fcustomerid,ou.fdutyid,ou.famount,sf.fname as 'saleman',se.fdeptid,p.fname as position
    from t_ydj_order od  with(nolock) 
    left join t_ydj_orderduty ou with(nolock) on ou.fid=od.fid
    left join t_bd_staff sf with(nolock) on sf.fid=ou.fdutyid and sf.fmainorgid='{3}'
    left join t_bd_staffentry se with(nolock) on se.fid=sf.fid
    left join t_ydj_position p with(nolock) on p.fid=se.fpositionid
    where od.fdeptid='{2}' and od.forderdate>={0} and od.forderdate<{1} and od.fmainorgid='{3}' and od.fcancelstatus='0' 
    and sf.fforbidstatus='0' and p.fforbidstatus='0' {4}
) x 
group by x.fdutyid",
             "{ts '" + dtTimes[0] + "'}",
             "{ts '" + dtTimes[1] + "'}",
             depart, this.OperationContext.UserContext.Company, statusCondition);

            string orderSqlSelect = sqlSelect;
            string sqlCount = string.Format("select count(1) from ( {0} ) xa ", sqlSelect);
            desc = new ListDesc();
            var dbSvc = this.UserCtx.Container.GetService<IDBService>();
            using (var reader = dbSvc.ExecuteReader(this.UserCtx, sqlCount))
            {
                while (reader.Read())
                {
                    desc.CurrentRows = Convert.ToInt64(reader[0]);
                    break;
                }
            }
            //根据部门排序 为了防止不同审核状态时 数值问题
            orderSqlSelect = string.Format("select * from ( {0} ) aa order by {1} desc", orderSqlSelect, "billingunit");
            var data = dbSvc.ExecuteReader(this.UserCtx, orderSqlSelect);
            return data;
        }

        protected QueryDataInfo PackQueryData(IDataReader reader, ListDesc desc, List<ColumnObject> cols)
        {
            QueryDataInfo datas = new QueryDataInfo();

            if (cols != null && cols.Count > 0)
            {
                if (IsOffice)
                {
                    datas.OfficeDatas = PackExcelData(reader, cols);
                }
                else
                {
                    datas.Datas = PackReportData(reader, cols);
                }
            }
            datas.DatasDesc = desc;
            datas.DataTitle = this.DataTitle;
            datas.FilterTitle = this.FilterTitle;

            return datas;
        }

        private Dictionary<string, List<object>> PackExcelData(IDataReader reader, List<ColumnObject> cols)
        {
            Dictionary<string, List<object>> lstListData = new Dictionary<string, List<object>>(StringComparer.OrdinalIgnoreCase);
            for (int colIndex = 0; colIndex < cols.Count; colIndex++)
            {
                if (!lstListData.ContainsKey(cols[colIndex].Id))
                {
                    lstListData.Add(cols[colIndex].Id, new List<object>());
                }
            }
            while (reader.Read())
            {
                for (int colIndex = 0; colIndex < cols.Count; colIndex++)
                {
                    var key = cols[colIndex].Id;
                    var value = reader[cols[colIndex].DBFieldName];
                    if (value is DBNull)
                    {
                        value = "";
                    }
                    if (cols[colIndex].ColType == QueryColTypeEnum.Text)
                    {
                        lstListData[key].Add(string.Format("'{0}", value));
                    }
                    else
                    {
                        lstListData[key].Add(value);
                    }
                }
            }

            return lstListData;
        }

        //数据源
        protected override void BuildRptChartDataSource(UserContext userCtx, ReportOperationContext rptOperCtx, ReportDataSource rptDataSource)
        {
            //var orderByList = rptDataSource.RptGridDataSource.ToList().OrderByDescending(d => d[StatType]).ToList();
            var orderByList = SetData_Dynamic("All").ToList().OrderByDescending(d => d[StatType]).ToList();
            //获取未审核的数据
            var orderByList_WSH = SetData_Dynamic("WSH").ToList().OrderByDescending(d => d[StatType]).ToList();
            var orderByList_YSH = SetData_Dynamic("YSH").ToList().OrderByDescending(d => d[StatType]).ToList();
            rptDataSource.ChartDataSource = GetRptChartData(rptOperCtx.HtmlForm.Id, orderByList, orderByList_WSH, orderByList_YSH);
        }

        // 构建报表图表数据源
        protected override void BuildRptChartDataDesc(UserContext userCtx, ReportOperationContext rptOperCtx, ReportModelDesc rptModelDesc)
        {
            var chartDesc = new Dictionary<string, RptChartDesc>();
            List<string> legendData = new List<string>();
            if (rptModelDesc != null && rptModelDesc.RptGridModel != null)
            {
                int colCount = 0;
                foreach (var col in rptModelDesc.RptGridModel.AllColumns)
                {
                    if (colCount > 0)
                    {
                        legendData.Add(col.Id);
                    }
                    colCount++;
                }
            }
            chartDesc.Add(rptOperCtx.HtmlForm.Id, new RptChartDesc()
            {
                type = 1,
                title = "柱状图",
                Option = new
                {
                    title = new
                    {
                        text = OperationContext.FormCaption,//图表表名
                        subtext = "折线图"//图表描述，在表名的下方
                    },
                    legend = new
                    {
                        data = legendData.ToArray() //趋势类别：有多少条线，就有多少个
                    },
                    xAxis = new
                    {
                        type = "category",
                        data = new object() // 统计归类字段（时间刻度 yyyy-MM-dd）
                    },
                    yAxis = new
                    {
                        type = "value",
                        axisLabel = new
                        {
                            formatter = "{value} "
                        }
                    },
                    series = new object() // 趋势数据集
                }
            });
            if (rptModelDesc.Charts == null || rptModelDesc.Charts.Count < 1) rptModelDesc.Charts = chartDesc;
        }

        private Dictionary<string, RptChartInfo> GetRptChartData(string chartId, List<Dictionary<string, object>> chartData, List<Dictionary<string, object>> chartData_WSH, List<Dictionary<string, object>> chartData_YSH)
        {
            Dictionary<string, RptChartInfo> result = new Dictionary<string, RptChartInfo>();
            List<string> xAxis = new List<string>();
            List<string> yAxis = new List<string>();

            List<string> legendData = new List<string>();
            List<dynamic> serieData = new List<dynamic>();// 客户数/订单数/订单金额/客单价
            List<Dictionary<string, object>> dynamicData = chartData;
            //应对不同情况报表插件列与数据对不上的问题
            if (statusType == "type_02")
            {
                dynamicData = chartData_YSH;
            }
            else if (statusType == "type_03")
            {
                dynamicData = chartData_WSH;
            }
            //这里X轴只能定义固定的有已审核、未审核数据的部门
            for (int x = 0; x < dynamicData.Count; x++)
            {
                xAxis.Add(dynamicData[x]["billingunit"].ToString()); // x 门店
                yAxis.Add(dynamicData[x][StatType].ToString());


                #region 统计数据维度 legend/series data
                if (x < 1)
                {
                    //已审核
                    if (statusType == "type_02")
                    {
                        dynamic series = new ExpandoObject();
                        series.name = "已审核";
                        series.type = "bar";
                        series.stack = 'x';
                        series.itemStyle = new
                        {
                            normal = new
                            {
                                color = "#C23531"
                            }
                        };
                        series.data = chartData_YSH.Select(c => c[StatType]).ToArray();
                        serieData.Add(series);
                        legendData.Add("已审核");
                    }
                    else if (statusType == "type_03")
                    {
                        dynamic series = new ExpandoObject();
                        series.name = "未审核";
                        series.type = "bar";
                        series.stack = 'x';
                        series.itemStyle = new
                        {
                            normal = new
                            {
                                color = "#2f4554"
                            }
                        };
                        series.data = chartData_WSH.Select(c => c[StatType]).ToArray();
                        serieData.Add(series);
                        legendData.Add("未审核");
                    }
                    else
                    {
                        List<decimal> Data_YSH = new List<decimal>();
                        List<decimal> Data_WSH = new List<decimal>();
                        for (int i = 0; i < chartData.Count; i++)
                        {
                            //获取门店
                            var billingunit = chartData[i]["billingunit"].ToString();
                            decimal value_YSH = 0;
                            decimal value_WSH = 0;
                            for (int j = 0; j < chartData_YSH.Count; j++)
                            {
                                if (chartData_YSH[j]["billingunit"].ToString() == billingunit)
                                {
                                    value_YSH = Convert.ToDecimal(chartData_YSH[j][StatType]);
                                }
                            }
                            for (int k = 0; k < chartData_WSH.Count; k++)
                            {
                                if (chartData_WSH[k]["billingunit"].ToString() == billingunit)
                                {
                                    value_WSH = Convert.ToDecimal(chartData_WSH[k][StatType]);
                                }
                            }
                            Data_YSH.Add(value_YSH);
                            Data_WSH.Add(value_WSH);
                            //if (chartData_YSH.Count-1 > i && !string.IsNullOrEmpty(Convert.ToString(chartData_YSH[i]["billingunit"])) && Convert.ToString(chartData_YSH[i]["billingunit"]) == billingunit)
                            //{
                            //    Data_YSH.Add(chartData_YSH[i][StatType].ToString());
                            //}
                            //else
                            //{
                            //    Data_YSH.Add("0");
                            //}
                            //if (!chartData_WSH.Select(c => c["billingunit"]).Contains(billingunit))
                            //{
                            //    Data_WSH.Add("0");
                            //}
                            //else
                            //{
                            //    Data_WSH.Add(chartData_WSH[i][StatType].ToString());
                            //}
                        }
                        dynamic series = new ExpandoObject();
                        series.name = "已审核";
                        series.type = "bar";
                        series.stack = 'x';
                        series.itemStyle = new
                        {
                            normal = new
                            {
                                color = "#C23531"
                            }
                        };

                        //series.data = chartData_YSH.Select(c => c[StatType]).ToArray();
                        series.data = Data_YSH;
                        serieData.Add(series);
                        legendData.Add("已审核");

                        series = new ExpandoObject();
                        series.name = "未审核";
                        series.type = "bar";
                        series.stack = 'x';
                        series.itemStyle = new
                        {
                            normal = new
                            {
                                color = "#2f4554"
                            }
                        };
                        //series.data = chartData_WSH.Select(c => c[StatType]).ToArray();
                        series.data = Data_WSH;
                        serieData.Add(series);
                        legendData.Add("未审核");
                    }
                    //dynamic series = new ExpandoObject();
                    //series.name = this.ColumnHead.AllColumns.Where(c => c.DBFieldName == StatType).First().Caption;
                    //series.type = "bar";
                    //series.stack = 'x';
                    //series.label = new
                    //{
                    //    normal = new
                    //    {
                    //        show = true,
                    //        position = "top"
                    //    }
                    //};
                    //series.data = chartData.Select(c => c[StatType]).ToArray();
                    //serieData.Add(series);
                    //if (legendData.Count < chartData.Count)
                    //{
                    //    legendData.Add(this.ColumnHead.AllColumns.Where(c => c.DBFieldName == StatType).First().Caption);
                    //}
                    //未审核

                    //int colCount = 0;
                    //foreach (var dic in tmpData)
                    //{
                    //    if (colCount > 0)
                    //    {
                    //        dynamic series = new ExpandoObject();
                    //        series.name = this.ColumnHead.AllColumns.Where(c => c.DBFieldName == dic.Key).First().Caption;
                    //        series.type = "bar";
                    //        series.data = chartData.Select(c => c[dic.Key]).ToArray();
                    //        serieData.Add(series);
                    //        if (legendData.Count < chartData.Count - 1)
                    //        {
                    //            legendData.Add(series.name);
                    //        }
                    //    }
                    //    colCount++;
                    //}
                }
                #endregion
            }
            result.Add(chartId, new RptChartInfo()
            {
                categoryaxis = string.Join(",", xAxis.ToArray()),
                valueaxis = string.Join(",", yAxis.ToArray()),
                title = "柱状图",
                type = 1,
                Option = new
                {
                    title = new
                    {
                        text = this.OperationContext.FormCaption,//图表表名
                        subtext = ""//图表描述，在表名的下方
                    },
                    legend = new
                    {
                        data = legendData.ToArray() //趋势类别：有多少条线，就有多少个
                    },
                    xAxis = new
                    {
                        type = "category",
                        data = xAxis.ToArray() // 统计归类字段（时间刻度 yyyy-MM-dd）
                    },
                    yAxis = new
                    {
                        type = "value",
                        axisLabel = new
                        {
                            formatter = "{value} "
                        }
                    },
                    series = serieData // 趋势数据集
                }
            });
            return result;
        }
    }
}
