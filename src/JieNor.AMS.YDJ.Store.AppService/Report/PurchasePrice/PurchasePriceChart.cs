using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.Report.PurchasePrice
{
    /// <summary>
    /// 报表：总部出厂价
    /// </summary>
    [InjectService("PurchasePriceChart")]
    [FormId("rpt_purchaseprice")]
    public class PurchasePriceChart : OfficeRptDataService
    {
        protected override IDataReader BuildQueryData(out ListDesc desc)
        {
            desc = new ListDesc();
            var dbSvc = this.UserCtx.Container.GetService<IDBService>();

            string fproductnumber = this.GetQueryOrSimpleParam<string>("fproductnumber"); // 商品编码

            var productId = QueryProductId(fproductnumber);
            if (productId.IsNullOrEmptyOrWhiteSpace())
            {
                desc.CurrentRows = 0;

                return dbSvc.ExecuteReader(this.UserCtx, "select 1 from t_ydj_purchaseprice with(nolock) where 1<>1");
            }

            string sqlSelect = $@"
select
	pe.fproductid_e as fproductid
	,m.fnumber as fproductnumber
	,m.fname as fproductname
	,pe.fpurprice
	,pe.fstartdate_e as fstartdate
	,pe.fexpiredate_e as fexpiredate
	,c.fname as fcategoryname
	,b.fname as fbrandname
	,u.fname as funitname
    ,s.fname as fseriesname
from t_ydj_purchaseprice p with(nolock) 
inner join t_ydj_purchasepriceentry pe with(nolock) on p.fid=pe.fid
inner join t_bd_material m with(nolock) on pe.fproductid_e=m.fid
left join ser_ydj_category c with(nolock) on m.fcategoryid=c.fid
left join t_ydj_brand b with(nolock) on m.fbrandid=b.fid
left join t_ydj_unit u with(nolock) on pe.funitid_e=u.fid
left join t_ydj_series s with(nolock) on m.fseriesid=s.fid
where p.fmainorgid='{this.UserCtx.TopCompanyId}' and (p.fforbidstatus='0' or p.fforbidstatus='') and pe.fproductid_e='{productId}'
";

            string sqlCount = $@"
select COUNT(1)
from t_ydj_purchaseprice p with(nolock) 
inner join t_ydj_purchasepriceentry pe with(nolock) on p.fid=pe.fid
inner join t_bd_material m with(nolock) on pe.fproductid_e=m.fid
where p.fmainorgid='{this.UserCtx.TopCompanyId}' and (p.fforbidstatus='0' or p.fforbidstatus='') and pe.fproductid_e='{productId}'
";
            using (var reader = dbSvc.ExecuteReader(this.UserCtx, sqlCount))
            {
                if (reader.Read())
                {
                    desc.CurrentRows = Convert.ToInt64(reader[0]) > 0 ? 1 : 0;
                }
            }

            var data = dbSvc.ExecuteReader(this.UserCtx, sqlSelect);
            return data;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productNumber"></param>
        /// <returns></returns>
        private string QueryProductId(string productNumber)
        {
            if (productNumber.IsNullOrEmptyOrWhiteSpace()) return null;

            HtmlForm productForm = this.MetaModelService.LoadFormModel(this.UserCtx, "ydj_product");

            var filterList = new List<FilterRowObject>();
            filterList.Add(new FilterRowObject("fnumber", "=", productNumber));

            SqlBuilderParameter queryParam = new SqlBuilderParameter(this.UserCtx, productForm.Id);
            queryParam.PageIndex = -1;
            queryParam.PageCount = -1;
            queryParam.SelectedFieldKeys.Add(productForm.BillPKFldName);
            queryParam.SetFilter(filterList);
            var queryObj = QueryService.BuilQueryObject(queryParam);

            var sqlText = $@"{queryObj.SqlSelect} {queryObj.SqlFrom} {queryObj.SqlWhere}";

            using (var reader = this.Container.GetService<IDBService>().ExecuteReader(this.UserCtx, sqlText, queryParam.DynamicParams))
            {
                if (reader.Read())
                {
                    var pkId = Convert.ToString(reader["fbillhead_id"]);

                    return pkId;
                }
            }

            return null;
        }

    }
}
