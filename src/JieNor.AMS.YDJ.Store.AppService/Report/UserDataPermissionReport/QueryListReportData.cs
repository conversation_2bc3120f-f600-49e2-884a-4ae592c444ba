using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using JieNor.AMS.YDJ.SWJ.API.Request.SWJ;
using JieNor.AMS.YDJ.SWJ.API.Response.SWJ;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Report.UserDataPermissionReport
{
    [InjectService]
    [FormId("rpt_userdatapermissionreport")]
    [OperationNo("QueryListReportData")]
    class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 用户数据权限报表,执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.ProfitListData();

        }
        protected void ProfitListData()
        {

            StringBuilder sbInsertSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();
            StringBuilder sbFromSql = new StringBuilder();
            StringBuilder sbWhereSql = new StringBuilder();

            //业务对象 多个
            var fnmenugroupame = this.CustomFilterObject["fnmenugroupame"] as string;
            var fnmenugroupameArray = fnmenugroupame?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //用户 多个 模糊查询
            //var fphone = this.CustomFilterObject["fuserphone"] as string;
            var fuserid = this.CustomFilterObject["fuserid"] as string;
            var fuseridArray = fuserid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var sqlParam = new List<SqlParam>
            {
              new SqlParam("@fmainorgid",System.Data.DbType.String, this.Context.Company),
              new SqlParam("@fuserid",System.Data.DbType.String,fuserid),
              new SqlParam("@fnmenugroupame",System.Data.DbType.String,fnmenugroupame)
            };
            sbWhereSql.Append(" where  u1.fmainorgid=@fmainorgid");

            if (fuserid.IsEmptyPrimaryKey() && fnmenugroupame.IsEmptyPrimaryKey())
            {
                sbWhereSql.Append("  and u1.fid=null and acl.fbizobjid=null ");
            }
            if (fuserid != null && fuseridArray.Length > 0)
            {
                sqlParam.AddRange(fuseridArray.Select((x, i) =>
                   new SqlParam($"@fuserid{i}", System.Data.DbType.String, x)));
                sbWhereSql.Append(string.Format(" and  u1.fid in ({0}) ",
                    string.Join(",", fuseridArray.Select((x, i) => $"@fuserid{i}"))));
            }
            if (fnmenugroupameArray != null && fnmenugroupameArray.Length > 0)
            {
                sqlParam.AddRange(fnmenugroupameArray.Select((x, i) =>
                    new SqlParam($"@fnmenugroupame{i}", System.Data.DbType.String, x)));
                sbWhereSql.Append(string.Format(" and  acl.fbizobjid in ({0}) ",
                    string.Join(",", fnmenugroupameArray.Select((x, i) => $"@fnmenugroupame{i}"))));
            }

            sbSelectSql.Append($@"select distinct u1.fid fuserid,u1.fphone fuserphone,
                                u2.fid froleids,
                                (
								select top 1 t3.fname from t_sys_menuitem t1 with(nolock)
								inner join t_sys_menugroup t2 with(nolock) on t1.fgroupid=t2.fgroupid
								inner join t_sys_bizmodule t3 with(nolock) on t2.fmoduleid=t3.fmoduleid
								where acl.fbizobjid= t1.fbillformid
								) fbizmodulename,
                                acl.fbizobjid fnmenugroupame,(
	                            case 
	                            when acl.ffiltertype='all' then '全部' 
	                            when acl.ffiltertype='fcurrentuser' then '本用户' 
	                            when acl.ffiltertype='fcurrentstaff' then '本员工' 
	                            when acl.ffiltertype='fmydepartment' then '本部门' 
	                            when acl.ffiltertype='fmyandsubdepartment' then '本部门及下属部门' 
	                            else '' 
	                            end) + (case when fsuperiorperm='1' then ';上级可以查看所属下级的数据' else '' end) flistdatarange,
                                isnull(bacl.fid, '') fbilldataacl");

            sbFromSql.Append($@" from t_sec_roleuser t with(nolock) 
                               inner join t_sec_user u1  with(nolock) on t.fuserid=u1.fid
                               inner join t_sec_role u2  with(nolock) on t.froleid=u2.fid and (u1.fmainorgid=u2.fmainorgid or u2.fmainorgid='0' or u2.fmainorgid='' or u2.fmainorgid=' ') 
                               inner join t_sec_roledatarowacl acl with(nolock) on t.froleid=acl.froleid
                               left join t_bd_staff bs with(nolock) on  bs.flinkuserid=u1.fid or (bs.fphone=u1.fphone and bs.fname=u1.fname)
							   left join t_bd_staffentry bst with(nolock) on bst.fid=bs.fid
                               left join t_bas_billdataacl bacl  with(nolock) on bacl.fbillformid=acl.fbizobjid and bacl.fmainorgid=u1.fmainorgid and bacl.fforbidstatus='0'  and (bacl.fctlobjectid=bs.fid or bacl.fctlobjectid=bst.fdeptid)");

            sbInsertSql.Append($@"insert into {this.DataSourceTableName}
                               (fid,fuserid,fuserphone,froleids,fbizmodulename,fnmenugroupame,flistdatarange,fbilldataacl)");

            var selectDataSql = $@"select row_number() over(order by fuserid,froleids,fbizmodulename,fnmenugroupame,fbilldataacl desc) as fid,fuserid,fuserphone,froleids,fbizmodulename,fnmenugroupame,flistdatarange,fbilldataacl from
                                  ({sbSelectSql.ToString()}{sbFromSql.ToString()}{sbWhereSql.ToString()}) as subquery where flistdatarange!='' or fbilldataacl!=''";

            var strSql = $@"{sbInsertSql.ToString()}{selectDataSql.ToString()}";

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            var affecteds = dbServiceExt.Execute(this.Context, strSql, sqlParam);

        }

    }
}
