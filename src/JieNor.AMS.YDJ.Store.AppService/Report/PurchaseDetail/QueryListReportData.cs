using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Report.PurchaseDetail
{
    /// <summary>
    /// 采购订单收发明细表
    /// </summary>
    [InjectService]
    [FormId("rpt_purchasedetail")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {


        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
           
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
           
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];            

            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
            }
            if ((dtDateFrom == null && dtDateTo != null)|| (dtDateFrom != null && dtDateTo == null))
            {
                throw new BusinessException("过滤条件【起始日期必须填写完整】！");
            }

        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            this.GetSaleOrderData();
        }

        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {
            base.OnPrepareReportQueryParameter(listQueryPara);
            listQueryPara.OrderByString = "order by forderdate,fsointerid,fsoentryid,fdatatype";
        }
        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>


        private void GetSaleOrderData()
        {

            StringBuilder sbOrderSql = new StringBuilder();
            StringBuilder sbInsertSelectSql = new StringBuilder();
            StringBuilder sbOrderSelectSql = new StringBuilder();
            StringBuilder sbOrderFromSql = new StringBuilder();
            StringBuilder sbOrderWhereSql = new StringBuilder();

            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);


            //供应商 多个
            var fsupplierid = this.CustomFilterObject["fsupplierid"] as string;
            var fsupplieridArray = fsupplierid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //商品 多个
            var fmaterialid = this.CustomFilterObject["fmaterialid"] as string;
            var fmaterialidArray = fmaterialid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //合同编号 模糊查询
            var fbillno = this.CustomFilterObject["fbillno"] as string;

            //显示执行明细
            var fimplementdetail = this.CustomFilterObject["fimplementdetail"] as string;

            var sqlParam = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
              new SqlParam("@fcancelstatus", DbType.String, "0"),
              new SqlParam("@fproductid",System.Data.DbType.String,fmaterialid),
              new SqlParam("@fcustomerid",System.Data.DbType.String,fsupplierid),
              new SqlParam("@fbillno",System.Data.DbType.String,fbillno),
              new SqlParam("@fdatefrom",System.Data.DbType.DateTime,dtStart),
              new SqlParam("@fdateto",System.Data.DbType.DateTime,dtEnd)
            };

            sbOrderWhereSql.Append(" where p.fmainorgid=@fmainorgid and p.fcancelstatus=@fcancelstatus");

            //如果日期不为空
            if (dtStart != null && dtEnd != null)
            {
                sbOrderWhereSql.Append(" and p.fdate between @fdatefrom  and @fdateto ");
            }

            if (fmaterialidArray != null && fmaterialidArray.Length > 0)
            {
                sqlParam.AddRange(fmaterialidArray.Select((x, i) => new SqlParam($"@fmaterialid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.Append(string.Format(" and po.fmaterialid in ({0}) ", string.Join(",", fmaterialidArray.Select((x, i) => $"@fmaterialid{i}"))));
            }
            if (fsupplieridArray != null && fsupplieridArray.Length > 0)
            {

                sqlParam.AddRange(fsupplieridArray.Select((x, i) => new SqlParam($"@fsupplierid{i}", System.Data.DbType.String, x)));
                sbOrderWhereSql.Append(string.Format(" and p.fsupplierid in ({0}) ", string.Join(",", fsupplieridArray.Select((x, i) => $"@fsupplierid{i}"))));
            }
            if (fbillno != null)
            {
                sbOrderWhereSql.Append(string.Format(" and p.fbillno like '%{0}%' ", @fbillno));
            }

            sbOrderSelectSql.Append(@"select p.fdate forderdate,p.fid fsointerid,po.fentryid fsoentryid,'1' fdatatype,p.fbillno fbillno,p.fbillno purchasefbillno,
p.FFormId fbillformid,p.fbilltypeid fbilltype,p.fdate fbilldate,
p.fsupplierid fsupplierid,p.ffbillamount ffbillamount,po.fmaterialid fmaterialid,po.fbizqty fbizqty,po.fdealprice fdealprice,po.fdealamount fdealamount,
po.fbizinstockqty fbizinstockqty,(po.fbizinstockqty*po.fdealprice) fbizoutamount,
(po.fbizrefundqty+po.fbizreturnqty) fbizrefundqty,(po.fdealprice*(po.fbizrefundqty+po.fbizreturnqty)) freturnamount,
'' fstorehouseid,'' fstorelocationid,'' fstockstatus");
            sbOrderFromSql.Append(@" from t_ydj_purchaseorder p
left join t_ydj_poorderentry po on p.fid=po.fid
");

            sbInsertSelectSql.Append($@"insert into {this.DataSourceTableName}(fid,forderdate,fsointerid,fsoentryid,fdatatype,fbillno,purchasefbillno,
fbillformid,fbilltype,fbilldate,fsupplierid,ffbillamount,
fmaterialid,fbizqty,fdealprice,fdealamount,fbizinstockqty,fbizoutamount,fbizrefundqty,freturnamount,fstorehouseid,fstorelocationid,fstockstatus)");

            if (fimplementdetail == "1")
            {
                sbOrderSql.Append($@"
select row_number() over(order by fbillno desc) as fid, forderdate,
(case when(fsointerid is null) then '' else fsointerid end) as fsointerid,
(case when(fsoentryid is null) then '' else fsoentryid end) as fsoentryid,
fdatatype,
(case when(fbillno is null) then '' else fbillno end) as fbillno,
(case when(purchasefbillno is null) then '' else purchasefbillno end) as purchasefbillno,
(case when(fbillformid is null) then '' else fbillformid end) as fbillformid,
(case when(fbilltype is null) then '' else fbilltype end) as fbilltype,
fbilldate,
(case when(fsupplierid is null) then '' else fsupplierid end) as fsupplierid,
ffbillamount,
(case when(fmaterialid is null) then '' else fmaterialid end) as fmaterialid,
(case when(fbizqty is null) then 0 else fbizqty end) as fbizqty,
(case when(fdealprice is null) then 0 else fdealprice end) as fdealprice,
(case when(fdealamount is null) then 0 else fdealamount end) as fdealamount,
(case when(fbizinstockqty is null) then 0 else fbizinstockqty end) as fbizinstockqty,
(case when(fbizoutamount is null) then 0 else fbizoutamount end) as fbizoutamount,
(case when(fbizrefundqty is null) then 0 else fbizrefundqty end) as fbizrefundqty,
(case when(freturnamount is null) then 0 else freturnamount end) as freturnamount,
(case when(fstorehouseid is null) then '' else fstorehouseid end) as fstorehouseid,
(case when(fstorelocationid is null) then '' else fstorelocationid end) as fstorelocationid,
(case when(fstockstatus is null) then '' else fstockstatus end) as fstockstatus
from (
select p.fdate forderdate,p.fid fsointerid,po.fentryid fsoentryid,'1' fdatatype,p.fbillno fbillno,p.fbillno purchasefbillno,p.FFormId fbillformid,p.fbilltypeid fbilltype,
p.fdate fbilldate,p.fsupplierid fsupplierid,p.ffbillamount ffbillamount,po.fmaterialid fmaterialid,po.fbizqty fbizqty,po.fdealprice fdealprice,po.fdealamount fdealamount,
fbizinstockqty=(select sum(st.fbizqty) from t_stk_postockin s left join t_stk_postockinentry st on po.fentryid=st.fpoorderentryid where s.fid=st.fid and po.fmaterialid=st.fmaterialid),
fbizoutamount=(select sum(st.famount) from t_stk_postockin s left join t_stk_postockinentry st on po.fentryid=st.fpoorderentryid where s.fid=st.fid and po.fmaterialid=st.fmaterialid),
fbizrefundqty=(select sum(st.fbizqty) from t_stk_postockreturn so left join t_stk_postockreturnentry st on po.fentryid=st.fpoorderentryid where so.fid=st.fid and po.fmaterialid=st.fmaterialid),
freturnamount=(select sum(st.famount) from t_stk_postockreturn so left join t_stk_postockreturnentry st on po.fentryid=st.fpoorderentryid where so.fid=st.fid and po.fmaterialid=st.fmaterialid),
'' fstorehouseid,'' fstorelocationid,'' fstockstatus
{sbOrderFromSql.ToString()}{sbOrderWhereSql.ToString()}
union all
select 
p.fdate forderdate,st.fpoorderinterid fsointerid,st.fpoorderentryid fsoentryid,'2' fdatatype,s.fbillno fbillno,p.fbillno purchasefbillno,
s.FFormId fbillformid,s.fbilltype fbilltype,s.fdate fbilldate,s.fsupplierid fsupplierid,
p.ffbillamount ffbillamount,po.fmaterialid fmaterialid,po.fbizqty fbizqty,po.fdealprice fdealprice,po.fdealamount fdealamount,
st.fbizqty fbizinstockqty,st.famount fbizoutamount,0 fbizrefundqty,0 freturnamount,
st.fstorehouseid fstorehouseid,st.fstorelocationid fstorelocationid,st.fstockstatus fstockstatus
from t_ydj_purchaseorder p
left join t_ydj_poorderentry po on p.fid=po.fid
left join t_stk_postockinentry st on po.fentryid=st.fpoorderentryid
left join  t_stk_postockin s on s.fid=st.fid
inner join ({sbOrderSelectSql.ToString()}{sbOrderFromSql.ToString()}{sbOrderWhereSql.ToString()}) u3 on po.fentryid=u3.fsoentryid 
where u3.fmaterialid=st.fmaterialid
union all
select 
p.fdate forderdate,st.fpoorderinterid fsointerid,st.fpoorderentryid fsoentryid,'3' fdatatype,s.fbillno fbillno,p.fbillno purchasefbillno,
s.FFormId fbillformid,s.fbilltype fbilltype,s.fdate fbilldate,s.fsupplierid fsupplierid,
p.ffbillamount ffbillamount,po.fmaterialid fmaterialid,0 fbizqty,0 fdealprice,0 fdealamount,0 fbizinstockqty,0 fbizoutamount,
st.fbizqty  fbizrefundqty,
st.famount  freturnamount,
st.fstorehouseid fstorehouseid,st.fstorelocationid fstorelocationid,st.fstockstatus fstockstatus
from t_ydj_purchaseorder p
left join t_ydj_poorderentry po on p.fid=po.fid 
left join t_stk_postockreturnentry st on po.fentryid=st.fpoorderentryid
left join  t_stk_postockreturn s on  s.fid=st.fid
inner join ({sbOrderSelectSql.ToString()}{sbOrderFromSql.ToString()}{sbOrderWhereSql.ToString()}) u4 on  po.fentryid=u4.fsoentryid 
where u4.fmaterialid=st.fmaterialid 
) u1
");
            }
            else
            {
                sbOrderSql.Append($@"select row_number() over(order by fbillno desc) as fid, forderdate,
(case when(fsointerid is null) then '' else fsointerid end) as fsointerid,
(case when(fsoentryid is null) then '' else fsoentryid end) as fsoentryid,
fdatatype,
(case when(fbillno is null) then '' else fbillno end) as fbillno,
(case when(purchasefbillno is null) then '' else purchasefbillno end) as purchasefbillno,
(case when(fbillformid is null) then '' else fbillformid end) as fbillformid,
(case when(fbilltype is null) then '' else fbilltype end) as fbilltype,
fbilldate,
(case when(fsupplierid is null) then '' else fsupplierid end) as fsupplierid,
ffbillamount,(case when(fmaterialid is null) then '' else fmaterialid end) as fmaterialid,
(case when(fbizqty is null) then 0 else fbizqty end) as fbizqty,
(case when(fdealprice is null) then 0 else fdealprice end) as fdealprice,
(case when(fdealamount is null) then 0 else fdealamount end) as fdealamount,
(case when(fbizinstockqty is null) then 0 else fbizinstockqty end) as fbizinstockqty,
(case when(fbizoutamount is null) then 0 else fbizoutamount end) as fbizoutamount,
(case when(fbizrefundqty is null) then 0 else fbizrefundqty end) as fbizrefundqty,
(case when(freturnamount is null) then 0 else freturnamount end) as freturnamount,
(case when(fstorehouseid is null) then '' else fstorehouseid end) as fstorehouseid,
(case when(fstorelocationid is null) then '' else fstorelocationid end) as fstorelocationid,
(case when(fstockstatus is null) then '' else fstockstatus end) as fstockstatus
from ({sbOrderSelectSql.ToString()}{sbOrderFromSql.ToString()}{sbOrderWhereSql.ToString()}) u1");
            }

            var strSql = $@"{sbInsertSelectSql.ToString()}{sbOrderSql.ToString()}";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, strSql, sqlParam);


        }






    }
}
