using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.SuperOrm.Serialization;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.StoreSalesConversionRate
{
    [InjectService]
    [FormId("rpt_storesalesconversionrate")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 库存访问服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【起始日期】必录！");
            }
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【结束日期】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【结束日期】不能小于【开始日期】！");
            }
        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            this.ProfitListData();
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        protected void ProfitListData()
        {
            DateTime? dtStart;
            DateTime? dtEnd;
            this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);

            if (dtStart.IsNullOrEmptyOrWhiteSpace() || dtEnd.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }
            //门店过滤条件
            var fstoreid = this.CustomFilterObject["fstoreid"] as string;
            var fstoreidArray = fstoreid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var sqlParams = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

            var filterBuilder = new StringBuilder();
            if (fstoreidArray != null && fstoreidArray.Length > 0)
            {
                builSqlAndAddParams(fstoreidArray, "fstoreid", filterBuilder, sqlParams);
            }
            var companyBuild = new StringBuilder();
            companyBuild.Append("fmainorgid = @fmainorgid");
            //在本期范围内新增的商机数汇总(含作废+关闭)的商机 过滤条件
            var custincreasesql = $@"select count(fdeptid) as fcustincreaseqty,fdeptid from ( 
                                select fdeptid from t_ydj_customerrecord where {companyBuild} 
                                and fgoshopdate >= @fdatefrom and fgoshopdate < @fdateto )t1 group by fdeptid";
            //本期内无效关闭或作废的商机数量汇总（按作废或关闭的时间计算）
            var custinvalidsql = $@"select count(fdeptid) as fcustinvalidqty,fdeptid from (
                               select fdeptid from t_ydj_customerrecord where {companyBuild} 
                               and (fcanceldate >= @fdatefrom and fcanceldate < @fdateto and fcancelstatus ='1')
                               or (fclosedate >= @fdatefrom and fclosedate < @fdateto and fchancestatus ='chance_status_04'))t1 group by fdeptid";
            //系统内所有的未关闭且未作废且无下游意向与合同的商机数汇总
            var custeffectivesql = $@"select count(fdeptid) as fcusteffectiveqty,fdeptid from (select fdeptid from  t_ydj_customerrecord where {companyBuild} 
                                 and fcancelstatus <> '1' and fchancestatus <>'chance_status_04'and  fintentionno ='' and forderno='')t1 group by fdeptid";
            //本期范围内未作废且未关闭且只有下推意向单无下推合同的商机数汇总
            var custtransfersql = $@"select fdeptid,count(fdeptid) as fcusttransferqty from (
                                select a.fdeptid from  t_ydj_customerrecord a left join t_ydj_saleintention b 
                                on a.fintentionno =b.fbillno and a.fmainorgid = b.fmainorgid
                                where  a.fgoshopdate >= @fdatefrom and a.fgoshopdate < @fdateto and a.fcancelstatus <> '1' 
                                and a.fchancestatus <>'chance_status_04'  and fintentionno<>'' and forderno='' 
                                and a.fmainorgid = @fmainorgid and b.fispushorder = '0')t1 group by fdeptid ";
            //本期新增的意向单数量汇总 、【订单金额】汇总 (含作废)
            var saleincreasesql = $@"select fdeptid,count(fdeptid) as fincreaseqty,sum(ffbillamount) as fincreaseamount
                                 from (select fdeptid,ffbillamount from t_ydj_saleintention 
                                where {companyBuild} and fdate >= @fdatefrom and fdate < @fdateto)t1 group by fdeptid ";
            //统计系统内所有已审核且无下游合同的意向单数量、订单金额 汇总 
            var saleuntransfersql = $@"select fdeptid,count(fdeptid) funtransferqty,sum(ffbillamount) funtransferamount from
                                (select fdeptid,ffbillamount from t_ydj_saleintention where {companyBuild} 
                                and fstatus ='E' and fispushorder = '0')t1 group by fdeptid ";
            //本期时间范围内下推过销售合同的意向单数量、订单金额汇总(在统计范围内下推的合同且合同已审核).关联查询销售合同的创建时间
            var saletransfersql = $@"select fdeptid,count(fdeptid) ftransferqty,sum(ffbillamount) ftransferamount from 
                                (select fdeptid,ffbillamount from t_ydj_saleintention where fbillno in ( 
                                select fsourcenumber from t_ydj_order o where  
                                o.fstatus ='E' and o.fcreatedate >= @fdatefrom and o.fcreatedate < @fdateto  
                                and o.fsourcetype ='ydj_saleintention' and {companyBuild}) 
                                and {companyBuild}) t1 group by fdeptid";
            //本期作废的销售意向数、订单金额 汇总(在统计日期内作废的意向单)（按作废日期）
            var salecancelsql = $@"select fdeptid,count(fdeptid) as fcancelqty,sum(ffbillamount) as fcancelamount from 
                                (select fdeptid,ffbillamount from t_ydj_saleintention where {companyBuild} and
                                fcanceldate  >= @fdatefrom and fcanceldate < @fdateto and fcancelstatus ='1') t1 group by fdeptid ";
            var sql = $@"select NEWID() as fid,isnull(s1.fdeptid,isnull(s2.fdeptid,isnull(s3.fdeptid,isnull(s4.fdeptid,isnull(s5.fdeptid,isnull(s6.fdeptid,isnull(s7.fdeptid,isnull(s8.fdeptid,'')))))))) as fstoreid,
                        isnull(fcustincreaseqty,0) as fcustincreaseqty,
                        isnull(fcustinvalidqty,0) as fcustinvalidqty,
                        isnull(fcusteffectiveqty,0) as fcusteffectiveqty,
                        isnull(fcusttransferqty,0) as fcusttransferqty,
                        case when (isnull(fcustincreaseqty,0)=0) then 0 else (isnull(fincreaseqty,0)/convert(decimal(16,2),fcustincreaseqty)*100) end as fcusttransferrate,
                        isnull(fincreaseqty,0) as fincreaseqty,
                        isnull(fincreaseamount,0) as  fincreaseamount,
                        isnull(funtransferqty,0) as  funtransferqty,
                        isnull(funtransferamount,0) as funtransferamount,
                        isnull(ftransferqty,0) as ftransferqty,
                        isnull(ftransferamount,0) as ftransferamount,
                        isnull(fcancelqty,0) as fcancelqty,
                        isnull(fcancelamount,0) as fcancelamount,
                        case when (isnull(fincreaseqty,0)=0) then 0 else (isnull(ftransferqty,0)/convert(decimal(16,2),fincreaseqty)*100) end as ftransferrate  
                        from ({custincreasesql}) s1 full join ({custinvalidsql}) s2 on isnull(s1.fdeptid,'') =isnull(s2.fdeptid,'') 
                        full join ({custeffectivesql}) s3 on isnull(s1.fdeptid,isnull(s2.fdeptid,'')) = isnull(s3.fdeptid,'')
                        full join ({custtransfersql}) s4 on isnull(s1.fdeptid,isnull(s2.fdeptid,isnull(s3.fdeptid,''))) = isnull(s4.fdeptid,'')
                        full join ({saleincreasesql}) s5 on isnull(s1.fdeptid,isnull(s2.fdeptid,isnull(s3.fdeptid,isnull(s4.fdeptid,'')))) = isnull(s5.fdeptid,'')
                        full join ({saleuntransfersql}) s6 on isnull(s1.fdeptid,isnull(s2.fdeptid,isnull(s3.fdeptid,isnull(s4.fdeptid,isnull(s5.fdeptid,'')))))= isnull(s6.fdeptid,'')
                        full join ({saletransfersql}) s7 on isnull(s1.fdeptid,isnull(s2.fdeptid,isnull(s3.fdeptid,isnull(s4.fdeptid,isnull(s5.fdeptid,isnull(s6.fdeptid,''))))))=isnull(s7.fdeptid,'')
                        full join ({salecancelsql}) s8 on isnull(s1.fdeptid,isnull(s2.fdeptid,isnull(s3.fdeptid,isnull(s4.fdeptid,isnull(s5.fdeptid,isnull(s6.fdeptid,isnull(s7.fdeptid,'')))))))= isnull(s8.fdeptid,'')";
            sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart.Value));
            sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd.Value));


            var strSql = $@"
            insert into {this.DataSourceTableName}
            (fid,fstoreid,fcustincreaseqty,fcustinvalidqty,fcusteffectiveqty,fcusttransferqty,fcusttransferrate,
            fincreaseqty,fincreaseamount,funtransferqty,funtransferamount,ftransferqty,ftransferamount,fcancelqty,fcancelamount,ftransferrate)
            (select * from ({sql})rr where  fstoreid <> '' {filterBuilder.ToString()})
            ";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            dbServiceExt.Execute(this.Context, strSql, sqlParams);

        }

        protected override void InitializeServicePlugIn(OperationContext operCtx)
        {
            base.InitializeServicePlugIn(operCtx);

            updateCustomFilterFormYdjTarget();
        }

        /// <summary>
        /// 获取从销售目标小组件的参数并更新报表自定义参数
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        /// <param name="storeidArray"></param>
        private void updateCustomFilterFormYdjTarget()
        {
            var sender = this.GetPageSessionValue<string>("sender", string.Empty, this.HtmlForm.Id, this.ParentPageId);
            if (false == sender.EqualsIgnoreCase("ydj_target") || string.IsNullOrWhiteSpace(this.HtmlForm.CustomFilterForm))
            {
                return;
            }

            var dtType = this.GetPageSessionValue<string>("dtType", string.Empty, this.HtmlForm.Id, this.ParentPageId);
            var dataPermId = this.GetPageSessionValue<string>("dataPermId", string.Empty, this.HtmlForm.Id, this.ParentPageId);

            dtType = string.IsNullOrWhiteSpace(dtType) ? "本月" : dtType;
            dataPermId = string.IsNullOrWhiteSpace(dataPermId) ? "mycompany" : dataPermId;

            if (dataPermId.EqualsIgnoreCase("myself"))
            {
                throw new BusinessException("evalType参数无效");
            }

            string[] dtTimes = ObjectUtils.GetDatetimesByName(dtType);
            string[] storeIdArray = null;
            var baseFormProvider = this.Container.GetService<IBaseFormProvider>();

            switch (dataPermId)
            {
                case "mydepartment":
                    var deptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
                    if (false == string.IsNullOrWhiteSpace(deptId))
                    {
                        storeIdArray = new[] { deptId };
                    }
                    break;
                case "mysubordinates":
                    storeIdArray = baseFormProvider.GetMySubordinates(this.Context).Select(x => x.Id).ToArray();
                    break;
            }

            this.SetPageSessionValue("sender", string.Empty, this.HtmlForm.Id, this.ParentPageId);
            this.SetPageSessionValue("dataPermId", string.Empty, this.HtmlForm.Id, this.ParentPageId);
            this.SetPageSessionValue("dtType", string.Empty, this.HtmlForm.Id, this.ParentPageId);

            var customerFilter = this.CustomFilterFormMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
            var isUpdated = false;

            if (customerFilter == null)
            {
                return;
            }

            if (dtTimes != null && dtTimes.Length == 2)
            {
                customerFilter["fdatefrom"] = dtTimes[0];
                customerFilter["fdateto"] = dtTimes[1];
                isUpdated = true;
            }

            if (storeIdArray != null && storeIdArray.Length > 0)
            {
                customerFilter["fstoreid"] = string.Join(",", storeIdArray);
                isUpdated = true;
            }

            if (isUpdated)
            {
                var dcSerializer = this.Container.GetService<IDynamicSerializer>();
                var jsonData = dcSerializer.ToDynamicJson(customerFilter);
                this.SimpleData["__customFilter__"] = jsonData;
            }
            
        }

        private void builSqlAndAddParams(string[] data, string fieldKey, StringBuilder sql, List<SqlParam> sqlParams)
        {
            if (data == null || data.Length <= 0)
            {
                return;
            }

            if (data.Length == 1)
            {
                sqlParams.Add(new SqlParam($"@{fieldKey}", DbType.String, data[0]));
                sql.Append($" and {fieldKey} = @{fieldKey} ");
                return;
            }

            sqlParams.AddRange(data.Select((x, i) => new SqlParam($"@{fieldKey}{i}", DbType.String, x)));
            sql.Append($" and {fieldKey} in ( ");
            sql.Append(string.Join(",", data.Select((x, i) => $"@{fieldKey}{i}")));
            sql.Append(") ");
        }
    }
}
