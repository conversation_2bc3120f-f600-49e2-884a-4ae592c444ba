using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Report.StockAgeDetail
{
    /// <summary>
    /// 准备报表动态列模型
    /// </summary>
    [InjectService]
    [FormId("rpt_stockagedetail")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {

        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        protected override void OnIniHtmlForm(HtmlForm htmlForm)
        {
            base.OnIniHtmlForm(htmlForm);
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            if (this.PageSession == null)
            {
                throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
            }

            var usableQty = this.GetQueryOrSimpleParam<decimal>("usableQty");
            var date = this.GetQueryOrSimpleParam<string>("date");
            var fmaterialid = this.GetQueryOrSimpleParam<string>("fmaterialid");
            var fmainorgid = this.GetQueryOrSimpleParam<string>("fmainorgid");
            var fstorehouseid = this.GetQueryOrSimpleParam<string>("fstorehouseid");
            var fcustomdesc = this.GetQueryOrSimpleParam<string>("fcustomdesc");
            var fattrinfo = this.GetQueryOrSimpleParam<string>("fattrinfo");
            var fattrinfo_e = this.GetQueryOrSimpleParam<string>("fattrinfo_e");
            var fstockunitid = this.GetQueryOrSimpleParam<string>("fstockunitid");
            this.PageSession.usableQty = usableQty;
            this.PageSession.date = date;
            this.PageSession.fmaterialid = fmaterialid;
            this.PageSession.fmainorgid = fmainorgid;
            this.PageSession.fstorehouseid = fstorehouseid;
            this.PageSession.fcustomdesc = fcustomdesc;
            this.PageSession.fattrinfo = fattrinfo;
            this.PageSession.fattrinfo_e = fattrinfo_e;
            this.PageSession.fstockunitid = fstockunitid;
        }
    }
}
