using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp;
using System.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Report
{
    /// <summary>
    /// 收支明细报表数据查询服务抽象基类
    /// </summary>
    public abstract class IncomeDisburseChart : OfficeRptDataService
    {
        /// <summary>
        /// 报表类型：采购 或 销售
        /// </summary>
        public virtual string RptType { get { return ""; } }

        /// <summary>
        /// 结算主体
        /// </summary>
        private string SettleMainId { get { return this.GetQueryOrSimpleParam<string>("settleMainId", ""); } }

        /// <summary>
        /// 用途
        /// </summary>
        private string Purpose { get { return this.GetQueryOrSimpleParam<string>("purpose", ""); } }

        /// <summary>
        /// 状态
        /// </summary>
        private string BizStatus { get { return this.GetQueryOrSimpleParam<string>("bizStatus", ""); } }

        /// <summary>
        /// 支付方式
        /// </summary>
        private string Way { get { return this.GetQueryOrSimpleParam<string>("way", ""); } }

        /// <summary>
        /// 交易生成方式（全部/协同/非协同)
        /// </summary>
        private string Mode { get { return this.GetQueryOrSimpleParam<string>("mode", ""); } }

        /// <summary>
        /// 源单主键Id，发起收支记录的单据主键ID，比如：销售意向单主键ID，销售合同主键ID 等等
        /// </summary>
        private string SourceId { get { return this.GetQueryOrSimpleParam<string>("sourceId", ""); } }

        /// <summary>
        /// 账户增减方向
        /// </summary>
        private string Direction { get { return this.GetQueryOrSimpleParam<string>("direction", ""); } }

        /// <summary>
        /// 账户
        /// </summary>
        private string Account { get { return this.GetQueryOrSimpleParam<string>("account", ""); } }

        /// <summary>
        /// 构建列表数据源
        /// </summary>
        /// <param name="listDesc"></param>
        /// <returns></returns>
        protected override IDataReader BuildQueryData(out ListDesc listDesc)
        {
            if (this.RptType.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("报表类型必须是 pur 或 sal，请检查！");
            }

            listDesc = new ListDesc();

            var dataTuple = this.DateTimeFormat();

            var joinStr = "";
            var selectFieldStr = "";
            var companyFieldStr = "";
            switch (this.RptType.Trim().ToLower())
            {
                case "pur":
                    joinStr = " left join t_ydj_supplier t5 with(nolock) on t5.fmainorgid=@fmainorgid and t1.fsupplierid=t5.fid";
                    selectFieldStr = "t5.fname as fsupplier,";
                    companyFieldStr = "fsalecompany";
                    break;
                case "sal":
                    joinStr = " left join t_ydj_customer t5 with(nolock) on t5.fmainorgid=@fmainorgid and t1.fcustomerid=t5.fid";
                    selectFieldStr = "t5.fname as fcustomer,";
                    companyFieldStr = "fpurcompany";
                    break;
                default:
                    break;
            }

            StringBuilder sbSqlCount = new StringBuilder();
            sbSqlCount.AppendFormat(@"
            select count(1) from t_coo_incomedisburse t1 with(nolock) {0}
            where t1.fmainorgid=@fmainorgid", joinStr);

            StringBuilder sbSqlData = new StringBuilder();
            sbSqlData.AppendFormat(@"
	        select t1.fid,t1.ftranid,t1.fdate,t1.fpurpose,t2.fenumitem as fpurposename,t1.fway,t3.fenumitem as fwayname,t1.famount,t1.fbizstatus,
	        t1.{0},t4.fenumitem as fbizstatusname,t1.fcreatecompanyid,t1.fdirection,t1.fbizdirection,t1.fdescription,t1.fissyn,t1.foperationmode,
            tu1.fname as fcreator,t1.fcreatedate,tu2.foperator as fconfirmor,tu2.fopdate as fconfirmdate,
            t11.fname as fsourcetype,t1.fsourcenumber,
            t1.fverificstatus,t10.fenumitem as fverificstatusname,tu3.foperator as fverificor,tu3.fopdate as fverificdate,'' as foperate, {2}
            row_number() over(order by t1.fid desc) as fjnidentity from t_coo_incomedisburse t1  with(nolock)
	        left join t_bd_enumdataentry t2 with(nolock) on t1.fpurpose=t2.fentryid 
	        left join t_bd_enumdataentry t3 with(nolock) on t1.fway=t3.fentryid
	        left join t_bd_enumdataentry t4 with(nolock) on t1.fbizstatus=t4.fentryid
	        left join t_bd_enumdataentry t10 with(nolock) on t1.fverificstatus=t10.fentryid
            left join t_sys_bizobject t11 with(nolock) on t1.fsourceformid=t11.fid
            left join t_sec_user tu1 with(nolock) on t1.fcreatorid=tu1.fid
            left join t_coo_incomedisburse_lg tu2 with(nolock) on t1.fid=tu2.fbizobjid and tu2.fopcode='confirm' 
            left join t_coo_incomedisburse_lg tu3 with(nolock) on t1.fid=tu3.fbizobjid and tu3.fopcode='verific' {1}
	        where t1.fmainorgid=@fmainorgid", companyFieldStr, joinStr, selectFieldStr);

            List<SqlParam> paramList = new List<SqlParam>()
            {
                new SqlParam("fmainorgid", DbType.String, this.UserCtx.Company)
            };

            //如果明确按源单ID查询，则忽略其他条件
            if (!this.SourceId.IsNullOrEmptyOrWhiteSpace())
            {
                switch (this.RptType.Trim().ToLower())
                {
                    case "pur":
                        sbSqlCount.Append(" and t1.fsourceid=@fsourceid");
                        sbSqlData.Append(" and t1.fsourceid=@fsourceid");
                        paramList.Add(new SqlParam("fsourceid", DbType.String, this.SourceId.Trim()));
                        break;
                    case "sal":
                        string sourceIdSql = " and (t1.fsourceid=@fsourceid or t1.fsourceid = (select top 1 fid from t_ydj_saleintention sal with(nolock) where sal.forderid=@fsourceid and sal.fmainorgid=@fmainorgid) or t1.fsourceid = (select top 1 forderid from t_ydj_saleintention sal with(nolock) where sal.fid=@fsourceid and sal.fmainorgid=@fmainorgid)) and len(t1.fsourceid)>0";
                        sbSqlCount.Append(sourceIdSql);
                        sbSqlData.Append(sourceIdSql);
                        paramList.Add(new SqlParam("fsourceid", DbType.String, this.SourceId.Trim()));
                        break;
                }
            }

            //按日期范围过滤
            sbSqlCount.Append(" and t1.fdate>=@fstartdate and t1.fdate<@fenddate");
            sbSqlData.Append(" and t1.fdate>=@fstartdate and t1.fdate<@fenddate");
            paramList.Add(new SqlParam("fstartdate", DbType.String, dataTuple.Item1));
            paramList.Add(new SqlParam("fenddate", DbType.String, dataTuple.Item2));

            //报表类型
            switch (this.RptType.Trim().ToLower())
            {
                //采购收支明细，过滤条件：非协同的 或者 收支记录.销售方企业在协同企业关系中性质=供应商，且已建立协同关系的
                case "pur":
                    var purWhere = @" 
                        and ((t1.fissyn='0' and fpurcompanyid=@fmainorgid) or t1.fsalecompanyid in
                        (
                            select distinct fcompanyid from t_coo_company  with(nolock)
                            where fmainorgid=@fmainorgid and fservicetype='供应商' and fcoostatus='已协同'
                        ))";
                    sbSqlCount.Append(purWhere);
                    sbSqlData.Append(purWhere);

                    //根据某个具体的供应商查询
                    if (!this.SettleMainId.IsNullOrEmptyOrWhiteSpace())
                    {
                        sbSqlCount.Append(" and t1.fsupplierid=@settlemainid");
                        sbSqlData.Append(" and t1.fsupplierid=@settlemainid");
                        paramList.Add(new SqlParam("settlemainid", DbType.String, this.SettleMainId.Trim()));
                    }
                    break;

                //销售收支明细，过滤条件：非协同的 或者 收支记录.采购方企业在协同企业关系中性质=客户，且已建立协同关系的
                case "sal":
                    var salWhere = @" 
                        and ((t1.fissyn='0' and fsalecompanyid=@fmainorgid) or (t1.fissyn='1' and t1.foperationmode=1) or t1.fpurcompanyid in
                        (
                            select distinct fcompanyid from t_coo_company with(nolock) 
                            where fmainorgid=@fmainorgid and fservicetype='客户' and fcoostatus='已协同'
                        ))";
                    if (this.Mode.IsNullOrEmptyOrWhiteSpace()) // 全部
                    {
                        salWhere = @" 
                            and ((t1.fissyn='0' and fsalecompanyid=@fmainorgid) or (t1.fissyn='1' and t1.foperationmode=1) or t1.fpurcompanyid 
                            in 
                            (
                                select distinct fcompanyid from t_coo_company with(nolock) 
                                where fmainorgid=@fmainorgid and fservicetype='客户' and fcoostatus='已协同'
                            ) or t5.fname>'') ";
                    }
                    else if (this.Mode.EqualsIgnoreCase("mode_02")) // 非协同
                    {
                        salWhere = @" and t1.fissyn <>'1' ";
                    }
                    else if (this.Mode.EqualsIgnoreCase("mode_01")) // 协同
                    {
                        salWhere = @" and t1.fissyn ='1' ";
                    }
                    sbSqlCount.Append(salWhere);
                    sbSqlData.Append(salWhere);

                    //根据某个具体的客户查询
                    if (!this.SettleMainId.IsNullOrEmptyOrWhiteSpace())
                    {
                        sbSqlCount.Append(" and t1.fcustomerid=@settlemainid");
                        sbSqlData.Append(" and t1.fcustomerid=@settlemainid");
                        paramList.Add(new SqlParam("settlemainid", DbType.String, this.SettleMainId.Trim()));
                    }
                    break;

                default:
                    break;
            }

            //如果前端有传递其他条件，则根据其他条件进行过滤
            if (!this.Purpose.IsNullOrEmptyOrWhiteSpace())
            {
                sbSqlCount.Append(" and t1.fpurpose=@fpurpose");
                sbSqlData.Append(" and t1.fpurpose=@fpurpose");
                paramList.Add(new SqlParam("fpurpose", DbType.String, this.Purpose.Trim()));
            }
            if (!this.BizStatus.IsNullOrEmptyOrWhiteSpace())
            {
                sbSqlCount.Append(" and t1.fbizstatus=@fbizstatus");
                sbSqlData.Append(" and t1.fbizstatus=@fbizstatus");
                paramList.Add(new SqlParam("fbizstatus", DbType.String, this.BizStatus.Trim()));
            }
            if (!this.Way.IsNullOrEmptyOrWhiteSpace())
            {
                sbSqlCount.Append(" and t1.fway=@fway");
                sbSqlData.Append(" and t1.fway=@fway");
                paramList.Add(new SqlParam("fway", DbType.String, this.Way.Trim()));
            }
            if (!this.Direction.IsNullOrEmptyOrWhiteSpace())
            {
                sbSqlCount.Append(" and t1.fdirection=@fdirection");
                sbSqlData.Append(" and t1.fdirection=@fdirection");
                paramList.Add(new SqlParam("fdirection", DbType.String, this.Direction.Trim()));
            }
            if (!this.Account.IsNullOrEmptyOrWhiteSpace())
            {
                sbSqlCount.Append(" and t1.faccount=@faccount");
                sbSqlData.Append(" and t1.faccount=@faccount");
                paramList.Add(new SqlParam("faccount", DbType.String, this.Account.Trim()));
            }

            //查询总记录数
            using (var reader = this.DBService.ExecuteReader(this.UserCtx, sbSqlCount.ToString(), paramList))
            {
                if (reader.Read())
                {
                    listDesc.Rows = Convert.ToInt64(reader[0]);
                }
            }

            //查询分页数据
            int pageIndex = 1;
            int pageSize = 10;
            int.TryParse(this.GetQueryOrSimpleParam<string>("pageIndex"), out pageIndex);
            int.TryParse(this.GetQueryOrSimpleParam<string>("pageSize"), out pageSize);
            if (pageIndex < 1)
            {
                pageIndex = 1;
            }
            if (pageSize < 1)
            {
                pageSize = 10;
            }
            listDesc.PageCount = pageSize;
            var sqlPageData = "select top {1} * from ({0})x where fjnidentity>{1}*({2}-1)".Fmt(sbSqlData, pageSize, pageIndex);
            IDataReader dataReader = this.DBService.ExecuteReader(this.UserCtx, sqlPageData, paramList);
            return dataReader;
        }

        /// <summary>
        /// 构建图表数据源
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="rptOperCtx"></param>
        /// <param name="rptDataSource"></param>
        protected override void BuildRptChartDataSource(UserContext userCtx, ReportOperationContext rptOperCtx, ReportDataSource rptDataSource)
        {
            //处理报表数据源
            if (rptDataSource == null
                || rptDataSource.RptGridDataSource == null
                || rptDataSource.RptGridDataSource.Count() <= 0)
            {
                return;
            }

            List<Dictionary<string, object>> newDataSource = new List<Dictionary<string, object>>();
            foreach (var item in rptDataSource.RptGridDataSource)
            {
                if (item["fsourcetype"].IsNullOrEmptyOrWhiteSpace()) item["fsourcetype"] = "";

                //处理确认人，确认时间字段
                if (item["fconfirmor"].IsNullOrEmptyOrWhiteSpace()) item["fconfirmor"] = "";
                if (item["fconfirmdate"].IsNullOrEmptyOrWhiteSpace()) item["fconfirmdate"] = "";

                //处理核销人，核销时间字段
                if (item["fverificor"].IsNullOrEmptyOrWhiteSpace()) item["fverificor"] = "";
                if (item["fverificdate"].IsNullOrEmptyOrWhiteSpace()) item["fverificdate"] = "";

                newDataSource.Add(item);
            }

            rptDataSource.RptGridDataSource = newDataSource;
        }

        /// <summary>
        /// 所有涉及到日期的格式化查询
        /// </summary>
        /// <returns></returns>
        private Tuple<string, string> DateTimeFormat()
        {
            string startDateStr = this.GetQueryOrSimpleParam<string>("startDate");
            string endDateStr = this.GetQueryOrSimpleParam<string>("endDate");

            DateTime startDate = DateTime.MinValue;
            DateTime endDate = DateTime.MaxValue;
            if (startDateStr.IsNullOrEmptyOrWhiteSpace() || endDateStr.IsNullOrEmptyOrWhiteSpace())
            {
                DateTime dataNow = DateTime.Now;
                int dataType = this.GetQueryOrSimpleParam<int>("dataType");
                switch (dataType)
                {
                    case 1://本月
                        startDate = new DateTime(dataNow.Year, dataNow.Month, 1);
                        endDate = startDate.AddMonths(1);
                        break;
                    case 2://上月
                        startDate = new DateTime(dataNow.Year, dataNow.Month, 1).AddMonths(-1);
                        endDate = new DateTime(dataNow.Year, dataNow.Month, 1);
                        break;
                    case 3://本年
                        startDate = new DateTime(dataNow.Year, 1, 1);
                        endDate = new DateTime(dataNow.Year, 1, 1).AddYears(1);
                        break;
                    case 4://上年
                        startDate = new DateTime(dataNow.Year, 1, 1).AddYears(-1);
                        endDate = new DateTime(dataNow.Year, 1, 1);
                        break;
                    default:
                        break;
                }
            }
            else
            {
                if (!DateTime.TryParse(startDateStr, out startDate) || !DateTime.TryParse(endDateStr, out endDate))
                {
                    throw new BusinessException("开始日期或结束日期格式错误，请检查！");
                }
                else
                {
                    if (startDate > endDate)
                    {
                        throw new BusinessException("开始日期不能大于结束日期！");
                    }
                }
            }
            return new Tuple<string, string>(startDate.ToString("yyyy-MM-dd HH:mm:ss"), endDate.AddDays(1).ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }
}