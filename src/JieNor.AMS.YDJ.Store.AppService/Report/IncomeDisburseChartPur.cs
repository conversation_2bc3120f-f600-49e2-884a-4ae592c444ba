using JieNor.Framework.IoC;

namespace JieNor.AMS.YDJ.Store.AppService.Report
{
    /// <summary>
    /// 采购收支明细报表数据查询服务
    /// </summary>
    [InjectService("IncomeDisburseChartPur")]
    [FormId("coo_incomedisburserptpur")]
    public class IncomeDisburseChartPur : IncomeDisburseChart
    {
        /// <summary>
        /// 报表类型：采购 或 销售
        /// </summary>
        public override string RptType { get { return "pur"; } }
    }
}