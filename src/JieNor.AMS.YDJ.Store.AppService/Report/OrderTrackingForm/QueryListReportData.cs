using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System.Collections.Generic;
using System.Data;
using System.Text;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Store.AppService.Report.OrderTrackingForm
{
    /// <summary>
    /// 客户订单跟踪表
    /// </summary>
    [InjectService]
    [FormId("rpt_ordertrackingform")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.TryGetDate(out var dtStart, out var dtEnd);

            var whereBuilder = new StringBuilder();
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
            };

            whereBuilder.Append(" and t.forderdate >= @fdatefrom ");
            sqlParams.Add(new SqlParam("@fdatefrom", DbType.DateTime, dtStart));

            whereBuilder.Append(" and t.forderdate <= @fdateto ");
            sqlParams.Add(new SqlParam("@fdateto", DbType.DateTime, dtEnd));

            StringBuilder sbOrderSql = new StringBuilder();
            StringBuilder sbInsertSelectSql = new StringBuilder();

            //创建临时表
            sbInsertSelectSql.Append($@"/*dialect*/ insert into {this.DataSourceTableName}
                    (fid, fjnidentityid, fbillno, fdeptid, fstoreid, fcustomerid, fphone, faddress, forderdate, fdeliverydate, finstalldate, 
                    fstylistid, fstaffid, fpurbillno, fmaterialid, fbizinstockqty,assistant ,assistantdep)");
            //查询
            sbOrderSql.Append($@" 
SELECT CAST(NEWID() AS NVARCHAR(50)),
       ROW_NUMBER() OVER (ORDER BY forderdate desc) AS fjnidentityid,
       fbillno,
       fdeptid,
       fstoreid,
       fcustomerid,
       fphone,
       faddress,
       forderdate,
       fdeliverydate,
       finstalldate,
       fstylistid,
       fstaffid,
       fpurbillno,
       fmaterialid,
       fbizinstockqty,
       assistant,
       assistantdep
FROM
(
    SELECT DISTINCT t.fid,
           t.fbillno,
           t.fdeptid,
           t.fstore AS fstoreid,
           t.fcustomerid fcustomerid,
           t.fphone,
           t.faddress,
           t.forderdate,
           t.fdeliverydate,
           t.fdeliverydate finstalldate,
           t.fstylistid,
           t.fstaffid,
           t.fmainorgid,
           ISNULL(p.fbillno, '') fpurbillno,
           ISNULL(pe.fentryid, '') fpurentryid,
           ISNULL(pe.fmaterialid, '') fmaterialid,
           ISNULL(pe.fbizinstockqty, 0) fbizinstockqty,
           ISNULL(STUFF(
                  (
                      SELECT CONCAT(';', s.fname, ' ', CONVERT(NVARCHAR(10), CONVERT(DECIMAL, od1.fratio)), '%')
                      FROM T_YDJ_ORDERDUTY od1 WITH (NOLOCK)
                          LEFT JOIN T_BD_STAFF s WITH (NOLOCK)
                              ON s.fid = od1.fdutyid
                      WHERE t.fid = od1.fid
                      FOR XML PATH('')
                  ),
                  1,
                  1,
                  ''
                       ),
                  ''
                 ) AS assistant,
           ISNULL(STUFF(
                  (
                      SELECT CONCAT(';', s.fname, ' ', dp.fname)
                      FROM T_YDJ_ORDERDUTY od1 WITH (NOLOCK)
                          LEFT JOIN T_BD_STAFF s WITH (NOLOCK)
                              ON s.fid = od1.fdutyid
                          LEFT JOIN T_BD_DEPARTMENT dp WITH (NOLOCK)
                              ON dp.fid = s.fdeptid
                      WHERE t.fid = od1.fid
                      FOR XML PATH('')
                  ),
                  1,
                  1,
                  ''
                       ),
                  ''
                 ) AS assistantdep
    FROM T_YDJ_ORDER t WITH (NOLOCK)
        INNER JOIN T_YDJ_ORDERENTRY te WITH (NOLOCK)
            ON t.fid = te.fid
        LEFT JOIN T_YDJ_POORDERENTRY pe WITH (NOLOCK)
            ON te.fentryid = pe.fsoorderentryid
        LEFT JOIN T_YDJ_PURCHASEORDER p WITH (NOLOCK)
            ON pe.fid = p.fid
    WHERE t.fmainorgid = @fmainorgid
          AND t.fcancelstatus = '0' {whereBuilder}
) p");
            var strSql = $@"{sbInsertSelectSql}{sbOrderSql}";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, strSql, sqlParams);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected void TryGetDate(out DateTime dtStart, out DateTime dtEnd)
        {
            dtStart = ((DateTime?)this.CustomFilterObject["fdatefrom"] ?? DateTime.Now.GetFirstDayOfMonth()).DayBegin();
            dtEnd = ((DateTime?)this.CustomFilterObject["fdateto"] ?? DateTime.Now).DayEnd();
        }
    }
}
