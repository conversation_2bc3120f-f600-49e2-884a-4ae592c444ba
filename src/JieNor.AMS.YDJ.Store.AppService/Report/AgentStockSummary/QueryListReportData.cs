using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Store.AppService.Report.AgentStockSummary
{
    /// <summary>
    /// 商品库存分析表 - 经销商
    /// </summary>
    [InjectService]
    [FormId("rpt_agentstocksummary")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {

        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        private string topCompanyId = "";

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            topCompanyId = this.Context.TopCompanyId;
            //this.GetData();
        }

        /// <summary>
        /// 报表排序逻辑
        /// </summary>
        /// <param name="listQueryPara"></param>
        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {
            base.OnPrepareReportQueryParameter(listQueryPara);
            listQueryPara.OrderByString = " fsaleregionalid,fserviceregionalid,fprovince,fagnetnumber,fcategoryid ";
        }


        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        private void GetData()
        {
            var sqlParam = new List<SqlParam>
            {
              new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
              new SqlParam("@ftopmainorgid", DbType.String, this.Context.TopCompanyId)
            };

            var fcategoryid = this.CustomFilterObject["fcategoryid"] as string;
            string region = this.ParentPageSession?.fsaleregionalid as string;
            string center = this.ParentPageSession?.fserviceregionalid as string;
            string provice = this.ParentPageSession?.fprovince as string;
            var fcatyid = this.ParentPageSession?.fcategoryid as string;//区域的ID
            if (fcategoryid.IsNullOrEmptyOrWhiteSpace())
            {
                //固定取产品类
                string sql = $@" SELECT fid, fname FROM dbo.SER_YDJ_CATEGORY with(nolock) WHERE fparentid =(select fid from SER_YDJ_CATEGORY with(nolock) where fname='产品类' and fnumber='10') AND fmainorgid = '{ topCompanyId }'
                                UNION ALL SELECT 'Other' fid,'其他类' fname";
                var cats = this.DBService.ExecuteDynamicObject(this.Context, sql);
                foreach (var item in cats)
                {
                    fcategoryid += item["fid"] + ",";
                }
                fcategoryid = fcategoryid.Substring(0, fcategoryid.Length - 1);
                //查询传入参数，默认固定：床垫类、床架类、其它类(排除前二者)。
            }
            if (!string.IsNullOrEmpty(fcatyid))
            {
                fcategoryid = fcatyid;
            }
            //fcategoryid = "822158660800221297,822158660800221298";
            var fcategoryidArray = fcategoryid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fcategoryidArray.Count() > 0)
            {
                var matTable = this.DBService.CreateTemporaryTableName(this.Context, "T_MATERIAL");
                {
                    var inventorylistTableSql = $@"select * into {matTable} from T_BD_MATERIAL with(nolock)";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, inventorylistTableSql);
                }

                var catTable = this.DBService.CreateTemporaryTableName(this.Context, "T_CAT");
                var catSql = $@"SELECT *  FROM SER_YDJ_CATEGORY  with(nolock)";
                var catdy = this.DBService.ExecuteDynamicObject(Context, catSql);

                var tmpAgentTable = this.DBService.CreateTemporaryTableName(this.Context, "T_AGENT");
                {
                    var inventorylistTableSql = $@"select * into {tmpAgentTable} from T_BAS_AGENT with(nolock)";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, inventorylistTableSql);
                }
                var invTable = this.DBService.CreateTemporaryTableName(this.Context, "T_INVELIST");
                {
                    var inventorylistTableSql = $@"select a.*,b.fnumber fmainorgnumber,b.fname fmainorgname,t2.fwarehousetype into {invTable} 
                        from T_STK_INVENTORYLIST as a with(nolock) 
                        inner join T_BAS_AGENT as b with(nolock) on a.fmainorgid=b.fid
                        inner join t_ydj_storehouse t2  WITH(nolock) on a.fstorehouseid = t2.fid ";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, inventorylistTableSql);
                }
                ////查询即时库存中所有商品类别
                //var sqlStr = $@"select t3.fid,MAX(t3.fname) fname,MAX(t3.fnumber) fnumber, MAX(t3.fparentid) fparentid from {invTable} t1 with(nolock)
                //               inner join {matTable} t2 WITH(nolock) on t1.fmaterialid = t2.fid
                //               inner join ser_ydj_category t3 WITH(nolock) on t2.fcategoryid = t3.fid
                //                group by t3.fid";
                //var allProType = this.DBService.ExecuteDynamicObject(Context, sqlStr);
                //其他类的类别id合集
                var otherFcategoryIds = new List<string>();
                //类别id合集
                Dictionary<string, List<string>> fcategoryid_dics = new Dictionary<string, List<string>>();
                List<string> exceptOther = new List<string>();
                foreach (var fcategoryidItem in fcategoryidArray)
                {
                    //var _fid = allProType.Where(x => (string)x["fid"] == fcategoryidItem).Select(x => (string)x["fid"]).FirstOrDefault();
                    //if (string.IsNullOrEmpty(_fid))
                    //    continue;
                    //var _fcategoryid = getCategorys(allProType, _fid).Where(x => x.fparentid == _fid).Select(x => x.fid).ToList();
                    var _fcategoryid1 = getAllChildrenCategorys(catdy, fcategoryidItem).Select(x => x.fid).Distinct().ToList();
                    if (_fcategoryid1.Any())
                    {
                        exceptOther.AddRange(_fcategoryid1);
                        fcategoryid_dics.Add(fcategoryidItem, _fcategoryid1);
                    }
                }
                if (fcategoryidArray.Any(_ => _ == "Other"))
                {
                    otherFcategoryIds = catdy.Select(x => (string)x["fid"]).Except(exceptOther).ToList();
                }
                Random rd = new Random();

                #region 先把商品《采购价目》表的采购价查出放在临时表中
                //取有效期内并已确认的最新《采购价目》的【采购价】
                var priceTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpPurPrice");
                {
                    var tableStrSql = $@"select * into {priceTable} from (
                            select fproductid_e,fpurprice, row_number() over(partition by fproductid_e order by fconfirmdate desc) as count 
                            from (select n.fproductid_e,n.fpurprice,n.fconfirmdate,n.fconfirmstatus,n.fstartdate_e,n.fexpiredate_e 
                            from t_ydj_purchaseprice m  WITH(nolock) left join t_ydj_purchasepriceentry n  WITH(nolock) on m.fid = n.fid
                            where  m.fmainorgid='{topCompanyId}'
                            ) t_ydj_purchasepriceentry --报价信息
                            where fconfirmstatus ='2' --采购价目 单据体 状态
                            and fstartdate_e <= getdate() and fexpiredate_e >= getdate() --日期
                            ) pur where pur.count= 1;";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql, sqlParam);
                }
                #endregion
                #region 先把商品《销售价目》表销售价查出放在临时表中
                //取有效期内并已确认的最新《采购价目》的【采购价】
                var salepriceTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpSalePrice");
                {
                    //priceTable = "t_purprice_999999"; 
                    var tableStrSql = $@"select * into {salepriceTable} from (
                            select fproductid,fsalprice, row_number() over(partition by fproductid order by fconfirmdate desc) as count 
                            from (select n.fproductid,n.fsalprice,n.fconfirmdate,n.fconfirmstatus,n.fstartdate,n.fexpiredate 
                            from t_ydj_price m  WITH(nolock) left join t_ydj_priceentry n  WITH(nolock) on m.fid = n.fid
                            where  m.fmainorgid='{topCompanyId}'
                            ) t_ydj_priceentry --报价信息
                            where fconfirmstatus ='2' --销售价目 单据体 状态
                            and fstartdate <= getdate() and fexpiredate >= getdate() --日期
                            ) pur where pur.count= 1;";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql, sqlParam);
                }
                #endregion

                var pricematerial = this.DBService.CreateTemporaryTableName(this.Context, "pricemat");
                {
                    //priceTable = "t_purprice_999999"; 
                    var tableStrSql = $@"select t3.fid,t3.fnumber,t3.fname,t3.fcategoryid,t3.fmainorgid,purprice.fpurprice,salprice.fsalprice into {pricematerial}
        from {matTable} t3 WITH(nolock) 
        LEFT join t_Ydj_Brand as brand WITH(nolock) on t3.fbrandid=brand.fid
        LEFT join t_ydj_series as series WITH(nolock) on t3.fseriesid=series.fid
        left join {priceTable} as purprice with(nolock) on t3.fid=purprice.fproductid_e
        left join {salepriceTable} as salprice with(nolock) on t3.fid=salprice.fproductid
        where 
        brand.fid  in (select fid from t_Ydj_Brand  WITH(nolock) where fmainorgid='821347239912935425') or 
        series.fid  in (select fid from t_ydj_series  WITH(nolock) where fmainorgid='821347239912935425')";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql, sqlParam);
                }

                #region 生成总部物料临时表   【检查当前商品的 品牌 和 系列，是否总部的，为空或不是总部的，则非慕思的】
                var materialTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpMaterial");
                {
                    var tableStrSql = $@"select t3.fid,t3.fnumber,t3.fname,t3.fcategoryid,t3.fmainorgid into {materialTable} from {matTable} t3 WITH(nolock) LEFT join t_Ydj_Brand as brand WITH(nolock) on t3.fbrandid=brand.fid
								 LEFT join t_ydj_series as series WITH(nolock) on t3.fseriesid=series.fid
								  where brand.fid  in (select fid from t_Ydj_Brand  WITH(nolock) where fmainorgid='{topCompanyId}') or series.fid  in (select fid from t_ydj_series  WITH(nolock) where fmainorgid='{topCompanyId}')";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql);
                }
                #endregion
                #region 生成销售合同临时表
                var orderTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpOrder");
                {
                    var tableStrSql = $@"select t1.fneedtransferorder,t1.fmainorgid,t4.fnumber as fmainorgnumber,t4.fname as fmainorgname,t3.fid,t2.fproductid,t2.fattrinfo,t2.fcustomdes_e,t3.fcategoryid,t2.fbizqty,t2.fbizoutqty,fbizreturnqty,t2.fprice   
                                into {orderTable}
                                from  t_ydj_order t1  WITH(nolock)
                                 inner join t_ydj_orderentry t2  WITH(nolock) on t1.fid = t2.fid and t1.fcancelstatus = '0' and t2.fclosestatus!=4 and t1.fneedtransferorder=0 and t1.fisresellorder=0
                                 inner join {materialTable} t3 on t2.fproductid = t3.fid 
                                 inner join {tmpAgentTable} t4  WITH(nolock) on t4.fid = t1.fmainorgid ";
                    tableStrSql = $@"select t2.*,t3.fcategoryid,t4.fnumber as fmainorgnumber,t4.fname as fmainorgname  into {orderTable} from (select t1.fmainorgid,t2.fproductid,t2.fbizqty,t2.fbizoutqty,fbizreturnqty,t2.fprice   from    t_ydj_order t1  WITH(nolock)
                                 inner join t_ydj_orderentry t2  WITH(nolock) on t1.fid = t2.fid
								  where   t1.fcancelstatus = '0' and t2.fclosestatus!=4 and t1.fneedtransferorder=0) as t2
								    inner join {materialTable} t3 on t2.fproductid = t3.fid 
                                 inner join {tmpAgentTable} t4  WITH(nolock) on t4.fid = t2.fmainorgid";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql);
                }
                #endregion




                var inventorylistTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpInv");
                {
                    var inventorylistTableSql = $@"(SELECT DISTINCT c.fid fmainorgid,c.fnumber AS fmainorgnumber,c.fname fmainorgname, b.fcategoryid into {inventorylistTable} FROM  {tmpAgentTable} AS c WITH(nolock) left JOIN {invTable} AS a WITH(nolock)  ON c.fid = a.fmainorgid INNER JOIN {matTable} AS b WITH(nolock) ON a.fmaterialid = b.fid  ) ";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, inventorylistTableSql);
                }
                //补偿逻辑，把即时库存不存在，但销售订单存在的类别，补偿插入即时库存临时表
                {
                    string insSql = $@"insert into {inventorylistTable}  select t1.fmainorgid,t5.fnumber fmainorgnumber,t5.fname fmainorgname,t3.fcategoryid
								  from    t_ydj_order t1  WITH(nolock)
                                 inner join t_ydj_orderentry t2  WITH(nolock) on t1.fid = t2.fid
								  inner join {materialTable} t3 on t2.fproductid = t3.fid 
								  left join {inventorylistTable} as t4 on t4.fcategoryid=t3.fcategoryid and t1.fmainorgid=t4.fmainorgid
                                 inner join T_BAS_AGENT as t5  WITH(nolock) on t5.fid = t1.fmainorgid
								  where   t1.fcancelstatus = '0' and t2.fclosestatus!=4 and t1.fneedtransferorder=0 
								  and ISNULL(t4.fcategoryid,'')='' 
								  group by t1.fmainorgid,t5.fnumber ,t5.fname ,t3.fcategoryid";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, insSql);
                }

                #region 旧版本with里面的都改成临时表，避免循环调用
                var categoryTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpCategory");
                {
                    var tableStrSql = $@"SELECT  t6.fid  fcategoryid ,
                                        t3.fmainorgid ,
                                        t3.fmainorgnumber ,
                                        t3.fmainorgname ,
                                        SUM(t3.fbizqty - t3.fbizoutqty + fbizreturnqty) qty ,
                                        SUM(( t3.fbizqty - t3.fbizoutqty + fbizreturnqty ) * t4.fpurprice) price ,
                                        SUM(( t3.fbizqty - t3.fbizoutqty + fbizreturnqty ) * t5.fsalprice) retailprice
                                        into {categoryTable}
                                FROM    {orderTable} t3
                                        LEFT JOIN {priceTable} t4 ON t3.fproductid = t4.fproductid_e
                                        LEFT JOIN {salepriceTable} t5 ON t3.fproductid = t5.fproductid
                                        INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                WHERE   ISNULL(t6.fparentid,'')<>'' 
                                GROUP BY t3.fmainorgid , t3.fmainorgname , fmainorgnumber,t6.fid ORDER BY t3.fmainorgid";
                    tableStrSql = $@"
                                        select *  into {categoryTable} from(
                                        SELECT  'Y' fhprice,'ALL' fhpricetype,t6.fid  fcategoryid ,
                                        t3.fmainorgid ,
                                        t3.fmainorgnumber ,
                                        t3.fmainorgname ,
                                        SUM(t3.fbizqty - t3.fbizoutqty + fbizreturnqty) qty ,
                                        SUM(( t3.fbizqty - t3.fbizoutqty + fbizreturnqty ) * t4.fpurprice) price
                                FROM    {orderTable} t3
                                        LEFT JOIN {priceTable} t4 ON t3.fproductid = t4.fproductid_e 
                                        INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                --WHERE   ISNULL(t6.fparentid,'')<>''  
                                GROUP BY t3.fmainorgid , t3.fmainorgname , fmainorgnumber,t6.fid 
                                        UNION ALL
                                        SELECT  'N' fhprice,'PUR' fhpricetype,t6.fid  fcategoryid ,
                                        t3.fmainorgid ,
                                        t3.fmainorgnumber ,
                                        t3.fmainorgname ,
                                        SUM(t3.fbizqty - t3.fbizoutqty + fbizreturnqty) qty ,
                                        SUM(( t3.fbizqty - t3.fbizoutqty + fbizreturnqty ) * t4.fpurprice) price
                                FROM    {orderTable} t3
                                        LEFT JOIN {priceTable} t4 ON t3.fproductid = t4.fproductid_e  
                                        INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                WHERE    (t4.fpurprice IS NULL OR t4.fpurprice=0)--ISNULL(t6.fparentid,'')<>''  and
                                GROUP BY t3.fmainorgid , t3.fmainorgname , fmainorgnumber,t6.fid 
                                        UNION ALL
                                        SELECT  'N' fhprice,'SAL' fhpricetype,t6.fid  fcategoryid ,
                                        t3.fmainorgid ,
                                        t3.fmainorgnumber ,
                                        t3.fmainorgname ,
                                        SUM(t3.fbizqty - t3.fbizoutqty + fbizreturnqty) qty ,
                                        SUM(( t3.fbizqty - t3.fbizoutqty + fbizreturnqty ) * t3.fprice) price
                                FROM    {orderTable} t3
                                        INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                WHERE   (t3.fprice IS NULL OR t3.fprice=0)--ISNULL(t6.fparentid,'')<>''   and 
                                GROUP BY t3.fmainorgid , t3.fmainorgname , fmainorgnumber,t6.fid 
                                        UNION ALL
                                        SELECT  'Y' fhprice,'SAL' fhpricetype,t6.fid  fcategoryid ,
                                        t3.fmainorgid ,
                                        t3.fmainorgnumber ,
                                        t3.fmainorgname ,
                                        SUM(t3.fbizqty - t3.fbizoutqty + fbizreturnqty) qty ,
                                        SUM(( t3.fbizqty - t3.fbizoutqty + fbizreturnqty ) * t3.fprice) price
                                FROM    {orderTable} t3
                                        INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                --WHERE   ISNULL(t6.fparentid,'')<>'' 
                                GROUP BY t3.fmainorgid , t3.fmainorgname , fmainorgnumber,t6.fid
                                    UNION ALL
                                        SELECT  'Y' fhprice,'TC' fhpricetype,t6.fid  fcategoryid ,
                                        t3.fmainorgid ,
                                        t3.fmainorgnumber ,
                                        t3.fmainorgname ,
                                        SUM(t3.fbizqty - t3.fbizoutqty + fbizreturnqty) qty ,
                                        0 price
                                FROM    {orderTable} t3
                                        LEFT JOIN ( 
-- 自建商品+总部停产商品：即《商品》档案的【产品销售组织】中所有的记录的【禁用状态】均为已禁用
								 select cc.fid,cc.fname from (select aa.fid,aa.fname,count(1) allcount from {matTable} aa WITH(nolock) 
                                 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                                 where aa.fmainorgid = '{topCompanyId}'
								 group by aa.fid,aa.fname) cc 
								 left join (select aa.fid,bb.fdisablestatus,COUNT(1) acount from {matTable} aa  WITH(nolock)
                                 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                                 where aa.fmainorgid ='{topCompanyId}'
								 group by aa.fid, bb.fdisablestatus) dd
								 on cc.fid = dd.fid 
								 where cc.allcount = dd.acount and dd.fdisablestatus=2
                                 ) t5 on t3.fproductid=t5.fid 
                                        INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                --WHERE   ISNULL(t6.fparentid,'')<>''  
                                GROUP BY t3.fmainorgid , t3.fmainorgname , fmainorgnumber,t6.fid ) t";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql);
                }

                var tmpinv = this.DBService.CreateTemporaryTableName(this.Context, "tmpinv");
                {
                    var tableStrSql = $@"select t3.fcategoryid ,t1.fmainorgid ,--t3.fid,
                                                t1.fmainorgnumber ,
                                                t1.fmainorgname ,t1.fwarehousetype ,SUM(t1.fqty) fqty  ,SUM(t1.fqty*t3.fpurprice) fpurprice,SUM(t1.fqty*t3.fsalprice)  fsalprice
												into {tmpinv}
												from 
												{invTable} t1  WITH(nolock)
                                                INNER JOIN {pricematerial} t3 ON t1.fmaterialid = t3.fid   
												 WHERE   t1.fwarehousetype IS NOT NULL --AND t1.fqty>0  
												   GROUP BY t3.fcategoryid , t3.fid, t1.fmainorgid ,  t1.fmainorgnumber , t1.fmainorgname ,t1.fwarehousetype";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql);
                }


                var warehouseTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpWarehouse");
                {
                    var tableStrSql = $@"SELECT t6.fid fcategoryid ,
                                                t1.fmainorgid ,
                                                t5.fnumber AS fmainorgnumber ,
                                                t5.fname AS fmainorgname ,
                                                t2.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                                SUM(t1.fqty * t4.fpurprice) price
                                                into {warehouseTable}
                                        FROM    {invTable} t1  WITH(nolock)
                                                LEFT JOIN T_YDJ_STOREHOUSE t2  WITH(nolock) ON t1.fstorehouseid = t2.fid AND t1.fmainorgid = t1.fmainorgid
                                                LEFT JOIN {materialTable} t3 ON t1.fmaterialid = t3.fid   and t1.fmainorgid=t3.fmainorgid
                                                LEFT JOIN {priceTable} t4 ON t3.fid = t4.fproductid_e
                                                LEFT JOIN {tmpAgentTable} t5  WITH(nolock) ON t5.fid = t1.fmainorgid 
                                                LEFT JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                                WHERE   t2.fwarehousetype IS NOT NULL AND t1.fqty>0
                                        GROUP BY t6.fid ,  t1.fmainorgid ,  t5.fnumber , t5.fname ,t2.fwarehousetype ;";

                    tableStrSql = $@"
                                               select * into {warehouseTable} from (
                                               SELECT 'Y' fhprice,'ALL' fhpricetype,t6.fid fcategoryid ,
                                                t1.fmainorgid ,
                                                t5.fnumber AS fmainorgnumber ,
                                                t5.fname AS fmainorgname ,
                                                t2.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                                SUM(t1.fqty * t4.fpurprice) price
                                        FROM    {invTable} t1  WITH(nolock)
                                                INNER JOIN T_YDJ_STOREHOUSE t2  WITH(nolock) ON t1.fstorehouseid = t2.fid AND t1.fmainorgid = t1.fmainorgid
                                                INNER JOIN {materialTable} t3 ON t1.fmaterialid = t3.fid  
                                                LEFT JOIN {priceTable} t4 ON t3.fid = t4.fproductid_e 
                                                INNER JOIN {tmpAgentTable} t5  WITH(nolock) ON t5.fid = t1.fmainorgid 
                                                INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                                WHERE   t2.fwarehousetype IS NOT NULL AND t1.fqty>0  
                                        GROUP BY t6.fid ,  t1.fmainorgid ,  t5.fnumber , t5.fname ,t2.fwarehousetype
                                               UNION ALL
                                               SELECT 'N' fhprice,'PUR' fhpricetype,t6.fid fcategoryid ,
                                                t1.fmainorgid ,
                                                t5.fnumber AS fmainorgnumber ,
                                                t5.fname AS fmainorgname ,
                                                t2.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                                SUM(t1.fqty * t4.fpurprice) price
                                        FROM    {invTable} t1  WITH(nolock)
                                                INNER JOIN T_YDJ_STOREHOUSE t2  WITH(nolock) ON t1.fstorehouseid = t2.fid AND t1.fmainorgid = t1.fmainorgid
                                                INNER JOIN {materialTable} t3 ON t1.fmaterialid = t3.fid  
                                                LEFT JOIN {priceTable} t4 ON t3.fid = t4.fproductid_e 
                                                INNER JOIN {tmpAgentTable} t5  WITH(nolock) ON t5.fid = t1.fmainorgid 
                                                INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                                WHERE   t2.fwarehousetype IS NOT NULL AND t1.fqty>0   and (t4.fpurprice IS NULL OR t4.fpurprice=0) 
                                        GROUP BY t6.fid ,  t1.fmainorgid ,  t5.fnumber , t5.fname ,t2.fwarehousetype
                                               UNION ALL
                                               SELECT 'N' fhprice,'SAL' fhpricetype,t6.fid fcategoryid ,
                                                t1.fmainorgid ,
                                                t5.fnumber AS fmainorgnumber ,
                                                t5.fname AS fmainorgname ,
                                                t2.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                                SUM(t1.fqty * t41.fsalprice) price
                                        FROM    {invTable} t1  WITH(nolock)
                                                INNER JOIN T_YDJ_STOREHOUSE t2  WITH(nolock) ON t1.fstorehouseid = t2.fid AND t1.fmainorgid = t1.fmainorgid
                                                INNER JOIN {materialTable} t3 ON t1.fmaterialid = t3.fid   
                                                LEFT JOIN {salepriceTable} t41 ON t3.fid = t41.fproductid
                                                INNER JOIN {tmpAgentTable} t5  WITH(nolock) ON t5.fid = t1.fmainorgid 
                                                INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                                WHERE   t2.fwarehousetype IS NOT NULL AND t1.fqty>0   and (t41.fsalprice IS NULL OR t41.fsalprice=0)
                                        GROUP BY t6.fid ,  t1.fmainorgid ,  t5.fnumber , t5.fname ,t2.fwarehousetype
                                            UNION ALL
                                               SELECT 'Y' fhprice,'SAL' fhpricetype,t6.fid fcategoryid ,
                                                t1.fmainorgid ,
                                                t5.fnumber AS fmainorgnumber ,
                                                t5.fname AS fmainorgname ,
                                                t2.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                                SUM(t1.fqty * t41.fsalprice) price
                                        FROM    {invTable} t1  WITH(nolock)
                                                INNER JOIN T_YDJ_STOREHOUSE t2  WITH(nolock) ON t1.fstorehouseid = t2.fid AND t1.fmainorgid = t1.fmainorgid
                                                INNER JOIN {materialTable} t3 ON t1.fmaterialid = t3.fid   
                                                LEFT JOIN {salepriceTable} t41 ON t3.fid = t41.fproductid
                                                INNER JOIN {tmpAgentTable} t5  WITH(nolock) ON t5.fid = t1.fmainorgid 
                                                INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                                                WHERE   t2.fwarehousetype IS NOT NULL AND t1.fqty>0  
                                        GROUP BY t6.fid ,  t1.fmainorgid ,  t5.fnumber , t5.fname ,t2.fwarehousetype) t;";
                    tableStrSql = $@"
                                               select * into {warehouseTable} from (
                                               SELECT 'Y' fhprice,'ALL' fhpricetype,t1.fcategoryid ,
                                                t1.fmainorgid ,
                                                t1.fmainorgnumber ,
                                                t1.fmainorgname  ,
                                                t1.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                               SUM(t1.fpurprice) price
                                        FROM     {tmpinv} t1 
                                        GROUP BY t1.fcategoryid ,  t1.fmainorgid ,  t1.fmainorgnumber , t1.fmainorgname ,t1.fwarehousetype
                                               UNION ALL
                                               SELECT 'N' fhprice,'PUR' fhpricetype,t1.fcategoryid ,
                                                 t1.fmainorgid ,
                                                t1.fmainorgnumber ,
                                                t1.fmainorgname  ,
                                                t1.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                                SUM( t1.fpurprice) price
                                        FROM    {tmpinv} t1
                                                WHERE    ISNULL(t1.fpurprice,0)=0
                                        GROUP BY t1.fcategoryid ,  t1.fmainorgid ,  t1.fmainorgnumber , t1.fmainorgname ,t1.fwarehousetype
                                               UNION ALL
                                               SELECT 'N' fhprice,'SAL' fhpricetype,t1.fcategoryid ,
                                                 t1.fmainorgid ,
                                                t1.fmainorgnumber ,
                                                t1.fmainorgname  ,
                                                t1.fwarehousetype ,
                                                SUM(t1.fqty) qty ,
                                                SUM(t1.fsalprice) price
                                        FROM   {tmpinv} t1
                                                WHERE    ISNULL(t1.fsalprice,0)=0
                                        GROUP BY t1.fcategoryid ,  t1.fmainorgid ,  t1.fmainorgnumber , t1.fmainorgname ,t1.fwarehousetype
                                UNION ALL 
                                select *,'0' price from (
								 SELECT 'N' fhprice,'TC' fhpricetype,t3.fcategoryid ,
                                                t1.fmainorgid ,
                                                t1.fmainorgnumber ,
                                                t1.fmainorgname ,
                                                t2.fwarehousetype ,
                                                SUM(t1.fqty) qty 
                                        FROM    {invTable} t1  WITH(nolock)
                                                INNER JOIN T_YDJ_STOREHOUSE t2  WITH(nolock) ON t1.fstorehouseid = t2.fid AND t1.fmainorgid = t1.fmainorgid
                                                inner join ((select cc.fid,cc.fname,cc.fcategoryid from (select aa.fid,aa.fname,aa.fcategoryid,count(1) allcount from {matTable} aa  WITH(nolock)
															 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
															 where aa.fmainorgid ='{topCompanyId}'
															 group by aa.fid,aa.fname,aa.fcategoryid) cc 
															 left join (select aa.fid,aa.fcategoryid,bb.fdisablestatus,COUNT(1) acount from {matTable} aa WITH(nolock) 
															 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
															 where aa.fmainorgid ='{topCompanyId}'
															 group by aa.fid,aa.fcategoryid, bb.fdisablestatus) dd
															 on cc.fid = dd.fid 
															 where cc.allcount = dd.acount and dd.fdisablestatus=2) ) t3 on t1.fmaterialid = t3.fid 
                                                WHERE   t2.fwarehousetype IS NOT NULL --AND t1.fqty>0  
												GROUP BY t3.fcategoryid ,  t1.fmainorgid ,  t1.fmainorgnumber , t1.fmainorgname ,t2.fwarehousetype) t
                                                ) t;";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql);
                }
                var tmpzj = this.DBService.CreateTemporaryTableName(this.Context, "tmpzj");
                {
                    var tableStrSql = $@" select t3.fcategoryid ,t1.fmainorgid, t1.fmainorgnumber , t1.fmainorgname ,sum(t1.fqty) fqty,t3.fpurprice  ,t3.fsalprice  into {tmpzj}
								from {invTable}  t1  WITH(nolock)
                                 inner join {pricematerial} t3 WITH(nolock) on t1.fmaterialid=t3.fid  -- and t1.fmainorgid=t3.fmainorgid
                                 inner join ( 
                                -- 自建商品+总部停产商品：即《商品》档案的【产品销售组织】中所有的记录的【禁用状态】均为已禁用
								 select cc.fid,cc.fname from (select aa.fid,aa.fname,count(1) allcount from {matTable} aa  WITH(nolock)
                                 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                                 where aa.fmainorgid =''{topCompanyId}''
								 group by aa.fid,aa.fname) cc 
								 left join (select aa.fid,bb.fdisablestatus,COUNT(1) acount from {matTable} aa  WITH(nolock)
                                 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                                 where aa.fmainorgid =''{topCompanyId}''
								 group by aa.fid, bb.fdisablestatus) dd
								 on cc.fid = dd.fid 
								 where cc.allcount = dd.acount and dd.fdisablestatus=2
                                 ) t5 on t3.fid=t5.fid 
								 group by t1.fmainorgid,t1.fmainorgnumber ,t1.fmainorgname ,t3.fcategoryid,t3.fpurprice  ,t3.fsalprice";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql);

                }
                var zjtcTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpZjtc");
                {
                    var tableStrSql = $@"select t7.fid fcategoryid ,t1.fmainorgid, t6.fnumber AS fmainorgnumber , t6.fname AS fmainorgname ,sum(t1.fqty) qty,SUM(t1.fqty*t4.fpurprice) price into {zjtcTable} from {invTable} t1  WITH(nolock)
                                 inner join t_ydj_storehouse t2  WITH(nolock) on t1.fstorehouseid = t2.fid --and t1.fmainorgid =@fmainorgid
                                 inner join {materialTable} t3 on t1.fmaterialid=t3.fid 
                                 INNER JOIN {tmpAgentTable} t6  WITH(nolock) ON t6.fid = t1.fmainorgid 
                                 INNER JOIN SER_YDJ_CATEGORY t7  WITH(nolock) ON t7.fid = t3.fcategoryid 
                                 left join {priceTable} t4 on t3.fid = t4.fproductid_e 
                                 inner join (
                                 select fid from {matTable} t3  WITH(nolock) 
								 union -- 自建商品+总部停产商品：即《商品》档案的【产品销售组织】中所有的记录的【禁用状态】均为已禁用
								 select cc.fid from (select aa.fid,count(1) allcount from {materialTable} aa 
                                 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                                 where aa.fmainorgid ='{topCompanyId}'
								 group by aa.fid) cc 
								 left join (select aa.fid,bb.fdisablestatus,COUNT(1) acount from {materialTable} aa 
                                 left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                                 where aa.fmainorgid ='{topCompanyId}'
								 group by aa.fid, bb.fdisablestatus) dd
								 on cc.fid = dd.fid 
								 where cc.allcount = dd.acount and dd.fdisablestatus=2
                                 ) t5 on t3.fid=t5.fid 
								 group by t1.fmainorgid,t6.fnumber ,t6.fname ,t7.fid;";
                    tableStrSql = $@"
                                  select * into {zjtcTable} from (
                                select 'Y' fhprice,'ALL' fhpricetype,t1.fcategoryid ,t1.fmainorgid, t1.fmainorgnumber , t1.fmainorgname ,sum(t1.fqty) qty,SUM(t1.fqty*t1.fpurprice) price
                                from {tmpzj} t1
								 group by t1.fmainorgid,t1.fmainorgnumber , t1.fmainorgname ,t1.fcategoryid 
                                UNION ALL
                                select 'N' fhprice,'PUR' fhpricetype,t1.fcategoryid ,t1.fmainorgid, t1.fmainorgnumber , t1.fmainorgname ,sum(t1.fqty) qty,SUM(t1.fqty*t1.fpurprice) price  from {tmpzj} t1
                                WHERE   (t1.fpurprice IS NULL OR t1.fpurprice=0)
								 group by t1.fmainorgid,t1.fmainorgnumber , t1.fmainorgname ,t1.fcategoryid 
                                UNION ALL
                                select 'N' fhprice,'SAL' fhpricetype,t1.fcategoryid ,t1.fmainorgid, t1.fmainorgnumber , t1.fmainorgname ,sum(t1.fqty) qty,SUM(t1.fqty*t1.fsalprice) price  from {tmpzj} t1
                                WHERE   (t1.fsalprice IS NULL OR t1.fsalprice=0)
								  group by t1.fmainorgid,t1.fmainorgnumber , t1.fmainorgname ,t1.fcategoryid 
                                UNION ALL
                                select 'Y' fhprice,'SAL' fhpricetype,t1.fcategoryid ,t1.fmainorgid, t1.fmainorgnumber , t1.fmainorgname ,sum(t1.fqty) qty,SUM(t1.fqty*t1.fsalprice) price  from {tmpzj} t1
								 group by t1.fmainorgid,t1.fmainorgnumber , t1.fmainorgname ,t1.fcategoryid 
                                            ) t;";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableStrSql);
                }

                //var ordtable = this.DBService.CreateTemporaryTableName(this.Context, "tmpord");
                //{
                //    var sql = $@"select t1.fmainorgid,t2.fproductid,t2.fbizqty,t2.fbizoutqty,fbizreturnqty,t2.fprice into {ordtable}  
                //                 from t_ydj_order t1  WITH(nolock)
                //                 inner join t_ydj_orderentry t2  WITH(nolock) on t1.fid = t2.fid
                //                  where t1.fcancelstatus = '0' and t2.fclosestatus != 4 and t1.fneedtransferorder = 0";
                //    var tempTbl = this.DBService.ExecuteDynamicObject(Context, sql);

                //}
                //       var tcwqTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpTcwq");
                //       {

                //           string tablesql = $@"select  fmainorgid,SUM(fbizqty - fbizoutqty + fbizreturnqty) qty,SUM(fprice) fprice,t2.fcategoryid into {tcwqTable} from (
                // select t2.*,t4.fcategoryid
                //from (select t1.fmainorgid,t2.fproductid,t2.fbizqty,t2.fbizoutqty,fbizreturnqty,t2.fprice   
                //from    t_ydj_order t1  WITH(nolock)
                //                        inner join t_ydj_orderentry t2  WITH(nolock) on t1.fid = t2.fid
                // where   t1.fcancelstatus = '0' and t2.fclosestatus!=4 and t1.fneedtransferorder=0) as t2
                //inner join (select cc.fid,cc.fname from (select aa.fid,aa.fname,count(1) allcount from {matTable} aa  WITH(nolock)
                //                        left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                //                        where aa.fmainorgid ='{topCompanyId}'
                //group by aa.fid,aa.fname) cc 
                //left join (select aa.fid,bb.fdisablestatus,COUNT(1) acount from {matTable} aa WITH(nolock) 
                //                        left join t_bd_materialsaleorg bb WITH(nolock) on aa.fid = bb.fid 
                //                        where aa.fmainorgid ='{topCompanyId}'
                //group by aa.fid, bb.fdisablestatus) dd
                //on cc.fid = dd.fid 
                //where cc.allcount = dd.acount and dd.fdisablestatus=2) t3 on t2.fproductid = t3.fid 
                //inner join {matTable} t4 on t3.fid = t4.fid)  t2 GROUP BY fmainorgid,fcategoryid";
                //           var tempTbl = this.DBService.ExecuteDynamicObject(Context, tablesql);
                //           //tablesql = $@"
                //           //                    SELECT  'Y' fhprice,'ALL' fhpricetype,t6.fid  fcategoryid ,
                //           //                    t3.fmainorgid ,
                //           //                    t3.fmainorgnumber ,
                //           //                    t3.fmainorgname ,
                //           //                    SUM(t3.fbizqty - t3.fbizoutqty + fbizreturnqty) qty ,
                //           //                    SUM(( t3.fbizqty - t3.fbizoutqty + fbizreturnqty ) * t4.fpurprice) price
                //           //            FROM    {orderTable} t3
                //           //                    LEFT JOIN {priceTable} t4 ON t3.fproductid = t4.fproductid_e 
                //           //                    INNER JOIN SER_YDJ_CATEGORY t6  WITH(nolock) ON t6.fid = t3.fcategoryid 
                //           //            WHERE   ISNULL(t6.fparentid,'')<>''  
                //           //            GROUP BY t3.fmainorgid , t3.fmainorgname , fmainorgnumber,t6.fid ";
                //           //var tempTbl = this.DBService.ExecuteDynamicObject(Context, tablesql);
                //       }
                #endregion
                string tname = "";
                var agentTable = this.DBService.CreateTemporaryTableName(this.Context, "tmpAgent");
                {

                    {
                        var createTableSql = $@"	CREATE TABLE {agentTable}	
        (fid VARCHAR(200), fcrmdistributorid VARCHAR(200), fcrmagnetnumber VARCHAR(200), fcrmagnetname VARCHAR(300), fname VARCHAR(300), fnumber VARCHAR(300), fcity VARCHAR(1000), fcity_txt VARCHAR(1000), fcitylevel VARCHAR(100))";
                        var tempTbl = this.DBService.ExecuteDynamicObject(Context, createTableSql);
                    }
                    {
                        var inventorylistTableSql = $@" insert into {agentTable} SELECT * FROM (
                            SELECT  a.fid,isnull(fcrmdistributorid,'') fcrmdistributorid,isnull(b.fnumber,'') fcrmagnetnumber,isnull(fcrmdistributorid_txt,'') as fcrmagnetname,a.fname,a.fnumber,fcity ,a.fcity_txt,c.fenumitem fcitylevel
                            FROM {tmpAgentTable}  AS a WITH(nolock)  left JOIN T_MS_CRMDISTRIBUTOR AS b WITH(nolock) ON   a.fcrmdistributorid = b.fid left JOIN ( select b.fenumitem,a.fid from  T_YDJ_CITY as a left join (select * from v_bd_enum where fcategory='城市等级') as b on a.flevel=b.fid) AS c  ON   a.fcity = c.fid
                            WHERE a.fisreseller=0

                            UNION all
                            SELECT  a.fid,isnull(b.fcrmdistributorid,'') fcrmdistributorid,isnull(c.fnumber,'') fcrmagnetnumber,isnull(b.fcrmdistributorid_txt,'') as fcrmagnetname,a.fname,a.fnumber,a.fcity ,a.fcity_txt,d.fenumitem fcitylevel
                            FROM {tmpAgentTable}  AS a INNER JOIN {tmpAgentTable}  AS b WITH(nolock) ON a.forgid=b.fid  left JOIN T_MS_CRMDISTRIBUTOR AS c WITH(nolock) ON   b.fcrmdistributorid = c.fid left JOIN ( select b.fenumitem,a.fid from T_YDJ_CITY as a WITH(nolock) left join (select * from v_bd_enum where fcategory='城市等级') as b on a.flevel=b.fid) AS d  ON   a.fcity = d.fid
                            WHERE a.fisreseller=1
                            ) t  ";
                        var tempTbl = this.DBService.ExecuteDynamicObject(Context, inventorylistTableSql);
                    }
                    //处理经销商city的问题
                    List<DataRow> drs = new List<DataRow>();
                    DataTable dt = this.DBService.ExecuteDataTable(this.Context, $@"SELECT  * from {agentTable} ");
                    DataTable newdt = dt.Copy();
                    foreach (DataRow item in dt.Rows)
                    {
                        string city = Convert.ToString(item["fcity"]);
                        if (city.Contains(","))
                        {
                            var citys = city.Split(',');
                            for (int i = 0; i < citys.Length; i++)
                            {
                                var dr1 = newdt.NewRow();
                                dr1["fid"] = item["fid"];
                                dr1["fnumber"] = item["fnumber"];
                                dr1["fname"] = item["fname"];
                                dr1["fcity"] = citys[i];
                                newdt.Rows.Add(dr1);
                            }
                            //var dr2 = newdt.NewRow();
                            //dr2["fid"] = item["fid"];
                            //dr2["fnumber"] = item["fnumber"];
                            //dr2["fname"] = item["fname"];
                            //dr2["fcity"] = citys[1];
                            ////newdt.Rows.Remove(item);
                            //newdt.Rows.Add(dr2);
                        }
                    }

                    string updSql = "";
                    var crmObjSql = " select * from t_ms_crmdistributor WITH(nolock)";
                    var crmobjs = this.DBService.ExecuteDynamicObject(this.Context, crmObjSql);
                    var citylevelsql = "   select b.fenumitem,a.* from T_YDJ_CITY as a WITH(nolock) inner join (select * from v_bd_enum where fcategory='城市等级') as b on a.flevel=b.fid ";
                    var citylevelobjs = this.DBService.ExecuteDynamicObject(this.Context, citylevelsql);
                    foreach (DataRow item in newdt.Rows)
                    {
                        string city1 = Convert.ToString(item["fcity"]);

                        if (city1.Contains(","))
                        {
                            var citys = city1.Split(',');
                            var t1 = citylevelobjs.Where(_ => city1.Contains(Convert.ToString(_["fid"]))).OrderBy(a => a["fenumitem"]).ToList();
                            if (t1.Count > 0)
                            {
                                updSql += $@" update {agentTable}	 set fcitylevel='{Convert.ToString(t1[0]["fenumitem"])}' where fid='{item["fid"]}'";
                            }
                            //string citylevel = "";
                            //for (int i = 0; i < citys.Length; i++)
                            //{
                            //    var citylevelobjItem = citylevelobjs.Where(_ => Convert.ToString(_["fid"]) == citys[i]).FirstOrDefault();
                            //    var t1 = citylevelobjs.Where(_ => city1.Contains(Convert.ToString(_["fid"]))).ToList();
                            //    if (citylevelobjItem != null)
                            //    {
                            //        citylevel += citylevelobjItem["fenumitem"] + ",";
                            //    }
                            //}
                            //if (citylevel.Length > 0)
                            //{
                            //    item["fcitylevel"] = citylevel.Substring(0, citylevel.Length - 1);
                            //    updSql += $@" update {agentTable}	 set fcitylevel='{item["fcitylevel"]}' where fid='{item["fid"]}'";
                            //}
                        }
                        else
                        {
                            string citylevel = "";
                            var cityd = citylevelobjs.Where(_ => Convert.ToString(_["fid"]) == city1).FirstOrDefault();
                            if (cityd != null)
                            {
                                citylevel = cityd["fenumitem"] as string;
                                item["fcitylevel"] = citylevel;
                                //updSql += $@" update {agentTable}	 set fcitylevel='{item["fcitylevel"]}' where fid='{item["fid"]}'";
                            }
                        }
                        string city = Convert.ToString(item["fcrmdistributorid"]);
                        if (string.IsNullOrWhiteSpace(city))
                        {
                            item["fcrmdistributorid"] = " ";
                            item["fcrmagnetnumber"] = " ";
                            item["fcrmagnetname"] = " ";
                            continue;

                        }
                        if (city.Contains(","))
                        {
                            var citys = city.Split(',');
                            string crmnumber = "";
                            for (int i = 0; i < citys.Length; i++)
                            {
                                var crmobjItem = crmobjs.Where(_ => Convert.ToString(_["fid"]) == citys[i]).FirstOrDefault();
                                if (crmobjItem != null)
                                {
                                    crmnumber += crmobjItem["fnumber"] + ",";
                                }
                            }
                            if (crmnumber.Length > 0)
                            {
                                item["fcrmagnetnumber"] = crmnumber.Substring(0, crmnumber.Length - 1);
                            }
                            updSql += $@" update {agentTable}	 set fcrmagnetnumber='{item["fcrmagnetnumber"]}' where fid='{item["fid"]}'";
                        }
                        else
                        {
                            string crmnumber = "";
                            var crmd = crmobjs.Where(_ => Convert.ToString(_["fid"]) == city).FirstOrDefault();
                            if (crmd != null)
                            {
                                crmnumber = crmd["fnumber"] as string;
                                item["fcrmagnetnumber"] = crmnumber;
                            }
                        }
                    }

                    this.DBServiceEx.Execute(Context, updSql);
                    tname = agentTable;
                    dt.Clear();
                    dt.Dispose();
                    dt = null;
                    newdt.Clear();
                    newdt.Dispose();
                    newdt = null;
                    //tname = this.DBService.CreateTempTableWithDataTable(Context, newdt);

                }


                //补偿逻辑，把即时库存不存在，但采购订单存在的类别，补偿插入即时库存临时表
                {
                    string insSql = $@"insert into {inventorylistTable}    select
								a.fmainorgid,t5.fnumber fmainorgnumber,t5.fname fmainorgname,d.fcategoryid
								from T_YDJ_PURCHASEORDER as a with(nolock) 
								inner join t_ydj_poorderentry as b with(nolock)  on a.fid=b.fid  
								INNER JOIN {materialTable} AS d with(nolock)  ON b.fmaterialid=d.fid 
								  left join {inventorylistTable} as t4 on t4.fcategoryid=d.fcategoryid and t4.fmainorgid=a.fmainorgid
                                 inner join {tmpAgentTable} as t5  WITH(nolock) on t5.fid = a.fmainorgid
								   where   ISNULL(t4.fcategoryid,'')='' and fhqderstatus in ('03','02')
								  group by a.fmainorgid,t5.fnumber ,t5.fname ,d.fcategoryid,t4.fcategoryid";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, insSql);
                }
                string purchaseordertable = this.DBService.CreateTemporaryTableName(this.Context, "tmpPurchaseOrder");
                {
                    string tmppurtable = this.DBService.CreateTemporaryTableName(this.Context, "tmpPurTable");
                    string tmppursql = $@"select a.fmainorgid,d.fcategoryid,b.fmaterialid,fsourcenumber,fsourcetype,fhqderstatus,fqty,finstockqty,fsalprice
								into {tmppurtable}
								from T_YDJ_PURCHASEORDER as a with(nolock) 
								inner join t_ydj_poorderentry as b with(nolock)  on a.fid=b.fid  
								INNER JOIN {materialTable} AS d with(nolock)  ON b.fmaterialid=d.fid  ";
                    var tempTbl1 = this.DBService.ExecuteDynamicObject(Context, tmppursql);
                    string purtablesql = $@"SELECT  * INTO {purchaseordertable} FROM (select 't1' ftype,a.fmainorgid,d.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount,sum((b.fqty-b.finstockqty)*salprice.fsalprice) as retailprice from T_YDJ_PURCHASEORDER as a WITH(nolock) inner join t_ydj_poorderentry as b WITH(nolock) on a.fid=b.fid INNER JOIN {materialTable} AS d ON b.fmaterialid=d.fid LEFT JOIN {priceTable} AS c ON b.fmaterialid=c.fproductid_e LEFT JOIN {salepriceTable} AS salprice ON b.fmaterialid=salprice.fproductid where  fsourcenumber='' and fsourcetype='' and fhqderstatus in ('03','02')
                                group by a.fmainorgid
								UNION all
								select 't2' ftype,a.fmainorgid,d.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount,sum((b.fqty-b.finstockqty)*salprice.fsalprice) as retailprice from T_YDJ_PURCHASEORDER as a WITH(nolock) inner join t_ydj_poorderentry as b WITH(nolock) on a.fid=b.fid  INNER JOIN {materialTable} AS d ON b.fmaterialid=d.fid   LEFT JOIN {priceTable} AS c ON b.fmaterialid=c.fproductid_e LEFT JOIN {salepriceTable} AS salprice ON b.fmaterialid=salprice.fproductid where  fsourcenumber<>'' and fsourcetype<>''  and fhqderstatus in ('03','02')
                                group by a.fmainorgid) AS t";
                    purtablesql = $@"SELECT  * into {purchaseordertable} FROM (
SELECT  *  FROM (select 't1' ftype,'Y' fhprice,a.fmainorgid,d.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount,sum((b.fqty-b.finstockqty)*salprice.fsalprice) as retailprice from T_YDJ_PURCHASEORDER as a inner join t_ydj_poorderentry as b on a.fid=b.fid
 INNER JOIN {materialTable} AS d ON b.fmaterialid=d.fid
LEFT JOIN {priceTable} c ON b.fmaterialid=c.fproductid_e and c.fpurprice>0 
LEFT JOIN {salepriceTable} AS salprice ON b.fmaterialid=salprice.fproductid  and salprice.fsalprice>0 where  fsourcenumber='' and fsourcetype='' and fhqderstatus in ('03','02')
                                group by a.fmainorgid,d.fcategoryid
								UNION all
								select 't2' ftype,'Y' fhprice,a.fmainorgid,d.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount,sum((b.fqty-b.finstockqty)*salprice.fsalprice) as retailprice from T_YDJ_PURCHASEORDER as a inner join t_ydj_poorderentry as b on a.fid=b.fid  INNER JOIN {materialTable} AS d ON b.fmaterialid=d.fid   LEFT JOIN {priceTable} AS c ON b.fmaterialid=c.fproductid_e  and c.fpurprice>0 LEFT JOIN {salepriceTable} AS salprice ON b.fmaterialid=salprice.fproductid  and salprice.fsalprice>0 where  fsourcenumber<>'' and fsourcetype<>''  and fhqderstatus in ('03','02')
                                group by a.fmainorgid,d.fcategoryid) AS t
union all

SELECT  *  FROM (select 't1' ftype,'N' fhprice,a.fmainorgid,d.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount,sum((b.fqty-b.finstockqty)*d.fsalprice) as retailprice from T_YDJ_PURCHASEORDER as a inner join t_ydj_poorderentry as b on a.fid=b.fid   INNER JOIN {materialTable} AS d ON b.fmaterialid=d.fid  
LEFT JOIN {priceTable} AS c ON b.fmaterialid=c.fproductid_e  and   (c.fpurprice IS NULL OR c.fpurprice=0)  LEFT JOIN {salepriceTable} AS salprice ON b.fmaterialid=salprice.fproductid  and   (salprice.fsalprice IS NULL OR salprice.fsalprice=0) where a.fstatus='E' and fsourcenumber='' and fsourcetype='' and fhqderstatus in ('03','02')
                                group by a.fmainorgid,d.fcategoryid
								UNION all
								select 't2' ftype,'N' fhprice,a.fmainorgid,d.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount,sum((b.fqty-b.finstockqty)*salprice.fsalprice) as retailprice from T_YDJ_PURCHASEORDER as a inner join t_ydj_poorderentry as b on a.fid=b.fid  INNER JOIN {materialTable} AS d ON b.fmaterialid=d.fid  LEFT JOIN {priceTable} AS c ON b.fmaterialid=c.fproductid_e  and   (c.fpurprice IS NULL OR c.fpurprice=0)  LEFT JOIN {salepriceTable} AS salprice ON b.fmaterialid=salprice.fproductid  and   (salprice.fsalprice IS NULL OR salprice.fsalprice=0)  where a.fstatus='E'  and fsourcenumber<>'' and fsourcetype<>''  and fhqderstatus in ('03','02')
                                group by a.fmainorgid,d.fcategoryid) AS t1
)AS t";
                    purtablesql = $@"
SELECT  * into {purchaseordertable} FROM (
--所有
select 't1' ftype,'Y' fhprice,'PUR' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount
from {tmppurtable} as b   
LEFT JOIN {priceTable} c ON b.fmaterialid=c.fproductid_e
where  fsourcenumber='' and fsourcetype='' and fhqderstatus in ('03','02')  --and c.fpurprice>0 
                                group by b.fmainorgid,b.fcategoryid
								UNION all
								select 't2' ftype,'Y' fhprice,'PUR' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount
								from {tmppurtable} as b   
								LEFT JOIN {priceTable} AS c ON b.fmaterialid=c.fproductid_e
								where   fhqderstatus in ('03','02')  --and c.fpurprice>0 
                                group by b.fmainorgid,b.fcategoryid
								union all
--采购价	
select 't1' ftype,'N' fhprice,'PUR' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount
from {tmppurtable} as b   
LEFT JOIN {priceTable} c ON b.fmaterialid=c.fproductid_e
where  fsourcenumber='' and fsourcetype='' and fhqderstatus in ('03','02')   and   (c.fpurprice IS NULL OR c.fpurprice=0)  
                                group by b.fmainorgid,b.fcategoryid
								UNION all
								select 't2' ftype,'N' fhprice,'PUR' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*c.fpurprice) as fdealamount
								from {tmppurtable} as b   
								LEFT JOIN {priceTable} AS c ON b.fmaterialid=c.fproductid_e
								where   fhqderstatus in ('03','02')   and   (c.fpurprice IS NULL OR c.fpurprice=0)  
                                group by b.fmainorgid,b.fcategoryid
								union all
--销售价【所有】								
select 't1' ftype,'Y' fhprice,'SAL' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*b.fsalprice) as fdealamount
from {tmppurtable} as b   
where  fsourcenumber='' and fsourcetype='' and fhqderstatus in ('03','02') 
                                group by b.fmainorgid,b.fcategoryid
								UNION all
								select 't2' ftype,'N' fhprice,'SAL' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*b.fsalprice) as fdealamount
								from {tmppurtable} as b   
								where   fhqderstatus in ('03','02')   
                                group by b.fmainorgid,b.fcategoryid
								union all
--销售价【无销售价】								
select 't1' ftype,'N' fhprice,'SAL' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*b.fsalprice) as fdealamount
from {tmppurtable} as b   
where  fsourcenumber='' and fsourcetype='' and fhqderstatus in ('03','02')   and   (b.fsalprice IS NULL OR b.fsalprice=0)  
                                group by b.fmainorgid,b.fcategoryid
								UNION all
								select 't2' ftype,'N' fhprice,'SAL' fhpricetype,b.fmainorgid,b.fcategoryid,sum(b.fqty-b.finstockqty) as fqty,sum((b.fqty-b.finstockqty)*b.fsalprice) as fdealamount
								from {tmppurtable} as b   
								where   fhqderstatus in ('03','02')   and   (b.fsalprice IS NULL OR b.fsalprice=0)  
                                group by b.fmainorgid,b.fcategoryid ) t";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, purtablesql);
                }
                string purinvtable = this.DBService.CreateTemporaryTableName(this.Context, "tmpPurInv");
                {
                    string tableSql = $@" select ftype,fhpricetype,fhprice,fqty,fdealamount,fmainorgid,MAX(fcategoryid) fcategoryid into {purinvtable}  from (
                            select ftype,fhpricetype,fhprice,fqty,fdealamount,fmainorgid,fcategoryid, row_number() over(partition by fmainorgid order by fmainorgid desc) as count 
                            from (select n.ftype,'ALL' fhpricetype,n.fhprice,n.fmainorgid,n.fqty,n.fdealamount,m.fcategoryid
                            from {inventorylistTable} m  WITH(nolock) left join {purchaseordertable} n  WITH(nolock) on m.fmainorgid = n.fmainorgid  AND m.fcategoryid=n.fcategoryid and n.fhprice='Y'   and n.fhpricetype='PUR'
                                where n.ftype='t1'
union all
select n.ftype,'SAL' fhpricetype,n.fhprice,n.fmainorgid,n.fqty,n.fdealamount,m.fcategoryid
                            from {inventorylistTable} m  WITH(nolock) left join {purchaseordertable} n  WITH(nolock) on m.fmainorgid = n.fmainorgid  AND m.fcategoryid=n.fcategoryid and n.fhprice='Y'   and n.fhpricetype='SAL'
                                where n.ftype='t1'
union all
select n.ftype,'SAL' fhpricetype,n.fhprice,n.fmainorgid,n.fqty,n.fdealamount,m.fcategoryid
                            from {inventorylistTable} m  WITH(nolock) left join {purchaseordertable} n  WITH(nolock) on m.fmainorgid = n.fmainorgid  AND m.fcategoryid=n.fcategoryid and n.fhprice='N'  and n.fhpricetype='SAL'
                                where n.ftype='t1'
                            ) t_ydj_purchasepriceentry --报价信息
                            ) pur GROUP BY ftype,fhpricetype,fhprice,fqty,fdealamount,fmainorgid ";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableSql);
                }
                string purinvtable1 = this.DBService.CreateTemporaryTableName(this.Context, "tmpPurInv1");
                {
                    string tableSql = $@" select ftype,fhpricetype,fhprice,fqty,fdealamount,fmainorgid,MAX(fcategoryid) fcategoryid into {purinvtable1}  from (
                            select ftype,fhpricetype,fhprice,fqty,fdealamount,fmainorgid,fcategoryid, row_number() over(partition by fmainorgid order by fmainorgid desc) as count 
                            from (select n.ftype,'ALL' fhpricetype,n.fhprice,n.fmainorgid,n.fqty,n.fdealamount,m.fcategoryid
                            from {inventorylistTable} m  WITH(nolock) left join {purchaseordertable} n  WITH(nolock) on m.fmainorgid = n.fmainorgid  AND m.fcategoryid=n.fcategoryid and n.fhprice='Y' and n.fhpricetype='PUR'
                               where  n.ftype='t2'
union all
select n.ftype,'SAL' fhpricetype,n.fhprice,n.fmainorgid,n.fqty,n.fdealamount,m.fcategoryid
                            from {inventorylistTable} m  WITH(nolock) left join {purchaseordertable} n  WITH(nolock) on m.fmainorgid = n.fmainorgid  AND m.fcategoryid=n.fcategoryid and n.fhprice='N'  and n.fhpricetype='SAL'
                                where n.ftype='t2'
union all
select n.ftype,'SAL' fhpricetype,n.fhprice,n.fmainorgid,n.fqty,n.fdealamount,m.fcategoryid
                            from {inventorylistTable} m  WITH(nolock) left join {purchaseordertable} n  WITH(nolock) on m.fmainorgid = n.fmainorgid  AND m.fcategoryid=n.fcategoryid and n.fhprice='Y'  and n.fhpricetype='SAL'
                                where n.ftype='t2'
                            ) t_ydj_purchasepriceentry --报价信息
                            ) pur GROUP BY ftype,fhpricetype,fhprice,fqty,fdealamount,fmainorgid ";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableSql);
                }
                string t_agentcitytable = this.DBService.CreateTemporaryTableName(this.Context, "tmpAgentCity");
                {
                    string tableSql = $@"/*dialect*/ select a.fid,a.fname,a.fnumber,a.fcrmdistributorid,b.fcity,c.fenumitem into {t_agentcitytable}
                                from
                                (select fid, fnumber, fname, fcrmdistributorid, fcity = cast('<v>' + replace(fcity, ',', '</v><v>') + '</v>' as xml) from(select fid, fcity, fnumber, fname, fcrmdistributorid from {tmpAgentTable} ) t) as a
                                outer apply(select fcity = T.C.value('.', 'varchar(20)') from a.fcity.nodes('v') as T(C)) as b
                                 left JOIN(select b.fenumitem, a.fid from  T_YDJ_CITY as a left join(select* from v_bd_enum where fcategory= '城市等级') as b on a.flevel = b.fid) AS c on b.fcity = c.fid ";
                    tableSql = $@"/*dialect*/ select * into {t_agentcitytable} from (select a.fid,a.fname,a.fnumber,a.fcrmdistributorid,b.fcity,c.fenumitem, row_number() over(partition by a.fid order by c.enumid asc) as fcount 
                                from
                                (select fid, fnumber, fname, fcrmdistributorid, fcity = cast('<v>' + replace(fcity, ',', '</v><v>') + '</v>' as xml) from(select fid, fcity, fnumber, fname, fcrmdistributorid from {tmpAgentTable} ) t) as a
                                outer apply(select fcity = T.C.value('.', 'varchar(20)') from a.fcity.nodes('v') as T(C)) as b
                                 left JOIN(select b.fenumitem, a.fid,b.fid as enumid from  T_YDJ_CITY as a left join(select* from v_bd_enum where fcategory= '城市等级') as b on a.flevel = b.fid) AS c on b.fcity = c.fid
	                                where b.fcity<>''
								     ) pur --where pur.fcount= 1";
                    var tempTbl = this.DBService.ExecuteDynamicObject(Context, tableSql);

                }
                #region 三种类型循环追加查询
                var strSql = $@"/*dialect*/with ";
                strSql = $@"";
                #endregion
                strSql += $@"/*dialect*/ insert into {this.DataSourceTableName}
                                (fid,fformid,fsaleregionalid,fserviceregionalid,fprovince,fagnetnumber,fagnetname,fcrmagnetnumber,fcrmagnetname,fcategoryid,forderqty,fncostorderqty,fordercost,fnpriceorderqty,forderprice,faftersaleqty,fncostaftersaleqty,faftersalecost,fnpriceaftersaleqty,faftersaleprice,fsampleqty,fncostsampleqty,fsamplecost,fnpricesampleqty,fsampleprice,fvirtualqty,fncostvirtualqty,fvirtualcost,fnpricevirtualqty,fvirtualprice,fproductionqty,fncostproductionqty,fproductioncost,fnpriceproductionqty,fproductionprice,fgeneralqty,fncostgeneralqty,fgeneralcost,fnpricegeneralqty,fgeneralprice,fstockupqty,fstockupcost,fstockupprice,fstaypurqty,fstaypurcost,fstaypurprice,fallqty,fallcost,fallprice,fcitylevel,fproductionorderqty,fproductiongeneralqty,fproductionsampleqty)";

                strSql += " select  t1.fid,t.fformid,fsaleregionalid,fserviceregionalid,fprovince,fagnetnumber,fagnetname,t1.fcrmagnetnumber,t1.fcrmagnetname,t.fcategoryid,forderqty,fncostorderqty,fordercost,fnpriceorderqty,forderprice,faftersaleqty,fncostaftersaleqty,faftersalecost,fnpriceaftersaleqty,faftersaleprice,fsampleqty,fncostsampleqty,fsamplecost,fnpricesampleqty,fsampleprice,fvirtualqty,fncostvirtualqty,fvirtualcost,fnpricevirtualqty,fvirtualprice,fproductionqty,fncostproductionqty,fproductioncost,fnpriceproductionqty,fproductionprice,fgeneralqty,fncostgeneralqty,fgeneralcost,fnpricegeneralqty,fgeneralprice,fstockupqty,fstockupcost,fstockupprice,fstaypurqty,fstaypurcost,fstaypurprice,fallqty,fallcost,fallprice,isnull(t1.fcitylevel,'') fcitylevel,isnull(fproductionorderqty,'0') fproductionorderqty,isnull(fproductiongeneralqty,'0') fproductiongeneralqty,isnull(fproductionsampleqty,'0') fproductionsampleqty from ";
                foreach (var item in fcategoryidArray)
                {
                    if (item == fcategoryidArray.First()) strSql += $@"(";

                    string where = "";
                    //string where1 = "";

                    if (item != "Other")
                    {
                        var othetIds = "";
                        fcategoryid_dics.TryGetValue(item, out List<string> ids);
                        foreach (var otherItem in ids)
                        {
                            othetIds += "'" + otherItem + "',";
                        }
                        othetIds = othetIds.Substring(0, othetIds.Length - 1);

                        where = $" where aa.fcategoryid in ({othetIds}) ";
                        //where = $" where  (aa.fcategoryid='{item}' OR aa.fcategoryid IN (SELECT fid FROM dbo.SER_YDJ_CATEGORY  WHERE fparentid='{item}'))  ";
                        //where1 = $" where  (m.fcategoryid='{item}' OR m.fcategoryid IN (SELECT fid FROM dbo.SER_YDJ_CATEGORY  WHERE fparentid='{item}'))  ";
                    }
                    else
                    {
                        var othetIds = "";
                        if (otherFcategoryIds.Count > 0)
                        {
                            foreach (var otherItem in otherFcategoryIds)
                            {
                                othetIds += "'" + otherItem + "',";
                            }
                            othetIds = othetIds.Substring(0, othetIds.Length - 1);

                            where = $" where aa.fcategoryid in ({othetIds}) ";
                            // where = $" where aa.fcategoryid in  ('822158608392392821','822158660800221332','822158712289497100','822158712289497203','827335690122563590') ";
                            //where1 = $" where  (m.fcategoryid='{item}' OR m.fcategoryid IN (SELECT fid FROM dbo.SER_YDJ_CATEGORY  WHERE fparentid='{item}'))  ";
                        }
                    }
                    {
                        //strSql += $@"select '{item}' fcategoryid,fformid,fsaleregionalid,fserviceregionalid,fprovince,fagnetnumber,fagnetname,t1.fcrmagnetnumber,t1.fcrmagnetname,t.fcategoryid,forderqty,fncostorderqty,fordercost,fnpriceorderqty,forderprice,faftersaleqty,fncostaftersaleqty,faftersalecost,fnpriceaftersaleqty,faftersaleprice,fsampleqty,fncostsampleqty,fsamplecost,fnpricesampleqty,fsampleprice,fvirtualqty,fncostvirtualqty,fvirtualcost,fvirtualprice,fnpricevirtualqty,fproductionqty,fncostproductionqty,fproductioncost,fnpriceproductionqty,fproductionprice,fgeneralqty,fncostgeneralqty,fgeneralcost,fnpricegeneralqty,fgeneralprice,fstockupqty,fstockupcost,fstockupprice,fstaypurqty,fstaypurcost,fstaypurprice,fallqty,fallcost,fallprice from {table} aa ";
                        strSql += $@"select 'rpt_categorystocksummary' fformid,
                                    aa.fmainorgid,
                                    aa.fmainorgnumber fagnetnumber,
                                    aa.fmainorgname fagnetname,
                                    '{item}' fcategoryid,
                                    SUM(isnull(ta.qty,0)) forderqty,SUM(isnull(ta1.qty,0)) fncostorderqty,SUM(isnull(ta.price,0)) fordercost,SUM(isnull(ta2.qty,0)) fnpriceorderqty,SUM(isnull(ta3.price,0)) forderprice,
                                    SUM( isnull(a.qty,0)) faftersaleqty,SUM(isnull(a1.qty,0)) fncostaftersaleqty,SUM(isnull(a.price,0)) faftersalecost,SUM(isnull(a2.qty,0)) fnpriceaftersaleqty,SUM(isnull(a3.price,0)) faftersaleprice,
                                    SUM(isnull(b.qty,0)) fsampleqty,SUM(isnull(b1.qty,0)) fncostsampleqty,SUM(isnull(b.price,0)) fsamplecost,SUM(isnull(b2.qty,0)) fnpricesampleqty,SUM(isnull(b3.price,0)) fsampleprice,
                                    SUM(isnull(d.qty,0)) fvirtualqty,SUM(isnull(d1.qty,0)) fncostvirtualqty,SUM(isnull(d.price,0)) fvirtualcost,SUM(isnull(d2.qty,0)) fnpricevirtualqty,SUM(isnull(d3.price,0)) fvirtualprice,
                                    SUM(isnull(tc.qty,0)) fproductionqty,SUM(isnull(tc1.qty,0)) fncostproductionqty,SUM(isnull(tc.price,0)) fproductioncost,SUM(isnull(tc2.qty,0)) fnpriceproductionqty,SUM(isnull(tc3.price,0)) fproductionprice,
                                    SUM(isnull(c.qty,0)) fgeneralqty,SUM(isnull(c1.qty,0)) fncostgeneralqty,SUM(isnull(c.price,0)) fgeneralcost,SUM(isnull(c2.qty,0)) fnpricegeneralqty,SUM(isnull(c3.price,0)) fgeneralprice,
                                    SUM(isnull(c.qty,0)-isnull(ta.qty,0)-isnull(a.qty,0)+isnull(nspur.fqty,0)) fstockupqty,--还要加上无销售订单的未清采购订单数量
                                    SUM(isnull(c.price,0)-isnull(ta.price,0)-isnull(a.price,0)+isnull(nspur.fdealamount,0)) fstockupcost ,
                                    SUM(isnull(c3.price,0)-isnull(ta3.price,0)-isnull(a3.price,0)+isnull(nspur1.fdealamount,0)) fstockupprice ,
                                    SUM(isnull(ta.qty,0)+isnull(nspur.fqty,0)-isnull(c.qty,0)-isnull(a.qty,0)-isnull(spur.fqty,0)) fstaypurqty,--还要加上无销售订单的未清采购订单数量
                                    SUM(isnull(ta.price,0)+isnull(nspur.fdealamount,0)-isnull(c.price,0)-isnull(a.price,0)-isnull(spur.fdealamount,0)) fstaypurcost,  
                                    SUM(isnull(ta3.price,0)+isnull(nspur1.fdealamount,0)-isnull(c3.price,0)-isnull(a3.price,0)-isnull(spur1.fdealamount,0)) fstaypurprice  ,  
                                    SUM(isnull(c.qty,0)+isnull(b.qty,0)+isnull(a.qty,0)+isnull(d.qty,0)) fallqty  ,  
                                    SUM(isnull(c.price,0)+isnull(b.price,0)+isnull(a.price,0)+isnull(d.price,0)) fallcost ,  
                                    SUM(isnull(c3.price,0)+isnull(b3.price,0)+isnull(a3.price,0)+isnull(d3.price,0))fallprice ,
                                    sum(isnull(ta4.qty,0)) fproductionorderqty,sum(isnull(e.qty,0)) fproductiongeneralqty,sum(isnull(e1.qty,0)) fproductionsampleqty
                                    from {inventorylistTable} AS aa 
                                    left join   {categoryTable} ta  ON aa.fmainorgid=ta.fmainorgid  AND  aa.fcategoryid=ta.fcategoryid and ta.fhprice='Y' and ta.fhpricetype='ALL'
                                    left join   {categoryTable} ta1  ON aa.fmainorgid=ta1.fmainorgid  AND  aa.fcategoryid=ta1.fcategoryid and ta1.fhprice='N' and ta1.fhpricetype='PUR'
                                    left join   {categoryTable} ta2  ON aa.fmainorgid=ta2.fmainorgid  AND  aa.fcategoryid=ta2.fcategoryid and ta2.fhprice='N' and ta2.fhpricetype='SAL'
                                    left join   {categoryTable} ta3  ON aa.fmainorgid=ta3.fmainorgid  AND  aa.fcategoryid=ta3.fcategoryid and ta3.fhprice='Y' and ta3.fhpricetype='SAL'
                                    left join   {categoryTable} ta4  ON aa.fmainorgid=ta4.fmainorgid  AND  aa.fcategoryid=ta4.fcategoryid and ta4.fhprice='Y' and ta4.fhpricetype='TC'
                                    left join {warehouseTable} a on  a.fmainorgid=aa.fmainorgid and a.fcategoryid=aa.fcategoryid and a.fwarehousetype ='warehouse_04'  and a.fhprice='Y' and a.fhpricetype='ALL'--售后仓
                                    left join {warehouseTable} a1 on  a1.fmainorgid=aa.fmainorgid and a1.fcategoryid=aa.fcategoryid and a1.fwarehousetype ='warehouse_04' and a1.fhprice='N' and a1.fhpricetype='PUR' --售后仓
                                    left join {warehouseTable} a2 on  a2.fmainorgid=aa.fmainorgid and a2.fcategoryid=aa.fcategoryid and a2.fwarehousetype ='warehouse_04' and a2.fhprice='N' and a2.fhpricetype='SAL' --售后仓
                                    left join {warehouseTable} a3 on  a3.fmainorgid=aa.fmainorgid and a3.fcategoryid=aa.fcategoryid and a3.fwarehousetype ='warehouse_04' and a3.fhprice='Y' and a3.fhpricetype='SAL' --售后仓
                                    left join {warehouseTable}  b on  b.fmainorgid=aa.fmainorgid and b.fcategoryid=aa.fcategoryid and b.fwarehousetype ='warehouse_02'  and b.fhprice='Y' and b.fhpricetype='ALL' --样品，门店仓
                                    left join {warehouseTable}  b1 on  b1.fmainorgid=aa.fmainorgid and b1.fcategoryid=aa.fcategoryid and b1.fwarehousetype ='warehouse_02'  and b1.fhprice='N' and b1.fhpricetype='PUR'  --样品，门店仓
                                    left join {warehouseTable}  b2 on  b2.fmainorgid=aa.fmainorgid and b2.fcategoryid=aa.fcategoryid and b2.fwarehousetype ='warehouse_02'  and b2.fhprice='N' and b2.fhpricetype='SAL'  --样品，门店仓
                                    left join {warehouseTable}  b3 on  b3.fmainorgid=aa.fmainorgid and b3.fcategoryid=aa.fcategoryid and b3.fwarehousetype ='warehouse_02'  and b3.fhprice='Y' and b3.fhpricetype='SAL'  --样品，门店仓
                                    left join {warehouseTable}  c on  c.fmainorgid=aa.fmainorgid and c.fcategoryid=aa.fcategoryid and c.fwarehousetype ='warehouse_01' and c.fhprice='Y' and c.fhpricetype='ALL'--总仓
                                    left join {warehouseTable}  c1 on  c1.fmainorgid=aa.fmainorgid and c1.fcategoryid=aa.fcategoryid and c1.fwarehousetype ='warehouse_01'   and c1.fhprice='N' and c1.fhpricetype='PUR' --总仓
                                    left join {warehouseTable}  c2 on  c2.fmainorgid=aa.fmainorgid and c2.fcategoryid=aa.fcategoryid and c2.fwarehousetype ='warehouse_01'   and c2.fhprice='N' and c2.fhpricetype='SAL' --总仓
                                    left join {warehouseTable}  c3 on  c3.fmainorgid=aa.fmainorgid and c3.fcategoryid=aa.fcategoryid and c3.fwarehousetype ='warehouse_01'   and c3.fhprice='Y' and c3.fhpricetype='SAL' --总仓
                                    left join {warehouseTable}  d on  d.fmainorgid=aa.fmainorgid and d.fcategoryid=aa.fcategoryid and d.fwarehousetype ='warehouse_03'  and d.fhprice='Y'  and d.fhpricetype='ALL'--虚拟仓
                                    left join {warehouseTable}  d1 on  d1.fmainorgid=aa.fmainorgid and d1.fcategoryid=aa.fcategoryid and d1.fwarehousetype ='warehouse_03'  and d1.fhprice='N'  and d1.fhpricetype='PUR' --虚拟仓
                                    left join {warehouseTable}  d2 on  d2.fmainorgid=aa.fmainorgid and d2.fcategoryid=aa.fcategoryid and d2.fwarehousetype ='warehouse_03'  and d2.fhprice='N'  and d2.fhpricetype='SAL' --虚拟仓
                                    left join {warehouseTable}  d3 on  d3.fmainorgid=aa.fmainorgid and d3.fcategoryid=aa.fcategoryid and d3.fwarehousetype ='warehouse_03'  and d3.fhprice='Y'  and d3.fhpricetype='SAL' --虚拟仓
                                    left join {warehouseTable}  e on  e.fmainorgid=aa.fmainorgid and e.fcategoryid=aa.fcategoryid and e.fwarehousetype ='warehouse_01'  and e.fhprice='N'  and e.fhpricetype='TC'--在总仓中找出停产的
                                    left join {warehouseTable}  e1 on e1.fmainorgid=aa.fmainorgid and e1.fcategoryid=aa.fcategoryid and e1.fwarehousetype ='warehouse_02'  and e1.fhprice='N'  and e1.fhpricetype='TC' --在门店仓中找出停产的

                                    left join {zjtcTable} tc on  tc.fmainorgid=aa.fmainorgid   and tc.fcategoryid=aa.fcategoryid   and tc.fhprice='Y'  and tc.fhpricetype='ALL'
                                    left join {zjtcTable} tc1 on  tc1.fmainorgid=aa.fmainorgid   and tc1.fcategoryid=aa.fcategoryid and tc1.fhprice='N'  and tc1.fhpricetype='PUR'
                                    left join {zjtcTable} tc2 on  tc2.fmainorgid=aa.fmainorgid   and tc2.fcategoryid=aa.fcategoryid and tc2.fhprice='N' and tc2.fhpricetype='SAL'
                                    left join {zjtcTable} tc3 on  tc3.fmainorgid=aa.fmainorgid   and tc3.fcategoryid=aa.fcategoryid and tc3.fhprice='Y' and tc3.fhpricetype='SAL'
                                    left join {purinvtable} as nspur on nspur.fmainorgid=aa.fmainorgid  AND nspur.fcategoryid = aa.fcategoryid and nspur.ftype='t1' AND nspur.fhpricetype='ALL'  and nspur.fhprice='Y'
                                    left join {purinvtable} as nspur1 on nspur1.fmainorgid=aa.fmainorgid  AND nspur1.fcategoryid = aa.fcategoryid and nspur1.ftype='t1' AND  nspur1.fhpricetype='SAL' and nspur1.fhprice='Y'
                              left join {purinvtable1} as spur on spur.fmainorgid=aa.fmainorgid  AND spur.fcategoryid = aa.fcategoryid and spur.ftype='t2' AND spur.fhpricetype='ALL'  and spur.fhprice='Y'
                              left join {purinvtable1} as spur1 on spur1.fmainorgid=aa.fmainorgid  AND spur1.fcategoryid = aa.fcategoryid and spur1.ftype='t2' AND spur1.fhpricetype='SAL'  and spur1.fhprice='Y' ";
                    }
                    #region 新版本，将销售合同分开查询，性能存在问题
                    if (false)
                    {
                        strSql += " select  fformid,fmainorgid, fmainorgnumber fagnetnumber, fmainorgname fagnetname,fcategoryid,sum(forderqty) forderqty,sum(fncostorderqty) fncostorderqty,sum(fordercost) fordercost,sum(fnpriceorderqty) fnpriceorderqty,sum(forderprice) forderprice,sum(faftersaleqty) faftersaleqty,sum(fncostaftersaleqty) fncostaftersaleqty,sum(faftersalecost) faftersalecost,sum(fnpriceaftersaleqty) fnpriceaftersaleqty,sum(faftersaleprice) faftersaleprice,sum(fsampleqty) fsampleqty,sum(fncostsampleqty) fncostsampleqty,sum(fsamplecost) fsamplecost,sum(fnpricesampleqty) fnpricesampleqty,sum(fsampleprice) fsampleprice,sum(fvirtualqty) fvirtualqty,sum(fncostvirtualqty) fncostvirtualqty,sum(fvirtualcost) fvirtualcost,sum(fnpricevirtualqty) fnpricevirtualqty,sum(fvirtualprice) fvirtualprice,sum(fproductionqty) fproductionqty,sum(fncostproductionqty) fncostproductionqty,sum(fproductioncost) fproductioncost,sum(fnpriceproductionqty) fnpriceproductionqty,sum(fproductionprice) fproductionprice,sum(fgeneralqty) fgeneralqty,sum(fncostgeneralqty) fncostgeneralqty,sum(fgeneralcost) fgeneralcost,sum(fnpricegeneralqty) fnpricegeneralqty,sum(fgeneralprice) fgeneralprice,sum(fstockupqty-forderqty) fstockupqty,sum(fstockupcost-fncostorderqty) fstockupcost,sum(fstockupprice-forderprice) fstockupprice,sum(fstaypurqty+forderqty) fstaypurqty,sum(fstaypurcost+fncostorderqty) fstaypurcost,sum(fstaypurprice+forderprice) fstaypurprice,sum(fallqty) fallqty,sum(fallcost) fallcost,sum(fallprice) fallprice,sum(fproductionorderqty) fproductionorderqty,sum(fproductiongeneralqty) fproductiongeneralqty,sum(fproductionsampleqty) fproductionsampleqty " +
                            "from (";
                        {

                            //不查
                            strSql += $@"select 'rpt_categorystocksummary' fformid,
                                aa.fmainorgid,
                                aa.fmainorgnumber ,
                                aa.fmainorgname ,
                                '{item}' fcategoryid,
                               0 forderqty,0 fncostorderqty,0 fordercost,0 fnpriceorderqty,0 forderprice,
                                SUM( isnull(a.qty,0)) faftersaleqty,SUM(isnull(a1.qty,0)) fncostaftersaleqty,SUM(isnull(a.price,0)) faftersalecost,SUM(isnull(a2.qty,0)) fnpriceaftersaleqty,SUM(isnull(a3.price,0)) faftersaleprice,
                                SUM(isnull(b.qty,0)) fsampleqty,SUM(isnull(b1.qty,0)) fncostsampleqty,SUM(isnull(b.price,0)) fsamplecost,SUM(isnull(b2.qty,0)) fnpricesampleqty,SUM(isnull(b3.price,0)) fsampleprice,
                                SUM(isnull(d.qty,0)) fvirtualqty,SUM(isnull(d1.qty,0)) fncostvirtualqty,SUM(isnull(d.price,0)) fvirtualcost,SUM(isnull(d2.qty,0)) fnpricevirtualqty,SUM(isnull(d3.price,0)) fvirtualprice,
                                SUM(isnull(tc.qty,0)) fproductionqty,SUM(isnull(tc1.qty,0)) fncostproductionqty,SUM(isnull(tc.price,0)) fproductioncost,SUM(isnull(tc2.qty,0)) fnpriceproductionqty,SUM(isnull(tc3.price,0)) fproductionprice,
                                SUM(isnull(c.qty,0)) fgeneralqty,SUM(isnull(c1.qty,0)) fncostgeneralqty,SUM(isnull(c.price,0)) fgeneralcost,SUM(isnull(c2.qty,0)) fnpricegeneralqty,SUM(isnull(c3.price,0)) fgeneralprice,
                                SUM(isnull(c.qty,0)-0-isnull(a.qty,0)+isnull(nspur.fqty,0)) fstockupqty,--还要加上无销售订单的未清采购订单数量
                                SUM(isnull(c.price,0)-0-isnull(a.price,0)+isnull(nspur.fdealamount,0)) fstockupcost ,
                                SUM(isnull(c3.price,0)-0-isnull(a3.price,0)+isnull(nspur1.fdealamount,0)) fstockupprice ,
                                SUM(0+isnull(nspur.fqty,0)-isnull(c.qty,0)-isnull(a.qty,0)-isnull(spur.fqty,0)) fstaypurqty,--还要加上无销售订单的未清采购订单数量
                                SUM(0+isnull(nspur.fdealamount,0)-isnull(c.price,0)-isnull(a.price,0)-isnull(spur.fdealamount,0)) fstaypurcost,  
                                SUM(0+isnull(nspur1.fdealamount,0)-isnull(c3.price,0)-isnull(a3.price,0)-isnull(spur1.fdealamount,0)) fstaypurprice  ,  
                                SUM(isnull(c.qty,0)+isnull(b.qty,0)+isnull(a.qty,0)+isnull(d.qty,0)) fallqty  ,  
                                SUM(isnull(c.price,0)+isnull(b.price,0)+isnull(a.price,0)+isnull(d.price,0)) fallcost ,  
                                SUM(isnull(c3.price,0)+isnull(b3.price,0)+isnull(a3.price,0)+isnull(d3.price,0))fallprice ,
                                0 fproductionorderqty,sum(isnull(e.qty,0)) fproductiongeneralqty,sum(isnull(e1.qty,0)) fproductionsampleqty
                                from {inventorylistTable} AS aa 
                                left join {warehouseTable} a on  a.fmainorgid=aa.fmainorgid and a.fcategoryid=aa.fcategoryid and a.fwarehousetype ='warehouse_04'  and a.fhprice='Y' and a.fhpricetype='ALL'--售后仓
                                left join {warehouseTable} a1 on  a1.fmainorgid=aa.fmainorgid and a1.fcategoryid=aa.fcategoryid and a1.fwarehousetype ='warehouse_04' and a1.fhprice='N' and a1.fhpricetype='PUR' --售后仓
                                left join {warehouseTable} a2 on  a2.fmainorgid=aa.fmainorgid and a2.fcategoryid=aa.fcategoryid and a2.fwarehousetype ='warehouse_04' and a2.fhprice='N' and a2.fhpricetype='SAL' --售后仓
                                left join {warehouseTable} a3 on  a3.fmainorgid=aa.fmainorgid and a3.fcategoryid=aa.fcategoryid and a3.fwarehousetype ='warehouse_04' and a3.fhprice='Y' and a3.fhpricetype='SAL' --售后仓
                                left join {warehouseTable}  b on  b.fmainorgid=aa.fmainorgid and b.fcategoryid=aa.fcategoryid and b.fwarehousetype ='warehouse_02'  and b.fhprice='Y' and b.fhpricetype='ALL' --样品，门店仓
                                left join {warehouseTable}  b1 on  b1.fmainorgid=aa.fmainorgid and b1.fcategoryid=aa.fcategoryid and b1.fwarehousetype ='warehouse_02'  and b1.fhprice='N' and b1.fhpricetype='PUR'  --样品，门店仓
                                left join {warehouseTable}  b2 on  b2.fmainorgid=aa.fmainorgid and b2.fcategoryid=aa.fcategoryid and b2.fwarehousetype ='warehouse_02'  and b2.fhprice='N' and b2.fhpricetype='SAL'  --样品，门店仓
                                left join {warehouseTable}  b3 on  b3.fmainorgid=aa.fmainorgid and b3.fcategoryid=aa.fcategoryid and b3.fwarehousetype ='warehouse_02'  and b3.fhprice='Y' and b3.fhpricetype='SAL'  --样品，门店仓
                                left join {warehouseTable}  c on  c.fmainorgid=aa.fmainorgid and c.fcategoryid=aa.fcategoryid and c.fwarehousetype ='warehouse_01' and c.fhprice='Y' and c.fhpricetype='ALL'--总仓
                                left join {warehouseTable}  c1 on  c1.fmainorgid=aa.fmainorgid and c1.fcategoryid=aa.fcategoryid and c1.fwarehousetype ='warehouse_01'   and c1.fhprice='N' and c1.fhpricetype='PUR' --总仓
                                left join {warehouseTable}  c2 on  c2.fmainorgid=aa.fmainorgid and c2.fcategoryid=aa.fcategoryid and c2.fwarehousetype ='warehouse_01'   and c2.fhprice='N' and c2.fhpricetype='SAL' --总仓
                                left join {warehouseTable}  c3 on  c3.fmainorgid=aa.fmainorgid and c3.fcategoryid=aa.fcategoryid and c3.fwarehousetype ='warehouse_01'   and c3.fhprice='Y' and c3.fhpricetype='SAL' --总仓
                                left join {warehouseTable}  d on  d.fmainorgid=aa.fmainorgid and d.fcategoryid=aa.fcategoryid and d.fwarehousetype ='warehouse_03'  and d.fhprice='Y'  and d.fhpricetype='ALL'--虚拟仓
                                left join {warehouseTable}  d1 on  d1.fmainorgid=aa.fmainorgid and d1.fcategoryid=aa.fcategoryid and d1.fwarehousetype ='warehouse_03'  and d1.fhprice='N'  and d1.fhpricetype='PUR' --虚拟仓
                                left join {warehouseTable}  d2 on  d2.fmainorgid=aa.fmainorgid and d2.fcategoryid=aa.fcategoryid and d2.fwarehousetype ='warehouse_03'  and d2.fhprice='N'  and d2.fhpricetype='SAL' --虚拟仓
                                left join {warehouseTable}  d3 on  d3.fmainorgid=aa.fmainorgid and d3.fcategoryid=aa.fcategoryid and d3.fwarehousetype ='warehouse_03'  and d3.fhprice='Y'  and d3.fhpricetype='SAL' --虚拟仓
                                left join {warehouseTable}  e on  e.fmainorgid=aa.fmainorgid and e.fcategoryid=aa.fcategoryid and e.fwarehousetype ='warehouse_01'  and e.fhprice='N'  and e.fhpricetype='TC'--在总仓中找出停产的
                                left join {warehouseTable}  e1 on e1.fmainorgid=aa.fmainorgid and e1.fcategoryid=aa.fcategoryid and e1.fwarehousetype ='warehouse_02'  and e1.fhprice='N'  and e1.fhpricetype='TC' --在门店仓中找出停产的
                               
                                left join {zjtcTable} tc on  tc.fmainorgid=aa.fmainorgid   and tc.fcategoryid=aa.fcategoryid   and tc.fhprice='Y'  and tc.fhpricetype='ALL'
                                left join {zjtcTable} tc1 on  tc1.fmainorgid=aa.fmainorgid   and tc1.fcategoryid=aa.fcategoryid and tc1.fhprice='N'  and tc1.fhpricetype='PUR'
                                left join {zjtcTable} tc2 on  tc2.fmainorgid=aa.fmainorgid   and tc2.fcategoryid=aa.fcategoryid and tc2.fhprice='N' and tc2.fhpricetype='SAL'
                                left join {zjtcTable} tc3 on  tc3.fmainorgid=aa.fmainorgid   and tc3.fcategoryid=aa.fcategoryid and tc3.fhprice='Y' and tc3.fhpricetype='SAL'
                                left join {purinvtable} as nspur on nspur.fmainorgid=aa.fmainorgid  AND nspur.fcategoryid = aa.fcategoryid and nspur.ftype='t1' AND nspur.fhpricetype='ALL'  and nspur.fhprice='Y'
                                left join {purinvtable} as nspur1 on nspur1.fmainorgid=aa.fmainorgid  AND nspur1.fcategoryid = aa.fcategoryid and nspur1.ftype='t1' AND  nspur1.fhpricetype='SAL' and nspur1.fhprice='Y'
                          left join {purinvtable1} as spur on spur.fmainorgid=aa.fmainorgid  AND spur.fcategoryid = aa.fcategoryid and spur.ftype='t2' AND spur.fhpricetype='ALL'  and spur.fhprice='Y'
                          left join {purinvtable1} as spur1 on spur1.fmainorgid=aa.fmainorgid  AND spur1.fcategoryid = aa.fcategoryid and spur1.ftype='t2' AND spur1.fhpricetype='SAL'  and spur1.fhprice='Y' ";

                            strSql += where;
                            strSql += "  GROUP BY aa.fmainorgid,aa.fmainorgnumber,aa.fmainorgname ";
                            strSql += "  UNION ALL ";

                            //查
                            strSql += $@"select 'rpt_categorystocksummary' fformid,
                                aa.fmainorgid,
                                aa.fmainorgnumber ,
                                aa.fmainorgname ,
                                '{item}' fcategoryid,
                                SUM(isnull(ta0.qty,0)) forderqty,SUM(isnull(ta1.qty,0)) fncostorderqty,SUM(isnull(ta0.price,0)) fordercost,SUM(isnull(ta2.qty,0)) fnpriceorderqty,SUM(isnull(ta3.price,0)) forderprice,
                                0 faftersaleqty,0 fncostaftersaleqty,0 faftersalecost,0 fnpriceaftersaleqty,0 faftersaleprice,
                                0 fsampleqty,0 fncostsampleqty,0 fsamplecost,0 fnpricesampleqty,0 fsampleprice,
                                0 fvirtualqty,0 fncostvirtualqty,0 fvirtualcost,0 fnpricevirtualqty,0 fvirtualprice,
                                0 fproductionqty,0 fncostproductionqty,0 fproductioncost,0 fnpriceproductionqty,0 fproductionprice,
                                0 fgeneralqty,0 fncostgeneralqty,0 fgeneralcost,0 fnpricegeneralqty,0 fgeneralprice,
                                0 fstockupqty,--还要加上无销售订单的未清采购订单数量
                                0 fstockupcost ,
                                0 fstockupprice ,
                                0 fstaypurqty,--还要加上无销售订单的未清采购订单数量
                                0 fstaypurcost,  
                                0 fstaypurprice  ,  
                                0 fallqty  ,  
                                0 fallcost ,  
                                0 fallprice ,
                                sum(isnull(ta4.qty,0)) fproductionorderqty,0 fproductiongeneralqty,0 fproductionsampleqty
                                from {categoryTable} aa  
                                left join   {categoryTable} ta0  ON aa.fmainorgid=ta0.fmainorgid  AND  aa.fcategoryid=ta0.fcategoryid and 'Y'=aa.fhprice  and 'Y'=ta0.fhprice and aa.fhpricetype='ALL' and aa.fhpricetype=ta0.fhpricetype
                                left join   {categoryTable} ta1  ON aa.fmainorgid=ta1.fmainorgid  AND  aa.fcategoryid=ta1.fcategoryid and 'N'=aa.fhprice  and 'N'=ta1.fhprice and aa.fhpricetype='PUR' and aa.fhpricetype=ta1.fhpricetype
                                left join   {categoryTable} ta2  ON aa.fmainorgid=ta2.fmainorgid  AND  aa.fcategoryid=ta2.fcategoryid and 'N'=aa.fhprice  and 'N'=ta2.fhprice and aa.fhpricetype='SAL' and aa.fhpricetype=ta2.fhpricetype
                                left join   {categoryTable} ta3  ON aa.fmainorgid=ta3.fmainorgid  AND  aa.fcategoryid=ta3.fcategoryid and 'Y'=aa.fhprice  and 'Y'=ta3.fhprice and aa.fhpricetype='SAL' and aa.fhpricetype=ta3.fhpricetype
                                left join   {categoryTable} ta4  ON aa.fmainorgid=ta3.fmainorgid  AND  aa.fcategoryid=ta3.fcategoryid and 'Y'=aa.fhprice  and 'Y'=ta3.fhprice and aa.fhpricetype='TC' and aa.fhpricetype=ta3.fhpricetype ";

                            strSql += where;
                            strSql += "  GROUP BY aa.fmainorgid,aa.fmainorgnumber,aa.fmainorgname ";
                        }
                        strSql += " ) aa";
                    }
                    #endregion
                    strSql += where;
                    strSql += "  GROUP BY aa.fmainorgid,aa.fmainorgnumber,aa.fmainorgname ";
                    if (item != fcategoryidArray.Last()) strSql += $@" union all ";
                    if (item == fcategoryidArray.Last()) strSql += $@" )";
                }

                strSql += $@"as t inner join  ( SELECT NEWID() fid,* FROM ( SELECT distinct a.fmainorgid,ISNULL(e.fid,'') fprovince,ISNULL(d.fregionalid,'') fsaleregionalid,isnull(d.fid,'')  fserviceregionalid,c.fcrmagnetnumber,c.fcrmagnetname,c.fcity_txt,c.fcitylevel  FROM  {invTable} AS a  with(nolock)
		                    INNER JOIN {t_agentcitytable} AS bb ON a.fmainorgid=bb.fid
		                    INNER JOIN {tname} AS c ON bb.fid=c.fid  
		                    INNER JOIN dbo.T_YDJ_CITY AS cc ON bb.fcity=cc.fid 
		                    INNER JOIN t_bas_manageregional AS d  ON cc.fmanageregionalid=d.fid
		                    INNER JOIN dbo.v_bd_enum AS e  ON e.fid=cc.fprovince
                            where a.fmainorgid<>'829653207454318616'
                            ) AS tt ) as t1 on t.fmainorgid=t1.fmainorgid     where 1=1  ";

                if (!string.IsNullOrEmpty(region))
                {
                    strSql += $" and t1.fsaleregionalid='{region}' ";
                }
                if (!string.IsNullOrEmpty(center))
                {
                    strSql += $" and t1.fserviceregionalid='{center}' ";
                }
                if (!string.IsNullOrEmpty(provice))
                {
                    strSql += $" and t1.fprovince='{provice}' ";
                }
                if (!string.IsNullOrEmpty(fcatyid))
                {
                    strSql += $" and t.fcategoryid='{fcatyid}' ";
                }
                var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
                dbServiceExt.Execute(this.Context, strSql, sqlParam);
                if (true)
                {
                    this.DBService.DeleteTempTableByName(this.Context, materialTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, orderTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, priceTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, salepriceTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, inventorylistTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, categoryTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, warehouseTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, zjtcTable, true);

                    this.DBService.DeleteTempTableByName(this.Context, purchaseordertable, true);
                    this.DBService.DeleteTempTableByName(this.Context, agentTable, true);
                    this.DBService.DeleteTempTableByName(this.Context, tname, true);
                    this.DBService.DeleteTempTableByName(this.Context, t_agentcitytable, true);


                    this.DBService.DeleteTempTableByName(this.Context, purinvtable, true);
                    this.DBService.DeleteTempTableByName(this.Context, purinvtable1, true);
                }
            }
        }

        /// <summary>
        /// 获取商品类别及其子类
        /// </summary>
        /// <param name="fcategoryids"></param>
        /// <param name="sqlParam"></param>
        /// <returns></returns>
        private List<keyValue> getCategorys(DynamicObjectCollection dynamicObjects, string fcategoryid)
        {
            var allcatyKeyValues = dynamicObjects.Select(x => new keyValue()
            {
                fid = Convert.ToString(x["fid"]),
                fparentid = Convert.ToString(x["fparentid"]),
            }).ToList();
            List<keyValue> result = new List<keyValue>();
            GetCategoryTreeNodes(allcatyKeyValues, fcategoryid, ref result);
            return result;
        }


        /// <summary>
        /// 获取商品类别及其子类
        /// </summary>
        /// <param name="fcategoryids"></param>
        /// <param name="sqlParam"></param>
        /// <returns></returns>
        private List<keyValue> getAllChildrenCategorys(DynamicObjectCollection dynamicObjects, string fcategoryid)
        {
            var allcatyKeyValues = dynamicObjects.Select(x => new keyValue()
            {
                fid = Convert.ToString(x["fid"]),
                fparentid = Convert.ToString(x["fparentid"]),
            }).ToList();
            List<keyValue> result = new List<keyValue>();
            GetCategoryTreeNodes(allcatyKeyValues, fcategoryid, fcategoryid, ref result);
            return result;
        }

        /// <summary>
        /// 获取组织结构树(包含本身)
        /// </summary>
        /// <param name="list"></param>
        /// <param name="id"></param>
        /// <param name="treeNodes"></param>
        /// <returns></returns>
        static void GetCategoryTreeNodes(List<keyValue> list, string id, ref List<keyValue> treeNodes)
        {
            treeNodes.Add(new keyValue() { fid = id, fparentid = id });

            if (list == null || string.IsNullOrWhiteSpace(id))
                return;

            List<keyValue> sublist = null;
            if (!string.IsNullOrWhiteSpace(id))
            {
                sublist = list.Where(t => t.fparentid == id).ToList();
            }
            if (!sublist.Any())
                return;
            foreach (var item in sublist)
            {
                treeNodes.Add(new keyValue() { fid = item.fid, fparentid = item.fparentid });
                GetCategoryTreeNodes(list, item.fid, ref treeNodes);
            }
        }

        /// <summary>
        /// 获取组织结构树(包含本身)
        /// </summary>
        /// <param name="list"></param>
        /// <param name="id"></param>
        /// <param name="treeNodes"></param>
        /// <returns></returns>
        static void GetCategoryTreeNodes(List<keyValue> list, string id, string fparentid, ref List<keyValue> treeNodes)
        {
            treeNodes.Add(new keyValue() { fid = id, fparentid = fparentid });

            if (list == null || string.IsNullOrWhiteSpace(id))
                return;

            List<keyValue> sublist = null;
            if (!string.IsNullOrWhiteSpace(id))
            {
                sublist = list.Where(t => t.fparentid == fparentid).ToList();
            }
            if (!sublist.Any())
                return;
            foreach (var item in sublist)
            {
                treeNodes.Add(new keyValue() { fid = item.fid, fparentid = item.fparentid });
                GetCategoryTreeNodes(list, item.fid, ref treeNodes);
            }
        }

        private class keyValue
        {
            public string fparentid { get; set; }
            public string fid { get; set; }
        }
    }
}
