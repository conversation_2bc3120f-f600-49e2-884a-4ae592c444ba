using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Report.OutingAmount
{
    /// <summary>
    /// 待出库明细
    /// </summary>
    [InjectService]
    [FormId("rpt_outingamount")]
    [OperationNo("QueryListReportData")]
	public class QueryListReportData : AbstractReportServicePlugIn
	{
		/// <summary>
		/// 数据库服务
		/// </summary>
		[InjectProperty]
		protected IDBServiceEx DBServiceEx { get; set; }


		/// <summary>
		/// 执行报表逻辑
		/// </summary>
		protected override void OnExecuteLogic()
		{
            var stockId = this.ParentPageSession.stockId as string;
            if (stockId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"参数 stockId 为空，请检查！");
            }
            //查询数据后往报表对应的临时表中插入数据
            var insertSql = $@"/*dialect*/
            insert into {this.DataSourceTableName}
            (
                fid,fjnidentityid,fbillno,fbizobject,fqty,fcreatorid,fcreatedate,fstatus
            )
            select fid,row_number() over(order by fbillno) fjnidentityid, fbillno,FFormId as fbizobject ,fqty,fcreatorid,fcreatedate,fstatus from  (
            -- 销售出库单
             select t.fid,t.fbillno,t.FFormId,te.fqty,t.fcreatorid,t.fcreatedate,fstatus
            from t_stk_sostockout t with(nolock)
            inner join t_stk_sostockoutentry te with(nolock) on t.fid=te.fid
            where t.fmainorgid='{this.Context.Company}' and t.fstatus<>'E' and t.fcancelstatus='0' and exists(select 1 from t_stk_inventorylist inv with(nolock) where inv.fid in ('{stockId}') and te.fmaterialid= inv.fmaterialid and te.fattrinfo= inv.fattrinfo and te.fcustomdesc= inv.fcustomdesc and te.flotno= inv.flotno and te.fmtono= inv.fmtono and te.fownerid= inv.fownerid and te.fownertype= inv.fownertype and te.funitid= inv.funitid and te.fstockunitid= inv.fstockunitid and te.fstorehouseid= inv.fstorehouseid and te.fstorelocationid= inv.fstorelocationid and te.fstockstatus= inv.fstockstatus)
            union all
            -- 其它出库单
             select t.fid,t.fbillno,t.FFormId,te.fqty,t.fcreatorid,t.fcreatedate,fstatus
            from t_stk_otherstockout t with(nolock)
            inner join t_stk_otherstockoutentry te with(nolock) on t.fid=te.fid
            where t.fmainorgid='{this.Context.Company}' and t.fstatus<>'E' and t.fcancelstatus='0' and exists(select 1 from t_stk_inventorylist inv with(nolock) where inv.fid in ('{stockId}') and te.fmaterialid= inv.fmaterialid and te.fattrinfo= inv.fattrinfo and te.fcustomdesc= inv.fcustomdesc and te.flotno= inv.flotno and te.fmtono= inv.fmtono and te.fownerid= inv.fownerid and te.fownertype= inv.fownertype and te.funitid= inv.funitid and te.fstockunitid= inv.fstockunitid and te.fstorehouseid= inv.fstorehouseid and te.fstorelocationid= inv.fstorelocationid and te.fstockstatus= inv.fstockstatus)
            union all
            -- 库存调拨单
             select t.fid,t.fbillno,t.FFormId,te.fqty,t.fcreatorid,t.fcreatedate,fstatus
            from t_stk_invtransfer t with(nolock)
            inner join t_stk_invtransferentry te with(nolock) on t.fid=te.fid
            where t.fmainorgid='{this.Context.Company}' and (t.fstatus in ('A', 'B', 'C') or (t.fstatus = 'D' and t.fisstockout='0')) and t.fcancelstatus='0' and exists(select 1 from t_stk_inventorylist inv with(nolock) where inv.fid in ('{stockId}') and te.fmaterialid= inv.fmaterialid and te.fattrinfo= inv.fattrinfo and te.fcustomdesc= inv.fcustomdesc and te.flotno= inv.flotno and te.fmtono= inv.fmtono and te.fownerid= inv.fownerid and te.fownertype= inv.fownertype and te.funitid= inv.funitid and te.fstockunitid= inv.fstockunitid and te.fstorehouseid= inv.fstorehouseid and te.fstorelocationid= inv.fstorelocationid and te.fstockstatus= inv.fstockstatus)
            union all
            -- 采购退货单
             select t.fid,t.fbillno,t.FFormId,te.fqty,t.fcreatorid,t.fcreatedate,fstatus
            from t_stk_postockreturn t with(nolock)
            inner join t_stk_postockreturnentry te with(nolock) on t.fid=te.fid
            where t.fmainorgid='{this.Context.Company}' and t.fstatus<>'E' and t.fcancelstatus='0' and exists(select 1 from t_stk_inventorylist inv with(nolock) where inv.fid in ('{stockId}') and te.fmaterialid= inv.fmaterialid and te.fattrinfo= inv.fattrinfo and te.fcustomdesc= inv.fcustomdesc and te.flotno= inv.flotno and te.fmtono= inv.fmtono and te.fownerid= inv.fownerid and te.fownertype= inv.fownertype and te.funitid= inv.funitid and te.fstockunitid= inv.fstockunitid and te.fstorehouseid= inv.fstorehouseid and te.fstorelocationid= inv.fstorelocationid and te.fstockstatus= inv.fstockstatus)
            ) s
            
            ";
			this.DBServiceEx.Execute(this.Context, insertSql);

        }

        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
		{
			base.OnPrepareReportQueryParameter(listQueryPara);

			listQueryPara.OrderByString = "fjnidentityid asc";
		}
	}
}
