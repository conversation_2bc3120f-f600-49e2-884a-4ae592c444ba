using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CustomerBalanceDetail
{
    /// <summary>
    /// 客户余额明细报表
    /// </summary>
    [InjectService]
    [FormId("rpt_customerbalancedetail")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 业务日期从
        /// </summary>
        private DateTime dateFrom { get; set; }
        /// <summary>
        /// 业务日期至
        /// </summary>
        private DateTime dateTo { get; set; }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.CheckDataEnvironment();
            base.OnExecuteLogic();
            this.GetCustomerBalanceDetailData();
        }

        /// <summary>
        /// 检查当前过滤界面必须录入的信息 
        /// </summary>
        protected void CheckDataEnvironment()
        {

            if (this.CustomFilterObject != null)
            {
                DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
                DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];

                if (!dtDateFrom.HasValue && !dtDateTo.HasValue)
                {
                    //如果都为空，则说明为报表打开第一次查询，此时需跟前端初始化保持一致
                    var nowTime = DateTime.Now;
                    //dtDateFrom = new DateTime(nowTime.Year, nowTime.Month, 1);//当月第一天
                    dtDateFrom = new DateTime(2004, 1, 1);

                    var nextTime = nowTime.AddMonths(1);
                    dtDateTo = new DateTime(nextTime.Year, nextTime.Month, 1).AddDays(-1);//当月最后一天
                }

                if (!dtDateFrom.HasValue)
                {
                    throw new BusinessException("过滤条件【业务日期从】不能为空！");
                }
                if (!dtDateTo.HasValue)
                {
                    throw new BusinessException("过滤条件【业务日期至】不能为空！");
                }

                if (dtDateTo < dtDateFrom)
                {
                    throw new BusinessException("过滤条件【截止日期不能早于开始日期】！");
                }

                dateFrom = dtDateFrom.Value.DayBegin();
                dateTo = dtDateTo.Value.DayEnd();
            }
        }

        /// <summary>
        /// 查询数据后往报表对应的临时表中插入数据
        /// </summary>
        private void GetCustomerBalanceDetailData()
        {


            //查询需要用到的收支记录支付方式
            var waySql = "SELECT fid,fenumitem FROM v_bd_enum WHERE fcategory='收支记录支付方式' AND fenumitem IN ('账户支付','商场代金券')";
            var wayObjs = this.DBService.ExecuteDynamicObject(this.Context, waySql);

            //本期收款：按【支付日期】在已选时间范围，汇总【数据状态】=“已审核”的《收款单》中的【结算明细.金额】，并需剔除【支付方式】=“账户支付”且【账户方向】=“减”（账户支付无需包含，但订单付款需包含）的记录
            var recExcludeWayIds = wayObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("账户支付"))
                                       .Select(e => Convert.ToString(e["fid"])).ToList();
            //本期退款：按【支付日期】在已选时间范围，汇总【数据状态】=“已审核”的《收款退款单》中的【结算明细.金额】，并需剔除【支付方式】=“账户支付”且【账户方向】=“减”（退到账户无需包含，但订单退款需包含）的记录
            var refExcludeWayIds = wayObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("账户支付"))
                                       .Select(e => Convert.ToString(e["fid"])).ToList();

            //查询需要用到的订单金额方向
            var drctSql = "SELECT fid,fenumitem FROM v_bd_enum WHERE fcategory='订单金额方向'";
            var drctObjs = this.DBService.ExecuteDynamicObject(this.Context, drctSql);
            //增
            var addDrctId = drctObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("增")).Select(e => Convert.ToString(e["fid"])).FirstOrDefault();
            //减
            var reduceDrctId = drctObjs.Where(t => Convert.ToString(t["fenumitem"]).EqualsIgnoreCase("减")).Select(e => Convert.ToString(e["fid"])).FirstOrDefault();

            //《客户余额报表》传过来的参数
            var customerId = this.ParentPageSession?.fcustomerid as string;
            string customerWhere = "";
            if (!string.IsNullOrWhiteSpace(customerId))
            {
                customerWhere = $" AND B.fid = @fcustomerid";
            }

            var startTime = this.ParentPageSession?.fdatefrom as DateTime?;
            if (startTime.HasValue)
            {
                dateFrom = startTime.Value;
            }

            var endTime = this.ParentPageSession?.fdateto as DateTime?;
            if (endTime.HasValue)
            {
                dateTo = endTime.Value;
            }

            if (dateFrom < DateTime.Parse("1900-01-01") || dateFrom > DateTime.Parse("9999-12-31"))
            {
                dateFrom = new DateTime(2004, 1, 1);
            }

            if (dateTo < DateTime.Parse("1900-01-01") || dateTo > DateTime.Parse("9999-12-31"))
            {
                var nowTime = dateFrom > DateTime.Now ? dateFrom : DateTime.Now;
                var nextTime = nowTime.AddMonths(1);
                dateTo = new DateTime(nextTime.Year, nextTime.Month, 1).AddDays(-1);//当月最后一天
            }

            //要查询的字段
            var selectField = "*";
            var fieldId = this.ParentPageSession?.fieldId as string;
            if (!string.IsNullOrWhiteSpace(fieldId))
            {
                selectField = fieldId;
            }

            var sqlParams = new List<SqlParam>()
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                new SqlParam("@fstatus", DbType.String, "E"),
                new SqlParam("@fcancelstatus", DbType.String, "0"),
                new SqlParam("@fforbidstatus", DbType.String, "0"),
                new SqlParam("@fdatefrom",DbType.DateTime,dateFrom.DayBegin()),
                new SqlParam("@fdateto",DbType.DateTime,dateTo.DayEnd()),
                new SqlParam("@fdirection_rec",DbType.String,reduceDrctId),
                new SqlParam("@fdirection_ref",DbType.String,addDrctId),
                new SqlParam("@fway_ref",DbType.String,refExcludeWayIds.First())
            };
            if (!string.IsNullOrWhiteSpace(customerWhere))
            {
                sqlParams.Add(new SqlParam("@fcustomerid", DbType.String, customerId));
            }

            StringBuilder sbCurrRecSql = new StringBuilder();
            sbCurrRecSql.AppendLine(@"--本期收款
                                      SELECT B.fid AS fcustomerid,'coo_incomedisburse' AS flinkformid,A.fbillno AS flinkbillno,A.fid AS flinkbillinterid
                                      ,A.fdate AS flinkbilldate,A.fstaffid AS fsalstaffid,A.fdeptid AS fsaldeptid 
                                      ,A.famount AS fcurrentreceipt,0 AS fcurrentrefund,0 AS fcurrentstockout,0 AS fcurrentreturn,0 AS fotherreceipt
                                      FROM T_COO_INCOMEDISBURSE AS A WITH(NOLOCK) 
                                      INNER JOIN T_YDJ_CUSTOMER AS B ON A.fcustomerid = B.fid
                                      WHERE A.fmainorgid = @fmainorgid AND A.fstatus = @fstatus AND A.fcancelstatus = @fcancelstatus AND B.fforbidstatus = @fforbidstatus 
                                      	AND A.fdate >= @fdatefrom AND A.fdate <= @fdateto 
                                        AND A.fsourceformid IN ('ydj_saleintention','ydj_order','ydj_customer','ydj_collectreceipt') AND A.fbizdirection = 'bizdirection_01' --主控菜单【收款单】预置条件
	                                    AND NOT EXISTS (
	                                    	SELECT 1 FROM T_COO_INCOMEDISBURSE AS ICDB WITH(NOLOCK) 
	                                    	WHERE ICDB.fid = A.fid AND ICDB.fway IN ('{0}') AND ICDB.fdirection = @fdirection_rec
	                                    )
                                      	{1}".Fmt(string.Join("','", recExcludeWayIds), customerWhere));

            StringBuilder sbCurrRefSql = new StringBuilder();
            sbCurrRefSql.AppendLine(@"--本期退款
                                      SELECT B.fid AS fcustomerid,'coo_incomedisburse' AS flinkformid,A.fbillno AS flinkbillno,A.fid AS flinkbillinterid
                                      ,A.fdate AS flinkbilldate,A.fstaffid AS fsalstaffid,A.fdeptid AS fsaldeptid 
                                      ,0 AS fcurrentreceipt,A.famount AS fcurrentrefund,0 AS fcurrentstockout,0 AS fcurrentreturn,0 AS fotherreceipt
                                      FROM T_COO_INCOMEDISBURSE AS A WITH(NOLOCK) 
                                      INNER JOIN T_YDJ_CUSTOMER AS B ON A.fcustomerid = B.fid
                                      WHERE A.fmainorgid = @fmainorgid AND A.fstatus = @fstatus AND A.fcancelstatus = @fcancelstatus AND B.fforbidstatus = @fforbidstatus 
                                      	AND A.fdate >= @fdatefrom AND A.fdate <= @fdateto 
                                        AND A.fsourceformid IN ('ydj_saleintention','ydj_order','ydj_customer','ydj_collectreceipt') AND A.fbizdirection = 'bizdirection_02' --主控菜单【收款退款单】预置条件
	                                    AND NOT EXISTS (
	                                    	SELECT 1 FROM T_COO_INCOMEDISBURSE AS ICDB WITH(NOLOCK) 
	                                    	WHERE ICDB.fid = A.fid AND ICDB.fway = @fway_ref AND ICDB.fdirection = @fdirection_ref
	                                    )
                                      	{0}".Fmt(customerWhere));

            StringBuilder sbCurrStockOutSql = new StringBuilder();
            sbCurrStockOutSql.AppendLine(@"--本期出库
                                           SELECT B.fid AS fcustomerid,'stk_sostockout' AS flinkformid,A.fbillno AS flinkbillno,A.fid AS flinkbillinterid
                                           ,A.fdate AS flinkbilldate,A.fstockstaffid AS fsalstaffid,A.fstockdeptid AS fsaldeptid 
                                           ,0 AS fcurrentreceipt,0 AS fcurrentrefund,SUM(ENT.famount) AS fcurrentstockout,0 AS fcurrentreturn,0 AS fotherreceipt
                                           FROM T_STK_SOSTOCKOUT AS A WITH(NOLOCK) 
                                           INNER JOIN T_STK_SOSTOCKOUTENTRY AS ENT WITH(NOLOCK) ON A.fid = ENT.fid
                                           INNER JOIN T_YDJ_CUSTOMER AS B ON A.fcustomerid = B.fid
                                           WHERE A.fmainorgid = @fmainorgid AND A.fstatus = @fstatus AND A.fcancelstatus = @fcancelstatus AND B.fforbidstatus = @fforbidstatus 
                                           	AND A.fdate >= @fdatefrom AND A.fdate <= @fdateto {0}
                                           GROUP BY B.fid,A.fbillno,A.fid,A.fdate,A.fstockstaffid,A.fstockdeptid ".Fmt(customerWhere));

            StringBuilder sbCurrRetSql = new StringBuilder();
            sbCurrRetSql.AppendLine(@"--本期退货
                                      SELECT B.fid AS fcustomerid,'stk_sostockreturn' AS flinkformid,A.fbillno AS flinkbillno,A.fid AS flinkbillinterid
                                      ,A.fdate AS flinkbilldate,A.fsostaffid AS fsalstaffid,A.fsodeptid AS fsaldeptid 
                                      ,0 AS fcurrentreceipt,0 AS fcurrentrefund,0 AS fcurrentstockout,SUM(ENT.fbizqty * ENT.fprice) AS fcurrentreturn,0 AS fotherreceipt
                                      FROM T_STK_SOSTOCKRETURN AS A WITH(NOLOCK) 
                                      INNER JOIN T_STK_SOSTOCKRETURNENTRY AS ENT WITH(NOLOCK) ON A.fid = ENT.fid
                                      INNER JOIN T_YDJ_CUSTOMER AS B ON A.fcustomerid = B.fid
                                      WHERE A.fmainorgid = @fmainorgid AND A.fstatus = @fstatus AND A.fcancelstatus = @fcancelstatus AND B.fforbidstatus = @fforbidstatus 
                                      	AND A.fdate >= @fdatefrom AND A.fdate <= @fdateto {0}
                                      GROUP BY B.fid,A.fbillno,A.fid,A.fdate,A.fsostaffid,A.fsodeptid ".Fmt(customerWhere));

            StringBuilder sbOtherRecSql = new StringBuilder();
            sbOtherRecSql.AppendLine(@"--其他应收
                                       SELECT B.fid AS fcustomerid,'ydj_collectreceipt' AS flinkformid,A.fbillno AS flinkbillno,A.fid AS flinkbillinterid
                                       ,A.fregistdate AS flinkbilldate,A.frelatemanid AS fsalstaffid,A.ftrainingdept AS fsaldeptid 
                                       ,0 AS fcurrentreceipt,0 AS fcurrentrefund,0 AS fcurrentstockout,0 AS fcurrentreturn,A.fsumtaxamount AS fotherreceipt
                                       FROM T_YDJ_COLLECTRECEIPT AS A WITH(NOLOCK) 
                                       INNER JOIN T_YDJ_CUSTOMER AS B ON A.frelatecusid = B.fid
                                       WHERE A.fmainorgid = @fmainorgid AND A.fstatus = @fstatus AND A.fcancelstatus = @fcancelstatus AND B.fforbidstatus = @fforbidstatus 
                                       	AND A.frelatetype='ydj_customer' AND A.fregistdate >= @fdatefrom AND A.fregistdate <= @fdateto {0}".Fmt(customerWhere));

            StringBuilder sbChildSql = new StringBuilder();
            if (selectField == "*" || selectField == "fcurrentbalance")
            {
                sbChildSql.AppendLine(sbCurrRecSql.ToString());
                sbChildSql.AppendLine("UNION ALL");
                sbChildSql.AppendLine(sbCurrRefSql.ToString());
                sbChildSql.AppendLine("UNION ALL");
                sbChildSql.AppendLine(sbCurrStockOutSql.ToString());
                sbChildSql.AppendLine("UNION ALL");
                sbChildSql.AppendLine(sbCurrRetSql.ToString());
                sbChildSql.AppendLine("UNION ALL");
                sbChildSql.AppendLine(sbOtherRecSql.ToString());
            }
            else
            {
                switch (selectField)
                {
                    case "fcurrentreceipt":
                        sbChildSql.AppendLine(sbCurrRecSql.ToString());
                        break;
                    case "fcurrentrefund":
                        sbChildSql.AppendLine(sbCurrRefSql.ToString());
                        break;
                    case "fcurrentstockout":
                        sbChildSql.AppendLine(sbCurrStockOutSql.ToString());
                        break;
                    case "fcurrentreturn":
                        sbChildSql.AppendLine(sbCurrRetSql.ToString());
                        break;
                    case "fotherreceipt":
                        sbChildSql.AppendLine(sbOtherRecSql.ToString());
                        break;
                }
            }
            if (sbChildSql.ToString().IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            //查询当前经销商下所有的客户
            StringBuilder sbCustomerSql = new StringBuilder();
            sbCustomerSql.AppendLine($@"/*dialect*/INSERT INTO {this.DataSourceTableName}(fid,fjnidentityid
                                                   ,FFormId,fcustomerid,flinkformid,flinkbillno,flinkbillinterid,flinkbilldate,fsalstaffid,fsaldeptid
                                                   ,fcurrentreceipt,fcurrentrefund,fcurrentstockout,fcurrentreturn,fotherreceipt)  ");
            sbCustomerSql.AppendLine(@"SELECT row_number() over(order by T.flinkbilldate DESC) AS fid,row_number() over(order by T.flinkbilldate DESC) AS fjnidentityid 
                                        ,'' AS FFormId,T.fcustomerid,T.flinkformid,T.flinkbillno,T.flinkbillinterid,T.flinkbilldate,T.fsalstaffid,T.fsaldeptid
                                        ,T.fcurrentreceipt,T.fcurrentrefund,T.fcurrentstockout,T.fcurrentreturn,T.fotherreceipt
                                        FROM (
                                        	{0}
                                        ) AS T ORDER BY T.flinkbilldate DESC".Fmt(sbChildSql.ToString()));

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, sbCustomerSql.ToString(), sqlParams);
        }
    }
}
