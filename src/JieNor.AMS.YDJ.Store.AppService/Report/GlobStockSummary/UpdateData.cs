using JieNor.AMS.YDJ.Store.AppService;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.GlobStockSummary
{
    /// <summary>
    /// 商品库存分析表 - 经销商-手动更新
    /// </summary>
    [InjectService]
    [FormId("rpt_globstocksummary")]
    [OperationNo("updateData")]
    public class UpdateData : AbstractOperationServicePlugIn
    {
        public override async void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            // 通过临时表生成数据
            var tmpTableName = string.Empty;
            try
            {
                var fcategoryid = this.GetQueryOrSimpleParam<string>("fcategoryid");
                Task.Run(async () =>
                {
                    var stockAgeAnalysisService = this.Context.Container.GetService<GlobStockSumService>();
                    stockAgeAnalysisService?.StockCalculate(this.Context, fcategoryid);
                });
            }
            finally
            {
                //this.DBService.DeleteTempTableByName(this.Context, tmpTableName, true);
                //刷新页面
                this.AddRefreshPageAction();
            }
        }
    }
}
