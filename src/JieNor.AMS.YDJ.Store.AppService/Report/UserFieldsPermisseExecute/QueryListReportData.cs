using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;

namespace JieNor.AMS.YDJ.Store.AppService.Report.UserFieldsPermisseExecute
{
    [InjectService]
    [FormId("rpt_userfieldspermisse")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 数据库服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        protected override void OnExecuteLogic()
        {

            this.ProfitListData();

        }

        protected void ProfitListData()
        {

            StringBuilder sbInsertSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();
            StringBuilder sbFromSql = new StringBuilder();
            StringBuilder sbWhereSql = new StringBuilder();
            var sqlList = new List<string>();
            //业务对象 多个
            var fnmenugroupame = this.CustomFilterObject["fnmenugroupame"] as string;
            var fnmenugroupameArray = fnmenugroupame?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            //用户ID
            var userid = this.CustomFilterObject["fuserid"] as string;
            var useridArray = userid?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fuserid",System.Data.DbType.String,userid),
                new SqlParam("@fnmenugroupame",System.Data.DbType.String,fnmenugroupame)
            };

            sbWhereSql.Append(" where  u1.fmainorgid=@fmainorgid");

            if (userid.IsEmptyPrimaryKey() && fnmenugroupame.IsEmptyPrimaryKey())
            {
                sbWhereSql.Append("  and u1.fid=null and t0.fbizobjid=null ");
            }
            /*if (userid != null && !userid.IsNullOrEmptyOrWhiteSpace())
            {
                sbWhereSql.Append(string.Format(" and u1.fid = '{0}' ", @userid));
            }*/
            if (useridArray != null && useridArray?.Length > 0)
            {
                sqlParam.AddRange(useridArray.Select((x,i)=>new SqlParam($"@fuserid{i}",System.Data.DbType.String, x)));
                sbWhereSql.Append(string.Format(" and u1.fid in ({0})", string.Join(",", useridArray.Select((x, i) => $"@fuserid{i}"))));
                //sbWhereSql.Append(string.Format(" and u1.fid  in ('{0}')",string.Join(",", useridArray)));
            }
            if (fnmenugroupameArray != null && fnmenugroupameArray.Length > 0)
            {
                sqlParam.AddRange(fnmenugroupameArray.Select((x, i) => new SqlParam($"@fnmenugroupame{i}", System.Data.DbType.String, x)));
                sbWhereSql.Append(string.Format(" and  t0.fbizobjid in ({0}) ", string.Join(",", fnmenugroupameArray.Select((x, i) => $"@fnmenugroupame{i}"))));
            }

            // sbWhereSql.Append(" order by froleids desc ");
            //sbWhereSql.Append(" order by fnmenugroupame desc ");

            sbSelectSql.Append(@"
                                select row_number() over(order by u1.fid desc) as fid,
                                u1.fid fuserid,u1.fphone fuserphone,
                                (
                                select top 1 t3.fname from t_sys_menuitem t1
                                inner join t_sys_menugroup t2 on t1.fgroupid=t2.fgroupid
                                inner join t_sys_bizmodule t3 on t2.fmoduleid=t3.fmoduleid
                                where t0.fbizobjid= t1.fbillformid
                                ) fbizmodulename,
                                t0.fbizobjid fnmenugroupame,
                                t0.ffieldname ffieldname,
                                t0.ffieldid ffieldid,
                                (case when (t0.fvisible = 0) then '不可见' else '可见' end) fallow,u2.fid froleids
                                ");
            sbFromSql.Append(@" 
                                 from t_sec_roleuser t with (nolock)
                                 inner join t_sec_user u1 with (nolock) on t.fuserid = u1.fid
                                 inner join t_sec_role u2 with (nolock) on t.froleid = u2.fid and
                                                                           (u1.fmainorgid = u2.fmainorgid or u2.fmainorgid = '0' or
                                                                            u2.fmainorgid = '' or u2.fmainorgid = ' ')
                                 inner join t_sec_rolefieldacl t0 with (nolock) on t0.froleid = u2.fid and
                                                                                  (u1.fmainorgid = t0.fcompanyid or t0.fcompanyid = '0' or
                                                                                   t0.fcompanyid = '' or t0.fcompanyid = '')
                                ");
            sbInsertSql.Append($@"insert into {this.DataSourceTableName}
                                (fid,fuserid,fuserphone,fbizmodulename,fnmenugroupame,ffieldname,ffieldid,fallow,froleids
                                )");

            var strSql = $@"{sbInsertSql.ToString()}{sbSelectSql.ToString()}{sbFromSql.ToString()}{sbWhereSql.ToString()}";

            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();

            var affecteds = dbServiceExt.Execute(this.Context, strSql,sqlParam);

            if (affecteds > 0)
            {
                var rptSql = string.Format("select fid,fnmenugroupame,ffieldid from {0}",
                    this.DataSourceTableName);
                var rptDt = this.HtmlForm.GetDynamicObjectType(this.Context);
                var rptDynObjs = this.DBService.ExecuteDynamicObject(this.Context, rptDt, rptSql, new List<SqlParam>());

                //循环查询的数据，根据业务表单id查询业务表单模型
                foreach (var item in rptDynObjs)
                {
                    //获取临时表中的业务操作id
                    var fieldid = Convert.ToString(item["ffieldid"]);
                    //获取临时表中的业务对象
                    var formId = Convert.ToString(item["fnmenugroupame"]);
                    //获取要更新的表单模型
                    var bizForm = this.MetaModelService.LoadFormModel(this.Context, formId);

                    if (!fieldid.IsNullOrEmptyOrWhiteSpace())
                    {
                        if (bizForm.FieldList.ContainsKey(fieldid))
                        {
                            var field = bizForm.FieldList[fieldid];

                            var fileName = field.Caption;

                            //循环生成update语句
                            sqlList.Add($@"update {this.DataSourceTableName} set ffieldname='{fileName}'
                                       where fid='{item["id"]}' and ffieldid='{item["ffieldid"]}' and fnmenugroupame='{item["fnmenugroupame"]}'");
                        }
                    }
                    

                }


                //执行更新
                if (sqlList.Count > 0)
                {
                    this.DBServiceEx.ExecuteBatch(this.Context, sqlList);

                }

            }

        }

        /*protected override void OnAfterListData(List<Dictionary<string, object>> listData)
        {
            base.OnAfterListData(listData);
            listData = listData.OrderByDescending(x => x["fnmenugroupame"]).ToList();
        }*/

        /// <summary>
        /// 报表排序逻辑
        /// </summary>
        /// <param name="listQueryPara"></param>
        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {
            base.OnPrepareReportQueryParameter(listQueryPara);
            listQueryPara.OrderByString = "fnmenugroupame desc";
        }


    }
}