using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormModel.List;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Store.AppService.Report.EemployeeSalesrevenue
{
    /// <summary>
    /// 销售收款明细报表
    /// </summary>
    [InjectService]
    [FormId("ydj_employeesalesrevenue")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            this.GetData();
        }

        private void GetData()
        {
            StringBuilder sbOrderSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();
            StringBuilder sbOrderSelectSql = new StringBuilder();
            StringBuilder sbOrderFromSql = new StringBuilder();
            StringBuilder sbOrderWhereSql = new StringBuilder();

            var sqlParam = new List<SqlParam>
            {
                 new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
            };

            sbSelectSql.Append(@"select t1.fid,
			isnull((select fname from ser_ydj_category c where 
			c.fid = ss3.fparentid)+'/','')+
			isnull((select fname from ser_ydj_category c where 
			c.fid = ss2.fparentid)+'/','')+
			isnull((select fname from ser_ydj_category c where 
			c.fid = s1.fparentid)+'/','') + 
			isnull(s1.fname,'')  ptypename,
			fproductid,famount_s2,
							fsourceformid, fdate_s1,
							 fdescription_s1, fcustomerid,
							 fphone, fbillno,fbillno_s1,fdate_t1,fway,t1.fname,t1.fcreatedate,t1.fstatus,
                 fbizdirection, fdirection, fbizstatus, fconfirmdate, fstaffid, fstylistid, fdeptid,t1.fdescription,
                 ramount, fassistid, paymentdesc,fpurpose,fmnumber
			from view_employeesalesrevenue t1
			left join ser_ydj_category s1 on t1.fcategoryid = s1.fid
			left join ser_ydj_category ss2 on ss2.fid= s1.fparentid
			left join ser_ydj_category ss3 on ss3.fid= ss2.fparentid");

            var strSql = "/*dialect*/";
            strSql += $@"insert into {this.DataSourceTableName}
                                  (fid,ptypename,fproductid,famount_s2,fsourceformid,fdate_s1,fdescription_s1,fcustomerid,
                            fphone,fbillno,fbillno_s1,fdate_t1,fway,fname,fcreatedate,fstatus,
                            fbizdirection,fdirection,fbizstatus,fconfirmdate,fstaffid,fstylistid,
                            fdeptid,fdescription,ramount,fassistid,paymentdesc,fpurpose,fmnumber)
                            {sbSelectSql.ToString()}
                            ";
            var dbServiceExt = this.Context.Container.GetService<IDBServiceEx>();
            dbServiceExt.Execute(this.Context, strSql, sqlParam);

        }
    }
}
