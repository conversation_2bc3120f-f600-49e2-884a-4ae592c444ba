using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report.CategorystockDetailmary
{
    /// <summary>
    /// 父页面向子页面传经销商参数
    /// </summary>
    [InjectService]
    [FormId("rpt_categorystockdetailmary")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {

        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }

        protected override void OnIniHtmlForm(HtmlForm htmlForm)
        {
            base.OnIniHtmlForm(htmlForm);

            var ctx = this.Context;
            var dm = this.GetDataManager();
            var userprofileForm = this.MetaModelService.LoadFormModel(ctx, "sys_userprofile");
            var userprofileDt = userprofileForm.GetDynamicObjectType(ctx);
            dm.InitDbContext(this.Context, userprofileDt);

            var option = dm.SelectBy(string.Format(@" fbillformid= '{0}' And fuserid ='{1}' And fcategory =N'{2}' ", "rpt_categorystockdetailmary",
                this.Context.UserId, "listreport"));

            if (option.Count == 0)
            {
                var dbSvc = ctx.Container.GetService<IDBService>();
                var sql = @" select r.fid,r.fnumber from t_sec_roleuser ru with(nolock)
                             left join  t_sec_role r with(nolock) on ru.froleid = r.fid
                             where ru.fuserid = '{0}'".Fmt(ctx.UserId);
                var roleObj = dbSvc.ExecuteDynamicObject(ctx, sql)?.ToList();
                //查询自身用户角色是否为经销商老板,是则成本字段放开可见
                if (roleObj != null && roleObj.Count > 0)
                {
                    var fnumber = roleObj.Where(x => (string)x["fnumber"] == "YHJS01").FirstOrDefault();
                    if (!fnumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        foreach (var item in htmlForm.FieldList)
                        {
                            if (item.Value.Caption.Contains("成本"))
                            {
                                item.Value.Visible = -1;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            var fcategoryid = this.GetQueryOrSimpleParam<string>("fcategoryid");
            if (!fcategoryid.IsNullOrEmptyOrWhiteSpace())
            {
                if (this.PageSession == null)
                {
                    throw new BusinessException("页面缓存区已回收，请关闭本页面后重新打开！");
                }
                var sqlParam = new List<SqlParam>
                    {
                        new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                    };
                var sqlStr = @"select t3.fid,MAX(t3.fname) fname,MAX(t3.fnumber) fnumber, MAX(t3.fparentid) fparentid from t_stk_inventorylist t1 
                               inner join T_BD_MATERIAL t2 on t1.fmaterialid = t2.fid
                               inner join ser_ydj_category t3 on t2.fcategoryid = t3.fid
                               where t1.fmainorgid=@fmainorgid group by t3.fid";
                var allProType = this.DBService.ExecuteDynamicObject(Context, sqlStr, sqlParam);
                //床架类:取《即时库存》中包含【商品类别】=“床架(1001)”及其所有子类。
                var fid_01 = allProType.Where(x => (string)x["fnumber"] == "1001" && (string)x["fname"] == "床架").Select(x => (string)x["fid"]).FirstOrDefault();
                var fcategoryid_01 = getCategorys(allProType, fid_01).Where(x => x.fparentid == fid_01).Select(x => x.fid);
                //床垫类：取《即时库存》中包含【商品类别】=“床垫(1003)”及其所有子类。
                var fid_02 = allProType.Where(x => (string)x["fnumber"] == "1003" && (string)x["fname"] == "床垫").Select(x => (string)x["fid"]).FirstOrDefault();
                var fcategoryid_02 = getCategorys(allProType, fid_02).Where(x => x.fparentid == fid_02).Select(x => x.fid);
                //其它类:排除以上两类
                var fcategoryid_03 = allProType.Select(x => (string)x["fid"]).Except(fcategoryid_01).Except(fcategoryid_02);

                IEnumerable<string> fcategoryids = null;
                switch (fcategoryid)
                {
                    case "fcategoryid_01":
                        fcategoryids = fcategoryid_01;
                        break;
                    case "fcategoryid_02":
                        fcategoryids = fcategoryid_02;
                        break;
                    case "fcategoryid_03":
                        fcategoryids = fcategoryid_03;
                        break;
                }
                if (fcategoryids != null)
                {
                    this.PageSession.fcategoryid = string.Join(",", fcategoryids);
                }
            }
        }

        /// <summary>
        /// 获取商品类别及其子类
        /// </summary>
        /// <param name="fcategoryids"></param>
        /// <param name="sqlParam"></param>
        /// <returns></returns>
        private List<keyValue> getCategorys(DynamicObjectCollection dynamicObjects, string fcategoryid)
        {
            var allcatyKeyValues = dynamicObjects.Select(x => new keyValue()
            {
                fid = Convert.ToString(x["fid"]),
                fparentid = Convert.ToString(x["fparentid"]),
            }).ToList();
            List<keyValue> result = new List<keyValue>();
            GetCategoryTreeNodes(allcatyKeyValues, fcategoryid, ref result);
            return result;
        }

        /// <summary>
        /// 获取组织结构树(包含本身)
        /// </summary>
        /// <param name="list"></param>
        /// <param name="id"></param>
        /// <param name="treeNodes"></param>
        /// <returns></returns>
        static void GetCategoryTreeNodes(List<keyValue> list, string id, ref List<keyValue> treeNodes)
        {
            treeNodes.Add(new keyValue() { fid = id, fparentid = id });

            if (list == null || string.IsNullOrWhiteSpace(id))
                return;

            List<keyValue> sublist = null;
            if (!string.IsNullOrWhiteSpace(id))
            {
                sublist = list.Where(t => t.fparentid == id).ToList();
            }
            if (!sublist.Any())
                return;
            foreach (var item in sublist)
            {
                treeNodes.Add(new keyValue() { fid = item.fid, fparentid = item.fparentid });
                GetCategoryTreeNodes(list, item.fid, ref treeNodes);
            }
        }

        private class keyValue
        {
            public string fparentid { get; set; }
            public string fid { get; set; }
        }
    }
}
