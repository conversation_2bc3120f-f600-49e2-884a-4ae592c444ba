using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Data;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.Stock.AppService.Report.StockOutStoreSlaer
{
    /// <summary>
    /// 准备报表动态列模型
    /// </summary>
    [InjectService]
    [FormId("rpt_stockout_storesaler")]
    [OperationNo("QueryListReport")]
    public class QueryListReport : AbstractReportServicePlugIn
    {
        /// <summary>
        /// 执行报表逻辑
        /// </summary>
        protected override void OnExecuteLogic()
        {
            var rule = new DataQueryRuleParaInfo()
            {
                SrcFormId = "ydj_series",
                SrcFldId = "fseriesid"
            };
            var authView = this.Context.GetAgenAuthSeriDataPKID(rule);
            if (authView.Item2.IsNullOrEmptyOrWhiteSpace()) 
            { 
                return; 
            }

            //查询《系列》档案，所有授权且未禁用的系列以及自建的都需要在报表字段中体现
            var sql = "";
            List <DynamicObject> seriesInfo = new List<DynamicObject>();            
            if (authView.Item1 == "=")
            {
                sql = @"select t0.fname,t0.fnumber from  t_ydj_series t0 
                        where t0.fid ='{0}' and t0.fforbidstatus='0' and t0.fmainorgid in(@fmainorgid,@topfmainorgid) 
                        order by t0.fid".Fmt(authView.Item2);
            }
            else if (authView.Item1.EqualsIgnoreCase("in"))
            {
                sql = @"select t0.fname,t0.fnumber from  t_ydj_series t0 
                        where t0.fid in ({0}) and t0.fforbidstatus='0' and t0.fmainorgid in(@fmainorgid,@topfmainorgid) 
                        order by t0.fid".Fmt(authView.Item2);
            }
            else if (authView.Item1.EqualsIgnoreCase("exists"))
            {
                sql = @"select t0.fname,t0.fnumber from  t_ydj_series t0 
                        where exists ( select 1 from ({0}) TMPXX where TMPXX.FPKId=t0.fid ) 
                                and t0.fforbidstatus='0' and t0.fmainorgid in(@fmainorgid,@topfmainorgid) 
                        order by t0.fid".Fmt(authView.Item2);
            }
            
            var serparams = new List<SqlParam> { new SqlParam("@fmainorgid", DbType.String, this.Context.Company),
                                                new SqlParam("@topfmainorgid", DbType.String, this.Context.TopCompanyId)};
            seriesInfo = this.Context.ExecuteDynamicObject(sql, serparams).ToList();
             
            for (int i = 0; i < seriesInfo.Count; i++)
            {
                if (!this.HtmlForm.FieldList.ContainsKey($"fseries{i}"))
                {
                    //this.GetOrAddReportField($"fseries{i}", seriesInfo[i]["fname"] + "(" + seriesInfo[i]["fnumber"] + ")", HtmlElementType.HtmlField_AmountField);
                    var htmlField = HtmlParser.GetHtmlFieldInstance(this.Container, HtmlElementType.HtmlField_AmountField);
                    htmlField.Id = $"fseries{i}";
                    htmlField.FieldName = $"fseries{i}";
                    htmlField.EntityKey = "fbillhead";
                    htmlField.Caption = seriesInfo[i]["fname"] + "(" + seriesInfo[i]["fnumber"] + ")";
                    htmlField.Group = this.HtmlForm.Caption;
                    htmlField.Width = htmlField.Caption.GetSBytes().Length * 9;
                    htmlField.Visible = -1;
                    htmlField.ListTabIndex = this.HtmlForm.FieldList == null ? 0 : this.HtmlForm.FieldList.Max(x => x.Value.ListTabIndex) + 1;
                    this.HtmlForm.FieldList.Add($"fseries{i}", htmlField);
                }
            }
        }
    }
}
