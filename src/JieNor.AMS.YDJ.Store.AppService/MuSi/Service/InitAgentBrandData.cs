using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    /// <summary>
    /// 标准品映射 错误数据修复
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("InitAgentBrandData")]
    public class InitAgentBrandData: AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            var htmlForm = this.Context.Container.GetService<IMetaModelService>()
                             .LoadFormModel(this.Context, "ydj_agentbrandseries");

            base.AfterExecuteOperationTransaction(e);

            var productAuthDatas = this.Context.LoadBizDataByFilter("ydj_productauth", " fforbidstatus = 0 and forgtype = '4' ");

            if (productAuthDatas == null || !productAuthDatas.Any())
            {
                this.Result.SimpleMessage = "没有查询到商品授权数据，当前无需处理。";
                return;
            }
            Task.Run(() =>
            { 
                var deliverService = this.Container.GetService<IDeliverService>();
                deliverService.AddOrUpdateAgentBrandSeriesData(this.Context, productAuthDatas);
            });
        }
    }
}
