using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    /// <summary>
    /// 业务对象字段映射服务接口实现
    /// </summary>
    [InjectService]
    public class MuSiBizObjMapService : IMuSiBizObjMapService
    {
        public List<DynamicObject> GetBizObjMaps(UserContext userCtx, HtmlForm htmlForm, Enu_MuSiSyncDir syncDir, Enu_MuSiSyncTimePoint syncTimePoint, string filter = null)
        {
            var topCtx = userCtx.CreateTopOrgDBContext();

            var sysProfileService = topCtx.Container.GetService<ISystemProfile>();

            var bizObjMaps = new List<DynamicObject>();

            if ((syncDir & Enu_MuSiSyncDir.CurrentToMuSi) == Enu_MuSiSyncDir.CurrentToMuSi)
            {
                var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiextappid", "");

                bizObjMaps.AddRange(GetBizObjMaps(topCtx, htmlForm, extAppId, Enu_MuSiSyncDir.CurrentToMuSi, syncTimePoint, filter));

                //认证中心
                extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiauthappid", "");

                bizObjMaps.AddRange(GetBizObjMaps(topCtx, htmlForm, extAppId, Enu_MuSiSyncDir.CurrentToMuSi, syncTimePoint, filter));
            }
            if ((syncDir & Enu_MuSiSyncDir.CurrentToOMSMuSi) == Enu_MuSiSyncDir.CurrentToOMSMuSi)
            {
                var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiomsextappid", "");
                bizObjMaps.AddRange(GetBizObjMaps(topCtx, htmlForm, extAppId, Enu_MuSiSyncDir.CurrentToMuSi, syncTimePoint, filter));
            }

            if ((syncDir & Enu_MuSiSyncDir.MuSiToCurrent) == Enu_MuSiSyncDir.MuSiToCurrent)
            {
                var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiextappidpull", "");
                bizObjMaps.AddRange(GetBizObjMaps(topCtx, htmlForm, extAppId, Enu_MuSiSyncDir.MuSiToCurrent, syncTimePoint, filter));
            }

            return bizObjMaps;
        }

        public List<DynamicObject> GetBizObjMaps(UserContext userCtx, HtmlForm htmlForm, string extAppId, Enu_MuSiSyncDir syncDir, Enu_MuSiSyncTimePoint syncTimePoint, string filter = null)
        {
            if (extAppId.IsNullOrEmptyOrWhiteSpace()) return new List<DynamicObject>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            var strWhere = $@" fmyobjectid='{htmlForm.Id}' and fforbidstatus='0' and fextappid='{extAppId}'";

            if (!filter.IsNullOrEmptyOrWhiteSpace())
            {
                strWhere += $" and ({filter})";
            }

            switch (syncTimePoint)
            {
                case Enu_MuSiSyncTimePoint.SyncTimer:
                    strWhere += " and fsynctimer='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncManual:
                    strWhere += " and fsyncmanual='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncAfterSave:
                    strWhere += " and fsyncaftersave='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncAfterDelete:
                    strWhere += " and fsyncafterdelete='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncAfterAudit:
                    strWhere += " and fsyncafteraudit='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncAfterUnaudit:
                    strWhere += " and fsyncafterunaudit='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncAfterSubmit:
                    strWhere += " and fsyncaftersubmit='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncAfterForbid:
                    strWhere += " and fsyncafterforbid='1' ";
                    break;
                case Enu_MuSiSyncTimePoint.SyncAfterUnForbid:
                    strWhere += " and fsyncafterunforbid='1' ";
                    break;
                default:
                    throw new BusinessException($"{syncTimePoint}未定义！");
            }

            switch (syncDir)
            {
                case Enu_MuSiSyncDir.CurrentToMuSi:
                case Enu_MuSiSyncDir.CurrentToOMSMuSi:
                    strWhere += " and fsyncdir='currenttomusi' ";
                    break;
                case Enu_MuSiSyncDir.MuSiToCurrent:
                    strWhere += " and fsyncdir='musitocurrent' ";
                    break;
            }

            var datas = topCtx.LoadBizDataByFilter("si_musibizobjmap", strWhere);

            if (datas == null || datas.Count == 0) return datas;

            // 按顺序执行
            datas = datas.OrderBy(s => Convert.ToInt32(s["fsyncorder"])).ToList();

            //先加载总部的配置
            var configs = datas.Where(x => Convert.ToString(x["fmainorgid"]).EqualsIgnoreCase(topCtx.Company)).ToList();
            //再加载系统预置的配置
            foreach (var data in datas.Where(x => Convert.ToString(x["fmainorgid"]).EqualsIgnoreCase("0")))
            {
                var fapino = Convert.ToString(data["fapino"]);
                var fextappid = Convert.ToString(data["fextappid"]);
                var fmyobjectid = Convert.ToString(data["fmyobjectid"]);

                if (fextappid.IsNullOrEmptyOrWhiteSpace()) continue;

                //如果已存在相同的配置则不需要再加载
                var config = configs.FirstOrDefault(x => Convert.ToString(x["fapino"]).EqualsIgnoreCase(fapino) &&
                                                         Convert.ToString(x["fextappid"]).EqualsIgnoreCase(fextappid) &&
                                                         Convert.ToString(x["fmyobjectid"]).EqualsIgnoreCase(fmyobjectid));

                if (config == null)
                {
                    configs.Add(data);
                }
            }

            return configs;
        }
    }
}
