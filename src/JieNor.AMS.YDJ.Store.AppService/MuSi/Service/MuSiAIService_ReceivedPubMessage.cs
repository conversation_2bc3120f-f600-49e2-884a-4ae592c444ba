using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.MP;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    /// <summary>
    /// 慕思AI云缓存实现
    /// </summary>
    public partial class MuSiAIService
    {
        public static Tuple<string, string> Prop_BedHeight { get; } = new Tuple<string, string>("S006", "床垫高");

        public static Tuple<string, string> Prop_BedLength { get; } = new Tuple<string, string>("S044", "床垫长");

        public static Tuple<string, string> Prop_BedWidth { get; } = new Tuple<string, string>("S045", "床垫宽");

        public static Tuple<string, string> Prop_CID { get; } = new Tuple<string, string>("S648", "客户ID");

        public static Tuple<string, string> Prop_CustomParam { get; } = new Tuple<string, string>("S173", "床垫其它定制");



        #region 订阅

        private static object _lockObj = new object();

        private static MSAICloudCache _cache;

        private static List<Tuple<string, string>> PropItems = new List<Tuple<string, string>>
        {
            Prop_CID, Prop_BedHeight, Prop_BedLength, Prop_BedWidth, Prop_CustomParam
        };

        /// <summary>
        /// 清空缓存
        /// </summary>
        /// <param name="userCtx"></param>
        public static void ClearCache(UserContext userCtx)
        {
            ClearCache(userCtx.CreateTopOrgDBContext());
            userCtx.Container.GetService<IPubSubService>().PublishMessage(ConstPubSubChannel.MS_AICloud, userCtx.TopCompanyId);
        }

        private static void DoClearCache(UserContext topCtx)
        {
            lock (_lockObj)
            {
                WriteDebugLog("慕思AI云属性清空缓存");

                _cache.PropDic?.Clear();
                _cache.PropDic = null;
                _cache = null;

                CacheDatas(topCtx);
            }
        }

        private static bool TryCacheDatas(UserContext userCtx)
        {
            if (_cache != null && _cache.PropDic.Count > 0) return false;

            CacheDatas(userCtx);

            return true;
        }

        private static void CacheDatas(UserContext userCtx)
        {
            lock (_lockObj)
            {
                MSAICloudCache cache = new MSAICloudCache();

                CacheProp(userCtx, cache);

                cache.CacheTime = DateTime.Now;

                _cache = cache;
            }
        }

        private static void CacheProp(UserContext userCtx, MSAICloudCache cache)
        {
            List<string> filters = new List<string>();

            foreach (var item in PropItems)
            {
                filters.Add($"(fnumber='{item.Item1}' and fname='{item.Item2}')");
            }

            string sql = $@"
select fid, fnumber, fname from t_sel_prop with(nolock) 
where {filters.JoinEx(" or ", false)}
";

            WriteDebugLog(sql);

            var dynObjs = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>());

            foreach (var dynObj in dynObjs)
            {
                string fid = Convert.ToString(dynObj["fid"]);
                string fnumber = Convert.ToString(dynObj["fnumber"]);
                string fname = Convert.ToString(dynObj["fname"]);

                foreach (var item in PropItems)
                {
                    if (item.Item1.EqualsIgnoreCase(fnumber) && item.Item2.EqualsIgnoreCase(fname))
                    {
                        cache.PropDic[fid] = item;
                        break;
                    }
                }
            }

            dynObjs.Clear();
            dynObjs = null;
        }

        public static void WriteDebugLog(string content)
        {
            DebugUtil.WriteLogToFile(content, "MSAICloud");
        }

        public static void Init(UserContext userCtx)
        {
            TryCacheDatas(userCtx);
        }


        public string ChannelName
        {
            get { return ConstPubSubChannel.MS_AICloud; }
        }

        public void OnReceivedMessage(string msg)
        {
            string topCompanyId = msg;

            var topCtx = topCompanyId.CreateDBContextByCompanyId(topCompanyId);

            DoClearCache(topCtx);
        }

        #endregion

        /// <summary>
        /// 是否AI床垫
        /// </summary>
        /// <param name="auxPropValSet"></param>
        /// <returns></returns>
        public string GetPropValue(UserContext userCtx, DynamicObject auxPropValSet, Tuple<string, string> prop)
        {
            TryCacheDatas(userCtx);

            var fentity = auxPropValSet["fentity"] as DynamicObjectCollection;

            foreach (var entity in fentity)
            {
                var fauxpropid = Convert.ToString(entity["fauxpropid"]);

                if (_cache.PropDic != null && _cache.PropDic.TryGetValue(fauxpropid, out var value) && value.Equals(prop))
                {
                    var fvalueid = Convert.ToString(entity["fvalueid"]);
                    var fvaluename = Convert.ToString(entity["fvaluename"]);

                    return fvaluename;
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// 是否拥有此属性
        /// </summary>
        /// <param name="auxPropValSet"></param>
        /// <returns></returns>
        public bool HasProp(UserContext userCtx, DynamicObject auxPropValSet, Tuple<string, string> prop)
        {
            TryCacheDatas(userCtx);

            var fentity = auxPropValSet["fentity"] as DynamicObjectCollection;

            foreach (var entity in fentity)
            {
                var fauxpropid = Convert.ToString(entity["fauxpropid"]);

                if (_cache.PropDic != null && _cache.PropDic.TryGetValue(fauxpropid, out var value) && value.Equals(prop))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 是否此属性
        /// </summary>
        /// <param name="fpropid"></param>
        /// <returns></returns>
        public bool IsProp(UserContext userCtx, string fpropid, Tuple<string, string> prop)
        {
            TryCacheDatas(userCtx);

            if (_cache.PropDic != null && _cache.PropDic.TryGetValue(fpropid, out var value) && value.Equals(prop))
            {
                return true;
            }

            return false;
        }

        public Tuple<string, string> GetProp_CID()
        {
            return Prop_CID;
        }

        public Tuple<string, string> GetProp_CustomParam()
        {
            return Prop_CustomParam;
        }
    }

    internal class MSAICloudCache
    {
        public DateTime CacheTime { get; set; }

        /// <summary>
        /// 属性id -> 特定属性number、name
        /// </summary>
        public Dictionary<string, Tuple<string, string>> PropDic = new Dictionary<string, Tuple<string, string>>();
    }
}
