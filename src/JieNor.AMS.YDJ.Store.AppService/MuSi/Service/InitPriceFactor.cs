using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    /// <summary>
    /// 标准品映射 错误数据修复
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("InitPriceFactor")]
    public class InitPriceFactor : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {

            base.AfterExecuteOperationTransaction(e);
            ResetSysAdminUserContext(this.Context);
            var htmlForm = this.Context.Container.GetService<IMetaModelService>()
                             .LoadFormModel(this.Context, "ydj_factorprice");

            var PriceFactorObj = this.Context.LoadBizDataByFilter("ydj_factorprice",$"fmainorgid = '{this.Context.TopCompanyId}' and fcreatorid = 'sysadmin' ")?.FirstOrDefault();
            if (PriceFactorObj.IsNullOrEmptyOrWhiteSpace())
            {
                PriceFactorObj = htmlForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                PriceFactorObj["id"] = string.Empty;
                PriceFactorObj["fnumber"] = string.Empty;
                PriceFactorObj["fcreatorid"] = "sysadmin";
                PriceFactorObj["fmodifierid"] = "sysadmin";
                var productIds = this.Context.LoadBizBillHeadDataByACLFilter("ydj_product", $" (fcustom =1 or fispresetprop=1 or funstdtype=1 ) and exists (select 1 from T_YDJ_PRICEENTRY as pcmx with (nolock) inner join T_YDJ_PRICE as pc with (nolock) on pc.fid =pcmx.fid where pcmx.fproductid = t_bd_material.fid and pcmx.fattrinfo ='' and pc.fmainorgid ='{this.Context.TopCompanyId}') ", "fid")
                    .Select(o => Convert.ToString(o["fid"])).ToList();

                Task.Run(() =>
                {
                    IPriceService priseService = this.Container.GetService<IPriceService>();
                    priseService.CreateOrAlterPriceFactor(this.Context, PriceFactorObj, productIds);
                    var geteway = this.Context.Container.GetService<IHttpServiceInvoker>();   
                    geteway.InvokeBillOperation(this.Context, htmlForm.Id, new[] { PriceFactorObj }, "draft", new Dictionary<string, object> { { "IgnoreCheckPermssion", "true" } });
                });
            }
            else
            {
                this.Result.SimpleMessage = "已预置过总部零售系数，当前无需处理。";
                return;
            }  
        }


        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext(UserContext context)
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = context.Product;
            session.Company = context.Company;
            session.BizOrgId = context.Company;
            session.TopCompanyId = context.TopCompanyId;
            session.ParentCompanyId = context.ParentCompanyId;
            session.Companys = context.Companys.ToList();
            session.Id = context.Id;

            context.SetUserSession(session);
        }


        /// <summary>
        /// 加载城市+经销商 匹配送达方 的商品授权清单
        /// </summary>
        private DynamicObjectCollection LoadProductAuthByDeliver()
        {
            //总部企业标识
            var topOrgId = this.Context.IsTopOrg ? this.Context.Company : this.Context.TopCompanyId;
            var sqlText = $@"select distinct deliver.fid as deliverid,deliver.fsaleorgid,pauth.fid,'4' as forgtype  
                            from t_ydj_productauth pauth
                            inner join T_BAS_DELIVER deliver on pauth.forgid = deliver.fagentid and pauth.fcityid = deliver.fcity
                            where forgtype ='4' and fcityid !='' and deliver.fforbidstatus = 0 and pauth.fmainorgid = '{topOrgId}'
                            union
                            select distinct deliver.fid as deliverid,deliver.fsaleorgid,pauth.fid,'5' as forgtype
                            from t_ydj_productauth pauth
                            inner join T_BAS_STORE store on pauth.forgid = store.fid 
                            inner join T_BAS_DELIVER deliver on store.fagentid = deliver.fagentid and store.fmycity = deliver.fcity 
                            where forgtype ='5' and fcityid !='' and store.fforbidstatus = 0 and deliver.fforbidstatus = 0 and pauth.fmainorgid = '{topOrgId}'
";

            var dynObs = this.DBService.ExecuteDynamicObject(this.Context, sqlText);

            return dynObs;
        }
    }
}
