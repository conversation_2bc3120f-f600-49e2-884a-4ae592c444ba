using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Integration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Utils
{
    /// <summary>
    /// 慕思帮助类
    /// </summary>
    public static class MuSiUtil
    {
        /// <summary>
        /// 转换辅助属性组合值
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="auxPropValId">辅助属性组合值ID，用于处理默认的辅助属性值</param>
        /// <returns>商品辅助属性列表</returns>
        public static JArray ToAttribute(UserContext userCtx, string auxPropValId)
        {
            JArray attributes = new JArray();
            if (auxPropValId.IsNullOrEmptyOrWhiteSpace()) return attributes;

            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var dm = userCtx.Container.GetService<IDataManager>();

            var valSetForm = metaService.LoadFormModel(userCtx, "bd_auxpropvalueset");
            dm.InitDbContext(userCtx, valSetForm.GetDynamicObjectType(userCtx));
            var setObj = dm.Select(auxPropValId) as DynamicObject;
            var setEntrys = setObj?["fentity"] as DynamicObjectCollection;
            if (setEntrys != null && setEntrys.Any())
            {
                var muSiAiService = userCtx.Container.GetService<IMuSiAIService>();

                var refMgr = userCtx.Container.GetService<LoadReferenceObjectManager>();
                refMgr.Load(userCtx, valSetForm.GetDynamicObjectType(userCtx), setObj, false);

                // 获取属性值实体
                var propValues = userCtx.LoadBizDataById("sel_propvalue", setEntrys.Select(s => s["fvalueid"].ToString()));

                var isAiBed = setEntrys.Any(s => muSiAiService.IsProp(userCtx, Convert.ToString(s["fauxpropid"]), muSiAiService.GetProp_CID()));

                foreach (var item in setEntrys)
                {
                    JObject attribute = new JObject();

                    var prop = item["fauxpropid_ref"] as DynamicObject;
                    var propId = Convert.ToString(prop?["id"]);
                    var propValue = propValues.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(Convert.ToString(item["fvalueid"])));

                    attribute["attrCode"] = Convert.ToString(prop?["fnumber"]);       // 属性code
                    attribute["attrName"] = Convert.ToString(prop?["fname"]);       // 属性名称
                    attribute["attrOcusText"] = "";   // 其他定制属性值
                    attribute["attrCodeVal"] = "";    // 属性值code
                    attribute["attrVal"] = "";        // 属性值

                    if (isAiBed && muSiAiService.IsProp(userCtx, propId, muSiAiService.GetProp_CustomParam()))
                    {
                        attribute["attrOcusText"] = "无";   // 其他定制属性值 
                    }
                    else
                    {
                        if (propValue == null)
                        {
                            attribute["attrCodeVal"] = Convert.ToString(item["fvaluenumber"]);    // 属性值code
                            attribute["attrVal"] = Convert.ToString(item["fvaluename"]);        // 属性值
                        }
                        else
                        {
                            // 非标录入生成
                            var fnosuitcreate = Convert.ToBoolean(propValue["fnosuitcreate"]);
                            if (fnosuitcreate)
                            {
                                attribute["attrOcusText"] = Convert.ToString(propValue["fname"]);   // 其他定制属性值 
                            }
                            else
                            {
                                attribute["attrCodeVal"] = Convert.ToString(propValue["fnumber"]);    // 属性值code
                                attribute["attrVal"] = Convert.ToString(propValue["fname"]);        // 属性值
                            }
                        }
                    }

                    attributes.Add(attribute);
                }
            }

            return attributes;
        }

        /// <summary>
        /// 转换加工要求
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="auxPropValId">辅助属性id</param>
        /// <param name="customDes">定制说明</param>
        /// <param name="packageDes">套件说明</param>
        /// <returns></returns>
        public static string ToProccessReq(UserContext userCtx, string auxPropValId, string customDes, string packageDes)
        {
            List<string> lstComboStr = new List<string>();

            if (!customDes.IsNullOrEmptyOrWhiteSpace())
            {
                lstComboStr.Add(customDes);
            }

            var auxPropService = userCtx.Container.GetService<IAuxPropService>();
            var auxPropName = auxPropService.GetSortedAuxPropText(userCtx, auxPropValId);
            if (!auxPropName.IsNullOrEmptyOrWhiteSpace())
            {
                lstComboStr.Add(auxPropName);
            }

            if (!packageDes.IsNullOrEmptyOrWhiteSpace())
            {
                lstComboStr.Add(packageDes);
            }

            // 【定制说明】+【辅助属性】+【套件说明】
            return string.Join("", lstComboStr);
        }

        /// <summary>
        /// 映射指定实体数据
        /// </summary>
        /// <param name="htmlForm"></param>
        /// <param name="entity"></param>
        /// <param name="syncFieldIds">同步字段标识</param>
        /// <param name="fromEntity">来源实体</param>
        /// <param name="toEntity">目标实体</param>
        /// <returns></returns>
        public static void MapEntityData(HtmlForm htmlForm, HtmlEntity entity, List<string> syncFieldIds, DynamicObject fromEntity, DynamicObject toEntity)
        {
            if (entity is HtmlHeadEntity)
            {
                // 遍历字段映射
                var allFlds = entity.GetFieldList(htmlForm.FieldList.Values.ToList());
                foreach (var field in allFlds)
                {
                    bool canMap = syncFieldIds.Contains(field.Id, StringComparer.OrdinalIgnoreCase);
                    if (canMap)
                    {
                        toEntity[field.PropertyName] = fromEntity[field.PropertyName];

                        // 如果是多选基础资料
                        if (field.ElementType == HtmlElementType.HtmlField_MulBaseDataField)
                        {
                            toEntity[field.PropertyName + "_txt"] = fromEntity[field.PropertyName + "_txt"];
                        }
                    }
                }
            }
            else if (entity is HtmlEntryEntity)
            {
                var allFlds = entity.GetFieldList(htmlForm.FieldList.Values.ToList());
                // 如果映射字段里没有此单据体，就跳过
                if (!allFlds.Any(field => syncFieldIds.Contains(field.Id, StringComparer.OrdinalIgnoreCase)))
                {
                    return;
                }

                string idFieldId = "ftranid";

                var fromEntryRows = (DynamicObjectCollection)fromEntity[entity.PropertyName];
                var toEntryRows = (DynamicObjectCollection)toEntity[entity.PropertyName];

                List<DynamicObject> entrys = new List<DynamicObject>();

                foreach (var fromEntryRow in fromEntryRows)
                {
                    // 判断当前行是否存在
                    string fromTranId = Convert.ToString(fromEntryRow[idFieldId]);
                    var toEntryRow = toEntryRows.FirstOrDefault(s => Convert.ToString(s[idFieldId]).EqualsIgnoreCase(fromTranId));
                    if (toEntryRow == null)
                    {
                        toEntryRow = (DynamicObject)entity.DynamicObjectType.CreateInstance();
                        toEntryRow[idFieldId] = fromTranId;
                    }

                    // 遍历字段映射
                    foreach (var field in allFlds)
                    {
                        bool canMap = syncFieldIds.Contains(field.Id, StringComparer.OrdinalIgnoreCase);
                        if (canMap)
                        {
                            toEntryRow[field.PropertyName] = fromEntryRow[field.PropertyName];

                            // 如果是多选基础资料
                            if (field.ElementType == HtmlElementType.HtmlField_MulBaseDataField)
                            {
                                toEntryRow[field.PropertyName + "_txt"] = fromEntryRow[field.PropertyName + "_txt"];
                            }
                        }
                    }

                    // 覆写 
                    entrys.Add(toEntryRow);
                }

                // 覆写 
                toEntryRows.Clear();
                foreach (var entry in entrys)
                {
                    toEntryRows.Add(entry);
                }
            }
        }

        /// <summary>
        /// 获取当前企业的经销商字段值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agent"></param>
        /// <returns></returns>
        public static string GetCurrAgentFieldValue(UserContext userCtx, DynamicObject agent, string fieldId)
        {
            if (agent == null) return string.Empty;

            /*
             * 根据当前企业对应的《经销商》是否勾选上【是否分销商】来判断
             * 1. 如果对应的《经销商》勾选上【是否分销商】时, 则获取《经销商》表头【所属上级组织】赋值
             * 2. 如果没勾选上时, 则根据当前企业对应的《经销商》的值
             */

            var fisreseller = Convert.ToBoolean(agent["fisreseller"]);

            if (fisreseller)
            {
                var parentMainOrgId = Convert.ToString(agent["forgid"]);
                var parentMainOrg = userCtx.LoadBizDataById("bas_agent", parentMainOrgId);

                return Convert.ToString(parentMainOrg?[fieldId]);
            }

            return Convert.ToString(agent[fieldId]);
        }

        /// <summary>
        /// 获取二级经销商的字段值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="agent"></param>
        /// <returns></returns>
        public static string GetResellerAgentFieldValue(UserContext userCtx, DynamicObject agent, string fieldId)
        {
            if (agent == null) return string.Empty;

            /*
             * 根据当前企业对应的《经销商》是否勾选上【是否分销商】来判断
             * 1. 如果对应的《经销商》勾选上【是否分销商】时, 则获取当前企业对应的《经销商》的字段值
             * 2. 如果没勾选上时, 则不赋值
             */

            var fisreseller = Convert.ToBoolean(agent["fisreseller"]);

            if (fisreseller)
            {
                return Convert.ToString(agent[fieldId]);
            }

            return string.Empty;
        }

        public static CompanyDCInfo GetCompanyDcInfo(UserContext userCtx, string companyId)
        {
            userCtx.GetAllCompanys().TryGetValue(companyId, out var dcInfo);

            return dcInfo;
        }
    }
}
