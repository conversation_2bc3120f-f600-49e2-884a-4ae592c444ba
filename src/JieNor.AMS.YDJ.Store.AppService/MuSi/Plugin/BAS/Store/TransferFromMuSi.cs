using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Store
{
    /// <summary>
    /// 门店：慕思同步
    /// </summary>
    [InjectService]
    [FormId("bas_store")]
    [OperationNo("syncfrommusi")]
    [ThirdSystemId("musi")]
    public class TransferFromMuSi : AbstractSyncDataFromMuSiPlugIn
    {
        [InjectProperty]
        public IStoreService StoreService { get; set; }

        [InjectProperty]
        public IProductAuthService ProductAuthService { get; set; }

        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            var fldId = e.FieldEntry["fmyfieldid"]?.ToString();
            if (fldId.IsNullOrEmptyOrWhiteSpace()) return;

            var fld = this.HtmlForm.GetField(fldId);
            if (fld == null) return;

            // 判断经销商是否存在
            string fextfieldid = e.FieldEntry["fextfieldid"]?.ToString();
            e.ExternalData.TryGetValue(fextfieldid, out var token);

            switch (fldId.ToLower())
            {
                // 如果中台的【门店类型编码】="SJ007"（新渠道店）时，对接时勾选上该字段【创新渠道标记】，否则不勾选
                case "fisnewchannel":
                    {
                        if (token != null)
                        {
                            var value = token.ToString();

                            e.Result = value.EqualsIgnoreCase("SJ007");
                            e.Cancel = true;
                        }
                    }
                    break;
                case "ftype":

                    break;
                // 公司地址（TASK#38182）
                case "fcompanyaddress":
                    {
                        //  province + city + area + street + shop_type_value + shop_floor + shop_floor_number

                        e.ExternalData.TryGetValue("province", out var province);
                        e.ExternalData.TryGetValue("city", out var city);
                        e.ExternalData.TryGetValue("area", out var area);
                        e.ExternalData.TryGetValue("street", out var street);
                        e.ExternalData.TryGetValue("shop_type_value", out var shop_type_value);
                        e.ExternalData.TryGetValue("shop_floor", out var shop_floor);

                        string[] addressFlds = new string[] { "province", "city", "area", "street", "shop_type_value", "shop_floor", "shop_floor_number" };
                        StringBuilder address = new StringBuilder();
                        foreach (var addressFld in addressFlds)
                        {
                            e.ExternalData.TryGetValue(addressFld, out var addressValue);
                            address.Append(addressValue?.ToString() ?? "");
                        }

                        e.Result = address.ToString();
                        e.Cancel = true;

                        break;
                    }
            }
        }

        public override void BeforeSave(BeforeSaveEventArgs e)
        {
            base.BeforeSave(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            this.StoreService.UpdateBoss(this.Context, e.DataEntitys);
            this.StoreService.UpdateAgentAndDeliver(this.Context, e.DataEntitys);
            this.StoreService.UpdateCrmDistributor(this.Context, e.DataEntitys);

            var lstValidDataEntities = new List<DynamicObject>();

            var agentFld = this.HtmlForm.GetField("fagentid");
            var actualownernumberFld = this.HtmlForm.GetField("actualownernumber");

            foreach (var dataEntity in e.DataEntitys)
            {
                // 假如没有前置同步送达方及经销商，过滤不执行后续的保存
                if (dataEntity["fagentid"].IsNullOrEmptyOrWhiteSpace())
                {
                    string msg =
                        $"{this.HtmlForm.Caption}{dataEntity["fnumber"]}的【{agentFld.Caption}】字段为空，无法保存，请检查是否未同步经销商或送达方！";

                    this.WriteOpLog(msg);
                    e.Result.ComplexMessage.ErrorMessages.Add(msg);

                    continue;
                }

                if (dataEntity["actualownernumber"].IsNullOrEmptyOrWhiteSpace())
                {
                    string msg =
                        $"{this.HtmlForm.Caption}{dataEntity["fnumber"]}的【{actualownernumberFld.Caption}】字段为空，无法保存，请检查是否未同步实控人！";

                    this.WriteOpLog(msg);
                    e.Result.ComplexMessage.ErrorMessages.Add(msg);

                    continue;
                }

                lstValidDataEntities.Add(dataEntity);
            }
            // 更新【门店类型】对应经销商 经营模式
            this.StoreService.UpdateStoreType(this.Context, lstValidDataEntities);

            e.DataEntitys = lstValidDataEntities;
        }

        public override void AfterSave(AfterSaveEventArgs e)
        {
            base.AfterSave(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            var stores = e.DataEntitys;

            ToggleForbid(stores, e.Result);

            //AuditStore(stores, e.Result);

            //RewriteDept(stores);

            // 已审核 且 非禁用的门店才生成
            var needToAddStores = stores?
                .Where(s => Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E"))
                .Where(s => !Convert.ToBoolean(s["fforbidstatus"])).ToList();

            //this.StoreService.AddOrUpdateProductAuth(this.Context, needToAddStores);
        }

        /// <summary>
        /// 切换禁用门店
        /// </summary>
        /// <param name="stores"></param>
        /// <param name="result"></param>
        private void ToggleForbid(IEnumerable<DynamicObject> stores, IOperationResult result)
        {
            IOperationResult opResult = null;

            /*
             * 切换禁用状态
             */

            // 查出【状态】=启用 且 【禁用状态】=是，进行反禁用
            var toUnforbidStores = stores
                .Where(s => Convert.ToString(s["fstorestatus"])
                    .EqualsIgnoreCase(((int)Enu_StoreStatus.Enable).ToString()))
                .Where(s => Convert.ToBoolean(s["fforbidstatus"]))
                .ToList();
            if (toUnforbidStores.Any())
            {
                opResult = this.Gateway.InvokeBillOperation(this.Context, "bas_store", toUnforbidStores, "UnForbid",
                  new Dictionary<string, object>());
                result.MergeResult(opResult);

                this.WriteOpLog("门店反禁用结果：" + result.ToString());
            }

            // 查出【状态】=禁用 且 【禁用状态】=否，进行禁用
            var toForbidStores = stores
                .Where(s => Convert.ToString(s["fstorestatus"])
                    .EqualsIgnoreCase(((int)Enu_StoreStatus.Disable).ToString()))
                .Where(s => !Convert.ToBoolean(s["fforbidstatus"]))
                .ToList();
            if (toForbidStores.Any())
            {
                // 反审核
                var unauditStores = toForbidStores
                    .Where(s => Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E"))
                    .ToList();
                if (unauditStores.Any())
                {
                    opResult = this.Gateway.InvokeBillOperation(this.Context, "bas_store", unauditStores, "UnAudit",
                     new Dictionary<string, object>());
                    result.MergeResult(opResult);
                    this.WriteOpLog("门店反审核结果：" + opResult.ToString());
                }

                // 撤消提交
                var unsubmitStores = toForbidStores
                    .Where(s => Convert.ToString(s["fstatus"]).EqualsIgnoreCase("D"))
                    .ToList();
                if (unsubmitStores.Any())
                {
                    opResult = this.Gateway.InvokeBillOperation(this.Context, "bas_store", unsubmitStores, "UnSubmit",
                      new Dictionary<string, object>());
                    result.MergeResult(opResult);
                    this.WriteOpLog("门店撤销结果：" + opResult.ToString());
                }

                // 禁用
                opResult = this.Gateway.InvokeBillOperation(this.Context, "bas_store", toForbidStores, "Forbid",
                 new Dictionary<string, object>());
                result.MergeResult(opResult);
                this.WriteOpLog("门店禁用结果：" + opResult.ToString());
            }

            var forbidingIds = stores
                .Where(s => Convert.ToString(s["fstorestatus"])
                    .EqualsIgnoreCase(((int)Enu_StoreStatus.Disable).ToString()))
                .Select(s => Convert.ToString(s["id"])).ToList();
            if (forbidingIds.Any())
            {
                opResult = this.ProductAuthService.Forbid(this.Context, this.HtmlForm, forbidingIds);
                result.MergeResult(opResult);
                this.WriteOpLog("门店商品授权清单禁用结果：" + opResult.ToString());
            }

            var unforbidingIds = stores
                .Where(s => Convert.ToString(s["fstorestatus"])
                    .EqualsIgnoreCase(((int)Enu_StoreStatus.Enable).ToString()))
                .Select(s => Convert.ToString(s["id"])).ToList();
            if (unforbidingIds.Any())
            {
                opResult = this.ProductAuthService.Unforbid(this.Context, this.HtmlForm, unforbidingIds);
                result.MergeResult(opResult);
                this.WriteOpLog("门店商品授权清单反禁用结果：" + opResult.ToString());
            }
        }

        /// <summary>
        /// 审核门店
        /// </summary>
        /// <param name="stores"></param>
        /// <param name="result"></param>
        private void AuditStore(IEnumerable<DynamicObject> stores, IOperationResult result)
        {
            /*
             * #35204 子 【慕思二期】门店接口调整生成时的状态, 根据不同情况判断 / 《门店》同步接口增加判断决定是否自动提交审核(简化实施)
             * http://dmp.jienor.com:81/zentao/task-view-35204.html
             *
             * 判断经销商下是否有已审核的门店，如有，则审核此门店
             */

            // 查出【非禁用】且【状态】=启用 的状态
            var agentIds = stores
                .Where(s => !Convert.ToBoolean(s["fforbidstatus"]))
                .Where(s => Convert.ToString(s["fstorestatus"]).EqualsIgnoreCase("1"))
                .Select(s => Convert.ToString(s["fagentid"]))
                .Where(s => !s.IsNullOrEmptyOrWhiteSpace())
                .Distinct().ToList();

            var dbService = this.Container.GetService<IDBService>();

            List<string> hasAuditStoreAgentIds = new List<string>();

            string sql =
                $"select distinct fagentid from t_bas_store where fagentid in ({agentIds.JoinEx(",", true)}) and fstatus='E';";

            using (var reader = this.DBService.ExecuteReader(this.Context, sql))
            {
                while (reader.Read())
                {
                    var fagentid = reader["fagentid"] as string;
                    hasAuditStoreAgentIds.Add(fagentid);
                }
            }

            // 查出非【已审核】且【非禁用】且【状态】=启用 的状态
            var noAuditStores = stores
                .Where(s => !Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E"))
                .Where(s => !Convert.ToBoolean(s["fforbidstatus"]))
                .Where(s => Convert.ToString(s["fstorestatus"]).EqualsIgnoreCase("1"))
                .ToList();
            var needAuditStores = new List<DynamicObject>();

            foreach (var store in noAuditStores)
            {
                string fagentid = Convert.ToString(store["fagentid"]);
                if (hasAuditStoreAgentIds.Contains(fagentid))
                {
                    // 设置为已提交
                    //store["fstatus"] = "D";
                    needAuditStores.Add(store);
                }
            }

            if (needAuditStores.Any())
            {
                var auditResult = this.Gateway.InvokeBillOperation(this.Context, "bas_store", needAuditStores, "submit",
                     this.Option.ToDictionary());
                result.MergeResult(auditResult);

                this.WriteOpLog("门店提交结果：" + auditResult.ToString());
            }
        }

        public override void AfterExecute(AfterExecuteEventArgs e)
        {
            base.AfterExecute(e);

            if (e.DataEntitys.IsNullOrEmpty()) return;

            IOperationResult result = null;

            var dataEntities = e.DataEntitys;
            if (!dataEntities.IsNullOrEmpty())
            {
                foreach (var dataEntity in dataEntities)
                {
                    //改为【提交】状态，便于后续全部执行审核
                    //dataEntity["fstatus"] = "D";
                    if (Convert.ToString(dataEntity["fstatus"]) != "D" && Convert.ToString(dataEntity["fstatus"]) != "E")
                    {
                        result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, new[] { dataEntity }, "submit",
                        this.Option.ToDictionary());
                        this.Result.MergeResult(result);
                    }
                }
            }
        }

        ///// <summary>
        ///// 反写部门
        ///// </summary>
        ///// <param name="stores"></param> 
        //private void RewriteDept(IEnumerable<DynamicObject> stores)
        //{
        //    // 已审核的需要更新部门信息
        //    List<DynamicObject> auditedStores =
        //        stores.Where(s => Convert.ToString(s["fstatus"]).EqualsIgnoreCase("E")).ToList();
        //    if (auditedStores.Any())
        //    {
        //        var groups = auditedStores.GroupBy(s => Convert.ToString(s["fagentid"]));

        //        foreach (var group in groups)
        //        {
        //            var agentId = @group.Key;
        //            if (agentId.IsNullOrEmptyOrWhiteSpace()) continue;

        //            var thisAgentStores = @group.ToList();

        //            var agentCtx = this.Context.CreateAgentDBContext(agentId);
        //            var depts = this.StoreService.GetDeptByStoreNo(agentCtx,
        //                thisAgentStores.Select(s => Convert.ToString(s["fnumber"])));

        //            this.StoreService.RewriteDept(agentCtx, thisAgentStores, depts);
        //        }
        //    }
        //}
    }
}
