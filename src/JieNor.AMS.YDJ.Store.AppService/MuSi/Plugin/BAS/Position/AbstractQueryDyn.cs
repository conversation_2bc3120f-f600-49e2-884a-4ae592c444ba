using System;
using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Position
{  /// <summary>
   /// 属性选配：动态列基础资料字段（模糊查询、弹窗查询）操作抽象基类
   /// </summary>
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理基础资料字段过滤条件解析后事件逻辑
        /// </summary>
        /// <param name="e"></param>
        private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData.Item1?.ToLowerInvariant(); //基础资料字段标识

            var orgid = this.Context.BizOrgId;
            switch (fieldKey)
            {
                case "fmarkingassistant":
                    var sql = $@"/*dialect*/ 
SELECT
	fcustomchannel 
FROM
	t_bas_agent age
	JOIN t_bas_organization org ON org.fid = '{orgid}' 
WHERE
	age.fnumber = org.fnumber";
                    var agents = this.DBService.ExecuteDynamicObject(this.Context, sql);
                    if (agents.Count > 0 && Convert.ToString(agents.First()?["fcustomchannel"]) == "1")
                    {
                        var filter = "fid='0'";
                        e.Result = filter;
                        e.Cancel = true;
                    }
                    break;
            }
        }
    }
}
