using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Const;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.BAS.Agent
{
    /// <summary>
    /// 主经销商配置表修改时：清空相关缓存信息
    /// </summary>
    [InjectService]
    [FormId("bas_mainagentconfig")]
    [OperationNo("save")] 
    public class MainAgentConfigSave : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //清除缓存 
            var pubSubService = this.Container.GetService<IPubSubService>();
            pubSubService.PublishMessage<string>(ConstPubSubChannel.SubOrgRelationChangeChannel, " ");
        }
    }
}
