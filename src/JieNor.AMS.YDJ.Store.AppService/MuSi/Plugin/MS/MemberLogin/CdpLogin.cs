using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Api;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.MS.MemberLogin
{
    /// <summary>
    /// 登录会员CDP系统: 立即登录
    /// </summary>
    [InjectService]
    [FormId("ms_member_login")]
    [OperationNo("cdplogin")]
    public class CdpLogin : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            var resquestFormId = JNConvert.ToStringAndTrim(e.DataEntitys[0]["frequestformid"]);

            var crmdistributorid = JNConvert.ToStringAndTrim(e.DataEntitys[0]["fcrmdistributorid"]);
            if (crmdistributorid.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("经销商名称不能为空！");
            }

            var crmdistributornumber = JNConvert.ToStringAndTrim(e.DataEntitys[0]["fusername"]);
            if (crmdistributornumber.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("经销商编码不能为空！");
            }

            var password = JNConvert.ToStringAndTrim(e.DataEntitys[0]["fpassword"]);
            if (password.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("登录密码不能为空！");
            }

            //Base64编码加密
            byte[] bytesToEncode = System.Text.Encoding.UTF8.GetBytes(password);
            string base64EncodedText = Convert.ToBase64String(bytesToEncode);

            var requestData = new Dictionary<string, string>();
            requestData["username"] = crmdistributornumber;
            requestData["password"] = base64EncodedText;



            if (resquestFormId.IsNullOrEmptyOrWhiteSpace())
            {
                e.DataEntitys[0]["furl"] = "";
                this.Result.SrvData = e.DataEntitys[0]["furl"];
                this.Result.IsSuccess = false;
            }
            else
            {
                //获取跳转Url
                var resp = MusiMenberCDPApi.GetUrl(this.Context, requestData, resquestFormId);

                if (resp["success"].Equals("0"))
                {
                    e.DataEntitys[0]["furl"] = "";
                    throw new BusinessException(resp["message"]);
                }

                e.DataEntitys[0]["furl"] = resp["message"];

                this.Result.SrvData = e.DataEntitys[0]["furl"];
                this.Result.IsSuccess = true;

                //存储密码以及登录时间
                var dTime = DateTime.Now;
                var sqlList = new List<string>();
                sqlList.Add($"update t_ms_crmdistributor set fcdppassword='{base64EncodedText}',flastlogintime='{dTime}' where fid='{crmdistributorid}'");

                var dbServiceEx = this.Container.GetService<IDBServiceEx>();
                dbServiceEx.ExecuteBatch(this.Context, sqlList);

                //该方法会进行权限校验
                //var crmdistributorObj = this.Context.LoadBizDataById("ms_crmdistributor", crmdistributorid);
                //if (crmdistributorObj!=null)
                //{
                //    crmdistributorObj["fcdppassword"] = base64EncodedText;
                //    var result = this.Gateway.InvokeBillOperation(this.Context, "ms_crmdistributor", new[] { crmdistributorObj }, "draft", new Dictionary<string, object>());
                //}
            }

        }
    }
}
