using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.MS.FineBI
{
    /// <summary>
    /// FineBI获取RSAKey(FineBI平台加密后的（系统管理-集成后台单点登录-下的加密密钥和用户名）)
    /// </summary>
    [InjectService]
    [FormId("ydj_finebiview")]
    [OperationNo("getFineBIRSAKey")]
    public class getFineBIRSAKey : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            //string username =this.Context.UserPhone;
            //不开启 ssoToken 超时设置功能用这种
            //string username =this.Context.UserPhone;
            //username 为用户名，issueTime 为当前毫秒时间戳
            //{\"username\": \"1\",\"issueTime\": 1640832102097}
            //开启 ssoToken 超时设置功能用这种，否则会报错
            string username = "{\"username\": \""+ "sysadmin" + "\",\"issueTime\": "+ (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000 + "}";
            //string defaultKey =
            //        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoIFMx34BvNJmMVDl+JaqF4pCjqQMDuQN\r\n" +
            //        "jVrx1ZFnhM7uFl9F3QHNyIXDwVrfJt/JuSPLgrNNbmHyHM/EbTFW9skPgwe5G6CwCtZP0i+7CMIR\r\n" +
            //        "IvteeVSkHpHeXW2dUjmJsQZhqZbXIgCPcvWzZwdiZjvrA36hZGMylv9jyyF9MPUayQf+CzJQpetR\r\n" +
            //        "WzCttxEVw1TXdw3LBclhdToJ6ltCp8H+JTv8q9CQDDAaUO+k8bvnMuxDUoiTVr2tH8AreWLJuoP/\r\n" +
            //        "ly/iGmNovO122aBTok2xjCqneEmLfU+yksHfFt67vgCLdrbkPeRQCAMZolL3ASoYxW3CcO/cLPvZ\r\n" +
            //        "9EjhSQIDAQAB";
            string defaultKey = CommonUtil.GetAppConfig(null, "ms.finebi.rsakey") ?? "";
            RSAHelper obj = new RSAHelper(RSAType.RSA,System.Text.Encoding.UTF8,string.Empty,defaultKey);
            var encrypt=obj.Encrypt(username);

            var paramterSql = $"select fparameter from t_sys_menuitem  with(nolock) where fbillformid='{this.HtmlForm.Id.ToLower()}'";
            var paramter = string.Empty;
            using (var reader = this.Context.ExecuteReader(paramterSql, null))
            {
                while (reader.Read())
                {
                    paramter = reader["fparameter"] as string;
                }
            }
            var reportId = string.Empty;
            foreach (var item in paramter.Split(','))
            {
                var detail = item.Split(':');
                if (detail[0].Replace("\"", "").ToLower() == "reportid")
                {
                    reportId = detail[1].Replace("\"", "");
                }
            }
            var returnUrl = $"http://localhost:37799/webroot/decision/v5/design/report/{reportId}/view?ssoToken={HttpUtility.UrlEncode(encrypt)}";
            this.Result.SrvData = returnUrl;
            //this.Result.SrvData = new Dictionary<string, string> {
            //    { "reportid", reportId },
            //    { "rsaToken", HttpUtility.UrlEncode(encrypt) }
            //};
            this.Result.IsSuccess = true;
        }
    }
}
