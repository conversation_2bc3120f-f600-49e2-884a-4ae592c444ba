using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin
{
    /// <summary>
    /// 删除商品明细行
    /// </summary>
    [InjectService]
    [FormId("*")]
    [OperationNo("deleterow")]
    public class DeleteRow : AbstractOperationServicePlugIn
    {
        protected Dictionary<string, string> FormField = new Dictionary<string, string>() {
         {"ydj_order","fproductid_ref"},
         {"ydj_purchaseorder","fmaterialid_ref"},
         {"stk_postockin","fmaterialid_ref"},
         {"stk_postockreturn","fmaterialid_ref"},
         {"stk_sostockout","fmaterialid_ref"},
         {"stk_sostockreturn","fmaterialid_ref"},
         {"stk_inventorytransfer","fmaterialid_ref"},
         {"stk_otherstockin","fmaterialid_ref"},
         {"stk_otherstockout","fmaterialid_ref"},
         {"stk_inventoryverify","*"}
        };
        /// <summary>
        /// 调用操作事物前触发的事件 
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            this.Option.SetIgnoreOpLogFlag();

            var refManager = this.Container.GetService<LoadReferenceObjectManager>();
            var Ids = new List<string>();
            var details = new List<string>();
            foreach (var row in this.SelectedRows)
            {
                var data = e.DataEntitys.FirstOrDefault(s =>
                            Convert.ToString(s["id"]).EqualsIgnoreCase(row.PkValue));
                if (data == null) continue;

                if (FormField.ContainsKey(this.HtmlForm.Id))
                {
                    //盘点单的特殊处理
                    refManager.Load(this.Context, data.DynamicObjectType, data, true);
                }

                //子明细，不做日志记录
                int elementType = this.HtmlForm.GetEntity(row.EntityKey).ElementType;
                if (elementType == 53)
                    continue;
                var entrys = data[row.EntityKey] as DynamicObjectCollection;
                var entry = entrys?.FirstOrDefault(
                    s => Convert.ToString(s["id"]).EqualsIgnoreCase(row.EntryPkValue));
                if (entry == null) continue;
                //附件明细不包含ftranid，避免删除报错
                if (!entry.DynamicObjectType.Properties.ContainsKey("ftranid")) continue;

               
                if (FormField.ContainsKey(this.HtmlForm.Id)) {
                    if (this.HtmlForm.Id == "stk_inventoryverify")
                    {
                        //盘点单记录明细
                        var matObj = entry["fmaterialid_ref"] as DynamicObject;
                        var attrObj = entry["fattrinfo_ref"] as DynamicObject;
                        var storeHouseObj = entry["fstorehouseid_ref"] as DynamicObject;
                        var storeLocationObj = entry["fstorelocationid_ref"] as DynamicObject;
                        //记录：商品编码、商品名称、辅助属性、仓库、仓位、账存数
                        details.Add($"商品编码：{matObj["fnumber"]}、商品名称：{matObj["fname"]}、辅助属性：{attrObj?["fname"]}、仓库：{storeHouseObj?["fname"]}、仓位：{storeLocationObj?["fname"]}、账存数：{Convert.ToDecimal(entry["fstockqty"]).ToString("0.##")}");
                    }
                    else {
                        var strfield = FormField[this.HtmlForm.Id];
                        var matObj = entry[strfield] as DynamicObject;
                        details.Add($"商品编码：{matObj["fnumber"]}、商品名称：{matObj["fname"]}");
                    }
                }

                Ids.Add(Convert.ToString(entry?["ftranid"]));
            }
            if (Ids.Count() > 0)
            {
                var str = Ids.JoinEx(",", false);
                var detailStr = details.JoinEx("；", false);
                if (!detailStr.IsNullOrEmptyOrWhiteSpace())
                {
                    str += $"；{detailStr}";
                }
                this.Logger.WriteLog(this.Context, new LogEntry
                {
                    BillIds = e.DataEntitys[0]["id"] as string,
                    //BillNos = e.DataEntitys[0]["fnumber"] as string,
                    BillFormId = HtmlForm.Id,
                    OpName = "删除明细行",
                    OpCode = this.OperationNo,
                    Content = "执行了【删除明细行】操作，行内码：{0}".Fmt(str),
                    Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                    Level = Enu_LogLevel.Info.ToString(),
                    LogType = Enu_LogType.RecordType_03
                });
            }
        }
    }
}

