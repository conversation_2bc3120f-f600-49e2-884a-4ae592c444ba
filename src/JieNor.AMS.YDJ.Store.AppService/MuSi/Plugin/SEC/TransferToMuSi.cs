using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.SEC
{
    /// <summary>
    /// 用户/员工/岗位/角色/部门：同步
    /// </summary>
    [InjectService]
    [FormId("sec_user|ydj_staff|ydj_position|sec_role|ydj_dept")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {
                //操作类型
                case "operationtype":
                    {
                        // 根据 是否来源数据库判断 操作类型是新增、还是删除
                        e.Cancel = true;
                        //1 新增，2 更新，3 删除
                        e.Result = "1";

                        //为true表示修改
                        if (dataEntity.DataEntityState.FromDatabase)
                        {
                            var Obj = this.UserContext.LoadBizDataById(this.HtmlForm.Id, Convert.ToString(dataEntity["Id"]));
                            if (Obj.IsNullOrEmpty())
                            {
                                e.Result = "3";
                                break;
                            }
                            else {
                                e.Result = "2";
                                break;
                            }
                            
                        }
                    }
                    break;
                //经销商经营模式。是否直营经销商，类型值描述：10-直营，20-经销
                case "dealertype":
                    e.Cancel = true;

                    var mainOrgIdField = this.HtmlForm.GetField("fmainorgid") != null
                    ? Convert.ToString(e.DataEntity["fmainorgid"])
                    : this.UserContext.Company;

                    //判断是否为二级经销商，如果是二级经销商
                    var agentData = this.UserContext.LoadBizBillHeadDataById("bas_agent", mainOrgIdField, "fmanagemodel");
                    var agentMode = Convert.ToString(agentData?["fmanagemodel"]?? "0");
                    //'0':'经销商','1':'直营'；默认是经销商类型，如果为1则是直营类型
                    if (agentMode.IsNullOrEmptyOrWhiteSpace() || agentMode.EqualsIgnoreCase("0"))
                    {
                        e.Result = "20";
                        break;
                    }
                    else 
                    {
                        e.Result = "10";
                        break;
                    } 
                //
                case "mainwxorgid":
                    {
                        // 根据 当前企业所在经销商去 匹配 《主经销商配置表》，如果是主经销商或者子经销商匹配到，则返回表头的 企业微信主体经销商 字段
                        e.Cancel = true;

                        //获取企业微信主体经销商 
                        var qywxMainAgent = GetQywxMainAgent(e);
                        e.Result = Convert.ToString(qywxMainAgent?["id"]);
                    }
                    break;
                case "mainwxorgnum":
                    {
                        // 根据 当前企业所在经销商去 匹配 《主经销商配置表》，如果是主经销商或者子经销商匹配到，则返回表头的 企业微信主体经销商 字段
                        e.Cancel = true;

                        //获取企业微信主体经销商 
                        var qywxMainAgent = GetQywxMainAgent(e);
                        e.Result = Convert.ToString(qywxMainAgent?["fnumber"]);
                    }
                    break;
                case "outid":
                    //岗位id预置的 sys_preset_position_001需要转换成int，因为对方是以int接收的
                    if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_position"))
                    {
                        break;
                    }
                    var posid= Convert.ToString(dataEntity["Id"]);
                    if (posid.Contains('_')) {
                        e.Result = ConvertId(posid);
                        e.Cancel = true;
                    }
                    break;
                case "positionid":
                case "positionnum":
                    //员工的岗位传过去也要转换岗位id
                    if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_staff"))
                    {
                        break;
                    }
                    posid = Convert.ToString(dataEntity["fpositionid"]); 
                    if (posid.Contains('_'))
                    {
                        e.Result = ConvertId(posid);
                        e.Cancel = true;
                    }
                    break;
                case "dealernum":
                    //【关联门店】对应《门店》表头的【招商经销商】传给中台, 如果该部门没有【关联门店】则默认当前企业对应的经销商的《经销商》表头的【招商经销商】 (如果存在多个时随机给一个就行, 但保证每次给都是同一个就行)
                    if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_dept"))
                    {
                        break;
                    }
                    var dealerObj = GetDealer(dataEntity);
                    e.Result = Convert.ToString(dealerObj?["fnumber"]);
                    e.Cancel = true;
                    break;
                case "dealername":
                    if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_dept"))
                    {
                        break;
                    }
                    dealerObj = GetDealer(dataEntity);
                    e.Result = Convert.ToString(dealerObj?["fname"]);
                    e.Cancel = true;
                    break;
                case "linkuserid":  
                    {
                        if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_staff")) {
                            break;
                        }
                        e.Cancel = true;
                        //当保存员工还未更新关联用户时，才走以下逻辑
                        if (e.Result.IsNullOrEmptyOrWhiteSpace()) 
                        {
                            var userObj = GetUserLink(dataEntity);
                            if (userObj != null) 
                            {
                                e.Result = userObj?["fid"];
                            }
                            else e.Result = "";
                        }
                    }
                    break;
                case "linkusernum":
                    {
                        if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_staff"))
                        {
                            break;
                        }
                        e.Cancel = true;
                        //当保存员工还未更新关联用户时，才走以下逻辑
                        if (e.Result.IsNullOrEmptyOrWhiteSpace())
                        {
                            var userObj = GetUserLink(dataEntity);
                            if (userObj != null)
                            {
                                e.Result = userObj?["fnumber"];
                            }
                            else e.Result = "";
                        }
                    }
                    break;
                case "mainOrgId":
                    e.Cancel = true;
                    var mainOrgId = this.HtmlForm.GetField("fmainorgid") != null
                                    ? Convert.ToString(e.DataEntity["fmainorgid"])
                                    : this.UserContext.Company;

                    //判断是否为二级经销商，如果是二级经销商
                    var agent = this.UserContext.LoadBizDataById("bas_agent", mainOrgId);
                    if (agent!=null&&Convert.ToBoolean(agent["fisreseller"]))
                    {
                        //如果是二级经销商，则应该取上级经销商（一级经销商）去取企业微信主体
                        mainOrgId = Convert.ToString(agent["forgid"]);
                    }
                    e.Result = mainOrgId;
                    break;
                case "mainorgnum":
                    e.Cancel = true;
                    mainOrgId = this.HtmlForm.GetField("fmainorgid") != null
                                    ? Convert.ToString(e.DataEntity["fmainorgid"])
                                    : this.UserContext.Company;

                    agent = this.UserContext.LoadBizDataById("bas_agent", mainOrgId);
                    if (agent!=null&&Convert.ToBoolean(agent["fisreseller"]))
                    {
                        mainOrgId = Convert.ToString(agent["forgid"]);
                    }
                    var TopAgent = this.UserContext.LoadBizDataById("bas_agent", mainOrgId);
                    e.Result =Convert.ToString(TopAgent?["fnumber"]);
                    break;
                //员工是否是二级经销商
                case "issuborg":
                    if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_staff"))
                    {
                        break;
                    }
                    e.Cancel = true;
                    var mainOrgIdStr = this.HtmlForm.GetField("fmainorgid") != null
                        ? Convert.ToString(e.DataEntity["fmainorgid"])
                        : this.UserContext.Company;

                    //判断是否为二级经销商，如果是二级经销商
                    var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", mainOrgIdStr, "fisreseller");
                    var isSecondaryDealer = false;
                    if (agentDy != null && Convert.ToBoolean(Convert.ToInt32(agentDy["fisreseller"])))
                    {
                        isSecondaryDealer = true;
                    }
                    e.Result = isSecondaryDealer;
                    break;
                //员工关联用户状态
                case "linkuserstatus":
                    if (!this.HtmlForm.Id.EqualsIgnoreCase("ydj_staff"))
                    {
                        break;
                    }
                    e.Cancel = true;
                    var userDy = GetUserLink(dataEntity);
                    if (userDy != null)
                    {
                        //用户禁用状态
                        var userForbidstatus = Convert.ToBoolean(Convert.ToInt32(userDy["fforbidstatus"]));
                        if (userForbidstatus)
                        {
                            e.Result = "Y";
                        }
                        else
                        {
                            e.Result = "N";
                        }
                    }
                    else
                    {
                        e.Result = "Y";
                    }
                    break;
            }
        }

        private string ConvertId(string posid) 
        {
            byte[] array = System.Text.Encoding.Default.GetBytes(posid); //string转换的字母
            var newstr = string.Empty;
            foreach (var i in array)
            {
                newstr += i.ToString();
            }
            newstr = newstr.Length>18 ? newstr.Substring(newstr.Length - 18, 18) : newstr;
            return newstr;
        }

        /// <summary>
        /// 获取部门对应门店记录的 招商经销商
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject GetDealer(DynamicObject dataEntities) 
        {
            var deptId = Convert.ToString(dataEntities["Id"]);
            var fmainorgid = Convert.ToString(dataEntities["fmainorgid"]);
            if (deptId == null)
            {
                return null;
            }
            var store = Convert.ToString(dataEntities?["fstore"]);
            DynamicObject DealerObj = null;
            // 如果该部门没有【关联门店】则默认当前企业对应的经销商的《经销商》表头的【招商经销商】 (如果存在多个时随机给一个就行, 但保证每次给都是同一个就行)
            if (store.IsNullOrEmptyOrWhiteSpace())
            {
                return GetDealerByAgent(fmainorgid);
            }
            else
            {
                var strSql = $@"SELECT top 1 t_ms_crmdistributor.fid,t_ms_crmdistributor.fnumber,t_ms_crmdistributor.fname FROM t_bd_department dept 
                        INNER JOIN dbo.T_BAS_STORE store ON store.fid = dept.fstore
                        INNER JOIN t_ms_crmdistributor ON t_ms_crmdistributor.fid = store.fcrmdistributorid
                        WHERE dept.fid = '{deptId}' ";

                var dbService = this.UserContext.Container.GetService<IDBService>();
                DealerObj = dbService.ExecuteDynamicObject(this.UserContext, strSql).FirstOrDefault();
                //如果门店的招商经销商没有值  再走经销商 的逻辑
                if (Convert.ToString(DealerObj?["fname"]).IsNullOrEmptyOrWhiteSpace()) {
                    return GetDealerByAgent(fmainorgid);
                }
            }

            return DealerObj;
        }

        private DynamicObject GetDealerByAgent(string fmainorgid)
        {
            DynamicObject DealerObj = null;
            GetResultBrandData re = new GetResultBrandData();
            var crmdistributorids = re.GetBaseDataNameById(this.UserContext, "bas_agent", fmainorgid, "fcrmdistributorid");
            var crmdistributoridLst = crmdistributorids.Split(',');
            if (crmdistributoridLst.Length > 0)
            {
                var crmdistributorid = Convert.ToString(crmdistributoridLst[0]);
                DealerObj = this.UserContext.LoadBizBillHeadDataById("ms_crmdistributor", crmdistributorid, "fid,fnumber,fname");
            }
            return DealerObj;
        }

        /// <summary>
        /// 查询用户关联
        /// </summary>
        /// <param name="dataEntities"></param>
        private DynamicObject GetUserLink(DynamicObject dataEntities)
        {
            var mainOrgId = this.HtmlForm.GetField("fmainorgid") != null
            ? Convert.ToString(dataEntities["fmainorgid"])
            : this.UserContext.Company;
            var strSql = "";
            var staffIds =Convert.ToString(dataEntities["Id"]);

            if (staffIds == null)
            {
                return null;
            }

            strSql = $@"SELECT top 1 fid,fnumber,fname,fforbidstatus FROM t_sec_user WHERE fphone = '{Convert.ToString(dataEntities["fphone"])}' AND fphone<> '' 
                        AND fmainorgid='{mainOrgId}' ";

            var dbService = this.UserContext.Container.GetService<IDBService>();
            var userlink= dbService.ExecuteDynamicObject(this.UserContext, strSql).FirstOrDefault();

            return userlink;
        }


        private DynamicObject GetQywxMainAgent(BeforeFieldMappingEventArgs e)
        {
            var mainOrgId = this.HtmlForm.GetField("fmainorgid") != null
                ? Convert.ToString(e.DataEntity["fmainorgid"])
                : this.UserContext.Company;

            //判断是否为二级经销商，如果是二级经销商
            var agent = this.UserContext.LoadBizDataById("bas_agent", mainOrgId);
            if (agent!=null&&Convert.ToBoolean(agent["fisreseller"])) 
            {
                //如果是二级经销商，则应该取上级经销商（一级经销商）去取企业微信主体
                mainOrgId = Convert.ToString(agent["forgid"]); 
            }

            DynamicObject qywxMainAgent;

            if (e.Option != null)
            {
                if (!e.Option.TryGetVariableValue("QywxMainAgent",
                    out Dictionary<string, DynamicObject> dicQywxMainAgent))
                {
                    dicQywxMainAgent = new Dictionary<string, DynamicObject>();
                    e.Option.SetVariableValue("QywxMainAgent", dicQywxMainAgent);
                }

                if (!dicQywxMainAgent.TryGetValue(mainOrgId, out qywxMainAgent))
                {
                    qywxMainAgent = GetMainQYWXAgent(mainOrgId);
                    if (qywxMainAgent == null)
                    {
                        qywxMainAgent = this.UserContext.LoadBizDataById("bas_agent", mainOrgId);
                    }
                    dicQywxMainAgent[mainOrgId] = qywxMainAgent;
                }
            }
            else
            {
                qywxMainAgent = GetMainQYWXAgent(mainOrgId);
                if (qywxMainAgent == null)
                {
                    qywxMainAgent = this.UserContext.LoadBizDataById("bas_agent", mainOrgId);
                }
            }

            return qywxMainAgent;
        }

        /// <summary>
        /// 根据经销商id  匹配 《主经销商配置表》，如果是主经销商或者子经销商匹配到，则返回其配置的 企业微信主体经销商
        /// </summary>
        /// <param name="agentId"></param>
        /// <returns></returns>
        private DynamicObject GetMainQYWXAgent(string agentId)
        {
            DynamicObject qywxMainAgent;
            string sql = $@"select distinct fqywxmainagentid as 'id',agent.fnumber as 'fnumber' from t_bas_mac 
                            inner join t_bas_macentry mx on mx.fid = t_bas_mac.fid
                            inner join T_BAS_AGENT agent on agent.fid = t_bas_mac.fqywxmainagentid
                            where (mx.fsubagentid = '{agentId}' or fmainagentid = '{agentId}') and t_bas_mac.fforbidstatus='0'";
            var dbService = this.UserContext.Container.GetService<IDBService>();
            qywxMainAgent = dbService.ExecuteDynamicObject(this.UserContext, sql).FirstOrDefault();
            return qywxMainAgent;
        }
    }
}
