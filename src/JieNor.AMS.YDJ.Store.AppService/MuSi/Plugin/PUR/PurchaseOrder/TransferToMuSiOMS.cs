using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.PUR.PurchaseOrder
{
    /// <summary>
    /// 采购订单：同步OMS
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("synctomusioms")]
    [ThirdSystemId("musi")]
    public class TransferToMuSiOMS : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {
                // 合同附件集合
                case "ordfile":
                    {
                        e.Cancel = true;
                        e.Result = new List<OrderImgModel>();
                        if (!dataEntity["forderimage"].IsNullOrEmptyOrWhiteSpace())
                        {
                            List<OrderImgModel> result = new List<OrderImgModel>();
                            var imgIds = Convert.ToString(dataEntity["forderimage"]).Split(',');
                            var imgTxts = Convert.ToString(dataEntity["forderimage_txt"]).Split(',');
                            for (int i = 0; i < imgIds.Length; i++)
                            {
                                if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                    result.Add(new OrderImgModel
                                    {
                                        code = imgIds[i],
                                        name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                        fileAddr = imgIds[i]?.GetSignedFileUrl()
                                    });
                                }
                            }
                            e.Result = result;
                        }
                    }
                    break;
                //焕新标记传OMS Y/N
                case "ordflag":
                    e.Cancel = true;
                    var frenewalflag = Convert.ToBoolean(dataEntity["frenewalflag"]);
                    e.Result = frenewalflag ? "Y" : "N";
                    break;
                case "salordernum":
                    //如果二级合同号不为空，则传二级合同号
                    var fsecondorderno = Convert.ToString(dataEntity["fsecondorderno"]);
                    if (!fsecondorderno.IsNullOrEmptyOrWhiteSpace())
                    {
                        e.Cancel = true;
                        e.Result = fsecondorderno;
                    }
                    break;
                case "renovationcategory":
                    {
                        var customcategoryObj = dataEntity["fcustomcategory_ref"] as DynamicObject;
                        var customcategorycode = Convert.ToString(customcategoryObj?["Id"]);
                        if (!customcategorycode.IsNullOrEmptyOrWhiteSpace()) 
                        {
                            e.Cancel = true;
                            e.Result = customcategorycode;
                        } 
                    }
                    break;
                case "cusadd":
                    {
                        string provinceName = Convert.ToString((dataEntity?["fprovince_ref"] as DynamicObject)?["fenumitem"]);
                        string cityName = Convert.ToString((dataEntity?["fcity_ref"] as DynamicObject)?["fenumitem"]);
                        string regionName = Convert.ToString((dataEntity?["fregion_ref"] as DynamicObject)?["fenumitem"]);
                        string address = Convert.ToString(dataEntity?["faddress"]);

                        e.Cancel = true;
                        e.Result = $"{provinceName}{cityName}{regionName}{address}";
                    }
                    break;
                // 配件父行号
                case "partsparnum":
                    {
                        // 根据《采购订单》单据体.商品明细行的【配件主商品】+【配件组合号】来判断, 如果商品行有【配件组合号】且【配件主商品】为否的, 都需要赋值套件父行号, 赋值逻辑为对应的【配件组合号】 且 有勾选上【配件主商品】的行号, 即为配件父行号
                        e.Cancel = true;
                        e.Result = "";

                        bool isCombMain = Convert.ToBoolean(dataEntity["fiscombmain"]);
                        string partsCombNumber = Convert.ToString(dataEntity["fpartscombnumber"]);

                        if (!partsCombNumber.IsNullOrEmptyOrWhiteSpace() && !isCombMain)
                        {
                            var entries = (DynamicObjectCollection)bizEntity["fentity"];

                            foreach (var entry in entries)
                            {
                                if (Convert.ToString(entry["fpartscombnumber"]).EqualsIgnoreCase(partsCombNumber) &&
                                    Convert.ToBoolean(entry["fiscombmain"]))
                                {
                                    e.Result = Convert.ToString(entry["fseq"]);
                                    break;
                                }
                            }
                        }
                    }
                    break;
                // 套件父行号
                case "suitparnum":
                    {
                        // 根据《采购订单》单据体.商品明细行的【是否套件】+【套件组合号】来判断, 如果商品行有【套件组合号】且【是否套件】为否的, 都需要赋值套件父行号, 赋值逻辑为对应的【套件组合号】 且 有勾选上【是否套件】的行号, 即为套件父行号
                        e.Cancel = true;
                        e.Result = "";

                        var productObj = dataEntity["fmaterialid_ref"] as DynamicObject;
                        if (productObj == null)
                            dataEntity["fmaterialid_ref"] = productObj = this.UserContext.LoadBizDataById("ydj_product", Convert.ToString(dataEntity["fmaterialid"]));

                        bool isSuitFlag = Convert.ToBoolean(productObj?["fsuiteflag"]);
                        string suitCombNumber = Convert.ToString(dataEntity["fsuitcombnumber"]);

                        if (!suitCombNumber.IsNullOrEmptyOrWhiteSpace() && !isSuitFlag)
                        {
                            var entries = (DynamicObjectCollection)bizEntity["fentity"];

                            foreach (var entry in entries)
                            {
                                if (Convert.ToString(entry["fsuitcombnumber"]).EqualsIgnoreCase(suitCombNumber) &&
                                    Convert.ToBoolean((entry["fmaterialid_ref"] as DynamicObject)?["fsuiteflag"]))
                                {
                                    e.Result = Convert.ToString(entry["fseq"]);
                                    break;
                                }
                            }
                        }
                    }
                    break;
                // 沙发父行号
                case "sofaminnum":
                    {
                        // 【沙发组合号】一样的行, 对应找到最小的行序号
                        e.Cancel = true;
                        e.Result = "";

                        string sofaCombNumber = Convert.ToString(dataEntity["fsofacombnumber"]);

                        if (!sofaCombNumber.IsNullOrEmptyOrWhiteSpace())
                        {
                            var entries = (DynamicObjectCollection)bizEntity["fentity"];
                            var minSeq = entries
                                .Where(x => Convert.ToString(x["fsofacombnumber"]) == sofaCombNumber)
                                .Select(x => Convert.ToInt32(x["fseq"]))
                                .Min();

                            e.Result = Convert.ToString(minSeq);
                        }
                    }
                    break;
                case "thflag"://直发标记

                    e.Cancel = true;
                    e.Result = "";

                    e.Result = Convert.ToString(dataEntity["fsendtarget"]);
                    break;
                case "urgentflag"://加急标识

                    e.Cancel = true;
                    e.Result = "";

                    e.Result = Convert.ToInt32(dataEntity["furgent"]);
                    break;
                case "deleteflag"://作废标识

                    e.Cancel = true;
                    e.Result = "";

                    e.Result = Convert.ToInt32(dataEntity["fcancelstatus"]);
                    break;
                case "attributecode":
                    e.Cancel = true;
                    e.Result = "";
                    if (!string.IsNullOrWhiteSpace(Convert.ToString(dataEntity["forderattr"])))
                    {
                        e.Result = Convert.ToInt32(dataEntity["forderattr"]);
                    }
                    break;
            }
        }

        /// <summary>
        /// 打包前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforePackSourceBill(BeforePackSourceBillEventArgs e)
        {
            base.BeforePackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            // 获取合同附件
            var purchaseorders = e.DataEntitys;

            //取消接口排序逻辑
            //foreach (var purchase in purchaseorders)
            //{
            //    SortBysuit(purchase["fentity"] as DynamicObjectCollection);
            //}
        }

        /// <summary>
        /// 向第三方系统发送请求前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeSendEvent(BeforeSendEventArgs e)
        {
            base.BeforeSendEvent(e);

            if (e.DataEntitys == null || e.RuquestData == null) return;

            var RuquestData = e.RuquestData;
            var datas = RuquestData["data"] as List<Dictionary<string, object>>;
            if (datas == null) return;

            foreach (var data in datas)
            {
                (data as Dictionary<string, object>).TryGetValue("ordPchInt", out object ordPchInt);
                if (ordPchInt == null) continue;

                (ordPchInt as Dictionary<string, object>).TryGetValue("ordItem", out object ordItemLst);
                if (ordItemLst == null) continue;

                var ordItem = ordItemLst as List<Dictionary<string, object>>;
                if (ordItem == null) continue;

                foreach (var entry in ordItem)
                {
                    if (entry.ContainsKey("processReq"))
                    {
                        entry.TryGetValue("processReq", out object processReq);
                        var str = Convert.ToString(processReq);
                        if (!str.IsNullOrEmptyOrWhiteSpace())
                        {
                            //接口传出前 将加工要求参数的 逗号替换为分号给中台。
                            entry["processReq"] = str.Replace(',', ';');
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 根据套件商品做排序
        /// </summary>
        /// <param name="entry"></param>
        private void SortBysuit(DynamicObjectCollection fentry)
        {
            if (!fentry.IsNullOrEmpty())
            {
                var entrys = fentry.OrderByDescending(f => Convert.ToInt32((f["fmaterialid_ref"] as DynamicObject)?["fsuiteflag"])).OrderBy(g => Convert.ToString(g["fsuitcombnumber"])).ToList();
                fentry.Clear();
                for (int i = 0; i < entrys.Count; i++)
                {
                    var entry = entrys[i] as DynamicObject;
                    //传过去的行也做排序
                    entry["fseq"] = i + 1;
                    fentry.Add(entry);
                }
            }
        }
        /// <summary>
        /// 中台合同附件模型
        /// </summary>
        public class OrderImgModel
        {
            public string code { get; set; }
            public string name { get; set; }
            public string fileAddr { get; set; }
        }
    }
}
