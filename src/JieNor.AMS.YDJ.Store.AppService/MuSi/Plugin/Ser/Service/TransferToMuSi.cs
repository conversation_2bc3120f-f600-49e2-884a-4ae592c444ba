using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.Ser.Service
{
    /// <summary>
    /// 服务单：同步
    /// </summary>
    [InjectService]
    [FormId("ydj_service")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {
                //经销商编码
                case "salesnum":
                    {
                        e.Cancel = true;
                        e.Result = this.UserContext.GetAllCompanys().Values.FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(UserContext.Company))?.CompanyNumber;
                    }
                    break;
                // 作废状态
                case "delstatus":
                    e.Cancel = true;
                    e.Result = !dataEntity["fcancelstatus"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(dataEntity["fcancelstatus"]);
                    break;
                // 签字确认单集合
                case "signconfirmimg":
                    {
                        e.Cancel = true;
                        e.Result = new List<PbImgModel>();
                        if (!dataEntity["fsignconfirmimg"].IsNullOrEmptyOrWhiteSpace())
                        {
                            List<PbImgModel> result = new List<PbImgModel>();
                            var imgIds = Convert.ToString(dataEntity["fsignconfirmimg"]).Split(',');
                            var imgTxts = Convert.ToString(dataEntity["fsignconfirmimg_txt"]).Split(',');
                            for (int i = 0; i < imgIds.Length; i++)
                            {
                                if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                    result.Add(new PbImgModel
                                    {
                                        id = imgIds[i],
                                        name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                        url = imgIds[i]?.GetSignedFileUrl()
                                    });
                                }
                            }
                            e.Result = result;
                        }
                    }
                    break;
                // 现场图片图集合
                case "doneimage":
                    {
                        e.Cancel = true;
                        e.Result = new List<PbImgModel>();
                        if (!dataEntity["fdoneimage"].IsNullOrEmptyOrWhiteSpace())
                        {
                            List<PbImgModel> result = new List<PbImgModel>();
                            var imgIds = Convert.ToString(dataEntity["fdoneimage"]).Split(',');
                            var imgTxts = Convert.ToString(dataEntity["fdoneimage_txt"]).Split(',');
                            for (int i = 0; i < imgIds.Length; i++)
                            {
                                if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                    result.Add(new PbImgModel
                                    {
                                        id = imgIds[i],
                                        name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                        url = imgIds[i]?.GetSignedFileUrl()
                                    });
                                }
                            }
                            e.Result = result;
                        }
                    }
                    break;
                // 附件图片图集合
                case "image":
                    {
                        e.Cancel = true;
                        e.Result = new List<PbImgModel>();
                        if (!dataEntity["fimage"].IsNullOrEmptyOrWhiteSpace())
                        {
                            List<PbImgModel> result = new List<PbImgModel>();
                            var imgIds = Convert.ToString(dataEntity["fimage"]).Split(',');
                            var imgTxts = Convert.ToString(dataEntity["fimage_txt"]).Split(',');
                            for (int i = 0; i < imgIds.Length; i++)
                            {
                                if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                    result.Add(new PbImgModel
                                    {
                                        id = imgIds[i],
                                        name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                        url = imgIds[i]?.GetSignedFileUrl()
                                    });
                                }
                            }
                            e.Result = result;
                        }
                    }
                    break;
                // 问题图片名称集合
                case "questionimage":
                    {
                        e.Cancel = true;
                        e.Result = new List<PbImgModel>();
                        if (!dataEntity["fquestionimage"].IsNullOrEmptyOrWhiteSpace())
                        {
                            List<PbImgModel> result = new List<PbImgModel>();
                            var imgIds = Convert.ToString(dataEntity["fquestionimage"]).Split(',');
                            var imgTxts = Convert.ToString(dataEntity["fquestionimage_txt"]).Split(',');
                            for (int i = 0; i < imgIds.Length; i++)
                            {
                                if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                    result.Add(new PbImgModel
                                    {
                                        id = imgIds[i],
                                        name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                        url = imgIds[i]?.GetSignedFileUrl()
                                    });
                                }
                            }
                            e.Result = result;
                        }
                    }
                    break;
                //创建人电话
                case "crtphone":
                    if (Convert.ToString(dataEntity["fsourcecancl"]) == "0")
                    {
                        e.Cancel = true;
                        e.Result = dataEntity["fzbcreatephone"];
                    }
                    break;
                //创建人
                case "crtname":
                    if (Convert.ToString(dataEntity["fsourcecancl"]) == "0")
                    {
                        e.Cancel = true;
                        e.Result = dataEntity["fzbcreatename"];
                    }
                    break;
                //创建时间
                case "crtdate":
                    if (Convert.ToString(dataEntity["fsourcecancl"]) == "0")
                    {
                        e.Cancel = true;
                        e.Result = Convert.ToDateTime(dataEntity["fzbcreatedate"]).ToString("yyyy-MM-dd HH:mm:ss");
                    }
                    break;
                //客户电话
                case "customerphone":
                    var data = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_customer", Convert.ToString(dataEntity["fcustomerid"]));
                    var concatEntry = (data?["fcuscontacttry"] as DynamicObjectCollection);
                    if (concatEntry != null && concatEntry.Any())
                    {
                        var defaultInfo = concatEntry.FirstOrDefault(x => Convert.ToBoolean(x["fisdefault"]));
                        if (defaultInfo == null)
                        {
                            defaultInfo = concatEntry.FirstOrDefault();
                        }
                        e.Cancel = true;
                        e.Result = defaultInfo?["fphone"];
                    }
                    else
                    {
                        e.Cancel = true;
                        e.Result = data?["fphone"];
                    }
                    break;
                case "salesmanid"://销售员Id
                    {
                        e.Cancel = true;
                        e.Result = "";
                        var csutomerObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_customer", Convert.ToString(dataEntity["fcustomerid"]));
                        var dutyentrys = csutomerObj?["fdutyentry"] as DynamicObjectCollection;
                        if (dutyentrys != null && dutyentrys.Any())
                        {
                            var dytyItem = dutyentrys.OrderByDescending(a => a["fjointime"]).FirstOrDefault();
                            var staffObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_staff", Convert.ToString(dytyItem["fdutyid"]));
                            if (staffObj!=null)
                            {
                                e.Result = Convert.ToString(staffObj["Id"]);
                            }
                        }
                    }
                    break;
                case "salesman"://销售员
                    {
                        e.Cancel = true;
                        e.Result = "";
                        var csutomerObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_customer", Convert.ToString(dataEntity["fcustomerid"]));
                        var dutyentrys = csutomerObj?["fdutyentry"] as DynamicObjectCollection;
                        if (dutyentrys != null && dutyentrys.Any())
                        {
                            var dytyItem = dutyentrys.OrderByDescending(a => a["fjointime"]).FirstOrDefault();
                            var staffObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_staff", Convert.ToString(dytyItem["fdutyid"]));
                            if (staffObj != null)
                            {
                                e.Result = Convert.ToString(staffObj["fname"]);
                            }
                        }
                    }
                    break;
                case "salesmanphone"://销售员电话
                    {
                        e.Cancel = true;
                        e.Result = "";
                        var csutomerObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_customer", Convert.ToString(dataEntity["fcustomerid"]));
                        var dutyentrys = csutomerObj?["fdutyentry"] as DynamicObjectCollection;
                        if (dutyentrys != null && dutyentrys.Any())
                        {
                            var dytyItem = dutyentrys.OrderByDescending(a => a["fjointime"]).FirstOrDefault();
                            var staffObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_staff", Convert.ToString(dytyItem["fdutyid"]));
                            if (staffObj != null)
                            {
                                e.Result = Convert.ToString(staffObj["fphone"]);
                            }
                        }
                    }
                    break;
                case "storenum"://门店编码
                    {
                        e.Cancel = true;
                        e.Result = "";
                        var csutomerObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_customer", Convert.ToString(dataEntity["fcustomerid"]));
                        var dutyentrys = csutomerObj?["fdutyentry"] as DynamicObjectCollection;
                        if (dutyentrys != null && dutyentrys.Any())
                        {
                            var dytyItem = dutyentrys.OrderByDescending(a => a["fjointime"]).FirstOrDefault();
                            var deptObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_dept", Convert.ToString(dytyItem["fdeptid"]));
                            if (deptObj != null)
                            {
                                var storeObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "bas_store", Convert.ToString(deptObj["fstore"]));
                                if (storeObj!=null)
                                {
                                    e.Result = Convert.ToString(storeObj["fnumber"]);
                                }
                            }
                        }
                    }
                    break;
                case "storename"://门店名称
                    {
                        e.Cancel = true;
                        e.Result = "";
                        var csutomerObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_customer", Convert.ToString(dataEntity["fcustomerid"]));
                        var dutyentrys = csutomerObj?["fdutyentry"] as DynamicObjectCollection;
                        if (dutyentrys != null && dutyentrys.Any())
                        {
                            var dytyItem = dutyentrys.OrderByDescending(a => a["fjointime"]).FirstOrDefault();
                            var deptObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "ydj_dept", Convert.ToString(dytyItem["fdeptid"]));
                            if (deptObj != null)
                            {
                                var storeObj = GetReferenceDataEntity(e.ReferenceDataEntityMaps, "bas_store", Convert.ToString(deptObj["fstore"]));
                                if (storeObj != null)
                                {
                                    e.Result = Convert.ToString(storeObj["fname"]);
                                }
                            }
                        }
                    }
                    break;
                case "deleteflag":
                    e.Cancel = true;
                    e.Result = "0";

                    var SimpleData = ((JieNor.Framework.DataTransferObject.DynamicDTOWrapper)UserContext.CurrentRequestObject).SimpleData;

                    if (SimpleData.Count>0)
                    {
                        var operation = string.Empty;

                        if (SimpleData.ContainsKey("operationno"))
                            operation = Convert.ToString(SimpleData["operationno"]).Trim();

                        if (operation.Equals("delete"))
                        {
                            e.Result = "1";
                        }
                    }
                    

                    break;
            }
        }

        /// <summary>
        /// 打包前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforePackSourceBill(BeforePackSourceBillEventArgs e)
        {
            base.BeforePackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            //为空行删除掉，不传给中台
            foreach (var item in e.DataEntitys)
            {

                var confentry = (item["fhqhandleentry"] as DynamicObjectCollection);
                for (int i = 0; i < confentry.Count; i++)
                {
                    if (confentry[i]["fbillno_e"].IsNullOrEmpty() || confentry[i]["fmaterialid_e"].IsNullOrEmpty())
                    {
                        confentry.Remove(confentry[i]);
                        i--;
                    }
                }

                var fentry = (item["fserviceentry"] as DynamicObjectCollection);
                for (int i = 0; i < fentry.Count; i++)
                {
                    if (fentry[i]["fseritemid"].IsNullOrEmpty())
                    {
                        fentry.Remove(fentry[i]);
                        i--;
                    }
                }
                var profentry = (item["fproductentry"] as DynamicObjectCollection);
                for (int i = 0; i < profentry.Count; i++)
                {
                    if (profentry[i]["fmaterialid"].IsNullOrEmpty())
                    {
                        profentry.Remove(profentry[i]);
                        i--;
                    }
                }
            }
        }

        ///// <summary>
        ///// 根据状态码获取状态名称
        ///// </summary>
        ///// <param name="status"></param>
        ///// <returns></returns>
        //private string GetStatusName(string status)
        //{
        //    string statusName = string.Empty;
        //    switch (status.ToUpper())
        //    {
        //        case "A":
        //            statusName = "关联暂存";
        //            break;
        //        case "B":
        //            statusName = "创建";
        //            break;
        //        case "C":
        //            statusName = "重新审核";
        //            break;
        //        case "D":
        //            statusName = "已提交";
        //            break;
        //        case "E":
        //            statusName = "已审核";
        //            break;
        //        default:
        //            break;
        //    }
        //    return statusName;
        //}
    }
    /// <summary>
    /// 中台图片公用模型
    /// </summary>
    public class PbImgModel
    {
        public string id { get; set; }
        public string name { get; set; }
        public string url { get; set; }
    }
}
