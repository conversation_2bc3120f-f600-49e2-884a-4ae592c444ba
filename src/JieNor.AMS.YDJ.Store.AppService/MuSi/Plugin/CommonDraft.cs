using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Validation;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin
{
    [InjectService]
    [FormId("*")]
    [OperationNo("save")]
    public class CommonDraft : AbstractOperationServicePlugIn, IReceivedPubMessage
    {
        static ConcurrentDictionary<string, string> _cacheRelation = new ConcurrentDictionary<string, string>(StringComparer .OrdinalIgnoreCase);
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            var agent = this.Context.Company;
            var MainagentName =  GetMaingAgent(this.Context, agent);

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!MainagentName.IsNullOrEmptyOrWhiteSpace()) {
                    return false;
                }
                return true;
            }).WithMessage("对不起，当前组织为子经销商，请切换到【{0}】组织下操作！", (billObj, propObj) => MainagentName));
        }

        public string  GetMaingAgent(UserContext userCtx, string agentId)
        {
            if(_cacheRelation.Count ==0)
            {
                var sql = @"select distinct me.fsubagentid, ag.fname
                        from t_bas_mac m 
                        inner join t_bas_macentry me on m.fid=me.fid
                        inner join t_bas_agent ag on ag.fid = m.fmainagentid
						where m.fforbidstatus='0' ";

                var datas = this.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql) ;
                foreach (var item in datas)
                {
                    _cacheRelation[item["fsubagentid"].ToString()] = item["fname"].ToString();
                }
            }

            if(_cacheRelation.ContainsKey (agentId ))
            {
                return _cacheRelation[agentId];
            }

            return ""; 
        }



        public string ChannelName
        {
            get { return ConstPubSubChannel.SubOrgRelationChangeChannel; }
        }

        public void OnReceivedMessage(string msg)
        {
            _cacheRelation.Clear();
        }


    }
}
