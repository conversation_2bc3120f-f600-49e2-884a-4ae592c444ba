using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.AFT.Vist
{
    /// <summary>
    /// 下推/修改回访单
    /// </summary>
    [InjectService]
    [FormId("ydj_vist")]
    [OperationNo("MSSaveSync")]
    public class MSSaveSync : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "BeforeConvertBaseData":
                    this.BeforeConvertBaseData(e);
                    break;
                case "AfterPackSourceBill":
                    this.AfterPackSourceBill(e);
                    break;
                case "BeforeSaveSourceBill":
                    this.BeforeSaveSourceBill(e);
                    break;
                case "SourceBillFieldMapping":
                    this.SourceBillFieldMapping(e);
                    break;
                case "BeforeCreateBaseData":
                    this.BeforeCreateBaseData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// //基础资料字段值转换前事件：可指定当前需要转换的基础资料字段
        /// </summary>
        /// <param name="e"></param>
        private void BeforeConvertBaseData(OnCustomServiceEventArgs e)
        {
        }

        /// <summary>
        /// 来源单据打包后事件：可对打包后的数据包进行处理或者直接覆盖整个数据包都是可以的
        /// </summary>
        /// <param name="e"></param>
        private void AfterPackSourceBill(OnCustomServiceEventArgs e)
        {

        }

        /// <summary>
        /// 创建基础资料前事件：可更改哪些基础资料不可创建
        /// </summary>
        /// <param name="e"></param>
        private void BeforeCreateBaseData(OnCustomServiceEventArgs e)
        {
            e.Cancel = true;
            e.Result = new List<string>
            {

            };
        }

        /// <summary>
        /// 来源单据保存前事件：可对当前要保存的数据包做处理
        /// </summary>
        /// <param name="e"></param>
        private void BeforeSaveSourceBill(OnCustomServiceEventArgs e)
        {
            var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            if (dataEntitys == null || dataEntitys.Count < 1) return;
            var billTypeService = this.Context.Container.GetService<IBillTypeService>();
            var billTypeId = billTypeService.GetDefaultBillTypeId(this.Context, this.HtmlForm);
            billTypeId = billTypeId.IsNullOrEmptyOrWhiteSpace() ? "vist_type_01" : billTypeId;
            var enumList = GetEnumAllList();List<DynamicObject> allProduct = null;
            List<string> proIds = new List<string>();
            List<DynamicObject> allOrder = null;
            List<string> orderIds = new List<string>();
            StringBuilder sqlWhere = null;
            List<SqlParam> parm = null;
            foreach (var item in dataEntitys)
            {
                var productentry = item["fproductentry"] as DynamicObjectCollection;
                if (productentry != null && productentry.Any())
                {
                    proIds.AddRange(productentry.Where(x => !x["fmaterialid"].IsNullOrEmptyOrWhiteSpace()).Select(x => x["fmaterialid"].ToString()));
                }
                if (!item["forderno"].IsNullOrEmptyOrWhiteSpace())
                {
                    orderIds.Add(item["forderno"].ToString());
                }
            }
            if (proIds != null && proIds.Any())
            {
                proIds.Distinct();
                sqlWhere = new StringBuilder();
                parm = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", proIds.Select((x, i) => $"@fid{i}")));
                parm.AddRange(proIds.Select((x, i) => new SqlParam($"@fid{i}", System.Data.DbType.String, x)));
                allProduct = this.Context.ExecuteDynamicObject($"select fid id,funitid from t_bd_material with(nolock) where fid in ({sqlWhere})", parm)?.ToList();
            }
            if (orderIds != null && orderIds.Any())
            {
                orderIds.Distinct();
                sqlWhere = new StringBuilder();
                parm = new List<SqlParam>();
                sqlWhere.Append(string.Join(",", orderIds.Select((x, i) => $"@fid{i}")));
                parm.AddRange(orderIds.Select((x, i) => new SqlParam($"@fid{i}", System.Data.DbType.String, x)));
                allOrder = this.Context.ExecuteDynamicObject($"select fid id,fdeptid from t_ydj_order with(nolock) where fid in ({sqlWhere})", parm)?.ToList();
            }

            foreach (var dataEntity in dataEntitys)
            {
                dataEntity["fbilltype"] = billTypeId;
                var fservicetypeid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Equals(dataEntity["fservicetype"]) && x["fid"].Equals("a299d3fa3c4d49e4b72049a289e8b455")).OrderByDescending(x => x["fispreset"]).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                dataEntity["fservicetype"] = fservicetypeid.IsNullOrEmptyOrWhiteSpace() ? "" : fservicetypeid;

                var fvistmodeid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Equals(dataEntity["fvistmode"]) && x["fid"].Equals("b058b5acafed4091a0bf2e178330b68f")).OrderByDescending(x => x["fispreset"]).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                dataEntity["fvistmode"] = fvistmodeid.IsNullOrEmptyOrWhiteSpace() ? "" : fvistmodeid;

                var fprovince = Convert.ToString(dataEntity["fprovince"]);
                if (!fprovince.IsNumeric())
                {
                    var fprovinceid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Contains(fprovince) && x["fid"].Equals("4ce1b6b47093415585878493ec1ad98b")).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                    dataEntity["fprovince"] = fprovinceid;
                    if (!fprovinceid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var fcity = Convert.ToString(dataEntity["fcity"]);
                        var fcityid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Contains(fcity) && x["fid"].Equals("6916ea2d44f24379a377cca5362bd7cf")).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        //if (!fcity.IsNullOrEmptyOrWhiteSpace() && fcityid.IsNullOrEmptyOrWhiteSpace() || fcityid.Length == 2)
                        //{
                        //    //fcityid = enumList.Where(x => Convert.ToString(x["fgroup"]).Contains(fprovinceid)).OrderBy(x => Convert.ToString(x["fentryid"])).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        //}
                        dataEntity["fcity"] = fcityid;
                        if (!fcityid.IsNullOrEmptyOrWhiteSpace())
                        {
                            var fregion = Convert.ToString(dataEntity["fregion"]);
                            var fregionid = enumList.Where(x => Convert.ToString(x["fenumitem"]).Contains(fregion) && x["fid"].Equals("208b665d355d446f890fcc46568a3784")).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                            dataEntity["fregion"] = fregionid;
                        }
                        else
                        {
                            dataEntity["fregion"] = string.Empty;
                        }
                    }
                    else
                    {
                        dataEntity["fcity"] = string.Empty;
                        dataEntity["fregion"] = string.Empty;
                    }
                }

                switch (dataEntity["fservicetype"])
                {
                    case "fres_type_02"://增值
                        if (Convert.ToInt32(dataEntity["fservicescore"]) < 3)
                        {
                            dataEntity["fisrecommend"] = "0";
                        }
                        var fservicescore = enumList.Where(x => Convert.ToString(x["fenumitem"]).Equals(dataEntity["fservicescore"]) && x["fid"].Equals("3452aacaeae6425699559a39c98a583d")).OrderByDescending(x => x["fispreset"]).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["fservicescore"] = fservicescore.IsNullOrEmptyOrWhiteSpace() ? "" : fservicescore;
                        break;
                    case "fres_type_01"://送装
                        var fscore = enumList.Where(x => Convert.ToString(x["fenumitem"]).Equals(dataEntity["fscore"]) && x["fid"].Equals("3452aacaeae6425699559a39c98a583d")).OrderByDescending(x => x["fispreset"]).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["fscore"] = fscore.IsNullOrEmptyOrWhiteSpace() ? "" : fscore;
                        var fsalescore = enumList.Where(x => Convert.ToString(x["fenumitem"]).Equals(dataEntity["fsalescore"]) && x["fid"].Equals("3452aacaeae6425699559a39c98a583d")).OrderByDescending(x => x["fispreset"]).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["fsalescore"] = fsalescore.IsNullOrEmptyOrWhiteSpace() ? "" : fsalescore;
                        var finstallscore = enumList.Where(x => Convert.ToString(x["fenumitem"]).Equals(dataEntity["finstallscore"]) && x["fid"].Equals("3452aacaeae6425699559a39c98a583d")).OrderByDescending(x => x["fispreset"]).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        dataEntity["finstallscore"] = finstallscore.IsNullOrEmptyOrWhiteSpace() ? "" : finstallscore;
                        break;
                    case "fres_type_03"://售后
                        break;
                    default:
                        break;
                }

                if (!dataEntity["forderno"].IsNullOrEmptyOrWhiteSpace())
                {
                    var order = allOrder?.FirstOrDefault(x => x["id"].Equals(dataEntity["forderno"]));
                    if (order != null)
                    {
                        dataEntity["forderdeptid"] = order["fdeptid"];
                    }
                }
                if (!dataEntity["fproductentry"].IsNullOrEmptyOrWhiteSpace())
                {
                    var fproductentry = dataEntity["fproductentry"] as DynamicObjectCollection;
                    if (fproductentry != null && fproductentry.Any())
                    {
                        foreach (var item in dataEntity["fproductentry"] as DynamicObjectCollection)
                        {
                            var product = allProduct?.FirstOrDefault(x => x["id"].Equals(item["fmaterialid"]));
                            if (product != null)
                            {
                                item["funitid"] = product["funitid"];
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 得到服务类型、--服务类型、回访方式、整体评分
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetEnumAllList()
        {
            string strSql = "select fid,fispreset,fentryid,fenumitem from T_BD_ENUMDATAENTRY with(nolock) where fid in('a299d3fa3c4d49e4b72049a289e8b455','b058b5acafed4091a0bf2e178330b68f','3452aacaeae6425699559a39c98a583d','4ce1b6b47093415585878493ec1ad98b','6916ea2d44f24379a377cca5362bd7cf','208b665d355d446f890fcc46568a3784') ";//,'4ad9d1898c0442f5b2829466b2da1161'
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }


        /// <summary>
        /// 来源单据字段映射事件：可对已存在的数据包做映射覆盖
        /// </summary>
        /// <param name="e"></param>
        private void SourceBillFieldMapping(OnCustomServiceEventArgs e)
        {
        }
    }
}