using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.SEL.Category
{
    /// <summary>
    /// 选配类别：慕思通用保存同步操作
    /// </summary>
    [InjectService]
    [FormId("sel_category")]
    [OperationNo("MSSaveSync")]
    public class MSSaveSync : BaseMSSaveSync
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "BeforeConvertBaseData":
                    this.BeforeConvertBaseData(e);
                    break;
                case "AfterPackSourceBill":
                    this.AfterPackSourceBill(e);
                    break;
                case "BeforeSaveSourceBill":
                    this.BeforeSaveSourceBill(e);
                    break;
                case "SourceBillFieldMapping":
                    this.SourceBillFieldMapping(e);
                    break;
                case "BeforeCreateBaseData":
                    this.BeforeCreateBaseData(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// //基础资料字段值转换前事件：可指定当前需要转换的基础资料字段
        /// </summary>
        /// <param name="e"></param>
        private void BeforeConvertBaseData(OnCustomServiceEventArgs e)
        {
            //e.Cancel = true;
            //e.Result = new List<string>
            //{

            //};
        }

        /// <summary>
        /// 来源单据打包后事件：可对打包后的数据包进行处理或者直接覆盖整个数据包都是可以的
        /// </summary>
        /// <param name="e"></param>
        private void AfterPackSourceBill(OnCustomServiceEventArgs e)
        {
            //var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            //if (dataEntitys == null || dataEntitys.Count < 1) return;

        }

        /// <summary>
        /// 创建基础资料前事件：可指定当前不需要创建的基础资料表单
        /// </summary>
        /// <param name="e"></param>
        private void BeforeCreateBaseData(OnCustomServiceEventArgs e)
        {
            //e.Cancel = true;
            //e.Result = new List<string>
            //{ 
            //};
        }

        /// <summary>
        /// 来源单据保存前事件：可对当前要保存的数据包做处理
        /// </summary>
        /// <param name="e"></param>
        private void BeforeSaveSourceBill(OnCustomServiceEventArgs e)
        {
            var dataEntitys = (e.EventData as DynamicObject[])?.ToList();
            if (dataEntitys == null || dataEntitys.Count < 1) return;

            Dictionary<string, string> dicPropValueFieldKey2PropFiledKey = new Dictionary<string, string>
            {
                { "fdefaultpropvalueid", "fpropid" }
            };

            ConvertPropValue(dataEntitys, dicPropValueFieldKey2PropFiledKey);
        }

        /// <summary>
        /// 来源单据字段映射事件：可对已存在的数据包做映射覆盖
        /// </summary>
        /// <param name="e"></param>
        private void SourceBillFieldMapping(OnCustomServiceEventArgs e)
        {            
            //var eventData = e.EventData as Tuple<DynamicObject[], IEnumerable<DynamicObject>>;
            //if (eventData == null
            //    || eventData.Item1 == null
            //    || eventData.Item1.Length < 1) return;
        }
    }
}
