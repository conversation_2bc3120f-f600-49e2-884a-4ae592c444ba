using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Order
{
    /// <summary>
    /// 属性选配：动态列基础资料字段（模糊查询、弹窗查询）操作抽象基类
    /// </summary>
    public abstract class AbstractQueryDyn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            //事件名称
            switch (e.EventName)
            {
                case "onAfterParseFilterString":
                    this.OnAfterParseFilterString(e);
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 处理基础资料字段过滤条件解析后事件逻辑
        /// </summary>
        /// <param name="e"></param>
        private void OnAfterParseFilterString(OnCustomServiceEventArgs e)
        {
            var eventData = e.EventData as Tuple<string, string>;
            var fieldKey = eventData.Item1?.ToLowerInvariant(); //基础资料字段标识
            var fieldFilter = eventData.Item2; //基础资料字段过滤条件
            var formid = this.HtmlForm.Id;
            switch (fieldKey)
            {
                case "fresultbrandid":
                    //如果单据类型为“大客户销售合同”
                    var fbilltype = this.GetQueryOrSimpleParam<string>("fbilltype", "");
                    var seriesid = this.GetQueryOrSimpleParam<string>("fseriesid", "");
                    var fproductid = this.GetQueryOrSimpleParam<string>("fproductid", "");
                    //附属品牌
                    var fauxseriesid = this.GetQueryOrSimpleParam<string>("fauxseriesid", "");
                    //销售部门 合同传id
                    var deptid = this.GetQueryOrSimpleParam("deptid", "");
                    //送达方 采购订单传此id
                    var fdeliverid = this.GetQueryOrSimpleParam("fdeliverid", "");
                    var fattrinfo = this.GetQueryOrSimpleParam("fattrinfo", "");
                    var fcustomdes = this.GetQueryOrSimpleParam("fcustomdes", "");
                    var fisoutspot = this.GetQueryOrSimpleParam("fisoutspot", false);
                    var fisfromfirstinventory = this.GetQueryOrSimpleParam("fisfromfirstinventory", false);
                    //if (this.HtmlForm.Id != "ydj_order") {
                    //    deptid = fdeliverid;
                    //}
                    //当门店仅代理慕思家纺品牌、或者门店仅代理慕思美居品牌时 处理【业绩品牌】候选项
                    var filter = "1=1 ";
                    filter = this.Container.GetService<IResultBrandService>().GetFilter(fproductid, this.Context, fbilltype, seriesid, fauxseriesid, deptid, formid, e.DataEntities, fdeliverid, fattrinfo, fcustomdes, fisoutspot, fisfromfirstinventory);

                    e.Result = filter;
                    e.Cancel = true;
                    break;
                //case "fdeptid":
                //    var billtypeName = this.GetQueryOrSimpleParam<string>("billtypeName", "");
                //    var orgid = Context.IsTopOrg ? Context.TopCompanyId : Context.Company;
                //    if (formid == "ydj_order" && billtypeName == "大客户销售合同")
                //    {
                //         e.Result = $@" EXISTS (SELECT 1 FROM t_bas_store c with (nolock)  WHERE fstoreid=c.fnumber AND ISNULL(fisnewchannel,0) = 1) or exists(SELECT 1 FROM t_ydj_productauth t1 with (nolock) 
                //            INNER JOIN t_ydj_productauthbs t2 with(nolock) ON t1.fid = t2.fid
                //            inner join t_ydj_series t3 with(nolock) on CHARINDEX(t3.fid,t2.fserieid) > 0
                //            where fstore = t1.forgid and  CHARINDEX('慕思经典', t3.fname) > 0 AND t1.fforbidstatus = 0) ";
                //        e.Cancel = true;
                //    }
                //    break;
            }
        }
    }
}
