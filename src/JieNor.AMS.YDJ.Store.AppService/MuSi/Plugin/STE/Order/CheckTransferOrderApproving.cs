using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Service;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：检查转单申请是否审核中
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("CheckTransferOrderApproving")]
    public class CheckTransferOrderApproving : AbstractOperationServicePlugIn
    { 
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string id = this.GetQueryOrSimpleParam("id", "");
            if (id.Length == 0) throw new BusinessException("请求参数销售合同ID无效，请检查！");
             
            var order = this.Context.LoadBizDataById(this.HtmlForm.Id, id);
            if (order == null) throw new BusinessException("销售合同不存在，请检查！");

            var isApproving = new OrderCommon(this.Context).IsTransferOrderApproving(order);
            if (isApproving)
            {
                throw new BusinessException($"销售合同[{order["fbillno"]}]中的商品明细存在转单申请单为审批中时无法反审核！");
            }
        } 
    }
}
