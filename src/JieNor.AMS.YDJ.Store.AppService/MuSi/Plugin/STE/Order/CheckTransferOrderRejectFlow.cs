using JieNor.AMS.YDJ.Store.AppService.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：检查转单申请状态是否可以反审核
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("CheckTransferOrderRejectFlow")]
    public class CheckTransferOrderRejectFlow : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string ids = this.GetQueryOrSimpleParam("ids", "");
            if (ids.Length == 0) throw new BusinessException("请求参数销售合同ID无效，请检查！");
            var pkIds = ids.Trim(',').Split(',');

            var orderList = this.Context.LoadBizDataById(this.HtmlForm.Id, pkIds);
            if (orderList == null || !orderList.Any()) throw new BusinessException("销售合同不存在，请检查！");

            List<string> faildNos = new List<string>();
            foreach (var item in orderList)
            {
                //var needTrans = Convert.ToBoolean(item["fneedtransferorder"]);
                //if (needTrans)
                //{
                var entries = item["fentry"] as DynamicObjectCollection;
                if (entries != null && entries.Count > 0)
                {
                    foreach (var entry in entries)
                    {
                        var status = entry["ftransferorderstatus"].ToString();
                        if (!status.IsNullOrEmptyOrWhiteSpace() && Convert.ToInt32(status) != (int)TransferOrderStatus.Reject)
                        {
                            faildNos.Add(Convert.ToString(item["fbillno"]));
                            break;
                        }
                    }
                }
                //}
            }
            this.Result.SrvData = faildNos;
        }
    }
}
