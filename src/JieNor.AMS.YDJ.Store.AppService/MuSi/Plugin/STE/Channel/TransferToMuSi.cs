using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Channel
{
    [InjectService]
    [FormId("ste_channel")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {  
                //case "zykorg":
                case "zybukrs":
                    e.Cancel = true;
                    var mainOrgId = this.HtmlForm.GetField("fmainorgid") != null
                                    ? Convert.ToString(e.DataEntity["fmainorgid"])
                                    : this.UserContext.Company;

                    var agentItem = this.UserContext.LoadBizDataById("bas_agent", mainOrgId);
                    if (agentItem != null)
                    {
                        var mate = this.UserContext.Container.GetService<IMetaModelService>();
                        var agentForm = mate.LoadFormModel(this.UserContext, "bas_agent");
                        // 加载数据
                        var refObjMgr = this.UserContext.Container.GetService<LoadReferenceObjectManager>();
                        refObjMgr?.Load(this.UserContext, new DynamicObject[] { agentItem }, true, agentForm, new List<string> { "fsaleorgid" });
                        e.Result = Convert.ToString((agentItem["fsaleorgid_ref"] as DynamicObject)["fnumber"]); //传销售组织
                    } 
                    break; 
            }
        }


        /// <summary>
        /// 向第三方系统发送请求后，解析第三方接口返回值事件
        /// </summary>
        /// <param name="e"></param>
        public override void ParseResult(ParseResultEventArgs e)
        {
            base.ParseResult(e);

            var result = e.Result;

            if (result == null) return;

            var resultObj = JObject.Parse((string)result.ToJson());
            if (Convert.ToString(resultObj?["code"] ?? "").EqualsIgnoreCase("200")) 
            {
                var data = Convert.ToString(resultObj?["data"] ?? "").Trim();
                if (!data.IsNullOrEmptyOrWhiteSpace()) 
                {
                    var dataEntity = e.DataEntitys?.FirstOrDefault();
                    if (dataEntity == null) return;

                    dataEntity["fsuppliercode"] = data;

                    this.UserContext.SaveBizData("ste_channel", dataEntity);
                }
            }
        }
    }
}
