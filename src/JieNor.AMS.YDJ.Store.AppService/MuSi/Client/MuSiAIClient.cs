using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Store.AppService.Clients;
using JieNor.AMS.YDJ.Store.AppService.Clients.Ewc;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Client
{
    /// <summary>
    /// 慕思AI云Client
    /// </summary>
    public class MuSiAIClient
    {
        public TargetServer TargetServer { get; private set; }

        public DynamicObject ExtApp { get; private set; }

        public MuSiAIClient(UserContext userCtx, DynamicObject extApp)
        {
            userCtx.CheckNoTestCompany();

            if (extApp == null)
            {
                throw new BusinessException("外部应用不存在！");
            }

            this.ExtApp = extApp;
            this.TargetServer = GetTargetServer(extApp);
        }

        /// <summary>
        /// 获取服务地址
        /// </summary>
        /// <param name="extApp">外部应用</param>
        /// <returns></returns>
        private TargetServer GetTargetServer(DynamicObject extApp)
        {
            var targetServer = new TargetServer();

            targetServer.Host = Convert.ToString(extApp["fserverurl"]);

            return targetServer;
        }

        private JObject GetReqeustJson(DynamicObject extApp)
        {
            string appKey = Convert.ToString(extApp["fappkey"]);
            if (appKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException("外部应用匹配不正确！");
            }

            #region 认证Token

            JObject configObj = JObject.Parse(appKey);

            string clientId = configObj.GetJsonValue("clientId", "");
            string clientSecret = configObj.GetJsonValue("clientSecret", "");
            int version = 1;
            string requestId = Guid.NewGuid().ToString("N");

            long timestamp = BeiJingTime.Now.TimestampFromBeiJingTime();

            string strA = $"clientId={clientId}&clientSecret={clientSecret}&version={version}&requestId={requestId}&timestamp={timestamp}";

            // SHA256加密
            MemoryStream ms = new MemoryStream(Encoding.UTF8.GetBytes(strA));
            string sign = SecurityUtil.ComputeHash(ms, "sha256").ToHexString();


            JObject requestJson = new JObject();
            JObject header = new JObject();
            requestJson["header"] = header;

            header["clientId"] = clientId;
            header["requestId"] = requestId;
            header["timestamp"] = timestamp;
            header["version"] = version;
            header["sign"] = sign;

            #endregion

            return requestJson;
        }

        /// <summary>
        /// 发送请求
        /// </summary>
        /// <typeparam name="TResponse"></typeparam>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public TResponse Send<TResponse>(string url, object data, Enu_HttpMethod method = Enu_HttpMethod.Post) where TResponse : class
        {
            var requestJson = GetReqeustJson(this.ExtApp);

            requestJson["payload"] = JToken.FromObject(data ?? new object());

            var resp = JsonClient.InvokeThirdByJson<object, TResponse>(
                this.TargetServer,
                url,
                requestJson,
                method);

            return resp;
        }
    }
}
