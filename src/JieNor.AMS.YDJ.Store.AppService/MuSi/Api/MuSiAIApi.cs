using System;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Client;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Response;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Api
{
    /// <summary>
    /// 慕思AI云Api
    /// </summary>
    public class MuSiAIApi
    {
        /// <summary>
        /// 获取睡感定制问题列表接口
        /// </summary>
        private static readonly UrlData QuestionUrl = new UrlData { Name = "获取睡感定制问题列表接口", Url = "/t10/open-api/ts3/kdo-exm" };

        /// <summary>
        /// 下单通知慕思云同步接口
        /// </summary>
        private static readonly UrlData SyncUrl = new UrlData { Name = "下单通知慕思云同步接口", Url = "/t10/open-api/ts3/kdo-callback" };

        /// <summary>
        /// 获取睡感定制问题列表接口
        /// </summary>
        /// <param name="userCtx"></param>
        public static MuSiAIResponse<JObject> GetQuestion(UserContext userCtx)
        {
            return Invoke<JObject>(userCtx, QuestionUrl.Url, QuestionUrl.Name, null);
        }

        /// <summary>
        /// 下单通知慕思云同步接口
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param>
        /// <returns></returns>
        public static MuSiAIResponse<object> Sync(UserContext userCtx, SyncAIDTO dto)
        {
            return Invoke<object>(userCtx, SyncUrl.Url, SyncUrl.Name, dto, "ydj_purchaseorder");
        }

        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        private static MuSiAIResponse<T> Invoke<T>(UserContext userCtx, string url, string opName, BaseDTO data, string formId = "", Enu_HttpMethod method = Enu_HttpMethod.Post)
        {
            var extApp = GetExtApp(userCtx);
            if (extApp == null) return MuSiAIResponse<T>.SUCCESS;

            var client = new MuSiAIClient(userCtx, extApp);

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, formId);

            opLogObj["fdescription"] = $"当前系统调用{extApp["fname"]}接口";
            opLogObj["fopstatus"] = "2";

            try
            {
                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求Url：{client.TargetServer.Host}{url}");
                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");

                var resp = client.Send<MuSiAIResponse<T>>(url, data, method);

                if (resp.Success)
                {
                    opLogObj["fopstatus"] = "2";
                    opLogObj["fsuccessnumbers"] = data?.GetNumbers().JoinEx(",", false);
                }
                else
                {
                    opLogObj["fopstatus"] = "3";
                    opLogObj["ffailnumbers"] = data?.GetNumbers().JoinEx(",", false);
                    opLogObj["fcanretry"] = true;
                    opLogObj["ferrorsource"] = "2";
                    opLogObj["frequestdata"] = data?.ToJson();
                }

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{ resp.ToJson() }");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                return resp;
            }
            catch (Exception ex)
            {
                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                opLogObj["fopstatus"] = "3";
                opLogObj["ffailnumbers"] = data?.GetNumbers().JoinEx(",", false);
                opLogObj["fcanretry"] = true;
                opLogObj["ferrorsource"] = "2";
                opLogObj["frequestdata"] = data?.ToJson();

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, ex.Message + "\r\n" + ex.StackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                return new MuSiAIResponse<T> { Code = 500, Msg = ex.Message };
            }
        }

        /// <summary>
        /// 获取外部应用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static DynamicObject GetExtApp(UserContext userCtx)
        {
            var sysProfileService = userCtx.Container.GetService<ISystemProfile>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            var fmusiaiextappid = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiaiextappid", "");

            if (fmusiaiextappid.IsNullOrEmptyOrWhiteSpace())
            {
                var formMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_datasyncparam");

                throw new BusinessException($"未设置【{formMeta.Caption}】的【{formMeta.GetField("fmusiaiextappid")?.Caption}】");
            }

            var extApp = topCtx.LoadBizDataById("sys_externalapp", fmusiaiextappid);
            return extApp;
        }
    }

    public class UrlData
    {
        public string Url { get; set; }
        public string Name { get; set; }
    }
}
