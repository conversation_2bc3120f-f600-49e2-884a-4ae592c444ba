using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.DataTransferObject
{
    /// <summary>
    /// 同步至慕思配置
    /// </summary>
    public class ToMuSiSetting
    {
        /// <summary>
        /// 外部应用标识
        /// </summary>
        public string ExtAppId { get; set; }

        /// <summary>
        /// 字段映射对象主键
        /// </summary>
        public string BillMapId { get; set; }

        /// <summary>
        /// 服务前置条件
        /// </summary>
        public string Condition { get; set; }

        /// <summary>
        /// 外部应用动态对象
        /// </summary>
        public DynamicObject ExtAppObj { get; set; }

        /// <summary>
        /// 是否写日志
        /// </summary>
        public bool IsLogging { get; set; } = true;

        /// <summary>
        /// 操作日志表单模型
        /// </summary>
        public HtmlForm OpLogForm { get; set; }

        /// <summary>
        /// 操作日志
        /// </summary>
        public DynamicObject OpLogObj { get; set; }


        /// <summary>
        /// 操作日志表单模型重试
        /// </summary>
        public HtmlForm OpLogRetryForm { get; set; }

        /// <summary>
        /// 操作日志重试
        /// </summary>
        public DynamicObject OpLogRetryObj { get; set; }
        /// <summary>
        /// 同步模式 <see cref="Enu_MuSiSyncMode"/>
        /// </summary>
        public Enu_MuSiSyncMode SyncMode { get; set; } = Enu_MuSiSyncMode.HTTP;
    }
}
