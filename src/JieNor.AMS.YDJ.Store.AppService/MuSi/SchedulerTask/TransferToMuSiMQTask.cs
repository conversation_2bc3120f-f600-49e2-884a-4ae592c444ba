using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.SchedulerTask
{
    /// <summary>
    /// 同步数据到慕思消息队列
    /// </summary>
    [InjectService]
    [TaskSvrId("transfertomusimq")]
    [Caption("同步数据到慕思消息队列")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class TransferToMuSiMQTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "si_transfertomusitaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected Dictionary<string, object> JobParameter { get; set; }

        protected IMuSiBizObjMapService MuSiBizObjMapService { get; set; }

        protected IDBServiceEx DBServiceEx { get; set; }

        protected string ExtAppId { get; set; }

        protected string BizFormId { get; set; }

        /// <summary>
        /// 消息队列用户上下文（用于区分主库）
        /// </summary>
        protected UserContext MQContext { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        protected ILogServiceEx LogService { get; set; }

        /// <summary>
        /// 执行数据打包同步分发逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            this.MuSiBizObjMapService = this.UserContext.Container.GetService<IMuSiBizObjMapService>();
            this.LogService = this.UserContext.Container.GetService<ILogServiceEx>();
            this.DBServiceEx = this.UserContext.Container.GetService<IDBServiceEx>();

            this.JobParameter = Convert.ToString(this.TaskObject.Parameter).FromJson<Dictionary<string, object>>() ?? new Dictionary<string, object>();

            this.ExtAppId = this.JobParameter?.GetString("extAppId");
            this.BizFormId = this.JobParameter?.GetString("syncBizFormId");

            if (this.ExtAppId.IsNullOrEmptyOrWhiteSpace() || this.BizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                this.WriteLog($"{this.TaskObject.TaskName}配置错误：【外部应用】或【业务对象】不能为空！");
                return;
            }

            this.MQContext = this.UserContext.CreateSlaveDBContext(true, this.UserContext.TopCompanyId);

            var bizFormMeta = this.MetaModelService.LoadFormModel(this.MQContext, this.BizFormId);
            var bizObjMaps = this.MuSiBizObjMapService.GetBizObjMaps(
                this.MQContext,
                bizFormMeta,
                this.ExtAppId,
                  Enu_MuSiSyncDir.CurrentToMuSi,
                Enu_MuSiSyncTimePoint.SyncTimer);

            if (bizObjMaps.IsNullOrEmpty())
            {
                this.WriteLog($"没有找到匹配的映射对象！", bizFormMeta);
                return;
            }

            string tmpTableName = this.DBService.CreateTemporaryTableName(this.MQContext);
            string successBizObjTmp = null;

            try
            {
                DeleteTestCompanyRecords();

                // 插入
                string sql = $@"
select * into {tmpTableName} 
from (
    select top 1000 fid, fcompanyid, fbizformid, fbizobjid, fbizobjno, fbizobjdeletestatus 
    from t_ms_mqsyncrecord with(nolock) 
    where fbizformid='{bizFormMeta.Id}' and fsenddate is null 
    order by fcreatedate
) t";
                this.DBServiceEx.Execute(this.MQContext, sql);

                //todo:
                //1. 根据当前调度方案找到集成参数，并且读取所有待同步的业务映射对象
                //2. 构建CommonListDTO，调用标准系统数据同步操作，以便于手工从列表上点击同步达到相同效果
                //3. 采用流式分批机制来调用系统操作（并且按业务对象顺序）
                //4. 业务对象顺序，尽量自动做到自动分析前后，防止死循环嵌套
                sql = $"select distinct fcompanyid,fbizobjid,fbizobjno from {tmpTableName} where fbizobjdeletestatus='1'";
                var deletedBizObjs = this.DBService.ExecuteDynamicObject(this.MQContext, sql);

                foreach (var fieldMapObj in bizObjMaps)
                {
                    try
                    {
                        var allSuccessBizObjIds = SyncData(fieldMapObj, bizFormMeta, tmpTableName, deletedBizObjs);
                        if (allSuccessBizObjIds.Count == 0) continue;

                        if (allSuccessBizObjIds.Count < 50)
                        {
                            sql = $@"
                update t_ms_mqsyncrecord set fsenddate=getdate(),fsyncstatus ='1' 
                where fid in (select fid from {tmpTableName} where fbizformid='{bizFormMeta.Id}' and fbizobjid in ({allSuccessBizObjIds.JoinEx(",", true)})) ";
                        }
                        else
                        {
                            successBizObjTmp =
                                this.DBService.CreateTempTableWithDataList(this.MQContext, allSuccessBizObjIds);

                            sql = $@"
                update t_ms_mqsyncrecord set fsenddate=getdate() ,fsyncstatus ='1' 
                where fid in (select fid from {tmpTableName} where fbizformid='{bizFormMeta.Id}' and fbizobjid in (select fid from {successBizObjTmp}))";
                        }

                        this.DBServiceEx.Execute(this.MQContext, sql);
                    }
                    catch (BusinessException ex)
                    {
                        this.WriteLog(ex.Message, bizFormMeta);
                    }
                    catch (Exception ex)
                    {
                        this.WriteLog($"业务对象同步出现未知错误：{ex.Message}, stacktrace:{ex.StackTrace}", bizFormMeta);
                        this.LogService.Error("同步数据到慕思消息队列计划任务失败", ex);
                    }
                }
            }
            finally
            {
                this.DBService.DeleteTempTableByName(this.MQContext, tmpTableName, true);
                this.DBService.DeleteTempTableByName(this.MQContext, successBizObjTmp, true);
            }
        }

        /// <summary>
        /// 删除全量测试组织的记录
        /// </summary>
        private void DeleteTestCompanyRecords()
        {
            var testCompanyIds = this.MQContext.GetTestCompanyIds();
            if (testCompanyIds.Any())
            {
                string sql = $"delete from t_ms_mqsyncrecord where fcompanyid in ({testCompanyIds.JoinEx(",", true)})";

                this.DBServiceEx.Execute(this.MQContext, sql);
            }
        }

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="fieldMapObj"></param>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private List<string> SyncData(DynamicObject fieldMapObj, HtmlForm formMeta, string tmpTableName, DynamicObjectCollection deletedBizObjs)
        {
            //todo:同步某个特定业务对象的数据至对方系统

            // 获取需要同步的经销商
            var agents = GetSyncAgents(fieldMapObj, formMeta, tmpTableName);
            // 总记录数
            long lTotalBills = 0;
            decimal progress = 0;
            decimal allAgentCount = agents.Count;
            decimal index = 1;

            List<string> allSuccessBizObjIds = new List<string>();

            this.WriteLog($"需要同步经销商数({allAgentCount})", formMeta);

            foreach (var agent in agents)
            {
                lTotalBills += SyncDataByAgent(agent, fieldMapObj, formMeta, tmpTableName, deletedBizObjs, out var successBizObjIds);

                allSuccessBizObjIds.AddRange(successBizObjIds);

                progress = index / allAgentCount;

                this.WriteLog($"[{agent["fnumber"]}/{agent["fname"]}]数据同步完成(总进度{progress:P})", formMeta);
                this.SaveLog();

                index++;

                this.SetTaskProgress(progress, $"[{agent["fnumber"]}/{agent["fname"]}]数据同步完成！");
            }

            this.SetTaskProgress(99, "全部数据同步完成！");

            if (lTotalBills == 0)
            {
                this.WriteLog($"没有数据需要同步，可能原因是待同步对象自上次同步以来没有更新！", formMeta);
            }
            else
            {
                this.WriteLog($"本次共同步{lTotalBills}条数据！", formMeta);
            }

            return allSuccessBizObjIds;
        }

        /// <summary>
        /// 根据经销商同步数据
        /// </summary>
        /// <param name="agent">经销商</param>
        /// <param name="fieldMapObj"></param>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private long SyncDataByAgent(DynamicObject agent, DynamicObject fieldMapObj, HtmlForm formMeta, string tmpTableName, DynamicObjectCollection deletedBizObjs, out List<string> succecssBizObjIds)
        {
            succecssBizObjIds = new List<string>();

            string agentId = agent["id"].ToString();
            // 日志title
            string title = $"[{agent["fnumber"]}/{agent["fname"]}]";

            var agentCtx_Slave = this.MQContext.CreateSlaveDBContext(true, agentId);
            // if (agentCtx_Slave.Companys.IsNullOrEmpty())
            // {
            //     this.WriteLog($"无法连接数据中心（fmainorgid={agentId}）！", formMeta);

            //     return 0;
            // }

            var agentCtx_Master = this.UserContext.CreateAdminDbContext(agentId);
            // if (agentCtx_Master.Companys.IsNullOrEmpty())
            // {
            //     this.WriteLog($"无法连接数据中心（fmainorgid={agentId}）！", formMeta);

            //     return 0;
            // }

            var currDeleteBizObjs =
                deletedBizObjs.Where(s => Convert.ToString(s["fcompanyid"]).EqualsIgnoreCase(agentCtx_Slave.Company));

            SqlBuilderParameter sqlpara = new SqlBuilderParameter(agentCtx_Slave, formMeta);
            sqlpara.SelectedFieldKeys.Add("test");
            sqlpara.PageCount = -1;
            sqlpara.PageIndex = -1;
            sqlpara.QueryUserFieldOnly = false;
            sqlpara.IsDistinct = true;
            sqlpara.NoIsolation = false;
            sqlpara.NoColorSetting = true;
            sqlpara.ReadDirty = true;
            sqlpara.IsShowForbidden = true;
            sqlpara.EnableDataQueryRule = false;
            sqlpara.EnableDataRowACL = false;

            // 过滤
            sqlpara.FilterString +=
                $" (fid in (select fbizobjid from {tmpTableName} where fcompanyid='{agentCtx_Slave.Company}' and fbizformid='{formMeta.Id}')) ";

            var filterString = this.JobParameter.GetString("filter");
            if (!filterString.IsNullOrEmptyOrWhiteSpace())
            {
                sqlpara.FilterString += $" and ({filterString})";
            }

            if (!fieldMapObj["ffilterstring"].IsNullOrEmptyOrWhiteSpace())
            {
                sqlpara.FilterString += $" and ({fieldMapObj["ffilterstring"]})";
            }

            //取得处理批量的参数，默认为100单一个批次
            var procBatchSize = this.JobParameter.GetInt("batchSize");
            if (procBatchSize == 0)
            {
                procBatchSize = 100;
            }

            var querObj = QueryService.BuilQueryObject(sqlpara);

            long lTotalBills = 0; // 累计数量
            int batchTimes = 1;     // 批次
            int updatedCount = 0;   // 更新同步数量
            int deletedCount = currDeleteBizObjs.Count();   // 删除同步数量

            using (var reader = this.DBService.ExecuteReader(agentCtx_Slave, querObj.AllCountSql, sqlpara.DynamicParams))
            {
                if (reader.Read())
                {
                    updatedCount = reader.GetInt32(0);
                }
            }

            if (updatedCount == 0 && deletedCount == 0)
            {
                this.WriteLog($"{title}无数据同步(总进度100%)", formMeta);
                this.SaveLog();

                return 0;
            }

            int allCount = updatedCount + deletedCount; // 总数

            #region 同步更新数据
            List<SelectedRow> lstBatchRows = new List<SelectedRow>();
            var bizObjs = agentCtx_Slave.ExecuteDynamicObject(querObj.SqlNoPage, sqlpara.DynamicParams);
            foreach (var bizOjb in bizObjs)
            {
                var pkId = bizOjb["fbillhead_id"] as string;
                if (pkId.IsNullOrEmptyOrWhiteSpace()) continue;

                if (lstBatchRows.Where(a => a.PkValue == pkId).Any()) continue;
                lstBatchRows.Add(new SelectedRow { PkValue = pkId, });
                if ((lstBatchRows.Count % procBatchSize) == 0 && lstBatchRows.Count > 0)
                {
                    // 使用主库操作，避免因从库未同步表造成影响
                    var result = this.DoSyncData(agentCtx_Master, lstBatchRows, formMeta, fieldMapObj);
                    this.Result.MergeResult(result);

                    if (result.IsSuccess)
                    {
                        this.WriteLog($"{title}第{batchTimes}批成功：" + result.ToString(), formMeta);

                        succecssBizObjIds.AddRange(lstBatchRows.Select(s => s.PkValue));
                    }
                    else
                    {
                        this.WriteLog($"{title}第{batchTimes}批失败：" + result.ToString(), formMeta);
                    }

                    lTotalBills += lstBatchRows.Count;

                    decimal progress = lTotalBills * 1.0M / allCount;

                    this.WriteLog($"{title}第{batchTimes}批完成(总进度{progress:P})", formMeta);
                    this.SaveLog();

                    lstBatchRows.Clear();
                    batchTimes++;
                }
            }

            //上面的数据没被整除掉的话，下面也会执行一次
            if (lstBatchRows.Any())
            {
                // 使用主库操作，避免因从库未同步表造成影响
                var result = this.DoSyncData(agentCtx_Master, lstBatchRows, formMeta, fieldMapObj);
                this.Result.MergeResult(result);

                if (result.IsSuccess)
                {
                    this.WriteLog($"{title}第{batchTimes}批成功：" + result.ToString(), formMeta);

                    succecssBizObjIds.AddRange(lstBatchRows.Select(s => s.PkValue));
                }
                else
                {
                    this.WriteLog($"{title}第{batchTimes}批失败：" + result.ToString(), formMeta);
                }

                lTotalBills += lstBatchRows.Count;

                decimal progress = lTotalBills * 1.0M / allCount;

                this.WriteLog($"{title}第{batchTimes}批完成(总进度{progress:P})", formMeta);
                this.SaveLog();

                lstBatchRows.Clear();
                batchTimes++;
            }
            #endregion

            #region 同步删除记录 
            Dictionary<string, string> deletedObjs = new Dictionary<string, string>();
            foreach (var deletedBizObj in currDeleteBizObjs)
            {
                var pkId = Convert.ToString(deletedBizObj["fbizobjid"]);
                if (pkId.IsNullOrEmptyOrWhiteSpace()) continue;

                deletedObjs[pkId] = Convert.ToString(deletedBizObj["fbizobjno"]);

                if ((deletedObjs.Count % procBatchSize) == 0 && deletedObjs.Count > 0)
                {
                    var result = this.DoSyncDeletedData(agentCtx_Slave, deletedObjs, formMeta, fieldMapObj);
                    this.Result.MergeResult(result);

                    if (result.IsSuccess)
                    {
                        this.WriteLog($"{title}第{batchTimes}批成功：" + result.ToString(), formMeta);

                        succecssBizObjIds.AddRange(deletedObjs.Keys);
                    }
                    else
                    {
                        this.WriteLog($"{title}第{batchTimes}批失败：" + result.ToString(), formMeta);
                    }

                    lTotalBills += deletedObjs.Count;

                    decimal progress = lTotalBills * 1.0M / allCount;

                    this.WriteLog($"{title}第{batchTimes}批完成(总进度{progress:P})", formMeta);
                    this.SaveLog();

                    deletedObjs.Clear();
                    batchTimes++;
                }
            }

            if (deletedObjs.Any())
            {
                var result = this.DoSyncDeletedData(agentCtx_Slave, deletedObjs, formMeta, fieldMapObj);
                this.Result.MergeResult(result);

                if (result.IsSuccess)
                {
                    this.WriteLog($"{title}第{batchTimes}批成功：" + result.ToString(), formMeta);

                    succecssBizObjIds.AddRange(deletedObjs.Keys);
                }
                else
                {
                    this.WriteLog($"{title}第{batchTimes}批失败：" + result.ToString(), formMeta);
                }

                lTotalBills += deletedObjs.Count;

                decimal progress = lTotalBills * 1.0M / allCount;

                this.WriteLog($"{title}第{batchTimes}批完成(总进度{progress:P})", formMeta);
                this.SaveLog();

                deletedObjs.Clear();
                batchTimes++;
            }

            #endregion

            return lTotalBills;
        }

        /// <summary>
        /// 执行数据同步逻辑
        /// </summary>
        /// <param name="lstBatchData"></param>
        /// <param name="fieldMapObj"></param>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        private IOperationResult DoSyncData(UserContext userCtx, IEnumerable<SelectedRow> lstBatchData, HtmlForm bizForm, DynamicObject fieldMapObj)
        {
            JObject joServicePara = new JObject();
            joServicePara["serConfig"] = $"{{'extAppId':'{fieldMapObj["fextappid"]}','billMapId':'{fieldMapObj["id"]}'}}";   //服务配置参数
            joServicePara["condition"] = "";     //服务前置条件

            CommonListDTO listDto = new CommonListDTO()
                .SetSelectedRows(lstBatchData)
                .SetFormId(bizForm.Id)
                .SetOperationNo("ToMuSi")
                .SetTaskId(this.TaskId)
                .SetOption("__fieldMapObj__", fieldMapObj)
                .SetOption("extAppId", fieldMapObj["fextappid"])
                // 操作服务的参数
                .SetOption("servicePara", joServicePara.ToJson())
                // 不写日志
                //.SetOption("IsLogging", false)
                // 设置发消息队列
                .SetOption("SyncMode", Enu_MuSiSyncMode.MQ);

            Dictionary<string, object> dctHeader = new Dictionary<string, object>();
            dctHeader["X-AppId"] = userCtx.AppId;
            dctHeader["X-CompanyId"] = userCtx.Company;

            //交给本地操作行为执行，目的是具体同步的代码与按钮操作代码进行复用
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var objResult = gateway.InvokeLocal<object>(userCtx, listDto, Enu_HttpMethod.Post, dctHeader);
            if (objResult is DynamicDTOResponse)
            {
                var procResult = (objResult as DynamicDTOResponse).OperationResult;
                this.LogService.Info("慕思同步数据：" + procResult.ToString());

                return procResult;
            }

            return new OperationResult { SimpleMessage = "操作失败！" };
        }

        /// <summary>
        /// 执行数据同步逻辑
        /// </summary>
        /// <param name="deletedBizObjIds"></param>
        /// <param name="fieldMapObj"></param>
        /// <param name="bizForm"></param>
        /// <returns></returns>
        private IOperationResult DoSyncDeletedData(UserContext userCtx, Dictionary<string, string> deletedObjs, HtmlForm bizForm, DynamicObject fieldMapObj)
        {
            if (deletedObjs.Count == 0) return new OperationResult() { IsSuccess = true };

            var deletedIds = new HashSet<string>(deletedObjs.Keys);

            JObject joServicePara = new JObject();
            joServicePara["serConfig"] = $"{{'extAppId':'{fieldMapObj["fextappid"]}','billMapId':'{fieldMapObj["id"]}'}}";   //服务配置参数
            joServicePara["condition"] = "";     //服务前置条件

            OperateOption option = OperateOption.Create();
            option.SetVariableValue("__fieldMapObj__", fieldMapObj);
            option.SetVariableValue("extAppId", fieldMapObj["fextappid"]);
            option.SetVariableValue("servicePara", joServicePara.ToJson());
            //option.SetVariableValue("IsLogging", false);
            option.SetVariableValue("SyncMode", Enu_MuSiSyncMode.MQ);
            option.SetVariableValue("DeletedIds", deletedIds);

            var deletedBizObjs = new List<DynamicObject>();
            var dt = bizForm.GetDynamicObjectType(this.MQContext);
            var mainOrgFld = bizForm.GetField("fmainorgid");
            var numberFld = bizForm.GetNumberField();
            foreach (var deletedObj in deletedObjs)
            {
                var deletedBizObj = new DynamicObject(dt);
                deletedBizObj["id"] = deletedObj.Key;
                if (numberFld != null)
                {
                    deletedBizObj[numberFld.PropertyName] = deletedObj.Value;
                }
                if (mainOrgFld != null)
                {
                    deletedBizObj[mainOrgFld.PropertyName] = userCtx.Company;
                }

                deletedBizObjs.Add(deletedBizObj);
            }

            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.InvokeBillOperation(userCtx, bizForm.Id, deletedBizObjs, "ToMuSi",
                   option.ToDictionary(s => s.Key, s => s.Value));

            this.LogService.Info("慕思同步删除数据：" + result.ToString());

            return result;
        }

        /// <summary>
        /// 获取有数据要同步的经销商
        /// </summary>
        /// <returns></returns>
        private List<DynamicObject> GetSyncAgents(DynamicObject fieldMapObj, HtmlForm formMeta, string tmpTableName)
        {
            string sql = $@"
select o.fid as id, o.fnumber, o.fname 
from t_bas_organization as o with(nolock)
where o.ftopcompanyid='{this.MQContext.TopCompanyId}' and exists(select 1 from {tmpTableName} tmp where tmp.fcompanyid=o.fid)";

            return this.MQContext.ExecuteDynamicObject(sql, new List<SqlParam>()).ToList();
        }

    }
}
