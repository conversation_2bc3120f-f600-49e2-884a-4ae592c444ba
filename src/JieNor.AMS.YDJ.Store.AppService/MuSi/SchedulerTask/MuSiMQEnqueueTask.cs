using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Serialization;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.SchedulerTask
{
    /// <summary>
    /// 加入慕思消息队列
    /// </summary>
    [InjectService]
    [TaskSvrId("musimqenqueue")]
    [Caption("加入慕思消息队列")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class MuSiMQEnqueueTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "si_musimqenqueuetaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected MuSiMQEnqueueTaskParamter JobParameter { get; set; }

        protected ILogServiceEx LogService { get; set; }

        protected string BizFormId { get; set; }

        protected HtmlForm BizForm { get; set; }

        /// <summary>
        /// 比较字段集合
        /// </summary>
        protected List<HtmlField> CompareFields { get; set; }

        /// <summary>
        /// 消息队列用户上下文（用于区分主库）
        /// </summary>
        protected UserContext MQContext { get; set; }

        /// <summary>
        /// 调试信息
        /// </summary>
        protected string CompareDebugInfo;

        protected IDBServiceEx DbServiceEx { get; set; }

        /// <summary>
        /// 执行数据打包同步分发逻辑
        /// </summary>
        protected override async Task DoExecute()
        {
            this.LogService = this.UserContext.Container.GetService<ILogServiceEx>();

            this.JobParameter = Convert.ToString(this.TaskObject.Parameter)?.FromJson<MuSiMQEnqueueTaskParamter>();

            this.BizFormId = this.JobParameter?.fbizformid.GetJsonValue<string>("id");
            if (this.BizFormId.IsNullOrEmptyOrWhiteSpace())
            {
                this.WriteLog($"{this.TaskObject.TaskName}配置错误：【业务对象】不能为空！");
                return;
            }

            this.DbServiceEx = this.UserContext.Container.GetService<IDBServiceEx>();

            this.BizForm = this.MetaModelService.LoadFormModel(this.MQContext, this.BizFormId);

            string compareFieldIds = this.JobParameter?.fcomparefieldids;
            if (compareFieldIds.IsNullOrEmptyOrWhiteSpace())
            {
                this.WriteLog($"{this.TaskObject.TaskName}配置错误：【比较字段】不能为空！");
                return;
            }

            this.CompareFields = GetCompareFields(this.BizForm, compareFieldIds);
            if (this.CompareFields.IsNullOrEmpty())
            {
                this.WriteLog($"{this.TaskObject.TaskName}配置错误：【比较字段】不存在于业务对象{this.BizForm.Caption}！");
                return;
            }

            this.MQContext = this.UserContext.CreateSlaveDBContext(true, this.UserContext.TopCompanyId);

            var allEnqueueingBizObjs = GetEnqueueingBizObjs(this.BizForm);
            if (allEnqueueingBizObjs.IsNullOrEmpty())
            {
                this.WriteLog($"没有数据需要加入队列，没有符合【过滤条件】的范围内数据！", this.BizForm);
                return;
            }

            var companyGrps = allEnqueueingBizObjs.GroupBy(s => s.fmainorgid);
            decimal progress = 0;
            decimal allCompanys = companyGrps.Count();
            decimal index = 1;

            var mqsyncrecordMeta = this.MetaModelService.LoadFormModel(this.MQContext, "ms_mqsyncrecord");
            var mqsyncrecordDt = mqsyncrecordMeta.GetDynamicObjectType(this.MQContext);
            var bizDt = this.BizForm.GetDynamicObjectType(this.MQContext);
            var mqDm = this.MQContext.Container.GetService<IDataManager>();
            var bizDm = this.MQContext.Container.GetService<IDataManager>();
            mqDm.InitDbContext(this.MQContext, mqsyncrecordDt);
            bizDm.InitDbContext(this.MQContext, bizDt);

            var prepareSaveDataService =
                this.MQContext.Container.GetService<IPrepareSaveDataService>();

            var allCompanyInfos = this.MQContext.GetAllCompanys();

            foreach (var companyGrp in companyGrps)
            {
                string companyId = companyGrp.Key;

                var bizObjIds = companyGrp.Select(s => s.fid).ToList();
                var enqueuedNewestBizObjs = GetEnqueuedNewestBizObjs(this.BizForm, bizObjIds);
                var enqueueingBizObjs = bizDm.Select(bizObjIds).OfType<DynamicObject>().ToList();

                List<DynamicObject> savingMQSyncRecords = new List<DynamicObject>();

                foreach (var enqueueingBizObj in enqueueingBizObjs)
                {
                    var bizObjId = Convert.ToString(enqueueingBizObj["id"]);
                    var enqueuedNewestBizObj =
                        enqueuedNewestBizObjs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(bizObjId));

                    if (CanEnqueue(this.BizForm, enqueueingBizObj, enqueuedNewestBizObj))
                    {
                        var record = new DynamicObject(mqsyncrecordDt);

                        record["fbizformid"] = this.BizForm.Id;
                        record["fbizobjno"] = enqueueingBizObj[this.BizForm.NumberFldKey];
                        record["fbizobjid"] = enqueueingBizObj["id"];
                        record["fcreatedate"] = DateTime.Now;
                        record["fdescription"] = "";
                        record["fcompanyid"] = companyId;
                        //默认排队中
                        record["fsyncstatus"] = "0";
                        record["fsnapshot"] = enqueueingBizObj.ToJson();

                        savingMQSyncRecords.Add(record);

                        DebugUtil.WriteLogToFile(this.CompareDebugInfo, "MuSiMQEnqueueTask_Detail");

                        this.CompareDebugInfo = string.Empty;
                    }
                    else
                    {
                        DebugUtil.WriteLogToFile($"{this.BizForm.Caption}【{enqueueingBizObj[this.BizForm.NumberFldKey]}】没有变化", "MuSiMQEnqueueTask_Detail");

                        this.CompareDebugInfo = string.Empty;
                    }
                }

                allCompanyInfos.TryGetValue(companyId, out var dcInfo);

                if (savingMQSyncRecords.Any())
                {
                    prepareSaveDataService.PrepareDataEntity(this.MQContext, mqsyncrecordMeta, savingMQSyncRecords.ToArray(), OperateOption.Create());

                    mqDm.Save(savingMQSyncRecords);
                }

                progress = index / allCompanys;
                this.WriteLog($"[{dcInfo?.CompanyNumber}/{dcInfo?.CompanyName}]共{savingMQSyncRecords.Count}加入队列完成(总进度{progress:P})", this.BizForm);
                this.SaveLog();

                index++;

                this.SetTaskProgress(progress, $"[{dcInfo?.CompanyNumber}/{dcInfo?.CompanyName}]数据同步完成！");
            }

            this.SetTaskProgress(99, "全部数据加入队列完成！");
        }

        /// <summary>
        /// 获取待加入队列的业务对象
        /// </summary>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private List<MuSiMQBizObjModel> GetEnqueueingBizObjs(HtmlForm formMeta)
        {
            var models = new List<MuSiMQBizObjModel>(); 
            var maxsize = Convert.ToInt32(this.JobParameter?.fmaxsize);
            if (maxsize == 0) 
            {
                maxsize = 1000;
            }
            long maxId = 0;
            //处理前1000，消化完再取下个1000，分批处理
            using (var reader = this.MQContext.ExecuteReader($"select ISNULL(max(fid), 0) from (select top {maxsize} fid from T_MS_MQSYNCINGRECORD with(nolock) where fbizformid='{formMeta.Id}' order by fid asc) as tmp",
                new List<SqlParam>()))
            {
                if (reader.Read())
                {
                    maxId = reader.GetInt64(0);
                }
            }

            if (maxId == 0)
            {
                return models;
            }

            SqlBuilderParameter para = new SqlBuilderParameter(this.MQContext, formMeta);
            para.SelectedFieldKeys.Add("fmainorgid");
            para.PageCount = -1;
            para.PageIndex = -1;
            para.QueryUserFieldOnly = false;
            para.IsDistinct = true;
            para.NoIsolation = true;
            para.NoColorSetting = true;
            para.ReadDirty = true;
            para.IsShowForbidden = true;

            string fcondition = this.JobParameter?.fcondition.GetJsonValue<string>("id");
            if (!fcondition.IsNullOrEmptyOrWhiteSpace())
            {
                var filters = fcondition.FromJson<List<FilterRowObject>>();

                para.SetFilter(filters);
            }

            // 排队全量测试组织
            var testCompanyIds = this.MQContext.GetTestCompanyIds();
            if (testCompanyIds.Any())
            {
                para.AppendFilterString($"fmainorgid not in ({testCompanyIds.JoinEx(",", true)})");
            }

            para.AppendFilterString(
                $"exists(select 1 from T_MS_MQSYNCINGRECORD s with(nolock) where s.fid<={maxId} and s.fbizformid='{formMeta.Id}' and s.fbizobjid=t0.fid)");

            var queryObject = QueryService.BuilQueryObject(para);

            var dynObjs = this.MQContext.ExecuteDynamicObject(queryObject.Sql, para.DynamicParams).ToList();

            //// 记录SQL
            //StringBuilder log = new StringBuilder();
            //log.Append($"[{this.TaskObject?.TaskName}][{formMeta.Id}]执行语句：");

            //foreach (var sqlParam in para.DynamicParams)
            //{
            //    log.AppendLine();

            //    string name = sqlParam.Name.StartsWith("@") ? sqlParam.Name : "@" + sqlParam.Name;

            //    log.Append($"declare {name} nvarchar(100);");
            //    log.Append($"set {name}='{sqlParam.Value}'");
            //}

            //log.AppendLine();
            //log.Append(queryObject.Sql);

            //DebugLog(log.ToString());
            DebugLog($"待同步数量：{dynObjs.Count}");

            foreach (var dynObj in dynObjs)
            {
                models.Add(new MuSiMQBizObjModel
                {
                    fid = Convert.ToString(dynObj["fbillhead_id"]),
                    fmainorgid = Convert.ToString(dynObj["fmainorgid"])
                });
            }

            // 清空数据
            this.DbServiceEx.Execute(this.MQContext, $"delete from T_MS_MQSYNCINGRECORD where fid <= {maxId} and fbizformid='{formMeta.Id}'");

            return models;
        }

        /// <summary>
        /// 获取已在队列中的最新的业务对象
        /// </summary>
        /// <param name="formMeta"></param>
        /// <param name="bizObjIds"></param>
        /// <returns></returns>
        private List<DynamicObject> GetEnqueuedNewestBizObjs(HtmlForm formMeta, List<string> bizObjIds)
        {
            using (var tran = MQContext.CreateTransaction())
            {
                var tmpTableName = this.DBService.CreateTempTableWithDataList(this.MQContext, bizObjIds,false);

                try
                {
                    string sql = $@"
                                select t0.fid as id, t0.fbizformid, t0.fbizobjid, t0.fsnapshot from t_ms_mqsyncrecord t0
                                inner join 
                                (
                                    select max(fcreatedate) as fcreatedate, fbizobjid from t_ms_mqsyncrecord t1 
                                    where exists(select 1 from {tmpTableName} TMPXX where TMPXX.fid=t1.fbizobjid)
                                    and t1.fbizformid='{formMeta.Id}'
                                    group by t1.fbizobjid
                                ) tmp on t0.fcreatedate=tmp.fcreatedate and t0.fbizobjid=tmp.fbizobjid
                                ";
                    var enqueuedBizObjs = this.MQContext.ExecuteDynamicObject(sql, new List<SqlParam>());

                    tran.Complete();

                    var jArray = new JArray();

                    foreach (var bizObj in enqueuedBizObjs)
                    {
                        string fsnapshot = Convert.ToString(bizObj["fsnapshot"]);
                        if (fsnapshot.IsNullOrEmptyOrWhiteSpace())
                        {
                            fsnapshot = "{}";
                        }

                        var jObj = JObject.Parse(fsnapshot);

                        jArray.Add(jObj);
                    }

                    var dt = formMeta.GetDynamicObjectType(this.MQContext);
                    List<DynamicObject> lstDataEntities = new List<DynamicObject>();
                    var dcSerializer = this.MQContext.Container.GetService<IDynamicSerializer>();
                    dcSerializer.Sync(dt, lstDataEntities, jArray, neglectPkid: false, xssConvert: false);

                    return lstDataEntities;
                }
                catch (Exception ex)
                {
                    this.WriteLog("加入慕思消息队列计划任务快照还原失败：" + ex.Message, formMeta);

                    this.LogService?.Error("加入慕思消息队列计划任务快照还原失败", ex);

                    return new List<DynamicObject>();
                }
                finally
                {
                    this.DBService.DeleteTempTableByName(this.MQContext, tmpTableName, true);
                }
            }
        }

        /// <summary>
        /// 能否加入队列
        /// </summary>
        /// <param name="newDataEntity"></param>
        /// <param name="oldDataEntity"></param>
        /// <returns></returns>
        private bool CanEnqueue(HtmlForm formMeta, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            if (oldDataEntity == null)
            {
                this.CompareDebugInfo = $"{this.BizForm.Caption}【{newDataEntity[this.BizForm.NumberFldKey]}】新增";
                return true;
            }

            if (this.CompareFields.IsNullOrEmpty()) return false;

            // 判断单据头
            if (CanEnqueueWithHead(formMeta.HeadEntity, newDataEntity, oldDataEntity))
            {
                return true;
            }

            // 判断单据体、子单据体
            foreach (var entryEntity in formMeta.EntryList)
            {
                if (CanEnqueueWithEntry(entryEntity, newDataEntity, oldDataEntity))
                {
                    return true;
                }
            }

            return false;
        }

        private bool CanEnqueueWithHead(HtmlHeadEntity headEntity, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            // 判断单据头
            var headFlds = this.CompareFields.Where(s => s.EntityKey.EqualsIgnoreCase(headEntity.Id));
            foreach (var fld in headFlds)
            {
                if (!CompareFieldValue(fld, newDataEntity, oldDataEntity))
                {
                    this.CompareDebugInfo = $"{this.BizForm.Caption}【{newDataEntity[this.BizForm.NumberFldKey]}】变化字段：{fld.Caption}，newvalue={newDataEntity[fld.PropertyName]}，oldvalue={oldDataEntity[fld.PropertyName]}";
                    return true;
                }
            }

            return false;
        }

        private bool CanEnqueueWithEntry(HtmlEntryEntity entryEntity, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            var entryFlds = this.CompareFields.Where(s => s.EntityKey.EqualsIgnoreCase(entryEntity.Id));
            if (entryFlds.Count() == 0)
            {
                return false;
            }

            var newEntryRows = (DynamicObjectCollection)newDataEntity[entryEntity.PropertyName];
            var oldEntryRows = (DynamicObjectCollection)oldDataEntity[entryEntity.PropertyName];
            // 单据体数量不同，直接返回true
            if (newEntryRows.Count != oldEntryRows.Count)
            {
                this.CompareDebugInfo = $"{this.BizForm.Caption}【{newDataEntity[this.BizForm.NumberFldKey]}】单据体【{entryEntity.Caption}】数量变化：newvalue={newEntryRows.Count}，oldvalue={oldEntryRows.Count}";
                return true;
            }

            foreach (var newEntryRow in newEntryRows)
            {
                var enId = Convert.ToString(newEntryRow["id"]);
                var oldEntryRow =
                    oldEntryRows.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(enId));
                if (oldEntryRow == null)
                {
                    this.CompareDebugInfo = $"{this.BizForm.Caption}【{newDataEntity[this.BizForm.NumberFldKey]}】单据体【{entryEntity.Caption}】新增行：{enId}";
                    return true;
                }

                foreach (var fld in entryFlds)
                {
                    if (!CompareFieldValue(fld, newEntryRow, oldEntryRow))
                    {
                        this.CompareDebugInfo = $"{this.BizForm.Caption}【{newDataEntity[this.BizForm.NumberFldKey]}】单据体【{entryEntity.Caption}】变化字段：{fld.Caption}，newvalue={newEntryRow[fld.PropertyName]}，oldvalue={oldEntryRow[fld.PropertyName]}";
                        return true;
                    }
                }

                foreach (var subEntryEntity in entryEntity.SubEntryList)
                {
                    if (CanEnqueueWithEntry(subEntryEntity, newEntryRow, oldEntryRow))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private bool CompareFieldValue(HtmlField fld, DynamicObject newDataEntity, DynamicObject oldDataEntity)
        {
            // 数字字段特殊比较
            if (fld is HtmlDecimalField)
            {
                var newVal = Convert.ToDecimal(newDataEntity[fld.PropertyName]);
                var oldVal = Convert.ToDecimal(oldDataEntity[fld.PropertyName]);

                return newVal == oldVal;
            }
            else
            {
                var newVal = Convert.ToString(newDataEntity[fld.PropertyName]).Trim();
                var oldVal = Convert.ToString(oldDataEntity[fld.PropertyName]).Trim();

                return newVal.EqualsIgnoreCase(oldVal);
            }
        }

        /// <summary>
        /// 获取比较字段集合
        /// </summary>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private List<HtmlField> GetCompareFields(HtmlForm formMeta, string compareFieldIds)
        {
            var fldIds = compareFieldIds.SplitKey(",");

            List<HtmlField> flds = new List<HtmlField>();

            foreach (var fldId in fldIds)
            {
                var fld = formMeta.GetField(fldId);
                if (fld != null && !flds.Contains(fld))
                {
                    flds.Add(fld);
                }
            }

            return flds;
        }

        private void DebugLog(string message)
        {
            message = $"[{this.TaskObject.Id}_{this.TaskObject.TaskName}]{message}";

            DebugUtil.WriteLogToFile($"{message}", "MuSiMQEnqueueTask");
        }
    }

    public class MuSiMQEnqueueTaskParamter
    {
        public JObject fbizformid { get; set; }

        public JObject fcondition { get; set; }

        public string fcomparefieldids { get; set; }

        public int fbatchsize { get; set; }

        public int fmaxsize { get; set; }
    }

    internal class MuSiMQBizObjModel
    {
        public string fid { get; set; }

        public string fmainorgid { get; set; }
    }
}
