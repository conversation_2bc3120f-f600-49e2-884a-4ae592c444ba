using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 处理采购订单提交总部未发送请求
    /// </summary>
    [InjectService]
    [TaskSvrId("GetMSPurchaseOrder")]
    [Caption("处理采购订单提交总部未发送请求（在总部执行）")]
    [Browsable(false)]
    public class GetMSPurchaseOrderTask : AbstractScheduleWorker
    {
        /// <summary>
        /// 计划任务配置时的参数设置界面
        /// </summary>
        public override string ParamFormId => "ms_mspurchaseordertaskparam";

        /// <summary>
        /// 计划同步任务参数
        /// </summary>
        protected Dictionary<string, object> JobParameter { get; set; }

        protected IHttpServiceInvoker Gateway { get; set; }

        protected ILogServiceEx LogService { get; set; }
        /// <summary>
        /// 从库上下文（用于区分主库）
        /// </summary>
        protected UserContext MQContext { get; set; }

        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {
            this.ResetSysAdminUserContext();

            this.Gateway = this.UserContext.Container.GetService<IHttpServiceInvoker>();

            this.MQContext = this.UserContext.CreateSlaveDBContext(true, this.UserContext.TopCompanyId);

            this.JobParameter = Convert.ToString(this.TaskObject.Parameter)
                                    .FromJson<Dictionary<string, object>>()
                                ?? new Dictionary<string, object>();

            var auth = this.JobParameter.GetString("fauthorization");
            var startDateStr = this.JobParameter.GetString("fstartbilldate");
            var endDateStr = this.JobParameter.GetString("fendbilldate");
            //auth = "178c8a9b6a9b95fdb26370157921a0ec";
            var targetServer = GetTargetServer(auth);

            var meta = this.MetaModelService.LoadFormModel(this.UserContext, "ms_purchaseorder");
            var dt = meta.GetDynamicObjectType(this.UserContext);

            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.Option = OperateOption.Create();
            dm.Option.SetVariableValue("forceRecreate", true);
            dm.Option.SetAutoUpdateScheme(true);
            dm.InitDbContext(this.UserContext, dt);

            //if (DateTime.TryParse(startDateStr))

            DateTime startDate = DateTime.Parse("2024-03-01");
            DateTime endDate = DateTime.Parse("2024-04-04");

            DateTime currDate = startDate;
            //1、处理采购订单未同步到中台的
            this.SubmitHqPurOrders();

            //2、处理服务单未同步到中台的

            //因为中台未提供公共接口，所以后续偷偷处理从中台获取数据，token通过手动从前端传进来的方式
            //while (currDate < endDate)
            //{
            //    Excuse(targetServer, dm, dt, currDate, currDate.AddDays(1));

            //    currDate = currDate.AddDays(1);
            //}

        }

        /// <summary>
        /// 从集成操作日志中获取
        /// </summary>
        private List<DynamicObject> GetUnSubmitHqPurByLog()
        {
            // 排队全量测试组织
            var filter = " and 1=1 ";
            var testCompanyIds = this.MQContext.GetTestCompanyIds();
            if (testCompanyIds.Any())
            {
                filter = $" and fmainorgid not in ({testCompanyIds.JoinEx(",", true)})";
            }

            //取3个月前的日期，因为日志只保留近三个月的日期
            var date3M = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01")).AddMonths(-1).ToString("yyyy-MM-dd");
            var date7D = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-dd")).AddDays(-7).ToString("yyyy-MM-dd");
            var purs = new List<DynamicObject>();

            var strSql = $@" /*dialect*/
                            select distinct pur.fid,pur.fbillno,pur.fmainorgid,pur.frenewalflag 
                            from t_ydj_purchaseorder as pur with (nolock) 
                            --总部合同状态：提交至总部
                            where pur.fhqderstatus ='02' 
                            --查提交总部时间是近三个月的（集成日志未清理）
                            and fhqderdate > '{date3M}' 
                            --排除二级分销提交场景
                            and fonelvoderstatus  != '01'
                            and not exists (select 1 from t_si_operationlog (nolock)
                            where fopapi in ('/DRC-INTERFACE/derucci/purchaseOrder/push','/GENGEN-BASE-V2/derucci/purchaseOrder/push')  
                            and (fsuccessnumbers = pur.fbillno or fdescription = '中台已有重复采购订单,采购订单号为：'+ pur.fbillno)) {filter} ";
            //关联根据操作日志按提交总部时间查询集成操作日志
            strSql += $@" UNION ALL
                        /*dialect*/
                            select distinct pur.fbizobjid fid,pur.fbizobjno fbillno,pur.fmainorgid ,0 as frenewalflag
                            from T_YDJ_PURCHASEORDER_LG as pur with (nolock) 
                            --总部合同状态：提交至总部
                            where 
                            --查提交总部时间是近七天的（集成日志未清理）
                             pur.fopdate > '{date7D}' 
                            and pur.fopcode='submithq' 
                            AND exists (select 1 from T_YDJ_PURCHASEORDER po (nolock) where po.fid=pur.fbizobjid and po.fchangestatus=0 AND po.fhqderstatus ='02' and fonelvoderstatus!= '01' AND po.fhqderno='')
                            and not exists (select 1 from t_si_operationlog (nolock)
                            where fopapi in ('/DRC-INTERFACE/derucci/purchaseOrder/push','/GENGEN-BASE-V2/derucci/purchaseOrder/push') 
                            AND CONVERT(datetime, CONVERT(varchar(19), fcreatedate, 121))>= CONVERT(datetime, CONVERT(varchar(19), fopdate, 121))
                            and (fsuccessnumbers = pur.fbizobjno or fdescription = '中台已有重复采购订单,采购订单号为：'+ pur.fbizobjno)) {filter} ";
            //关联变更查询
            strSql += $@" UNION ALL
                        /*dialect*/
                            SELECT t1.fid,t1.fbillno,t1.fmainorgid,t1.frenewalflag
                        FROM T_YDJ_PURCHASEORDER t1 (nolock)
                        INNER JOIN (
                            SELECT MAX(fbillno) fbillno,fsourcenumber
                            FROM T_YDJ_PURCHASEORDER_CHG (nolock)
	                        where fstatus<>'E' AND fchangereason<>''
                            GROUP BY fsourcenumber
                        ) t2 ON t1.fbillno = t2.fsourcenumber
                        where t1.fchangestatus in ('1','3') AND t1.fhqderstatus='03' 
                        and fhqderdate > '{date3M}' 
                        and fonelvoderstatus  != '01'
                            and not exists (select 1 from t_si_operationlog (nolock)
                            where fopapi in ('/DRC-INTERFACE/derucci/purchaseOrder/alter','/GENGEN-BASE-V2/derucci/purchaseOrder/alter')  
                            and (fsuccessnumbers = t2.fbillno)) {filter} ";
            //关联焕新提交总部场景
            strSql += $@"UNION ALL
                        /*dialect*/
                        select distinct pur.fid,pur.fbillno,pur.fmainorgid,pur.frenewalflag
                        from t_ydj_purchaseorder as pur with (nolock) 
                        --焕新订单勾选提交总部
                        where ( pur.fissubmithq = 1)
                        --查提交总部时间是近三个月的（集成日志未清理）
                        and pur.fapprovedate > '{date3M}' 
                        --排除二级分销提交场景
                        and fonelvoderstatus  != '01'
                        and not exists (select 1 from t_si_operationlog (nolock)
                        where fopapi in ('/DRC-INTERFACE/derucci/purchaseOrder/headquarters/push','/GENGEN-BASE-V2/derucci/purchaseOrder/headquarters/push')  
                        and (fsuccessnumbers = pur.fbillno or fdescription = '中台已有重复采购订单,采购订单号为：'+ pur.fbillno)) ";
            //strSql = $@"
            //        select distinct pur.fid,pur.fbillno,pur.fmainorgid 
            //        from t_ydj_purchaseorder as pur with (nolock) 
            //        --总部合同状态：提交至总部
            //        where pur.fhqderstatus ='02' 
            //        --查提交总部时间是近三个月的（集成日志未清理）
            //        and fhqderdate > '2024-04-01' 
            //        --排除二级分销提交场景
            //        and fonelvoderstatus != '01'
            //        and not exists (select 1 from t_si_operationlog (nolock)
            //        where fopapi in ('/DRC-INTERFACE/derucci/purchaseOrder/push','/GENGEN-BASE-V2/derucci/purchaseOrder/push')  
            //        and fsuccessnumbers = pur.fbillno) 
            //        and fmainorgid <> '829653207454318616' 
            //        union
            //        --补充第一次提交总部再驳回后二次提交的情况没有二次请求的情况 
            //        select fid,fbillno,fmainorgid,count_si,count_op from (
            //        select pur.fid,pur.fbillno,pur.fmainorgid
            //        ,(select count(0) from t_si_operationlog (nolock) where fsuccessnumbers = pur.fbillno and foptime > '2024-04-01' and fopapi ='/DRC-INTERFACE/derucci/purchaseOrder/push' ) as count_si
            //        ,(select count(0) from t_ydj_purchaseorder_lg as lg (nolock) where fbizobjid = pur.fid and fdetail =''  and fopdate > '2024-04-01' and fopcode='Submithq' collate Chinese_PRC_CS_AS) as count_op
            //        from t_ydj_purchaseorder as pur with (nolock) 
            //        where pur.fhqderstatus ='02' 
            //        and fhqderdate > '2024-04-01' 
            //        and fonelvoderstatus != '01'
            //        --操作日志提交总部多次的
            //        and exists (select 1 from  T_YDJ_PURCHASEORDER_LG as lg with (nolock) where fbizobjid = pur.fid and fopcode='Submithq' collate Chinese_PRC_CS_AS group by fbizobjid having count(0) >1)
            //        ) as a where 
            //        --比较提交总部的次数和集成操作日志提交总部的次数
            //         count_si < count_op";
            purs = this.MQContext.ExecuteDynamicObject(strSql, null).ToList();

            return purs;
        }

        /// <summary>
        /// 从集成操作日志中获取服务单未及时同步到中台的数据
        /// </summary>
        private List<DynamicObject> GetUnSyncServiceByLog()
        {
            // 排队全量测试组织
            var filter = " and 1=1 ";
            var testCompanyIds = this.MQContext.GetTestCompanyIds();
            if (testCompanyIds.Any())
            {
                filter = $" and fmainorgid not in ({testCompanyIds.JoinEx(",", true)})";
            }

            //取3个月前的日期，因为日志只保留近三个月的日期
            var date3M = DateTime.Parse(DateTime.Now.ToString("yyyy-MM-01")).AddMonths(-2).ToShortDateString();
            var purs = new List<DynamicObject>();

            return purs;
        }

        private void SubmitHqPurOrders()
        {
            var purs = GetUnSubmitHqPurByLog();
            if (purs == null || purs.Count == 0)
            {
                this.WriteLog("未找到需要需要提交到中台的数据");
                return;
            }
            Dictionary<string, List<DynamicObject>> data = purs.GroupBy(x => x["fmainorgid"]).ToDictionary(g => g.Key.ToString(), g => g.ToList());

            foreach (var item in data)
            {
                var agentCtx = this.MQContext.CreateDBContextByCompanyId(item.Key);
                var lstSelRows = item.Value
                    .Where(o=> Convert.ToString(o["frenewalflag"]).ToLower().EqualsIgnoreCase("false") || Convert.ToString(o["frenewalflag"]).EqualsIgnoreCase("0"))
                    .Select(o => new SelectedRow()
                {
                    PkValue = o["fid"] as string
                }).ToArray();

                var lstSelRows_Renew = item.Value
                    .Where(o => Convert.ToString(o["frenewalflag"]).ToLower().EqualsIgnoreCase("true") || Convert.ToString(o["frenewalflag"]).EqualsIgnoreCase("1"))
                    .Select(o => new SelectedRow()
                    {
                        PkValue = o["fid"] as string
                    }).ToArray();

                Dictionary<string, object> option = new Dictionary<string, object>();
                option.Add("IgnoreCheckPermssion", "true");//去掉权限校验
                option.Add("istopoper", "1");
                if (lstSelRows.Any()) 
                {
                    var result = this.Gateway.InvokeListOperation(agentCtx, "ydj_purchaseorder", lstSelRows, "SubmitHQ", option);

                    var billnos = item.Value.Where(o => Convert.ToString(o["frenewalflag"]).ToLower().EqualsIgnoreCase("false") || Convert.ToString(o["frenewalflag"]).EqualsIgnoreCase("0")).Select(o => Convert.ToString(o["fbillno"])).ToList();
                    if (!result.IsSuccess)
                    {
                        this.WriteLog($"采购订单{billnos.JoinEx(",", false)} 提交总部失败");
                        return;
                    }
                    if (billnos.Count() > 0)
                    {
                        this.WriteLog($" 采购订单{billnos.JoinEx(",", false)} 提交总部成功");
                    }
                }

                if (lstSelRows_Renew.Any())
                {
                    option.Add("IgnoreValidateDataEntities", "true"); 
                    var result = this.Gateway.InvokeListOperation(agentCtx, "ydj_purchaseorder", lstSelRows_Renew, "renewsubmithq", option);

                    var billnos = item.Value.Where(o => Convert.ToString(o["frenewalflag"]).ToLower().EqualsIgnoreCase("true") || Convert.ToString(o["frenewalflag"]).EqualsIgnoreCase("1"))
                        .Select(o => Convert.ToString(o["fbillno"])).ToList();
                    if (!result.IsSuccess)
                    {
                        this.WriteLog($"焕新采购订单{billnos.JoinEx(",", false)} 提交总部失败");
                        return;
                    }
                    if (billnos.Count() > 0)
                    {
                        this.WriteLog($"焕新采购订单{billnos.JoinEx(",", false)} 提交总部成功");
                    }
                }
            }
        }


        private static void Excuse(TargetServer targetServer, IDataManager dm, DynamicObjectType dt, DateTime startDate, DateTime endDate)
        {
            var totalRecords = GetTotalRecords(targetServer, startDate, endDate);

            var pageSize = 1000;
            var totalPages = Convert.ToInt32(Math.Ceiling(totalRecords * 1.0 / pageSize));

            for (int pageIndex = 1; pageIndex <= totalPages; pageIndex++)
            {
                string url = "/v2/bmeBo/drc_purchase_order/pageList";

                var jsonString =
                    "{\"pageIndex\":" + pageIndex + ",\"pageSize\":" + pageSize + ",\"deleteFlag\":\"0\",\"orderbyField\":{\"crtDate\":\"DESC\"},\"executeType\":\"release\",\"searchField\":[{\"search_link\":\"and\",\"search_term\":\">=\",\"search_field\":\"crtDate\",\"search_value\":" +
                    startDate.Timestamp() +
                    "},{\"search_link\":\"and\",\"search_term\":\"<=\",\"search_field\":\"crtDate\",\"search_value\":" +
                    endDate.Timestamp() +
                    "}],\"queryString\":null,\"kdSaletoCode\":null,\"syncStatus\":null,\"count\":\"8000\",\"orderStatus\":null}";

                var requestJson = JObject.Parse(jsonString);

                var resp = JsonClient.InvokeThirdByJson<object, PurOrderResponse>(
                    targetServer,
                    url,
                    requestJson);

                if (resp.state && resp.data?.records != null)
                {
                    int totalCount = resp.data.records.Count;
                    int perCount = 100;

                    int loopCount = Convert.ToInt32(Math.Ceiling(totalCount * 1.0 / perCount));

                    for (int i = 0; i < loopCount; i++)
                    {
                        var records = resp.data.records.Skip(i * perCount).Take(perCount);

                        var ids = records.Select(s => s.GetJsonValue("id", ""));

                        var purchaseOrders = dm.Select(ids).OfType<DynamicObject>().ToList();

                        foreach (var record in records)
                        {
                            var id = record.GetJsonValue("id", "");
                            if (id.IsNullOrEmptyOrWhiteSpace())
                            {
                                continue;
                            }

                            var purchaseOrder =
                                purchaseOrders.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(id));
                            if (purchaseOrder == null)
                            {
                                purchaseOrder = dt.CreateInstance() as DynamicObject;
                                purchaseOrder["id"] = id;
                                purchaseOrders.Add(purchaseOrder);
                            }

                            purchaseOrder["fbillno"] = record.GetJsonValue("code", "");
                            purchaseOrder["ftype"] = record.GetJsonValue("type", "");
                            var status = record.GetJsonValue("status", "");
                            purchaseOrder["fstatus"] = status;

                            // '01':'新建','02':'提交至总部','03':'已终审','04':'排产中','05':'驳回'
                            switch (status)
                            {
                                case "提交至总部":
                                    purchaseOrder["fhqderstatus"] = "02";
                                    break;
                                case "已终审":
                                    purchaseOrder["fhqderstatus"] = "03";
                                    break;
                                case "驳回":
                                    purchaseOrder["fhqderstatus"] = "05";
                                    break;
                            }

                            purchaseOrder["fshiptocode"] = record.GetJsonValue("shipToCode", "");
                            purchaseOrder["fsaletocode"] = record.GetJsonValue("saletoCode", "");
                            purchaseOrder["fchangestatus"] = record.GetJsonValue("changeStatus", "");
                            var crtDateStr = record.GetJsonValue("crtDate", "").ToString();
                            if (!crtDateStr.IsNullOrEmptyOrWhiteSpace())
                            {
                                var crtDate = double.Parse(crtDateStr);
                                purchaseOrder["fcreatedate"] = DateTimeUtil.GetBeiJingTime(crtDate);
                            }

                            purchaseOrder["fjson"] = record.ToString();
                        }

                        dm.Save(purchaseOrders);
                    }
                }
            }
        }

        private static int GetTotalRecords(TargetServer targetServer, DateTime startDate, DateTime endDate)
        {
            string url = "/v2/bmeBo/drc_purchase_order/pageList";

            var jsonString =
                "{\"pageIndex\":1,\"pageSize\":1,\"deleteFlag\":\"0\",\"orderbyField\":{\"crtDate\":\"DESC\"},\"executeType\":\"release\",\"searchField\":[{\"search_link\":\"and\",\"search_term\":\">=\",\"search_field\":\"crtDate\",\"search_value\":" +
                startDate.Timestamp() +
                "},{\"search_link\":\"and\",\"search_term\":\"<=\",\"search_field\":\"crtDate\",\"search_value\":" +
                endDate.Timestamp() +
                "}],\"queryString\":null,\"kdSaletoCode\":null,\"syncStatus\":null,\"count\":\"8000\",\"orderStatus\":null}";

            var requestJson = JObject.Parse(jsonString);

            var resp = JsonClient.InvokeThirdByJson<object, PurOrderResponse>(
                targetServer,
                url,
                requestJson);

            return resp.data.totalRecord;
        }

        /// <summary>
        /// 获取服务地址
        /// </summary>
        /// <returns></returns>
        private TargetServer GetTargetServer(string auth)
        {
            var targetServer = new TargetServer();
            //正式
            targetServer.Host = "https://gggateway.derucci.com";
            //测试
            targetServer.Host = "http://wx.derucci.com:9088";

            targetServer.Headers["Authorization"] = auth;

            return targetServer;
        }


        /// <summary>
        /// 重置为系统管理员
        /// </summary>
        private void ResetSysAdminUserContext()
        {
            UserAuthTicket session = new UserAuthTicket();

            // 用系统预设的管理员身份操作
            session.UserId = "sysadmin";
            session.DisplayName = "系统管理员";
            session.UserName = "系统管理员";

            session.Product = this.UserContext.Product;
            session.Company = this.UserContext.Company;
            session.BizOrgId = this.UserContext.Company;
            session.TopCompanyId = this.UserContext.TopCompanyId;
            session.ParentCompanyId = this.UserContext.ParentCompanyId;
            session.Companys = this.UserContext.Companys.ToList();
            session.Id = this.UserContext.Id;

            this.UserContext.SetUserSession(session);
        }
    }

    public class PurOrderResponse
    {
        public bool state { get; set; }
        public string code { get; set; }
        public string msg { get; set; }
        public string log { get; set; }
        public Data data { get; set; }
        public class Data
        {
            public int totalPage { get; set; }
            public int totalRecord { get; set; }
            public List<JObject> records { get; set; }
        }
    }
}