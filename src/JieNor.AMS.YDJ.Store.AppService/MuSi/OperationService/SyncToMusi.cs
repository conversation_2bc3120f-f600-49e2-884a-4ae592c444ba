using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Enums;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.OperationService
{
    /// <summary>
    /// 慕思同步
    /// </summary>
    [InjectService("synctomusi")]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    public class SyncToMusi : AbstractOperationService
    {
        /// <summary>
        /// 表单模型
        /// </summary>
        protected HtmlForm HtmlForm => this.OperationContext.HtmlForm;

        protected override void InitializeService()
        {
            base.InitializeService();

            this.OperationContext.Option.SetIgnoreOpLogFlag();
        }

        /// <summary>
        /// 初始化操作参数，禁用事务
        /// </summary>
        protected override void InitializeOperationDataEntities(ref DynamicObject[] dataEntities)
        {
            this.OpCtlParam.DisableTransaction = true;

            base.InitializeOperationDataEntities(ref dataEntities);
        }

        /// <summary>
        /// 将请求转发至服务上进行处理
        /// </summary>
        /// <param name="lstOpServices"></param>
        protected override void PrepareBusinessService(List<FormServiceDesc> lstOpServices)
        {
            base.PrepareBusinessService(lstOpServices);

            //可能计划任务定时调用时传递过来的
            var fieldMapObj = this.GetQueryOrSimpleParam<DynamicObject>("__fieldMapObj__");
            if (fieldMapObj == null)
            {
                var fieldMapObjId = this.GetQueryOrSimpleParam<string>("fieldMapObjId");
                if (false == string.IsNullOrWhiteSpace(fieldMapObjId))
                {
                    fieldMapObj = this.UserCtx.LoadBizDataById("si_musibizobjmap", fieldMapObjId);
                }
            }

            List<DynamicObject> fieldMapObjs = new List<DynamicObject>();

            if (fieldMapObj != null)
            {
                fieldMapObjs.Add(fieldMapObj);
            }
            else
            {
                fieldMapObjs = this.Container.GetService<IMuSiBizObjMapService>().GetBizObjMaps(this.UserCtx,
                  this.HtmlForm, Enu_MuSiSyncDir.CurrentToMuSi, Enu_MuSiSyncTimePoint.SyncManual);
            }

            foreach (var item in fieldMapObjs)
            {
                var billMapId = Convert.ToString(item["id"]);
                var extAppId = Convert.ToString(item["fextappid"]);
                var filterStr = Convert.ToString(item["ffilterstring"]);
                if (billMapId.IsNullOrEmptyOrWhiteSpace() || extAppId.IsNullOrEmptyOrWhiteSpace())
                {
                    return;
                }

                lstOpServices.Add(new FormServiceDesc()
                {
                    ServiceId = YDJHtmlElementType.HtmlBizService_MuSiSysSync,
                    ServiceAlias = "慕思系统数据集成服务",
                    ParamString = $"{{'extAppId':'{extAppId}','billMapId':'{billMapId}'}}",
                    Condition = filterStr
                });
            }
        }

        /// <summary>
        /// 操作执行过程
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {

        }
    }
}
