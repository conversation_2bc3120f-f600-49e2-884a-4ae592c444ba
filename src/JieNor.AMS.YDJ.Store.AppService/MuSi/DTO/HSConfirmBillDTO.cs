using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.DTO
{
    public class HSConfirmBillDTO : BaseDTO
    {
        public HSConfirmBillDTO()
        {

        }

        /// <summary>
        /// 明细实体列表
        /// </summary>
        /// <param name="dynObjs"></param>
        public HSConfirmBillDTO(IEnumerable<DynamicObject> dynObjs)
        {
            if (dynObjs != null)
            {
                foreach (var dynObj in dynObjs)
                {
                    this.Entrys.Add(new HSDConfirmBillEntryData
                    {
                        id = Convert.ToString(dynObj["fbillno"])
                    });
                }
            }
        }

        public List<HSDConfirmBillEntryData> Entrys { get; set; } = new List<HSDConfirmBillEntryData>();


        public class HSDConfirmBillEntryData
        {

            /// <summary>
            /// 对账单编码
            /// </summary>
            public string id { get; set; }

            /// <summary>
            /// 汇款经销商备注
            /// </summary>
            public string dealerCommentsSum { get; } = string.Empty;

            /// <summary>
            /// 出货经销商备注
            /// </summary>
            public string dealerCommentsSale { get; } = string.Empty;

            /// <summary>
            /// 退货经销商备注
            /// </summary>
            public string dealerCommentsReturn { get; } = string.Empty;

            /// <summary>
            /// 维修经销商备注
            /// </summary>
            public string dealerCommentsRepair { get; } = string.Empty;

            /// <summary>
            /// 借贷项经销商备注
            /// </summary>
            public string dealerCommentsBorrow { get; } = string.Empty;

            /// <summary>
            /// 手工调整经销商备注
            /// </summary>
            public string dealerCommentsManual { get; } = string.Empty;

            /// <summary>
            /// 0：待对账(提交总部时，传0)，1：对账相符，2：撤销对账
            /// </summary>
            public string status { get; } = "1";
        }

        /// <summary>
        /// 获取数据包里的编码
        /// </summary>
        /// <returns></returns>
        public override List<string> GetNumbers()
        {
            return Entrys?.Select(s => s.id)?.Distinct()?.ToList();
        }
    }
}
