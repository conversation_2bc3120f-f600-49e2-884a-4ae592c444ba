using System.Collections.Generic;
using System.ComponentModel;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration
{
    /// <summary>
    /// 向第三方系统发送请求前事件参数对象
    /// </summary>
    public class BeforeSendEventArgs : CancelEventArgs
    {
        /// <summary>
        /// 字段映射对象
        /// </summary>
        public DynamicObject FieldMapObject { get; set; }

        /// <summary>
        /// 当前待同步的单据数据包
        /// </summary>
        public IEnumerable<DynamicObject> DataEntitys { get; set; }

        /// <summary>
        /// 结果
        /// </summary>
        public object Result { get; set; }

        /// <summary>
        /// 动态输入参数
        /// </summary>
        public Dictionary<string, string> InputArgs { get; set; }
        /// <summary>
        /// 接口参数
        /// </summary>
        public Dictionary<string, object> RuquestData { get;set;}
    }
}