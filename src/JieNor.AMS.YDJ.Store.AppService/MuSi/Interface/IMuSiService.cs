using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Interface
{
    /// <summary>
    /// 慕思服务接口定义
    /// </summary>
    public interface IMuSiService
    {
        /// <summary>
        /// 写入协同订单日志
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="billIds">单据ids</param>
        /// <param name="opCode">操作编号</param>
        /// <param name="opName">操作名称</param>
        /// <param name="content">日志内容</param>
        /// <param name="caller">请求来源对象</param>
        /// <param name="opDate">操作日期</param>
        void WriteLog(UserContext userCtx, HtmlForm htmlForm, string billIds, string opCode, string opName,
            string content, CallerContext caller = null, DateTime? opDate = null);

        /// <summary>
        /// 获取服务地址
        /// </summary>
        /// <param name="extApp">外部应用</param>
        /// <returns></returns>
        TargetServer GetTargetServer(DynamicObject extApp);

        /// <summary>
        /// 刷新Token
        /// </summary>
        /// <param name="extApp"></param>
        TargetServer RefreshToken(DynamicObject extApp);

        /// <summary>
        /// 获取外部应用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="extAppId"></param>
        /// <returns></returns>
        DynamicObject GetExternalAppObject(UserContext userCtx, string extAppId);

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        Task SyncAsync(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        Task OMSSyncAsync(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);
        /// <summary>
        /// 生成专供商品授权清单
        /// </summary>
        /// <param name="context"></param>
        /// <param name="formid"></param>
        /// <param name="ProductAuthObjs_deliver"></param>
        void SaveProductAuth_other(UserContext context, string formid, DynamicObjectCollection ProductAuthObjs_deliver);

        void SaveToSubMitDataTable(UserContext context, string formid, IEnumerable<DynamicObject> DataObjs,bool AfterSend);

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        IOperationResult Sync(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据-焕新
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        IOperationResult SyncReNew(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据-焕新订单同步
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        IOperationResult SyncReNewOrder(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <param name="formId"></param>
        /// <param name="filterName"></param>
        /// <returns></returns>
        IOperationResult SyncData(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites, string formId, string filterName);

        /// <summary>
        /// 同步数据-收款单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntites"></param>
        /// <returns></returns>
        IOperationResult SyncReNewIncome(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据-焕新订单OMS
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        IOperationResult OMSSyncReNewOrder(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);
        
        /// <summary>
        /// 同步数据-终端销售合同(直营)
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">销售合同表单</param>
        /// <param name="dataEntites">销售合同实体</param>
        /// <returns></returns>
        IOperationResult SyncNormalOrder(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据-终端销售合同(直营)共享计提单号
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntites"></param>
        /// <returns></returns>
        IOperationResult SyncDirectOrderShareCostBillNo(UserContext userCtx,HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据-焕新OMS
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        IOperationResult OMSSyncReNew(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);
        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">表单</param>
        /// <param name="dataEntites">数据实体</param>
        /// <returns></returns>
        IOperationResult OMSSync(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentIds">经销商</param>
        /// <param name="formIds">同步的业务对象</param>
        /// <returns></returns>
        Task SyncByAgentAsync(UserContext userCtx, IEnumerable<string> agentIds, IEnumerable<string> formIds);

        /// <summary>
        /// 同步数据
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="agentIds">经销商</param>
        /// <param name="formIds">同步的业务对象</param>
        /// <returns></returns>
        IOperationResult SyncByAgent(UserContext userCtx, IEnumerable<string> agentIds, IEnumerable<string> formIds);

        /// <summary>
        /// 同步数据-销售退货单(直营)
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="htmlForm">销售合同表单</param>
        /// <param name="dataEntites">销售合同实体</param>
        /// <returns></returns>
        IOperationResult SyncSoStockReturn(UserContext userCtx, HtmlForm htmlForm, IEnumerable<DynamicObject> dataEntites);
    }
}
