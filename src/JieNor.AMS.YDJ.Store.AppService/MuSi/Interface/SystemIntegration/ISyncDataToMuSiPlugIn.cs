using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn; 

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration
{
    /// <summary>
    /// 同步数据到慕思中台插件
    /// </summary>
    public interface ISyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        void OnCustomServiceEvent(OnCustomServiceEventArgs e);

        /// <summary>
        /// 向第三方系统发送请求前事件（可干预请求参数）
        /// </summary>
        /// <param name="e"></param>
        void BeforeSendEvent(BeforeSendEventArgs e);

        /// <summary>
        /// 来源单据打包前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforePackSourceBill(BeforePackSourceBillEventArgs e);

        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforeFieldMapping(BeforeFieldMappingEventArgs e); 

        /// <summary>
        /// 来源单据打包后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterPackSourceBill(AfterPackSourceBillEventArgs e);

        /// <summary>
        /// 向第三方系统发送请求后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterSendEvent(AfterSendEventArgs e);

        /// <summary>
        /// 向第三方系统发送请求后，解析第三方接口返回值事件
        /// </summary>
        /// <param name="e"></param>
        void ParseResult(ParseResultEventArgs e); 
    }
}
