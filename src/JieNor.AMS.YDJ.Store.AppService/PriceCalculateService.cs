using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService
{
    /// <summary>
    /// 综合价目表计算
    /// </summary>
    [InjectService]
    public class PriceCalculateService
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IDBServiceEx DBServiceEx { get; set; }
        public IOperationResult PriceCalculate(UserContext userCtx, string temptable, OperateOption option, DynamicObjectCollection data = null)
        {
            var result = userCtx.Container.GetService<IOperationResult>();
            var dbService = userCtx.Container.GetService<IDBService>();
            var priceTableName = string.Empty;
            DataTable dt = new DataTable();
            try
            {
                var rowIds = new List<string>();
                option.TryGetVariableValue("UpdatePrice_RowIds", out rowIds);
                if (rowIds != null && rowIds.Count > 0)
                {
                    if (data == null || data.Count == 0) return result;
                }
                else
                {
                    if (temptable.IsNullOrEmptyOrWhiteSpace()) return result;
                    //查询经销商商品数据
                    var querySql = $@"/*dialect*/select t0.fmaterialid,t0.fattrinfo_e,t0.fcustomdesc,t0.funitid,max(t0.fproductorgid) fproductorgid,
                                            0*1.0 fpurfacprice,0*1.0 fpurdealprice,0*1.0 funifysaleprice,0*1.0 fsellprice,0*1.0 freprice,0*1.0 fterprice,0*1.0 funitcostprice 
                                            from {temptable} as t0 with(nolock)  
                                            group by t0.fmaterialid,t0.fattrinfo_e,t0.fcustomdesc,t0.funitid ";
                    data = dbService.ExecuteDynamicObject(userCtx, querySql);
                }

                #region 采购相关
                //查询企业采购订单商品维度最新大于0的【采购单价】
                var strSql = $@"/*dialect*/select * from ( 
                            select row_number() over(partition by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e,t1.funitid order by t2.fcreatedate desc) as count,
                            t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e fcustomdesc,t1.funitid,t1.fprice from t_ydj_poorderentry t1 with(nolock)
                            left join t_ydj_purchaseorder t2 with(nolock) on t1.fid=t2.fid 
                            where t1.fprice>0 and t2.fmainorgid='{userCtx.Company}' and t2.frenewalflag='0' 
                            ) tab where tab.count=1";
                var fpurpriceDatasX = dbService.ExecuteDynamicObject(userCtx, strSql);
                var fpurpriceDatas = fpurpriceDatasX.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fprice = Convert.ToDecimal(x["fprice"])
                }).ToList();
                //查询企业采购订单商品维度最新大于0的【成交单价】
                strSql = $@"/*dialect*/select * from ( 
                            select row_number() over(partition by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e,t1.funitid order by t2.fcreatedate desc) as count,
                            t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e fcustomdesc,t1.funitid,t1.fdealprice from t_ydj_poorderentry t1 with(nolock)
                            left join t_ydj_purchaseorder t2 with(nolock) on t1.fid=t2.fid  
                            where t1.fdealprice>0 and t2.fmainorgid='{userCtx.Company}' and t2.frenewalflag='0' 
                            ) tab where tab.count=1";
                var fpurdealpriceDatasX = dbService.ExecuteDynamicObject(userCtx, strSql);
                var fpurdealpriceDatas = fpurdealpriceDatasX.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fdealprice = Convert.ToDecimal(x["fdealprice"])
                }).ToList();

                //查询企业采购价目商品维度最新的【采购单价】
                var fmainorgids = userCtx.IsSecondOrg ? $@"'{userCtx.Company}'" : $@"'{userCtx.Company}','{userCtx.TopCompanyId}'";//二级分销不能看总部采购价
                strSql = $@"/*dialect*/select * from ( 
                            select row_number() over(partition by t1.fproductid_e,t1.fattrinfo_e,t1.funitid_e order by t1.fconfirmdate desc) as count,
                            t1.fproductid_e fmaterialid,t1.fattrinfo_e,t1.funitid_e funitid,t1.fpurprice from t_ydj_purchasepriceentry t1 with(nolock)
                            left join t_ydj_purchaseprice t2 with(nolock) on t1.fid=t2.fid  
                            where t1.fconfirmstatus=2 and t2.fmainorgid in ({fmainorgids})
                            and t1.fstartdate_e <= getdate() and t1.fexpiredate_e >= getdate() --日期
                            ) tab where tab.count=1";
                var purPriceDatasX = dbService.ExecuteDynamicObject(userCtx, strSql);
                var purPriceDatas = purPriceDatasX.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fpurprice = Convert.ToDecimal(x["fpurprice"])
                }).ToList();

                //查询企业盘点单商品维度最新的【盘点单价】
                strSql = $@"/*dialect*/select * from(
                            select row_number() over(partition by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid order by t2.fcreatedate desc) as count,
                            t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t1.fbizpdprice from t_stk_invverifyentry t1 with(nolock)
                            left join t_stk_invverify t2 with(nolock) on t1.fid = t2.fid 
                            where t1.fbizpdprice>0 and t2.fmainorgid='{userCtx.Company}'
                            ) tab where tab.count = 1";
                var invverifyDatasX = dbService.ExecuteDynamicObject(userCtx, strSql);
                var invverifyDatas = invverifyDatasX.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fbizpdprice = Convert.ToDecimal(x["fbizpdprice"])
                }).ToList();
                #endregion

                #region 销售相关
                //查询企业销售合同商品维度最新大于0的【零售价】
                strSql = $@"/*dialect*/select * from ( 
                            select row_number() over(partition by t1.fproductid,t1.fattrinfo_e,t1.fcustomdes_e,t1.funitid,t2.fisresellorder order by t2.fcreatedate desc) as count,
                            t1.fproductid fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e fcustomdesc,t1.funitid,t1.fprice,t1.fhqprice,fisresellorder from t_ydj_orderentry t1 with(nolock)
                            left join t_ydj_order t2 with(nolock) on t1.fid=t2.fid 
                            where t1.fprice>0 and t2.fmainorgid='{userCtx.Company}'
                            ) tab where tab.count=1";
                var forderpriceDatasX = dbService.ExecuteDynamicObject(userCtx, strSql);
                var forderpriceDatas = forderpriceDatasX.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fprice = Convert.ToDecimal(x["fprice"]),
                    fhqprice = Convert.ToDecimal(x["fhqprice"]),
                    fisresellorder = x["fisresellorder"].ToString()
                }).ToList();
                //查询企业销售价目商品维度最新的【统一零售价】
                strSql = $@"/*dialect*/select * from ( 
                            select row_number() over(partition by t1.fproductid,t1.fattrinfo_e,t1.funitid,t2.ftype order by t1.fconfirmdate desc) as count,
                            t1.fproductid fmaterialid,t1.fattrinfo_e,t1.funitid,t1.fsalprice,t2.ftype from t_ydj_priceentry t1 with(nolock)
                            left join t_ydj_price t2 with(nolock) on t1.fid=t2.fid 
                            where t1.fconfirmstatus=2 and t2.fmainorgid in ('{userCtx.Company}','{userCtx.ParentCompanyId}','{userCtx.TopCompanyId}')
                            and t1.fstartdate <= getdate() and t1.fexpiredate >= getdate() --日期
                            ) tab where tab.count=1";
                var orderPriceDatasX = dbService.ExecuteDynamicObject(userCtx, strSql);
                var orderPriceDatas = orderPriceDatasX.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fsalprice = Convert.ToDecimal(x["fsalprice"]),
                    ftype = x["ftype"].ToString()
                }).ToList();
                #endregion

                #region 库存相关
                //查询企业库存综合查询商品维度最新的【单位成本】
                strSql = $@"/*dialect*/select * from ( 
                            select row_number() over(partition by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid order by t1.fmaterialid desc) as count,
                            t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,fcostprice from t_stk_inventorylist as t1 with(nolock)  
                            where fqty>0 and t1.fmainorgid='{userCtx.Company}'
                            ) tab where tab.count=1";
                var inventoryDatasX = dbService.ExecuteDynamicObject(userCtx, strSql);
                var inventoryDatas = inventoryDatasX.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fcostprice = Convert.ToDecimal(x["fcostprice"])
                }).ToList();
                #endregion
                var addData = data.Select(x => new
                {
                    fmaterialid = x["fmaterialid"].ToString(),
                    fattrinfo_e = Convert.ToString(x["fattrinfo_e"]).Trim(),
                    fcustomdesc = Convert.ToString(x["fcustomdesc"]).Trim(),
                    funitid = x["funitid"].ToString(),
                    fproductorgid = x["fproductorgid"].ToString()
                }).ToList();

                var saveDatas = (from a in addData
                                 join b in fpurpriceDatas on new { fmaterialid = a.fmaterialid, fattrinfo_e = a.fattrinfo_e, fcustomdesc = a.fcustomdesc, funitid = a.funitid }
                                 equals new { fmaterialid = b.fmaterialid, fattrinfo_e = b.fattrinfo_e, fcustomdesc = b.fcustomdesc, funitid = b.funitid } into tempB
                                 from bn in tempB.DefaultIfEmpty()
                                 join c in purPriceDatas on new { fmaterialid = a.fmaterialid, fattrinfo_e = a.fattrinfo_e, funitid = a.funitid }
                                 equals new { fmaterialid = c.fmaterialid, fattrinfo_e = c.fattrinfo_e, funitid = c.funitid } into tempC
                                 from cn in tempC.DefaultIfEmpty()
                                 join d in fpurdealpriceDatas on new { fmaterialid = a.fmaterialid, fattrinfo_e = a.fattrinfo_e, fcustomdesc = a.fcustomdesc, funitid = a.funitid }
                                 equals new { fmaterialid = d.fmaterialid, fattrinfo_e = d.fattrinfo_e, fcustomdesc = d.fcustomdesc, funitid = d.funitid } into tempD
                                 from dn in tempD.DefaultIfEmpty()
                                 join e in invverifyDatas on new { fmaterialid = a.fmaterialid, fattrinfo_e = a.fattrinfo_e, fcustomdesc = a.fcustomdesc, funitid = a.funitid }
                                 equals new { fmaterialid = e.fmaterialid, fattrinfo_e = e.fattrinfo_e, fcustomdesc = e.fcustomdesc, funitid = e.funitid } into tempE
                                 from en in tempE.DefaultIfEmpty()
                                 join f in inventoryDatas on new { fmaterialid = a.fmaterialid, fattrinfo_e = a.fattrinfo_e, fcustomdesc = a.fcustomdesc, funitid = a.funitid }
                                 equals new { fmaterialid = f.fmaterialid, fattrinfo_e = f.fattrinfo_e, fcustomdesc = f.fcustomdesc, funitid = f.funitid } into tempF
                                 from fn in tempF.DefaultIfEmpty()
                                 select new
                                 {
                                     fmaterialid = a.fmaterialid,
                                     fattrinfo_e = a.fattrinfo_e, 
                                     fcustomdesc = a.fcustomdesc,
                                     funitid = a.funitid,
                                     fproductorgid = a.fproductorgid,
                                     fpurpriceData_fprice = bn == null ? 0.00M : bn.fprice,
                                     purPriceData_fpurprice = cn == null ? 0.00M : cn.fpurprice,
                                     fpurdealpriceData_fdealprice = dn == null ? 0.00M : dn.fdealprice,
                                     invverifyData_fbizpdprice = en == null ? 0.00M : en.fbizpdprice,
                                     inventoryData_fcostprice = fn == null ? 0.00M : fn.fcostprice
                                 }).ToList();

                dt.Columns.Add("fmaterialid");
                //dt.Columns.Add("fattrinfo");
                dt.Columns.Add("fattrinfo_e");
                dt.Columns.Add("fcustomdesc");
                dt.Columns.Add("funitid");
                dt.Columns.Add("fpurfacprice", typeof(decimal));
                dt.Columns.Add("fpurdealprice", typeof(decimal));
                dt.Columns.Add("funifysaleprice", typeof(decimal));
                dt.Columns.Add("fsellprice", typeof(decimal));
                dt.Columns.Add("freprice", typeof(decimal));
                dt.Columns.Add("fterprice", typeof(decimal));
                dt.Columns.Add("funitcostprice", typeof(decimal));
                dt.Columns.Add("pkid");

                var IsEnableSellPrice = userCtx.IsEnableSellPrice();
                dt.BeginLoadData();
                Random rd = new Random();
                Parallel.ForEach(saveDatas, saveData =>
                {
                    var fpurfacprice = saveData.fpurpriceData_fprice; //采购单价（折前）=采购订单采购单价
                    var fpurdealprice = saveData.fpurdealpriceData_fdealprice;//采购单价（折后）=采购订单成交单价
                    var funifysaleprice = 0.00M;//统一零售价
                    var fsellprice = 0.00M;//经销价（折前）
                    var freprice = 0.00M;//分销价（折前）
                    var fterprice = 0.00M;//终端零售价（折前）
                    var funitcostprice = saveData.inventoryData_fcostprice;//单位成本(加权平均)=即时库存单位成本
                    #region 采购相关金额计算
                    //【采购单价（折前）】优先取采购订单采购单价
                    if (fpurfacprice <= 0)
                    {
                        //取不到取采购价目
                        fpurfacprice = saveData.purPriceData_fpurprice;
                    }
                    if (fpurfacprice <= 0)
                    {
                        //以上都取不到，再取盘点单盘点单价
                        fpurfacprice = saveData.invverifyData_fbizpdprice;
                    }

                    //采购订单成交单价
                    if (fpurdealprice <= 0)
                    {
                        //采购订单取不到，再取盘点单盘点单价
                        fpurdealprice = saveData.invverifyData_fbizpdprice;
                    }
                    #endregion

                    #region 销售相关金额计算
                    var orderPriceData = new { fmaterialid = "", fattrinfo_e = "", funitid = "", fsalprice = 0.00M, ftype = "" };
                    var orderData = new { fmaterialid = "", fattrinfo_e = "", fcustomdesc = "", funitid = "", fprice = 0.00M,fhqprice =0.00M, fisresellorder = "" };
                    if (saveData.fproductorgid == userCtx.Company)//判断是否为自建商品
                    {
                        //取价目表统一零售价
                        orderPriceData = orderPriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                         && x.fattrinfo_e == saveData.fattrinfo_e
                                           && x.funitid == saveData.funitid
                                            && x.ftype == "quote_type_01").FirstOrDefault();//销售价目类型
                        if (orderPriceData != null) funifysaleprice = orderPriceData.fsalprice;
                    }
                    else
                    { 
                        //总部商品，判断经销商是否启用经销价
                        if (IsEnableSellPrice)
                        {
                            //【经销价（折前）】取最新经销价目
                            orderPriceData = orderPriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                                    && x.fattrinfo_e == saveData.fattrinfo_e
                                                    && x.funitid == saveData.funitid
                                                    && x.ftype == "quote_type_03").FirstOrDefault();//销售价目类型
                            if (orderPriceData != null) fsellprice = orderPriceData.fsalprice;
                            if (fsellprice <= 0)
                            {
                                orderData = forderpriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                                 && x.fattrinfo_e == saveData.fattrinfo_e
                                                 && x.fcustomdesc == saveData.fcustomdesc
                                                 && x.funitid == saveData.funitid
                                                 && x.fisresellorder == "0").FirstOrDefault();//是否二级分销合同
                                //经销价目经取不到，取合同零售价
                                if (orderData != null) fsellprice = orderData.fprice;
                            }
                        }

                        //取价目表统一零售价
                        orderPriceData = orderPriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                            && x.fattrinfo_e == saveData.fattrinfo_e
                                            && x.funitid == saveData.funitid
                                            && x.ftype == "quote_type_01").FirstOrDefault();//销售价目类型
                        //【统一零售价（折前）】取销售价目
                        if (orderPriceData != null) funifysaleprice = orderPriceData.fsalprice;
                        if (funifysaleprice <= 0)
                        {
                            //销售价目取不到，取合同零售价
                            orderData = forderpriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                             && x.fattrinfo_e == saveData.fattrinfo_e
                                             && x.fcustomdesc == saveData.fcustomdesc
                                             && x.funitid == saveData.funitid).FirstOrDefault();//是否二级分销合同
                            //if (orderData != null) funifysaleprice = orderData.fprice;
                            //取销售合同的总部零售价
                            if (orderData != null) funifysaleprice = orderData.fhqprice;
                        }
                    }
                    //取价目表统一零售价
                    orderPriceData = orderPriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                         && x.fattrinfo_e == saveData.fattrinfo_e
                                         && x.funitid == saveData.funitid
                                         && x.ftype == "quote_type_04").FirstOrDefault();//销售价目类型
                    if (orderPriceData != null) freprice = orderPriceData.fsalprice;
                    if (freprice <= 0)
                    {
                        //分销价目取不到，取二级分销合同零售价
                        orderData = forderpriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                         && x.fattrinfo_e == saveData.fattrinfo_e
                                         && x.fcustomdesc == saveData.fcustomdesc
                                         && x.funitid == saveData.funitid
                                         && x.fisresellorder == "1").FirstOrDefault();//是否二级分销合同
                        if (orderData != null) freprice = orderData.fprice;
                    }

                    //终端零售价（折前）取价合同价格
                    orderData = forderpriceDatas.Where(x => x.fmaterialid == saveData.fmaterialid
                                         && x.fattrinfo_e == saveData.fattrinfo_e
                                         && x.fcustomdesc == saveData.fcustomdesc
                                         && x.funitid == saveData.funitid
                                         && x.fisresellorder == "1").FirstOrDefault();//是否二级分销合同
                    if (orderData != null) fterprice = orderData.fprice;//取合同零售价
                    #endregion

                    var pkid = DateTime.Now.ToString("yyyyMMddHHmmssfff") + Guid.NewGuid().ToString("N").Substring(0, 10);
                    lock (dt.Rows)
                    {
                        dt.Rows.Add(saveData.fmaterialid, saveData.fattrinfo_e, saveData.fcustomdesc, saveData.funitid,
                        fpurfacprice, fpurdealprice, funifysaleprice, fsellprice, freprice, fterprice, funitcostprice, pkid);
                    }
                });
                dt.EndLoadData();

                priceTableName = dbService.CreateTempTableWithDataTable(userCtx, dt, 1000);

                if (rowIds != null && rowIds.Count > 0)
                {
                    //勾选更新
                    string sql = $@"/*dialect*/
                    update t set fpurfacprice=t2.fpurfacprice, fpurdealprice=t2.fpurdealprice, funifysaleprice=t2.funifysaleprice, fsellprice=t2.fsellprice,
					freprice=t2.freprice, fterprice=t2.fterprice, funitcostprice=t2.funitcostprice,fmodifierid='{userCtx.UserId}',fupdatetime=getdate()
                    from t_ydj_pricesynthesize t inner join {priceTableName} t2 
					on t.fmaterialid=t2.fmaterialid and t.fcustomdesc=t2.fcustomdesc and t.funitid=t2.funitid
                    and t.fattrinfo_e=t2.fattrinfo_e  
                    where  t.fmainorgid='{userCtx.Company}' and t.fid in ({rowIds.JoinEx(",", true)}) 
                    and (t.fpurfacprice<>t2.fpurfacprice or t.fpurdealprice<>t2.fpurdealprice or t.funifysaleprice<>t2.funifysaleprice or t.fsellprice<>t2.fsellprice 
                    or t.freprice<>t2.freprice or t.fterprice<>t2.fterprice or t.funitcostprice<>t2.funitcostprice)";
                    this.DBServiceEx.Execute(userCtx, sql);
                }
                else
                {
                    //创建索引
                    try
                    {
                        var idxName = "idx_" + priceTableName;
                        var indexSql = @" create index {0} on {1}(fmaterialid ,fattrinfo_e ,fcustomdesc,funitid) ;".Fmt(idxName, priceTableName);
                        this.DBServiceEx.Execute(userCtx, indexSql);
                    }
                    catch (Exception) { }


                    //删除不在临时表的数据
                    string dleteSql = $@"/*dialect*/delete t from t_ydj_pricesynthesize t
                    where not exists(select 1 from {priceTableName} t2 with(nolock) where t.fmaterialid=t2.fmaterialid and t.fattrinfo_e=t2.fattrinfo_e and t.fcustomdesc=t2.fcustomdesc and t.funitid=t2.funitid)
                    and t.fmainorgid='{userCtx.Company}'";
                    this.DBServiceEx.Execute(userCtx, dleteSql);

                    //更新存在的新数据
                    string updateSql = $@"/*dialect*/
                    update t set fpurfacprice=t2.fpurfacprice, fpurdealprice=t2.fpurdealprice, funifysaleprice=t2.funifysaleprice, fsellprice=t2.fsellprice,
					freprice=t2.freprice, fterprice=t2.fterprice, funitcostprice=t2.funitcostprice,fmodifierid='{userCtx.UserId}',fupdatetime=getdate()
                    from t_ydj_pricesynthesize t with(nolock) inner join {priceTableName} t2 with(nolock)
					on t.fmaterialid=t2.fmaterialid and t.fcustomdesc=t2.fcustomdesc and t.funitid=t2.funitid and t.fmainorgid='{userCtx.Company}'
                    and t.fattrinfo_e=t2.fattrinfo_e 
                    where (t.fpurfacprice<>t2.fpurfacprice or t.fpurdealprice<>t2.fpurdealprice or t.funifysaleprice<>t2.funifysaleprice or t.fsellprice<>t2.fsellprice 
                    or t.freprice<>t2.freprice or t.fterprice<>t2.fterprice or t.funitcostprice<>t2.funitcostprice) ";
                    this.DBServiceEx.Execute(userCtx, updateSql);

                    //新增增量数据
                    var insertSql = $@"/*dialect*/insert into t_ydj_pricesynthesize(fid,FFormId,fmaterialid,fattrinfo_e,fcustomdesc,funitid,fmainorgid,
                                        fpurfacprice,fpurdealprice,funifysaleprice,fsellprice,freprice,fterprice,funitcostprice,fmodifierid,fupdatetime)
                                        select t.pkid,'rpt_pricesynthesize',t.fmaterialid,t.fattrinfo_e,t.fcustomdesc,t.funitid,'{userCtx.Company}' fmainorgid ,
                                        t.fpurfacprice,t.fpurdealprice,t.funifysaleprice,t.fsellprice,t.freprice,t.fterprice,t.funitcostprice,'{userCtx.UserId}',getdate() 
                                        from {priceTableName} t with(nolock)
                                        where not exists(select 1 from t_ydj_pricesynthesize t2 with(nolock) 
                                        where t.fmaterialid=t2.fmaterialid and  (t2.fattrinfo_e = t.fattrinfo_e ) and t.fcustomdesc=t2.fcustomdesc and t.funitid=t2.funitid
                                        and t2.fmainorgid='{userCtx.Company}')"; 
                    this.DBServiceEx.Execute(userCtx, insertSql);

                    //将综合价目表中的辅助属性内码根据固定名称找一个内码更新上去，目的是不影响未上线之前的逻辑
                    updateSql = $@"/*dialect*/
                    update pr set pr.fattrinfo = attr.fid
                    from t_ydj_pricesynthesize as pr with(nolock)  inner join T_BD_AUXPROPVALUE as attr with(nolock) on attr.fattrinfo_e = pr.fattrinfo_e AND attr.fattrinfo_e !=''
                    where attr.fattrinfo_e !=''  and pr.fmainorgid='{userCtx.Company}' ";
                    this.DBServiceEx.Execute(userCtx, updateSql);

                    ////全量更新
                    //var sqlList = new List<string>();
                    ////先删除商品综合价目
                    //sqlList.Add($@"/*dialect*/delete from t_ydj_pricesynthesize where fmainorgid='{userCtx.Company}'");
                    ////根据临时表新增商品综合价目
                    //sqlList.Add($@"insert into t_ydj_pricesynthesize(fid,FFormId,fmaterialid,fattrinfo,fcustomdesc,funitid,fmainorgid,
                    //                fpurfacprice,fpurdealprice,funifysaleprice,fsellprice,freprice,fterprice,funitcostprice,fmodifierid,fupdatetime)
                    //                select LOWER(REPLACE(LTRIM(NEWID()),'-','')),'rpt_pricesynthesize',fmaterialid,fattrinfo,fcustomdesc,funitid,'{userCtx.Company}' fmainorgid ,
                    //                fpurfacprice,fpurdealprice,funifysaleprice,fsellprice,freprice,fterprice,funitcostprice,'{userCtx.UserId}',getdate() 
                    //                from {priceTableName} with(nolock)");
                    //this.DBServiceEx.ExecuteBatch(userCtx, sqlList);
                }

                //更新当前组织的销售合同和销售出库单采购单价（折前）
                this.UpdateOrderPrice(userCtx, rowIds);

                #region 释放转换集合
                fpurpriceDatasX.Clear(); fpurpriceDatasX = null;
                fpurpriceDatas.Clear(); fpurpriceDatas = null;
                fpurdealpriceDatasX.Clear(); fpurdealpriceDatasX = null;
                fpurdealpriceDatas.Clear(); fpurdealpriceDatas = null;
                purPriceDatasX.Clear(); purPriceDatasX = null;
                purPriceDatas.Clear(); purPriceDatas = null;
                invverifyDatasX.Clear(); invverifyDatasX = null;
                invverifyDatas.Clear(); invverifyDatas = null;
                forderpriceDatasX.Clear(); forderpriceDatasX = null;
                forderpriceDatas.Clear(); forderpriceDatas = null;
                orderPriceDatasX.Clear(); orderPriceDatasX = null;
                orderPriceDatas.Clear(); orderPriceDatas = null;
                inventoryDatasX.Clear(); inventoryDatasX = null;
                inventoryDatas.Clear(); inventoryDatas = null;
                data.Clear(); data = null;
                addData.Clear(); addData = null;
                saveDatas.Clear(); saveDatas = null;
                #endregion


                result.SimpleMessage = "综合价目计算成功！";
                return result;
            }
            catch (Exception e)
            {
                result.SimpleMessage = "综合价目计算异常:" + e.Message;
                return result;
            }
            finally
            {
                if (!priceTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    dbService.DeleteTempTableByName(userCtx, priceTableName, true);
                }
                dt.Clear();
                dt.Dispose();
                dt = null;
            }
        }


        /// <summary>
        /// 更新当前组织的销售合同和销售出库单采购单价（折前）和单位成本（加权平均）及对应金额
        /// </summary>
        /// <param name="userCtx"></param>
        private void UpdateOrderPrice(UserContext userCtx, List<string> rowIds)
        {
            List<string> lstSqls = new List<string>();
            //更新销售合同采购单价（折前）和采购折前金额
            var orderpurSql = $@"/*dialect*/update t1 set t1.fpurfacprice=ISNULL(t3.fpurfacprice,0),t1.fpurfacamount=ISNULL(t3.fpurfacprice,0)*t1.fbizqty from t_ydj_orderentry t1 with(nolock) 
                        inner join t_ydj_order t2 with(nolock) on t2.fid=t1.fid
                        inner join t_ydj_pricesynthesize t3 with(nolock) on t3.fmaterialid=t1.fproductid
                        and t3.funitid=t1.funitid and t3.fcustomdesc=t1.fcustomdes_e and t2.fmainorgid=t3.fmainorgid
                        left join t_bd_auxpropvalue as att1 (nolock) on t3.fattrinfo = att1.fid
                        left join t_bd_auxpropvalue as att2 (nolock) on t1.fattrinfo = att2.fid
                        where  t3.fattrinfo_e = t1.fattrinfo_e and t2.fmainorgid='{userCtx.Company}' and t1.fpurfacprice=0 and ISNULL(t3.fpurfacprice,0)>0";
            //如果是勾选更新，则更新勾选的商品
            if (rowIds != null && rowIds.Count > 0) orderpurSql += $@" and t3.fid in ({rowIds.JoinEx(",", true)})";
            lstSqls.Add(orderpurSql);
            //更新销售出库单采购单价（折前）和采购折前金额
            var sostockpurSql = $@"/*dialect*/update t1 set t1.fpurfacprice=ISNULL(t3.fpurfacprice,0),t1.fpurfacamount=ISNULL(t3.fpurfacprice,0)*t1.fqty from t_stk_sostockoutentry t1 with(nolock)
                        inner join t_stk_sostockout t2 with(nolock) on t1.fid=t2.fid
                        inner join t_ydj_pricesynthesize t3 with(nolock) on t1.fmaterialid=t3.fmaterialid
                        and t1.funitid=t3.funitid and t1.fcustomdesc=t3.fcustomdesc and t2.fmainorgid=t3.fmainorgid 
                        where t3.fattrinfo_e = t1.fattrinfo_e and t2.fmainorgid='{userCtx.Company}' and t1.fpurfacprice=0 and ISNULL(t3.fpurfacprice,0)>0";
            //如果是勾选更新，则更新勾选的商品
            if (rowIds != null && rowIds.Count > 0) sostockpurSql += $@" and t3.fid in ({rowIds.JoinEx(",", true)})";
            lstSqls.Add(sostockpurSql);
            //更新销售合同单位成本（加权平均）和成本金额
            var ordercostSql = $@"/*dialect*/update t1 set t1.fcostprice=ISNULL(t3.funitcostprice,0),t1.fcost=ISNULL(t3.funitcostprice,0)*t1.fbizqty from t_ydj_orderentry t1 with(nolock) 
                        inner join t_ydj_order t2 with(nolock) on t2.fid=t1.fid
                        inner join t_ydj_pricesynthesize t3 with(nolock) on t3.fmaterialid=t1.fproductid  
                        and t3.funitid=t1.funitid and t3.fcustomdesc=t1.fcustomdes_e and t2.fmainorgid=t3.fmainorgid 
                        where t3.fattrinfo_e = t1.fattrinfo_e and t2.fmainorgid='{userCtx.Company}' and t1.fcostprice=0 and ISNULL(t3.funitcostprice,0)>0";
            //如果是勾选更新，则更新勾选的商品
            if (rowIds != null && rowIds.Count > 0) ordercostSql += $@" and t3.fid in ({rowIds.JoinEx(",", true)})";
            lstSqls.Add(ordercostSql);
            //更新销售出库单单位成本（加权平均）和成本金额
            var sostockcostSql = $@"/*dialect*/update t1 set t1.fcostprice=ISNULL(t3.funitcostprice,0),t1.fcostamt=ISNULL(t3.funitcostprice,0)*t1.fqty from t_stk_sostockoutentry t1 with(nolock)
                        inner join t_stk_sostockout t2 with(nolock) on t1.fid=t2.fid
                        inner join t_ydj_pricesynthesize t3 with(nolock) on t1.fmaterialid=t3.fmaterialid 
                        and t1.funitid=t3.funitid and t1.fcustomdesc=t3.fcustomdesc and t2.fmainorgid=t3.fmainorgid 
                        where t3.fattrinfo_e = t1.fattrinfo_e and t2.fmainorgid='{userCtx.Company}' and t1.fcostprice=0 and ISNULL(t3.funitcostprice,0)>0";
            //如果是勾选更新，则更新勾选的商品
            if (rowIds != null && rowIds.Count > 0) sostockcostSql += $@" and t3.fid in ({rowIds.JoinEx(",", true)})";
            lstSqls.Add(sostockcostSql);

            if (lstSqls != null && lstSqls.Count() > 0)
            {
                var dbServiceEx = userCtx.Container.GetService<IDBServiceEx>();
                dbServiceEx.ExecuteBatch(userCtx, lstSqls);
            }
        }

        /// <summary>  
        /// 获取盘点单价
        /// </summary>
        /// <param name="invverifyDatas"></param>
        /// <param name="item"></param>
        /// <returns></returns>
        private decimal getPurdealprice(DynamicObjectCollection invverifyDatas, DynamicObject item)
        {
            //取盘点单盘点单价
            var invverifyData = invverifyDatas.Where(x => (string)x["fmaterialid"] == (string)item["fmaterialid"]
                            && (string)x["fattrinfo"] == (string)item["fattrinfo"]
                            && (string)x["fcustomdesc"] == (string)item["fcustomdesc"]
                            && (string)x["funitid"] == (string)item["funitid"]).FirstOrDefault();
            var fpurdealprice = invverifyData == null ? 0M : Convert.ToDecimal(invverifyData["fbizpdprice"]);//【采购单价（折后）】取盘点单盘点单价
            return fpurdealprice;
        }

        /// <summary>
        /// 按类型取获取销售价目表价格（quote_type_01销售价目，quote_type_03经销销售价目，quote_type_04分销销售价目）
        /// </summary>
        /// <param name="orderPriceDatas"></param>
        /// <param name="item"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        private decimal getProductprice(DynamicObjectCollection orderPriceDatas, DynamicObject item, string type)
        {
            //取价目表统一零售价
            var orderPriceData = orderPriceDatas.Where(x => (string)x["fmaterialid"] == (string)item["fmaterialid"]
                                && (string)x["fattrinfo"] == (string)item["fattrinfo"]
                                && (string)x["funitid"] == (string)item["funitid"]
                                && (string)x["ftype"] == type).FirstOrDefault();//销售价目类型
            var fsalprice = orderPriceData == null ? 0M : Convert.ToDecimal(orderPriceData["fsalprice"]);//取销售价目统一零售价
            return fsalprice;
        }

        /// <summary>
        /// 按条件取获取合同零售价（是否二级分销合同）
        /// </summary>
        /// <param name="orderDatas"></param>
        /// <param name="item"></param>
        /// <param name="fisresellorder"></param>
        /// <returns></returns>
        private decimal getOrderprice(DynamicObjectCollection orderDatas, DynamicObject item, string fisresellorder, string fieldKey = "fprice")
        {
            //取价合同价格
            var orderData = orderDatas.Where(x => (string)x["fmaterialid"] == (string)item["fmaterialid"]
                                                && (string)x["fattrinfo"] == (string)item["fattrinfo"]
                                                && (string)x["fcustomdesc"] == (string)item["fcustomdesc"]
                                                && (string)x["funitid"] == (string)item["funitid"]
                                                && (string)x["fisresellorder"] == fisresellorder).FirstOrDefault();//是否二级分销合同
            var price = orderData == null ? 0M : Convert.ToDecimal(orderData[fieldKey]);//默认取合同零售价fprice
            return price;
        }
    }
}
