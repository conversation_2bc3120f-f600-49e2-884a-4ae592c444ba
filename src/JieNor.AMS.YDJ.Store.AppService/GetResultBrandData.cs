using JieNor.AMS.YDJ.Core;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.Utils;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using JieNor.Framework.DataEntity.BillType;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.Store.AppService
{
    public class GetResultBrandData : AbstractOperationServicePlugIn
    {
        public string GetFilterData(string fproductid, UserContext context, string fbilltype, string seriesid, string fauxseriesid, string deptid, string formid, DynamicObject[] Entitys, string fdeliverid = "", string fattrinfo = "", string fcustomdes = "", bool fisoutspot = false, bool fisfromfirstinventory = false)
        {
            //根据销售部门找到其对应的经销商组织id
            var orgid = ProductDataIsolateHelper.GetAgentOrgIdByDept(context, deptid);
            //先找到当前企业对应经销商的实控人ID
            var actualownernumber = this.GetBaseDataNameById(context, "bas_agent", context.Company, "actualownernumber");
            var seriescode = this.GetBaseDataNameById(context, "ydj_series", seriesid, "fnumber");

            var svc = context.Container.GetService<IBillTypeService>();
            var billTypeInfo = svc.GetBillTypeInfor(context, fbilltype);

            var cityid = GetCityByDept(context, deptid);
            //按 城市 + 实控人 获取当前用户对应的所有经销商组织
            var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(context);
            //获取 经销商所有的【业绩品牌】
            var Agents = AgentInfos.Select(o => o.OrgId).ToList();

            // 根据 城市 + 实控人 获取当前用户对应的所有经销商组织，比如说 查出来是(A、B、C)，以及当前企业 A 为主经销商匹配 主经销商配置表下的 A1、A2 最后经销商 结果集就是 （A、B、C、A1、A2）
            var MainAgentConfigs = context.LoadBizDataByACLFilter("bas_mainagentconfig", $" fmainagentid = '{context.Company}'").FirstOrDefault();
            if (MainAgentConfigs != null)
            {
                var MainAgentConfigsEntity = (MainAgentConfigs?["fsubagent"] as DynamicObjectCollection).Select(o => Convert.ToString(o["fsubagentid"])).ToList();
                Agents.AddRange(MainAgentConfigsEntity);
            }

            var filter = " FForbidStatus='0'  ";
            var deliverfilter = " and 1=1 ";
            //表示从采购订单传过来 通过送达方判断业绩品牌
            if (!fdeliverid.IsNullOrEmptyOrWhiteSpace())
            {
                deliverfilter = $" and exists (SELECT 1 FROM t_bas_deliverentry WHERE   fenable =1 and t_bas_deliverentry.fid= '{fdeliverid}' AND ISNULL(fserieid,'')!='' and t0.fid =t_bas_deliverentry.fserieid) ";
            }

            //当门店仅代理慕思助眠、Home、慕思美居
            var f = GetFilterByonly(context, seriesid, fauxseriesid, deptid, Entitys, fdeliverid, formid);
            //如果f不为空则表示 仅代理了某一个系列
            if (!f.IsNullOrEmptyOrWhiteSpace())
            {
                filter += f;
            }
            //其他情况为当前组织代理了多系列的情况
            else
            {
                //【系列】为”通配品牌” (编码”Z1”) 或 ”慕思助眠” (编码为”M1”) 或”慕思家纺” (编码为”J1”)
                if (seriescode == "Z1" || seriescode == "M1")
                {
                    var SeriesData = GetSeriesData(context, Agents);
                    if (SeriesData.Count > 0)
                    {
                        List<string> SeriseDataList = new List<string>();
                        //系列可能会有特殊字符拼接先拆开再过滤
                        foreach (var i in SeriesData)
                        {
                            var fserieid = i["fserieid"]?.ToString();
                            SeriseDataList.AddRange(fserieid.SplitKey());
                        }
                        var productfilter_str = " and 1=1 ";
                        //to do 31381【慕思现场】停产商品的业绩品牌需求补充调整 / 业绩品牌逻辑补充调整 - PC端 2022-05-16
                      
                        productfilter_str = Getproductfilter(context, fproductid, fattrinfo, fcustomdes, fisoutspot, fisfromfirstinventory, formid, billTypeInfo, SeriseDataList);

                        //SeriseDataList = SeriesData.Select(o => Convert.ToString(o["fseriesid"])).Distinct().ToList();
                        //业绩品牌，当商品对应《商品》的【系列】为”通配品牌” (编码”Z1”) 或 ”慕思助眠” (编码为”M1”) 或”慕思家纺” (编码为”J1”)，可选项走实控人+城市+商品.【附属品牌】
                        //if (!fauxseriesid.IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    SeriseDataList.Add(fauxseriesid);
                        //}
                        //如果是采购订单的摆场订单的话 在所有逻辑的基础上还要加上 采购部门对应门店的授权系列过滤
                        if (!billTypeInfo.IsNullOrEmptyOrWhiteSpace() && billTypeInfo.fname.EqualsIgnoreCase("摆场订单"))
                        {
                            string filter_SD = GetStoreFilter(context, deptid);
                            if (filter_SD.IsNullOrEmptyOrWhiteSpace())
                            {
                                filter_SD = "and 1=2 ";
                            }
                            filter += $"and fid in ('{string.Join("','", SeriseDataList)}') {deliverfilter} {productfilter_str} {filter_SD} AND fisresultbrand =1 AND fnumber NOT IN ('Z1','M1')";
                        }
                        else
                        {
                            //非摆场订单时 单独新增“新渠道”的判断
                            //if (seriescode == "Z2") 
                            //{
                            //    //                1)所有 慕思经典 C (大品牌)下的商品, 也就是商品的【品牌】编码为”C”下的【系列】, 都可以选到
                            //    //                2)【系列】的编码为” K1” (慕思儿童)
                            //    //                3)不可以选到【系列】的编码为” M1” (慕思助眠)(M1)
                            //    //                4)不可以选到【系列】的编码为” Z1” (通配品牌)
                            //    //                5)如果其它所有系列都没有授权则只能选Z2
                            //    //                6)其它不为上述系列就要过滤掉
                            //    productfilter_str = "and 1=1";

                            //    filter += $@" and fnumber ='Z2' or fnumber IN ( SELECT  T_YDJ_SERIES.fnumber FROM T_YDJ_SERIES 
                            //                LEFT JOIN T_YDJ_BRAND ON T_YDJ_BRAND.fid = fbrandid
                            //                WHERE T_YDJ_SERIES.fnumber = 'K1' OR (T_YDJ_BRAND.fnumber = 'C' AND T_YDJ_SERIES.fnumber NOT IN('M1', 'Z1')) ) AND fisresultbrand =1 ";
                            //}
                            //如果不是摆场订单的其它采购订单需要考虑送达方授权，且如果有采购部门还要考虑门店授权
                            if (!fdeliverid.IsNullOrEmptyOrWhiteSpace())
                            {
                                string filter_SD = " and 1=1 ";
                                //只有摆场的才要过滤门店的授权 2022-09-09
                                //1.非摆场订单下 如果采购部门不论有没有授权 都不加过滤了
                                //2.摆场订单下 如果采购部门对应门店没有授权 则业绩品牌为空
                                //if (!deptid.IsNullOrEmptyOrWhiteSpace()) 
                                //{
                                //    filter_SD = GetStoreFilter(context, deptid);
                                //}
                                filter += $" and fid in ('{string.Join("','", SeriseDataList)}') {deliverfilter} {productfilter_str} {filter_SD} AND fisresultbrand =1 AND fnumber NOT IN ('Z1','M1')";
                            }
                            else
                            {
                                filter += $"and fid in ('{string.Join("','", SeriseDataList)}') {productfilter_str} AND fisresultbrand =1 AND fnumber NOT IN ('Z1','M1')";
                            }
                        }
                    }
                    else
                    {
                        filter += " and 1 = 2 ";
                    }
                }
                //业绩品牌 默认情况 带出商品的系列不可编辑(所以以下逻辑不会走进去)
                else
                {
                    List<string> FilterList = new List<string>();
                    if (!seriesid.IsNullOrEmptyOrWhiteSpace())
                    {
                        FilterList.Add(seriesid);
                    }
                    if (!fauxseriesid.IsNullOrEmptyOrWhiteSpace())
                    {
                        FilterList.Add(fauxseriesid);
                    }
                    if (FilterList.Count > 0)
                    {
                        filter += $"and fid in ('{string.Join("','", FilterList)}') AND fisresultbrand =1 and fnumber NOT IN('Z1')";
                    }
                    else
                    {
                        //如果没有传单据类型 可能是在列表高级搜索选业绩品牌去筛选合同，那就直接加载所有 勾选业绩品牌的系列
                        if (fbilltype.IsNullOrEmpty())
                        {
                            filter += $" and fisresultbrand =1";
                        }
                        else
                        {
                            //默认情况下  如果系列和附属品牌都为空的情况下，业绩品牌应该没有候选项
                            filter += $"and 1=2";
                        }
                    }
                }
            }
            //当同时满足 商品授权和 单据类型的时候 直接走单据类型的逻辑
            if (!billTypeInfo.IsNullOrEmptyOrWhiteSpace() && billTypeInfo.fname == "大客户销售合同")
            {
                //                1)所有 慕思经典 C (大品牌)下的商品, 也就是商品的【品牌】编码为”C”下的【系列】, 都可以选到
                //                2)【系列】的编码为” K1” (慕思儿童)
                //                3)不可以选到【系列】的编码为” M1” (慕思助眠)(M1)
                //                4)不可以选到【系列】的编码为” Z1” (通配品牌)
                //                5)不可选到【系列】的编码为”Z2” (慕思经典 - 新渠道)
                //                6)其它不为上述系列就要过滤掉
                if (formid == "ydj_order")
                {
                    filter = " FForbidStatus='0'  ";
                    var SeriesData = GetSeriesData(context, Agents);
                    //如果经销商下没有系列有权限
                    var ff = "and 1=2";
                    if (SeriesData.Count > 0)
                    {
                        List<string> SeriseDataList = new List<string>();
                        foreach (var i in SeriesData)
                        {
                            var fserieid = i["fserieid"]?.ToString();
                            SeriseDataList.AddRange(fserieid.SplitKey());
                        }
                        ff = $"and fid in ('{string.Join("','", SeriseDataList)}')";
                    }
                    var productfilter_str = " and 1=1 ";
                    if (HasOrg(fproductid, context))
                    {
                        filter += $@" and fnumber IN ( SELECT  T_YDJ_SERIES.fnumber FROM T_YDJ_SERIES 
                                            LEFT JOIN T_YDJ_BRAND ON T_YDJ_BRAND.fid = fbrandid
                                            WHERE T_YDJ_SERIES.fnumber = 'K1' OR (T_YDJ_BRAND.fnumber = 'C' AND T_YDJ_SERIES.fnumber NOT IN('M1', 'Z1')) ) AND fisresultbrand =1 ";
                    }
                    else
                    {
                       
                            //to do 31381【慕思现场】停产商品的业绩品牌需求补充调整 / 业绩品牌逻辑补充调整 - PC端 2022-05-16
                            productfilter_str = Getproductfilter(context, fproductid, fattrinfo, fcustomdes, fisoutspot, fisfromfirstinventory, formid, billTypeInfo,null);
                        
                    }
                    filter += $" {productfilter_str} AND fisresultbrand =1 AND fnumber NOT IN ('Z1','M1') {ff}";
                    //filter = $@" fnumber IN ( SELECT  T_YDJ_SERIES.fnumber FROM T_YDJ_SERIES 
                    //                        LEFT JOIN T_YDJ_BRAND ON T_YDJ_BRAND.fid = fbrandid
                    //                        WHERE T_YDJ_SERIES.fnumber = 'K1' OR (T_YDJ_BRAND.fnumber = 'C' AND T_YDJ_SERIES.fnumber NOT IN('M1', 'Z1')) ) AND fisresultbrand =1  {ff}";
                }
            }

            return filter;
        }

        /// <summary>
        /// 根据辅助属性、定制说明等维度判断是否有库存
        /// 无库存则需要返回销售组织的过滤
        /// </summary>
        /// <returns></returns>
        private string Getproductfilter(UserContext context, string fproductid, string fattrinfo, string fcustomdes, bool fisoutspot,bool fisfromfirstinventory,string formid, BillTypeInfo billTypeInfo,List<string> objects)
        {
            var productfilter_str = " and 1 = 1";
            List<Dictionary<string, string>> attrInfos = null;
            if (!fattrinfo.IsNullOrEmptyOrWhiteSpace())
            {
                var attrinfoobj = context.LoadBizDataById("bd_auxpropvalueset", Convert.ToString(fattrinfo));
                JArray attrinfoArr = JArray.Parse(fattrinfo);
                if (attrinfoArr.Count > 0)
                {
                    //辅助属性组合值键值对
                    attrInfos = new List<Dictionary<string, string>>();
                    foreach (var attrinfoObj in attrinfoArr)
                    {
                        var auxPropKv1 = new Dictionary<string, string>();
                        var proplist = attrInfos.Select(o => o["auxPropId"]).ToList();
                        if (proplist.Contains(Convert.ToString(attrinfoObj["fauxpropid"]["id"]))) continue;
                        auxPropKv1["auxPropId"] = Convert.ToString(attrinfoObj["fauxpropid"]["id"]);
                        auxPropKv1["fname"] = Convert.ToString(attrinfoObj["fauxpropid"]["fname"]);
                        auxPropKv1["fnumber"] = Convert.ToString(attrinfoObj["fauxpropid"]["fnumber"]);
                        auxPropKv1["valueId"] = Convert.ToString(attrinfoObj["fvalueid"]);
                        auxPropKv1["fvaluenumber"] = Convert.ToString(attrinfoObj["fvaluenumber"]);
                        auxPropKv1["fvaluename"] = Convert.ToString(attrinfoObj["fvaluename"]);
                        attrInfos.Add(auxPropKv1);
                    }
                }
            }
            //如果商品行勾选出现货时, 业绩品牌的可选项,无需过滤销售组织、如果商品行未勾选出现货时, 业绩品牌的可选项, 需过滤销售组织
            //获取库存
            decimal qty = getInventory(context, fproductid, attrInfos, fcustomdes);
            //有库存的 不需要判断“【业绩品牌】对应的销售组织必须在产品的销售组织中” 没有库存的才需要 加此过滤（原逻辑）
            //如果是不传辅助属性的情况，那就按库存为0，因为是辅助属性级别，所以要走销售组织过滤

            //2025-04：75667 通配助眠业绩品牌处理-主任务 现逻辑商品如果商品没勾选出现货，但是有库存，导致没有走送达方销售组织过滤，从而选到了不该选择到的业绩品牌
            //沟通结论：如果商品没勾选出现货，即便是有库存，也要走送达方销售组织的过滤（排除禁用送达方的干扰）。

            var PodcuctFilterList = ResultBrandDataUnoutspot(context, fproductid, billTypeInfo, objects, formid);
            if (!fisoutspot&&!fisfromfirstinventory)
            {
                if (PodcuctFilterList.Count > 0)
                {
                    productfilter_str = $"and  fid in ('{string.Join("','", PodcuctFilterList)}') ";
                }
                //如果商品没有销售组织 或者没有找到销售组织对应业绩品牌关系的话，返回可选业绩品牌应该为空。
                else
                {
                    productfilter_str = $"and  1=2 ";
                }
            }
            //出现货会考虑 出现货为否时业绩品牌可选是否有值，如果查不到就从历史销售历史代理的系列中
            else
            { 
                if (PodcuctFilterList.Count > 0)
                {
                    productfilter_str = $"and  fid in ('{string.Join("','", PodcuctFilterList)}') ";
                }
                //如果查不到就从历史销售历史代理的系列中
                else
                {
                    //展示该商品 “未启用销售组织” (即：产品销售组织.【禁用状态】不等于“已启用”），对应当前经销商代理的业绩品牌。若查不到数据，进行下一步
                    var sql_str = $@"select fseriesid as fserieid from t_ydj_orgresultbrandentry where forganizationid in 
                                        (select fsaleorgid from  t_bd_materialsaleorg  as bd_org where fid ='{fproductid}' and fdisablestatus != '1')";
                    var productfilter = context.Container.GetService<IDBService>().ExecuteDynamicObject(context, sql_str).ToList();
                    foreach (var c in productfilter)
                    {
                        var fserieid_p = c["fserieid"]?.ToString();
                        PodcuctFilterList.Add(fserieid_p);
                    }
                    //如果依然找不到 
                    if (PodcuctFilterList.Count == 0) 
                    {
                        PodcuctFilterList = GetAgentBrandDatas(context, fproductid);
                    }

                    if (PodcuctFilterList.Count > 0)
                    {
                        productfilter_str = $"and  fid in ('{string.Join("','", PodcuctFilterList)}') ";
                    }
                    //如果商品没有销售组织 或者没有找到销售组织对应业绩品牌关系的话，返回可选业绩品牌应该为空。
                    else
                    {
                        productfilter_str = $"and  1=2 ";
                    }
                }
            }

            return productfilter_str;
        }

        private List<string> GetAgentBrandDatas(UserContext context, string fproductid) 
        { 
            var agentid = context.IsSecondOrg ? context.ParentCompanyId : context.Company;
            var agentIds = new List<string>();
            agentIds.Add(agentid);

            var agentService = context.Container.GetService<IAgentService>();
            var subAgentIds = agentService.GetSubAgentIds(context, agentid);
            agentIds.AddRange(subAgentIds);

            //1）若存在主子关系，需将主子经销商的《经销商历史代理品牌及系列表》均纳入一起查询，得到经销商历史代理的系列。（注意：需剔除【通配】【慕思助眠】这两个系列）
            var sql_str = $@"select t1.fseriesid from  t_ydj_agentbrandseries as t0 
                                inner join t_ydj_agentbrandseriesentry as t1 on t0.fid = t1.fid
                                where t0.fagentid in ({agentIds.JoinEx(",", true)}) ";

            var AgentBrandLst = context.Container.GetService<IDBService>().ExecuteDynamicObject(context, sql_str).Select(o=> Convert.ToString(o["fseriesid"])).ToList();

            // 2）根据该商品对应的销售组织，匹配到总部视角符合条件的 全部业绩品牌（依据商品关联的【销售组织】与《销售组织与业绩品牌关系》表中的【销售组织编码】匹配，获取到对应 的【系列】数据。
            sql_str = $@"select fseriesid as fserieid from t_ydj_orgresultbrandentry where forganizationid in 
                                        (select fsaleorgid from  t_bd_materialsaleorg  as bd_org where fid ='{fproductid}')";
            var productOrgLst = context.Container.GetService<IDBService>().ExecuteDynamicObject(context, sql_str).Select(o => Convert.ToString(o["fserieid"])).ToList();
            var PodcuctFilterList = AgentBrandLst.Intersect(productOrgLst).ToList();

            return PodcuctFilterList;
        }

        /// <summary>
        /// 出现货为否时，可选的业绩品牌。
        /// </summary>
        /// <param name="context"></param>
        /// <param name="fproductid"></param>
        /// <param name="agentIds"></param>
        /// <param name="billTypeInfo"></param>
        /// <param name="objects"></param>
        /// <param name="formid"></param>
        /// <returns></returns>
        private List<string> ResultBrandDataUnoutspot(UserContext context,string fproductid, BillTypeInfo billTypeInfo, List<string> objects, string formid) 
        {
            List<string> PodcuctFilterList = new List<string>();

            //如果是二级经销商 则取一级经销商关联送达方
            var agentid = context.IsSecondOrg ? context.ParentCompanyId : context.Company;
            var agentIds = new List<string>();
            agentIds.Add(agentid);

            var agentService = context.Container.GetService<IAgentService>();
            var subAgentIds = agentService.GetSubAgentIds(context, agentid);
            agentIds.AddRange(subAgentIds);

            //且销售组织业绩品牌关系表中配置的组织必须在 商品档案销售组织中
            var sql_str = $@"select fseriesid as fserieid from t_ydj_orgresultbrandentry where forganizationid in 
                                        (select fsaleorgid from  t_bd_materialsaleorg  as bd_org where fid ='{fproductid}' and fdisablestatus = '1'
                --根据未禁用送达方销售组织进一步过滤
                and exists (select 1 from T_BAS_DELIVER as dev where fagentid in ({agentIds.JoinEx(",", true)})  and fforbidstatus = 0 and bd_org.fsaleorgid =dev.fsaleorgid )
                )";
            var productfilter = context.Container.GetService<IDBService>().ExecuteDynamicObject(context, sql_str).ToList();

            foreach (var c in productfilter)
            {
                var fserieid_p = c["fserieid"]?.ToString();
                PodcuctFilterList.Add(fserieid_p);
            }
            //73233 二级分销采购订单选择业绩品牌  针对二级经销商非摆场的采购订单，如果慕思商品已全部停产(销售组织全禁用)，则选择采购订单的业绩品牌，允许能按二级授权代理系列
            if (formid.Equals("ydj_purchaseorder") && context.IsSecondOrg && !billTypeInfo.IsNullOrEmptyOrWhiteSpace() && !billTypeInfo.fname.EqualsIgnoreCase("摆场订单"))
            {
                //1 看已启用的销售组织授权系列是否在商品授权清单中存在 
                if (!PodcuctFilterList.Any(item => objects.Contains(item)))
                {
                    //2: 不存在则取全部
                    var _sql = $@"select fseriesid as fserieid from t_ydj_orgresultbrandentry where forganizationid in 
                                        (select fsaleorgid from  t_bd_materialsaleorg where fid ='{fproductid}')";
                    productfilter = context.Container.GetService<IDBService>().ExecuteDynamicObject(context, _sql).ToList();
                    foreach (var c in productfilter)
                    {
                        var fserieid_p = c["fserieid"]?.ToString();
                        PodcuctFilterList.Add(fserieid_p);
                    }
                }
            }
            return PodcuctFilterList;
        }

        /// <summary>
        /// 判断是否存在已启用且销售组织编码为2000的 销售组织
        /// </summary>
        /// <param name="fproductid"></param>
        /// <returns></returns>
        private bool HasOrg(string fproductid, UserContext ctx)
        {
            var dbService = ctx.Container.GetService<IDBService>();
            var sql = $@"select 1 from  t_bd_materialsaleorg
                    inner join t_bd_material on t_bd_material.fid = t_bd_materialsaleorg.fid
                    LEFT JOIN dbo.T_BAS_ORGANIZATION org ON org.fid = t_bd_materialsaleorg.fsaleorgid
                     where t_bd_materialsaleorg.fid = '{fproductid}' and org.fnumber = '2000' AND fdisablestatus = '1' ";
            return dbService.ExecuteDynamicObject(ctx, sql).Count > 0;
        }

        private string GetStoreFilter(UserContext context, string deptid)
        {
            var storeid = ProductDataIsolateHelper.GetStoreOrgIdByDept(context, deptid);
            List<string> storeList = new List<string>();
            storeList.Add(storeid);
            var SeriesData_BC = GetSeriesData(context, storeList);
            List<string> SeriseDataList_BC = new List<string>();
            //系列可能会有特殊字符拼接先拆开再过滤
            foreach (var i in SeriesData_BC)
            {
                var fserieid = i["fserieid"]?.ToString();
                SeriseDataList_BC.AddRange(fserieid.SplitKey());
            }
            //如果摆场订单 采购部门对应门店没有授权系列 则业绩品牌候选项为空
            var filter_BC = string.Empty;
            if (SeriseDataList_BC.Count > 0)
            {
                filter_BC = $"and  fid in ('{string.Join("','", SeriseDataList_BC)}') ";
            }
            return filter_BC;
        }

        private List<string> GetResultBrandDataList(UserContext context, string seriesid, string fauxseriesid, string deptid, DynamicObject[] Entitys, string fdeliverid, string formid)
        {
            //如果fpodeptid不为空则表示采购订单
            var gateway = context.Container.GetService<IHttpServiceInvoker>();
            var response = gateway.InvokeBillOperation(context, formid, Entitys, "checkorderresultbrand", new Dictionary<string, object> { { "deptid", deptid }, { "fdeliverid", fdeliverid }, { "row", "" } });
            List<string> FilterList = new List<string>();
            if (response != null && response.IsSuccess)
            {
                var srvData = response.SrvData.ToJson().FromJson<Dictionary<string, string>>();
                if (srvData != null)
                {
                    if (!srvData["resultSeriescode"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var resultSeriescode = JArray.Parse(srvData["resultSeriescode"]).ToList();
                        var brandcode = resultSeriescode.Where(o => Convert.ToString(o["fnumber"]) != "M1" && Convert.ToString(o["fnumber"]) != "Z1").ToList();
                        var brandcode_M1 = resultSeriescode.Where(o => Convert.ToString(o["fnumber"]) == "M1").ToList();
                        //1、如果授权系列 排除 M1、Z1 仅剩A1  则视为仅代理A1
                        if (brandcode.Count == 1 && Convert.ToString(brandcode[0]["fnumber"]) == "A1")
                        {
                            FilterList.Add(Convert.ToString(brandcode[0]["fserieid"]));
                        }
                        //2、如果授权系列 排除 M1、Z1 仅剩Y1  则视为仅代理Y1
                        else if (brandcode.Count == 1 && Convert.ToString(brandcode[0]["fnumber"]) == "Y1")
                        {
                            FilterList.Add(Convert.ToString(brandcode[0]["fserieid"]));
                        }
                        //3、如果授权系列 排除 M1、Z1 为空 则视为仅代理M1
                        else if (brandcode_M1.Count > 0 && brandcode_M1 != null && brandcode.Count == 0)
                        {
                            FilterList.Add(Convert.ToString(brandcode_M1[0]["fserieid"]));
                        }
                        else
                        {

                        }
                    }
                }
            }
            return FilterList;
        }

        //当门店仅代理慕思家纺品牌、或者门店仅代理慕思美居品牌时 处理【业绩品牌】候选项
        private string GetFilterByonly(UserContext context, string seriesid, string fauxseriesid, string deptid, DynamicObject[] Entitys, string fdeliverid, string formid)
        {
            //如果fpodeptid不为空则表示采购订单
            var gateway = context.Container.GetService<IHttpServiceInvoker>();
            var response = gateway.InvokeBillOperation(context, formid, Entitys, "checkorderresultbrand", new Dictionary<string, object> { { "deptid", deptid }, { "fdeliverid", fdeliverid }, { "row", "" } });
            var filter = "";
            List<string> FilterList = new List<string>();
            if (response != null && response.IsSuccess)
            {
                var srvData = response.SrvData.ToJson().FromJson<Dictionary<string, string>>();
                if (srvData != null)
                {
                    if (!srvData["resultSeriescode"].IsNullOrEmptyOrWhiteSpace())
                    {
                        var resultSeriescode = JArray.Parse(srvData["resultSeriescode"]).ToList();
                        var brandcode = resultSeriescode.Where(o => Convert.ToString(o["fnumber"]) != "M1" && Convert.ToString(o["fnumber"]) != "Z1").ToList();
                        var brandcode_M1 = resultSeriescode.Where(o => Convert.ToString(o["fnumber"]) == "M1").ToList();
                        //1、如果授权系列 排除 M1、Z1 仅剩A1  则视为仅代理A1
                        if (brandcode.Count == 1 && Convert.ToString(brandcode[0]["fnumber"]) == "A1")
                        {
                            FilterList.Add(Convert.ToString(brandcode[0]["fserieid"]));
                        }
                        //2、如果授权系列 排除 M1、Z1 仅剩Y1  则视为仅代理Y1
                        else if (brandcode.Count == 1 && Convert.ToString(brandcode[0]["fnumber"]) == "Y1")
                        {
                            FilterList.Add(Convert.ToString(brandcode[0]["fserieid"]));
                        }
                        //3、如果授权系列 排除 M1、Z1 为空 则视为仅代理M1
                        else if (brandcode_M1.Count > 0 && brandcode_M1 != null && brandcode.Count == 0)
                        {
                            FilterList.Add(Convert.ToString(brandcode_M1[0]["fserieid"]));
                        }
                        //如果授权系列 排除 M1、Z1 仅剩Z2  则视为仅代理Z2 
                        //门店授权 M1、Z1、Z2不属于仅代理Z2的场景最新业绩品牌需求改动过
                        //else if (brandcode.Count == 1 && Convert.ToString(brandcode[0]["fnumber"]) == "Z2")
                        //{
                        //    FilterList.Add(Convert.ToString(brandcode[0]["fserieid"]));
                        //}
                        //if (!seriesid.IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    FilterList.Add(seriesid);
                        //}
                        //if (!fauxseriesid.IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    FilterList.Add(fauxseriesid);
                        //}
                        //FilterList.Add(Convert.ToString(srvData["seriesid"]));
                        if (FilterList.Count > 0)
                        {
                            filter += $"and fid in ('{string.Join("','", FilterList)}') and fnumber NOT IN('Z1')";
                        }
                    }

                }
            }
            return filter;
        }

        //根据id返回fname 或者其他字段
        public string GetBaseDataNameById(UserContext context, string formId, string id, string fieldName = "fname")
        {
            //            this.MetaModelService = context.Container.GetService<IMetaModelService>();

            if (id.IsNullOrEmptyOrWhiteSpace()) return "";

            var htmlForm = context.Container.GetService<IMetaModelService>()?.LoadFormModel(context, formId);
            var dm = context.Container.GetService<IDataManager>();
            dm.InitDbContext(context, htmlForm.GetDynamicObjectType(context));

            var dynObj = dm.Select(id) as DynamicObject;
            if (dynObj != null)
            {
                return Convert.ToString(dynObj[fieldName]);
            }
            return "";
        }

        /// <summary>
        /// 通过部门id获取对应门店的城市
        /// </summary>
        /// <param name="deptId"></param>
        /// <returns></returns>
        private string GetCityByDept(UserContext context, string deptId)
        {
            UserContext ctx = context;
            if (!deptId.IsNullOrEmptyOrWhiteSpace())
            {
                var dbService = ctx.Container.GetService<IDBService>();
                var sql = @" SELECT c.fmycity FROM t_bd_department d 
                             inner join t_bas_store  c on d.fstore=c.fid
                             INNER JOIN t_ydj_city city ON city.fid = c.fmycity
                             WHERE d.fid='{0}' 
                             UNION 
                             SELECT fcity as fmycity  FROM t_bas_deliver WHERE fid ='{0}'
                            ".Fmt(deptId);
                var data = dbService.ExecuteDynamicObject(ctx, sql);
                if (data != null && data.Count > 0)
                {
                    var cityId = data[0]["fmycity"]?.ToString();
                    return cityId;
                }
            }
            return "";
        }

        private DynamicObjectCollection GetSeriesData(UserContext context, List<string> Agents)
        {
            string sql = $@"SELECT DISTINCT te.fserieid FROM t_ydj_productauth t with(nolock)
                            INNER JOIN t_ydj_productauthbs te with(nolock) ON te.fid = t.fid
                            WHERE t.forgid in ('{string.Join("','", Agents)}') AND t.fforbidstatus = 0";
            return context.Container.GetService<IDBService>().ExecuteDynamicObject(context, sql);
        }

        private decimal getInventory(UserContext context, string id, List<Dictionary<string, string>> attrInfos, string fcustomdes)
        {
            var arg = new[]
            {
                new Dictionary<string, object>
                {
                    { "clientId",Guid.NewGuid().ToString("N") },
                    { "productId",id},
                    { "customDesc",fcustomdes},
                    { "attrInfos",attrInfos},
                    { "type","1"}
                }
            };
            // 获取库存
            var gateway = context.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.InvokeBillOperation(context,
                            "stk_inventorylist",
                             null,
                            "getinventory",
                            new Dictionary<string, object>
                            {
                                { "productInfos",arg.ToJson()}
                            }
                         );

            if (result == null || !result.IsSuccess || result.SrvData == null)
            {
                return 0;
            }

            JArray srvData = result.SrvData is string ? JArray.Parse(result.SrvData.ToString()) : JArray.FromObject(result.SrvData);
            if (srvData == null || srvData.Count <= 0)
            {
                return 0;
            }

            decimal qty = 0M;
            foreach (var item in srvData)
            {
                if ((bool)item["success"])
                {
                    if (decimal.TryParse(Convert.ToString(item["qty"]), out decimal qty_s))
                    {
                        qty += qty_s;
                    }
                }
            }
            return qty;
        }
    }
}
