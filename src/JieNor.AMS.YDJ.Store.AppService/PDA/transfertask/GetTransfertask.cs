using JieNor.AMS.YDJ.Store.AppService.Model;
using JieNor.AMS.YDJ.Store.AppService.Model.DeliveryScanTask;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.inventorytransfer
{
    /// <summary>
    /// 调出扫描任务：PDA下载扫描任务
    /// </summary>
    [InjectService]
    [FormId("bcm_transfertask")]
    [OperationNo("gettransfertask")]
    public class GetTransfertask : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var way = this.GetQueryOrSimpleParam<int>("way");//1-调出 2-调入
            var type = this.GetQueryOrSimpleParam<int>("type");//1-扫描调出任务 2-扫描调拨单号
            var billno = this.GetQueryOrSimpleParam<string>("billno");//扫描的单号
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);
            var formId = "";
            var formName = "";
            switch (way)
            {
                case 1:
                    formId = "bcm_transfertask";
                    formName = "调出扫描任务";
                    break;
                case 2:
                    formId = "bcm_transferintask";
                    formName = "调入扫描任务";
                    break;
            }
            //if (string.IsNullOrWhiteSpace(billno))
            //{
            //    this.Result.IsSuccess = false;
            //    this.Result.ComplexMessage.ErrorMessages.Add("扫描的内容不许为空。");
            //    return;
            //}
            var filter = "";
            switch (way)
            {
                case 1:
                    filter += $" ftask_type = 'transferout'";
                    break;
                case 2:
                    filter += $" ftask_type = 'transferin'";
                    break;
                default:
                    this.Result.IsSuccess = false;
                    this.Result.ComplexMessage.ErrorMessages.Add("扫描类型传值错误，请检查参数【way】的传值，1-调出，2-调出。");
                    return;
            }
            if (string.IsNullOrWhiteSpace(billno))
            {
                filter += $" and (ftaskstatus = 'ftaskstatus_01' or ftaskstatus = 'ftaskstatus_03') and ftaskbillno in (select fbillno from t_stk_invtransfer where fstatus <> 'E ' and fmainorgid = '{this.AgentContext.Company}')";
            }
            else
            {
                switch (type)
                {
                    case 1:
                        filter += $" and fbillno = '{billno}'";
                        break;
                    case 2:
                        filter += $" and ftaskbillno = '{billno}'";
                        break;
                    default:
                        this.Result.IsSuccess = false;
                        this.Result.ComplexMessage.ErrorMessages.Add("任务类型传值错误，请检查参数【type】的传值，1-扫描调出任务，2-扫描调出任务。");
                        return;
                }
            }

            //扫码枪商品排序规则
            var profileService = this.AgentContext.Container.GetService<ISystemProfile>();
            var stockparam = profileService.GetSystemParameter(this.AgentContext, "stk_stockparam");

            var bills = this.AgentContext.LoadBizDataByFilter("bcm_transfertask", filter, true);
            if (bills is null || !bills.Any())
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"扫描失败, 当前条码未查到对应{formName} !");
                return;
            }
            //获取仓库
            var storehouseids = bills.SelectMany(o => o["ftaskentity"] as DynamicObjectCollection).Select(o => o["fstorehouseid"]?.ToString()).ToList().Distinct();
            var storehouseObjs = this.AgentContext.LoadBizDataById("ydj_storehouse", storehouseids, true);

            var fens = bills.SelectMany(s => s["ftaskentity"] as DynamicObjectCollection).ToList();
            var enIds = fens.Select(s => Convert.ToString(s["id"])).ToList();

            List<DynamicObject> allscanbarcodes = new List<DynamicObject>();
            using (var tran = AgentContext.CreateTransaction())
            {
                var enIdsTmpTable = this.DBService.CreateTempTableWithDataList(this.Context, enIds,false);
                allscanbarcodes = this.AgentContext.LoadBizDataByFilter("bcm_scanresult", $"fisparentbarcode='1' and fscantaskformid='{formId}' and fscantaskentryid in (select fid from {enIdsTmpTable})", true);
                tran.Complete();
            }
                
            var barmasterids = allscanbarcodes.Select(f => f["fbarcode"]?.ToString()).Distinct().ToList();

            var sql = $@"select a.fid as Id,a.fnumber,b.fmaterialid
            from T_BCM_BARCODEMASTER a with(nolock)
            left join T_BCM_MASTERMTRLENTRY b with(nolock) on b.fid=a.fid
            where a.fmainorgid ='{this.AgentContext.Company}' and a.fid in ('{string.Join("','", barmasterids)}')";
            var allbcmasters = this.AgentContext.ExecuteDynamicObject(sql, null).ToList();
            var ret = new List<JObject>();
            foreach(var bill in bills)
            {
                bill["fsortrule"] = stockparam["fsortrule"]?.ToString(); //扫码枪商品排序规则
                var ftaskstatus = Convert.ToString(bill["ftaskstatus"]);
                var uiConverter = this.AgentContext.Container.GetService<IUiDataConverter>();

                var htmlForm = this.MetaModelService.LoadFormModel(this.AgentContext, "bcm_transfertask");
                var uidata = uiConverter.CreateUIDataObject(this.AgentContext, htmlForm, bill);
                JToken tmpToken = null;
                uidata.TryGetValue("uidata", out tmpToken);
                if (tmpToken != null)
                {
                    JArray entryRows = JArray.FromObject(tmpToken["ftaskentity"]);
                    if (entryRows != null && entryRows.Any())
                    {
                        foreach (JObject jRow in entryRows)
                        {
                            var storehouse = storehouseObjs.FirstOrDefault(o=>o["id"]?.ToString()== jRow["fstorehouseid"]?["id"]?.ToString());
                            //仓库
                            var storehouseObj = new JObject();
                            storehouseObj["id"] = storehouse?["id"]?.ToString();
                            storehouseObj["fnumber"] = storehouse?["fnumber"]?.ToString();
                            storehouseObj["fname"] = storehouse?["fname"]?.ToString();
                            storehouseObj["priority"] = storehouse?["fstorehousepriority"]?.ToString();//仓库优先级
                            jRow["fstorehouseid"] = storehouseObj;

                            //仓位
                            var fentitys = storehouse?["fentity"] as DynamicObjectCollection;
                            var fentity = fentitys.FirstOrDefault(o => o["id"]?.ToString() == jRow["fstorelocationid"]?["id"]?.ToString());
                            var storelocationObj = new JObject();
                            storelocationObj["id"] = fentity?["id"]?.ToString();
                            storelocationObj["fnumber"] = fentity?["flocnumber"]?.ToString();
                            storelocationObj["fname"] = fentity?["flocname"]?.ToString(); 
                            storelocationObj["priority"] = fentity?["flocationpriority"]?.ToString(); //仓位优先级
                            jRow["fstorelocationid"] = storelocationObj;

                            var bcscanresults = allscanbarcodes?.Where(f => f["fscantaskentryid"]?.ToString() == Convert.ToString(jRow["id"])).ToList();
                            var barCodeInfos = new JArray();
                            foreach (var scanbc in bcscanresults)
                            {
                                var bcmaster = allbcmasters.FirstOrDefault(f => f["Id"]?.ToString() == Convert.ToString(scanbc["fbarcode"]));
                                var barCodeInfo = new JObject(); ;
                                barCodeInfo["mainid"] = Convert.ToString(tmpToken["id"]);
                                barCodeInfo["entryid"] = Convert.ToString(jRow["id"]);
                                barCodeInfo["subentryid"] = Convert.ToString(scanbc["fbarcode"]);
                                barCodeInfo["barcode"] = bcmaster == null ? "" : Convert.ToString(bcmaster["fnumber"]);
                                barCodeInfo["warehouse"] = BaseData(scanbc["fstorehouseid_ref"] as DynamicObject);
                                barCodeInfo["warehouselocation"] = BaseData(scanbc["fstorelocationid_ref"] as DynamicObject);
                                barCodeInfo["scanuser"] = BaseData(scanbc["foperatorid_ref"] as DynamicObject);
                                barCodeInfo["scantime"] = Convert.ToString(scanbc["fopdatetime"]);
                                barCodeInfo["issubmit"] = "1";
                                barCodeInfos.Add(barCodeInfo);
                            }
                            jRow["barcodeinfos"] = barCodeInfos;
                        }
                    }
                    tmpToken["ftaskentity"] = entryRows;
                }
                ret.Add(uidata);
            }
            this.Result.SrvData = ret;
        }
        public static JToken BaseData(DynamicObject data)
        {
            return JToken.FromObject(new
            {
                id = data == null ? "" : Convert.ToString(data["Id"]),
                fnumber = data == null ? "" : Convert.ToString(data["fnumber"]),
                fname = data == null ? "" : Convert.ToString(data["fname"])
            });
        }
    }
}
