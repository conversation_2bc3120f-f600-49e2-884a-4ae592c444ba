using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model;
using System.Collections.Generic;
using JieNor.Framework;
using System;
using JieNor.Framework.MetaCore.FormModel.Office;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Store.AppService.Model.Unboxing;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.Framework.DataTransferObject.Print;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.Print
{
    /// <summary>
    /// PDA根据打印模板和数据源返回PDA文件
    /// </summary>
    [InjectService]
    [FormId("bcm_barcodemaster")]
    [OperationNo("getprintfileurl")]
    public class GetPrintFileUrl : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var data = this.GetQueryOrSimpleParam<string>("printdata");//打印信息
            if (data.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"打印信息不能为空！");
                return;
            }
            var printData = data.FromJson<PrintDataModel>();
            if(printData == null)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"打印信息不能为空！");
                return;
            }

            if (printData.templateid.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"打印模板不能为空！");
                return;
            }

            if(printData.barcodes ==null || printData.barcodes.Count <= 0)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"打印条码信息不能为空！");
                return;
            }

            var barcodes = printData.barcodes.Select(x => x.barcode).ToList();
            

            var bizdatas = this.Context.LoadBizDataByFilter(this.HtmlForm.Id, $" fnumber in ('{string.Join("','", barcodes)}') ");

            if(bizdatas!=null && bizdatas.Count <= 0)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add($"未找到对应的条码主档数据！");
                return;
            }
            var prints = new List<PrintData>();
            
            foreach(var barcodeinfo in printData.barcodes)
            {
                if (barcodeinfo.printcount <= 0) continue;
                var bizdata = bizdatas.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == barcodeinfo.barcode);
                if (bizdata == null) continue;
                var print = new PrintData();
                print.BizData = bizdata;
                print.PrintCount = barcodeinfo.printcount;
                prints.Add(print);
            }

            try
            {
                var printdatasource = printtmpl.CreatePrintData(this.Context, this.HtmlForm, prints);
                var svc = this.Container.GetService<IPrintService>();
                PrintOption fprintOption = svc.GetPrintTmpl(this.Context, this.HtmlForm.Id, printData.templateid);
                if (fprintOption == null)
                {
                    throw new Exception("未找到对应的打印模板！");
                }
                var offCtx = svc.BuildPrintContext(this.Context, this.HtmlForm, printdatasource, fprintOption);
                var url = svc.PrintWithTemplete(offCtx);

                url = url.Replace("\\", "/");
                url = url.Substring(url.LastIndexOf("/prints"));
                var host = "".GetCurrentAppServer()?.ToString();
                url = host + url;
            
                this.Result.IsSuccess = true;
                this.Result.SrvData = url;
                this.Result.ComplexMessage.SuccessMessages.Add($"执行成功!");
            }
            catch (Exception ex)
            {
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add("发生异常了，请联系管理员："+ex.Message);
                return;
            }

        }      
    }
}
