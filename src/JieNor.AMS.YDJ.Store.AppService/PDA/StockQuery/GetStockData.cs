using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework;
using System.Data;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model.StockQuery;
using JieNor.AMS.YDJ.Store.AppService.Model;
using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.StockQuery
{
    /// <summary>
    /// 获取库存信息
    /// </summary>
    [InjectService]
    [FormId("stk_inventorylist")]
    [OperationNo("getstockdata")]
    public class GetStockData : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var type = this.GetQueryOrSimpleParam<string>("type"); //查询类型 1仓库仓位查询 2商品条码查询
            var materialid = this.GetQueryOrSimpleParam<string>("materialid"); //商品编码(支持模糊查询)
            var storeid = this.GetQueryOrSimpleParam<string>("storeid"); //仓库编码
            var storelocationid = this.GetQueryOrSimpleParam<string>("storelocationid"); //仓位编码
            var barcode = this.GetQueryOrSimpleParam<string>("barcode"); //商品条码
            var pageindex = this.GetQueryOrSimpleParam<int>("pageindex");
            var pagecount = this.GetQueryOrSimpleParam<int>("pagecount");
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);

            if (pageindex == 0)
            {
                pageindex = 1;
            }
            if (pagecount == 0)
            {
                pagecount = 10;
            }
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String,this.AgentContext.Company),
            };
            //var whereString = "";
            //var filter = "";
            var bcfilter = "";
            var totalcount = 1;
            var totalpage = 1;
            //仓库仓位查询
            if (type == "1")
            {
                #region 仓库仓位查询_旧代码
//                if (string.IsNullOrWhiteSpace(materialid) && string.IsNullOrWhiteSpace(storeid) && string.IsNullOrWhiteSpace(storelocationid))
//                {
//                    this.Result.IsSuccess = false;
//                    this.Result.SimpleMessage = "查询库存至少要填入一种条件！";
//                    return;
//                }
                              
//                var productids = GetProductIds(this.AgentContext, materialid);
//                //whereString += $"and a.fmaterialid in ('{string.Join("','", productids)}')";
//                //filter += $"and re.fmaterialid in ('{string.Join("','", productids)}')";
//                var matIdTableName = "";
//                if (productids.Count > 50)
//                {
//                    //数据过多使用临时表
//                    matIdTableName = this.DBService.CreateTempTableWithDataList(this.AgentContext, productids);

//                    try
//                    {
//                        //创建索引
//                        var idxName = "idx_" + this.DBService.CreateTemporaryTableName(this.AgentContext);
//                        var idxSql = @" create index {0} on {1}(FID) ;".Fmt(idxName, matIdTableName);
//                        var dbSvcEX = this.AgentContext.Container.GetService<IDBServiceEx>();
//                        dbSvcEX.Execute(this.AgentContext, idxSql);
//                    }
//                    catch (Exception)
//                    { }

//                    whereString += $"and a.fmaterialid in (select FID from {matIdTableName})";
//                    filter += $"and re.fmaterialid in (select FID from {matIdTableName})";
//                }
//                else if (productids.Count == 1)
//                {
//                    whereString += $"and a.fmaterialid = '{productids[0]}'";
//                    filter += $"and re.fmaterialid = '{productids[0]}'";
//                }
//                else
//                {
//                    whereString += $"and a.fmaterialid in ('{string.Join("','", productids)}')";
//                    filter += $"and re.fmaterialid in ('{string.Join("','", productids)}')";
//                }
//                //bcfilter += $"and b.fmaterialid = '{materialid}'";

//                if (!string.IsNullOrWhiteSpace(storeid))
//                {
//                    whereString += $"and f.fid = '{storeid}'";
//                    filter += $"and rd.fstorehouseid = '{storeid}'";
//                    //bcfilter += $"and a.fstorehouseid = '{storeid}'";
//                }
//                if (!string.IsNullOrWhiteSpace(storelocationid))
//                {
//                    whereString += $"and g.fentryid = '{storelocationid}'";
//                    filter += $"and rd.fstorelocationid = '{storelocationid}'";
//                    //bcfilter += $"and a.fstorelocationid = '{storelocationid}'";
//                }
//                var dbService = this.AgentContext.Container.GetService<IDBService>();
//                var tempTbl = dbService.CreateTemporaryTableName(this.AgentContext);
//                try
//                {


//                    var sql1 = string.Format($@"/*dialect*/SELECT 
//    ROW_NUMBER()OVER(ORDER BY a.fid,a.fmaterialid) as rownum,
//    a.fid,
//	a.fmaterialid,
//	b.fnumber AS fmaterialcode,
//	b.fname AS fmaterialname,
//	i.fid AS fcategoryid,
//	i.fnumber AS fcategorycode,
//	i.fname AS fcategoryname,
//	d.fid AS funitid,
//	d.fnumber AS funitcode,
//	d.fname AS funitname,
//	e.fid AS fstockunitid,
//	e.fnumber AS fstockunitcode,
//	e.fname AS fstockunitname,
//	(CAST(isnull(b.fpiece,0) AS NVARCHAR(10)) +'件'+CAST(isnull(b.fbag,0) AS NVARCHAR(10))+'包') AS fpackrule,
//	f.fid AS fstorehouseid,
//	f.fnumber AS fstorehousecode,
//	f.fname AS fstorehousename,
//	g.fentryid AS fstorelocationid,
//	g.flocnumber AS fstorelocationcode,
//	g.flocname AS fstorelocationname,
//	h.fid AS fstockstatus,
//	h.fnumber AS fstockstatuscode,
//	h.fname AS fstockstatusname,
//	c.fid AS fattrinfo,
//	c.fnumber AS fattrinfocode,
//	c.fname AS fattrinfoname,
//	a.fcustomdesc,
//	a.flotno,
//	a.fmtono,
//	j.fid AS fownertype,
//	j.fnumber AS fownertypecode,
//	j.fname AS fownertypename,
//	a.fownerid,
//	a.fqty,
//	a.fstockqty,
//	a.fqty AS fusableqty,
//	a.fstockqty AS fstockusableqty,
//    0 as freserveqty,
//    0 as fstockreserveqty,
//    0 as fstockintransitqty,
//    ((
//		select isnull(sum(fqty),0) from t_stk_otherstockin r with(nolock) 
//		inner join t_stk_otherstockinentry e with(nolock) on e.fid=r.fid 
//		where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
//		and e.fmaterialid=a.fmaterialid 
//		and e.fattrinfo=a.fattrinfo 
//		and e.fcustomdesc=a.fcustomdesc 
//		and e.fmtono=a.fmtono
//	    )-(
//		select isnull(sum(fqty),0) from t_stk_otherstockout r with(nolock) 
//		inner join t_stk_otherstockoutentry e with(nolock) on e.fid=r.fid 
//		where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
//		and e.fmaterialid=a.fmaterialid 
//		and e.fattrinfo=a.fattrinfo 
//		and e.fcustomdesc=a.fcustomdesc 
//		and e.fmtono=a.fmtono
//	    )+(
//		select isnull(sum(fqty),0) from t_stk_sostockreturn r with(nolock) 
//		inner join t_stk_sostockreturnentry e with(nolock) on e.fid=r.fid 
//		where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
//		and e.fmaterialid=a.fmaterialid 
//		and e.fattrinfo=a.fattrinfo 
//		and e.fcustomdesc=a.fcustomdesc 
//		and e.fmtono=a.fmtono
//	    )+(
//		select isnull(sum(fqty),0) from t_stk_invtransfer r with(nolock) 
//		inner join t_stk_invtransferentry e with(nolock) on e.fid=r.fid 
//		where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
//		and e.fmaterialid=a.fmaterialid 
//		and e.fattrinfo=a.fattrinfo 
//		and e.fcustomdesc=a.fcustomdesc 
//		and e.fmtono=a.fmtono
//	    )+(
//		select isnull(sum((fqty-finstockqty-frefundqty)+freturnqty),0) from t_ydj_purchaseorder r with(nolock) 
//		inner join t_ydj_poorderentry e with(nolock) on e.fid=r.fid 
//		where r.fmainorgid=@fmainorgid and r.fstatus='E' and r.fcancelstatus='0' and (e.fclosestatus='0' or e.fclosestatus='') 
//		and e.fmaterialid=a.fmaterialid 
//		and e.fattrinfo=a.fattrinfo 
//		and e.fcustomdes_e=a.fcustomdesc 
//		and e.fmtono=a.fmtono
//	)) as fintransitqty 
//into {tempTbl}
//FROM T_STK_INVENTORYLIST a WITH(NOLOCK)
//LEFT JOIN T_BD_MATERIAL b WITH(NOLOCK) ON b.fid=a.fmaterialid
//LEFT JOIN T_BD_AUXPROPVALUE c WITH(NOLOCK) ON c.fid=a.fattrinfo
//LEFT JOIN T_YDJ_UNIT d WITH(NOLOCK) ON d.fid=a.funitid 
//LEFT JOIN T_YDJ_UNIT e WITH(NOLOCK) ON e.fid=a.fstockunitid
//LEFT JOIN T_YDJ_STOREHOUSE f WITH(NOLOCK) ON f.fid=a.fstorehouseid 
//LEFT JOIN T_YDJ_STOREHOUSELOCATION g WITH(nolock) ON g.fentryid = a.fstorelocationid AND g.fid=f.fid
//LEFT JOIN T_YDJ_STOCKSTATUS h WITH(NOLOCK) ON h.fid=a.fstockstatus
//LEFT JOIN SER_YDJ_CATEGORY i WITH(nolock) ON i.fid=b.fcategoryid
//LEFT JOIN v_bd_ownerdata j WITH(NOLOCK) ON j.fid=a.fownertype
//where a.fmainorgid=@fmainorgid and a.fstockqty>0 {whereString}");

//                    var dbSvc = this.Container.GetService<IDBServiceEx>();
//                    var ddd = dbSvc.Execute(this.AgentContext, sql1, sqlParam);

//                    //计算预留量以及可用量
//                    this.UpdateReserveQty(tempTbl, filter);

//                    var sql2 = string.Format($@"/*dialect*/select top {pagecount} * from {tempTbl} where rownum>{(pageindex - 1) * pagecount}");
//                    var bizdatas = this.AgentContext.ExecuteDynamicObject(sql2, null).ToList();
//                    var sqlcount = string.Format($@"/*dialect*/select count(1) as totalcount from {tempTbl}");
//                    var totalcountdata = this.AgentContext.ExecuteDynamicObject(sqlcount, null).ToList();
//                    totalcount = totalcountdata == null ? 0 : totalcountdata.Sum(f => Convert.ToInt32(f["totalcount"]));
//                    totalpage = (int)Math.Ceiling(totalcount / (pagecount * 1.0));
//                    if (bizdatas == null || bizdatas.Count <= 0)
//                    {
//                        this.Result.IsSuccess = false;
//                        this.Result.SimpleMessage = "当前条件查询无库存！";
//                        return;
//                    }
//                    var bcmaterialids = new List<string>();
//                    var storeids = new List<string>();
//                    var storelocids = new List<string>();
//                    bcmaterialids = bizdatas.Select(x => x["fmaterialid"]?.ToString()).Distinct().ToList();
//                    storeids = bizdatas.Where(x => !x["fstorehouseid"].IsNullOrEmptyOrWhiteSpace()).Select(x => x["fstorehouseid"]?.ToString()).Distinct().ToList();
//                    storelocids = bizdatas.Where(x => !x["fstorelocationid"].IsNullOrEmptyOrWhiteSpace()).Select(x => x["fstorelocationid"]?.ToString()).Distinct().ToList();
//                    if (bcmaterialids != null && bcmaterialids.Count > 0)
//                    {
//                        bcfilter += $" and b.fmaterialid in ('{string.Join("','", bcmaterialids)}')";
//                    }
//                    if (storeids != null && storeids.Count > 0)
//                    {
//                        bcfilter += $" and a.fstorehouseid in ('{string.Join("','", storeids)}')";
//                    }
//                    if (storelocids != null && storelocids.Count > 0)
//                    {
//                        bcfilter += $" and a.fstorelocationid in ('{string.Join("','", storelocids)}')";
//                    }
//                    //获取所有的条码主档数据
//                    var bcids = new List<string>();

//                    var sql3 = string.Format($@"/*dialect*/SELECT a.fid,b.fmaterialid
//                            FROM T_BCM_BARCODEMASTER a 
//                            LEFT JOIN T_BCM_MASTERMTRLENTRY b ON b.fid=a.fid
//                           WHERE a.fmainorgid =@fmainorgid {bcfilter}");
//                    using (var reader = this.DBService.ExecuteReader(this.AgentContext, sql3, sqlParam))
//                    {
//                        if (reader.Read())
//                        {
//                            bcids.Add(Convert.ToString(reader["fid"]));
//                            bcmaterialids.Add(Convert.ToString(reader["fmaterialid"]));
//                        }
//                    }
//                    var allbcmasters = this.AgentContext.LoadBizDataById("bcm_barcodemaster", bcids, true);

//                    //获取所有的商品
//                    var allMaterials = this.AgentContext.LoadBizDataById("ydj_product", bcmaterialids);

//                    //获取条码主档所有的扫描记录
//                    var allbcscanresults = this.AgentContext.LoadBizDataByFilter("bcm_scanresult", $" fbarcode in ('{string.Join("','", bcids)}')");

//                    //
//                    /*
//                    var rrr = this.MetaModelService.LoadFormModel(this.AgentContext, "rpt_stocksynthesize");
//                    var model = rrr.GetDynamicObjectType(this.AgentContext).CreateInstance() as DynamicObject;
//                    foreach (var item in bizdatas)
//                    {
//                        foreach (var ff in rrr.FieldList)
//                        {

//                            var ii = item.GetType().GetProperties();
//                            var tt = ii.FirstOrDefault(f => f.Name.Equals(ff.Key)) == null? false:true;
//                            if (tt)
//                                model[ff.Key] = item[ff.Key];

//                        }

//                    }
//                    this.AgentContext.Container.GetService<LoadReferenceObjectManager>().Load(this.AgentContext, model.DynamicObjectType, model, false);
//                    var uiConverter = this.Container.GetService<IUiDataConverter>();
//                    var uiData = uiConverter.CreateUIDataObject(this.AgentContext, rrr, model);
//                    uiData["yxf"] = "shuaige";
//                    */
//                    var resdata = new StockQueryModel();
//                    var StockQuerys = new List<MaterialList>();
//                    var BarCodeInfos = new List<BarCodeInfo>();
//                    foreach (var item in bizdatas)
//                    {
//                        var fmaterialid = Convert.ToString(item["fmaterialid"]);
//                        var fattrinfo = Convert.ToString(item["fattrinfo"]);
//                        var fcustomdesc = Convert.ToString(item["fcustomdesc"]);
//                        var flotno = Convert.ToString(item["flotno"]);
//                        var fmtono = Convert.ToString(item["fmtono"]);
//                        var fownertype = Convert.ToString(item["fownertype"]);
//                        var fownerid = Convert.ToString(item["fownerid"]);
//                        var fstorehouseid = Convert.ToString(item["fstorehouseid"]);
//                        var fstorelocationid = Convert.ToString(item["fstorelocationid"]);

//                        var fmaterialno = Convert.ToString(item["fmaterialcode"]);
//                        var funitid = Convert.ToString(item["funitid"]);
//                        var fstockunitid = Convert.ToString(item["fstockunitid"]);
//                        var fusableqty = Convert.ToDecimal(item["fusableqty"]);
//                        var freserveqty = Convert.ToDecimal(item["freserveqty"]);
//                        var fintransitqty = Convert.ToDecimal(item["fintransitqty"]);

//                        var stockQuery = new MaterialList();

//                        stockQuery.fmaterial = new BaseDataModel(fmaterialid, item["fmaterialcode"], item["fmaterialname"]);
//                        stockQuery.fcategory = new BaseDataModel(item["fcategoryid"], item["fcategorycode"], item["fcategoryname"]);
//                        stockQuery.funit = new BaseDataModel(item["funitid"], item["funitcode"], item["funitname"]);
//                        stockQuery.fstockunit = new BaseDataModel(item["fstockunitid"], item["fstockunitcode"], item["fstockunitname"]);
//                        stockQuery.fpackrule = Convert.ToString(item["fpackrule"]);
//                        stockQuery.fstorehouse = new BaseDataModel(item["fstorehouseid"], item["fstorehousecode"], item["fstorehousename"]);
//                        stockQuery.fstorelocation = new BaseDataModel(item["fstorelocationid"], item["fstorelocationcode"], item["fstorelocationname"]);
//                        stockQuery.fstockstatus = new BaseDataModel(item["fstockstatus"], item["fstockstatuscode"], item["fstockstatusname"]);
//                        stockQuery.fattrinfo = new BaseDataModel(fattrinfo, item["fattrinfocode"], item["fattrinfoname"]);
//                        stockQuery.fcustomdesc = fcustomdesc;
//                        stockQuery.flotno = flotno;
//                        stockQuery.fmtono = fmtono;
//                        stockQuery.fownertype = new BaseDataModel(fownertype, item["fownertypecode"], item["fownertype"]);
//                        stockQuery.fownerid = fownerid;
//                        stockQuery.fqty = Math.Round(Convert.ToDecimal(item["fqty"]), 2);
//                        stockQuery.fstockqty = Math.Round(Convert.ToDecimal(item["fstockqty"]), 2);
//                        stockQuery.fusableqty = Math.Round(Convert.ToDecimal(item["fusableqty"]), 2);
//                        stockQuery.fstockusableqty = Math.Round(UnitConvertHelper.BaseUnitConvert(this.AgentContext, allMaterials, fmaterialno, funitid, fusableqty, fstockunitid), 2);
//                        stockQuery.freserveqty = Math.Round(Convert.ToDecimal(item["freserveqty"]), 2);
//                        stockQuery.fstockreserveqty = Math.Round(UnitConvertHelper.BaseUnitConvert(this.AgentContext, allMaterials, fmaterialno, funitid, freserveqty, fstockunitid), 2);
//                        stockQuery.fintransitqty = Math.Round(Convert.ToDecimal(item["fintransitqty"]), 2);
//                        stockQuery.fstockintransitqty = Math.Round(UnitConvertHelper.BaseUnitConvert(this.AgentContext, allMaterials, fmaterialno, funitid, fintransitqty, fstockunitid), 2);


//                        var bcinfos = allbcmasters.Where(f => Convert.ToString(f["fstorehouseid"]).Trim() == fstorehouseid.Trim() && Convert.ToString(f["fstorelocationid"]).Trim() == fstorelocationid.Trim()).ToList();
//                        if (bcinfos != null && bcinfos.Count > 0)
//                        {
//                            foreach (var bcinfo in bcinfos)
//                            {

//                                var bcens = bcinfo["fentity"] as DynamicObjectCollection;
//                                var bcen = bcens.FirstOrDefault(f =>
//                                    Convert.ToString(f["fmaterialid"]).Trim() == fmaterialid.Trim()
//                                    && Convert.ToString(f["fattrinfo"]).Trim() == fattrinfo.Trim()
//                                    && Convert.ToString(f["fcustomdesc"]).Trim() == fcustomdesc.Trim()
//                                    && Convert.ToString(f["flotno"]).Trim() == flotno.Trim()
//                                    && Convert.ToString(f["fmtono"]).Trim() == fmtono.Trim()
//                                    && Convert.ToString(f["fownertype"]).Trim() == fownertype.Trim()
//                                    && Convert.ToString(f["fownerid"]).Trim() == fownerid.Trim()
//                                   );
//                                if (bcen != null)
//                                {
//                                    var barcodeinfo = new BarCodeInfo();
//                                    barcodeinfo.barcode = Convert.ToString(bcinfo["fnumber"]);
//                                    barcodeinfo.store = new BaseDataModel(bcinfo["fstorehouseid_ref"] as DynamicObject);
//                                    barcodeinfo.storelocation = new BaseDataModel(bcinfo["fstorelocationid_ref"] as DynamicObject);

//                                    var bcscanresult = allbcscanresults.Where(f => f["fbarcode"]?.ToString() == bcinfo["Id"]?.ToString()).OrderByDescending(f => Convert.ToDateTime(f["fopdatetime"])).FirstOrDefault();
//                                    if (bcscanresult != null)
//                                    {
//                                        barcodeinfo.scanuser = new BaseDataModel(bcscanresult["foperatorid_ref"] as DynamicObject);
//                                        barcodeinfo.scantime = Convert.ToDateTime(bcscanresult["fopdatetime"]).ToString("yyyy-MM-dd hh:mm:ss");
//                                    }
//                                    BarCodeInfos.Add(barcodeinfo);
//                                }
//                            }
//                        }


//                        stockQuery.barcodeinfos = BarCodeInfos;

//                        StockQuerys.Add(stockQuery);

//                    }
//                    resdata.totalcount = totalcount;
//                    resdata.totalpage = totalpage;
//                    resdata.list = StockQuerys;


//                    this.Result.IsSuccess = true;
//                    this.Result.SimpleMessage = "数据查询成功!";
//                    this.Result.SrvData = resdata;                   
//                }
//                catch (Exception ex)
//                {
//                    this.Result.IsSuccess = false;
//                    this.Result.SimpleMessage = ex.Message;
//                }
//                finally
//                {
//                    dbService.DeleteTempTableByName(this.AgentContext, matIdTableName, true);
//                    dbService.DeleteTempTableByName(this.AgentContext, tempTbl, true);
//                }
                #endregion

                #region 仓库仓位查询
                if (string.IsNullOrWhiteSpace(materialid) && string.IsNullOrWhiteSpace(storeid) && string.IsNullOrWhiteSpace(storelocationid))
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "查询库存至少要填入一种条件！";
                    return;
                }

                List<string> lstWhere = new List<string>();
                var productids = GetProductIds(this.AgentContext, materialid);
                var matIdTableName = "";
                if (productids.Count > 50)
                {
                    //数据过多使用临时表
                    matIdTableName = this.DBService.CreateTempTableWithDataList(this.AgentContext, productids);

                    try
                    {
                        //创建索引
                        var idxName = "idx_" + this.DBService.CreateTemporaryTableName(this.AgentContext);
                        var idxSql = @" create index {0} on {1}(FID) ;".Fmt(idxName, matIdTableName);
                        var dbSvcEX = this.AgentContext.Container.GetService<IDBServiceEx>();
                        dbSvcEX.Execute(this.AgentContext, idxSql);
                    }
                    catch (Exception)
                    { 
                        //创建失败不做处理
                    }

                    lstWhere.Add($"fmaterialid in (select FID from {matIdTableName})");
                }
                else if (productids.Count == 1)
                {
                    lstWhere.Add($"fmaterialid = '{productids[0]}'");
                }
                else
                {
                    lstWhere.Add($"fmaterialid in ('{string.Join("','", productids)}')");
                }

                if (!string.IsNullOrWhiteSpace(storeid))
                {
                    lstWhere.Add($"fstorehouseid = '{storeid}'");
                }
                if (!string.IsNullOrWhiteSpace(storelocationid))
                {
                    lstWhere.Add($"fstorelocationid = '{storelocationid}'");
                }
                try
                {
                    CommonListDTO listDto = new CommonListDTO()
                        .SetFormId("rpt_stocksynthesize")
                        .SetOperationNo("querydata")
                        .SetPageIndex(pageindex)
                        .SetPageCount(pagecount);
                    listDto.FilterString = string.Join(" and ", lstWhere);

                    var gateway = this.AgentContext.Container.GetService<IHttpServiceInvoker>();
                    var resp = gateway.InvokeLocal<CommonListDTOResponse>(this.AgentContext, listDto);
                    var result = resp.OperationResult;
                    result.ThrowIfHasError(true, $"获取库存失败！");

                    var srvData = JObject.Parse(result.SrvData.ToJson());
                    var data = srvData["data"];

                    if (data == null || data.Count() <= 0)
                    {
                        this.Result.IsSuccess = false;
                        this.Result.SimpleMessage = "当前条件查询无库存！";
                        return;
                    }

                    var dataDesc = srvData["dataDesc"];
                    totalcount = dataDesc.GetJsonValue<int>("bills");
                    totalpage = dataDesc.GetJsonValue<int>("pages");

                    var bcmaterialids = new List<string>();
                    var storeids = new List<string>();
                    var storelocids = new List<string>();
                    bcmaterialids = data.Select(x => x.GetJsonValue<string>("fmaterialid")).Distinct().ToList();
                    storeids = data.Where(x => !x.GetJsonValue<string>("fstorehouseid").IsNullOrEmptyOrWhiteSpace()).Select(x => x.GetJsonValue<string>("fstorehouseid")).Distinct().ToList();
                    storelocids = data.Where(x => !x.GetJsonValue<string>("fstorelocationid").IsNullOrEmptyOrWhiteSpace()).Select(x => x.GetJsonValue<string>("fstorelocationid")).Distinct().ToList();
                    if (bcmaterialids != null && bcmaterialids.Count > 0)
                    {
                        bcfilter += $" and b.fmaterialid in ('{string.Join("','", bcmaterialids)}')";
                    }
                    if (storeids != null && storeids.Count > 0)
                    {
                        bcfilter += $" and a.fstorehouseid in ('{string.Join("','", storeids)}')";
                    }
                    if (storelocids != null && storelocids.Count > 0)
                    {
                        bcfilter += $" and a.fstorelocationid in ('{string.Join("','", storelocids)}')";
                    }
                    //获取所有的条码主档数据
                    var bcids = new List<string>();

                    var sql3 = string.Format($@"/*dialect*/SELECT a.fid,b.fmaterialid
                            FROM T_BCM_BARCODEMASTER a 
                            LEFT JOIN T_BCM_MASTERMTRLENTRY b ON b.fid=a.fid
                           WHERE a.fmainorgid =@fmainorgid {bcfilter}");
                    using (var reader = this.DBService.ExecuteReader(this.AgentContext, sql3, sqlParam))
                    {
                        if (reader.Read())
                        {
                            bcids.Add(Convert.ToString(reader["fid"]));
                        }
                    }
                    var allbcmasters = this.AgentContext.LoadBizDataById("bcm_barcodemaster", bcids, true);

                    //获取条码主档所有的扫描记录
                    var allbcscanresults = this.AgentContext.LoadBizDataByFilter("bcm_scanresult", $" fbarcode in ('{string.Join("','", bcids)}')");

                    var resdata = new StockQueryModel();
                    var StockQuerys = new List<MaterialList>();
                    var BarCodeInfos = new List<BarCodeInfo>();
                    var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
                    var stockRptForm = this.MetaModelService.LoadFormModel(this.AgentContext, "ydj_product");
                    var formDt = stockRptForm.GetDynamicObjectType(this.AgentContext);
                    foreach (var item in data)
                    {
                        var stkPkId = item.GetJsonValue<string>("fbillhead_id");
                        var stockObj= this.AgentContext.LoadBizDataById("rpt_stocksynthesize", stkPkId, true);

                        var stockQuery = new MaterialList();

                        var matObj = stockObj["fmaterialid_ref"] as DynamicObject;
                        if (matObj == null) continue;
                        //加载引用数据
                        refMgr.Load(this.AgentContext, formDt, matObj, false);
                        stockQuery.fcategory = new BaseDataModel(matObj["fcategoryid_ref"] as DynamicObject);

                        stockQuery.fmaterial = new BaseDataModel(matObj);

                        var fcategoryid = item.GetJsonValue<string>("");
                        var fcategoryno = item.GetJsonValue<string>("");
                        var fcategoryname = item.GetJsonValue<string>("fcategoryid");

                        stockQuery.funit = new BaseDataModel(stockObj["funitid_ref"] as DynamicObject);
                        stockQuery.fstockunit = new BaseDataModel(stockObj["fstockunitid_ref"] as DynamicObject);

                        int matPiece = 0, matBag = 0;
                        int.TryParse(Convert.ToString(matObj["fpiece"]), out matPiece);
                        int.TryParse(Convert.ToString(matObj["fbag"]), out matBag);
                        stockQuery.fpackrule = $"{matPiece}件{matBag}包";
                        stockQuery.fstorehouse = new BaseDataModel(stockObj["fstorehouseid_ref"] as DynamicObject);
                        stockQuery.fstorelocation = new BaseDataModel(stockObj["fstorelocationid_ref"] as DynamicObject);
                        stockQuery.fstockstatus = new BaseDataModel(stockObj["fstockstatus_ref"] as DynamicObject);
                        stockQuery.fattrinfo = new BaseDataModel(stockObj["fattrinfo_ref"] as DynamicObject);

                        var fcustomdesc = item.GetJsonValue<string>("fcustomdesc");
                        var flotno = item.GetJsonValue<string>("flotno");
                        var fmtono = item.GetJsonValue<string>("fmtono");
                        stockQuery.fcustomdesc = fcustomdesc;
                        stockQuery.flotno = flotno; 
                        stockQuery.fmtono = fmtono;

                        var fownertypeid = item.GetJsonValue<string>("fownertype");
                        fownertypeid = fownertypeid.IsNullOrEmptyOrWhiteSpace() ? "" : fownertypeid;
                        string fownertypeno = "", fownertypename = "";
                        if (!string.IsNullOrWhiteSpace(fownertypeid))
                        {
                            var ownSql = $"SELECT fnumber,fname FROM v_bd_ownerdata WITH(NOLOCK) WHERE fid='{fownertypeid}'";
                            var ownObjs = this.AgentContext.ExecuteDynamicObject(ownSql, new List<SqlParam>());
                            if (ownObjs != null && ownObjs.Any())
                            {
                                var firstObj = ownObjs.FirstOrDefault();
                                fownertypeno = Convert.ToString(firstObj["fnumber"]);
                                fownertypename = Convert.ToString(firstObj["fname"]);
                            }
                        }
                        stockQuery.fownertype = new BaseDataModel(fownertypeid, fownertypeno, fownertypename);

                        var fownerid = item.GetJsonValue<string>("fownerid");
                        fownerid = fownerid.IsNullOrEmptyOrWhiteSpace() ? "" : fownerid;
                        stockQuery.fownerid = fownerid;
                        stockQuery.fqty = Math.Round(item.GetJsonValue<decimal>("fqty"), 2);
                        stockQuery.fstockqty = Math.Round(item.GetJsonValue<decimal>("fstockqty"), 2);
                        stockQuery.fusableqty = Math.Round(item.GetJsonValue<decimal>("fusableqty"), 2);
                        stockQuery.fstockusableqty = Math.Round(item.GetJsonValue<decimal>("fstockusableqty"), 2);
                        stockQuery.freserveqty = Math.Round(item.GetJsonValue<decimal>("freserveqty"), 2);
                        stockQuery.fstockreserveqty = Math.Round(item.GetJsonValue<decimal>("fstockreserveqty"), 2);
                        stockQuery.fintransitqty = Math.Round(item.GetJsonValue<decimal>("fintransitqty"), 2);
                        stockQuery.fstockintransitqty = Math.Round(item.GetJsonValue<decimal>("fstockintransitqty"), 2);

                        var fstorehouseid = item.GetJsonValue<string>("fstorehouseid");
                        var fstorelocationid = item.GetJsonValue<string>("fstorelocationid");
                        var fmaterialid = item.GetJsonValue<string>("fmaterialid");
                        var fattrinfo = item.GetJsonValue<string>("fattrinfo");
                        var bcinfos = allbcmasters.Where(f => Convert.ToString(f["fstorehouseid"]).Trim() == fstorehouseid.Trim() && Convert.ToString(f["fstorelocationid"]).Trim() == fstorelocationid.Trim()).ToList();
                        if (bcinfos != null && bcinfos.Count > 0)
                        {
                            foreach (var bcinfo in bcinfos)
                            {

                                var bcens = bcinfo["fentity"] as DynamicObjectCollection;
                                var bcen = bcens.FirstOrDefault(f =>
                                    Convert.ToString(f["fmaterialid"]).Trim() == fmaterialid.Trim()
                                    && Convert.ToString(f["fattrinfo"]).Trim() == fattrinfo.Trim()
                                    && Convert.ToString(f["fcustomdesc"]).Trim() == fcustomdesc.Trim()
                                    && Convert.ToString(f["flotno"]).Trim() == flotno.Trim()
                                    && Convert.ToString(f["fmtono"]).Trim() == fmtono.Trim()
                                    && Convert.ToString(f["fownertype"]).Trim() == fownertypeid.Trim()
                                    && Convert.ToString(f["fownerid"]).Trim() == fownerid.Trim()
                                   );
                                if (bcen != null)
                                {
                                    var barcodeinfo = new BarCodeInfo();
                                    barcodeinfo.barcode = Convert.ToString(bcinfo["fnumber"]);
                                    barcodeinfo.store = new BaseDataModel(bcinfo["fstorehouseid_ref"] as DynamicObject);
                                    barcodeinfo.storelocation = new BaseDataModel(bcinfo["fstorelocationid_ref"] as DynamicObject);

                                    var bcscanresult = allbcscanresults.Where(f => f["fbarcode"]?.ToString() == bcinfo["Id"]?.ToString()).OrderByDescending(f => Convert.ToDateTime(f["fopdatetime"])).FirstOrDefault();
                                    if (bcscanresult != null)
                                    {
                                        barcodeinfo.scanuser = new BaseDataModel(bcscanresult["foperatorid_ref"] as DynamicObject);
                                        barcodeinfo.scantime = Convert.ToDateTime(bcscanresult["fopdatetime"]).ToString("yyyy-MM-dd hh:mm:ss");
                                    }
                                    BarCodeInfos.Add(barcodeinfo);
                                }
                            }
                        }


                        stockQuery.barcodeinfos = BarCodeInfos;

                        StockQuerys.Add(stockQuery);

                    }

                    resdata.totalcount = totalcount;
                    resdata.totalpage = totalpage;
                    resdata.list = StockQuerys;


                    this.Result.IsSuccess = true;
                    this.Result.SimpleMessage = "数据查询成功!";
                    this.Result.SrvData = resdata;
                }
                catch (Exception ex)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = ex.Message;
                }
                finally
                {
                    this.DBService.DeleteTempTableByName(this.AgentContext, matIdTableName, true);
                }
                #endregion
            }
            else
            {
                #region 条码信息查询
                if (string.IsNullOrWhiteSpace(barcode))
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "请输入包装条码!";
                    return;
                }              

                var bcdatas = this.AgentContext.LoadBizDataByFilter("bcm_barcodemaster", $" fnumber = '{barcode}'", true);
                if (bcdatas == null || bcdatas.Count <= 0)
                {
                    this.Result.IsSuccess = false;
                    this.Result.SimpleMessage = "查询不到对应条码信息！";
                    return;
                }

                var barCodeInfo = bcdatas.FirstOrDefault();
                var entitys = barCodeInfo["fentity"] as DynamicObjectCollection;

                //获取所有子条码对应的条码主档
                var subbcnos = new List<string>();
                BarCodeMasterHelper.GetSubBarCode(this.AgentContext, new List<string>() { barcode }, ref subbcnos);
                var alllastbcmasters = this.AgentContext.LoadBizDataByFilter("bcm_barcodemaster", $" fnumber in ('{string.Join("','", subbcnos)}') ", true);

                var resbarcodeinfo = new BarCodeModel();
                var materialinfos = new List<MaterialInfo>();
                resbarcodeinfo.fbarcode = barcode;
                var fpackagtype = Convert.ToString(barCodeInfo["fpackagtype"]);
                switch (fpackagtype)
                {
                    case "1":
                        fpackagtype = "标准";
                        break;
                    case "2":
                        fpackagtype = "1件多包";
                        break;
                    case "3":
                        fpackagtype = "1包多件";
                        break;
                }
                resbarcodeinfo.fpackagetype = fpackagtype;
                var fbizstatus = Convert.ToString(barCodeInfo["fbizstatus"]);
                switch (fbizstatus)
                {
                    case "1":
                        fbizstatus = "可用";
                        break;
                    case "2":
                        fbizstatus = "已出库";
                        break;
                    case "4":
                        fbizstatus = "待入库";
                        break;
                    case "5":
                        fbizstatus = "已备货";
                        break;
                }
                resbarcodeinfo.fnowstatus = fbizstatus;
                var fstoreref = barCodeInfo["fstorehouseid_ref"] as DynamicObject;
                resbarcodeinfo.fstore = fstoreref==null ?"":Convert.ToString(fstoreref["fname"]);
                var fstorelocationref = barCodeInfo["fstorelocationid_ref"] as DynamicObject;
                resbarcodeinfo.fstorelocation = fstorelocationref==null ?"":Convert.ToString(fstorelocationref["fname"]);
                resbarcodeinfo.fpackcount = Convert.ToInt32(barCodeInfo["fpackcount"]);
                resbarcodeinfo.fpackindex = Convert.ToInt32(barCodeInfo["fpackindex"]);

                var loadSer = this.Container.GetService<LoadReferenceObjectManager>();
                foreach (var entity in entitys)
                {
                    var cursubbclists = new List<string>();
                    var cursubbc = Convert.ToString(entity["fbarcode"]);
                    if (cursubbc.IsNullOrEmptyOrWhiteSpace())
                    {
                        var materialinforef = entity["fmaterialid_ref"] as DynamicObject;
                        if (materialinforef == null) continue;
                        loadSer.Load(this.AgentContext, materialinforef.DynamicObjectType, materialinforef, false);
                        var materialinfo = new MaterialInfo();
                        materialinfo.fmaterial = new BaseDataModel(materialinforef);
                        var baseunitinfo = materialinforef["funitid_ref"] as DynamicObject;
                        materialinfo.funit = new Model.BaseDataModel(baseunitinfo);
                        var stockunitinfo = materialinforef["fstockunitid_ref"] as DynamicObject;
                        materialinfo.fstockunit = new Model.BaseDataModel(stockunitinfo);
                        var attrinfo = entity["fattrinfo_ref"] as DynamicObject;
                        materialinfo.fattrinfo = new Model.BaseDataModel(attrinfo);
                        materialinfo.fcustomdesc = Convert.ToString(entity["fcustomdesc"]);
                        materialinfo.fmtono = Convert.ToString(entity["fmtono"]);
                        materialinfo.flotno = Convert.ToString(entity["flotno"]);
                        materialinfo.fownertype = Convert.ToString(entity["fownertype"]);
                        materialinfo.fownerid = Convert.ToString(entity["fownerid"]);
                        materialinfo.fmtrlmodel = Convert.ToString(materialinforef["fspecifica"]);
                        var fbrandid_ref = materialinforef["fbrandid_ref"] as DynamicObject;
                        materialinfo.fbrandid = new Model.BaseDataModel(fbrandid_ref);
                        var fseriesid_ref = materialinforef["fseriesid_ref"] as DynamicObject;
                        materialinfo.fseriesid = new Model.BaseDataModel(fseriesid_ref);
                        materialinfo.fstockqty = Math.Round(Convert.ToDecimal(entity["fstockqty"]), 2);
                        materialinfo.fsuitcombnumber = Convert.ToString(entity["fsuitcombnumber"]);
                        materialinfo.fpartscombnumber = Convert.ToString(entity["fpartscombnumber"]);
                        materialinfo.fsofacombnumber = Convert.ToString(entity["fsofacombnumber"]);
                        materialinfos.Add(materialinfo);
                    }
                    else
                    {                    
                        var curbarcodeinfo = alllastbcmasters.FirstOrDefault(f => f["fnumber"]?.ToString() == cursubbc);
                        BarCodeMasterHelper.GetLastBarCode(this.AgentContext, new List<string>() { cursubbc }, ref cursubbclists);
                        foreach (var subbc in cursubbclists)
                        {
                            var bcinfo = alllastbcmasters.FirstOrDefault(f => f["fnumber"]?.ToString() == subbc);
                            if (bcinfo == null) continue;
                            var subentitys = bcinfo["fentity"] as DynamicObjectCollection;
                            foreach (var subentity in subentitys)
                            {
                                var materialinforef = subentity["fmaterialid_ref"] as DynamicObject;
                                if (materialinforef == null) continue;
                                loadSer.Load(this.AgentContext, materialinforef.DynamicObjectType, materialinforef, false);
                                var ddd = materialinfos.FirstOrDefault(f =>
                                  Convert.ToString((f.fmaterial as Model.BaseDataModel).id).Trim() == Convert.ToString(materialinforef["Id"]).Trim()
                                   && Convert.ToString((f.fattrinfo as Model.BaseDataModel).id).Trim() == Convert.ToString(subentity["fattrinfo"]).Trim()
                                   && Convert.ToString(f.fcustomdesc).Trim() == Convert.ToString(subentity["fcustomdesc"]).Trim()
                                   && Convert.ToString(f.fmtono).Trim() == Convert.ToString(subentity["fmtono"]).Trim()
                                   && Convert.ToString(f.flotno).Trim() == Convert.ToString(subentity["flotno"]).Trim()
                                   && Convert.ToString(f.fownertype).Trim() == Convert.ToString(subentity["fownertype"]).Trim()
                                   );
                                if (ddd != null)
                                {
                                    ddd.fstockqty = Math.Round(ddd.fstockqty + Convert.ToDecimal(subentity["fstockqty"]));
                                }
                                else
                                {
                                    var materialinfo = new MaterialInfo();
                                    materialinfo.fmaterial = new BaseDataModel(materialinforef);
                                    var baseunitinfo = materialinforef["funitid_ref"] as DynamicObject;
                                    materialinfo.funit = new Model.BaseDataModel(baseunitinfo);
                                    var stockunitinfo = materialinforef["fstockunitid_ref"] as DynamicObject;
                                    materialinfo.fstockunit = new Model.BaseDataModel(stockunitinfo);
                                    var attrinfo = subentity["fattrinfo_ref"] as DynamicObject;
                                    materialinfo.fattrinfo = new Model.BaseDataModel(attrinfo);
                                    materialinfo.fcustomdesc = Convert.ToString(subentity["fcustomdesc"]);
                                    materialinfo.fmtono = Convert.ToString(subentity["fmtono"]);
                                    materialinfo.flotno = Convert.ToString(subentity["flotno"]);
                                    materialinfo.fownertype = Convert.ToString(subentity["fownertype"]);
                                    materialinfo.fownerid = Convert.ToString(subentity["fownerid"]);
                                    materialinfo.fmtrlmodel = Convert.ToString(materialinforef["fspecifica"]);
                                    var fbrandid_ref = materialinforef["fbrandid_ref"] as DynamicObject;
                                    materialinfo.fbrandid = new Model.BaseDataModel(fbrandid_ref);
                                    var fseriesid_ref = materialinforef["fseriesid_ref"] as DynamicObject;
                                    materialinfo.fseriesid = new Model.BaseDataModel(fseriesid_ref);
                                    materialinfo.fstockqty = Math.Round(Convert.ToDecimal(subentity["fstockqty"]), 2);
                                    materialinfo.fsuitcombnumber = Convert.ToString(subentity["fsuitcombnumber"]);
                                    materialinfo.fpartscombnumber = Convert.ToString(subentity["fpartscombnumber"]);
                                    materialinfo.fsofacombnumber = Convert.ToString(subentity["fsofacombnumber"]);
                                    materialinfos.Add(materialinfo);
                                }
                            }
                        }
                    }
                }
                resbarcodeinfo.materialinfos = materialinfos;

                this.Result.IsSuccess = true;
                this.Result.ComplexMessage.SuccessMessages.Add("执行成功!");
                this.Result.SrvData = resbarcodeinfo;
                #endregion
            }

        }

        /// <summary>
        /// 获取商品id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="filterstr"></param>
        /// <returns></returns>
        private List<string> GetProductIds(UserContext ctx, string filterstr)
        {
            var ids = new List<string>();
            #region 旧代码
            //var filter = $" and fforbidstatus='0' ";
            //var res = new List<string>();
            //if (!string.IsNullOrWhiteSpace(filterstr))
            //{
            //    res = SplitString(' ', filterstr);
            //    if (res.Count == 1)
            //    {
            //        res = SplitString('%', filterstr);
            //    }
            //    if (res != null && res.Count > 0)
            //    {
            //        filter += $" and ( fnumber like '%{res[0]}%' or fname like '%{res[0]}%' )";
            //    }
            //}
            ////获取当前用户可查看的商品信息
            //var authPara = new DataQueryRuleParaInfo();
            //var pkidssql = ctx.GetAuthProductDataPKID(authPara);
            //if (!string.IsNullOrWhiteSpace(pkidssql))
            //{
            //    filter += $" and fid in ({pkidssql})";
            //}

            //var sql = $@" select fid as id,fnumber,fname from t_bd_material WHERE 1=1  {filter} ";
            //var datas = ctx.ExecuteDynamicObject(sql, null).Distinct().ToList();

            //if (datas != null && datas.Count > 0)
            //{
            //    for (var i = 0; i < res.Count; i++)
            //    {
            //        if (i == 0) continue;
            //        datas = datas.Where(f => Convert.ToString(f["fnumber"]).Contains(res[i]) || Convert.ToString(f["fname"]).Contains(res[i])).ToList();
            //    }
            //}
            //if (datas != null && datas.Count > 0)
            //{
            //    ids.AddRange(datas.Select(f => Convert.ToString(f["id"])).ToList());
            //}
            //return ids;
            #endregion

            HtmlForm matFormMate = this.MetaModelService.LoadFormModel(ctx, "bd_material");//条码扫描记录 
            SqlBuilderParameter para = new SqlBuilderParameter(ctx, matFormMate);
            para.PageCount = 10000000;
            para.PageIndex = 1;
            para.NoIsolation = true;
            para.ReadDirty = true;
            para.SelectedFieldKeys.Add(matFormMate.NumberFldKey);
            para.SelectedFieldKeys.Add(matFormMate.NameFldKey);
            if (!string.IsNullOrWhiteSpace(filterstr))
            {
                var res = SplitString(' ', filterstr);
                if (res.Count == 1)
                {
                    res = SplitString('%', filterstr);
                }
                if (res.Any())
                {
                    para.SetFilter(new FilterRowObject()
                    {
                        Id = matFormMate.NumberFldKey,
                        Operator = "like",
                        Value = $"%{res[0]}%",
                        LeftBracket = "("
                    });
                    para.SetFilter(new FilterRowObject()
                    {
                        Id = matFormMate.NameFldKey,
                        Operator = "like",
                        Value = $"%{res[0]}%",
                        RightBracket = ")",
                        Logic = "or"
                    });
                }
            }
            var queryObject = QueryService.BuilQueryObject(para);

            var datas = ctx.ExecuteDynamicObject(queryObject.Sql, para.DynamicParams);
            if (datas.Any())
            {
                ids = datas.Select(t => Convert.ToString(t["fbillhead_id"])).Distinct().ToList();
            }
            return ids;
        }

        /// <summary>
        /// 按 ' '或'%'分割字符串
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private static List<string> SplitString(char splitstr, string str)
        {
            var reslist = new List<string>();
            if (str.IndexOf(splitstr) < 0)
            {
                reslist.Add(str);
                return reslist;
            }
            else
            {
                var strarray = str.Split(splitstr);
                foreach (var item in strarray)
                {
                    reslist.Add(item);
                }
            }
            return reslist;
        }

        /// <summary>
        /// 计算预留量以及可用量
        /// </summary>
        private void UpdateReserveQty(string tempTbl, string filter)
        {
            DynamicObjectCollection allReserveObjs = GetReserveInfos(filter);
            if (allReserveObjs == null || allReserveObjs.Count <= 0)
            {
                return;
            }

            var rptDt = this.HtmlForm.GetDynamicObjectType(this.AgentContext);
            var rptSql = $@" /*dialect*/select r.fid,r.fmaterialid,r.fattrinfo,r.fcustomdesc,r.funitid,r.fstockstatus,r.fmtono,r.fstorehouseid,r.fstorelocationid,r.freserveqty,r.fqty                 
                            from {tempTbl} r 
                            order by r.fmtono desc";
            var allInventoryObjs = this.AgentContext.ExecuteDynamicObject(rptSql, new List<SqlParam>());

            if (allInventoryObjs == null || allInventoryObjs.Count <= 0)
            {
                return;
            }

            var grpBy = allReserveObjs.GroupBy(f => new
            {
                fmaterialid = Convert.ToString(f["fmaterialid"]),
                fattrinfo = Convert.ToString(f["fattrinfo"]),
                fcustomdesc = Convert.ToString(f["fcustomdesc"]),
                funitid = Convert.ToString(f["funitid"]),
                fstockstatus = Convert.ToString(f["fstockstatus"]),
                fmtono = Convert.ToString(f["fmtono"]),
                fstorehouseid = Convert.ToString(f["fstorehouseid"]),
                fstorelocationid = Convert.ToString(f["fstorelocationid"]),
            }).OrderByDescending(x => x.Key.fmtono)//按跟踪号进行排序，以便优先分配有跟踪号报表数据行
            .ThenByDescending(x => x.Key.fstorehouseid)
            .ThenByDescending(x => x.Key.fstorelocationid)
            .ToList();

            foreach (var item in grpBy)
            {
                //总的预留量（所有仓库仓位的总和）
                var allReserveQty = item.ToList().Sum(f => Convert.ToDecimal(f["freserveqty"]));
                if (allReserveQty <= 0)
                {
                    continue;
                }

                //找到对应的即时库存数据行(先严格按仓库仓位匹配)
                var inventoryItems = allInventoryObjs.Where(x => StockFieldComparator(item.First(), x, true))?.ToList();
                if (inventoryItems != null && inventoryItems.Count > 0)
                {
                    //按跟踪号进行排序，以便优先分配有跟踪号报表数据行
                    inventoryItems = inventoryItems.OrderByDescending(x => Convert.ToString(x["fmtono"])).ToList();

                    //按仓库仓位遍历匹配
                    foreach (var inventoryItem in inventoryItems)
                    {
                        if (allReserveQty <= 0)
                        {
                            break;
                        }

                        //当前仓库仓位的预留数据
                        var currReserveQty = item.ToList()?.Sum(f => Convert.ToDecimal(f["freserveqty"]));
                        if (currReserveQty.Value <= 0)
                        {
                            continue;
                        }

                        var reserveQty = UpdateReserveQty(inventoryItem, currReserveQty.Value);

                        //总的预留量减少
                        allReserveQty -= reserveQty;
                    }
                }

                //如果最后还是有预留量没有匹配完，按仓库的可用量排序，从可用量大的仓库做预留
                if (allReserveQty > 0)
                {
                    //找到对应的即时库存数据行(忽略仓库仓位)
                    inventoryItems = allInventoryObjs.Where(x => StockFieldComparator(item.First(), x, false))?.ToList();
                    if (inventoryItems != null && inventoryItems.Count > 0)
                    {
                        inventoryItems = inventoryItems.Where(f => Convert.ToDecimal(f["fqty"]) - Convert.ToDecimal(f["freserveqty"]) > 0)?
                        .OrderByDescending(f => Convert.ToDecimal(f["fqty"]) - Convert.ToDecimal(f["freserveqty"]))?
                        .ToList();
                        if (inventoryItems == null || inventoryItems.Count == 0)
                        {
                            continue;
                        }

                        allReserveQty = UpdateReserveQty(inventoryItems, allReserveQty);
                    }
                }

                //如果到这个时候还是有预留量没有匹配完，没撤了，放最后一行吧
                if (allReserveQty > 0)
                {
                    if (inventoryItems != null && inventoryItems.Count > 0)
                    {
                        var lastItem = inventoryItems.LastOrDefault();
                        //库存基本单位已预留数量
                        var invReserveQty = Convert.ToDecimal(lastItem["freserveqty"]);
                        lastItem["freserveqty"] = invReserveQty + allReserveQty;
                    }
                }
            }

            //批量更新
            var sqlList = new List<string>();
            foreach (var item in allInventoryObjs)
            {
                var reserveQty = Convert.ToDecimal(item["freserveqty"]);
                if (reserveQty > 0)
                {
                    sqlList.Add($"/*dialect*/update {tempTbl} set freserveqty={reserveQty},fusableqty=fqty-{reserveQty} where fid='{item["fid"]}'");
                }
            }

            if (sqlList.Count > 0)
            {
                var dbSvc = this.Container.GetService<IDBServiceEx>();
                dbSvc.ExecuteBatch(this.AgentContext, sqlList);
            }
        }

        /// <summary>
        /// 获取预留信息
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetReserveInfos(string filter)
        {
            //加载有效的预留单明细行
            List<SqlParam> lstSqlParas = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.AgentContext.Company)
            };

            var reserveSql = $@"
            select re.fentryid,re.fparentid,re.fmaterialid,re.fattrinfo,re.fcustomdesc,re.funitid,rd.fstockstatus,re.fmtono,rd.fstorehouseid,
                    rd.fstorelocationid,rd.fqty_d,rd.fdirection_d,rd.fqty_d *0 as fusedqty,
                    case rd.fdirection_d when 0 then rd.fqty_d else rd.fqty_d *-1 end as freserveqty 
            from t_stk_reservebill r with(nolock) 
            inner join t_stk_reservebillentry re with(nolock) on r.fid=re.fid and re.fqty >0 
            inner join t_stk_reservebilldetail rd with(nolock) on rd.fentryid=re.fentryid 
            where fmainorgid=@fmainorgid and fstatus='E' and fcancelstatus='0' {filter}";

            var allReserveObjs = this.DBService.ExecuteDynamicObject(this.AgentContext, reserveSql, lstSqlParas);

            return allReserveObjs;
        }

        /// <summary>
        /// 比较两个实体数据对象中的字段值
        /// </summary>
        /// <param name="dyn1"></param>
        /// <param name="dyn2"></param>
        /// <returns></returns>
        private bool StockFieldComparator(DynamicObject dyn1, DynamicObject dyn2, bool includeStock)
        {
            if (!includeStock)
            {
                return Convert.ToString(dyn1["fmaterialid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fmaterialid"]).Trim())
                    && Convert.ToString(dyn1["fattrinfo"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fattrinfo"]).Trim())
                    && Convert.ToString(dyn1["fcustomdesc"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fcustomdesc"]).Trim())
                    && Convert.ToString(dyn1["funitid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["funitid"]).Trim())
                    //&& Convert.ToString(dyn1["fstockstatus"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fstockstatus"]).Trim())
                    && (Convert.ToString(dyn1["fmtono"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fmtono"]).Trim()) || dyn2["fmtono"].IsNullOrEmptyOrWhiteSpace());
            }
            else
            {
                return Convert.ToString(dyn1["fmaterialid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fmaterialid"]).Trim())
                    && Convert.ToString(dyn1["fattrinfo"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fattrinfo"]).Trim())
                    && Convert.ToString(dyn1["fcustomdesc"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fcustomdesc"]).Trim())
                    && Convert.ToString(dyn1["funitid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["funitid"]).Trim())
                    //&& Convert.ToString(dyn1["fstockstatus"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fstockstatus"]).Trim())
                    && (Convert.ToString(dyn1["fmtono"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fmtono"]).Trim()) || dyn2["fmtono"].IsNullOrEmptyOrWhiteSpace())
                    && Convert.ToString(dyn1["fstorehouseid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fstorehouseid"]).Trim())
                    && Convert.ToString(dyn1["fstorelocationid"]).Trim().EqualsIgnoreCase(Convert.ToString(dyn2["fstorelocationid"]).Trim());
            }
        }

        /// <summary>
        /// 更新预留量
        /// </summary>
        /// <param name="inventoryItem">即时库存信息</param>
        /// <param name="beReserveQty">即将增加的预留量</param>
        /// <returns>实际增加的预留量</returns>
        private static decimal UpdateReserveQty(DynamicObject inventoryItem, decimal beReserveQty)
        {
            //库存基本单位数量
            var invQty = Convert.ToDecimal(inventoryItem["fqty"]);

            //库存基本单位已预留数量
            var invReserveQty = Convert.ToDecimal(inventoryItem["freserveqty"]);

            //库存当前基本单位可用量
            var invUseQty = invQty - invReserveQty;

            //如果当前库存可用量小于等于0，则不能匹配
            if (invUseQty <= 0)
            {
                return 0;
            }

            //预留量=库存当前基本单位可用量和预留单基本单位预留量两者的最小数量
            var minQty = invUseQty >= beReserveQty ? beReserveQty : invUseQty;

            //库存基本单位已预留数量叠加最小数量
            inventoryItem["freserveqty"] = invReserveQty + minQty;

            return minQty;
        }

        /// <summary>
        /// 更新预留
        /// </summary>
        /// <param name="inventoryItems">即时库存信息</param>
        /// <param name="beReserveQty">即将增加的预留量</param>
        /// <returns>实际增加的预留量</returns>
        private static decimal UpdateReserveQty(List<DynamicObject> inventoryItems, decimal beReserveQty)
        {
            foreach (var inventoryItem in inventoryItems)
            {
                if (beReserveQty <= 0)
                {
                    break;
                }

                var reserveQty = UpdateReserveQty(inventoryItem, beReserveQty);

                //总的预留量减少
                beReserveQty -= reserveQty;
            }

            return beReserveQty;
        }
    }
}
