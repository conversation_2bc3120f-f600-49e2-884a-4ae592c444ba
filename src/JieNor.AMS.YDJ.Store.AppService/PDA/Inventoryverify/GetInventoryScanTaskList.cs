using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Store.AppService.Model;
using Newtonsoft.Json;
using JieNor.AMS.YDJ.Store.AppService.Model.Inventoryverify;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.Inventoryverify
{
    /// <summary>
    ///  PDA盘点任务列表
    /// 取数规则如下：
    /// 0:是否请求列表 0否 1是 
    /// 当扫描条码下载扫描任务时，传0或不传，当下载全部的扫描任务时，传1
    /// 1：请求类型：0全部 1待盘点 2盘点中 3已盘点
    /// 根据收货扫描任务中的任务状态判断，1待盘点取状态为待作业，2盘点中取状态为作业中，3已盘点取状态为已作业
    /// 2：数据类型： 1盘点记录 2快速盘点 3新增盘点方案
    /// 根据收货扫描任务中的来源单据判断，1盘点记录取来源单据为盘点单
    /// 3：扫描类型：1扫描盘点任务 2扫描盘点单号
    /// 根据对应的数据类型和扫描类型带出对应的扫描任务数据
    /// </summary>
    [InjectService]
    [FormId("bcm_countscantask")]
    [OperationNo("getinventorytasklist")]
    public class GetInventoryScanTaskList : AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var islist = this.GetQueryOrSimpleParam<string>("islist");//是否请求列表
            var type = this.GetQueryOrSimpleParam<string>("type");//请求类型：0全部 1待盘点 2盘点中 3已盘点
            var datatype = this.GetQueryOrSimpleParam<string>("datatype");//数据类型： 1盘点记录 2快速盘点 3新增盘点方案
            var scantype = this.GetQueryOrSimpleParam<string>("scantype");//扫描类型：1扫描盘点任务 2扫描盘点单号
            var scanbarcode = this.GetQueryOrSimpleParam<string>("scanbarcode");//扫描条码
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreateAgentDBContext(agentId);
            var errMsg = new List<string>();

            var inventoryTasks = new List<InventoryTaskListModel>();
            if (string.IsNullOrWhiteSpace(datatype))
            {
                errMsg.Add("数据类型不能为空!");
            }

            if (islist == "1")
            {
                if (string.IsNullOrWhiteSpace(type))
                {
                    errMsg.Add("请求类型不能为空!");
                }
            }
            else
            {
                if (string.IsNullOrWhiteSpace(scantype))
                {
                    errMsg.Add("按扫描条码的方式加载数据时，扫描类型不能为空!");
                }
                if (string.IsNullOrWhiteSpace(scanbarcode))
                {
                    errMsg.Add("按扫描条码的方式加载数据时，扫描条码不能为空!");
                }
            }
            if (errMsg.Count > 0)
            {
                this.Result.SimpleMessage = String.Join("", errMsg);
                this.Result.IsSuccess = false;
                this.Result.SrvData = inventoryTasks;
                return;
            }           

            try
            {
                var mysql = string.Format(@"
                    select a.fid as Id
                    FROM T_BCM_COUNTSCANTASK a with(nolock)
                    LEFT JOIN T_BCM_CSCANTASKENTITY b with(nolock) ON b.fid=a.fid
                    where a.fmainorgid ='{0}'
                ".Fmt(this.AgentContext.Company));

                //数据类型： 1盘点记录
                switch (datatype)
                {                    
                    case "1":
                        mysql += " and b.fsourceformid='stk_inventoryverify'";//盘点记录取来源单类型为盘点单的数据
                        break;                      
                }

                ////请求类型：0全部 1待盘点 2盘点中 3已盘点
                if (islist == "1")
                {
                    switch (type)
                    {
                        case "0":
                            break;
                        case "1":
                            mysql += " and a.ftaskstatus ='ftaskstatus_01'"; //待作业
                            break;
                        case "2":
                            mysql += " and a.ftaskstatus ='ftaskstatus_03'"; //作业中
                            break;
                        case "3":
                            mysql += " and a.ftaskstatus ='ftaskstatus_04'"; //已作业
                            break;
                    }
                }
                else
                {
                    //扫描类型：1扫描盘点任务 2扫描盘点单号
                    switch (scantype)
                    {
                        case "1":
                            mysql += $" and a.fbillno='{scanbarcode}'";
                            break;
                        case "2"://扫描盘点单号
                            mysql += $" and b.fsourcebillno='{scanbarcode}'";
                            break;                       
                    }
                }

                var datas = this.AgentContext.ExecuteDynamicObject(mysql, null).ToList();

                var loadSer = this.Container.GetService<LoadReferenceObjectManager>();

                if (datas == null || datas.Count <= 0)
                {
                    if (islist != "1")
                    {
                        this.Result.SimpleMessage = "扫描失败, 当前条码未查到对应盘点任务！";
                        this.Result.IsSuccess = false;
                        this.Result.SrvData = inventoryTasks;
                        return;
                    }
                    else
                    {
                        this.Result.SimpleMessage = "";
                        this.Result.IsSuccess = false;
                        this.Result.SrvData = inventoryTasks;
                        return;
                    }
                }

                var ids = datas.Select(f => f["Id"]?.ToString()).Distinct().ToList();
                var bizdatas = this.AgentContext.LoadBizDataById(this.HtmlForm.Id, ids, true);
                if (bizdatas.Count() <= 0)
                {
                    this.Result.SimpleMessage = "扫描失败, 当前条码未查到对应盘点任务！";
                    this.Result.IsSuccess = false;
                    this.Result.SrvData = inventoryTasks;
                    return;
                }

                if (islist != "1")
                {
                    var completedatas = bizdatas.Where(f => f["ftaskstatus"]?.ToString() == "ftaskstatus_04").ToList();
                    if (completedatas != null && completedatas.Count > 0)
                    {
                        this.Result.SimpleMessage = "当前盘点任务已完成, 无需进行盘点！";
                        this.Result.IsSuccess = false;
                        this.Result.SrvData = inventoryTasks;
                        return;
                    }
                }

                /*
                //根据盘点扫描任务，获取已提交的所有条码扫描记录,再获取所有的条码主档数据
                var billnos = bizdatas.Select(f => f["fbillno"]?.ToString()).Distinct().ToList();
                var allscanbarcodes = this.AgentContext.LoadBizDataByFilter("bcm_scanresult", $"fisparentbarcode='1' and fscantaskformid='bcm_countscantask' and fscantaskbillno in ('{string.Join("','", billnos)}')", true);
                var barmasterids = allscanbarcodes.Select(f => f["fbarcode"]?.ToString()).Distinct().ToList();

                var sql = string.Format(@"
                select a.fid as Id,a.fnumber,b.fmaterialid
                from T_BCM_BARCODEMASTER a with(nolock)
                left join T_BCM_MASTERMTRLENTRY b with(nolock) on b.fid=a.fid
                where a.fmainorgid ='{0}' and a.fid in ('{1}')".Fmt(this.AgentContext.Company,string.Join("','", barmasterids)));
                var allbcmasters = this.AgentContext.ExecuteDynamicObject(sql, null).ToList();
                */

                //获取扫描任务对应的所有盘点单
                var invnos = new List<string>();
                foreach (var firstdata in bizdatas)
                {
                    var firstens = firstdata["ftaskentity"] as DynamicObjectCollection;
                    foreach (var firsten in firstens)
                    {
                        var fsourcebillno = Convert.ToString(firsten["fsourcebillno"]);
                        if (!invnos.Contains(fsourcebillno))
                        {
                            invnos.Add(firsten["fsourcebillno"]?.ToString());
                        }
                    }
                }
                var allinventorys = this.AgentContext.LoadBizDataByFilter("stk_inventoryverify", $" fbillno in ('{string.Join("','", invnos)}')",true);

                #region 获取商品相关的字段：单位、库存单位、品牌、系列等
                var materialids = new List<string>();
                foreach (var data in bizdatas)
                {
                    var dentitys = data["ftaskentity"] as DynamicObjectCollection;
                    var dids = dentitys.Select(f => Convert.ToString(f["fmaterialid"])).Distinct().ToList();
                    foreach (var did in dids)
                    {
                        if (!materialids.Contains(did))
                        {
                            materialids.Add(did);

                        }
                    }
                }
                var tempTableName = "";
                var allMaterials = new List<DynamicObject>();
                if (materialids != null && materialids.Any())
                {
                    using (var tran = AgentContext.CreateTransaction())
                    {
                        tempTableName = this.DBService.CreateTempTableWithDataList(this.AgentContext, materialids,false);

                        var matSql = string.Format($@"select 
                                        a.fid as id,a.fnumber,a.fname,a.fspecifica,
	                                    c.fid as funitid,c.fnumber as funitnumber,c.fname as funitname,
	                                    d.fid as fstockunitid,d.fnumber as fstockunitnumber,d.fname as fstockunitname,
	                                    e.fid as fbrandid,e.fnumber as fbrandnumber,e.fname as fbrandname,
	                                    f.fid as fseriesid,f.fnumber as fseriesnumber,f.fname as fseriesname 
                                    from T_BD_MATERIAL a with(nolock)
                                    inner join {tempTableName} temp on temp.fid = a.fid
                                    left join T_YDJ_UNIT c with(nolock) on c.fid=a.funitid
                                    left join T_YDJ_UNIT d with(nolock) on d.fid=a.fstockunitid
                                    left join t_ydj_brand e with(nolock) on e.fid=a.fbrandid
                                    left join t_ydj_series f with(nolock) on f.fid=a.fseriesid");

                        allMaterials = this.DBService.ExecuteDynamicObject(this.AgentContext, matSql).ToList();

                        if (!tempTableName.IsNullOrEmptyOrWhiteSpace())
                        {
                            this.DBService.DeleteTempTableByName(this.AgentContext, tempTableName, true);
                        }

                        tran.Complete();
                    }
                }
                #endregion

                //获取仓位
                List<DynamicObject> storelocationObjs = new List<DynamicObject>();
                var fstorelocationids = bizdatas.SelectMany(o => o["ftaskentity"] as DynamicObjectCollection).Select(o => o["fstorelocationid"]?.ToString()).ToList().Distinct();
                var sql = string.Format(@"select * from t_ydj_storehouselocation where 
                                    fentryid in ('{0}')".Fmt(string.Join("','", fstorelocationids)));
                storelocationObjs = this.DBService.ExecuteDynamicObject(this.AgentContext, sql).ToList();


                #region 查询默认仓库编码
                var storehouse = new List<DynamicObject>();
                var warehouseIds = new List<string>();
                foreach (var data in bizdatas)
                {
                    var inventorywarehouses = Convert.ToString(data["finventorywarehouses"]);
                    if (!inventorywarehouses.IsNullOrEmptyOrWhiteSpace())
                    {
                        warehouseIds.AddRange(inventorywarehouses.Split(','));
                    }
                }
                if (warehouseIds != null && warehouseIds.Count > 0)
                {
                    sql = string.Format(@"select t1.fid,t1.fnumber,isnull(t2.flocnumber,'') flocnumber from t_ydj_storehouse t1 with(nolock)
                                            left join t_ydj_storehouselocation t2 with(nolock) on t1.fid=t2.fid
                                            where t1.fid in ('{0}')".Fmt(string.Join("','", warehouseIds)));
                    storehouse = this.DBService.ExecuteDynamicObject(this.AgentContext, sql).ToList();
                }
				#endregion

				foreach (var data in bizdatas)
                {
                    var inventoryTask = new InventoryTaskListModel();

                    inventoryTask.mainid = Convert.ToString(data["Id"]);
                    inventoryTask.inventtaskno = Convert.ToString(data["fbillno"]);
                    inventoryTask.inventdate = data["ftaskdate"].IsNullOrEmptyOrWhiteSpace()?DateTime.Now.ToString("yyyy-MM-dd") :Convert.ToDateTime(data["ftaskdate"]).ToString("yyyy-MM-dd");
                    var fcountdefval = "";
                    if (Convert.ToString(data["fcountdefval"]) == "0")
                    {
                        fcountdefval = "账存数";
                    }
                    inventoryTask.inventorydefval = fcountdefval;

                    var taskstatus = Convert.ToString(data["ftaskstatus"]);

                    var ftaskstatus = "待盘点";
                    switch (taskstatus)
                    {
                        case "ftaskstatus_01":
                            ftaskstatus = "待盘点";
                            break;
                        case "ftaskstatus_03":
                            ftaskstatus = "盘点中";
                            break;
                        case "ftaskstatus_04":
                            ftaskstatus = "已盘点";
                            break;
                    }

                    inventoryTask.taskstatus = ftaskstatus;

                    var inventorywarehouses = Convert.ToString(data["finventorywarehouses"]);
                    if (!inventorywarehouses.IsNullOrEmptyOrWhiteSpace())
                    {
                        var storehousenumber = storehouse?.Where(x => inventorywarehouses.Split(',').Contains(Convert.ToString(x["fid"])))
                                .Select(x => Convert.ToString(x["fnumber"])).Distinct();
                        inventoryTask.inventorywarehouses = new InventoryWarehouses()
                        {
                            id = inventorywarehouses,
                            number = string.Join(",", storehousenumber),
                            name = Convert.ToString(data["finventorywarehouses_txt"]),
                            isUseLocation = "0"
                        };
                        if (storehousenumber?.Count() == 1)
                        {
                            var locinfos = storehouse.Where(x => Convert.ToString(x["fid"]) == inventorywarehouses && !Convert.ToString(x["flocnumber"]).IsNullOrEmptyOrWhiteSpace());
                            if (locinfos != null && locinfos.Any())
                            {
                                inventoryTask.inventorywarehouses.isUseLocation = "1";
                            }
                        }
                    }

                    #region 获取商品信息
                    var materials = new List<MaterialInfo>();
                    var entitys = data["ftaskentity"] as DynamicObjectCollection;
                    var submitentitys = data["fsubmitentity"] as DynamicObjectCollection;

                    var sourcetypeName = "";
                    var sourcebillno = "";
                    var inventbase = "";
                    var inventuser = "";

                    foreach (var entity in entitys)
                    {
                        #region 获取盘点单号、盘点方案、盘点员
                        var fsourcebillno = entity["fsourcebillno"]?.ToString();
                        if (!sourcebillno.Contains(fsourcebillno)&& !string.IsNullOrWhiteSpace(fsourcebillno))
                        {
                            sourcebillno = string.IsNullOrWhiteSpace(sourcebillno) ? fsourcebillno : sourcebillno + "," + fsourcebillno;
                        }
                        var inv = allinventorys.FirstOrDefault(d => d["fbillno"]?.ToString() == fsourcebillno);
                        if (inv != null)
                        {
                            var finventbase = inv["finventbase_ref"] as DynamicObject;
                            if (finventbase != null)
                            {
                                var finventbasename = Convert.ToString(finventbase["fname"]);
                                if (!inventbase.Contains(finventbasename))
                                {
                                    inventbase = string.IsNullOrWhiteSpace(inventbase) ? finventbasename : inventbase + "," + finventbasename;
                                }
                            }
                            var finventuser = inv["fstockstaffid_ref"] as DynamicObject;
                            if (finventuser != null)
                            {
                                var finventusername = Convert.ToString(finventuser["fname"]);
                                if (!inventuser.Contains(finventusername))
                                {
                                    inventuser = string.IsNullOrWhiteSpace(inventuser) ? finventusername : inventuser + "," + finventusername;
                                }
                            }
                        }
                        #endregion

                        //当盘点扫描任务不为已完成时，只显示提交记录中没有的商品明细数据
                        /*
                        if (taskstatus != "ftaskstatus_04")
                        {
                            var suben = submitentitys.FirstOrDefault(f => f["fscanrowinterid"]?.ToString() == entity["Id"]?.ToString());
                            if (suben != null) continue;
                        }
                        */
                        var materialinfo = new MaterialInfo();
                        var materialiObj = allMaterials.FirstOrDefault(d => Convert.ToString(d["id"]) == Convert.ToString(entity["fmaterialid"]));
                        if (materialiObj == null) continue;
                        materialinfo.mainid = Convert.ToString(data["Id"]);
                        materialinfo.entryid = Convert.ToString(entity["Id"]);
                        materialinfo.material = new Model.BaseDataModel(materialiObj["id"], materialiObj["fnumber"], materialiObj["fname"]);
                        materialinfo.baseunit = new Model.BaseDataModel(materialiObj["funitid"], materialiObj["funitnumber"], materialiObj["funitname"]);
                        materialinfo.stockunit = new Model.BaseDataModel(materialiObj["fstockunitid"], materialiObj["fstockunitnumber"], materialiObj["fstockunitname"]);
                        materialinfo.stockqty = Math.Round(Convert.ToDecimal(entity["fwaitworkqty"]),2);
                        materialinfo.pdqty = Math.Round(Convert.ToDecimal(entity["fworkedqty"]),2);  
                        materialinfo.activepdqty = Math.Round(Convert.ToDecimal(entity["fworkedqty"]),2);
                        var attrinfo = entity["fattrinfo_ref"] as DynamicObject;
                        materialinfo.attrinfo = new Model.BaseDataModel(attrinfo);
                        materialinfo.attrinfo_e = Convert.ToString(entity["fattrinfo_e"]);
                        materialinfo.customdesc = Convert.ToString(entity["fcustomdesc"]);
                        materialinfo.mtono = Convert.ToString(entity["fmtono"]);
                        materialinfo.lotno = Convert.ToString(entity["flotno"]);
                        materialinfo.ownertype = Convert.ToString(entity["fownertype"]);
                        materialinfo.ownerid = Convert.ToString(entity["fownerid"]);
                        materialinfo.mtrlmodel = Convert.ToString(materialiObj["fspecifica"]);                      
                        materialinfo.brandid = new Model.BaseDataModel(materialiObj["fbrandid"], materialiObj["fbrandnumber"], materialiObj["fbrandname"]);
                        materialinfo.seriesid = new Model.BaseDataModel(materialiObj["fseriesid"], materialiObj["fseriesnumber"], materialiObj["fseriesname"]);
                        var store = entity["fstorehouseid_ref"] as DynamicObject;
                        //materialinfo.store = new Model.BaseDataModel(store);
                        materialinfo.store = new
                        {
                            id = store?["Id"],
                            fnumber = store?["fnumber"],
                            fname = store?["fname"],
                            priority = store?["fstorehousepriority"],//仓库优先级
                        };
                        var storelocation = entity["fstorelocationid_ref"] as DynamicObject;
                        //materialinfo.storelocation = new Model.BaseDataModel(storelocation);
                        var storelocationObj = storelocationObjs.FirstOrDefault(o => o["fentryid"]?.ToString() == storelocation?["Id"]?.ToString());
                        materialinfo.storelocation = new
                        {
                            id = storelocation?["Id"],
                            fnumber = storelocation?["fnumber"],
                            fname = storelocation?["fname"],
                            priority = storelocationObj?["flocationpriority"],//库位优先级
                        };
                        materialinfo.packagerule = "";

                      
                        var barCodeInfos = new List<BCReturnData>();
                        /*
                        var bcscanresults = allscanbarcodes.Where(f => f["fscantaskentryid"]?.ToString() == Convert.ToString(entity["Id"])).ToList();
                        foreach (var scanbc in bcscanresults)
                        {
                            var bcmaster = allbcmasters.FirstOrDefault(f => f["Id"]?.ToString() == Convert.ToString(scanbc["fbarcode"]));
                            var barCodeInfo = new BCReturnData();
                            barCodeInfo.mainid = Convert.ToString(data["Id"]);
                            barCodeInfo.entryid = Convert.ToString(entity["Id"]);
                            barCodeInfo.subentryid = Convert.ToString(scanbc["fbarcode"]);
                            barCodeInfo.barcode = bcmaster == null ? "" : Convert.ToString(bcmaster["fnumber"]);
                            barCodeInfo.warehouse = new Model.BaseDataModel(scanbc["fstorehouseid_ref"] as DynamicObject);
                            barCodeInfo.warehouselocation = new Model.BaseDataModel(scanbc["fstorelocationid_ref"] as DynamicObject);
                            barCodeInfo.scanuser = new Model.BaseDataModel(scanbc["foperatorid_ref"] as DynamicObject);
                            barCodeInfo.scantime = Convert.ToString(scanbc["fopdatetime"]);
                            barCodeInfo.issubmit = "1";
                            barCodeInfos.Add(barCodeInfo);
                        }
                       */
                        if (string.IsNullOrWhiteSpace(sourcetypeName))
                        {
                            switch (entity["fsourceformid"]?.ToString())
                            {
                                case "stk_inventoryverify":
                                    sourcetypeName = "盘点单";
                                    break;                              
                            }
                        }
                        
                       
                        materialinfo.barcodeinfos = barCodeInfos;
                        materials.Add(materialinfo);
                    }
                    
                    inventoryTask.sourcetype = sourcetypeName;
                    inventoryTask.sourcebillno = sourcebillno;
                    inventoryTask.inventbase = inventbase;
                    inventoryTask.inventuser = inventuser;
                    

                    inventoryTask.materialinfos = materials;
                    inventoryTask.materialcount = Convert.ToString(materials.Count);

                    //扫码枪商品排序规则
                    var profileService = this.AgentContext.Container.GetService<ISystemProfile>();
                    var stockparam = profileService.GetSystemParameter(this.AgentContext, "stk_stockparam");
                    inventoryTask.sortrule = stockparam["fsortrule"]?.ToString();

                    #endregion
                    inventoryTasks.Add(inventoryTask);
                    
                }

                this.Result.IsSuccess = true;
                this.Result.SrvData = inventoryTasks;
                this.Result.SimpleMessage = "加载数据成功!";
            }
            catch (Exception ex)
            {
                this.Result.IsSuccess = false;
                this.Result.SimpleMessage = ex.Message;
                this.Result.SrvData = inventoryTasks;
                return;
            }
            
        }
    }
}
