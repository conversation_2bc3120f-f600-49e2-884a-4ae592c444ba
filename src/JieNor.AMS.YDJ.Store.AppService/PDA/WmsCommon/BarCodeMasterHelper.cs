using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon
{
    /// <summary>
    /// 条码主档帮助类
    /// </summary>
    public class BarCodeMasterHelper
    {
        /// <summary>
        /// 递归获取最外层条码
        /// 就是循环获取条码主档中商品明细中的条码信息为空的条码主档数据
        /// </summary>
        /// <param name="ctx">用户操作上下文</param>
        /// <param name="barcodes">条码码文列表</param>
        /// <param name="bcList">返回条码列表</param>
        public static void GetLastBarCode(UserContext ctx, List<string> barcodes, ref List<string> bcList)
        {
            var sql = string.Format(@" 
SELECT a.fnumber,b.fbarcode
FROM T_BCM_BARCODEMASTER a
LEFT JOIN T_BCM_MASTERMTRLENTRY b ON b.fid = a.fid
where a.fmainorgid = '{0}' and isnull(b.fbarcode,'')<>''
and a.fnumber in ('{1}')", ctx.BizOrgId, string.Join("','", barcodes));
            var datas = ctx.ExecuteDynamicObject(sql, null).ToList();
            if (datas != null && datas.Count > 0)
            {
                datas = datas.Where(f => !Convert.ToString(f["fnumber"]).EqualsIgnoreCase(Convert.ToString(f["fbarcode"]))).ToList();
            }
            if (datas == null || datas.Count <= 0)
            {
                //如果第一层没有找到，则说明这个条码就是最底层的，直接返回
                bcList.AddRange(barcodes);
            }
            else
            {
                //因为存在第二层只有部分条码存在下级条码，没有返回的说明是最底层条码
                var bcs = datas.Where(t => !t["fnumber"].IsNullOrEmptyOrWhiteSpace()).Select(x => x["fnumber"]?.ToString()).Distinct().ToList();
                foreach (var item in barcodes)
                {
                    if (!bcs.Contains(item))
                    {
                        bcList.Add(item);
                    }
                }

                var newbcs = datas.Where(t => !t["fbarcode"].IsNullOrEmptyOrWhiteSpace()).Select(x => x["fbarcode"]?.ToString()).Distinct().ToList();
                if (newbcs != null && newbcs.Count > 0)
                {
                    GetLastBarCode(ctx, newbcs, ref bcList);
                }
            }
        }
    
        /// <summary>
        /// 检查是否齐套
        /// </summary>
        /// <param name="allbcmasters"></param>
        /// <param name="barcode"></param>
        /// <returns></returns>
        public static bool CheckIntegrality(List<DynamicObject> allbcmasters, List<string> barcodelists, List<string> errMsg)
        {
            var yes = true;
            foreach (var barcode in barcodelists)
            {
                var bcmaster = allbcmasters.FirstOrDefault(f => f["fbarcode"]?.ToString() == barcode);
                if (bcmaster != null)
                {
                    var packagetype = bcmaster["fpackagtype"]?.ToString();
                    if (packagetype == "2")
                    {
                        var fpackcount = Convert.ToInt16(bcmaster["fpackcount"]);
                        var fmaterialid = Convert.ToString(bcmaster["fmaterialid"]).Trim();
                        var fmaterialname = Convert.ToString(bcmaster["fmaterialname"]).Trim();
                        var fattrinfo = Convert.ToString(bcmaster["fattrinfo"]).Trim();
                        var fcustomdesc = Convert.ToString(bcmaster["fcustomdesc"]).Trim();
                        var fmtono = Convert.ToString(bcmaster["fmtono"]).Trim();
                        var flotno = Convert.ToString(bcmaster["flotno"]).Trim();
                        var fownertype = Convert.ToString(bcmaster["fownertype"]).Trim();
                        var fownerid = Convert.ToString(bcmaster["fownerid"]).Trim();
                        var fparentbc = getstr(barcode, "-"); 
                        var bcmasters = allbcmasters.Where(f =>
                            Convert.ToString(f["fmaterialid"]).Trim() == fmaterialid
                            && Convert.ToString(f["fattrinfo"]).Trim() == fattrinfo
                            && Convert.ToString(f["fcustomdesc"]).Trim() == fcustomdesc
                            && Convert.ToString(f["fmtono"]).Trim() == fmtono
                            && Convert.ToString(f["flotno"]).Trim() == flotno
                            && Convert.ToString(f["fownertype"]).Trim() == fownertype
                            && Convert.ToString(f["fownerid"]).Trim() == fownerid
                            && getstr(Convert.ToString(f["fbarcode"]), "-").Trim() == fparentbc
                            );
                        for (int i = 1; i <= fpackcount; i++)
                        {
                            var bc = bcmasters.FirstOrDefault(f => Convert.ToInt16(f["fpackindex"]) == i);
                            if (bc == null)
                            {
                                errMsg.Add($"商品【{fmaterialname}】的条码不齐套,请检查! ");
                                yes =  false;
                            }
                        }
                    }
                }
            }
            return yes;
        }

        public static string getstr(string str,string splitstr)
        {
            if (string.IsNullOrWhiteSpace(str)) return "";
            if (string.IsNullOrWhiteSpace(splitstr)) return str;
            var index = str.LastIndexOf(splitstr);
            if (index == -1) return str;
            return  str.Substring(0, index);

        }
        /// <summary>
        /// 获取条码对应的最上级条码
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="barcode"></param>
        /// <param name="resbc"></param>
        public static void GetFirstBarCode(UserContext ctx, string barcode, ref string resbc)
        {
            var sql = string.Format(@" 
SELECT a.fnumber,b.fbarcode
FROM T_BCM_BARCODEMASTER a
LEFT JOIN T_BCM_MASTERMTRLENTRY b ON b.fid = a.fid
where a.fmainorgid = '{0}' 
and b.fbarcode = '{1}'", ctx.Company, barcode);
            var datas = ctx.ExecuteDynamicObject(sql, null).ToList();
            if (datas != null && datas.Count > 0)
            {
                datas = datas.Where(f => !Convert.ToString(f["fnumber"]).EqualsIgnoreCase(Convert.ToString(f["fbarcode"]))).ToList();
            }
            if (datas == null || datas.Count <= 0)
            {
                //如果第一层没有找到，则说明这个条码就是最上层的，直接返回
                resbc = barcode;
            }
            else
            {
                var newbcs = datas.FirstOrDefault()["fnumber"]?.ToString();
                if (!string.IsNullOrWhiteSpace(newbcs))
                {
                    GetFirstBarCode(ctx, newbcs, ref resbc);
                }
            }
        }

        public static List<DynamicObject> GetUpperBarcode(UserContext ctx, List<string> barcode)
        {
            var barcodes = barcode.Where(w => !w.IsNullOrEmptyOrWhiteSpace());
            if (!barcodes.Any())
                return null;
            var sql = $@"select distinct fid from T_BCM_MASTERMTRLENTRY  where fbarcode in ('{string.Join("','", barcodes)}') and fmainorgid = '{ctx.Company}'";
            return ctx.LoadBizDataByFilter("bcm_barcodemaster", $" fid in ({sql})");
        }

        /// <summary>
        /// 获取条码对应的下一层条码
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="barcode"></param>
        /// <param name="resbcs"></param>
        public static void GetNextBarCode(UserContext ctx, string barcode, ref List<string> resbcs)
        {
            var sql = string.Format(@" 
SELECT a.fnumber,b.fbarcode
FROM T_BCM_BARCODEMASTER a
LEFT JOIN T_BCM_MASTERMTRLENTRY b ON b.fid = a.fid
where a.fmainorgid = '{0}' and isnull(b.fbarcode,'')<>''
and a.fnumber = '{1}'", ctx.Company, barcode);
            var datas = ctx.ExecuteDynamicObject(sql, null).ToList();
            if (datas != null && datas.Count > 0)
            {
                datas = datas.Where(f => !Convert.ToString(f["fnumber"]).EqualsIgnoreCase(Convert.ToString(f["fbarcode"]))).ToList();
            }
            if (datas == null || datas.Count <= 0)
            {
                return;
            }
            else
            {
                var newbcs = datas.Select(f=>Convert.ToString(f["fbarcode"])).Distinct().ToList();
                if (newbcs!=null && newbcs.Count>0)
                {
                    resbcs.AddRange(newbcs);
                }
            }
        }

        /// <summary>
        /// 获取包含条码本身的所有下层条码
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="barcode"></param>
        /// <param name="resbcs"></param>
        public static void GetSubBarCode(UserContext ctx, List<string> barcodes, ref List<string> bcList,bool isfirst = true)
        {
            //第一层直接放入
            if(isfirst)
            {
                bcList.AddRange(barcodes);
            }
            //第二层根据第一层去找
            var sql = string.Format(@" 
SELECT a.fnumber,b.fbarcode
FROM T_BCM_BARCODEMASTER a with(nolock)
LEFT JOIN T_BCM_MASTERMTRLENTRY b with(nolock) ON b.fid = a.fid
where a.fmainorgid = '{0}' 
and a.fnumber in ('{1}')", ctx.BizOrgId, string.Join("','", barcodes));
            var datas = ctx.ExecuteDynamicObject(sql, null).ToList();
            if (datas != null && datas.Count > 0)
            {
                datas = datas.Where(f => !Convert.ToString(f["fnumber"]).EqualsIgnoreCase(Convert.ToString(f["fbarcode"]))).ToList();
            }
            if (datas != null || datas.Count >= 0)
            {
                var subbcs = datas.Where(f => !f["fbarcode"].IsNullOrEmptyOrWhiteSpace()).Select(f => Convert.ToString(f["fbarcode"])).Distinct().ToList();
                //若存在子条码，将子条码放入条码列表中，并继续对子条码获取下一层操作
                if (subbcs != null && subbcs.Count > 0)
                {
                    bcList.AddRange(subbcs);
                    GetSubBarCode(ctx, subbcs, ref bcList,false);
                }
            }
        }
    }

}
