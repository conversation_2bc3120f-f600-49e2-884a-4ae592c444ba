using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Helper
{
    /// <summary>
    /// 总部直营模式
    /// </summary>
    public class DirectHelper
    {
        public static void UnAudit(UserContext userCtx, DynamicObject bill)
        {
            CheckDirectSynergyOperation(bill);
            // 反审核逻辑...
        }

        public static void UnAudit(UserContext userCtx, DynamicObject[] bills)
        {
            foreach (var bill in bills)
            {
                CheckDirectSynergyOperation(bill);
            }
            // 反审核逻辑...
        }

        public static void Cancel(UserContext userCtx, DynamicObject bill)
        {
            CheckDirectSynergyOperation(bill);
            // 作废逻辑...
        }

        public static void Cancel(UserContext userCtx, DynamicObject[] bills)
        {
            foreach (var bill in bills)
            {
                CheckDirectSynergyOperation(bill);
            }
            // 作废逻辑...
        }

        public static void Delete(UserContext userCtx, DynamicObject bill)
        {
            CheckDirectSynergyOperation(bill);
            // 删除逻辑...
        }

        public static void Delete(UserContext userCtx, DynamicObject[] bills)
        {
            foreach (var bill in bills)
            {
                CheckDirectSynergyOperation(bill);
            }
            // 作废逻辑...
        }

        // 示例：在单据操作前调用此方法进行校验
        private static void CheckDirectSynergyOperation(DynamicObject bill)
        {
            // 假设有字段标识已同步总部，如fsyncedtohq
            var isSynced = Convert.ToBoolean(bill["fhqderstatus"]);
            if (isSynced)
            {
                throw new BusinessException("该单据审核通过后已自动同步总部，不允许该操作。");
            }
        }

    }
}
