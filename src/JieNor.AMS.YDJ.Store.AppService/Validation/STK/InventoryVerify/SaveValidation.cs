using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Validation.STK.InventoryVerify
{
    public class SaveValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            // 所有的商品ID
            var materialIds = dataEntities?.SelectMany(t =>
            {
                var entrys = t["fentity"] as DynamicObjectCollection;
                var _productIds = entrys
                .Select(entry => Convert.ToString(entry["fmaterialid"]))
                .Where(productId => !productId.IsNullOrEmptyOrWhiteSpace());
                return _productIds;
            })
            ?.Distinct()
            ?.ToList();

            // 批量加载商品信息
            DynamicObjectCollection productObjs = null;
            if (materialIds != null && materialIds.Any())
            {
                productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", materialIds, "fnumber,funitid,fstockunitid");
            }

            ValidationResult result = new ValidationResult();
            foreach (var dataEntity in dataEntities)
            {
                var currEntrys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var ent in currEntrys)
                {
                    var materialid = Convert.ToString(ent["fmaterialid"]);
                    var materialObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(materialid));
                    if (materialObj == null) continue;

                    if (!Convert.ToString(ent["funitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["funitid"])))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{formInfo.Caption}【{dataEntity["fbillno"]}】第{Convert.ToString(ent["fseq"])}行商品【基本单位】与商品基础信息【基本单位】不一致！",
                            DataEntity = dataEntity,
                        });
                    }
                    if (!Convert.ToString(ent["fstockunitid"]).EqualsIgnoreCase(Convert.ToString(materialObj["fstockunitid"])))
                    {
                        result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"{formInfo.Caption}【{dataEntity["fbillno"]}】第{Convert.ToString(ent["fseq"])}行商品【库存单位】与商品基础信息【库存单位】不一致！",
                            DataEntity = dataEntity,
                        });
                    }
                }
                ////校验同维度商品的【单位体积】是否一致
                //var proGroups = currEntrys.GroupBy(t => new
                //{
                //    fmaterialid = t["fmaterialid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fmaterialid"]).Trim(),
                //    fattrinfo = t["fattrinfo"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fattrinfo"]).Trim(),
                //    fcustomdesc = t["fcustomdesc"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["fcustomdesc"]),
                //    funitid = t["funitid"].IsNullOrEmptyOrWhiteSpace() ? "" : Convert.ToString(t["funitid"])
                //});
                //foreach (var groupItem in proGroups)
                //{
                //    var volGroups = groupItem.GroupBy(t => Convert.ToDecimal(t["fsinglevolume"]));//按照【单位体积】再次分组
                //    if (volGroups.Count() > 1)
                //    {
                //        //如果不止一个分组则表示有不一样的体积，需提示
                //        var materialObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(groupItem.Key.fmaterialid));
                //        result.Errors.Add(new ValidationResultEntry()
                //        {
                //            ErrorMessage = $"{formInfo.Caption}【{dataEntity["fbillno"]}】商品【{materialObj["fnumber"]}】体积不一致，请检查！",
                //            DataEntity = dataEntity,
                //        });
                //    }
                //}
            }

            return result;
        }
    }
}
