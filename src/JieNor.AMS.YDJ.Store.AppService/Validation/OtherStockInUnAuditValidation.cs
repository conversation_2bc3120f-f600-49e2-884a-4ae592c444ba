using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 其它入库单检查是否存在已完成的收货扫描任务
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_OtherStockInUnAuditValidation)]
    public class OtherStockInUnAuditValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult validationResult = new ValidationResult();

            var deliveryScanTasks = DeliveryScanTasks(dataEntities);

            foreach (var dataEntitie in dataEntities)
            {
                var result = deliveryScanTasks.FirstOrDefault(f => Convert.ToString(f["fsourcebillno"]).EqualsIgnoreCase(Convert.ToString(dataEntitie["fbillno"])));
                if (result != null)
                    validationResult.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"当前其他入库单【{dataEntitie["fbillno"]}】对应收货扫描任务【{Convert.ToString(result["fbillno"])}】已完成, 不允许反审核!",
                        DataEntity = dataEntitie
                    });
            }

            return validationResult;
        }

        /// <summary>
        /// 是否存在关联的收货扫描任务，并且返回收货扫描任务编号
        /// </summary>
        /// <param name="dataEntitie">当前单据</param>
        /// <param name="receptionScanTasks">收货扫描任务集合</param>
        /// <returns></returns>
        private string IsHasAuditedDeliveryScanTask(DynamicObject dataEntitie, IEnumerable<DynamicObject> receptionScanTasks)
        {
            var fsourcetype = Convert.ToString(dataEntitie["fsourcetype"]);
            var fsourcenumber = Convert.ToString(dataEntitie["fsourcenumber"]);

            var receptionScanTask = receptionScanTasks.FirstOrDefault(item => Convert.ToString(item["fsourceformid"]) == fsourcetype && Convert.ToString(item["fsourcebillno"]) == fsourcenumber);
            if (receptionScanTask == null)
                return null;
            return Convert.ToString(receptionScanTask["fbillno"]) ?? "";
        }

        /// <summary>
        /// 查询收货扫描任务
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> DeliveryScanTasks(DynamicObject[] dataEntities)
        {
            var fSourceNumbers = dataEntities.Select(f => Convert.ToString(f["fbillno"])).ToList();

            var sqlString = $"SELECT DISTINCT re.fbillno ,rt.fsourceformid,rt.fsourcebillno FROM t_bcm_receptionscantask AS re left JOIN t_bcm_rescantaskentity AS rt ON re.fid = rt.fid WHERE rt.fsourceformid ='stk_otherstockin' AND rt.fsourcebillno IN ({fSourceNumbers.JoinEx(",", true)}) AND re.ftaskstatus = 'ftaskstatus_04' and re.fmainorgid='{this.Context.Company}' ";
            return DBService.ExecuteDynamicObject(this.Context, sqlString).ToList();
        }

        private IEnumerable<string> FSourceTypes(DynamicObject[] dataEntities)
        {
            var _list = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                _list.Add(Convert.ToString(dataEntity["fsourcetype"]));
                var entryObjs = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entryObj in entryObjs)
                {
                    _list.Add(Convert.ToString(entryObj["fsourceformid"]));
                }
            }
            return _list.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
        }
        private IEnumerable<string> FSourceNumbers(DynamicObject[] dataEntities)
        {
            var _list = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                _list.Add(Convert.ToString(dataEntity["fsourcenumber"]));
                var entryObjs = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entryObj in entryObjs)
                {
                    _list.Add(Convert.ToString(entryObj["fsourcebillno"]));
                }
            }
            return _list.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
        }




    }
}
