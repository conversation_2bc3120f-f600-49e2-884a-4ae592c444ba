using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 收支记录的<保存><保存并提交><保存并审核><提交>时，需要校验：
    /// http://dmp.jienor.com:81/zentao/task-view-38327.html
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_IncomedisburseValidation)]
    public class IncomedisburseValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult validationResult = new ValidationResult();
            foreach (var dataEntitie in dataEntities)
            {
                var famount = Convert.ToDecimal(dataEntitie["famount"]);
                //校验金额不能小于0
                if (famount <= 0)
                {
                    validationResult.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"金额必须大于0！",
                        DataEntity = dataEntitie
                    });
                }

                var fcontactunitid = Convert.ToString(dataEntitie["fcontactunitid"]);//代收单位
                var fcontactunittype = Convert.ToString(dataEntitie["fcontactunittype"]);//代收单位类型
                if (!fcontactunitid.IsNullOrEmptyOrWhiteSpace() && fcontactunittype.IsNullOrEmptyOrWhiteSpace())
                {
                    validationResult.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"已选代收单位，代收单位类型不能为空！",
                        DataEntity = dataEntitie
                    });
                }
                if (fcontactunitid.IsNullOrEmptyOrWhiteSpace() && !fcontactunittype.IsNullOrEmptyOrWhiteSpace())
                {
                    validationResult.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"已选代收单位类型，代收单位不能为空！",
                        DataEntity = dataEntitie
                    });
                }

                //源单编号为销售出库单时需校验，【实退货款金额】-汇总其它退款记录的【结算金额】>【结算金额】
                var sourceformid = Convert.ToString(dataEntitie["fsourceformid"]);//源单FromId
                if (sourceformid == "stk_sostockreturn")
                {
                    var fsourceid = Convert.ToString(dataEntitie["fsourceid"]);//源单id

                    //相关判断
                    //取销售退货单所有的退款记录
                    string sql = $@"select famount,fid from t_coo_incomedisburse
                                    where fmainorgid=@fmainorgid and fbizdirection=@fbizdirection and fsourceid=@fsourceid";

                    List<SqlParam> sqlParams = new List<SqlParam>();
                    sqlParams.Add(new SqlParam("@fmainorgid", DbType.String, dataEntitie["fmainorgid"] as string));
                    sqlParams.Add(new SqlParam("@fsourceid", DbType.String, fsourceid));
                    sqlParams.Add(new SqlParam("@fbizdirection", DbType.String, "bizdirection_02"));//退款

                    var dbService = userCtx.Container.GetService<IDBService>();
                    var result = dbService.ExecuteDynamicObject(this.Context, sql, sqlParams);

                    var otherfamount = 0M;
                    foreach (var item in result)
                    {
                        if ((string)item["fid"] != (string)dataEntitie["Id"])
                        {
                            otherfamount += Convert.ToDecimal(item["famount"]);
                        }
                    }

                    // 查询销售退货单实退款金额
                    var settleMain = userCtx.LoadBizBillHeadDataById(sourceformid, fsourceid, "factualreturnamount");
                    if (settleMain == null)
                    {
                        validationResult.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"交易关联业务单据主体信息不存在",
                            DataEntity = dataEntitie
                        });
                    }
                    else
                    {
                        //收支记录在<保存> < 提交 >< 审核 > 时，都需校验：销售退货单的【结算金额】>【实退货款金额】-汇总其它退款收支记录的【结算金额】
                        var factualreturnamount = Convert.ToDecimal(settleMain["factualreturnamount"]);
                        if (famount > factualreturnamount - otherfamount)
                        {
                            validationResult.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = $"对不起，当前结算金额已超出销售退货单的实退货款金额，请重新调整结算金额！",
                                DataEntity = dataEntitie
                            });
                        }
                    }
                }
            }

            return validationResult;
        }
    }
}
