using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Validation.Pur.PurchaseOrderApplyChg
{
    /// <summary>
    /// 采购变更申请单：保存提交校验器
    /// </summary>
    public class PurOrderApplyChgSaveValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        public PurOrderApplyChgSaveValidation()
        {
        }

        public PurOrderApplyChgSaveValidation(bool autosubmit, bool autoaudit)
        {
            _autosubmit = autosubmit;
            _autoaudit = autoaudit;
        }

        public PurOrderApplyChgSaveValidation(bool autosubmit)
        {
            this._autosubmit = autosubmit;
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        /// <summary>
        /// 保存提交
        /// </summary>
        private bool _autosubmit { get; set; }

        /// <summary>
        /// 保存审核
        /// </summary>
        private bool _autoaudit { get; set; }


        /// <summary>
        /// 业务表单模型
        /// </summary>
        private HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 校验结果
        /// </summary>
        private ValidationResult Result { get; set; } = new ValidationResult();

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            this.HtmlForm = formInfo;

            CheckLevelOneOrderChgStatus(userCtx, dataEntities);

            CheckLevelOneOrderUnshippedQuantity(userCtx,dataEntities);

            CheckEntryNewQty(userCtx, dataEntities);

            return this.Result;
        }

        /// <summary>
        /// 检查二级采购订单的一级销售合同的变更状态
        /// '0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntitys"></param>
        private void CheckLevelOneOrderChgStatus(UserContext userCtx, DynamicObject[] dataEntitys)
        {
            if (this._autosubmit)
            {
                var onelvodernoList = dataEntitys.Where(x => !Convert.ToString(x["fleveloneorderno"]).IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fleveloneorderno"])).ToList();

                if (onelvodernoList != null && onelvodernoList.Any())
                {
                    var distinctOnelvOneNoList = onelvodernoList.Distinct().ToList();

                    var parentAgentId = userCtx.ParentCompanyId;

                    var parentAgentCtx = userCtx.CreateAdminDbContext(parentAgentId);

                    var sqlStr = $" fbillno in ({String.Join(",", distinctOnelvOneNoList.Select(x => $"'{x}'"))}) and fmainorgid = '{parentAgentId}' ";

                    var loadOrderTempDys = parentAgentCtx.LoadBizBillHeadDataByACLFilter("ydj_order", sqlStr, "fbillno,fchangestatus,fstatus");

                    if (loadOrderTempDys != null && loadOrderTempDys.Any())
                    {
                        foreach (var loadOrderTempDy in loadOrderTempDys)
                        {
                            var changeStatus = Convert.ToString(loadOrderTempDy["fchangestatus"]);

                            var orderBillNo = Convert.ToString(loadOrderTempDy["fbillno"]);

                            //当一级销售合同.【单据头.变更状态】=变更中 或者 变更已提交。
                            if (changeStatus.Equals("1") || changeStatus.Equals("3"))
                            {
                                var findLvOneOrderIsChgOrChgSumitDy = dataEntitys.FirstOrDefault(x => Convert.ToString(x["fleveloneorderno"]).Equals(orderBillNo));

                                if (findLvOneOrderIsChgOrChgSumitDy != null)
                                {
                                    this.Result.Errors.Add(new ValidationResultEntry()
                                    {
                                        ErrorMessage = $"一级合同正在变更中，不允许提交变更申请，请核查！",
                                        DataEntity = findLvOneOrderIsChgOrChgSumitDy,
                                    });
                                }
                            }

                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查二级采购订单变更减少的数量是否大于一级合同未出库商品数量
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntitys"></param>
        private void CheckLevelOneOrderUnshippedQuantity(UserContext userCtx, DynamicObject[] dataEntitys)
        {
            //有自动提交才校验(会生成一级合同变更申请单)
            if (this._autosubmit)
            {
                var onelvodernoList = dataEntitys.Where(x => !Convert.ToString(x["fleveloneorderno"]).IsNullOrEmptyOrWhiteSpace()).Select(x => Convert.ToString(x["fleveloneorderno"])).ToList();

                if (onelvodernoList != null && onelvodernoList.Any())
                {
                    var distinctOnelvOneNoList = onelvodernoList.Distinct().ToList();

                    var parentAgentId = userCtx.ParentCompanyId;

                    var parentAgentCtx = userCtx.CreateAdminDbContext(parentAgentId);

                    var loadLeaveOneOrderDys = parentAgentCtx.LoadBizDataByNo("ydj_order", "fbillno", distinctOnelvOneNoList);

                    foreach (var dataEntity in dataEntitys)
                    {
                        var findLeavelOneOrderDy = loadLeaveOneOrderDys.FirstOrDefault(x => Convert.ToString(x["fbillno"]).Equals(Convert.ToString(dataEntity["fleveloneorderno"])));

                        if (findLeavelOneOrderDy != null)
                        {
                            var findLeaveOneOrderEntrys = findLeavelOneOrderDy["fentry"] as DynamicObjectCollection;

                            var purchaseOrderApplyChgEntrys = dataEntity["fentity"] as DynamicObjectCollection;

                            foreach (var purchaseOrderApplyChgEntry in purchaseOrderApplyChgEntrys)
                            {
                                var seq = Convert.ToString(purchaseOrderApplyChgEntry["fseq"]);
                                //采购数量
                                var bizQty = Convert.ToDecimal(purchaseOrderApplyChgEntry["fbizqty"]);

                                //采购数量(新)
                                var newBizQty = Convert.ToDecimal(purchaseOrderApplyChgEntry["fnewbizqty"]);

                                //采购订单明细行id
                                var sourcePurchaseOrderEntryId = Convert.ToString(purchaseOrderApplyChgEntry["fsourceentryid"]);

                                //A=【采购数量】-【采购数量（新）】
                                var diffQty = bizQty - newBizQty;

                                //找到对应的销售合同明细行
                                var findLeaveOneOrderEntry = findLeaveOneOrderEntrys.FirstOrDefault(x => Convert.ToString(x["fsourceentryid_e"]).Equals(sourcePurchaseOrderEntryId));

                                if (findLeaveOneOrderEntry != null)
                                {
                                    //销售合同 销售数量
                                    var orderBizQty = Convert.ToDecimal(findLeaveOneOrderEntry["fbizqty"]);

                                    //销售合同 销售退还数量
                                    var orderBizReturnQty = Convert.ToDecimal(findLeaveOneOrderEntry["fbizreturnqty"]);

                                    //销售合同 已推出库数
                                    var orderTransOutQty = Convert.ToDecimal(findLeaveOneOrderEntry["ftransoutqty"]);

                                    //B =【销售数量】+【销售退换数量】-【已推出库数】
                                    var orderCompareQty = (orderBizQty + orderBizReturnQty) - orderTransOutQty;

                                    //若A>B，则视为一级合同未出库商品数量无法满足变更减少的数量，需做校验。
                                    if (diffQty > orderCompareQty)
                                    {
                                        this.Result.Errors.Add(new ValidationResultEntry()
                                        {
                                            ErrorMessage = $"第{seq}行商品变更减少的数量大于一级合同未出库商品数量，不允许变更，请与一级跟单核查货物是否已出库！",
                                            DataEntity = dataEntity,
                                        });
                                    }
                                }
                            }

                        }
                    }
                }
            }
            
        }

        /// <summary>
        /// 获取采购订单变更单的变更数量
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dataEntitys"></param>
        private void CheckEntryNewQty(UserContext userCtx, DynamicObject[] dataEntitys)
        {
            foreach (var dataEntity in dataEntitys)
            {
                var entitys = dataEntity["fentity"] as DynamicObjectCollection;
                foreach (var entity in entitys)
                {
                    var seq = Convert.ToString(entity["fseq"]);

                    var bizQty = Convert.ToDecimal(entity["fbizqty"]);

                    var newBizQty = Convert.ToDecimal(entity["fnewbizqty"]);

                    if (bizQty - newBizQty < 0)
                    {
                        this.Result.Errors.Add(new ValidationResultEntry()
                        {
                            ErrorMessage = $"第{seq}行商品变更的新采购数量大于采购数量，请检查!",
                            DataEntity = dataEntity,
                        });
                        break;
                    }

                }
            }
        }
    }
}