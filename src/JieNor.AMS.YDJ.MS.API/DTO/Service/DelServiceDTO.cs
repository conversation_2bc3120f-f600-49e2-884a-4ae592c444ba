using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO
{
    /// <summary>
    /// 总部删除服务单接口
    /// </summary>
    [Api("总部删除服务单接口")]
    [Route("/msapi/service/deletefw")]
    [Authenticate]
    public class DelServiceDTO : BaseDTO
    {
        /// <summary>
        /// 汇总列表
        /// </summary>
        public List<ServiceCancelEntry> Data { get; set; }

        public class ServiceCancelEntry
        {
            /// <summary>
            /// 服务单编码
            /// </summary>
            public string BillNo { get; set; }
            /// <summary>
            /// 经销商编码
            /// </summary>

            public string AgentNo { get; set; }
            internal string AgentId { get; set; }

        }
    }
}
