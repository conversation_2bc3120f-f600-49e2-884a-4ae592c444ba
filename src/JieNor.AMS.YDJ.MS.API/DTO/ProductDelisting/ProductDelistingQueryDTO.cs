using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.ProductDelisting
{
    /// <summary>
    /// 下市商品数据总部下发接口
    /// </summary>
    [Api("下市商品数据总部查询接口")]
    [Route("/msapi/ProductDelisting/Query")]
    [Authenticate]
    [OperationLogFilter("ydj_productdelisting")]
    public class ProductDelistingQueryDTO : BaseDTO
    {
        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }

        //public List<ProductDelistingBase> Data { get; set; }
        public List<string> Data { get; set; }
    }

    /// <summary>
    /// 查询接口响应类
    /// </summary>
    public class ProductDelistingQueryResponse : ProductDelistingBase
    {
        /// <summary>
        /// 中台数据ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 预估可下单采购数量
        /// </summary>
        public decimal EstimateQty { get; set; }

        /// <summary>
        /// 预警数量
        /// </summary>
        public decimal WarnQty { get; set; }

        /// <summary>
        /// 销售可下单数量
        /// </summary>
        public decimal SalCanPlaceOrderQty { get; set; }

        /// <summary>
        /// 销售累计已下单数量
        /// </summary>
        public decimal SumSalOrderQty { get; set; }

        /// <summary>
        /// 采购可下单数量
        /// </summary>
        public decimal PurCanPlaceOrderQty { get; set; }

        /// <summary>
        /// 采购累计已下单数量
        /// </summary>
        public decimal SumPurOrderQty { get; set; }
    }
}
