using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.STK.Inventoryverify

{
    [Api("直营订单总部状态回写接口")]
    [Authenticate]
    [Route("/msapi/direct/stk/pushinventorystatus")]
    [OperationLogFilter("stk_inventoryverify")]
    public class InventoryPushDTO : BaseDTO
    {
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string OrderBillNo { set; get; }

        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNumber { set; get; }

        /// <summary>
        /// sap订单号
        /// </summary>
        public string SapOrderNo { set; get; }

        /// <summary>
        /// SAP合同类型
        /// </summary>
        public string SapOrderType { set; get; }

        /// <summary>
        /// Sap合同状态
        /// '1':'已提交总部','2':'已驳回','3':'已终审'
        /// </summary>
        public string SapOrderStatus { set; get; }

        /// <summary>
        /// sap驳回原因
        /// </summary>
        public string SapRejectReason { set; get; }

        /// <summary>
        /// sap终审时间
        /// </summary>
        public DateTime SapFinalAuditTime { set; get; }
    }
}