using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.Order.DirectOrder
{
    [Api("回传共享计提单号接口")]
    [Authenticate]
    [Route("/msapi/direct/order/pushdirectordersharecostbillno")]
    [OperationLogFilter("ydj_order")]
    public class PushDirectOrderShareCostBillNoDTO : BaseDTO
    {
        /// <summary>
        /// 销售合同编码
        /// </summary>
        public string OrderBillNo { get; set; }
        
        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNo { get; set; }
        
        /// <summary>
        /// 共享计提单号
        /// </summary>
        public string ShareCostBillNo { set; get; }
    }
}