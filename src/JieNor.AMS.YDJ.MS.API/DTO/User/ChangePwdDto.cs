using JieNor.AMS.YDJ.MS.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.User
{
    /// <summary>
    /// 用户：修改密码接口
    /// </summary>
    [Api("修改密码接口")]
    [Route("/msapi/changepwd/sync")]
    [Authenticate]
    [OperationLogFilter("sec_user")]
    public class ChangePwdDto : BaseDTO
    {
        public List<ChangePwd> Data { get; set; }
        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }

    public class ChangePwd
    {
        public string signcode { get; set; }
        /// <summary>
        /// 账号
        /// </summary>
        public string account { get; set; }
        /// <summary>
        /// 旧密码
        /// </summary>
        public string oldpwd { get; set; }
        /// <summary>
        /// 新密码
        /// </summary>
        public string newpwd { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string operatorid { get; set; }
        /// <summary>
        ///  0：密码修改   1：密码重置（忘记密码短信重置） 2：管理员重置 
        /// </summary>
        public string type { get; set; }
    }
}
