using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.SelFittingsMap
{
    /// <summary>
    /// 选配配件映射：总部下发接口
    /// </summary>
    [Api("总部下发接口")]
    [Route("/msapi/sel_fittingsmap/push")]
    [Authenticate]
    //[OperationLogFilter("sel_fittingsmap")]
    public class SelFittingsMapPushDTO : BaseDTO
    {
        public JArray Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }
}