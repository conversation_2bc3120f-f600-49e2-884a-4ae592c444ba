using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.Ste.Customer
{
    /// <summary>
    /// 总部下发客户接口
    /// </summary>
    [Api("总部下发客户接口")]
    [Route("/msapi/Customer/push")]
    [Authenticate]
    public class PushCustomerDTO : BaseDTO
    {
        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNo { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }

        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }

        public List<PushCtMod> Data { get; set; } = new List<PushCtMod>();
    }

    public class PushCtMod
    {
        public string dealerNo { get; set; }

        public string fname { get; set; }

        public string fphone { get; set; }

        public string fwechat { get; set; }

        public string fcontacts { get; set; }

        public string fprovince { get; set; }
        public string fcity { get; set; }
        public string fregion { get; set; }
        public string memberNo { get; set; }
        public string code { get; set; }
        public List<cuscontacttryMod> fcuscontacttry { get; set; } = new List<cuscontacttryMod>();
        public List<dutyentryMod> fdutyentry { get; set; } = new List<dutyentryMod>();
        //金蝶消费者编码
        public List<cbsentryMod> fcbsentrymod { get; set; } = new List<cbsentryMod>();
    }

    public class cbsentryMod
    {
        public string fconsumernumber { get; set; }
    }

    public class dutyentryMod
    {
        public string fdutyid { get; set; }
        public string fdeptid { get; set; }
        public string fjointime { get; set; }

    }

    public class cuscontacttryMod
    {
        public string fcontact_a { get; set; }
        public string fphone_a { get; set; }
        public string fcarea { get; set; }
        public string faddress_a { get; set; }
        public string fprovince_a { get; set; }
        public string fcity_a { get; set; }
        public string fregion_a { get; set; }
        public string fisdefault { get; set; }
    }
}
