using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.DTO.Ste.Customerrecord
{
    /// <summary>
    /// 总部下发商机接口
    /// </summary>
    [Api("总部下发商机接口")]
    [Route("/msapi/customerrecord/push")]
    [Authenticate]
    public class PushCustomerrecordDTO : BaseDTO
    {
        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNo { get; set; }
        /// <summary>
        /// 表单数据Jarray，详细格式见RAP接口文档
        /// </summary>
        public JArray Data { get; set; }
    }
}
