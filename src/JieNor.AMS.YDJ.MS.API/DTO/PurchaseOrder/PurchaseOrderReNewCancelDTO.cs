using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder
{
    /// <summary>
    /// 采购订单：焕新订单取消接口
    /// </summary>
    [Api("焕新订单取消接口")]
    [Route("/msapi/purchaseorder/renew_cancel")]
    [Authenticate]
    [OperationLogFilter("ydj_purchaseorder")]
    public class PurchaseOrderReNewCancelDTO : BaseDTO
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        public string BillNo { get; set; }

        /// <summary>
        /// 业务经销商id
        /// </summary>
        internal string BizAgentId { get; set; }

        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNo { get; set; }

        public bool IsRefund { get; set; }

        public JObject Data { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }


        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }
    }
}