using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MS.API.Filter;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder
{
    /// <summary>
    /// 采购订单：总部下发接口
    /// </summary>
    [Api("总部下发接口")]
    [Route("/msapi/purchaseorder/push")]
    [Authenticate]
    [OperationLogFilter("ydj_purchaseorder")]
    public class PurchaseOrderPushDTO : BaseDTO
    {
        //应收、应付单DTO
        /// <summary>
        /// 订单类型
        /// </summary>
        public string OrderType { get; set; }
        /// <summary>
        /// 数据来源单号
        /// </summary>
        public string Sourcenumber { get; set; }
        /// <summary>
        /// 业务日期
        /// </summary>
        public DateTime? Registdate { get; set; }
        /// <summary>
        /// 同步原因
        /// </summary>
        public string Syncreason { get; set; }

        public List<PayOrCollectPushEntry> PayOrCollectEntrys { get; set; }
        //应收应付明细
        public class PayOrCollectPushEntry
        {
            public int Seq { get; set; }
            /// <summary>
            /// 发票号
            /// </summary>
            public string Spart { get; set; }
            /// <summary>
            /// 发票号
            /// </summary>
            public string SpartName { get; set; }
            /// <summary>
            /// 发票号
            /// </summary>
            public string Invoiceno { get; set; }
            /// <summary>
            /// 子类
            /// </summary>
            public string Invoicetype { get; set; }
            /// <summary>
            /// 发票创建时间
            /// </summary>
            public DateTime Createtime { get; set; }
            /// <summary>
            /// 发票过账日期
            /// </summary>
            public DateTime Invoicedate { get; set; }
            /// <summary>
            /// 总金额
            /// </summary>
            public decimal Totalamount { get; set; }
            /// <summary>
            /// 备注
            /// </summary>
            public string Remark { get; set; }
        }


        //采购订单DTO

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string BillNo { get; set; }

        ///// <summary>
        ///// 经销商id
        ///// </summary>
        //public string AgentId { get; set; }
        /// <summary>
        /// 经销商编码
        /// </summary>
        public string AgentNo { get; set; }

        /// <summary>
        /// 供方预计交货日期
        /// </summary>
        public DateTime? DeliveryPlanDate { get; set; }

        ///// <summary>
        ///// 供应商id
        ///// </summary>
        //public string SupplierId { get; set; }

        ///// <summary>
        ///// 供应商编码
        ///// </summary>
        //public string SupplierNo { get; set; }

        ///// <summary>
        ///// 送达方id
        ///// </summary>
        //public string DeliverId { get; set; }

        /// <summary>
        /// 送达方编码
        /// </summary>
        public string DeliverNo { get; set; }

        /// <summary>
        /// 总部合同号
        /// </summary>
        public string HqderNo { get; set; }

        /// <summary>
        /// 总部合同状态
        /// 01：新建
        /// 02：提交至总部
        /// 03：已终审
        /// 04：排产中
        /// 05：驳回
        /// </summary>
        public string HqderStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 总部合同类型
        /// </summary>
        public string fhqdertype { get; set; }

        /// <summary>
        /// 总部终审时间
        /// </summary>
        public string fhqderauditdate { get; set; }

        /// <summary>
        /// 商品明细
        /// </summary>
        public List<PurchaseOrderPushEntry> Entrys { get; set; }

        /// <summary>
        /// 请求时间戳
        /// </summary>
        public string RequestStamp { get; set; }


        private bool _isRetry = false;
        /// <summary>
        /// 是否错误重试（默认：否）
        /// </summary>
        public bool IsRetry
        {
            get
            {
                return _isRetry;
            }
            set
            {
                _isRetry = value;
            }
        }

        /// <summary>
        /// 商品明细
        /// </summary>
        public class PurchaseOrderPushEntry
        {
            /// <summary>
            /// 交易流水号
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// 序号
            /// </summary>
            public int Seq { get; set; }

            ///// <summary>
            ///// 商品id
            ///// </summary>
            //public string ProductId { get; set; }

            /// <summary>
            /// 商品编码
            /// </summary>
            public string ProductNo { get; set; }
            /// <summary>
            /// 商品名称
            /// </summary>
            public string ProductName { get; set; }
            /// <summary>
            /// 单位
            /// </summary>
            public string Unitid { get; set; }

            /// <summary>
            /// 辅助属性
            /// </summary>
            public List<AuxPropValDTO> AuxPropVals { get; set; }

            ///// <summary>
            ///// 业绩品牌id
            ///// </summary>
            //public string ResultBrandId { get; set; }

            /// <summary>
            /// 业绩品牌编码
            /// </summary>
            public string ResultBrandNo { get; set; }

            /// <summary>
            /// 采购价
            /// </summary>
            public decimal Price { get; set; }

            /// <summary>
            /// 折率
            /// </summary>
            public decimal DistRate { get; set; }

            /// <summary>
            /// 折扣额
            /// </summary>
            public decimal DistAmount { get; set; }

            /// <summary>
            /// 采购数量
            /// </summary>
            public decimal Qty { get; set; }

            /// <summary>
            /// 是否非标
            /// </summary>
            public bool UnstdType { get; set; }

            /// <summary>
            /// 非标审批状态
            /// 01：新建
            /// 02：待审批
            /// 03：审批成功
            /// 04：驳回
            /// 05：待定价
            /// 06：终审
            /// </summary>
            public string UnstdTypeStatus { get; set; }

            /// <summary>
            /// 非标审批意见
            /// </summary>
            public string UnstdTypeComment { get; set; }

            /// <summary>
            /// 备注
            /// </summary>
            public string Description { get; set; }

            /// <summary>
            /// 套件组合号
            /// </summary>
            public string selSuiteNumber { get; set; }

            /// <summary>
            /// 配件组合号
            /// </summary>
            public string PartsCombNumber { get; set; }

            /// <summary>
            /// 组合号
            /// </summary>
            public string Combine { get; set; }

            ///// <summary>
            ///// 家纺套件要求id
            ///// </summary>
            //public string SelSuiteRequireId { get; set; }

            /// <summary>
            /// 家纺套件要求
            /// </summary>
            public string SelSuiteRequire { get; set; }
            /// <summary>
            /// 预计业务日期
            /// </summary>
            public DateTime? PlanDate { get; set; }

            public string Cancelreason { get; set; }

            /// <summary>
            /// 需求日期
            /// </summary>
            public DateTime? DemandDate { get; set; }

            /// <summary>
            /// 折扣项
            /// </summary>
            public List<DiscountItem> fdiscounts { get; set; }

            /// <summary>
            /// SAP手工单尺寸
            /// </summary>
            public string sapProdDesc { get; set; }
            /// <summary>
            /// 是否虚拟商品
            /// </summary>
            public bool IsVisual { get; set; } = false;

            ///// <summary>
            ///// 含税出厂价
            ///// </summary>
            //public decimal ftaxprice { get; set; }

            ///// <summary>
            ///// 星级折扣/上市折扣
            ///// </summary>
            //public decimal fstardiscount { get; set; }

            ///// <summary>
            ///// 深度护理折扣
            ///// </summary>
            //public decimal fdepthdiscount { get; set; }

            ///// <summary>
            ///// 新品折扣
            ///// </summary>
            //public decimal fnewdiscount { get; set; }

            ///// <summary>
            ///// 费用支持返利
            ///// </summary>
            //public decimal fexpenserebate { get; set; }

            ///// <summary>
            ///// 其他折扣
            ///// </summary>
            //public decimal fotherdiscount { get; set; }

            ///// <summary>
            ///// SAP折扣总额
            ///// </summary>
            //public decimal fsapdiscount { get; set; }

            public class DiscountItem
            {
                /// <summary>
                /// 折扣类型
                /// ZP01出厂价
                /// ZK03上市折扣
                /// ZK05深度护理折扣
                /// ZK06新品折扣
                /// ZK99费用支持返利
                /// </summary>
                public string ftype { get; set; }

                public decimal fvalue { get; set; }
            }
        }
    }
}