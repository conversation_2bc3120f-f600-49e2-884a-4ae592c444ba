using JieNor.AMS.YDJ.MS.API.DTO.InventoryVerify;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.InventoryVerify
{
    /// <summary>
    /// 获取慕思中台返回的产品信息
    /// </summary>
    public class BarCodeReturnProductInfoController : BaseController<BarCodeReturnProductInfoDTO>
    {
        public string FormId
        {
            get { return "stk_inventoryverify"; }
        }

        protected static HtmlForm CustomerForm { get; set; }

        public override object Execute(BarCodeReturnProductInfoDTO dto)
        {
            var resp = new BaseResponse<object>();
            CustomerForm = this.MetaModelService.LoadFormModel(this.Context, FormId);

            if (dto.data.Count <= 0)
            {
                resp.Success = false;
                resp.Message = "传入参数为空，请检查!";
                return resp;
            }
            List<string> errMsg = new List<string>();
            bool isError = false;
            foreach (var item in dto.data)
            {
                if (string.IsNullOrWhiteSpace(item.barcode))
                {
                    isError = true;
                }
                if (string.IsNullOrWhiteSpace(item.prodNum))
                {
                    isError = true;
                }
                if (string.IsNullOrWhiteSpace(item.packMethod))
                {
                    isError = true;

                }
                if (string.IsNullOrWhiteSpace(item.set) && item.packMethod == "")
                {
                    errMsg.Add("当包装方式为2(合包)时，包装方式(套)为必传项，不能为空!");
                }
            }
            if (isError)
            {
                resp.Success = false;
                resp.Message = "参数【条码】,【产品编码】，【包装方式】为必传项，不能为空，请检查是否正确传递";
                return resp;
            }

            if (errMsg.Count > 0)
            {
                resp.Success = false;
                resp.Message = errMsg.First();
                return resp;
            }

            #region 根据返回的条码信息写入盘点明细并保存提交审核

            //根据返回的条码记录获取所有的盘点单扫描记录
            var barcodes = dto.data.Select(f => f.barcode).Distinct().ToList();
            string sql = string.Format(@"
                select a.fid,b.fstorehouseid,b.fid as barcodeid 
                from t_stk_invverify a with(nolock)
                inner join t_bcm_scanresult b with(nolock) on b.fsourcebillno=a.fbillno and b.fsourceformid = a.FFormId
                where a.fcancelstatus='0' and b.fbarcodetext in ('{0}')".Fmt(string.Join("','", barcodes)));
            var scandatas = this.Context.ExecuteDynamicObject(sql, null).ToList();

            //根据条码扫描记录获取全部的盘点单
            var invids = scandatas.Select(f => f["fid"]?.ToString()).Distinct().ToList();
            var allinventoryverifys = this.Context.LoadBizDataById(FormId, invids);

            if (allinventoryverifys.Count <= 0)
            {
                resp.Success = false;
                resp.Message = "传过来的条码未找到对应的盘点单!";
                return resp;
            }

            foreach (var item in allinventoryverifys)
            {
                if (item["fstatus"]?.ToString() == "E")
                {
                    resp.Success = false;
                    resp.Message = "条码已经提交过了，对应的单据已审核，不能重复提交!";
                    return resp;
                }
            }
            //根据返回的物料编码获取所有的商品
            var materialIds = dto.data.Select(f => f.prodNum).Distinct().ToList();
            var allmaterials = this.Context.LoadBizDataByFilter("ydj_product", $" fnumber in ('{string.Join("','", materialIds)}')");
            if (allmaterials.Count <= 0)
            {
                resp.Success = false;
                resp.Message = "传过来的物料编码未找到!";
                return resp;
            }

            //根据返回的条码获取所有的仓库信息
            var warehouseids = scandatas.Select(f => f["fstorehouseid"]?.ToString()).Distinct().ToList();
            var warehouses = this.Context.LoadBizDataById("ydj_storehouse", warehouseids);

            //根据返回的条码获取所有的条码扫描记录
            var allbarcodeids = scandatas.Select(f => f["barcodeid"]?.ToString()).Distinct().ToList();
            var invScanEntitys = this.Context.LoadBizDataById("bcm_scanresult", allbarcodeids);


            foreach (var inv in allinventoryverifys)
            {
                var bcList = new List<string>();
                var invEntitys = inv["fentity"] as DynamicObjectCollection;
                foreach (var item in dto.data)
                {
                    var invScanEntity = invScanEntitys.Where(f => f["fbarcodetext"]?.ToString() == item.barcode).FirstOrDefault();
                    var warehouse = warehouses.Where(f => f["Id"]?.ToString() == invScanEntity["fstorehouseid"]?.ToString()).FirstOrDefault();
                    if (invScanEntity != null)
                    {
                        decimal pdqty = 0;
                        switch (item.packMethod)
                        {
                            case "0"://标准
                                pdqty = 1;
                                break;
                            case "1"://分包
                                var newbc = item.barcode.Split('-').FirstOrDefault();
                                if (!bcList.Contains(newbc))
                                {
                                    bcList.Add(newbc);
                                    pdqty = 1;
                                }
                                break;
                            case "2"://合包，直接取包装方式（套）的值
                                pdqty = Convert.ToDecimal(item.set);
                                break;
                        }

                        var material = allmaterials.FirstOrDefault(f => f["fnumber"]?.ToString() == item.prodNum);
                        var baseunitqty = GetUnitVonversion(this.Context, material["fstockunitid"]?.ToString(), pdqty, material["funitid"]?.ToString());

                        #region 获取定制说明
                        //加工要求
                        StringBuilder fcustomdesc = new StringBuilder();
                        if (Convert.ToBoolean(material["freplaceflag"]) || !string.IsNullOrWhiteSpace(item.requirements))
                        {
                            fcustomdesc.Append("加工要求：" + item.requirements);
                        }
                        var materialtypedata = this.Context.LoadBizDataById("ydj_category", material["fcategoryid"]?.ToString());
                        var materialtype = materialtypedata == null ? "" : materialtypedata["fname"]?.ToString();
                        //ddd
                        if (materialtype == "床箱底版")
                        {
                            foreach (var attribute in item.items)
                            {
                                if (attribute.attr == "床架型号" || attribute.attr == "床架其他定制")
                                {
                                    fcustomdesc.Append(String.IsNullOrWhiteSpace(fcustomdesc?.ToString()) ? attribute.attr + ":" + attribute.attrValue : "，" + attribute.attr + "：" + attribute.attrValue);
                                }
                            }
                        }
                        #endregion

                        #region 获取辅助属性
                        var customerattribute = new StringBuilder();
                        foreach (var attribute in item.items)
                        {
                            //当商品类别为床箱底版时，这个按需求文档放到定制说明中
                            if ((attribute.attr == "床架型号" || attribute.attr == "床架其他定制") && materialtype == "床箱底版")
                            {
                                continue;
                            }
                            customerattribute.Append(String.IsNullOrWhiteSpace(customerattribute?.ToString()) ? attribute.attr + ":" + attribute.attrValue : "，" + attribute.attr + "：" + attribute.attrValue);
                        }
                        #endregion

                        var invEntity = invEntitys.Where(f => f["fmaterialid"]?.ToString() == material["Id"]?.ToString()
                            && f["fstorehouseid"]?.ToString() == invScanEntity["fstorehouseid"]?.ToString()
                            && f["fstorelocationid"]?.ToString() == invScanEntity["fstorelocationid"]?.ToString()
                            && f["fcustomdesc"]?.ToString() == fcustomdesc.ToString()
                            //&& f["fattrinfo"]?.ToString() == ""
                            ).FirstOrDefault();
                        if (invEntity != null)
                        {
                            invEntity["fpdqty"] = Convert.ToDecimal(invEntity["fpdqty"]) + baseunitqty;
                            invEntity["fpyqty"] = Convert.ToDecimal(invEntity["fpyqty"]) + baseunitqty;
                            invEntity["fbizpdqty"] = Convert.ToDecimal(invEntity["fbizpdqty"]) + pdqty;
                            invEntity["fbizpyqty"] = Convert.ToDecimal(invEntity["fbizpyqty"]) + pdqty;
                        }
                        else
                        {
                            invEntity = invEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                            invEntity["fmaterialid"] = material["Id"];//商品                            
                            invEntity["funitid"] = material["funitid"]; //基本单位
                            invEntity["fpdqty"] = baseunitqty;//基本单位盘点数量
                            invEntity["fpyqty"] = baseunitqty;//基本单位盘盈数量
                            invEntity["fstockunitid"] = material["fstockunitid"];//库存单位
                            invEntity["fbizpdqty"] = pdqty;//盘点数量
                            invEntity["fbizpyqty"] = pdqty;//盘盈数量
                            invEntity["fstorehouseid"] = invScanEntity["fstorehouseid"];//仓库
                            invEntity["fstorelocationid"] = invScanEntity["fstorelocationid"];//仓位
                            invEntity["fcustomdesc"] = fcustomdesc.ToString();//定制说明
                            //invEntity["fattrinfo"] = customerattribute;//辅助属性
                            invEntity["fstockstatus"] = warehouse == null ? "" : warehouse["fstockid"];//库存状态
                            //invEntity["funstdtype"] = material["funstdtype"];//是否非标

                            invEntitys.Add(invEntity);
                        }
                    }
                }

                inv["fscale"] = "2";//扫描状态为盘点完成
            }

            //保存数据
            var geteway = this.Context.Container.GetService<IHttpServiceInvoker>();
            var ressave = geteway.InvokeBillOperation(this.Context, FormId, allinventoryverifys, "save", null);
            if (ressave.IsSuccess)
            {
                foreach (var item in allinventoryverifys)
                {
                    var ressubmit = geteway.InvokeBillOperation(this.Context, FormId, allinventoryverifys, "submit", null);
                    if (ressubmit.IsSuccess)
                    {
                        var resaudit = geteway.InvokeBillOperation(this.Context, FormId, allinventoryverifys, "audit", null);
                        if (!resaudit.IsSuccess)
                        {
                            resp.Success = false;
                            resp.Message = "执行审核失败:" + string.Join("", ressave.ComplexMessage.ErrorMessages);
                            return resp;
                        }
                    }
                    else
                    {
                        resp.Success = false;
                        resp.Message = "执行提交失败:" + string.Join("", ressave.ComplexMessage.ErrorMessages);
                        return resp;
                    }
                }

            }
            else
            {
                resp.Success = false;
                resp.Message = "执行保存失败:" + string.Join("", ressave.ComplexMessage.ErrorMessages);
                return resp;
            }

            #endregion

            resp.Success = true;
            resp.Message = "执行成功";
            return resp;
        }

        /// <summary>
        /// 单位换算
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="baseunit">当前单位</param>
        /// <param name="baseqty">当前单位数量</param>
        /// <param name="tounit">目标单位</param>
        /// <returns></returns>
        private static decimal GetUnitVonversion(UserContext ctx, string baseunit, decimal baseqty, string tounit)
        {
            decimal toqty = baseqty;
            if (string.IsNullOrWhiteSpace(baseunit) || string.IsNullOrWhiteSpace(tounit))
            {
                return toqty;
            }
            if (baseunit == tounit)
            {
                return toqty;
            }
            //获取对应目标单位及基本单位的换算率
            var units = ctx.LoadBizDataById("ydj_unit", tounit);
            if (units == null) return 0;
            if (units["fbaseunitid"]?.ToString() != baseunit)
            {
                return toqty;
            }
            var froundtype = units["froundtype"]?.ToString();
            var fcvtrate = Convert.ToDecimal(units["fcvtrate"]);
            var fprecision = Convert.ToInt16(units["fprecision"]);

            switch (froundtype)
            {
                case "round":
                    toqty = Math.Round(baseqty * fcvtrate, fprecision);
                    break;
                case "ceil":
                    toqty = Math.Ceiling(baseqty * fcvtrate);
                    break;
                case "floor":
                    toqty = Math.Floor(baseqty * fcvtrate);
                    break;
                default:
                    toqty = Math.Round(baseqty * fcvtrate, fprecision);
                    break;
            }
            return toqty;
        }

        protected override Dictionary<string, string> CreateDistributedLocks(BarCodeReturnProductInfoDTO dto)
        {
            Dictionary<string, string> dicResult = new Dictionary<string, string>();
            foreach (var item in dto.data)
            {
                if (dicResult.ContainsKey($"DistributedLock:{this.FormId}:{item.id}"))
                {
                    continue;
                }
                dicResult.Add($"DistributedLock:{this.FormId}:{item.id}", $"产品信息 {item.id} 正在锁定中，请稍后再操作！");
            }
            return dicResult;
        }
    }
}
