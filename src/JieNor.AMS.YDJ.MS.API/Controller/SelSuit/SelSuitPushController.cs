using JieNor.AMS.YDJ.MS.API.DTO.Category;
using JieNor.AMS.YDJ.MS.API.DTO.SelFittingsMap;
using JieNor.AMS.YDJ.MS.API.DTO.SelSuit;
using JieNor.AMS.YDJ.MS.API.DTO.SelType;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.SelSuit
{
    public class SelSuitPushController : BaseController<SelSuitPushDTO>
    {

        public string FormId
        {
            get { return "sel_suite"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "fnumber";

        protected override string BizObjectFormId => this.FormId;

        //protected string[] IgnoreFields { get; set; } = new[] { "MidData"};
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(SelSuitPushDTO dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<object>();

            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);

            var dataEntities = ConvertToDyns(dt, dto.Data);

            var simplePara = new Dictionary<string, string>();
            simplePara.Add("RequestRawUrl", this.Request.RawUrl);
            simplePara.Add("__RawData__", dto.ToJson());
            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(this.Context, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "MSSaveSync",
                BillData = dataEntities.ToJson(),
                SimpleData = simplePara
            });

            resp = response?.OperationResult.ToResponseModel<object>();
            var srvData = response?.OperationResult?.SrvData?.ToJson() ?? "{}";
            resp.Data = JToken.Parse(srvData);

            return resp;
        }

        private IEnumerable<DynamicObject> ConvertToDyns(DynamicObjectType dt, JArray data)
        {
            if (data.IsNullOrEmpty()) return new List<DynamicObject>();

            List<DynamicObject> dyns = new List<DynamicObject>();

            foreach (JObject item in data)
            {
                var dyn = ConvertToDyn(dt, item);
                dyns.Add(dyn);
            }

            return dyns;
        }

        private DynamicObject ConvertToDyn(DynamicObjectType dt, JObject data)
        {
            var dyn = dt.CreateInstance() as DynamicObject;

            foreach (var property in data.Properties())
            {
                if (property.Value is JArray)
                {
                    var entryEntity = this.HtmlForm.GetEntryEntity(property.Name);
                    if (entryEntity == null) continue;

                    var entrys = dyn[entryEntity.PropertyName] as DynamicObjectCollection;

                    var dyns = ConvertToDyns(entryEntity.DynamicObjectType, (JArray)property.Value);

                    foreach (var item in dyns)
                    {
                        entrys.Add(item);
                    }

                    continue;
                }

                if (property.Value is JObject)
                {
                    continue;
                }

                var field = this.HtmlForm.GetField(property.Name);
                if (field == null) continue;

                dyn[field.PropertyName] = property.Value;
            }

            return dyn;
        }

        protected override Dictionary<string, string> CreateDistributedLocks(SelSuitPushDTO dto)
        {
            var hash = dto.ToJson().HashString();
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{hash}", $"套件 {hash} 正在锁定中，请稍后再操作！" }
            };
        }
    }
}
