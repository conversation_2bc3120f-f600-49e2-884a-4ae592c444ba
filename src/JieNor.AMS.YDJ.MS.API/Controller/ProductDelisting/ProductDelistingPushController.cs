using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.MS.API.DTO.ProductDelisting;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.ProductDelisting
{
    public class ProductDelistingPushController : BaseController<ProductDelistingPushDTO>
    {
        public string FormId
        {
            get { return "ydj_productdelisting"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "id";

        protected override string BizObjectFormId => this.FormId;

        public override object Execute(ProductDelistingPushDTO dto)
        {
            //this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
            //var recordFormId = "ydj_delistingqtyrecord";
            //var recordForm = this.MetaModelService.LoadFormModel(this.Context, recordFormId);

            var resp = new BaseResponse<MuSiData>();
            if (dto == null || dto.Data == null || !dto.Data.Any())
            {
                resp.Data.Flag = MuSiFlag.FAIL.ToString();
                resp.Data.ErrorMsgs.Add("请传递正确的数据！");
                return resp;
            }
            List<string> failNumbers = new List<string>();
            List<string> products = dto.Data.Where(a => !a.ProductNo.IsNullOrEmptyOrWhiteSpace()).Select(a => a.ProductNo).Distinct().ToList();
            List<string> seltype = dto.Data.Where(a => !a.SelType.IsNullOrEmptyOrWhiteSpace()).Select(a => a.SelType).Distinct().ToList();
            var productObjs = this.Context.LoadBizDataByNo("ydj_product", "fnumber", products);
            var typeObjs = this.Context.LoadBizDataByNo("sel_type", "fnumber", seltype);
            var exists = productObjs.Select(a => Convert.ToString(a["fnumber"])).ToList();
            var excepts = products.Except(exists).ToList();
            excepts.ForEach(a =>
            {
                var removeItem = dto.Data.Where(_ => _.ProductNo.Equals(a)).FirstOrDefault();
                if (removeItem != null)
                {
                    dto.Data.Remove(removeItem);
                }
            });
            var typeexists = typeObjs.Select(a => Convert.ToString(a["fnumber"])).ToList();
            var typeexcepts = seltype.Except(typeexists).ToList();
            typeexcepts.ForEach(a =>
            {
                var removeItem = dto.Data.Where(_ => !string.IsNullOrWhiteSpace(_.SelType)).Where(_ => Convert.ToString(_.SelType).Equals(a)).ToList();
                if (removeItem != null)
                {
                    removeItem.ForEach(c =>
                    {
                        dto.Data.Remove(c);
                    });
                }
            });
            failNumbers.AddRange(excepts);
            failNumbers.AddRange(typeexcepts);
            var topCtx = this.Context.CreateTopOrgDBContext();
            // 向麦浩系统发送请求
            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(topCtx, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "ProductDelistingPush",
                PageId = Guid.NewGuid().ToString("N"),
                SimpleData = new Dictionary<string, string>
                {
                    { "dto",JsonConvert.SerializeObject(dto)}
                }
            });
            if (response.OperationResult.IsSuccess)
            {
                resp.Message = "退市清单下发成功！";
                resp.Success = true;
                resp.Code = 200;
                resp.Data.FailedNumbers.AddRange(failNumbers);
                //this.Request.Items.Add("ffailnumbers", failNumbers);
                //this.Request.Items.Add("fsuccessnumber", dto.Data[]);
                return resp;
            }
            else
            {
                resp.Message = string.Join(",", response.OperationResult.ComplexMessage.ErrorMessages);
                resp.Success = false;
                resp.Code = 500;
                resp.Data.FailedNumbers.AddRange(failNumbers);
                //this.Request.Items.Add("ffailnumbers", failNumbers);
                //this.Request.Items.Add("ffailnumbers", dto.fbillno);
                return resp;
            }
        }
    }
}
