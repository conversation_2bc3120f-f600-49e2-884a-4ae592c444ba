using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.AMS.YDJ.MS.API.DTO.Order;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.Order
{
    /// <summary>
    /// OMS：线上订单进度（回传）
    /// </summary>
    public class OrderProcessController : BaseController<OrderProcessDTO>
    {
        public string FormId
        {
            get { return "ydj_order"; }
        }

        protected UserContext agentCtx { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(OrderProcessDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;
            string mes = "";
            #region 屏蔽
            //if (obj == null)
            //{
            //    resp.Message = "销售合同数据不存在！";
            //    resp.Success = false;
            //    resp.Code = 500;
            //    return resp;
            //}
            //if (Convert.ToInt32(obj["fcancelstatus"]) == 1)
            //{
            //    resp.Message = "销售合同:" + Convert.ToString(obj["fbillno"]) + "数据已作废！";
            //    resp.Success = false;
            //    resp.Code = 500;
            //    return resp;
            //}
            //var entrys = obj["fentry"] as DynamicObjectCollection;
            //if (dto.omsprogress == "-1")
            //{
            //    var entryItem = entrys.Where(a => Convert.ToString(a["ftranid"]).Equals(dto.tranid)).FirstOrDefault();
            //    if (entryItem != null)
            //    {
            //        //。如果对应商品行存在下有【作废状态】=“否”且【总部合同状态】=“提交至总部”或“已终审”的《采购订单》时，则需报错提示：“对不起，当前商品已转采购，总部状态为“提交至总部”或“已终审”，禁止作废！”
            //        var strSql = $@"select a.fid from T_YDJ_PURCHASEORDER as a with(nolock) inner join T_YDJ_POORDERENTRY as b with(nolock) on a.fid=b.fid where fsourceentryid ='{entryItem["id"]}' and a.fcancelstatus=0 and fhqderstatus in ('02','03') and b.fbizqty>0";
            //        using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            //        {
            //            if (dr.Read())
            //            {
            //                resp.Message = "对不起，当前商品已转采购，总部状态为“提交至总部”或“已终审”，禁止作废！";
            //                resp.Success = false;
            //                resp.Code = 500;
            //                return resp;
            //            }
            //        }
            //    }
            //}
            //var orderFormMate = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
            //bool attrIsChange = false;
            //bool ischange = false;
            //int num = 0;
            //List<string> entryIds = new List<string>();
            //foreach (var item in entrys)
            //{
            //    string ftranid = item["ftranid"] as string;
            //    if (ftranid.Equals(dto.tranid))
            //    {
            //        item["fomsbillno"] = dto.omsbillno;
            //        item["fomsoperate"] = dto.omsoperate;
            //        //3.OMS线上订单进度返回omsprogress为空 或 待接单 时，不更新销售合同明细.【定制订单进度】；
            //        if (!Convert.ToString(dto.omsprogress).IsNullOrEmptyOrWhiteSpace() && !("10".Equals(Convert.ToString(dto.omsprogress).Trim())))
            //            item["fomsprogress"] = dto.omsprogress;
            //        item["fomsbackreason"] = dto.hqreturnreason;
            //        item["faftreason"] = dto.serviceReasons;
            //        item["fomsreason"] = dto.serviceReasonsDesc;

            //        if (string.IsNullOrWhiteSpace(Convert.ToString(obj["forderattr"])))
            //        {
            //            attrIsChange = true;
            //            item["forderattr_e"] = dto.orderattr;
            //            obj["forderattr"] = dto.orderattr;
            //            obj["factivenumber"] = dto.activenumber;
            //            obj["factivediscount"] = dto.activediscount;
            //        }
            //        else
            //        {
            //            if (!Convert.ToInt32(obj["forderattr"]).Equals(dto.orderattr))
            //            {
            //                attrIsChange = true;
            //                item["forderattr_e"] = dto.orderattr;
            //                obj["forderattr"] = dto.orderattr;
            //                obj["factivenumber"] = dto.activenumber;
            //                obj["factivediscount"] = dto.activediscount;
            //            }
            //        }

            //        var deliverItem = agentCtx.LoadBizDataByNo("bas_deliver", "fnumber", new List<string>() { dto.omsdeliver }, false).FirstOrDefault();
            //        item["fomsdeliver"] = deliverItem?["id"];


            //        decimal qty = Convert.ToDecimal(item["fbizqty"]);
            //        decimal oldprice = Convert.ToDecimal(item["fprice"]);
            //        decimal price = dto.price;
            //        if (oldprice.Equals(price))
            //        {
            //            //只有当新旧价格不一致时，才处理金额数据
            //            continue;
            //        }
            //        //待门店确认状态，才更新价格
            //        if (dto.omsprogress == "25")
            //        {
            //            item["furgent"] = dto.isurgent;
            //            item["fadditionalcharge"] = dto.additionalcharge;
            //            ischange = true;
            //            //业务要求，重置当前折率
            //            decimal distrate = 10;
            //            decimal amount = qty * price;

            //            // 成交单价=零售价*折率/10
            //            decimal dealprice = decimal.Round(price * distrate / 10, 6);
            //            // 成交金额=成交单价*数量
            //            decimal dealamount = dealprice * qty;
            //            // 折扣额=金额-成交金额
            //            decimal distamount = amount - dealamount;

            //            item["fprice"] = price;
            //            item["famount"] = amount;
            //            item["fdistamount"] = distamount;
            //            item["fdealamount"] = dealamount;
            //            item["fdealprice"] = dealprice;
            //        }
            //    }
            //    if (Convert.ToString(item["fomsprogress"]).Equals("-1"))
            //    {
            //        entryIds.Add(Convert.ToString(item["Id"]));
            //        num++;
            //    }
            //}
            //if (ischange)
            //{
            //    //重置整单折
            //    obj["fiswholedis"] = 0;
            //    obj["fdisopt"] = "";
            //    obj["fdiscscale"] = 0;
            //    obj["fdiscscale_temp"] = 0;

            //    //重置抹尾差
            //    obj["ffavoropt"] = "";
            //    obj["fisremovetail"] = 0;

            //    //重置一口价
            //    obj["fisfixedprice"] = 0;
            //    obj["ffixedprice"] = 0;
            //}

            //var dm = this.GetDataManager();
            //var projectnumber = Convert.ToString(obj["fprojectnumber"]);
            //if (dto.orderattr > 0 && !string.IsNullOrWhiteSpace(projectnumber) && attrIsChange)
            //{
            //    string sql = $@"select a.fid,a.fbillno,d.fname,b.ftranspurqty,a.fmainorgid,b.fomsprogress from T_YDJ_ORDER a with(nolock)
            //                                    inner join T_YDJ_ORDERENTRY b with(nolock) on a.fid = b.fid
            //                                    inner join T_BD_MATERIAL c with(nolock) on b.fproductid = c.fid
            //                                    inner join T_YDJ_BRAND d with(nolock) on c.fbrandid = d.fid
            //                                    where a.fprojectnumber = '{projectnumber}' and a.fmainorgid = '{Convert.ToString(obj["fmainorgid"])}' and a.fcancelstatus=0   and a.fbilltype in (select fid from T_BD_BILLTYPE with(nolock) where fname= '标准销售合同')";
            //    var orders = this.Context.ExecuteDynamicObject(sql, null);

            //    List<string> ids = new List<string>();
            //    foreach (var item in orders)
            //    {
            //        if (Convert.ToDecimal(item["ftranspurqty"]) > 0)
            //        {
            //            continue;
            //        }
            //        if (Convert.ToString(item["fname"]).Equals("V6传统"))
            //        {
            //            ids.Add(Convert.ToString(item["fid"]));
            //        }
            //    }

            //    List<DynamicObject> updItem = new List<DynamicObject>();
            //    if (orders.Count > 0)
            //    {
            //        updItem = this.Context.LoadBizDataById("ydj_order", ids);
            //        //关联合同打上大家居标识
            //        updItem.ForEach(c =>
            //        {
            //            c["forderattr"] = dto.orderattr;
            //            c["factivenumber"] = dto.activenumber;
            //            c["factivediscount"] = dto.activediscount;
            //            var _e = c["fentry"] as DynamicObjectCollection;
            //            foreach (var _obj in _e)
            //            {
            //                _obj["forderattr_e"] = dto.orderattr;
            //            }
            //        });
            //        obj["forderattr"] = dto.orderattr;
            //        var _entry = obj["fentry"] as DynamicObjectCollection;
            //        foreach (var _entryIten in _entry)
            //        {
            //            _entryIten["forderattr_e"] = dto.orderattr;
            //        }

            //        var projectinfoForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(this.Context, "ydj_projectinfo");
            //        var projectItem = this.Context.LoadBizDataById("ydj_projectinfo", projectnumber);
            //        if (projectItem != null)
            //        {
            //            projectItem["forderattr"] = obj["forderattr"];
            //            projectItem["factivenumber"] = dto.activenumber;
            //            var projectinfoFormType = projectinfoForm.GetDynamicObjectType(this.Context);
            //            dm.InitDbContext(this.Context, projectinfoFormType);
            //            dm.Save(projectItem);
            //        }

            //        dm.InitDbContext(this.Context, orderFormMate.GetDynamicObjectType(Context));
            //        dm.Save(updItem);
            //    }
            //    else
            //    {
            //        //清空标识
            //        //updItem.ForEach(c => c["forderattr"] = "");
            //        obj["forderattr"] = "";
            //        obj["factivenumber"] = "";
            //        var _entry = obj["fentry"] as DynamicObjectCollection;
            //        foreach (var _entryIten in _entry)
            //        {
            //            _entryIten["forderattr_e"] = "";

            //        }
            //    }
            //}

            //var orderService = this.Container.GetService<IOrderService>();
            //orderService.CalculateSettlement(agentCtx, obj, orderFormMate);
            //orderService.CalculateUnreceived(agentCtx, new[] { obj });
            //orderService.CalculateReceiptStatus(agentCtx, new[] { obj });

            ////dm.InitDbContext(agentCtx, orderFormMate.GetDynamicObjectType(Context));
            ////dm.Save(obj);

            ////var _result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", new[] { obj }, "draft",
            ////    new Dictionary<string, object>());


            ////1.《线上订单进度(回传)》接口调整：当接收到定制OMS中台下发【定制订单进度】=“单据作废”的商品行，如果对应商品行存在下有【作废状态】=“否”且【总部合同状态】=“新建”或“驳回”或为空 的《采购订单》时，则需一并作废。
            //if (entryIds.Count > 0)
            //{
            //    var strSql = $@"select a.fid,fhqderstatus from T_YDJ_PURCHASEORDER as a inner join T_YDJ_POORDERENTRY as b on a.fid=b.fid where fsourceentryid in ('{string.Join("','", entryIds)}') and a.fcancelstatus=0 and fhqderstatus in ('01','05','')";
            //    List<string> fids = new List<string>();
            //    using (var dr = this.Context.ExecuteReader(strSql, new List<SqlParam>() { }))
            //    {
            //        while (dr.Read())
            //        {
            //            fids.Add(dr["fid"].ToString());
            //        }
            //    }
            //    if (fids.Count > 0)
            //    {
            //        var purOrders = this.Context.LoadBizDataById("ydj_purchaseorder", fids);
            //        //http://pmp.jienor.com/www/index.php?m=bug&f=view&bugID=43174
            //        foreach (var item in purOrders)
            //        {
            //            if (Convert.ToInt32(item["fchangestatus"]) == 1 || Convert.ToInt32(item["fchangestatus"]) == 3)
            //            {
            //                //调用取消变更
            //                //再反审核
            //                var unchangeresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "unchange",
            //                 new Dictionary<string, object>());
            //                if (unchangeresult.IsSuccess)
            //                {
            //                    var unauditresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "unaudit",
            //                     new Dictionary<string, object>());
            //                    if (unauditresult.IsSuccess)
            //                    {
            //                        var cancelresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "cancel",
            //                         new Dictionary<string, object>());
            //                    }
            //                }

            //            }
            //            else
            //            {
            //                if (Convert.ToString(item["fstatus"]) == "D")
            //                {
            //                    //调用撤销
            //                    var terminateflowresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "terminateflow",
            //                     new Dictionary<string, object>());
            //                    if (terminateflowresult.IsSuccess)
            //                    {
            //                        var cancelresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "cancel",
            //                         new Dictionary<string, object>());
            //                    }
            //                }
            //                else if (Convert.ToString(item["fstatus"]) == "E")
            //                {
            //                    //调用反审核
            //                    var unauditresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "unaudit",
            //                     new Dictionary<string, object>());
            //                    if (unauditresult.IsSuccess)
            //                    {
            //                        var cancelresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "cancel",
            //                         new Dictionary<string, object>());
            //                    }
            //                }
            //                else
            //                {
            //                    var cancelresult = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { item }, "cancel",
            //                     new Dictionary<string, object>());
            //                }
            //            }
            //        }
            //    }
            //}
            //if (num == entrys.Count)
            //{
            //    if (Convert.ToString(obj["fstatus"]) == "D" || Convert.ToString(obj["fstatus"]) == "E")
            //    {
            //        string updateSqls = $"update t_ydj_order set fcanceldate=GETDATE(),fcancelid='sysadmin',fcancelstatus='1' where fid='{obj["id"]}'";
            //        var dbServiceEx = this.Context.Container.GetService<IDBServiceEx>();
            //        dbServiceEx.Execute(this.Context, updateSqls);

            //        var loger = agentCtx.Container.GetService<ILogService>();
            //        loger.WriteLog(agentCtx, new LogEntry()
            //        {
            //            BillIds = obj["Id"]?.ToString(),
            //            BillNos = obj["fbillno"]?.ToString(),
            //            BillFormId = "ydj_order",
            //            OpName = "作废",
            //            OpCode = "cancel",
            //            Content = "执行了【作废】操作！",
            //            DebugData = "执行了【作废】操作！",
            //            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
            //            Level = Enu_LogLevel.Info.ToString(),
            //            LogType = Enu_LogType.RecordType_03

            //        });
            //        try
            //        {
            //            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，执行脚本更新销售合同至作废状态】，内容:{4}".Fmt(this.Context.UserName,
            //                    this.Context.UserPhone, this.Context.Company,
            //                    DateTime.Now.ToString("HH:mm:ss"), "SQL:" + updateSqls),
            //                    "SWJLogFile");
            //        }
            //        catch (Exception)
            //        {
            //            //日志文件可能会出现被占用的情况，不做处理
            //        }
            //    }
            //    else
            //    {
            //        //所有行被作废了，则整单作废
            //        var result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", new[] { obj }, "cancel",
            //            new Dictionary<string, object>());
            //        if (result.IsSuccess)
            //        {
            //            mes = "单号：" + Convert.ToString(obj["fbillno"]) + "整单作废成功！";
            //        }
            //        else
            //        {
            //            if (result.ComplexMessage.HasMessage)
            //            {
            //                mes = "单号：" + Convert.ToString(obj["fbillno"]) + "整单作废失败！" + string.Join(";", result.ComplexMessage.ErrorMessages);
            //            }
            //            else
            //                mes = "单号：" + Convert.ToString(obj["fbillno"]) + "整单作废失败！";
            //        }
            //    }
            //}
            #endregion
            //var param = new Dictionary<string, object>() { };
            //param.Add("dto", JsonConvert.SerializeObject(dto));
            //var _result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", new[] { }, "OrderProcess", param);

            // 向麦浩系统发送请求
            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(agentCtx, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "OrderProcess",
                PageId = Guid.NewGuid().ToString("N"),
                SimpleData = new Dictionary<string, string>
                {
                    { "dto",JsonConvert.SerializeObject(dto)}
                }
            });

            if (response.OperationResult.IsSuccess)
            {
                resp.Message = mes.IsEmpty() ? "单号【" + dto.fbillno + "】" : mes + "线上订单进度更新成功！";
                resp.Success = true;
                resp.Code = 200;
                this.Request.Items.Add("fsuccessnumber", dto.fbillno);
                return resp;
            }
            else
            {
                resp.Message = string.Join(",", response.OperationResult.ComplexMessage.ErrorMessages);
                resp.Success = false;
                resp.Code = 500;
                this.Request.Items.Add("ffailnumbers", dto.fbillno);
                return resp;
            }
        }

        /// <summary>
        /// 更新明细行的订单属性以及表头，基础档案，关联项目的订单的订单属性，活动方案编码
        /// </summary>
        /// <param name="obj"></param>
        //private void UpdProjectAttr(DynamicObject obj)
        //{
        //    if (!string.IsNullOrWhiteSpace(Convert.ToString(obj["fprojectnumber"])))
        //    {
        //        string projectnumber = Convert.ToString(obj["fprojectnumber"]);
        //        var entry = obj["fentry"] as DynamicObjectCollection;
        //        foreach (var item in entry)
        //        {
        //            item["forderattr"] = obj["forderattr"];
        //        }
        //        //以下更新都是不管你自己有没有订单属性，都会重新检查一遍再更新
        //        var projectItem = this.Context.LoadBizDataById("ydj_projectinfo", projectnumber);
        //        projectItem["forderattr"] = obj["forderattr"];
        //        projectItem["factivenumber"] = obj["factivenumber"];
        //        var projectResult = this.HttpGateway.InvokeBillOperation(this.Context, "ydj_projectinfo", new[] { projectItem }, "save",
        //         new Dictionary<string, object>());


        //        var orders = this.Context.LoadBizDataByFilter(OrderFormId, $" fprojectnumber='{projectnumber}'");
        //        if (orders.Count == 0)
        //            continue;

        //        List<DynamicObject> updItem = new List<DynamicObject>();
        //        bool canchangefilter = false;//是否满足更新条件
        //        bool canUpd = true;//是否允许更新数据

        //        var materialObjs = this.Context.ExecuteDynamicObject("select top 1 fid,funitid,fsalunitid,fseriesid from T_BD_MATERIAL with(nolock) where fnumber=@fnumber and fforbidstatus=0;", new List<SqlParam> { new SqlParam("fnumber", DbType.String, "VFZ1-M001") });
        //        if (materialObjs == null)
        //            continue;
        //        // 加载数据
        //        refObjMgr?.Load(this.Context, orders.ToArray(), true, htmlForm, new List<string> { "fbilltype" });
        //        foreach (var orderItem in orders)
        //        {
        //            if (Convert.ToString((orderItem["fbilltype_ref"] as DynamicObject)?["fname"]).Equals("标准销售合同"))
        //            {
        //                //要判断明细行状态，明细行状态都满足待提交、待接单才可以更新
        //                var orderItemEntry = orderItem["fentry"] as DynamicObjectCollection;
        //                foreach (var _item in orderItemEntry)
        //                {
        //                    if (Convert.ToInt32(_item["ftranspurqty"]) > 0 && !(Convert.ToString(_item["fproductid"]).Equals(materialObjs[0]["fid"])))
        //                    {
        //                        canUpd = false;
        //                    }
        //                }
        //                if (canUpd)
        //                {
        //                    canchangefilter = true;
        //                    updItem.Add(orderItem);
        //                }
        //            }
        //        }
        //    }
        //}

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(OrderProcessDTO dto, BaseResponse<object> resp)
        {
            if (Convert.ToString(dto.tranid).IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数tranid不能为空！";
                resp.Success = false;

                return false;
            }

            if (Convert.ToString(dto.omsbillno).IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数omsbillno不能为空！";
                resp.Success = false;

                return false;
            }

            //if (Convert.ToString(dto.omsprogress).IsNullOrEmptyOrWhiteSpace())
            //{
            //    resp.Code = 400;
            //    resp.Message = "参数omsprogress不能为空！";
            //    resp.Success = false;

            //    return false;
            //}

            if (Convert.ToString(dto.omsoperate).IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = "参数omsoperate不能为空！";
                resp.Success = false;

                return false;
            }
            if (dto.isurgent > 1 || dto.isurgent < 0)
            {
                resp.Code = 400;
                resp.Message = "参数isurgent不符合规范，只允许（0，1）！";
                resp.Success = false;
                return false;
            }

            if (dto.orderattr > 3)
            {
                resp.Code = 400;
                resp.Message = "参数orderattr不符合规范，只允许（1，2，3）！";
                resp.Success = false;
                return false;
            }

            if (dto.price < 0)
            {
                resp.Code = 400;
                resp.Message = "参数price不允许小于0！";
                resp.Success = false;
                return false;
            }
            var dbService = this.Context.Container.GetService<IDBService>();
            string sql = "select a.fid,a.fbillno,a.frenewalflag,a.frenewalrectag,a.fsettlprogress,a.fmainorgid,c.fnumber from t_ydj_order as a with(nolock) inner join T_YDJ_ORDERENTRY as b with(nolock) on a.fid=b.fid inner join T_BAS_AGENT as c on a.fmainorgid=c.fid where b.ftranid=@tranid";
            SqlParam sqlParam = new SqlParam("@tranid", System.Data.DbType.String, Convert.ToString(dto.tranid).Trim());
            using (var dr = dbService.ExecuteReader(this.Context, sql, sqlParam))
            {
                if (dr.Read())
                {
                    string fid = Convert.ToString(dr["fid"]);
                    string fbillno = Convert.ToString(dr["fbillno"]);
                    string fmainorgid = Convert.ToString(dr["fmainorgid"]);
                    string fnumber = Convert.ToString(dr["fnumber"]);
                    if (!fid.IsNullOrEmptyOrWhiteSpace())
                    {
                        agentCtx = this.Context.CreateAgentDBContext(fmainorgid);
                        if (agentCtx == null)
                        {
                            resp.Code = 400;
                            resp.Message = $"经销商【{fnumber}】不存在！";
                            resp.Success = false;

                            return false;
                        }
                        dto.fmainorgid = fmainorgid;
                        dto.fid = fid;
                        dto.fbillno = fbillno;
                        dto.fnumber = fnumber;

                        var frenewalflag = Convert.ToString(dr["frenewalflag"]);
                        var frenewalrectag = Convert.ToString(dr["frenewalrectag"]);
                        var fsettlprogress = Convert.ToString(dr["fsettlprogress"]);
                        if (frenewalrectag != "1")
                        {
                            if (frenewalflag == "1" && fsettlprogress != Enu_RenewalSettleProgress.待发起)
                            {
                                resp.Code = 400;
                                resp.Message = $"销售合同【{dto.fbillno}】的【结算进度】<>'待发起'，不允许调用";
                                resp.Success = false;

                                return false;
                            }
                        }

                    }
                }
                else
                {
                    resp.Code = 400;
                    resp.Message = $"当前tranid未查询到数据，请检查！";
                    resp.Success = false;

                    return false;
                }
            };



            return true;
        }


        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as OrderProcessDTO)?.fbillno);
        }

        protected override Dictionary<string, string> CreateDistributedLocks(OrderProcessDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.tranid}", $"OMS销售合同 {dto.tranid} 正在锁定中，请稍后再操作！" }
            };
        }
    }
}
