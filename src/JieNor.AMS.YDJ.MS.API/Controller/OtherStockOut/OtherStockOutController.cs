using JieNor.AMS.YDJ.MS.API.DTO.Order.DirectOrder;
using JieNor.AMS.YDJ.MS.API.DTO.OtherStockOut;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller
{
    public class OtherStockOutController : BaseController<OtherStockOutAuditDTO>
    {
        public string FormId
        {
            get { return "ydj_order"; }
        }

        protected UserContext agentCtx { get; set; }

        /// <summary>
        /// 数据包
        /// </summary>
        private List<DynamicObject> orderDys { get; set; }
        public override object Execute(OtherStockOutAuditDTO dto)
        {
            throw new NotImplementedException();
        }
    }
}
