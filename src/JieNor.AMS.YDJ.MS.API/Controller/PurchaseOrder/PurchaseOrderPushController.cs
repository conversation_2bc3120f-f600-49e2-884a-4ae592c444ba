using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.PurchaseOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Consts;
using JieNor.AMS.YDJ.MS.API.Filter;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.MetaCore.FormOp.FormService;
using System.Text.RegularExpressions;
using System.Text;

namespace JieNor.AMS.YDJ.MS.API.Controller.PurchaseOrder
{
    /// <summary>
    /// 采购订单：总部下发接口
    /// </summary>
    //[RepeatedRequestFilter("ydj_purchaseorder", 30)] //对于高并发此方法不够全面，弃用
    public class PurchaseOrderPushController : BaseController<PurchaseOrderPushDTO>
    {
        public string FormId
        {
            get { return "ydj_purchaseorder"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 经销商用户上下文
        /// </summary>
        protected UserContext AgentContext { get; set; }

        /// <summary>
        /// 业务对象
        /// </summary>
        protected DynamicObject BizData { get; set; }

        protected Dictionary<string, string> bizAgents { get; set; }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "billNo";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(PurchaseOrderPushDTO dto)
        {
            var resp = new BaseResponse<object>();

            // 获取业务经销商
            this.bizAgents = this.Container.GetService<IAgentService>().GetBizAgentIdByNos(this.Context, new List<string> { dto.AgentNo });
            bizAgents.TryGetValue(dto.AgentNo, out string bizAgentId);

            if (bizAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Code = 400;
                resp.Message = $"经销商编码【{dto.AgentNo}】不存在！";
                resp.Success = false;

                return resp;
            }

            this.AgentContext = this.Context.CreateAgentDBContext(bizAgentId);
            string FormId = "ydj_purchaseorder";
            //生成其他应收单
            if (dto.OrderType.EqualsIgnoreCase("Z009贷"))
            {
                FormId = "ydj_collectreceipt_mid";
            }
            //生成其他应付单
            else if (dto.OrderType.EqualsIgnoreCase("Z010借"))
            {
                FormId = "ydj_payreceipt_mid";
            }

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, FormId);
            try
            {

                this.BizData = CreateBizData(dto);

                if (!Valid(dto, resp)) return resp;
                // 设置默认值，放在最前面，防止部分值没有后导致关闭状态判断后修正错误
                IDefaultValueCalculator defaultValueCalculator = this.Container.GetService<IDefaultValueCalculator>();
                defaultValueCalculator.Execute(this.AgentContext, this.HtmlForm, new[] { this.BizData });

                if (this.HtmlForm.Id == "ydj_purchaseorder")
                {
                    FillAuxPropFieldValue(this.BizData, dto);

                    FillRefValue(this.BizData, dto);

                    Calculate(this.BizData);
                    Core.Helpers.DocumentStatusHelper.CalcPurchaseOrderCloseStatus(this.BizData);
                }
                IPrepareSaveDataService prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(this.AgentContext, this.HtmlForm, new[] { this.BizData }, null);
                //改成只保存数据到数据库 
                var dm = this.GetDataManager();
                dm.InitDbContext(this.AgentContext, this.HtmlForm.GetDynamicObjectType(this.AgentContext));
                dm.Save(this.BizData);
                resp.Code = 200;
                resp.Message = "操作成功！";
                resp.Success = true;
            }

            catch (Exception ex)
            {
                resp.Code = 400;
                resp.Message = $"操作失败！,{ex.Message}{ex.StackTrace}";
                resp.Success = false;

            }

            return resp;
        }

        /// <summary>
        /// 填充引用值
        /// </summary>
        /// <param name="bizData"></param>
        private void FillRefValue(DynamicObject bizData, PurchaseOrderPushDTO dto)
        {
            var entrys = bizData["fentity"] as DynamicObjectCollection;
            foreach (var entry in entrys)
            {
                var tranId = Convert.ToString(entry["ftranid"]);
                var product = entry["fmaterialid_ref"] as DynamicObject;
                //如果是虚拟料号商品则需要更新定制说明为商品名称
                if (Convert.ToBoolean(product?["fisvirtual"]))
                {
                    var dtoEntry = dto.Entrys.First(s => s.Id.EqualsIgnoreCase(tranId));
                    entry["fcustomdes_e"] = dtoEntry?.ProductName ?? "";
                }
                entry["funitid"] = product["funitid"];
                entry["fbizunitid"] = product["fpurunitid"];
            }

            //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
            var unitService = this.Container.GetService<IUnitConvertService>();
            unitService.ConvertByBasQty(this.Context, this.HtmlForm, new[] { this.BizData }, null);
        }

        /// <summary>
        /// 判断是否虚拟商品：商品的编码是定制品编码（编码含字母）+辅助属性为空 
        /// 沟通需要上游给虚拟商品的标识，但是sap给不了，只能通过此方式
        /// </summary>
        /// <returns></returns>
        private void Isvirtual(PurchaseOrderPushDTO dto)
        {
            foreach (var entry in dto.Entrys)
            {
                if (Isexist(entry.ProductNo) && entry.AuxPropVals != null && entry.AuxPropVals.Count == 0)
                {
                    entry.IsVisual = true;
                }
            }
        }


        static Regex _regexLett = new Regex("[a-zA-Z]");

        /// <summary>
        /// 判断是否包含字母
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private bool Isexist(string str)
        {
            return _regexLett.Matches(str).Count > 0;
        }

        private DynamicObject CreateBizData(PurchaseOrderPushDTO dto)
        {
            switch (this.HtmlForm.Id.ToLower())
            {
                case "ydj_collectreceipt_mid":
                    return CreateCollectReceiptBizData(dto);
                case "ydj_payreceipt_mid":
                    return CreatePayReceiptBizData(dto);
                case "ydj_purchaseorder":
                    return CreatePurchaseOrderBizData(dto);
                default: return null;
            }
        }

        /// <summary>
        /// 创建采购订单数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private DynamicObject CreatePurchaseOrderBizData(PurchaseOrderPushDTO dto)
        {
            DynamicObject bizData = null;
            if (!dto.BillNo.IsNullOrEmptyOrWhiteSpace())
            {
                bizData = this.AgentContext.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new[] { dto.BillNo })
                    .FirstOrDefault();

                if (bizData != null)
                {
                    var fbilltype = Convert.ToString(bizData?["fbilltypeid"]);
                    if (!fbilltype.EqualsIgnoreCase("ydj_purchaseorder_zb"))
                    {
                        throw new WarnException("已经存在非总部下发单据,无法覆盖保存！");
                    }

                    var entrys = bizData["fentity"] as DynamicObjectCollection;

                    var selectedRows = new List<SelectedRow>();
                    foreach (var entry in entrys)
                    {
                        selectedRows.Add(new SelectedRow
                        {
                            EntityKey = "fentity",
                            PkValue = Convert.ToString(bizData["id"]),
                            EntryPkValue = Convert.ToString(entry["id"])
                        });
                    }

                    var isPush = this.Container.GetService<IConvertService>().IsPush(this.AgentContext, this.HtmlForm,
                        selectedRows, new[] { "stk_postockin" }, OperateOption.Create());
                    if (isPush)
                    {
                        //throw new WarnException($"{this.HtmlForm.Caption} {bizData["fbillno"]}已入库，无法覆盖保存！");
                        var tranIds = entrys.Select(t => Convert.ToString(t["ftranid"])).ToList();
                        if (dto.Entrys.Any(t => !tranIds.Contains(t.Id)))
                        {
                            throw new WarnException($"{this.HtmlForm.Caption} {bizData["fbillno"]}已入库，不能新增明细行！");
                        }
                        if (dto.Entrys.Count < tranIds.Count)
                        {
                            throw new WarnException($"{this.HtmlForm.Caption} {bizData["fbillno"]}已入库，不能删除明细行！");
                        }

                        var entryIds = entrys.Select(t => Convert.ToString(t["id"])).ToList();
                        var inStockInfos = GetInStockQty(entryIds);
                        StringBuilder errMsg = new StringBuilder();
                        if (inStockInfos != null && inStockInfos.Any())
                        {
                            foreach (var info in inStockInfos)
                            {
                                var purEntryId = Convert.ToString(info["fpoorderentryid"]);
                                var inStockQty = Convert.ToDecimal(info["inStockQty"]);
                                var purEntry = entrys.FirstOrDefault(t => Convert.ToString(t["id"]).EqualsIgnoreCase(purEntryId));
                                var purQty = Convert.ToDecimal(purEntry["fqty"]);

                                var tranId = Convert.ToString(purEntry["ftranid"]);
                                var pushInfo = dto.Entrys.FirstOrDefault(t => t.Id.EqualsIgnoreCase(tranId));
                                if (pushInfo.Qty > purQty)
                                {
                                    errMsg.AppendLine($"{this.HtmlForm.Caption} {bizData["fbillno"]}已入库，第{pushInfo.Seq}行商品【{pushInfo.ProductNo}】采购数量不能超过原采购数【{purQty}】！");
                                }
                                else if (pushInfo.Qty < inStockQty)
                                {
                                    errMsg.AppendLine($"{this.HtmlForm.Caption} {bizData["fbillno"]}已入库，第{pushInfo.Seq}行商品【{pushInfo.ProductNo}】采购数量不能少于入库数【{inStockQty}】！");
                                }
                            }
                        }
                        if (!errMsg.ToString().IsNullOrEmptyOrWhiteSpace())
                        {
                            throw new WarnException(errMsg.ToString());
                        }
                    }
                }
            }

            if (bizData == null)
            {
                bizData = (DynamicObject)this.HtmlForm.GetDynamicObjectType(this.AgentContext).CreateInstance();
                bizData["fclosestatus"] = CloseStatusConst.Default;
            }

            IPurchaseOrderService purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();

            var deliver = this.Context.LoadBizDataByNo("bas_deliver", "fnumber", new[] { dto.DeliverNo })
                .FirstOrDefault();

            var deliverId = Convert.ToString(deliver?["id"]);

            bizData["fbillno"] = dto.BillNo;
            bizData["fdeliverid"] = dto.DeliverNo;
            bizData["fdeliveryplandate"] = dto.DeliveryPlanDate;
            bizData["fdescription"] = dto.Description;
            bizData["fhqderno"] = dto.HqderNo;
            bizData["fhqderstatus"] = dto.HqderStatus.Equals("G") ? "03" : dto.HqderStatus;
            bizData["fhqdertype"] = dto.fhqdertype;
            if (this.AgentContext.IsDirectSale && string.IsNullOrWhiteSpace(dto.HqderNo))
            {
                bizData["fhqderno"] = dto.BillNo;
            }
            if (this.AgentContext.IsDirectSale && string.IsNullOrWhiteSpace(dto.HqderStatus))
            {
                bizData["fhqderstatus"] = "03";
            }
            if (!string.IsNullOrWhiteSpace(dto.fhqderauditdate) && dto.HqderStatus.Equals("03"))
            {
                bizData["fhqderauditdate"] = dto.fhqderauditdate;
            }
            if (this.AgentContext.IsDirectSale && string.IsNullOrWhiteSpace(dto.fhqderauditdate))
            {
                bizData["fhqderauditdate"] = DateTime.Now;
            }
            bizData["fbilltypeid"] = "ydj_purchaseorder_zb"; // 总部手工单

            // 根据送达方带出供应商
            var supplierId = purchaseOrderService.GetSupplierIdByDeliverId(this.AgentContext, deliverId);
            var supplier = this.AgentContext.LoadBizDataById("ydj_supplier", supplierId);
            bizData["fsupplierid"] = Convert.ToString(supplier?["id"]);

            if (dto.Entrys != null)
            {
                List<DynamicObject> newEntrys = new List<DynamicObject>();
                var entrys = bizData["fentity"] as DynamicObjectCollection;

                //将总部下发的套件组合号、配件组合号、沙发组合号 统一处理成我们系统的组合号规则
                ResetCombnumber(dto);
                //to do 处理商品档案
                LoadOrCreatePro(dto);

                foreach (var dtoEntry in dto.Entrys)
                {
                    if (this.AgentContext.IsDirectSale)
                    {
                        if (dtoEntry.Id.IsNullOrEmptyOrWhiteSpace())
                        {
                            dtoEntry.Id = Convert.ToInt32(dtoEntry.Seq).ToString();
                        }
                    }
                    var entry = entrys.FirstOrDefault(s => Convert.ToString(s["ftranid"]).EqualsIgnoreCase(dtoEntry.Id));
                    if (entry == null)
                    {
                        entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                        entry["fclosestatus_e"] = CloseStatusConst.Default;
                    }

                    entry["fcombine"] = dtoEntry.Combine;
                    entry["fnote"] = dtoEntry.Description;
                    entry["fdistamount"] = dtoEntry.DistAmount;
                    entry["ftranid"] = dtoEntry.Id;
                    entry["fpartscombnumber"] = dtoEntry.PartsCombNumber;
                    entry["fprice"] = dtoEntry.Price;
                    entry["fmaterialid"] = dtoEntry.ProductNo;
                    entry["fqty"] = dtoEntry.Qty;
                    entry["fresultbrandid"] = dtoEntry.ResultBrandNo;
                    entry["fselsuiterequire"] = dtoEntry.SelSuiteRequire;
                    entry["fdemanddate"] = dtoEntry.DemandDate;
                    entry["fplandate"] = dtoEntry.PlanDate;
                    entry["fseq"] = dtoEntry.Seq;
                    entry["fsuitcombnumber"] = dtoEntry.selSuiteNumber;
                    entry["funstdtype"] = dtoEntry.UnstdType ? "1" : "0";
                    entry["funstdtypecomment"] = dtoEntry.UnstdTypeComment;
                    entry["funstdtypestatus"] = dtoEntry.UnstdTypeStatus;
                    var cancelreason = dtoEntry.Cancelreason;
                    decimal ftaxprice = 0;
                    decimal fstardiscount = 0;
                    decimal fdepthdiscount = 0;
                    decimal fnewdiscount = 0;
                    decimal fexpenserebate = 0;
                    if (dtoEntry.fdiscounts != null)
                    {
                        // 新增总部折扣字段
                        ftaxprice = dtoEntry.fdiscounts
                                               .FirstOrDefault(s => s.ftype.EqualsIgnoreCase(Enu_DiscountType.ZP01.ToString()))
                                               ?.fvalue ?? 0;
                        fstardiscount = dtoEntry.fdiscounts
                                                   .FirstOrDefault(s => s.ftype.EqualsIgnoreCase(Enu_DiscountType.ZK03.ToString()))
                                                   ?.fvalue ?? 0;
                        fdepthdiscount = dtoEntry.fdiscounts
                                                    .FirstOrDefault(
                                                        s => s.ftype.EqualsIgnoreCase(Enu_DiscountType.ZK05.ToString()))?.fvalue ??
                                                0;
                        fnewdiscount = dtoEntry.fdiscounts
                                                  .FirstOrDefault(s => s.ftype.EqualsIgnoreCase(Enu_DiscountType.ZK06.ToString()))
                                                  ?.fvalue ?? 0;
                        fexpenserebate = dtoEntry.fdiscounts
                                                    .FirstOrDefault(
                                                        s => s.ftype.EqualsIgnoreCase(Enu_DiscountType.ZK99.ToString()))?.fvalue ??
                                                0;
                    }
                    decimal fsapdiscount = dtoEntry.DistAmount;
                    // 其他折扣=SAP折扣总额-ZK03 -ZK05-ZK06-ZK99
                    decimal fotherdiscount = fsapdiscount - fstardiscount - fdepthdiscount - fnewdiscount -
                                             fexpenserebate;

                    entry["ftaxprice"] = ftaxprice;
                    entry["fstardiscount"] = fstardiscount;
                    entry["fdepthdiscount"] = fdepthdiscount;
                    entry["fnewdiscount"] = fnewdiscount;
                    entry["fexpenserebate"] = fexpenserebate;
                    entry["fotherdiscount"] = fotherdiscount;
                    entry["fsapdiscount"] = fsapdiscount;

                    // 折扣额=0 且 折率=0，重置10
                    if (dtoEntry.DistAmount == 0 && dtoEntry.DistRate == 0)
                    {
                        entry["fdistrate"] = 10;
                    }
                    else
                    {
                        entry["fdistrate"] = dtoEntry.DistRate;
                    }

                    //手工单下发也更新到固定字段，因为手工单下发 总部合同状态是已终审不会在走提交总部所以fseq_e需要赋值
                    entry["fseq_e"] = dtoEntry.Seq;

                    //将下发的手工单尺寸赋给明细行定制说明
                    entry["fcustomdes_e"] = dtoEntry.sapProdDesc;
                    newEntrys.Add(entry);
                }

                // 清空后重新填充
                entrys.Clear();
                foreach (var entry in newEntrys)
                {
                    entrys.Add(entry);
                }
            }

            return bizData;
        }

        /// <summary>
        /// 如果传过来的商品总部不存在则需要创建一份总部商品
        /// </summary>
        /// <param name="dto"></param>
        private void LoadOrCreatePro(PurchaseOrderPushDTO dto)
        {
            //处理虚拟品标识
            Isvirtual(dto);

            var metaService = this.Context.Container.GetService<IMetaModelService>();
            var productForm = metaService.LoadFormModel(this.Context, "ydj_product");
            var formDt = productForm.GetDynamicObjectType(this.Context);

            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, formDt);
            var dyobjs = new List<DynamicObject>();

            //总部手工单中台下发业绩品牌resultBrandNo，我们赋给商品系列
            var ResultBrandNos = dto.Entrys.Select(o => o.ResultBrandNo).ToList<string>();
            var SeriesObjs = this.Context.LoadBizBillHeadDataByACLFilter("ydj_series", $"fnumber in ('{ResultBrandNos.JoinEx("','", false)}')", "fid,fnumber,fname,fbrandid").ToList();
            //sap 只下发单位名称
            var UnitNames = dto.Entrys.Select(o => o.Unitid).ToList<string>();
            var UnitObjs = this.Context.LoadBizBillHeadDataByACLFilter("ydj_unit", $" fforbidstatus = '0' and fname in ('{UnitNames.JoinEx("','", false)}')", "fid,fnumber,fname").ToList();

            var ProNums = dto.Entrys.Select(o => o.ProductNo).ToList<string>();
            var ProNumsExists = this.Context.LoadBizBillHeadDataByACLFilter("ydj_product", $" fnumber in ('{ProNums.JoinEx("','", false)}')", "fnumber").Select(o => o["fnumber"].ToString()).ToList();
            //获取总部未匹配
            var ProNumsNoexist = ProNums.Except(ProNumsExists);
            //组装商品档案
            foreach (var entry in dto.Entrys)
            {
                if (!ProNumsNoexist.Contains(entry.ProductNo)) continue;
                //如果已经创建同编码商品则跳过 只创建一次应对虚拟产品
                if (dyobjs.Any(o => Convert.ToString(o["fnumber"]).EqualsIgnoreCase(entry.ProductNo))) continue;

                var dyobj = new DynamicObject(formDt);
                dyobj["fnumber"] = entry.ProductNo;
                dyobj["fname"] = entry.ProductName;
                if (entry.IsVisual)
                {
                    dyobj["fisvirtual"] = true;
                    //下发手工单创建虚拟商品时 以编码作为商品名称
                    dyobj["fname"] = entry.ProductNo;
                }
                //商品类别取预置商品类别
                dyobj["fcategoryid"] = "SGDSP";
                dyobj["fdescription"] = "总部手工单下发自动创建";
                //停购 勾选
                dyobj["fendpurchase"] = true;
                var funitid = Convert.ToString(UnitObjs.Where(o => o["fname"].ToString().EqualsIgnoreCase(entry.Unitid))?.FirstOrDefault()?["fid"]);
                if (!funitid.IsNullOrEmpty())
                {
                    //各单位默认取基本单位 
                    dyobj["funitid"] = funitid;
                    dyobj["fsalunitid"] = funitid;
                    dyobj["fpurunitid"] = funitid;
                    dyobj["fstockunitid"] = funitid;
                }

                //慕思商品 勾选
                dyobj["fismusiproduct"] = true;
                //总部手工单中台下发业绩品牌resultBrandNo，我们赋给商品系列,品牌
                var seriesObj = SeriesObjs.Where(o => o["fnumber"].ToString().EqualsIgnoreCase(entry.ResultBrandNo))?.FirstOrDefault();
                if (seriesObj != null)
                {
                    dyobj["fseriesid"] = Convert.ToString(seriesObj["fid"]);
                    dyobj["fbrandid"] = Convert.ToString(seriesObj["fbrandid"]);
                }
                dyobjs.Add(dyobj);
            }
            if (dyobjs.Any())
            {
                List<string> errorMsgs = null;
                // 设置要转换的基础资料：送达方、商品、业务品牌
                var baseDataFieldIds = new[] { "funitid", "fsalunitid", "fpurunitid", "fstockunitid", "fcategoryid" };
                this.Context.TryConvertBaseData(productForm, dyobjs, baseDataFieldIds,
                    out errorMsgs);

                // 设置默认值，放在最前面
                IDefaultValueCalculator defaultValueCalculator = this.Container.GetService<IDefaultValueCalculator>();
                defaultValueCalculator.Execute(this.Context, productForm, dyobjs.ToArray());

                IPrepareSaveDataService prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(this.Context, productForm, dyobjs.ToArray(), null);
                //保存选项：确保出现异常时所有操作都能回滚事务
                var saveOption = new Dictionary<string, object>
                {
                    { "NotRefreshNumber", true },
                    { "IsMuSiSync", true }
                };
                //保存商品
                var result = this.HttpGateway.InvokeBillOperation(this.Context, productForm.Id, dyobjs, "save", saveOption);
                result?.ThrowIfHasError(true, $"商品{ProNumsNoexist.JoinEx(",", false)}【自动生成】失败！+ {result.ComplexMessage.ErrorMessages}");
            }
        }

        /// <summary>
        /// 处理套件组合号、沙发组合号、配件组合号
        /// </summary>
        /// <param name="dto"></param>

        private void ResetCombnumber(PurchaseOrderPushDTO dto)
        {
            var entrys = dto.Entrys;
            //对方格式 以下举例有两组套件
            //序号   套件组合号
            //10       0
            //20       10
            //30       10
            //40       0
            //50       40
            var Seq_suits = entrys.Where(o => o.selSuiteNumber.EqualsIgnoreCase("0")).Select(x => x.Seq * 10).ToList<int>();
            if (Seq_suits.Count() > 0)
            {
                foreach (var Seqsuit in Seq_suits)
                {
                    //如果后续商品的套件组合号没有对应该行号 则不生成套件组合号
                    if (!entrys.Any(o => o.selSuiteNumber.EqualsIgnoreCase(Seqsuit.ToString()))) continue;
                    //按照PC前端组合号规则生成一个新的组合号
                    var NewComNumber = Guid.NewGuid().ToString();
                    //说明存在多组套件组合
                    foreach (var entry in entrys)
                    {
                        //将当前套件商品行和其子套件的套件组合号都更新
                        if ((entry.Seq * 10).Equals(Seqsuit) || entry.selSuiteNumber.EqualsIgnoreCase(Seqsuit.ToString()))
                        {
                            entry.selSuiteNumber = NewComNumber;
                        }
                    }
                }
            }
            var Seq_partcoms = entrys.Where(o => o.PartsCombNumber.EqualsIgnoreCase("0")).Select(x => x.Seq * 10).ToList<int>();
            if (Seq_partcoms.Count() > 0)
            {
                foreach (var Seqpartcom in Seq_partcoms)
                {
                    //按照PC前端组合号规则生成一个新的组合号
                    var NewComNumber = Guid.NewGuid().ToString();
                    foreach (var entry in entrys)
                    {
                        //将当前配件主商品行和其子件的套件组合号都更新
                        if ((entry.Seq * 10).Equals(Seqpartcom) || entry.PartsCombNumber.EqualsIgnoreCase(Seqpartcom.ToString()))
                        {
                            entry.PartsCombNumber = NewComNumber;
                        }
                    }
                }
            }
            //处理沙发组合号
            var Seq_coms = entrys.Where(o => o.Combine.EqualsIgnoreCase("0")).Select(x => x.Seq * 10).ToList<int>();
            if (Seq_coms.Count() > 0)
            {
                foreach (var Seqcom in Seq_coms)
                {
                    //按照PC前端组合号规则生成一个新的组合号
                    var NewComNumber = Guid.NewGuid().ToString();
                    foreach (var entry in entrys)
                    {
                        //将当前沙发组合号行和其他沙发商品 都更新
                        if ((entry.Seq * 10).Equals(Seqcom) || entry.Combine.EqualsIgnoreCase(Seqcom.ToString()))
                        {
                            entry.Combine = NewComNumber;
                        }
                    }
                }
            }

        }

        /// <summary>
        /// 创建应收数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private DynamicObject CreateCollectReceiptBizData(PurchaseOrderPushDTO dto)
        {
            bizAgents.TryGetValue(dto.AgentNo, out string bizAgentId);
            var topCtx = this.Context.CreateTopOrgDBContext();
            var agentmidid = topCtx.LoadBizDataByNo("bas_agent", "fnumber", new List<string> { dto.AgentNo }).FirstOrDefault()?["id"];
            //agentmid.TryGetValue(dto.AgentNo, out string agentmidid);

            IPurchaseOrderService purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
            DynamicObject bizData = null;
            if (!dto.BillNo.IsNullOrEmptyOrWhiteSpace())
            {
                bizData = this.AgentContext.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new[] { dto.BillNo })
                                           .FirstOrDefault();
            }
            if (bizData == null)
            {
                bizData = (DynamicObject)this.HtmlForm.GetDynamicObjectType(this.AgentContext).CreateInstance();
            }

            //bizData["fbilltype"] = "ydj_collectreceipt_mid_01";
            bizData["fbillno"] = dto.BillNo;
            var deliverId = this.AgentContext.LoadBizDataByFilter("bas_deliver", $"fnumber = '{Convert.ToString(dto.DeliverNo)}'")
                .Select(o => Convert.ToString(o["id"])).FirstOrDefault();

            //往来单位类型 默认 “供应商”
            //bizData["frelatetype"] = "ydj_supplier";
            //bizData["fsourcenumber"] = dto.Sourcenumber;
            bizData["ftype"] = dto.OrderType;
            bizData["fdeliverid"] = deliverId;
            //中台传的子的就显示子的经销商
            bizData["fagentid"] = agentmidid;
            bizData["fregistdate"] = dto.Registdate;
            //bizData["frelatecusid"] = purchaseOrderService.GetSupplierIdByDeliverId(this.AgentContext, deliverId);
            //bizData["fdatasource"] = "慕思总部";
            //bizData["fcurrency"] = "fcurrency_type_01";
            bizData["fsyncreason"] = dto.Syncreason;
            bizData["fdescription"] = dto.Description;
            if (dto.PayOrCollectEntrys != null)
            {
                var entrys = bizData["fentry"] as DynamicObjectCollection;
                entrys.Clear();
                decimal sumAmount = 0;
                foreach (var dtoEntry in dto.PayOrCollectEntrys)
                {
                    //全量更新
                    var entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                    //金额 根据借贷项+子类重新计算
                    dtoEntry.Totalamount = calculateInvoice(dto.OrderType, dtoEntry.Invoicetype, dtoEntry.Totalamount);

                    //费用项目 默认为新增的预置 “借贷项费用”
                    //entry["fexpenseitem"] = "EXPENSEITEM_JDXFY";
                    entry["fremark"] = dtoEntry.Remark;
                    entry["fseq_sap"] = dtoEntry.Seq;
                    entry["finvoicetype"] = dtoEntry.Invoicetype;
                    entry["totalamount"] = dtoEntry.Totalamount;
                    var rate = 0;
                    //税额 = 总金额 * 税率 （因为税率默认为空 即0，所以税额也为0） 
                    //entry["ftaxamount"] = 0;
                    //不含税金额 =  总金额 - 税额，因为税额为0，所以不含税金额=总金额
                    //entry["fnontaxamount"] = dtoEntry.Totalamount;
                    //不含税本位币 = 不含税金额
                    //entry["fnontaxamountcurrency"] = dtoEntry.Totalamount;
                    //税额本位币 = 税额 
                    //entry["ftaxamountcurrency"] = 0;
                    sumAmount += dtoEntry.Totalamount;
                    entry["finvoiceno"] = dtoEntry.Invoiceno;
                    entry["fspart"] = dtoEntry.Spart;
                    entry["fspartname"] = dtoEntry.SpartName;
                    entry["fcreatetime"] = dtoEntry.Createtime;
                    entry["finvoicedate"] = dtoEntry.Invoicedate;
                    entrys.Add(entry);
                }

                //总金额= 合计明细的总金额得到
                bizData["fsumtaxamount"] = sumAmount;
            }

            return bizData;
        }

        /// <summary>
        /// 创建应付数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private DynamicObject CreatePayReceiptBizData(PurchaseOrderPushDTO dto)
        {
            bizAgents.TryGetValue(dto.AgentNo, out string bizAgentId);
            var topCtx = this.Context.CreateTopOrgDBContext();
            var agentmidid = topCtx.LoadBizDataByNo("bas_agent", "fnumber", new List<string> { dto.AgentNo }).FirstOrDefault()?["id"];

            IPurchaseOrderService purchaseOrderService = this.Container.GetService<IPurchaseOrderService>();
            DynamicObject bizData = null;
            if (!dto.BillNo.IsNullOrEmptyOrWhiteSpace())
            {
                bizData = this.AgentContext.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", new[] { dto.BillNo })
                                           .FirstOrDefault();
            }
            if (bizData == null)
            {
                bizData = (DynamicObject)this.HtmlForm.GetDynamicObjectType(this.AgentContext).CreateInstance();
            }
            //新借贷项页面没有单据类型
            //bizData["fbilltype"] = "ydj_payreceipt_mid_01";
            bizData["fbillno"] = dto.BillNo;
            var deliverId = this.AgentContext.LoadBizDataByFilter("bas_deliver", $"fnumber = '{Convert.ToString(dto.DeliverNo)}'")
                .Select(o => Convert.ToString(o["id"])).FirstOrDefault();

            //往来单位类型 默认 “供应商”
            //bizData["frelatetype"] = "ydj_supplier";
            bizData["ftype"] = dto.OrderType;
            bizData["fdeliverid"] = deliverId;
            bizData["fagentid"] = agentmidid;
            //bizData["fsourcenumber"] = dto.Sourcenumber;
            bizData["fregistdate"] = dto.Registdate;
            //bizData["frelatecusid"] = purchaseOrderService.GetSupplierIdByDeliverId(this.AgentContext, deliverId);
            //bizData["fdatasource"] = "慕思总部";
            //bizData["fcurrency"] = "fcurrency_type_01";
            bizData["fsyncreason"] = dto.Syncreason;
            bizData["fdescription"] = dto.Description;
            if (dto.PayOrCollectEntrys != null)
            {
                var entrys = bizData["fentry"] as DynamicObjectCollection;
                entrys.Clear();
                decimal sumAmount = 0;
                foreach (var dtoEntry in dto.PayOrCollectEntrys)
                {
                    //全量更新
                    var entry = (DynamicObject)entrys.DynamicCollectionItemPropertyType.CreateInstance();
                    //金额 根据借贷项+子类重新计算
                    dtoEntry.Totalamount = calculateInvoice(dto.OrderType, dtoEntry.Invoicetype, dtoEntry.Totalamount);

                    //费用项目 默认为新增的预置 “借贷项费用”
                    //entry["fexpenseitem"] = "EXPENSEITEM_JDXFY";
                    entry["fremark"] = dtoEntry.Remark;
                    entry["fseq_sap"] = dtoEntry.Seq;
                    entry["finvoicetype"] = dtoEntry.Invoicetype;
                    entry["totalamount"] = dtoEntry.Totalamount;
                    var rate = 0;
                    //税额 = 总金额 * 税率 （因为税率默认为空 即0，所以税额也为0） 
                    //entry["ftaxamount"] = 0;
                    //不含税金额 =  总金额 - 税额，因为税额为0，所以不含税金额=总金额
                    //entry["fnontaxamount"] = dtoEntry.Totalamount;
                    //不含税本位币 = 不含税金额
                    //entry["fnontaxamountcurrency"] = dtoEntry.Totalamount;
                    //税额本位币 = 税额 
                    //entry["ftaxamountcurrency"] = 0;
                    sumAmount += dtoEntry.Totalamount;
                    entry["finvoiceno"] = dtoEntry.Invoiceno;
                    entry["fspart"] = dtoEntry.Spart;
                    entry["fspartname"] = dtoEntry.SpartName;
                    entry["fcreatetime"] = dtoEntry.Createtime;
                    entry["finvoicedate"] = dtoEntry.Invoicedate;
                    entrys.Add(entry);
                }

                //总金额= 合计明细的总金额得到
                bizData["fsumtaxamount"] = sumAmount;
            }

            return bizData;
        }
        /// <summary>
        /// 根据借贷项类型+子类 计算金额
        /// </summary>
        /// <param name="orderType"></param>
        /// <param name="InvoiceType"></param>
        /// <returns></returns>
        private decimal calculateInvoice(string orderType, string invoiceType, decimal amount)
        {
            if (orderType.EqualsIgnoreCase("Z009贷") && invoiceType.ToUpper().EqualsIgnoreCase("O"))
            {
                amount = Math.Abs(amount);
            }
            if (orderType.EqualsIgnoreCase("Z009贷") && invoiceType.ToUpper().EqualsIgnoreCase("S"))
            {
                amount = Math.Abs(amount) * -1;
            }
            if (orderType.EqualsIgnoreCase("Z010借") && invoiceType.ToUpper().EqualsIgnoreCase("P"))
            {
                amount = Math.Abs(amount) * -1;
            }
            if (orderType.EqualsIgnoreCase("Z010借") && invoiceType.ToUpper().EqualsIgnoreCase("N"))
            {
                amount = Math.Abs(amount);
            }
            return amount;
        }

        /// <summary>
        /// 填充辅助属性字段
        /// </summary>
        /// <param name="bizData"></param>
        /// <param name="dto"></param>
        private void FillAuxPropFieldValue(DynamicObject bizData, PurchaseOrderPushDTO dto)
        {
            var entrys = bizData["fentity"] as DynamicObjectCollection;

            foreach (var entry in entrys)
            {
                var tranId = Convert.ToString(entry["ftranid"]);
                var productId = Convert.ToString(entry["fmaterialid"]);
                var dtoEntry = dto.Entrys.First(s => s.Id.EqualsIgnoreCase(tranId));

                entry["fattrinfo_ref"] = ProductUtil.ConvertAuxPropFieldValue(this.Context, productId, dtoEntry.AuxPropVals);
            }
        }

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Valid(PurchaseOrderPushDTO dto, BaseResponse<object> resp)
        {
            List<string> errorMsgs = null;
            if (this.HtmlForm.Id == "ydj_purchaseorder")
            {
                // 设置要转换的基础资料：送达方、商品、业务品牌
                var baseDataFieldIds = new[] { "fdeliverid", "fmaterialid", "fresultbrandid" };
                this.AgentContext.TryConvertBaseData(this.HtmlForm, new List<DynamicObject> { this.BizData }, baseDataFieldIds,
                    out errorMsgs);

                TryConvertEnum(this.HtmlForm, this.BizData, out var errorMsgs2);
                errorMsgs.AddRange(errorMsgs2);
                if (dto.Entrys == null || !dto.Entrys.Any())
                {
                    errorMsgs.Add("entrys不能为空！");
                }
            }
            //else
            //{
            //    this.AgentContext.TryConvertBaseData(this.HtmlForm, new List<DynamicObject> { this.BizData },
            //        out errorMsgs);
            //    var obj = this.AgentContext.LoadBizDataByFilter(this.HtmlForm.Id, $"fsourcenumber = '{dto.Sourcenumber}'").FirstOrDefault();
            //    if (obj != null)
            //    {
            //        //errorMsgs.Add($"已同步过来源订单为{dto.Sourcenumber}的单据！");
            //        throw new WarnException($"已同步过来源订单为{dto.BillNo}的单据！");
            //    }
            //}

            if (!errorMsgs.IsNullOrEmpty())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;
                return false;
            }

            return true;
        }

        private bool TryConvertEnum(HtmlForm htmlForm, DynamicObject bizData, out List<string> errorMsgs)
        {
            errorMsgs = new List<string>();

            var selSuiteRequireField = htmlForm.GetField("fselsuiterequire");

            var assistDataSource = htmlForm.GetAssistDataSource(this.Context, selSuiteRequireField.Id);

            var entrys = bizData["fentity"] as DynamicObjectCollection;

            foreach (var entry in entrys)
            {
                string fselsuiterequire = Convert.ToString(entry[selSuiteRequireField.PropertyName]);
                if (fselsuiterequire.IsNullOrEmptyOrWhiteSpace()) continue;

                var assistData = assistDataSource[selSuiteRequireField.Id].FirstOrDefault(s => Convert.ToString(s["name"]).EqualsIgnoreCase(fselsuiterequire));
                if (assistData == null)
                {
                    errorMsgs.Add($"{selSuiteRequireField.Caption}【{fselsuiterequire}】不存在！");
                    continue;
                }

                // 替换为id
                entry[selSuiteRequireField.PropertyName] = assistData["id"];
            }

            return errorMsgs.Count == 0;
        }

        /// <summary>
        /// 计算
        /// </summary>
        /// <param name="purchaseOrder"></param>
        private void Calculate(DynamicObject purchaseOrder)
        {
            PurchaseOrderUtil.Calcuate(this.AgentContext, purchaseOrder);
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected override void SetNumbers()
        {
            this.Request.SetBillNo(MSKey.BillNo, (this.Request.Dto as PurchaseOrderPushDTO)?.BillNo);
        }

        /// <summary>
        /// 获取入库数量
        /// </summary>
        /// <param name="purEntryIds">采购订单明细行内码</param>
        /// <returns></returns>
        private List<DynamicObject> GetInStockQty(List<string> purEntryIds)
        {
            if (purEntryIds == null || !purEntryIds.Any()) return null;
            var sql = $@"SELECT fpoorderentryid,SUM(fqty) AS inStockQty 
                         FROM T_STK_POSTOCKINENTRY WITH(NOLOCK) 
                         WHERE fpoorderentryid IN ('{string.Join("','", purEntryIds)}')  
                         GROUP BY fpoorderentryid ";
            var inStockObjs = this.Context.ExecuteDynamicObject(sql, null);
            return inStockObjs.ToList();
        }

        protected override Dictionary<string, string> CreateDistributedLocks(PurchaseOrderPushDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.BillNo}", $"采购订单 {dto.BillNo} 正在锁定中，请稍后再操作！" }
            };
        }
    }
}