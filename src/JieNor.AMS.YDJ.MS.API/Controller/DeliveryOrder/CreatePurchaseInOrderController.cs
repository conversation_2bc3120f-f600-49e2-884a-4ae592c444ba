using JieNor.AMS.YDJ.MS.API.DTO.DeliveryOrder;
using JieNor.AMS.YDJ.MS.API.Filter;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataEntity.BillType;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Integration;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using ServiceStack.Redis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace JieNor.AMS.YDJ.MS.API.Controller.DeliveryOrder
{
    /// <summary>
    /// 中台交货单接口：创建采购入库单
    /// </summary>
    //[RepeatedRequestFilter("stk_postockin", 60, "交货单接口")] //对于高并发此方法不够全面，弃用
    public class CreatePurchaseInOrderController : BaseController<DeliveryOrderDTO>
    {
        public string FormId
        {
            get { return "stk_postockin"; }
        }
        private static string opName
        {
            get { return "交货单接口"; }
        }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "receiveNum";

        protected override string BizObjectFormId => this.FormId;

        public bool IsRetry { get; set; }

        //private Object lockObj = new Object();

        private ConcurrentDictionary<string, List<DynamicObject>> _postockinDic = new ConcurrentDictionary<string, List<DynamicObject>>();//采购入库单

        private ConcurrentDictionary<string, List<DynamicObject>> _receptionscantaskDic = new ConcurrentDictionary<string, List<DynamicObject>>();//收货扫描任务

        private ConcurrentDictionary<string, List<DynamicObject>> _barcodemasterDic = new ConcurrentDictionary<string, List<DynamicObject>>();//条码主档

        private ConcurrentDictionary<string, List<DynamicObject>> _scanresultDic = new ConcurrentDictionary<string, List<DynamicObject>>();//扫描记录

        public override object Execute(DeliveryOrderDTO dto)
        {
            var resp = new BaseResponse<object>();
            JObject resdata = new JObject();
            //同步日志对象
            var loghtmlForm = this.MetaModelService.LoadFormModel(this.Context, "si_operationlog");

            var successbills = new List<string>();//成功的交货单
            var errbills = new List<string>();//失败的交货单
            var errMsgList = new List<string>();
            var resmsg = "";

            if (dto == null || dto.Head == null || dto.Head.Count <= 0)
            {
                resp.Success = false;
                resp.Message = "未传递数据，请检查!";
                WriteErrorOPLog(loghtmlForm, dto, resp);
                return resp;
            }
            string sourceType = Convert.ToString(dto.Head.FirstOrDefault().SourceType);
            if (sourceType != null && sourceType.Equals("ZY10"))
            {
                //由销售订单生成采购入库单
                resp = this.SalOrderToPoStockLogic(dto, loghtmlForm);
                return resp;
            }
            else if (sourceType != null && (sourceType.Equals("ZY07")))
            {
                //1：一件代发走标准流程，生成采购入库单后，由计划任务自动生成销售出库单
                //2：标准采购入库单逻辑
                //【分批入库】：(2)当接口推送单据过来时，单据所属经销商.经销类型=直营，并且销售订单类型=ZY07(直营样品备货)
                //满足以上条件，生成收货扫描任务，由扫描任务去触发生成采购入库单，完成入库。
                resp = this.PoStockLogic(dto, loghtmlForm, true);
                return resp;
            }
            else
            {
                //经销商模式标准功能
                resp = this.PoStockLogic(dto, loghtmlForm, false);
                return resp;
            }
        }

        /// <summary>
        /// 直营销售合同逻辑
        /// 由销售合同生成采购入库单
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="loghtmlForm"></param>
        /// <returns></returns>
        private BaseResponse<object> SalOrderToPoStockLogic(DeliveryOrderDTO dto, HtmlForm loghtmlForm)
        {
            var resp = new BaseResponse<object>();
            JObject resdata = new JObject();
            var successbills = new List<string>();//成功的交货单
            var errbills = new List<string>();//失败的交货单
            var errMsgList = new List<string>();
            var resmsg = "";
            var savepurchaseInOrders = new List<DynamicObject>(); //本次所保存的采购入库单

            var retryFlag = GetIsRetry(dto);
            var requestFlat = GetSameTiemRequest();
            IsRetry = retryFlag || requestFlat;

            var systemIntegrationService = this.Container.GetService<ISystemIntegrationService>();
            var opLogObj = systemIntegrationService.CreateOperationLog(this.Context, loghtmlForm, this.Request.AbsoluteUri, opName, FormId, "2", "慕思中台调用当前系统接口");
            opLogObj["fopstatus"] = "2";
            WriteOperationLog(systemIntegrationService, opLogObj, $"请求参数：{dto.ToJson()}");

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, FormId);//采购入库单
            var geteway = this.Context.Container.GetService<IHttpServiceInvoker>();
            try
            {
                // 假设在 SalOrderToPoStockLogic 或 PoStockLogic 方法内，直营判断后添加如下代码
                if (true)
                {
                    // 1. 收集所有明细的 OrderLineNum
                    var orderNumList = dto.Head
                        .Where(h => h.DetailDatas != null)
                        .SelectMany(h => h.DetailDatas)
                        .Select(d => d.OrderNum)
                        .Where(x => !string.IsNullOrWhiteSpace(x))
                        .Distinct()
                        .ToList();

                    // 2. 批量查询所有经销商编码
                    Dictionary<string, string> orderLineNum2DealerNum = new Dictionary<string, string>();
                    if (orderNumList.Any())
                    {
                        string inClause = string.Join(",", orderNumList.Select(x => $"'{x.Replace("'", "''")}'"));
                        string _sql = $@"
                                    SELECT b.fbillno AS OrderNum, a.fnumber AS DealerNum
                                    FROM T_BAS_AGENT a
                                    INNER JOIN T_YDJ_ORDER b ON a.fid = b.fmainorgid
                                    WHERE b.fbillno IN ({inClause})";
                        var result = this.Context.ExecuteDynamicObject(_sql, null);
                        orderLineNum2DealerNum = result
                            .GroupBy(x => x["OrderNum"]?.ToString())
                            .ToDictionary(
                                g => g.Key,
                                g => g.First()["DealerNum"]?.ToString(),
                                StringComparer.OrdinalIgnoreCase
                            );
                    }

                    // 3. 赋值到 OADealerNum
                    foreach (var head in dto.Head)
                    {
                        if (head.DetailDatas == null) continue;
                        foreach (var detail in head.DetailDatas)
                        {
                            if (string.IsNullOrWhiteSpace(detail.OrderNum)) continue;
                            if (orderLineNum2DealerNum.TryGetValue(detail.OrderNum, out var dealerNum) && !string.IsNullOrWhiteSpace(dealerNum))
                            {
                                head.OADealerNum = dealerNum;
                            }
                        }
                    }
                }
                //获取所有的经销商对应的组织
                var agents = dto.Head.Select(f => f.OADealerNum).ToList();
                string sql = string.Format(@"select a.fmainagentid,b.fsubagentid,d.fnumber as mainagent,c.fnumber as subagent
                FROM T_BAS_MAC a with(nolock)
                left join T_BAS_MACENTRY b with(nolock) on b.fid = a.fid
                left join T_BAS_AGENT c with(nolock) on c.fid=b.fsubagentid
                left join T_BAS_AGENT d with(nolock) on d.fid= a.fmainagentid
                where a.fforbidstatus = '0' and c.fnumber in ('{0}')".Fmt(string.Join("','", agents)));
                var subagents = this.Context.ExecuteDynamicObject(sql, null).ToList();
                var newagents = subagents.Select(f => f["mainagent"]?.ToString()).ToList();

                foreach (var agent in agents)
                {
                    if (!newagents.Contains(agent))
                    {
                        newagents.Add(agent);
                    }
                }

                var agentdatas = this.Context.LoadBizBillHeadDataByACLFilter("bas_agent", $"fnumber in ('{string.Join("','", newagents)}')", "fnumber,fname").ToList();

                var profileService = this.Container.GetService<ISystemProfile>();
                //按经销商分组
                var groups = dto.Head.GroupBy(f => f.OADealerNum).ToList();
                foreach (var group in groups)
                {
                    var orgno = group.Key;
                    var agent = subagents.FirstOrDefault(f => f["subagent"]?.ToString() == group.Key);
                    if (agent != null)
                    {
                        orgno = agent["mainagent"]?.ToString();
                    }
                    var orgdata = agentdatas.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == orgno);
                    var orgid = Convert.ToString(orgdata?["id"]);
                    //根据经销商创建上下文
                    UserContext ctx = this.Context;

                    if (!orgid.IsNullOrEmptyOrWhiteSpace())
                    {
                        ctx = this.Context.CreateAgentDBContext(orgid);
                    }
                    else
                    {
                        resmsg = $"经销商【{group.Key}】对应的交货单下发成功：未找到经销商【{group.Key}】，无需下发。";
                        RecordMsg(systemIntegrationService, opLogObj, resmsg, group, ref errMsgList, ref successbills);
                        continue;
                    }
                    //总部发货后自动生成入库单
                    var fautostockinorder = profileService.GetSystemParameter(ctx, "stk_stockparam", "fautostockinorder", false);
                    //同步总部交货单起始日期
                    var fwmsenabledate = profileService.GetSystemParameter(ctx, "stk_stockparam", "fwmsenabledate", DateTime.MinValue);
                    if (!fautostockinorder)
                    {
                        resmsg = $"经销商【{group.Key}】对应的交货单下发成功：未启用库存管理参数【总部发货后自动生成入库单】，无需下发！";
                        RecordMsg(systemIntegrationService, opLogObj, resmsg, group, ref errMsgList, ref successbills);
                        continue;
                    }

                    var purorders = new List<string>();
                    var htNos = new List<string>();
                    var barcodelists = new List<string>();
                    var purinnos = new List<string>();
                    var factorys = new List<string>();
                    var securityLists = new List<string>();
                    GetAllNumberDatas(group, ref purinnos, ref purorders, ref htNos, ref factorys, ref barcodelists, ref securityLists);

                    //获取所有的采购订单
                    var pofilter = "";
                    //if (purorders != null && purorders.Any())
                    //{
                    //    pofilter += $" fbillno in ('{string.Join("','", purorders)}')";
                    //}
                    if (purorders != null && purorders.Any())
                    {
                        if (pofilter.IsNullOrEmptyOrWhiteSpace())
                        {
                            pofilter += $" fbillno in ('{string.Join("','", purorders)}')";
                        }
                        else
                        {
                            pofilter += $" or fhqderno in ('{string.Join("','", purorders)}')";
                        }
                    }
                    var order = ctx.LoadBizDataByFilter("ydj_order", pofilter, true);
                    if (order == null || order.Count <= 0)
                    {
                        resmsg = $"经销商【{group.Key}】对应的交货单下发失败：没有对应的销售合同！";
                        RecordMsg(systemIntegrationService, opLogObj, resmsg, group, ref errMsgList, ref errbills);
                        continue;
                    }

                    //获取所有的采购入库单
                    var purchaseInOrders = ctx.LoadBizDataByFilter("stk_postockin", $" fbillno in ('{string.Join("','", purinnos)}')");

                    //获取所有的交货工厂信息
                    var nowfactorys = ctx.LoadBizDataByFilter("ydj_deliveryfactory", $" fnumber in ('{string.Join("','", factorys)}')");

                    // 根据采购订单获取所有的物料信息
                    var materialids = new List<string>();
                    foreach (var salOrder in order)
                    {
                        var salOrderEns = salOrder["fentry"] as DynamicObjectCollection;
                        materialids.AddRange(salOrderEns.Select(f => f["fproductid"]?.ToString()).ToList());
                    }
                    var materials = ctx.LoadBizDataById("ydj_product", materialids, true);

                    var currGroupSuccessBills = new List<string>();
                    foreach (var data in group)
                    {
                        try
                        {
                            if (currGroupSuccessBills.Contains(data.ReceiveNum))
                            {
                                if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发成功：中台有重复下发，金蝶已去重处理!";
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            double expirySeconds = 30;
                            if (!CheckRepeatOrderData(ctx, data, expirySeconds))
                            {
                                // 多加一重判断：判断该采购入库单是否存在，如果存在，则认为是重复下发
                                if (ExistsPoInOrder(ctx, htmlForm, data))
                                {
                                    resmsg = $"交货单【{data.ReceiveNum}】下发成功：{expirySeconds}秒内中台有重复下发，金蝶已去重处理!";
                                    RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                    continue;
                                }
                            }

                            if (data.DetailDatas == null)
                            {
                                if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发成功：下发的数据无明细数据!";
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            //判断交货日期是否小于库存管理参数中的同步总部交货单起始日期，是的话整单提示下发成功但是不能生成对应单据
                            if (Convert.ToDateTime(data.ShipDate) < fwmsenabledate)
                            {
                                if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发成功：同步总部交货单起始日期【{fwmsenabledate.ToString("yyyy-MM-dd")}】之前的单据已经手工做单，不需要对接生成!";
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            var errMsg = new List<string>();
                            //检查必录字段是否有值
                            errMsg = CheckMustInputInfo(data);
                            if (errMsg != null && errMsg.Any())
                            {
                                if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + string.Join("", errMsg);
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            DynamicObject curscantask = purchaseInOrders.FirstOrDefault(f => f["fbillno"]?.ToString() == data.ReceiveNum);

                            if (curscantask != null)
                            {
                                if (Convert.ToString(curscantask["fstatus"]) == "E" || Convert.ToString(curscantask["fstatus"]) == "D")
                                {
                                    if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                    resmsg = $"交货单【{data.ReceiveNum}】下发成功：当前交货单【{data.ReceiveNum}】产生的采购入库单【{Convert.ToString(curscantask["fbillno"])}】已提交或已审核，不能继续下发 ! ";
                                    RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                    continue;
                                }
                            }

                            //检查是否可以下发数据
                            List<string> inStockMsg = new List<string>();
                            errMsg = CheckIsCanIssueDataByZY(data, purchaseInOrders, order, nowfactorys, ref inStockMsg);
                            if (errMsg != null && errMsg.Any())
                            {
                                if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + string.Join("", errMsg);
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }


                            #region 创建并保存采购入库单

                            var postockinValue = new List<DynamicObject>();
                            bool postockinFlag = _postockinDic.TryGetValue(data.ReceiveNum, out postockinValue);
                            if (!postockinFlag)
                            {
                                var newPurchaseInOrder = CreatePurchaseInOrderByZY(ctx, htmlForm, order, materials, dto, nowfactorys, purchaseInOrders, data.ReceiveNum);
                                if (newPurchaseInOrder.Any())
                                {
                                    purchaseInOrders.AddRange(newPurchaseInOrder);
                                    _postockinDic.TryAdd(data.ReceiveNum, newPurchaseInOrder);
                                }
                            }

                            #endregion


                            currGroupSuccessBills.Add(data.ReceiveNum);
                            successbills.Add(data.ReceiveNum);
                            WriteOperationLog(systemIntegrationService, opLogObj, $"交货单【{data.ReceiveNum}】下发成功！{string.Join("", inStockMsg)}");
                            if (inStockMsg.Any())
                            {
                                errMsgList.Add($"交货单【{data.ReceiveNum}】部分下发成功！{string.Join("", inStockMsg)}");
                            }
                        }
                        catch (Exception ex)
                        {
                            //禅道#55663：异常交货单不要影响其他单据下发
                            resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + ex.Message;
                            RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);

                            this.LogService.Error("交货单下发失败！", ex);
                        }
                    }

                    // 反写销售合同【总部已发货数】
                    Core.Helpers.OrderQtyWriteBackHelper.WriteBackHqDeliveryQty(
                        ctx, htmlForm, purchaseInOrders, "save");

                    // 反写销售合同【流程状态】
                    Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                        ctx, htmlForm, purchaseInOrders);
                }

                #region 保存日志并返回结果
                errMsgList = errMsgList.Distinct().ToList();
                errbills = errbills.Distinct().ToList();
                successbills = successbills.Distinct().ToList();
                resdata["successbills"] = string.Join(",", successbills);
                resdata["errorbills"] = string.Join(",", errbills);
                resp.Data = resdata;
                if (successbills != null && successbills.Count > 0)
                {
                    resp.Success = true;
                    resp.Message = "下发成功!" + string.Join("", errMsgList);
                    resp.Code = 200;
                    if (successbills != null && errbills.Count > 0)
                    {
                        resp.Message = "部分下发成功!" + string.Join("", errMsgList);
                    }
                }
                else
                {
                    resp.Success = false;
                    resp.Message = "下发失败!" + string.Join("", errMsgList);
                    resp.Code = 201;
                }

                JObject responseDtoJObj = null;
                try
                {
                    responseDtoJObj = JObject.Parse(resp.ToJson());
                }
                catch
                {
                    // ignored
                }

                if (responseDtoJObj == null)
                {
                    opLogObj["fopstatus"] = "3";
                }
                else
                {
                    opLogObj["fopstatus"] = responseDtoJObj.GetJsonValue<bool>("Success", false) ? "2" : "3";
                }
                opLogObj["fsuccessnumbers"] = string.Join(",", successbills);
                opLogObj["ffailnumbers"] = string.Join(",", errbills);
                WriteOperationLog(systemIntegrationService, opLogObj, $"响应参数：{resp.ToJson()}");
                SaveOperationLog(systemIntegrationService, loghtmlForm, new[] { opLogObj });

                #endregion

                return resp;
                //}
            }
            catch (Exception ex)
            {
                //释放锁的对象
                //Monitor.Exit(lockObj);
                foreach (var data in dto.Head)
                {
                    if (!errbills.Contains(data.ReceiveNum) && !successbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                }
                resdata["successbills"] = string.Join(",", successbills);
                resdata["errorbills"] = string.Join(",", errbills);
                resp.Success = false;
                resp.Message = "执行发生异常:" + ex.Message;
                resp.Data = resdata;

                opLogObj["fopstatus"] = "3";
                opLogObj["ferrorsource"] = "2";
                opLogObj["fsuccessnumbers"] = string.Join(",", successbills);
                opLogObj["ffailnumbers"] = string.Join(",", errbills);
                WriteOperationLog(systemIntegrationService, opLogObj, "执行异常:" + ex.Message);
                opLogObj["fopstatus"] = "3";
                opLogObj["fdescription"] = "执行异常：" + ex.Message;
                SaveOperationLog(systemIntegrationService, loghtmlForm, new[] { opLogObj });

                this.LogService.Error("交货单下发失败！", ex);
                return resp;
            }
        }

        /// <summary>
        /// 采购入库单逻辑
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="loghtmlForm"></param>
        /// <returns></returns>
        private BaseResponse<object> PoStockLogic(DeliveryOrderDTO dto, HtmlForm loghtmlForm, bool isDirectSale)
        {
            var resp = new BaseResponse<object>();
            JObject resdata = new JObject();
            var successbills = new List<string>();//成功的交货单
            var errbills = new List<string>();//失败的交货单
            var errMsgList = new List<string>();
            var resmsg = "";
            var savepurchaseInOrders = new List<DynamicObject>(); //本次所保存的采购入库单

            var retryFlag = GetIsRetry(dto);
            var requestFlat = GetSameTiemRequest();
            IsRetry = retryFlag || requestFlat;

            var systemIntegrationService = this.Container.GetService<ISystemIntegrationService>();
            var opLogObj = systemIntegrationService.CreateOperationLog(this.Context, loghtmlForm, this.Request.AbsoluteUri, opName, FormId, "2", "慕思中台调用当前系统接口");
            opLogObj["fopstatus"] = "2";
            WriteOperationLog(systemIntegrationService, opLogObj, $"请求参数：{dto.ToJson()}");

            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, FormId);//采购入库单
            var bchtmlForm = this.MetaModelService.LoadFormModel(this.Context, "bcm_barcodemaster"); //条码主档
            var scanhtmlForm = this.MetaModelService.LoadFormModel(this.Context, "bcm_scanresult");//条码扫描记录
            var taskhtmlForm = this.MetaModelService.LoadFormModel(this.Context, "bcm_receptionscantask");//收货扫描任务
            var geteway = this.Context.Container.GetService<IHttpServiceInvoker>();
            try
            {
                // 假设在 SalOrderToPoStockLogic 或 PoStockLogic 方法内，直营判断后添加如下代码
                if (isDirectSale)
                {
                    // 1. 收集所有明细的 OrderLineNum
                    var orderNumList = dto.Head
                        .Where(h => h.DetailDatas != null)
                        .SelectMany(h => h.DetailDatas)
                        .Select(d => d.OrderNum)
                        .Where(x => !string.IsNullOrWhiteSpace(x))
                        .Distinct()
                        .ToList();

                    // 2. 批量查询所有经销商编码
                    Dictionary<string, string> orderLineNum2DealerNum = new Dictionary<string, string>();
                    if (orderNumList.Any())
                    {
                        string inClause = string.Join(",", orderNumList.Select(x => $"'{x.Replace("'", "''")}'"));
                        string _sql = $@"
                                    SELECT b.fbillno AS OrderNum, a.fnumber AS DealerNum
                                    FROM T_BAS_AGENT a
                                    INNER JOIN t_ydj_purchaseorder b ON a.fid = b.fmainorgid
                                    WHERE b.fbillno IN ({inClause})";
                        var result = this.Context.ExecuteDynamicObject(_sql, null);
                        orderLineNum2DealerNum = result
                            .GroupBy(x => x["OrderNum"]?.ToString())
                            .ToDictionary(
                                g => g.Key,
                                g => g.First()["DealerNum"]?.ToString(),
                                StringComparer.OrdinalIgnoreCase
                            );
                    }

                    // 3. 赋值到 OADealerNum
                    foreach (var head in dto.Head)
                    {
                        if (head.DetailDatas == null) continue;
                        foreach (var detail in head.DetailDatas)
                        {
                            if (string.IsNullOrWhiteSpace(detail.OrderNum)) continue;
                            if (orderLineNum2DealerNum.TryGetValue(detail.OrderNum, out var dealerNum) && !string.IsNullOrWhiteSpace(dealerNum))
                            {
                                head.OADealerNum = dealerNum;
                            }
                        }
                    }
                }


                //lock (lockObj) 
                //{
                //获取所有的经销商对应的组织
                var agents = dto.Head.Select(f => f.OADealerNum).ToList();
                string sql = string.Format(@"select a.fmainagentid,b.fsubagentid,d.fnumber as mainagent,c.fnumber as subagent
                FROM T_BAS_MAC a with(nolock)
                left join T_BAS_MACENTRY b with(nolock) on b.fid = a.fid
                left join T_BAS_AGENT c with(nolock) on c.fid=b.fsubagentid
                left join T_BAS_AGENT d with(nolock) on d.fid= a.fmainagentid
                where a.fforbidstatus = '0' and c.fnumber in ('{0}')".Fmt(string.Join("','", agents)));
                var subagents = this.Context.ExecuteDynamicObject(sql, null).ToList();
                var newagents = subagents.Select(f => f["mainagent"]?.ToString()).ToList();

                foreach (var agent in agents)
                {
                    if (!newagents.Contains(agent))
                    {
                        newagents.Add(agent);
                    }
                }

                var agentdatas = this.Context.LoadBizBillHeadDataByACLFilter("bas_agent", $"fnumber in ('{string.Join("','", newagents)}')", "fnumber,fname").ToList();

                var profileService = this.Container.GetService<ISystemProfile>();
                //按经销商分组
                var groups = dto.Head.GroupBy(f => f.OADealerNum).ToList();
                foreach (var group in groups)
                {
                    var orgno = group.Key;
                    var agent = subagents.FirstOrDefault(f => f["subagent"]?.ToString() == group.Key);
                    if (agent != null)
                    {
                        orgno = agent["mainagent"]?.ToString();
                    }
                    var orgdata = agentdatas.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == orgno);
                    var orgid = Convert.ToString(orgdata?["id"]);
                    //根据经销商创建上下文
                    UserContext ctx = this.Context;

                    if (!orgid.IsNullOrEmptyOrWhiteSpace())
                    {
                        ctx = this.Context.CreateAgentDBContext(orgid);
                    }
                    else
                    {
                        resmsg = $"经销商【{group.Key}】对应的交货单下发成功：未找到经销商【{group.Key}】，无需下发。";
                        RecordMsg(systemIntegrationService, opLogObj, resmsg, group, ref errMsgList, ref successbills);
                        continue;
                    }

                    //是否启用条码管理                    
                    var fenablebarcode = profileService.GetSystemParameter(ctx, "stk_stockparam", "fenablebarcode", false);
                    //总部发货后自动生成入库单
                    var fautostockinorder = profileService.GetSystemParameter(ctx, "stk_stockparam", "fautostockinorder", false);
                    //同步总部交货单起始日期
                    var fwmsenabledate = profileService.GetSystemParameter(ctx, "stk_stockparam", "fwmsenabledate", DateTime.MinValue);
                    if (!fautostockinorder)
                    {
                        resmsg = $"经销商【{group.Key}】对应的交货单下发成功：未启用库存管理参数【总部发货后自动生成入库单】，无需下发！";
                        RecordMsg(systemIntegrationService, opLogObj, resmsg, group, ref errMsgList, ref successbills);
                        continue;
                    }

                    var purorders = new List<string>();
                    var htNos = new List<string>();
                    var barcodelists = new List<string>();
                    var purinnos = new List<string>();
                    var factorys = new List<string>();
                    var securityLists = new List<string>();
                    GetAllNumberDatas(group, ref purinnos, ref purorders, ref htNos, ref factorys, ref barcodelists, ref securityLists);

                    //获取所有的采购订单
                    var pofilter = "";
                    if (purorders != null && purorders.Any())
                    {
                        pofilter += $" fbillno in ('{string.Join("','", purorders)}')";
                    }
                    if (htNos != null && htNos.Any())
                    {
                        if (pofilter.IsNullOrEmptyOrWhiteSpace())
                        {
                            pofilter += $" fhqderno in ('{string.Join("','", htNos)}')";
                        }
                        else
                        {
                            pofilter += $" or fhqderno in ('{string.Join("','", htNos)}')";
                        }
                    }
                    var purchaseOrders = ctx.LoadBizDataByFilter("ydj_purchaseorder", pofilter, true);
                    if (purchaseOrders == null || purchaseOrders.Count <= 0)
                    {
                        resmsg = $"经销商【{group.Key}】对应的交货单下发失败：没有对应的采购订单！";
                        RecordMsg(systemIntegrationService, opLogObj, resmsg, group, ref errMsgList, ref errbills);
                        continue;
                    }
                    //当采购单的下游存在非系统管理员创建的入库单,不允许下发
                    //foreach (var purItem in purchaseOrders)
                    //{
                    //    // 查询下游入库单
                    //    var poBillNo = purItem["fbillno"]?.ToString();
                    var poBillNos = purchaseOrders.Select(a => Convert.ToString(a["fbillno"])).ToList();
                    var _sql = $@"SELECT DISTINCT A.fbillno,B.fsourcebillno, A.fcreatorid
                    FROM T_STK_POSTOCKIN A WITH(NOLOCK)
					INNER JOIN T_STK_POSTOCKINENTRY B WITH(NOLOCK) ON A.fid=B.fid
                    WHERE B.fsourcebillno in ('{string.Join("','", poBillNos)}') AND A.fcancelstatus = '0'
    ";
                    var inStockList = ctx.ExecuteDynamicObject(_sql, null);

                    // 判断是否存在非系统管理员创建的入库单
                    if (inStockList != null && inStockList.Any())
                    {
                        // 这里假设系统管理员账号为“admin”
                        var hasNonAdmin = inStockList.Where(x =>
                            !string.Equals(Convert.ToString(x["fcreatorid"]), "sysadmin", StringComparison.OrdinalIgnoreCase)
                        );
                        if (hasNonAdmin != null)
                        {
                            List<string> _errorBillno = hasNonAdmin.Select(a => Convert.ToString(a["fsourcebillno"])).ToList();
                            resmsg = $"经销商【{group.Key}】对应的交货单下发失败：采购订单:{string.Join("','", _errorBillno)}已存在手工下推的入库单，禁止下发！";
                            RecordMsg(systemIntegrationService, opLogObj, resmsg, group, ref errMsgList, ref errbills);
                            //continue;
                            //过滤异常的采购订单对应的入库单
                            // 1. 找出异常采购订单号
                            var abnormalPoBillNos = new HashSet<string>(
                                inStockList
                                    .Where(x => !string.Equals(Convert.ToString(x["fcreatorid"]), "sysadmin", StringComparison.OrdinalIgnoreCase))
                                    .Select(x => Convert.ToString(x["fsourcebillno"])),
                                StringComparer.OrdinalIgnoreCase
                            );

                            // 2. 过滤掉异常采购订单号对应的 purinnos
                            //if (abnormalPoBillNos.Count > 0)
                            //{
                            //    var error = group
                            //        .Where(a => !abnormalPoBillNos.Contains(a.DetailDatas.FirstOrDefault()?.OrderNum))
                            //        .ToList();
                            //}
                            // 过滤掉没有明细的 group
                            if (abnormalPoBillNos.Count() > 0)
                            {
                                var _group = group.Where(a => !abnormalPoBillNos.Contains(a.DetailDatas.FirstOrDefault()?.OrderNum)).ToList();
                                if (_group.Count() > 0)
                                {
                                    purorders = new List<string>();
                                    htNos = new List<string>();
                                    barcodelists = new List<string>();
                                    purinnos = new List<string>();
                                    factorys = new List<string>();
                                    securityLists = new List<string>();
                                    GetAllNumberDatas(_group, ref purinnos, ref purorders, ref htNos, ref factorys, ref barcodelists, ref securityLists);
                                }
                            }
                        }
                    }
                    //}

                    //获取所有的采购入库单
                    var purchaseInOrders = ctx.LoadBizDataByFilter("stk_postockin", $" fbillno in ('{string.Join("','", purinnos)}')");

                    //获取所有的交货工厂信息
                    var nowfactorys = ctx.LoadBizDataByFilter("ydj_deliveryfactory", $" fnumber in ('{string.Join("','", factorys)}')");

                    //获取所有的条码主档
                    var barcodemasters = ctx.LoadBizDataByNo("bcm_barcodemaster", "fnumber", barcodelists, true);

                    //获取所有防伪码生成的条码主档
                    var securityCodemasters = ctx.LoadBizDataByNo("bcm_barcodemaster", "fnumber", securityLists, true);

                    //根据条码主档获取条码扫描记录
                    var bcids = barcodemasters.Select(f => f["Id"]?.ToString()).ToList();
                    var scanresults = ctx.LoadBizDataByFilter("bcm_scanresult", $" fsourceformid='ydj_purchaseorder' and fbarcode in ('{string.Join("','", bcids)}') ");

                    // 根据采购订单获取所有的物料信息
                    var materialids = new List<string>();
                    foreach (var purOrder in purchaseOrders)
                    {
                        var purOrderEns = purOrder["fentity"] as DynamicObjectCollection;
                        materialids.AddRange(purOrderEns.Select(f => f["fmaterialid"]?.ToString()).ToList());
                    }
                    var materials = ctx.LoadBizDataById("ydj_product", materialids, true);

                    //因为只有启用了条码管理参数后才产生扫描任务，交货单号就是收货扫描任务的收货单号
                    var ScanTasks = ctx.LoadBizDataByFilter("bcm_receptionscantask", $"freceptionno in ('{string.Join("','", purinnos)}')");

                    var currGroupSuccessBills = new List<string>();
                    foreach (var data in group)
                    {
                        try
                        {
                            if (currGroupSuccessBills.Contains(data.ReceiveNum))
                            {
                                if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发成功：中台有重复下发，金蝶已去重处理!";
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            double expirySeconds = 30;
                            if (!CheckRepeatOrderData(ctx, data, expirySeconds))
                            {
                                // 多加一重判断：判断该采购入库单是否存在，如果存在，则认为是重复下发
                                if (ExistsPoInOrder(ctx, htmlForm, data))
                                {
                                    resmsg = $"交货单【{data.ReceiveNum}】下发成功：{expirySeconds}秒内中台有重复下发，金蝶已去重处理!";
                                    RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                    continue;
                                }
                            }

                            if (data.DetailDatas == null)
                            {
                                if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发成功：下发的数据无明细数据!";
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            //判断交货日期是否小于库存管理参数中的同步总部交货单起始日期，是的话整单提示下发成功但是不能生成对应单据
                            if (Convert.ToDateTime(data.ShipDate) < fwmsenabledate)
                            {
                                if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发成功：同步总部交货单起始日期【{fwmsenabledate.ToString("yyyy-MM-dd")}】之前的单据已经手工做单，不需要对接生成!";
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            var errMsg = new List<string>();
                            //检查必录字段是否有值
                            errMsg = CheckMustInputInfo(data);
                            if (errMsg != null && errMsg.Any())
                            {
                                if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + string.Join("", errMsg);
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            DynamicObject curscantask = null;
                            if (fenablebarcode)
                                curscantask = ScanTasks.FirstOrDefault(f => Convert.ToString(f["freceptionno"]) == data.ReceiveNum);
                            else
                                curscantask = purchaseInOrders.FirstOrDefault(f => f["fbillno"]?.ToString() == data.ReceiveNum);

                            if (curscantask != null)
                            {
                                if (fenablebarcode)
                                {
                                    if (Convert.ToString(curscantask["ftaskstatus"]) != "ftaskstatus_01")
                                    {
                                        if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                        resmsg = $"交货单【{data.ReceiveNum}】下发成功：当前交货单【{data.ReceiveNum}】产生的收货扫描任务【{Convert.ToString(curscantask["fbillno"])}】已进行收货，不能继续下发 ! ";
                                        RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                        continue;
                                    }
                                }
                                else
                                {
                                    if (Convert.ToString(curscantask["fstatus"]) == "E" || Convert.ToString(curscantask["fstatus"]) == "D")
                                    {
                                        if (!successbills.Contains(data.ReceiveNum)) successbills.Add(data.ReceiveNum);
                                        resmsg = $"交货单【{data.ReceiveNum}】下发成功：当前交货单【{data.ReceiveNum}】产生的采购入库单【{Convert.ToString(curscantask["fbillno"])}】已提交或已审核，不能继续下发 ! ";
                                        RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                        continue;
                                    }
                                }
                            }

                            //检查是否可以下发数据
                            List<string> inStockMsg = new List<string>();
                            errMsg = CheckIsCanIssueData(ctx, data, fenablebarcode, ScanTasks, purchaseInOrders, purchaseOrders, nowfactorys, ref inStockMsg);
                            if (errMsg != null && errMsg.Any())
                            {
                                if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                                resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + string.Join("", errMsg);
                                RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                continue;
                            }

                            if (fenablebarcode)
                            {
                                #region 创建并保存收货扫描任务

                                var receptionscantaskValue = new List<DynamicObject>();
                                bool receptionscantaskFlag = _receptionscantaskDic.TryGetValue(data.ReceiveNum, out receptionscantaskValue);
                                if (!receptionscantaskFlag)
                                {
                                    List<string> opMsgs = new List<string>();
                                    var newReceiveScanTask = CreateReceiveScanTask(ctx, taskhtmlForm, purchaseOrders, ScanTasks, materials, dto, data.ReceiveNum, nowfactorys, ref opMsgs);
                                    if (newReceiveScanTask.Any())
                                    {
                                        ScanTasks.AddRange(newReceiveScanTask);
                                        _receptionscantaskDic.TryAdd(data.ReceiveNum, newReceiveScanTask);
                                    }
                                    else
                                    {
                                        if (opMsgs.Any())
                                        {
                                            if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                                            resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + string.Join("", opMsgs);
                                            RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                            continue;
                                        }
                                    }
                                }

                                #endregion

                                #region 创建并保存条码主档

                                var barcodemasterValue = new List<DynamicObject>();
                                bool barcodemasterFlag = _barcodemasterDic.TryGetValue(data.ReceiveNum, out barcodemasterValue);
                                var haveNullPackType = data.DetailDatas.Any(t => t.PackageType == null);//存在商品包装类型为空
                                                                                                        //判断数据库是否已存在防伪码生成的条码主档

                                var allBarcodeInfos = data.DetailDatas.Where(a => a.BarCodeInfos != null).SelectMany(t => t.BarCodeInfos).ToList();
                                var dbHaveSecurityCode = allBarcodeInfos.Any(t => !t.SecurityCode.IsNullOrEmptyOrWhiteSpace() && securityCodemasters.Any(s => Convert.ToString(s["fnumber"]).EqualsIgnoreCase(t.SecurityCode)));
                                if (haveNullPackType)
                                {
                                    if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                                    string nullMsg = "收货扫描任务成功接收，条码主档因工厂未维护【打包类型】无法生成！";
                                    resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + nullMsg;
                                    RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                    continue;
                                }
                                else if (dbHaveSecurityCode)
                                {
                                    if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                                    var dbCodeMaster = securityCodemasters.Where(s => allBarcodeInfos.Any(t => Convert.ToString(s["fnumber"]).EqualsIgnoreCase(t.SecurityCode)));
                                    var codeNumbers = string.Join("，", dbCodeMaster.Select(t => Convert.ToString(t["fnumber"])).Distinct());
                                    var codeSources = string.Join("，", dbCodeMaster.Select(t => Convert.ToString(t["frecnum"])).Distinct());
                                    resmsg = $"交货单【{data.ReceiveNum}】下发失败：包含已下发过的防伪码【{codeNumbers}】，来源交货单【{codeSources}】！";
                                    RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);
                                    continue;
                                }
                                else if (!barcodemasterFlag)
                                {
                                    var newBarcodeMaster = CreateBarcodeMaster(ctx, bchtmlForm, purchaseOrders, materials, barcodemasters, dto, data.ReceiveNum);
                                    //重复添加校验
                                    var barCodeNumbers = barcodemasters.Select(o => o["fnumber"]?.ToString()).ToList();
                                    foreach (var item in newBarcodeMaster)
                                    {
                                        if (!barCodeNumbers.Contains(item["fnumber"]?.ToString()))
                                        {
                                            if (item != null)
                                            {
                                                barcodemasters.Add(item);

                                            }
                                        }
                                    }
                                    _barcodemasterDic.TryAdd(data.ReceiveNum, newBarcodeMaster);
                                }

                                #endregion

                                #region 创建并保存条码扫描记录

                                var scanresultValue = new List<DynamicObject>();
                                bool scanresultFlag = _scanresultDic.TryGetValue(data.ReceiveNum, out scanresultValue);
                                if (!scanresultFlag)
                                {
                                    var newScanResult = CreateScanResult(ctx, scanhtmlForm, purchaseOrders, barcodemasters, scanresults, dto, data.ReceiveNum);
                                    _scanresultDic.TryAdd(data.ReceiveNum, newScanResult);
                                }

                                #endregion
                            }
                            else
                            {
                                #region 创建并保存采购入库单

                                var postockinValue = new List<DynamicObject>();
                                bool postockinFlag = _postockinDic.TryGetValue(data.ReceiveNum, out postockinValue);
                                if (!postockinFlag)
                                {
                                    var newPurchaseInOrder = CreatePurchaseInOrder(ctx, htmlForm, purchaseOrders, materials, dto, nowfactorys, purchaseInOrders, data.ReceiveNum);
                                    if (newPurchaseInOrder.Any())
                                    {
                                        purchaseInOrders.AddRange(newPurchaseInOrder);
                                        _postockinDic.TryAdd(data.ReceiveNum, newPurchaseInOrder);
                                    }
                                }

                                #endregion
                            }

                            currGroupSuccessBills.Add(data.ReceiveNum);
                            successbills.Add(data.ReceiveNum);
                            WriteOperationLog(systemIntegrationService, opLogObj, $"交货单【{data.ReceiveNum}】下发成功！{string.Join("", inStockMsg)}");
                            if (inStockMsg.Any())
                            {
                                errMsgList.Add($"交货单【{data.ReceiveNum}】部分下发成功！{string.Join("", inStockMsg)}");
                            }
                        }
                        catch (Exception ex)
                        {
                            //禅道#55663：异常交货单不要影响其他单据下发
                            resmsg = $"交货单【{data.ReceiveNum}】下发失败：" + ex.Message;
                            RecordSimpleMsg(systemIntegrationService, opLogObj, resmsg, ref errMsgList);

                            this.LogService.Error("交货单下发失败！", ex);
                        }
                    }

                    // 反写销售合同【总部已发货数】
                    Core.Helpers.OrderQtyWriteBackHelper.WriteBackHqDeliveryQty(
                        ctx, htmlForm, purchaseInOrders, "save");

                    // 反写销售合同【流程状态】
                    Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                        ctx, htmlForm, purchaseInOrders);
                }

                #region 保存日志并返回结果
                errMsgList = errMsgList.Distinct().ToList();
                errbills = errbills.Distinct().ToList();
                successbills = successbills.Distinct().ToList();
                resdata["successbills"] = string.Join(",", successbills);
                resdata["errorbills"] = string.Join(",", errbills);
                resp.Data = resdata;
                if (successbills != null && successbills.Count > 0)
                {
                    resp.Success = true;
                    resp.Message = "下发成功!" + string.Join("", errMsgList);
                    resp.Code = 200;
                    if (successbills != null && errbills.Count > 0)
                    {
                        resp.Message = "部分下发成功!" + string.Join("", errMsgList);
                    }
                }
                else
                {
                    resp.Success = false;
                    resp.Message = "下发失败!" + string.Join("", errMsgList);
                    resp.Code = 201;
                }

                JObject responseDtoJObj = null;
                try
                {
                    responseDtoJObj = JObject.Parse(resp.ToJson());
                }
                catch
                {
                    // ignored
                }

                if (responseDtoJObj == null)
                {
                    opLogObj["fopstatus"] = "3";
                }
                else
                {
                    opLogObj["fopstatus"] = responseDtoJObj.GetJsonValue<bool>("Success", false) ? "2" : "3";
                }
                opLogObj["fsuccessnumbers"] = string.Join(",", successbills);
                opLogObj["ffailnumbers"] = string.Join(",", errbills);
                WriteOperationLog(systemIntegrationService, opLogObj, $"响应参数：{resp.ToJson()}");
                SaveOperationLog(systemIntegrationService, loghtmlForm, new[] { opLogObj });

                #endregion

                return resp;
                //}
            }
            catch (Exception ex)
            {
                //释放锁的对象
                //Monitor.Exit(lockObj);
                foreach (var data in dto.Head)
                {
                    if (!errbills.Contains(data.ReceiveNum) && !successbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                }
                resdata["successbills"] = string.Join(",", successbills);
                resdata["errorbills"] = string.Join(",", errbills);
                resp.Success = false;
                resp.Message = "执行发生异常:" + ex.Message;
                resp.Data = resdata;

                opLogObj["fopstatus"] = "3";
                opLogObj["ferrorsource"] = "2";
                opLogObj["fsuccessnumbers"] = string.Join(",", successbills);
                opLogObj["ffailnumbers"] = string.Join(",", errbills);
                WriteOperationLog(systemIntegrationService, opLogObj, "执行异常:" + ex.Message);
                opLogObj["fopstatus"] = "3";
                opLogObj["fdescription"] = "执行异常：" + ex.Message;
                SaveOperationLog(systemIntegrationService, loghtmlForm, new[] { opLogObj });

                this.LogService.Error("交货单下发失败！", ex);
                return resp;
            }
        }

        private void GetAllNumberDatas(IEnumerable<DTO.DeliveryOrder.DeliveryOrder> group, ref List<string> purinnos, ref List<string> purorders, ref List<string> htNos, ref List<string> factorys, ref List<string> barcodelists, ref List<string> securityLists)
        {
            foreach (var item in group)
            {
                if (item.DetailDatas == null) continue;
                if (!purinnos.Contains(item.ReceiveNum)) purinnos.Add(item.ReceiveNum);
                foreach (var item2 in item.DetailDatas)
                {
                    if (!purorders.Contains(item2.OrderNum) && !string.IsNullOrWhiteSpace(item2.OrderNum))
                    {
                        purorders.Add(item2.OrderNum);
                    }
                    if (!htNos.Contains(item2.vbelns) && !string.IsNullOrWhiteSpace(item2.vbelns))
                    {
                        htNos.Add(item2.vbelns);
                    }
                    if (!factorys.Contains(item2.DeliveryFactory) && !string.IsNullOrWhiteSpace(item2.DeliveryFactory))
                    {
                        factorys.Add(item2.DeliveryFactory);
                    }
                    if (item2.BarCodeInfos != null)
                    {
                        foreach (var barCodeInfo in item2.BarCodeInfos)
                        {
                            if (!barcodelists.Contains(barCodeInfo.ParentCode) && !string.IsNullOrWhiteSpace(barCodeInfo.ParentCode))
                            {
                                barcodelists.Add(barCodeInfo.ParentCode);
                            }
                            if (!barcodelists.Contains(barCodeInfo.ChildCode) && !string.IsNullOrWhiteSpace(barCodeInfo.ChildCode))
                            {
                                barcodelists.Add(barCodeInfo.ChildCode);
                            }
                            if (!securityLists.Contains(barCodeInfo.SecurityCode) && !string.IsNullOrWhiteSpace(barCodeInfo.SecurityCode))
                            {
                                securityLists.Add(barCodeInfo.SecurityCode);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 创建采购入库单
        /// </summary>
        private static List<DynamicObject> CreatePurchaseInOrder(
            UserContext ctx, HtmlForm htmlForm, List<DynamicObject> purchaseOrders, List<DynamicObject> allMaterials,
            DeliveryOrderDTO dto, List<DynamicObject> allFactorys, List<DynamicObject> allPurchaseInOrders, string ReceiveNum)
        {
            var purchaseInOrders = new List<DynamicObject>();
            var item = dto.Head.FirstOrDefault(f => f.ReceiveNum == ReceiveNum);
            var companyId = ctx.Company;
            var fdeliverybillno = item.DetailDatas.Select(x => x.TransReceiveNum).FirstOrDefault();
            var billtypecollection = GetBillType(ctx, "stk_postockin");
            var billtype = billtypecollection.FirstOrDefault(f => f.fname.ToString() == "标准采购入库" || (!f.fprimitivename.IsNullOrEmptyOrWhiteSpace() && f.fprimitivename.ToString() == "标准采购入库"));

            var purchaseInOrder = allPurchaseInOrders.FirstOrDefault(f => Convert.ToString(f["fbillno"]).EqualsIgnoreCase(item.ReceiveNum));
            if (purchaseInOrder == null)
            {
                purchaseInOrder = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                purchaseInOrder["fbillno"] = item.ReceiveNum;
                purchaseInOrder["fbilltype"] = billtype == null ? "poinstock_billtype_01" : billtype.fid;//"标准采购入库";//单据类型
                purchaseInOrder["fdeliverybillno"] = fdeliverybillno;//物流单号
                purchaseInOrder["fdescription"] = item.Comments;//备注
                purchaseInOrder["fdate"] = DateTime.Now;//入库日期
                purchaseInOrder["fstockstaffid"] = "";//收货人
                purchaseInOrder["fstockdeptid"] = "";//收货部门
                purchaseInOrder["fsourcetype"] = "ydj_purchaseorder";//来源单据
                purchaseInOrder["fbizruleid"] = "ydj_purchaseorder2stk_postockin";//规则
                purchaseInOrder["fsenddate"] = item.ShipDate;//工厂发货日期
            }

            purchaseInOrder["fmainorgid"] = companyId; //使用组织ID

            var purchaseInOrderEntitys = purchaseInOrder["fentity"] as DynamicObjectCollection;
            //重复下发数据时，先清掉原来的明细，重新写入数据
            purchaseInOrderEntitys.Clear();
            foreach (var detailData in item.DetailDatas)
            {
                var purchaseOrder = SelectOrders(purchaseOrders, detailData.OrderNum, detailData.vbelns);
                //var purchaseOrder = purchaseOrders.FirstOrDefault(t => t["fbillno"]?.ToString() == detailData.OrderNum || Convert.ToString(t["fhqderno"]).EqualsIgnoreCase(detailData.vbelns));
                if (purchaseOrder == null)
                {
                    continue;
                }
                purchaseInOrder["fsupplierid"] = purchaseOrder["fsupplierid"]; //供应商
                purchaseInOrder["fpostaffid"] = purchaseOrder["fpostaffid"];//采购员
                purchaseInOrder["fpodeptid"] = purchaseOrder["fpodeptid"];//采购部门
                purchaseInOrder["fsupplieraddr"] = purchaseOrder["fsupplieraddr"];//供方地址
                purchaseInOrder["fsourcenumber"] = purchaseOrder["fbillno"];//源单编码
                purchaseInOrder["fsourceinterid"] = purchaseOrder["Id"];//源单id
                purchaseInOrder["fdescription"] = purchaseOrder["fdescription"];//备注
                purchaseInOrder["frenewalflag"] = purchaseOrder["frenewalflag"]; //焕新订单标记
                purchaseInOrder["fpiecesendtag"] = purchaseOrder["fpiecesendtag"]; //一件代发标记

                var purchaseOrderEntitys = purchaseOrder["fentity"] as DynamicObjectCollection;

                var purchaseOrderEntity = purchaseOrderEntitys.FirstOrDefault(t => t["fseq_e"]?.ToString() == detailData.OrderLineNum);
                if (purchaseOrderEntity != null)
                {
                    var purchaseOrderId = purchaseOrder["Id"]?.ToString();
                    var purchaseOrderEntryId = purchaseOrderEntity["Id"]?.ToString();
                    if (purchaseInOrderEntitys.Any(t => t["fpoorderinterid"]?.ToString() == purchaseOrderId && t["fpoorderentryid"]?.ToString() == purchaseOrderEntryId))
                    {
                        continue;
                    }

                    var purchaseInOrderEntity = purchaseInOrderEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                    var materialid = purchaseOrderEntity["fmaterialid"]?.ToString();
                    var nowqty = Convert.ToDecimal(detailData.ShipQty);
                    var nowamount = Convert.ToDecimal(purchaseOrderEntity["fdealprice"]) * nowqty;
                    var amount = Convert.ToDecimal(purchaseOrderEntity["fprice"]) * nowqty;

                    var sotckunitid = Convert.ToString((purchaseOrderEntity["fmaterialid_ref"] as DynamicObject)?["fstockunitid"]);
                    var unitid = purchaseOrderEntity["funitid"]?.ToString();
                    var qty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, nowqty, sotckunitid);
                    var factqty = Convert.ToDecimal(detailData.ShipQty);
                    var basefactqty = UnitConvertToBaseById(ctx, allMaterials, materialid, sotckunitid, factqty, unitid);
                    var purchaseunitid = purchaseOrderEntity["fbizunitid"]?.ToString();
                    var basepurchaseqty = Convert.ToDecimal(purchaseOrderEntity["fqty"]);
                    var purchaseqty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, basepurchaseqty, purchaseunitid);

                    purchaseInOrderEntity["fmaterialid"] = purchaseOrderEntity["fmaterialid"];//商品
                    purchaseInOrderEntity["fcustomdesc"] = purchaseOrderEntity["fcustomdes_e"];//定制说明
                    purchaseInOrderEntity["fattrinfo"] = purchaseOrderEntity["fattrinfo"]; //辅助属性
                    purchaseInOrderEntity["fattrinfo_e"] = purchaseOrderEntity["fattrinfo_e"]; //辅助属性
                    purchaseInOrderEntity["funitid"] = unitid;//基本单位
                    purchaseInOrderEntity["fbizunitid"] = purchaseunitid;//采购单位
                    purchaseInOrderEntity["fstockunitid"] = sotckunitid; //库存单位
                    purchaseInOrderEntity["fplanqty"] = nowqty;//基本单位应收数量
                    purchaseInOrderEntity["fbizplanqty"] = qty;//应收数量
                    purchaseInOrderEntity["fqty"] = basefactqty;//基本单位实收数量
                    purchaseInOrderEntity["fbizqty"] = detailData.ShipQty;//实收数量
                    purchaseInOrderEntity["forderqty"] = basepurchaseqty;//基本单位采购订单数量
                    purchaseInOrderEntity["fbizorderqty"] = purchaseqty;//采购订单数量
                    purchaseInOrderEntity["fstockqty"] = qty;//库存单位默认值
                    purchaseInOrderEntity["fprice"] = purchaseOrderEntity["fdealprice"];//成交单价
                    purchaseInOrderEntity["famount"] = nowamount;//成交金额
                    purchaseInOrderEntity["fpoprice"] = purchaseOrderEntity["fprice"];//采购单价
                    purchaseInOrderEntity["fpoamount"] = amount;//金额
                    purchaseInOrderEntity["fmtono"] = purchaseOrderEntity["fmtono"];//物流跟踪号
                    //purchaseInOrderEntity["fstockstatus"] = "311858936800219137";//库存状态：从下推的数据取
                    purchaseInOrderEntity["fownertype"] = purchaseOrderEntity["fownertype"];//货主类型
                    purchaseInOrderEntity["fownerid"] = purchaseOrderEntity["fownerid"];//货主
                    purchaseInOrderEntity["fentrynote"] = purchaseOrderEntity["fnote"];//备注
                    purchaseInOrderEntity["fsourceformid"] = "ydj_purchaseorder";//来源单类型
                    purchaseInOrderEntity["fsourcebillno"] = purchaseOrder["fbillno"];//来源单编号
                    purchaseInOrderEntity["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单分录内码
                    purchaseInOrderEntity["fpoorderno"] = purchaseOrder["fbillno"];//采购订单编号
                    purchaseInOrderEntity["fsourceinterid"] = purchaseOrder["Id"];//采购订单内码
                    purchaseInOrderEntity["fpoorderinterid"] = purchaseOrder["Id"];//采购订单内码
                    purchaseInOrderEntity["fpoorderentryid"] = purchaseOrderEntity["Id"];//采购订单分录内码
                    purchaseInOrderEntity["fmtrlimage"] = purchaseOrderEntity["fmtrlimage"];//图片
                    purchaseInOrderEntity["fsoorderno"] = purchaseOrderEntity["fsoorderno"];//销售合同编号
                    purchaseInOrderEntity["fsoorderinterid"] = purchaseOrderEntity["fsoorderinterid"];//销售合同内码
                    purchaseInOrderEntity["fsoorderentryid"] = purchaseOrderEntity["fsoorderentryid"];//销售合同分录内码
                    purchaseInOrderEntity["fcustomer"] = purchaseOrderEntity["fcustomer"];//客户
                    purchaseInOrderEntity["fhqderno"] = purchaseOrder["fhqderno"];//总部合同号
                    purchaseInOrderEntity["fresultbrandid"] = purchaseOrderEntity["fresultbrandid"];//业绩品牌
                    purchaseInOrderEntity["fentrystaffid"] = purchaseOrderEntity["fentrystaffid"];//销售员
                    purchaseInOrderEntity["fsupplierorderno"] = purchaseOrder["fsupplierorderno"];//供货方订单号

                    var totalVolume = Convert.ToDecimal(detailData.volum);
                    var unitVolume = totalVolume / factqty;//单位体积=总体积÷实收数量
                    purchaseInOrderEntity["fvolumeunit"] = detailData.voleh;//体积单位
                    purchaseInOrderEntity["ftotalvolume"] = totalVolume;//总体积
                    purchaseInOrderEntity["fsinglevolume"] = unitVolume;//单位体积

                    var factoryid = "";
                    var factory = allFactorys.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == detailData.DeliveryFactory);
                    if (factory == null)
                    {
                        factory = allFactorys.FirstOrDefault(f => Convert.ToString(f["fname"]) == detailData.DeliveryFactory);
                    }
                    if (factory != null) factoryid = Convert.ToString(factory["Id"]);
                    purchaseInOrderEntity["fdeliveryfactoryid"] = factoryid;
                    purchaseInOrderEntitys.Add(purchaseInOrderEntity);

                    //选配套件时
                    if (IsSuiteFlag(ctx, allMaterials, materialid))
                    {
                        var fsuitcombnumber = Convert.ToString(purchaseOrderEntity["fsuitcombnumber"]);
                        if (!string.IsNullOrWhiteSpace(fsuitcombnumber))
                        {
                            CreateChildProductDetail(ctx, purchaseInOrderEntitys, purchaseOrder, fsuitcombnumber, materialid, allMaterials, purchaseInOrderEntity);
                        }
                    }
                }
            }
            var geteway = ctx.Container.GetService<IHttpServiceInvoker>();
            purchaseInOrders.Add(purchaseInOrder);
            if (purchaseInOrderEntitys != null && purchaseInOrderEntitys.Count() > 0)
            {
                //ctx.SaveBizData(htmlForm.Id, purchaseInOrder);
                //改成走插件的形式不然不会反写采购订单 总部已发货数
                geteway.InvokeBillOperation(ctx, htmlForm.Id, new[] { purchaseInOrder }, "draft", new Dictionary<string, object> { { "IgnoreCheckPermssion", "true" } });
            }

            return purchaseInOrders;
        }

        /// <summary>
        /// 直营创建采购入库单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="purchaseOrders"></param>
        /// <param name="allMaterials"></param>
        /// <param name="dto"></param>
        /// <param name="allFactorys"></param>
        /// <param name="allPurchaseInOrders"></param>
        /// <param name="ReceiveNum"></param>
        /// <returns></returns>
        private static List<DynamicObject> CreatePurchaseInOrderByZY(
            UserContext ctx, HtmlForm htmlForm, List<DynamicObject> purchaseOrders, List<DynamicObject> allMaterials,
            DeliveryOrderDTO dto, List<DynamicObject> allFactorys, List<DynamicObject> allPurchaseInOrders, string ReceiveNum)
        {
            var purchaseInOrders = new List<DynamicObject>();
            var item = dto.Head.FirstOrDefault(f => f.ReceiveNum == ReceiveNum);
            var companyId = ctx.Company;
            var fdeliverybillno = item.DetailDatas.Select(x => x.TransReceiveNum).FirstOrDefault();
            var billtypecollection = GetBillType(ctx, "stk_postockin");
            var billtype = billtypecollection.FirstOrDefault(f => f.fname.ToString() == "标准采购入库" || (!f.fprimitivename.IsNullOrEmptyOrWhiteSpace() && f.fprimitivename.ToString() == "标准采购入库"));
            var defaultStore = ctx.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "ZFCK" });

            var purchaseInOrder = allPurchaseInOrders.FirstOrDefault(f => Convert.ToString(f["fbillno"]).EqualsIgnoreCase(item.ReceiveNum));
            if (purchaseInOrder == null)
            {
                purchaseInOrder = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                purchaseInOrder["fbillno"] = item.ReceiveNum;
                purchaseInOrder["fbilltype"] = billtype == null ? "poinstock_billtype_01" : billtype.fid;//"标准采购入库";//单据类型
                purchaseInOrder["fdeliverybillno"] = fdeliverybillno;//物流单号
                purchaseInOrder["fdescription"] = item.Comments;//备注
                purchaseInOrder["fdate"] = DateTime.Now;//入库日期
                purchaseInOrder["fsourcetype"] = "ydj_order";//来源单据
                purchaseInOrder["fbizruleid"] = "ydj_order2stk_postockin";//规则
                purchaseInOrder["fsenddate"] = item.ShipDate;//工厂发货日期
            }

            purchaseInOrder["fmainorgid"] = companyId; //使用组织ID

            var purchaseInOrderEntitys = purchaseInOrder["fentity"] as DynamicObjectCollection;
            //重复下发数据时，先清掉原来的明细，重新写入数据
            purchaseInOrderEntitys.Clear();
            foreach (var detailData in item.DetailDatas)
            {
                var salOrder = SelectOrders(purchaseOrders, detailData.OrderNum, detailData.vbelns);

                if (salOrder == null)
                {
                    continue;
                }
                purchaseInOrder["fsupplierid"] = ""; //供应商
                purchaseInOrder["fpostaffid"] = salOrder["fstaffid"];//采购员
                purchaseInOrder["fpodeptid"] = salOrder["fdeptid"];//采购部门
                purchaseInOrder["fstockstaffid"] = salOrder["fstaffid"];//收货人（默认为销售合同的销售员
                purchaseInOrder["fstockdeptid"] = salOrder["fdeptid"];//收货部门（默认销售合同的销售部门
                purchaseInOrder["fsupplieraddr"] = "";//供方地址
                purchaseInOrder["fsourcenumber"] = salOrder["fbillno"];//源单编码
                purchaseInOrder["fsourceinterid"] = salOrder["Id"];//源单id
                purchaseInOrder["fdescription"] = salOrder["fdescription"];//备注
                purchaseInOrder["frenewalflag"] = salOrder["frenewalflag"]; //焕新订单标记
                purchaseInOrder["fpiecesendtag"] = salOrder["fpiecesendtag"]; //一件代发标记
                purchaseInOrder["fmanagemodel"] = salOrder["fmanagemodel"]; //经营模式

                var salOrderEntitys = salOrder["fentry"] as DynamicObjectCollection;

                //var salOrderEntity = salOrderEntitys.FirstOrDefault(t => t["fentryid"]?.ToString() == detailData.id);
                var salOrderEntity = salOrderEntitys.FirstOrDefault(t => t["fseq"]?.ToString() == detailData.OrderLineNum);
                if (salOrderEntity != null)
                {
                    var purchaseOrderId = salOrder["Id"]?.ToString();
                    var purchaseOrderEntryId = salOrderEntity["Id"]?.ToString();
                    if (purchaseInOrderEntitys.Any(t => t["fsourceinterid"]?.ToString() == purchaseOrderId && t["fsourceentryid"]?.ToString() == purchaseOrderEntryId))
                    {
                        continue;
                    }

                    var purchaseInOrderEntity = purchaseInOrderEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                    var materialid = salOrderEntity["fproductid"]?.ToString();
                    var nowqty = Convert.ToDecimal(detailData.ShipQty);
                    var nowamount = Convert.ToDecimal(salOrderEntity["fdealprice"]) * nowqty;
                    var amount = Convert.ToDecimal(salOrderEntity["fprice"]) * nowqty;

                    var sotckunitid = Convert.ToString((salOrderEntity["fproductid_ref"] as DynamicObject)?["fstockunitid"]);
                    var unitid = salOrderEntity["funitid"]?.ToString();
                    var qty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, nowqty, sotckunitid);
                    var factqty = Convert.ToDecimal(detailData.ShipQty);
                    var basefactqty = UnitConvertToBaseById(ctx, allMaterials, materialid, sotckunitid, factqty, unitid);
                    var purchaseunitid = salOrderEntity["fbizunitid"]?.ToString();
                    var basepurchaseqty = Convert.ToDecimal(salOrderEntity["fqty"]);
                    var purchaseqty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, basepurchaseqty, purchaseunitid);

                    purchaseInOrderEntity["fmaterialid"] = salOrderEntity["fproductid"];//商品
                    purchaseInOrderEntity["fcustomdesc"] = salOrderEntity["fcustomdes_e"];//定制说明
                    purchaseInOrderEntity["fattrinfo"] = salOrderEntity["fattrinfo"]; //辅助属性
                    purchaseInOrderEntity["fattrinfo_e"] = salOrderEntity["fattrinfo_e"]; //辅助属性
                    purchaseInOrderEntity["funitid"] = unitid;//基本单位
                    purchaseInOrderEntity["fbizunitid"] = purchaseunitid;//采购单位
                    purchaseInOrderEntity["fstockunitid"] = sotckunitid; //库存单位
                    purchaseInOrderEntity["fplanqty"] = nowqty;//基本单位应收数量
                    purchaseInOrderEntity["fbizplanqty"] = qty;//应收数量
                    purchaseInOrderEntity["fqty"] = basefactqty;//基本单位实收数量
                    purchaseInOrderEntity["fbizqty"] = detailData.ShipQty;//实收数量
                    purchaseInOrderEntity["forderqty"] = basepurchaseqty;//基本单位采购订单数量
                    purchaseInOrderEntity["fbizorderqty"] = purchaseqty;//采购订单数量
                    purchaseInOrderEntity["fstockqty"] = qty;//库存单位默认值
                    purchaseInOrderEntity["fprice"] = salOrderEntity["fdealprice"];//成交单价
                    purchaseInOrderEntity["famount"] = nowamount;//成交金额
                    purchaseInOrderEntity["fpoprice"] = salOrderEntity["fprice"];//采购单价
                    purchaseInOrderEntity["fpoamount"] = amount;//金额
                    purchaseInOrderEntity["fmtono"] = salOrderEntity["fmtono"];//物流跟踪号
                    //purchaseInOrderEntity["fstockstatus"] = "311858936800219137";//库存状态：从下推的数据取
                    purchaseInOrderEntity["fownertype"] = salOrderEntity["fownertype"];//货主类型
                    purchaseInOrderEntity["fownerid"] = salOrderEntity["fownerid"];//货主
                    purchaseInOrderEntity["fentrynote"] = salOrderEntity["fdescription"];//备注
                    purchaseInOrderEntity["fsourceformid"] = "ydj_order";//来源单类型
                    purchaseInOrderEntity["fsourcebillno"] = salOrder["fbillno"];//来源单编号
                    purchaseInOrderEntity["fsourceentryid"] = salOrderEntity["Id"];//来源单分录内码
                    purchaseInOrderEntity["fpoorderno"] = "";//销售合同编号
                    purchaseInOrderEntity["fsourceinterid"] = salOrder["Id"];//销售合同内码
                    purchaseInOrderEntity["fpoorderinterid"] = salOrder["Id"];//销售合同内码
                    purchaseInOrderEntity["fpoorderentryid"] = salOrderEntity["Id"];//销售合同分录内码
                    purchaseInOrderEntity["fmtrlimage"] = salOrderEntity["fmtrlimage"];//图片
                    purchaseInOrderEntity["fsoorderno"] = salOrder["fbillno"];//销售合同编号
                    purchaseInOrderEntity["fsoorderinterid"] = salOrder["id"];//销售合同内码
                    purchaseInOrderEntity["fsoorderentryid"] = salOrderEntity["Id"];//销售合同分录内码
                    purchaseInOrderEntity["fcustomer"] = salOrder["fcustomerid"];//客户
                    //purchaseInOrderEntity["fhqderno"] = salOrder["fhqderno"];//总部合同号
                    purchaseInOrderEntity["fresultbrandid"] = salOrderEntity["fresultbrandid"];//业绩品牌
                    purchaseInOrderEntity["fentrystaffid"] = salOrder["fstaffid"];//销售员
                    //purchaseInOrderEntity["fsupplierorderno"] = purchaseOrder["fsupplierorderno"];//供货方订单号
                    purchaseInOrderEntity["fstorehouseid"] = defaultStore?.FirstOrDefault()?["Id"] ?? "";//仓库ID，直营默认发往总部自营仓
                    purchaseInOrderEntity["fstockstatus"] = "311858936800219137";//
                    var totalVolume = Convert.ToDecimal(detailData.volum);
                    var unitVolume = totalVolume / factqty;//单位体积=总体积÷实收数量
                    purchaseInOrderEntity["fvolumeunit"] = detailData.voleh;//体积单位
                    purchaseInOrderEntity["ftotalvolume"] = totalVolume;//总体积
                    purchaseInOrderEntity["fsinglevolume"] = unitVolume;//单位体积

                    var factoryid = "";
                    var factory = allFactorys.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == detailData.DeliveryFactory);
                    if (factory == null)
                    {
                        factory = allFactorys.FirstOrDefault(f => Convert.ToString(f["fname"]) == detailData.DeliveryFactory);
                    }
                    if (factory != null) factoryid = Convert.ToString(factory["Id"]);
                    purchaseInOrderEntity["fdeliveryfactoryid"] = factoryid;
                    purchaseInOrderEntitys.Add(purchaseInOrderEntity);

                    //选配套件时
                    if (IsSuiteFlag(ctx, allMaterials, materialid))
                    {
                        var fsuitcombnumber = Convert.ToString(salOrderEntity["fsuitcombnumber"]);
                        if (!string.IsNullOrWhiteSpace(fsuitcombnumber))
                        {
                            CreateChildProductDetailByZY(ctx, purchaseInOrderEntitys, salOrder, fsuitcombnumber, materialid, allMaterials, purchaseInOrderEntity, defaultStore);
                        }
                    }
                }
            }
            var geteway = ctx.Container.GetService<IHttpServiceInvoker>();
            purchaseInOrders.Add(purchaseInOrder);
            if (purchaseInOrderEntitys != null && purchaseInOrderEntitys.Count() > 0)
            {
                Dictionary<string, object> keys = new Dictionary<string, object>();
                keys.Add("e3Auto", "true");
                keys.Add("IgnoreCheckPermssion", "true");
                //ctx.SaveBizData(htmlForm.Id, purchaseInOrder);
                //改成走插件的形式不然不会反写采购订单 总部已发货数
                var draftFlow = geteway.InvokeBillOperation(ctx, htmlForm.Id, new[] { purchaseInOrder }, "draft", keys);
                //计划任务做提交审核
                //if (ctx.IsDirectSale && draftFlow.IsSuccess)
                //{
                //    var saveResult = geteway.InvokeBillOperation(ctx, htmlForm.Id, new[] { purchaseInOrder }, "save", keys);
                //    if (saveResult.IsSuccess)
                //    {
                //        var submitResult = geteway.InvokeBillOperation(ctx, htmlForm.Id, new[] { purchaseInOrder }, "submitflow", keys);
                //        if (submitResult.IsSuccess)
                //        {
                //            var auditResult = geteway.InvokeBillOperation(ctx, htmlForm.Id, new[] { purchaseInOrder }, "auditflow", keys);
                //        }
                //    }
                //}
            }

            return purchaseInOrders;
        }

        /// <summary>
        /// 根据采购订单号或总部合同号获取采购订单
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="billno"></param>
        /// <param name="hth"></param>
        /// <returns></returns>
        private static DynamicObject SelectOrders(List<DynamicObject> orders, string billno, string hth)
        {
            var ret = orders.FirstOrDefault(f => f["fbillno"]?.ToString() == billno);
            if (ret == null)
            {
                ret = orders.FirstOrDefault(f => f["fhqderno"]?.ToString() == hth);
            }
            return ret;
        }

        /// <summary>
        /// 创建条码主档
        /// </summary>
        private static List<DynamicObject> CreateBarcodeMaster(UserContext ctx, HtmlForm htmlForm, List<DynamicObject> purchaseOrders, List<DynamicObject> allMaterials, List<DynamicObject> allbcmaster, DeliveryOrderDTO dto, string ReceiveNum)
        {
            var datas = new List<DynamicObject>();
            var savedbc = new List<string>();
            var data = dto.Head.FirstOrDefault(f => f.ReceiveNum == ReceiveNum);
            var companyId = ctx.Company;
            foreach (var item in data.DetailDatas)
            {
                var packageType = item.PackageType;
                var purchaseOrder = SelectOrders(purchaseOrders, item.OrderNum, item.vbelns);
                //var purchaseOrder = purchaseOrders.FirstOrDefault(t =>t["fbillno"]?.ToString() == item.OrderNum || Convert.ToString(t["fhqderno"]).EqualsIgnoreCase(item.vbelns));
                if (purchaseOrder == null)
                {
                    continue;
                }
                var purchaseOrderEntitys = purchaseOrder["fentity"] as DynamicObjectCollection;
                var purchaseOrderEntity = purchaseOrderEntitys.FirstOrDefault(t => t["fseq_e"]?.ToString() == item.OrderLineNum);
                if (purchaseOrderEntity != null)
                {
                    if (!packageType.HasValue) packageType = 0;

                    if (item.BarCodeInfos != null)
                    {
                        var plmaterialid = purchaseOrderEntity["fmaterialid"]?.ToString();
                        if (IsSuiteFlag(ctx, allMaterials, plmaterialid))
                        {
                            var fsuitcombnumber = Convert.ToString(purchaseOrderEntity["fsuitcombnumber"]);
                            //套件调整为套件头本身的商品也写入条码主档明细中
                            var posubentitys = purchaseOrderEntitys.Where(f => Convert.ToString(f["fsuitcombnumber"]).EqualsIgnoreCase(fsuitcombnumber)).ToList();
                            if (posubentitys == null || posubentitys.Count <= 0)
                            {
                                continue;
                            }

                            foreach (var barcode in item.BarCodeInfos)
                            {
                                var handbc = barcode.ParentCode;
                                if (savedbc.Contains(handbc)) continue;
                                var bcmaster = allbcmaster.FirstOrDefault(t => t["fnumber"]?.ToString() == handbc);
                                if (bcmaster != null) continue; //若条码主档中已存在此记录，则不继续生成
                                var barcodeMaster = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                                barcodeMaster["fnumber"] = handbc;//条码主档编码
                                barcodeMaster["fname"] = handbc;//条码主档名称
                                barcodeMaster["fpackcount"] = 1;//总包数
                                barcodeMaster["fpackindex"] = 1;//包序号
                                barcodeMaster["fbizstatus"] = "4"; //业务状态：1可用 ,4待入库                  
                                barcodeMaster["fsourcetype"] = "ydj_purchaseorder";//来源单据类型
                                barcodeMaster["fsourcenumber"] = purchaseOrder["fbillno"];//来源单据编号
                                barcodeMaster["fsourcelinenumber"] = item.OrderLineNum;//来源单据行号
                                barcodeMaster["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单行内码
                                barcodeMaster["fsourcebillid"] = purchaseOrder["Id"];//来源单内码
                                barcodeMaster["frecnum"] = ReceiveNum;
                                switch (packageType)
                                {
                                    case 0:
                                        barcodeMaster["fpackagtype"] = "1";//打包类型：标准
                                        break;
                                    case 1:
                                        barcodeMaster["fpackagtype"] = "2";//打包类型：1件多包
                                        break;
                                    case 2:
                                        barcodeMaster["fpackagtype"] = "3";//打包类型：1包多件
                                        break;
                                    default:
                                        barcodeMaster["fpackagtype"] = "1";//打包类型：标准
                                        break;
                                }
                                barcodeMaster["fmainsncode"] = handbc;
                                barcodeMaster["fmainsecuritycode"] = barcode.SecurityCode;
                                barcodeMaster["fdescription"] = "总部同步生成";
                                barcodeMaster["fmainorgid"] = ctx.Company;
                                barcodeMaster["fcreatedate"] = DateTime.Now;//创建时间
                                var barcodeMasterEntitys = barcodeMaster["fentity"] as DynamicObjectCollection;

                                int i = 0;
                                foreach (var posubentity in posubentitys)
                                {
                                    i++;
                                    var barcodeMasterEntity = barcodeMasterEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                                    var materialid = Convert.ToString(posubentity["fmaterialid"]);
                                    barcodeMasterEntity["fserialno"] = Convert.ToString(i);//序列号
                                    barcodeMasterEntity["fmaterialid"] = materialid;//商品编码
                                    barcodeMasterEntity["fattrinfo"] = posubentity["fattrinfo"]; //辅助属性
                                    barcodeMasterEntity["fattrinfo_e"] = posubentity["fattrinfo_e"]; //辅助属性
                                    barcodeMasterEntity["fcustomdesc"] = posubentity["fcustomdes_e"]; //定制说明
                                    barcodeMasterEntity["fmtono"] = posubentity["fmtono"];//物流跟踪号
                                    barcodeMasterEntity["fownertype"] = posubentity["fownertype"]; //货主类型
                                    barcodeMasterEntity["fownerid"] = posubentity["fownerid"];//货主
                                    barcodeMasterEntity["fsuitcombnumber"] = posubentity["fsuitcombnumber"];//套件组合号
                                    barcodeMasterEntity["fpartscombnumber"] = posubentity["fpartscombnumber"];//配件组合号
                                    barcodeMasterEntity["fsofacombnumber"] = posubentity["fsofacombnumber"];//沙发组合号

                                    decimal fstockqty = 1;
                                    if (!materialid.EqualsIgnoreCase(plmaterialid))
                                    {
                                        fstockqty = Convert.ToDecimal(posubentity["fsubqty"]); // 库存数量：取子件数量
                                    }
                                    var fstockunitid = Convert.ToString((posubentity["fmaterialid_ref"] as DynamicObject)?["fstockunitid"]);
                                    var funitid = Convert.ToString((posubentity["fmaterialid_ref"] as DynamicObject)?["funitid"]);
                                    var baseqty = UnitConvertToBaseById(ctx, allMaterials, materialid, fstockunitid, fstockqty, funitid);
                                    barcodeMasterEntity["fstockunitid"] = fstockunitid;//库存单位
                                    barcodeMasterEntity["fstockqty"] = fstockqty;//库存数量
                                    barcodeMasterEntity["funitid"] = funitid; //基本单位
                                    barcodeMasterEntity["fqty"] = baseqty;

                                    barcodeMasterEntitys.Add(barcodeMasterEntity);
                                }
                                savedbc.Add(handbc);
                                datas.Add(barcodeMaster);
                            }
                        }
                        else
                        {
                            if (packageType == 0 || packageType == 1)
                            {
                                foreach (var barcode in item.BarCodeInfos)
                                {
                                    var bc = barcode.ParentCode;
                                    if (packageType == 1)
                                    {
                                        bc = barcode.ChildCode;
                                    }
                                    if (savedbc.Contains(bc)) continue;
                                    var bcmaster = allbcmaster.FirstOrDefault(t => t["fnumber"]?.ToString() == bc);
                                    if (bcmaster != null) continue; //若条码主档中已存在此记录，则不继续生成

                                    CreateBarCodeMasterByStd(ctx, htmlForm, savedbc, item, datas, bc, packageType, allMaterials, Convert.ToString(purchaseOrder["Id"]), purchaseOrderEntity, Convert.ToString(purchaseOrder["fbillno"]), ReceiveNum, bc, barcode.SecurityCode);
                                }
                            }
                            else if (packageType == 2)
                            {
                                var groups = item.BarCodeInfos.GroupBy(t => t.ParentCode);
                                foreach (var group in groups)
                                {
                                    var bc = group.Key;
                                    if (savedbc.Contains(bc)) continue;
                                    var bcmaster = allbcmaster.FirstOrDefault(t => t["fnumber"]?.ToString() == bc);
                                    if (bcmaster != null) continue; //若条码主档中已存在此记录，则不继续生成
                                    var barcodeMaster = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;

                                    barcodeMaster["fnumber"] = bc;//条码主档编码
                                    barcodeMaster["fname"] = bc;//条码主档名称
                                    barcodeMaster["fpackcount"] = 1;//总包数
                                    barcodeMaster["fpackindex"] = 1;//包序号
                                    barcodeMaster["fbizstatus"] = "4"; //业务状态：1可用 ,4待入库                  
                                    barcodeMaster["fsourcetype"] = "ydj_purchaseorder";//来源单据类型
                                    barcodeMaster["fsourcenumber"] = purchaseOrder["fbillno"];//来源单据编号
                                    barcodeMaster["fsourcelinenumber"] = item.OrderLineNum;//来源单据行号
                                    barcodeMaster["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单行内码
                                    barcodeMaster["fsourcebillid"] = purchaseOrder["Id"];//来源单内码
                                    switch (packageType)
                                    {
                                        case 0:
                                            barcodeMaster["fpackagtype"] = "1";//打包类型：标准
                                            break;
                                        case 1:
                                            barcodeMaster["fpackagtype"] = "2";//打包类型：1件多包
                                            break;
                                        case 2:
                                            barcodeMaster["fpackagtype"] = "3";//打包类型：1包多件
                                            break;
                                        default:
                                            barcodeMaster["fpackagtype"] = "1";//打包类型：标准
                                            break;
                                    }
                                    //barcodeMaster["fpackagtype"] = packageType == 0 ? "3" : Convert.ToString(packageType);//打包类型
                                    barcodeMaster["fmainsncode"] = bc;
                                    barcodeMaster["fmainsecuritycode"] = group.First().SecurityCode;
                                    barcodeMaster["fdescription"] = "总部同步生成";
                                    barcodeMaster["fmainorgid"] = companyId;
                                    barcodeMaster["frecnum"] = ReceiveNum;
                                    barcodeMaster["fcreatedate"] = DateTime.Now;//创建时间
                                    var barcodeMasterEntitys = barcodeMaster["fentity"] as DynamicObjectCollection;
                                    var existsubbcList = new List<string>();
                                    int i = 0;
                                    foreach (var subbcinfo in group)
                                    {
                                        i++;
                                        //var subbc = subbcinfo.ChildCode;
                                        var subbc = subbcinfo.SecurityCode;
                                        if (string.IsNullOrWhiteSpace(subbc)) continue;
                                        if (existsubbcList.Contains(subbc)) continue;
                                        var barcodeMasterEntity = barcodeMasterEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                        var materialid = Convert.ToString(purchaseOrderEntity["fmaterialid"]);
                                        barcodeMasterEntity["fbarcode"] = subbc;
                                        barcodeMasterEntity["fserialno"] = Convert.ToString(i);//序列号
                                        barcodeMasterEntity["fmaterialid"] = materialid;//商品编码
                                        barcodeMasterEntity["fattrinfo"] = purchaseOrderEntity["fattrinfo"]; //辅助属性
                                        barcodeMasterEntity["fattrinfo_e"] = purchaseOrderEntity["fattrinfo_e"]; //辅助属性
                                        barcodeMasterEntity["fcustomdesc"] = purchaseOrderEntity["fcustomdes_e"]; //定制说明
                                        barcodeMasterEntity["fmtono"] = purchaseOrderEntity["fmtono"];//物流跟踪号
                                        barcodeMasterEntity["fownertype"] = purchaseOrderEntity["fownertype"]; //货主类型
                                        barcodeMasterEntity["fownerid"] = purchaseOrderEntity["fownerid"];//货主
                                        barcodeMasterEntity["fsuitcombnumber"] = purchaseOrderEntity["fsuitcombnumber"];//套件组合号
                                        barcodeMasterEntity["fpartscombnumber"] = purchaseOrderEntity["fpartscombnumber"];//配件组合号
                                        barcodeMasterEntity["fsofacombnumber"] = purchaseOrderEntity["fsofacombnumber"];//沙发组合号

                                        barcodeMasterEntity["fmainsncode"] = subbcinfo.ChildCode;
                                        barcodeMasterEntity["fmainsecuritycode"] = subbcinfo.SecurityCode;

                                        var fstockunitid = Convert.ToString((purchaseOrderEntity["fmaterialid_ref"] as DynamicObject)?["fstockunitid"]);
                                        var fstockqty = 1; // 库存数量：合包时子件的数量为1
                                        var funitid = Convert.ToString((purchaseOrderEntity["fmaterialid_ref"] as DynamicObject)?["funitid"]);
                                        var baseqty = UnitConvertToBaseById(ctx, allMaterials, materialid, fstockunitid, fstockqty, funitid);
                                        barcodeMasterEntity["fstockunitid"] = fstockunitid;//库存单位
                                        barcodeMasterEntity["fstockqty"] = fstockqty;//库存数量
                                        barcodeMasterEntity["funitid"] = funitid; //基本单位
                                        barcodeMasterEntity["fqty"] = baseqty;

                                        existsubbcList.Add(subbc);
                                        barcodeMasterEntitys.Add(barcodeMasterEntity);
                                    }
                                    savedbc.Add(bc);
                                    datas.Add(barcodeMaster);
                                    foreach (var subbcinfo in group)
                                    {

                                        //var subbc = subbcinfo.ChildCode;
                                        var subbc = subbcinfo.SecurityCode;
                                        if (string.IsNullOrWhiteSpace(subbc)) continue;
                                        //合包子条码也产生条码主档数据
                                        if (savedbc.Contains(subbc)) continue;
                                        var subbcmaster = allbcmaster.FirstOrDefault(t => t["fnumber"]?.ToString() == subbc);
                                        if (subbcmaster != null) continue; //若条码主档中已存在此记录，则不继续生成

                                        CreateBarCodeMasterByStd(ctx, htmlForm, savedbc, item, datas, subbc, packageType, allMaterials, Convert.ToString(purchaseOrder["Id"]), purchaseOrderEntity, Convert.ToString(purchaseOrder["fbillno"]), ReceiveNum, subbcinfo.ChildCode, subbcinfo.SecurityCode);
                                    }
                                }
                            }
                        }

                    }
                }
            }
            //var geteway = ctx.Container.GetService<IHttpServiceInvoker>();
            if (datas.Any())
                ctx.SaveBizData(htmlForm.Id, datas);
            //geteway.InvokeBillOperation(ctx, htmlForm.Id, datas, "save", new Dictionary<string, object> { { "IgnoreCheckPermssion", "true" } });
            return datas;
        }

        /// <summary>
        /// 创建条码扫描记录
        /// </summary>
        private static List<DynamicObject> CreateScanResult(UserContext ctx, HtmlForm htmlForm, List<DynamicObject> purchaseOrders, List<DynamicObject> barcodeMasters, List<DynamicObject> scanResults, DeliveryOrderDTO dto, string ReceiveNum)
        {
            var scanRecords = new List<DynamicObject>();
            var data = dto.Head.FirstOrDefault(f => f.ReceiveNum == ReceiveNum);
            var companyId = ctx.Company;
            foreach (var item in data.DetailDatas)
            {
                var packageType = item.PackageType;
                if (!packageType.HasValue) packageType = 0;
                var purchaseOrder = SelectOrders(purchaseOrders, item.OrderNum, item.vbelns);
                //var purchaseOrder = purchaseOrders.FirstOrDefault(t => t["fbillno"]?.ToString() == item.OrderNum || Convert.ToString(t["fhqderno"]).EqualsIgnoreCase(item.vbelns));
                if (purchaseOrder == null)
                {
                    continue;
                }
                var purchaseOrderEntitys = purchaseOrder["fentity"] as DynamicObjectCollection;
                var purchaseOrderEntity = purchaseOrderEntitys.FirstOrDefault(t => t["fseq_e"]?.ToString() == item.OrderLineNum);
                if (purchaseOrderEntity != null)
                {
                    if (item.BarCodeInfos != null)
                    {
                        if (packageType == 2)
                        {
                            var groups = item.BarCodeInfos.GroupBy(t => t.ParentCode);
                            foreach (var group in groups)
                            {
                                var barcode = group.Key;
                                if (string.IsNullOrWhiteSpace(barcode)) continue;
                                var barcodeMaster = barcodeMasters.FirstOrDefault(t => t["fnumber"]?.ToString() == barcode);
                                if (barcodeMaster == null) continue; //若条码主档未找到，则不继续生成
                                var bcresult = scanResults.FirstOrDefault(t => t["fbarcode"]?.ToString() == Convert.ToString(barcodeMaster["Id"]));
                                if (bcresult != null) continue; //若条码扫描记录中已存在此条码码文，且来源单据为采购订单，则不继续生成
                                var scanRecord = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                                scanRecord["fbarcode"] = barcodeMaster["Id"];//条码
                                scanRecord["fbarcodetext"] = barcode;//条码码文
                                scanRecord["fsourceformid"] = "ydj_purchaseorder";//来源单据:采购订单
                                scanRecord["fsourcebillno"] = purchaseOrder["fbillno"];//来源单据编号
                                scanRecord["fscansceneid"] = "2";//'1':'收货','2':'发货','3':'调拨','4':'盘点','5':'装箱','6':'拆箱'                          
                                scanRecord["foperatorid"] = ctx.UserId;//操作员
                                scanRecord["fopdatetime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");//DateTime.Now;//操作日期
                                scanRecord["fdescription"] = "总部发货给经销商, 待经销商收货";//备注
                                scanRecord["fscanqty"] = 1;//扫描数量
                                scanRecord["fmainorgid"] = companyId;

                                //扫描记录--商品明细
                                var objDetail = scanRecord["fentity"] as DynamicObjectCollection;

                                var barcodeMasterEntitys = barcodeMaster["fentity"] as DynamicObjectCollection;

                                foreach (var entitysItem in barcodeMasterEntitys)
                                {
                                    var detailItem = objDetail.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                    detailItem["fmaterialid"] = entitysItem["fmaterialid"];
                                    detailItem["fattrinfo"] = entitysItem["fattrinfo"];
                                    detailItem["fattrinfo_e"] = entitysItem["fattrinfo_e"];
                                    detailItem["fcustomdesc"] = entitysItem["fcustomdesc"];
                                    objDetail.Add(detailItem);
                                }

                                scanRecords.Add(scanRecord);

                                var existsubbcList = new List<string>();
                                foreach (var barcodeinfo in group)
                                {
                                    var subbarcode = barcodeinfo.ChildCode;
                                    if (string.IsNullOrWhiteSpace(subbarcode)) continue;
                                    if (existsubbcList.Contains(subbarcode)) continue;
                                    var subbarcodeMaster = barcodeMasters.FirstOrDefault(t => t["fnumber"]?.ToString() == subbarcode);
                                    if (subbarcodeMaster == null) continue;
                                    var subbcresult = scanResults.FirstOrDefault(t => t["fbarcode"]?.ToString() == Convert.ToString(subbarcodeMaster["Id"]));
                                    if (bcresult != null) continue; //若条码扫描记录中已存在此条码码文，且来源单据为采购入库单，则不继续生成

                                    var subscanRecord = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                                    subscanRecord["fbarcode"] = subbarcodeMaster["Id"];//条码
                                    subscanRecord["fbarcodetext"] = subbarcode;//条码码文
                                    subscanRecord["fsourceformid"] = "ydj_purchaseorder";//来源单据:采购订单
                                    subscanRecord["fsourcebillno"] = purchaseOrder["fbillno"];//来源单据编号
                                    subscanRecord["fscansceneid"] = "2";//'1':'收货','2':'发货','3':'调拨','4':'盘点','5':'装箱','6':'拆箱'                          
                                    subscanRecord["foperatorid"] = ctx.UserId;//操作员
                                    subscanRecord["fopdatetime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");//DateTime.Now;//操作日期
                                    subscanRecord["fdescription"] = "总部发货给经销商, 待经销商收货";//备注
                                    subscanRecord["fscanqty"] = 1;//扫描数量
                                    subscanRecord["fmainorgid"] = companyId;

                                    //扫描记录--商品明细
                                    var subObjDetail = subscanRecord["fentity"] as DynamicObjectCollection;

                                    var subBarcodeMasterEntitys = subbarcodeMaster["fentity"] as DynamicObjectCollection;

                                    foreach (var subentitysItem in subBarcodeMasterEntitys)
                                    {
                                        var subDetailItem = subObjDetail.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                        subDetailItem["fmaterialid"] = subentitysItem["fmaterialid"];
                                        subDetailItem["fattrinfo"] = subentitysItem["fattrinfo"];
                                        subDetailItem["fattrinfo_e"] = subentitysItem["fattrinfo_e"];
                                        subDetailItem["fcustomdesc"] = subentitysItem["fcustomdesc"];
                                        objDetail.Add(subDetailItem);
                                    }

                                    scanRecords.Add(subscanRecord);
                                    existsubbcList.Add(subbarcode);
                                }
                            }
                        }
                        else
                        {
                            var existsubbcList = new List<string>();
                            foreach (var barcodeinfo in item.BarCodeInfos)
                            {
                                var barcode = packageType == 1 ? barcodeinfo.ChildCode : barcodeinfo.ParentCode;
                                if (string.IsNullOrWhiteSpace(barcode)) continue;
                                if (existsubbcList.Contains(barcode)) continue;
                                var barcodeMaster = barcodeMasters.FirstOrDefault(t => t["fnumber"]?.ToString() == barcode);
                                if (barcodeMaster == null) continue;
                                var bcresult = scanResults.FirstOrDefault(t => t["fbarcode"]?.ToString() == Convert.ToString(barcodeMaster["Id"]));
                                if (bcresult != null) continue; //若条码扫描记录中已存在此条码码文，且来源单据为采购入库单，则不继续生成

                                var scanRecord = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                                scanRecord["fbarcode"] = barcodeMaster["Id"];//条码
                                scanRecord["fbarcodetext"] = barcode;//条码码文
                                scanRecord["fsourceformid"] = "ydj_purchaseorder";//来源单据:采购入库单
                                scanRecord["fsourcebillno"] = purchaseOrder["fbillno"];//来源单据编号
                                scanRecord["fscansceneid"] = "2";//'1':'收货','2':'发货','3':'调拨','4':'盘点','5':'装箱','6':'拆箱'                          
                                scanRecord["foperatorid"] = ctx.UserId;//操作员
                                scanRecord["fopdatetime"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");//DateTime.Now;//操作日期
                                scanRecord["fdescription"] = "总部发货给经销商, 待经销商收货";//备注
                                scanRecord["fscanqty"] = 1;//扫描数量
                                scanRecord["fmainorgid"] = companyId;
                                scanRecords.Add(scanRecord);
                                existsubbcList.Add(barcode);
                            }
                        }
                    }
                }
            }
            if (scanRecords.Any())
            {
                //ctx.SaveBizData(htmlForm.Id, scanRecords);//容易产生重复编号，弃用,关联禅道BUG：34890
                var geteway = ctx.Container.GetService<IHttpServiceInvoker>();
                var result = geteway.InvokeBillOperation(ctx, htmlForm.Id, scanRecords, "Draft", new Dictionary<string, object>());
                //geteway.InvokeBillOperation(ctx, htmlForm.Id, scanRecords, "save", new Dictionary<string, object> { { "IgnoreCheckPermssion", "true" } });
            }
            return scanRecords;
        }

        /// <summary>
        /// 创建收货扫描任务
        /// </summary>
        private static List<DynamicObject> CreateReceiveScanTask(UserContext ctx, HtmlForm htmlForm, List<DynamicObject> purchaseOrders, List<DynamicObject> allScanTasks, List<DynamicObject> allMaterials, DeliveryOrderDTO dto, string ReceiveNum, List<DynamicObject> allfactorys, ref List<string> opMsgs)
        {
            var receiveScanTasks = new List<DynamicObject>();
            var companyId = ctx.Company;
            var data = dto.Head.FirstOrDefault(f => f.ReceiveNum == ReceiveNum);
            if (data == null)
            {
                return receiveScanTasks;
            }
            var receiveScanTask = allScanTasks.FirstOrDefault(f =>
                Convert.ToString(f["freceptionno"]).EqualsIgnoreCase(ReceiveNum));
            if (receiveScanTask == null)
            {
                receiveScanTask = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;
                receiveScanTask["ftask_type"] = "stk_postockin"; //扫描任务类型:采购入库
                receiveScanTask["ftaskstatus"] = "ftaskstatus_01";//扫描任务状态：待作业
                receiveScanTask["ftaskdate"] = DateTime.Now.ToString("yyyy-MM-dd");//业务日期
                receiveScanTask["freceptionno"] = ReceiveNum;//收货单号：交货单单号
                receiveScanTask["flogisticsno"] = data.DetailDatas.FirstOrDefault().TransReceiveNum;//物流单号：运输交接单号
                receiveScanTask["fexistednetherbill"] = "0";//已存在下游库存单据
                receiveScanTask["fdescription"] = "SAP交货单接口产生";//备注
                receiveScanTask["fmainorgid"] = companyId;
                receiveScanTask["fsenddate"] = data.ShipDate;//工厂发货日期
            }
            receiveScanTask["fisdistribute"] = "1";//总部下发生成

            var receiveScanTaskEntitys = receiveScanTask["ftaskentity"] as DynamicObjectCollection;
            //若是重复下发，先将原来的数据清掉，再重新写入数据
            receiveScanTaskEntitys.Clear();
            var isaddentity = false;
            foreach (var item in data.DetailDatas)
            {
                var purchaseOrderBillNo = item.OrderNum;
                var purchaseOrderLineNo = item.OrderLineNum;
                var purchaseInBillNo = item.ReceiveNum;
                var packageType = item.PackageType;
                if (!packageType.HasValue) packageType = 0;
                var purchaseOrder = SelectOrders(purchaseOrders, item.OrderNum, item.vbelns);
                if (purchaseOrder == null)
                {
                    continue;
                }

                var purchaseOrderEntitys = purchaseOrder["fentity"] as DynamicObjectCollection;

                //如果已存在同一采购订单和订单行号数据，则不在添加
                var eeadfdsaf = receiveScanTaskEntitys.FirstOrDefault(t =>
                    Convert.ToString(t["fsourcebillno"]).EqualsIgnoreCase(item.OrderNum)
                    && Convert.ToString(t["fsourceinterid"]).EqualsIgnoreCase(item.OrderLineNum));
                if (eeadfdsaf != null) continue;

                var receiveScanTaskEntity = receiveScanTaskEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                var purchaseOrderEntity = purchaseOrderEntitys.FirstOrDefault(t => t["fseq_e"]?.ToString() == item.OrderLineNum);
                if (purchaseOrderEntity != null)
                {
                    var materialid = purchaseOrderEntity["fmaterialid"]?.ToString();
                    receiveScanTaskEntity["fmaterialid"] = materialid;//商品
                    receiveScanTaskEntity["fcustomdesc"] = purchaseOrderEntity["fcustomdes_e"];//定制说明
                    receiveScanTaskEntity["fattrinfo"] = purchaseOrderEntity["fattrinfo"]; //辅助属性
                    receiveScanTaskEntity["fattrinfo_e"] = purchaseOrderEntity["fattrinfo_e"]; //辅助属性
                    decimal fqty = 0;
                    var fpackagingtype = "0";
                    decimal fwaitscanqty = Convert.ToDecimal(item.ShipQty);
                    switch (packageType)
                    {
                        case 0:
                            fpackagingtype = "1";
                            fqty = fwaitscanqty;
                            break;
                        case 1:
                            fqty = Convert.ToDecimal(item.Box);
                            fpackagingtype = "2";
                            fwaitscanqty = Math.Round(fwaitscanqty * fqty, 2);
                            break;
                        case 2:
                            fqty = Convert.ToDecimal(item.Set);
                            fpackagingtype = "3";
                            if (fqty == 0)
                            {
                                fwaitscanqty = 0;
                            }
                            else
                            {
                                fwaitscanqty = Math.Round(fwaitscanqty / fqty, 2);
                            }
                            break;
                    }
                    receiveScanTaskEntity["fpackagingqty"] = fqty; //包数/件数
                    receiveScanTaskEntity["fwaitworkqty"] = item.ShipQty;//待作业数：中台的数量
                    receiveScanTaskEntity["fworkedqty"] = 0;//已作业数
                    receiveScanTaskEntity["fwaitscanqty"] = fwaitscanqty;//待扫描包数
                    receiveScanTaskEntity["fscannedqty"] = 0;//已扫描包数
                    receiveScanTaskEntity["flotno"] = "";//批号
                    receiveScanTaskEntity["fpackagingtype"] = fpackagingtype;//打包类型
                    receiveScanTaskEntity["fmtono"] = purchaseOrderEntity["fmtono"];//物流跟踪号
                    receiveScanTaskEntity["fownertype"] = purchaseOrderEntity["fownertype"];//货主类型
                    receiveScanTaskEntity["fsuitegroupid"] = purchaseOrderEntity["fsuitcombnumber"];//套件组合号
                    receiveScanTaskEntity["fcategorygroupid"] = purchaseOrderEntity["fpartscombnumber"];//配件组合号
                    receiveScanTaskEntity["fsafagroupid"] = purchaseOrderEntity["fsofacombnumber"];//沙发组合号
                    receiveScanTaskEntity["fqty"] = purchaseOrderEntity["fqty"];//基本单位数量                   
                    receiveScanTaskEntity["fsourceformid"] = "ydj_purchaseorder";//来源单据：采购订单
                    receiveScanTaskEntity["fsourcebillno"] = purchaseOrder["fbillno"];//来源单据编号:采购订单编号
                    receiveScanTaskEntity["fsourceinterid"] = purchaseOrderEntity["fseq_e"];//来源单行号
                    receiveScanTaskEntity["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单行内码
                    receiveScanTaskEntity["fhqderno"] = item.vbelns;//总部合同号

                    receiveScanTaskEntity["fcustomerid"] = purchaseOrderEntity["fcustomer"];//客户
                    receiveScanTaskEntity["fsoorderno"] = purchaseOrderEntity["fsoorderno"];//销售合同编号

                    var totalVolume = Convert.ToDecimal(item.volum);
                    var unitVolume = totalVolume / item.ShipQty;//单位体积=总体积÷待作业数量
                    receiveScanTaskEntity["fvolumeunit"] = item.voleh;//体积单位
                    receiveScanTaskEntity["ftotalvolume"] = totalVolume;//总体积
                    receiveScanTaskEntity["fsinglevolume"] = unitVolume;//单位体积

                    var nowqty = Convert.ToDecimal(item.ShipQty);
                    var dealprice = Convert.ToDecimal(purchaseOrderEntity["fdealprice"]);
                    var nowamount = dealprice * nowqty;
                    receiveScanTaskEntity["fprice"] = dealprice;//成交单价
                    receiveScanTaskEntity["famount"] = nowamount;//成交金额
                    receiveScanTaskEntity["fsupplierorderno"] = purchaseOrder["fsupplierorderno"];//供货方订单号

                    receiveScanTaskEntity["fcurrworkqty"] = item.ShipQty;//本次作业数量=待作业数量（即：中台的数量）

                    var factoryid = "";
                    var factory = allfactorys.FirstOrDefault(f => Convert.ToString(f["fnumber"]) == item.DeliveryFactory);
                    if (factory == null)
                    {
                        factory = allfactorys.FirstOrDefault(f => Convert.ToString(f["fname"]) == item.DeliveryFactory);
                    }
                    if (factory != null) factoryid = Convert.ToString(factory["Id"]);
                    receiveScanTaskEntity["fdeliveryfactoryid"] = factoryid;
                    receiveScanTaskEntitys.Add(receiveScanTaskEntity);

                    //选配套件时
                    if (IsSuiteFlag(ctx, allMaterials, materialid))
                    {
                        var fsuitcombnumber = Convert.ToString(purchaseOrderEntity["fsuitcombnumber"]);
                        if (!string.IsNullOrWhiteSpace(fsuitcombnumber))
                        {
                            CreateChildScanTaskDetail(ctx, receiveScanTaskEntitys, purchaseOrder, fsuitcombnumber, materialid, allMaterials, packageType, item, factoryid);
                        }
                    }
                    isaddentity = true;
                }
            }
            var geteway = ctx.Container.GetService<IHttpServiceInvoker>();
            if (isaddentity)
            {
                // ctx.SaveBizData(htmlForm.Id, receiveScanTask);
                try
                {
                    geteway.InvokeBillOperation(ctx, htmlForm.Id, new[] { receiveScanTask }, "save", new Dictionary<string, object> { { "IgnoreCheckPermssion", "true" } });
                }
                catch (Exception ex)
                {
                    opMsgs.Add(ex.Message);
                    return receiveScanTasks;
                }
            }
            receiveScanTasks.Add(receiveScanTask);
            return receiveScanTasks;
        }

        /// <summary>
        /// 分割字符串（获取-和/中间的数字）
        /// </summary>
        private static int GetSplitStr(string str)
        {
            if (str.Contains("-") && str.Contains("/"))
            {
                var strarr = str.Split('-');
                var split = strarr[strarr.Length - 1].Split('/')[0];
                return Convert.ToInt32(split);
            }
            else
                return 1;
        }

        /// <summary>
        /// 单位换算(业务单位转换为基本单位)
        /// 1优先从物料中获取单位明细做换算
        /// 2若1中没有找到，则从单位中获取换算公式
        /// 3.若1和2中都没有，则直接返回传的数量
        /// </summary>
        /// <returns></returns>        
        private static decimal UnitConvertToBaseById(UserContext ctx, List<DynamicObject> allMaterial, string materialId, string unit, decimal qty, string toUnit)
        {
            decimal d = qty;
            if (string.IsNullOrWhiteSpace(unit) || string.IsNullOrWhiteSpace(toUnit))
            {
                return d;
            }

            if (unit == toUnit)
            {
                return d;
            }
            var material = allMaterial.FirstOrDefault(f => f["Id"]?.ToString() == materialId);
            if (material == null)
            {
                return d;
            }
            //先根据商品下的单位明细做换算，若没有则从单位中取
            var unitentitys = material["funitentry"] as DynamicObjectCollection;
            var unitentity = unitentitys.FirstOrDefault(f => f["funitid"]?.ToString() == unit);

            string fcvttype = "";
            decimal fcvtrate = 0;
            int fprecision = 0;
            if (unitentity != null)
            {
                fcvttype = unitentity["fcvttype"]?.ToString();
                fcvtrate = Convert.ToDecimal(unitentity["fcvtrate"]);
                fprecision = Convert.ToInt32(unitentity["fprecision"]);
            }
            else
            {
                //获取对应目标单位及基本单位的换算率
                var units = ctx.LoadBizDataById("ydj_unit", unit);
                if (units == null) return d;
                if (units["fbaseunitid"]?.ToString() != toUnit)
                {
                    return d;
                }
                fcvttype = units["froundtype"]?.ToString();
                fcvtrate = Convert.ToDecimal(units["fcvtrate"]);
                fprecision = Convert.ToInt16(units["fprecision"]);
            }
            switch (fcvttype)
            {
                case "1":
                    d = qty * fcvtrate;
                    break;
                case "2":
                    d = Math.Round(qty * fcvtrate, fprecision);
                    break;
                case "round":
                    d = Math.Round(qty * fcvtrate, fprecision);
                    break;
                case "ceil":
                    d = Math.Ceiling(qty * fcvtrate);
                    break;
                case "floor":
                    d = Math.Floor(qty * fcvtrate);
                    break;
                default:
                    d = Math.Round(qty * fcvtrate, fprecision);
                    break;
            }
            return d;
        }

        /// <summary>
        /// 单位换算(基本单位转换为业务单位)
        /// 1优先从物料中获取单位明细做换算
        /// 2若1中没有找到，则从单位中获取换算公式
        /// 3.若1和2中都没有，则直接返回传的数量
        /// </summary>
        /// <returns></returns>
        private static decimal BaseUnitConvertById(UserContext ctx, List<DynamicObject> allMaterial, string materialId, string unit, decimal qty, string toUnit)
        {
            decimal d = qty;
            if (string.IsNullOrWhiteSpace(unit) || string.IsNullOrWhiteSpace(toUnit))
            {
                return d;
            }

            if (unit == toUnit)
            {
                return d;
            }
            var material = allMaterial.FirstOrDefault(f => f["Id"]?.ToString() == materialId);
            if (material == null)
            {
                return d;
            }
            //先根据商品下的单位明细做换算，若没有则从单位中取
            var unitentitys = material["funitentry"] as DynamicObjectCollection;
            var unitentity = unitentitys.FirstOrDefault(f => f["funitid"]?.ToString() == unit);

            string fcvttype = "";
            decimal fcvtrate = 0;
            int fprecision = 0;
            if (unitentity != null)
            {
                fcvttype = unitentity["fcvttype"]?.ToString();
                fcvtrate = Convert.ToDecimal(unitentity["fcvtrate"]);
                fprecision = Convert.ToInt32(unitentity["fprecision"]);
            }
            else
            {
                //获取对应目标单位及基本单位的换算率
                var units = ctx.LoadBizDataById("ydj_unit", unit);
                if (units == null) return d;
                if (units["fbaseunitid"]?.ToString() != toUnit)
                {
                    return d;
                }
                fcvttype = units["froundtype"]?.ToString();
                fcvtrate = Convert.ToDecimal(units["fcvtrate"]);
                fprecision = Convert.ToInt16(units["fprecision"]);
            }
            switch (fcvttype)
            {
                case "1":
                    d = qty / fcvtrate;
                    break;
                case "2":
                    d = Math.Round(qty / fcvtrate, fprecision);
                    break;
                case "round":
                    d = Math.Round(qty / fcvtrate, fprecision);
                    break;
                case "ceil":
                    d = Math.Ceiling(qty / fcvtrate);
                    break;
                case "floor":
                    d = Math.Floor(qty / fcvtrate);
                    break;
                default:
                    d = Math.Round(qty / fcvtrate, fprecision);
                    break;
            }
            return d;
        }

        /// <summary>
        /// 获取单据类型
        /// </summary>
        private static List<BillTypeInfo> GetBillType(UserContext userCtx, string fbizobject)
        {
            var svc = userCtx.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(userCtx, fbizobject);
            return billTypeInfos;

            //string sql = $@"select t0.fid as id,t0.fnumber,t0.fname 
            //from T_BD_BILLTYPE t0 with(nolock)
            //LEFT OUTER JOIN t_bd_billtype pri with(nolock) ON ((pri.fprimitiveid = t0.fid AND (pri.fprimitiveid <> '')) AND pri.fmainorgid = '{userCtx.Company}') 
            //where ((((t0.fmainorgid = '0' OR t0.fmainorgid = '{userCtx.Company}') AND t0.fbizobject = '{fbizobject}') AND t0.fforbidstatus = 0) AND (pri.fid IS NULL)) ";
            //return userCtx.ExecuteDynamicObject(sql, null);
        }

        /// <summary>
        /// 判断商品是否勾选选配套件
        /// </summary>
        private static bool IsSuiteFlag(UserContext userCtx, List<DynamicObject> allMaterials, string materialId)
        {
            if (string.IsNullOrWhiteSpace(materialId)) return false;

            var material = allMaterials.FirstOrDefault(f => Convert.ToString(f["Id"]).EndsWithIgnoreCase(materialId));
            if (material == null) return false;

            if (Convert.ToBoolean(material["fsuiteflag"])) return true;

            return false;

        }

        /// <summary>
        /// 创建子件采购入库明细
        /// </summary>
        private static void CreateChildProductDetail(UserContext ctx, DynamicObjectCollection poinentitys, DynamicObject purchaseOrder, string fsuitcombnumber, string mainMaterialId, List<DynamicObject> allMaterials, DynamicObject poinentity)
        {
            if (purchaseOrder == null) return;
            var poentitys = purchaseOrder["fentity"] as DynamicObjectCollection;
            if (poentitys == null || poentitys.Count <= 0)
            {
                return;
            }
            var subentitys = poentitys.Where(f => Convert.ToString(f["fsuitcombnumber"]).EqualsIgnoreCase(fsuitcombnumber) && !Convert.ToString(f["fmaterialid"]).EqualsIgnoreCase(mainMaterialId)).ToList();
            if (subentitys == null || subentitys.Count <= 0)
            {
                return;
            }

            foreach (var purchaseOrderEntity in subentitys)
            {
                var purchaseInOrderEntity = poinentitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                //基本单位应收数量 = 套件基本单位应收数量*子件数量
                var materialid = purchaseOrderEntity["fmaterialid"]?.ToString();
                var subqty = Convert.ToDecimal(purchaseOrderEntity["fsubqty"]);//子件数量
                var nowqty = Convert.ToDecimal(poinentity["fplanqty"]) * subqty;//子件基本单位应收数量 = 套件基本单位应收数量*子件数量
                var nowamount = Convert.ToDecimal(purchaseOrderEntity["fdealprice"]) * nowqty;//金额
                var amount = Convert.ToDecimal(purchaseOrderEntity["fprice"]) * nowqty;//采购金额

                var sotckunitid = Convert.ToString((purchaseOrderEntity["fmaterialid_ref"] as DynamicObject)?["fstockunitid"]);//库存单位
                var unitid = purchaseOrderEntity["funitid"]?.ToString();//基本单位
                var qty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, nowqty, sotckunitid);//应收数量
                var factqty = Convert.ToDecimal(poinentity["fbizqty"]) * subqty; //子件实收数量 = 套件实收数量*子件数量
                var basefactqty = UnitConvertToBaseById(ctx, allMaterials, materialid, sotckunitid, factqty, unitid);//基本单位实收数量
                var purchaseunitid = purchaseOrderEntity["fbizunitid"]?.ToString();//采购单位
                var basepurchaseqty = Convert.ToDecimal(purchaseOrderEntity["fqty"]);//基本单位采购订单数量
                var purchaseqty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, basepurchaseqty, purchaseunitid);//采购订单数量

                purchaseInOrderEntity["fmaterialid"] = purchaseOrderEntity["fmaterialid"];//商品
                purchaseInOrderEntity["fcustomdesc"] = purchaseOrderEntity["fcustomdes_e"];//定制说明
                purchaseInOrderEntity["fattrinfo"] = purchaseOrderEntity["fattrinfo"]; //辅助属性
                purchaseInOrderEntity["fattrinfo_e"] = purchaseOrderEntity["fattrinfo_e"]; //辅助属性
                purchaseInOrderEntity["funitid"] = unitid;//基本单位
                purchaseInOrderEntity["fbizunitid"] = purchaseunitid;//采购单位
                purchaseInOrderEntity["fstockunitid"] = sotckunitid; //库存单位
                purchaseInOrderEntity["fplanqty"] = nowqty;//基本单位应收数量
                purchaseInOrderEntity["fbizplanqty"] = qty;//应收数量
                purchaseInOrderEntity["fqty"] = basefactqty;//基本单位实收数量
                purchaseInOrderEntity["fbizqty"] = factqty;//实收数量
                purchaseInOrderEntity["forderqty"] = basepurchaseqty;//基本单位采购订单数量
                purchaseInOrderEntity["fbizorderqty"] = purchaseqty;//采购订单数量
                purchaseInOrderEntity["fstockqty"] = qty;//库存单位默认值
                purchaseInOrderEntity["fprice"] = purchaseOrderEntity["fdealprice"];//成交单价
                purchaseInOrderEntity["famount"] = nowamount;//成交金额
                purchaseInOrderEntity["fpoprice"] = purchaseOrderEntity["fprice"];//采购单价
                purchaseInOrderEntity["fpoamount"] = amount;//金额
                purchaseInOrderEntity["fmtono"] = purchaseOrderEntity["fmtono"];//物流跟踪号
                purchaseInOrderEntity["fownertype"] = purchaseOrderEntity["fownertype"];//货主类型
                purchaseInOrderEntity["fownerid"] = purchaseOrderEntity["fownerid"];//货主
                purchaseInOrderEntity["fentrynote"] = purchaseOrderEntity["fnote"];//备注
                purchaseInOrderEntity["fsourceformid"] = "ydj_purchaseorder";//来源单类型
                purchaseInOrderEntity["fsourcebillno"] = purchaseOrder["fbillno"];//来源单编号
                purchaseInOrderEntity["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单分录内码
                purchaseInOrderEntity["fpoorderno"] = purchaseOrder["fbillno"];//采购订单编号
                purchaseInOrderEntity["fpoorderinterid"] = purchaseOrder["Id"];//采购订单内码
                purchaseInOrderEntity["fpoorderentryid"] = purchaseOrderEntity["Id"];//采购订单分录内码
                purchaseInOrderEntity["fmtrlimage"] = purchaseOrderEntity["fmtrlimage"];//图片
                purchaseInOrderEntity["fsoorderno"] = purchaseOrderEntity["fsoorderno"];//销售合同编号
                purchaseInOrderEntity["fsoorderinterid"] = purchaseOrderEntity["fsoorderinterid"];//销售合同内码
                purchaseInOrderEntity["fsoorderentryid"] = purchaseOrderEntity["fsoorderentryid"];//销售合同分录内码
                purchaseInOrderEntity["fcustomer"] = purchaseOrderEntity["fcustomer"];//客户
                purchaseInOrderEntity["fhqderno"] = purchaseOrder["fhqderno"];//总部合同号
                purchaseInOrderEntity["fresultbrandid"] = purchaseOrderEntity["fresultbrandid"];//业绩品牌
                poinentitys.Add(purchaseInOrderEntity);
            }
        }


        /// <summary>
        /// 直营创建子件采购入库明细
        /// </summary>
        private static void CreateChildProductDetailByZY(UserContext ctx, DynamicObjectCollection poinentitys, DynamicObject salOrder, string fsuitcombnumber, string mainMaterialId, List<DynamicObject> allMaterials, DynamicObject poinentity, List<DynamicObject> defaultStore)
        {
            if (salOrder == null) return;
            var salentitys = salOrder["fentry"] as DynamicObjectCollection;
            if (salentitys == null || salentitys.Count <= 0)
            {
                return;
            }
            var subentitys = salentitys.Where(f => Convert.ToString(f["fsuitcombnumber"]).EqualsIgnoreCase(fsuitcombnumber) && !Convert.ToString(f["fproductid"]).EqualsIgnoreCase(mainMaterialId)).ToList();
            if (subentitys == null || subentitys.Count <= 0)
            {
                return;
            }

            foreach (var salOrderEntity in subentitys)
            {
                var purchaseInOrderEntity = poinentitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                //基本单位应收数量 = 套件基本单位应收数量*子件数量
                var materialid = salOrderEntity["fproductid"]?.ToString();
                var subqty = Convert.ToDecimal(salOrderEntity["fsubqty"]);//子件数量
                var nowqty = Convert.ToDecimal(poinentity["fplanqty"]) * subqty;//子件基本单位应收数量 = 套件基本单位应收数量*子件数量
                var nowamount = Convert.ToDecimal(salOrderEntity["fdealprice"]) * nowqty;//金额
                var amount = Convert.ToDecimal(salOrderEntity["fprice"]) * nowqty;//采购金额

                var sotckunitid = Convert.ToString((salOrderEntity["fproductid_ref"] as DynamicObject)?["fstockunitid"]);//库存单位
                var unitid = salOrderEntity["funitid"]?.ToString();//基本单位
                var qty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, nowqty, sotckunitid);//应收数量
                var factqty = Convert.ToDecimal(poinentity["fbizqty"]) * subqty; //子件实收数量 = 套件实收数量*子件数量
                var basefactqty = UnitConvertToBaseById(ctx, allMaterials, materialid, sotckunitid, factqty, unitid);//基本单位实收数量
                var purchaseunitid = salOrderEntity["fbizunitid"]?.ToString();//采购单位
                var basepurchaseqty = Convert.ToDecimal(salOrderEntity["fqty"]);//基本单位采购订单数量
                var purchaseqty = BaseUnitConvertById(ctx, allMaterials, materialid, unitid, basepurchaseqty, purchaseunitid);//采购订单数量

                purchaseInOrderEntity["fmaterialid"] = salOrderEntity["fproductid"];//商品
                purchaseInOrderEntity["fcustomdesc"] = salOrderEntity["fcustomdes_e"];//定制说明
                purchaseInOrderEntity["fattrinfo"] = salOrderEntity["fattrinfo"]; //辅助属性
                purchaseInOrderEntity["fattrinfo_e"] = salOrderEntity["fattrinfo_e"]; //辅助属性
                purchaseInOrderEntity["funitid"] = unitid;//基本单位
                purchaseInOrderEntity["fbizunitid"] = purchaseunitid;//采购单位
                purchaseInOrderEntity["fstockunitid"] = sotckunitid; //库存单位
                purchaseInOrderEntity["fplanqty"] = nowqty;//基本单位应收数量
                purchaseInOrderEntity["fbizplanqty"] = qty;//应收数量
                purchaseInOrderEntity["fqty"] = basefactqty;//基本单位实收数量
                purchaseInOrderEntity["fbizqty"] = factqty;//实收数量
                purchaseInOrderEntity["forderqty"] = basepurchaseqty;//基本单位销售合同数量
                purchaseInOrderEntity["fbizorderqty"] = purchaseqty;//销售合同数量
                purchaseInOrderEntity["fstockqty"] = qty;//库存单位默认值
                purchaseInOrderEntity["fprice"] = salOrderEntity["fdealprice"];//成交单价
                purchaseInOrderEntity["famount"] = nowamount;//成交金额
                purchaseInOrderEntity["fpoprice"] = salOrderEntity["fprice"];//采购单价
                purchaseInOrderEntity["fpoamount"] = amount;//金额
                purchaseInOrderEntity["fmtono"] = salOrderEntity["fmtono"];//物流跟踪号
                purchaseInOrderEntity["fownertype"] = salOrderEntity["fownertype"];//货主类型
                purchaseInOrderEntity["fownerid"] = salOrderEntity["fownerid"];//货主
                purchaseInOrderEntity["fentrynote"] = salOrderEntity["fdescription"];//备注
                purchaseInOrderEntity["fsourceformid"] = "ydj_order";//来源单类型
                purchaseInOrderEntity["fsourcebillno"] = salOrder["fbillno"];//来源单编号
                purchaseInOrderEntity["fsourceentryid"] = salOrderEntity["Id"];//来源单分录内码
                purchaseInOrderEntity["fpoorderno"] = "";//销售合同编号
                purchaseInOrderEntity["fpoorderinterid"] = salOrder["Id"];//销售合同内码
                purchaseInOrderEntity["fpoorderentryid"] = salOrderEntity["Id"];//采购订单分录内码
                purchaseInOrderEntity["fmtrlimage"] = salOrderEntity["fmtrlimage"];//图片
                purchaseInOrderEntity["fsoorderno"] = salOrder["fbillno"];//销售合同编号
                purchaseInOrderEntity["fsoorderinterid"] = salOrder["Id"];//销售合同内码
                purchaseInOrderEntity["fsoorderentryid"] = salOrderEntity["Id"];//销售合同分录内码
                purchaseInOrderEntity["fcustomer"] = salOrder["fcustomerid"];//客户
                //purchaseInOrderEntity["fhqderno"] = salOrder["fhqderno"];//总部合同号
                purchaseInOrderEntity["fresultbrandid"] = salOrderEntity["fresultbrandid"];//业绩品牌
                purchaseInOrderEntity["fstorehouseid"] = defaultStore?.FirstOrDefault()?["Id"] ?? "";//仓库ID，直营默认发往总部自营仓
                purchaseInOrderEntity["fstockstatus"] = "311858936800219137";//
                poinentitys.Add(purchaseInOrderEntity);
            }
        }


        /// <summary>
        /// 创建标准条码主档数据，即最底层条码数据
        /// </summary>
        private static void CreateBarCodeMasterByStd(UserContext ctx, HtmlForm htmlForm, List<string> savedbc, DetailData item, List<DynamicObject> datas, string bc, int? packageType, List<DynamicObject> allMaterials, string purchaseOrderId, DynamicObject purchaseOrderEntity, string pobillno, string ReceiveNum, string snCode = "", string securityCode = "")
        {
            var barcodeMaster = htmlForm.GetDynamicObjectType(ctx).CreateInstance() as DynamicObject;

            barcodeMaster["fnumber"] = bc;//条码主档编码
            barcodeMaster["fname"] = bc;//条码主档名称
            barcodeMaster["fpackcount"] = packageType == 1 ? Convert.ToInt32(item.Box) : 1;//总包数
            barcodeMaster["fpackindex"] = packageType == 1 ? GetSplitStr(bc) : 1;//包序号
            barcodeMaster["fbizstatus"] = "4"; //业务状态：1可用 ,4待入库                  
            barcodeMaster["fsourcetype"] = "ydj_purchaseorder";//来源单据类型
            barcodeMaster["fsourcenumber"] = pobillno;//来源单据编号
            barcodeMaster["fsourcelinenumber"] = item.OrderLineNum;//来源单据行号
            barcodeMaster["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单行内码
            barcodeMaster["fsourcebillid"] = purchaseOrderId;//来源单内码
            switch (packageType)
            {
                case 0:
                    barcodeMaster["fpackagtype"] = "1";//打包类型：标准
                    break;
                case 1:
                    barcodeMaster["fpackagtype"] = "2";//打包类型：1件多包
                    break;
                case 2:
                    barcodeMaster["fpackagtype"] = "3";//打包类型：1包多件
                    break;
                default:
                    barcodeMaster["fpackagtype"] = "1";//打包类型：标准
                    break;
            }
            //barcodeMaster["fpackagtype"] = packageType == 0 ? "3" : Convert.ToString(packageType);//打包类型
            barcodeMaster["fmainsncode"] = snCode;
            barcodeMaster["fmainsecuritycode"] = securityCode;
            barcodeMaster["fdescription"] = "总部同步生成";
            barcodeMaster["fmainorgid"] = ctx.Company;
            barcodeMaster["frecnum"] = ReceiveNum;
            var barcodeMasterEntitys = barcodeMaster["fentity"] as DynamicObjectCollection;
            var barcodeMasterEntity = barcodeMasterEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

            var materialid = Convert.ToString(purchaseOrderEntity["fmaterialid"]);
            //barcodeMasterEntity["fbarcode"] = barcodeMaster["fnumber"];//条码 
            barcodeMasterEntity["fserialno"] = "1";//序列号
            barcodeMasterEntity["fmaterialid"] = materialid;//商品编码
            barcodeMasterEntity["fattrinfo"] = purchaseOrderEntity["fattrinfo"]; //辅助属性
            barcodeMasterEntity["fattrinfo_e"] = purchaseOrderEntity["fattrinfo_e"]; //辅助属性
            barcodeMasterEntity["fcustomdesc"] = purchaseOrderEntity["fcustomdes_e"]; //定制说明
            //barcodeMasterEntity["flotno"] = purchaseOrderEntity["flotno"]; //批号
            barcodeMasterEntity["fmtono"] = purchaseOrderEntity["fmtono"];//物流跟踪号
            barcodeMasterEntity["fownertype"] = purchaseOrderEntity["fownertype"]; //货主类型
            barcodeMasterEntity["fownerid"] = purchaseOrderEntity["fownerid"];//货主
            barcodeMasterEntity["fsuitcombnumber"] = purchaseOrderEntity["fsuitcombnumber"];//套件组合号
            barcodeMasterEntity["fpartscombnumber"] = purchaseOrderEntity["fpartscombnumber"];//配件组合号
            barcodeMasterEntity["fsofacombnumber"] = purchaseOrderEntity["fsofacombnumber"];//沙发组合号

            var fstockunitid = Convert.ToString((purchaseOrderEntity["fmaterialid_ref"] as DynamicObject)?["fstockunitid"]);
            var fstockqty = 1; // 库存数量：合包时子件的数量为1
            var funitid = Convert.ToString((purchaseOrderEntity["fmaterialid_ref"] as DynamicObject)?["funitid"]);
            var baseqty = UnitConvertToBaseById(ctx, allMaterials, materialid, fstockunitid, fstockqty, funitid);
            barcodeMasterEntity["fstockunitid"] = fstockunitid;//库存单位
            barcodeMasterEntity["fstockqty"] = fstockqty;//库存数量
            barcodeMasterEntity["funitid"] = funitid; //基本单位
            barcodeMasterEntity["fqty"] = baseqty;

            barcodeMasterEntitys.Add(barcodeMasterEntity);
            savedbc.Add(bc);
            datas.Add(barcodeMaster);

        }

        /// <summary>
        /// 创建子件收货任务明细
        /// </summary>
        private static void CreateChildScanTaskDetail(UserContext ctx, DynamicObjectCollection receiveScanTaskEntitys, DynamicObject purchaseOrder, string fsuitcombnumber, string mainMaterialId, List<DynamicObject> allMaterials, int? packageType, DetailData item, string factorid)
        {
            if (purchaseOrder == null) return;
            var poentitys = purchaseOrder["fentity"] as DynamicObjectCollection;
            if (poentitys == null || poentitys.Count <= 0)
            {
                return;
            }
            var subentitys = poentitys.Where(f => Convert.ToString(f["fsuitcombnumber"]).EqualsIgnoreCase(fsuitcombnumber) && !Convert.ToString(f["fmaterialid"]).EqualsIgnoreCase(mainMaterialId)).ToList();
            if (subentitys == null || subentitys.Count <= 0)
            {
                return;
            }

            foreach (var purchaseOrderEntity in subentitys)
            {
                var receiveScanTaskEntity = receiveScanTaskEntitys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                receiveScanTaskEntity["fmaterialid"] = purchaseOrderEntity["fmaterialid"];//商品
                receiveScanTaskEntity["fcustomdesc"] = purchaseOrderEntity["fcustomdes_e"];//定制说明
                receiveScanTaskEntity["fattrinfo"] = purchaseOrderEntity["fattrinfo"]; //辅助属性
                receiveScanTaskEntity["fattrinfo_e"] = purchaseOrderEntity["fattrinfo_e"]; //辅助属性

                var subqty = Convert.ToDecimal(purchaseOrderEntity["fsubqty"]);//子件数量
                decimal fqty = 0;
                var fpackagingtype = "0";
                decimal fwaitscanqty = (Convert.ToDecimal(item.ShipQty) * subqty);
                switch (packageType)
                {
                    case 0:
                        fpackagingtype = "1";
                        fqty = fwaitscanqty;
                        break;
                    case 1:
                        fqty = Convert.ToDecimal(item.Box);
                        fpackagingtype = "2";
                        fwaitscanqty = Math.Round(fwaitscanqty * fqty, 2);
                        break;
                    case 2:
                        fqty = Convert.ToDecimal(item.Set);
                        fpackagingtype = "3";
                        if (fqty == 0)
                        {
                            fwaitscanqty = 0;
                        }
                        else
                        {
                            fwaitscanqty = Math.Round(fwaitscanqty / fqty, 2);
                        }
                        break;
                }
                receiveScanTaskEntity["fpackagingqty"] = fqty; //包数/件数                                                               
                receiveScanTaskEntity["fwaitworkqty"] = purchaseOrderEntity["fbizqty"];//待作业数：采购数量
                receiveScanTaskEntity["fworkedqty"] = 0;//已作业数
                receiveScanTaskEntity["fwaitscanqty"] = fwaitscanqty;//待扫描包数
                receiveScanTaskEntity["fscannedqty"] = 0;//已扫描包数
                receiveScanTaskEntity["flotno"] = "";//批号
                receiveScanTaskEntity["fpackagingtype"] = fpackagingtype;//打包类型
                receiveScanTaskEntity["fmtono"] = purchaseOrderEntity["fmtono"];//物流跟踪号
                receiveScanTaskEntity["fownertype"] = purchaseOrderEntity["fownertype"];//货主类型
                receiveScanTaskEntity["fsuitegroupid"] = purchaseOrderEntity["fsuitcombnumber"];//套件组合号
                receiveScanTaskEntity["fcategorygroupid"] = purchaseOrderEntity["fpartscombnumber"];//配件组合号
                receiveScanTaskEntity["fsafagroupid"] = purchaseOrderEntity["fsofacombnumber"];//沙发组合号
                receiveScanTaskEntity["fqty"] = purchaseOrderEntity["fqty"];//基本单位数量               
                receiveScanTaskEntity["fsourceformid"] = "ydj_purchaseorder";//来源单据：采购订单
                receiveScanTaskEntity["fsourcebillno"] = purchaseOrder["fbillno"];//来源单据编号:采购订单编号
                receiveScanTaskEntity["fsourceinterid"] = purchaseOrderEntity["fseq_e"];//来源单行号
                receiveScanTaskEntity["fsourceentryid"] = purchaseOrderEntity["Id"];//来源单行内码
                receiveScanTaskEntity["fdeliveryfactoryid"] = factorid;//交货工厂
                receiveScanTaskEntitys.Add(receiveScanTaskEntity);
            }
        }

        /// <summary>
        /// 写入失败操作日志
        /// </summary>
        private void WriteErrorOPLog(HtmlForm htmlForm, DeliveryOrderDTO dto, BaseResponse<object> resp)
        {
            //同步日志对象
            var loghtmlForm = this.MetaModelService.LoadFormModel(this.Context, "si_operationlog");
            var systemIntegrationService = this.Container.GetService<ISystemIntegrationService>();
            var opLogObj = systemIntegrationService.CreateOperationLog(this.Context, loghtmlForm, this.Request.AbsoluteUri, opName, FormId, "2", "慕思中台调用当前系统接口");
            opLogObj["fopstatus"] = "3";
            opLogObj["ferrorsource"] = "2";
            systemIntegrationService.WriteOperationLog(this.Context, opLogObj, $"请求参数：{dto.ToJson()}");
            systemIntegrationService.WriteOperationLog(this.Context, opLogObj, $"响应参数：{resp.ToJson()}");
            systemIntegrationService.SaveOperationLog(this.Context, htmlForm, new[] { opLogObj });
        }

        /// <summary>
        /// 记录消息并写入日志记录
        /// </summary>
        /// <param name="systemIntegrationService"></param>
        /// <param name="opLogObj"></param>
        /// <param name="resmsg"></param>
        /// <param name="successMsgList"></param>
        /// <param name="errMsgList"></param>
        private void RecordMsg(ISystemIntegrationService systemIntegrationService, DynamicObject opLogObj, string resmsg, IGrouping<string, DTO.DeliveryOrder.DeliveryOrder> group, ref List<string> errMsgList, ref List<string> successbills)
        {
            foreach (var data in group)
            {
                if (!successbills.Contains(data.ReceiveNum))
                    successbills.Add(data.ReceiveNum);
            }
            errMsgList.Add(resmsg);
            WriteOperationLog(systemIntegrationService, opLogObj, resmsg);
        }

        /// <summary>
        /// 记录简单消息并写入日志记录
        /// </summary>
        /// <param name="systemIntegrationService"></param>
        /// <param name="opLogObj"></param>
        /// <param name="resmsg"></param>
        /// <param name="errMsgList"></param>
        private void RecordSimpleMsg(ISystemIntegrationService systemIntegrationService, DynamicObject opLogObj, string resmsg, ref List<string> errMsgList)
        {
            errMsgList.Add(resmsg);
            WriteOperationLog(systemIntegrationService, opLogObj, resmsg);
        }

        /// <summary>
        /// 获取交货单中的编码列表
        /// </summary>
        private void GetAllNumberDatas(IGrouping<string, DTO.DeliveryOrder.DeliveryOrder> group, ref List<string> purinnos, ref List<string> purorders, ref List<string> htNos, ref List<string> factorys, ref List<string> barcodelists, ref List<string> securityLists)
        {
            foreach (var item in group)
            {
                if (item.DetailDatas == null) continue;
                if (!purinnos.Contains(item.ReceiveNum)) purinnos.Add(item.ReceiveNum);
                foreach (var item2 in item.DetailDatas)
                {
                    if (!purorders.Contains(item2.OrderNum) && !string.IsNullOrWhiteSpace(item2.OrderNum))
                    {
                        purorders.Add(item2.OrderNum);
                    }
                    if (!htNos.Contains(item2.vbelns) && !string.IsNullOrWhiteSpace(item2.vbelns))
                    {
                        htNos.Add(item2.vbelns);
                    }
                    if (!factorys.Contains(item2.DeliveryFactory) && !string.IsNullOrWhiteSpace(item2.DeliveryFactory))
                    {
                        factorys.Add(item2.DeliveryFactory);
                    }
                    if (item2.BarCodeInfos != null)
                    {
                        foreach (var barCodeInfo in item2.BarCodeInfos)
                        {
                            if (!barcodelists.Contains(barCodeInfo.ParentCode) && !string.IsNullOrWhiteSpace(barCodeInfo.ParentCode))
                            {
                                barcodelists.Add(barCodeInfo.ParentCode);
                            }
                            if (!barcodelists.Contains(barCodeInfo.ChildCode) && !string.IsNullOrWhiteSpace(barCodeInfo.ChildCode))
                            {
                                barcodelists.Add(barCodeInfo.ChildCode);
                            }
                            if (!securityLists.Contains(barCodeInfo.SecurityCode) && !string.IsNullOrWhiteSpace(barCodeInfo.SecurityCode))
                            {
                                securityLists.Add(barCodeInfo.SecurityCode);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查必录字段是否有值
        /// </summary>
        private List<string> CheckMustInputInfo(DTO.DeliveryOrder.DeliveryOrder data)
        {
            var errMsg = new List<string>();
            if (string.IsNullOrWhiteSpace(data.ReceiveNum) || data.DetailDatas.Count <= 0)
            {
                errMsg.Add($"交货单【{data.ReceiveNum}】下发失败：【入库单编号】和入库单明细信息必须录入！");
                return errMsg;
            }

            foreach (var detail in data.DetailDatas)
            {
                if (string.IsNullOrWhiteSpace(detail.ReceiveLineNum))
                {
                    errMsg.Add($"交货单【{data.ReceiveNum}】单据明细信息中的参数【入库单行号】不能为空!");
                }
                if (string.IsNullOrWhiteSpace(detail.OrderNum) && string.IsNullOrWhiteSpace(detail.vbelns))
                {
                    errMsg.Add($"交货单【{data.ReceiveNum}】单据明细信息中入库单行号为[{detail.ReceiveLineNum}]的参数【采购订单编号】或【销售订单号】不能为空!");
                }
                if (string.IsNullOrWhiteSpace(detail.OrderLineNum))
                {
                    errMsg.Add($"交货单【{data.ReceiveNum}】单据明细信息中入库单行号为[{detail.ReceiveLineNum}]的参数【采购订单行号】不能为空!");
                }
                if (string.IsNullOrWhiteSpace(Convert.ToString(detail.ShipQty)))
                {
                    errMsg.Add($"交货单【{data.ReceiveNum}】单据明细信息中入库单行号为[{detail.ReceiveLineNum}]的参数【数量】不能为空!");
                }
            }
            return errMsg;
        }

        /// <summary>
        /// 验证是否可以下发数据
        /// </summary>
        private List<string> CheckIsCanIssueData(UserContext ctx, DTO.DeliveryOrder.DeliveryOrder data, bool fenablebarcode, List<DynamicObject> ScanTasks, List<DynamicObject> purchaseInOrders, List<DynamicObject> purchaseOrders, List<DynamicObject> nowfactorys, ref List<string> inStockMsg)
        {
            var errMsg = new List<string>();

            int inStockCount = 0;
            List<string> inStockOrderNos = new List<string>();//记录当前交货单中商品已入库的入库单号
            List<DetailData> inStockDetail = new List<DetailData>();//记录已入库的商品明细
            foreach (var d in data.DetailDatas)
            {
                var purorder = SelectOrders(purchaseOrders, d.OrderNum, d.vbelns);
                if (purorder == null)
                {
                    errMsg.Add($"交货单【{data.ReceiveNum}】下发失败：未找到对应的采购订单【{d.OrderNum}】! ");
                    return errMsg;
                }
                var purorderens = purorder["fentity"] as DynamicObjectCollection;
                if (purorderens != null && purorderens.Count > 0)
                {
                    var purorderen = purorderens.FirstOrDefault(f => Convert.ToString(f["fseq_e"]) == d.OrderLineNum);
                    if (purorderen == null)
                    {
                        errMsg.Add($"采购订单【{d.OrderNum}】不存在行号为【{d.OrderLineNum}】的数据!");
                        continue;
                    }

                    var material = purorderen["fmaterialid_ref"] as DynamicObject;
                    var materialid = "";
                    if (material != null)
                    {
                        materialid = material["fnumber"]?.ToString();
                    }
                    if (!materialid.EqualsIgnoreCase(d.PLMProdId))
                    {
                        errMsg.Add($"当前交货单【{data.ReceiveNum}】行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】与采购订单【{d.OrderNum}】行号【{d.OrderLineNum}】对应的商品【{materialid}】不一致,不能下发!");
                    }
                    //因为可以重复覆盖下发，此判断不需要了
                    /* 
                    DynamicObjectCollection curtaskentitys = null;
                    DynamicObject curtaskentity = null;
                    if(curscantask != null)
                    {
                        if (fenablebarcode)
                        {                              
                            curtaskentitys = curscantask["ftaskentity"] as DynamicObjectCollection;
                            if (curtaskentitys != null && curtaskentitys.Count > 0)
                            {
                                curtaskentity = curtaskentitys.FirstOrDefault(f =>
                                    Convert.ToString(f["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder")
                                    && Convert.ToString(f["fsourcebillno"]).EqualsIgnoreCase(purorder["fbillno"]?.ToString())
                                    && Convert.ToString(f["fsourceentryid"]).EqualsIgnoreCase(purorderen["Id"]?.ToString()));
                            }                                
                        }
                        else
                        {
                            if (curscantask != null)
                                curtaskentitys = curscantask["fentity"] as DynamicObjectCollection;
                            if (curtaskentitys != null && curtaskentitys.Count > 0)
                            {
                                curtaskentity = curtaskentitys.FirstOrDefault(f =>
                                    Convert.ToString(f["fpoorderinterid"]).EqualsIgnoreCase(purorder["Id"]?.ToString())
                                    && Convert.ToString(f["fpoorderentryid"]).EqualsIgnoreCase(purorderen["Id"]?.ToString()));
                            }
                        }
                    }
                    if (curtaskentity != null)
                    {
                        errMsg.Add($"当前交货单【{data.ReceiveNum}】对应的采购订单行【{d.OrderLineNum}】已下发，不能重复下发! ");
                    } 
                    */
                    //查询采购订单中该商品对应的采购入库单
                    var purEntId = Convert.ToString(purorderen["id"]);
                    var orgId = Convert.ToString(purorder["fmainorgid"]);
                    var sql = $@"SELECT A.fbillno,B.fsourceentryid,SUM(B.fqty) fqty  
                                 FROM T_STK_POSTOCKIN AS A WITH(NOLOCK)
                                 INNER JOIN T_STK_POSTOCKINENTRY AS B WITH(NOLOCK) ON A.fid=B.fid 
                                 WHERE B.fsourceentryid='{purEntId}' AND A.fbillno<>'{data.ReceiveNum}' AND A.fmainorgid='{orgId}' AND A.fcancelstatus='0' 
                                 GROUP BY A.fbillno,B.fsourceentryid";
                    var purInObjs = this.Context.ExecuteDynamicObject(sql, null);
                    if (purInObjs != null && purInObjs.Any())
                    {
                        var inStockQty = purInObjs.Select(t => Convert.ToDecimal(t["fqty"])).Sum();//入库单实收总数
                        var purQty = Convert.ToDecimal(purorderen["fbizqty"]);//采购单采购数量
                        var purRetQty = Convert.ToDecimal(purorderen["freturnqty"]);//采购单退换数量
                        var currQty = d.ShipQty == null ? 0 : d.ShipQty.Value;//本次入库数量
                        var billNos = purInObjs.Select(t => Convert.ToString(t["fbillno"])).ToList();
                        var canInQty = purQty - inStockQty + purRetQty;//可入库数量
                        if (canInQty <= 0)
                        {
                            //已全部入库
                            //errMsg.Add($"当前交货单【{data.ReceiveNum}】行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已全部入库，入库单号【{string.Join("，",billNos)}】,不能下发!");
                            inStockMsg.Add($"行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已全部入库，入库单号【{string.Join("，", billNos)}】；");
                            inStockOrderNos.AddRange(billNos);
                            inStockDetail.Add(d);
                            inStockCount++;//记录已全部入库的商品数量
                        }
                        else if (canInQty < currQty)
                        {
                            //超收
                            //errMsg.Add($"当前交货单【{data.ReceiveNum}】行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已入库数量【{currQty}】大于可入库数量【{canInQty}】，入库单号【{string.Join("，", billNos)}】,不能下发!");
                            inStockMsg.Add($"行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已入库数量（含退货）【{inStockQty - purRetQty}】，入库单号【{string.Join("，", billNos)}】；");
                            d.ShipQty = canInQty;//将入库数调整为可入库数
                        }
                    }
                }
                var factory = nowfactorys.FirstOrDefault(f => f["fnumber"]?.ToString() == d.DeliveryFactory || f["fname"]?.ToString() == d.DeliveryFactory);
                if (factory == null && !string.IsNullOrWhiteSpace(d.DeliveryFactory))
                {
                    if (!ctx.IsDirectSale)
                    {
                        errMsg.Add($"当前交货单【{data.ReceiveNum}】对应的交货工厂【{d.DeliveryFactory}】不存在，请先在系统中维护交货工厂信息! ");
                    }
                }

            }
            if (inStockCount == data.DetailDatas.Count)
            {
                //如果全部已入库，这提示商品全部入库完成
                errMsg.Clear();
                errMsg.Add($"当前交货单【{data.ReceiveNum}】中的所有商品已全部入库，入库单号【{string.Join("，", inStockOrderNos.Distinct())}】,不能下发!");
            }
            else if (inStockDetail.Any())
            {
                //把已入库的商品从明细中移除
                foreach (var d in inStockDetail)
                {
                    data.DetailDatas.Remove(d);
                }
            }
            return errMsg;
        }

        /// <summary>
        /// 直营门店下发数据验证
        /// </summary>
        /// <param name="data"></param>
        /// <param name="fenablebarcode"></param>
        /// <param name="ScanTasks"></param>
        /// <param name="purchaseInOrders"></param>
        /// <param name="purchaseOrders"></param>
        /// <param name="nowfactorys"></param>
        /// <param name="inStockMsg"></param>
        /// <returns></returns>
        private List<string> CheckIsCanIssueDataByZY(DTO.DeliveryOrder.DeliveryOrder data, List<DynamicObject> purchaseInOrders, List<DynamicObject> purchaseOrders, List<DynamicObject> nowfactorys, ref List<string> inStockMsg)
        {
            var errMsg = new List<string>();

            int inStockCount = 0;
            List<string> inStockOrderNos = new List<string>();//记录当前交货单中商品已入库的入库单号
            List<DetailData> inStockDetail = new List<DetailData>();//记录已入库的商品明细
            foreach (var d in data.DetailDatas)
            {
                var purorder = SelectOrders(purchaseOrders, d.OrderNum, d.vbelns);
                if (purorder == null)
                {
                    errMsg.Add($"交货单【{data.ReceiveNum}】下发失败：未找到对应的销售合同【{d.OrderNum}】! ");
                    return errMsg;
                }
                var purorderens = purorder["fentry"] as DynamicObjectCollection;
                if (purorderens != null && purorderens.Count > 0)
                {
                    //目前待确认匹配条件
                    var purorderen = purorderens.FirstOrDefault(f => Convert.ToString(f["fseq"]) == d.OrderLineNum);
                    //var purorderen = purorderens.FirstOrDefault(f => Convert.ToString(f["fentryid"]) == d.id);
                    if (purorderen == null)
                    {
                        errMsg.Add($"销售合同【{d.OrderNum}】不存在行ID为【{d.OrderLineNum}】的数据!");
                        continue;
                    }

                    var material = purorderen["fproductid_ref"] as DynamicObject;
                    var materialid = "";
                    if (material != null)
                    {
                        materialid = material["fnumber"]?.ToString();
                    }
                    if (!materialid.EqualsIgnoreCase(d.PLMProdId))
                    {
                        errMsg.Add($"当前交货单【{data.ReceiveNum}】行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】与销售合同【{d.OrderNum}】行号【{d.OrderLineNum}】对应的商品【{materialid}】不一致,不能下发!");
                    }
                    //查询采购订单中该商品对应的采购入库单
                    var purEntId = Convert.ToString(purorderen["id"]);
                    var orgId = Convert.ToString(purorder["fmainorgid"]);
                    var sql = $@"SELECT A.fbillno,B.fsourceentryid,SUM(B.fqty) fqty  
                                 FROM T_STK_POSTOCKIN AS A WITH(NOLOCK)
                                 INNER JOIN T_STK_POSTOCKINENTRY AS B WITH(NOLOCK) ON A.fid=B.fid 
                                 WHERE B.fsourceentryid='{purEntId}' AND A.fbillno<>'{data.ReceiveNum}' AND A.fmainorgid='{orgId}' AND A.fcancelstatus='0' 
                                 GROUP BY A.fbillno,B.fsourceentryid";
                    var purInObjs = this.Context.ExecuteDynamicObject(sql, null);
                    if (purInObjs != null && purInObjs.Any())
                    {
                        var inStockQty = purInObjs.Select(t => Convert.ToDecimal(t["fqty"])).Sum();//入库单实收总数
                        var purQty = Convert.ToDecimal(purorderen["fbizqty"]);//采购单采购数量
                        var purRetQty = Convert.ToDecimal(purorderen["freturnqty"]);//采购单退换数量
                        var currQty = d.ShipQty == null ? 0 : d.ShipQty.Value;//本次入库数量
                        var billNos = purInObjs.Select(t => Convert.ToString(t["fbillno"])).ToList();
                        var canInQty = purQty - inStockQty + purRetQty;//可入库数量
                        if (canInQty <= 0)
                        {
                            //已全部入库
                            //errMsg.Add($"当前交货单【{data.ReceiveNum}】行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已全部入库，入库单号【{string.Join("，",billNos)}】,不能下发!");
                            inStockMsg.Add($"行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已全部入库，入库单号【{string.Join("，", billNos)}】；");
                            inStockOrderNos.AddRange(billNos);
                            inStockDetail.Add(d);
                            inStockCount++;//记录已全部入库的商品数量
                        }
                        else if (canInQty < currQty)
                        {
                            //超收
                            //errMsg.Add($"当前交货单【{data.ReceiveNum}】行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已入库数量【{currQty}】大于可入库数量【{canInQty}】，入库单号【{string.Join("，", billNos)}】,不能下发!");
                            inStockMsg.Add($"行号【{d.ReceiveLineNum}】对应的商品【{d.PLMProdId}】已入库数量（含退货）【{inStockQty - purRetQty}】，入库单号【{string.Join("，", billNos)}】；");
                            d.ShipQty = canInQty;//将入库数调整为可入库数
                        }
                    }
                }
                var factory = nowfactorys.FirstOrDefault(f => f["fnumber"]?.ToString() == d.DeliveryFactory || f["fname"]?.ToString() == d.DeliveryFactory);
                if (factory == null && !string.IsNullOrWhiteSpace(d.DeliveryFactory))
                {
                    errMsg.Add($"当前交货单【{data.ReceiveNum}】对应的交货工厂【{d.DeliveryFactory}】不存在，请先在系统中维护交货工厂信息! ");
                }

            }
            if (inStockCount == data.DetailDatas.Count)
            {
                //如果全部已入库，这提示商品全部入库完成
                errMsg.Clear();
                errMsg.Add($"当前交货单【{data.ReceiveNum}】中的所有商品已全部入库，入库单号【{string.Join("，", inStockOrderNos.Distinct())}】,不能下发!");
            }
            else if (inStockDetail.Any())
            {
                //把已入库的商品从明细中移除
                foreach (var d in inStockDetail)
                {
                    data.DetailDatas.Remove(d);
                }
            }
            return errMsg;
        }

        /// <summary>
        /// 检查交货单是否短时间有重复下发
        /// </summary>
        /// <param name="orderData"></param>
        /// <returns></returns>
        private bool CheckRepeatOrderData(UserContext agentCtx, DTO.DeliveryOrder.DeliveryOrder orderData, double expirySeconds)
        {
            if (IsRetry) return true;//接口重试的不做检查

            // 先到缓存里找
            var hash = orderData.ToJson().HashString();
            var cacheKey = GetCacheKey(agentCtx, hash);
            var cacheValue = this.CacheClient.Get<string>(cacheKey);

            if (cacheValue.IsNullOrEmptyOrWhiteSpace())
            {
                // 如果缓存里没有，就生成一个
                // 获取流水号
                ISequenceService seqSvc = this.Container.GetService<ISequenceService>();
                var tranId = seqSvc.GetSequence<string>();

                // 缓存起来
                this.CacheClient.Set(cacheKey, tranId, TimeSpan.FromSeconds(expirySeconds));
            }
            else
            {
                return false;
            }
            return true;
        }

        // 查询经销商编码方法（请根据实际业务实现）
        private string GetDealerNumByOrderLineNum(string orderLineNum)
        {
            // 示例：根据采购订单行号查找经销商编码
            string sql = $@"
        SELECT TOP 1 a.fnumber
        FROM T_BAS_AGENT a
        INNER JOIN T_PUR_POORDERENTRY b ON a.fid = b.fagentid
        WHERE b.fseq_e = '{orderLineNum}'";
            var result = this.Context.ExecuteDynamicObject(sql, null).FirstOrDefault();
            return result?["fnumber"]?.ToString();
        }

        /// <summary>
        /// 检查交货单是否存在
        /// </summary>
        /// <param name="orderData"></param>
        /// <returns></returns>
        private bool ExistsPoInOrder(UserContext agentCtx, HtmlForm htmlForm, DTO.DeliveryOrder.DeliveryOrder orderData)
        {
            if (IsRetry) return true;//接口重试的不做检查

            string sql =
                $"select top 1 fbillno from {htmlForm.BillHeadTableName} with(nolock) where fmainorgid='{agentCtx.Company}' and fbillno='{orderData.ReceiveNum}'";

            return this.DBService.ExecuteDynamicObject(agentCtx, sql).FirstOrDefault() != null;
        }

        private string GetCacheKey(UserContext agentCtx, string hash)
        {
            string cacheKey = $"MSAPI:RepeatedDeliveryOrder:{agentCtx.Company}:{FormId}:{hash}";

            return cacheKey;
        }

        protected override Dictionary<string, string> CreateDistributedLocks(DeliveryOrderDTO dto)
        {
            Dictionary<string, string> dicResult = new Dictionary<string, string>();
            foreach (var item in dto.Head)
            {
                if (dicResult.ContainsKey($"DistributedLock:{this.FormId}:{item.ReceiveNum}"))
                {
                    continue;
                }
                dicResult.Add($"DistributedLock:{this.FormId}:{item.ReceiveNum}", $"交货单 {item.ReceiveNum} 正在锁定中，请稍后再操作！");
            }
            return dicResult;
        }

        private void WriteOperationLog(ISystemIntegrationService systemIntegrationService, DynamicObject opLogObj, string logMsg)
        {
            if (!IsRetry)
            {
                //非错误重试才记录日志
                systemIntegrationService.WriteOperationLog(this.Context, opLogObj, logMsg);
            }
        }

        private void SaveOperationLog(ISystemIntegrationService systemIntegrationService, HtmlForm htmlForm, DynamicObject[] logData)
        {
            if (!IsRetry)
            {
                //非错误重试才记录日志
                systemIntegrationService.SaveOperationLog(this.Context, htmlForm, logData);
            }
        }

        private bool GetSameTiemRequest()
        {
            //判断是否同一次请求内的错误重试
            string cacheKey = $"MSAPI:CreatePurchaseInOrder:{this.Context.Company}:{FormId}:{this.Context.RequestId}";
            var cacheValue = this.CacheClient.Get<string>(cacheKey);
            if (cacheValue.IsNullOrEmptyOrWhiteSpace())
            {
                // 如果缓存里没有，就生成一个
                // 获取流水号
                ISequenceService seqSvc = this.Container.GetService<ISequenceService>();
                var tranId = seqSvc.GetSequence<string>();

                // 缓存起来
                var expiredSec = "".GetAppConfig<int>(HostConfigKeys.FW.AsyncRequestExpiredSec, 600);
                this.CacheClient.Set(cacheKey, tranId, TimeSpan.FromSeconds(expiredSec));
            }
            return !cacheValue.IsNullOrEmptyOrWhiteSpace();
        }
    }


}
