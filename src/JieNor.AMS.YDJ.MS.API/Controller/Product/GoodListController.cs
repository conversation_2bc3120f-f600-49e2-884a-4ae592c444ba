using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.Product;
using JieNor.AMS.YDJ.MS.API.DTO.Ste.Customer;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.Product.Product
{
    public class GoodListController : BaseController<GoodListDTO>
    {
        public string FormId
        {
            get { return "ydj_product"; }
        }

        /// <summary>
        /// 经销商用户上下文
        /// </summary>
        protected UserContext AgentContext { get; set; }

        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(GoodListDTO dto)
        {
            var resp = new BaseResponse<object>();

            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
            var dt = this.HtmlForm.GetDynamicObjectType(this.AgentContext);
            var dataEntities = ConvertToDyns(dt, dto.Data);
            // 向麦浩系统发送请求
            var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(Context, new CommonBillDTO()
            {
                FormId = this.FormId,
                OperationNo = "goodlist",
                SimpleData = new Dictionary<string, string>
                {
                    { "param",dto.Data.ToJson() }
                }
            });

            var result = response?.OperationResult;
            return result.ToResponseModel<object>();
        }

        /// <summary>
        /// 得到客户
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetCustomer()
        {
            var formData = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, formData.GetDynamicObjectType(this.Context));
            var where = " 1=1 ";
            var reader = this.Context.GetPkIdDataReader(formData, where, new List<SqlParam> { });
            var data = dm.SelectBy(reader).OfType<DynamicObject>().ToList();
            return data;
        }

        private IEnumerable<DynamicObject> ConvertToDyns(DynamicObjectType dt, JArray data)
        {
            if (data.IsNullOrEmpty()) return new List<DynamicObject>();

            List<DynamicObject> dyns = new List<DynamicObject>();

            foreach (JObject item in data)
            {
                dyns.Add(ConvertToDyn(dt, item));
            }

            return dyns;
        }

        private DynamicObject ConvertToDyn(DynamicObjectType dt, JObject data)
        {
            var dyn = dt.CreateInstance() as DynamicObject;

            foreach (var property in data.Properties())
            {
                if (property.Value is JArray)
                {
                    var entryEntity = this.HtmlForm.GetEntryEntity(property.Name);
                    if (entryEntity == null) continue;

                    var entrys = dyn[entryEntity.PropertyName] as DynamicObjectCollection;

                    var dyns = ConvertToDyns(entryEntity.DynamicObjectType, (JArray)property.Value);

                    foreach (var item in dyns)
                    {
                        entrys.Add(item);
                    }

                    continue;
                }

                if (property.Value is JObject)
                {
                    continue;
                }

                var field = this.HtmlForm.GetField(property.Name);
                if (field == null) continue;

                dyn[field.PropertyName] = property.Value;
            }

            return dyn;
        }
    }

}
