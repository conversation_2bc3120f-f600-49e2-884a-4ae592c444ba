using JieNor.AMS.YDJ.MS.API.DTO.Category;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.Category
{
    public class ProductPackPushController : BaseController<ProductPackPushDTO>
    {

        public string FormId
        {
            get { return "ydj_product"; }
        }

        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(ProductPackPushDTO dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<MuSiData>();

            var dt = this.HtmlForm.GetDynamicObjectType(this.Context);

            string data = System.Text.RegularExpressions.Regex.Unescape(dto.Data.ToJson());
            List<datasend> materialpackage = JsonConvert.DeserializeObject<List<datasend>>(data);

            List<string> retmes = new List<string>();

            var matlistgroup = materialpackage.GroupBy(g => new
            {
                fmatnumber = g.fnumber,
                fisbarcode = g.fisbarcode,
                fpackagtype = g.fpackagtype,
                fpiece = g.fpiece,
                fbag = g.fbag
            }).ToList();


            List<DynamicObject> dataEntities = new List<DynamicObject>();
            List<string> matnums = new List<string>();
            foreach (var matlist in matlistgroup)
            {
                var matnumber = Convert.ToString(matlist.Key.fmatnumber);//物料编码
                matnums.Add(matnumber);
            }
            var filter = string.Format(@" fnumber in ('{0}')", string.Join("','", matnums.Distinct()));
            var dynmats = this.Context.LoadBizDataByFilter(this.FormId, filter);
            foreach (var matlist in matlistgroup)
            {
                var matnumber = Convert.ToString(matlist.Key.fmatnumber);//物料编码
                var dyn = dynmats.Where(w => Convert.ToString(w["fnumber"]) == matnumber).FirstOrDefault();
                if (dyn == null)
                {
                    retmes.Add("物料编码【" + matnumber + "】不存在!");
                    //失败
                    resp.Data.FailedNumbers.Add(matnumber);
                    continue;
                }
                dyn["fisbarcode"] = matlist.Key.fisbarcode;
                dyn["fpiece"] = matlist.Key.fpiece == "" ? "0" : matlist.Key.fpiece;
                dyn["fbag"] = matlist.Key.fbag == "" ? "0" : matlist.Key.fbag;
                if (matlist.Key.fpackagtype == "0") //标准
                {
                    dyn["fpackagtype"] = "1";
                }
                else if (matlist.Key.fpackagtype == "1") //1件多包
                {
                    dyn["fpackagtype"] = "2";
                    //BUG-37459:目前WMS其实用不到包装明细，过滤掉包装明细的接收只接收包装信息：
                    //var entrys = dyn["fpackage"] as DynamicObjectCollection;
                    //entrys.Clear();
                    //var matlistnew = matlist.OrderBy(o => Convert.ToString(o.fpacknumber)).ToList();
                    //foreach (var item in matlistnew)
                    //{
                    //    var entry = entrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    //    entry["fpacknumber"] = Convert.ToString(item.fpacknumber);
                    //    entry["fdescribe"] = Convert.ToString(item.fdescribe);
                    //    entry["fstandard"] = Convert.ToString(item.fstandard);
                    //    entry["flength"] = Convert.ToString(item.flength);
                    //    entry["fwidth"] = Convert.ToString(item.fwidth);
                    //    entry["fheight"] = Convert.ToString(item.fheight);
                    //    entry["fpackvolume"] = Convert.ToString(item.fpackvolume);
                    //    entrys.Add(entry);
                    //}
                }
                else if (matlist.Key.fpackagtype == "2") //1包多件
                {
                    dyn["fpackagtype"] = "3";
                }
                else
                {
                    dyn["fpackagtype"] = "1";
                }
                var match = dataEntities.Where(w => Convert.ToString(w["fnumber"]).Equals(matnumber));
                if (match == null || !match.Any())
                    dataEntities.Add(dyn);
            }
            var geteway = this.Context.Container.GetService<IHttpServiceInvoker>();
            var response = geteway.InvokeBillOperation(this.Context, this.HtmlForm.Id, dataEntities, "save", null);
            if (!response.IsSuccess)
            {
                resp.Code = 201;
                response.SimpleData.TryGetValue("pkids", out var succdatid);
                var pkids = new List<string>();
                if (succdatid != null)
                {
                    pkids = JsonConvert.DeserializeObject<List<string>>(succdatid);
                }

                foreach (var item in dataEntities)
                {
                    var nodata = pkids.Where(s => Convert.ToString(s) == Convert.ToString(item["id"])).ToList();
                    if (nodata.Count() == 0)
                    {
                        resp.Data.FailedNumbers.Add(Convert.ToString(item["fnumber"]));
                    }
                    else
                    {
                        resp.Data.SucceedNumbers.Add(Convert.ToString(item["fnumber"]));
                    }
                }
                resp.Success = false;
                if (resp.Data.FailedNumbers.Count == 0)
                {
                    resp.Data.Flag = MuSiFlag.SUCCESS.ToString();
                }
                if (resp.Data.FailedNumbers.Count > 0 && resp.Data.FailedNumbers.Distinct().Count() < matlistgroup.Count())
                {
                    resp.Data.Flag = MuSiFlag.PARTSUCCESS.ToString();
                }
                if (resp.Data.FailedNumbers.Count > 0 && resp.Data.FailedNumbers.Distinct().Count() == matlistgroup.Count())
                {
                    resp.Data.Flag = MuSiFlag.FAIL.ToString();
                }
                foreach (var item in response.ComplexMessage.ErrorMessages)
                {
                    retmes.Add(item);
                }
                resp.Data.ErrorMsgs = retmes;
                resp.Message = string.Join("", retmes);

            }
            else
            {
                resp.Code = 200;
                resp.Success = true;
                resp.Data.SucceedNumbers.AddRange(dataEntities.Select(s => Convert.ToString(s["fnumber"])).ToList());
                if (resp.Data.FailedNumbers.Count == 0)
                {
                    resp.Data.Flag = MuSiFlag.SUCCESS.ToString();
                }
                if (resp.Data.FailedNumbers.Count > 0 && resp.Data.FailedNumbers.Distinct().Count() < matlistgroup.Count())
                {
                    resp.Data.Flag = MuSiFlag.PARTSUCCESS.ToString();
                }
                if (resp.Data.FailedNumbers.Count > 0 && resp.Data.FailedNumbers.Distinct().Count() == matlistgroup.Count())
                {
                    resp.Data.Flag = MuSiFlag.FAIL.ToString();
                }
                resp.Data.ErrorMsgs = retmes;
                resp.Message = string.Join("", response.ComplexMessage.SuccessMessages);
            }

            return resp;

        }

        protected override Dictionary<string, string> CreateDistributedLocks(ProductPackPushDTO dto)
        {
            string data = System.Text.RegularExpressions.Regex.Unescape(dto.Data.ToJson());
            List<datasend> materialpackage = JsonConvert.DeserializeObject<List<datasend>>(data);
            Dictionary<string, string> dicResult = new Dictionary<string, string>();
            foreach (var item in materialpackage)
            {
                if (dicResult.ContainsKey($"DistributedLock:{this.FormId}:{item.fnumber}"))
                {
                    continue;
                }
                dicResult.Add($"DistributedLock:{this.FormId}:{item.fnumber}", $"产品包装明细 {item.fnumber} 正在锁定中，请稍后再操作！");
            }
            return dicResult;
        }



        public class datasend
        {
            /// <summary>
            /// 物料编码
            /// </summary>
            public string fnumber { get; set; }

            /// <summary>
            /// 启用条码
            /// </summary>
            public bool fisbarcode { get; set; }

            /// <summary>
            /// 打包类型
            /// </summary>
            public string fpackagtype { get; set; }

            /// <summary>
            /// 件
            /// </summary>
            public string fpiece { get; set; }

            /// <summary>
            /// 包
            /// </summary>
            public string fbag { get; set; }

            /// <summary>
            /// 包件序号
            /// </summary>
            public string fpacknumber { get; set; }

            /// <summary>
            /// 包件描述
            /// </summary>
            public string fdescribe { get; set; }

            /// <summary>
            /// 包件规格
            /// </summary>
            public string fstandard { get; set; }

            /// <summary>
            /// 包件长
            /// </summary>
            public string flength { get; set; }

            /// <summary>
            /// 包件宽
            /// </summary>
            public string fwidth { get; set; }

            /// <summary>
            /// 包件高
            /// </summary>
            public string fheight { get; set; }

            /// <summary>
            /// 包件体积
            /// </summary>
            public string fpackvolume { get; set; }
        }




    }
}
