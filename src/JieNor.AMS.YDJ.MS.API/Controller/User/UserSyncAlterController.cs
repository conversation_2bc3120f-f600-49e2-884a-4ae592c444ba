using JieNor.AMS.YDJ.MS.API.DTO.User;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Validation;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.User
{
    /// <summary>
    /// 用户：同步接口
    /// </summary>
    public class UserSyncAlterController : BaseAuthController<UserSyncAlterDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "sec_user"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "id";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(UserSyncAlterDto dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<MuSiData>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();

            var sql = $@"select fid,ftranid,fstatus,fagentstatus from t_bas_agent with(nolock) where ftranid in ({dto.Data.Select(x => x.agentid)?.JoinEx(",", true)})";
            var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());

            // 根据外部Id查询所有的用户Ids
            sql = $@"select fid,ftranid from t_sec_user with(nolock) where ftranid in ({dto.Data.Select(x => x.operatorid)?.JoinEx(",", true)})";
            var users = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());

            foreach (var group in dto.Data.GroupBy(s => new { agentid = s.agentid, operatorid = s.operatorid }))
            {
                try
                {
                    var agentCtx = MusiAuthValidation.GetUserContext(this.Context, agents, Tuple.Create(group.Key.agentid, group?.FirstOrDefault().agentno), users, group.Key.operatorid, resp);
                    //检查转换数据
                    var datas = this.GetUserData(this.Context, group.ToList());
                    if (!datas.Any())
                    {
                        resp.Code = 400;
                        resp.Success = false;
                        resp.Data.ErrorMsgs.Add($"用户编码【{group.ToList().Select(o=>Convert.ToString(o.userid))}】未找到相关用户数据，请检查！");
                        resp.Data.FailedNumbers.AddRange(group.ToList().Select(o => Convert.ToString(o.id)));
                        resp.Data.FailedNumbers_Log.AddRange(group.ToList().Select(o => Convert.ToString(o.userid)));
                        continue;
                    }

                    //UpdatePhone(this.Context, group, resp);
                    if (!resp.Success) 
                    {
                        resp.Data.FailedNumbers.AddRange(group.ToList().Select(o => Convert.ToString(o.id)).ToList());
                        resp.Data.FailedNumbers_Log.AddRange(group.ToList().Select(o => Convert.ToString(o.userid)).ToList());
                        continue;
                    }

                    // 本批处理的转换数据
                    if (datas != null && datas.Count > 0)
                    {
                        // 直接保存数据库
                        //var dm = this.GetDataManager();
                        //dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
                        //dm.Save(datas);
                        var tranids = datas.Select(x => Convert.ToString(x["ftranid"]));
                        var Numbers = datas.Select(x => Convert.ToString(x["fnumber"]));
                        var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                        prepareSaveDataService.PrepareDataEntity(agentCtx, this.HtmlForm, datas.ToArray(), OperateOption.Create());
                        var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, datas, "save", new Dictionary<string, object>());
                        if (result.IsSuccess)
                        {
                            resp.Data.SucceedNumbers.AddRange(tranids);
                            resp.Data.SucceedNumbers_Log.AddRange(Numbers);
                        }
                        else
                        {
                            resp.Success = false;
                            resp.Data.FailedNumbers.AddRange(tranids);
                            resp.Data.FailedNumbers_Log.AddRange(Numbers);
                            resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                        }
                    } 
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            }

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            return resp;
        }

        private List<DynamicObject> GetUserData(UserContext ctx,List<UserData> datas) 
        {
            var dynObjs = new List<DynamicObject>();
            foreach (var data in datas) 
            {
                var Form = this.MetaModelService.LoadFormModel(ctx, this.HtmlForm.Id);
                var reader = ctx.GetPkIdDataReader(Form, $@"ftranid ='{data.id}'", new List<SqlParam>() { });
                var formDt = Form.GetDynamicObjectType(Context);
                var dm = ctx.Container.GetService<IDataManager>();
                dm.InitDbContext(Context, formDt);
                var dynObj = dm.SelectBy(reader).OfType<DynamicObject>()?.FirstOrDefault();

                if (dynObj != null)
                { 
                    dynObj["fphone"] = data.phone;
                    dynObj["fnumber"] = data.userid;
                    dynObj["fname"] = data.name;
                    dynObj["femail"] = data.email;
                    dynObj["fqqid"] = data.qqid;
                    dynObj["fwechatid"] = data.wechatid;
                    dynObj["fisadmin"] = data.isadmin;
                    dynObj["fdescription"] = data.description;

                    dynObjs.Add(dynObj);
                } 
            }  

            return dynObjs;
        }

        private void UpdatePhone(UserContext ctx, UserData data, BaseResponse<MuSiData> resp) 
        {
            var localUserNumber = data.userid;
            var remoteUserId = ExistsAuthUser(this.Context, localUserNumber);
            if (remoteUserId.IsNullOrEmptyOrWhiteSpace()) return;

            //更新auth站点登录方式
            //更新远程用户信息
            var changePhoneDto = new CommonFormDTO()
                .SetFormId("auth_user")
                .SetOperationNo("changephone")
                .SetSimpleData("phoneNo", data.phone)
                .SetSelectedRows(new SelectedRow[]
                {
                    new SelectedRow()
                    {
                        PkValue = remoteUserId
                    }
                });
            var gateway = ctx.Container.GetService<IHttpServiceInvoker>();
            var result = gateway.Invoke(ctx,
                TargetSEP.AuthService,
                changePhoneDto);
             
            var respResult = (result as DynamicDTOResponse)?.OperationResult;
            if (!respResult.IsSuccess) 
            {
                resp.Code = 400;
                resp.Message = $@"用户更新远程auth站点手机号失败，错误原因： {respResult.ComplexMessage}";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);
            } 
        }


        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        private string ExistsAuthUser(UserContext ctx, string userName, string userId = "")
        {
            var para = new CommonBillDTO()
            {
                FormId = "auth_user",
                OperationNo = "fetch"
            };
            para.SimpleData.Add("userName", userName);
            para.SimpleData.Add("userId", userId);
            var gateway = ctx.Container.GetService<IHttpServiceInvoker>();

            var targetSEP = TargetSEP.AuthService;
            var appId = ctx.AppId;
            if (!targetSEP.Headers.ContainsKey("X-AppId")) targetSEP.Headers.Add("X-AppId", appId);

            var result = gateway.Invoke(null, targetSEP, para) as DynamicDTOResponse;
            var exists = result?.OperationResult?.SrvData?.ToString().FromJson<CustomUserAuth>();

            return exists?.RefIdStr;
        }
        private bool Valid(UserSyncAlterDto dto, BaseResponse<MuSiData> resp)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);

                return false;
            }

            List<string> errorMsgs = new List<string>();
            foreach (var data in dto.Data)
            {
                if (data.id.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数id不能为空！");
                }
                //if (data.userid != data.phone)
                //{
                //    errorMsgs.Add("登录账号和手机号必须保持一致！");
                //} 
                if(data.name.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数name不能为空！");
                }
                //else if (data.agentid.IsNullOrEmptyOrWhiteSpace())
                //{
                //    errorMsgs.Add("参数agentid不能为空！");
                //}

                //如果二级组织不为空，说明是二级分销用户组织操作新增修改
                if (!data.secagentid.IsNullOrEmptyOrWhiteSpace())
                {
                    data.agentid = data.secagentid;
                    data.agentno = data.secagentno;
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();

                return false;
            }

            return true;
        }
    }
}
