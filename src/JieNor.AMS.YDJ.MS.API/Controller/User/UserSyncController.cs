using JieNor.AMS.YDJ.MS.API.DTO.User;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Validation;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.User
{
    /// <summary>
    /// 用户：同步接口
    /// </summary>
    public class UserSyncController : BaseAuthController<UserSyncDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "sec_user"; }
        }

        protected UserContext TopContext;

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "id";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(UserSyncDto dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<MuSiData>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();
            this.TopContext = this.Context.CreateTopOrgDBContext();

            //如果当前修改的用户关联了 【系统运维】，则角色修改失败，避免被删除了经销商角色导致经销商组织下的所有权限错乱
            var sql = @"select fid,fname,fnumber from T_sec_role with(nolock) where fmainorgid='{0}' and fnumber in ('Admin_DevOps') ".Fmt(this.Context.TopCompanyId);
            var roleInfo = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());

            #region 查询相关数据
            // 根据外部Id查询所有的经销商Ids
            sql = $@"select fid,ftranid,fstatus,fagentstatus from t_bas_agent with(nolock) where ftranid in ({dto.Data.Select(x => x.agentid)?.JoinEx(",", true)})";
            var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());

            //根据经销商id 查询主经销商配置表子经销商数据
            sql = $@"select fsubagentid from t_bas_mac tb0 with(nolock)
                                                    inner join t_bas_macentry tb1  with(nolock) on tb0.fid = tb1.fid
                                                          where tb1.fsubagentid  in ({agents.Select(x => Convert.ToString(x["fid"]))?.JoinEx(",", true)})";
            List<string> allbasmacentry = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>())?.Select(p=>Convert.ToString(p["fsubagentid"])).ToList();

            // 根据外部Id查询所有的用户Ids
            sql = $@"select fid,ftranid from t_sec_user with(nolock) where ftranid in ({dto.Data.Select(x => x.operatorid)?.JoinEx(",", true)})";
            var users = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            #endregion
            foreach (var group in dto.Data.GroupBy(s => new { agentid = s.agentid, operatorid = s.operatorid }))
            {
                try
                {
                    //判断经销商和操作人
                    var agentCtx = MusiAuthValidation.GetUserContext(this.Context, agents, Tuple.Create(group.Key.agentid, group?.FirstOrDefault().agentno), users, group.Key.operatorid, resp);
                    if (!resp.Success)
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        resp.Data.FailedNumbers_Log.AddRange(group.Select(x => x.userid));
                        continue;
                    }
                    if (!CheckAgent(group.Key.agentid, allbasmacentry, resp)) {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        resp.Data.FailedNumbers_Log.AddRange(group.Select(x => x.userid));
                        continue;
                    }
                    //检查转换数据
                    var datas = this.ConvertToDynsData(agentCtx, users, group.ToList(), roleInfo.ToList(), resp);
                    if (!resp.Success)
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        resp.Data.FailedNumbers_Log.AddRange(group.Select(x => x.userid));
                        continue;
                    }

                    // 本批处理的转换数据
                    if (datas != null && datas.Count > 0)
                    {
                        var tranids = datas.Select(x => Convert.ToString(x["ftranid"]));
                        var Numbers = datas.Select(x => Convert.ToString(x["fnumber"]));
                        var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                        prepareSaveDataService.PrepareDataEntity(agentCtx, this.HtmlForm, datas.ToArray(), OperateOption.Create());
                        var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, datas, "save", new Dictionary<string, object>());
                        if (result.IsSuccess)
                        {
                            resp.Data.SucceedNumbers.AddRange(tranids);
                            resp.Data.SucceedNumbers_Log.AddRange(Numbers);
                        }
                        else
                        {
                            resp.Success = false;
                            resp.Data.FailedNumbers.AddRange(tranids);
                            resp.Data.FailedNumbers_Log.AddRange(Numbers);
                            resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                        }
                    }
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            }

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            return resp;
        }

        //构造数据
        private List<DynamicObject> ConvertToDynsData(UserContext agentCtx, IEnumerable<DynamicObject> users, List<UserData> Datas, List<DynamicObject> roleInfo, BaseResponse<MuSiData> resp)
        {
            var dataMeta = this.MetaModelService.LoadFormModel(agentCtx, this.HtmlForm.Id);
            var dataMetaType = dataMeta.GetDynamicObjectType(agentCtx);

            //查询已经存在的数据
            var Ids = Datas.Where(x => !x.id.IsNullOrEmptyOrWhiteSpace()).Select(x => x.id);
            var exsitDatas = agentCtx.LoadBizDataByNo(this.HtmlForm.Id, "ftranid", Ids);

            // 所有的角色Ids
            var roleids = Datas?.Where(x => !x.roleid.IsNullOrEmptyOrWhiteSpace()).SelectMany(t =>
              {
                  return t.roleid?.Split(',');
              });
            var roleDatas = agentCtx.LoadBizDataByNo("sec_role", "ftranid", roleids);
            var roleDatasTop = this.TopContext.LoadBizDataByNo("sec_role", "ftranid", roleids);
            roleDatas.AddRange(roleDatasTop);

            var dataObjs = new List<DynamicObject>();
            foreach (var data in Datas)
            {
                if (data.roleid.IsNullOrEmptyOrWhiteSpace()) 
                {
                    resp.Data.ErrorMsgs.Add($@"当前修改的用户{data.userid}关联的角色roleid不能为空!"); 
                    continue;
                }  

                var dynObj = exsitDatas.Where(x => Convert.ToString(x["ftranid"]) == data.id).FirstOrDefault();
                if (dynObj == null)
                {
                    dynObj = dataMetaType.CreateInstance() as DynamicObject;
                    dynObj["ftranid"] = data.id;
                    dynObj["fphone"] = data.phone;
                    dynObj["fnumber"] = data.userid; 
                }
                var roleIds = Convert.ToString(dynObj["froleIds"]);
                if (!agentCtx.IsTopOrg)
                {
                    foreach (var roleId in roleInfo)
                    {
                        if (roleIds.Split(',').Any(f => f.EqualsIgnoreCase(Convert.ToString(roleId["fid"]))))
                        {
                            resp.Code = 400;
                            resp.Message = $@"当前修改的用户{data.userid}关联了【经销商角色】【系统管理员】【系统运维】，角色修改失败!";
                            resp.Success = false;
                            resp.Data.ErrorMsgs.Add(resp.Message);
                            break;
                        }
                    }
                }
                var queryRoles = roleDatas?.Where(x => data.roleid.Split(',').Contains(Convert.ToString(x["ftranid"])));
                if (queryRoles == null || !queryRoles.Any())
                {
                    resp.Code = 400;
                    resp.Message = $@"用户{data.userid}关联角色不能为空！";
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(resp.Message);
                    break;
                }
                dynObj["froleIds"] = queryRoles?.Select(x => Convert.ToString(x["Id"]))?.JoinEx(",", false);
                dynObj["froleids_txt"] = queryRoles?.Select(x => Convert.ToString(x["fname"]))?.JoinEx(",", false);
                dynObj["fname"] = data.name;
                dynObj["femail"] = data.email;
                dynObj["fqqid"] = data.qqid;
                dynObj["fwechatid"] = data.wechatid;
                dynObj["fisadmin"] = data.isadmin;
                dynObj["fdescription"] = data.description;

                var userId = users.Where(x => Convert.ToString(x["ftranid"]) == data.operatorid).Select(x => Convert.ToString(x["fid"]))?.FirstOrDefault();
                if (!userId.IsNullOrEmptyOrWhiteSpace())
                {
                    dynObj["fcreatorid"] = userId;
                    dynObj["fmodifierid"] = userId;
                }

                dataObjs.Add(dynObj);
            }
            return dataObjs;
        }

        private bool Valid(UserSyncDto dto, BaseResponse<MuSiData> resp)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);

                return false;
            }

            List<string> errorMsgs = new List<string>();
            foreach (var data in dto.Data)
            {
                if (data.id.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数id不能为空！");
                }
                //if (data.userid != data.phone)
                //{
                //    errorMsgs.Add("登录账号和手机号必须保持一致！");
                //}
                else if(data.name.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数name不能为空！");
                }
                else if (data.agentid.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数agentid不能为空！");
                }

                //如果二级组织不为空，说明是二级分销用户组织操作新增修改
                if (!data.secagentid.IsNullOrEmptyOrWhiteSpace())
                {
                    data.agentid = data.secagentid;
                    data.agentno = data.secagentno;
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();

                return false;
            }

            return true;
        }

        private bool CheckAgent(string agentId,List<string> allBasmacentry, BaseResponse<MuSiData> resp) {
            if (allBasmacentry.Contains(agentId)) {
                resp.Code = 400;
                resp.Message = "当前组织营业执照发生变更";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);
                return false;
            }
            return true;
        }
    }
}
