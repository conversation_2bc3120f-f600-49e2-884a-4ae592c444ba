using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO.EnumData;
using JieNor.AMS.YDJ.MS.API.DTO.HeadStatement;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MS.API.Controller.EnumData
{
    /// <summary>
    /// 省, 市, 区对应资料
    /// </summary>
    public class EnumDataListController : BaseController<EnumDataListDTO>
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(EnumDataListDTO dto)
        {
            var resp = new BaseResponse<object>();

            string strSql = "select t1.fname,t2.fentryid,t2.fenumitem from T_BD_ENUMDATA t1 with(nolock) join T_BD_ENUMDATAENTRY t2 with(nolock) on t1.fid = t2.fid where t1.fname in ('省', '市', '区')";

            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });

            resp.Data = data;
            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            return resp;
        }

    }
}