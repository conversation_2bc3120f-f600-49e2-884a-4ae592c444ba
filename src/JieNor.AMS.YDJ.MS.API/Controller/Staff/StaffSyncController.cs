using JieNor.AMS.YDJ.MS.API.DTO.Staff;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Validation;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.Staff
{
    /// <summary>
    /// 员工：同步接口
    /// </summary>
    public class StaffSyncController : BaseAuthController<StaffSyncDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "ydj_staff"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "id";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(StaffSyncDto dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<MuSiData>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();

            #region 查询相关数据
            // 根据外部Id查询所有的经销商Ids
            string sql = $@"select fid,ftranid,fstatus,fagentstatus from t_bas_agent with(nolock) where ftranid in ({dto.Data.Select(x => x.agentid)?.JoinEx(",", true)})";
            var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            //根据经销商id 查询主经销商配置表子经销商数据
            sql = $@"select fsubagentid from t_bas_mac tb0 with(nolock)
                                                    inner join t_bas_macentry tb1  with(nolock) on tb0.fid = tb1.fid
                                                          where tb1.fsubagentid  in ({agents.Select(x => Convert.ToString(x["fid"]))?.JoinEx(",", true)})";
            List<string> allbasmacentry = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>())?.Select(p => Convert.ToString(p["fsubagentid"])).ToList();
            // 根据外部Id查询所有的用户Ids
            sql = $@"select fid,ftranid from t_sec_user with(nolock) where ftranid in ({dto.Data.Select(x => x.operatorid)?.JoinEx(",", true)})";
            var users = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            #endregion
            foreach (var group in dto.Data.GroupBy(s => new { agentid = s.agentid, operatorid = s.operatorid }))
            {
                try
                {
                    //判断经销商和操作人
                    var agentCtx = MusiAuthValidation.GetUserContext(this.Context, agents, Tuple.Create(group.Key.agentid, group?.FirstOrDefault().agentno), users, group.Key.operatorid, resp);
                    if (!resp.Success)
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        resp.Data.FailedNumbers_Log.AddRange(group.Select(x => x.userid));
                        continue;
                    }
                    if (!CheckAgent(group.Key.agentid, allbasmacentry, resp))
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        resp.Data.FailedNumbers_Log.AddRange(group.Select(x => x.userid));
                        continue;
                    }
                    var datas = this.ConvertToDynsData(agentCtx,users, group.ToList(), resp);
                    if (!resp.Success)
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        resp.Data.FailedNumbers_Log.AddRange(group.Select(x => x.userid));
                        continue;
                    }

                    // 本批处理的转换数据
                    if (datas != null && datas.Count > 0)
                    {
                        var tranids = datas.Select(x => Convert.ToString(x["ftranid"]));
                        var Numbers = datas.Select(x => Convert.ToString(x["fnumber"]));
                        var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                        prepareSaveDataService.PrepareDataEntity(agentCtx, this.HtmlForm, datas.ToArray(), OperateOption.Create());

                        var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, datas, "save", new Dictionary<string, object>());
                        if (result.IsSuccess)
                        {
                            resp.Data.SucceedNumbers.AddRange(tranids);
                            resp.Data.SucceedNumbers_Log.AddRange(Numbers);
                        }
                        else
                        {
                            resp.Success = false;
                            resp.Data.FailedNumbers.AddRange(tranids);
                            resp.Data.FailedNumbers_Log.AddRange(Numbers);
                            resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                        }
                    }
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            }

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            return resp;
        }

        //构造数据
        private List<DynamicObject> ConvertToDynsData(UserContext agentCtx, IEnumerable<DynamicObject> users, List<StaffData> Datas, BaseResponse<MuSiData> resp)
        {
            var dataObjs = new List<DynamicObject>();

            var dataMeta = this.MetaModelService.LoadFormModel(agentCtx, this.HtmlForm.Id);
            var dataMetaType = dataMeta.GetDynamicObjectType(agentCtx);

            //查询已经存在的数据
            var Ids = Datas.Where(x => !x.id.IsNullOrEmptyOrWhiteSpace()).Select(x => x.id);
            var exsitDatas = agentCtx.LoadBizDataByNo(this.HtmlForm.Id, "ftranid", Ids);

            #region 查询相关数据
            // 所有的岗位Ids
            var positionids = Datas?.SelectMany(t =>
            {
                var _positionids = t.staffpositions
                .Select(x => x.positionid).Distinct();
                return _positionids;
            })?.ToList();
            var positionDatas = agentCtx.LoadBizDataByNo("ydj_position", "ftranid", positionids);
            if (positionDatas == null || positionDatas.Count ==0)
            {
                resp.Code = 400;
                resp.Message = $@"positionid岗位Id为空或未同步！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);
                return dataObjs;
            }

            // 所有的部门Ids
            var deptids = Datas?.SelectMany(t =>
            {
                var _deptids = t.staffpositions
                .Select(x => x.deptid).Distinct();
                return _deptids;
            })?.ToList();
            var deptDatas = agentCtx.LoadBizDataByNo("ydj_dept", "ftranid", deptids);
            if (deptDatas == null || deptDatas.Count == 0)
            {
                resp.Code = 400;
                resp.Message = $@"deptid部门Id为空或不存在！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);
                return dataObjs;
            }

            // 所有的关联用户Ids
            var userids = Datas?.Select(x=>x.userid)?.ToList();
            var userDatas = agentCtx.LoadBizDataByNo("sec_user", "ftranid", userids);
            #endregion

            
            foreach (var data in Datas)
            {
                var dynObj = exsitDatas.Where(x => Convert.ToString(x["ftranid"]) == data.id).FirstOrDefault();
                if (dynObj == null)
                {
                    dynObj = dataMetaType.CreateInstance() as DynamicObject;
                    dynObj["ftranid"] = data.id;
                    dynObj["fentrydate"] = DateTime.Now;//入职日期默认当天
                }
                if (!data.birthday.IsNullOrEmptyOrWhiteSpace())
                {
                    dynObj["fbirthday"] = Convert.ToDateTime(data.birthday);//生日
                }
                dynObj["fnumber"] = data.number;
                dynObj["fname"] = data.name;
                dynObj["fphone"] = data.phone;
                dynObj["fwechat"] = data.wechat;
                dynObj["faddress"] = data.address;
                dynObj["flinkuserid"] = userDatas?.Where(x => Convert.ToString(x["ftranid"]) == data.userid).Select(x => Convert.ToString(x["Id"])).FirstOrDefault();
                dynObj["fsex"] = "sex"+ data.sex;
                dynObj["fdescription"] = data.description;
                dynObj["femail"] = data.email;
                dynObj["fentrydate"] = data.entrydate;
                var userId = users.Where(x => Convert.ToString(x["ftranid"]) == data.operatorid).Select(x => Convert.ToString(x["fid"]))?.FirstOrDefault();
                if (!userId.IsNullOrEmptyOrWhiteSpace())
                {
                    dynObj["fcreatorid"] = userId;
                    dynObj["fmodifierid"] = userId;
                }
                var entity = dynObj["fentity"] as DynamicObjectCollection;               
                entity.Clear();
                foreach (var staffposition in data.staffpositions)
                {
                    //员工下的岗位全量覆盖
                    //var dynEntry = entity.FirstOrDefault(s => Convert.ToString(s["ftranid"]).EqualsIgnoreCase(staffposition.id));
                    //if (dynEntry == null)
                    //{
                    //    dynEntry = entity.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    //    dynEntry["ftranid"] = staffposition.id;
                    //    entity.Add(dynEntry);
                    //}
                    var dynEntry = entity.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                    dynEntry["ftranid"] = staffposition.id;
                    dynEntry["fpositionid"] = positionDatas?.Where(x => Convert.ToString(x["ftranid"]) == staffposition.positionid).Select(x => Convert.ToString(x["Id"])).FirstOrDefault();
                    dynEntry["fdeptid"] = deptDatas?.Where(x => Convert.ToString(x["ftranid"]) == staffposition.deptid).Select(x => Convert.ToString(x["Id"])).FirstOrDefault();
                    dynEntry["fbiztype"] = staffposition.type;
                    dynEntry["fisleader"] = staffposition.isleader;
                    dynEntry["fismain"] = staffposition.ismain;
                    entity.Add(dynEntry);
                }
                dataObjs.Add(dynObj);
            }
            return dataObjs;
        }

        private bool Valid(StaffSyncDto dto, BaseResponse<MuSiData> resp)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);

                return false;
            }

            List<string> errorMsgs = new List<string>();
            foreach (var data in dto.Data)
            {
                if (data.id.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数id不能为空！");
                }
                if (data.name.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数name不能为空！");
                }
                else if (data.agentid.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数agentid不能为空！");
                }
                //如果二级组织不为空，说明是二级分销用户组织操作新增修改
                if (!data.secagentid.IsNullOrEmptyOrWhiteSpace())
                {
                    data.agentid = data.secagentid;
                    data.agentno = data.secagentno;
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();

                return false;
            }

            return true;
        }

        private bool CheckAgent(string agentId, List<string> allBasmacentry, BaseResponse<MuSiData> resp)
        {
            if (allBasmacentry.Contains(agentId))
            {
                resp.Code = 400;
                resp.Message = "当前组织营业执照发生变更";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);
                return false;
            }
            return true;
        }
    }
}
