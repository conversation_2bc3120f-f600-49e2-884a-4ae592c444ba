
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.MS.API.Controller
{
    public class PushServiceController : BaseController<PushServiceDTO>
    {
        public string FormId
        {
            get { return "ydj_service"; }
        }



        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 经销商用户上下文
        /// </summary>
        protected UserContext AgentContext { get; set; }

        protected override bool IsAsync => true;

        protected override string UniquePrimaryKey => "fbillno";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(PushServiceDTO dto)
        {
            var resp = new BaseResponse<object>();
            try
            {
                this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);
                var dt = this.HtmlForm.GetDynamicObjectType(this.Context);
                var dataEntities = ConvertToDyns(dt, dto.Data/*,ref AgentNo*/);
                if (dataEntities.IsNullOrEmpty() || dataEntities.Any(x => (x?["fagentid"]).IsNullOrEmptyOrWhiteSpace()))
                {
                    resp.Code = 400;
                    resp.Message = $"存在【招商经销商】为空的数据，请补齐参数使【招商经销商】必录！";
                    resp.Success = false;
                    return resp;
                }
                else if (dataEntities.Select(x => Convert.ToString(x["fagentid"])).Distinct().Count() > 1)
                {
                    resp.Code = 400;
                    resp.Message = $"请保持传过来的数据所有招商经销商数据一致！";
                    resp.Success = false;
                    return resp;
                }
                var agentDic = this.Container.GetService<ICrmDistributorService>().GetAgentObjByCrmAgent(this.Context, dataEntities.Select(x => Convert.ToString(x["fagentid"]))?.ToList());
                if (agentDic == null || !agentDic.Any() || agentDic.First().Value.IsNullOrEmptyOrWhiteSpace())
                {
                    var agentDicKey = agentDic == null || !agentDic.Any() ? "" : agentDic.First().Key;
                    resp.Code = 400;
                    resp.Message = $"【招商经销商{ agentDicKey}】匹配不到售达方，请检查！";
                    resp.Success = false;
                    return resp;
                }

                //1.总部服务单下发接口调整：下发接口需同时下发【招商经销商编码】【建议售达方编码】。
                // 
                // 2.金蝶接收总部服务单接口判断逻辑调整：
                // 
                //     2.1.当金蝶接收到《总部服务单》时，先根据【招商经销商编码】在《招商经销商》档案中查找《售达方》。如果只有1个售达方，则取该售达方为目标经销商。如果存在多个售达方，则需查找该售达方是否有配置《主经销商配置表》：
                // 
                //         2.1.1.如果有配置，则取【主经销商】为目标经销商。
                // 
                //         2.1.2.如果没有配置，则直接取接口中下发的【建议售达方编码】作为目标经销商。
                // 
                //         2.1.3.如果没有配置，且接口中【建议售达方编码】也为空，则取第1个售达方作为目标经销商。

                var crmDistributorNumber = dataEntities.Select(x => Convert.ToString(x["fagentid"])).FirstOrDefault();

                string agentDicNumber = string.Empty;

                var queryAgentSql = $@"/*dialect*/  SELECT fnumber
                                                        FROM t_bas_agent
                                                        WHERE fid IN (
                                                            SELECT value
                                                            FROM (
                                                                SELECT 
                                                                    value = x.i.value('(./text())[1]', 'nvarchar(4000)')
                                                                FROM (
                                                                    SELECT CAST('<i>' + REPLACE(fagentid, ',', '</i><i>') + '</i>' AS XML) AS x
                                                                    FROM t_ms_crmdistributor
                                                                    WHERE fnumber = '{crmDistributorNumber}'
                                                                ) AS a
                                                                CROSS APPLY x.nodes('i') AS x(i)
                                                            ) AS b
                                                        ) and fforbidstatus!='1'";

                var agentObjs = this.DBService.ExecuteDynamicObject(this.Context, queryAgentSql);
                
                var agentno = dto?.AgentNo.Trim();  //接口下发【建议售达方编码】
                if (agentObjs.Count == 1 && agentDic.Count==1)
                {
                    agentDicNumber = agentDic.First().Value;  
                }
                else
                {
                    var hasMac = false;  //多个售达方中是否存在配有《主经销商配置表》的

                    foreach (var item in agentObjs)
                    {
                        var sql = $@"/*dialect*/ select t1.fnumber from t_bas_mac t0 with(nolock) inner join T_BAS_ORGANIZATION t1 with(nolock) on t0.fmainagentid=t1.fid where t1.fnumber='{item?["fnumber"]}'";
                        var macObj = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault();

                        if (!macObj.IsNullOrEmptyOrWhiteSpace())
                        {
                            agentDicNumber = Convert.ToString(macObj?["fnumber"]);
                            hasMac = true;
                            break; //遍历售达方时，当前售达方有配置《主经销商配置表》时，则取【主经销商】为目标经销商，这时就不用继续遍历了
                        }
                    }

                    //多个售达方中不存在存在配有《主经销商配置表》的
                    if (!hasMac)
                    {
                        if (!agentno.IsNullOrEmptyOrWhiteSpace())
                            agentDicNumber = agentno;
                        else
                            agentDicNumber = agentDic.First().Value;
                    }
                }

                this.AgentContext = this.Context.CreateAgentDBContextByNo(agentDicNumber);

                //var AgentNo = string.Empty;

                var imageMaxNos = dataEntities.Where(x => (Convert.ToString(x["fimage"]).Split(',').Length + Convert.ToString(x["fztimage"]).Split('|').Length) > 9).Select(x => x["fbillno"]).ToList();
                if (imageMaxNos != null && imageMaxNos.Any())
                {
                    resp.Code = 400;
                    resp.Message = $"单据【{string.Join("、", imageMaxNos)}】中问题图片附件(总部图片+经销商图片总张数)最多9张！";
                    resp.Success = false;
                    return resp;
                }

                if (dataEntities.Any(x => x["fcusphone"].IsNullOrEmptyOrWhiteSpace()))
                {
                    resp.Code = 400;
                    resp.Message = $"上传的单据中存在客户手机号为空的数据，请检查！";
                    resp.Success = false;
                    return resp;
                }

                if (dataEntities.Any(x => Convert.ToString(x["fservicetype"]).Trim().Equals("售后") && x["fquestiontype"].IsNullOrEmptyOrWhiteSpace()))
                {
                    resp.Code = 400;
                    resp.Message = $"上传的单据中存在服务类型=售后且问题类别为空的数据，请检查！";
                    resp.Success = false;
                    return resp;
                }

                if (dataEntities.Any(x => Convert.ToString(x["fservicetype"]).Trim().Equals("售后") && x["fquestiondesc"].IsNullOrEmptyOrWhiteSpace()))
                {
                    resp.Code = 400;
                    resp.Message = $"上传的单据中存在服务类型=售后且问题描述为空的数据，请检查！";
                    resp.Success = false;
                    return resp;
                }

                bool isResponse = CreateOrUpdateCusInfo(dataEntities, resp);
                if (isResponse)
                {
                    return resp;
                }

                var allBillNos = dataEntities.Where(x => !x["fbillno"].IsNullOrEmptyOrWhiteSpace()).Select(x => x["fbillno"].ToString()).ToList();
                StringBuilder sqlWhere = null;
                List<SqlParam> parm = null;
                List<DynamicObject> allBills = null;
                if (allBillNos != null && allBillNos.Any())
                {
                    allBillNos.Distinct();
                    sqlWhere = new StringBuilder();
                    parm = new List<SqlParam>();
                    sqlWhere.Append(string.Join(",", allBillNos.Select((x, i) => $"@fbillno{i}")));
                    parm.AddRange(allBillNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
                    allBills = this.AgentContext.LoadBizBillHeadDataByACLFilter(this.FormId, $"fbillno in ({sqlWhere})", "fbillno,fprovince,fcity,fregion,fcollectadd,fsourcecancl,fserstatus", parm);
                }
                HashSet<string> errorMsgs = new HashSet<string>();
                foreach (var item in dataEntities)
                {
                    string fbillno = Convert.ToString(item["fbillno"]);
                    var bill = allBills?.FirstOrDefault(x => x["fbillno"].Equals(fbillno));
                    item["fsourcecancl"] = "0";
                    item["fcustomername"] = string.Empty;
                    item["fcusphone"] = string.Empty;
                    //item["fagentid"] = dto.AgentNo;
                    //item["fauthcity"] = dto.AuthCityNo;
                    item["fzbcollectadd"] = Convert.ToString(item["fprovince"]).Trim() +
                                            Convert.ToString(item["fcity"]).Trim() +
                                            Convert.ToString(item["fregion"]).Trim() +
                                            Convert.ToString(item["fcollectadd"]).Trim();

                    switch (Convert.ToString(item["freservtype"]).Trim())
                    {
                        case "自约":
                            item["freservtype"] = "0";
                            break;
                        case "代约":
                            item["freservtype"] = "1";
                            break;

                    }
                    //根据服务类型自动设置单据类型
                    switch (Convert.ToString(item["fservicetype"]).Trim())
                    {
                        case "送装":
                            item["fbilltypeid"] = "ydj_service_billtype_02";
                            break;
                        case "增值":
                            item["fbilltypeid"] = "ydj_service_billtype_03";
                            break;
                        case "售后":
                            item["fbilltypeid"] = "ydj_service_billtype_04";
                            break;
                        default:
                            item["fbilltypeid"] = "ydj_service_billtype_03";
                            if (Convert.ToString(item["fservicetype"]).Trim().IsNullOrEmptyOrWhiteSpace())
                            {
                                item["fservicetype"] = "增值";
                            }
                            break;
                    }

                    if (Convert.ToBoolean(item["fcancelstatus"]))
                    {
                        item["fcancelid"] = this.AgentContext.UserId;
                        item["fcanceldate"] = DateTime.Now;
                    }
                    else
                    {
                        item["fcancelid"] = string.Empty;
                        item["fcanceldate"] = null;
                    }
                    if (bill != null)
                    {

                        //item["fquestionimage"] = bill["fquestionimage"];

                        //if (!bill["fdutycustomerid"].IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    item["fdutycustomerid"] = bill["fdutycustomerid"];
                        //    item["finstitutiontype"] = "客户";
                        //}
                        //else if (!bill["fdutystaffid"].IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    item["fdutystaffid"] = bill["fdutystaffid"];
                        //    item["finstitutiontype"] = "员工";
                        //}
                        //else if (!bill["fdutydeptid"].IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    item["fdutydeptid"] = bill["fdutydeptid"];
                        //    item["finstitutiontype"] = "部门";
                        //}
                        //else if (!bill["fdutysupplierid"].IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    item["fdutysupplierid"] = bill["fdutysupplierid"];
                        //    item["finstitutiontype"] = "供应商";
                        //}
                        item["fsourcecancl"] = bill["fsourcecancl"];

                        //item["fhandleconclusion"] = bill["fhandleconclusion"];
                        //item["fscheme"] = bill["fscheme"];

                        item["fsourcecancl"] = bill["fsourcecancl"];
                        //目前只能做取消操作，这里判断前后状态是否一致，不一致则认为是取消操作
                        //以oper标识为oper操作
                        var fserstatus = Convert.ToString(item["fserstatus"]);
                        //这里中台说已取消的不会下发了，那么我们这只要收到中台下发的已取消的就默认发通知了，
                        //后续如需调整则新增服务状态为主动取消，并比对即可识别
                        var operStatus = "已取消";
                        //var cancelStatus = new HashSet<string> { "sersta01", "sersta02", "sersta03"};
                        if (fserstatus == operStatus)
                        {
                            //if (!cancelStatus.Contains(Convert.ToString(bill["fserstatus"])))
                            //{
                            //    errorMsgs.Add(fbillno);
                            //    continue;
                            //}
                            if (Convert.ToString(bill["fserstatus"]) == "sersta06")
                            {
                                item["fserstatus"] = "sersta06";
                            }
                            else
                            {
                                item["fserstatus"] = "sersta06";
                                item["fcancelreason"] = item["fcancelreason"].IsNullOrEmptyOrWhiteSpace() ? "客户确认取消！" : item["fcancelreason"];
                            }
                        }
                        else
                        {
                            item["fserstatus"] = bill["fserstatus"];
                        }
                        //item["fprovince"] = bill["fprovince"];
                        //item["fcity"] = bill["fcity"];
                        //item["fregion"] = bill["fregion"];
                        //item["fcollectadd"] = bill["fcollectadd"];
                    }
                    else
                    {
                        //金蝶没有的单据，处理中台已取消的会下发的问题
                        IEnumerable<DynamicObject> serbaseData = GetEnumAllList();
                        var fserstatus = serbaseData.Where(x => Convert.ToString(x["fenumitem"]).Contains(Convert.ToString(item["fserstatus"]).Trim()) && x["fid"].Equals("4ad9d1898c0442f5b2829466b2da1161")).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();

                        if (!fserstatus.IsNullOrEmptyOrWhiteSpace())
                        {
                            item["fserstatus"] = fserstatus;
                        }
                        else
                        {
                            item["fserstatus"] = "sersta01";
                        }

                        //item["fprovince"] = string.Empty;
                        //item["fcity"] = string.Empty;
                        //item["fregion"] = string.Empty;
                        item["fcollectadd"] = item["fzbcollectadd"];
                    }
                }
                if (errorMsgs != null && errorMsgs.Any())
                {
                    resp.Code = 400;
                    resp.Message = $"单据【" + string.Join("\r\n", errorMsgs) + $"】中，服务单状态不为【待派单、待预约、待完工】，不允许取消服务";
                    resp.Success = false;
                    return false;
                }
                var simplePara = new Dictionary<string, string>();
                simplePara.Add("RequestRawUrl", this.Request.RawUrl);
                simplePara.Add("__RawData__", dto.ToJson());
                var response = this.HttpGateway.InvokeLocal<CommonBillDTOResponse>(this.AgentContext, new CommonBillDTO()
                {
                    FormId = this.FormId,
                    OperationNo = "MSSaveSync",
                    BillData = dataEntities.ToJson(),
                    SimpleData = simplePara
                });

                resp = response?.OperationResult.ToResponseModel<object>();
                var srvData = response?.OperationResult?.SrvData?.ToJson() ?? "{}";
                resp.Data = JToken.Parse(srvData);
            }
            catch (Exception e)
            {
                resp.Code = 500;
                resp.Message = e.Message;
                resp.Success = false;
            }
            return resp;
        }

        private IEnumerable<DynamicObject> ConvertToDyns(DynamicObjectType dt, JArray data/*,ref string AgentNo*/)
        {
            if (data.IsNullOrEmpty()) return new List<DynamicObject>();

            List<DynamicObject> dyns = new List<DynamicObject>();

            foreach (JObject item in data)
            {
                dyns.Add(ConvertToDyn(dt, item/*, ref AgentNo*/));
            }

            return dyns;
        }

        private DynamicObject ConvertToDyn(DynamicObjectType dt, JObject data/*, ref string AgentNo*/)
        {
            var dyn = dt.CreateInstance() as DynamicObject;

            foreach (var property in data.Properties())
            {
                //if(property.Name== "fagentid")
                //{
                //    AgentNo = property.Value.ToString().Split(',').FirstOrDefault();
                //}
                if (property.Value is JArray)
                {
                    var entryEntity = this.HtmlForm.GetEntryEntity(property.Name);
                    if (entryEntity == null) continue;

                    var entrys = dyn[entryEntity.PropertyName] as DynamicObjectCollection;

                    var dyns = ConvertToDyns(entryEntity.DynamicObjectType, (JArray)property.Value/*,ref AgentNo*/);

                    foreach (var item in dyns)
                    {
                        entrys.Add(item);
                    }

                    continue;
                }

                if (property.Value is JObject)
                {
                    continue;
                }

                var field = this.HtmlForm.GetField(property.Name);
                if (field == null) continue;

                dyn[field.PropertyName] = property.Value;
            }

            return dyn;
        }
        /// <summary>
        /// 根据手机号判断客户是否存在，不存在则创建
        /// </summary>
        /// <param name="dataEntities"></param>
        private bool CreateOrUpdateCusInfo(IEnumerable<DynamicObject> dataEntities, BaseResponse<object> resp)
        {
            bool response = false;
            var cusValidNos = dataEntities.Where(x => !x["fcustomerid"].IsNullOrEmptyOrWhiteSpace())?.Select(x => Convert.ToString(x["fcustomerid"]));
            IEnumerable<string> existsCusNos = null;
            if (cusValidNos != null && cusValidNos.Any())
            {
                existsCusNos = GetExistsCusNos(AgentContext, cusValidNos).Select(x => Convert.ToString(x["fnumber"]));
            }
            var cusValidPhones = dataEntities.Where(x =>
            {
                if (existsCusNos == null || !existsCusNos.Any()) return true;
                return !existsCusNos.Contains(Convert.ToString(x["fcustomerid"]));
            })?.Select(x => Convert.ToString(x["fcusphone"]));
            Dictionary<string, string> cusPhone2NumDic = new Dictionary<string, string>();
            if (cusValidPhones != null && cusValidPhones.Any())
            {
                cusPhone2NumDic = GetExistsCusPhones(AgentContext, cusValidPhones);
            }
            var noExistsCusInfo = dataEntities.Where(x =>
            {
                bool result = true;
                if (existsCusNos != null)
                {
                    result = !existsCusNos.Contains(Convert.ToString(x["fcustomerid"]));
                    if (!result) return result;
                }
                if (cusPhone2NumDic != null && cusPhone2NumDic.Any())
                {
                    result = !cusPhone2NumDic.Keys.Contains(Convert.ToString(x["fcusphone"]));
                }
                return result;
            });

            if (noExistsCusInfo != null && noExistsCusInfo.Any())
            {
                var cusForm = this.MetaModelService.LoadFormModel(AgentContext, "ydj_customer");
                DynamicObjectType cusDType = cusForm.GetDynamicObjectType(AgentContext);
                List<DynamicObject> newCusData = new List<DynamicObject>();
                HashSet<string> existsPhone = new HashSet<string>();
                IEnumerable<DynamicObject> baseData = GetEnumAllList();
                foreach (var item in noExistsCusInfo)
                {
                    if (!existsPhone.Contains(Convert.ToString(item["fcusphone"])))//避免手机号重复创建对应客户
                    {
                        var fprovinceid = baseData.Where(x => Convert.ToString(x["fenumitem"]).Contains(Convert.ToString(item["fprovince"])) && x["fid"].Equals("4ce1b6b47093415585878493ec1ad98b")).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        var fcityid = baseData.Where(x => Convert.ToString(x["fenumitem"]).Contains(Convert.ToString(item["fcity"])) && x["fid"].Equals("6916ea2d44f24379a377cca5362bd7cf")).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        var fregionid = baseData.Where(x => Convert.ToString(x["fenumitem"]).Contains(Convert.ToString(item["fregion"])) && x["fid"].Equals("208b665d355d446f890fcc46568a3784")).Select(x => Convert.ToString(x["fentryid"])).FirstOrDefault();
                        //if(fprovinceid.IsNullOrEmpty())
                        //{
                        //    resp.Code = 400;
                        //    resp.Message = $"单据【{item["fbillno"]}】中省【{item["fprovince"]}】未同步或者为空！";
                        //    resp.Success = false;
                        //    return true;
                        //}
                        //if (fcityid.IsNullOrEmpty())
                        //{
                        //    resp.Code = 400;
                        //    resp.Message = $"单据【{item["fbillno"]}】中市【{item["fcity"]}】未同步或者为空！";
                        //    resp.Success = false;
                        //    return true;
                        //}
                        //if (fregionid.IsNullOrEmpty())
                        //{
                        //    resp.Code = 400;
                        //    resp.Message = $"单据【{item["fbillno"]}】中区【{item["fregion"]}】未同步或者为空！";
                        //    resp.Success = false;
                        //    return true;
                        //}
                        var cusInfo = cusDType.CreateInstance() as DynamicObject;
                        cusInfo["fphone"] = item["fcusphone"];
                        cusInfo["fcontacts"] = item["fcollectrel"];
                        cusInfo["fname"] = item["fcustomername"].IsNullOrEmptyOrWhiteSpace() ? item["fcollectrel"] : item["fcustomername"];
                        cusInfo["fprovince"] = fprovinceid;
                        if (!fprovinceid.IsNullOrEmptyOrWhiteSpace())
                        {
                            cusInfo["fcity"] = fcityid;
                        }
                        if (!fcityid.IsNullOrEmptyOrWhiteSpace())
                        {
                            cusInfo["fregion"] = fregionid;
                        }
                        cusInfo["faddress"] = item["fcollectadd"];
                        if (Convert.ToBoolean(item["fisdonecus"]))
                        {
                            cusInfo["fcusnature"] = "cusnature_02";
                        }
                        else
                        {
                            cusInfo["fcusnature"] = "cusnature_00";
                        }
                        cusInfo["fsource"] = "164743697441886214";//金管家增值服务
                        cusInfo["fgeneratesource"] = "总部下发服务单接口";

                        var ContactEntry = cusInfo["fcuscontacttry"] as DynamicObjectCollection;
                        var defaultuscontacttry = new DynamicObject(cusForm.GetEntryEntity("fcuscontacttry").DynamicObjectType);
                        defaultuscontacttry["fseq"] = 1;
                        defaultuscontacttry["fcontacter"] = cusInfo["fname"];
                        defaultuscontacttry["fphone"] = item["fcusphone"];
                        defaultuscontacttry["fprovince"] = fprovinceid;
                        if (!fprovinceid.IsNullOrEmptyOrWhiteSpace())
                        {
                            defaultuscontacttry["fcity"] = fcityid;
                        }
                        if (!fcityid.IsNullOrEmptyOrWhiteSpace())
                        {
                            defaultuscontacttry["fregion"] = fregionid;
                        }
                        defaultuscontacttry["faddress"] = item["fcollectadd"];
                        //defaultuscontacttry["fcdescription"] = dataEntity["fdescription"];
                        defaultuscontacttry["fisdefault"] = 1;
                        defaultuscontacttry["fcisfirst"] = 1;
                        ContactEntry.Add(defaultuscontacttry);
                        if (Convert.ToString(item["fcusphone"]) != Convert.ToString(item["fcollectpho"]))
                        {
                            var contacttry = new DynamicObject(cusForm.GetEntryEntity("fcuscontacttry").DynamicObjectType);
                            contacttry["fseq"] = 2;
                            contacttry["fcontacter"] = item["fcollectrel"];
                            contacttry["fphone"] = item["fcollectpho"];
                            contacttry["fprovince"] = fprovinceid;
                            if (!fprovinceid.IsNullOrEmptyOrWhiteSpace())
                            {
                                contacttry["fcity"] = fcityid;
                            }
                            if (!fcityid.IsNullOrEmptyOrWhiteSpace())
                            {
                                contacttry["fregion"] = fregionid;
                            }
                            contacttry["faddress"] = item["fcollectadd"];
                            //contacttry["fcdescription"] = dataEntity["fdescription"];
                            contacttry["fisdefault"] = 0;
                            contacttry["fcisfirst"] = 0;
                            ContactEntry.Add(contacttry);
                        }
                        newCusData.Add(cusInfo);
                        existsPhone.Add(Convert.ToString(item["fcusphone"]));
                    }
                }
                // 填充字段默认值
                var defCalulator = this.Container.GetService<IDefaultValueCalculator>();
                defCalulator.Execute(this.AgentContext, cusForm, newCusData.ToArray());

                // 保存前预处理
                var preService = this.Container.GetService<IPrepareSaveDataService>();
                preService.PrepareDataEntity(this.AgentContext, cusForm, newCusData.ToArray(), OperateOption.Create());
                //AgentContext.SaveBizData(cusForm.Id, newCusData);
                var cusResult = this.HttpGateway.InvokeBillOperation(this.AgentContext, cusForm.Id, newCusData, "save", new Dictionary<string, object>());
                if (!cusResult.IsSuccess)
                {
                    resp.Code = 400;
                    resp.Message = "自动新建时客户出现以下异常提醒：" + cusResult.GetMessage();
                    resp.Success = false;
                    return true;
                }
                var dic = from a in existsPhone
                          join b in newCusData on a equals Convert.ToString(b["fphone"])
                          select new { phone = a, cusnum = Convert.ToString(b["fnumber"]) };
                foreach (var item in dic)
                {
                    if (!cusPhone2NumDic.Keys.Contains(item.phone))
                    {
                        cusPhone2NumDic.Add(item.phone, item.cusnum);
                    }
                }
            }
            foreach (var item in dataEntities.Where(x => existsCusNos == null || !existsCusNos.Contains(Convert.ToString(x["fcustomerid"]))))
            {
                item["fcustomerid"] = cusPhone2NumDic[Convert.ToString(item["fcusphone"])];
            }
            return response;
        }

        /// <summary>
        /// 根据编码判断客户是否存在于这个经销商
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="existsCusValidNos"></param>
        /// <returns></returns>
        public List<DynamicObject> GetExistsCusNos(UserContext userCtx, IEnumerable<string> existsCusValidNos)
        {
            List<DynamicObject> data = new List<DynamicObject>();

            if (existsCusValidNos.IsNullOrEmpty() || !existsCusValidNos.Any()) return data;
            var sqlText = string.Empty;
            var tempTable = string.Empty;
            IDBService dbService = null;

            if (existsCusValidNos.Count() > 50)
            {
                if (dbService == null)
                {
                    dbService = userCtx.Container.GetService<IDBService>();
                }
                //用临时表关联查询
                tempTable = dbService.CreateTempTableWithDataList(userCtx, existsCusValidNos);
                sqlText = $@"select t1.fnumber from t_ydj_customer t1 with(nolock)
                                           inner join {tempTable} tt with(nolock) on tt.fid = t1.fnumber
                                           where t1.fmainorgid='{userCtx.Company}' and t1.fforbidstatus='0'";

            }
            else
            {
                sqlText = $@"select t1.fnumber from t_ydj_customer t1 with(nolock)
                            where t1.fnumber in('" + string.Join("','", existsCusValidNos) + $"')  and t1.fforbidstatus='0' and t1.fmainorgid='{userCtx.Company}'";
            }

            var result = userCtx.ExecuteDynamicObject(sqlText, null).ToList();
            if (tempTable != string.Empty)
            {
                this.DBService.DeleteTempTableByName(this.AgentContext, tempTable, true);
            }
            return result;
        }
        /// <summary>
        /// 根据手机号判断客户是否存在于这个经销商
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="existsCusValidPhones"></param>
        /// <returns></returns>
        public Dictionary<string, string> GetExistsCusPhones(UserContext userCtx, IEnumerable<string> existsCusValidPhones)
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();

            if (existsCusValidPhones.IsNullOrEmpty() || !existsCusValidPhones.Any()) return dic;
            var sqlText = string.Empty;
            var tempTable = string.Empty;
            IDBService dbService = null;

            if (existsCusValidPhones.Count() > 50)
            {
                if (dbService == null)
                {
                    dbService = userCtx.Container.GetService<IDBService>();
                }
                //用临时表关联查询
                tempTable = dbService.CreateTempTableWithDataList(userCtx, existsCusValidPhones);
                sqlText = $@"select t1.fnumber,t1.fphone from t_ydj_customer t1 with(nolock)
                                           inner join {tempTable} tt with(nolock) on tt.fid = t1.fphone
                                           where t1.fmainorgid='{userCtx.Company}' and t1.fforbidstatus='0'";
            }
            else
            {
                sqlText = $@"select t1.fnumber,t1.fphone from t_ydj_customer t1 with(nolock)
                            where t1.fphone in('" + string.Join("','", existsCusValidPhones) + $"')  and t1.fforbidstatus='0' and t1.fmainorgid='{userCtx.Company}'";
            }
            var result = userCtx.ExecuteDynamicObject(sqlText, null).ToList();
            if (tempTable != string.Empty)
            {
                this.DBService.DeleteTempTableByName(this.AgentContext, tempTable, true);
            }
            foreach (var item in result)
            {
                var key = Convert.ToString(item["fphone"]);
                if (!dic.ContainsKey(key))
                {
                    dic.Add(key, Convert.ToString(item["fnumber"]));
                }
            }

            return dic;
        }
        /// <summary>
        /// 得到省市区基本资料、服务状态辅助资料
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetEnumAllList()
        {
            string strSql = "select fid,fentryid,fenumitem from T_BD_ENUMDATAENTRY  with(nolock) where fid in('4ce1b6b47093415585878493ec1ad98b','6916ea2d44f24379a377cca5362bd7cf','208b665d355d446f890fcc46568a3784','4ad9d1898c0442f5b2829466b2da1161')";
            var data = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return data;
        }

        protected override Dictionary<string, string> CreateDistributedLocks(PushServiceDTO dto)
        {
            var hash = dto.ToJson().HashString();
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{hash}", $"服务单 {hash} 正在锁定中，请稍后再操作！" }
            };
        }
    }

}
