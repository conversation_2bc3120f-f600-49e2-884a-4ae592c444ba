using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MS.API.Plugin
{
    public class BeforeMapFieldEventArgs : CancelEventArgs
    {
        public JProperty Property { get; set; }

        public JObject Data { get; set; }

        public DynamicObject DataEntity { get; set; }
    }
}
