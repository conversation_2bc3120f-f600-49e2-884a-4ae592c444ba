using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MS.API.Plugin.HeadStatement
{
    /// <summary>
    /// 总部对账单：保存
    /// </summary>
    [InjectService]
    [FormId("ydj_headstatement")]
    [OperationNo("MSCancel")]
    public class MSCancel : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            var dataStr = this.GetQueryOrSimpleParam("data", "");
            JArray billNos = null;
            try
            {
                billNos = JArray.Parse(dataStr);
            }
            catch (Exception ex)
            {
                throw new BusinessException("参数格式不正确！");
            }

            if (billNos.Any())
            {
                var bills = this.Context.LoadBizDataByNo(this.HtmlForm.Id, "fbillno", billNos.Select(s => s.ToString()));
                if (bills.Any())
                {
                    var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, bills, "Cancel", new Dictionary<string, object>());
                    result.ThrowIfHasError(true, $"{this.HtmlForm.Caption}作废失败！");
                }
            }

            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "操作成功！";
        }
    }
}
