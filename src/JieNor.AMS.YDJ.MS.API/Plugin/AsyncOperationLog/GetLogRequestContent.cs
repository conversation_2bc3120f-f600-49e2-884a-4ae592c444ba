using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Plugin.AsyncOperationLog
{
    /// <summary>
    /// 异步操作日志：获取请求报文
    /// </summary>
    [InjectService]
    [FormId("si_asyncoperationlog")]
    [OperationNo("getlogrequestcontent")]
    public class GetLogRequestContent : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            string logId = this.GetQueryOrSimpleParam<string>("logId");

            if (logId.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            var topCtx = this.Context.CreateTopOrgDBContext();
            var slaveCtx = topCtx.CreateSlaveDBContext();

            var sql = $"SELECT freqcontent FROM T_SI_ASYNCOPERATIONLOG WITH(NOLOCK) WHERE fid='{logId}'";
            var logContentObj = slaveCtx.ExecuteDynamicObject(sql, null);
            string result = "";
            if (logContentObj != null && logContentObj.Any())
            {
                result = Convert.ToString(logContentObj.First()["freqcontent"]);
            }

            this.Result.IsSuccess = true;
            this.Result.SrvData = result;
        }
    }
}
