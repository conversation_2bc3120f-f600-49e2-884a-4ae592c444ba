using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Model
{
    public class MuSiData
    {
        /// <summary>
        /// 成功编码
        /// </summary>
        public List<string> SucceedNumbers { get; set; } = new List<string>();
        /// <summary>
        /// 失败编码
        /// </summary>
        public List<string> FailedNumbers { get; set; } = new List<string>();

        /// <summary>
        /// 成功编码-集成操作日志记录(应对前端需要根据编码名称去查数据，但是接口又要根据id返回唯一标识)
        /// </summary>
        public List<string> SucceedNumbers_Log { get; set; } = new List<string>();
        /// <summary>
        /// 失败编码-集成操作日志记录
        /// </summary>
        public List<string> FailedNumbers_Log { get; set; } = new List<string>();

        public List<string> ErrorMsgs { get; set; } = new List<string>();

        public string Flag { get; set; }
    }

    public enum MuSiFlag
    {
        /// <summary>
        /// 全部成功
        /// </summary>
        SUCCESS,
        /// <summary>
        /// 全部失败
        /// </summary>
        FAIL,
        /// <summary>
        /// 部分成功
        /// </summary>
        PARTSUCCESS,
        /// <summary>
        /// 异常
        /// </summary>
        ERROR
    }
}
