using JieNor.AMS.YDJ.MS.API.DTO.DeliveryOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.SystemIntegration;
using Newtonsoft.Json.Linq;
using ServiceStack;
using ServiceStack.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Filter
{
    /// <summary>
    /// 重复请求过滤器
    /// </summary>
    public class RepeatedRequestFilter : BaseRequestFilter
    {
        /// <summary>
        /// 单据标识
        /// </summary>
        protected string FormId { get; set; }

        /// <summary>
        /// 缓存生命周期（单位：秒）
        /// </summary>
        protected double ExpirySeconds { get; set; }

        /// <summary>
        /// 操作名称
        /// </summary>
        protected string OpName { get; set; }

        /// <summary>
        /// 重复请求过滤器
        /// </summary>
        /// <param name="formId">单据标识</param>
        /// <param name="seconds">重复请求限制时间（单位：秒）</param>
        /// <param name="opname">操作名称（用于记录日志）</param>
        public RepeatedRequestFilter(string formId, double seconds, string opname = "")
        {
            FormId = formId;
            ExpirySeconds = seconds;
            OpName = opname;
        }

        public override void Execute(IRequest req, IResponse res, object requestDto)
        {
            base.Execute(req, res, requestDto);

            // 先到缓存里找
            var hash = requestDto.ToJson().HashString();
            var cacheKey = GetCacheKey(hash);
            var cacheValue = this.CacheClient.Get<string>(cacheKey);

            var resp = new BaseResponse<object>();
            if (cacheValue.IsNullOrEmptyOrWhiteSpace())
            {
                // 如果缓存里没有，就生成一个
                // 获取流水号
                ISequenceService seqSvc = this.Container.GetService<ISequenceService>();
                var tranId = seqSvc.GetSequence<string>();

                // 缓存起来
                this.CacheClient.Set(cacheKey, tranId, TimeSpan.FromSeconds(ExpirySeconds));
                
                //resp.Success = true;
                //resp.Message = "系统正在处理！";

                //res.ContentType = "application/json; charset=utf-8";
                //res.Write(resp.ToJson());
                //res.EndRequest(false);
            }
            else
            {
                if (requestDto is DeliveryOrderDTO delivery)
                {
                    //交货单下发接口
                    //记录日志
                    //同步日志对象
                    var loghtmlForm = this.MetaModelService.LoadFormModel(this.Context, "si_operationlog");
                    var systemIntegrationService = this.Container.GetService<ISystemIntegrationService>();
                    var opLogObj = systemIntegrationService.CreateOperationLog(this.Context, loghtmlForm, this.Request.AbsoluteUri, OpName, FormId, "2", "慕思中台调用当前系统接口");
                    opLogObj["fopstatus"] = "2";
                    systemIntegrationService.WriteOperationLog(this.Context, opLogObj, $"请求参数：{requestDto.ToJson()}");
                    systemIntegrationService.SaveOperationLog(this.Context, loghtmlForm, new[] { opLogObj });

                    var successbills = new List<string>();//成功的交货单
                    var errbills = new List<string>();//失败的交货单
                    foreach (var data in delivery.Head)
                    {
                        if (!errbills.Contains(data.ReceiveNum)) errbills.Add(data.ReceiveNum);
                    }
                    JObject resdata = new JObject();
                    resdata["successbills"] = string.Join(",", successbills);
                    resdata["errorbills"] = string.Join(",", errbills);
                    resp.Data = resdata;
                }
                // 如果缓存里有，证明前一个请求缓存还在，返回重复请求提示
                resp.Message = "重复数据请求过于频繁，稍后再试！";

                res.StatusCode = (int)HttpStatusCode.BadRequest;
                res.StatusDescription = "重复数据请求过于频繁，稍后再试！";
                res.ContentType = "application/json; charset=utf-8";
                res.Write(resp.ToJson());
                res.EndRequest(false);
            }
        }
        private string GetCacheKey(string hash)
        {
            string cacheKey = $"MSAPI:RepeatedRequest:{this.Context.Company}:{FormId}:{hash}";

            return cacheKey;
        }
    }
}
