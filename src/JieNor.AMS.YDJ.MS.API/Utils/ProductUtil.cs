using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MS.API.DTO;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MS.API.Utils
{
    public class ProductUtil
    {
        /// <summary>
        /// 填充属性和属性值
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productId">商品id</param>
        /// <param name="auxPropVals">辅助属性</param>
        private static void FillPropAndPropValue(UserContext userCtx, string productId, List<AuxPropValDTO> auxPropVals)
        {
            /* feat#4229 SAP手工单针对非标商品的属性自动创建
             * 现在逻辑改成只要是SAP下发的手工单, 如果没有已存在的属性值, 系统都自动进行创建成非标录入的那种属性, 也就是前端选不到的那种 (非标库也选不到), 但又可以赋值到对应的属性上的那种, 并且直接拿中台接口中的编码与名称来生成即可 , 保证不会再出现没有属性值不存在的报错, 也就是不论是标准还是非标的商品都要生成这种非标录入的属性值, 然后赋值到对应的属性上
             */

            if (auxPropVals == null || auxPropVals.Count == 0) return;

            var product = userCtx.LoadBizDataById("ydj_product", productId);

            var errorMsgs = new List<string>();

            // 填充属性
            FillProp(userCtx, auxPropVals, errorMsgs);
            if (errorMsgs.Any()) throw new BusinessException(string.Join("\r\n", errorMsgs));

            // 1) 先取标准的属性值 来赋值
            FillStdPropValue(userCtx, auxPropVals);

            // 2) 如果取不到时, 属性勾选上 "支持非标库" 时, 就取非标的属性值 来赋值
            FillUnstdPropValueInLib(userCtx, product, auxPropVals);

            // 3) 如果还是取不到, 先去取看看有没有对应非标录入的属性值 (后台有个标识),  没有的话系统会自动创建属性值, 并且是手工非标录入会生成的那种属性值 (后台有个标识)
            FillUnstdPropValueInCreate(userCtx, product, auxPropVals);

            // 4) 如果都还是不满足上面的情况还取不到值, 就报错终止
            foreach (var auxPropVal in auxPropVals)
            {
                if (auxPropVal.ValueId.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add($"属性【{auxPropVal.PropNo}】下的属性值【{auxPropVal.PropValueNo}】不存在！");
                }
            }
            if (errorMsgs.Any()) throw new BusinessException(string.Join("\r\n", errorMsgs));
        }

        /// <summary>
        /// 填充属性
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        /// <param name="errorMsgs"></param>
        private static void FillProp(UserContext userCtx, List<AuxPropValDTO> auxPropVals, List<string> errorMsgs)
        {
            List<DynamicObject> props = userCtx.LoadBizDataByNo("sel_prop", "fnumber",
                auxPropVals.Select(s => s.PropNo).Where(s => !s.IsNullOrEmptyOrWhiteSpace()));

            foreach (var item in auxPropVals)
            {
                if (item.PropNo.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add($"辅助属性里的属性不能为空！");
                    continue;
                }

                var prop = props.FirstOrDefault(s => Convert.ToString(s["fnumber"]).EqualsIgnoreCase(item.PropNo));
                if (prop == null)
                {
                    errorMsgs.Add($"属性【{item.PropNo}】不存在！");
                    continue;
                }

                item.PropId = Convert.ToString(prop["id"]);
                item.AllowCustom = Convert.ToBoolean(prop["fallowcustom"]);
                item.AllowNoSuitlib = Convert.ToBoolean(prop["fallownosuitlib"]);
                item.DataType = Convert.ToString(prop["fdatatype"]);
            }
        }

        /// <summary>
        /// 填充标准属性值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        private static void FillStdPropValue(UserContext userCtx, List<AuxPropValDTO> auxPropVals)
        {
            // 过滤取标准属性值的
            var stdPropVals = auxPropVals.Where(s => s.ValueId.IsNullOrEmptyOrWhiteSpace()).ToList();
            if (stdPropVals.IsNullOrEmpty()) return;

            List<string> sqls = new List<string>();

            foreach (var item in stdPropVals)
            {
                string sql =
                    $"select top 1 fid, fnumber, fname, fpropid from t_sel_propvalue pv with(nolock) where pv.fpropid='{item.PropId}' and pv.fnosuit='0'";

                if (item.DataType.EqualsIgnoreCase("2"))
                {
                    sql += $" and pv.fnumber in ('{item.PropValueNo}', '{Convert.ToDecimal(item.PropValueNo):0.##########}')";
                }
                else
                {
                    sql += $" and pv.fnumber='{item.PropValueNo}'";
                }

                sqls.Add(sql);
            }

            var propValues = userCtx.Container.GetService<IDBService>().ExecuteDynamicObjectByUnion(userCtx, sqls);

            foreach (var item in stdPropVals)
            {
                var propValue = propValues.FirstOrDefault(s =>
                    Convert.ToString(s["fpropid"]).EqualsIgnoreCase(item.PropId) &&
                    Convert.ToString(s["fnumber"]).EqualsIgnoreCase(item.PropValueNo)
                );
                // 如果是数值，则根据数值获取
                if (propValue == null && item.DataType.EqualsIgnoreCase("2"))
                {
                    propValue = propValues.FirstOrDefault(s =>
                        Convert.ToString(s["fpropid"]).EqualsIgnoreCase(item.PropId) &&
                        Convert.ToDecimal(s["fnumber"]) == Convert.ToDecimal(item.PropValueNo)
                    );
                }

                if (propValue == null)
                {
                    continue;
                }

                item.ValueId = Convert.ToString(propValue["fid"]);
                item.ValueName = Convert.ToString(propValue["fname"]);
                item.ValueNumber = Convert.ToString(propValue["fnumber"]);
            }
        }

        /// <summary>
        /// 填充非标库里的非标属性值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        private static void FillUnstdPropValueInLib(UserContext userCtx, DynamicObject product, List<AuxPropValDTO> auxPropVals)
        {
            // 过滤取非标属性值的
            var unstdPropVals = auxPropVals
                .Where(s => s.ValueId.IsNullOrEmptyOrWhiteSpace() && s.AllowNoSuitlib).ToList();
            if (unstdPropVals.IsNullOrEmpty()) return;

            List<string> sqls = new List<string>();

            foreach (var item in unstdPropVals)
            {
                string sql =
                    $"select top 1 fid, fnumber, fname, fpropid from t_sel_propvalue pv with(nolock) where pv.fpropid='{item.PropId}' and pv.fnosuit='1' and fnosuitcreate='0'";

                if (item.DataType.EqualsIgnoreCase("2"))
                {
                    sql += $" and pv.fnumber in ('{item.PropValueNo}', '{Convert.ToDecimal(item.PropValueNo):0.##########}')";
                }
                else
                {
                    sql += $" and pv.fnumber='{item.PropValueNo}'";
                }

                sqls.Add(sql);
            }

            var propValues = userCtx.Container.GetService<IDBService>().ExecuteDynamicObjectByUnion(userCtx, sqls);

            foreach (var item in unstdPropVals)
            {
                var propValue = propValues.FirstOrDefault(s =>
                    Convert.ToString(s["fpropid"]).EqualsIgnoreCase(item.PropId) &&
                    Convert.ToString(s["fnumber"]).EqualsIgnoreCase(item.PropValueNo)
                );
                // 如果是数值，则根据数值获取
                if (propValue == null && item.DataType.EqualsIgnoreCase("2"))
                {
                    propValue = propValues.FirstOrDefault(s =>
                        Convert.ToString(s["fpropid"]).EqualsIgnoreCase(item.PropId) &&
                        Convert.ToDecimal(s["fnumber"]) == Convert.ToDecimal(item.PropValueNo)
                    );
                }

                if (propValue == null)
                {
                    continue;
                }

                item.ValueId = Convert.ToString(propValue["fid"]);
                item.ValueName = Convert.ToString(propValue["fname"]);
                item.ValueNumber = Convert.ToString(propValue["fnumber"]);
            }
        }

        /// <summary>
        /// 填充录入创建的非标属性值
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        private static void FillUnstdPropValueInCreate(UserContext userCtx, DynamicObject product, List<AuxPropValDTO> auxPropVals)
        {
            // 过滤剩余的属性值
            var unstdPropVals = auxPropVals.Where(s => s.ValueId.IsNullOrEmptyOrWhiteSpace()).ToList();
            if (unstdPropVals.IsNullOrEmpty()) return;

            var propSelectionService = userCtx.Container.GetService<IPropSelectionService>();

            var propValues = propSelectionService.LoadOrCreatePropValue(userCtx, unstdPropVals.Select(s =>
                new PropEntity
                {
                    PropId = s.PropId,
                    ValueName = s.PropValueName
                }).ToList(), Convert.ToString(product?["id"]));

            foreach (var item in unstdPropVals)
            {
                var propValue = propValues.FirstOrDefault(s =>
                    Convert.ToString(s["fpropid"]).EqualsIgnoreCase(item.PropId)
                    //&& Convert.ToString(s["fname"]).EqualsIgnoreCase(item.PropValueName)
                     && CultureInfo.InvariantCulture.CompareInfo.Compare(Convert.ToString(s["fname"]).Trim().ToUpper(), item.PropValueName.ToUpper(), CompareOptions.IgnoreSymbols) == 0
                );

                if (propValue == null) continue;

                item.ValueId = Convert.ToString(propValue["id"]);
                item.ValueName = Convert.ToString(propValue["fname"]);
                item.ValueNumber = Convert.ToString(propValue["fnumber"]);
            }
        }

        /// <summary>
        /// 转换辅助属性字段值
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="productId">产品id</param>
        /// <param name="auxPropVals"></param>
        /// <returns></returns>
        public static DynamicObject ConvertAuxPropFieldValue(UserContext userCtx, string productId, List<AuxPropValDTO> auxPropVals)
        {
            if (auxPropVals == null || auxPropVals.Count == 0) return null;

            FillPropAndPropValue(userCtx, productId, auxPropVals);

            var auxPropValSetForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "bd_auxpropvalueset");
            var auxPropValSetObj = new DynamicObject(auxPropValSetForm.GetDynamicObjectType(userCtx));
            auxPropValSetObj["fmaterialid"] = productId;
            var auxPropValSetEntity = auxPropValSetForm.GetEntryEntity("FEntity");
            var auxPropValSetEntrys = auxPropValSetObj["FEntity"] as DynamicObjectCollection;

            foreach (var item in auxPropVals)
            {
                var auxPropValSetEntry = new DynamicObject(auxPropValSetEntity.DynamicObjectType);
                auxPropValSetEntry["fauxpropid"] = Convert.ToString(item.PropId);
                auxPropValSetEntry["fvalueid"] = Convert.ToString(item.ValueId);
                auxPropValSetEntry["fvaluename"] = Convert.ToString(item.ValueName);
                auxPropValSetEntry["fvaluenumber"] = Convert.ToString(item.ValueNumber);
                auxPropValSetEntrys.Add(auxPropValSetEntry);
            }

            return auxPropValSetObj;
        }



        /// <summary>
        /// 处理套件、配件、沙发、促销总部变更行状态
        /// </summary>
        /// <param name="item"></param>
        /// <param name="entrys"></param>
        public static void DoCombinesNumber(UserContext userCtx, DynamicObjectCollection fentry)
        {
            var productIds = fentry
                ?.Select(o => Convert.ToString(o["fmaterialid"]))
                ?.Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                ?.Distinct()
                ?.ToList();
            if (productIds == null || !productIds.Any()) return;

            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "fname,fsuiteflag");
            if (productObjs == null || !productObjs.Any()) return;

            //套件组合号
            var suitcombnumberLst = fentry
                .Where(f => Convert.ToString(productObjs.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1"))
                .Select(o => Convert.ToString(o["fsuitcombnumber"])).Distinct().ToList<string>();

            //配件组合号
            var partscombnumberLst = fentry.Where(o => !Convert.ToString(o["fpartscombnumber"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fpartscombnumber"])).Distinct().ToList<string>();
            //沙发组合号
            var sofacombnumberLst = fentry.Where(o => !Convert.ToString(o["fsofacombnumber"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fsofacombnumber"])).Distinct().ToList<string>();
            //促销组合
            var promotionnumberLst = fentry.Where(o => !Convert.ToString(o["fcombinenumber"]).IsNullOrEmptyOrWhiteSpace()).Select(o => Convert.ToString(o["fcombinenumber"])).Distinct().ToList<string>();
            //统一更新配件组合总部变更行状态
            var Mainhqstatus_part = string.Empty;
            foreach (var partscombnumber in partscombnumberLst)
            {
                //获取配件主商品
                Mainhqstatus_part = fentry.Where(f => Convert.ToBoolean(f["fiscombmain"]) && partscombnumber == Convert.ToString(f["fpartscombnumber"]) && !Convert.ToString(f["fhqderchgstatus"]).IsNullOrEmptyOrWhiteSpace())
                .Select(o => Convert.ToString(o["fhqderchgstatus"])).FirstOrDefault();

                foreach (var entry in fentry)
                {
                    //更新其它子件总部变更行状态
                    if (partscombnumber.EqualsIgnoreCase(Convert.ToString(entry["fpartscombnumber"])))
                    {
                        //如果返回的是终审或者驳回，配件不一起更新状态，因为配件不同于套件、沙发根据套件头、沙发头一起联动数量和驳回关闭状态，配件是各自返回状态。业务交互不同。
                        if (Mainhqstatus_part.EqualsIgnoreCase("02") || Mainhqstatus_part.EqualsIgnoreCase("03"))
                        {
                            continue;
                        }
                        //之前如果是先关闭主再关闭子此没问题，但是如果是先返回子件的关闭，这里因为主商品未返回关闭还是01，然后子件返回的02又会变成被这里覆盖为主件的01
                        //所以注释此行，因为配件场景不需要联动状态，因为配件是特殊场景需要各自返回状态，不同于沙发、套件组合
                        //entry["fhqderchgstatus"] = Mainhqstatus_part;
                    }
                }
            }
            //统一更新促销组合总部变更行状态
            //foreach (var promotionnumber in promotionnumberLst)
            //{
            //    var Mainstatus_promo = fentry.Where(f => promotionnumber == Convert.ToString(f["fcombinenumber"]) && !Convert.ToString(f["fhqderchgstatus"]).IsNullOrEmptyOrWhiteSpace())
            //                            .Select(o => Convert.ToString(o["fhqderchgstatus"])).FirstOrDefault();
            //    foreach (var entry in fentry)
            //    {
            //        //更新其它促销商品总部变更行状态
            //        if (promotionnumber.EqualsIgnoreCase(Convert.ToString(entry["fcombinenumber"])))
            //        {
            //            //如果返回的是终审或者驳回，配件不一起更新状态，因为配件不同于套件、沙发根据套件头、沙发头一起联动数量和驳回关闭状态，这里是各自返回状态。
            //            if (Mainstatus_promo.EqualsIgnoreCase("02") || Mainstatus_promo.EqualsIgnoreCase("03"))
            //            {
            //                continue;
            //            }
            //            entry["fhqderchgstatus"] = Mainstatus_promo;
            //        }
            //    }
            //}
            //统一更新沙发组合总部变更行状态
            foreach (var sofacombnumber in sofacombnumberLst)
            {
                var Mainstatus_sofa = fentry.Where(f => sofacombnumber == Convert.ToString(f["fsofacombnumber"]) && !Convert.ToString(f["fhqderchgstatus"]).IsNullOrEmptyOrWhiteSpace())
                                        .Select(o => Convert.ToString(o["fhqderchgstatus"])).FirstOrDefault();
                foreach (var entry in fentry)
                {
                    //更新其它沙发总部变更行状态
                    if (sofacombnumber.EqualsIgnoreCase(Convert.ToString(entry["fsofacombnumber"])))
                    {
                        entry["fhqderchgstatus"] = Mainstatus_sofa;
                    }
                }
            }
            //统一更新套件组合总部变更行状态
            var Mainstatus_suit = string.Empty;
            foreach (var suitcombnumber in suitcombnumberLst)
            {
                Mainstatus_suit = fentry
                        .Where(f => Convert.ToString(productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(Convert.ToString(f["fmaterialid"])))?["fsuiteflag"]).EqualsIgnoreCase("1") && suitcombnumber == Convert.ToString(f["fsuitcombnumber"]))
                        .Select(o => Convert.ToString(o["fhqderchgstatus"])).FirstOrDefault();

                foreach (var entry in fentry)
                {
                    if (suitcombnumber.EqualsIgnoreCase(Convert.ToString(entry["fsuitcombnumber"])))
                    {
                        entry["fhqderchgstatus"] = Mainstatus_suit;
                    }
                }
            }
        }

    }
}
