using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;

namespace JieNor.AMS.YDJ.MS.API.Utils
{
    /// <summary>
    /// ServiceStack扩展类
    /// </summary>
    public static class ServiceStackExtentions
    {
        public static void AddItems(this ServiceStack.Web.IRequest request, Dictionary<string, object> items)
        {
            if (items == null)
            {
                return;
            }

            foreach (var item in items)
            {
                request.SetItem(item.Key, item.Value);
            }
        }

        /// <summary>
        /// 获取真实响应报文
        /// </summary>
        /// <param name="request"></param>
        /// <param name="responseDto"></param>
        /// <returns></returns>
        public static object GetRealResponse(this ServiceStack.Web.IRequest request, object responseDto)
        {
            request.Items.TryGetValue(MSKey.RealResponse, out var realResponse);

            return realResponse ?? responseDto;
        }

        /// <summary>
        /// 获取单据编码
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static string GetBillNo(this ServiceStack.Web.IRequest request, string key)
        {
            request.Items.TryGetValue(key, out var billNo);

            return billNo?.ToString() ?? "";
        }

        public static void SetBillNo(this ServiceStack.Web.IRequest request, string key, string billNo)
        {
            request.Items[key] = billNo ?? string.Empty;
        }
    }
}
