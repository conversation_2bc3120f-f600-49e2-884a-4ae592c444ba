using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Utils
{
    public static class StreamUtility
    {

        /// <summary>
        /// 将图片文件转换为Base64字符串
        /// </summary>
        /// <param name="imagePath">图片文件路径</param>
        /// <returns>Base64编码的图片字符串</returns>
        public static string ConvertImageToBase64(string url)
        {
            var tempFile = DownloadLicFile(url);
            if (tempFile.IsNullOrEmptyOrWhiteSpace()) 
            {
                throw new FileNotFoundException("图片文件不存在", tempFile);
            }

            //if (!File.Exists(imagePath))
            //{
            //    throw new FileNotFoundException("图片文件不存在", imagePath);
            //}

            byte[] imageBytes = File.ReadAllBytes(tempFile);
            return Convert.ToBase64String(imageBytes);
        }

        public static string DownloadLicFile(string fileUrl)
        {
            try
            {
                HttpWebRequest request = WebRequest.Create(fileUrl) as HttpWebRequest;
                request.Method = "Get";
                HttpWebResponse response = request.GetResponse() as HttpWebResponse;
                Stream responseStream = response.GetResponseStream();

                var file = Path.GetTempFileName();
                using (FileStream fs = System.IO.File.Create(file))
                {
                    byte[] bytes = new byte[102400];
                    int n = 1;
                    while (n > 0)
                    {
                        n = responseStream.Read(bytes, 0, 10240);
                        fs.Write(bytes, 0, n);
                    }
                }

                return file;
            }
            catch (Exception ex)
            {
 
                return null;
            }
        }


    }
}
