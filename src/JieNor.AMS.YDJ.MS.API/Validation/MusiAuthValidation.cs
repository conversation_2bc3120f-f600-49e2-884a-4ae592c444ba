using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Validation
{
	public class MusiAuthValidation
	{
        /// <summary>
        ///  musi认证验证，验证通过返回对应上下文信息
        /// </summary>
        /// <param name="agents"></param>
        /// <param name="users"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        public static UserContext GetUserContext(UserContext userContext, IEnumerable<DynamicObject> agents, Tuple<string, string> agentTup, IEnumerable<DynamicObject> users, string operatorid, BaseResponse<MuSiData> resp)
        {
            var agentid = agentTup.Item1; 
            //如果是总部直接返回总部上下文
            if (agentid.EqualsIgnoreCase(userContext.TopCompanyId)) 
            {
                //var topCtx = userContext.CreateTopOrgDBContext();
                //return topCtx;
                return userContext;
            }
            var agent = agents.Where(x => Convert.ToString(x["ftranid"]) == agentid).FirstOrDefault();
            if (agent == null)
            {
                resp.Success = false;
                resp.Data.ErrorMsgs.Add($@"经销商{agentTup.Item2}不存在!");
                return userContext;
            }
            var status = Convert.ToString(agent["fstatus"]);
            var agentstatus = Convert.ToString(agent["fagentstatus"]);
            if (status != "E")
            {
                resp.Success = false;
                resp.Data.ErrorMsgs.Add($@"经销商{agentTup.Item2}未审核或已禁用，不允许同步相关数据!");
                return userContext;
            }
            var userId = users.Where(x => Convert.ToString(x["ftranid"]) == operatorid).Select(x => Convert.ToString(x["fid"])).FirstOrDefault();
            if (userId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Data.ErrorMsgs.Add($@"操作人{operatorid}不存在!");
                return userContext;
            }
            var agentId = Convert.ToString(agent["fid"]);
            var agentCtx = userContext.CreateAgentDBContext(agentId);
            //if (operatorid == "822126547921145857") //先固定系统管理员外部账号联调使用
            //{
            //    agentCtx = userContext.CreateAgentDBContext(agentId);
            //}
            return agentCtx;
        }
	}
}
