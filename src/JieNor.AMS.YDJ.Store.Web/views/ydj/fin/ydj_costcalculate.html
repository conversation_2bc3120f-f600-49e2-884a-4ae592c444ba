<div class="page-menu-list tab_title"></div>
<form action="###" class="form-horizontal">
    <div class="leftTab" style="padding-bottom:200px;">
        <div class="portlet box yellow-casablanca">
            <div class="portlet-title">
                <div class="caption">库存初始化控制</div>
            </div>
            <div class="portlet-body form">
                <div class="form-body">
                    <div class="row">
                        <div class="col-md-7">

                            <div class="form-group">
                                <label class="col-md-4 control-label">计算月份</label>
                                <div class="col-md-7">
                                    <div class="input-icon right input-group date date-picker">
                                        <input type="text" class="form-control" name="fenddate" placeholder="计算月份" />
                                        <span class="input-group-addon">
                                            <i class="fa fa-calendar"></i>
                                        </span>
                                    </div>
                                    <span class="help-block">选择要计算哪个月份的成本，只能选择已关账月份及以后的月份</span>
                                </div>
                            </div>
                            <div class="form-group" hidden>
                                <label class="col-md-4 control-label">重算的开始月份</label>
                                <div class="col-md-7">
                                    <div class="input-icon right input-group date date-picker">
                                        <input type="text" class="form-control" name="fbegindate" placeholder="重算的开始月份" />
                                        <span class="input-group-addon">
                                            <i class="fa fa-calendar"></i>
                                        </span>
                                    </div>
                                    <span class="help-block">从哪个期间开始重算，假定之前的月份已经计算过成本但是未结账，则这些月份的成本可以重新计算，计算时将从开始月份进行计算</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-4 control-label">已关账月份</label>
                                <div class="col-md-7">
                                    <textarea class="form-control" autocomplete="off"
                                              name="fclosedate" placeholder="当前已经关账的月份"></textarea>
                                </div>
                            </div>


                            <div class="form-group">
                                <label class="col-md-4 control-label">最近成本计算</label>
                                <div class="col-md-7">
                                    <div class="input-icon right">
                                        <input type="text" class="form-control" autocomplete="off" name="flastcalc" placeholder="最近成本计算" />
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-3 control-label"></label>
                                <div class="col-md-7">
                                    <div class="checkbox-list">
                                        <label class="checkbox-inline">
                                            <input type="checkbox" name="fiswritelog" />输出计算日志
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <a href="###" domaintype="list" formid="stk_inventorylist">
                                    <b>即时库存</b>
                                </a>
                            </div>
                            <div class="form-group">
                                <a href="###" domaintype="list" formid="stk_inventorybalance">
                                    <b>库存余额</b>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>