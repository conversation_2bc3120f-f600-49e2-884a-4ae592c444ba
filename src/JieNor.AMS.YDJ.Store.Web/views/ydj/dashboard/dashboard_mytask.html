<style>
    .list-wrapper .nav-tabs .dropdown.open > .dropdown-toggle{
        background-color:white;
        border:1px solid #ddd;
    }

    #tabSearch {
        float:right;
    }
</style>
<div class="list" event="click">
    <ul class="nav nav-tabs">
        <li class="active"><a href="###" data-toggle="tab" aria-expanded="true" optype="task" data-param="searchType:'mytask'">待审批</a></li>
        <!--<li><a href="###" data-toggle="tab" aria-expanded="false" optype="task" data-param="searchType:'mytakepartin'">我参与的</a></li>-->
        <li><a href="###" data-toggle="tab" aria-expanded="false" optype="task" data-param="searchType:'myallot'">我创建</a></li>
        <li><a href="###" data-toggle="tab" aria-expanded="false" optype="task" data-param="searchType:'myhasdone'">已处理</a></li>
        <li class="dropdown">
            <a href="###" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                我的消息<span class="badge badge-danger y-unreads y-myunreads" style="margin-top:-2px;margin-left:5px;display:none;">0</span>
                <i class="fa fa-angle-down"></i>
            </a>
            <ul class="dropdown-menu" role="menu" style="min-width:135px;">
                <li>
                    <a href="###" data-toggle="tab" optype="task" data-param="searchType:'mymsg',readStatus:''">
                        <i class="icon-bell"></i> 全部<span class="badge badge-default y-alls">0</span>
                    </a>
                </li>
                <li>
                    <a href="###" data-toggle="tab" optype="task" data-param="searchType:'mymsg',readStatus:'unread'">
                        <i class="icon-envelope-letter"></i> 未读<span class="badge badge-danger y-unreads">0</span>
                    </a>
                </li>
                <li>
                    <a href="###" data-toggle="tab" optype="task" data-param="searchType:'mymsg',readStatus:'read'">
                        <i class="icon-envelope-open"></i> 已读<span class="badge badge-success y-reads">0</span>
                    </a>
                </li>
                <li class="divider"></li>
                <li><a href="###" optype="msg" data-param="readStatus:'read'"><i class="icon-check"></i> 全部标为已读</a></li>
                <li><!--<a href="###" optype="msg" data-param="readStatus:'unread'">全部标为未读</a>--></li>
            </ul>
        </li>
        <li id="tabSearch" class="searchFilter">
            <input id="searchfilter" name="searchfilter" type="text" placeholder="按回车键搜索..." class="form-control" />
        </li>
    </ul>
    
    <div id="{0}_mytask_container" style="margin-top:5px;">
        <table id="{0}_mytask_list"></table>
        <div id="{0}_mytask_pager"></div>
    </div>
</div>
