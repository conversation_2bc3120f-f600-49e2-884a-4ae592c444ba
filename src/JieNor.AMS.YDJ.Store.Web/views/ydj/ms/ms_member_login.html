<style>
    .button {
        display: block;
        margin: 6px auto;
        width: 80%;
        padding: 8px;
        border-radius: 1px;
        border-color: lightgrey;
        border: none;
        background-color: dodgerblue;
        color: white;
        cursor: pointer;
        font-size: 15px;
    }

    .form-control {
        display: block;
        margin: 6px auto;
        width: 80%;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-sizing: border-box;
    }


    input[type="text"] {
        /* display: block; */
        margin: 6px auto;
        /*
        width: 80%;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ccc;
        margin-bottom: 10px;*/
    }

    .form-body {
        /* horiz-align: center */
        align-self: center;
    }

    .fromgroupbackground:hover {
        background-color: white;
    }

    /* .test{
        margin-left: 190px;
    } */

</style>

<form action="###" class="form-horizontal">
    <div class="portlet box yellow-casablanca">
        <div class="portlet-body form">

            <div class="form-body" style="border: none">
                <div class="row">
                    <div class="form-group fromgroupbackground">
                        <label class="control-label" style="width:60%;padding-top: 5px;padding-bottom:5px ;font-size: 30px;">授权登录</label>
                    </div>

                </div>
            </div>

            <div class="form-body" style="border: none">
                <div class="row">
                    <div>
                        <div class="form-group fromgroupbackground">
                            <label class="control-label" style="margin-left: 10%;">经销商名称</label>
                            <select class="form-control select2me dynamic" name="fcrmdistributorid" placeholder="经销商名称" autocomplete="off"></select>
                        </div>
                    </div>

                </div>
            </div>

            <div class="form-body" style="border: none">
                <div class="row">
                    <div class="form-group fromgroupbackground">
                        <label class="control-label" style="margin-left: 10%;">经销商编号</label>
                        <input type="text" class="form-control" disabled name="fusername" />
                    </div>
                </div>

            </div>


            <div class="form-body" style="border: none">
                <div class="row">

                    <div class="form-group fromgroupbackground">
                        <label class="control-label" style="margin-left: 10%;">请输入会员登录系统的密码</label>
                        <input id="fpassword" class="form-control placeholder-no-fix" placeholder="会员登录系统的密码" type="password" 
                               style="margin-left: 10%; width: 80%; height: 35px" name="fpassword" autocomplete="off"/>
                        <label class="control-label" style="margin-left: 10%; color: red;"><span class="required">*</span>经销商ERP系统首次免密登录CDP，需要输入CDP密码进行验证</label>
                    </div>

                </div>
            </div>


            <div class="form-body" style="border: none">
                <div class="row">
                    <div class="form-group fromgroupbackground">
                        <button type="button" opcode="cdplogin" class="button">立即登录</button>
                        <label class=" control-label" style="margin-left: 10%;">若忘记密码请咨询400客服或会员部门同事进行密码重置</label>
                    </div>

                </div>
            </div>

        </div>
    </div>
</form>

<!--<script>
    $('#fpassword').on('focus', function (e) {
        $(this).attr('type', 'password')
    })
    $('#fpassword').on('change', function (e) {
        $(this).attr('type', 'password')
        if ($(this).value == '') {
            $(this).attr('type', 'text')
        }
    })
</script>-->
