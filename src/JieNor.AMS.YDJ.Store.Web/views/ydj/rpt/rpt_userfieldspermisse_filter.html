<style>
    .i-left {
        padding-left: 19px;
        padding-top: 14px;
    }

    .i-right {
        padding-top: 14px;
    }

    .icon-upload {
        background-color: #529DE3;
        color: white;
        padding: 10px;
        width: 100px;
        text-align: center;
        cursor: pointer;
        border-radius: 3px !important;
    }
</style>
<form action="###" class="form-horizontal">

    <!--基本信息-->
    <div class="portlet box yellow-casablanca base-mes">
        <div class="portlet-title">
            <div class="caption">过滤条件</div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <!-- 手机号-->
                <!--按编号模糊查询-->
                <div class="row">
                    <!--<div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">用户</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <i class="fa"></i>
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fuserid" placeholder="用户" maxlength="50" />
                                </div>
                            </div>
                        </div>
                    </div>-->
                    <!--<div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">用户</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fuserid" placeholder="用户" data-options="multSelect:false" />
                                </div>
                            </div>
                        </div>
                    </div>-->

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">用户</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fuserid" placeholder="用户" data-options="multSelect:false" />
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- 业务对象-->
                <!-- 业务对象选择，可以多选按分号隔开-->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-md-3 control-label">业务对象</label>
                            <div class="col-md-8">
                                <div class="input-icon right input-group">
                                    <input type="lookup" class="form-control" autocomplete="off"
                                           name="fnmenugroupame" placeholder="业务对象" data-options="multSelect:true" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>





</form>
