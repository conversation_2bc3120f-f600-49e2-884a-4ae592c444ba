<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>日程</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>

<body class="schedule_info" id="confirm">
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="Andoggy.finishPage()"></i>
        <div class="title">添加日程</div>
        <a class="bar-title-right blue" onclick="submit()">确定</a>
    </div>
    <!--div class="content has-header marginTop44 top">-->
    <ul class="has-header">
        <li class="grey border bg-white item-input">
            <span class="input-label">　　</span>
            <i class="icon ion-ios-arrow-right right grey"></i>
        </li>
        <li class="grey border bg-white item-input">
            <span class="input-label">日程标题</span>
            <input class="content-right grey" type="text" />
            <i class="icon ion-ios-arrow-right right grey"></i>
        </li>
        <li class="grey border bg-white item-input">
            <span class="input-label">开始时间</span>
            <input class="content-right grey" type="datetime-local" />
            <i class="icon ion-ios-arrow-right right grey"></i>
        </li>
        <li class="grey border bg-white item-input">
            <span class="input-label">结束时间</span>
            <input class="content-right grey" type="datetime-local" />
            <i class="icon ion-ios-arrow-right right grey"></i>
        </li>
        <li class="grey border bg-white item-input">
            <span class="input-label">位置</span>
            <input class="content-right grey" type="text" />
            <i class="icon ion-ios-arrow-right right grey"></i>
        </li>
        <li class="border bg-white " id="Chktag">
            <label class="item item-input">
                <span class="input-label">提醒方式</span>
                <select name="fremind" data-bind="fremind" id="fremind" data-ch_event="Ch" class="content-right"></select>
            </label>
        </li>
    </ul>
    <ul class="bg-white">
        <li>日程说明：</li>
        <li class="record" id="cancel">
            <textarea id="cancel-box" class="textarea" maxlength="100"></textarea>
        </li>
    </ul>

    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        var intcol = document.getElementsByTagName("input");
        intcol[1].value = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 1).ToString("yyyy-MM-dd") + "T" + new Date().ToString("HH:mm:00.000");
        intcol[2].value = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate() + 2).ToString("yyyy-MM-dd") + "T" + new Date().ToString("HH:mm:00.000");
        var billno = GetQueryString("fbillno");
        var FromId = "sys_schedule";
        GetEnum(FromId, "fremind", function () {
            var _nl = arguments[0];
            DomTag("fremind").innerHTML = "";
            //循环提醒数据
            for (var i = 0; i < _nl.length; i++) {
                var op = CreateDom("option");
                op.innerText = _nl[i].name;
                op.value = _nl[i].id;
                DomTag("fremind").appendChild(op);
            }
        });

        function Insertcallback() {
            Message.Alert("日程加入成功");
        }

        function submit() {
            var data = {
                ftitle: intcol[0].value,
                fiswholeday: false,
                fstartdate: intcol[1].value.replace("T", " "),
                fenddate: intcol[2].value.replace("T", " "),
                fremind: document.getElementsByTagName("select")[0].value,
                fparticipants: UseId,
                fposition: intcol[3].value,
                fdescription: document.getElementsByTagName("textarea")[0].value,
                fbizobject: "ydj_service",
                fbizobjnumber: billno
            };
            if (String(data.ftitle).isNullOrEmpty()) {
                Message.Alert("请输入日程标题");
                return;
            }
            var ct = parseInt(fremind.value.replace("remind_", ""));
            var NowTime = new Date();
            var formatStr = null;
            switch (ct) {
                case 2:
                    formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 10)) ? time.ToString("yyyy-MM-dd HH:mm:ss") : null;
                    break;
                case 3:
                    formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 40)) ? new Date(time.setMinutes(time.getMinutes() - 30)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                    break;
                case 4:
                    formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 70)) ? new Date(time.setMinutes(time.getMinutes() - 60)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                    break;
                case 5:
                    formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 130)) ? new Date(time.setMinutes(time.getMinutes() - 120)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                    break;
                case 6:
                    formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 1450)) ? new Date(time.setMinutes(time.getMinutes() - 1440)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                    break;
                default:
            }
            if (!String(formatStr).isNullOrEmpty()) {
                Andoggy.addCalendarEvent(localStorage.getItem('Phone'), "预约上门" + time.ToString("yyyy-MM-dd HH:mm:ss"), DomTag('certain-box').value, formatStr, fposition, 'Insertcallback');
            }
            else if (ct > 1) {
                Message.Alert("时间超出范围，日程提醒只能在提醒时间10分钟之前");
                return;
            }
            Ajax({
                url: "/bill/sys_schedule?operationNo=save&format=json", data: {
                    fromId: "sys_schedule",
                    operationNo: "save",
                    simpleData: { saveMyProfile: true },
                    billData: JSON.stringify([data]),

                }, callback: function () {
                    var _Json = arguments[0];
                    if (_Json.operationResult.isSuccess) {
                        Message.Alert("添加成功", 2000);
                        setTimeout(function () {
                            Andoggy.finishPage();
                        }, 2000)
                    }
                    else {
                        Message.Alert(_Json.operationResult.simpleMessage);
                    }
                }
            });
        }
    </script>
</body>
</html>
