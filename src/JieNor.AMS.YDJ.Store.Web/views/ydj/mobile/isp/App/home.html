<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>易到家（师傅端）首页</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="main-content" id="panel" style="position:relative">
        <div class="bar bar-header bar-white">
            <div menu-toggle="left" class="button button-icon icon ion-navicon " id="menu-toggle"></div>
            <div class="title">易到家（师傅端）首页</div>
        </div>
        <div id="wrapper">
            <div id="scroller" class="bg-white">
                <div id="scroller-pullDown" style="top:0px">
                    <span id="down-icon" class="icon-double-angle-down pull-down-icon"></span>
                    <span id="pullDown-msg" class="pull-down-msg">下拉刷新</span>
                </div>
                <div id="scroller-content">
                    <div class="content has-header scroll marginTop44" id="home">
                        <div class="padding list">
                            <span id="today">今天无日程</span>
                            <span class="btn-blue floatR" onclick="Redirect('/views/ydj/mobile/isp/App/SD/Schedule.html')">全部日程</span>
                        </div>
                        <div class="bg-white schedule hide">
                            <i class="icon ion-record"></i>
                            <span id="schedule">安排到门店取货安装</span>
                        </div>
                        <div class="padding">消息（3）</div>
                        <div class="list msgicon">
                            <a class="item item-avatar hide">
                                <img src="">
                                <h2><span>李师傅</span><span class="floatR"><span>04-01</span><span>下午4：30</span></span></h2>
                                <p><span class="ellipsis">客户的橱柜已安装稳妥</span></p>
                            </a>

                            <a class="item item-avatar relative" onclick="Redirect('/views/ydj/mobile/isp/App/WB/order_dynamic.html')">
                                <img src="/fw/images/dynamic.png" />
                                <em id="dynamicRecord" class="hide" data-count="0"></em>
                                <h2><span>订单动态</span><span class="floatR"><span id="dynamicTime"></span></span></h2>
                                <p class="ellipsis" id="dynamicHead">无新消息</p>
                            </a>
                            <a class="item item-avatar relative" onclick="Redirect('/views/ydj/mobile/isp/App/WB/notice.html')">
                                <img src="/fw/images/notice.png" />
                                <em id="noticeRecord" class="hide" data-count="0"></em>
                                <h2><span>公告</span><span class="floatR" id="noticeTime"></span></h2>
                                <p><span class="ellipsis" id="noticeHead">无新消息</span></p>
                            </a>
                            <a class="item item-avatar relative" onclick="Redirect('/views/ydj/mobile/isp/App/WB/remind.html')">
                                <img src="/fw/images/remind.png" />
                                <em id="remindRecord" class="hide" data-count="0"></em>
                                <h2><span>提醒</span><span class="floatR" id="remindTime"></span></h2>
                                <p><span class="ellipsis" id="remindHead">无新消息</span></p>
                            </a>
                        </div>

                        <div class="bg-white clearB hide" id="uncertific">
                            <p>请完成实名认证</p>
                            <p>你需要提交身份证等信息</p>
                            <button class="btn-bg-blue" onclick="Redirect('/views/ydj/mobile/isp/App/certification.html')">马上认证</button>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    <div class="left-content slideout-menu" id="menu">
        <!--个人主页-->
        <div class="bar bar-header user-center black">
            <a onclick="Redirect('/views/ydj/mobile/isp/App/personalInfo.html')"><img src="/fw/css/ydj/mobile/isp/App/images/App-logo.png" class="user-image" data-bind="fimage" data-big="none" /></a>
            <span class="user-account" data-bind="fname"></span>
        </div>
        <div class="content has-header personal-info">
            <div class="list">
                <label class="item item-input" onclick="Redirect('/views/ydj/mobile/isp/App/my_team.html')">
                    <i class="icon ion-ios-people-outline"></i>我的团队
                </label>
                <label class="item item-input" onclick="ClearCache(0)">
                    <i class="icon ion-ios-keypad-outline"></i>清除缓存
                </label>
            </div>
        </div>
    </div>

    <script src="/fw/include/jquery/jquery-1.11.3.min.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/slideout.min.js"></script>
    <!--Reference the SignalR library.广播通信插件 -->
    <script src="/fw/include/signalr/jquery.signalR-2.2.2.js"></script>
    <!--Reference the autogenerated SignalR hub script. -->
    <script src="/signalr/hubs"></script>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/iscroll.js"></script>
    <script>
        function TabExec() {
            var arg = parseInt(arguments[0]);
            var Did = null;
            switch (arg) {
                case 0:
                    Did = 'dynamicRecord';
                    break;
                case 1:
                    Did = 'noticeRecord';
                    break;
                case 2:
                    Did = 'remindRecord';
                    break;
                default:

            }
            var cou = parseInt(DomTag(Did).getAttribute("data-count"));
            cou--;
            if (cou>0) {
                DomTag(Did).setAttribute("data-count", cou);
                DomTag(Did).innerHTML = numbernine(cou);
            }else
            {
                AddClass(Did, 'hide');
            }
        }
        
        Andoggy.onPageResume();
        function ClearCache() {
            var arg = arguments[0];
            Message.Loading('正在清除,请稍后');
            Andoggy.clearWebCache();
            setTimeout(function () { Message.Remove(); }, 2000);
        }
        var slideout = new Slideout({
            'panel': DomTag('panel'),
            'menu': DomTag('menu'),
            'padding': 220
        });
        window.onload = function () {
            DomTag('menu-toggle').addEventListener('click', function () {
                slideout.toggle();
            });
        };
        //获得本地存储用户信息数据进行填充至页面
        var id = null;
        var sql = new WebSql();
        sql.Select("UserInfo", ["*"], null, function () {
            var Data = arguments[0];
            if (Data.length == 0) {
                return;
            }
            Data = Data[0];
            var Init = new HtmlSetData();
            Init.Init(Data);
            id = Init.id;
            //判断认证状态有无认证
            var approvestatus = JSON.parse(Data.fapprovestatus).fname;
            if (approvestatus == '等待审核' || approvestatus == '已认证') {
                AddClass('uncertific', 'hide');
            } else {
                RemoveClass('uncertific', 'hide');
            }
        }, function () { Message.Alert(arguments[0]) });
        //请求日历数据
        Ajax({
            isLoad: false,
            url: "/bill/sys_schedule?operationNo=getScheduleTimeListByDay&format=json", data: {
                fromId: "sys_schedule",
                operationNo: "getScheduleTimeListByDay",
                simpleData: { "Id": UseId, "date": new Date().ToString() }

            }, callback: function () {
                var _Json = arguments[0];
                var data = _Json.operationResult.srvData;
                if (data.length > 0) {
                    DomTag('schedule').innerText = data[0].title;
                    RemoveClass(DomTag('.schedule')[0], 'hide');
                    DomTag('today').innerHTML = '今天';
                }
            }
        });
        //连接消息通道并获取消息
        var loghub = $.connection.log;
        var imhub = $.connection.ydjIMHub;
        // Start the connection.
        $.connection.hub.start().done(function (r) {
            //订单动态
            imhub.server["getServiceMessageCountAndFrist"]().done(function (r) {
                if (!String(r.cDate).isNullOrEmpty()) {
                    RemoveClass('dynamicRecord', 'hide');
                    DomTag('dynamicTime').innerText = r.cDate;
                    DomTag('dynamicHead').innerText = r.subject;
                    DomTag('dynamicRecord').innerHTML = numbernine(r.msgcount);
                    DomTag('dynamicRecord').setAttribute("data-count",r.msgcount);
                }

            }).fail(function (e) { console.info(e); });
            //提醒
            imhub.server["getRemindMessageCountAndFrist"]().done(function (r) {
                if (!String(r.cDate).isNullOrEmpty()) {
                    RemoveClass('remindRecord', 'hide');
                    DomTag('remindTime').innerText = r.cDate;
                    DomTag('remindHead').innerText = r.subject;
                    DomTag('remindRecord').innerHTML = numbernine(r.msgcount);
                    DomTag('remindRecord').setAttribute("data-count", r.msgcount);
                }
            }).fail(function (e) { console.info(e); });
            //公告
            imhub.server["getNoticeMessageCountAndFrist"]().done(function (r) {
                if (!String(r.cDate).isNullOrEmpty()) {
                    RemoveClass('noticeRecord', 'hide');
                    DomTag('noticeTime').innerText = r.cDate;
                    DomTag('noticeHead').innerText = r.subject;
                    DomTag('noticeRecord').innerHTML = numbernine(r.msgcount);
                    DomTag('noticeRecord').setAttribute("data-count", r.msgcount);
                }
            }).fail(function (e) { console.info(e); });

        })
        .fail(function (e) {
            console.info(e);
        });
        imhub.client.onRecvBusinessMessage = function (callId, messages) {
            if (!messages) {
                return;
            }
            PageInit(messages[0]);
        }
        function PageInit() {
            var type = arguments[0].qid;
            var cou=0;
            switch (type) {
                case 'ydj_service':
                    RemoveClass('dynamicRecord', 'hide');
                    cou= parseInt(DomTag('dynamicRecord').getAttribute("data-count"));
                    cou++;
                    DomTag('dynamicRecord').setAttribute("data-count", cou);
                    DomTag('dynamicRecord').innerHTML = numbernine(cou);
                    DomTag('dynamicTime').innerText = arguments[0].cDate;
                    DomTag('dynamicHead').innerText = arguments[0].subject;
                    break;
                case 'y-notice':
                    RemoveClass('noticeRecord', 'hide');
                    cou = parseInt(DomTag('noticeRecord').getAttribute("data-count"));
                    cou++;
                    DomTag('noticeRecord').setAttribute("data-count", cou);
                    DomTag('noticeRecord').innerHTML = numbernine(cou);
                    DomTag('noticeTime').innerText = arguments[0].cDate;
                    DomTag('noticeHead').innerText = arguments[0].subject;
                    break;
                case 'y-alarm':
                    RemoveClass('remindRecord', 'hide');
                    cou = parseInt(DomTag('remindRecord').getAttribute("data-count"));
                    cou++;
                    DomTag('remindRecord').setAttribute("data-count", cou);
                    DomTag('remindRecord').innerHTML = numbernine(cou);
                    DomTag('remindTime').innerText = arguments[0].cDate;
                    DomTag('remindHead').innerText = arguments[0].subject;
                    break;
            }
        }
        
    </script>

    <script>
        window.addEventListener("load", loaded, false);
        document.addEventListener('touchmove', function (e) { e.preventDefault(); }, false);
        //下拉刷新
        function ReLoadData() {
            window.location.reload();
        }
        var myScroll = null;
      
        //滚动条
        if (DomTag('scroller')) {
            var downIcon = $("#down-icon");
            myScroll = new IScroll('#wrapper', {
                click: true,
                momentum: true,
                probeType: 3, mouseWheel: true

               
            });
            myScroll.refresh();
        }
        function loaded() {
            setTimeout(function () {
                myScroll.on("scroll", function () {
                    var y = this.y,
                        maxY = this.maxScrollY - y,
                        downHasClass = downIcon.hasClass("reverse_icon");
                    if (y<=0) {
                        this.scrollTo(0, 0);
                    }
                    if (y >= 40) {
                        !downHasClass && downIcon.addClass("reverse_icon");
                        return "";
                    } else if (y < 40 && y > 0) {
                        downHasClass && downIcon.removeClass("reverse_icon");
                        return "";
                    }
                });
                myScroll.on("slideDown", function () {
                    if (this.y > 40) {
                        ReLoadData();
                        upIcon.removeClass("reverse_icon")
                    }
                });
            }, 500);
            DomTag("wrapper").style.height = Pixel.H + "px";
            DomTag("wrapper").style.backgroundColor = "#fff";
        }
    </script>
</body>
</html>