<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title></title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>

<body>
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="Andoggy.finishPage()"></i>
        <div class="title">设置密码</div>
        <i class="bar-title-right assertive" onclick="Save()">完成</i>
    </div>
    <div class="content has-header" id="setPwd">
        <div class="list">
            <label class="item item-input hide" id="Code">
                <span class="input-label">验证码</span>
                <input type="number" maxlength="4" placeholder="验证码" yi-name="login" yi-hover="true" name="AuthCode" yi-pattern="^\d{4}$" yi-error="请输入四位验证码">
                <i class="icon input-code-clear ion-android-close hide"></i>
                <i class="code-right" onclick="GetCodeNoDom(this)">获取验证码</i>
            </label>
            <label class="item item-input hide" id="oldpwd">
                <span class="input-label">旧密码</span>
                <input type="password" placeholder="请输入旧密码" name="oldpwd">
                <input type="text" placeholder="请输入旧密码" name="oldpwd" class="hide">
                <i class="icon ion-ios-eye" onclick="ShowPwd(this)"></i>
            </label>
            <label class="item item-input">
                <span class="input-label">密码</span>
                <input type="password" placeholder="请输入密码" name="mobilePhone">
                <input type="text" placeholder="请输入密码" name="mobilePhone" class="hide">
                <i class="icon ion-ios-eye" onclick="ShowPwd(this)"></i>
            </label>
            <label class="item item-input">
                <span class="input-label">重复密码</span>
                <input type="password" placeholder="请输入重复密码" name="AuthCode">
                <input type="text" placeholder="请输入重复密码" name="mobilePhone" class="hide">
                <i class="icon ion-ios-eye" onclick="ShowPwd(this)"></i>
            </label>
        </div>
    </div>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        var Mobile = GetQueryString('mobilePhone');
        var Code = GetQueryString('Code');
        var SetPwd = GetQueryString('Set');
        var old = GetQueryString('Old');
        if (SetPwd) {
            RemoveClass('Code', 'hide');
        } else if (old) {
            RemoveClass('oldpwd', 'hide');
        }
        function GetCodeNoDom() {
            GetCode(arguments[0], Mobile);
        }
        function Save() {
            var codedom = document.getElementsByTagName("input")[0];
            var _code = codedom.value;
            var _opwd = document.getElementsByTagName("input")[1].className.indexOf("hide") > -1 ? document.getElementsByTagName("input")[2].value : document.getElementsByTagName("input")[1].value;
            var _pwd = document.getElementsByTagName("input")[3].className.indexOf("hide") > -1 ? document.getElementsByTagName("input")[4].value : document.getElementsByTagName("input")[3].value;
            var _rpwd = document.getElementsByTagName("input")[5].className.indexOf("hide") > -1 ? document.getElementsByTagName("input")[6].value : document.getElementsByTagName("input")[5].value;
            var _postData = {};
            var _url = "";
            if (SetPwd) {
                if (!String(codedom.getAttribute("yi-validate")).Boolean()) {
                    Message.Alert(codedom.getAttribute("yi-error"));
                    return;
                }
                _postData = {
                    MobilePhone: Mobile,
                    ActiveCode: _code,
                    Password: _pwd,
                    confirmpassword: _rpwd
                };
                _url = '/authuser/update?format=json';
            } else {
                _postData = {
                    userName: Mobile,
                    passWord: _pwd,
                    rePassword: _rpwd,
                    ActiveCode: Code,
                    userType: 4098,
                    company: localStorage.getItem("CompanyCode")
                };
                _url = '/reguser?format=json';
            }
            if (old) {
                if (String(_opwd).isNullOrEmpty()) {
                    Message.Alert("旧密码不能为空");
                    return;
                }
                //参数
                _postData = {
                    simpleData: {
                        userName: Mobile,
                        oldpwd: _opwd,
                        newpwd: _pwd,
                        newrepwd: _rpwd
                    }
                };

                _url = '/dynamic/sys_mainfw?operationno=modifypwd&format=json';
            }
            if (String(_pwd).isNullOrEmpty()) {
                Message.Alert("密码不能为空");
                return;
            }
            if (String(_rpwd).isNullOrEmpty()) {
                Message.Alert("重复密码不能为空");
                return;
            }
            if (_rpwd != _pwd) {
                Message.Alert("两次密码不一致");
                return;
            }
            Ajax({
                url: _url,
                data: _postData,
                callback: function () {
                    var Json = arguments[0];
                    if (String(SetPwd).isNullOrEmpty() && String(old).isNullOrEmpty()) {//新用户设置密码
                        if (String(Json.responseStatus.message).isNullOrEmpty()) {
                            localStorage.setItem("Phone", Mobile);
                            localStorage.setItem("Pwd", _pwd);
                            Message.Confirm('注册成功，去登陆？', null, function () {
                                Redirect('/views/ydj/mobile/isp/App/login.html', '', 1);
                            }, '否', '是');

                        } else {
                            Message.Alert(Json.responseStatus.message);
                        }
                        return;
                    }
                    if (!String(SetPwd).isNullOrEmpty()) {
                        if (String(Json.operationResult.isSuccess).Boolean()) {
                            localStorage.setItem("Phone", Mobile);
                            localStorage.setItem("Pwd", _pwd);
                            Message.Confirm('重置成功，去登陆？', null, function () {
                                Redirect('/views/ydj/mobile/isp/App/login.html', '', 1);
                            }, '否', '是');
                        }
                        else {
                            Message.Alert(Json.operationResult.simpleMessage)
                        }
                        return;
                    }
                    if (!String(old).isNullOrEmpty()) {
                        if (String(Json.operationResult.isSuccess).Boolean()) {
                            localStorage.setItem("Phone", Mobile);
                            localStorage.setItem("Pwd", _pwd);
                            Message.Confirm('修改成功，去登陆？', null, function () {
                                Redirect('/views/ydj/mobile/isp/App/login.html', '', 1);
                            }, '否', '是');
                        }
                        else {
                            Message.Alert(Json.operationResult.simpleMessage)
                        }
                    }
                },
                error: function () {
                    var Json = arguments[1];
                    if (Json.responseStatus.message && !old) {
                        Message.Confirm(Json.responseStatus.message, null, function () {
                            Redirect('/views/ydj/mobile/isp/App/login.html', '', 1);
                        }, '取消', '直接登录');

                    } else {
                        Message.Alert(Json.responseStatus.message);
                    }
                }
            });
        }
    </script>
</body>
</html>
