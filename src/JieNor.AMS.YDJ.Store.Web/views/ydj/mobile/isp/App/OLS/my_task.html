<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>我的任务</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="bar bar-header bar-white">
        <div class="title">我的任务</div>
    </div>
    <div class="tabs tabs-changes marginTop44 bg-white top">
        <a class="tab-item active">待确认</a>
        <a class="tab-item">待预约</a>
        <a class="tab-item">服务中</a>
        <a class="tab-item">已完工</a>
        <a class="tab-item">已结算</a>
    </div>
    <div id="wrapper">
        <div id="scroller">
            <div id="scroller-pullDown" style="top:60px;">
                <span id="down-icon" class="icon-double-angle-down pull-down-icon"></span>
                <span id="pullDown-msg" class="pull-down-msg">下拉刷新</span>
            </div>
            <div id="scroller-content">
                <div class="content has-header task scroll marginTop95" id="mytask">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </div>
            <div id="scroller-pullUp" class="hide">
                <span id="up-icon" class="icon-double-angle-up pull-up-icon"></span>
                <span id="pullUp-msg" class="pull-up-msg">加载更多</span>
            </div>
        </div>
    </div>
    <div class="mask-layer hide">
    </div>
    <div id="Qrcode" class="hide">
        <img src="/fw/images/Qrcode.png" id="WeChatCode" />
        <div>扫描二维码评价</div>
        <div class="grey hide">长按保存二维码</div>
        <i class="icon ion-ios-close-empty blue" onclick="cancel('Qrcode')"></i>
    </div>
    <div class="mask-layer hide">
    </div>
    <div id="evaluation" class="hide">
        <i class="icon ion-ios-star grey"></i><i class="icon ion-ios-star grey"></i><i class="icon ion-ios-star grey"></i>
        <i class="icon ion-ios-star grey"></i><i class="icon ion-ios-star grey"></i>
        <div class="grey">本单实际收入</div>
        <div class="assertive"><span id="frealamount"></span><span>元</span></div>
        <i class="icon ion-ios-close-empty blue" onclick="cancel('evaluation')"></i>
        <div id="evaltag"></div>
        <div id="evaldes" class="text-left"></div>
    </div>
    <script src="/fw/js/ydj/mobile/isp/App/jquery.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/iscroll.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        var Status = ["toBeConfirm", "toBeOrder", "servicing", "done", "cleared"];
        var OrderMore = [true, true, true, true, true];
        var IsInit = [true, true, true, true, true];
        var pageIndex = [1, 1, 1, 1, 1];
        DomTag('mytask').style.width = Pixel.W * Status.length;
        DomTag('mytask').style.height = Pixel.H;
        var Panel = Children(DomTag('mytask'));
        function TabExec() {
            var arg = parseInt(arguments[0]);
            var b = String(arguments[1]).Boolean();
            //Message.Alert(arg + "|" + b);
            if (b) {
                DomTag(".tab-item")[arg+1].click();
                GetServerData(Status[arg + 1], Panel[arg + 1], arg + 1);
            }
            GetServerData(Status[arg], Panel[arg], arg);
        }
        Andoggy.onPageResume();
        sessionStorage.removeItem("reloadhome");
        if (MasterAuth == "auth2") {
            //初始化加载数据
            for (var i = 0; i < Status.length; i++) {
                Panel[i].style.float = "left";
                Panel[i].style.minHeight = "20px";
                GetServerData(Status[i], Panel[i], i);
            }
        }
        else if (MasterAuth == "auth3") {
            Message.Box('审核还未通过，不能进行该操作', null, '确定');
        }
        else {
            Message.Confirm('您还没有进行实名认证，马上认证？', null, function () {
                Redirect('/views/ydj/mobile/isp/App/certification.html', '', 1);
            }, '否', '是');
        }
        function GetServerData() {
            var operationNo = arguments[0];
            var thisDom = arguments[1];
            var Index = arguments[2];
            Ajax({
                url: "/bill/ydj_service?operationno=" + operationNo + "&format=json",
                data: {
                    fromId: "ydj_service",
                    operationNo: operationNo,
                    simpleData: { id: localStorage.getItem("Id"), pageIndex: pageIndex[Index] }
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean() && !String(Json.operationResult.srvData).isNullOrEmpty()) {
                        var result = Json.operationResult.srvData.data;
                        var num = Json.operationResult.srvData.dataDesc;
                        if ((num.bills - num.pageCount) < 0) {
                            OrderMore[Index] = false;
                        }
                        if (result) {
                            if (IsInit[Index] == true) {
                                thisDom.innerHTML = '';
                            }
                            var _lurl = ['/OD/refuse_reason.html', '/OD/person_selection.html', '/OD/new_feedback.html', '/OD/done_detail.html', '/OD/done_detail.html'];
                            var _lname = ['拒绝', '转单', '反馈', '完工查询'];
                            var _eurl = ['/OD/order_detail.html', '/OD/confirm_order.html', '/OD/done_report.html', '', ''];
                            var _ename = ['确认接收', '确定预约', '完工', '展示二维码'];
                            for (var i = 0; i < result.length; i++) {
                                var Html = '<ul class="bg-white" data-master="' + result[i].fmasterid + '">';
                                Html += '<li>';
                                Html += '<span>订单编号：<span class="grey">' + result[i].fbillno + '</span></span>';
                                Html += '<span class="right grey">' + result[i].fname + '</span>';
                                Html += '</li>';
                                Html += '<li onclick="toDetail(\'' + result[i].fbillhead_id + '\')">';
                                Html += '<div>';
                                Html += '<span class="ellipsis">' + String(result[i].fservicename).NullReplaceEmpty() + '</span>';
                                Html += '<span class="right assertive">¥' + result[i].fexpectamount + '</span>';
                                Html += '</div>';
                                Html += '<i class="icon ion-ios-arrow-right right hide grey"></i>';
                                Html += '<div>';
                                Html += '<span class="grey ellipsis address">' + result[i].faddress + '</span>';
                                Html += '<span class="right">' + result[i].fservicedate.split(' ')[0] + '</span>';
                                Html += '</div>';
                                Html += '</li>';
                                Html += '<li class="btn">';
                                Html += '<em>负责师傅：' + result[i].fmasterid_fname + '</em>';
                                if (Index == 3) {
                                    if (result[i].fserstatus == 'sersta07' || result[i].fserstatus == 'sersta08') {
                                        Html += '<button name="fjump" class="btn-blue" onclick="jumpPage(\'' + _lurl[Index] + '\',\'' + result[i].fbillhead_id + '\')">完工查询</button>';
                                        Html += '<button name="fsure" class="btn-bg-blue" onclick="LookEval(\'' + result[i].fbillhead_id + '\')")">查看评价</button>';
                                    } else {
                                        Html += '<button name="fjump" class="btn-blue" onclick="jumpPage(\'' + _lurl[Index] + '\',\'' + result[i].fbillhead_id + '\')">完工查询</button>';
                                        Html += '<button name="fsure" class="btn-bg-blue" onclick="showQrcode(\'' + result[i].fbillhead_id + '\')">展示二维码</button>';
                                    }
                                } else
                                    if (Index == 4) {
                                        Html += '<button name="fjump" class="btn-blue" onclick="jumpPage(\'' + _lurl[Index] + '\',\'' + result[i].fbillhead_id + '\')">完工查询</button>';
                                        Html += '<button name="fsure" class="btn-bg-blue" onclick="LookEval(\'' + result[i].fbillhead_id + '\')")">查看评价</button>';
                                    }
                                    else {
                                        if (_lname[Index] == "反馈") {
                                            Html += '<button class="btn-blue" onclick="jumpPage(\'' + _lurl[Index] + '\', \'' + result[i].fbillhead_id + '\', \'' + result[i].fbillno + '\')">' + _lname[Index] + '</button>';
                                        } else {
                                            Html += '<button class="btn-blue" onclick="jumpPage(\'' + _lurl[Index] + '\', \'' + result[i].fbillhead_id + '\')">' + _lname[Index] + '</button>';
                                        }
                                        Html += '<button class="btn-bg-blue" onclick="jumpPage(\'' + _eurl[Index] + '\', \'' + result[i].fbillhead_id + '\')">' + _ename[Index] + '</button>';
                                    }
                                Html += '</li>';
                                Html += '</ul>';
                                Html.CreateHtml(thisDom);
                                myScroll.refresh();
                            }
                        }
                    }
                    else {
                        Message.Alert(Json.operationResult.simpleMessage);
                    }
                }
            });
        }
        var Scroll = function () {
            var params = arguments[0];
            var def = { dom: null, prevent: false, Edown: null }
            ParameterInit(def, params);
            this.dom = DomTag(params.conid);//触发元素
            this.panel = Children(this.dom)[0];//内容块
            this.item = Children(this.panel);//内容明细块
            this.isprevent = params.prevent;//是否阻止默认事件
            this.start = { x: 0, y: 0 };//开始坐标
            this.move = { x: 0, y: 0 };//移动坐标
            this.before = { x: 0, y: 0 };//元素原始作标
            this.pixel = 10;//每次移动的像素
            this.trigger = 70;//触发范围大于
            this.index = 0;//左右的当前页面索引
            this.count = 0;//总数
            this.Edown = params.Edown;//下拉事件
            this.Eup = params.Eup;//上拉事件
            this.Eleft = params.Eleft;
            this.Eright = params.Eright;
        };
        Scroll.prototype = {
            Init: function () {
                var n = this.item;
                this.panel.style.width = Pixel.W * n.length + "px";
                for (var i = 0; i < n.length; i++) {
                    n[i].style.width = Pixel.W + "px";
                }
                this.count = n.length;
                (function (_this) {
                    if (isAndroid || isiOS) {
                        _this.dom.addEventListener("touchstart", function (event) {
                            if (_this.isprevent) {
                                event.preventDefault();//阻止默认事件
                            }
                            var touch = event.targetTouches[0];
                            _this.start = { x: touch.pageX, y: touch.pageY };
                            _this.move = { x: touch.pageX, y: touch.pageY };
                            _this.before = { x: -_this.index * Pixel.W + "px", y: _this.panel.offsetTop + "px" }
                        });
                        _this.dom.addEventListener("touchmove", function (event) {
                            var touch = event.targetTouches[0];
                            var Y = _this.move.y - _this.start.y;
                            var X = _this.move.x - _this.start.x;
                            if (Math.abs(Y) > Math.abs(X)) {
                                // _this.panel.style.top = _this.panel.offsetTop + touch.pageY - _this.move.y + 'px';
                            }
                            else {
                                _this.panel.style.left = _this.panel.offsetLeft + touch.pageX - _this.move.x + 'px';
                            }
                            _this.move = { x: touch.pageX, y: touch.pageY };
                        });
                        _this.dom.addEventListener("touchend", function (event) {
                            var touch = event.targetTouches[0];
                            var left = _this.panel.offsetLeft;
                            var top = _this.panel.offsetTop;
                            var Y = _this.move.y - _this.start.y;//Y轴移动距离
                            var X = _this.move.x - _this.start.x;//X轴移动距离
                            //_this.index = parseInt(Math.abs(left) / Pixel.W);
                            /*没有超过区域还原*/
                            if (Math.abs(X) < _this.trigger && Math.abs(Y) < _this.trigger) {
                                _this.panel.style.left = _this.before.x;
                                return;
                            }
                            /*上拉加载更多，下拉刷新*/
                            if (Math.abs(Y) > _this.trigger) {
                                _this.panel.style.left = _this.before.x;
                            }
                            else {
                                if (left > 0) {//左顶
                                    _this.Timer({ left: 0 });
                                    return;
                                }
                                if (left < -(_this.count - 1) * Pixel.W) {//右顶
                                    _this.Timer({ left: -(_this.count - 1) * Pixel.W });
                                    return;
                                }
                                if (Math.abs(X) > _this.trigger) {
                                    if (X < 0) {//从右往左
                                        _this.ToLeft();
                                    }
                                    else {
                                        _this.ToRight();
                                    }
                                }
                            }
                            _this.start = { x: 0, y: 0 };
                            _this.move = { x: 0, y: 0 };
                        });
                    }
                    else {
                        _this.dom.onmousedown = function (event) {
                            if (_this.isprevent) {
                                event.preventDefault();//阻止默认事件
                            }
                            _this.start = {
                                x: event.clientX, y: event.clientY
                            };
                        }

                    }
                })(this);

            },
            Timer: function () {
                var _this = this;
                var arg = arguments[0];
                var st = setInterval(function () {
                    if (!String(arg.left).isNullOrEmpty()) {
                        if (arg.operation) {
                            if (_this.panel.offsetLeft <= arg.left) {
                                clearInterval(st);
                                _this.panel.style.left = arg.left + "px";
                                return;
                            }
                        } else {
                            if (_this.panel.offsetLeft >= arg.left) {
                                clearInterval(st);
                                _this.panel.style.left = arg.left + "px";
                                return;
                            }
                        }

                        if (_this.panel.offsetLeft > arg.left) {
                            _this.panel.style.left = _this.panel.offsetLeft - _this.pixel + "px";
                        } else {
                            _this.panel.style.left = _this.panel.offsetLeft + _this.pixel + "px";
                        }
                    }
                }, 1);
            },
            ToLeft: function () {
                this.index++;
                this.Timer({ left: -this.index * Pixel.W, operation: true });
                var nl = DomTag(".tab-item");
                for (var i = 0; i < nl.length; i++) {
                    if (this.index == i) {
                        AddClass(nl[i], "active");
                        ReLoadData();
                    }
                    else {
                        RemoveClass(nl[i], "active");
                    }
                }
            },
            ToRight: function () {
                this.index--;
                this.Timer({ left: -this.index * Pixel.W, operation: false });
                var nl = DomTag(".tab-item");
                for (var i = 0; i < nl.length; i++) {
                    if (this.index == i) {
                        AddClass(nl[i], "active");
                        ReLoadData();
                    }
                    else {
                        RemoveClass(nl[i], "active");
                    }
                }
            },
            Resize: function () {
            },
            TabInit: function () {
                var nl = DomTag(".tab-item");
                for (var i = 0; i < nl.length; i++) {
                    (function (_i, _this) {
                        nl[i].onclick = function () {
                            _this.index = _i;
                            _this.panel.style.left = -_i * Pixel.W + "px";
                            for (var j = 0; j < nl.length; j++) {
                                if (j == _i) {
                                    AddClass(nl[j], "active");
                                    ReLoadData();
                                }
                                else {
                                    RemoveClass(nl[j], "active");
                                }
                            }
                        }
                    })(i, this);
                }
            }
        };
        var s = new Scroll({ conid: "scroller-content", prevent: true });
        s.Init();
        s.TabInit();
        document.addEventListener('touchmove', function (e) { e.preventDefault(); }, false);
        //点击列表，跳转到详情页
        function toDetail() {
            Redirect('/views/ydj/mobile/isp/App/OD/order_detail.html', 'fid=' + arguments[0]);
        }
        //不同按钮跳转到不同页面
        function jumpPage(url, fid) {
            var event = event || window.event;
            var msid = event.target.parentNode.parentNode.getAttribute("data-master");
            if (url != '/OD/done_detail.html') {
                if (MasterId != msid) {//是否是对应的师傅
                    Message.Alert(authmsg);
                    return;
                }
            }
            var arg = arguments[0];
            var billno = arguments[2];
            if (url == "/OD/new_feedback.html") {
                Redirect('/views/ydj/mobile/isp/App' + url, 'fid=' + fid + '&fbillno=' + billno);
            } else {
                Redirect('/views/ydj/mobile/isp/App' + url, 'fid=' + fid);
            }
        }
        function showQrcode() {
            RemoveClass('Qrcode', 'hide');
            RemoveClass(DomTag('.mask-layer')[0], 'hide');
            Ajax({
                url: "/bill/ydj_service?operationno=getWeChatCode&format=json",
                data: {
                    fromId: 'ydj_service',
                    operationNo: 'getWeChatCode',
                    simpleData: { id: arguments[0] }
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean()) {
                        DomTag('WeChatCode').src = Json.operationResult.srvData.codeurl;
                    }
                }
            });
        };
        function LookEval() {
            RemoveClass('evaluation', 'hide');
            RemoveClass(DomTag('.mask-layer')[0], 'hide');
            Ajax({
                url: "/bill/ydj_service?operationno=evaluateservice&format=json",
                data: {
                    fromId: 'ydj_service',
                    operationNo: 'evaluateservice',
                    simpleData: { id: arguments[0] }
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean() && !String(Json.operationResult.srvData).isNullOrEmpty()) {

                        var result = Json.operationResult.srvData;
                        if (result) {
                            var star = DomTag('#evaluation .ion-ios-star');
                            var len = parseInt(String(result.fqualstar).replace('gradestar_', ''));
                            for (var i = 0; i < len; i++) {
                                RemoveClass(star[i], 'grey');
                                AddClass(star[i], 'yellow')
                            }
                            DomTag('frealamount').innerText = parseFloat(result.frealamount).toFixed(1);
                            if (!String(result.fqual_txt).isNullOrEmpty()) {
                                DomTag('evaltag').innerHTML = "";
                                DomTag("evaldes").innerText = result.fevaludesc;
                                var arr = result.fqual_txt.split(',');
                                for (var j = 0; j < arr.length; j++) {
                                    var d = CreateDom("div");
                                    d.className = "lable";
                                    d.innerText = arr[j];
                                    DomTag('evaltag').appendChild(d);
                                }
                            }

                        }

                    }
                }
            });
        };
        //二维码的关闭
        function cancel() {
            AddClass(arguments[0], 'hide');
            AddClass(DomTag('.mask-layer')[0], 'hide')
        }
        window.addEventListener("load", loaded, false);
        //下拉刷新
        function ReLoadData() {
            OrderMore[s.index] = true;
            pageIndex[s.index] = 1;
            GetServerData(Status[s.index], Panel[s.index], s.index);
        }
        //上拉加载数据
        function DataEvent() {
            OrderMore[s.index] = false;
            GetServerData(Status[s.index], Panel[s.index], s.index);
        }
        //滚动条
        if (DomTag('scroller')) {
            var upIcon = $("#up-icon"),
                downIcon = $("#down-icon");
            myScroll = new IScroll('#wrapper', {
                click: true,
                momentum: true,
                probeType: 3, mouseWheel: true
            });
            myScroll.refresh();
        }
        function loaded() {
            setTimeout(function () {
                myScroll.on("scroll", function () {
                    var y = this.y,
                        maxY = this.maxScrollY - y,
                        downHasClass = downIcon.hasClass("reverse_icon"),
                        upHasClass = upIcon.hasClass("reverse_icon");

                    if (y >= 40) {
                        !downHasClass && downIcon.addClass("reverse_icon");
                        return "";
                    } else if (y < 40 && y > 0) {
                        downHasClass && downIcon.removeClass("reverse_icon");
                        return "";
                    }

                    if (maxY >= 40) {
                        !upHasClass && upIcon.addClass("reverse_icon");
                        return "";
                    } else if (maxY < 40 && maxY >= 0) {
                        upHasClass && upIcon.removeClass("reverse_icon");
                        return "";
                    }
                });

                myScroll.on("slideDown", function () {
                    if (this.y > 40) {
                        IsInit[s.index] = true;
                        ReLoadData();
                        upIcon.removeClass("reverse_icon")
                    }
                });

                myScroll.on("slideUp", function () {
                    if (this.maxScrollY - this.y > 40) {
                        RemoveClass('scroller-pullUp', 'hide');
                        if (!OrderMore[s.index]) {
                            $("#pullUp-msg").html("没有更多数据了");
                            return;
                        }
                        pageIndex++;
                        IsInit[s.index] = false;
                        DataEvent();
                        upIcon.removeClass("reverse_icon")
                    }
                });
            }, 500);
        }
    </script>
</body>
</html>
