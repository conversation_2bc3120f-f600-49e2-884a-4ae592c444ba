<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>登录</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="bar bar-header bar-white">
        <div class="title">用户登陆</div>
        <a class="bar-title-right assertive" onclick="Redirect('/views/ydj/mobile/isp/App/register.html')">注册</a>
    </div>
    <dl id="login" class="marginTop44">
        <dt class="tabs tabs-changes" data-tab="tab-content">
            <a class="tab-item active">密码登录</a>
            <a class="tab-item">验证码登录</a>
        </dt>
        <dd class="content has-header task" id="password-login" tab-content="true">
            <div class="list">
                <div class="item item-input">
                    <span class="input-label">手机号</span>
                    <input type="tel" maxlength="11" placeholder="请输入手机号" class="mobilephone" name="mobilePhone" yi-name="pwdlogin" yi-hover="true" yi-pattern="^[1]{1}[3,4,5,7,8]{1}[0-9]{9}$" yi-error="手机号码格式错误" />
                    <i class="icon input-clear ion-android-close hide"></i>
                </div>
                <div class="item item-input">
                    <span class="input-label">密码</span>
                    <input type="password" name="pwd" placeholder="请输入密码" class="pwd" yi-name="pwdlogin">
                    <input type="text" placeholder="请输入密码" class="hide">
                    <i class="icon ion-ios-eye" onclick="ShowPwd(this)"></i>
                </div>
            </div>
            <div class="padding text-center">
                <button class="button w40 button-positive" yi-event="Submit" yi-name="pwdlogin">登录</button>
            </div>
        </dd>

        <dd class="content has-header task hide" id="reg-code-login" tab-content="true">
            <div class="list">
                <div class="item item-input">
                    <span class="input-label">手机号</span>
                    <input type="tel" maxlength="11" placeholder="请输入手机号" class="mobilephone" yi-hover="true" name="mobilePhone" yi-name="codelogin" yi-pattern="^[1]{1}[3,4,5,7,8]{1}[0-9]{9}$" yi-error="手机号码格式错误" />
                    <i class="icon input-clear ion-android-close hide"></i>
                </div>
                <div class="item item-input">
                    <span class="input-label">验证码</span>
                    <input type="number" placeholder="验证码" yi-name="codelogin" yi-hover="true" name="code" maxlength="4" yi-pattern="^\d{4}$" yi-error="请输入四位验证码">
                    <i class="icon input-code-clear ion-android-close hide"></i>
                    <i class="code-right" onclick="GetCode(this,this.parentNode.parentNode.getElementsByTagName('input')[0].value)">获取验证码</i>
                </div>
            </div>
            <div class="padding text-center">
                <button class="button w40 button-positive" yi-name="codelogin" yi-event="Submit">登录</button>
            </div>
        </dd>

    </dl>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        Andoggy.GetDriverID("GetDriverCode");
        function GetDriverCode() {
            var Cc = typeof (arguments[0]) == "object" ? arguments[0] : JSON.parse(arguments[0]);
            var Driver = Cc.registrationID;
            localStorage.setItem("DriverID", Driver);
        }
        //判断是否存在手机号，如果存在则自动填充
        var mobilePhone = localStorage.getItem("Phone");
        var password = localStorage.getItem("Pwd");
        if (!String(mobilePhone).isNullOrEmpty()) {
            var _nl = DomTag(".mobilephone");
            for (var i = 0; i < _nl.length; i++) {
                _nl[i].value = mobilePhone;
                _nl[i].setAttribute("yi-validate", true);
            }
        }
        if (!String(password).isNullOrEmpty()) {
            DomTag('.pwd')[0].value = password;
        }
        //数据提交验证
        function Submit(message) {
            var data;
            if (message.code) {
                data = { userName: message.mobilePhone, password: message.code };
            } else if (message.pwd) {
                data = { userName: message.mobilePhone, password: message.pwd };
            }
            if (String(data.password).isNullOrEmpty()) {
                Message.Alert("请输入密码！");
                return;
            }
            Login(data);
        }
    </script>
</body>
</html>
