<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>人员选择</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body class="feedback" id="selection">
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="Andoggy.finishPage()"></i>
        <div class="title">人员选择</div>
        <a class="bar-title-right blue" id="submit">确定</a>
    </div>
    <div class="bg-blue marginTop44 team" id="team"></div>
    <div class="content ionic-pseudo" id="Scroll" data-calc="90">
        <div class="bg-grey subtitle">队长</div>
        <div class="list" id="caption">
        </div>
        <div class="bg-grey subtitle">队员</div>
        <div class="list" id="member">
        </div>
    </div>

    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        var Config = JSON.parse(FileConfig);
        Ajax({
            url: "/bill/ydj_team?operationno=getTeamInMasterByMId&format=json",
            data: {
                fromId: "ydj_team",
                operationNo: "getTeamInMasterByMId",
                simpleData: { "Id": localStorage.getItem("Id") }
            },
            callback: function () {
                var Json = arguments[0];
                if (String(Json.operationResult.isSuccess).Boolean()) {
                    var result = Json.operationResult.srvData;
                    if (result == null || result.length == 0) {
                        return;
                    }
                    id = result[0]["tid"];
                    tname = result[0]["tname"];
                    SetVal(DomTag("team"), tname);
                    for (var i = 0; i < result.length; i++) {
                        var Html = '<a class="item item-avatar" href="javascript:;" fmasterid=' + result[i]["fid"] + '>';
                        if (String(result[i]["fimage"]).isNullOrEmpty()) {
                            Html += '<img src="/fw/css/ydj/mobile/isp/App/images/App-logo.png">';
                        } else {
                            Html += '<img src="' + Config.fsApiUrl + '/FileInfo/File/' + result[i]["fimage"] + '">';
                        }
                        Html += '<i class="icon ion-ios-checkmark right hide blue"></i>';
                        Html += '<h2>' + result[i]["fname"] + '</h2>';
                        Html += '<p class="grey">' + result[i]["fphone"] + '</p>';
                        Html += '</a>';
                        Html.CreateHtml(result[i]["fiscaptain"] == 1 ? "caption" : "member");
                    }
                    showIcon();
                }
            }
        });

        function showIcon() {
            var _elem = DomTag('Scroll').getElementsByTagName('a');
            var _icon = document.getElementsByClassName('ion-ios-checkmark');
            //人员选择
            for (var i = 0; i < _elem.length; i++) {
                (function (_i, len) {
                    _elem[i].onclick = function () {
                        console.log(this.getAttribute('fmasterid'))
                        submit(this.getAttribute('fmasterid'));

                        for (var j = 0; j < len; j++) {
                            if (j == _i) {
                                RemoveClass(_icon[j], "hide");
                            }
                            else {
                                AddClass(_icon[j], "hide");
                            }
                        }
                    };
                })(i, _icon.length);
            }
        }
        var fid = GetQueryString('fid');
        function submit() {
            var fmasterid = arguments[0];
            DomTag('submit').onclick = function () {
                Ajax({
                    url: "/bill/ydj_service?operationno=transferservice&format=json",
                    data: {
                        fromId: "ydj_service",
                        operationNo: 'transferservice',
                        simpleData: { id: fid, fmasterid: fmasterid }
                    },
                    callback: function () {
                        var Json = arguments[0];
                        var result = Json.operationResult.complexMessage;
                        if (String(Json.operationResult.isSuccess).Boolean()) {
                            Message.Alert('操作成功', 2000)
                            setTimeout(function () {
                                var isdetail = String(GetQueryString("isdetail")).Boolean();
                                isdetail ? localStorage.setItem("Reload", "true") : localStorage.setItem("FunEvent", "TabExec(1)");
                                Andoggy.finishPage();
                            }, 2000)
                        } else {
                            Message.Alert(Json.operationResult.simpleMessage)
                        }
                    }
                });
            }
        }

    </script>
</body>
</html>
