<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>完工汇报</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body class="feedback" id="done">
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="Andoggy.finishPage()"></i>
        <div class="title">完工汇报</div>
        <a class="bar-title-right blue" onclick="submit()">提交</a>
    </div>
    <ul class="bg-white marginTop44">
        <li class="border grey hide">完成订单前请提交核销码及现场图片</li>
        <li class="border hide"><div class="col60 floatL">给业主发送核销码</div><div class="bg-blue col38 floatL" id="verificcode">发送核销码</div></li>
        <li class="item item-input hide">
            <span class="input-label">核销码</span>
            <input type="text" placeholder="请输入" yi-hover="true" />
            <i class="icon input-clear ion-android-close hide"></i>
        </li>
    </ul>
    <div id="Scroll" data-calc="44">
        <ul class="content has-header bg-white" id="outer-problem">
            <li class="border">
                整体外观图(<span id="outer-select">0</span>/3)
            </li>
            <li class="row border">
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
            </li>
        </ul>
        <ul class="content has-header bg-white" id="inner-problem">
            <li class="border">
                内部构造图(<span id="inner-select">0</span>/3)
            </li>
            <li class="row border">
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
            </li>
        </ul>
        <ul class="content has-header bg-white" id="group-problem">
            <li class="border">
                安装合影图(<span id="group-select">0</span>/3)
            </li>
            <li class="row border">
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
                <div class="col col-32">
                    <img src="/fw/css/ydj/mobile/isp/App/images/imgupload.png" />
                </div>
            </li>
        </ul>
        <ul>
            <li class="bg-white">完工说明：</li>
            <li class="border record bg-white">
                <textarea id="done-desc" class="textarea"></textarea>
            </li>
        </ul>
    </div>
    <input type="file" accept="image/*" class="hide" id="upload" />

    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>

        //填写完原因后提交原因数据到后台保存
        function submit() {
            var elem = document.getElementsByTagName('ul');
            var fid = GetQueryString('fid');
            var datas = { id: fid, "fnote": DomTag("done-desc").value, "fdoneimage": { "id": "" }, "ftectonicimage": { "id": "" }, "fgroupimage": { "id": "" } }, doneId = [], groupId = [], tectonicId = [];
            for (var i = 0; i < elem.length; i++) {
                var id = elem[i].id;
                var _img = elem[i].getElementsByTagName('img');
                switch (id) {
                    case 'outer-problem':
                        for (var j = 0; j < _img.length; j++) {
                            if (_img[j].getAttribute('data-fileid') != null) {
                                doneId.push(_img[j].getAttribute('data-fileid'));
                            }
                        }
                        datas.fdoneimage.id = doneId.join(",");
                        break;
                    case 'inner-problem':
                        for (var j = 0; j < _img.length; j++) {
                            if (_img[j].getAttribute('data-fileid') != null) {
                                tectonicId.push(_img[j].getAttribute('data-fileid'));
                            }
                        }
                        datas.ftectonicimage.id = tectonicId.join(",");
                        break;
                    case 'group-problem':
                        for (var j = 0; j < _img.length; j++) {
                            if (_img[j].getAttribute('data-fileid') != null) {
                                groupId.push(_img[j].getAttribute('data-fileid'));
                            }
                        }
                        datas.fgroupimage.id = groupId.join(",");
                        break;
                }
            }

            if (doneId.length > 0 && tectonicId.length > 0 && groupId.length > 0) {
                Ajax({
                    url: "/bill/ydj_service?operationno=setstatus09&format=json",
                    data: {
                        operationName: "完工汇报",
                        selectedRows: [{ PKValue: fid }],
                        operationNo:"setstatus09",
                        simpledata: { opid: "setstatus09", serstatus: "sersta05" },
                        billData: JSON.stringify([datas])
                    },
                    callback: function () {
                        var Json = arguments[0];
                        if (String(Json.operationResult.isSuccess).Boolean()) {

                            Message.Alert('操作成功!', 2000)
                            setTimeout(function () {
                                var isdetail = String(GetQueryString("isdetail")).Boolean();
                                isdetail ? localStorage.setItem("Reload", "true") : localStorage.setItem("FunEvent", "TabExec(2,true)");
                                Andoggy.finishPage();
                            }, 2000)

                        }
                    }
                });
            } else {
                Message.Alert('整体外观图、内部构造图和安装合影图，三个选项不能空，且最少上传1张图片！')
            }
        };
        //上传图片
        var _nl = document.getElementsByTagName("img");
        for (var i = 0; i < _nl.length; i++) {
            _nl[i].onclick = function () {
                var _this = this;
                var dom = DomTag("upload");
                dom.onchange = function () {
                     FileUpload.Upload(dom, _this, 0, function () {
                         setTimeout(function () {
                             var p = _this.parentNode.parentNode.parentNode;
                             p.getElementsByTagName("span")[0].innerText = imgcount(p.getElementsByTagName("img"));
                         }, 500)
                     });
                }
                DomTag("upload").click();
            }
        }
        function imgcount() {
            var arg = arguments[0];
            var inum = 0;
            for (var i = 0; i < arg.length; i++) {
                if (arg[i].getAttribute("data-fileid") != null) {
                    inum++;
                }
            }
            return inum;
        }
    </script>
</body>
</html>
