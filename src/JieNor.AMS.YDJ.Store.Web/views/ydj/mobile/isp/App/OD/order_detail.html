<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>订单详情</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon" onclick="BackEvent()"></i>
        <div class="title">订单详情</div>
    </div>
    <dl class="order-detail marginTop44">
        <dt class="tabs tabs-changes">
            <a class="tab-item active" onclick="tabsChanges(this,0)">详情</a>
            <a class="tab-item" onclick="tabsChanges(this,1)">进度</a>
        </dt>

        <dd class="content has-header top1 detail" id="Scroll" data-calc="144">
            <ul class="no-margin">
                <li class="li-height">
                    <div class="col85">
                        <div class="name">
                            <div data-bind="fname" name="fname">无数据</div>
                            <div id="address">
                                <span data-bind="fprovince fcity fregion" name="fprovince fcity fregion">无数据</span>
                                <span data-bind="faddress" name="faddress"></span>
                            </div>
                        </div>
                    </div>
                    <div class="mobile-phone col15" onclick="Andoggy.makePhoneCall(DomTag('.fphone')[0].innerText)"><span data-bind="fphone" name="fphone" class="fphone hide"></span><i class="icon ion-ios-telephone btn-blue"></i></div>
                </li>
                <li class="li-height">
                    <div class="distance col50">
                        <span>距离：</span><span class="grey" id="fdistance" name="fdistance"></span>
                    </div>
                    <div class="dealermsg col50 blue">[商户信息]</div>
                </li>
            </ul>
            <div id="BaiduMap" style="width:100%;height:200px;margin-bottom:10px;"></div>
            <ul>
                <li class="li-height">
                    <span>订单类型：<span data-bind="fservicetype" name="fservicetype" class="grey">无数据</span></span>
                </li>
                <li class="li-height">
                    <span>预计收入：<span data-bind="fexpectamount" name="fexpectamount" class="assertive">0</span><span class="assertive">元</span></span>
                </li>
                <li class="li-height">
                    <div>
                        <div class="col25 floatL">服务类目：</div>
                        <div data-bind="fservicecate" name="fservicecate" class="grey col75 floatR over-scroll-x">无数据</div>
                    </div>
                </li>
                <li class="li-height">
                    <span>服务时间：<span data-bind="fservicedate" name="fservicedate" class="assertive">无数据</span></span>
                </li>
                <li class="li-height" onclick="pageJump('serviceItem')">
                    <span>服务详情：<span class="ellipsis grey" id="serdetail">无数据</span></span>
                    <i class="icon ion-ios-arrow-right right grey"></i>
                </li>
                <li class="minheight descparent">
                    <div>
                        <div class="col25 floatL">订单备注：</div>
                        <div data-bind="fdescription" name="fdescription" id="fdescription" class="grey col75 floatR ">无数据</div>
                    </div>
                </li>
            </ul>
            <ul>
                <li class="li-height">
                    <span>商户单号：<span data-bind="fdealernumber" name="fdealernumber" class="grey">无数据</span></span>
                </li>
                <li class="li-height">
                    <span>服务单号：<span data-bind="fbillno" name="fbillno" class="grey">无数据</span></span>
                </li>
                <li class="li-height">
                    <span>服务团队：<span data-bind="fteamid" name="fteamid" class="grey">无数据</span></span>
                </li>
                <li class="li-height">
                    <span>服务人员：<span data-bind="fmasterid" name="fmasterid" class="grey">无数据</span></span>
                </li>
                <li class="li-height">
                    <span>预约时间：<span data-bind="fappointdate" name="fappointdate" class="grey" id="appointdate">无数据</span></span>
                </li>
            </ul>
            <ul>
                <li class="li-height">
                    <span>实际收入：<span data-bind="frealamount" name="frealamount" class="assertive">0</span><span class="assertive">元</span></span>
                </li>
                <li id="refusereason" class="hide li-height">
                    <span>拒单原因：<span id="frefusereason" class="grey"></span></span>
                </li>
                <li id="problemfeed" class="hide li-height" onclick="pageJump('problem_feedback')">
                    <span>问题反馈：<span data-bind="fproblemfeed" name="fproblemfeed" class="assertive"></span></span>
                    <i class="icon ion-ios-arrow-right right grey"></i>
                </li>
            </ul>
        </dd>

        <dd class="content has-header task hide top1" id="progress">
            <ul id="prog-content">
                <li class="noborder">
                    <div class="col2 floatL arrow-up">
                        <div class="h50"></div>
                        <div class="h50"><i class="icon ion-record"></i></div>
                    </div>
                    <div class="col92 floatR border">
                        <div class="col38 floatL status">
                            <div>平台已结算</div>
                        </div>
                        <div class="col60 floatR time grey">

                        </div>
                    </div>
                </li>
                <li class="noborder">
                    <div class="col2 floatL arrow-up">
                        <div class="h50"></div>
                        <div class="h50"><i class="icon ion-record"></i></div>
                    </div>
                    <div class="col92 floatR border">
                        <div class="col38 floatL status">
                            <div>客户已评价</div>
                            <div class="blue-light hide alert" onclick="Redirect(furl+'/OD/order_detail.html', 'fid=' + fid + '&showEvaluat=showEvaluat')">查看评价</div>
                        </div>
                        <div class="col60 floatR time grey">

                        </div>
                    </div>
                </li>
                <li class="noborder">
                    <div class="col2 floatL arrow-up">
                        <div class="h50"></div>
                        <div class="h50"><i class="icon ion-record"></i></div>
                    </div>
                    <div class="col92 floatR border">
                        <div class="col38 floatL status">
                            <div>已完工</div>
                            <div class="blue-light hide alert" onclick="Redirect(furl+'/OD/done_detail.html', 'fid=' + fid)">查看完工情况</div>
                        </div>
                        <div class="col60 floatR time grey">

                        </div>
                    </div>
                </li>
                <li class="noborder">
                    <div class="col2 floatL arrow-up">
                        <div class="h50"></div>
                        <div class="h50"><i class="icon ion-record"></i></div>
                    </div>
                    <div class="col92 floatR border">
                        <div class="col38 floatL status link">
                            <div>已预约</div>
                            <div class="blue-light alert" onclick="Redirect(furl+'/OD/appoint_record.html', 'fid=' + fid)">预约记录</div>
                        </div>
                        <div class="col60 floatR time grey">

                        </div>
                    </div>
                </li>
                <li class="noborder">
                    <div class="col2 floatL arrow-up">
                        <div class="h50"></div>
                        <div class="h50"><i class="icon ion-record"></i></div>
                    </div>
                    <div class="col92 floatR border">
                        <div class="col38 floatL status">
                            <div>已拒单</div>
                            <div class="grey hide alert">请等待平台客服审核</div>
                        </div>
                        <div class="col60 floatR time grey">

                        </div>
                    </div>
                </li>
                <li class="noborder">
                    <div class="col2 floatL arrow-up">
                        <div class="h50"></div>
                        <div class="h50"><i class="icon ion-record"></i></div>
                    </div>
                    <div class="col92 floatR border">
                        <div class="col38 floatL status">
                            <div>已确认</div>
                            <div class="grey hide alert">请及时预约客户</div>
                        </div>
                        <div class="col60 floatR time grey">

                        </div>
                    </div>
                </li>
                <li class="noborder">
                    <div class="col2 floatL arrow-up">
                        <div class="h50"></div>
                        <div class="h50"><i class="icon ion-record"></i></div>
                    </div>
                    <div class="col92 floatR border">
                        <div class="col38 floatL status">
                            <div>客服已派单</div>
                            <div class="grey hide alert">请及时确认</div>
                        </div>
                        <div class="col60 floatR time grey">

                        </div>
                    </div>
                </li>
            </ul>
        </dd>
    </dl>
    <div class="bg-blue" id="task">
    </div>
    <div class="mask-layer hide">
    </div>
    <div id="Qrcode" class="hide">
        <img src="/fw/images/Qrcode.png" id="WeChatCode" />
        <div>扫描二维码评价</div>
        <div class="grey hide">长按保存二维码</div>
        <i class="icon ion-ios-close-empty blue" onclick="cancel('Qrcode')"></i>
    </div>
    <div id="evaluation" class="hide">
        <i class="icon ion-ios-star grey"></i><i class="icon ion-ios-star grey"></i><i class="icon ion-ios-star grey"></i>
        <i class="icon ion-ios-star grey"></i><i class="icon ion-ios-star grey"></i>
        <!--<div>本次获得结算系数<span class="assertive">1.2</span></div>-->
        <div class="grey">本单实际收入</div>
        <div class="assertive"><span id="frealamount"></span><span>元</span></div>
        <i class="icon ion-ios-close-empty blue" onclick="cancel('evaluation')"></i>
        <div id="evaltag"></div>
        <div class="border record bg-white">
            <textarea id="evaldes" class="textarea" disabled></textarea>
        </div>
        <!--<div class="lable">专业</div><div class="lable">礼貌</div><div class="lable">专业</div>-->
    </div>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/jquery.js"></script>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=7SHrBLxZ1HWlX0G1cR2fq2aggRzR5nLe&s=1"></script>
    <script>
        var furl = '/views/ydj/mobile/isp/App';

        //获取数据填充到页面中
        var billno, msid = null;

        var fid = GetQueryString('fid');
        var _fbillno = GetQueryString('fbillno');
        var operation, isSuccess;
        if (MasterAuth == "auth2") {
            //初始化加载数据
            if (fid != null || _fbillno != null) {
                getData('getservicedetail');
            }
        }
        else if (MasterAuth == "auth3") {
            Message.Box('审核还未通过，不能进行该操作', null, '确定');
        }
        else {
            Message.Confirm('您还没有进行实名认证，马上认证？', null, function () {
                Redirect(furl + '/certification.html', '', 1);
            }, '否', '是');
        }
        //tab切换
        function tabsChanges() {
            var _tab = DomTag('.tab-item');
            var index = arguments[1];
            var _tabitem = document.getElementsByTagName('dd');
            if (index == 0) {
                RemoveClass('task', 'hide');
                AddClass(_tab[index], 'active');
                RemoveClass(_tab[index + 1], 'active');
                RemoveClass(_tabitem[index], 'hide');
                AddClass(_tabitem[index + 1], 'hide');
                getData('getservicedetail');
            } else {
                AddClass('task', 'hide');
                AddClass(_tab[index], 'active');
                RemoveClass(_tab[index - 1], 'active');
                RemoveClass(_tabitem[index], 'hide');
                AddClass(_tabitem[index - 1], 'hide');
                getData('serviceprogress');
            }
        };

        //点击按钮页面跳转
        function pageJump() {
            var arg = arguments[0];
            if (["showQrcode", "showEvaluat", "serviceItem", "problem_feedback"].indexOf(arg) == -1) {
                if (MasterId != msid) {
                    Message.Alert(authmsg);
                    return;
                }
            }
            switch (arg) {
                case 'refuse'://拒绝订单
                    Redirect(furl + '/OD/refuse_reason.html', 'fid=' + fid + '&isdetail=true');
                    localStorage.setItem("reloadhome", "TabExec(0)");
                    break;
                case 'receive'://确定接收
                    var params = {
                        operationName: "队长确认",
                        selectedRows: [{ PKValue: fid }],
                        operationNo: "setstatus03",
                        simpledata: { opid: "setstatus03", serstatus: "sersta04" },
                    };
                    isSuccess = changeStatus(params, function () {
                        if (isSuccess == true) {
                            Message.Alert('操作成功!', 2000)
                            setTimeout(function () {
                                Andoggy.reloadPage();
                                localStorage.setItem("reloadhome", "TabExec(0,true)");
                            }, 2000)
                        }
                    });
                    break;
                case 'confirm'://确定预约
                    Redirect(furl + '/OD/confirm_order.html', 'fid=' + fid + '&isdetail=true');
                    break;
                case 'select'://转单
                    Redirect(furl + '/OD/person_selection.html', 'fid=' + fid + '&isdetail=true');
                    localStorage.setItem("reloadhome", "TabExec(1)");
                    break;
                case 'serviceItem':
                    Redirect(furl + '/OD/serviceItem_detail.html', 'fid=' + fid);
                    break;
                case 'problem_feedback':
                    Redirect(furl + '/OD/problem_feedback.html', 'fbillno=' + billno);
                    break;
                case 'feedback'://反馈
                    Redirect(furl + '/OD/new_feedback.html', 'fid=' + fid + '&fbillno=' + billno);
                    break;
                case 'donereport'://完工汇报
                    Redirect(furl + '/OD/done_report.html', 'fid=' + fid + '&isdetail=true');
                    localStorage.setItem("FunEvent", "TabExec(2,true)");
                    break;
                case 'donesearch'://完工查询
                    Redirect(furl + '/OD/done_detail.html', 'fid=' + fid);
                    break;
                case 'showQrcode'://显示二维码
                    Ajax({
                        url: "/bill/ydj_service?operationno=getWeChatCode&format=json",
                        data: {
                            fromId: 'ydj_service',
                            operationNo: 'getWeChatCode',
                            simpleData: { id: fid }
                        },
                        callback: function () {
                            var Json = arguments[0];
                            if (String(Json.operationResult.isSuccess).Boolean()) {
                                DomTag('WeChatCode').src = Json.operationResult.srvData.codeurl;
                                RemoveClass('Qrcode', 'hide');
                                RemoveClass(DomTag('.mask-layer')[0], 'hide')
                            }
                        }
                    });
                    break;
                case 'showEvaluat'://查看评价
                    Ajax({
                        url: "/bill/ydj_service?operationno=evaluateservice&format=json",
                        data: {
                            fromId: 'ydj_service',
                            operationNo: 'evaluateservice',
                            simpleData: { id: fid }
                        },
                        callback: function () {
                            var Json = arguments[0];
                            if (String(Json.operationResult.isSuccess).Boolean() && !String(Json.operationResult.srvData).isNullOrEmpty()) {
                                var result = Json.operationResult.srvData;
                                if (result) {
                                    var star = DomTag('#evaluation .ion-ios-star');
                                    var len = parseInt(String(result.fqualstar).replace('gradestar_', ''));
                                    for (var i = 0; i < len; i++) {
                                        RemoveClass(star[i], 'grey');
                                        AddClass(star[i], 'yellow')
                                    }
                                    DomTag('frealamount').innerText = parseFloat(result.frealamount).toFixed(1);
                                    DomTag("evaldes").value = result.fevaludesc;
                                    if (!String(result.fqual_txt).isNullOrEmpty()) {
                                        DomTag('evaltag').innerHTML = "";
                                        var arr = result.fqual_txt.split(',');
                                        for (var j = 0; j < arr.length; j++) {
                                            var d = '<button class="btn-bg-blue" style="background:#009DFE">' + arr[j] + '</button>';
                                            d.CreateHtml("evaltag");
                                        }
                                    }
                                    RemoveClass('evaluation', 'hide');
                                    RemoveClass(DomTag('.mask-layer')[0], 'hide');
                                }

                            }
                        }
                    });
                    break;
            }
        }
        //改变服务状态
        function changeStatus() {
            var params = arguments[0];
            var call = arguments[1];
            Ajax({
                url: "/bill/ydj_service?operationno=setstatus03&format=json",
                data: params,
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean()) {
                        isSuccess = true;
                        call(isSuccess);
                    }
                }
            });
        }
        function getData() {
            var operationNo = arguments[0];
            Ajax({
                url: "/bill/ydj_service?operationno=" + operationNo + "&format=json",
                data: {
                    fromId: "ydj_service",
                    operationNo: operationNo,
                    simpleData: { id: fid, fbillno: _fbillno }
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean()) {
                        if (Json.operationResult.srvData == null) {
                            return;
                        }
                        if (Json.operationResult.simpleMessage) {
                            DomTag('frefusereason').innerText = Json.operationResult.simpleMessage;
                        }
                        if ('serviceprogress' == operationNo) {
                            var result = Json.operationResult.srvData;
                        } else {
                            var result = Json.operationResult.srvData.uidata;
                            if (result["fmasterid"]) {
                                msid = result["fmasterid"].id;
                            }
                            var showEvaluat = GetQueryString('showEvaluat');
                            if (showEvaluat) {
                                pageJump(showEvaluat);
                            }
                            /*地图初始化话及算距离*/
                            var address = result.fprovince.fname + result.fcity.fname + result.fregion.fname + result.faddress;
                            // 创建地址解析器实例
                            var myGeo = new BMap.Geocoder();
                            // 将地址解析结果显示在地图上,并调整地图视野
                            myGeo.getPoint(address, function (point) {
                                if (point) {
                                    DomTag("BaiduMap").innerHTML = "";
                                    var _img = CreateDom("img");
                                    _img.src = "http://api.map.baidu.com/staticimage/v2?ak=sUy6SFvZ3Kca8Hu2z6BYebqpW1T2H8sf&mcode=666666&center=" + point.lng + "," + point.lat + "&markers=" + point.lng + "," + point.lat + "&width=" + Pixel.W + "&height=200&zoom=16";
                                    DomTag("BaiduMap").appendChild(_img);
                                } else {
                                    Message.Alert("您选择地址没有解析到结果!");
                                }
                            }, result.fregion.fname);

                            /*地图结束*/
                        }

                        if (result) {
                            switch (operationNo) {
                                case 'serviceprogress'://订单进度
                                    var timepanel = DomTag('.time');
                                    var ion = DomTag('.ion-record');
                                    var _alert = DomTag('.alert');
                                    var arrow_up = DomTag('.arrow-up');
                                    var il = timepanel.length;
                                    for (var d in result) {
                                        il--;
                                        var _val = result[d];
                                        if (String(_val).isNullOrEmpty()) {
                                            if (il == 4) {
                                                AddClass(timepanel[il].parentNode.parentNode, "hide");
                                            }
                                            timepanel[il].innerHTML = "";
                                        }
                                        else {
                                            AddClass(ion[il], "blue-light");
                                            timepanel[il].innerHTML = String(_val).ToDate().ToString("<div>MM月dd日</div><div>HH:mm</div>");
                                            for (var i = 0; i < Children(arrow_up[il], 'div').length; i++) {
                                                AddClass(Children(arrow_up[il], 'div')[i], 'border-right')
                                            }
                                            if (_alert[il - 1].getAttribute('class').indexOf('hide') > -1) {
                                                RemoveClass(_alert[il - 1], 'hide');
                                                AddClass(_alert[il - 1].parentNode, 'link');
                                            }
                                        }
                                    }
                                    break;
                                default:
                                    if (result.fserstatus) {
                                        var _sersta = parseInt(String(result.fserstatus.id).replace("sersta", ""));
                                        if (_sersta > 4) {
                                            var _di = CreateDom("i");
                                            if (result.fisschedule) {
                                                _di.className = "orange ion-android-alarm-clock";
                                                _di.onclick = function () {
                                                    Redirect(furl + "/SD/SdDetail.html", "fbillno=" + result.fbillno);
                                                }
                                            }
                                            else {
                                                _di.className = "gray ion-android-alarm-clock";
                                                _di.onclick = function () {
                                                    Redirect(furl + "/SD/AddSchedule.html", "fbillno=" + result.fbillno);
                                                }
                                            }

                                            DomTag("appointdate").parentNode.parentNode.appendChild(_di);
                                        }
                                        changeBtn(_sersta);
                                    }
                                    var Init = new HtmlSetData();
                                    Init.Init(result);
                                    var html = '';
                                    billno = result.fbillno;
                                    for (var i = 0; i < result.fproductentry.length; i++) {
                                        if (!String(result.fproductentry[i].fseritemid.fname).isNullOrEmpty()) {
                                            html += result.fproductentry[i].fseritemid.fname + ' ';
                                        }
                                    }
                                    DomTag('serdetail').innerText = html;
                                    for (var i = 0; i < result.fhistoryentry.length; i++) {
                                        if (!String(result.fhistoryentry[i].fappointdate).isNullOrEmpty()) {
                                            DomTag('appointdate').innerText = result.fhistoryentry[i].fappointdate;
                                        }
                                    }

                                    //订单描述三行内折行显示，多于三行则水平滚动条显示
                                    if (DomTag('fdescription').offsetHeight > 102) {
                                        RemoveClass(DomTag('.descparent')[0], 'minheight');
                                        AddClass(DomTag('.descparent')[0], 'minheight2')
                                        AddClass('fdescription', 'over-scroll-y');
                                    }

                                    break;
                            }
                        } else {
                            Message.Alert('该订单号不存在');
                        }

                    }
                }
            });
        }
        //根据从不同页面跳转过来进行改变底部按钮
        function changeBtn(operation) {
            var arr = [];
            switch (operation) {
                case 2://待确认
                    AddClass('problemfeed', 'hide');
                    arr.push('拒绝订单', '确认接收', 'refuse', 'receive');
                    break;
                case 4:
                    AddClass('problemfeed', 'hide');
                    arr.push('转单', '确认预约', 'select', 'confirm');
                    break;
                case 3:
                    AddClass('problemfeed', 'hide');
                    RemoveClass('refusereason', 'hide');
                    RemoveClass('task', 'bg-blue');
                    AddClass('task', 'btn-bg-grey');
                    DomTag('task').innerText = '拒单待审核';
                    break;
                case 6:
                    RemoveClass('problemfeed', 'hide');
                    arr.push('反馈', '完工汇报', 'feedback', 'donereport');
                    break;
                case 5:
                    RemoveClass('problemfeed', 'hide');
                    arr.push('完工查询', '显示二维码', 'donesearch', 'showQrcode');
                    break;
                case 8:
                case 7:
                case 11:
                    RemoveClass('problemfeed', 'hide');
                    arr.push('完工查询', '查看评价', 'donesearch', 'showEvaluat');
                    break;
                case 10:
                    AddClass('problemfeed', 'hide');
                    AddClass('task', 'hide');
                    var Scroll = DomTag('Scroll');
                    if (Scroll) {
                        Scroll.style.height = Pixel.H - 94 + "px";
                    }
                    break;
            }
            if (arr.length > 0) {
                AddClass('refusereason', 'hide')
                var Html = '<a onclick="pageJump(\'' + arr[2] + '\')">';
                Html += arr[0];
                Html += '</a>';
                Html += '<a onclick="pageJump(\'' + arr[3] + '\')">';
                Html += arr[1];
                Html += '</a>';
                DomTag('task').innerHTML = Html;
            }
        };

        //二维码和评价显示的关闭
        function cancel() {
            AddClass(arguments[0], 'hide');
            AddClass(DomTag('.mask-layer')[0], 'hide')
        }

        function BackEvent() {
            var E = String(localStorage.getItem("reloadhome"));
            if (!E.isNullOrEmpty()) {
                localStorage.setItem("FunEvent", E);
            }
            localStorage.removeItem("reloadhome");
            Andoggy.finishPage();
        }
    </script>
</body>
</html>
