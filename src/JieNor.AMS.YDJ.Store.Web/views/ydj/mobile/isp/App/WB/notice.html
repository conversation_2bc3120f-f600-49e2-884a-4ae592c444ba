<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>公告</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body class="workbench">
    <div class="bar bar-header bar-white">
        <i class="bar-title-left icon ion-ios-arrow-back blue button button-icon"></i>
        <div class="title">公告</div>
    </div>
    <div class="tabs tabs-changes marginTop44 bg-white top">
        <a class="tab-item active">未读<span class="unread hide num"></span></a>
        <a class="tab-item">已读</a>
    </div>
    <div id="wrapper">
        <div id="scroller">
            <div id="scroller-pullDown" style="top:60px;">
                <span id="down-icon" class="icon-double-angle-down pull-down-icon"></span>
                <span id="pullDown-msg" class="pull-down-msg">下拉刷新</span>
            </div>
            <div id="scroller-content">
                <div class="content has-header scroll marginTop95" id="notice">

                </div>
            </div>
            <div id="scroller-pullUp" class="hide">
                <span id="up-icon" class="icon-double-angle-up pull-up-icon"></span>
                <span id="pullUp-msg" class="pull-up-msg">加载更多</span>
            </div>
        </div>
    </div>

    <script src="/fw/include/jquery/jquery-1.11.3.min.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/iscroll.js"></script>
    <!--Reference the SignalR library.广播通信插件 -->
    <script src="/fw/include/signalr/jquery.signalR-2.2.2.js"></script>
    <!--Reference the autogenerated SignalR hub script. -->
    <script src="/signalr/hubs"></script>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>

    <script>
        var loghub = $.connection.log;
        var imhub = $.connection.ydjIMHub;
        //连接消息通道并获取消息
        // Start the connection.
        var LinkStatus = false;
        var PageIndex = 1;
        var PageSize = 10;
        var ReadStatus = false;
        var Ismore = true;
        var myScroll = null;
        var UnReadCount = 0;
        $.connection.hub.start().done(function (r) {
            LinkStatus = true;
        })
        .fail(function (e) {
            console.info(e);
        });
        var sll = setInterval(function () {
            if (!LinkStatus) {
                return;
            }
            clearInterval(sll);
            LoadData();
        }, 500);
        function LoadData() {
            imhub.server["getNoticeMessageListByPage"](PageIndex, PageSize, ReadStatus).done(function (r) {
                var data = r.data;
                if (!ReadStatus) {
                    UnReadCount = r.count;
                    if (UnReadCount > 0) {
                        RemoveClass(DomTag('.unread')[0], 'hide');
                        DomTag('.num')[0].innerHTML = numbernine(r.count);
                    }
                }
                if (data.length < PageSize) {
                    Ismore = false;
                }
                for (var i = 0; i < data.length; i++) {
                    var item = data[i];
                    var Html = '<ul class="bg-white" onclick="setHaveReadAndJumpPage(\'' + item.id + '\')" data-index="' + ((PageIndex - 1) * PageSize + i) + '">';
                    Html += '<li class="border">';
                    Html += '<p class="grey">' + item.subject + '</p>';
                    Html += '<p>' + item.subject;
                    Html += '<span>见明细</span>';
                    Html += '</p>';
                    Html += '<p><span>' + item.cDate.split(' ')[0] + '</span><span class="floatR blue">' + item.senderKey + '</span></p>';
                    Html += '</li>';
                    Html += '<li class="grey">查看详情</li>';
                    Html += '</ul>';
                    Html.CreateHtml('notice');
                }
                myScroll.refresh();
            }).fail(function (e) { console.info(e); });
        }
        var _tabitem = DomTag('.tab-item');
        var len = _tabitem.length;
        //点击不同tab进行样式切换
        for (var i = 0; i < _tabitem.length; i++) {
            (function (_i, _len) {
                _tabitem[i].onclick = function () {
                    AddClass('scroller-pullUp', 'hide');
                    for (var j = 0; j < _len; j++) {
                        if (j == _i) {
                            AddClass(_tabitem[j], "active");
                            ReadStatus = j == 0 ? false : true;
                            Ismore = true;
                            PageIndex = 1;
                            DomTag('notice').innerHTML = '';
                            LoadData();
                        }
                        else {
                            RemoveClass(_tabitem[j], "active");
                        }
                    }
                };
            })(i, _tabitem.length);
        }

        document.addEventListener('touchmove', function (e) { e.preventDefault(); }, false);

        function setHaveReadAndJumpPage() {
            if (!ReadStatus) {//未读状态
                var e = event || window.event;
                var dom = e.target;
                while (dom.tagName.toLocaleLowerCase() != "ul") {
                    dom = dom.parentNode;
                }
                dom.parentNode.removeChild(dom);
                imhub.server.setMessageReadStatus(arguments[0], true);
                UnReadCount--;
                if (UnReadCount > 0) {
                    DomTag('.num')[0].innerHTML = numbernine(UnReadCount);
                }
                else {
                    AddClass(DomTag('.num')[0], "hide");
                }
                if (parseInt(dom.getAttribute("data-index")) == 0) {
                    localStorage.setItem("reloadhome", "true");
                } else {
                    localStorage.setItem("setHome", "true");
                }
            }
            Redirect('/views/ydj/mobile/isp/App/WB/notice_detail.html', 'fid=' + arguments[0]);
        }
        window.addEventListener("load", loaded, false);


        //下拉刷新
        function ReLoadData() {
            PageIndex = 1;
            LoadData();
        }

        //上拉加载数据
        function DataEvent() {
            PageIndex++;
            LoadData();
        }


        /*Other*/
        //滚动条
        if (DomTag('scroller')) {
            var upIcon = $("#up-icon"),
                downIcon = $("#down-icon");
            myScroll = new IScroll('#wrapper', {
                click: true,
                momentum: true,
                probeType: 3, mouseWheel: true
            });
            myScroll.refresh();
        }
        function loaded() {
            setTimeout(function () {
                myScroll.on("scroll", function () {
                    var y = this.y,
                        maxY = this.maxScrollY - y,
                        downHasClass = downIcon.hasClass("reverse_icon"),
                        upHasClass = upIcon.hasClass("reverse_icon");

                    if (y >= 40) {
                        !downHasClass && downIcon.addClass("reverse_icon");
                        return "";
                    } else if (y < 40 && y > 0) {
                        downHasClass && downIcon.removeClass("reverse_icon");
                        return "";
                    }

                    if (maxY >= 40) {
                        !upHasClass && upIcon.addClass("reverse_icon");
                        return "";
                    } else if (maxY < 40 && maxY >= 0) {
                        upHasClass && upIcon.removeClass("reverse_icon");
                        return "";
                    }
                });

                myScroll.on("slideDown", function () {
                    if (this.y > 40) {
                        Ismore = true;
                        ReLoadData();
                        upIcon.removeClass("reverse_icon")
                    }
                });

                myScroll.on("slideUp", function () {
                    if (this.maxScrollY - this.y > 40) {
                        RemoveClass('scroller-pullUp', 'hide');
                        if (!Ismore) {
                            $("#pullUp-msg").html("没有更多数据了");
                            return;
                        }
                        DataEvent();
                        upIcon.removeClass("reverse_icon")
                    }
                });
            }, 500);
        }
        DomTag('.ion-ios-arrow-back')[0].onclick = function () {

            try {
                if (String(localStorage.getItem("reloadhome")).Boolean()) {
                    localStorage.setItem("Reload", "true");
                    localStorage.removeItem("reloadhome");
                }
                if (String(localStorage.getItem("setHome")).Boolean()) {
                    localStorage.setItem("FunEvent", "TabExec(1)");
                    localStorage.removeItem("setHome")
                }
                $.connection.hub.stop();
            } catch (e) {
                Message.Alert(e.message);
            }
            Andoggy.finishPage();
        }
    </script>
</body>
</html>
