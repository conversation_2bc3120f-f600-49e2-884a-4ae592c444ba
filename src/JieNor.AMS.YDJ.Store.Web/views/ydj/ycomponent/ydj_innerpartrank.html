<div class="no-pad index-boxh" event="click">
    <div class="linechart">
        <div class="info-title">
            <select class="form-control select2me pull-left" name="frankselect" style="width:140px;display: inline-block;margin-right:5px;"></select>
            <select class="form-control select2me simple" name="fdept" style="width: 90px;display: inline-block;"></select>
            <div class="form-group display-hide">
                <label class="col-md-3 control-label">受理部门</label>
                <div class="col-md-9">
                    <div class="input-icon right input-group">
                        <i class="fa"></i>
                        <input type="lookup" class="form-control" autocomplete="off"
                               name="fdeptid" placeholder="受理部门" />
                    </div>
                </div>
            </div>
            <div class="form-group display-hide">
                <label class="col-md-3 control-label">开始时间</label>
                <div class="col-md-8">
                    <div class="input-group date date-picker">
                        <input type="text" class="form-control" name="flasttime" />
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group display-hide">
                <label class="col-md-3 control-label">结束时间</label>
                <div class="col-md-8">
                    <div class="input-group date date-picker">
                        <input type="text" class="form-control" name="fnowtime" />
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>

            <button class="rank lastmonth" optype="rank" data-param="filterKey:'上月',dtType:'lastmonth'">上月</button>
            <button class="rank thismonth" optype="rank" data-param="filterKey:'本月',dtType:'thismonth'">本月</button>
            <button class="rank lastweek" optype="rank" data-param="filterKey:'上周',dtType:'lastweek'">上周</button>
            <button class="rank active thisweek" optype="rank" data-param="filterKey:'本周',dtType:'thisweek'">本周</button>
        </div>
        <div class="ranking col-xs-12 no-pad" chartid="rpt_innerpartranking" style="height:300px;margin-top:15px;">

        </div>
        <div class="ranking_nodata display-hide">
            <p>无关联数据</p>
        </div>
    </div>
</div>