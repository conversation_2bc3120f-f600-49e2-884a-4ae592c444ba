<form action="###" class="form-horizontal">
    <div class="portlet box yellow-casablanca">
        <div class="portlet-title">
            <div class="caption">表达式语法解析</div>
            <div class="tools"><a href="###" class="collapse"></a></div>
        </div>
        <div class="portlet-body form">
            <div class="form-body">
                <div class="row">
                    <div class="col-md-7">
                        <div class="form-group">
                            <label class="col-md-3 control-label">字符串格式分割符号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" placeholder="字符串格式分割符号" name="fstringseparators">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <label class="col-md-10 control-label text-left">and,or,in,like之类的,关键字之间用英文“,”号隔开</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-7">
                        <div class="form-group">
                            <label class="col-md-3 control-label">解析字符串格式正则表达式</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" placeholder="解析字符串格式正则表达式" name="fregularexpression">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <label class="col-md-10 control-label text-left">需要进行base64加密，以密文形式保存，正则表达式明文可能保存后丢失原串内容</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-7">
                        <div class="form-group">
                            <label class="col-md-3 control-label">字符串内容分割符号</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" placeholder="字符串内容分割符号" name="fstringcontentseparators">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <label class="col-md-10 control-label text-left" ">用于获取固定格式的键值对形式的值，比如 [床架长]=200 配置 " ]=" 得到" 床架长"、200</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>