<style>
.attrbtn-box .attr_button{
    font-size: 14px;
    background: #F7F9FB;
    color: #16325C;
    padding: 6px 15px;
    border: 1px #E4E4E4 solid;
    margin-right: 10px;
    border-radius: 3px !important;
    outline: none;
    margin-bottom: 10px;
	-webkit-transition: 0.2s;
	-moz-transition: 0.2s;
	transition: 0.2s;
}
.attrbtn-box .attr_button:hover{
    background: rgb(237, 239, 241);
	-webkit-transition: 0.2s;
	-moz-transition: 0.2s;
	transition: 0.2s;
}
.attrbtn-box .attr_button.active{
    background: #5a758a;
    color: #fff;
    border: 1px #5a758a solid;
}
</style>
<!--添加商品属性弹窗-->
<form action="###" class="form-horizontal">
    <div class="row">
        <div class="form-group" style="background: #fff !important;">
            <div class="col-xs-8" style="line-height: 20px;font-size: 14px;color: #45535e;padding-left:27px;">可选属性：</div>
        </div>
        <div class="col-md-12 attrbtn-box">
        	
        </div>
    </div>
</form>

<div class="Btn-menu static-menu dialog-actions">
    <button id="" type="button" opcode="cancel" class="btn cancel">取消</button>
    <button id="" type="button" opcode="confirm" class="btn confirm">确定</button>
</div>