<style>
    .box-one{
	    border: 1px solid #e0e0e0;
	    border-radius: 4px!important;
	    background: #fff;
	    padding: 13px;
        padding-bottom:11px;
        margin-bottom:8px;
    }
    .portlet>.portlet-title>.caption.act{
	    line-height: 32px;
	    padding-right: 6px;
    }
    .button-right{
	    line-height: 46px;
    }
    .button-right button{
	    margin-left: 0px;
	    margin-right:-10px;
    }
    .syn-account{
        padding:0px;
        min-height:78px;
        line-height:30px;
        padding:8px 0px;
        color:#868686;
        font-size:18px;
        margin-bottom:8px;
    }
    .syn-account .balance{
        color:#019fe8;
        margin:0px 5px 0px 5px;
    }
    .syn-account a{
        color:#22b169;
        margin-left:5px;
    }
    .syn-account .syn-account-item{
        text-align:center;
    }
    .syn-account .syn-account-item {
        border-right:solid 1px #D7D7D7;
    }
    .syn-account .syn-account-item:last-child {
        border-right:none;
    }
</style>
<div class="portlet box yellow-casablanca" style="margin-bottom: 0px;">
    <div class="box-one clearfix syn-account">
        <div class="clearfix account-info">
            <!--此处html由js插件动态渲染-->
        </div>
    </div>
    <div class="box-one clearfix">
        <div class="clearfix">
            <div class="col-md-3">
                <div class="input-icon right input-group">
                    <i class="fa"></i>
                    <input type="lookup" class="form-control" autocomplete="off"
                           name="fcustomerid" placeholder="客户" maxlength="50" />
                </div>
            </div>
            <div class="col-md-3">
                <div class="btn-group time-area">
                    <button type="button" optype="tswk" event="click" datetype="1" class="btn btn-default btn-sm active" timearea="true">本月</button>
                    <button type="button" optype="ltwk" event="click" datetype="2" class="btn btn-default btn-sm">上月</button>
                    <button type="button" optype="tsmh" event="click" datetype="3" class="btn btn-default btn-sm">本年</button>
                    <button type="button" optype="ltmh" event="click" datetype="4" class="btn btn-default btn-sm">去年</button>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group date date-picker col-md-4 pull-left">
                    <input type="text" class="form-control" name="fstartdate" placeholder="开始日期" />
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
                <div class="btn btn-sm group-space">至</div>
                <div class="input-group date date-picker col-md-4 pull-left">
                    <input type="text" class="form-control" name="fenddate" placeholder="结束日期" />
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
                <div class="input-group col-md-3 pull-left" style="padding-left:10px;">
                    <button type="button" opcode="search" class="save-btn">查询</button>
                </div>
            </div>
        </div>
    </div>
    <div class="portlet-title">
        <div class="caption act" style="padding-right:30px;">账户流水</div>
        <div class="button-right account-search time-area">
            <!--此处html由js插件动态渲染-->
        </div>
    </div>
    <div class="portlet-body form">
        <div class="form-body">
            <div class="row">
                <div class="col-md-12">
                    <table entryid="freportlist" data-options="allowAdd:false,allowDel:false,allowEdit:false,defRow:0,height:400"></table>
                </div>
            </div>
        </div>
    </div>
</div>