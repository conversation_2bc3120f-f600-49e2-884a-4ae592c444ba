<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="rpt_indbqty" el="8" basemodel="rpt_basetmpl" cn="分步式调拨在途明细" rac="false" >
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="分步式调拨在途明细">

        <input group="基本信息" el="100" ek="fbillhead" id="fbillno" fn="fbillno" pn="fbillno" visible="-1" cn="单据编号" lock="-1" copy="0" lix="1" notrace="true" ts="" />

        <input el="106" ek="fbillhead" id="fbizobject" fn="fbizobject" pn="fbizobject" cn="单据类型" visible="-1" refid="sys_bizobject" sformid="" lix="2" width="150" />

        <input group="基本信息" el="103" ek="fbillhead" id="fqty" fn="fqty" pn="fqty" cn="调拨在途数量" visible="-1" format="0,000.00" width="100" lix="3" />

        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="单据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="单据状态" visible="-1" xlsin="0" copy="0" lix="4" align="center" defls="true"></select>

        <input group="基本信息" el="118" lix="5" ek="fbillhead" visible="-1" id="fcreatorid" fn="fcreatorid" cn="创建人" />

        <input group="基本信息" el="113" ek="fbillhead" visible="-1" id="fcreatedate" fn="fcreatedate" pn="fcreatedate" cn="创建日期" lix="6" width="120" format="yyyy-MM-dd HH:mm" copy="0" />

    </div>
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">

    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">

    </div>
</body>
</html>