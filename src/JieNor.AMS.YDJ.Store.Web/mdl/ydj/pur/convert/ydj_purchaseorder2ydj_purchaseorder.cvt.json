{"Id": "ydj_purchaseorder2ydj_purchaseorder", "Number": "ydj_purchaseorder2ydj_purchaseorder", "Name": "采购订单下推采购增补单", "SourceFormId": "ydj_purchaseorder", "TargetFormId": "ydj_purchaseorder", "ControlFieldKey": "", "SourceControlFieldKey": "", "RelationFieldKey": "", "RealtionFormIdFieldKey": "", "ConvertWay": 0, "ActiveEntityKey": "f<PERSON>head", "FilterString": "fstatus='E' and ftype='order_type_01'", "Message": "增补失败：\r\n1、采购订单必须是已审核状态！\r\n2、采购订单的业务类型必须是正单！", "FieldMappings": [{"Id": "ftype", "Name": "业务类型", "MapType": 1, "SrcFieldId": "'order_type_02'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fbilltypeid", "Name": "单据类型", "MapType": 0, "SrcFieldId": "fbilltypeid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fdate", "Name": "订单日期", "MapType": 0, "SrcFieldId": "fdate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpickdate", "Name": "交货日期", "MapType": 0, "SrcFieldId": "fpickdate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsupplierid", "Name": "供应商", "MapType": 0, "SrcFieldId": "fsupplierid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpostaffid", "Name": "采购员", "MapType": 0, "SrcFieldId": "fpostaffid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpodeptid", "Name": "采购部门", "MapType": 0, "SrcFieldId": "fpodeptid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsupplieraddr", "Name": "供货地址", "MapType": 0, "SrcFieldId": "fsupplieraddr", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsupplieroldorderno", "Name": "供货方正单单号", "MapType": 0, "SrcFieldId": "fsupplierorderno", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fdescription", "Name": "备注", "MapType": 0, "SrcFieldId": "fdescription", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcetype", "Name": "源单类型", "MapType": 1, "SrcFieldId": "'ydj_purchaseorder'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcenumber", "Name": "源单编号", "MapType": 0, "SrcFieldId": "f<PERSON><PERSON>", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "ftranid", "Name": "交易流水标识", "MapType": 1, "SrcFieldId": "''", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fparenttranid", "Name": "父级交易流水标识", "MapType": 0, "SrcFieldId": "ftranid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "ftoptranid", "Name": "顶级交易流水标识", "MapType": 0, "SrcFieldId": "ftoptranid", "MapActionWhenGrouping": 0, "Order": 0}], "BillGroups": [{"Id": "f<PERSON><PERSON>", "Order": 1}], "FieldGroups": []}