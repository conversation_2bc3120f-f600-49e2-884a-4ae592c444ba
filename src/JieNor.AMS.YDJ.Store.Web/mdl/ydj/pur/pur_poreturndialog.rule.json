{
  //规则引擎基类
  "base": "",

  //定义表单锁定规则
  "lockRules": [

    //基本信息字段的锁定与解锁
    {
      "id": "lock_refundamountfield",
      "expression": "field:frefundamount|freturntype == 'postockreturn_scenetype_01'"
    },
    //结算按钮的隐藏与显示
    {
      "id": "unlock_refundamountfield",
      "expression": "field:$frefundamount|freturntype != 'postockreturn_scenetype_01'"
    },
      //此规则表示：该商品对应的商品商品属性“允许定制”=true时放开，为false时，锁定
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdesc$|fcustom!=true"
    },
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdesc|fcustom==true"
    }
    
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "famount=fprice*fqty" },
    { "expression": "fpoamount=fpoprice*fqty" }
    //客户基础资料值变化时，携带客户属性字段到页面指定的字段上面
    
  ]
}