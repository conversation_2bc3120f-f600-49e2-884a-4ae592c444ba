{
  //规则引擎基类
  "base": "/mdl/ydj/tpl/ydj_logisticnoticetpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lockheadfield_bypush",
      "expression": "field:freturntype,fsupplierid$|fsourcenumber!='' and fsourcenumber!=' '"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fpodeptid=fpostaffid__fdeptid" },
    { "expression": "flinkstaffid=fsupplierid__fcontacts" },
    { "expression": "flinkmobile=fsupplierid__fphone" },
    { "expression": "flinkaddress=fsupplierid__faddress" },
    { "expression": "fbizunitid=fmaterialid__fpurunitid" },
    { "expression": "fenablebarcode=getBillTypeParam(fbilltype,'fenablebarcode')" },
    { "expression": "fautobcoutstock=getBillTypeParam(fbilltype,'fautobcoutstock')" },
    { "expression": "factualreturnamount=sum(famount)|freturntype=='postockreturn_biztype_02'" },
    { "expression": "factualreturnamount=0|freturntype=='postockreturn_biztype_01'" }
  ]
}