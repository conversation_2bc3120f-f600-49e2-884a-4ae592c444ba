{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lock_pushmenu",
      "expression": "menu:$tbPush2Order|fstatus=='E'"
    },
    {
      "id": "unlock_pushmenu",
      "expression": "menu:tbPush2Order|fstatus!='E'"
    },
    {
      "id": "lock_entryfield",
      "expression": "field:fmaterialid,fcustomdesc,fattrinfo,funitid,fdemanddate,fqty$|fsourcebillno != '' and fsourcebillno != ' '"
    },
    {
      "id": "unlock_entryfield",
      "expression": "field:$fmaterialid,fcustomdesc,fattrinfo,funitid,fdemanddate,fqty|fsourcebillno == '' or fsourcebillno == ' '"
    },
      //此规则表示：该商品对应的商品商品属性“允许定制”=true时放开，为false时，锁定
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdesc$|fcustom!=true"
    },
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdesc|fcustom==true"
    },
    //此规则表示：该商品对应的商品商品属性“允许选配”=true时，辅助属性放开，为false时，锁定
    {
      "id": "lock_fsel",
      "expression": "field:fattrinfo$|fispresetprop!=true"
    },
    {
      "id": "unlock_fsel",
      "expression": "field:$fattrinfo|fispresetprop==true"
    },
    {
      "id": "lock_changefield",
      "expression": "field:fattrinfo,fcustomdesc$|fchangestatus=='1' and fstatus=='B' and forderqty>0"
    },
    {
      "id": "unlock_changefield",
      "expression": "field:$fattrinfo,fcustomdesc|fchangestatus=='1' and fstatus=='B' and forderqty<=0"
    },
    {
      "id": "lock_fownerid",
      "expression": "field:fownerid$|fownertype=='' or fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_fownerid",
      "expression": "field:$fownerid|fownertype!='' and fstatus!='D' and fstatus!='E'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
    //如果没有源单隐藏选单按钮
    {
      "id": "hide_pull",
      "expression": "other:[menu=pull]|fsourcenumber=='' or fsourcenumber == ' '"
    }
  ],

  //定义表单计算规则
  "calcRules": [

    //选择商品，携带单位
    { "expression": "funitid=fmaterialid__funitid" },
    { "expression": "fbizunitid=fmaterialid__fpurunitid" },
    { "expression": "freqdeptid=freqstaffid__fdeptid" },
    { "expression": "fpostdeptid=fpostaffid__fdeptid" },
    { "expression": "fqty=1|fmaterialid!='' and fqty<=0" },
    { "expression": "fsupplierid=fmaterialid__fsupplierid" },
    { "expression": "fbrandid=fmaterialid__fbrandid" },
    { "expression": "fseriesid=fmaterialid__fseriesid" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    { "expression": "fmtrlimage=getImages(fmaterialid,fattrinfo,fcustomdesc)" },
    //销售金额计算
    { "expression": "fsalamount=fsalprice*fbizqty" },
    //销售总额汇总
    { "expression": "fsumamount=sum(fsalamount)" }
  ]
}