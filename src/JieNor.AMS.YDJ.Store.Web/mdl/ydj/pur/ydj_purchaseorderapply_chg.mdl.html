<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
    <body id="ydj_purchaseorderapply_chg" basemodel="bill_basetmpl" el="1" cn="采购订单变更申请单" approvalflow="true">
        <div id="fbillhead" el="51" pk="fid" tn="t_ydj_purchaseorderapply_chg" pn="fbillhead" cn="采购订单变更申请单">
            <input type="text" id="fsourcetype" el="140" ek="fbillhead" fn="fsourcetype" pn="fsourcetype" apipn="sourceType" cn="源单类型" visible="1124" xlsin="0" copy="0" lock="-1">
            <input type="text" id="fsourcenumber" el="141" ek="fbillhead" fn="fsourcenumber" pn="fsourcenumber" apipn="sourceNumber" cn="采购订单编号" ctlfk="fsourcetype" visible="1124" xlsin="0" copy="0" lock="-1">
            <input group="基本信息" ek="fbillhead" el="100" id="fleveloneorderno" fn="fleveloneorderno" pn="fleveloneorderno" cn="一级合同号" visible="-1" copy="0" lock="-1" />
            <input group="基本信息" ek="fbillhead" el="152" id="fchangeapplystatus" fn="fchangeapplystatus" pn="fchangeapplystatus" cn="变更申请状态" vals="'01':'变更中','02':'变更完成','03':'驳回'" visible="-1" copy="0" lock="-1" />
            <input group="基本信息" ek="fbillhead" el="100" id="fchangeapplyreason" fn="fchangeapplyreason" pn="fchangeapplyreason" cn="变更申请原因" visible="-1" copy="0" lock="0" />
            <input group="基本信息" ek="fbillhead" el="100" id="frejectedreason" fn="frejectedreason" pn="frejectedreason" cn="被驳回原因" visible="-1" copy="0" lock="-1" />

            <input group="采购订单基本信息(变更前)" ek="fbillhead" el="112" id="fpickdate" fn="fpickdate" pn="fpickdate" cn="交货日期" visible="-1" copy="0" lock="-1" />
            <input group="采购订单基本信息(变更前)" ek="fbillhead" el="100" id="faddress" fn="faddress" pn="faddress" cn="详细地址" visible="-1" copy="0" lock="-1" />
            <input group="采购订单基本信息(变更后)" ek="fbillhead" el="112" id="fnewpickdate" fn="fnewpickdate" pn="fnewpickdate" cn="交货日期(新)" visible="-1" copy="0" lock="0" />
            <input group="采购订单基本信息(变更后)" ek="fbillhead" el="100" id="fnewaddress" fn="fnewaddress" pn="fnewaddress" cn="详细地址(新)" visible="-1" copy="0" lock="0" />
            <input group="基本信息" el="165" ek="fbillhead" visible="0" id="fsourceid" fn="fsourceid" pn="fsourceid" copy="0" lix="62" cn="源单ID" />
        </div>

        <table id="fentity" el="52" pk="fentryid" tn="t_ydj_purapplychgentry" pn="fentity" cn="商品明细" kfks="fmaterialid">
            <th lix="5" el="107" ek="fentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0" frozen="1"></th>
            <th lix="10" ek="fentity" el="106" id="fmaterialid" fn="fmaterialid" refid="ydj_product" multsel="true" sformid="" ts="" cn="商品" visible="1150" reflvt="1" width="160" copy="0"
                dfld="fspecifica,fvolume,fgrossload,fpacksize,fstockunitid,fselectioncategoryid,fselcategoryid,fbedpartflag,fcategoryid,fauxseriesid,fispresetprop,fpackqty,fseriesid,fseltypeid,fpackagtype,fmainorgid,fispartflag"
                must="1" lock="-1" copy="0" frozen="1">
            </th>
            <th lix="203" ek="fentity" el="132" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" nstdfk="funstdtype" pricefk="fprice" width="140" lock="-1" visible="1150"></th>
            <!-- <th el="100" ek="fentity" id="fattrinfo_first" cn="初始辅助属性" fn="fattrinfo_first" pn="fattrinfo_first" visible="1150" len="4000" width="200" lock="-1" /> -->

            <th lix="25" ek="fentity" el="100" len="2000" id="fcustomdes_e" fn="fcustomdes_e" pn="fcustomdes_e" cn="定制说明" width="200" visible="1150" lock="-1" copy="0" canchange="true"></th>

            <th lix="20" el="107" ek="fentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0"></th>
            <th lix="205" ek="fentity" el="103" id="fbizqty" fn="fbizqty" pn="fbizqty" cn="采购数量" ctlfk="fbizunitid" format="0,000.00" width="80" visible="1150" copy="0" lock="-1" notrace="false"></th>
            <th lix="210" ek="fentity" el="103" id="fnewbizqty" fn="fnewbizqty" pn="fnewbizqty" cn="采购数量(新)"  format="0,000.00" visible="1150" copy="0" canchange="true" notrace="false"></th>
            <!-- <th lix="205" ek="fentity" el="103" id="fbizqty" fn="fbizqty" pn="fbizqty" cn="采购数量" ctlfk="fbizunitid" format="0,000.00" basqtyfk="fqty" width="80" visible="1150" canchange="true" notrace="false"></th> -->
            <!-- <th lix="206" ek="fentity" el="109" id="funitid" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fmaterialid" width="80" lock="-1" visible="0" copy="0" must="1" notrace="false"></th> -->
            <!-- <th lix="800" ek="fentity" el="103" id="fqty" fn="fqty" ts="" cn="基本单位数量" ctlfk="funitid" format="0,000.00" width="100" visible="1086" copy="0" canchange="true" notrace="false" lock="-1"></th> -->
            <th lix="240" ek="fentity" el="109" id="fbizunitid" fn="fbizunitid" pn="fbizunitid" cn="采购单位" refid="ydj_unit" sformid="" ctlfk="fmaterialid" width="90" visible="0" must="0" notrace="false"></th>
            <th lix="800" ek="fentity" el="100" id="fsourceentryid" fn="fsourceentryid" pn="fsourceentryid" cn="来源单分录内码" visible="170" lock="-1" copy="0"></th>
            <th lix="800" el="140" ek="fentity" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单类型" visible="1086" copy="0" lock="-1"></th>
            <th lix="800" el="141" ek="fentity" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单编号" ctlfk="fsourceformid" visible="0" copy="0" lock="-1"></th>
            <th lix="800" el="100" ek="fentity" id="fsourceinterid_e" fn="fsourceinterid" ts="" cn="来源单内码" visible="0" copy="0" lock="-1"></th>
            <th lix="265" el="107" ek="fentity" visible="32" id="fissuitflag" fn="fissuitflag" pn="fissuitflag" cn="是否套件" ctlfk="fmaterialid" dispfk="fsuiteflag" lock="-1" refValueType="116"></th>
            <th lix="295" el="103" ek="fentity" id="fsubqty" fn="fsubqty" pn="fsubqty" visible="32" cn="子件数量" width="120" lock="-1" copy="0"  ts="" roundType="0" format="0,000.00"></th>
            <th lix="285" el="100" ek="fentity" visible="1150" id="fsuitcombnumber" fn="fsuitcombnumber" pn="fsuitcombnumber" cn="套件组合号" lock="-1"></th>
            <th lix="290" el="100" ek="fentity" visible="1150" id="fpartscombnumber" fn="fpartscombnumber" pn="fpartscombnumber" cn="配件组合号" lock="-1"></th>
            <th lix="325" el="100" ek="fentity" visible="1150" id="fsofacombnumber" fn="fsofacombnumber" pn="fsofacombnumber" cn="沙发组合号" lock="-1"></th>
            <th lix="800" el="116" ek="fentity" visible="-1" id="fiscombmain" fn="fiscombmain" pn="fiscombmain" cn="配件主商品" lock="-1"></th>
            <th lix="22" el="103" ek="fentity" id="fpartqty" fn="fpartqty" pn="fpartqty" cn="配件数量" format="0,000.00" width="100" visible="0" apipn="qty" copy="0" lock="-1" canchange="true"></th>
        </table>
        <!--表单操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
        <div id="opList">
            <ul el="10" ek="fbillhead" id="savesubmit" op="savesubmit" opn="保存并提交" data="" permid="fw_savesubmit"></ul>
        </div>

        <!--表单操作关联的权限项列表-->
        <div id="permList">
            <ul el="12" id="fw_savesubmit" cn="保存并提交"></ul>
        </div>
    </body>
</html>
