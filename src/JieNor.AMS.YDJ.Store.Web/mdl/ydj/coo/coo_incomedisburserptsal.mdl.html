<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="coo_incomedisburserptsal" el="4" basemodel="rpt_basetmpl" cn="销售收支明细" module="">

    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="销售收支明细">
        <input el="106" ek="fbillhead" visible="-1" id="fsettlemainid" cn="客户" refid="ydj_customer" />
        <input el="122" ek="fbillhead" visible="-1" id="fpurpose_s" cn="用途" refid="bd_enum" cg="业务用途" dfld="fenumitem" />
        <input el="122" ek="fbillhead" visible="-1" id="fway_s" cn="支付方式" refid="bd_enum" cg="收支记录支付方式" dfld="fenumitem" />
        <input el="122" ek="fbillhead" visible="-1" id="fbizstatus_s" cn="状态" refid="bd_enum" cg="收支记录业务状态" dfld="fenumitem" />
        <input el="152" ek="fbillhead" visible="-1" id="fmode_s" cn="状态" vals="'':'全部','mode_01':'协同','mode_02':'非协同'" />
        <input el="112" ek="fbillhead" visible="-1" id="fstartdate" cn="开始日期" />
        <input el="112" ek="fbillhead" visible="-1" id="fenddate" cn="结束日期" />
    </div>

    <table id="freportlist" el="52" pk="fentryid" pn="freportlist" cn="收支明细" esp="true">
        <tr>
            <th el="100" ek="freportlist" id="fid" fn="fid" cn="收支记录主键ID" visible="0" lock="-1" width="150" align="center"></th>
            <th el="100" ek="freportlist" id="ftranid" fn="ftranid" cn="流水号" visible="1150" lock="-1" width="130" align="center"></th>
            <th el="112" ek="freportlist" id="fdate" fn="fdate" cn="日期" visible="1150" lock="-1" width="80" align="center"></th>
            <th el="100" ek="freportlist" id="fcustomer" fn="fcustomer" cn="客户" visible="1150" lock="-1" width="120"></th>
            <th el="100" ek="freportlist" id="fpurcompany" fn="fpurcompany" cn="对方企业" visible="1150" lock="-1" width="180"></th>
            <th el="100" ek="freportlist" id="fpurposename" fn="fpurposename" cn="用途" visible="1150" lock="-1" width="80" align="center"></th>
            <th el="122" ek="freportlist" id="fpurpose" fn="fpurpose" cn="用途" visible="0" lock="-1" width="120" refid="bd_enum" cg="业务用途" dfld="fenumitem" align="center"></th>
            <th el="100" ek="freportlist" id="fwayname" fn="fwayname" cn="支付方式" visible="1150" lock="-1" width="120" align="center"></th>
            <th el="122" ek="freportlist" id="fway" fn="fway" cn="支付方式" visible="0" lock="-1" width="120" refid="bd_enum" cg="收支记录支付方式" dfld="fenumitem" align="center"></th>
            <th el="105" ek="freportlist" id="famount" fn="famount" cn="金额" visible="1150" lock="-1" width="100" format="0,000.00"></th>
            <th el="100" ek="freportlist" id="fbizstatusname" fn="fbizstatusname" cn="状态" visible="1150" lock="-1" width="70" align="center"></th>
            <th el="122" ek="freportlist" id="fbizstatus" fn="fbizstatus" cn="状态" visible="0" lock="-1" width="80" refid="bd_enum" cg="收支记录业务状态" dfld="fenumitem" align="center"></th>
            <th el="100" ek="freportlist" id="fcreatecompanyid" fn="fcreatecompanyid" cn="创建方Id" visible="0" lock="-1" width="100"></th>
            <th el="100" ek="freportlist" id="fsourcetype" fn="fsourcetype" cn="源单类型" visible="-1" lock="-1" width="80"></th>
            <th el="100" ek="freportlist" id="fsourcenumber" fn="fsourcenumber" cn="源单编号" visible="-1" lock="-1" width="110"></th>
            <th el="100" ek="freportlist" id="fdirection" fn="fdirection" cn="账户收支方向" visible="0" lock="-1" width="100"></th>
            <th el="100" ek="freportlist" id="fbizdirection" fn="fbizdirection" cn="业务收支方向" visible="0" lock="-1" width="100"></th>
            <th el="152" ek="freportlist" id="foperationmode" fn="foperationmode" cn="运营模式" visible="0" lock="-1" width="100"></th>
            <th el="100" ek="freportlist" id="fdescription" fn="fdescription" cn="备注" lock="-1" width="130" visible="1150"></th>
            <th el="116" ek="freportlist" id="fissyn" fn="fissyn" cn="是否协同" lock="-1" width="130" visible="0"></th>
            <th el="100" ek="freportlist" id="fcreator" fn="fcreator" cn="创建人" visible="1150" lock="-1" width="90"></th>
            <th el="113" ek="freportlist" id="fcreatedate" fn="fcreatedate" cn="创建时间" visible="1150" lock="-1" width="120" align="center"></th>
            <th el="100" ek="freportlist" id="fconfirmor" fn="fconfirmor" cn="确认人" visible="1150" lock="-1" width="90"></th>
            <th el="113" ek="freportlist" id="fconfirmdate" fn="fconfirmdate" cn="确认时间" visible="1150" lock="-1" width="120" align="center"></th>
            <th el="100" ek="freportlist" id="fverificstatusname" fn="fverificstatusname" cn="核销状态" visible="1150" lock="-1" width="100" align="center"></th>
            <th el="122" ek="freportlist" id="fverificstatus" fn="fverificstatus" cn="核销状态" visible="0" lock="-1" width="80" refid="bd_enum" cg="收支记录核销状态" dfld="fenumitem" align="center"></th>
            <th el="100" ek="freportlist" id="fverificor" fn="fverificor" cn="核销人" visible="1150" lock="-1" width="90"></th>
            <th el="113" ek="freportlist" id="fverificdate" fn="fverificdate" cn="核销时间" visible="1150" lock="-1" width="120" align="center"></th>
            <!--<th el="144" ek="freportlist" id="foperate" fn="foperate" cn="操作" visible="1150" lock="-1" width="125" btnid="detail" btntxt="详情" align="right"></th>-->
        </tr>
    </table>

</body>
</html>