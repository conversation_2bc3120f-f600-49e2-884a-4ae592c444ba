{"base": "/mdl/bd.menu.json", "common": [{"id": "tbNew", "visible": "false", "disabled": "false"}, {"id": "tbCopy", "visible": "false", "disabled": "false"}, {"id": "tbSubmit", "visible": "false", "disabled": "false"}, {"id": "tbUnsubmit", "visible": "false", "disabled": "false"}, {"id": "tb<PERSON><PERSON><PERSON>", "visible": "false", "disabled": "false"}, {"id": "tb<PERSON>na<PERSON>t", "visible": "false", "disabled": "false"}, {"id": "tbLookauditlog", "visible": "false", "disabled": "false"}, {"id": "tbForbid", "visible": "false", "disabled": "false"}, {"id": "tbUnforbid", "visible": "false", "disabled": "false"}], "listmenu": [], "billmenu": []}