<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_dealerinfo" basemodel="bd_basetmpl" el="3" cn="商户用户" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_dealerinfo" pn="fbillhead" cn="商户用户">

        <!--基本信息-->
        <input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" cn="手机号" visible="-1" />
        <input group="基本信息" el="122" id="fsex" ek="fbillhead" fn="fsex" refId="bd_enum" dfld="fenumitem" ts="" cg="性别" visible="0" cn="专业领域" />
        <input group="基本信息" el="122" id="fusertype" ek="fbillhead" fn="fusertype" refId="bd_enum" dfld="fenumitem" ts="" cg="用户类别" visible="0" cn="用户类别" />
        <input group="基本信息" el="106" ek="fbillhead" id="fdealerid" fn="fdealerid" pn="fdealerid" cn="所属商户" refid="ydj_customer" dfld="fname" filter="fcustype='customercate_03'" visible="-1" />
        <input group="基本信息" el="116" id="fisadmin" ek="fbillhead" fn="fisadmin" pn="fisadmin" cn="管理员" visible="-1" lock="-1" />
        
    </div>

</body>
</html>