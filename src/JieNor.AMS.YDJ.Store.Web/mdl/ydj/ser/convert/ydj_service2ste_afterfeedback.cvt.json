{
  "Id": "ydj_service2ste_afterfeedback",
  "Number": "ydj_service2ste_afterfeedback",
  "Name": "服务单下推售后反馈单",
  "SourceFormId": "ydj_service",
  "TargetFormId": "ste_afterfeedback",
  "ActiveEntityKey": "fproductentry",
  "FilterString": "",
  //"FilterString": "fserstatus='sersta04' or fserstatus='sersta03'",
  //"Message": "转售后失败：<br>服务状态必须是待完工或待评价状态！",
  "Message": "",
  "FieldMappings": [
    //{
    //  "Id": "fagentid",
    //  "Name": "慕思经销商",
    //  "MapType": 0,
    //  "SrcFieldId": "fagentid",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fagentid",
      "Name": "招商经销商",
      "MapType": 0,
      "SrcFieldId": "fagentid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstaffid",
      "Name": "售后人员",
      "MapType": 0,
      "SrcFieldId": "fmasterid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeptid",
      "Name": "售后部门",
      "MapType": 0,
      "SrcFieldId": "fteamid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fphone",
      "Name": "售后人员电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fquestiontype",
      "Name": "问题类别",
      "MapType": 0,
      "SrcFieldId": "fquestiontype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fquestiondesc",
      "Name": "问题描述",
      "MapType": 0,
      "SrcFieldId": "fquestiondesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fquestionimage",
      "Name": "问题图片",
      "MapType": 0,
      "SrcFieldId": "fquestionimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "finstitutiontype",
      "Name": "责任单位类型",
      "MapType": 0,
      "SrcFieldId": "finstitutiontype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdutysupplierid",
      "Name": "责任单位(供应商)",
      "MapType": 0,
      "SrcFieldId": "fdutysupplierid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdutycustomerid",
      "Name": "责任单位(客户)",
      "MapType": 0,
      "SrcFieldId": "fdutycustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdutystaffid",
      "Name": "责任单位(员工)",
      "MapType": 0,
      "SrcFieldId": "fdutystaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdutydeptid",
      "Name": "责任单位(部门)",
      "MapType": 0,
      "SrcFieldId": "fdutydeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fhandleconclusion",
      "Name": "内部处理结论",
      "MapType": 0,
      "SrcFieldId": "fhandleconclusion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fscheme",
      "Name": "内部处理方式",
      "MapType": 0,
      "SrcFieldId": "fscheme",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fauthcity",
      "Name": "授权城市",
      "MapType": 1,
      "SrcFieldId": "fagentid.fcityid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderno",
      "Name": "合同编号",
      "MapType": 0,
      "SrcFieldId": "forderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "反馈时间",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid",
      "Name": "联系人",
      "MapType": 0,
      "SrcFieldId": "fcollectrel",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    {
      "Id": "flinkmobile",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "fcollectpho",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkaddress",
      "Name": "联系地址",
      "MapType": 0,
      "SrcFieldId": "fcollectadd",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fzblinkaddress",
      "Name": "总部联系地址",
      "MapType": 0,
      "SrcFieldId": "fzbcollectadd",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_service'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //商品信息字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fcategoryid_e",
      "Name": "商品类别",
      "MapType": 0,
      "SrcFieldId": "fcategoryid",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid_e",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fqty",
      "Name": "基本单位实发数量",
      "MapType": 0,
      "SrcFieldId": "fqty_e",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "famount",
      "Name": "成交金额",
      "MapType": 0,
      "SrcFieldId": "famount_e",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_service'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fproductentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

    //总部处理结论字段
    //{
    //  "Id": "fsourceformid_e",
    //  "Name": "来源单类型",
    //  "MapType": 1,
    //  "SrcFieldId": "'ydj_service'",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourcebillno_e",
    //  "Name": "来源单编号",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillno",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourceinterid_e",
    //  "Name": "来源单内码",
    //  "MapType": 0,
    //  "SrcFieldId": "fbillhead.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //{
    //  "Id": "fsourceentryid_e",
    //  "Name": "来源单分录内码",
    //  "MapType": 0,
    //  "SrcFieldId": "fhqhandleentry.id",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //}
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}