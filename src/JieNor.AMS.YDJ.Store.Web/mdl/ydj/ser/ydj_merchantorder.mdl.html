<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_merchantorder" basemodel="bill_basetmpl" el="1" cn="商户订单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="ser_ydj_merchantorder" pn="fbillhead" cn="商户订单">
    	
    	<!--基本信息-->
    	<select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fservicetype" fn="fservicetype" pn="fservicetype" cn="服务类型" cg="服务类型" defval="'fres_type_02'" refid="bd_enum" dfld="fenumitem"></select>
    	<input group="基本信息" el="112" type="date" id="forderdate" ek="fbillhead" fn="forderdate" pn="forderdate" cn="服务日期" visible="-1" defval="@currentshortdate"/>
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fmerchantid" fn="fmerchantid" pn="fmerchantid" cn="商户" cg="商户" refid="ydj_customer" filter="fcustype='customercate_03'" />
        
        
        
        <input group="物流信息" el="106" ek="fbillhead" visible="-1" id="fcarid" fn="fcarid" pn="fcarid" cn="车辆类型" cg="车辆类型" refid="ser_truckinfo"/>
        <input group="物流信息" type="number" id="fceper" el="102" ek="FBillHead" fn="fceper" ts="" cn="物流公里" visible="-1" lix="35" />
        <input group="物流信息" el="100" type="text" id="fcollectadd" ek="fbillhead" fn="fcollectadd" ts="" visible="-1" cn="提货点" />
        <input group="物流信息" el="100" type="text" id="fcollectrel" ek="fbillhead" fn="fcollectrel" ts="" visible="-1" cn="提货联系人" />
        <input group="物流信息" el="100" type="text" id="fcollectpho" ek="fbillhead" fn="fcollectpho" ts="" visible="-1" cn="提货电话" />
        <input group="物流信息" el="116" type="checkbox" ek="FBillHead" id="fisupstairs" fn="fisupstairs" pn="fisupstairs" cn="需抬楼" visible="96"  />
        <input group="物流信息" el="116" type="checkbox" ek="FBillHead" id="fistransport" fn="fistransport" pn="fistransport" cn="需搬运" visible="96"  />
        
        <input group="物流信息" el="100" type="text" id="fname" ek="fbillhead" fn="fname" ts="" visible="-1" cn="业主名称" />
        <input group="物流信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" visible="-1" cn="业主电话" />
        <input group="物流信息" el="100" type="text" id="fcusaddress" ek="fbillhead" fn="fcusaddress" ts="" visible="-1" cn="目的地" />
        
        <input group="基本信息" el="100" type="text" id="fgetnote" ek="fbillhead" fn="fgetnote" ts="" visible="-1" cn="收款记录" lock="-1"/>
        <input group="基本信息" el="100" type="text" id="frenode" ek="fbillhead" fn="frenode" ts="" visible="-1" cn="退款记录" lock="-1"/>
        
		<select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="faddress" fn="faddress" pn="faddress" cn="详细地址" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fserstatus" fn="fserstatus" pn="fserstatus" cn="订单状态" cg="商户订单状态" lock="-1" ts="" refid="bd_enum" dfld="fenumitem" defval="'sht_serstatus00'" lix="3" copy="0"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fsettlestatus" fn="fsettlestatus" pn="fsettlestatus" cn="结算状态" cg="结算状态" lock="-1" ts="" refid="bd_enum" dfld="fenumitem" defval="'settle_status01'" copy="0"></select>
        <input el="106" ek="fbillhead" id="fservobjid" fn="fservobjid" pn="fservobjid" cn="服务方" refid="ydj_customer" lock="-1" visible="-1" copy="0" desc="基础资料：代理商" />
        <input el="106" ek="fbillhead" id="fsettleobjid" fn="fsettleobjid" pn="fsettleobjid" cn="结算方" refid="ydj_settlemain" lock="-1" visible="0" copy="0" desc="协同企业字段" />
		
        <input el="100" ek="fbillhead"  id="fserviceid" fn="fserviceid" pn="fserviceid" cn="服务单id"  visible="0"  copy="0"/>
		<input group="基本信息" el="105" type="text" id="fexpectamount" ek="fbillhead" fn="fexpectamount" pn="fexpectamount" ts="" visible="-1" cn="订单金额"  format="0,000.00" lock="-1"/>
        <input group="基本信息" el="105" type="text" id="fsettleamount" ek="fbillhead" fn="fsettleamount" pn="fsettleamount" ts="" visible="-1" cn="已结算金额" format="0,000.00" lock="-1" copy="0" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fsourcebillnum" fn="fsourcebillnum" pn="fsourcebillnum" cn="源单编号"  copy="0"/>
        <input group="基本信息" el="100" ek="fbillhead"  id="fsourceid" fn="fsourceid" pn="fsourceid" cn="源单id" visible="0" lock="-1"  copy="0"/>
        
        <!--后台隐藏字段冗余用，用于记录表体的物流信息-->
        <input group="物流信息" el="106" ek="fbillhead" type="text" id="fwlcaritemid" fn="fwlcaritemid" pn="fwlcaritemid" refid="ser_truckinfo" visible="96" cn="物流车辆" />
        <input group="物流信息" el="102" ek="fbillhead" type="number" id="fmileage" fn="fmileage" ts="" cn="里程数" visible="96" />
		<!--服务项目-->
	    <table id="fserviceentry" el="52" pk="fentryid" tn="ser_ydj_servicecate" pn="fserviceentry" cn="服务项目" kfks="fseritemid">
	        <tr>
	            <th el="106" ek="fserviceentry" id="fseritemid" fn="fseritemid" pn="fseritemid" cn="服务" refid="ydj_seritem" sformid="" visible="96" width="200"></th>
                <th el="109" ek="fserviceentry" id="funitid" fn="funitid" pn="funitid" cn="单位" refid="ydj_unit" sformid="" visible="96" width="100" lock="-1"></th>
                <th el="104" ek="fserviceentry" id="fprice" fn="fprice" ts="" cn="单价" visible="96" width="130" format="0,000.00"></th>
                <th el="102" ek="fserviceentry" id="fqty" fn="fqty" pn="fqty" cn="数量" visible="96" width="90"></th>
                <th el="105" ek="fserviceentry" id="famount" fn="famount" ts="" cn="金额" visible="96" width="140" format="0,000.00" lock="-1"></th>
                <th el="100" ek="fserviceentry" id="frequire" fn="frequire" pn="frequire" cn="要求" visible="96" width="200"></th>
	        </tr>
	    </table>
    	<input group="服务信息" type="text" id="fimage" el="135" ek="FBillHead" fn="fimage" pn="fimage" cn="商品图片" visible="0" len="2000" />
        
        
		<!--商户信息-->
        <!--<input group="商户信息" el="106" ek="fbillhead" visible="-1" id="fstoreid" fn="fstoreid" pn="fstoreid" cn="门店" cg="门店" refid="ser_store"/>-->
    	<input group="商户信息" el="100" ek="fbillhead" visible="-1" id="forderbillno" fn="forderbillno" pn="forderbillno" cn="订单号" copy="0"/>
    	<input group="商户信息" el="116" type="checkbox" id="fisdelivery" ek="fbillhead" fn="fisdelivery" visible="-1" cn="是否需送货" />
    	<input group="商户信息" el="112" type="date" id="finstalldate" ek="fbillhead" fn="finstalldate" pn="finstalldate" cn="安装日期" visible="0" defval="@currentshortdate"/>
    	<input group="商户信息" el="112" type="date" id="fdeliverydate" ek="fbillhead" fn="fdeliverydate" pn="fdeliverydate" cn="送货日期" visible="-1"/>
    	<input group="商户信息" el="100" ek="fbillhead" visible="-1" id="fcareful" fn="fcareful" pn="fcareful" cn="注意事项" len="500" />
		
        <!--派单信息-->
        <select group="派单信息" el="122" lock="-1" ek="fbillhead" visible="-1" id="fdispatchtype" fn="fdispatchtype" pn="fdispatchtype" cn="派单方式" cg="派单方式" refid="bd_enum" dfld="fenumitem"  copy="0"></select>
        <input group="派单信息" el="100" lock="-1" ek="fbillhead" visible="-1" id="fdispatchbillno" fn="fdispatchbillno" pn="fdispatchbillno" cn="配送单"  copy="0"/>        
        <input group="派单信息" el="112" lock="-1" type="date" id="fdispatchbilldate" ek="fbillhead" fn="fdispatchbilldate" pn="fdispatchbilldate" cn="派单日期" visible="-1"  copy="0"/>
			
        <!-- 原来的服务单号变成安装单号。 <input group="派单信息" el="100" ek="fbillhead" visible="-1" id="finstallbillno" fn="finstallbillno" pn="finstallbillno" cn="安装单" />-->
        <input group="基本信息" el="100" lock="-1" ek="fbillhead" visible="-1" id="fservicebillno" fn="fservicebillno" pn="fservicebillno" cn="安装单"   copy="0"/>
        <input group="派单信息" el="112" lock="-1" type="date" id="finstallbilldate" ek="fbillhead" fn="finstallbilldate" pn="finstallbilldate" cn="派单日期" visible="-1"  copy="0"/>

        <input group="派单信息" el="100" lock="-1" ek="fbillhead" visible="-1" id="fdispatchcombinebillno" fn="fdispatchcombinebillno" pn="fdispatchcombinebillno" cn="配送安装单"  copy="0"/>
        <input group="派单信息" el="112" lock="-1" type="date" id="fdispatchcombinebilldate" ek="fbillhead" fn="fdispatchcombinebilldate" pn="fdispatchcombinebilldate" cn="派单日期" visible="-1"  copy="0"/>


    </div>
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <!--商户订单状态字段定义
        sht_serstatus01	 	商户订单状态	已取消
        sht_serstatus02	 	商户订单状态	已拒单
        sht_serstatus03	 	商户订单状态	已提交
        sht_serstatus04	 	商户订单状态	平台审核
        sht_serstatus05	 	商户订单状态	商户确认
        sht_serstatus06	 	商户订单状态	服务中
        sht_serstatus07	 	商户订单状态	已完工
        sht_serstatus08	 	商户订单状态	验收完成
       
        sht_serstatus09	 	商户订单状态	已指派服务商（下一步：平台审核）       
    -->
    <div id="opList">
        <ul el="10" id="sht_submitbill" op="setstatus" opn="提交" permid="sht_submitbill"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus03'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus00\' or fserstatus==\'sht_serstatus02\' or fserstatus==\'sht_serstatus04\'','message':'订单状态只有为平台审核、平台已拒单或草稿时，才需要提交'}}"></ul>

        <ul el="10" id="sht_quoteprice" op="setstatus" opn="平台审核"  permid="sht_quoteprice"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus04'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus03\'','message':'只有订单状态为已提交时才可以执行此操作！'}}"></ul>

        <ul el="10" id="sht_refusebill" op="setstatus" opn="拒单" permid="sht_refusebill"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus02'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus03\' or fserstatus==\'sht_serstatus09\'','message':'只有订单状态为已提交、已指派服务商时才可以执行此操作！'}}"></ul>

        <ul el="10" id="sht_confirmprice" op="setstatus" opn="商户确认" permid="sht_confirmprice"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus05'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus04\' or fserstatus==\'sht_serstatus09\'','message':'只有订单状态为平台审核 或已指派服务商时时才可以执行此操作'}}"></ul>

        <ul el="10" id="sht_dispatchbill" op="setstatus" opn="派单" permid="sht_dispatchbill"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus06'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus05\'','message':'只有订单状态为商户确认时才可以执行此操作'}}"></ul>

        <ul el="10" id="sht_finishbill" op="setstatus" opn="完工" permid="sht_finishbill"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus07'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus06\'','message':'只有订单状态为服务中时才可以执行此操作'}}"></ul>

        <ul el="10" id="sht_cancelbill" op="setstatus" opn="取消" permid="sht_cancelbill"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus01'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus00\' or fserstatus==\'sht_serstatus02\' or fserstatus==\'sht_serstatus03\' or fserstatus==\'sht_serstatus04\'','message':'只有订单状态为已提交、已拒单或平台审核时才可以执行此操作'}}"></ul>

        
        
        <ul el="10" id="sht_acceptbill" op="setstatus" opn="验收完成" permid="sht_acceptbill"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus08'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus07\'','message':'只有订单状态为已完工时才可以执行此操作'}}"></ul>

        <ul el="10" id="sht_acceptcancel" op="setstatus" opn="取消验收" permid="sht_acceptcancel"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus07'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus08\'','message':'只有订单状态为验收完成时才可以执行此操作'}}"></ul>

        <ul el="10" id="sht_confirmcancel" op="setstatus" opn="取消确认" permid="sht_confirmcancel"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus04'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus05\'','message':'只有订单状态为商户确认时，才可以执行驳回操作'}}"></ul>
        
        <ul el="10" id="sht_confirmagent" op="setstatus" opn="指派服务商" permid="sht_confirmagent"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus09'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus03\'','message':'只有订单状态为已提交时，才可以指派服务商'}}"></ul>

        <ul el="10" id="sht_refuseagent" op="setstatus" opn="驳回" permid="sht_refuseagent"
            data="{'parameter':{'statusFieldKey':'fserstatus','statusValue':'sht_serstatus02'},
                    'condition':{'expr':'fserstatus==\'sht_serstatus03\'','message':'只有订单状态为已提交时，才可以执行此操作'}}"></ul>
        <!--校验规则：当点击某个操作是，必须满足什么条件，例如：点击作废时，服务单ID必须为空（表示没有下游单据）
            expr:条件表达式，message：当不满足条件是的提示信息-->
        <ul el="10" id="cancel" op="cancel" opn="作废" >
            <li el="11" vid="510" cn="点击作废是，服务单ID必须为空" data="{'expr':'fserviceid==\'\' or fserviceid==\' \'','message':'数据存在下游单据不允许作废'}"></li>
            </ul>
    </div>
    <div id="permList">
        <ul el="12" id="sht_submitbill" cn="重新提交"></ul>
        <ul el="12" id="sht_quoteprice" cn="报价"></ul>
        <ul el="12" id="sht_refusebill" cn="拒单"></ul>
        <ul el="12" id="sht_confirmprice" cn="确认价格"></ul>
        <ul el="12" id="sht_dispatchbill" cn="派单"></ul>
        <ul el="12" id="sht_finishbill" cn="完工"></ul>
        <ul el="12" id="sht_cancelbill" cn="取消"></ul>

        <ul el="12" id="sht_acceptbill" cn="验收完成"></ul>
        <ul el="12" id="sht_acceptcancel" cn="取消验收"></ul>

        <ul el="12" id="sht_confirmcancel" cn="取消确认"></ul>
        <ul el="12" id="sht_confirmagent" cn="指派服务商"></ul>

        <ul el="12" id="sht_refuseagent" cn="驳回(已指派服务商)"></ul>
    </div>
	
</body>
</html>