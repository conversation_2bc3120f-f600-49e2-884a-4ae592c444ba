<!--服务
    -->
<html lang="en">
<head>
</head>
<body id="ydj_servicechange" basemodel="bill_basetmpl" el="1" cn="服务变更单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_servicechange" pn="fbillhead" cn="服务变更单">

        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fservicetype" fn="fservicetype" pn="fservicetype" cn="服务类型" cg="服务类型" refid="bd_enum" defval="'fres_type_02'" dfld="fenumitem" lix="2" lock="-1"></select>
        <input group="基本信息" el="113" type="datetime" id="fdate" ek="fbillhead" fn="fdate" ts="" cn="业务日期" defval="@currentlongdate" visible="-1" lix="8" width="160" lock="-1" />


        <!-- 隐藏商户、商户单号 -->
        <input group="基本信息" el="106" ek="fbillhead" visible="0" id="fdealerid" fn="fdealerid" pn="fdealerid" cn="商户" cg="商户" refid="ydj_customer" lix="5" width="120" lock="-1" filter="fcustype='customercate_03'" />
        <input group="基本信息" el="100" type="text" id="fmerbill" ek="fbillhead" fn="fmerbill" ts="" visible="0" cn="商户单号" lock="-1" />
        <!-- 新增销售部门、客户字段展示 -->
        <input group="基本信息" el="106" ek="fbillhead" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" cg="客户" refid="ydj_customer" lock="-1" visible="-1" lix="5" />
        <input group="基本信息" el="106" ek="fbillhead" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="销售部门" refid="ydj_dept" reflvt="1" canchange="true" lock="-1" visible="-1" lix="25" />

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fteamid" fn="fteamid" pn="fteamid" cn="团队" cg="团队" refid="ydj_team" lix="9" copy="0" lock="-1" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid_link" fn="fdeptid_link" pn="fdeptid_link" cn="关联部门" cg="关联部门" refid="ydj_dept" reflvt="1" canchange="true" lock="-1"  lix="25" />
        <input group="基本信息" el="106" type="text" id="fcaptainid" ek="fbillhead" fn="fcaptainid" ts="" refid="ydj_master" cn="队长" visible="32" lock="-1" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fmasterid" fn="fmasterid" pn="fmasterid" cn="师傅" refid="ydj_master" lix="9" lock="-1" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid_link" fn="fstaffid_link" pn="fstaffid_link" cn="关联账号" cg="关联账号" refid="sec_user" lix="25" />

        <input group="基本信息" el="100" type="text" id="fdealernumber" fn="fdealernumber" pn="fdealernumber" visible="-1" cn="订单号" lix="1" width="145" copy="0" lock="-1" />

        <input group="基本信息" el="100" type="text" id="fcustname" ek="fbillhead" fn="fcustname" ts="" visible="-1" cn="业主名称" lock="-1" />
        <input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" visible="-1" cn="业主电话" lock="-1" />

        <select group="基本信息" el="122" ek="fbillhead" visible="36" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lock="-1"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="36" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lock="-1"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="36" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lock="-1"></select>
        <input group="基本信息" el="100" ek="FBillHead" id="fchangereason" fn="fchangereason" pn="fchangereason" visible="-1" cn="变更原因"
               lock="0" copy="1" lix="0" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="FBillHead" id="funauditreason" fn="funauditreason" pn="funauditreason" visible="-1" cn="商户驳回原因"
               lock="-1" copy="0" lix="40" notrace="true" ts="" />

        <input group="基本信息" el="104" type="text" id="ftotalamount" ek="fbillhead" fn="ftotalamount" ts="" cn="结算金额" visible="1062" lock="-1" defls="true"/>
        <input group="基本信息" el="104" type="text" id="ftotalamount_chg" ek="fbillhead" fn="ftotalamount_chg" ts="" cn="变更后结算金额" visible="1062" lock="-1" defls="true"/>

        <table id="fentity" el="52" pk="fentryid" tn="t_ydj_servicechangeentry" pn="fentity" kfks="fseritemid,fqty" cn="服务项目" must="1">
            <tr>
                <th el="106" ek="fentity" id="fseritemid" fn="fseritemid" pn="fseritemid" cn="服务项目" refid="ydj_seritem" sformid="" visible="-1" width="200" lock="-1" must="1"></th>
                <th el="109" ek="fentity" id="funitid" fn="funitid" pn="funitid" cn="单位" refid="ydj_unit" sformid="" visible="96" width="100" lock="-1"></th>
                <th el="104" ek="fentity" id="fprice" fn="fprice" ts="" cn="单价" visible="96" width="130" format="0,000.00" lock="-1"></th>
                <th el="102" ek="fentity" id="fqty" fn="fqty" pn="fqty" cn="数量" visible="96" width="90" lock="-1" defls="true"></th>
                <th el="102" ek="fentity" id="fqty_chg" fn="fqty_chg" pn="fqty_chg" cn="变更数量" visible="96" width="90" defls="true"></th>
                <th el="105" ek="fentity" id="famount" fn="famount" ts="" cn="金额" visible="96" width="140" format="0,000.00" lock="-1"></th>
                <th el="105" ek="fentity" id="famount_chg" fn="famount_chg" ts="" cn="变更金额" visible="96" width="140" format="0,000.00" lock="-1"></th>

                <!--补价时携带到变更单上，系统内部使用-->
                <th el="104" ek="fentity" id="forderprice" fn="forderprice" pn="forderprice" visible="0" cn="商户下单价"
                    lock="-1" copy="1" lix="0" notrace="true" ts="" roundType="0" format="0,000.00" defls="true"></th>

                <th el="140" ek="fentity" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单类型" visible="96" copy="0" lix="150"></th>
                <th el="141" ek="fentity" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单编号" visible="96" copy="0" lix="153"></th>
                <th el="100" ek="fentity" id="fsourceinterid" fn="fsourceinterid" ts="" cn="来源单内码" visible="0" copy="0" lix="155"></th>
                <th el="100" ek="fentity" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单分录内码" visible="0" copy="0" lix="157"></th>

                <th el="141" ek="fentity" id="fmerchantorderno" fn="fmerchantorderno" ts="" cn="商户订单编号" visible="96" copy="0" lix="160"></th>
                <th el="100" ek="fentity" id="fmerchantorderinterid" fn="fmerchantorderinterid" ts="" cn="商户订单内码" visible="0" copy="0" lix="163"></th>
                <th el="100" ek="fentity" id="fmerchantorderentryid" fn="fmerchantorderentryid" ts="" cn="商户订单分录内码" visible="0" copy="0" lix="165"></th>
            </tr>
        </table>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <!--反写服务订单信息：覆盖反写数量、金额、表头总金额-->
            <li el="17" sid="1002" cn="反写服务单数量" data="{
                'sourceFormId':'ydj_service',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fqty',
                'expression':'fqty_chg',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>


            <!--跨级反写商户订单信息：覆盖反写数量-->
            <li el="17" sid="1002" cn="反写商户订单数量" data="{
                'sourceFormId':'ydj_merchantorder',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fmerchantorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fqty',
                'expression':'fqty_chg',
                'writebackMode':3,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="1002" cn="反写服务单数量" data="{
                'sourceFormId':'ydj_service',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'fentryid',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>

    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        
    </div>

</body>
</html>