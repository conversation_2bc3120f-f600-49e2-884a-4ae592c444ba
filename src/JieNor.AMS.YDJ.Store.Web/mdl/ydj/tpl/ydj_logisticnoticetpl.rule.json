{
  //规则引擎基类
  "base": "/mdl/ydj/stk/base_stkbilltmpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lockmenu_inoutstock",
      "expression": "menu:tbStockIn,tbStockOut$|fstatus!='E'"
    },
    {
      "id": "unlockmenu_inoutstock",
      "expression": "menu:$tbStockIn,tbStockOut|fstatus=='E'"
    },
    //启用条码作业后（打码，备码）按钮的可用性控制
    {
      "id": "lockmenu_barcode",
      "expression": "menu:tbCreatePackage,tbCreateBarcodeLink,tbDeleteBarcodeLink$|fenablebarcode==false and id!='' or fstatus!='D'"
    },
    {
      "id": "unlockmenu_barcode",
      "expression": "menu:$tbCreatePackage,tbCreateBarcodeLink,tbDeleteBarcodeLink|fenablebarcode==true and id!='' and fstatus=='D'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //选择商品，携带包装尺寸、体积、毛重
    { "expression": "fpacksize=fmaterialid__fpacksize" },
    { "expression": "fvolumeqty=fmaterialid__fvolume" },
    { "expression": "fgrossqty=fmaterialid__fgrossload" },

    //数量变化时计算总体积
    { "expression": "fvolume=fqty*fvolumeqty" },
    //数量变化时计算总重量
    { "expression": "fgross=fqty*fgrossqty" },

    //计算总件数
    {
      "expression": "ftotalpackageqty=sum(fpackqty)",
      "mode": 1
    },
    //计算总体积
    {
      "expression": "ftotalcubeqty=sum(fvolume)",
      "mode": 1
    },
    //计算总重量
    {
      "expression": "ftotalgrossload=sum(fgross)",
      "mode": 1
    }

  ]
}