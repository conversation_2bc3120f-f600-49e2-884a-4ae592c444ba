<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="aft_manage" basemodel="bill_basetmpl" el="1" cn="售后处理单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_aft_manage" pn="fbillhead" cn="售后处理单">

        <input el="100" ek="FBillHead" id="fdescription" visible="0" />
        <input group="基本信息" el="118" ek="fbillhead" visible="1150" id="fcreatorid" fn="fcreatorid" cn="创建人" />
        <input group="基本信息" el="119" ek="fbillhead" visible="1150" id="fcreatedate" fn="fcreatedate" cn="创建日期" />

        <!--基本信息-->
        <input el="100" ek="FBillHead" id="fbillno" fn="fbillno" ts="" visible="-1" cn="单据编号" lix="10" />
        <input group="基本信息" el="112" type="datetime" id="fbilldate" ek="FBillHead" fn="fbilldate" ts="" cn="业务日期" visible="-1" defval="@currentshortdate" lix="20" />
        <input group="基本信息" id="fsourcetype" el="140" cn="源单类型" visible="1150" />
        <input group="基本信息" id="fsourcenumber" el="141" cn="源单编号" visible="1150" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fafterstatus" fn="fafterstatus" pn="fafterstatus" cn="售后状态" cg="售后状态" refid="bd_enum" dfld="fenumitem" defVal="'aft_service_01'" lock="-1" lix="70"></select>
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="受理部门" refid="ydj_dept" dfld="fname" defVal="@currentDeptId" lix="50" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="受理人" refid="ydj_staff" dfld="fname" defVal="@currentStaffId" lix="40" />
        <input group="基本信息" id="fcustomerid" el="106" ek="fbillhead" fn="fcustomerid" ts="" visible="-1" cn="客户" refid="ydj_customer" lix="50" />
        <input group="基本信息" id="fcontacts" el="100" ek="fbillhead" fn="fcontacts" ts="" cn="联系人" visible="1150" />
        <input group="基本信息" id="fphone" el="100" ek="fbillhead" fn="fphone" ts="" cn="联系电话" visible="-1" lix="60" />
        <input group="基本信息" id="faddress" el="100" ek="fbillhead" fn="faddress" ts="" cn="联系地址" visible="1150" />

        <input group="基本信息" el="100" ek="fbillhead" id="forderno" fn="forderno" pn="forderno" visible="-1" cn="合同编号" lock="0" copy="0" ts="" lix="30" />

        <!-- 售后问题 -->
        <input group="售后问题" id="fproductid" el="106" ek="fbillhead" fn="fproductid" ts="" visible="-1" cn="问题商品" refid="ydj_product" lix="130" />
        <select group="售后问题" el="122" ek="fbillhead" visible="-1" id="faftqustiontype" fn="faftqustiontype" pn="faftqustiontype" cn="问题类别" cg="售后问题类别" refid="bd_enum" dfld="fenumitem" lix="110"></select>
        <select group="售后问题" el="122" ek="fbillhead" visible="-1" id="fpriority" fn="fpriority" pn="fpriority" cn="紧急程度" cg="优先级" refid="bd_enum" dfld="fenumitem" lix="120"></select>
        <input group="售后问题" el="100" id="fdescribe" ek="fbillhead" fn="fdescribe" ts="" cn="问题描述" visible="1150" len="2000" />
        <input group="售后问题" el="135" ek="FBillHead" id="fquestionimage" fn="fquestionimage" pn="fquestionimage" visible="32" cn="问题图片" lock="0" copy="1" notrace="true" ts="" />


        <!-- 售后结论 -->
        <select group="售后结论" el="122" ek="fbillhead" visible="-1" id="forigin" fn="forigin" pn="forigin" cn="售后成因" cg="售后成因" refid="bd_enum" dfld="fenumitem" lix="80"></select>
        <input group="售后结论" el="112" id="fhandledate" ek="FBillHead" fn="fhandledate" ts="" cn="预计处理日期" visible="32" defval="@currentshortdate" />
        <select group="售后结论" el="122" ek="fbillhead" visible="-1" id="fhandleconclusion" fn="fhandleconclusion" pn="fhandleconclusion" cn="处理结论" cg="处理结论" refid="bd_enum" dfld="fenumitem" lix="90"></select>
        <select group="售后结论" el="122" ek="fbillhead" visible="32" id="finstitutiontype" fn="finstitutiontype" pn="finstitutiontype" cn="责任单位类型" cg="责任单位类型" refid="bd_enum" dfld="fenumitem"></select>
        <input id="fdutysupplierid" el="106" ek="fbillhead" fn="fdutysupplierid" ts="" visible="0" cn="责任单位(供应商)" refid="ydj_supplier" />
        <input id="fdutycustomerid" el="106" ek="fbillhead" fn="fdutycustomerid" ts="" visible="0" cn="责任单位(客户)" refid="ydj_customer" />
        <input id="fdutystaffid" el="106" ek="fbillhead" fn="fdutystaffid" ts="" visible="0" cn="责任单位(员工)" refid="ydj_staff" />
        <input id="fdutydeptid" el="106" ek="fbillhead" fn="fdutydeptid" ts="" visible="0" cn="责任单位(部门)" refid="ydj_dept" />

        <input group="售后结论" el="112" id="ffinishdate" ek="FBillHead" fn="ffinishdate" ts="" cn="实际完成日期" visible="-1" defval="@currentshortdate" lix="100" />
        <input group="售后结论" el="100" id="fscheme" ek="fbillhead" fn="fscheme" ts="" cn="处理方案" visible="0" len="2000" />


    </div>

    <div id="opList">
        <ul el="10" id="finishmanage" op="setstatus" opn="完成" permid="finishmanage"
            data="{'parameter':{'statusFieldKey':'fafterstatus','statusValue':'aft_service_02'},
            'condition':{'expr':'fafterstatus==\'aft_service_01\'','message':'已经完成的处理单，无需再完成'}}"></ul>

        <ul el="10" id="closemanage" op="setstatus" opn="关闭"  permid="closemanage"
            data="{'parameter':{'statusFieldKey':'fafterstatus','statusValue':'aft_service_03'},
                    'condition':{'expr':'fafterstatus==\'aft_service_01\'','message':'已经关闭的处理单，不允许重复关闭'}}"></ul>
    </div>
    
    <div id="permList">
        <ul el="12" id="finishmanage" cn="完成"></ul>
        <ul el="12" id="closemanage" cn="关闭"></ul>
    </div>

    
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">

            <li el="17" sid="1002" cn="反写售后单号" data="{
                'sourceFormId':'ste_afterfeedback',
                'sourceControlFieldKey':'ffeedstatus',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fsourcenumber',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'',
                'writebackFieldKey':'ffeedstatus',
                'expression':'\'feedback_status02\'',
                'writebackMode':3,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>
    </div>
</body>
</html>