{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
    "lockRules": [
        ////已审核状态下且回访结论是转售后，则转售后按钮可用
        //{
        //  "id": "lock_pushafterbtn",
        //  "expression": "menu:tbPushAfterManage|fstatus!='E' or fconclusion == 'vist_conclusion_01'"
        //},
        //{
        //  "id": "unlock_pushafterbtn",
        //  "expression": "menu:$tbPushAfterManage|fstatus=='E' and fconclusion == 'vist_conclusion_02'"
        //}
        {
            "id": "fstatus_",
            "expression": "menu:$tbSaveSubmit,tbSaveAudit|fstatus==''"
        }, //保存并提交锁定锁定&解锁
        {
            "id": "lock_tbSaveSubmit",
            "expression": "menu:tbSaveSubmit|fstatus=='D' or fstatus=='E'"
        },
        {
            "id": "unlock_tbSaveSubmit",
            "expression": "menu:$tbSaveSubmit|fstatus!='D' and fstatus!='E'"
        }, //保存并审核锁定锁定&解锁
        {
            "id": "lock_tbSaveAudit",
            "expression": "menu:tbSaveAudit$|fstatus=='E'"
        },
        {
            "id": "unlock_tbSaveAudit",
            "expression": "menu:$tbSaveAudit|fstatus!='E'"
        }
    ],

  //定义表单可见性规则
  "visibleRules": [
    ////随着服务状态显示隐藏
    //{
    //  "id": "hide_product",
    //  "expression": "other:.product-body|fservicetype=='fres_type_02' or fservicetype==''"
    //},
    //{
    //  "id": "show_product",
    //  "expression": "other:$.product-body|fservicetype!='fres_type_02' and fservicetype!=''"
    //},
    //{
    //  "id": "hide_feeback",
    //  "expression": "other:.feedback-body|fservicetype!='fres_type_03'"
    //},
    //{
    //  "id": "show_feeback",
    //  "expression": "other:$.feedback-body|fservicetype=='fres_type_03'"
    //},
    {
      "id": "hide_feeback",
      "expression": "other:.feedback-body|fsourcetype!='ste_afterfeedback'"
    },
    {
      "id": "show_feeback",
      "expression": "other:$.feedback-body|fsourcetype=='ste_afterfeedback'"
    },
    {
      "id": "hide_visit_install",
      "expression": "other:.visit-install|fservicetype!='fres_type_01'"
    },
    {
      "id": "show_visit_install",
      "expression": "other:$.visit-install|fservicetype=='fres_type_01'"
    },
    {
      "id": "hide_visit_added",
      "expression": "other:.visit-added|fservicetype!='fres_type_02' and fservicetype!=''"
    },
    {
      "id": "show_visit_added",
      "expression": "other:$.visit-added|fservicetype=='fres_type_02' or fservicetype==''"
    },
    {
      "id": "hide_visit_feeback",
      "expression": "other:.visit-feeback|fservicetype!='fres_type_03'"
    },
    {
      "id": "show_visit_feeback",
      "expression": "other:$.visit-feeback|fservicetype=='fres_type_03'"
    },
    {
      "id": "hide_fistransfer",
      "expression": "other:.feeback-hq|fservicetype!='1'"
    },
    {
      "id": "show_fistransfer",
      "expression": "other:$.feeback-hq|fistransfer=='1'"
    },
    //无效，已改到js处实现
    //{
    //  "id": "hide_fisrecommend",
    //  "expression": "other:.visit-fisrecommend|fservicetype=='' or fservicetype=='fres_type_03' or (fservicetype=='fres_type_02' and (fservicescore=='' or fservicescore=='customerscore_01' or fservicescore=='customerscore_02'))"
    //},
    //{
    //  "id": "show_fisrecommend",
    //  "expression": "other:$.visit-fisrecommend|fservicetype=='fres_type_01' or (fservicetype=='fres_type_02' and fservicescore!='' and fservicescore!='customerscore_01' and fservicescore!='customerscore_02')"
    //}
    //选择供应商隐藏规则
    {
      "id": "show_dutysupplier",
      "expression": "other:$.y-dutysupplier| finstitutiontype == 'dutyunit_type_01' or finstitutiontype == ''"
    },
    {
      "id": "hide_dutysupplier",
      "expression": "other:.y-dutysupplier| finstitutiontype != 'dutyunit_type_01' and finstitutiontype != ''"
    },
    //选择客户隐藏规则
    {
      "id": "show_dutycustomer",
      "expression": "other:$.y-dutycustomer| finstitutiontype == 'dutyunit_type_02'"
    },
    {
      "id": "hide_dutycustomer",
      "expression": "other:.y-dutycustomer| finstitutiontype != 'dutyunit_type_02'"
    },
    //选择员工隐藏规则
    {
      "id": "show_dutystaff",
      "expression": "other:$.y-dutystaff| finstitutiontype == 'dutyunit_type_03'"
    },
    {
      "id": "hide_dutystaff",
      "expression": "other:.y-dutystaff| finstitutiontype != 'dutyunit_type_03'"
    },
    //选择部门隐藏规则
    {
      "id": "show_dutydept",
      "expression": "other:$.y-dutydept| finstitutiontype == 'dutyunit_type_04'"
    },
    {
      "id": "hide_dutydept",
      "expression": "other:.y-dutydept| finstitutiontype != 'dutyunit_type_04'"
    }
  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "forderdeptid=forderno__fdeptid| 1==1" },
    ////选择受理人信息，自动带出受理部门
    //{ "expression": "fdeptid=fstaffid__fdeptid|fstaffid=='' or 1==1" },

    //客户基础资料值变化时，携带客户属性字段到页面指定的字段上面
    { "expression": "fcontacts=fcustomerid__fcontacts|fcustomerid!=''" },
    { "expression": "fphone=fcustomerid__fphone|fcustomerid!=''" },
    { "expression": "faddress=fcustomerid__faddress|fcustomerid!=''" },
    { "expression": "fprovince=fcustomerid__fprovince| 1==1" },
    { "expression": "fcity=fcustomerid__fcity| 1==1" },
    { "expression": "fregion=fcustomerid__fregion| 1==1" }

    ////子表选择人员带出部门
    //{ "expression": "fdeptid_e=fstaffid_e__fdeptid|fstaffid_e=='' or 1==1" },
    ////部门变化时清空岗位信息
    //{ "expression": "fpositionid=''|fdeptid_e=='' or 1==1" }
  ]
}