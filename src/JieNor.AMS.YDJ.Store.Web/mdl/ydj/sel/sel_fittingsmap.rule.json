/*    
  * ��ҵ�����������Ƴ�������
  * 1��Ҫ֧�ֵ���ĳ�ֶ�=ĳֵʱ������ָ���༭�ؼ�������Ԫ�أ������˵���������
  * 2��Ҫ֧�ִﵽĳ���ʽԼ������ʱ������ָ���༭�ؼ�������Ԫ�أ������˵���������
  * design by linus at 2017-06-10
  *ѡ������������
  *Author:zpf
  *CreateTime:2022-01-10 15pm
  */
{
  //�����������
  "base": "/mdl/bd.rule.json",

  //�������������
  //�������Թ�������ࣺfield �ֶΣ�menu �˵���other ���򣨱�ʾ��ĳһ�����е������ֶν��������ԣ�
  /*
    �������(array)
      |--����(object)
        |--id(string):�����ʶ
        |--expression(string):������ʽ | ǰ������
          |--expr:���ʽ
            |--field:�ֶ�
              |--����$����
            |--menu:�˵�
              |--����$����
            |--other:����
              |--����$����
          |--condition:ǰ������
  */
  "lockRules": [

  ],

  //������ɼ��Թ���
  "visibleRules": [
    //ѡ��ƥ��ά��-�ͺ����ع���  ͨ�� ���ͺ�ƥ�� ѡ�������������ʾ�Լ�����
    {
      "id": "hide_dimensionmodelentity",
      "expression": "other:#dimensionmodelentity$| fmatchbymodel == false"
    },
    {
      "id": "show_dimensionmodelentity",
      "expression": "other:$#dimensionmodelentity| fmatchbymodel == true"
    }
  ],

  //������������
  "calcRules": [
    //���ͺ�ƥ�� ȡ��ѡ��ʱ����յĵ�����ƥ��ά��-�ͺ���������Ϣ
    { "expression": "fseltypeid='' | fmatchbymodel==false" }, //����ͺű���
    { "expression": "fseltypeid_fumber='' | fmatchbymodel==false" } //�ͺ�����
  ]
}
