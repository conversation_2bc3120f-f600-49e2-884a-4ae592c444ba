<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="sel_prop" el="3" basemodel="bd_basetmpl" cn="属性" isolate="0" fqfks="fdatatype" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_sel_prop" pn="fbillhead" cn="属性">

        <!--重写基类模型中的部分字段属性-->
        <input el="100" id="fdescription" cn="描述" len="200" />
        <input type="text" id="fnumber" el="108" ek="fbillhead" fn="fnumber" pn="fnumber" apipn="number" cn="属性编码" desc="编码" width="120" notrace="false" visible="-1" copy="0" lix="1" must="1" len="200" />
        <input type="text" id="fname" el="100" ek="fbillhead" fn="fname" pn="fname" apipn="name" cn="属性名称" desc="名称" width="120" notrace="false" visible="-1" lix="2" must="1" len="200" fqd="1" />
        <input el="152" ek="fbillhead" visible="-1" id="fvaluesource" fn="fvaluesource" pn="fvaluesource" cn="值类型"
               vals="'basedata':'基础资料','enumdata':'辅助资料','text':'文本'" defval="'text'" lix="5" must="1" />
        <input el="106" ek="fbillhead" visible="-1" id="frefbaseformid" fn="frefbaseformid" pn="frefbaseformid" cn="基础资料" refid="sys_bizobject" filter="ftype=3" lix="10" />
        <input el="106" ek="fbillhead" visible="1150" id="frefenumformid" fn="frefenumformid" pn="frefenumformid" cn="辅助资料" refid="bd_enumdata" sformid="" filter="fvisible='1'" lix="15" />
        <input el="152" ek="fbillhead" visible="-1" id="fdatatype" fn="fdatatype" pn="fdatatype" cn="数据类型" vals="'1':'字符','2':'数值'" defval="'1'" lix="20" must="1" sbm="true" />
        <input el="116" ek="fbillhead" visible="-1" id="fallowcustom" fn="fallowcustom" pn="fallowcustom" cn="支持非标录入" lix="25" width="110" sbm="true" />
        <input el="116" ek="fbillhead" visible="-1" id="fisparts" fn="fisparts" pn="fisparts" cn="是否配件" lix="30" width="90" />
        <input el="116" ek="fbillhead" visible="-1" id="fallownosuitlib" fn="fallownosuitlib" pn="fallownosuitlib" cn="支持非标库" lix="25" width="110" sbm="true" />
        <input el="116" ek="fbillhead" visible="-1" id="fcontrolprice" fn="fcontrolprice" pn="fcontrolprice" cn="是否影响价格" lix="25" width="110" sbm="true" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fnumber','fname','fdatatype','fallowcustom','fisparts','fallownosuitlib','fcontrolprice']}" permid=""></ul>
    </div>
</body>
</html>