<!--
本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
-->
<html lang="en">
<head>
</head>
<body id="sel_standardprop" el="3" basemodel="bd_basetmpl" cn="标准品转换属性配置表" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_sel_standardprop" pn="fbillhead" cn="标准品转换属性配置表">

        <!--重写基类模型中的部分字段属性-->
        <input el="100" id="fdescription" cn="备注" visible="0" />

        <input el="106" ek="fbillhead" visible="-1" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" cn="商品类别" refid="ydj_category" lix="5" width="160" />
    </div>

    <table id="fentity" el="52" pk="fentryid" tn="t_sel_standardpropentry" pn="fentity" cn="属性明细" kfks="fpropid">
        <tr>
            <th el="107" ek="fentity" visible="-1" id="fpropnumber" fn="" pn="fpropnumber" cn="属性编码" ctlfk="fpropid" dispfk="fnumber" width="150" lix="20"></th>
            <th el="106" ek="fentity" visible="-1" id="fpropid" fn="fpropid" pn="fpropid" cn="属性名称" refid="sel_prop" width="200" lix="25" must="1"></th>
            <th el="116" ek="fentity" visible="-1" id="fispartincalc" fn="fispartincalc" pn="fispartincalc" cn="是否参与标准品计算" width="150" lix="30"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" id="save_valid_fcategoryid" cn="保存时商品类别唯一" vid="500" data="fmainorgid,fcategoryid" precon=""></li>
        </ul>
    </div>
</body>
</html>