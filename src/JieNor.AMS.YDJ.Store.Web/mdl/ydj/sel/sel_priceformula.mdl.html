<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="sel_priceformula" el="3" basemodel="bd_basetmpl" cn="选配计价公式" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_sel_priceformula" pn="fbillhead" cn="选配计价公式">
        <!--基础资料-->
        <input type="text" id="fpriority" el="101" ek="fbillhead" fn="fpriority" pn="fpriority" apipn="priority" cn="优先级" desc="优先级" width="70" visible="-1" lix="5" align="center" must="1" />

        <input type="text" id="fdescription" el="100" ek="fbillhead" fn="fdescription" apipn="description" pn="fdescription" cn="备注" width="280" visible="0" lix="500" />
        <!--计价维度-->
        <input group="计价维度" el="106" type="text" id="fbrandid" ek="fbillhead" fn="fbrandid" ts="" visible="-1" cn="品牌" refid="ydj_brand" lix="10" sbm="true" />
        <input group="计价维度" el="106" type="text" id="fseriesid" ek="fbillhead" fn="fseriesid" ts="" visible="-1" cn="系列" refid="ydj_series" lix="15" sbm="true" />

        <input group="计价维度" el="106" type="text" id="fcategoryid" ek="fbillhead" fn="fcategoryid" refid="ydj_category" ts="" visible="-1" sbm="true" cn="产品类别" lix="16" />
        <input group="计价维度" el="106" type="text" id="fselcategoryid" ek="fbillhead" fn="fselcategoryid" refid="sel_category" ts="" visible="-1" sbm="true" cn="选配类别" lix="20" />
        <input group="计价维度" el="106" type="text" id="fseltypeid" ek="fbillhead" fn="fseltypeid" ts="" visible="-1" cn="型号" refid="sel_type" lix="30" sbm="true" />
        <input group="计价维度" ek="fbillhead" type="text" el="116" id="fmodityprice" fn="fmodityprice" dispfk="fmodityprice" ts="" cn="按商品计价" visible="-1" sformid="" lock="0" lix="35" width="100" />

        <!--计价商品-->
        <table id="fproductentity" el="52" pk="fentryid" tn="t_sel_pricefproductentry" pn="fproductentity" cn="计价维度-商品" kfks="fproductid">
            <tr>
                <th lix="5" ek="fproductentity" el="107" id="fmtrlnumber" fn="" pn="fmtrlnumber" visible="1150" cn="商品编码" lock="-1" copy="1" notrace="true" ts="" width="150" ctlfk="fproductid" dispfk="fnumber"></th>
                <th lix="6" el="106" ek="fproductentity" id="fproductid" fn="fproductid" pn="fproductid" cn="商品" refid="ydj_product" multsel="true" sformid="" width="300" visible="-1" apipn="product" must="1"></th>
            </tr>
        </table>


        <!--子单据体-->
        <table id="fentity" el="52" pk="fentryid" tn="t_sel_priceformulaentry" pn="fentity" cn="明细信息" kfks="ffcontaint_e,fpriceformula_e">
            <tr>
                <th el="100" ek="fentity" visible="1150" id="ffcontaint_e" fn="ffcontaint" pn="ffcontaint" cn="条件" width="500" len="4000" xsslv="1" must="1"></th>
                <th el="100" ek="fentity" visible="1150" id="fpriceformula_e" fn="fpriceformula" pn="fpriceformula" cn="公式" width="400" len="2000" xsslv="1" must="1"></th>
            </tr>
        </table>

    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" clear></li>
            <li el="11" id="save_valid_fnumber" cn="保存时编码唯一" vid="500" data="fmainorgid,fnumber" precon=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fnumber','fname','fpriority','fbrandid','fseriesid','fseltypeid','fcategoryid','fselcategoryid','fmodityprice','fproductid','ffcontaint_e','fpriceformula_e']}" permid=""></ul>
    </div>
</body>
</html>