/*    
  * ��ҵ�����������Ƴ�������
  * 1��Ҫ֧�ֵ���ĳ�ֶ�=ĳֵʱ������ָ���༭�ؼ�������Ԫ�أ������˵���������
  * 2��Ҫ֧�ִﵽĳ���ʽԼ������ʱ������ָ���༭�ؼ�������Ԫ�أ������˵���������
  * design by linus at 2017-06-10
  */
{
  //�����������
  "base": "/mdl/bill.rule.json",

  //�������������
  /*
      �������(array)
        |--����(object)
          |--id(string):�����ʶ
          |--expression(string):������ʽ | ǰ������
            |--expr:���ʽ
              |--field:�ֶ�
                |--����$����
              |--menu:�˵�
                |--����$����
              |--other:����
                |--����$����
            |--condition:ǰ������
  
      �������Թ�������ࣺfield �ֶΣ�menu �˵���other ���򣨱�ʾ��ĳһ�����е������ֶν��������ԣ�
      { "expression": "[field:*$fremark;menu:tbAudit,tbSubmit;other:#box1,.selector1]|fstatus=='A'" }
    */
  "lockRules": [
    //�˹����ʾ����ͷ���ܲ��·����ɡ�Ϊtrueʱ�����߱��塰����ҵ����������0ʱ����������֮���ſ�
    {
      "id": "lock_fcurrworkqty",
      "expression": "field:fcurrworkqty|fisdistribute==true or fworkedqty>0"
    },
    //�˹����ʾ����ͷ���ܲ��·����ɡ�Ϊtrueʱ����������֮���ſ�
    {
      "id": "lock_freceptionno_flogisticsno",
      "expression": "field:freceptionno,flogisticsno,fsenddate,fdescription|fisdistribute==true"
    },
    //�˹����ʾ������״̬��Ϊ��������ʱ����������֮���ſ�
    {
      "id": "lock_fprice_famount",
      "expression": "field:fprice,famount|fstatus=='A'"
    },
    {
      "id": "unlock_tbStockIn",
      "expression": "menu:$tbStockIn|ftask_type=='stk_postockin' and ftaskstatus!='ftaskstatus_04'"
    },
    {
      "id": "lock_tbStockIn",
      "expression": "menu:tbStockIn|ftask_type!='stk_postockin' or ftaskstatus=='ftaskstatus_04'"
    }
  ],

  //������ɼ��Թ���
  "visibleRules": [

  ],

  //������������
  "calcRules": [
    //�Ƽ������仯ʱ������
    { "expression": "famount=fqty*fprice|fprice!='' or fqty!=''" },
    { "expression": "fprice=famount/fqty|fqty!=0 and fqty!=''" },
    //�����
    { "expression": "ftotalvolume=fqty*fsinglevolume|fqty!='' and fsinglevolume!=''" },
    //��λ���
    { "expression": "fsinglevolume=ftotalvolume/fqty|fqty!='' and fqty!=0 and ftotalvolume!=''" }
  ]
}
  