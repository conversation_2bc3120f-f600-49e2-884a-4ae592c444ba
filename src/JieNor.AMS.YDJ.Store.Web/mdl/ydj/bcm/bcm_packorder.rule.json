/*    
  * 表单业务规则引擎设计场景描述
  * 1、要支持单据某字段=某值时，锁定指定编辑控件及界面元素（包括菜单及容器）
  * 2、要支持达到某表达式约定条件时，锁定指定编辑控件及界面元素（包括菜单及容器）
  * design by linus at 2017-06-10
  */
{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  /*
      多个规则(array)
        |--规则(object)
          |--id(string):规则标识
          |--expression(string):规则表达式 | 前置条件
            |--expr:表达式
              |--field:字段
                |--锁定$解锁
              |--menu:菜单
                |--锁定$解锁
              |--other:区域
                |--锁定$解锁
            |--condition:前置条件
  
      表单锁定性规则分三类：field 字段，menu 菜单，other 区域（表示对某一区域中的所有字段进行锁定性）
      { "expression": "[field:*$fremark;menu:tbAudit,tbSubmit;other:#box1,.selector1]|fstatus=='A'" }
    */
  "lockRules": [
    //此项规则表示：单据状态=A 或 B 或 C（关联暂存，创建，重新审核）时，所有字段可用，审核 反审核 撤销 操作不可用，其他操作可用
    {
      "id": "fstatus_ABC",
      "expression": "field:$*;menu:tbAudit,tbUnaudit,tbUnsubmit$*|fstatus=='A' or fstatus=='B' or fstatus=='C'"
    },
    //此项规则表示：单据状态=D（已提交）时，所有字段不可用，提交 选单 操作不可用，其他操作可用
    {
      "id": "fstatus_D",
      "expression": "field:*$;menu:tbSubmit,tbPull,tbCreatePack,tbDeletePack,tbCommitPack$*|fstatus=='D'"
    },
    //此项规则表示：单据状态=E（已审核）时，所有字段不可用，审核 提交 选单 操作不可用，其他操作可用
    {
      "id": "fstatus_E",
      "expression": "field:*$;menu:tbSubmit,tbAudit,tbPull,tbCreatePack,tbDeletePack,tbCommitPack$*|fstatus=='E'"
    },
    //此项规则表示：单据状态='' 时(新增的时候)，所有字段可用，只有 新增 保存 选单 操作可用，其他操作都不可用
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave,tbPull,tbCreatePack,tbDeletePack,tbCommitPack|fstatus==''"
    },
    //此规则表示：该商品对应的商品商品属性“允许定制”=true时放开，为false时，锁定
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdesc_p$|fcustom!=true"
    },
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdesc_p|fcustom==true"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fstockstatus=fstorehouseid__fstockid" },
    { "expression": "fstockstatus_p=fstorehouseid_p__fstockid" }
    //{ "expression": "famount_ed=fdealamount_h*fratio/100|ftype='1'" }
  ]
}
  