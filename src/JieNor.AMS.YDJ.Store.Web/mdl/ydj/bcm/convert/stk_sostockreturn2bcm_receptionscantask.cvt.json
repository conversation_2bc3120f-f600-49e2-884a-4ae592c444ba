{"Id": "stk_sostockreturn2bcm_receptionscantask", "Number": "stk_sostockreturn2bcm_receptionscantask", "Name": "�����˻��������ջ�ɨ������(Ϊ������)", "SourceFormId": "stk_sostockreturn", "TargetFormId": "bcm_receptionscantask", "ActiveEntityKey": "fentity", "FilterString": "fsumpushreceiveqty<fbizqty", "Message": "�����ջ�ɨ������<br>1����Ʒ�ۼ������ջ���ҵ��������С��ʵ��������", "FieldMappings": [{"Id": "fsourcetype", "Name": "Դ������", "MapType": 1, "SrcFieldId": "'stk_sostockreturn'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcenumber", "Name": "Դ�����", "MapType": 0, "SrcFieldId": "f<PERSON><PERSON>", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fstorehouseid", "Name": "�ֿ�", "MapType": 0, "SrcFieldId": "fstorehouseid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fstorelocationid", "Name": "��λ", "MapType": 0, "SrcFieldId": "fstorelocationid", "MapActionWhenGrouping": 0, "Order": 0}], "BillGroups": [{"Id": "f<PERSON><PERSON>", "Order": 1}], "FieldGroups": [{"Id": "fentity_fentryid", "Order": 1}]}