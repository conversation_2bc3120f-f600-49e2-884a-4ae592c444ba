<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_customer" basemodel="bd_basetmpl" el="3" cn="客户" lfvs="ByAlphabeticAndNumber" fqfks="fphone" UseRelatedInfo="true" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_customer" pn="fbillhead" cn="客户">
        <input type="text" id="fname" el="100" ek="fbillhead" fn="fname" pn="fname" apipn="name" cn="名称" desc="名称" width="120" visible="-1" lix="2" must="1" notrace="false" />
        <!--基本信息-->
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="-1" lix="27"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" lix="251" />
        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" width="130" visible="-1" copy="0" lix="251" />
        <input el="100" ek="fbillhead" id="fdescription" len="1000" />
        <!--基本信息-->
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fphone" fn="fphone" pn="fphone" cn="手机号" lix="4" notrace="false" />
        <input group="基本信息" el="100" ek="fbillhead" id="foldphone" fn="foldphone" pn="foldphone" cn="旧手机号" lix="4" lock="-1" visible="0" />
        <input group="基本信息" ek="fbillhead" type="file" id="flogomax" el="111" fn="flogomax" ts="" cn="客户头像" visible="0" lix="10" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fchancesource" fn="fchancesource" pn="fchancesource" cn="来源机会" lock="-1" copy="0" lix="5" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fgender" fn="fgender" pn="fgender" cn="性别" cg="性别" refid="bd_enum" dfld="fenumitem" lix="7"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fwechat" fn="fwechat" pn="fwechat" cn="微信" lix="6" width="100" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcountry" fn="fcountry" pn="fcountry" cn="国家或地区" cg="国家或地区" refid="bd_enum" dfld="fenumitem" lix="9" defval="'CN'"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="10"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="11"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fregion" fn="fregion" pn="fregion" cn="地区" cg="区" refid="bd_enum" dfld="fenumitem" lix="12"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="faddress" fn="faddress" pn="faddress" cn="详细地址" lix="13" notrace="false" />
        <!-- 肖丹婷需求增加 -->
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fhousenumber" fn="fhousenumber" pn="fhousenumber" cn="门栋号" lix="13" />
        <select group="基本信息" el="122" ek="fbillhead" visible="0" id="fcustype" fn="fcustype" pn="fcustype" cn="客户类型" cg="客户类别" refid="bd_enum" dfld="fenumitem" defVal="'customercate_01'" lix="30"></select>
        <input group="基本信息" el="106" type="text" id="fbuildingid" ek="fbillhead" fn="fbuildingid" pn="fbuildingid" refid="ydj_building" ts="" visible="1150" cn="楼盘" lix="45" />
        <select group="基本信息" el="122" ek="fbillhead" id="fage" fn="fage" pn="fage" cn="年龄段" cg="年龄段" refid="bd_enum" dfld="fenumitem" lix="14" visible="1150"></select>
        <input group="基本信息" el="113" ek="fbillhead" visible="1150" id="freturndatetime" fn="freturndatetime" pn="freturndatetime" cn="回收时间" lock="-1" />
        <input group="基本信息" el="106" type="text" id="fchannelid" ek="fbillhead" fn="fchannelid" refid="ste_channel" ts="" visible="-1" cn="客户渠道" lix="15" />

        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="forgid" fn="forgid" pn="forgid" refid="bas_organization" cn="对应经销商" width="200" lix="25" lock="-1" />

        <!-- 不再使用，改为多负责人 -->
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="0" id="fdutyid" fn="fdutyid" pn="fdutyid" cn="负责人" refid="ydj_staff" dfld="fname" lix="14" defVal="@currentStaffId" />
    <input group="基本信息" el="106" ek="fbillhead" visible="0" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="所属门店" refid="ydj_dept" dfld="fname" reflvt="1" lix="13" defVal="@currentDeptId" />-->
        <!-- 多负责人，用于显示 -->
        <input group="后台字段" el="125" ek="fbillhead" id="fdutyids" fn="fdutyids" pn="fdutyids" visible="0" cn="负责人(数据范围不可选)" refid="ydj_staff" dfld="fname"
               lock="0" copy="1" lix="0" notrace="true" ts="" />
        <input group="后台字段" el="125" ek="fbillhead" id="fdeptids" fn="fdeptids" pn="fdeptids" visible="0" cn="所属门店(数据范围不可选)" refid="ydj_dept" dfld="fname" reflvt="1"
               lock="0" copy="1" lix="0" notrace="true" ts="" />

        <input group="后台字段" el="100" ek="fbillhead" id="fwxopenid" fn="fwxopenid" pn="fwxopenid" visible="0" cn="微信openid"
               lock="0" copy="1" lix="0" notrace="true" ts="" />
        <input group="后台字段" el="100" ek="fbillhead" id="fwxunionid" fn="fwxunionid" pn="fwxunionid" visible="0" cn="微信unoinid"
               lock="0" copy="1" lix="0" notrace="true" ts="" />

        <input group="基本信息" el="100" ek="fbillhead" id="fheadimgurl" fn="fheadimgurl" pn="fheadimgurl" visible="0" cn="头像"
               lock="0" copy="0" lix="1" notrace="true" ts="" len="300" />

        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fcontacts" fn="fcontacts" pn="fcontacts" cn="联系人" lix="13" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fbuyer" fn="fbuyer" pn="fbuyer" cn="买家ID" lix="13" width="100" sync="true" apipn="buyer" />

        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcustomertype" fn="fcustomertype" cn="客户分类" cg="客户分类" refid="bd_enum" dfld="fenumitem" lix="3"></select>
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fbizorgid" fn="fbizorgid" pn="fbizorgid" cn="客户所属组织" cg="客户所属组织" refid="bas_organization" lix="35" defVal="@currentOrgId" />

        <input type="text" id="fprimitiveid" el="100" ek="fbillhead" remove />

        <select group="基本信息" el="152" ek="fbillhead" visible="-1" id="fcusnature" fn="fcusnature" pn="fcusnature" cn="客户性质" vals="'cusnature_00':'线索','cusnature_01':'意向','cusnature_02':'成交'" copy="0" defval="'cusnature_02'" lix="19"></select>
        <input group="基本信息" el="135" ek="fbillhead" visible="1150" id="fimage" fn="fimage" pn="fimage" cn="附件" lix="17" />

        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fsource" fn="fsource" pn="fsource" cn="客户来源" cg="来源渠道" refid="bd_enum" dfld="fenumitem" lix="40" must="-1" copy="0" notrace="false"></select>

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fsrcstoreid" fn="fsrcstoreid" pn="fsrcstoreid" cn="来源门店" cg="来源门店" refid="ydj_dept" defVal="@currentDeptId" lix="35" notrace="false" />

        <input group="基本信息" el="105" ek="fbillhead" visible="0" id="fbalance" fn="fbalance" pn="fbalance" cn="定金" lock="-1" lix="20" copy="0" />
        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="foperationmode" fn="foperationmode" pn="foperationmode" cn="运营模式" vals="'1':'总部直营','2':'独立经营'" copy="0" lock="-1" defval="'2'" lix="21"></select>

        <input group="基本信息" el="100" type="text" id="fsourceform" ek="fbillhead" fn="fsourceform" ts="" cn="来源表单" visible="0" lock="-1" copy="0" />
        <input group="基本信息" el="100" type="text" id="fsourceid" ek="fbillhead" fn="fsourceid" ts="" cn="来源id" visible="0" lock="-1" copy="0" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="flegalperson" fn="flegalperson" pn="flegalperson" cn="单位法人" width="105" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fcardid" fn="fcardid" pn="fcardid" cn="身份证号码" width="105" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="femail" fn="femail" pn="femail" cn="电子邮箱" width="105" />
        <input group="基本信息" el="112" type="date" id="fbirthdate" ek="fbillhead" fn="fbirthdate" pn="fbirthdate" cn="出生日期" visible="1150" lix="11" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fbank" fn="fbank" pn="fbank" cn="开户行" lix="31" width="100" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="faccountname" fn="faccountname" pn="faccountname" cn="账户名称" lix="32" width="100" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fbanknumber" fn="fbanknumber" pn="fbanknumber" cn="银行卡号" lix="33" width="175" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fdealamount_h" fn="fdealamount" pn="fdealamount" cn="成交总额" copy="0" lix="45" format="0,000.00" lock="-1" />
        <input group="基本信息" el="101" type="text" id="fordernumber" ek="fbillhead" fn="fordernumber" pn="fordernumber" visible="1150" lock="-1" cn="订单数" lix="19" />

        <!-- 小程序新增：客户类型，对应商机里的商机类型，不同于 fcustomertype -->
        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="ftype" fn="ftype" pn="ftype" cn="类型" vals="'customertype_00':'个人','customertype_01':'公司'" copy="0" defval="'customertype_00'" lix="16" lock="2" must="1"></select>

        <!-- 优利文增强（********）：添加会员等级、当前会员积分、累计消费金额 -->
        <input group="基本信息" el="106" type="text" id="fcustomerlevel" ek="fbillhead" fn="fcustomerlevel" pn="fcustomerlevel" refid="ydj_customerlevel" ts="" visible="0" lock="-1" cn="客户等级" lix="45" />
        <input group="基本信息" el="101" type="text" id="favailableintegral" ek="fbillhead" fn="favailableintegral" pn="favailableintegral" visible="0" lock="-1" cn="当前会员积分" lix="19" />
        <input group="基本信息" el="105" type="text" id="fsumamount" ek="fbillhead" fn="fsumamount" pn="fsumamount" visible="0" lock="-1" cn="累计消费金额" lix="19" />

        <!--协同信息-->
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fcoocompany" fn="fcoocompany" pn="fcoocompany" cn="协同企业" lock="-1" lix="18" copy="0" />
        <input group="协同信息" el="100" ek="fbillhead" id="fcoocompanyid" fn="fcoocompanyid" pn="fcoocompanyid" cn="协同企业id" visible="0" lock="-1" lix="19" copy="0" />
        <input group="协同信息" el="100" ek="fbillhead" id="fcooproductid" fn="fcooproductid" pn="fcooproductid" cn="协同企业产品id" visible="0" lock="-1" copy="0" />
        <input group="协同信息" el="106" ek="fbillhead" visible="32" id="fserstaffid" fn="fserstaffid" pn="fserstaffid" cn="客服人员(数据范围不可选)" refid="ydj_staff" dfld="fname" lix="17" copy="0" />
        <input group="协同信息" el="106" ek="fbillhead" visible="32" id="fcoodeptid" fn="fcoodeptid" pn="fcoodeptid" cn="负责部门(数据范围不可选)" refid="ydj_dept" dfld="fname" reflvt="1" lix="18" copy="0" />
        <input group="协同信息" el="100" ek="fbillhead" visible="32" id="fcoostate" fn="fcoostate" pn="fcoostate" cn="协同状态" lock="-1" lix="19" copy="0" />

        <!--隐藏字段，该字段值由 账户设置（经销商）保存时反写，该字段用于控制客户协同账户信息是否允许修改-->
        <input group="协同信息" el="116" ek="fbillhead" visible="0" id="fissetup" fn="fissetup" pn="fissetup" cn="允许经销商资料单独设定" lock="-1" copy="0" />
        <input group="协同信息" el="152" ek="fbillhead" id="fmusisyncstatus" fn="fmusisyncstatus" pn="fmusisyncstatus" visible="1150" cn="慕思协同状态" copy="0" ts="" vals=" '01':'待协同','02':'协同成功','03':'协同失败'" defval="'01'" lix="8" />
        <input group="协同信息" el="113" type="text" id="fmusisyncdate" ek="fbillhead" fn="fmusisyncdate" pn="fmusisyncdate" ts="" visible="1150" cn="慕思协同日期" lix="6" copy="0" />

        <input group="基本信息" el="113" ek="fbillhead" visible="1150" id="flastfollowdata" fn="flastfollowdata" pn="flastfollowdata" cn="最后跟进时间" lix="20" width="105" copy="0" lock="-1" defval="@currentlongdate" />
        <!-- 初次成单时间：当初次客户性质改为成单时的时间，即初次合同审核通过的时间或主动成单关闭的时间 -->
        <input group="基本信息" el="113" ek="fbillhead" visible="1150" id="ffirstordertime" fn="ffirstordertime" pn="ffirstordertime" cn="初次成单时间" lix="20" width="105" copy="0" lock="-1" />
        <!--企业微信用户编码-->
        <input group="后台字段" el="100" ek="fbillhead" visible="0" type="text" id="fworkwxuserid" fn="fworkwxuserid" pn="fworkwxuserid" lock="-1" cn="企业微信用户编码" />
        <input group="后台字段" el="100" ek="fbillhead" visible="0" type="text" id="oppid" fn="oppid" pn="oppid" lock="-1" cn="中台ID" />
        <input group="后台字段" el="100" ek="fbillhead" visible="0" type="text" id="code" fn="code" pn="code" lock="-1" cn="总部消费者编码" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fmemberno" fn="fmemberno" pn="fmemberno" cn="会员ID" notrace="false" />

        <input group="后台字段" el="100" ek="fbillhead" visible="0" type="text"
               id="dealerNo" fn="dealerNo" pn="dealerNo" lock="-1" cn="慕思经销商" />

        <input group="后台字段" el="100" ek="fbillhead" id="ftimestr" fn="ftimestr" pn="ftimestr" visible="0" cn="创建日期字符串" />
        <input group="后台字段" el="100" ek="fbillhead" id="fgeneratesource" fn="fgeneratesource" pn="fgeneratesource" visible="0" cn="生成客户来源" />
        <input group="后台字段" el="100" ek="fbillhead" id="fcustomerunique" fn="fcustomerunique" pn="fcustomerunique" visible="0" cn="客户唯一性校验" />
        <!--用来区分是不是经销商还是直营-->
        <select el="152" ek="fbillhead" visible="1150" id="fmanagemodel" fn="fmanagemodel" pn="fmanagemodel" cn="经营模式" vals="'0':'经销商','1':'直营'" defval="'0'" notrace="false" width="90" lix="3" />

        <input type="text" id="fdescription" el="100" ek="fbillhead" fn="fdescription" apipn="description" pn="fdescription" cn="备注" width="280" visible="-1" len="4000" lix="500" />

        <input group="基本信息" el="106" ek="fbillhead" id="freferrer" fn="freferrer" pn="freferrer" visible="1150" cn="推荐人" refid="ydj_customer" />
        
        <input group="基本信息" el="100" ek="fbillhead" id="fcustomersapnumber" fn="fcustomersapnumber" pn="fcustomersapnumber" cn="SAP客户编码" copy="0" lock="-1" visible="-1" >
        <!--资金帐户-->
        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_CustomerAccount" pn="fentry" cn="协同账户" kfks="">
            <tr>
                <th el="122" ek="fentry" id="fpurpose" fn="fpurpose" pn="fpurpose" cg="结算单账户类型" cn="账户" refid="bd_enum" dfld="fenumitem" width="140" visible="96" lock="-1" copy="0" align="center"></th>
                <!--fisnegative字段已废弃，前端不显示即可，不允许删除已有的字段-->
                <th el="116" ek="fentry" id="fisnegative" fn="fisnegative" pn="fisnegative" cn="允许为负" width="140" visible="0" copy="0"></th>
                <th el="116" ek="fentry" id="fispayment" fn="fispayment" pn="fispayment" cn="可用于支付订单" width="140" visible="96" copy="0"></th>
                <th el="116" ek="fentry" id="fisrecharge" fn="fisrecharge" pn="fisrecharge" cn="可收款" width="150" visible="96" copy="0"></th>
                <th el="116" ek="fentry" id="fisbalance" fn="fisbalance" pn="fisbalance" cn="余额结算无需确认" width="140" visible="0" copy="0"></th>
                <th el="116" ek="fentry" id="fisbalanceinunconfirm" fn="fisbalanceinunconfirm" pn="fisbalanceinunconfirm" cn="账户可用金额考虑待确认金额" width="200" visible="0"></th>
                <th el="116" ek="fentry" id="fiscash" fn="fiscash" pn="fiscash" cn="现金账户" width="100" visible="96" copy="0"></th>
                <th el="105" ek="fentry" id="fcredit" fn="fcredit" pn="fcredit" cn="信用额度" width="150" visible="0" copy="0" lock="-1"></th>
                <th el="105" ek="fentry" id="fbalance_e" fn="fbalance_e" pn="fbalance_e" cn="余额" width="140" visible="100" lock="-1" copy="0"></th>
                <th el="116" ek="fentry" id="fissyninit" fn="fissyninit" pn="fissyninit" cn="协同初始化" width="140" visible="0" lock="-1" copy="0"></th>
                <th el="144" ek="fentry" id="foperate" pn="foperate" cn="操作" visible="96" lock="-1" width="170" btnid="recharge,debit" btntxt="收款,退款" copy="0" align="center"></th>
            </tr>
        </table>

        <!--客户联系人信息-->
        <table id="fcuscontacttry" el="52" pk="fcuscontacttryid" tn="t_ydj_fcuscontacttry" pn="fcuscontacttry" cn="客户联系人信息" kfks="fcontact_a">
            <!--kfks="fcontact_a,fphone_a,fprovince_a,fcity_a,fregion_a"-->
            <tr>
                <th el="100" ek="fcuscontacttry" id="fcontact_a" fn="fcontacter" pn="fcontacter" cn="联系人姓名" width="100" visible="1150" notrace="false"></th>
                <!--<th el="153" ek="fcuscontacttry" id="fcontact_a" fn="fcontacter" pn="fcontacter" cn="联系人姓名" luek="fcuscontacttry" lunmfk="flocname" lunbfk="flocnumber" sformid=""  width="100" visible="1150"></th>-->
                <th el="100" ek="fcuscontacttry" id="fphone_a" fn="fphone" pn="fphone" cn="联系方式" width="100" visible="1150" notrace="false"></th>
                <th el="100" ek="fcuscontacttry" id="fcarea" cn="所在地区" width="200" visible="1150" lock="-1"></th>
                <th el="122" ek="fcuscontacttry" id="fprovince_a" fn="fprovince" pn="fprovince" cn="省" cg="省" visible="0" lock="-1"></th>
                <th el="122" ek="fcuscontacttry" id="fcity_a" fn="fcity" pn="fcity" cn="市" cg="市" visible="0" lock="-1"></th>
                <th el="122" ek="fcuscontacttry" id="fregion_a" fn="fregion" pn="fregion" cn="区" cg="区" visible="0" lock="-1"></th>
                <th el="100" ek="fcuscontacttry" id="faddress_a" fn="faddress" pn="faddress" cn="详细地址" width="100" visible="1150" notrace="false"></th>
                <th el="100" ek="fcuscontacttry" id="fcdescription" fn="fcdescription" pn="fcdescription" cn="备注" width="170" len="4000" visible="1150"></th>
                <th el="116" ek="fcuscontacttry" id="fisdefault" fn="fisdefault" pn="fisdefault" cn="默认地址" width="80" visible="1150"></th>
                <th el="144" ek="fcuscontacttry" id="foperation" pn="foperate" cn="操作" visible="96" lock="-1" width="170" btnid="selectarea" btntxt="选择地区" copy="0" align="center"></th>
                <th el="116" ek="fcuscontacttry" id="fcisfirst" fn="fcisfirst" pn="fcisfirst" cn="是否首次创建" width="80" visible="0"></th>
            </tr>
        </table>

        <!--客户负责人：哪些部门可以使用此资料-->
        <table id="fdutyentry" el="52" pk="fentryid" tn="t_ydj_customerdutyentry" pn="fdutyentry" cn="客户负责人" kfks="fdutyid,fdeptid">
            <tr>
                <th el="106" ek="fdutyentry" id="fdutyid" fn="fdutyid" pn="fdutyid" visible="1150" cn="负责人"
                    lock="-1" copy="0" lix="300" notrace="true" ts="" refid="ydj_staff" dfld="fname" width="300"></th>
                <th el="106" ek="fdutyentry" id="fdeptid" fn="fdeptid" pn="fdeptid" visible="1150" cn="所属部门"
                    lock="-1" copy="0" lix="310" notrace="true" ts="" refid="ydj_dept" dfld="fname" reflvt="1" width="300"></th>
                <th el="113" ek="fdutyentry" id="fjointime" fn="fjointime" pn="fjointime" visible="1150" cn="加入时间"
                    lock="-1" copy="0" lix="320" notrace="true" ts="" width="300"></th>
                <th el="100" ek="fdutyentry" id="fdescription_d" fn="fdescription" pn="fdescription" visible="1150" cn="说明"
                    lock="0" copy="0" lix="330" notrace="true" ts="" width="300" len="1000"></th>
                <th el="100" ek="fdutyentry" id="fpath" fn="fpath" pn="fpath" cn="组织架构" lock="-1" visible="0" lix="52"></th>

            </tr>
        </table>

        <table id="finvoiceentry" el="52" pk="fentryid" tn="t_ydj_customerinvoiceentry" pn="finvoiceentry" cn="开票信息" kfks="finvoicetype">
            <tr>
                <th el="152" ek="finvoiceentry" id="finvoicetype" fn="finvoicetype" pn="finvoicetype" visible="1150" cn="开票类型" vals="'01':'增值税专用发票','02':'增值税普通发票'"
                    lock="0" copy="0" lix="5" notrace="true" width="300"></th>
                <th el="100" ek="finvoiceentry" id="fbuyerfullname" fn="fbuyerfullname" pn="fbuyerfullname" visible="1150" cn="购买方全称"
                    lock="0" copy="0" lix="10" notrace="true" width="300"></th>
                <th el="100" ek="finvoiceentry" id="ftaxpayeridentify" fn="ftaxpayeridentify" pn="ftaxpayeridentify" visible="1150" cn="纳税人识别"
                    lock="0" copy="0" lix="20" notrace="true" ts="" width="300"></th>
                <th el="100" ek="finvoiceentry" id="finvoiceemail" fn="finvoiceemail" pn="finvoiceemail" visible="1150" cn="电子邮箱"
                    lock="0" copy="0" lix="330" notrace="true" ts="" width="300" len="1000"></th>
                <th el="116" ek="finvoiceentry" id="finvoicedefault" fn="finvoicedefault" pn="finvoicedefault" cn="设为默认" lock="0" copy="0" visible="1150" lix="70"></th>
                <th el="100" ek="finvoiceentry" id="finvoiceaddress" fn="finvoiceaddress" pn="finvoiceaddress" cn="地址" lock="0" copy="0" visible="1150" lix="30" width="300"></th>
                <th el="100" ek="finvoiceentry" id="finvoicephone" fn="finvoicephone" pn="finvoicephone" cn="电话" lock="0" copy="0" visible="1150" lix="40" width="300"></th>
                <th el="100" ek="finvoiceentry" id="fdepositbankname" fn="fdepositbankname" pn="fdepositbankname" cn="开户银行" lock="0" copy="0" visible="1150" lix="50" width="300"></th>
                <th el="100" ek="finvoiceentry" id="fbankaccount" fn="fbankaccount" pn="fbankaccount" cn="银行账户" lock="0" copy="0" visible="1150" lix="60" width="300" ></th>
            </tr>
        </table>

        <!--收货地址信息-->
        <!--<table id="faddressentry" el="52" pk="fentryid" tn="t_ydj_customeraddressentry" pn="faddressentry" cn="收货地址信息">
        <tr>
            <th el="116" ek="faddressentry" id="fisdefault" fn="fisdefault" pn="fisdefault" cn="默认地址" width="80" visible="-1"></th>
            <th el="100" ek="faddressentry" id="fcontact_a" fn="fcontact" pn="fcontact" cn="联系电话" sformid="" width="110" visible="-1"></th>
            <th el="100" ek="faddressentry" id="fphone_a" fn="fphone" pn="fphone" cn="联系电话" sformid="" width="110" visible="-1"></th>

            <th el="100" ek="faddressentry" id="faddress_a" fn="faddress" pn="faddress" cn="详细地址" lix="10" visible="-1"></th>
        </tr>
    </table>-->
    </div>
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" id="save_valid_fnumber" cn="保存时编码唯一" vid="500" data="fmainorgid,fnumber" precon=""></li>
            <li el="11" id="save_valid_fname" cn="保存时名称唯一" vid="500" remove></li>
        </ul>
        <ul el="10" ek="fbillhead" id="opencompany" op="opencompany" opn="创建企业" data="" permid="fw_opencompany"></ul>
        <ul el="10" ek="fbillhead" id="changedutybydept" op="changedutybydept" opn="不按部门调整负责人" data="" permid="fw_changedutybydept"></ul>
        <ul el="10" id="fw_pushservice" op="fw_pushservice" opn="新增服务" data="{'parameter':{'ruleId':'ydj_customer2ydj_service'}}" permid="fw_pushservice"></ul>
        <ul el="10" id="followerrecord" op="followerrecord" opn="跟进" data="" permid="followerrecord"></ul>
        <!-- <ul el="10" id="leadsreplace" op="leadsreplace" opn="更换负责人" data="" permid="fw_ydj_customer_leadsreplace"></ul> -->
        <ul el="10" id="addduty" op="addduty" opn="新增负责人" data="" permid="fw_ydj_customer_addduty"></ul>
        <ul el="10" id="replaceduty" op="replaceduty" opn="更换负责人" data="" permid="fw_ydj_customer_replaceduty"></ul>
        <ul el="10" id="removeduty" op="removeduty" opn="移除负责人" data="" permid="fw_ydj_customer_removeduty"></ul>
        <ul el="10" id="recycle" op="recycle" opn="回收" data="" permid="ydj_customerrecord_recycle"></ul>
        <ul el="10" id="commoncus" op="commoncus" opn="查看公海客户" data="" permid="fw_commoncus"></ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fmemberno','fdescription_d','dealerNo','oppid','fnumber','fname','fphone','fwechat','fcontacts','fprovince','fcity','fregion','fdutyid','fdeptid','fjointime','fdescription']}" permid=""></ul>
        <ul el="10" ek="fbillhead" id="threeddesign" op="threeddesign" opn="3D设计" data="" permid="fw_3ddesign"></ul>
        <ul el="10" ek="fbillhead" id="savesubmit" op="savesubmit" opn="保存并提交" data="" permid="fw_savesubmit"></ul>
        <ul el="10" ek="fbillhead" id="saveaudit" op="saveaudit" opn="保存并审核" data="" permid="fw_saveaudit"></ul>
        <ul el="10" ek="fbillhead" id="distributeduty" op="distributeduty" opn="分配" data="" permid="fw_distributeduty"></ul>
        <!--<ul el="10" id="recycletodept" op="recycletodept" opn="回收到门店" data="" permid="fw_ydj_customer_recycletodept"></ul>
    <ul el="10" id="recycletocompany" op="recycletocompany" opn="回收到公司" data="" permid="fw_ydj_customer_recycletocompany"></ul>-->
    </div>
    <div id="permList">
        <ul el="12" id="fw_opencompany" cn="创建企业"></ul>
        <ul el="12" id="fw_changedutybydept" cn="不按部门调整负责人"></ul>
        <ul el="12" id="fw_pushservice" cn="新增服务"></ul>
        <ul el="12" id="followerrecord" cn="跟进"></ul>
        <!-- <ul el="12" id="fw_ydj_customer_leadsreplace" cn="更换负责人"></ul> -->
        <ul el="12" id="fw_ydj_customer_addduty" cn="新增负责人"></ul>
        <ul el="12" id="fw_ydj_customer_replaceduty" cn="更换负责人"></ul>
        <ul el="12" id="fw_ydj_customer_removeduty" cn="移除负责人"></ul>
        <ul el="12" id="ydj_customerrecord_recycle" cn="回收"></ul>
        <ul el="12" id="fw_commoncus" cn="查看公海客户"></ul>
        <ul el="12" id="fw_3ddesign" cn="3D设计"></ul>
        <ul el="12" id="fw_savesubmit" cn="保存并提交"></ul>
        <ul el="12" id="fw_saveaudit" cn="保存并审核"></ul>
        <ul el="12" id="fw_distributeduty" cn="分配"></ul>
        <!--<ul el="12" id="fw_ydj_customer_recycletodept" cn="回收到门店"></ul>
    <ul el="12" id="fw_ydj_customer_recycletocompany" cn="回收到公司"></ul>-->
    </div>
</body>
</html>