<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="bas_pricescheme" basemodel="bd_basetmpl" el="3" cn="经销定价方案" isolate="1" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bas_pricescheme" pn="fbillhead" cn="经销定价方案">

        <!--重写基类模型中的部分字段属性-->
        <input id="fnumber" el="100" cn="方案编码" visible="-1" width="140" />
        <input id="fname" el="100" cn="方案名称" visible="-1" width="200" />
        <input id="fdescription" el="100" visible="0" />

        <input el="125" ek="fbillhead" visible="-1" id="fpriceobj" fn="fpriceobj" pn="fpriceobj" cn="定价对象" cg="定价对象" refid="bd_enum" width="200" lix="5" />
        <input el="101" ek="fbillhead" visible="-1" id="fpriority" fn="fpriority" pn="fpriority" cn="优先级" lix="10" />

    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="11" id="save_valid_fpriority" cn="保存时优先级唯一" vid="500" data="fmainorgid,fpriority" precon=""></li>
            <li el="11" vid="510" ek="fbillhead" cn="优先级不能小于0" data="{'expr':'fpriority>=0','message':'优先级不能小于0！'}"></li>
        </ul>
        <!--<ul el="10" id="viewdetail" op="viewdetail" opn="查看明细" data="" permid="perm_viewdetail"></ul>-->
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <!--<ul el="12" id="perm_viewdetail" cn="查看明细"></ul>-->
    </div>
</body>
</html>