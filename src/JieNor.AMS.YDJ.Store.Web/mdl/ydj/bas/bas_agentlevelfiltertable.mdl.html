<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="bas_agentlevelfiltertable" el="3" cn="存储一级/二级经销商筛选条件设置" isolate="1" vom="3" nfsa="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bas_agentlevelfiltertable" pn="fbillhead" cn="存储一级/二级经销商筛选条件设置">

        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" refId="sec_user" dfld="fname" visible="-1" width="70" lock="-1" copy="0" lix="250" xlsin="0" />
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" visible="-1" width="105" lock="-1" copy="0" lix="251" xlsin="0" />
        <input el="120" ek="fbillhead" id="fmodifierid" fn="fmodifierid" cn="修改人" refId="sec_user" dfld="fname" visible="1124" width="70" lock="-1" copy="0" lix="252" xlsin="0" />
        <input el="121" ek="fbillhead" id="fmodifydate" fn="fmodifydate" cn="修改日期" visible="1124" width="105" lock="-1" copy="0" lix="253" xlsin="0" />
        <input el="148" ek="fbillhead" id="fmainorgid" fn="fmainorgid" cn="企业主体" visible="0" xlsin="0" copy="0" />

        <input ek="fbillhead" el="100" id="fagentnumber" fn="fagentnumber" pn="fagentnumber" cn="经销商编码" copy="0" width="120" visible="96" lock="-1"/>
        <input ek="fbillhead" el="106" id="fagentid" fn="fagentid" pn="fagentid" refid="bas_agent" cn="经销商名称" width="200" visible="96" dfld="fnumber,fname" must="1"/>
        <input ek="fbillhead" el="152" id="fagentstatus" fn="fagentstatus" pn="fagentstatus" cn="经销商状态" visible="96" width="120" vals="'0':'禁用','1':'启用'" lock="-1"/>
        <input ek="fbillhead" el="100" id="fdistributorsnumber" fn="fdistributorsnumber" pn="fdistributorsnumber" cn="招商经销商编码" visible="96" width="120" lock="-1"/>
        <input ek="fbillhead" el="100" id="fdistributorsname" fn="fdistributorsname" pn="fdistributorsname" cn="招商经销商名称" visible="96" width="160" lock="-1" copy="0"/>
    </div>
</body>
</html>