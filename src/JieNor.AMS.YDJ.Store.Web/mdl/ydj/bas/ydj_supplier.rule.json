{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //当为三证合一时，锁定对应图片字段
    {
      "id": "lock_licenseimage",
      "expression": "field:flicenseimage|fisthree=='isthree_03'"
    },
    {
      "id": "lock_organizationimage",
      "expression": "field:forganizationimage|fisthree=='isthree_03'"
    },
    {
      "id": "lock_taximage",
      "expression": "field:ftaximage|fisthree=='isthree_03'"
    },
    {
      "id": "unlock_licenseimage",
      "expression": "field:$flicenseimage|fisthree!='isthree_03'"
    },
    {
      "id": "unlock_organizationimage",
      "expression": "field:$forganizationimage|fisthree!='isthree_03'"
    },
    {
      "id": "unlock_taximage",
      "expression": "field:$ftaximage|fisthree!='isthree_03'"
    },
    //经营类型为公司时解锁法人相关字段
    {
      "id": "lock_corporatename",
      "expression": "field:fcorporatename|fmanagetype!='manage_type_01'"
    },
    {
      "id": "lock_corporatenumber",
      "expression": "field:fcorporatenumber|fmanagetype!='manage_type_01'"
    },
    {
      "id": "unlock_corporatename",
      "expression": "field:$fcorporatename|fmanagetype=='manage_type_01'"
    },
    {
      "id": "unlock_corporatenumber",
      "expression": "field:$fcorporatenumber|fmanagetype=='manage_type_01'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

    //账户信息的隐藏与显示
    {
      "id": "hide_accountinfo",
      "expression": "other:.y-accountinfo$|1==1"
    },
    {
      "id": "show_accountinfo",
      "expression": "other:$.y-accountinfo|1==1"
    },
    //法人信息的隐藏显示
    {
      "id": "hide_corporate",
      "expression": "other:.corporate-mes$|fmanagetype!='manage_type_01'"
    },
    {
      "id": "show_corporate",
      "expression": "other:$.corporate-mes|fmanagetype=='manage_type_01'"
    },
    //品牌信息的隐藏
    {
      "id": "hide_brandinfo",
      "expression": "other:#brandinfoarea$| 1 == 1"
    },
    {
      "id": "show_brandinfo",
      "expression": "other:$#brandinfoarea| 1 != 1"
    }
  ],

  //定义表单计算规则
  "calcRules": [
    //“名称“=“销售组织名称”，省市区、联系人、电话=联系电话、详细地址、法人姓名=法人、法人身份证号码，统一从【组织】带过来
    { "expression": "fcontacts=forgid.fcontacterphone | fname ==forgid.fname" }
  ]
}