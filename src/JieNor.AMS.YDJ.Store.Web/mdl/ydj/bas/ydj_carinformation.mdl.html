<!DOCTYPE html>
<html>
<head>
    <title></title>
	<meta charset="utf-8" />
</head>
<body id="ydj_carinformation" nubcr="true" basemodel="bd_basetmpl" el="3" cn="车辆" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_carinformation" pn="fbillhead" cn="车辆"> 
        <input group="基本信息" type="text" id="fname" el="108" ek="fbillhead" fn="fname" pn="fname" cn="名称"  width="120" visible="-1" copy="0"  lix="4" />
        <input group="基本信息" type="text" id="fnumber" el="108" ek="fbillhead" fn="fnumber" pn="fnumber" cn="车牌号" desc="车牌号" width="120" visible="-1" copy="0"  lix="1" />
        <input group="基本信息" el="106" ek="fbillhead" id="fdriver" fn="fdriver" pn="fdriver" cn="司机" refid="ydj_staff" visible="-1" lix="2" copy="0" />
        <input group="基本信息" el="106" ek="fbillhead" id="fcartype" fn="fcartype" pn="fcartype" cn="车辆类型" refid="ser_truckinfo" visible="-1" copy="0" lix="3"/>
        <input group="基本信息" el="106" ek="fbillhead" id="fserviceteamid" fn="fserviceteamid" pn="fserviceteamid" visible="-1" cn="服务团队"
               lock="0" copy="0" lix="43" notrace="true" ts="" refid="ydj_team" filter="" reflvt="0" dfld="" />
        
    
    </div>
    
</body>
</html>

