{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //解锁和锁定关联角色字段
    {
      "id": "lock_roleids",
      "expression": "field:froleids|flinkuserid==''"
    },
    {
      "id": "unlock_roleids",
      "expression": "field:$froleids|flinkuserid!=''"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fphone=flinkuserid__fphone|flinkuserid!=''" }
  ]
}