
{
  //�����������
  "base": "/mdl/bd.rule.json",

  //�������������
  //�������Թ�������ࣺfield �ֶΣ�menu �˵���other ���򣨱�ʾ��ĳһ�����е������ֶν��������ԣ�
  /*
    �������(array)
      |--����(object)
        |--id(string):�����ʶ
        |--expression(string):������ʽ | ǰ������
          |--expr:���ʽ
            |--field:�ֶ�
              |--����$����
            |--menu:�˵�
              |--����$����
            |--other:����
              |--����$����
          |--condition:ǰ������
  */
  "lockRules": [

  ],

  //������ɼ��Թ���
  "visibleRules": [
   
  ],

  //������������
  "calcRules": [
    { "expression": "fname=fmarkno__fenumitem| fmarkno!='' or fmarkno!=' '" },
    { "expression": "fname=''| fmarkno=='' or fmarkno==' '" }
  ]
}
