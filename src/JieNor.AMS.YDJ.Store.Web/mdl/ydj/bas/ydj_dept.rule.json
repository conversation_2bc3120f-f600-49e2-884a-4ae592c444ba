{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",
  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lock_flinkcustomerid",
      "expression": "field:flinkcustomerid|fisvendor==false"
    },
    {
      "id": "unlock_flinkcustomerid",
      "expression": "field:$flinkcustomerid|fisvendor==true"
    },
    {
      "id": "lock_flinkcustomerid",
      "expression": "field:fmarketplace,fisunifiedorder,fisunifiedcashier,fdeductrate|fissettled==false"
    },
    {
      "id": "unlock_flinkcustomerid",
      "expression": "field:$fmarketplace,fisunifiedorder,fisunifiedcashier,fdeductrate|fissettled==true"
    }
  ],
  "visibleRules": [
    //隐藏入驻卖场设置
    {
      "id": "hide_lookscheme",
      "expression": "other:.marketsetting$|fdeptstroe==false"
    },
    //显示入驻卖场设置
    {
      "id": "show_lookscheme",
      "expression": "other:$.marketsetting|fdeptstroe==true"
    }
  ],
  //定义表单计算规则
  "calcRules": [
    { "expression": "flinkcustomerid=''|fisvendor==false" },
    { "expression": "fissettled=false|fdeptstroe==false" },
    { "expression": "fmarketplace=''|fissettled==false" },
    { "expression": "fisunifiedorder=false|fissettled==false" },
    { "expression": "fisunifiedcashier=false|fissettled==false" },
    { "expression": "fdeductrate=0|fissettled==false" },
    { "expression": "fsname=fstore__fshortname|fstore!=''" }
  ]
}