<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_customerinvoice" el="3" cn="客户开票信息" rac="false" IsAsynLstDesc="true">
        <div id="fbillhead" el="51" pk="fentryid" tn="t_ydj_customerinvoiceentry" pn="fbillhead" cn="客户开票信息">
            <!-- 重写字段 -->
            <input el="100" ek="fbillhead" id="fid" fn="fid" cn="客户联系人ID" visible="0" lock="-1" width="150" align="center" />
            <input el="100" ek="fbillhead" id="fseq" fn="fseq" cn="行号" visible="0" align="center" />
            <input el="152" ek="fbillhead" id="finvoicetype" fn="finvoicetype" pn="finvoicetype" visible="-1" cn="开票类型" vals="'01':'增值税专用发票','02':'增值税普通发票'"
                lock="0" copy="0" lix="300" notrace="true" width="200"/>
            <input el="100" ek="fbillhead" id="fbuyerfullname" fn="fbuyerfullname" pn="fbuyerfullname" visible="-1" cn="购买方全称"
                lock="0" copy="0" lix="310" notrace="true" width="200"/>
            <input el="100" ek="fbillhead" id="ftaxpayeridentify" fn="ftaxpayeridentify" pn="ftaxpayeridentify" visible="-1" cn="纳税人识别"
                lock="0" copy="0" lix="320" notrace="true" ts="" width="200"/>
            <input el="100" ek="fbillhead" id="finvoiceemail" fn="finvoiceemail" pn="finvoiceemail" visible="-1" cn="电子邮箱"
                lock="0" copy="0" lix="330" notrace="true" ts="" width="200" len="1000"/>
            <input el="116" ek="fbillhead" id="finvoicedefault" fn="finvoicedefault" pn="finvoicedefault" cn="设为默认" lock="0" copy="0" visible="-1" lix="52"/>
            <input el="100" ek="fbillhead" id="finvoiceaddress" fn="finvoiceaddress" pn="finvoiceaddress" cn="地址" lock="0" copy="0" visible="-1" lix="52"/>
            <input el="100" ek="fbillhead" id="finvoicephone" fn="finvoicephone" pn="finvoicephone" cn="电话" lock="0" copy="0" visible="-1" lix="52"></input>
            <input el="100" ek="fbillhead" id="fdepositbankname" fn="fdepositbankname" pn="fdepositbankname" cn="开户银行" lock="0" copy="0" visible="-1" lix="52"/>
            <input el="100" ek="fbillhead" id="fbankaccount" fn="fbankaccount" pn="fbankaccount" cn="银行账户" lock="0" copy="0" visible="-1" lix="52"/>
        </div>
        <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
        <div id="opList">
        
        </div>
        <div id="permList">
        </div>
    </body>
</html>