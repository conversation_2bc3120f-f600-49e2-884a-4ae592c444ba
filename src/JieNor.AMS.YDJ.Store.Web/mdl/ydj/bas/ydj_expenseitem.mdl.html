<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_expenseitem" basemodel="bd_basetmpl" el="3" cn="费用项目" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_expenseitem" pn="fbillhead" cn="费用项目">
        <!--基本信息-->
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="ftype" fn="ftype" pn="ftype" cn="费用类型" cg="费用类型" refid="bd_enum" dfld="fenumitem" defval="'expensetype_01'" width="100"></select>

        <!--R-BL-BG035收款协同-已扣佣金/费用明细-->
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" id="fisbrokerage" fn="fisbrokerage" pn="fisbrokerage" cn="佣金项目" lix="8" width="90" />
    </div>
</body>
</html>