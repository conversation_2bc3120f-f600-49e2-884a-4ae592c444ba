{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [

    //保存后，锁定【选配套件】字段
    {
      "id": "lock_fsuiteflag",
      "expression": "field:fsuiteflag$|id!='' and id!=' '"
    },

    //如果是已发布则锁定全部字段
    {
      "id": "lock_allfield",
      "expression": "field:*|fsendstatus=='已发布'"
    },

    //发布按钮的锁定与解锁
    {
      "id": "lock_pubpro",
      "expression": "menu:tbPubPro|fsendstatus!='未发布' and fsendstatus!='已下架'"
    },
    {
      "id": "unlock_pubpro",
      "expression": "menu:$tbPubPro|fsendstatus=='未发布' and fsendstatus=='已下架'"
    },

    //重新发布按钮的锁定与解锁
    {
      "id": "lock_pubproagain",
      "expression": "menu:tbPubProAgain|fsendstatus!='待更新'"
    },
    {
      "id": "unlock_pubproagain",
      "expression": "menu:$tbPubProAgain|fsendstatus=='待更新'"
    },

    //下架按钮的锁定与解锁
    {
      "id": "lock_revokepro",
      "expression": "menu:tbRevokePro|fsendstatus!='待更新' and fsendstatus!='已发布'"
    },
    {
      "id": "unlock_revokepro",
      "expression": "menu:$tbRevokePro|fsendstatus=='待更新' and fsendstatus=='已发布'"
    },

    //如果是协同下载的商品，则锁定供应商和供货商货号和允许定制
    {
      "id": "lock_supplier",
      "expression": "field:fsupplierid,fsuppliernumber,fcustom|fdataorigin=='下载'"
    },

    //如果是协同下载的商品，则锁定允许定制
    {
      "id": "lock_fcustom",
      "expression": "field:fcustom|fdataorigin=='下载'"
    },
    //如果不是协同下载的商品，则不锁定允许定制
    {
      "id": "unlock_fcustom",
      "expression": "field:$fcustom|fdataorigin!='下载'"
    },

    //已发布商品锁定编码
    {
      "id": "unlock_revokepro",
      "expression": "field:fnumber|fsendstatus=='已发布'"
    },
    {
      "id": "lock_endpurchase",
      "expression": "field:fendpurchase,funitid|ffromchaindataid!=''"
    },
    {
      "id": "unlock_endpurchase",
      "expression": "field:$fendpurchase,funitid|ffromchaindataid==''"
    },
    {
      "id": "lock_pricefields",
      "expression": "field:fsalprice,fpurprice|fispresetprop==true"
    },
    {
      "id": "unlock_pricefields",
      "expression": "field:$fsalprice,fpurprice|fispresetprop==false"
    },

    //商品保存后，锁定基本单位和库存单位，以防止先入库后再修改基本单位和库存单位后再出库时，原始入库商品无法出库的问题
    {
      "id": "lock_baseunit",
      "expression": "field:funitid,fstockunitid$|id!='' and id!=' '"
    },

    //是套件时不能单独销售
    {
      "id": "lock_isindividualsale",
      "expression": "field:fisindividualsale|fissuit==true"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //{ "expression": "fstockunitid=funitid|funitid!='' and funitid!=' ' and (fstockunitid=='' or fstockunitid==' ')" },
    //{ "expression": "fsalunitid=funitid|funitid!='' and funitid!=' ' and (fsalunitid=='' or fsalunitid==' ')" },
    //{ "expression": "fpurunitid=funitid|funitid!='' and funitid!=' ' and (fpurunitid=='' or fpurunitid==' ')" },
    { "expression": "fstockunitid=funitid|funitid!='' and funitid!=' '" },
    { "expression": "fsalunitid=funitid|funitid!='' and funitid!=' '" },
    { "expression": "fpurunitid=funitid|funitid!='' and funitid!=' '" }
  ]
}