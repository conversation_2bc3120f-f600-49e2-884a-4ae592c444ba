<html lang="en">
<head>
</head>
<body id="bas_mainauxprop" el="3" basemodel="bd_basetmpl" cn="主辅属性约束" aqkf="9">
    <div id="fbillhead" el="51" pk="fid" tn="t_bas_mainauxprop" pn="fbillhead" cn="主属性信息">

        <!--重写基类模型部分字段属性-->
        <input el="108" id="fnumber" width="100" />
        <input el="100" id="fname" width="100" />
        <input el="122" id="fstatus" width="80" />
        <input el="118" id="fcreatorid" width="70" />
        <input el="119" id="fcreatedate" width="105" />

        <!--隐藏基类模型部分字段-->
        <input el="100" id="fdescription" visible="0" xlsin="0" />
        <input el="116" id="fispreset" visible="0" />
        <input el="152" id="fchangestatus" visible="0" />
        <input el="100" id="fnextprocnode" visible="0" xlsin="0" />
        <input el="128" id="fprintcount" visible="0" />
        <input el="124" id="fprintid" visible="0" />
        <input el="113" id="fprintdate" visible="0" />
        <input el="100" id="fsendstatus" visible="0" />
        <input el="100" id="fdataorigin" visible="0" />
        <input el="113" id="fsenddate" visible="0" />
        <input el="113" id="fdownloaddate" visible="0" />

        <input el="106" ek="fbillhead" id="fauxpropid" fn="fauxpropid" cn="属性类别" 
               refid="bd_auxproperty" sformid="" 
               filter="fmainprop='1' and fvaluesource='val_type_01' and frefenumformid='5b0f5e6d1db7a3'" 
               must="1" visible="-1" width="150" lix="5" />

        <!--单据头暂时不支持动态类型字段-->
        <!--<input el="152" ek="fbillhead" id="fvaluesrc" fn="fvaluesrc" cn="值类型" visible="-1" width="100"
               vals="'basedata':'基础资料','enumdata':'辅助资料','text':'文本'" lock="-1"/>
        <input el="166" ek="fbillhead" id="fvaluetype" fn="fvaluetype" cn="值对象" visible="-1" width="100"
               refid="bd_bizfieldtype" dfld="fsrc"/>
        <input el="167" ek="fbillhead" id="fvalueid" fn="fvalueid" cn="属性值" visible="-1" width="100"
               ctlfk="fvaluesrc,fvaluetype" desc="注意：ctlfk属性值顺序不能变"/>-->

        <!--<input el="117" ek="fbillhead" id="fvaluesource" fn="fvaluesource" cn="值类型" refid="bd_enum" cg="值类型" dfld="fenumitem"
               defval="'val_type_01'" filter="fid in('val_type_01','val_type_03')" must="1" visible="0" lock="-1" width="100" lix="10" />

        <input el="106" ek="fbillhead" id="frefenumformid" fn="frefenumformid" cn="辅助资料"
               refid="bd_enumdata" sformid="" filter="fvisible='1'" visible="0" lock="-1" width="120" lix="15" />-->

        <!--博领暂时写死用辅助资料字段来实现，后续等平台动态类型字段完善后再按需升级-->
        <input el="117" ek="fbillhead" id="frefenumvalue" fn="frefenumvalue" cn="属性值"
               refid="bd_enum" cg="辅助属性-材质" dfld="fenumitem" must="1" visible="-1" width="150" lix="20" />

        <!--<input el="100" ek="fbillhead" id="ftextvalue" fn="ftextvalue" cn="文本值" visible="0" width="150" lix="25" />-->

    </div>

    <table id="fentity" el="52" pk="fentryid" tn="t_bas_mainauxpropentry" pn="fentity" cn="辅属性信息" kfks="fauxpropid_e,frefenumvalue_e" aqkf="true">
        <tr>
            <th el="106" ek="fentity" visible="1150" id="fauxpropid_e" fn="fauxpropid" pn="fauxpropid" cn="属性类别"
                refid="bd_auxproperty" sformid="" 
                filter="fmainprop='0' and fvaluesource='val_type_01' and frefenumformid='5b0fd271f4956f'" 
                must="1" width="150"></th>

            <!--动态类型字段，暂时无用隐藏-->
            <!--<th el="152" ek="fentity" visible="0" id="fvaluesrc_e" fn="fvaluesrc" pn="fvaluesrc" cn="值类型"
                vals="'basedata':'基础资料','enumdata':'辅助资料','text':'文本'" lock="-1" width="130"></th>

            <th el="166" ek="fentity" visible="0" id="fvaluetype_e" fn="fvaluetype" pn="fvaluetype" cn="值对象"
                refid="bd_bizfieldtype" dfld="fsrc" sformid="" lock="-1" width="200"></th>

            <th el="167" ek="fentity" visible="0" id="fvalueid_e" fn="fvalueid" pn="fvalueid" cn="属性值"
                ctlfk="fvaluesrc_e,fvaluetype_e" must="0" width="200" desc="注意：ctlfk属性值顺序不能变，必须是先类型后对象"></th>-->

            <!--隐藏字段，不存储到数据库，用于实现【属性类别】字段值携带的逻辑-->
            <!--<th el="117" ek="fentity" visible="0" id="fvaluesource_e" fn="" pn="fvaluesource" cn="值类型"
                filter="fid in('val_type_01','val_type_03')" lock="-1" width="100"></th>
            <th el="106" ek="fentity" visible="0" id="frefenumformid_e" fn="" pn="frefenumformid" cn="辅助资料"
                refid="bd_enumdata" sformid="" filter="fvisible='1'" lock="-1" width="120"></th>-->

            <!--博领暂时写死用辅助资料字段来实现，后续等平台动态类型字段完善后再按需升级-->
            <th el="117" ek="fentity" visible="1150" id="frefenumvalue_e" fn="frefenumvalue" pn="frefenumvalue" cn="属性值"
                refid="bd_enum" cg="辅助属性-颜色" dfld="fenumitem" must="1" width="200"></th>

            <th el="111" ek="fentity" visible="1150" id="fimage_e" fn="fimage" pn="fimage" cn="色卡图片" 
                must="0" width="100" maxsize="2048" desc="最大限制2MB"></th>

            <th el="116" ek="fentity" visible="1150" id="fisdisable" fn="fisdisable" pn="fisdisable" cn="是否禁用" width="90"></th>
        </tr>
    </table>

    <table id="fexclentity" el="52" pk="fentryid" tn="t_bas_mainauxpropexcl" pn="fexclentity" cn="例外信息" kfks="fproductid_ee,fauxpropid_ee,frefenumvalue_ee" aqkf="true">
        <tr>
            <th el="107" ek="fexclentity" visible="1150" id="fmtrlnumber" fn="" pn="fmtrlnumber" cn="商品编码" 
                lock="-1" copy="1" ctlfk="fproductid_ee" dispfk="fnumber" width="150"></th>

            <th el="106" ek="fexclentity" visible="1150" id="fproductid_ee" fn="fproductid" pn="fproductid" cn="商品"
                refid="ydj_product" sformid="" must="1" width="200"></th>

            <th el="132" ek="fexclentity" visible="1150" id="fattrinfo_ee" fn="fattrinfo" pn="fattrinfo" cn="辅助属性"
                ctlfk="fproductid_ee" must="0" width="240"></th>

            <th el="106" ek="fexclentity" visible="1150" id="fauxpropid_ee" fn="fauxpropid" pn="fauxpropid" cn="属性类别"
                refid="bd_auxproperty" sformid="" 
                filter="fmainprop='0' and fvaluesource='val_type_01' and frefenumformid='5b0fd271f4956f'" 
                must="1" width="150"></th>

            <!--动态类型字段，暂时无用隐藏-->
            <!--<th el="152" ek="fexclentity" visible="0" id="fvaluesrc_ee" fn="fvaluesrc" pn="fvaluesrc" cn="值类型"
                vals="'basedata':'基础资料','enumdata':'辅助资料','text':'文本'" lock="-1" width="130"></th>

            <th el="166" ek="fexclentity" visible="0" id="fvaluetype_ee" fn="fvaluetype" pn="fvaluetype" cn="值对象"
                refid="bd_bizfieldtype" dfld="fsrc" sformid="" lock="-1" width="200"></th>

            <th el="167" ek="fexclentity" visible="0" id="fvalueid_ee" fn="fvalueid" pn="fvalueid" cn="属性值"
                ctlfk="fvaluesrc_ee,fvaluetype_ee" must="0" width="200" desc="注意：ctlfk属性值顺序不能变，必须是先类型后对象"></th>-->

            <!--隐藏字段，不存储到数据库，用于实现【属性类别】字段值携带的逻辑-->
            <!--<th el="117" ek="fexclentity" visible="0" id="fvaluesource_ee" fn="" pn="fvaluesource" cn="值类型"
                filter="fid in('val_type_01','val_type_03')" lock="-1" width="100"></th>
            <th el="106" ek="fexclentity" visible="0" id="frefenumformid_ee" fn="" pn="frefenumformid" cn="辅助资料"
                refid="bd_enumdata" sformid="" filter="fvisible='1'" lock="-1" width="120"></th>-->

            <!--博领暂时写死用辅助资料字段来实现，后续等平台动态类型字段完善后再按需升级-->
            <!-- <th el="117" ek="fexclentity" visible="1150" id="frefenumvalue_ee" fn="frefenumvalue" pn="frefenumvalue" cn="属性值"
                refid="bd_enum" cg="辅助属性-颜色" dfld="fenumitem" must="1" width="200"></th> -->
            <th el="117" ek="fexclentity" visible="1150" id="frefenumvalue_ee" fn="frefenumvalue" pn="frefenumvalue" cn="属性值"
                refid="bd_enum" dfld="fenumitem" must="1" width="200"></th> 
        </tr>
    </table>

</body>
</html>