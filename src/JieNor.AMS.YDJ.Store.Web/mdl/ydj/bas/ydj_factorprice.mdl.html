<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_factorprice" basemodel="bd_basetmpl" el="3" cn="定制品零售系数" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_factorprice" pn="fbillhead" cn="定制品零售系数">

        <!--重写基类模型中的部分字段属性-->
        <input id="fnumber" el="100" visible="-1" lix="1" />
        <input id="fname" el="100" visible="-1" copy="0" />
        <input id="fdescription" el="100" visible="0" />
        <!--基本信息-->
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lix="27"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="32" copy="0" lix="251" />
        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" width="130" visible="32" copy="0" lix="251" />
        <!--系数信息-->
        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_factorpriceentry" pn="fentry" cn="系数信息" kfks="fproductid">
            <tr>
                <th lix="190" el="107" ek="fentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fnumber" refvt="0" ></th>
                <th lix="191" el="106" ek="fentry" id="fproductid" fn="fproductid" refid="ydj_product" multsel="true" dfld="fselcategoryid,fispresetprop,fcustom" visible="-1" ts="" sformid="" cn="商品" width="200" lock="-1"></th>
                <th lix="231" el="104" ek="fentry" id="fsalprice" fn="fsalprice" cn="统一零售价" width="100" visible="-1" format="0,000.00" lock="-1"></th>
                <th lix="231" el="104" ek="fentry" id="fpurprice" fn="fpurprice" cn="采购价" width="100" visible="-1" format="0,000.00" lock="-1"></th>
                <th lix="192" el="107" ek="fentry" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fspecifica" refvt="0"></th>
                <th lix="193" el="102" ek="fentry" visible="-1" id="factor" fn="factor" pn="factor" cn="零售系数" lock="0"  format="0,000.00"/>


            </tr>
        </table>

        <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
        <div id="opList">
            <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            </ul>
        </div>

        <!--表单所涉及的权限项定义-->
        <div id="permList">

        </div>

    </div>
</body>
</html>