<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_price" basemodel="bd_basetmpl" el="3" cn="销售价目" isolate="0" IsAsynLstDesc="true" querydbnode="">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_price" pn="fbillhead" cn="销售价目">

        <!--重写基类模型中的部分字段属性-->
        <input id="fnumber" el="100" visible="-1" lix="1" />
        <input id="fname" el="100" visible="-1" copy="0" />
        <input id="fdescription" el="100" visible="0" />
        <!--基本信息-->
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lix="27"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="1150" copy="0" lix="251" />
        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" width="130" visible="1150" copy="0" lix="251" />
        <!--基本信息-->
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="flimit" fn="flimit" pn="flimit" cn="限定客户" cg="限定客户" refid="bd_enum" dfld="fenumitem" filter="fid='limit_01'" lix="3"></select>
        <input group="基本信息" el="122" ek="fbillhead" id="ftype" fn="ftype" visible="1150" cn="报价类型" cg="报价类型" refid="bd_enum" dfld="fenumitem" filter="fid<>'quote_type_02'" defval="'quote_type_01'" lix="5" lock="-1" width="120" must="1" />
        <input el="106" ek="fbillhead" id="fbizorgid" fn="fbizorgid" pn="fbizorgid" cn="业务组织" refid="bas_organization" defVal="@currentOrgId"
               lock="-1" visible="0" copy="0" len="50" lix="115" desc="使用组织信息，用于组织隔离和控制商品的可编辑" />

        <table id="fcusentry" el="52" pk="fentryid" tn="t_ydj_customerentry" pn="fcusentry" cn="适用客户" kfks="fcustomerid">
            <tr>
                <th el="106" ek="fcusentry" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" visible="1150" cn="客户" ts="" lix="7" sformid="" refid="ydj_customer" width="400" />
                <th el="122" ek="fcusentry" id="fcustype" fn="fcustype" pn="fcustype" cn="客户分类" cg="客户分类" refid="bd_enum" dfld="fenumitem" visible="0"></th>
            </tr>
        </table>

        <!--报价信息-->
        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_priceentry" pn="fentry" cn="报价信息" kfks="fproductid">
            <tr>
                <th lix="190" el="107" ek="fentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fnumber" refvt="0"></th>
                <th lix="191" el="106" ek="fentry" id="fproductid" fn="fproductid" refid="ydj_product" multsel="true" dfld="fselcategoryid,fispresetprop,fcustom" visible="-1" ts="" sformid="" cn="商品" width="200"></th>
                <th lix="192" el="107" ek="fentry" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fspecifica" refvt="0"></th>
                <th lix="205" el="132" ek="fentry" id="fattrinfo" fn="fattrinfo" cn="辅助属性" nstdfk="funstdtype" ctlfk="fproductid" visible="1150"></th>

                <th lix="245" el="107" ek="fentry" id="fcustom" fn="fcustom" pn="fcustom" visible="1096" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fcustom" refvt="116"></th>
                <th lix="12" el="107" ek="fentry" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fispresetprop" refvt="116"></th>
                <th lix="300" el="107" ek="fentry" visible="1150" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" ctlfk="fproductid" dispfk="fselcategoryid" lock="-1"></th>
                <th lix="270" el="116" ek="fentry" visible="1150" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="fproductid" dispfk="funstdtype" width="90" copy="0" lock="0" refValueType="116"></th>

                <th lix="205" el="107" ek="fentry" id="fbrandid" fn="fbrandid" ts="" cn="品牌" ctlfk="fproductid" dispfk="fbrandid" visible="-1" sformid="" lock="-1"></th>
                <th lix="210" el="107" ek="fentry" id="fseriesid" fn="fseriesid" ts="" cn="系列" ctlfk="fproductid" dispfk="fseriesid" visible="-1" sformid="" lock="-1"></th>
                <th lix="211" el="107" ek="fentry" id="fsubseriesid" fn="fsubseriesid" cn="子系列" ctlfk="fproductid" dispfk="fsubseriesid"  visible="1086" sformid="" width="100" lock="-1"></th>
                <th lix="215" el="107" ek="fentry" id="fcategoryid" fn="fcategoryid" ctlfk="fproductid" dispfk="fcategoryid" ts="" cn="商品类别" visible="-1" sformid="" lock="-1"></th>
                <th lix="231" el="104" ek="fentry" id="fsalprice" fn="fsalprice" cn="统一零售价" width="100" visible="-1" format="0,000.00"></th>
                <th lix="225" el="109" ek="fentry" id="funitid" fn="funitid" refid="ydj_unit" visible="-1" cn="销售单位" ctlfk="fproductid" ts="" sformid="" width="100"></th>
                <th lix="230" el="103" ek="fentry" id="fqty" fn="fqty" pn="fqty" cn="销售数量" ctlfk="fbizunitid" format="0,000.00" basqtyfk="fqty" defval="1" width="80" visible="-1" apipn="salQty" canchange="true"></th>
                <th lix="235" el="112" ek="fentry" id="fstartdate" fn="fstartdate" cn="生效日期" defval="@currentshortdate" visible="-1"></th>
                <th lix="240" el="112" ek="fentry" id="fexpiredate" fn="fexpiredate" cn="失效日期" defVal="'2099-01-01'" visible="-1"></th>
                <th lix="245" el="152" ek="fentry" id="fconfirmstatus" fn="fconfirmstatus" cn="状态" vals="'1':'未确认','2':'确认'" visible="-1" defval="'1'" lock="-1" copy="0"></th>

                <th el="113" ek="fentry" id="flastchangedate" fn="flastchangedate" cn="最后调价日" visible="1086" lock="-1" lix="24"></th>
                <th el="104" ek="fentry" id="fdefinedprice" fn="fdefinedprice" cn="经销价" width="100" visible="0" desc="该字段已废弃" format="0,000.00" lix="17"></th>
                <!--<th el="104" ek="fentry" id="fsalprice" fn="fsalprice" cn="统一零售价" width="100" visible="1086" format="0,000.00" lix="50"></th>-->
                <!-- 临时屏蔽，等功能开发完再拿掉 -->


                <th el="113" ek="fentry" id="fconfirmdate" fn="fconfirmdate" cn="确认时间" visible="-1" desc="确认时更新为当前系统时间，取消确认时清空"></th>

                <th el="113" ek="fentry" id="fsyncdate" fn="fsyncdate" cn="同步时间" pn="fsyncdate" visible="-1" lock="-1" desc="记录同步时间"></th>

            </tr>
        </table>

        <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
        <div id="opList">
            <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
                <li el="11" id="save_valid_fname" cn="保存时辅助属性唯一" remove></li>
                <li el="11" vid="510" ek="fentry" cn="统一零售价必须大于0" precon="ftype!='quote_type_03'" data="{'expr':'fsalprice>0 ','message':'统一零售价必须大于0！'}"></li>
                <li el="11" vid="510" ek="fentry" cn="经销价必须大于或等于0" data="{'expr':'fdefinedprice>=0 ','message':'经销价必须大于或等于0！'}"></li>
                <li el="11" vid="3030" cn="价目表中存在总部商品，报价类型必须为【经销报价或二级分销报价】！" data=""></li>
            </ul>
            <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fname','fnumber','fproductid','fsalprice','fstartdate','fexpiredate']}" permid=""></ul>
            <ul el="10" id="confirm" op="confirm" opn="确认" data="" permid="ydj_price_confirm"></ul>
            <ul el="10" id="cancelconfirm" op="cancelconfirm" opn="取消确认" data="" permid="ydj_price_cancelconfirm"></ul>
        </div>

        <!--表单所涉及的权限项定义-->
        <div id="permList">
            <ul el="12" id="ydj_price_confirm" cn="确认"></ul>
            <ul el="12" id="ydj_price_cancelconfirm" cn="取消确认"></ul>
        </div>

    </div>
</body>
</html>