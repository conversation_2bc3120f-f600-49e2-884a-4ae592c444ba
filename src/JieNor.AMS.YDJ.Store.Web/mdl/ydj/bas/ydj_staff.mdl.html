<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_staff" el="3" basemodel="bd_basetmpl" cn="员工" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bd_staff" pn="fbillhead" cn="员工信息">
        <input group="基本信息" el="108" id="fnumber" width="70" />
        <input group="基本信息" el="100" id="fname" width="70" />
        <input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" visible="-1" cn="手机号" must="1" lix="4" width="85" notrace="false" />
        <input group="基本信息" el="100" type="text" id="fwechat" ek="fbillhead" fn="fwechat" ts="" cn="微信号" visible="-1" lix="8" width="110" />
        <input group="基本信息" el="100" type="text" id="femail" ek="fbillhead" fn="femail" ts="" visible="-1" cn="电子邮箱" lix="12" width="140" />
        <input group="基本信息" el="112" type="date" id="fentrydate" ek="fbillhead" fn="fentrydate" ts="" cn="入职日期" must="1" lix="16" visible="-1" />
        <input group="基本信息" el="100" type="text" id="faddress" ek="fbillhead" fn="faddress" ts="" cn="联系地址" must="1" visible="-1" width="160" lix="36" />
        <input group="基本信息" el="106" ek="FBillHead" id="flinkuserid" fn="flinkuserid" pn="flinkuserid" visible="-1" cn="关联账号"
               lock="0" copy="0" lix="38" ts="" refid="sec_user" filter="not exists(select 1 from t_bd_staff u1 where u1.flinkuserid=fid)" reflvt="0" dfld="" notrace="false" />

        <!--岗位与部门字段已取消，改用基础资料实现，并要支持一人多岗，改由表体承载-->
        <select group="基本信息" el="122" ek="fbillhead" visible="0" id="fposition" fn="fposition" pn="fposition" cn="岗位" cg="岗位" refid="bd_enum" dfld="fenumitem" lix="20" width="80"></select>
        <input group="基本信息" el="106" ek="fbillhead" visible="0" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="所属部门" refid="ydj_dept" lix="24" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fsex" fn="fsex" pn="fsex" cn="性别" cg="性别" refid="bd_enum" dfld="fenumitem" width="60" must="1" lix="28" align="center"></select>
        <input group="基本信息" el="112" type="date" id="fbirthday" ek="FBillHead" fn="fbirthday" ts="" cn="生日" must="1" visible="1150" lix="32" />
        <!-- 小程序添加主岗位 -->
        <input group="基本信息" el="106" ek="fbillhead" id="fmainpositionid" fn="fmainpositionid" pn="fmainpositionid" cn="主岗位" refid="ydj_position" visible="0" lix="24" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fehrcode" fn="fehrcode" pn="fehrcode" cn="EHR工号" lix="45" width="175"  copy="0"/>

        <input type="text" id="fprimitiveid" el="100" ek="fbillhead" remove />


        <input group="协同信息" el="152" ek="fbillhead" id="fmusisyncstatus" fn="fmusisyncstatus" pn="fmusisyncstatus" visible="1150" cn="慕思协同状态" copy="0" ts="" vals=" '01':'待协同','02':'协同成功','03':'协同失败'" defval="'01'" lix="8" />
        <input group="协同信息" el="113" type="text" id="fmusisyncdate" ek="fbillhead" fn="fmusisyncdate" pn="fmusisyncdate" ts="" visible="1150" cn="慕思协同日期" lix="6" copy="0" />
        <input el="116" ek="fbillhead" visible="-1" id="fismusiemployee" fn="fismusiemployee" pn="fismusiemployee" cn="是否慕思员工" defval="1" />
    </div>

    <table id="fentity" el="52" pk="fentryid" tn="t_bd_staffentry" pn="fentity" cn="任岗明细" kfks="fpositionid" must="1">
        <tr>
            <th el="106" ek="fentity" id="fpositionid" fn="fpositionid" pn="fpositionid" visible="1150" cn="岗位" width="120"
                lock="0" copy="1" lix="110" notrace="true" ts="" refid="ydj_position" sformid="" filter="" reflvt="0" dfld="" must="1" />
            <th el="106" ek="fentity" id="fdeptid_e" fn="fdeptid" pn="fdeptid" visible="1150" cn="部门" width="150"
                lock="0" copy="1" lix="115" notrace="true" ts="" refid="ydj_dept" sformid="" filter="" reflvt="0" dfld="" must="1" />
            <!--<th el="107" ek="fentity" id="fdeptleader" fn="fdeptleader" pn="fdeptleader" visible="1150" cn="当前负责人"
                lock="-1" copy="0" lix="116" notrace="true" ts="" ctlfk="fdeptid_e" dispfk="fleaderid" refvt="0"></th>-->
            <th el="152" ek="fentity" id="fbiztype" fn="fbiztype" pn="fbiztype" visible="1150" cn="业务类别" width="100"
                lock="0" copy="1" lix="120" notrace="true" ts="" vals="0:'通用',1:'销售',2:'采购', 4:'库存', 5:'设计', 6:'服务',7:'售后'" defval="'0'" must="1"></th>
            <th el="116" ek="fentity" id="fisleader" fn="fisleader" pn="fisleader" visible="1150" cn="负责人"
                lock="0" copy="0" lix="125" notrace="true" ts=""></th>
            <th el="100" ek="fentity" id="fnote" fn="fnote" pn="fnote" visible="1150" cn="备注" width="300"
                lock="0" copy="1" lix="150" notrace="true" ts="" len="500" />
            <!-- 小程序添加主岗位 -->
            <th el="116" ek="fentity" id="fismain" fn="fismain" pn="fismain" visible="1150" cn="主岗位"
                lock="0" copy="1" lix="113" notrace="true" ts="" defval="false"></th>
        </tr>
    </table>

    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">

            <!--去掉名称唯一性校验-->
            <li el="11" id="save_valid_fname" cn="保存时名称唯一" remove></li>

            <li el="11" vid="510" cn="入职日期必须小于当前日期" data="{'expr':'fentrydate<=@currentshortdate','message':'入职日期必须小于当前日期！'}"></li>
            <li el="11" id="save_biz_unique" cn="保存时员工代码+岗位+部门需要唯一" vid="500" data="fmainorgid,fnumber,fpositionid,fdeptid" precon=""></li>
            <!--<li el="11" id="save_leader_unique" cn="保存时部门+负责人需要唯一" vid="500" data="fmainorgid,fdeptid,fisleader" precon=""></li>-->
        </ul>

        <ul el="10" id="createuserlink" op="createuserlink" opn="关联用户" data="" permid="ydj_createuserlink"></ul>
        <ul el="10" id="generateuser" op="generateuser" opn="生成用户" data="" permid="ydj_generateuser"></ul>
    </div>
    <div id="permList">
        <ul el="12" id="ydj_createuserlink" cn="关联用户"></ul>
        <ul el="12" id="ydj_generateuser" cn="生成用户"></ul>
    </div>
</body>
</html>