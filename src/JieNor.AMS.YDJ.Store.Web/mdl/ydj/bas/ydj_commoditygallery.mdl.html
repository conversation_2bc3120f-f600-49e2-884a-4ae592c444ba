<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_commoditygallery" basemodel="bd_basetmpl" el="3" cn="商品图库" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="T_Ydj_Commoditygallery" pn="fbillhead" cn="商品图库">
        <!--重写基类模型中的部分字段属性-->
        <input id="fnumber" el="100" visible="0" lock="-1" />
        <input id="fname" el="100" visible="0" lock="-1" />
        <input id="fdescription" el="100" visible="0" lock="-1" />
        <select id="fstatus" el="122" visible="0" lock="-1" ></select>


        <input group="基本信息" el="106" ek="fbillhead" id="fproductid" fn="fproductid" refid="ydj_product" visible="-1" cn="商品" lix="3" width="200" />
        <input group="基本信息" el="107" ek="fbillhead" id="fproductbillno" fn="fproductbillno" pn="fproductbillno" visible="-1" cn="商品编码"
            lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fnumber" refvt="0" />
        <input group="基本信息" el="107" ek="fbillhead" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号" lock="-1" notrace="true" ts="" ctlfk="fproductid" dispfk="fspecifica" />
        <input group="基本信息" el="132" ek="fbillhead" id="fattrinfo" fn="fattrinfo" cn="辅助属性" ctlfk="fproductid" visible="-1" lix="4" width="200"/>
        <input group="基本信息" el="135" type="text" id="fimage" ek="fbillhead" fn="fimage" pn="fimage" cn="商品主图" visible="-1" lix="5" must="1" />
        <input group="基本信息" el="100" ek="fbillhead" len="2000" id="fcustomdesc" fn="fcustomdesc" cn="定制说明" lix="90" width="160" visible="1150"/>
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="500" cn="保存时商品和辅助属性和定制说明组合唯一" data="fmainorgid,fproductid,fattrinfo,fcustomdesc" precon=""></li>
            <li el="11" vid="510" cn="商品不能为空" data="{'expr':'fproductid!=\'\' and fproductid!=\' \'','message':'商品不能为空！'}"></li>
            <li el="11" vid="510" cn="商品主图不能为空" data="{'expr':'fimage!=\'\' and fimage!=\' \'','message':'商品主图不能为空！'}"></li>
        </ul>
    </div>

</body>
</html>