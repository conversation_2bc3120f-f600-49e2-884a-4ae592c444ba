<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_customerleveleditor" el="0" cn="会员级别" rac="true">
    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="会员级别">

        <table id="fentity" el="52" pk="fentryid" pn="fentity" cn="会员级别明细" kfks="fname,fcondition">
            <tr>
                <th el="100" ek="fentity" id="fname" pn="fname" visible="1150" cn="等级名称"
                    lock="0" copy="0" lix="300" ts="" width="300"></th>
                <th el="105" ek="fentity" id="fcondition" pn="fcondition" visible="1150" cn="晋级条件（累积金额）"
                    lock="0" copy="0" lix="310" ts="" width="500"></th>
                <th el="100" ek="fentity" id="fcustomerlevelid" pn="fname" visible="0" cn="会员等级id"
                    lock="-1" copy="0" lix="300" ts=""></th>
            </tr>
        </table>

        <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
        <div id="opList">
            <ul el="10" ek="fbillhead" id="new" op="new" opn="新增" data="" permid="fw_new"></ul>            
        </div>
        <div id="permList">
            <ul el="12" id="fw_new" cn="新增" order="1"></ul>
        </div>

    </div>
</body>
</html>