<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="bas_promotioncombine" basemodel="bd_basetmpl" el="3" cn="组合促销套餐" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_bas_promotioncombine" pn="fbillhead" cn="组合促销套餐">

        <input el="106" ek="fbillhead" visible="-1" id="fpromotionid" fn="fpromotionid" pn="fpromotionid" cn="促销活动名称" dfld="fnumber" refid="bas_promotionscheme" width="200" lix="5" lock="-1" />
        <input group="基本信息" ek="fbillhead" id="fpromotionnumber" fn="fpromotionnumber" pn="fpromotionnumber" el="107" cn="促销活动编号" visible="-1" width="200" lix="10" ctlfk="fpromotionid" dispfk="fnumber" lock="-1" />
        <input id="fnumber" el="100" cn="组合促销编号" lock="-1" visible="-1" width="140" lix="20" />
        <input id="fname" el="100" cn="组合促销名称" visible="1150" width="200" lix="30" lock="-1" />


        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="ftype" fn="ftype" pn="ftype" cn="促销类型" lix="40" width="70" lock="-1" />

        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="fbegindate" fn="fbegindate" ts="" cn="促销开始日期" defval="@currentshortdate" width="90" lix="50" lock="-1" />
        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="fenddate" fn="fenddate" ts="" cn="促销结束日期" defval="@currentshortdate" width="90" lix="60" lock="-1" />

        <input group="基本信息" id="fpromotiondescription" el="100" cn="活动规则说明" visible="-1" lix="70" lock="-1" />

        <input id="fdescription" el="100" cn="组合描述" visible="-1" width="140" lix="80" lock="0" />
        <input id="fcombinerate" fn="fcombinerate" n="fcombinerate" el="100" cn="组合促销折扣率%" visible="0" width="200" lix="20" lock="-1" />
        <input id="fcombinepriority" fn="fcombinepriority" n="fcombinepriority" el="100" cn="促销套餐优先级" visible="-1" width="200" lix="20" lock="-1" />

        <input group="基本信息" el="116" ek="fbillhead" id="fisall" fn="fisall" pn="fisall" visible="-1" cn="是否所有送达方通用" copy="1" lix="0" notrace="false" ts="" defval="false" lock="-1" />

        <table id="fcombineentry" el="52" pk="fcombineentryid" tn="t_ydj_fcombineentry" pn="fcombineentry" cn="组合套餐" kfks="fproductid">
            <tr>

                <th lix="10" el="106" ek="fcombineentry" id="fproductid" fn="fproductid" pn="fproductid" multsel="true" cn="商品名称" dfld="fnumber" refid="ydj_product" width="300" visible="1150" lock="-1"></th>
                <th lix="20" el="107" ek="fcombineentry" id="fproductnumber" fn="fproductnumber" pn="fproductnumber" visible="1150" cn="商品编码"
                    lock="0" copy="1" notrace="true" ts="" ctlfk="fproductid" dispfk="fnumber"></th>
                <th lix="30" ek="fcombineentry" el="109" id="funitid" fn="funitid" pn="funitid" cn="单位" refid="ydj_unit" sformid="" ctlfk="fproductid" width="80" visible="1150" copy="0" lock="-1"></th>
                <th lix="80" ek="fcombineentry" el="100" id="fgroupnumber" fn="fgroupnumber" pn="fgroupnumber" cn="子组号" width="80" visible="1150" copy="0" lock="-1"></th>
                <th lix="90" ek="fcombineentry" el="100" id="fgroupdesc" fn="fgroupdesc" pn="fgroupdesc" cn="子组描述" width="80" visible="1150" copy="0" lock="-1"></th>

                <th lix="40" ek="fcombineentry" el="103" id="fbaseqty" fn="fbaseqty" pn="fbaseqty" cn="套餐组合基数" format="0,000.00" width="80" visible="1150" lock="-1"></th>
                <th lix="40" ek="fcombineentry" el="103" id="fqty" fn="fqty" pn="fqty" cn="采购数量" format="0,000.00" width="80" visible="0" lock="-1"></th>
                <th lix="50" el="104" ek="fcombineentry" id="fdistrate" fn="fdistrate" pn="fdistrate" cn="折扣%" width="60" visible="1150" format="0,000.00" lock="-1"></th>
                <th lix="60" el="100" ek="fcombineentry" id="fcdescription_e" fn="fcdescription" pn="fcdescription_e" cn="备注说明" width="170" len="4000" visible="1150" lock="-1"></th>
                <th lix="60" el="100" ek="fcombineentry" id="fcolor" fn="fcolor" pn="fcolor" cn="颜色" width="170" len="4000" visible="0" lock="-1"></th>
                <th lix="60" el="100" ek="fcombineentry" id="fsize" fn="fsize" pn="fsize" cn="尺寸" width="170" len="4000" visible="0" lock="-1"></th>
                <th lix="100" ek="fcombineentry" el="112" id="fproductbegindate" fn="fproductbegindate" pn="fproductbegindate" cn="有效开始时间" width="80" visible="1150" copy="0" lock="-1"></th>
                <th lix="110" ek="fcombineentry" el="112" id="fproductenddate" fn="fproductenddate" pn="fproductenddate" cn="有效截至时间" width="80" visible="1150" copy="0" lock="-1"></th>
            </tr>
        </table>

        <table id="frangeentry" el="52" pk="frangeentryid" tn="t_ydj_fcombinerangeentry" pn="frangeentry" cn="活动范围" kfks="fsaleorgid,fresultbrandid">
            <tr>
                <th el="106" ek="frangeentry" id="fsaleorgid" fn="fsaleorgid" pn="fsaleorgid" cn="销售组织名称" refid="bas_organization" filter="forgtype ='2'" sformid="" width="300" visible="1150" lock="-1"></th>
                <th el="107" ek="frangeentry" id="fsaleorgnumber" fn="fsaleorgnumber" pn="fsaleorgnumber" visible="1150" cn="销售组织编码"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fsaleorgid" dispfk="fnumber" refvt="0" frozen="1" lix="200"></th>

                <th el="100" ek="frangeentry" id="fcombinecode" fn="fcombinecode" pn="fcombinecode" visible="1150" cn="活动套餐明细编码" lock="-1" refvt="0" frozen="1" lix="200"></th>

                <th el="106" ek="frangeentry" id="fresultbrandid" fn="fresultbrandid" pn="fresultbrandid" cn="业绩品牌名称" refid="ydj_series" sformid="" filter="fisresultbrand='1'" width="300" visible="1150" lock="-1"></th>
                <th el="107" ek="frangeentry" id="fresultbrandnumber" fn="fresultbrandnumber" pn="fresultbrandnumber" visible="1150" cn="业绩品牌编码"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fresultbrandid" dispfk="fnumber" refvt="0" frozen="1" lix="200"></th>
                <th el="152" ek="frangeentry" id="fchannel" fn="fchannel" pn="fchannel" cn="分销渠道" width="170" vals="'0':'经销','1':'直营'" defval="'0'" visible="1150" lock="-1"></th>
                <th el="100" ek="frangeentry" id="fcdescription" fn="fcdescription" pn="fcdescription" cn="备注" width="170" len="4000" visible="1150" lock="-1"></th>
                <th el="106" ek="frangeentry" id="fdeliverid" fn="fdeliverid" pn="fdeliverid" cn="送达方" refid="bas_deliver" width="170" len="4000" visible="-1" lock="-1"></th>
                <th el="107" ek="frangeentry" id="fdelivername" fn="fdelivername" pn="fdelivername" visible="-1" cn="送达方"
                    lock="-1" ctlfk="fdeliverid" dispfk="fname" lix="200"></th>
                <th el="116" ek="frangeentry" visible="0" id="fdeleteflag" fn="fdeleteflag" pn="fdeleteflag" cn="是否启用" lock="-1" lix="52"></th>
                <th el="106" ek="frangeentry" id="fagentid" fn="fagentid" pn="fagentid" cn="经销商名称" refid="bas_agent" sformid="" width="300" visible="-1" lock="-1"></th>
                <th el="107" ek="frangeentry" id="fagentnumber" fn="fagentnumber" pn="fagentnumber" visible="-1" cn="经销商编码"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fagentid" dispfk="fnumber" frozen="1" lix="200"></th>
            </tr>
        </table>
    </div>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" clear></li>
            <li el="11" vid="510" ek="fcombineentry" cn="数量必须大于0" data="{'expr':'fbaseqty>0','message':'数量必须大于0！'}"></li>
        </ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">

    </div>
</body>
</html>