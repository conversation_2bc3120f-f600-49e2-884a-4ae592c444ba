//{
//  "Id": "ste_afterfeedback2aft_manage",
//  "Number": "ste_afterfeedback2aft_manage",
//  "Name": "售后处理",
//  "SourceFormId": "ste_afterfeedback",
//  "TargetFormId": "aft_manage",
//  "ActiveEntityKey": "fbillhead",
//  // "FilterString": "fsourcenumber=''",
//  "FieldMappings": [
//    {
//      "Id": "fsourcetype",
//      "Name": "源单类型",
//      "MapType": 1,
//      "SrcFieldId": "'ste_afterfeedback'",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourcenumber",
//      "Name": "源单编号",
//      "MapType": 0,
//      "SrcFieldId": "fbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "forderno",
//      "Name": "合同编号",
//      "MapType": 0,
//      "SrcFieldId": "forderno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
    
//    {
//      "Id": "fcustomerid",
//      "Name": "客户",
//      "MapType": 0,
//      "SrcFieldId": "fcustomerid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fcontacts",
//      "Name": "联系人",
//      "MapType": 0,
//      "SrcFieldId": "flinkstaffid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fphone",
//      "Name": "联系电话",
//      "MapType": 0,
//      "SrcFieldId": "flinkmobile",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "faddress",
//      "Name": "联系地址",
//      "MapType": 0,
//      "SrcFieldId": "flinkaddress",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "faftqustiontype",
//      "Name": "问题类别",
//      "MapType": 0,
//      "SrcFieldId": "fquestiontype",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fdescribe",
//      "Name": "问题描述",
//      "MapType": 0,
//      "SrcFieldId": "fquestiondesc",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fquestionimage",
//      "Name": "问题图片",
//      "MapType": 0,
//      "SrcFieldId": "fquestionimage",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fscheme",
//      "Name": "处理方案",
//      "MapType": 0,
//      "SrcFieldId": "fprocessplan",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    }
//  ],
//  "BillGroups": [
//    {
//      "Id": "fsourcenumber",
//      "Order": 1
//    }
//  ],
//  "FieldGroups": [

//  ]
//}
