//{
//  "Id": "ydj_merchantorder2ydj_service",
//  "Number": "ydj_merchantorder2ydj_service",
//  "Name": "商户订单转服务单",
//  "SourceFormId": "ydj_merchantorder",
//  "TargetFormId": "ydj_service",
//  "ActiveEntityKey": "fserviceentry",
//  "FieldMappings": [
//    {
//      "Id": "fsourceformid",
//      "Name": "来源单据",
//      "MapType": 1,
//      "SrcFieldId": "'ydj_merchantorder'",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourcebillnum",
//      "Name": "源单编号",
//      "MapType": 0,
//      "SrcFieldId": "fbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fservicetype",
//      "Name": "订单类型",
//      "MapType": 0,
//      "SrcFieldId": "fservicetype",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "forderdate",
//      "Name": "下单日期",
//      "MapType": 0,
//      "SrcFieldId": "forderdate",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fservicedate",
//      "Name": "服务时间",
//      "MapType": 0,
//      "SrcFieldId": "finstalldate",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fname",
//      "Name": "客户名称",
//      "MapType": 0,
//      "SrcFieldId": "fname",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fphone",
//      "Name": "客户电话",
//      "MapType": 0,
//      "SrcFieldId": "fphone",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "faddress",
//      "Name": "目的地",
//      "MapType": 0,
//      "SrcFieldId": "fcusaddress",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fprovince",
//      "Name": "省",
//      "MapType": 0,
//      "SrcFieldId": "fprovince",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fcity",
//      "Name": "市",
//      "MapType": 0,
//      "SrcFieldId": "fcity",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fregion",
//      "Name": "区",
//      "MapType": 0,
//      "SrcFieldId": "fregion",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fseritemid",
//      "Name": "服务项目",
//      "MapType": 0,
//      "SrcFieldId": "fseritemid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "funitid",
//      "Name": "单位",
//      "MapType": 0,
//      "SrcFieldId": "funitid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fqty",
//      "Name": "数量",
//      "MapType": 0,
//      "SrcFieldId": "fqty",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourceformid",
//      "Name": "来源单类型",
//      "MapType": 1,
//      "SrcFieldId": "'ydj_merchantorder'",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourcebillno",
//      "Name": "来源单编号",
//      "MapType": 0,
//      "SrcFieldId": "fbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourceinterid",
//      "Name": "来源单内码",
//      "MapType": 0,
//      "SrcFieldId": "fbillhead.id",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourceinterid_h",
//      "Name": "来源单内码",
//      "MapType": 0,
//      "SrcFieldId": "fbillhead.id",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fsourceentryid",
//      "Name": "来源单分录内码",
//      "MapType": 0,
//      "SrcFieldId": "fserviceentry.id",
//      "MapActionWhenGrouping": 0, 
//      "Order": 0
//    },
//    {
//      "Id": "frequire",
//      "Name": "要求",
//      "MapType": 0,
//      "SrcFieldId": "frequire",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fproimage",
//      "Name": "商品图片",
//      "MapType": 0,
//      "SrcFieldId": "fimage",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fattention",
//      "Name": "注意事项",
//      "MapType": 0,
//      "SrcFieldId": "fcareful",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fdealerid",
//      "Name": "商户",
//      "MapType": 0,
//      "SrcFieldId": "fmerchantid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fbrandid",
//      "Name": "品牌",
//      "MapType": 0,
//      "SrcFieldId": "fbrandid",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    },
//    {
//      "Id": "fdealernumber",
//      "Name": "商户单号",
//      "MapType": 0,
//      "SrcFieldId": "forderbillno",
//      "MapActionWhenGrouping": 0,
//      "Order": 0
//    }
//  ],
//  "BillGroups": [
//    {
//      "Id": "fbillno",
//      "Order": 1
//    }
//  ],
//  "FieldGroups": [
//    {
//      "Id": "fserviceentry_fentryid",
//      "Order": 1
//    }
//  ]
//}
