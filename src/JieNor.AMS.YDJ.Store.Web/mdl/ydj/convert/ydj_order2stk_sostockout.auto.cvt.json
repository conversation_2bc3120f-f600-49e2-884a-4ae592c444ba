{
  "Id": "ydj_order2stk_sostockout.auto",
  "Number": "ydj_order2stk_sostockout.auto",
  "Name": "销售合同下推销售出库单",
  "SourceFormId": "ydj_order",
  "TargetFormId": "stk_sostockout",
  "ActiveEntityKey": "fentry",
  "relationFieldKey": "fsoorderentryid",
  "SourceControlFieldKey": "foutqty",
  "FilterString": "fqty+freturnqty>foutqty and fstatus='E' and fisoutspot='1' and fdeliverymode='1' and (fclosestatus_e='0' or fclosestatus_e='2')",
  "Message": "至少要有一行商品明细是出现货且是立即提货！", //"出库失败：<br>1、销售合同必须是已审核状态！<br>2、至少要有一行商品明细没有完全出库(数量-出库数量+退货数量>0)！<br>3、至少要有一行商品明细是出现货且是立即提货！<br>4、至少有一行商品明细的关闭状态是正常或部分关闭状态！",
  //"FilterString": "fqty+freturnqty>foutqty and fstatus='E' and fisoutspot='1' and fdeliverymode='1' and (fclosestatus_e='0' or fclosestatus_e='2') and fshipperagent=@currentorgid",
  //"Message": "出库失败：<br>1、销售合同必须是已审核状态！<br>2、至少要有一行商品明细没有完全出库(数量-出库数量+退货数量>0)！<br>3、至少要有一行商品明细是出现货且是立即提货！<br>4、至少有一行商品明细的关闭状态是正常或部分关闭状态！<br>5、商品明细中不存在发货经销商等于当前销售合同的经销商数据！",
  "Visible": false,
  "FieldMappings": [
    //表头字段
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fconsignee",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fcustomercontactid",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fphone",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "faddress",
      "Name": "收货地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 5
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "flogisticsitems",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fisresellorder",
      "Name": "二级分销合同",
      "MapType": 0,
      "SrcFieldId": "fisresellorder",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fwithin",
      "Name": "手工单号",
      "MapType": 0,
      "SrcFieldId": "fwithin",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //出库明细字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fqty",
      "Name": "基本单位实发数量",
      "MapType": 1,
      "SrcFieldId": "fqty-foutqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应发数量",
      "MapType": 1,
      "SrcFieldId": "fqty-foutqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 19,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 1,
      "SrcFieldId": "fproductid.fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 21
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 23,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-foutqty+freturnqty)*fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 24,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fstorelocationid",
      "Name": "仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fmtono",
      "Name": "订单跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 1000
    },
    {
      "Id": "fsoorderno",
      "Name": "销售订单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 25
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售订单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 26
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 27
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderdate",
      "Name": "订单日期",
      "MapType": 0,
      "SrcFieldId": "forderdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "基本单位订单数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "ftranid",
      "Name": "流水号",
      "MapType": 0,
      "SrcFieldId": "ftranid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentity_ftranid",
      "Name": "明细流水号",
      "MapType": 0,
      "SrcFieldId": "fentry_ftranid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsostaffid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsodeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice_e",
      "Name": "零售价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fhqprice",
      "Name": "总部零售价",
      "MapType": 0,
      "SrcFieldId": "fhqprice",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fterprice",
      "Name": "终端零售价",
      "MapType": 0,
      "SrcFieldId": "fterprice",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fteramount",
      "Name": "终端金额",
      "MapType": 0,
      "SrcFieldId": "fteramount",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fisgiveaway",
      "Name": "赠品",
      "MapType": 0,
      "SrcFieldId": "fisgiveaway",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpurfacprice",
      "Name": "采购单价折前",
      "MapType": 0,
      "SrcFieldId": "fpurfacprice",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "fpurfacamount",
      "Name": "采购折前金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-foutqty+freturnqty)*fpurfacprice",
      "MapActionWhenGrouping": 0,
      "Order": 0,
      "IgnoreChangeValidation": true
    },
    {
      "Id": "frenewalflag",
      "Name": "焕新订单标记",
      "MapType": 0,
      "SrcFieldId": "frenewalflag",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpiecesendtag",
      "Name": "一件代发标记",
      "MapType": 0,
      "SrcFieldId": "fpiecesendtag",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverytype",
      "Name": "交货方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverytype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmanagemodel",
      "Name": "经营模式",
      "MapType": 0,
      "SrcFieldId": "fmanagemodel",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    },
    {
      "Id": "fstorehouseid",
      "Order": 2
    }
  ],
  "FieldGroups": [

  ]
}