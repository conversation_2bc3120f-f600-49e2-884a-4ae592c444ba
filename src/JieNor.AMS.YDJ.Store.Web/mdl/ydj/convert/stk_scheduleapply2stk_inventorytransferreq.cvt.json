{
  "Id": "stk_scheduleapply2stk_inventorytransferreq",
  "Number": "stk_scheduleapply2stk_inventorytransferreq",
  "Name": "排单申请单转库存调拨通知单",
  "SourceFormId": "stk_scheduleapply",
  "TargetFormId": "stk_inventorytransferreq",
  "ActiveEntityKey": "fentity",
  "relationFieldKey": "fsourceentryid",
  "FilterString": "fqty>fscheduleqty and fstatus='E'",
  "Message": "排单失败：<br>1、排单申请单必须是已审核状态！<br>2、至少要有一行商品明细没有完全排单(实排数量-已排数量>0)！",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'invtransferreq_billtype_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransfertype",
      "Name": "调拨类型",
      "MapType": 1,
      "SrcFieldId": "'invtransfer_biztype_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransferdirection",
      "Name": "调拨方向",
      "MapType": 1,
      "SrcFieldId": "'0'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgtypeidto",
      "Name": "收货单位类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_customer'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "调拨日期",
      "MapType": 1,
      "SrcFieldId": "fstockdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffidto",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptidto",
      "Name": "收货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgidto",
      "Name": "收货单位",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgresponsorto",
      "Name": "收货单位联系人",
      "MapType": 1,
      "SrcFieldId": "fcustomerid.fcontacts",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkmobile",
      "Name": "收货人电话",
      "MapType": 1,
      "SrcFieldId": "fstockdeptidto.fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid",
      "Name": "收货人",
      "MapType": 1,
      "SrcFieldId": "fstockdeptidto.fname",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockaddress",
      "Name": "收货人地址",
      "MapType": 0,
      "SrcFieldId": "fstockaddressto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "发货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffphone",
      "Name": "发货人电话",
      "MapType": 1,
      "SrcFieldId": "fstockstaffid.fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "发货申请部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockaddress",
      "Name": "发货人地址",
      "MapType": 0,
      "SrcFieldId": "fstockaddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "flinkaddress",
      "Name": "收货人地址",
      "MapType": 0,
      "SrcFieldId": "fstockaddressto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    //明细字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "调出辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfoto",
      "Name": "调入辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfoto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "调出定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcallupcustomdescto",
      "Name": "调入定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdescto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "调拨单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "申请数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanqty",
      "Name": "申请调拨数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 0,
      "SrcFieldId": "famount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "调出仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "调出仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "调出状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseidto",
      "Name": "调入仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationidto",
      "Name": "调入仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatusto",
      "Name": "调入状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatusto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "调出物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtonoto",
      "Name": "调入物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtonoto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fentrynote",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanbackdate",
      "Name": "预计回货日期",
      "MapType": 0,
      "SrcFieldId": "fplanbackdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_scheduleapply'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_scheduleapply'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [

  ]
}