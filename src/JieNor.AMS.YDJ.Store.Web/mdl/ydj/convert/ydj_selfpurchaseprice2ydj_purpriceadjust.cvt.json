{"Id": "ydj_selfpurchaseprice2ydj_purpriceadjust", "Number": "ydj_selfpurchaseprice2ydj_purpriceadjust", "Name": "采购价目表生成改价单", "SourceFormId": "ydj_selfpurchaseprice", "TargetFormId": "ydj_purpriceadjust", "ActiveEntityKey": "fentry", "FilterString": "", "Message": "", "FieldMappings": [{"Id": "fadjustmode", "Name": "调价方式", "MapType": 1, "SrcFieldId": "'price_adjust_01'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fadjustdate", "Name": "调价日期", "MapType": 1, "SrcFieldId": "@currentDate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fstaffid", "Name": "调价人", "MapType": 1, "SrcFieldId": "@currentStaffId", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fdeptid", "Name": "调价部门", "MapType": 1, "SrcFieldId": "@currentDeptId", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpurprice_percent", "Name": "采购价(调整百分百)", "MapType": 1, "SrcFieldId": "'100'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpurprice_param", "Name": "调后采购价(选择对象)", "MapType": 1, "SrcFieldId": "'purprice_01'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsupplierid", "Name": "供应商", "MapType": 0, "SrcFieldId": "fsupplierid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fid_e", "Name": "价目ID", "MapType": 0, "SrcFieldId": "fentry.id", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fproductid", "Name": "商品", "MapType": 0, "SrcFieldId": "fproductid_e", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "funitid", "Name": "单位", "MapType": 0, "SrcFieldId": "funitid_e", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fattrinfo", "Name": "辅助属性", "MapType": 0, "SrcFieldId": "fattrinfo", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fpurprice", "Name": "采购价", "MapType": 0, "SrcFieldId": "fpurprice", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fprice", "Name": "含税单价", "MapType": 0, "SrcFieldId": "fprice", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "ftaxrate", "Name": "税率", "MapType": 0, "SrcFieldId": "ftaxrate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fstartdate_e", "Name": "生效日期", "MapType": 0, "SrcFieldId": "fstartdate_e", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fexpiredate_e", "Name": "失效日期", "MapType": 0, "SrcFieldId": "fexpiredate_e", "MapActionWhenGrouping": 0, "Order": 0}], "BillGroups": [{"Id": "f<PERSON><PERSON>", "Order": 1}], "FieldGroups": [{"Id": "fentity_fentryid", "Order": 1}]}