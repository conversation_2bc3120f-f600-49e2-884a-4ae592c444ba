/*
  销售合同下推售后维修单
  Author:zpf
  CreatedTime:2021/02/15 15pm
  下推条件：
  1、单据状态	等于	已审核      并且   fstatus
  2、作废状态	等于	未作废      并且   fcancelstatus
  3、关闭状态	等于	未关闭      并且   fclosestate
  4、锁定状态 等于  未锁定      并且   flockstate
  5、流程状态	等于	已出库             flinkpro
*/

{
  "Id": "ydj_order2aft_repairorder",
  "Number": "ydj_order2aft_repairorder",
  "Name": "销售合同下推售后维修单",
  "SourceFormId": "ydj_order",
  "TargetFormId": "aft_repairorder",
  "ActiveEntityKey": "fentry",
  "FilterString": " fstatus='E' and fcancelstatus='0' and fclosestate='0' and flockstate='0' and flinkpro='已出库' ",
  "Message": "【单据状态】=已审核，作废状态=未作废，关闭状态=未关闭，锁定状态=未锁定，商品明细流程状态等于已出库",
  "FieldMappings": [
    //表头字段映射
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    { //业务日期==默认下推日期
      "Id": "fbilldate",
      "Name": "业务日期",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    { //客户
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fcontacts",
      "Name": "联系人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkmobile",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkaddress",
      "Name": "联系地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstaff",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdept",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //明细字段映射
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fproductid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdes_e",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbrandid",
      "Name": "品牌",
      "MapType": 0,
      "SrcFieldId": "fbrandid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizqty",
      "Name": "维修数量",
      "MapType": 0,
      "SrcFieldId": "fbizqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flotno",
      "Name": "批号",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fnote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentry_fentryid",
      "Order": 1
    }
  ]
}
