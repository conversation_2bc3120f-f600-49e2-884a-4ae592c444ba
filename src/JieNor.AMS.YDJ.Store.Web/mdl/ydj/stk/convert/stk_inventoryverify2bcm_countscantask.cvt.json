{
  "Id": "stk_inventoryverify2bcm_countscantask",
  "Number": "stk_inventoryverify2bcm_countscantask",
  "Name": "�̵㵥�����̵�ɨ������",
  "SourceFormId": "stk_inventoryverify",
  "TargetFormId": "bcm_countscantask",
  "ActiveEntityKey": "fentity",
  "FilterString": "fstatus='D' and fcancelstatus='0'",
  "Message": "�����̵�����ʧ�ܣ�<br>1���̵���������ύ��δ����״̬��",
  "FieldMappings": [
    {
      "Id": "ftask_type",
      "Name": "��������",
      "MapType": 1,
      "SrcFieldId": "'stk_inventoryverify'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftaskstatus",
      "Name": "����״̬",
      "MapType": 1,
      "SrcFieldId": "'ftaskstatus_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftaskdate",
      "Name": "ҵ������",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fexistednetherbill",
      "Name": "�Ѵ������ο�浥��",
      "MapType": 1,
      "SrcFieldId": "'1'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //{
    //  "Id": "fsourcetype",
    //  "Name": "��Դ����",
    //  "MapType": 1,
    //  "SrcFieldId": "'stk_postockreturn'",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    {
      "Id": "fsourcenumber",
      "Name": "��Դ���ݱ��",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //������Ʒ��ϸ�ֶ�ӳ��
    {
      "Id": "fmaterialid",
      "Name": "��Ʒ",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "����˵��",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "��������",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "��λ",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "�ֿ�",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "��λ",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fwaitworkqty",
      "Name": "����ҵ����",
      "MapType": 0,
      "SrcFieldId": "fstockqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flotno",
      "Name": "����",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "�������ٺ�",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "��������",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "����",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "������λ",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "������λ����",
      "MapType": 0,
      "SrcFieldId": "forderqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkformid",
      "Name": "��������",
      "MapType": 1,
      "SrcFieldId": "'stk_inventoryverify'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkbillno",
      "Name": "�������ݱ��",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkrowinterid",
      "Name": "������������",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "��Դ����",
      "MapType": 1,
      "SrcFieldId": "'stk_inventoryverify'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "��Դ���ݱ��",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "��Դ��������",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcefid",
      "Name": "��Դ������",
      "MapType": 0,
      "SrcFieldId": "fsourceinterid_h",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fvolumeunit",
      "Name": "�����λ",
      "MapType": 0,
      "SrcFieldId": "fvolumeunit",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalvolume",
      "Name": "�����",
      "MapType": 0,
      "SrcFieldId": "ftotalvolume",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsinglevolume",
      "Name": "��λ���",
      "MapType": 0,
      "SrcFieldId": "fsinglevolume",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}