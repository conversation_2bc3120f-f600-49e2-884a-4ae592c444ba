{
  "Id": "stk_inventorytransferreq2stk_inventorytransfer",
  "Number": "stk_inventorytransferreq2stk_inventorytransfer",
  "Name": "库存调拨通知生成库存调拨",
  "SourceFormId": "stk_inventorytransferreq",
  "TargetFormId": "stk_inventorytransfer",
  "ControlFieldKey": "fqty",
  "SourceControlFieldKey": "ftransferqty",
  "RelationFieldKey": "fsourceentryid",
  "RealtionFormIdFieldKey": "fsourceformid",
  "ActiveEntityKey": "fentity",
  "FilterString": "fqty-ftransferqty>0 and fstatus='E'",
  "Message": "库存调拨失败：<br>1、库存调拨通知单必须已审核！<br>2、基本单位实际调拨数量必须大于0！<br>3、可能已生成了下游未审核的调拨单！",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'invtransfer_billtype_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransfertype",
      "Name": "调拨类型",
      "MapType": 0,
      "SrcFieldId": "ftransfertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftransferdirection",
      "Name": "调拨方向",
      "MapType": 0,
      "SrcFieldId": "ftransferdirection",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate",
      "Name": "调拨日期",
      "MapType": 1,
      "SrcFieldId": "@currentDate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffid",
      "Name": "发货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptid",
      "Name": "发货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffidto",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockdeptidto",
      "Name": "收货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgtypeidto",
      "Name": "收货单位类型",
      "MapType": 0,
      "SrcFieldId": "forgtypeidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgidto",
      "Name": "收货单位",
      "MapType": 0,
      "SrcFieldId": "forgidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forgresponsorto",
      "Name": "收货单位联系人",
      "MapType": 0,
      "SrcFieldId": "forgresponsorto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeliverywayid",
      "Name": "货运方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverywayid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_inventorytransferreq'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //表体商品明细字段映射
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "调入定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcallupcustomdescto",
      "Name": "调出定制说明",
      "MapType": 0,
      "SrcFieldId": "fcallupcustomdescto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位库存数量",
      "MapType": 0,
      "SrcFieldId": "fplanqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "基本单位调拨数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 0,
      "SrcFieldId": "famount",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "调出仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationid",
      "Name": "调出仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatus",
      "Name": "调出库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fstorehouseidto",
      "Name": "调入仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorelocationidto",
      "Name": "调入仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationidto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstatusto",
      "Name": "调入库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatusto",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "flotno",
      "Name": "批号",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "调出物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtonoto",
      "Name": "调入物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtonoto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "调出货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "调出货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertypeto",
      "Name": "调入货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertypeto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fowneridto",
      "Name": "调入货主",
      "MapType": 0,
      "SrcFieldId": "fowneridto",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanbackdate",
      "Name": "预计回货日期",
      "MapType": 0,
      "SrcFieldId": "fplanbackdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_inventorytransferreq'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }


  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}