{
  "Id": "stk_sostockout2ydj_service",
  "Number": "stk_sostockout2ydj_service",
  "Name": "���۳������Ʒ���",
  "SourceFormId": "stk_sostockout",
  "TargetFormId": "ydj_service",
  "ActiveEntityKey": "fentity",
  //"FilterString": "fstatus='E'",
  //"Message": "���Ʒ���ʧ�ܣ�<br>1�����۳��ⵥ�����������״̬��",
  "FieldMappings": [
    //{
    //  "Id": "fagentid",
    //  "Name": "������",
    //  "MapType": 1,
    //  "SrcFieldId": "@currentOrgId",
    //  "MapActionWhenGrouping": 0,
    //  "Order": 0
    //},
    //��ͷ�ֶ�
    {
      "Id": "fbilltypeid",
      "Name": "��������",
      "MapType": 1,
      "SrcFieldId": "'ydj_service_billtype_02'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fservicetype",
      "Name": "��������",
      "MapType": 1,
      "SrcFieldId": "'fres_type_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fserstatus",
      "Name": "����״̬",
      "MapType": 1,
      "SrcFieldId": "'sersta01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fservicedate",
      "Name": "ԤԼʱ��",
      "MapType": 0,
      "SrcFieldId": "fdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "�ͻ�",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeptid",
      "Name": "���۲���",
      "MapType": 0,
      "SrcFieldId": "fsodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcollectrel",
      "Name": "�ջ���",
      "MapType": 0,
      "SrcFieldId": "fconsignee",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fcollectpho",
      "Name": "��ϵ�绰",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fcollectpho",
      "Name": "��ϵ�绰",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fcollectadd",
      "Name": "��ϸ��ַ",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fprovince",
      "Name": "ʡ",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "��",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "��",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "forderno",
      "Name": "��ͬ���",
      "MapType": 0,
      "SrcFieldId": "fsourceinterid_h",
      "MapActionWhenGrouping": 0,
      "Order": 5
    },
    {
      "Id": "fsourcetype",
      "Name": "Դ������",
      "MapType": 1,
      "SrcFieldId": "'stk_sostockout'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "Դ������",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fattention",
      "Name": "ע������",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 8
    },

    //��Ʒ��Ϣ�ֶ�
    {
      "Id": "fmaterialid",
      "Name": "��Ʒ",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fattrinfo",
      "Name": "��������",
      "MapType": 0,
      "SrcFieldId": "fattrinfo.fname",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "����˵��",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid_e",
      "Name": "������λ",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fqty_e",
      "Name": "������λʵ������",
      "MapType": 1,
      "SrcFieldId": "fqty-freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "famount_e",
      "Name": "�ɽ����",
      "MapType": 0,
      "SrcFieldId": "famount",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fsourceformid",
      "Name": "��Դ������",
      "MapType": 0,
      "SrcFieldId": "fsourceformid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "��Դ�����",
      "MapType": 0,
      "SrcFieldId": "fsourcebillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "��Դ������",
      "MapType": 0,
      "SrcFieldId": "fsourceinterid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "��Դ������",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "��Դ����¼����",
      "MapType": 0,
      "SrcFieldId": "fsourceentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpiecesendtag",
      "Name": "һ���������",
      "MapType": 0,
      "SrcFieldId": "fpiecesendtag",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "��Դ����¼����",
      "MapType": 0,
      "SrcFieldId": "fsourceentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
  ]
}