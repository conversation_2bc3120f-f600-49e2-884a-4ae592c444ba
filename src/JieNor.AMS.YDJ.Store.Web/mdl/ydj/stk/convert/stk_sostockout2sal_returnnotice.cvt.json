{
  "Id": "stk_sostockout2sal_returnnotice",
  "Number": "stk_sostockout2sal_returnnotice",
  "Name": "销售出库下推销售退货通知单",
  "SourceFormId": "stk_sostockout",
  "TargetFormId": "sal_returnnotice",
  "ActiveEntityKey": "fentity",
  "FilterString": "fqty>freturnnoticeqty and fstatus='E'",
  "Message": "退货失败：<br>1、销售出库单必须是已审核状态！<br>2、选择的退货明细必须满足：出库数量>退货通知数量（有货可退）！",
  "FieldMappings": [

    //表头字段
    {
      "Id": "fstockstaffid",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsostaffid",
      "Name": "销售员",
      "MapType": 0,
      "SrcFieldId": "fsostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsodeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fsodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },

    {
      "Id": "fstockdeptid",
      "Name": "收货部门",
      "MapType": 0,
      "SrcFieldId": "fstockdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockstaffphone",
      "Name": "收货人电话",
      "MapType": 0,
      "SrcFieldId": "fstockstaffid.fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 2
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 3
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 4
    },
    {
      "Id": "flinkstaffid",
      "Name": "发货人电话",
      "MapType": 0,
      "SrcFieldId": "fconsignee",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "flinkmobile",
      "Name": "发货人电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "flinkaddress",
      "Name": "发货人地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 1
    },
    {
      "Id": "fdeliverywayid",
      "Name": "货运方式",
      "MapType": 0,
      "SrcFieldId": "fdeliverywayid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_sostockout'",
      "MapActionWhenGrouping": 0,
      "Order": 6
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编码",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 7
    },
    {
      "Id": "fshippingbillno",
      "Name": "运输单号",
      "MapType": 0,
      "SrcFieldId": "fshippingbillno",
      "MapActionWhenGrouping": 0,
      "Order": 8
    },
    //退货通知明细字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdesc",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fbizunitid",
      "Name": "销售单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "forderqty",
      "Name": "销售订单数量",
      "MapType": 0,
      "SrcFieldId": "forderqty",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应退数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freturnnoticeqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fqty",
      "Name": "基本单位实退数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freturnnoticeqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 0,
      "SrcFieldId": "fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 0,
      "SrcFieldId": "fprice",
      "MapActionWhenGrouping": 0,
      "Order": 23
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-freturnnoticeqty)*fprice",
      "MapActionWhenGrouping": 0,
      "Order": 24
    },
    {
      "Id": "fvolumeqty",
      "Name": "体积",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fvolume",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fvolume",
      "Name": "总体积",
      "MapType": 1,
      "SrcFieldId": "(fqty-freturnnoticeqty)*fmaterialid.fvolume",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fgrossqty",
      "Name": "毛重",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fgrossload",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fgross",
      "Name": "总重",
      "MapType": 1,
      "SrcFieldId": "(fqty-freturnnoticeqty)*fmaterialid.fgrossload",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpacksize",
      "Name": "纸箱尺寸",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fpacksize",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstorehouseid",
      "Name": "仓库",
      "MapType": 0,
      "SrcFieldId": "fstorehouseid",
      "MapActionWhenGrouping": 0,
      "Order": 25
    },
    {
      "Id": "fstorelocationid",
      "Name": "仓位",
      "MapType": 0,
      "SrcFieldId": "fstorelocationid",
      "MapActionWhenGrouping": 0,
      "Order": 26
    },
    {
      "Id": "fstockstatus",
      "Name": "库存状态",
      "MapType": 0,
      "SrcFieldId": "fstockstatus",
      "MapActionWhenGrouping": 0,
      "Order": 27
    },
    {
      "Id": "flotno",
      "Name": "批号",
      "MapType": 0,
      "SrcFieldId": "flotno",
      "MapActionWhenGrouping": 0,
      "Order": 28
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 29
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 30
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 35
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fentrynote",
      "MapActionWhenGrouping": 0,
      "Order": 50
    },
    {
      "Id": "fsoorderno",
      "Name": "销售订单编号",
      "MapType": 0,
      "SrcFieldId": "fsoorderno",
      "MapActionWhenGrouping": 0,
      "Order": 60
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售订单内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderinterid",
      "MapActionWhenGrouping": 0,
      "Order": 61
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderentryid",
      "MapActionWhenGrouping": 0,
      "Order": 62
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'stk_sostockout'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsooutstockno",
      "Name": "销售出库单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsooutstockinterid",
      "Name": "销售出库单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsooutstockentryid",
      "Name": "销售出库单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fsoorderno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}