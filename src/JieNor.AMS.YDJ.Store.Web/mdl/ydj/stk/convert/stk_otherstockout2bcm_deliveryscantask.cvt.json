{
	"Id": "stk_otherstockout2bcm_deliveryscantask",
	"Number": "stk_otherstockout2bcm_deliveryscantask",
	"Name": "其他出库单生成发货扫描任务",
	"SourceFormId": "stk_otherstockout",
	"TargetFormId": "bcm_deliveryscantask",
	"ActiveEntityKey": "fentity",
	"FilterString": "fstatus='D' and fcancelstatus='0'",
	"Message": "生成发货任务失败：<br>1、其他出库单必须是已提交、未作废状态！",
	"FieldMappings": [
		{
			"Id": "ftask_type",
			"Name": "任务类型",
			"MapType": 1,
			"SrcFieldId": "'stk_otherstockout'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "ftaskstatus",
			"Name": "任务状态",
			"MapType": 1,
			"SrcFieldId": "'ftaskstatus_01'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "ftaskdate",
			"Name": "业务日期",
			"MapType": 1,
			"SrcFieldId": "@currentDate",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fdeliveryno",
			"Name": "收货单号",
			"MapType": 0,
			"SrcFieldId": "fbillno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fexistednetherbill",
			"Name": "已存在下游库存单据",
			"MapType": 1,
			"SrcFieldId": "'1'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		//{
		//  "Id": "fsourcetype",
		//  "Name": "来源单据",
		//  "MapType": 1,
		//  "SrcFieldId": "'stk_postockreturn'",
		//  "MapActionWhenGrouping": 0,
		//  "Order": 0
		//},
		//{
		//  "Id": "fsourcenumber",
		//  "Name": "来源单据编号",
		//  "MapType": 0,
		//  "SrcFieldId": "fbillno",
		//  "MapActionWhenGrouping": 0,
		//  "Order": 0
		//},
		//表体商品明细字段映射
		{
			"Id": "fmaterialid",
			"Name": "商品",
			"MapType": 0,
			"SrcFieldId": "fmaterialid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fcustomdesc",
			"Name": "定制说明",
			"MapType": 0,
			"SrcFieldId": "fcustomdesc",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fattrinfo",
			"Name": "辅助属性",
			"MapType": 0,
			"SrcFieldId": "fattrinfo",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fbizunitid",
			"Name": "单位",
			"MapType": 0,
			"SrcFieldId": "fbizunitid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fwaitworkqty",
			"Name": "待作业数量",
			"MapType": 0,
			"SrcFieldId": "fbizqty",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flotno",
			"Name": "批号",
			"MapType": 0,
			"SrcFieldId": "flotno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fmtono",
			"Name": "物流跟踪号",
			"MapType": 0,
			"SrcFieldId": "fmtono",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fownertype",
			"Name": "货主类型",
			"MapType": 0,
			"SrcFieldId": "fownertype",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fownerid",
			"Name": "货主",
			"MapType": 0,
			"SrcFieldId": "fownerid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fcategorygroupid",
			"Name": "配件组合号",
			"MapType": 0,
			"SrcFieldId": "fpartscombnumber",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "funitid",
			"Name": "基本单位",
			"MapType": 0,
			"SrcFieldId": "funitid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fqty",
			"Name": "基本单位数量",
			"MapType": 0,
			"SrcFieldId": "forderqty",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flinkformid",
			"Name": "关联单据",
			"MapType": 1,
			"SrcFieldId": "'stk_otherstockout'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flinkbillno",
			"Name": "关联单据编号",
			"MapType": 0,
			"SrcFieldId": "fbillno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "flinkrowinterid",
			"Name": "关联单行内码",
			"MapType": 0,
			"SrcFieldId": "fentity.id",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourceformid",
			"Name": "来源单据",
			"MapType": 1,
			"SrcFieldId": "'stk_otherstockout'",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourcebillno",
			"Name": "来源单据编号",
			"MapType": 0,
			"SrcFieldId": "fbillno",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourcefid",
			"Name": "来源单内码",
			"MapType": 0,
			"SrcFieldId": "fbillhead.id",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fsourceentryid",
			"Name": "来源单行内码",
			"MapType": 0,
			"SrcFieldId": "fentity.id",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fstorehouseid",
			"Name": "仓库",
			"MapType": 0,
			"SrcFieldId": "fstorehouseid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fstorelocationid",
			"Name": "仓位",
			"MapType": 0,
			"SrcFieldId": "fstorelocationid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		},
		{
			"Id": "fcustomerid",
			"Name": "客户",
			"MapType": 0,
			"SrcFieldId": "fcustomerid",
			"MapActionWhenGrouping": 0,
			"Order": 0
		}
	],
	"BillGroups": [
		{
			"Id": "fbillno",
			"Order": 1
		}
	],
	"FieldGroups": [
		{
			"Id": "fentity_fentryid",
			"Order": 1
		}
	]
}