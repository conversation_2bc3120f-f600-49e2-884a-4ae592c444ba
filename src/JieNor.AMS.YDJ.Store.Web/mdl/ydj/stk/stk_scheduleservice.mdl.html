<!--
排单计划单，用于排单平台作业后的数据存储

-->

<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_scheduleservice" basemodel="" el="0" cn="排单服务项目">
    <div id="fbillhead" el="51" pk="fid" tn="" pn="fbillhead" cn="排单服务项目">

        <!--创建排单-->
        <!--商品（明细）-->
        <table id="fschentry" el="52" pk="fentryid" tn="" pn="fschentry"  cn="服务项目">
            <tr>
                <th el="106" ek="fschentry" id="fseritemid_s" fn="fseritemid_s" pn="fseritemid_s" cn="服务项目" refid="ydj_seritem" sformid="" visible="-1" width="200"></th>
                <th el="106" ek="fschentry" id="funitid_s" fn="funitid_s" pn="funitid_s" cn="单位" refid="ydj_unit" sformid="" visible="96" width="100" lock="-1"></th>
                <th el="104" ek="fschentry" id="fprice_s" fn="fprice_s" ts="" cn="单价" visible="96" width="130" format="0,000.000000" dformat="0,000.00"></th>
                <th el="102" ek="fschentry" id="fqty_s" fn="fqty_s" pn="fqty_s" cn="数量" visible="96" width="90"></th>
                <th el="105" ek="fschentry" id="famount_s" fn="famount_s" ts="" cn="金额" visible="96" width="140" format="0,000.00" lock="-1"></th>
                
                <th el="140" ek="fproductentry" id="fsourceformid" fn="fsourceformid" ts="" cn="来源单类型" visible="1062" copy="0" lix="150"></th>
                <th el="141" ek="fproductentry" id="fsourcebillno" fn="fsourcebillno" ts="" cn="来源单编号" visible="1062" copy="0" lix="153"></th>
                <th el="100" ek="fproductentry" id="fsourceinterid" fn="fsourceinterid" ts="" cn="来源单内码" visible="0" copy="0" lix="155"></th>
                <th el="100" ek="fproductentry" id="fsourceentryid" fn="fsourceentryid" ts="" cn="来源单分录内码" visible="0" copy="0" lix="157"></th>
            </tr>
        </table>
    </div>

    

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">

    </div>

</body>
</html>

