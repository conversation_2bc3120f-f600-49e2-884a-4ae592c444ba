<!--
排单申请单

-->

<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_scheduleapply" basemodel="base_stkbilltmpl" el="1" cn="排单申请单">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_scheduleapply" pn="fbillhead" cn="排单申请单">

        <!--修改基类字段名-->
        <input group="基本信息" el="112" ek="fbillhead" id="fdate" fn="fdate" cn="申请日期" visible="-1" defval="@currentshortdate" lix="20" width="105" />

        <!--基本信息-->
        <input group="基本信息" el="106" ek="fbillhead" id="fsostaffid" fn="fsostaffid" pn="fsostaffid" cn="销售员" visible="-1" refid="ydj_staff" />
        <input group="基本信息" el="106" ek="fbillhead" id="fsodeptid" fn="fsodeptid" pn="fsodeptid" cn="销售部门" visible="-1" refid="ydj_dept" />

        <!--排单申请对象信息-->
        <input group="基本信息" el="152" ek="FBillHead" id="fapplytype" fn="fapplytype" pn="fapplytype" visible="-1" cn="业务类型"
               lock="0" copy="1" lix="22" notrace="true" ts="" vals="'0':'退货换货','1':'退货退款','2':'销售借货','3':'其它出库','4':'库存调拨'," defval="'0'" />
        
        <input group="基本信息" el="106" ek="FBillHead" id="fapplystaffid" fn="fapplystaffid" pn="fapplystaffid" visible="-1" cn="申请人"
               lock="0" copy="1" lix="23" notrace="true" ts="" refid="ydj_staff" defVal="@currentStaffId" filter="" reflvt="0" dfld="" />
        
        <input group="基本信息" el="106" ek="FBillHead" id="fapplydeptid" fn="fapplydeptid" pn="fapplydeptid" visible="-1" cn="申请部门"
               lock="0" copy="1" lix="24" notrace="true" ts="" refid="ydj_dept" defVal="@currentDeptId" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" visible="-1" cn="客户"
               lock="0" copy="1" lix="30" notrace="true" ts="" refid="ydj_customer" filter="" reflvt="0" dfld="fcontacts,fphone" />
        <input group="基本信息" el="100" ek="FBillHead" id="fcustphone" fn="fcustphone" pn="fcustphone" visible="-1" cn="联系电话"
               lock="0" copy="1" lix="31" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="FBillHead" id="faddress" fn="faddress" pn="faddress" visible="-1" cn="详细地址"
               lock="0" copy="1" lix="32" notrace="true" ts="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockstaffid" fn="fstockstaffid" pn="fstockstaffid" visible="-1" cn="发货人"
               lock="0" copy="1" lix="23" notrace="true" ts="" refid="ydj_staff" defVal="" filter="" reflvt="0" dfld="fphone" must="0" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockdeptid" fn="fstockdeptid" pn="fstockdeptid" visible="-1" cn="发货部门"
               lock="0" copy="1" lix="24" notrace="true" ts="" refid="ydj_dept" defVal="" filter="" reflvt="0" dfld="" must="0" />
               <input group="基本信息" el="112" ek="FBillHead" id="fstockdate" fn="fstockdate" pn="fstockdate" visible="-1" cn="发货日期"
               lock="0" copy="0" lix="35" notrace="true" ts="" />
         <input group="基本信息" el="112" ek="FBillHead" id="fstockdateto" fn="fstockdateto" pn="fstockdateto" visible="-1" cn="收货日期"
               lock="0" copy="0" lix="35" notrace="true" ts="" />       
        <input group="基本信息" el="100" ek="FBillHead" id="fstockaddress" fn="fstockaddress" pn="fstockaddress" visible="-1" cn="发货人地址"
               lock="0" copy="1" lix="32" notrace="true" ts="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockstaffidto" fn="fstockstaffidto" pn="fstockstaffidto" visible="-1" cn="收货联系人"
               lock="0" copy="1" lix="23" notrace="true" ts="" refid="ydj_staff" filter="" reflvt="1" dfld="fphone" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockdeptidto" fn="fstockdeptidto" pn="fstockdeptidto" visible="-1" cn="收货申请部门"
               lock="0" copy="1" lix="24" notrace="true" ts="" refid="ydj_dept" filter="" reflvt="1" dfld="" />
        <input group="基本信息" el="100" ek="FBillHead" id="fstockaddressto" fn="fstockaddressto" pn="fstockaddressto" visible="-1" cn="收货人地址"
               lock="0" copy="1" lix="32" notrace="true" ts="" />
    </div>

    <!--申请明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_stk_scheduleapplyentry" pn="fentity" cn="申请明细" kfks="fmaterialid">
        <tr>
            <!--修改基类字段属性-->
            <th el="103" ek="fentity" id="fbizplanqty" cn="计划数量" visible="0"></th>
            <th el="103" ek="fentity" id="fplanqty" cn="基本单位计划数量" visible="0"></th>

            <th el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" ctlfk="fmaterialid" dispfk="fbrandid" sformid="" width="100" lock="-1"></th>
            <th el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" ctlfk="fmaterialid" dispfk="fseriesid" sformid="" width="100" lock="-1"></th>
            <th el="106" ek="fentity" id="fmaterialid" lock="0" must="1"></th>

            <th el="132" ek="fentity" id="fattrinfo" lock="-1" must="0" cn="调出辅助属性"></th>
            <th el="132" ek="fentity" id="fattrinfoto" fn="fattrinfoto" cn="调入辅助属性" lix="80" ctlfk="fmaterialid" pricefk="" width="160" visible="1150" lock="-1"></th>
            <th el="100" ek="fentity" id="fcustomdesc" lock="0" must="0" cn="调出定制说明"></th>
            <th el="100" ek="fentity" len="2000" id="fcustomdescto" fn="fcustomdescto" cn="调入定制说明" lix="90" width="160" visible="1150" lock="0"></th>

            <th el="109" ek="fentity" id="funitid" lock="-1" must="1"></th>
            <th el="109" ek="fentity" id="fbizunitid" cn="申请单位"></th>

            <th el="103" ek="fentity" id="fqty" ts="" cn="基本单位申请数量" visible="-1" must="1" width="130"></th>
            <th el="103" ek="fentity" id="fbizqty" cn="申请数量"></th>

            <th el="103" ek="fentity" id="fscheduleqty" fn="fscheduleqty" pn="fscheduleqty" visible="-1" cn="已排数量"
                lock="-1" copy="0" lix="100" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>


            <th el="106" ek="fentity" id="fstorehouseid" cn="发货仓库" lock="0" must="0"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th el="153" ek="fentity" id="fstorelocationid" cn="发货仓位" lock="0" must="0"></th>
            <th el="106" ek="fentity" id="fstockstatus" cn="发货仓状态" lock="0" must="0"></th>

            <!--调入字段-->
            <th el="106" ek="fentity" id="fstorehouseidto" fn="fstorehouseidto" pn="fstorehouseidto" visible="-1" cn="收货仓库"
                lock="0" copy="1" lix="180" notrace="true" ts="" refid="ydj_storehouse" filter="" reflvt="0" dfld=""></th>
            <th el="153" ek="fentity" id="fstorelocationidto" fn="fstorelocationidto" pn="fstorelocationidto" visible="-1" cn="收货仓位"
                lock="0" copy="1" lix="181" notrace="true" ts="" ctlfk="fstorehouseidto" luek="fentity" lunbfk="fnumber" lunmfk="fname"></th>
            <th el="106" ek="fentity" id="fstockstatusto" fn="fstockstatusto" pn="fstockstatusto" visible="-1" cn="收货仓状态"
                lock="0" copy="1" lix="182" notrace="true" ts="" refid="ydj_stockstatus" filter="" reflvt="0" dfld=""></th>


            <th el="112" ek="fentity" id="fplanbackdate" fn="fplanbackdate" pn="fplanbackdate" visible="1150" cn="预计回货日期"
                lock="0" copy="0" lix="128" notrace="true" ts="" width="100"></th>

            <th el="100" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" cn="调出物流跟踪号" lix="160" width="100" visible="-1" lock="-1"></th>
            <th el="100" ek="fentity" id="fmtonoto" fn="fmtonoto" pn="fmtonoto" cn="调入物流跟踪号" lix="180" width="100" visible="1150" lock="-1"></th>

            <!--其它维度字段锁定-->
            <th el="100" ek="fentity" id="flotno" lock="-1" visible="1086"></th>

            <th el="149" ek="fentity" id="fownertype" cn="货主类型" lock="0" must="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fownerid" cn="货主" lock="0" must="0"></th>

            <th el="104" ek="fentity" id="fprice" lock="0"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="17" sid="1002" cn="反写合同已排申请数量" data="{
                'sourceFormId':'stk_sostockout',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'fapplyqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fapplyqty&gt;fqty',
                'excessMessage':'已排单申请数量不允许超过合同数量！'
                }"></li>
        </ul>
        <ul el="10" id="submit" op="submit" opn="提交"></ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核"></ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核"></ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除"></ul>
    </div>

</body>
</html>

