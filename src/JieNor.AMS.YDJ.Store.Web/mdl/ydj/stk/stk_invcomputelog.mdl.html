<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    库存计算日志
-->
<html lang="en">
<head>
</head>
<body id="stk_invcomputelog" el="1" basemodel="stk_inventorylist" cn="库存计算日志" rac="true" isolate="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_invcomputelog" pn="fbillhead" cn="单据头">
        <input group="基本信息" el="100" ek="FBillHead" id="fcomputetype" fn="fcomputetype" pn="fcomputetype" visible="-1" cn="计算类型"
               lock="-1" copy="1" lix="0" notrace="true" ts="" desc="0--结束初始化日志，1--重新初始化日志，5--库存校正日志，10--关账日志，11--反关账日志，支持自定义类型" />
        <input group="基本信息" el="100" ek="FBillHead" id="fdescription" fn="fdescription" pn="fdescription" visible="-1" cn="操作描述"
               lock="-1" copy="0" lix="0" notrace="true" ts="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fsourceformid" fn="fsourceformid" pn="fsourceformid" visible="-1" cn="单据名称"
               lock="-1" copy="0" lix="0" notrace="true" ts="" refid="sys_bizobject" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="100" ek="fbillhead" id="fsourcebillno" fn="fsourcebillno" pn="fsourcebillno" visible="-1" cn="单据编号"
               lock="-1" copy="0" lix="1" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="fbillhead" id="fsourceinterid" fn="fsourceinterid" pn="fsourceinterid" visible="0" cn="单据内码"
               lock="-1" copy="0" lix="2" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="fbillhead" id="fsourceentryid" fn="fsourceentryid" pn="fsourceentryid" visible="0" cn="分录内码"
               lock="-1" copy="0" lix="3" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="fbillhead" id="fsourceentitykey" fn="fsourceentitykey" pn="fsourceentitykey" visible="0" cn="分录标识"
               lock="-1" copy="0" lix="4" notrace="true" ts="" />
        <!--辅助计算总库存量，体现加减正负-->
        <input group="基本信息" el="102" ek="fbillhead" id="fupdatefactor" fn="fupdatefactor" pn="fupdatefactor" visible="-1" cn="更新因子"
               lock="-1" copy="0" lix="0" notrace="true" ts="" roundType="0" format="0,000.00" />
        <!--此字段用于一个单据上挂多个库存服务时的快照区分依据-->
        <input group="基本信息" el="100" ek="fbillhead" id="fupdserviceid" fn="fupdserviceid" pn="fupdserviceid" visible="-1" cn="库存更新服务标识"
               lock="-1" copy="0" lix="0" notrace="true" ts="" />

    </div>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>

        <ul el="10" ek="fbillhead" id="listdatatopdf" op="listdatatopdf" opn="导出PDF" data="" permid="fw_export"></ul>
        <ul el="10" ek="fbillhead" id="listdatatoexcel" op="listdatatoexcel" opn="导出Excel" data="" permid="fw_export"></ul>
    </div>
    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="fw_view" cn="查看"></ul>
        <ul el="12" id="fw_export" cn="导出"></ul>
    </div>

    <div id="ListFuzzyFlds" cn="默认支持快捷过滤的字段列表">
        <ul el="14" id="fw_fuzzyfld" fldkeys="fmaterialid.fname,fmtrlnumber,fmtrlmodel,fmtono" cn="默认支持快捷过滤的字段列表，多个用逗号或分号隔开"></ul>
    </div>
</body>
</html>