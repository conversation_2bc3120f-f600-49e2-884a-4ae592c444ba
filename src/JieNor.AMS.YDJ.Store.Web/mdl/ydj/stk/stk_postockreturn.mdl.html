<!--
采购退货单

-->

<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_postockreturn" basemodel="base_stkbilltmpl" el="1" cn="采购退货单" ubl="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_postockreturn" pn="fbillhead" cn="采购退货单">
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="1150" lix="27"></select>
        <!--修改基类字段名-->
        <!--采购退货类型：正常退货，退换并扣款-->

        <input lix="1" group="基本信息" el="112" id="fdate" cn="退货日期" visible="-1" must="1" />
        <input lix="1" group="基本信息" el="112" id="fplandate" fn="fplandate" cn="计划退货日期" defval="@currentshortdate" visible="-1"  uaul="true" />
        <select lix="5" group="基本信息" el="122" ek="fbillhead" id="freturntype" fn="freturntype" cn="退货类型" refid="bd_enum" cg="采购退货业务类型" must="1" visible="-1" lock="0" ></select>
        <input lix="10" group="基本信息" el="100" ek="FBillHead" id="freturnreason" fn="freturnreason" ts="" cn="退货原因" visible="-1" copy="0" len="1000" />
        <input lix="15" group="基本信息" el="106" ek="fbillhead" id="fpostaffid" fn="fpostaffid" pn="fpostaffid" cn="采购员" visible="-1" refid="ydj_staff" />
        <input lix="20" group="基本信息" el="107" ek="fbillhead" id="fpodeptnumber" fn="fpodeptnumber" pn="fpodeptnumber" cn="采购部门编码" visible="1150" ctlfk="fpodeptid" dispfk="fnumber" />
        <input lix="20" group="基本信息" el="106" ek="fbillhead" id="fpodeptid" fn="fpodeptid" pn="fpodeptid" cn="采购部门" visible="-1" refid="ydj_dept" />
        <input lix="25" group="基本信息" el="106" ek="fbillhead" id="fsupplierid" fn="fsupplierid" pn="fsupplierid" cn="供应商" visible="-1" refid="ydj_supplier" must="1" />
        <input lix="30" group="基本信息" el="106" id="fstockstaffid" cn="仓管员" visible="-1" must="1" />
        <input lix="35" group="基本信息" el="100" ek="FBillHead" id="fstockstaffphone" fn="fstockstaffphone" pn="fstockstaffphone" visible="-1" cn="仓管员联系方式"
               lock="0" copy="1" notrace="true" ts="" />
        <input lix="40" group="基本信息" el="106" ek="fbillhead" id="fdeliverywayid" fn="fdeliverywayid" pn="fdeliverywayid" visible="-1" cn="交货方式"
               lock="0" copy="1" notrace="true" ts="" refid="ydj_deliveryway" filter="" reflvt="0" dfld="" />
        <input lix="45" group="基本信息" el="141" ek="fbillhead" visible="-1" id="fsourcenumber" />
        <input lix="50" group="客户信息" el="100" ek="fbillhead" id="fsupplieraddr" fn="fsupplieraddr" ts="" cn="供方地址" visible="-1" />



        <input lix="1" group="基本信息" el="106" id="fstockdeptid" cn="仓管部门" lock="-1" />
        <!--此字段适用于退货罚款（正常还要送货，通常是质量原因引起的不符合使用条件，同时又耽误了采购方正常生产贸易进度了）场景，暂不实现此场景，后续支持此场景后再放开-->
        <input group="基本信息" el="105" ek="fbillhead" id="fdeductamount" fn="fdeductamount" pn="fdeductamount" cn="扣罚金额" visible="0" lock="-1" />



        <input group="基本信息" el="100" ek="fbillhead" id="fdeliverybillno" fn="fdeliverybillno" pn="fdeliverybillno" cn="物流单号" visible="1150" copy="0" />
        <input group="基本信息" el="111" ek="FBillHead" id="fdeliveryvoucher" fn="fdeliveryvoucher" ts="" cn="物流凭据" visible="1150" copy="0" />

        <input group="财务信息" el="105" ek="fbillhead" id="fplanreturnamount" fn="fplanreturnamount" pn="fplanreturnamount" visible="1150" cn="应退货款金额"
               lock="-1" copy="0" lix="80" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="财务信息" el="105" ek="fbillhead" id="factualreturnamount" fn="factualreturnamount" pn="factualreturnamount" visible="1150" cn="实退货款金额"
               lock="-1" copy="0" lix="81" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="基本信息" el="116" ek="fbillhead" id="fispdatask" fn="fispdatask" pn="fispdatask" visible="1150" cn="是否PDA作业"
               lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <input group="基本信息" el="116" ek="fbillhead" id="fcreatescantask" fn="fcreatescantask" pn="fcreatescantask" visible="1150" cn="已生成PDA扫描任务" lock="-1" copy="0" lix="70" notrace="true" ts="" />

        <input group="基本信息" el="116" ek="fbillhead" id="frenewalflag" fn="frenewalflag" pn="frenewalflag" visible="1150" cn="焕新订单标记" lock="-1" copy="0" notrace="false" ts="" defval="false" />
    </div>

    <!--商品信息-->
    <table id="fentity" el="52" pk="fentryid" tn="t_stk_postockreturnentry" pn="fentity" cn="退货明细">
        <tr>
            <!--修改基类字段属性-->
            <th lix="200" visible="1150" el="109" ek="fentity" id="fbizunitid" cn="采购单位"></th>
            <th lix="205" visible="1150" el="103" ek="fentity" id="fbizplanqty" cn="应退数量"></th>
            <th lix="210" visible="1150" el="103" ek="fentity" id="fbizqty" cn="采购实退数量"></th>
            <th lix="215" el="109" ek="fentity" id="fstockunitid" fn="fstockunitid" cn="库存单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="100" must="1" lock="-1"></th>
            <th lix="220" el="109" ek="fentity" id="funitid" fn="funitid" cn="基本单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" filter="fisbaseunit='1'" visible="1150" width="80" must="1" lock="-1"></th>
            <th lix="225" el="106" ek="fentity" id="fstorehouseid" fn="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="1150" width="100" must="2" filter="fname!='直发仓库'"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th lix="230" el="153" ek="fentity" id="fstorelocationid" fn="fstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1150" width="100"></th>
            <th lix="235" el="106" ek="fentity" id="fstockstatus" fn="fstockstatus" cn="库存状态" refid="ydj_stockstatus" defVal="'311858936800219137'" sformid="" visible="1150" width="100" must="2" lock="-1"></th>
            <th lix="240" el="108" ek="fentity" id="fpoinstockno" fn="fpoinstockno" ts="" cn="采购入库单编号" visible="1150"
                lock="-1" copy="0"></th>
            <th lix="245" el="100" ek="fentity" id="fpoorderno" fn="fpoorderno" pn="fpoorderno" visible="1150" cn="采购订单编号"
                lock="-1" copy="0" notrace="true" ts="" ctlfk="#ydj_purchaseorder"></th>

            <th lix="250" el="103" ek="fentity" id="fplanqty" cn="基本单位应退数量" visible="1086"></th>
            <th lix="255" el="103" ek="fentity" id="fqty" cn="基本单位实退数量" visible="1086" lock="-1"></th>

            <th lix="260" el="108" ek="fentity" id="fpoinstockinterid" fn="fpoinstockinterid" ts="" cn="采购入库单内码" visible="0"
                lock="-1" copy="0"></th>
            <th lix="265" el="108" ek="fentity" id="fpoinstockentryid" fn="fpoinstockentryid" ts="" cn="采购入库单分录内码" visible="0"
                lock="-1" copy="0"></th>


            <th el="100" ek="fentity" id="fpoorderinterid" fn="fpoorderinterid" pn="fpoorderinterid" visible="0" cn="采购订单内码"
                lock="-1" copy="0" lix="231" notrace="true" ts=""></th>
            <th el="100" ek="fentity" id="fpoorderentryid" fn="fpoorderentryid" pn="fpoorderentryid" visible="0" cn="采购订单分录内码"
                lock="-1" copy="0" lix="232" notrace="true" ts=""></th>
            <th el="112" ek="fentity" id="forderdate" fn="forderdate" pn="forderdate" visible="1086" cn="订单日期"
                lock="-1" copy="0" lix="233" notrace="true" ts="">订单日期</th>

            <th el="103" ek="fentity" id="forderqty" fn="forderqty" pn="forderqty" visible="1086" cn="基本单位订单数量" width="120"
                lock="-1" copy="0" lix="234" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizorderqty" fn="fbizorderqty" pn="fbizorderqty" visible="1086" cn="采购订单数量" width="100"
                lock="-1" copy="0" lix="235" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="forderqty" roundType="0" format="0,000.00"></th>

            <!--需求#3251-->
            <th lix="165" el="104" ek="fentity" id="fprice" must="0" lock="0" cn="成交单价" visible="1150" format="0,000.000000" dformat="0,000.00"></th>
            <th lix="175" el="105" ek="fentity" id="famount" must="0" lock="0" cn="成交金额" visible="1150"></th>
            <th el="104" ek="fentity" id="fpoprice" fn="fpoprice" pn="fpoprice" cn="采购单价" lix="135" width="70" visible="1150" format="0,000.000000" dformat="0,000.00"></th>
            <th el="105" ek="fentity" id="fpoamount" fn="fpoamount" pn="fpoamount" cn="金额" lix="135" width="70" visible="1150" format="0,000.00"></th>


            <th lix="200" el="103" ek="fentity" id="factualreturnpackqty" fn="factualreturnpackqty" ts="" visible="1150" cn="采购实退包数" lock="-1" copy="0"></th>
            <!--单位成本，总成本-->
            <th group="退货明细" el="105" ek="fentity" id="fcostprice" fn="fcostprice" pn="fcostprice" cn="单位成本(加权平均)" />
            <th group="退货明细" el="105" ek="fentity" id="fcostamt" fn="fcostamt" pn="fcostamt" cn="总成本(加权平均)" />

            <th id="fseltypeid" el="107" ek="fentity" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="320"></th>

            <!--后台字段-->
            <th el="100" ek="fentity" lock="-1" lix="450" id="fhqderno" fn="fhqderno" pn="fhqderno" cn="总部合同号" visible="0"></th>
            <th lix="480" el="108" ek="fentity" id="fsoorderno" fn="fsoorderno" pn="fsoorderno" visible="0" cn="销售合同编号"
                lock="-1" copy="0" ts=""></th>
            <th el="106" ek="fentity" visible="0" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" lix="490" lock="-1" />
            <th lix="500" el="107" ek="fentity" visible="0" id="fcustomerphone" fn="fcustomerphone" pn="fcustomerphone" cn="客户手机号" ctlfk="fcustomerid" dispfk="fphone" />
            <th el="106" ek="fentity" visible="0" id="fconsigneeid" fn="fconsigneeid" pn="fconsigneeid" refid="ydj_staff" cn="收货人" lix="505" />
            <th el="107" ek="fentity" visible="0" id="fconsignee" fn="fconsignee" pn="fconsignee" cn="收货人姓名" ctlfk="fconsigneeid" dispfk="fname" lix="510" />
            <th el="107" ek="fentity" visible="0" id="fconsigneephone" fn="fconsigneephone" pn="fconsigneephone" cn="收货人手机号" ctlfk="fconsigneeid" dispfk="fphone" lix="515" />

            <th el="100" lix="299" ek="fentity" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1086" cn="体积单位" lock="-1" must="0" uaul="true"></th>
            <th el="102" lix="300" ek="fentity" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1086" cn="总体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th el="102" lix="301" ek="fentity" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1086" cn="单位体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>

            <th lix="260" el="108" ek="fentity" id="ffeedbackinterid" fn="ffeedbackinterid" pn="ffeedbackinterid" ts="" cn="售后反馈单内码" visible="0"
                lock="-1" copy="0"></th>
            <th lix="265" el="108" ek="fentity" id="ffeedbackentryid" fn="ffeedbackentryid" pn="ffeedbackentryid" ts="" cn="售后反馈单分录内码" visible="0" lock="-1" copy="0"></th>
        </tr>
    </table>


    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">

            <li el="17" sid="2000" data="{'factor':-1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus=\'c\''}">审核时执行库存更新服务</li>

            <li el="17" sid="1002" cn="反写采购订单退货数量" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'finstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoorderentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_01\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty+frefundqty&gt;finstockqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购订单退款数量" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'finstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoorderentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_02\'',
                'writebackFieldKey':'frefundqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty+frefundqty&gt;finstockqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购订单申请退款金额" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'frefundamount',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fpoorderno',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_02\'',
                'writebackFieldKey':'frefundamount',
                'expression':'factualreturnamount',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>

            <li el="17" sid="1002" cn="反写采购入库单退货数量" data="{
                'sourceFormId':'stk_postockin',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoinstockentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty',
                'excessMessage':'退货数量不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购退货通知单退货数量" data="{
                'sourceFormId':'pur_returnnotice',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty',
                'excessMessage':'退货数量不允许超过可退数量（退货通知数量）！'
                }"></li>

            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'preCondition':'',
                'message':'单据审核，自动释放预留数量',
                'releaseWay':6,
                'releaseType':0,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'',
                'deptFieldKey':'fpodeptid',
                'staffFieldKey':'fpostaffid',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>

            <li el="17" sid="2000" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus!=\'c\''}">审核时执行库存更新服务</li>

            <li el="17" sid="1002" cn="反写采购订单退货数量" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'finstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoorderentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_01\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty+frefundqty&gt;finstockqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购订单退款数量" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'finstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoorderentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_02\'',
                'writebackFieldKey':'frefundqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty+frefundqty&gt;finstockqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购订单申请退款金额" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'frefundamount',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fpoorderno',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_02\'',
                'writebackFieldKey':'frefundamount',
                'expression':'factualreturnamount',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>

            <li el="17" sid="1002" cn="反写采购入库单退货数量" data="{
                'sourceFormId':'stk_postockin',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoinstockentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty',
                'excessMessage':'退货数量不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购退货通知单退货数量" data="{
                'sourceFormId':'pur_returnnotice',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty',
                'excessMessage':'退货数量不允许超过可退数量（退货通知数量）！'
                }"></li>

            <li el="17" sid="2002" cn="取消释放预留数量" data="{
                'preCondition':'',
                'message':'单据反审核，自动取消释放预留数量',
                'releaseWay':6,
                'releaseType':1,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'fcustomerid',
                'deptFieldKey':'fpodeptid',
                'staffFieldKey':'fpostaffid',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="17" sid="2000" cn="删除时更新库存" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'famount',
                'preCondition':'fstatus!=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写采购订单退货数量" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'finstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoorderentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_01\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty+frefundqty&gt;finstockqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购订单退款数量" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'finstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoorderentryid',
                'linkFormFieldKey':'fsourcetype',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_02\'',
                'writebackFieldKey':'frefundqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty+frefundqty&gt;finstockqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购订单申请退款金额" data="{
                'sourceFormId':'ydj_purchaseorder',
                'sourceControlFieldKey':'frefundamount',
                'sourceLinkFieldKey':'fbillno',
                'linkIdFieldKey':'fpoorderno',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\' and freturntype=\'postockreturn_biztype_02\'',
                'writebackFieldKey':'frefundamount',
                'expression':'factualreturnamount',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>

            <li el="17" sid="1002" cn="反写采购入库单退货数量" data="{
                'sourceFormId':'stk_postockin',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoinstockentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty',
                'excessMessage':'退货数量不允许超过可退数量（入库数量）！'
                }"></li>

            <li el="17" sid="1002" cn="反写采购退货通知单退货数量" data="{
                'sourceFormId':'pur_returnnotice',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty',
                'excessMessage':'退货数量不允许超过可退数量（退货通知数量）！'
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="2011" cn="反写采购入库单退货数量" data="{
                'sourceFormId':'stk_postockin',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoinstockentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fcancelstatus=\'0\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty+freturnqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>
                
            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            
            <li el="17" sid="2006" cn="删除预留" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="submit" op="submit" opn="提交">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unsubmit" op="unsubmit" opn="撤销">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="11" vid="3014" cn="反审核增加校验如果对应的《发货扫描任务》单据头【任务状态】=”已完成”时, 不允许反审核" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            
            <li el="17" sid="2006" cn="删除预留" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="uncancel" op="uncancel" opn="反作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="2011" cn="反写采购入库单退货数量" data="{
                'sourceFormId':'stk_postockin',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fpoinstockentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fcancelstatus=\'0\'',
                'writebackFieldKey':'freturnqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnqty&gt;fqty+freturnqty',
                'excessMessage':'采购累积退货数量（退货数量+退款数量）不允许超过可退数量（入库数量）！'
                }"></li>
                
            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" id="createscantask" op="createscantask" opn="生成发货任务" data="" permid="" ubl="1"></ul>
    </div>
</body>
</html>

