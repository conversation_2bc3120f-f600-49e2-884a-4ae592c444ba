{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
     //此规则表示：该商品对应的商品商品属性“允许定制”=true时放开，为false时，锁定
    {
      "id": "lock_fcustomdesc",
      "expression": "field:fcustomdesc$|fcustom!=true"
    },
    {
      "id": "unlock_fcustomdesc",
      "expression": "field:$fcustomdesc|fcustom==true"
    },
    //此规则表示：该商品对应的商品商品属性“允许选配”=true时，辅助属性放开，为false时，锁定
    {
      "id": "lock_fsel",
      "expression": "field:fattrinfo$|fispresetprop!=true"
    },
    {
      "id": "unlock_fsel",
      "expression": "field:$fattrinfo|fispresetprop==true"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "funitid=fmaterialid__funitid"},
    { "expression": "fbizunitid=fmaterialid__fstockunitid" },
    //{ "expression": "freservestatus='0'|(freservestatus=='' or freservestatus==' ') and (fmaterialid!='' or fmaterialid!=' ')" },
    { "expression": "fdirection_d='0'|(fdirection_d=='' or fdirection_d==' ') and (fmaterialid_d!='' or fmaterialid_d!=' ')" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    { "expression": "fmtrlimage=getImages(fmaterialid,fattrinfo,fcustomdesc)" }
  ]
}