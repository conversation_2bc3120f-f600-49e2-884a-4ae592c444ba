<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    发货扫描任务

-->

<html lang="en">
<head>
</head>
<body id="stk_barcodescan" el="1" basemodel="bill_basetmpl" cn="条码扫描记录" isolate="1" nfsa="true" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_barcodescan" pn="fbillhead" cn="单据头">

        <input el="100" ek="fbillHead" id="fbarcode" fn="fbarcode" pn="fbarcode" cn="条码"
               width="110" visible="1124"  lock="-1" />

        <input el="100" ek="fbillHead" id="fbarcodetext" fn="fbarcodetext" pn="fbarcodetext" cn="条码码文"
               width="110" visible="1124"  lock="-1" />

        <input el="100" ek="fbillHead" id="fhandler" fn="fhandler" pn="fsubmitstaffid" cn="操作人"
               width="110" visible="1124"  lock="-1" />
        <input ek="fbillHead" el="113" id="fhandlerdate" fn="fhandlerdate" pn="fhandlerdate" cn="操作时间" visible="1150" width="120"  lock="-1" />

        <input el="122" ek="fbillHead" visible="1150" id="fworkhandle" fn="fworkhandle" pn="fworkhandle" cn="业务操作" cg="扫描任务业务操作" refid="bd_enum" dfld="fenumitem" defval="'materialhandle_01'" lix="29" width="90" apipn="type"  lock="-1" />

        <input lix="100" el="106" ek="fbillHead" id="fmaterialid_d" fn="fmaterialid" pn="fmaterialid" visible="1150" cn="商品" width="160"
                lock="-1" copy="1" notrace="true" ts="" refid="ydj_product" multsel="true" filter="" reflvt="0" dfld="fspecifica" sformid="" />

        <input el="106" ek="fbillHead" id="fstorehouseid_d" fn="fstorehouseid" cn="仓库" lix="140" refid="ydj_storehouse" sformid="" visible="1150" width="100"  lock="-1" />

        <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fsubmitentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
        <input el="153" ek="fbillHead" id="fstorelocationid_d" fn="fstorelocationid" cn="仓位" lix="150" ctlfk="fstorehouseid_d" luek="fbillHead" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1150" width="100"  lock="-1" />

        <input ek="fbillHead" el="100" id="fnote_d" fn="fnote" pn="fnote" cn="备注" visible="96" width="260" copy="0" apipn="note"  lock="-1" />

        <input ek="fbillHead" el="100" id="fpda" fn="fpda" pn="fpda" cn="PDA" visible="1150" width="100"  lock="-1" />

        <input type="text" id="fsourcetype" el="140" ek="fbillhead" fn="fsourcetype" pn="fsourcetype" apipn="sourceType" cn="来源单据" visible="1124" xlsin="0" copy="0"  lock="-1">

        <input type="text" id="fsourcenumber" el="141" ek="fbillhead" fn="fsourcenumber" pn="fsourcenumber" apipn="sourceNumber" cn="来源单据编号" ctlfk="fsourcetype" visible="1124" xlsin="0" copy="0"  lock="-1">

        <!--<input group="基本信息" id="fseltypeid" el="107" ek="fbillhead" fn="fseltypeid" ctlfk="fmaterialid_d" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="460" />-->
    </div>

</body>
</html>