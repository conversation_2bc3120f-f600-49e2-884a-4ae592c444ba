{"base": "/mdl/bill.menu.json", "common": [], "listmenu": [], "billmenu": [{"id": "tbQueryBarcode", "caption": "扫码记录", "visible": "true", "disabled": "false", "style": "menu", "order": 58, "parent": "droplist_more", "opcode": "querybarcode", "param": "", "group": "standard", "entityKey": ""}, {"id": "tbQueryInventory", "caption": "库存查询", "visible": "true", "disabled": "false", "style": "menu", "order": 105, "parent": "", "opcode": "queryinventory", "param": "listMode:'lookup'", "group": "standard", "entityKey": "fentity"}]}