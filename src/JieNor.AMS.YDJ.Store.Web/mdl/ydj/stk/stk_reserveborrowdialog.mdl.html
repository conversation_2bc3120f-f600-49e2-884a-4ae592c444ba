<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_reserveborrowdialog" el="0" cn="释放借货">

    <div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="基本信息">

        <!--预留需求方信息-->
        <input el="140" ek="fbillhead" id="fdemandformid" pn="fdemandformid" cn="需求单类型" visible="0" lock="-1" />
        <input el="141" ek="fbillhead" id="fdemandbillpkid" pn="fdemandbillpkid" cn="需求单ID" visible="0" lock="-1" />
        <input el="100" ek="fbillhead" id="freservepkid" pn="freservepkid" cn="对应的预留单PKID" visible="0" lock="-1" />
    </div>

    <table id="fentry" el="52" pk="fentryid" pn="fentry" cn="需求明细">
        <tr>
            <th el="107" ek="fentry" id="fmtrlnumber" pn="fmtrlnumber" cn="商品编码" ctlfk="fmaterialid" dispfk="fnumber" width="100" visible="-1" lock="-1" lix="10"></th>
            <th el="106" ek="fentry" id="fmaterialid" pn="fmaterialid" cn="商品" refid="ydj_product" dfld="fname,fspecifica" sformid="" width="120" visible="-1" lock="-1" lix="20"></th>
            <th el="100" ek="fentry" len="2000" id="fcustomdesc" pn="fcustomdesc" cn="定制说明" width="120" visible="-1" lock="-1" lix="30"></th>
            <th el="100" ek="fentry" id="fmtono" pn="fmtono" cn="物流跟踪号" width="125" visible="-1" lock="-1" lix="40"></th>
            <th el="107" ek="fentry" id="fmtrlmodel" pn="fmtrlmodel" cn="规格型号" ctlfk="fmaterialid" dispfk="fspecifica" width="120" visible="-1" lock="-1" lix="50"></th>
            <th el="106" ek="fentry" id="fattrinfo_e" pn="fattrinfo_e" cn="辅助属性" refid="bd_auxpropvalue_ext" visible="-1" lock="-1"></th>
            <th el="132" ek="fentry" id="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" width="120" visible="0" lock="-1" lix="60"></th>
            <th el="107" ek="fentry" id="fbrandid" pn="fbrandid" cn="品牌" ctlfk="fmaterialid" dispfk="fbrandid" width="100" visible="-1" lock="-1" lix="70"></th>
            <th el="107" ek="fentry" id="fseriesid" pn="fseriesid" cn="系列" ctlfk="fmaterialid" dispfk="fseriesid" width="100" visible="-1" lock="-1" lix="80"></th>
            <th el="106" ek="fentry" id="fresultbrandid" pn="fresultbrandid" cn="业绩品牌" refid="ydj_series" sformid="" visible="-1" lock="-1" lix="90"></th>

            <th el="109" ek="fentry" id="funitid" pn="funitid" cn="基本单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" width="75" visible="1086" lock="-1" lix="100"></th>
            <th el="109" ek="fentry" id="fbizunitid" pn="fbizunitid" cn="销售单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" width="60" visible="-1" lock="-1" lix="110"></th>

            <th el="106" ek="fentry" id="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="-1" width="100" lock="-1" lix="120"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <!-- <th el="153" ek="fentry" id="fstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="-1" width="100" lock="-1" lix="130"></th> -->
            <th el="106" ek="fentry" id="fstockstatus" pn="fstockstatus" cn="库存状态" refid="ydj_stockstatus" defVal="'311858936800219137'" sformid="" dfld="fcolor" width="80" visible="-1" lock="-1" lix="140"></th>

            <th el="103" ek="fentry" id="fbizqty" pn="fbizqty" cn="预留数量" ctlfk="fbizunitid" basqtyfk="fqty" width="100" visible="-1" lock="-1" lix="150"></th>
            <th el="103" ek="fentry" id="fqty" pn="fqty" cn="基本单位预留数量" ctlfk="funitid" visible="1086" lock="-1" lix="160"></th>

            <th el="103" ek="fentry" id="fbizreleaseqty" pn="fbizreleaseqty" cn="本次需释放预留数量" ctlfk="fbizunitid" basqtyfk="freleaseqty" width="150" must="1" visible="-1" lix="170"></th>
            <th el="103" ek="fentry" id="freleaseqty" pn="freleaseqty" cn="基本单位本次需释放预留数量" ctlfk="funitid" visible="1086" lix="180"></th>

            <!-- 借贷合同 -->
            <th el="106" ek="fentry" id="fborroworderid" pn="fborroworderid" cn="借货合同" desc="引用的是商品明细行id" refid="stk_reserveborroworder" visible="-1" copy="0" must="1" lix="200"></th>
            <th el="106" ek="fentry" id="fborrowcustomerid" pn="fborrowcustomerid" cn="借货客户" refid="ydj_customer" visible="0" copy="0" must="1" lock="-1" width="100" lix="220" />
            <th el="100" ek="fentry" id="fborrowcustomername" pn="fborrowcustomername" cn="借货客户" visible="-1" copy="0" must="1" lock="-1" width="100" lix="221" />

            <th el="103" ek="fentry" id="fbizborrowsalqty" pn="fbizborrowsalqty" cn="借货商品销售数量" ctlfk="fbizunitid" basqtyfk="fborrowsalqty" width="150" visible="-1" lock="-1" lix="230"></th>
            <th el="103" ek="fentry" id="fborrowsalqty" pn="fborrowsalqty" cn="基本单位借货商品销售数量" ctlfk="funitid" visible="1086" lock="-1" lix="240"></th>

            <th el="103" ek="fentry" id="fbizborrowqty" pn="fbizborrowqty" cn="借货商品已预留量" ctlfk="fbizunitid" basqtyfk="fborrowqty" width="150" visible="-1" lock="-1" lix="250"></th>
            <th el="103" ek="fentry" id="fborrowqty" pn="fborrowqty" cn="基本单位借货商品已预留量" ctlfk="funitid" visible="1086" lock="-1" lix="260"></th>

            <th el="100" ek="fentry" id="fborroworderpkid" pn="fborroworderpkid" cn="借货合同内码" visible="0" copy="0" must="1" lix="270" />
            <th el="100" ek="fentry" id="fborroworderno" pn="fborroworderno" cn="借货合同编码" visible="0" copy="0" lix="271" />

            <!-- 预留单信息 -->
            <!-- <th el="140" ek="fentry" id="fsourceformid" pn="fsourceformid" cn="来源单类型" visible="0" copy="0" lix="280"></th>
    <th el="100" ek="fentry" id="fsourceinterid" pn="fsourceinterid" cn="来源单内码" visible="0" copy="0" lix="290"></th>
    <th el="100" ek="fentry" id="fsourceentryid" pn="fsourceentryid" cn="来源单分录内码" visible="0" copy="0" lix="300"></th>
    <th el="141" ek="fentry" id="fsourcebillno" pn="fsourcebillno" cn="来源单编号" ctlfk="fsourceformid" visible="0" copy="0" lix="310"></th> -->
            <!-- <th el="100" ek="fentry" id="freservepkid" pn="freservepkid" cn="预留单内码" visible="0" copy="0" lix="320"></th> -->
            <!-- <th el="100" ek="fentry" id="freserveentryid" pn="freserveentryid" cn="预留单分录内码" visible="0" copy="0" lix="330" /> -->

            <th el="100" ek="fentry" id="fdemandentryid" pn="fdemandentryid" cn="对应的需求单明细内码" visible="0"></th>
            <th el="100" ek="fentry" id="freserveentryid" pn="freserveentryid" cn="对应的预留单的行PKID" visible="0" lock="-1" />

        </tr>
    </table>
</body>
</html>