<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_otherstockout" basemodel="base_stkbilltmpl" el="1" cn="其它出库单" ubl="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_otherstockout" pn="fbillhead" cn="其它出库单">

        <!--基本信息-->
        <input group="基本信息" el="112" id="fdate" cn="出库日期" lix="5" must="1" />
        <input lix="1" group="基本信息" el="112" id="fplandate" fn="fplandate" cn="计划出库日期" defval="@currentshortdate" visible="-1" uaul="true" />
        <input group="基本信息" el="106" id="fstockstaffid" cn="仓管员" must="1" defVal="@currentStaffId" />
        <input group="基本信息" el="106" id="fstockdeptid" cn="仓管部门" lock="-1" must="1" defVal="@currentDeptId" />

        <!--<input group="基本信息" el="106" ek="fbillhead" id="fbasdeptid" fn="fbasdeptid" pn="fbasdeptid" cn="部门" visible="1150" refid="ydj_dept" />-->
        <!--客户信息-->
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" lix="61" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fcustomernumber" fn="fcustomernumber" pn="fcustomernumber" cn="客户编码" ctlfk="fcustomerid" dispfk="fnumber" lix="61" />


        <!--物流信息-->
        <input group="物流信息" el="106" ek="fbillhead" visible="1150" id="fcarrierid" fn="fcarrierid" pn="fcarrierid" cn="承运公司" lix="68" refid="ydj_supplier" filter="ftype='suppliertype_03'" />
        <input group="物流信息" el="100" ek="fbillhead" visible="1150" id="fshippingbillno" fn="fshippingbillno" pn="fshippingbillno" cn="运输单号" lix="69" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="领用人" refid="ydj_staff" defVal="@currentStaffId" lix="12" must="1" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="部门" refid="ydj_dept" defVal="@currentDeptId" lix="11" must="1" dfld="fnumber,fsapcostcenterno" />
        <input group="基本信息" el="107" ek="fbillhead" id="fdeptnumber" fn="fdeptnumber" pn="fdeptnumber" cn="部门编码" visible="1150" ctlfk="fdeptid" dispfk="fnumber" />
        <input group="基本信息" el="122" ek="fbillhead" visible="-1" id="ftype" fn="ftype" pn="ftype" refId="bd_enum" dfld="fenumitem" ts="" cg="出库类型" defval="''" cn="出库类型" lix="14" />
        <input group="基本信息" el="107" ek="fbillhead" id="fsapcostcenterno" fn="fsapcostcenterno" pn="fsapcostcenterno" cn="SAP成本中心编码" visible="1150" ctlfk="fdeptid" dispfk="fsapcostcenterno" />


        <input group="基本信息" el="100" ek="fbillhead" id="fdeliveryman" fn="fdeliveryman" pn="fdeliveryman" cn="配送员" visible="1150" />
        <input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" cn="创建人" visible="-1" copy="0" xlsin="0" lix="2" />
        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" xlsin="0" lix="3" />
        <input group="基本信息" el="116" ek="fbillhead" id="fispdatask" fn="fispdatask" pn="fispdatask" visible="1150" cn="是否PDA作业" lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <input group="基本信息" el="116" ek="fbillhead" id="fcreatescantask" fn="fcreatescantask" pn="fcreatescantask" visible="1150" cn="已生成PDA扫描任务" lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstore" fn="fstore" pn="fstore" cn="门店名称" notrace="false" refid="bas_store" canchange="true" lix="75" defls="true" />
        <input group="基本信息" el="113" ek="fbillhead" visible="0" id="fhqderdate" fn="fhqderdate" pn="fhqderdate" cn="提交总部时间" copy="0" lock="-1" lix="75" />
        <input group="基本信息" el="116" ek="fbillhead" visible="0" id="fhqderstatus" fn="fhqderstatus" pn="fhqderstatus" cn="是否提交总部" copy="0" lock="-1" lix="75" />
        <!--<input group="基本信息" ek="fbillhead" el="152" id="fstocktype" fn="fstocktype" pn="fstocktype" cn="出库仓库" vals="'01':'总仓','02':'门店仓'" visible="1150" copy="0" />-->
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcostcenterid" fn="fcostcenterid" pn="fcostcenterid" cn="成本中心" notrace="false" refid="ydj_zycostcenter" canchange="true" lix="75" defls="true" />-->
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstorehouseid_h" fn="fstorehouseid_h" pn="fstorehouseid_h" cn="出库仓库" notrace="false" refid="ydj_storehouse" canchange="true" lix="75" defls="true" filter="fname!='直发仓库'" copy="0" />
    </div>

    <!--出库商品（明细）-->
    <table id="fentity" el="52" pk="fentryid" tn="t_stk_otherstockoutentry" pn="fentity" cn="出库明细" kfks="fmaterialid,fqty">
        <tr>

            <!--以下字段暂时锁定-->
            <th el="100" ek="fentity" id="flotno" cn="批号" lock="-1" must="0"></th>
            <th el="100" ek="fentity" id="fmtono" cn="物流跟踪号" lock="0" must="0"></th>
            <th el="149" ek="fentity" id="fownertype" cn="货主类型" lock="0" must="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th lix="40" el="132" ek="fentity" id="fattrinfo" cn="辅助属性" nstdfk="funstdtype" visible="1150" lock="0" copy="0" width="120"></th>
            <th el="150" ek="fentity" id="fownerid" cn="货主" lock="0" must="0"></th>

            <th el="109" ek="fentity" id="fbizunitid" cn="出库单位"></th>

            <th el="103" ek="fentity" id="fplanqty" cn="基本单位应发数量"></th>
            <th el="103" ek="fentity" id="fbizplanqty" cn="应发数量"></th>

            <th el="103" ek="fentity" id="fqty" cn="基本单位实发数量" lock="-1"></th>
            <th el="103" ek="fentity" id="fbizqty" cn="实发数量" must="1"></th>

            <th el="104" ek="fentity" id="fprice" cn="单价" must="0"></th>
            <th el="105" ek="fentity" id="famount" cn="金额" must="0"></th>
            <!--慕思新增字段，标准定制非标定制-->
            <th lix="270" el="116" ek="fentity" visible="1150" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="fmaterialid" dispfk="funstdtype" width="90" copy="0" lock="0" canchange="true" refValueType="116"></th>
            <th lix="290" el="100" ek="fentity" visible="1150" id="fpartscombnumber" fn="fpartscombnumber" pn="fpartscombnumber" cn="配件组合号" lock="-1"></th>
            <th lix="300" el="107" ek="fentity" visible="1150" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" ctlfk="fmaterialid" dispfk="fselcategoryid" lock="-1"></th>
            <th lix="310" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>
            <th lix="320" el="116" ek="fentity" id="fdostandard" fn="fdostandard" pn="fdostandard" cn="已转标准品" visible="1150" lock="-1" defval="false"></th>

            <th lix="200" el="103" ek="fentity" id="fbizpackqty" fn="fbizpackqty" ts="" visible="1150" cn="实发包数" lock="-1" copy="0"></th>

            <th el="107" ek="fentity" id="fisnofifostock" fn="fisnofifostock" pn="fisnofifostock" visible="0" cn="不参与自动推荐" width="80" align="center" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstorehouseid" dispfk="fisnofifostock" refvt="116"></th>
            <!--单位成本，总成本-->
            <th group="出库明细" el="105" ek="fentity" id="fcostprice" fn="fcostprice" pn="fcostprice" cn="单位成本(加权平均)" />
            <th group="出库明细" el="105" ek="fentity" id="fcostamt" fn="fcostamt" pn="fcostamt" cn="总成本(加权平均)" />

            <th el="100" lix="299" ek="fentity" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1086" cn="体积单位" lock="-1" must="0" uaul="true"></th>
            <th el="102" lix="300" ek="fentity" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1086" cn="总体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th el="102" lix="301" ek="fentity" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1086" cn="单位体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="部门和客户不能同时为空" data="{'expr':'(fdeptid!=\'\' and fdeptid!=\' \') or (fcustomerid!=\'\' and fcustomerid!=\' \')','message':'部门和客户不能同时为空！'}"></li>
            <!--<li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fmaterialid'}"></li>-->

            <li el="17" sid="1002" cn="反写售后维修单其它出库数量" data="{
                'sourceFormId':'aft_repairorder',
                'sourceControlFieldKey':'fbizoutstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fsourcetype=\'aft_repairorder\'',
                'writebackFieldKey':'fbizoutstockqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'fbizoutstockqty&gt;fbizqty',
                'excessMessage':'其他出库数量不允许超过维修数量！'
                }"></li>


            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>
        <ul el="10" id="submit" op="submit" opn="提交">
            <li el="11" vid="510" cn="部门和客户不能同时为空" data="{'expr':'(fdeptid!=\'\' and fdeptid!=\' \') or (fcustomerid!=\'\' and fcustomerid!=\' \')','message':'部门和客户不能同时为空！'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="11" vid="510" cn="部门和客户不能同时为空" data="{'expr':'(fdeptid!=\'\' and fdeptid!=\' \') or (fcustomerid!=\'\' and fcustomerid!=\' \')','message':'部门和客户不能同时为空！'}"></li>

            <li el="17" sid="2000" cn="审核时更新库存" data="{'factor':-1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写其它出库申请单出库数量" data="{
                'sourceFormId':'stk_otherstockoutreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutstockqty&gt;fqty',
                'excessMessage':'其它出库数量不允许超过出库申请数量！'
                }"></li>

            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'message':'其它出库单审核，自动释放预留数量',
                'preCondition':'',
                'releaseWay':6,
                'releaseType':0,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'fcustomerid',
                'deptFieldKey':'fdeptid',
                'staffFieldKey':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <!--如果是已经归档过，那反审核时自动触发更新预留，生成预留占用，不再需要再手动保存。-->
            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
            <li el="17" sid="2000" cn="反审核时更新库存" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus!=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写其它出库申请单出库数量" data="{
                'sourceFormId':'stk_otherstockoutreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutstockqty&gt;fqty',
                'excessMessage':'其它出库数量不允许超过出库申请数量！'
                }"></li>

            <li el="17" sid="2002" cn="取消释放预留数量" data="{
                'message':'其它出库单反审核，自动取消释放预留数量',
                'preCondition':'',
                'releaseWay':6,
                'releaseType':1,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'',
                'SourceNumberFieldKey':'',
                'customerFieldKey':'fcustomerid',
                'deptFieldKey':'fdeptid',
                'staffFieldKey':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="17" sid="2000" cn="删除时更新库存" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus!=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写其它出库申请单出库数量" data="{
                'sourceFormId':'stk_otherstockoutreq',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutstockqty&gt;fqty',
                'excessMessage':'其它出库数量不允许超过出库申请数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写售后维修单其它出库数量为0" data="{
                'sourceFormId':'aft_repairorder',
                'sourceControlFieldKey':'fbizoutstockqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fsourcetype=\'aft_repairorder\'',
                'writebackFieldKey':'fbizoutstockqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>

            <li el="17" sid="2006" cn="删除预留" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" id="cancel" op="cancel" opn="作废">
            <li el="17" sid="2006" cn="删除预留" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" id="uncancel" op="uncancel" opn="反作废">
            <li el="17" sid="2003" cn="更新预留" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
        </ul>

        <ul el="10" id="createscantask" op="createscantask" opn="生成发货任务" data="" permid="" ubl="1"></ul>
    </div>
</body>
</html>