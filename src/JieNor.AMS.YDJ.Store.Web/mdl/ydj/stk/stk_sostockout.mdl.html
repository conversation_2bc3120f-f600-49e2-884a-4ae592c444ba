<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_sostockout" basemodel="base_stkbilltmpl" el="1" cn="销售出库单" ubl="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_sostockout" pn="fbillhead" cn="销售出库单">

        <!--基本信息-->
        <input group="基本信息" el="123" ek="fbillhead" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" visible="1150" refid="bd_billtype" lix="10" width="90" must="1" />
        <input group="基本信息" el="100" ek="FBillHead" id="fstockaddress" fn="fstockaddress" pn="fstockaddress" visible="1150" cn="仓管员地址"
               lock="0" copy="1" lix="42" notrace="true" ts="" />


        <input group="基本信息" el="106" id="fstockstaffid" cn="仓管员" dfld="fname,fphone" must="1" />
        <input group="基本信息" el="106" id="fstockdeptid" cn="仓管部门" lock="-1" must="1" />
        <input group="基本信息" el="100" ek="FBillHead" id="fstockstaffphone" fn="fstockstaffphone" pn="fstockstaffphone" visible="-1" cn="仓管员联系方式"
               lock="0" copy="1" lix="41" notrace="true" ts="" />
        <input group="基本信息" el="141" ek="fbillhead" visible="-1" id="fsourcenumber" lix="45" />
        <!--客户信息-->
        <input group="基本信息" el="112" id="fdate" cn="出库日期" lix="1" />
        <input lix="1" group="基本信息" el="112" id="fplandate" fn="fplandate" cn="计划出库日期" defval="@currentshortdate.AddDays(1)" visible="-1" uaul="true" />
        <input group="基本信息" el="107" ek="fbillhead" visible="1150" id="fcustomernumber" fn="fcustomernumber" pn="fcustomernumber" cn="客户编码" ctlfk="fcustomerid" dispfk="fnumber" lix="1" />
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" lix="2" must="1" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fconsignee" fn="fconsignee" pn="fconsignee" cn="收货人" lix="3" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fphone" fn="fphone" pn="fphone" cn="收货人电话" lix="5" must="1" />

        <input group="基本信息" el="106" ek="fbillhead" id="fsostaffid" fn="fsostaffid" pn="fsostaffid" cn="销售员" visible="1150" refid="ydj_staff" />
        <input group="基本信息" el="107" ek="fbillhead" id="fsodeptnumber" fn="fsodeptnumber" pn="fsodeptnumber" cn="部门编码" visible="1150" ctlfk="fsodeptid" dispfk="fnumber" />
        <input group="基本信息" el="106" ek="fbillhead" id="fsodeptid" fn="fsodeptid" pn="fsodeptid" cn="销售部门" visible="1150" refid="ydj_dept" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="64"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="65"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lix="66"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="faddress" fn="faddress" pn="faddress" cn="收货人地址" lix="67" must="1" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fhistorystaffer" fn="fhistorystaffer" pn="fhistorystaffer" cn="历史销售员" lix="67" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fwithin" fn="fwithin" pn="fwithin" cn="手工单号" width="150" />

        <!--源单类型-->
        <input group="基本信息" el="140" ek="fbillhead" visible="0" id="fsourcetype" lix="50" />

        <!--物流信息-->
        <input group="物流信息" el="106" ek="fbillhead" visible="1150" id="fcarrierid" fn="fcarrierid" pn="fcarrierid" cn="承运公司" lix="68" refid="ydj_supplier" filter="ftype='suppliertype_03'" />
        <input group="物流信息" el="100" ek="fbillhead" visible="1150" id="fshippingbillno" fn="fshippingbillno" pn="fshippingbillno" cn="运输单号" lix="69" />

        <input type="text" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" cn="创建人" visible="-1" copy="0" xlsin="0" lix="1" />
        <!--二级分销相关-->
        <input el="116" ek="fbillhead" visible="1150" id="fisresellorder" fn="fisresellorder" pn="fisresellorder" cn="二级分销合同" lock="-1" copy="0" width="100" />
        <!--终端客户-->
        <input group="终端客户" el="106" ek="fbillhead" visible="32" id="fterminalcustomer" fn="fterminalcustomer" pn="fterminalcustomer" cn="终端客户(协同)" refid="ydj_customer"
               width="90" copy="0" lock="0" dfld="fphone,fcontacts,faddress,fnumber" />
        <input group="终端客户" el="100" ek="fbillhead" visible="32" id="fcoophone" fn="fcoophone" cn="终端收货人电话" width="80" copy="0" lock="0" />
        <input group="终端客户" el="100" ek="fbillhead" visible="1150" id="fterminalcustomernumber" fn="fterminalcustomernumber" cn="终端客户编码" width="80" copy="0" lock="0" />
        <select group="终端客户" groupType="city2" el="122" ek="fbillhead" visible="1150" id="fprovince_c" fn="fprovince_c" pn="fprovince_c" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="10" apipn="province" canchange="true"></select>
        <select group="终端客户" groupType="city2" el="122" ek="fbillhead" visible="1150" id="fcity_c" fn="fcity_c" pn="fcity_c" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="11" apipn="city" canchange="true"></select>
        <select group="终端客户" groupType="city2" el="122" ek="fbillhead" visible="-1" id="fregion_c" fn="fregion_c" pn="fregion_c" cn="地区" cg="区" refid="bd_enum" dfld="fenumitem" apipn="region" lix="12" canchange="true"></select>
        <input group="终端客户" el="100" ek="fbillhead" visible="1150" id="fcontacts_c" fn="fcontacts_c" pn="fcontacts_c" cn="联系人" lix="13" lock="0" />
        <input group="终端客户" el="100" ek="fbillhead" visible="32" id="fcooaddress" fn="fcooaddress" cn="详细地址(协同)" width="180" copy="0" lock="0" />
        <!--<input group="基本信息" el="106" ek="fbillhead" id="finstallerid" fn="finstallerid" pn="finstallerid" cn="安装员" visible="1150" refid="ydj_staff" />-->
        <input group="基本信息" el="131" ek="fbillhead" id="finstallerid" fn="finstallerid" pn="finstallerid" cn="安装员" visible="1150" refid="ydj_staff" uaul="true" />
        <input group="基本信息" el="100" ek="fbillhead" id="fdrivername" fn="fdrivername" pn="fdrivername" cn="司机姓名" visible="1150" uaul="true" />
        <input group="基本信息" el="100" ek="fbillhead" id="fdeliveryman" fn="fdeliveryman" pn="fdeliveryman" cn="配送员" visible="1150" uaul="true" />


        <input group="协同信息" el="152" ek="fbillhead" id="fmusisyncstatus" fn="fmusisyncstatus" pn="fmusisyncstatus" visible="0" cn="慕思协同状态" copy="0" ts="" vals=" '01':'待协同','02':'协同成功','03':'协同失败'" defval="'01'" lix="8" />
        <input group="协同信息" el="113" ek="fbillhead" type="datetime" id="fmusisyncdate" fn="fmusisyncdate" pn="fmusisyncdate" ts="" visible="0" cn="慕思协同日期" lix="6" copy="0" />

        <input group="基本信息" el="116" ek="fbillhead" id="fispdatask" fn="fispdatask" pn="fispdatask" visible="1150" cn="是否PDA作业" lock="-1" copy="0" lix="70" notrace="true" ts="" />
        <select group="基本信息" el="152" ek="fbillhead" id="fhomedelivery" fn="fhomedelivery" pn="fhomedelivery" visible="-1" copy="0" cn="是否送货上门" vals=" '01':'是','02':'否'" defval=""></select>

        <input group="基本信息" el="116" ek="fbillhead" id="fcreatescantask" fn="fcreatescantask" pn="fcreatescantask" visible="1150" cn="已生成PDA扫描任务" lock="-1" copy="0" lix="70" notrace="true" ts="" />

        <input group="基本信息" el="116" ek="fbillhead" id="frenewalflag" fn="frenewalflag" pn="frenewalflag" visible="1150" cn="焕新订单标记" lock="-1" copy="0" notrace="false" ts="" defval="false" />
        <!-- 一件代发 -->
        <input group="基本信息" el="116" ek="fbillhead" id="fpiecesendtag" fn="fpiecesendtag" pn="fpiecesendtag" visible="1150" cn="一件代发标记" copy="1" lix="500" notrace="false" ts="" defval="false" canchange="true" lock="-1" />
        <input group="基本信息" el="116" ek="fbillhead" id="fisautopush" fn="fisautopush" pn="fisautopush" visible="0" cn="一件代发采购订单自动下推标记" copy="1" lix="500" notrace="false" ts="" defval="false" canchange="true" lock="-1" />
        <input group="基本信息" el="113" ek="fbillhead" visible="0" id="fhqderdate" fn="fhqderdate" pn="fhqderdate" cn="提交总部时间" copy="0" lock="-1" />
        <input group="基本信息" el="116" ek="fbillhead" visible="0" id="fhqderstatus" fn="fhqderstatus" pn="fhqderstatus" cn="是否提交总部" copy="0" lock="-1" />
    </div>

    <!--出库商品（明细）-->
    <table id="fentity" el="52" pk="fentryid" tn="t_stk_sostockoutentry" pn="fentity" cn="出库明细" kfks="fmaterialid">
        <tr>
            <th lix="100" el="106" ek="fentity" id="fmaterialid" cn="商品" dfld="fname,fspecifica,fvolume,fgrossload,fpacksize"></th>
            <th lix="105" el="107" ek="fentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0"></th>
            <th lix="110" el="107" ek="fentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0"></th>
            <th lix="112" el="132" ek="fentity" id="fattrinfo" fn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" pricefk="" width="160" visible="1150"></th>
            <th lix="114" ek="fentity" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="fmaterialid" width="200" visible="1150"></th>
            <th lix="115" el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" ctlfk="fmaterialid" dispfk="fbrandid" sformid="" width="100" lock="-1"></th>
            <th lix="120" el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" ctlfk="fmaterialid" dispfk="fseriesid" sformid="" width="100" lock="-1"></th>
            <th lix="120" el="109" ek="fentity" id="fbizunitid" cn="销售单位" notrace="false"></th>
            <th lix="125" el="103" ek="fentity" id="fbizplanqty" cn="应发数量" lock="-1" notrace="false"></th>
            <th lix="135" el="103" ek="fentity" id="fbizqty" cn="实发数量" notrace="false"></th>
            <th lix="140" el="109" ek="fentity" id="fstockunitid" fn="fstockunitid" cn="库存单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" visible="1150" width="100" must="1" notrace="false" lock="-1"></th>
            <th lix="145" el="109" ek="fentity" id="funitid" fn="funitid" cn="基本单位" ctlfk="fmaterialid" refid="ydj_unit" sformid="" filter="fisbaseunit='1'" visible="1150" width="80" must="1" lock="-1" notrace="false"></th>
            <th lix="150" el="106" ek="fentity" id="fstorehouseid" fn="fstorehouseid" cn="仓库" refid="ydj_storehouse" sformid="" visible="1150" width="100" must="2" filter="fname!='直发仓库'"></th>
            <!--基础资料分录字段，控制字段指向仓库，仓库上有个分录标识为fentity的仓位值集，此字段将仓位值集虚拟成普通基础资料-->
            <th lix="155" el="153" ek="fentity" id="fstorelocationid" fn="fstorelocationid" cn="仓位" ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1150" width="100"></th>
            <th lix="160" el="106" ek="fentity" id="fstockstatus" fn="fstockstatus" pn="fstockstatus" visible="1150" cn="库存状态" notrace="true" refid="ydj_stockstatus" defVal="'311858936800219137'" dfld="fcolor" lock="-1"></th>
            <th lix="165" el="108" ek="fentity" id="fsoorderno" fn="fsoorderno" pn="fsoorderno" visible="1150" cn="销售订单编号"
                lock="-1" copy="0" notrace="true" ts=""></th>
            <th lix="166" el="116" ek="fentity" id="fisgiveaway" fn="fisgiveaway" pn="fisgiveaway" cn="赠品" visible="32" lock="-1"></th>
            <th lix="500" el="100" ek="fentity" id="fentrynote" fn="fentrynote" pn="fentrynote" cn="备注" len="255" visible="1150" width="160"></th>
            <!--用于区分商品行是否为复制行-->
            <th lix="800" el="116" ek="fentity" visible="0" id="fiscopy" fn="fiscopy" pn="fiscopy" cn="是否复制行" lock="-1"></th>

            <th ek="fentity" lix="12" el="107" id="fattribute" fn="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fattribute" refvt="0"></th>
            <!--以下字段暂时锁定-->
            <th el="100" ek="fentity" id="flotno" cn="批号" lock="-1" must="0" visible="1086"></th>
            <th el="100" ek="fentity" id="fmtono" cn="物流跟踪号" lock="-1" must="0" visible="1086"></th>
            <th el="149" ek="fentity" id="fownertype" cn="货主类型" lock="0" must="0" visible="1086">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th el="150" ek="fentity" id="fownerid" cn="货主" lock="0" must="0" visible="1086"></th>

            <th el="100" ek="fentity" len="2000" id="fcustomdesc" fn="fcustomdesc" cn="定制说明" lix="90" width="160" visible="1086"></th>

            <th el="103" ek="fentity" id="fplanqty" cn="基本单位应发数量" visible="1086"></th>


            <th el="103" ek="fentity" id="fqty" cn="基本单位实发数量" visible="1086" lock="-1"></th>


            <th lix="125" el="103" ek="fentity" id="fapplyqty" fn="fapplyqty" pn="fapplyqty" cn="基本单位已排单申请数量" ctlfk="funitid" visible="1086" width="90" lock="-1" copy="0" desc="由下游发货排单申请单记录.业务场景=发货排单申请单保存反写生成" apipn="purQty"></th>
            <th lix="125" el="103" ek="fentity" id="fbizapplyqty" fn="fbizapplyqty" pn="fbizapplyqty" cn="已排单申请数量" basqtyfk="fapplyqty" ctlfk="fbizunitid" visible="1086" width="90" lock="-1" copy="0" desc="由下游发货排单申请单记录.业务场景=发货排单申请单保存反写生成" apipn="bizPurQty"></th>

            <th el="103" ek="fentity" id="freturnnoticeqty" fn="freturnnoticeqty" pn="freturnnoticeqty" visible="1086" cn="基本单位退货通知数量" width="150"
                lock="-1" copy="0" lix="158" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizreturnnoticeqty" fn="fbizreturnnoticeqty" pn="fbizreturnnoticeqty" visible="1086" cn="退货通知数量" width="120"
                lock="-1" copy="0" lix="158" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="freturnnoticeqty" roundType="0" format="0,000.00"></th>

            <th el="103" ek="fentity" id="freturnqty" fn="freturnqty" pn="freturnqty" visible="1086" cn="基本单位退货数量" width="120"
                lock="-1" copy="0" lix="160" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizreturnqty" fn="fbizreturnqty" pn="fbizreturnqty" visible="1150" cn="退货数量" width="100"
                lock="-1" copy="0" lix="160" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="freturnqty" roundType="0" format="0,000.00"></th>


            <th el="108" ek="fentity" id="fsoorderinterid" fn="fsoorderinterid" pn="fsoorderinterid" visible="0" cn="销售订单内码"
                lock="-1" copy="0" lix="231" notrace="true" ts=""></th>
            <th el="108" ek="fentity" id="fsoorderentryid" fn="fsoorderentryid" pn="fsoorderentryid" visible="0" cn="销售订单分录内码"
                lock="-1" copy="0" lix="232" notrace="true" ts=""></th>

            <th el="112" ek="FEntity" id="forderdate" fn="forderdate" pn="forderdate" visible="1086" cn="业务日期"
                lock="-1" copy="0" lix="235" notrace="true" ts="">业务日期</th>

            <th el="103" ek="fentity" id="forderqty" fn="forderqty" pn="forderqty" visible="1086" cn="基本单位订单数量" width="120"
                lock="-1" copy="0" lix="238" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizorderqty" fn="fbizorderqty" pn="fbizorderqty" visible="1086" cn="销售订单数量" width="100"
                lock="-1" copy="0" lix="240" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="forderqty" roundType="0" format="0,000.00"></th>

            <!--协同信息-->
            <th el="116" ek="FEntity" id="fsyncconfirm" fn="fsyncconfirm" ts="" cn="协同确认" visible="1150" copy="0">协同确认</th>

            <th el="103" ek="fentity" lix="245" lock="-1" id="fdeliverypackageqty" fn="fdeliverypackageqty" pn="fdeliverypackageqty" cn="销售实发包数" visible="1150"></th>
            <th el="103" ek="fentity" lix="246" lock="-1" id="freturnpackageqty" fn="freturnpackageqty" pn="freturnpackageqty" cn="销售退货包数" visible="1150"></th>

            <th el="104" ek="fentity" id="fprice" fn="fprice" pn="fprice" cn="成交单价" lix="135" width="70" lock="-1" visible="1086" format="0,000.000000" notrace="false"></th>
            <th el="105" ek="fentity" id="famount" fn="famount" pn="famount" cn="成交金额" lix="136" width="90" lock="-1" visible="1086" format="0,000.00" notrace="false"></th>

            <th lix="310" el="104" ek="fentity" id="fprice_e" fn="fprice_e" pn="fprice_e" cn="零售价" width="70" visible="1086" apipn="price" format="0,000.000000" dformat="0,000.00"></th>
            <th lix="315" el="104" ek="fentity" id="fhqprice" fn="fhqprice" pn="fhqprice" cn="总部零售价" width="100" visible="1086" lock="-1" format="0,000.000000" dformat="0,000.00"></th>
            <th id="fseltypeid" el="107" ek="fentity" fn="fseltypeid" ctlfk="fmaterialid" dispfk="fseltypeid" ts="" cn="型号" visible="1086" lix="320"></th>

            <th el="104" ek="fentity" id="fterprice" fn="fterprice" pn="fterprice" cn="终端零售价" width="70" visible="1150" apipn="fterprice" canchange="true" lock="-1"></th>
            <th el="105" ek="fentity" id="fteramount" fn="fteramount" pn="fteramount" cn="终端金额" width="90" visible="1150" apipn="fteramount" canchange="true" lock="-1"></th>

            <th el="107" ek="fentity" id="fisnofifostock" fn="fisnofifostock" pn="fisnofifostock" visible="0" cn="不参与自动推荐" width="80" align="center" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstorehouseid" dispfk="fisnofifostock" refvt="116"></th>
            <!--单位成本，总成本-->
            <th group="出库明细" el="105" ek="fentity" id="fcostprice" fn="fcostprice" pn="fcostprice" cn="单位成本(加权平均)" />
            <th group="出库明细" el="105" ek="fentity" id="fcostamt" fn="fcostamt" pn="fcostamt" cn="总成本(加权平均)" />

            <!--【采购折前金额】=【采购单价（折前）】*【销售数量】-->
            <th lix="500" el="105" ek="fentity" id="fpurfacprice" fn="fpurfacprice" pn="fpurfacprice" cn="采购单价（折前）" visible="-1" copy="0" notrace="false" uaul="true" roundType="0" format="0,000.00"></th>
            <th lix="501" el="105" ek="fentity" id="fpurfacamount" fn="fpurfacamount" pn="fpurfacamount" cn="采购折前金额" visible="-1" copy="0" notrace="false" uaul="true" roundType="0" format="0,000.00"></th>
            <!-- 三维家 -->
            <th lix="305" el="100" ek="fentity" id="ffactorybillid" fn="ffactorybillid" pn="ffactorybillid" cn="工厂订单ID" visible="0" lock="-1"></th>
            <th lix="306" el="100" ek="fentity" id="ffactorybillno" fn="ffactorybillno" pn="ffactorybillno" cn="工厂订单号" visible="1086" lock="-1"></th>

            <th el="100" lix="299" ek="fentity" id="fvolumeunit" fn="fvolumeunit" pn="fvolumeunit" visible="1086" cn="体积单位" lock="-1" must="0" uaul="true"></th>
            <th el="102" lix="300" ek="fentity" id="ftotalvolume" fn="ftotalvolume" pn="ftotalvolume" visible="1086" cn="总体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th el="102" lix="301" ek="fentity" id="fsinglevolume" fn="fsinglevolume" pn="fsinglevolume" visible="1086" cn="单位体积" lock="-1" copy="0" ts="" format="0,000.000" width="150" uaul="true"></th>
            <th lix="500" el="122" ek="fentity" visible="1086" id="fdeliverytype" fn="fdeliverytype" pn="fdeliverytype" cn="交货方式" cg="一件代发交货方式" apipn="deliverytype" refid="bd_enum" dfld="fenumitem" defval="''" canchange="true" lock="-1"></th>

            <th lix="260" el="108" ek="fentity" id="ffeedbackinterid" fn="ffeedbackinterid" pn="ffeedbackinterid" ts="" cn="售后反馈单内码" visible="0"
                lock="-1" copy="0"></th>
            <th lix="265" el="108" ek="fentity" id="ffeedbackentryid" fn="ffeedbackentryid" pn="ffeedbackentryid" ts="" cn="售后反馈单分录内码" visible="0" lock="-1" copy="0"></th>

        </tr>
    </table>

    <!--包件明细-->
    <table id="fpackageentry" el="52" pk="fentryid" tn="t_stk_sostockoutpackageentry" pn="fpackageentry" cn="包件明细">
        <tr>
            <th el="100" ek="fpackageentry" id="flinkmtrlentryid" fn="flinkmtrlentryid" ts="" cn="关联商品分录Id" visible="0" lock="-1" width="200"></th>
            <th el="100" ek="fpackageentry" id="flinkmtrlname" fn="flinkmtrlname" ts="" cn="关联商品名称" visible="96" lock="-1" width="200"></th>
            <th el="100" ek="fpackageentry" id="fpackageitemname" fn="fpackageitemname" ts="" cn="包件名称" visible="96" lock="-1" width="200"></th>
            <th el="100" ek="fpackageentry" id="fpackagebarcode" fn="fpackagebarcode" ts="" cn="包件条码" visible="96" lock="-1" width="200"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <!--<li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fmaterialid'}"></li>-->
            <li el="17" sid="2010" cn="反写销售合同出库数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fcancelstatus=\'0\'',
                'writebackFieldKey':'foutqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutqty&gt;fqty+freturnqty+foutqty',
                'excessMessage':'销售出库数量不允许超过订单数量+退换数量+出库数量！'
                }"></li>

            <li el="17" sid="2003" cn="更新预留，同时上游业务的预留转出" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes_e'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
            <!--<li el="17" sid="1002" cn="反写销售合同已转出库数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'ftransoutqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="17" sid="2000" cn="审核时更新库存" data="{'factor':-1,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写销售合同出库数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutqty&gt;fqty+freturnqty',
                'excessMessage':'销售出库数量不允许超过订单数量+退换数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写销售合同已转出库数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'ftransoutqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'foutqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>

            <li el="17" sid="1002" cn="反写发货通知单出库数量" data="{
                'sourceFormId':'sal_deliverynotice',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutstockqty&gt;fqty',
                'excessMessage':'销售出库数量不允许超过发货数量！'
                }"></li>

            <li el="17" sid="2002" cn="自动释放预留数量" data="{
                'message':'销售出库单审核，自动释放预留数量',
                'preCondition':'',
                'releaseWay':6,
                'releaseType':0,
                'activeEntityKey':'fentity',
                'SourceTypeFieldKey':'ydj_order',
                'SourceNumberFieldKey':'fsoorderno',
                'customerFieldKey':'fcustomerid',
                'deptFieldKey':'',
                'staffFieldKey':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>


            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
        'executeType':'add'
        }"></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="2003" cn="更新预留，同时上游业务的预留转出" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes_e'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
            <li el="17" sid="2000" cn="反审核时更新库存" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus!=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写销售合同出库数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutqty&gt;fqty+freturnqty',
                'excessMessage':'销售出库数量不允许超过订单数量+退换数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写发货通知单出库数量" data="{
                'sourceFormId':'sal_deliverynotice',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutstockqty&gt;fqty',
                'excessMessage':'销售出库数量不允许超过发货数量！'
                }"></li>

            <li el="17" sid="2002" cn="取消释放预留数量" data="{
            'message':'销售出库单反审核，自动取消释放预留数量',
            'preCondition':'',
            'releaseWay':6,
            'releaseType':1,
            'activeEntityKey':'fentity',
            'SourceTypeFieldKey':'ydj_order',
            'SourceNumberFieldKey':'fsoorderno',
            'customerFieldKey':'fcustomerid',
            'deptFieldKey':'',
            'staffFieldKey':'',
            'qtyFieldKey':'fqty',
            'flexMaps':[
                {'id':'fmaterialid','srcFieldId':'fmaterialid','isStrictMatch':true},
                {'id':'fattrinfo','srcFieldId':'fattrinfo','isStrictMatch':true},
                {'id':'fcustomdesc','srcFieldId':'fcustomdesc','isStrictMatch':true},
                {'id':'funitid','srcFieldId':'funitid'},
                {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                {'id':'fmtono','srcFieldId':'fmtono'}
            ]}"></li>

            <li el="11" vid="511" cn="下推了回访单后不可以反审核" data='{
                "expr": [
                    {"linkFormId":"ydj_vist","linkFieldKey":"fsourcenumber","sourceLinkFieldKey":"fbillno"}
                ],
                "message":"已经生成了下游单据，不允许反审核！"
                }'></li>

            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
    'executeType':'remove'
    }"></li>-->
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="17" sid="2000" cn="删除时更新库存" data="{'factor':0,'activeEntityKey':'fentity',
                'invFlexFieldSetting':{},
                'qtyFieldKey':'fqty',
                'stockQtyFieldKey':'fstockqty',
                'amountFieldKey':'famount',
                'costAmtFieldKey':'',
                'preCondition':'fstatus!=\'c\''}"></li>

            <li el="17" sid="1002" cn="反写销售合同出库数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutqty&gt;fqty+freturnqty',
                'excessMessage':'销售出库数量不允许超过订单数量+退换数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写发货通知单出库数量" data="{
                'sourceFormId':'sal_deliverynotice',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'foutstockqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutstockqty&gt;fqty',
                'excessMessage':'销售出库数量不允许超过发货数量！'
                }"></li>

            <li el="11" vid="511" cn="下推了回访单后不可以删除" data='{
                "expr": [
                    {"linkFormId":"ydj_vist","linkFieldKey":"fsourcenumber","sourceLinkFieldKey":"fbillno"}
                ],
                "message":"已经生成了下游单据，不允许删除！"
                }'></li>

            <li el="17" sid="2006" cn="删除对应的预留，同时预留转回到上游业务" data="{
                'preCondition':'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes_e'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>

            <li el="17" sid="1002" cn="反写销售合同已转出库数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'ftransoutqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>

        <ul el="10" id="uncancel" op="uncancel" opn="反作废">
            <li el="11" vid="3010" cn="stk_sostockout校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="2003" cn="更新预留，同时上游业务的预留转出" data="{
                'preCondition':'fstatus!=\'D\' and fstatus!=\'E\'',
                'qtyFieldKey':'fqty',
                'flexMaps':[
                    {'id':'fmaterialid','srcFieldId':'fmaterialid'},
                    {'id':'fattrinfo','srcFieldId':'fattrinfo'},
                    {'id':'fcustomdesc','srcFieldId':'fcustomdes_e'},
                    {'id':'funitid','srcFieldId':'funitid'},
                    {'id':'fbizunitid','srcFieldId':'fbizunitid'},
                    {'id':'fstorehouseid','srcFieldId':'fstorehouseid'},
                    {'id':'fstorelocationid','srcFieldId':'fstorelocationid'},
                    {'id':'fstockstatus','srcFieldId':'fstockstatus'},
                    {'id':'fmtono','srcFieldId':'fmtono'}
                ]}"></li>
            <li el="17" sid="2010" cn="反写销售合同出库数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fcancelstatus=\'0\'',
                'writebackFieldKey':'foutqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'foutqty&gt;fqty+freturnqty+foutqty',
                'excessMessage':'销售出库数量不允许超过订单数量+退换数量+出库数量！'
                }"></li>
            <li el="17" sid="1002" cn="反写销售合同已转出库数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'ftransoutqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="1002" cn="反写销售合同已转出库数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'ftransoutqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'fcancelstatus=\'0\'',
                'excessMessage':''
                }"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="submit" op="submit" opn="提交">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unsubmit" op="unsubmit" opn="撤销">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="511" cn="进行了退货后不可以反审核" data='{"expr": [{"linkFormId":"sal_returnnotice","linkFieldKey":"fsooutstockinterid"},
                {"linkFormId":"stk_sostockreturn","linkFieldKey":"fsooutstockinterid"}],"message":"已经生成了下游单据，不允许反审核！"}'></li>
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="11" vid="3012" cn="反审核增加校验如果对应的《发货扫描任务》单据头【任务状态】=”已完成”时, 不允许反审核" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="1002" cn="反写销售合同已转出库数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'ftransoutqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>

        <ul el="10" id="pushservice" op="pushservice" opn="新增服务" data="{'parameter':{'ruleId':'stk_sostockout2ydj_service'}}" permid="fw_pushservice" ubl="1"></ul>

        <ul el="10" id="createscantask" op="createscantask" opn="生成发货任务" data="" permid="" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="savesubmit" op="savesubmit" opn="保存并提交" data="" permid="fw_savesubmit"></ul>
        <ul el="10" ek="fbillhead" id="saveaudit" op="saveaudit" opn="保存并审核" data="" permid="fw_saveaudit"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_pushservice" cn="新增服务"></ul>
        <ul el="12" id="fw_savesubmit" cn="保存并提交"></ul>
        <ul el="12" id="fw_saveaudit" cn="保存并审核"></ul>
    </div>

</body>
</html>