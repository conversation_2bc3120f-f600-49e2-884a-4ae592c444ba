{
  //�����������
  "base": "",

  //�������������
  "lockRules": [
    {
      "id": "fstatus_ABC",
      "expression": ""
    },
    {
      "id": "fstatus_D",
      "expression": ""
    },
    {
      "id": "fstatus_E",
      "expression": ""
    },
    {
      "id": "fstatus_",
      "expression": ""
    },

    ////Ԥ�����������Թ���
    //{
    //  "id": "lock_freserveday",
    //  "expression": "field:freservedateto_d|freserveday>0"
    //},
    //{
    //  "id": "unlock_freserveday",
    //  "expression": "field:freservedateto_d|freserveday<=0"
    //},

    //Ԥ�����������Թ���
    {
      "id": "lock_byfopdesc",
      "expression": "field:fbizqty_d,freservedateto_d,fstorehouseid,fstorelocationid,fstockstatus|fopdesc=='3'"
    },
    {
      "id": "unlock_byfopdesc",
      "expression": "field:$fbizqty_d,freservedateto_d,fstorehouseid,fstorelocationid,fstockstatus|fopdesc!='3'"
    },

    //�йر�״̬��4�ֶ��ر� 3�Զ��ر�
    {
      "id": "lock_fclosestatus_d",
      "expression": "field:fbizqty_d,freservedateto_d,fstorehouseid,fstorelocationid,fstockstatus|fclosestatus_d=='3' or fclosestatus_d=='4'"
    },
    {
      "id": "unlock_fclosestatus_d",
      "expression": "field:$fbizqty_d,freservedateto_d,fstorehouseid,fstorelocationid,fstockstatus|fclosestatus_d!='3' and fclosestatus_d!='4'"
    },

    //��������ʾ������״̬=δ����ʱ(������ʱ��)�������ֶο���
    {
      "id": "fcancelstatus_0",
      "expression": "field:$*;menu:$*|fcancelstatus==false"
    },
    //��������ʾ������״̬='������' ʱ�����ֶβ�����
    {
      "id": "fcancelstatus_1",
      "expression": "field:*$;menu:*$tbReserveCancel|fcancelstatus==true"
    }

  ],

  //������ɼ��Թ���
  "visibleRules": [

  ],

  //������������
  "calcRules": [

  ]
}