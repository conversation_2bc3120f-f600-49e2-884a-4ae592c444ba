<!--
排单计划单，用于排单平台作业后的数据存储

-->

<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_schedulecreate" basemodel="" el="0" cn="创建排单" width="900px" height="600px">
    <div id="fbillhead" el="51" pk="fid" tn="" pn="fbillhead" cn="创建排单">

        <!--创建排单-->
        <input group="基本信息" el="159" ek="fbillhead" id="fdatesec" fn="fdatesec" pn="fdatesec" cn="日期区间" visible="-1" copy="0" lix="1"/>
        
        <input group="基本信息" el="158" ek="FBillHead" id="fstarttime" fn="fstarttime" pn="fstarttime" visible="-1" cn="开始时间"
        lock="0" copy="0" lix="25" notrace="true" ts="" />
        <input group="基本信息" el="158" ek="FBillHead" id="fendtime" fn="fendtime" pn="fendtime" visible="-1" cn="结束时间"
                lock="0" copy="0" lix="26" notrace="true" ts="" />
        <input group="基本信息" el="116" ek="fbillhead" id="fenablestock" fn="fenablestock" pn="fenablestock" visible="1062" cn="收发货排单"
        lock="0" copy="1" lix="0" notrace="true" ts="" defval="true" />
        
        <input group="基本信息" el="106" ek="fbillhead" id="fschedulestaffid" fn="fschedulestaffid" pn="fschedulestaffid" visible="-1" cn="排单员"
        lock="0" copy="0" lix="30" notrace="true" ts="" refid="ydj_staff" defVal="@currentStaffId" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="106" ek="fbillhead" id="fscheduledeptid" fn="fscheduledeptid" pn="fscheduledeptid" visible="-1" cn="排单部门"
                lock="0" copy="0" lix="31" notrace="true" ts="" refid="ydj_dept" defVal="@currentDeptId" filter="" reflvt="0" dfld="" />

        <input group="基本信息" el="112" ek="FBillHead" id="fstockdateto" fn="fstockdateto" pn="fstockdateto" visible="-1" cn="收货日期"
        lock="0" copy="0" lix="35" notrace="true" ts="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockdeptidto" fn="fstockdeptidto" pn="fstockdeptidto" visible="-1" cn="收货部门"
                lock="0" copy="0" lix="36" notrace="true" ts="" refid="ydj_dept" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockstaffidto" fn="fstockstaffidto" pn="fstockstaffidto" visible="-1" cn="收货人"
                lock="0" copy="0" lix="37" notrace="true" ts="" refid="ydj_staff" filter="" reflvt="0" dfld="" />

        <input group="基本信息" el="112" ek="FBillHead" id="fstockdate" fn="fstockdate" pn="fstockdate" visible="-1" cn="发货日期"
                lock="0" copy="0" lix="35" notrace="true" ts="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockdeptid" fn="fstockdeptid" pn="fstockdeptid" visible="-1" cn="发货部门"
                lock="0" copy="0" lix="36" notrace="true" ts="" refid="ydj_dept" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fstockstaffid" fn="fstockstaffid" pn="fstockstaffid" visible="-1" cn="发货人"
                lock="0" copy="0" lix="37" notrace="true" ts="" refid="ydj_staff" filter="" reflvt="0" dfld="" />
        <input group="基本信息" el="106" ek="FBillHead" id="fcarid" fn="fcarid" pn="fcarid" visible="-1" cn="车辆"
                lock="0" copy="0" lix="41" notrace="true" ts="" refid="ydj_carinformation" filter="" reflvt="0" dfld="" />
    </div>

    

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">

    </div>

</body>
</html>

