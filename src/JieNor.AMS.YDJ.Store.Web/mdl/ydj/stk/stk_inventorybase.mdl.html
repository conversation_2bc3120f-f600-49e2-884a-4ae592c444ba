<!DOCTYPE html>
<html>
<head>
    <title></title>
	<meta charset="utf-8" />
</head>
<body id="stk_inventorybase"  basemodel="bd_basetmpl" el="3" cn="盘点方案">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_inventorybase" pn="fbillhead" cn="盘点方案">
        <input group="基本信息" type="text" id="fnumber" el="108" ek="fbillhead" fn="fnumber" pn="fnumber" cn="编码" width="120" visible="-1" copy="0" lix="1" />
        <input group="基本信息" type="text" id="fdate" el="112" ek="fbillhead" fn="fdate" pn="fdate" cn="盘点日期" visible="-1" width="105" lix="5" />

        <input group="基本信息" el="106" ek="fbillhead" id="fstockstaffid" fn="fstockstaffid" pn="fstockstaffid" cn="盘点员" visible="-1" lix="20" refid="ydj_staff" must="1" defVal="@currentStaffId" />
        <input group="基本信息" el="106" ek="fbillhead" id="fstockdeptid" fn="fstockdeptid" pn="fstockdeptid" cn="盘点部门" visible="-1" lix="15" refid="ydj_dept" must="1" defVal="@currentDeptId" />

        <input group="基本信息" type="text" id="fname" el="108" ek="fbillhead" fn="fname" pn="fname" cn="名称" width="120" visible="-1" copy="0" lix="10" />

        <input group="基本信息" lock="-1" el="106" ek="fbillhead" id="fbizformid" fn="fbizformid" pn="fbizformid" cn="盘点对象" refid="sys_bizobject" defVal="'stk_inventorylist'" visible="0" lix="4" width="150" />

        <!--预警条件-->
        <input group="基本信息" el="156" ek="fbillhead" id="fcondition" fn="fcondition" pn="fcondition" cn="盘点条件" ctlfk="fbizformid" visible="96" lix="16" width="150" must="1" />

        <input group="基本信息" el="116" ek="fbillhead" id="fenablebarcode" fn="fenablebarcode" pn="fenablebarcode" visible="1150" cn="启用条码作业"
               copy="0" lix="70" notrace="true" ts="" />
        <input group="基本信息" el="116" ek="fbillhead" id="freezeout" fn="freezeout" pn="freezeout" visible="1150" cn="冻结出入库" defVal="true"
               copy="0" lix="75" ts="" />

        <input lix="106" el="152" ek="fbillhead" id="fpdqtydefval" fn="fpdqtydefval" pn="fpdqtydefval" visible="1150" cn="实盘数默认值"
               lock="0" copy="0" notrace="true" ts="" vals="'0':'0','1':'账存数'" defval="'1'" />

        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fstore" fn="fstore" pn="fstore" cn="盘点门店" notrace="false" refid="bas_store" canchange="true" lix="110" defls="true" copy="0" />

        <input lix="120" el="152" ek="fbillhead" id="fzytype" fn="fzytype" pn="fzytype" visible="1150" cn="盘点类型"
               lock="0" copy="0" notrace="true" ts="" vals="'1':'全盘','2':'部分盘点'" defval="''" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <!--<li el="11" vid="511" cn="已经生成盘点单不可以反审核" data='{"expr": [{"linkFormId":"stk_inventoryverify","linkFieldKey":"finventbase"}],
                "message":"已经生成了下游单据，不允许反审核！"}'></li>-->
        </ul>
    </div>
    
</body>
</html>

