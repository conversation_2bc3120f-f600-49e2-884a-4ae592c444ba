<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="stk_reservebill_history" basemodel="stk_reservebill" el="1" cn="预留单-归档" ubl="1">
    <div id="fbillhead" el="51" pk="fid" tn="t_stk_reservebill_history" pn="fbillhead" cn="预留单-归档"> 

    </div>

    <!--预留明细-->
    <table id="fentity" el="54" pk="fentryid" tn="t_stk_reservebillentry_history" pn="fentity" cn="需求明细" kfks="fmaterialid"
           lvlfk="flevel" pfk="fparentid" expfk="fexpanded" leffk="fisleaf" frtfk="ffrontid">
        <tr> 

        </tr>
    </table>


    <table id="fdetail" el="53" pek="fentity" pk="fdetailid" tn="t_stk_reservebilldetail_history" pn="fdetail" cn="预留跟踪">
        <tr>
            
        </tr>
    </table> 


    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
   
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
    </div>

</body>
</html>