{
  //规则引擎基类
  "base": "/mdl/ydj/stk/base_stkbilltmpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //此项规则表示：单据状态='' 时(新增的时候)，所有字段可用，只有 新增 保存 选择盘点数据操作可用，其他操作都不可用
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave,tbQueryInventory,btnUnStandardCustom,btnStandardCustom|fstatus==''"
    },
    {
      "id": "lock_tbBarCode",
      "expression": "menu:tbBarCode,tbPackorder,tbReceptionTaskCreate|fstatus!='D' or fcancelstatus=='0'"
    },
    {
      "id": "unlock_tbBarCode",
      "expression": "menu:$tbBarCode,tbPackorder,tbReceptionTaskCreate|fstatus=='D' and fcancelstatus!='0'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fbizunitid=fmaterialid__fpurunitid" },
    //带出商品是否非标
    { "expression": "funstdtype=fmaterialid__funstdtype|fmaterialid!='' or 1==1" },
    //单位成本默认=单价
    { "expression": "fcostprice=fprice|fcostprice==0 and fprice>0" },
    //总体积
    { "expression": "ftotalvolume=fbizqty*fsinglevolume|fbizqty!='' and fsinglevolume!=''" },
    //单位体积
    { "expression": "fsinglevolume=ftotalvolume/fbizqty|fbizqty!='' and fbizqty!=0 and ftotalvolume!=''" }
  ]
}