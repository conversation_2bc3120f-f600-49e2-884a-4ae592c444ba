{
  //规则引擎基类
  "base": "/mdl/bd.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //客户收款账号或客户退款账号：当其中任何一个为true时解锁客户明细表，都为false时锁住
    {
      "id": "lock_fcustomerid",
      "expression": "field:fcustomerid$|fiscustbanknum==false and fisrefundbanknum==false"
    },
    //客户收款账号或客户退款账号：当其中任何一个为true时解锁客户明细表
    {
      "id": "unlock_fcustomerid",
      "expression": "field:$fcustomerid|fiscustbanknum==true or fisrefundbanknum==true"
    },
    //此项规则表示：单据状态=D 时，所有字段可用，反审核 提交 操作不可用，其他操作可用
    {
      "id": "fstatus_BC",
      "expression": "field:fbanknum$;menu:tbAudit,tbUnaudit,tbUnsubmit$*|fstatus=='C'"
    }
    //此项规则表示：单据状态=E 时，所有字段不可用，审核 提交 操作不可用，其他操作可用
    //{
    //  "id": "fstatus_E",
    //  "expression": "field:*$fbanknum;menu:tbSubmit,tbAudit$*|fstatus=='E'"
    //}

  ],

  //定义表单可见性规则
  "visibleRules": [
  ],

  //定义表单计算规则
  "calcRules": [
    //自动将账号变化同步到编码
    { "expression": "fnumber=fbanknum" },
    { "expression": "fbankname=fbankid__fname" }
  ]
}