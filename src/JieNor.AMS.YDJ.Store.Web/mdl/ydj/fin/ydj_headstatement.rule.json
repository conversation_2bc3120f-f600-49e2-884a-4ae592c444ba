{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //锁定：对账相符/对账不符
    {
      "id": "lock_confirm",
      "expression": "menu:tbConfirmBill,tbUnconfirmBill|fcancelstatus==true or fbillstatus=='1' or fbillstatus=='2'"
    },
    //解锁：对账相符/对账不符
    {
      "id": "unlock_confirm",
      "expression": "menu:$tbConfirmBill,tbUnconfirmBill|fcancelstatus==false and fbillstatus!='1' and fbillstatus!='2'"
    },
    //锁定：对账撤销
    {
      "id": "lock_undo",
      "expression": "menu:tbUndoBill$|fcancelstatus==false and fbillstatus!='1'"
    },
    //解锁：对账撤销
    {
      "id": "unlock_undo",
      "expression": "menu:$tbUndoBill|fcancelstatus==false and fbillstatus=='1'"
    },
    //锁定：经销商备注字段
    {
      "id": "lock_remark",
      "expression": "field:fremitagentremark,foutagentremark,freturnagentremark,frepairagentremark,fdebitagentremark,fcertagentremark|fcancelstatus==true or fbillstatus=='1' or fbillstatus=='2'"
    },
    //解锁：经销商备注字段
    {
      "id": "unlock_remark",
      "expression": "field:$fremitagentremark,foutagentremark,freturnagentremark,frepairagentremark,fdebitagentremark,fcertagentremark|fcancelstatus==false and fbillstatus!='1' and fbillstatus!='2'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
  ],

  //定义表单计算规则
  "calcRules": [ 
  ]
}