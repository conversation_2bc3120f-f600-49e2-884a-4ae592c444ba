<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_logisticsstatement" basemodel="bill_basetmpl" el="1" cn="总部物流对账单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_logisticsstatement" pn="fbillhead" cn="总部物流对账单">
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fagent" fn="fagent" pn="fagent" cn="售达方名称" lix="2" width="145" apipn="agentNo" refid="bas_agent" lock="-1" />
        <input group="基本信息" el="100" ek="fbillhead" id="fprovince" fn="fprovince" pn="fprovince" cn="省份" visible="-1" lix="5" lock="-1" />
        <input group="基本信息" el="105" ek="fbillhead" id="foutsumamount" fn="foutsumamount" pn="foutsumamount" cn="体积" visible="-1" lix="10" lock="-1" format="0,000.00" />
        <input group="基本信息" el="107" ek="fbillhead" visible="-1" id="fagentnumber" fn="fagentnumber" pn="fagentnumber" cn="售达方编号" lix="1" width="145" lock="-1" ctlfk="fagent" dispfk="fnumber" />

        <input group="基本信息" el="100" ek="fbillhead" id="fdeliveryfactory_m" fn="fdeliveryfactory_m" pn="fdeliveryfactory_m" cn="发货工厂" visible="-1" lix="15" lock="-1" />
        <input group="财务信息" el="102" ek="fbillhead" visible="1124" id="fdistrate" fn="fdistrate" pn="fdistrate" cn="单价" lock="-1" lix="20" copy="0" apipn="distRate" />
        <input group="基本信息" el="100" ek="fbillhead" id="fmonth" fn="fmonth" pn="fmonth" cn="账单月份" visible="-1" lix="25" lock="-1" />
        <input group="基本信息" el="112" ek="fbillhead" visible="-1" id="fsyncdate" fn="fsyncdate" pn="fsyncdate" cn="同步时间" lix="30" width="105" lock="-1" format="yyyy-MM-dd" copy="0"
        <input group="基本信息" el="105" ek="fbillhead" id="fdisprice" fn="fdisprice" pn="fdisprice" cn="运费" visible="-1" lix="35" lock="-1" format="0,000.00" />
    </div>

    <table id="flogisticsstatemententry" el="52" pk="fentryid" tn="t_ydj_logisticsstatemententry" pn="flogisticsstatemententry" cn="总部物流对账单明细">
        <tr>
            <th lix="100" el="106" ek="flogisticsstatemententry" visible="0" id="fdeliver" fn="fdeliver" pn="fdeliver" cn="送达方名称" refid="bas_deliver" lock="-1"></th>
            <th lix="80" el="107" ek="flogisticsstatemententry" visible="0" id="fdelivernum" fn="fdelivernum" pn="fdelivernum" cn="送达方编号" ctlfk="fdeliver" dispfk="fnumber" lock="-1"></th>
            <th lix="100" el="100" ek="flogisticsstatemententry" visible="1150" id="fdelivertxt" fn="fdelivertxt" pn="fdelivertxt" cn="送达方编号" lock="-1"></th>
            <th lix="80" el="100" ek="flogisticsstatemententry" visible="1150" id="fdelivernumtxt" fn="fdelivernumtxt" pn="fdelivernumtxt" cn="送达方名称" lock="-1"></th>
            <th lix="130" el="100" ek="flogisticsstatemententry" visible="1150" id="fdeliveryfactory" fn="fdeliveryfactory" pn="fdeliveryfactory" cn="发货工厂" lock="-1"></th>
            <th lix="140" el="113" ek="flogisticsstatemententry" id="foutdate" fn="foutdate" pn="foutdate" cn="出货日期" width="120" lock="-1" copy="0" apipn="outdate" visible="1086"></th>
            <th lix="150" el="100" ek="flogisticsstatemententry" id="foutnum" fn="foutnum" pn="foutnum" cn="出货编号" width="120" lock="-1" copy="0" apipn="outnum" visible="1086"></th>
            <th lix="160" el="105" ek="flogisticsstatemententry" id="foutamount" fn="foutamount" pn="foutamount" cn="体积" width="200" visible="1150" lock="-1" format="0,000.00"></th>
            <th lix="170" el="105" ek="flogisticsstatemententry" id="foutdisamount" fn="foutdisamount" pn="foutdisamount" cn="运费" width="200" visible="1150" lock="-1" format="0,000.00"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fagent','fprovince','foutsumamount','fdeliveryfactory_m','fdistrate','fmonth','fsyncdate'
            ,'fdisprice','fdeliver','fdelivertxt','fdelivernumtxt','fdeliveryfactory','foutdate','foutnum','foutamount','foutdisamount']}" permid=""></ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">

    </div>
</body>
</html>