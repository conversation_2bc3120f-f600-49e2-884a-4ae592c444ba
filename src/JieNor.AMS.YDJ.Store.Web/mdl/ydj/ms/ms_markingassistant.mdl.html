<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ms_markingassistant" el="3" cn="营销助手app职位" basemodel="bd_basetmpl" ludt="ListTree" isolate="0">
    <div id="fbillhead" el="51" pk="fid" tn="t_ms_markingassistant" pn="fbillhead" cn="营销助手app职位">

        <input type="text" id="fnumber" el="100" ek="fbillhead" fn="fnumber" pn="fnumber" apipn="fnumber" cn="编码" desc="编码" width="120" visible="-1" lix="2" />
        <input type="text" id="fname" el="100" ek="fbillhead" fn="fname" pn="fname" apipn="name" cn="名称" desc="名称" width="120" visible="-1" lix="2" />
        <input type="text" id="foutid" el="100" ek="fbillhead" fn="foutid" pn="foutid" apipn="foutid" cn="ID" desc="ID" width="120" visible="-1" lix="2" />
        <input type="text" id="fparentcode" el="100" ek="fbillhead" fn="fparentcode" pn="fparentcode" apipn="fparentcode" cn="父级编码" desc="父级编码" width="120" visible="-1" lix="2" />

    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
            <li el="11" clear></li>
        </ul>
        <ul el="10" ek="fbillhead" id="syncfrommusi" op="syncfrommusi" opn="从慕思拉取数据" permid="fw_syncfrommusi"></ul>
    </div>

    <div id="permList"> 
        <!--<ul el="12" id="fw_view" cn="查看" order="1"></ul>-->
        <!--<ul el="12" id="fw_viewrecord" cn="查看记录" order="2"></ul>
        <ul el="12" id="fw_modify" cn="修改" order="4"></ul>-->
        <ul el="12" id="fw_syncfrommusi" cn="从慕思拉取数据"></ul>
    </div>
</body>
</html>