<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="sal_returnnotice" basemodel="ydj_logisticnoticetpl" el="1" cn="销售退货通知单" approvalflow="true" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_sal_returnnotice" pn="fbillhead" cn="销售退货通知单">
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lix="27"></select>
        <!--重写基类模型中的部分字段属性-->
        <input id="fbillno" el="108" visible="1150" lix="1" width="115" align="center" />
        <input id="fcreatorid" el="118" visible="1062" lix="9" width="80" />
        <input id="fcreatedate" el="119" visible="1062" lix="10" width="125" />
        <input id="fdescription" el="100" visible="1062" />

        <input el="106" id="fstockdeptid" cn="收货部门" visible="1150" must="1" />
        <input el="100" id="fstockaddress" cn="收货人地址" visible="1150" />


        <input group="基本信息" el="100" ek="FBillHead" id="freturnreason" fn="freturnreason" ts="" cn="退货说明" visible="1150" copy="0" len="1000" />
        <input group="基本信息" el="123" ek="fbillhead" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" visible="1150" refid="bd_billtype" lix="10" width="90" must="1" />
        <input group="基本信息" el="108" ek="fbillhead" visible="-1" id="fbillno" fn="fbillno" cn="单据编号" lix="1" width="145" />
        <!--销售退货类型：正常退货，退换并扣款-->
        <input lix="5" el="112" id="fdate" cn="退货通知日期" visible="-1" copy="0" must="1" />
        <input lix="10" visible="-1" group="基本信息" el="122" ek="fbillhead" id="freturntype" fn="freturntype" cn="退货类型" refid="bd_enum" cg="销售退货业务类型" defval="'sostockreturn_biztype_01'" />
        <select lix="15" visible="-1" group="基本信息" el="122" ek="fbillhead" id="freturncause" fn="freturncause" cn="退货原因" refid="bd_enum" cg="退货原因" must="1"></select>
        <input lix="20" visible="-1" el="106" id="fstockstaffid" cn="收货人" must="1" />
        <input lix="25" visible="-1" el="100" id="fstockstaffphone" cn="收货人电话" />
        <input lix="30" visible="-1" group="基本信息" el="106" ek="fbillhead" id="fsostaffid" fn="fsostaffid" pn="fsostaffid" cn="销售员" refid="ydj_staff" />
        <input lix="35" visible="-1" group="基本信息" el="106" ek="fbillhead" id="fsodeptid" fn="fsodeptid" pn="fsodeptid" cn="销售部门" refid="ydj_dept" />
        <input lix="40" visible="-1" group="基本信息" el="106" ek="fbillhead" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" must="1" />
        <input lix="45" visible="-1" el="100" id="flinkstaffid" cn="发货人" />
        <input lix="50" visible="-1" el="100" id="flinkmobile" cn="发货人电话" />
        <input lix="55" visible="-1" el="100" id="flinkaddress" cn="发货人地址" />
        <input lix="60" visible="-1" group="基本信息" el="141" ek="fbillhead" id="fsourcenumber" />


        <!--此字段适用于退货赔偿（正常还要换货，通常是质量原因引起的不符合使用条件，同时又耽误了客户正常使用了）场景-->
        <input group="财务信息" el="105" ek="fbillhead" id="fcompensateamount" fn="fcompensateamount" pn="fcompensateamount" cn="赔偿金额" visible="1150" />

        <input group="财务信息" el="105" ek="fbillhead" id="fplanreturnamount" fn="fplanreturnamount" pn="fplanreturnamount" visible="1150" cn="应退货款金额"
               lock="-1" copy="0" lix="80" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="财务信息" el="105" ek="fbillhead" id="factualreturnamount" fn="factualreturnamount" pn="factualreturnamount" visible="1150" cn="实退货款金额"
               copy="0" lix="81" notrace="true" ts="" roundType="0" format="0,000.00" />


        <!-- 相关信息 -->
        <input el="140" ek="fbillhead" id="fsourcetype" visible="1150" />

        <!--基本信息-->

        <input group="客户信息" el="122" ek="fbillhead" visible="1150" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="64" />
        <input group="客户信息" el="122" ek="fbillhead" visible="1150" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="65" />
        <input group="客户信息" el="122" ek="fbillhead" visible="1150" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lix="66" />

        <input group="基本信息" el="116" ek="FBillHead" id="fautobcinstock" fn="fautobcinstock" pn="fautobcinstock" visible="1150" cn="打码收货后同步入库"
               lock="-1" copy="0" lix="70" notrace="true" ts="" />

        <input group="基本信息" el="152" ek="FBillHead" id="fscanstatus" fn="fscanstatus" pn="fscanstatus" visible="1150" cn="扫描状态"
               lock="-1" copy="0" lix="90" notrace="true" ts="" vals="'1':'待打码收货','2':'待核验入库','3':'已入库','4':'处理中','5':'入库失败'" />

        <input group="基本信息" el="100" ek="FBillHead" id="ffailreason" fn="ffailreason" pn="ffailreason" visible="1086" cn="错误原因"
               lock="-1" copy="0" lix="91" notrace="true" ts="" len="1000" />
        <input group="物流信息" el="100" ek="fbillhead" visible="1150" id="fshippingbillno" fn="fshippingbillno" pn="fshippingbillno" cn="运输单号" />
    </div>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_sal_returnnoticeentry" pn="fentity" cn="商品明细" kfks="fmaterialid">
        <tr>
            <th lix="200" el="103" ek="fentity" id="fbizqty" fn="fbizqty" cn="实退数量" ctlfk="fbizunitid" basqtyfk="fqty" visible="1150" width="100" format="0,000.00" must="0"></th>
            <th lix="205" el="109" ek="fentity" id="fbizunitid" cn="销售单位" visible="1150"></th>
            <th lix="210" el="103" ek="fentity" id="fbizplanqty" fn="fbizplanqty" cn="应退数量" ctlfk="fbizunitid" basqtyfk="fplanqty"  visible="1150" width="100" lock="0" must="0" format="0,000.00"></th>
            <th lix="215" el="109" ek="fentity" id="funitid" fn="funitid" cn="基本单位" lix="108" ctlfk="fmaterialid" refid="ydj_unit" sformid="" filter="fisbaseunit='1'" visible="1150" width="80" must="1" lock="-1"></th>
            <th lix="220" el="102" ek="fentity" id="fvolumeqty" fn="fvolumeqty" pn="fvolumeqty" visible="1150" cn="体积(m³)"
                lock="-1" copy="1"  notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.000"></th>
            <th lix="225" el="102" ek="fentity" id="fvolume" fn="fvolume" pn="fvolume" visible="1150" cn="总体积(m³)"
                lock="0" copy="1"  notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.000"></th>
            <th lix="230" el="102" ek="fentity" id="fgrossqty" fn="fgrossqty" pn="fgrossqty" visible="1150" cn="毛重(kg)"
                lock="-1" copy="1"  notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="235" el="102" ek="fentity" id="fgross" fn="fgross" pn="fgross" visible="1150" cn="总重量(kg)"
                lock="0" copy="1"  notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="240" el="100" ek="fentity" id="fpacksize" fn="fpacksize" pn="fpacksize" visible="1150" cn="包装尺寸"
                lock="0" copy="1" notrace="true" ts=""></th>
            <th lix="245" el="106" ek="fentity" id="fstorehouseid" cn="仓库" must="0"></th>
            <th lix="250" el="153" ek="fentity" id="fstorelocationid" fn="fstorelocationid" cn="仓位"  ctlfk="fstorehouseid" luek="fentity" lunmfk="flocname" lunbfk="flocnumber" sformid="" visible="1150" width="100"></th>
            <th lix="255" el="106" ek="fentity" id="fstockstatus" cn="库存状态" must="0"></th>
            <th lix="260" el="108" ek="fentity" id="fsooutstockno" fn="fsooutstockno" pn="fsooutstockno" visible="1150" cn="销售出库单编号"
                lock="-1" copy="0"  notrace="true" ts=""></th>
            <th lix="265" el="108" ek="fentity" id="fsoorderno" fn="fsoorderno" pn="fsoorderno" visible="1150" cn="销售订单编号"
                lock="-1" copy="0"  notrace="true" ts=""></th>


            <th el="103" ek="fentity" id="fplanqty" fn="fplanqty" cn="基本单位应退数量" ctlfk="funitid" lix="104" visible="1086" width="120" lock="-1" must="0" format="0,000.00"></th>

            <th el="103" ek="fentity" id="fqty" ts="" cn="基本单位实退数量" visible="1086" width="120" must="0"></th>
            <th el="101" ek="fentity" id="fpackqty" fn="fpackqty" pn="fpackqty" visible="1086" cn="件数"
                lock="0" copy="1" lix="120" notrace="true" ts="" format="0,000"></th>
            <th el="103" ek="fentity" id="freturnqty" fn="freturnqty" pn="freturnqty" visible="1086" cn="基本单位退货数量" width="120"
                lock="-1" copy="0" lix="130" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizreturnqty" fn="fbizreturnqty" pn="fbizreturnqty" visible="1086" cn="退货数量" width="100"
                lock="-1" copy="0" lix="132" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="freturnqty" roundType="0" format="0,000.00"></th>
      
            <th el="108" ek="fentity" id="fsooutstockinterid" fn="fsooutstockinterid" pn="fsooutstockinterid" visible="0" cn="销售出库单内码"
                lock="-1" copy="0" lix="227" notrace="true" ts=""></th>
            <th el="108" ek="fentity" id="fsooutstockentryid" fn="fsooutstockentryid" pn="fsooutstockentryid" visible="0" cn="销售出库单分录内码"
                lock="-1" copy="0" lix="228" notrace="true" ts=""></th>


            <th el="108" ek="fentity" id="fsoorderinterid" fn="fsoorderinterid" pn="fsoorderinterid" visible="0" cn="销售订单内码"
                lock="-1" copy="0" lix="231" notrace="true" ts=""></th>
            <th el="108" ek="fentity" id="fsoorderentryid" fn="fsoorderentryid" pn="fsoorderentryid" visible="0" cn="销售订单分录内码"
                lock="-1" copy="0" lix="232" notrace="true" ts=""></th>

            <th el="112" ek="FEntity" id="forderdate" fn="forderdate" pn="forderdate" visible="1086" cn="订单日期"
                lock="-1" copy="0" lix="233" notrace="true" ts="">订单日期</th>

            <th el="103" ek="fentity" id="forderqty" fn="forderqty" pn="forderqty" visible="1086" cn="基本单位订单数量" width="120"
                lock="-1" copy="0" lix="234" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th el="103" ek="fentity" id="fbizorderqty" fn="fbizorderqty" pn="fbizorderqty" visible="1086" cn="销售订单数量" width="100"
                lock="-1" copy="0" lix="235" notrace="true" ts="" ctlfk="fbizunitid" basqtyfk="forderqty" roundType="0" format="0,000.00"></th>

            <th el="104" ek="fentity" id="fprice" lock="0"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="push2returngood" op="push" opn="退货" data="{'parameter':{'ruleId':'sal_returnnotice2stk_sostockreturn'}}" permid=""></ul>

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="17" sid="1002" cn="反写销售合同退货通知数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnnoticeqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnnoticeqty&gt;fqty+freturnqty',
                'excessMessage':'销售退货通知数量不允许超过订单数量+已退货数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写销售出库退货通知数量" data="{
                'sourceFormId':'stk_sostockout',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnnoticeqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnnoticeqty&gt;fqty',
                'excessMessage':'销售退货通知数量不允许超过订单出库数量！'
                }"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="511" cn="进行了退货后不可以反审核" data='{"expr": [{"linkFormId":"stk_sostockreturn","linkFieldKey":"fsourceinterid"}],
                "message":"已经生成了下游单据，不允许反审核！"}'></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="17" sid="1002" cn="反写销售合同退货通知数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnnoticeqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnnoticeqty&gt;fqty+freturnqty',
                'excessMessage':'销售退货通知数量不允许超过订单数量+已退货数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写销售出库退货通知数量" data="{
                'sourceFormId':'stk_sostockout',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnnoticeqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnnoticeqty&gt;fqty',
                'excessMessage':'销售退货通知数量不允许超过订单出库数量！'
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">

            <li el="17" sid="1002" cn="反写销售合同退货通知数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnnoticeqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnnoticeqty&gt;fqty+freturnqty',
                'excessMessage':'销售退货通知数量不允许超过订单数量+已退货数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写销售出库退货通知数量" data="{
                'sourceFormId':'stk_sostockout',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'freturnnoticeqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'freturnnoticeqty&gt;fqty',
                'excessMessage':'销售退货通知数量不允许超过订单出库数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写排单申请单已排数量" data="{
                'sourceFormId':'stk_scheduleapply',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'fscheduleqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fscheduleqty&gt;fqty',
                'excessMessage':'销售退货通知数量不允许超过排单申请单实排数量！'
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">

            <li el="17" sid="1002" cn="反写排单申请单已排数量" data="{
                'sourceFormId':'stk_scheduleapply',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'fscheduleqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fscheduleqty&gt;fqty',
                'excessMessage':'销售退货通知数量不允许超过排单申请单实排数量！'
                }"></li>
        </ul>

        <ul el="10" ek="fbillhead" id="unsubmit" op="unsubmit" opn="撤消">
            <li el="11" vid="511" cn="已经生成包装清单后不可以撤消" data='{"expr": [{"linkFormId":"bcm_packorder","linkFieldKey":"fsourceinterid"}],
                "message":"已经生成了下游单据，不允许撤消！"}'></li>
        </ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <!--<div id="permList">
        <ul el="12" id="" cn="发送"></ul>
        <ul el="12" id="" cn="撤销发送"></ul>
        <ul el="12" id="" cn="确认"></ul>
        <ul el="12" id="" cn="取消确认"></ul>
        <ul el="12" id="" cn="入库"></ul>
    </div>-->

</body>
</html>

