<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_payreceipt" el="1" cn="其它应付单" basemodel="bill_basetmpl" ludt="ListTree" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_payreceipt" pn="fbillhead" cn="其它应付单">
        <!--基本信息-->
        <input group="基本信息" el="108" ek="fbillhead" visible="1150" id="fbillno" fn="fbillno" cn="单据编号" lix="1" width="145" />
        <select lix="1" group="基本信息" el="123" ek="fbillhead" id="fbilltype" fn="fbilltype" pn="fbilltype" cn="单据类型" refid="bd_billtype" apipn="billType" visible="-1" must="1"  ></select>
        <input group="基本信息" el="100" ek="fbillhead" id="fsourceinterid" fn="fsourceinterid" pn="fsourceinterid" cn="源单Id" lix="1" width="145" visible="0" />
        <input lix="5" group="基本信息" el="150" ek="fbillhead" id="frelatecusid" fn="frelatecusid" pn="frelatecusid" ctlfk="frelatetype" cn="往来单位名称" width="100" visible="-1" must="1" />
        <input lix="5" group="基本信息" el="107" ek="fbillhead" visible="1150" id="frelatecusnumber" fn="frelatecusnumber" pn="frelatecusnumber" cn="往来单位编码" ctlfk="frelatecusid" dispfk="fnumber" />
        <input lix="10" group="基本信息" el="106" ek="fbillhead" visible="-1" id="ftrainingdept" fn="ftrainingdept" pn="ftrainingdept" cn="部门" refid="ydj_dept" defVal="@currentDeptId" must="1" />
        <input lix="10" group="基本信息" el="107" ek="fbillhead" visible="1150" id="fdeptnumber" fn="fdeptnumber" pn="fdeptnumber" cn="部门编码" ctlfk="ftrainingdept" dispfk="fnumber" />
        <input lix="15" group="基本信息" el="106" ek="fbillhead" visible="-1" id="frelatemanid" fn="frelatemanid" pn="frelatemanid" cn="员工" refid="ydj_staff" defVal="@currentStaffId" />
        <select lix="20" group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcurrency" fn="fcurrency" pn="fcurrency" cn="币别" cg="币别" refid="bd_enum" dfld="fenumitem" must="1"></select>
        <input lix="25" group="基本信息" el="105" ek="fbillhead" visible="-1" id="fsumtaxamount" fn="fsumtaxamount" pn="fsumtaxamount" cn="总金额" lock="-1" />
        <input lix="30" group="基本信息" el="100" ek="fbillhead" visible="-1" id="freceiveragent" fn="freceiveragent" pn="freceiveragent" cn="接单方经销商" copy="0" lock="-1" />
        <input lix="35" group="基本信息" el="105" ek="fbillhead" id="fsettledamount" fn="fsettledamount" cn="已结算金额" visible="-1" copy="0" />
        <input lix="40" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fagentid" fn="fagentid" pn="fagentid" refid="bas_agent" cn="结算组织" />
        <input lix="55" group="基本信息" el="105" ek="fbillhead" visible="-1" id="fmoney" fn="fmoney" pn="fmoney" cn="费用金额" />
        <input lix="65" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fdatasource" fn="fdatasource" pn="fdatasource" cn="数据来源" />
        <input lix="500" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fdescription" fn="fdescription" pn="fdescription" cn="备注" len="500" />
        <input lix="65" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fsourcenumber" fn="fsourcenumber" pn="fsourcenumber" cn="数据来源单号" />
        <input lix="500" group="基本信息" el="100" ek="fbillhead" visible="-1" id="fsyncreason" fn="fsyncreason" pn="fsyncreason" cn="同步原因" len="500" />


        <input group="基本信息" el="112" type="date" id="fregistdate" fn="fregistdate" pn="fregistdate" ek="fbillhead" cn="业务日期" visible="1150" copy="0" defval="@currentshortdate" must="1" />
        <select group="基本信息" el="149" ek="fbillhead" id="frelatetype" fn="frelatetype" pn="frelatetype" dataviewname="v_bd_relatedata" cn="往来单位类型" visible="1150" must="1">
            <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
            <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
            <dataSourceDesc formId="ste_channel" filter="" caption="合作渠道"></dataSourceDesc>
            <dataSourceDesc formId="ydj_staff" filter="" caption="员工"></dataSourceDesc>
        </select>
        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fmybankid" fn="fmybankid" cn="银行账号" refid="ydj_banknum" lix="108" dfld="fbankname,fbanknum" copy="0" apipn="myBankId" />
        <input group="基本信息" el="105" ek="fbillhead" id="funsettleamount" fn="funsettleamount" cn="待结算金额" visible="1150" copy="0" />
        <input group="基本信息" el="105" ek="fbillhead" id="funconfirmamount" fn="funconfirmamount" cn="待确认金额" visible="1150" copy="0" />
    </div>

    <!--费用项目明细-->
    <table id="fentry" el="52" pk="fentryid" tn="t_ydj_payreceipt_exp" pn="fentry" cn="费用项目明细" kfks="fexpenseitem">
        <tr>
            <th lix="36" el="107" ek="fentry" id="fexpenseitemno" fn="fexpenseitemno" pn="fexpenseitemno" cn="费用项目编号" ctlfk="fexpenseitem" dispfk="fnumber" visible="-1" sformid="" must="1"></th>
            <th lix="45" el="106" ek="fentry" id="fexpenseitem" fn="fexpenseitem" pn="fexpenseitem" cn="费用项目" refid="ydj_expenseitem" visible="-1" sformid="" must="1"></th>
            <th lix="50" el="106" ek="fentry" id="fdept" fn="fdept" pn="fdept" cn="费用承担部门" refid="ydj_dept" visible="-1" sformid=""></th>
            <th lix="60" el="102" ek="fentry" id="totalamount" fn="totalamount" pn="totalamount" cn="总金额" format="0,000.00" visible="-1"></th>
            <th lix="70" el="100" ek="fentry" id="fsourcebillno" fn="fsourcebillno" ts="" cn="源单编号" visible="-1" lock="-1"></th>
            <th el="152" ek="fentry" id="fexpensebilltype" fn="fexpensebilltype" pn="fexpensebilltype" cn="发票类型" visible="1150" vals="'0':'普通发票','1':'增值税发票','2':'机票','3':'火车票','4':'其他运输票','5':'其他'"></th>
            <th el="122" ek="fentry" id="ftaxrate" fn="ftaxrate" pn="ftaxrate" cn="税率" cg="税率" refid="bd_enum" dfld="fenumitem" defval="'0.03'" visible="1150"></th>
            <th el="102" ek="fentry" id="fnontaxamount" fn="fnontaxamount" pn="fnontaxamount" cn="不含税金额" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="ftaxamount" fn="ftaxamount" pn="ftaxamount" cn="税金额" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="nonloanamount" fn="nonloanamount" pn="nonloanamount" cn="未借款金额" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="fnontaxamountcurrency" fn="fnontaxamountcurrency" pn="fnontaxamountcurrency" cn="不含税金额本位币" format="0,000.00" visible="1150"></th>
            <th el="102" ek="fentry" id="ftaxamountcurrency" fn="ftaxamountcurrency" pn="ftaxamountcurrency" cn="税额本位币" format="0,000.00" visible="1150"></th>
            <th el="100" ek="fentry" id="fremark" fn="fremark" pn="fremark" cn="备注" len="500" visible="1150"></th>
            <th el="140" ek="fentry" id="fsourceformid" fn="fsourceformid" ts="" cn="源单类型" visible="1150" copy="0" lock="-1"></th>
            <th lix="70" el="100" ek="fentry" id="fdockingbillno" fn="fdockingbillno" ts="" cn="对接源单编号" visible="-1"></th>
            <th el="100" ek="fentry" id="finvoiceno" fn="finvoiceno" pn="finvoiceno" cn="发票号" visible="1150"></th>
            <th el="113" ek="fentry" id="fcreatetime" type="datetime" fn="fcreatetime" pn="fcreatetime" cn="发票创建时间" visible="1150"></th>
            <th el="112" ek="fentry" id="finvoicedate" type="date" fn="finvoicedate" pn="finvoicedate" cn="发票过账日期" visible="1150"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="17" sid="1002" cn="反写转单申请是否已经下推应收" data="{
                'sourceFormId':'ydj_transferorderapply',
                'sourceControlFieldKey':'fisalreadypushreceipt',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceinterid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'fisalreadypushreceipt',
                'expression':'1',
                'writebackMode':3,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="submit" op="submit" opn="提交">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unsubmit" op="unsubmit" opn="撤销" data="" permid="fw_unsubmit">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核" data="" permid="fw_audit">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核" data="" permid="fw_unaudit">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="uncancel" op="uncancel" opn="反作废">
            <li id="financeclosedaccounts" el="11" vid="6000" cn="财务关账逻辑控制" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="17" sid="1002" cn="反写转单申请是否已经下推应收" data="{
                'sourceFormId':'ydj_transferorderapply',
                'sourceControlFieldKey':'fisalreadypushreceipt',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceinterid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'fisalreadypushreceipt',
                'expression':'0',
                'writebackMode':3,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>
    </div>
</body>
</html>