{
  //规则引擎基类
  "base": "/mdl/ydj/tpl/ydj_logisticnoticetpl.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lockheadfield_bypush",
      "expression": "field:fsostaffid,fsodeptid,fcustomerid,fprovince,fcity,fregion|fsourcenumber!='' and fsourcenumber!=' '"
    },
    {
      "id": "lock_fprice",
      "expression": "field:fprice$|fsourcetype!='' and fsourcetype!=' '"
    },
    {
      "id": "unlock_fprice",
      "expression": "field:$fprice|fsourcetype=='' or fsourcetype==' '"
    },
    //此规则表示：当退货类型=“退货退款”时放开，为“正常退换”时，锁定
    {
      "id": "lock_factualreturnamount",
      "expression": "field:factualreturnamount$|freturntype=='sostockreturn_biztype_01'"
    },
    {
      "id": "unlock_factualreturnamount",
      "expression": "field:$factualreturnamount|freturntype=='sostockreturn_biztype_02'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fprovince=fcustomerid__fprovince" },
    { "expression": "fcity=fcustomerid__fcity" },
    { "expression": "fregion=fcustomerid__fregion" },
    { "expression": "fsodeptid=fsostaffid__fdeptid" },
    { "expression": "flinkstaffid=fcustomerid__fcontacts" },
    { "expression": "flinkmobile=fcustomerid__fphone" },
    { "expression": "flinkaddress=fcustomerid__faddress" },
    { "expression": "fbizunitid=fmaterialid__fsalunitid" },
    { "expression": "fenablebarcode=getBillTypeParam(fbilltype,'fenablebarcode')" },
    { "expression": "fautobcinstock=getBillTypeParam(fbilltype,'fautobcinstock')" },
    { "expression": "fplanreturnamount=sum(famount)|freturntype=='sostockreturn_biztype_02'" },
    { "expression": "factualreturnamount=sum(famount)|freturntype=='sostockreturn_biztype_02'" },
    { "expression": "factualreturnamount=0|freturntype=='sostockreturn_biztype_01'" },
    { "expression": "fplanreturnamount=0|freturntype=='sostockreturn_biztype_01'" }
  ]
}