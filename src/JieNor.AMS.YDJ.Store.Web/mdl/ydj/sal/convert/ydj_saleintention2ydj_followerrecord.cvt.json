{"Id": "ydj_saleintention2ydj_followerrecord", "Number": "ydj_saleintention2ydj_followerrecord", "Name": "销售意向单转跟进记录", "SourceFormId": "ydj_saleintention", "TargetFormId": "ydj_followerrecord", "ActiveEntityKey": "f<PERSON>head", "VisibleEx": 1, "FieldMappings": [{"Id": "fcustomerid", "Name": "客户", "MapType": 0, "SrcFieldId": "fcustomerid", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fcontacts", "Name": "联系人", "MapType": 0, "SrcFieldId": "fcustomerid.fcontacts", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fphone", "Name": "联系电话", "MapType": 0, "SrcFieldId": "fcustomerid.fphone", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "ffollowtime", "Name": "跟进时间", "MapType": 1, "SrcFieldId": "@currentDate", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "ffollowerid", "Name": "跟进人", "MapType": 1, "SrcFieldId": "@userId", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fdeptid", "Name": "跟进部门", "MapType": 1, "SrcFieldId": "@currentDeptId", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fstaffid", "Name": "跟进员工", "MapType": 1, "SrcFieldId": "@currentStaffId", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcetype", "Name": "源单类型", "MapType": 1, "SrcFieldId": "'ydj_saleintention'", "MapActionWhenGrouping": 0, "Order": 0}, {"Id": "fsourcenumber", "Name": "源单编号", "MapType": 0, "SrcFieldId": "f<PERSON><PERSON>", "MapActionWhenGrouping": 0, "Order": 0}], "BillGroups": [{"Id": "f<PERSON><PERSON>", "Order": 1}], "FieldGroups": []}