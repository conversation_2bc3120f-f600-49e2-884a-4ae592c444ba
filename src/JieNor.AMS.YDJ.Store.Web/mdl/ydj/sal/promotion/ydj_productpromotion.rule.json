{
  //规则引擎基类
  "base": "/mdl/ydj/sal/promotion/ydj_promotion.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "fpublishstatus_0",
      "expression": "field:$*;menu:tbUnpublish$tbPublish,tbQueryInventory,btnStandardCustom_prd,btnStandardCustom_gift,tbQueryInventory_gift|fpublishstatus=='0'"
    },
    {
      "id": "fpublishstatus_1",
      "expression": "field:*$fenddate;menu:tbPublish,tbQueryInventory,btnStandardCustom_prd,btnStandardCustom_gift,tbQueryInventory_gift$tbUnpublish|fpublishstatus=='1'"
    },
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave,tbQueryInventory,btnStandardCustom_prd,btnStandardCustom_gift,tbQueryInventory_gift|fstatus==''"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //选择商品，携带单位与计价单位
    { "expression": "funitid=fmaterialid__funitid|fmaterialid=='' or 1==1" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo=getAuxPropValue(fmaterialid)" },
    //选择商品，携带单位与计价单位
    { "expression": "funitid_g=fmaterialid_g__funitid|fmaterialid_g=='' or 1==1" },
    //选择商品时，携带出默认辅助属性
    { "expression": "fattrinfo_g=getAuxPropValue(fmaterialid_g)" },
    { "expression": "fmtrlimage=getImages(fmaterialid,fattrinfo)" },
    { "expression": "fmtrlimage_g=getImages(fmaterialid_g,fattrinfo_g)" },
    { "expression": "flowestprice=fprice|flowestprice<=0" },
    //【折率】=【销售价】/【零售价】，当零售价为0时，折率则为0。
    { "expression": "fdistrate=fprice/fsalprice|fsalprice>0" },
    { "expression": "fdistrate=0|fsalprice==0" }
  ]
}