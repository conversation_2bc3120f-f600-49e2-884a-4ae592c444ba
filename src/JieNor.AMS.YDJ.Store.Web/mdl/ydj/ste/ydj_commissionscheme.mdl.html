<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_commissionscheme" basemodel="bd_basetmpl" el="3" cn="提成方案" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_commissionscheme" pn="fbillhead" cn="提成方案">
        <select lix="10" el="149" ek="fbillhead" id="fobjecttype" fn="fobjecttype" pn="fobjecttype" dataviewname="v_ydj_commissionobject" cn="提成对象类型" width="100" visible="-1" lock="0" defval="'ydj_position'" must="1">
            <dataSourceDesc formId="ydj_position" filter="" caption="岗位"></dataSourceDesc>
            <dataSourceDesc formId="ydj_staff" filter="" caption="员工"></dataSourceDesc>
        </select>
        <input lix="20" el="150" ek="fbillhead" id="fobjectid" fn="fobjectid" pn="fobjectid" ctlfk="fobjecttype" cn="提成对象名称" width="100" visible="-1" lock="0" must="1"/>
        <input lix="30" el="116" ek="fbillhead" id="fdeductrefund" fn="fdeductrefund" pn="fdeductrefund" cn="退货扣减本单当月业绩" width="100" visible="-1" lock="0"/>
        <input lix="40" el="116" ek="fbillhead" id="finactivediscount" fn="finactivediscount" pn="finactivediscount" cn="非活动折扣业绩折上折" width="100" visible="-1" lock="0" />
        <input lix="50" el="102" ek="fbillhead" id="fsalesemiannualratio" fn="fsalesemiannualratio" pn="fsalesemiannualratio" cn="销售半年度提成比例" width="100" visible="-1" lock="0" format="0,000.00" />
        <input lix="60" el="102" ek="fbillhead" id="fsalemonthlyratio" fn="fsalemonthlyratio" pn="fsalemonthlyratio" cn="销售经理月度提成比例" width="100" visible="-1" lock="0" format="0,000.00" />
        <input lix="70" el="102" ek="fbillhead" id="fleadermonthlyratio" fn="fleadermonthlyratio" pn="fleadermonthlyratio" cn="店长月度提成比例" width="100" visible="-1" lock="0" format="0,000.00"  />
        <input lix="80" el="102" ek="fbillhead" id="fleaderquarterratio" fn="fleaderquarterratio" pn="fleaderquarterratio" cn="店长季度提成比例" width="100" visible="-1" lock="0" format="0,000.00" />
        <input lix="90" el="105" ek="fbillhead" id="fsalesemiannualgoal" fn="fsalesemiannualgoal" pn="fsalesemiannualgoal" cn="销售半年度目标" width="100" visible="-1" lock="0" format="0,000.00" />
        <input ek="fbillhead" id="fname" must="1"/>
    </div>

    <table id="fentry" el="52" pk="fentryid" tn="t_ydj_commissionschemeentry" pn="fentry" cn="提成明细" kfks="fratio" apipn="entry">
        <tr>
            <th lix="10" el="101" ek="fentry" id="flowerlimit" fn="flowerlimit" pn="flowerlimit" cn="金额下限" desc="最小值是0,不能设置无穷大值" width="90" visible="-1"></th>
            <th lix="30" el="102" ek="fentry" id="fratio" fn="fratio" pn="fratio" cn="提成比例" width="90" visible="-1"></th>
            <th lix="30" el="101" ek="fentry" id="fupperlimit" fn="fupperlimit" pn="fupperlimit" cn="金额上限" width="90" visible="-1" desc="最小值是1,0表示无穷大值"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="销售半年度提成比例不能小于等于0" data="{'expr':'fsalesemiannualratio > 0 or fobjecttype=\'ydj_staff\'','message':'销售半年度提成比例不能小于等于0！'}"></li>
            <li el="11" vid="510" cn="销售月度提成比例不能小于等于0" data="{'expr':'fsalemonthlyratio > 0 or fobjecttype=\'ydj_staff\'','message':'销售月度提成比例不能小于等于0！'}"></li>
            <li el="11" vid="510" cn="销售半年度目标不能小于等于0" data="{'expr':'fsalesemiannualgoal > 0 or fobjecttype=\'ydj_staff\'','message':'销售半年度目标不能小于等于0！'}"></li>
            <li el="11" vid="510" cn="店长月度提成比例不能小于等于0" data="{'expr':'fleadermonthlyratio > 0 or fobjecttype=\'ydj_position\'','message':'店长月度提成比例不能小于等于0！'}"></li>
            <li el="11" vid="510" cn="店长季度提成比例不能小于等于0" data="{'expr':'fleaderquarterratio > 0 or fobjecttype=\'ydj_position\'','message':'店长季度提成比例不能小于等于0！'}"></li>
        </ul>
    </div>

</body>
</html>
