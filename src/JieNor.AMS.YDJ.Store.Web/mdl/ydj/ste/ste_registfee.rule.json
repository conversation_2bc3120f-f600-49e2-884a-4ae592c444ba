{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lock_tbPaymentAndtbRefund",
      "expression": "menu:tbPayment,tbRefund$|fstatus!='E'"
    },
    {
      "id": "unlock_tbPaymentAndtbRefund",
      "expression": "menu:$tbPayment,tbRefund|fstatus=='E'"
    },
    //锁定按含税单价录入字段
    // {
    //   "id": "lock_fisincludetax",
    //   "expression": "field:fisincludetax$|fisexcludetax==false"
    // },
    // //解锁按含税单价录入合计字段
    // {
    //   "id": "unlock_fisincludetax",
    //   "expression": "field:$fisincludetax|fisexcludetax==true"
    // },
    // //锁定单价、价税合计字段,解锁含税单价、不含税金额字段
    // {
    //   "id": "lock_fprice",
    //   "expression": "field:fprice,ftaxamount$ftaxprice,famount|fisexcludetax==true and fisincludetax==true"
    // },
    // //锁定含税单价、价税合计字段,解锁单价、不含税金额字段
    // {
    //   "id": "lock_ftaxprice",
    //   "expression": "field:ftaxprice,ftaxamount$fprice,famount|fisexcludetax==true and fisincludetax==false"
    // },
    // //锁定单价、不含税金额字段，解锁含税单价、价税合计字段
    // {
    //   "id": "lock_famount",
    //   "expression": "field:famount,fprice$ftaxamount,ftaxprice|fisexcludetax==false and fisincludetax==true"
    // },
    //单位类型字段的锁定与解锁，条件：源单类型为销售合同时，锁定单位类型字段，否则解锁单位类型字段
    {
      "id": "lock_frelatetype",
      "expression": "field:frelatetype|fsourcetype=='ydj_order'"
    },
    {
      "id": "unlock_frelatetype",
      "expression": "field:$frelatetype|fsourcetype!='ydj_order'"
    },
    {
      "id": "lock_tbMergeRegistFee",
      "expression": "menu:tbMergeRegistFee|frelatetype!='ste_channel' or frelatecusid=='' or fstatus=='D' or fstatus=='E'"
    },
    {
      "id": "unlock_tbMergeRegistFee",
      "expression": "menu:$tbMergeRegistFee|frelatetype=='ste_channel' and frelatecusid!='' and fstatus!='D' and fstatus!='E'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    //选择人员，自动带出部门
    { "expression": "ftrainingdept=frelatemanid__fdeptid|frelatemanid=='' or 1==1" },
    // { "expression": "fisincludetax=true|fisexcludetax==false" },
    // //价外税（含税单价）
    // { "expression": "fprice=ftaxprice-((ftaxprice/(1+ftaxrate/100))*ftaxrate/100)|fisexcludetax==true and fisincludetax==true" },
    // { "expression": "famount=fprice*fqty|fisexcludetax==true and fisincludetax==true" },
    // { "expression": "ftax=(ftaxprice/(1+ftaxrate/100))*ftaxrate*fqty/100|fisexcludetax==true and fisincludetax==true" },
    // { "expression": "ftaxamount=ftaxprice*fqty|fisexcludetax==true and fisincludetax==true" },
    // //价内税（含税单价）
    // { "expression": "fprice=ftaxprice-(ftaxprice*ftaxrate/100)|fisexcludetax==false and fisincludetax==true" },
    // { "expression": "famount=fprice*fqty|fisexcludetax==false and fisincludetax==true" },
    // { "expression": "ftax=ftaxprice*ftaxrate*fqty/100|fisexcludetax==false and fisincludetax==true" },
    // { "expression": "ftaxamount=ftaxprice*fqty|fisexcludetax==false and fisincludetax==true" },
    //价外税（不含税单价）
    // { "expression": "ftaxprice=fprice*(1+ftaxrate/100)|fisexcludetax==true and fisincludetax==false" },
    // { "expression": "famount=fprice*fqty|fisexcludetax==true and fisincludetax==false" },
    // { "expression": "ftax=fprice*ftaxrate*fqty/100|fisexcludetax==true and fisincludetax==false" },
    { "expression": "ftaxamount=fprice*fqty" },
    { "expression": "fprice=ftaxamount/fqty" },
    //汇总字段
    // { "expression": "fsumamount=sum(famount)" },
    { "expression": "fsumtaxamount=sum(ftaxamount)" },
    // { "expression": "fmoney=fsumtaxamount|fisaccountcost==true" },
    // { "expression": "fmoney=fsumamount|fisaccountcost==false" },
    //待结算金额=价税合计-已结算金额-待确认金额
    { "expression": "funsettleamount=fsumtaxamount-fsettledamount-funconfirmamount" }
  ]
}