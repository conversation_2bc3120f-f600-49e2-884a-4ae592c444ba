<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_customerrecord" basemodel="bill_basetmpl" el="1" cn="销售机会" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_customerrecord" pn="fbillhead" cn="销售机会">
        <input group="基本信息" el="100" type="text" id="fcustomername" ek="fbillhead" fn="fcustomername" pn="fcustomername" cn="名称" visible="-1" lix="2" must="1" />
        <input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" pn="fphone" cn="联系电话" visible="-1" lix="4" />
        <input group="基本信息" el="100" type="text" id="fwechat" ek="fbillhead" fn="fwechat" pn="fwechat" cn="微信" len="50" visible="-1" lix="6" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcustomersource" fn="fcustomersource" pn="fcustomersource" cn="来源渠道" cg="来源渠道" refid="bd_enum" dfld="fenumitem" lix="8"></select>
        <input group="基本信息" el="112" type="date" id="fgoshopdate" ek="fbillhead" fn="fgoshopdate" pn="fgoshopdate" cn="接待日期" visible="-1" defval="@currentshortdate" lix="10" must="1" />

        <input group="基本信息" el="152" ek="fbillhead" id="fphase" fn="fphase" pn="fphase" visible="-1" cn="商机阶段" lock="2" copy="0" lix="14" ts="" vals="'customerrecord_phase_01':'商机报备','customerrecord_phase_02':'意向跟进','customerrecord_phase_03':'方案设计','customerrecord_phase_04':'合同签订','customerrecord_phase_05':'已成单'" defval="'customerrecord_phase_02'" />
        <!--客户意向-->
        <select group="客户意向" el="122" ek="fbillhead" visible="-1" id="fdemand" fn="fdemand" pn="fdemand" cn="需求等级" cg="需求等级" refid="bd_enum" dfld="fenumitem" lix="16"></select>

        <select group="客户意向" el="122" ek="fbillhead" visible="1150" id="fspace" fn="fspace" pn="fspace" cn="购买空间" cg="空间" refid="bd_enum" dfld="fenumitem" lix="60"></select>
        <input group="客户意向" el="131" ek="fbillhead" id="fproduct_type" fn="fproduct_type" ts="" visible="1150" cn="意向品类" refid="ydj_category" lix="23" sbm="true" len="2000" />
        <input group="客户意向" el="100" ek="fbillhead" id="fprod_type" fn="fprod_type" ts="" visible="0" cn="意向品类传参使用" lix="23" sbm="true" len="2000" />
        <input group="客户意向" el="100" ek="fbillhead" type="text" id="fproduct_size" fn="fproduct_size" pn="fproduct_size" cn="产品尺寸" visible="1150" lix="6" />
        <input group="客户意向" el="116" ek="fbillhead" id="fintention_sign" fn="fintention_sign" pn="fintention_sign" cn="是否有意向" defVal="0" visible="1150" apipn="isMain" />
        <input group="客户意向" el="116" ek="fbillhead" id="fwill_visit_type" fn="fwill_visit_type" pn="fwill_visit_type" cn="是否愿意到店" defVal="0" visible="1150" apipn="isMain" />
        <input group="客户意向" el="116" ek="fbillhead" id="fvisit_type" fn="fvisit_type" pn="fvisit_type" cn="是否到店" defVal="0" visible="1150" apipn="isMain" />
        <input group="客户意向" el="116" ek="fbillhead" id="furgency_type" fn="furgency_type" pn="furgency_type" cn="是否紧急" defVal="0" visible="1150" apipn="isMain" />
        <input group="客户意向" el="116" ek="fbillhead" id="fdelivery_type" fn="fdelivery_type" pn="fdelivery_type" cn="是否已交房" defVal="0" visible="1150" apipn="isMain" />
        <input group="客户意向" el="116" ek="fbillhead" id="fadd_wechat_type" fn="fadd_wechat_type" pn="fadd_wechat_type" cn="是否添加微信" defVal="0" visible="1150" apipn="isMain" />

        <input group="基本信息" el="100" type="text" id="faddress" ek="fbillhead" fn="faddress" pn="faddress" cn="详细地址" visible="-1" lix="18" />
        <input group="基本信息" el="106" type="text" id="fdeptid" ek="fbillhead" fn="fdeptid" refid="ydj_dept" reflvt="1" ts="" visible="-1" cn="所属部门" lix="20" defVal="@currentDeptId" dfld="" />
        <input group="基本信息" el="106" type="text" id="fdutyid" ek="fbillhead" fn="fdutyid" refid="ydj_staff" ts="" visible="-1" cn="负责人" lix="22" defVal="@currentStaffId" />
        <input group="基本信息" el="113" type="datetime" id="ffollowtime" ek="fbillhead" fn="ffollowtime" ts="" cn="最后跟进时间" visible="-1" lock="-1" lix="24" defval="@currentlongdate" />
        <input group="基本信息" el="106" type="text" id="ffollowerid" ek="fbillhead" fn="ffollowerid" refid="sec_user" ts="" visible="-1" cn="最后跟进人" lock="-1" lix="26" defval="@userid" />
        <input group="基本信息" el="152" ek="fbillhead" id="ftype" fn="ftype" pn="ftype" visible="-1" cn="商机类型" lock="2" copy="0" lix="28" ts="" vals="'2':'个人','1':'公司'" defval="'2'" must="1" />

        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fchancestatus" fn="fchancestatus" pn="fchancestatus" cn="业务状态" cg="业务状态(销售机会)" refid="bd_enum" dfld="fenumitem" copy="0" lix="30" lock="-1"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcountry" fn="fcountry" pn="fcountry" cn="国家或地区" cg="国家或地区" refid="bd_enum" dfld="fenumitem" lix="32" defval="'CN'"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="34"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="36"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lix="38"></select>

        <input group="基本信息" el="106" type="text" id="fbuildingid" ek="fbillhead" fn="fbuildingid" pn="fbuildingid" refid="ydj_building" ts="" visible="1150" cn="楼盘" lix="40" />
        <input group="基本信息" el="106" type="text" id="fcustomerid" ek="fbillhead" fn="fcustomerid" refid="ydj_customer" ts="" visible="1150" cn="所属客户" lix="42" lock="-1" dfld="fname,fcontacts,fphone" />


        <input group="基本信息" el="106" type="text" id="fchannelid" ek="fbillhead" fn="fchannelid" refid="ste_channel" ts="" visible="1150" cn="合作渠道" lix="44" />
        <input group="基本信息" el="100" type="text" id="fleadssource" ek="fbillhead" fn="fleadssource" pn="fleadssource" cn="源线索" visible="0" lix="46" lock="-1" copy="0" />
        <input el="100" type="text" id="fleadssourceid" ek="fbillhead" fn="fleadssourceid" pn="fleadssourceid" cn="源线索id" visible="0" lock="-1" copy="0" />
        <input group="基本信息" el="135" ek="fbillhead" visible="1150" id="fimage" fn="fimage" pn="fimage" cn="附件" lix="48" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fgender" fn="fgender" pn="fgender" cn="性别" cg="性别" refid="bd_enum" dfld="fenumitem" lix="50"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fage" fn="fage" pn="fage" cn="年龄段" cg="年龄段" refid="bd_enum" dfld="fenumitem" lix="52"></select>
        <input group="基本信息" el="100" type="text" id="fchancesource" ek="fbillhead" fn="fchancesource" pn="fchancesource" cn="源机会" visible="0" lix="54" lock="-1" copy="0" />
        <input group="基本信息" el="100" type="text" id="fclosereason" ek="fbillhead" fn="fclosereason" pn="fclosereason" cn="关闭原因" len="500" visible="32" lock="-1" lix="56" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fbusinessclos" fn="fbusinessclos" pn="fbusinessclos" lock="-1" cn="商机关闭原因" cg="商机关闭原因" refid="bd_enum" dfld="fenumitem" lix="57"></select>
        <input group="基本信息" el="100" type="text" id="fintentionno" ek="fbillhead" fn="fintentionno" pn="fintentionno" ts="" visible="0" cn="销售意向单编号" lix="58" lock="-1" copy="0" />
        <input group="基本信息" el="100" type="text" id="forderno" ek="fbillhead" fn="forderno" pn="forderno" ts="" visible="1150" cn="销售合同编号" lix="60" lock="-1" copy="0" />
        <input group="基本信息" el="123" ek="fbillhead" visible="1150" id="fbilltypeid" fn="fbilltypeid" pn="fbilltypeid" refid="bd_billtype" cn="单据类型" copy="0" lix="62" />

        <input group="基本信息" el="106" ek="fbillhead" id="finnercustomerid" fn="finnercustomerid" pn="finnercustomerid" visible="1150" cn="加盟商"
               lock="-1" copy="0" lix="64" notrace="true" ts="" refid="ydj_customer" filter="" reflvt="0" dfld="" />



        <input group="基本信息" el="152" ek="fbillhead" id="fassigntype" fn="fassigntype" pn="fassigntype" visible="1150" cn="商机分配类型" lock="2" copy="0" lix="66" ts="" vals="'customerrecord_assigntype_01':'指派'" />

        <input group="基本信息" el="100" type="text" id="fcontacts" ek="fbillhead" fn="fcontacts" pn="fcontacts" cn="联系人" visible="1150" lix="68" copy="0" />
        <!--组织架构-->
        <input group="基本信息" el="100" type="text" id="fpathname" ek="fbillhead" fn="fpathname" pn="fpathname" cn="组织架构路径名称" lock="-1" visible="1150" lix="70" />
        <input group="基本信息" el="100" type="text" id="fpath" ek="fbillhead" fn="fpath" pn="fpath" cn="组织架构" lock="-1" visible="1150" lix="72" />
        <!-- 防撞组别 -->
        <select group="客户意向" el="125" ek="fbillhead" visible="0" id="fsalecategory" fn="fsalecategory" pn="fsalecategory" cn="意向品类" cg="意向品类" refid="bd_enum" dfld="fenumitem" lix="12"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="-1" id="fsalecategory_type" fn="fsalecategory_type" pn="fsalecategory_type" cn="防撞组别" cg="防撞组别" refid="bd_enum" dfld="fenumitem" lix="12"></select>
        <select group="客户意向" el="125" ek="fbillhead" visible="1150" id="fspace" fn="fspace" pn="fspace" cn="空间" cg="空间" refid="bd_enum" dfld="fenumitem" lix="74"></select>
        <select group="客户意向" el="125" ek="fbillhead" visible="1150" id="fpurpose" fn="fpurpose" pn="fpurpose" cn="商机用途" cg="商机用途" refid="bd_enum" dfld="fenumitem" lix="76"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="fstyle" fn="fstyle" pn="fstyle" cn="装修风格" cg="风格" refid="bd_enum" dfld="fenumitem" lix="78"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="froom_enum" fn="froom_enum" pn="froom_enum" cn="室" cg="户型枚举" refid="bd_enum" dfld="fenumitem" lix="80"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="fhall_enum" fn="fhall_enum" pn="fhall_enum" cn="厅" cg="户型枚举" refid="bd_enum" dfld="fenumitem" lix="82"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="ftoilet_enum" fn="ftoilet_enum" pn="ftoilet_enum" cn="卫" cg="户型枚举" refid="bd_enum" dfld="fenumitem" lix="84"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="fbalcony_enum" fn="fbalcony_enum" pn="fbalcony_enum" cn="阳台" cg="户型枚举" refid="bd_enum" dfld="fenumitem" lix="86"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="farea" fn="farea" pn="farea" cn="面积" cg="面积" refid="bd_enum" dfld="fenumitem" lix="88"></select>
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="frenovation" fn="frenovation" pn="frenovation" cn="装修进度" cg="装修进度" refid="bd_enum" dfld="fenumitem" lix="90"></select>
        <input group="客户意向" el="112" type="date" id="fexpectdate" ek="fbillhead" fn="fexpectdate" pn="fexpectdate" cn="预计成交日期" visible="32" defval="@currentshortdate" lix="92" />
        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="fbudget" fn="fbudget_scope" pn="fbudget_scope" cn="预算范围" cg="预算范围" refid="bd_enum" dfld="fenumitem" lix="36"></select>

        <!--移除字段-->
        <select group="客户意向" el="105" ek="fbillhead" visible="0" id="fbudget_not" fn="fbudget" pn="fbudget" cn="预算" lix="90"></select>

        <select group="客户意向" el="122" ek="fbillhead" visible="32" id="frequirement_type" fn="frequirement_type" pn="frequirement_type" cn="需求类型" cg="需求类型" refid="bd_enum" dfld="fenumitem" lix="90"></select>


        <input group="业务进度" el="100" ek="fbillhead" id="flastnodename" fn="flastnodename" pn="flastnodename" cn="最新业务进度" visible="1150" width="80" lix="180" lock="-1" />
        <input group="业务进度" el="101" ek="fbillhead" id="flastnodeweight" fn="flastnodeweight" pn="flastnodeweight" cn="最新业务进度权重" visible="0" width="80" lix="180" lock="-1" />

        <!-- 回收时反写，用于领用判断 -->s
        <input group="后台字段" el="106" type="text" id="folddutyid" ek="fbillhead" fn="folddutyid" refid="ydj_staff" ts="" visible="0" cn="原责任人" lix="25" />
        <input group="后台字段" el="113" type="datetime" id="frecycletime" ek="fbillhead" fn="frecycletime" ts="" cn="回收时间" visible="1150" lock="-1" lix="180" />
        <!--企业微信用户编码-->
        <input group="后台字段" el="100" ek="fbillhead" visible="0" type="text" id="fworkwxuserid" fn="fworkwxuserid" pn="fworkwxuserid" lock="-1" cn="企业微信用户编码" />
        <input group="后台字段" el="100" ek="fbillhead" visible="0" type="text" id="house_type" fn="house_type" pn="house_type" lock="-1" cn="户型大小" />

        <input group="基本信息" el="106" ek="fbillhead" id="freferrer" fn="freferrer" pn="freferrer" visible="1150" cn="推荐人" refid="ydj_customer" />

        <input group="基本信息" el="100" ek="fbillhead" id="oppid" fn="oppid" pn="oppid" visible="0" cn="中台ID" />
        <!--销售员信息-->
        <table id="fdutyentry" el="52" pk="fentryid" tn="t_ydj_customerrecordduty" pn="fdutyentry" cn="销售员信息" kfks="fdutyid_e" apipn="dutyEntry">
            <tr>
                <th el="116" ek="fdutyentry" id="fismain" fn="fismain" pn="fismain" cn="主要负责" width="80" visible="96" lock="-1" apipn="isMain"></th>
                <th el="106" ek="fdutyentry" id="fdutyid_e" fn="fdutyid" pn="fdutyid" cn="销售员" refid="ydj_staff" sformid="" width="110" visible="96" apipn="saleMan"></th>
                <th el="106" ek="fdutyentry" id="fdeptid_e" fn="fdeptid" pn="fdeptid" cn="所属部门" refid="ydj_dept" sformid="" width="110" visible="0" apipn="dept"></th>
                <th lix="116" el="113" ek="fdutyentry" id="fjoindate" fn="fjoindate" pn="fjoindate" cn="加入时间" defval="@currentlongdate"
                    lock="-1" copy="0" width="160" visible="0" canchange="true"></th>
                <th el="100" ek="fdutyentry" id="fdescription_ed" fn="fdescription" pn="fdescription" cn="备注" width="170" visible="96" apipn="description"></th>
            </tr>
        </table>

        <!--业务进度-->
        <table id="fndentry" el="56" pk="fndentryid" tn="t_ydj_crnodedefine" cn="业务进度" apipn="ndEntry" kfks="fnodename">
            <tr>
                <th lix="10" ek="fndentry" el="100" id="fnodename" fn="fnodename" pn="fnodename" cn="节点名称" visible="1150" lix="100" copy="0" width="200" lock="-1" must="1">节点名称</th>
                <th lix="10" ek="fndentry" el="100" id="fnodecontent" fn="fnodecontent" pn="fnodecontent" cn="节点内容" visible="1150" copy="0" width="200" lock="-1">节点内容</th>
                <th lix="20" ek="fndentry" el="152" id="fnodetype" fn="fnodetype" pn="fnodetype" visible="1150" cn="节点类型" copy="0" lock="-1" notrace="true" ts="" vals="1:'业务节点',2:'人工节点',3:'协同节点'" defval="'1'"></th>
                <th lix="20" ek="fndentry" el="101" id="fnodeindex" fn="fnodeindex" pn="fnodeindex" visible="1150" cn="节点顺序" copy="0" width="200" lock="-1">节点顺序</th>
                <th lix="20" ek="fndentry" el="152" id="fnodestatus" fn="fnodestatus" pn="fnodestatus" visible="1150" cn="节点状态" copy="0" lock="-1" notrace="true" ts="" vals="'0':'计划','1':'完成'"></th>
                <th lix="40" ek="fndentry" el="106" id="freponseuserid" fn="freponseuserid" pn="freponseuserid" cn="操作人id" refId="Sec_User" visible="0" width="100" lock="-1" copy="0" apipn="operatorId"></th>
                <th lix="50" ek="fndentry" el="113" id="ffinishtime" fn="ffinishtime" pn="ffinishtime" cn="完成时间" visible="96" width="170" lock="-1" copy="0" apipn="operatorTime"></th>
            </tr>
        </table>

        <!-- 收藏案例 -->
        <table id="favoritecases" el="52" pk="fentryid" tn="t_ydj_favoritecases" pn="favoritecases" cn="收藏案例">
            <tr>
                <th el="100" ek="favoritecases" id="fcasebillno" fn="fcasebillno" pn="fcasebillno" refid="ydj_dept" cn="案例id" visible="96" copy="0"></th>
                <th el="100" ek="favoritecases" id="fcasename" fn="fcasename" pn="fcasename" cn="案例名" width="200" visible="96" copy="0"></th>
                <th el="100" ek="favoritecases" id="fcasenumber" fn="fcasenumber" pn="fcasenumber" cn="案例编号" visible="96" copy="0"></th>
                <th el="111" ek="favoritecases" id="fimageid" fn="fimageid" pn="fimageid" cn="首页图片" visible="96" copy="0"></th>
            </tr>
        </table>

        <!-- 意向商品 -->
        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_intentionprod" pn="fentry" cn="意向商品" kfks="goods_id">
            <tr>
                <th el="108" ek="fentry" visible="0" id="fnumber_e" fn="fnumber" pn="fnumber" cn="行编码" lock="-1"></th>
                <th el="106" ek="fentry" id="goods_id" fn="goods_id" pn="goods_id" cn="商品" refid="ydj_product" multsel="true"
                    dfld="fspecifica,fvolume,fgrossload,fpacksize,fstockunitid,fendpurchase,fguideprice,fseriesid,fbrandid" sformid=""
                    width="160" visible="-1" apipn="product" filter="fispulloff='0'"></th>
                <th el="107" ek="fentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码" lock="-1" copy="1" notrace="true" ts=""
                    ctlfk="goods_id" dispfk="fnumber"></th>
                <th el="107" ek="fentry" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="goods_id" sformid="" width="100" lock="-1"></th>
                <th el="107" ek="fentry" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="goods_id" sformid="" width="100" lock="-1"></th>
                <th el="107" ek="fentry" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="-1" cn="规格型号" lock="-1" copy="1" notrace="true" ts="" ctlfk="goods_id" dispfk="fspecifica"></th>
                <th el="132" ek="fentry" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="goods_id" nstdfk="funstdtype" pricefk="fprice" width="160" lock="0" visible="0" apipn="attrInfo"></th>
                <th el="104" ek="fentry" id="fprice" fn="fprice" pn="fprice" cn="零售价" lock="-1" width="70" visible="1150" apipn="price" canchange="true"></th>
                <th el="103" ek="fentry" id="fattention_num" fn="fattention_num" pn="fattention_num" cn="意向数量" width="200" visible="96" copy="0"></th>
                <th el="100" ek="fentry" visible="0" id="fsuitcombnumber" fn="fsuitcombnumber" pn="fsuitcombnumber" cn="套件组合号" lock="-1"></th>
                <th el="109" ek="fentry" id="fbizunitid" fn="fbizunitid" pn="fbizunitid" cn="销售单位" refid="ydj_unit" sformid="" ctlfk="goods_id" width="90" visible="-1"></th>
                <th lix="21" el="109" ek="fentry" id="funitid" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="goods_id"  width="80" visible="-1" apipn="unit"></th>
                <th lix="270" el="116" ek="fentry" visible="0" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="goods_id" dispfk="funstdtype" width="90" copy="0" lock="0" refValueType="116"></th>
                <th ek="fentry" lix="214" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="goods_id" width="200" visible="0" canchange="true"></th>
                <th ek="fentry" lix="243" el="107" id="fattribute" f="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="goods_id" dispfk="fattribute" refvt="0"></th>
                <th lix="245" el="107" ek="fentry" id="fcustom" fn="fcustom" pn="fcustom" visible="0" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="goods_id" dispfk="fcustom" refvt="116"></th>
                <th lix="300" el="107" ek="fentry" visible="0" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" ctlfk="goods_id" dispfk="fselcategoryid" lock="-1"></th>
            </tr>
        </table>
    </div>

    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" cn="预计成交日期必须大于接待日期" data="{'expr':'fexpectdate>=fgoshopdate','message':'预计成交日期必须大于接待日期!'}"></li>
        </ul>
        <ul el="10" id="QuotedPrice" op="QuotedPrice" opn="报价" data="" permid="ydj_customerrecord_createquotation"></ul>
        <ul el="10" id="PlaceOrder" op="PlaceOrder" opn="下单" data="" permid="ydj_customerrecord_createorder"></ul>

        <ul el="10" id="customerrecordreplace" op="customerrecordreplace" opn="更换负责人" data="" permid="ydj_customerrecord_replace"></ul>
        <ul el="10" id="customerrecordclaim" op="customerrecordclaim" opn="分配" data="" permid="ydj_customerrecord_allocate"></ul>
        <ul el="10" id="customerrecordclose" op="customerrecordclose" opn="关闭" data="" permid="ydj_customerrecord_close"></ul>
        <ul el="10" id="cusreturn" op="cusreturn" opn="回收" data="" permid="ydj_customerrecord_cusreturn"></ul>
        <!--<ul el="10" id="customerrecordreturn" op="customerrecordreturn" opn="回收到门店" data="" permid="ydj_customerrecord_deallocate"></ul>
    <ul el="10" id="recycletocompany" op="recycletocompany" opn="回收到公司" data="" permid="ydj_customerrecord_recycletocompany"></ul>-->
        <!-- <ul el="10" id="generateCustomer" op="generateCustomer" opn="转客户" data="" permid="ydj_customerrecord_generatecustomer"></ul> -->
        <ul el="10" id="followerrecord" op="followerrecord" opn="跟进" data="" permid="followerrecord"></ul>
        <ul el="10" id="finish" op="finish" opn="成单关闭" data="" permid="ydj_customerrecord_finish"></ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fintentionprod','froom_enum','fhall_enum','ftoilet_enum','fbalcony_enum','fnumber','fadd_wechat_type','faddress','fbudget','fchancestatus','fcustomername','fdemand','fdescription','fentryList','fgender','fintention_sign','fphone','fprod_type','fproduct_size','frenovation','frequirement_type','fspace','fstyle','furgency_type','fvisit_type','fwill_visit_type','house_type','house_size']}" permid=""></ul>
        <ul el="10" id="commoncus" op="commoncus" opn="查看公海商机" data="" permid="fw_commoncus"></ul>
    </div>


    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="ydj_customerrecord_createquotation" cn="报价"></ul>
        <ul el="12" id="ydj_customerrecord_createorder" cn="下单"></ul>
        <ul el="12" id="ydj_customerrecord_replace" cn="更换负责人"></ul>
        <ul el="12" id="ydj_customerrecord_allocate" cn="分配"></ul>
        <ul el="12" id="ydj_customerrecord_close" cn="关闭"></ul>
        <ul el="12" id="ydj_customerrecord_cusreturn" cn="回收"></ul>
        <ul el="12" id="fw_commoncus" cn="查看公海商机"></ul>
        <!--<ul el="12" id="ydj_customerrecord_deallocate" cn="回收到门店"></ul>
    <ul el="12" id="ydj_customerrecord_recycletocompany" cn="回收到公司"></ul>-->
        <!-- <ul el="12" id="ydj_customerrecord_generatecustomer" cn="转客户"></ul> -->
        <ul el="12" id="followerrecord" cn="跟进"></ul>
        <ul el="12" id="ydj_customerrecord_finish" cn="成单关闭"></ul>
    </div>

</body>
</html>

