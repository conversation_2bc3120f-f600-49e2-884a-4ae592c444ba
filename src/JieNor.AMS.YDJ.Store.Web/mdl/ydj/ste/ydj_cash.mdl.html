<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_cash" basemodel="bill_basetmpl" el="1" cn="收银单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_cash" pn="fbillhead" cn="收银单">
		<!--基本信息-->
        <input group="基本信息" el="100" type="text" id="forderno" ek="fbillhead" fn="forderno" cn="订货单" visible="-1" />

        <input el="112" group="基本信息" type="date" id="fdate" ek="fbillhead" fn="fdate" ts="" cn="日期" visible="-1" defval="@currentshortdate" />
        <select el="122" group="基本信息" ek="fbillhead" visible="-1" id="fszlx" fn="fszlx" pn="fszlx" cn="收支类型" cg="收支类型" refid="bd_enum" dfld="fenumitem" lock="2" ></select>
        <select el="122" group="基本信息" ek="fbillhead" visible="-1" id="fusage" fn="fusage" pn="fusage" cn="支付用途" cg="支付用途" refid="bd_enum" dfld="fenumitem" defval="'usage_type_01'" lock="-1"></select>
        <input el="106" group="基本信息" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" refid="ydj_customer" lock="2"
        	 dfld="fbalance"/>

        <input el="105" group="基本信息" type="text" id="fsum" ek="fbillhead" fn="fsum" ts="" visible="-1" cn="总额" lock="-1" />
        <input el="105" group="基本信息" type="text" id="ftotalmoney" ek="fbillhead" fn="ftotalmoney" ts="" visible="-1" cn="订单金额" lock="-1" />
        <!--结算信息 -->
        <select el="122" group="结算信息" ek="fbillhead" visible="-1" id="fpay" fn="fpay" pn="fpay" cn="结算方式" cg="结算方式" refid="bd_enum" dfld="fenumitem" ></select>
        <input el="106" group="结算信息" ek="fbillhead" visible="-1" id="fbanknumid" fn="fbanknumid" pn="fbanknumid" cn="银行账号" refid="ydj_banknum" />
        <input el="105" group="结算信息" type="text" id="fmoney" ek="fbillhead" fn="fmoney" ts="" visible="-1" cn="支付金额" />
        <input el="116" group="结算信息" ek="fbillhead" visible="-1" id="fisdeposit" fn="fisdeposit" pn="fisdeposit" cn="定金支付"  />
        <input el="105" group="结算信息" type="text" id="fdepositpay" ek="fbillhead" fn="fdepositpay" pn="fdepositpay" ts="" visible="-1" cn="定金支付金额" />
        <input el="105" group="结算信息" type="text" id="fdeposit" ek="fbillhead" fn="fdeposit" ts="" visible="-1" cn="本次定金消费" />
        <input el="111" group="结算信息" ek="fbillhead" visible="-1" id="fcredential" fn="fcredential" pn="fcredential" cn="凭据" />
    	
    	<!--源单信息-->
    	<table id="FLinkEntry" el="55" pk="fentryid" tn="t_ydj_cash_lk" pn="FLinkEntry" kfks="FSrcBillNo,FQty" cn="源单信息">
            <tr>
            	<th el="106" group="源单信息" ek="FLinkEntry" id="FSrcBillType" fn="FSrcBillType" ts="" cn="来源对象" refid="sys_bizobject" visible="-1" width="80" lock="-1">来源对象</th>                
            	<th el="100" group="源单信息" ek="FLinkEntry" id="FSrcBillNo" fn="FSrcBillNo" ts="" cn="来源对象编号" visible="-1" width="160" lock="-1">来源对象编号</th>
            	<th el="100" group="源单信息" ek="FLinkEntry" id="FSrcBizType" fn="FSrcBizType" ts="" cn="业务类型" visible="-1" width="80"  lock="-1">业务类型</th>
                <th el="105" group="源单信息" ek="FLinkEntry" id="FSrcQty" fn="FSrcQty" ts="" cn="应收金额" visible="-1" width="100" lock="-1">应收金额</th>
                <th el="105" group="源单信息" ek="FLinkEntry" id="FQty" fn="FQty" ts="" cn="结算金额" width="100" visible="-1">结算金额</th>
                <th el="105" group="源单信息" ek="FLinkEntry" id="FReceivedAmount" fn="FReceivedAmount" ts="" cn="已收金额" width="100" visible="-1" lock="-1">已收金额</th>
            </tr>
        </table>
    
    </div>

</body>
</html>