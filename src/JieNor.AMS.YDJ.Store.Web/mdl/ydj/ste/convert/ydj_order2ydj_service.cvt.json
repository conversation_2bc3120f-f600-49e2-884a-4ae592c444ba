{
  "Id": "ydj_order2ydj_service",
  "Number": "ydj_order2ydj_service",
  "Name": "销售合同下推服务单",
  "SourceFormId": "ydj_order",
  "TargetFormId": "ydj_service",
  "ActiveEntityKey": "fentry",
  "FilterString": "fstatus='E'",
  "Message": "服务单下推失败：\r\n1、销售合同必须是已审核状态！",
  "Visible": false,
  "FieldMappings": [
    {
      "Id": "fbilltypeid",
      "Name": "单据类型",
      "MapType": 0,
      "SrcFieldId": "'ydj_service_billtype_04'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fservicetype",
      "Name": "服务类型",
      "MapType": 0,
      "SrcFieldId": "'fres_type_03'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprovince",
      "Name": "省",
      "MapType": 0,
      "SrcFieldId": "fprovince",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcity",
      "Name": "市",
      "MapType": 0,
      "SrcFieldId": "fcity",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fregion",
      "Name": "区",
      "MapType": 0,
      "SrcFieldId": "fregion",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcollectadd",
      "Name": "详细地址",
      "MapType": 0,
      "SrcFieldId": "faddress",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcetype",
      "Name": "源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcenumber",
      "Name": "源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomerid",
      "Name": "客户",
      "MapType": 0,
      "SrcFieldId": "fcustomerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdeptid",
      "Name": "销售部门",
      "MapType": 0,
      "SrcFieldId": "fdeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fservicedate",
      "Name": "预约时间",
      "MapType": 0,
      "SrcFieldId": "fdeliverydate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcollectrel",
      "Name": "收货人",
      "MapType": 0,
      "SrcFieldId": "flinkstaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcollectpho",
      "Name": "联系电话",
      "MapType": 0,
      "SrcFieldId": "fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //商品信息字段
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fproductid",
      "MapActionWhenGrouping": 0,
      "Order": 15
    },
    {
      "Id": "fcategoryid",
      "Name": "商品类别",
      "MapType": 0,
      "SrcFieldId": "fcategoryid",
      "MapActionWhenGrouping": 0,
      "Order": 16
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo.fname",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "funitid_e",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 18
    },
    {
      "Id": "fqty_e",
      "Name": "数量",
      "MapType": 0,
      "SrcFieldId": "fbizqty",
      "MapActionWhenGrouping": 0,
      "Order": 19
    },
    {
      "Id": "famount_e",
      "Name": "金额",
      "MapType": 0,
      "SrcFieldId": "fdealamount_e",
      "MapActionWhenGrouping": 0,
      "Order": 17
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_order'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentry.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }
  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
  ]
}