{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    //不是新增单据则锁定所属部门和负责人字段
    {
      "id": "lock_deptid",
      "expression": "field:fdeptid|id != ''"
    },
    {
      "id": "lock_dutyid",
      "expression": "field:fdutyid|id != ''"
    },
    //已转换和已关闭状态字段全部锁定
    {
      "id": "lock_allfield",
      "expression": "field:*|fleadsstatus == 'leads_status_03' or fleadsstatus == 'leads_status_04' "
    },
    
    {
      //解锁认领按钮
      "id": "unlock_leadsclaim",
      "expression": "menu:$tbLeadsclaim| fleadsstatus == 'leads_status_01'"
    },
    {
      //禁用认领按钮
      "id": "lock_leadsclaim",
      "expression": "menu:tbLeadsclaim| fleadsstatus != 'leads_status_01'"
    },
    {
      //解锁更换负责人按钮
      "id": "unlock_leadsreplace",
      "expression": "menu:$tbLeadsreplace| fleadsstatus == 'leads_status_02'"
    },
    {
      //禁用更换负责人按钮
      "id": "lock_leadsreplace",
      "expression": "menu:tbLeadsreplace| fleadsstatus != 'leads_status_02'"
    },
    {
      //解锁退回按钮
      "id": "unlock_leadsreturn",
      "expression": "menu:$tbLeadsreturn| fleadsstatus == 'leads_status_02'"
    },
    {
      //禁用退回按钮
      "id": "slock_leadsreturn",
      "expression": "menu:tbLeadsreturn| fleadsstatus != 'leads_status_02'"
    },
    {
      //解锁关闭按钮
      "id": "unlock_leadsclose",
      "expression": "menu:$tbLeadsclose| fleadsstatus == 'leads_status_02'"
    },
    {
      //禁用关闭按钮
      "id": "lock_leadsclose",
      "expression": "menu:tbLeadsclose| fleadsstatus != 'leads_status_02'"
    },
    {
      //禁用转销售机会按钮
      "id": "lock_leadstochance",
      "expression": "menu:tbLeadstochance$|fleadsstatus == 'leads_status_01' or fleadsstatus == 'leads_status_03' or fleadsstatus == 'leads_status_04'"
    },
    {
      //解锁转销售机会按钮
      "id": "unlock_leadstochance",
      "expression": "menu:$tbLeadstochance|fleadsstatus == 'leads_status_02'"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [
    //关闭原因字段显示隐藏
    {
      "id": "show_closereason",
      "expression": "other:$.closereason| fleadsstatus == 'leads_status_04'"
    },
    {
      "id": "hide_closereason",
      "expression": "other:.closereason| fleadsstatus != 'leads_status_04'"
    },
    //如果没有源单隐藏选单按钮
    {
      "id": "hide_pull",
      "expression": "other:[menu=pull]|fsourcenumber=='' or fsourcenumber == ' '"
    }
  ],

  //定义表单计算规则
  "calcRules": [

  ]
}