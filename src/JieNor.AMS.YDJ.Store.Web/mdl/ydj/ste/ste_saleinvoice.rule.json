{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",

  //定义表单锁定规则
  "lockRules": [
    {
      "id": "lock_tbPull",
      "expression": "menu:tbPull$|fcustomerid=='' or fcustomerid==' '"
    },
    {
      "id": "unlock_tbPull",
      "expression": "menu:$tbPull|fcustomerid!='' or fcustomerid!=' '"
    }
  ],

  //定义表单可见性规则
  "visibleRules": [

  ],

  //定义表单计算规则
  "calcRules": [
    { "expression": "fsumtaxamount=sum(ftaxamount)" },
    { "expression": "famount=fsumtaxamount/(1+(ftaxrate*0.01))" },
    { "expression": "ftax=fsumtaxamount-famount" }
  ]
}