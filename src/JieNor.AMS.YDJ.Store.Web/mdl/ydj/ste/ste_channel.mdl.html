<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ste_channel" basemodel="bd_basetmpl" el="3" cn="合作渠道" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ste_channel" pn="fbillhead" cn="合作渠道">

        <!--基本信息-->
        <input group="基本信息" el="106" ek="fbillhead" id="fbizorgid" fn="fbizorgid" pn="fbizorgid" cn="合作渠道所属组织" refid="bas_organization" defVal="@currentOrgId"
               lock="-1" visible="-1" copy="0" lix="1" />
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lix="27"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="0" copy="0" lix="251" />
        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" width="130" visible="0" copy="0" lix="251" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="ftype" fn="ftype" pn="ftype" cn="类型" cg="渠道类型" refid="bd_enum" dfld="fenumitem" defval="'channel_type_01'" lix="5" width="70"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fcompany" fn="fcompany" pn="fcompany" cn="所属公司" lix="10" width="100" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fcontacts" fn="fcontacts" pn="fcontacts" cn="联系人" lix="15" must="1" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fphone" fn="fphone" pn="fphone" cn="联系电话" lix="20" width="100" must="1" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="faddress" fn="faddress" pn="faddress" cn="地址" lix="25" width="160" />
        <select group="基本信息" el="122" ek="fbillhead" visible="-1" id="fcooperation" fn="fcooperation" pn="fcooperation" cn="合作情况" cg="合作情况" refid="bd_enum" dfld="fenumitem" lix="30" width="80"></select>
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" id="fiscashagent" fn="fiscashagent" pn="fiscashagent" cn="代收货款" lix="35" width="85" />
 
        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="fchanneltype" fn="fchanneltype" pn="fchanneltype" cn="合作主体类型" vals="'0':'个人','1':'企业'" copy="0" defval="1" ></select>
        <input group="基本信息" el="100" ek="fbillhead" id="fdescription" cn="备注" len="500" lix="40" visible="-1" />

        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fbank" fn="fbank" pn="fbank" cn="开户行" lix="45" width="100" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="faccountname" fn="faccountname" pn="faccountname" cn="账户名称" lix="35" width="100" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fbanknumber" fn="fbanknumber" pn="fbanknumber" cn="银行卡号" lix="45" width="175" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fidcard" fn="fidcard" pn="fidcard" cn="身份证号" lix="45" width="175" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="fbankcode" fn="fbankcode" pn="fbankcode" cn="开户网点" refid="ydj_openingbank" lix="45" width="175" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fsuppliercode" fn="fsuppliercode" pn="fsuppliercode" cn="sap供应商编码" lix="45" width="175" lock="-1" copy="0"/>
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="合作门店" refid="ydj_dept" dfld="fname" reflvt="1" lix="70" defVal="@currentDeptId" must="1" />-->

        <input group="基本信息" el="135" ek="fbillhead" visible="1150" id="fmulimage" fn="fmulimage" pn="fmulimage" cn="证件" lix="86" />
        <input group="基本信息" el="105" ek="fbillhead" visible="1150" id="fsumbillamount" fn="fsumbillamount" pn="fsumbillamount" cn="累计带单金额" lock="-1" copy="0"/>

        <!-- 小程序需求：增加省市区、联系人、负责人 -->
        <select group="基本信息" el="122" ek="fbillhead" visible="32" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" refid="bd_enum" dfld="fenumitem" lix="10"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="32" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" refid="bd_enum" dfld="fenumitem" lix="11"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="32" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" refid="bd_enum" dfld="fenumitem" lix="12"></select>
        <select group="基本信息" el="100" ek="fbillhead" visible="0" id="fisapi" fn="fisapi" pn="fisapi" cn="是否接口上传" lix="12"></select>
        <select group="基本信息" el="100" ek="fbillhead" visible="0" id="fjson" fn="fjson" pn="fjson" cn="接口数据" len="1000" lix="12"></select>
        <!--<input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="负责人" refid="ydj_staff" defVal="@currentStaffId" apipn="saleMan" canchange="true" lix="30" must="1" />-->
    </div>

    <!--返佣比例-->
    <table id="fentry" el="52" pk="fentryid" tn="t_ste_channelentry" pn="fentry" cn="返佣比例" kfks="fratio" apipn="entry">
        <tr>
            <th lix="55" el="101" ek="fentry" id="flowerlimit" fn="flowerlimit" pn="flowerlimit" cn="金额下限" desc="最小值是0,不能设置无穷大值" width="90" visible="-1"></th>
            <th lix="60" el="102" ek="fentry" id="fratio" fn="fratio" pn="fratio" cn="提成比例%" width="90" visible="-1"></th>
            <th lix="65" el="101" ek="fentry" id="fupperlimit" fn="fupperlimit" pn="fupperlimit" cn="金额上限" width="90" visible="-1" desc="最小值是1,0表示无穷大值"></th>
        </tr>
    </table>
    <table id="fdutyentry" el="52" pk="fentryid" tn="t_ste_channeldutyentry" pn="fdutyentry" cn="负责人明细">
        <tr>
            <th el="106" ek="fdutyentry" id="fdutyid" fn="fdutyid" pn="fdutyid" visible="1150" cn="负责人" width="120"
                lock="-1" copy="0" lix="110" notrace="true" ts="" refid="ydj_staff" sformid="" filter="" reflvt="0" dfld="" />
            <th el="106" ek="fdutyentry" id="fdeptid_e" fn="fdeptid" pn="fdeptid" visible="1150" cn="部门" width="150"
                lock="-1" copy="0" lix="115" notrace="true" ts="" refid="ydj_dept" sformid="" filter="" reflvt="0" dfld="" />
            <th lix="116" el="113" ek="fdutyentry" id="fjoindate" fn="fjoindate" pn="fjoindate" cn="加入时间"
                lock="-1" copy="0" width="160" visible="1150"></th>
            <th el="100" ek="fdutyentry" id="fnote" fn="fnote" pn="fnote" visible="1150" cn="说明" width="300"
                lock="0" copy="0" lix="150" notrace="true" ts="" len="500" />
        </tr>
    </table>
    <div id="opList">
        <ul el="10" id="followerrecord" op="followerrecord" opn="跟进" data="" permid="followerrecord"></ul>
        <ul el="10" id="tbchangeduty" op="changeduty" opn="更换负责人" data="" permid="changeduty"></ul>
        <ul el="10" id="tbdeleteduty" op="deleteduty" opn="移除负责人" data="" permid="deleteduty"></ul>
        <ul el="10" id="tbaddduty" op="addduty" opn="新增负责人" data="" permid="addduty"></ul>
    </div>
    <div id="permList">
        <ul el="12" id="followerrecord" cn="跟进"></ul>
        <ul el="12" id="changeduty" cn="更换负责人"></ul>
        <ul el="12" id="deleteduty" cn="移除负责人"></ul>
        <ul el="12" id="addduty" cn="新增负责人"></ul>
    </div>
</body>
</html>