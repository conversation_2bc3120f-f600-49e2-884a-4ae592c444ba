{
  "base": "/mdl/bill.menu.json",
  "common": [
    {
      "id": "tbSaveSubmit",
      "caption": "保存并提交",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 1,
      "parent": "tbSave",
      "opcode": "savesubmit",
      "param": "autosubmit:true",
      "group": "standard"
    },
    {
      "id": "tbSaveAudit",
      "caption": "保存并审核",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 2,
      "parent": "tbSave",
      "opcode": "saveaudit",
      "param": "autoaudit:true",
      "group": "standard"
    },
    {
      "id": "tbSubmit",
      "caption": "提交",
      "visible": "false",
      "disabled": "false",
      "style": "menugroup",
      "order": 20,
      "parent": "",
      "opcode": "submitflow",
      "param": "'dataChangeWarn':true",
      "group": "standard"
    },
    {
      "id": "tbUnsubmit",
      "caption": "撤销",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 5,
      "parent": "tbSubmit",
      "opcode": "terminateflow",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbAudit",
      "caption": "审核",
      "visible": "false",
      "disabled": "false",
      "style": "menugroup",
      "order": 30,
      "parent": "",
      "opcode": "auditflow",
      "param": "'dataChangeWarn':true",
      "group": "standard"
    },
    {
      "id": "tbUnaudit",
      "caption": "反审核",
      "visible": "false",
      "disabled": "false",
      "style": "menu",
      "order": 5,
      "parent": "tbAudit",
      "opcode": "rejectflow",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbPrint",
      "caption": "打印",
      "visible": "true",
      "disabled": "false",
      "style": "menugroup",
      "order": 76,
      "parent": "",
      "opcode": "print",
      "param": "'dataChangeWarn':true",
      "group": "standard"
    }
  ],
  "listmenu": [],
  "billmenu": [
    {
      "id": "tbPull",
      "param": "enablePushAllEntity:true"
    },
    {
      "id": "tbSave",
      "caption": "保存",
      "visible": "true",
      "disabled": "false",
      "style": "button",
      "order": 0,
      "parent": "",
      "opcode": "save",
      "param": "",
      "group": "standard"
    },
    {
      "id": "tbSubmitHQ",
      "caption": "提交总部",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 30,
      "parent": "",
      "opcode": "submithq",
      "group": "standard",
      "param": "'dataChangeWarn':true"
    },
    {
      "id": "tbReturnSend",
      "caption": "返厂发货",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 31,
      "parent": "",
      "opcode": "returnsend",
      "group": "standard",
      "param": "'dataChangeWarn':true"
    },
    {
      "id": "tbPull",
      "caption": "选单",
      "visible": "true",
      "disabled": "false",
      "style": "button",
      "order": 10,
      "parent": "",
      "opcode": "pull",
      "param": "",
      "group": "standard"
    },
    //{
    //  "id": "tbPurReturn",
    //  "caption": "采购退货",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 35,
    //  "parent": "",
    //  "opcode": "purreturn",
    //  "group": "standard",
    //  "param": "'dataChangeWarn':true"
    //},
    {
      "id": "tbPurReturn",
      "caption": "采购退货",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 35,
      "opcode": "push2poreturn",
      "group": "standard",
      "param": "'dialog':{'formId':'pur_poreturndialog'}"
    },
    {
      "id": "tbReturnDepto",
      "caption": "返厂维修",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 38,
      "parent": "",
      "opcode": "returndepto",
      "group": "standard",
      "param": "'dataChangeWarn':true"
    },
    //{
    //  "id": "tbReturnGood",
    //  "caption": "退货",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 10,
    //  "parent": "tbStockOut",
    //  "opcode": "push2returngood",
    //  "param": "'dialog':{'formId':'sal_soreturndialog'}"
    //},
    {
      "id": "tbSalReturn",
      "caption": "销售退货",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 40,
      "parent": "",
      "opcode": "push2returngood",
      "group": "standard",
      "param": "'dialog':{'formId':'sal_soreturndialog'}"
    },
    {
      "id": "tbReceipt",
      "caption": "收款",
      "visible": "true",
      "disabled": "false",
      "style": "menu",
      "order": 42,
      "parent": "",
      "opcode": "receipt",
      "group": "standard",
      "param": "'dataChangeWarn':true"
    }
    //{
    //  "id": "tbAccept",
    //  "caption": "受理",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 33,
    //  "parent": "",
    //  "opcode": "accept",
    //  "group": "standard",
    //  "param": "'dataChangeWarn':true"
    //},
    //{
    //  "id": "tbTransfer",
    //  "caption": "转总部",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 34,
    //  "parent": "",
    //  "opcode": "transfer",
    //  "group": "standard",
    //  "param": "'dataChangeWarn':true"
    //},
    //{
    //  "id": "tbFinish",
    //  "caption": "完成",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 35,
    //  "parent": "",
    //  "opcode": "finish",
    //  "group": "standard",
    //  "param": "'dataChangeWarn':true"
    //},
    //{
    //  "id": "tbFclose",
    //  "caption": "关闭",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 36,
    //  "parent": "",
    //  "opcode": "fclose",
    //  "group": "standard",
    //  "param": "'dataChangeWarn':true"
    //},
    //{
    //  "id": "tbFeedBackVist",
    //  "caption": "回访",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 77,
    //  "parent": "",
    //  "opcode": "feedbackvist",
    //  "group": "standard",
    //  "param": "'dataChangeWarn':true"
    //},
    //{
    //  "id": "tbAddService",
    //  "caption": "新增服务",
    //  "visible": "true",
    //  "disabled": "false",
    //  "style": "menu",
    //  "order": 76,
    //  "parent": "droplist_more",
    //  "opcode": "addservice",
    //  "group": "standard",
    //  "param": "ruleId:'ste_afterfeedback2ydj_service'"
    //}
  ]
}
