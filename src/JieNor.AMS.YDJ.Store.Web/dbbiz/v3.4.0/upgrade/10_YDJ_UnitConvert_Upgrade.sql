--本文件存放多计量单位改造后，历史数据的升级脚本  by linus.

--商品的计量单位升级：库存单位，销售单位，采购单位
update t_bd_material set fstockunitid=funitid where fstockunitid='' or fstockunitid is null;
update t_bd_material set fsalunitid=funitid where fsalunitid='' or fsalunitid is null;
update t_bd_material set fpurunitid=funitid where fpurunitid='' or fpurunitid is null;

--添加商品明细的基本单位换算率
insert into t_bd_materialunit(fentryid,fid,funitid,fcvttype,fcvtrate)
select newid() fentryid,m1.fid,m1.funitid,m1.fcvttype,m1.fcvtrate
from (
select distinct t0.fid,t0.funitid,1 fcvttype,1 fcvtrate
from t_bd_material t0
left join t_bd_materialunit t1 on t0.fid=t1.fid and t0.funitid=t1.funitid
where t1.fentryid is null
) m1;

--单位
update t_ydj_unit set fisbaseunit='1',fcvtrate=1,fprecision=0 where fisbaseunit='' or fisbaseunit is null;

--销售意向
update t_ydj_saleentry set fbizunitid=funitid,fbizqty=fqty where fbizunitid='' or fbizunitid is null;
update t_ydj_saledetail set fbizunitid=funitid,fbizqty=fqty where fbizunitid='' or fbizunitid is null;

--销售合同
update t_ydj_orderentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizdeliveryqty=fdeliveryqty
							,fbizoutqty=foutqty
							,fbizreturnnoticeqty=freturnnoticeqty
							,fbizreturnqty=freturnqty
							,fbizrefundqty=frefundqty
where fbizunitid='' or fbizunitid is null;
update t_ydj_orderdetail set fbizunitid=funitid,fbizqty=fqty where fbizunitid='' or fbizunitid is null;

--采购申请
update t_pur_reqorderentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizorderqty=forderqty
where fbizunitid='' or fbizunitid is null;

--采购订单
update t_ydj_poorderentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizreceiptqty=freceiptqty
							,fbizinstockqty=finstockqty
							,fbizreturnnoticeqty=freturnnoticeqty
							,fbizreturnqty=freturnqty
							,fbizrefundqty=frefundqty
where fbizunitid='' or fbizunitid is null;

--采购收货通知单
update t_pur_receiptnoticeentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizinstockqty=finstockqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--采购入库单
update t_stk_postockinentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizreturnnoticeqty=freturnnoticeqty
							,fbizreturnqty=freturnqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--采购退货通知单
update t_pur_returnnoticeentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizreturnqty=freturnqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--采购退货单
update t_stk_postockreturnentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--销售发货通知单
update t_sal_deliverynoticeentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizoutstockqty=foutstockqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--销售出库单
update t_stk_sostockoutentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizreturnnoticeqty=freturnnoticeqty
							,fbizreturnqty=freturnqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--销售退货通知单
update t_sal_returnnoticeentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizreturnqty=freturnqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--销售退货单
update t_stk_sostockreturnentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbizorderqty=forderqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--其它入库通知单
update t_stk_otherstockinreqentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizinstockqty=finstockqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--其它入库单
update t_stk_otherstockinentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--其它出库通知单
update t_stk_otherstockoutreqentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizoutstockqty=foutstockqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--其它出库单
update t_stk_otherstockoutentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--库存调拨通知单
update t_stk_invtransferreqentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbiztransferqty=ftransferqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--库存调拨单
update t_stk_invtransferentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizplanqty=fplanqty
							,fbiztransferbackqty=ftransferbackqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--盘点单
update t_stk_invverifyentry set fbizunitid=funitid
							,fbizqty=fqty
							,fbizpdqty=fpdqty
							,fbizpyqty=fpyqty
							,fbizpkqty=fpkqty
							,fstockunitid=funitid
							,fstockqty=fqty
where fbizunitid='' or fbizunitid is null;

--即时库存
update t_stk_inventorylist set fstockunitid=funitid,fstockqty=fqty where fstockunitid='' or fstockunitid is null;
