
--创建商品标签视图 
--说明：不同处二维码【fqrcode】生成规则 【商品编码+辅助属性ID】

--创建商品标签基础资料视图 


if exists (select 1 from sysobjects where name = 'v_ydj_productbarcode' and xtype = 'v')
drop view v_ydj_productbarcode
go
create view v_ydj_productbarcode
as
SELECT t.* 
,(case when t.fbillformid='ydj_product' and c.fimage<>'' then isnull(c.fimage,'') when t.fbillformid='ydj_product' and t.fattrinfo='' then m.fimage else '' end) as fimage
,(case when t.fbillformid='ydj_product' and c.fimage<>'' then isnull(c.fimage_txt,'') when t.fbillformid='ydj_product' and t.fattrinfo='' then m.fimage_txt else '' end) as fimage_txt
FROM(
SELECT e.[fentryid] as [fid]
      ,'ydj_productbarcode' as [FFormId]
	  ,'ydj_product' as [fbillformid]
	  ,'' as [fsuitid]
      ,p.[fnumber]
      ,p.[fname]
      ,p.[fdescription]
      ,p.[fcreatorid]
      ,p.[fcreatedate]
      ,p.[fmodifierid]
      ,p.[fmodifydate]
      ,p.[fispreset]
      ,'E' as [fstatus]
      ,p.[fapproveid]
      ,p.[fapprovedate]
      ,p.[fforbidstatus]
      ,p.[fforbidid]
      ,p.[fforbiddate]
      ,m.[fmainorgid]
      ,p.[fmainorgid_txt]
      ,p.[fmainorgid_pid]
      ,p.[fbizruleid]
      ,p.[fflowinstanceid]
      ,e.[ftranid]
      ,p.[fsendstatus]
      ,p.[fdataorigin]
      ,p.[fsenddate]
      ,p.[fdownloaddate]
      ,p.[fchaindataid]
      ,p.[fname_py]
      ,p.[fname_py2]
      ,m.[fid] as [fproductid]
      ,e.[fattrinfo]
      ,e.[fentryid] as [fbarcode]
      ,p.[fpublishcid]
      ,p.[fpublishcid_txt]
      ,p.[fpublishcid_pid]
      ,p.[ffromchaindataid]
      ,p.[fchangestatus]
      ,m.[foriginplace]
      ,e.[funitid]
      ,(case when  isnull(v.[fname],'') = '' then m.[fnumber]  else  m.[fnumber] + ',' + v.[fname] end) as [fqrcode]
      ,p.[fprintcount]
      ,p.[fprintid]
      ,p.[fprintdate]
      ,p.[ffromtranid]
      ,p.[froottranid]
	  ,e.[fstartdate]
	  ,e.[fexpiredate]
	  ,e.[fsalprice]
	  ,p.[fnextprocnode]
	  ,'' as [fprimitiveid]
  FROM  [dbo].[T_BD_MATERIAL] m
  left JOIN [dbo].[T_YDJ_PRICEENTRY] e on  m.fid = e.fproductid and e.fconfirmstatus='2'
  left join [dbo].[T_YDJ_PRICE] p ON e.fid=p.fid and m.fmainorgid = p.fmainorgid and  p.flimit='' and p.fforbidstatus='0'
  left join [dbo].[T_BD_AUXPROPVALUE] v on v.fid =e.fattrinfo 
  union
  SELECT s.[fid] as [fid]
      ,'ydj_productbarcode' as [FFormId]
	  ,'ydj_suit' as [fbillformid]
	  ,s.[fid] as [fsuitid]
      ,s.[fnumber]
      ,s.[fname]
      ,s.[fdescription]
      ,s.[fcreatorid]
      ,s.[fcreatedate]
      ,s.[fmodifierid]
      ,s.[fmodifydate]
      ,s.[fispreset]
      ,'E' as [fstatus]
      ,s.[fapproveid]
      ,s.[fapprovedate]
      ,s.[fforbidstatus]
      ,s.[fforbidid]
      ,s.[fforbiddate]
      ,s.[fmainorgid]
      ,s.[fmainorgid_txt]
      ,s.[fmainorgid_pid]
      ,s.[fbizruleid]
      ,s.[fflowinstanceid]
      ,s.[ftranid]
      ,s.[fsendstatus]
      ,s.[fdataorigin]
      ,s.[fsenddate]
      ,s.[fdownloaddate]
      ,s.[fchaindataid]
      ,s.[fname_py]
      ,s.[fname_py2]
      ,'' as [fproductid]
      ,'' as [fattrinfo]
      ,s.[ftranid] as [fbarcode]
      ,s.[fpublishcid]
      ,s.[fpublishcid_txt]
      ,s.[fpublishcid_pid]
      ,s.[ffromchaindataid]
      ,s.[fchangestatus]
      ,'' as [foriginplace]
      ,s.[funitid]
      ,s.[fqrcode]
      ,s.[fprintcount]
      ,s.[fprintid]
      ,s.[fprintdate]
      ,s.[ffromtranid]
      ,s.[froottranid]
	  ,null as [fstartdate]
	  ,null as [fexpiredate]
	  ,s.[fsumamount] as [fsalprice]
	  ,s.[fnextprocnode]
	  ,'' as [fprimitiveid]
  FROM [dbo].[t_ydj_suit] s) t
  left join [T_BD_MATERIAL] m on t.fproductid=m.fid and t.fbillformid='ydj_product' 
  left join [T_Ydj_Commoditygallery] c on c.fproductid=t.fproductid and t.fbillformid='ydj_product' and t.fattrinfo=c.fattrinfo 
go






