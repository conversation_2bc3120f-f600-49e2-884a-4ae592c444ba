--采购通知单的升级脚本
update t_pur_receiptnoticeentry set flinkmobile=p.flinkmobile,
									flinkstaffid=p.flinkstaffid,
									flinkaddress=p.flinkaddress,
									fdate=p.fdate
from t_pur_receiptnotice p
where p.fid=t_pur_receiptnoticeentry.fid and (t_pur_receiptnoticeentry.flinkstaffid='' or t_pur_receiptnoticeentry.flinkstaffid=' ' or t_pur_receiptnoticeentry.flinkstaffid is null)
go

--费用登记表的升级脚本
update t_ste_registfee set fsumamount=fmoney
where fmoney>0 and (fsumamount=0 or fsumamount is null)
go

--承运公司的升级脚本
insert into t_ydj_supplier (fname,fmainorgid,fnumber,fid,FFormId,fispreset,fstatus,ftype)
select fcarrierid as fname,fmainorgid, 'CYGS'+right('1000'+row_number()over(partition by fmainorgid order by fcarrierid),3) as fnumber,lower(replace(newid(),'-','')) as fid,'ydj_supplier','0','B','suppliertype_03' from (
select fcarrierid,fmainorgid from t_stk_otherstockin where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_otherstockout where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_postockin where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_sostockout where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_sostockreturn where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_pur_receiptnotice where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_pur_returnnotice where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_sal_deliverynotice where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_sal_returnnotice where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_invtransferreq where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_otherstockinreq where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_otherstockout where len(fcarrierid)>0
union
select fcarrierid,fmainorgid from t_stk_otherstockoutreq where len(fcarrierid)>0
) t
go

if exists(select * from sys.objects where name='Get_StrArrayLength')
drop function Get_StrArrayLength;
go

if exists(select * from sys.objects where name='Get_StrArrayStrOfIndex')
drop function Get_StrArrayStrOfIndex;
go

create function Get_StrArrayLength
(
  @str varchar(5000),  --要分割的字符串
  @split varchar(10)  --分隔符号
)
returns int
as
begin
  declare @location int
  declare @start int
  declare @length int

  set @str=ltrim(rtrim(@str))
  set @location=charindex(@split,@str)
  set @length=1
  while @location<>0
  begin
    set @start=@location+1
    set @location=charindex(@split,@str,@start)
    set @length=@length+1
  end
  return @length
end
go

create function Get_StrArrayStrOfIndex
(
  @str varchar(5000),  --要分割的字符串
  @split varchar(10),  --分隔符号
  @index int --取第几个元素
)
returns varchar(5000)
as
begin
  declare @location int
  declare @start int
  declare @next int
  declare @seed int

  set @str=ltrim(rtrim(@str))
  set @start=1
  set @next=1
  set @seed=len(@split)
  
  set @location=charindex(@split,@str)
  while @location<>0 and @index>@next
  begin
    set @start=@location+@seed
    set @location=charindex(@split,@str,@start)
    set @next=@next+1
  end
  if @location =0 select @location =len(@str)+1 
  return substring(@str,@start,@location-@start)
end
go


declare @str varchar(5000)
set @str='t_stk_otherstockin,t_stk_otherstockout,t_stk_postockin,t_stk_sostockout,t_stk_sostockreturn,t_pur_receiptnotice,t_pur_returnnotice,t_sal_deliverynotice,t_sal_returnnotice,t_stk_invtransferreq,t_stk_otherstockinreq,t_stk_otherstockout,t_stk_otherstockoutreq'
print dbo.Get_StrArrayLength(@str,',')
declare @next int 
declare @s varchar(1000) 
set @next=1
while @next<=dbo.Get_StrArrayLength(@str,',')
begin
  set @s=dbo.Get_StrArrayStrOfIndex(@str,',',@next)
  set @s=N' update '+@s+N' set fcancelid=c.fid from t_ydj_supplier c where c.ftype=''suppliertype_03'' and Convert(nvarchar(500),ltrim(rtrim(c.fname)))=Convert(nvarchar(500),ltrim(rtrim('+@s+N'.fcarrierid))) and c.fmainorgid='+@s+N'.fmainorgid'
  print @s;
  exec(@s);
  set @next=@next+1
end
go

if exists(select * from sys.objects where name='Get_StrArrayLength')
drop function Get_StrArrayLength;
go

if exists(select * from sys.objects where name='Get_StrArrayStrOfIndex')
drop function Get_StrArrayStrOfIndex;
go
