--合同条款升级脚本 杨茂衡 2019-08-01


declare @constraintName varchar(200)
if not exists(select * from syscolumns where id=object_id('t_ydj_orderclause') and name='ffirstterm')
begin
alter table t_ydj_orderclause add [ffirstterm] [nvarchar](100) NOT NULL default(' ')
end
else
begin
select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_orderclause') and b.id=a.cdefault and a.name='ffirstterm' and b.name like 'DF%'
exec('alter table t_ydj_orderclause drop constraint '+@constraintName)
alter table t_ydj_orderclause alter column [ffirstterm] [nvarchar](100) NOT NULL
alter table t_ydj_orderclause add default (' ') for ffirstterm with values
end

if not exists(select * from syscolumns where id=object_id('t_ydj_orderclause') and name='fschemefee')
begin
alter table t_ydj_orderclause add [fschemefee] [nvarchar](100) NOT NULL default(' ')
end
else
begin
select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_orderclause') and b.id=a.cdefault and a.name='fschemefee' and b.name like 'DF%'
exec('alter table t_ydj_orderclause drop constraint '+@constraintName)
alter table t_ydj_orderclause alter column [fschemefee] [nvarchar](100) NOT NULL
alter table t_ydj_orderclause add default (' ') for fschemefee with values
end

if not exists(select * from syscolumns where id=object_id('t_ydj_orderclause') and name='funderupstairfee')
begin
alter table t_ydj_orderclause add [funderupstairfee] [nvarchar](100) NOT NULL default(' ')
end
else
begin
select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_orderclause') and b.id=a.cdefault and a.name='funderupstairfee' and b.name like 'DF%'
exec('alter table t_ydj_orderclause drop constraint '+@constraintName)
alter table t_ydj_orderclause alter column [funderupstairfee] [nvarchar](100) NOT NULL
alter table t_ydj_orderclause add default (' ') for funderupstairfee with values
end

if not exists(select * from syscolumns where id=object_id('t_ydj_orderclause') and name='faboveupstairfee')
begin
alter table t_ydj_orderclause add [faboveupstairfee] [nvarchar](100) NOT NULL default(' ')
end
else
begin
select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_orderclause') and b.id=a.cdefault and a.name='faboveupstairfee' and b.name like 'DF%'
exec('alter table t_ydj_orderclause drop constraint '+@constraintName)
alter table t_ydj_orderclause alter column [faboveupstairfee] [nvarchar](100) NOT NULL
alter table t_ydj_orderclause add default (' ') for faboveupstairfee with values
end

if not exists(select * from syscolumns where id=object_id('t_ydj_orderclause') and name='fnoticedays')
begin
alter table t_ydj_orderclause add [fnoticedays] [nvarchar](100) NOT NULL default(' ')
end
else
begin
select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_orderclause') and b.id=a.cdefault and a.name='fnoticedays' and b.name like 'DF%'
exec('alter table t_ydj_orderclause drop constraint '+@constraintName)
alter table t_ydj_orderclause alter column [fnoticedays] [nvarchar](100) NOT NULL
alter table t_ydj_orderclause add default (' ') for fnoticedays with values
end

if not exists(select * from syscolumns where id=object_id('t_ydj_orderclause') and name='finstallnoticedays')
begin
alter table t_ydj_orderclause add [finstallnoticedays] [nvarchar](100) NOT NULL default(' ')
end
else
begin
select @constraintName = b.name from syscolumns a,sysobjects b where a.id=object_id('t_ydj_orderclause') and b.id=a.cdefault and a.name='finstallnoticedays' and b.name like 'DF%'
exec('alter table t_ydj_orderclause drop constraint '+@constraintName)
alter table t_ydj_orderclause alter column [finstallnoticedays] [nvarchar](100) NOT NULL
alter table t_ydj_orderclause add default (' ') for finstallnoticedays with values
end
