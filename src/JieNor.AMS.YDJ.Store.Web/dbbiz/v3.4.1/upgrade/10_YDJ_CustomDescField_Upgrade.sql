/*字段的升级脚本，该脚本由 宋纪强 在 2019-03-19 20:45:09 通过工具生成*/
alter table t_pur_reqorderentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_sostockoutentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_apply_product alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_inventorybalance alter column fcustomdesc nvarchar(2000) not null;
alter table t_sal_deliverynoticeentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_ydj_saleentry alter column fcustomdes_e nvarchar(2000) not null;
alter table t_bcm_countscantaskentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_pur_receiptnoticeentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_bcm_transferscantaskentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_postockreturnentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_sostockreturnentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_reservebillentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_invverifyentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_invtransferreqentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_postockinentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_scheduleapplyentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_initstockbill alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_otherstockoutreqentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_bcm_mastermtrlentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_inventorylist alter column fcustomdesc nvarchar(2000) not null;
alter table t_ydj_poorderentry alter column fcustomdes_e nvarchar(2000) not null;
alter table t_ydj_orderentry alter column fcustomdes_e nvarchar(2000) not null;
alter table t_stk_otherstockoutentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_pur_returnnoticeentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_invcomputelog alter column fcustomdesc nvarchar(2000) not null;
alter table t_sal_returnnoticeentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_invtransferentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_otherstockinreqentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_ydj_poorderentry_chg alter column fcustomdes_e_chg nvarchar(2000) not null;
alter table t_ydj_poorderentry_chg alter column fcustomdes_e nvarchar(2000) not null;
alter table t_ydj_orderentry_chg alter column fcustomdes_e nvarchar(2000) not null;
alter table t_ydj_orderentry_chg alter column fcustomdes_e_chg nvarchar(2000) not null;
alter table t_bcm_packsourceentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_bcm_packorderentry alter column fcustomdesc nvarchar(2000) not null;
alter table t_stk_otherstockinentry alter column fcustomdesc nvarchar(2000) not null;



/*字段的升级脚本，该脚本由 宋纪强 在 2019-05-28 16:21:38 通过工具生成*/
update t_pur_reqorderentry set fmulfile=' ' where fmulfile is null;
update t_pur_reqorderentry set fmulfile_txt=' ' where fmulfile_txt is null;
update t_pur_reqorderentry set fmtrlimage=' ' where fmtrlimage is null;
update t_pur_reqorderentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_sostockoutentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_sostockoutentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ydj_commoditygallery set fimage=' ' where fimage is null;
update t_ydj_commoditygallery set fimage_txt=' ' where fimage_txt is null;
update t_ydj_service set fproimage=' ' where fproimage is null;
update t_ydj_service set fproimage_txt=' ' where fproimage_txt is null;
update t_ydj_service set fdoneimage=' ' where fdoneimage is null;
update t_ydj_service set fdoneimage_txt=' ' where fdoneimage_txt is null;
update t_ydj_service set ftectonicimage=' ' where ftectonicimage is null;
update t_ydj_service set ftectonicimage_txt=' ' where ftectonicimage_txt is null;
update t_ydj_service set fgroupimage=' ' where fgroupimage is null;
update t_ydj_service set fgroupimage_txt=' ' where fgroupimage_txt is null;
update t_ydj_scalerecord set fimage=' ' where fimage is null;
update t_ydj_scalerecord set fimage_txt=' ' where fimage_txt is null;
update t_stk_inventorybalance set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_inventorybalance set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_sal_deliverynoticeentry set fmtrlimage=' ' where fmtrlimage is null;
update t_sal_deliverynoticeentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ydj_saleentry set fmtrlimage=' ' where fmtrlimage is null;
update t_ydj_saleentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ydj_saleintention set fquotelist=' ' where fquotelist is null;
update t_ydj_saleintention set fquotelist_txt=' ' where fquotelist_txt is null;
update t_bcm_countscantaskentry set fmtrlimage=' ' where fmtrlimage is null;
update t_bcm_countscantaskentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_pur_receiptnoticeentry set fmtrlimage=' ' where fmtrlimage is null;
update t_pur_receiptnoticeentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_bcm_transferscantaskentry set fmtrlimage=' ' where fmtrlimage is null;
update t_bcm_transferscantaskentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_postockreturnentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_postockreturnentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_sostockreturnentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_sostockreturnentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_reservebillentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_reservebillentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_invverifyentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_invverifyentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ste_saleinvoice set fimage=' ' where fimage is null;
update t_ste_saleinvoice set fimage_txt=' ' where fimage_txt is null;
update t_ste_afterfeedback set fquestionimage=' ' where fquestionimage is null;
update t_ste_afterfeedback set fquestionimage_txt=' ' where fquestionimage_txt is null;
update t_stk_invtransferreqentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_invtransferreqentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_postockinentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_postockinentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_pay_settleorder set fremitimage=' ' where fremitimage is null;
update t_pay_settleorder set fremitimage_txt=' ' where fremitimage_txt is null;
update t_stk_scheduleapplyentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_scheduleapplyentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_initstockbill set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_initstockbill set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_otherstockoutreqentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_otherstockoutreqentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_coo_incomedisburse set fimage=' ' where fimage is null;
update t_coo_incomedisburse set fimage_txt=' ' where fimage_txt is null;
update t_ser_servicefeed set fprograph=' ' where fprograph is null;
update t_ser_servicefeed set fprograph_txt=' ' where fprograph_txt is null;
update t_stk_inventorylist set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_inventorylist set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ydj_poorderentry set fmtrlimage=' ' where fmtrlimage is null;
update t_ydj_poorderentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ydj_poorderentry set fmulfile=' ' where fmulfile is null;
update t_ydj_poorderentry set fmulfile_txt=' ' where fmulfile_txt is null;
update t_coo_inpourmoney set fimage=' ' where fimage is null;
update t_coo_inpourmoney set fimage_txt=' ' where fimage_txt is null;
update t_ydj_orderentry set fmulfile=' ' where fmulfile is null;
update t_ydj_orderentry set fmulfile_txt=' ' where fmulfile_txt is null;
update t_ydj_orderentry set fmtrlimage=' ' where fmtrlimage is null;
update t_ydj_orderentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_aft_manage set fquestionimage=' ' where fquestionimage is null;
update t_aft_manage set fquestionimage_txt=' ' where fquestionimage_txt is null;
update t_stk_otherstockoutentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_otherstockoutentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_pur_returnnoticeentry set fmtrlimage=' ' where fmtrlimage is null;
update t_pur_returnnoticeentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_invcomputelog set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_invcomputelog set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update ser_ydj_merchantorder set fimage=' ' where fimage is null;
update ser_ydj_merchantorder set fimage_txt=' ' where fimage_txt is null;
update t_ste_registfee set fevidence=' ' where fevidence is null;
update t_ste_registfee set fevidence_txt=' ' where fevidence_txt is null;
update t_sal_returnnoticeentry set fmtrlimage=' ' where fmtrlimage is null;
update t_sal_returnnoticeentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_coo_chargemoney set fimage=' ' where fimage is null;
update t_coo_chargemoney set fimage_txt=' ' where fimage_txt is null;
update t_stk_invtransferentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_invtransferentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_otherstockinreqentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_otherstockinreqentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ydj_poorderentry_chg set fmtrlimage=' ' where fmtrlimage is null;
update t_ydj_poorderentry_chg set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_ydj_poorderentry_chg set fmulfile=' ' where fmulfile is null;
update t_ydj_poorderentry_chg set fmulfile_txt=' ' where fmulfile_txt is null;
update t_ydj_orderentry_chg set fmulfile=' ' where fmulfile is null;
update t_ydj_orderentry_chg set fmulfile_txt=' ' where fmulfile_txt is null;
update t_ydj_orderentry_chg set fmtrlimage=' ' where fmtrlimage is null;
update t_ydj_orderentry_chg set fmtrlimage_txt=' ' where fmtrlimage_txt is null;
update t_stk_otherstockinentry set fmtrlimage=' ' where fmtrlimage is null;
update t_stk_otherstockinentry set fmtrlimage_txt=' ' where fmtrlimage_txt is null;


/*字段的升级脚本，该脚本由 宋纪强 在 2019-05-28 16:11:12 通过工具生成*/
--该脚本执行可能会出错，因为字段存在默认值约束，可以先批量删除字段默认值约束，再执行以下脚本，执行完以下脚本后，再批量创建字段默认值约束
--可以通过执行 10_YDJ_FieldDefConstraint_Upgrade.sql 生成 dropdefsql 脚本 和 adddefsql 脚本：
--1.先执行 dropdefsql 脚本。
--2.再执行以下脚本。
--3.再执行 adddefsql 脚本。
alter table t_pur_reqorderentry alter column fmulfile nvarchar(max) not null;
alter table t_pur_reqorderentry alter column fmulfile_txt nvarchar(max) not null;
alter table t_pur_reqorderentry alter column fmtrlimage nvarchar(max) not null;
alter table t_pur_reqorderentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_sostockoutentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_sostockoutentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ydj_commoditygallery alter column fimage nvarchar(max) not null;
alter table t_ydj_commoditygallery alter column fimage_txt nvarchar(max) not null;
alter table t_ydj_service alter column fproimage nvarchar(max) not null;
alter table t_ydj_service alter column fproimage_txt nvarchar(max) not null;
alter table t_ydj_service alter column fdoneimage nvarchar(max) not null;
alter table t_ydj_service alter column fdoneimage_txt nvarchar(max) not null;
alter table t_ydj_service alter column ftectonicimage nvarchar(max) not null;
alter table t_ydj_service alter column ftectonicimage_txt nvarchar(max) not null;
alter table t_ydj_service alter column fgroupimage nvarchar(max) not null;
alter table t_ydj_service alter column fgroupimage_txt nvarchar(max) not null;
alter table t_ydj_scalerecord alter column fimage nvarchar(max) not null;
alter table t_ydj_scalerecord alter column fimage_txt nvarchar(max) not null;
alter table t_stk_inventorybalance alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_inventorybalance alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_sal_deliverynoticeentry alter column fmtrlimage nvarchar(max) not null;
alter table t_sal_deliverynoticeentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ydj_saleentry alter column fmtrlimage nvarchar(max) not null;
alter table t_ydj_saleentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ydj_saleintention alter column fquotelist nvarchar(max) not null;
alter table t_ydj_saleintention alter column fquotelist_txt nvarchar(max) not null;
alter table t_bcm_countscantaskentry alter column fmtrlimage nvarchar(max) not null;
alter table t_bcm_countscantaskentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_pur_receiptnoticeentry alter column fmtrlimage nvarchar(max) not null;
alter table t_pur_receiptnoticeentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_bcm_transferscantaskentry alter column fmtrlimage nvarchar(max) not null;
alter table t_bcm_transferscantaskentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_postockreturnentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_postockreturnentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_sostockreturnentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_sostockreturnentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_reservebillentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_reservebillentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_invverifyentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_invverifyentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ste_saleinvoice alter column fimage nvarchar(max) not null;
alter table t_ste_saleinvoice alter column fimage_txt nvarchar(max) not null;
alter table t_ste_afterfeedback alter column fquestionimage nvarchar(max) not null;
alter table t_ste_afterfeedback alter column fquestionimage_txt nvarchar(max) not null;
alter table t_stk_invtransferreqentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_invtransferreqentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_postockinentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_postockinentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_pay_settleorder alter column fremitimage nvarchar(max) not null;
alter table t_pay_settleorder alter column fremitimage_txt nvarchar(max) not null;
alter table t_stk_scheduleapplyentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_scheduleapplyentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_initstockbill alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_initstockbill alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_otherstockoutreqentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_otherstockoutreqentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_coo_incomedisburse alter column fimage nvarchar(max) not null;
alter table t_coo_incomedisburse alter column fimage_txt nvarchar(max) not null;
alter table t_ser_servicefeed alter column fprograph nvarchar(max) not null;
alter table t_ser_servicefeed alter column fprograph_txt nvarchar(max) not null;
alter table t_stk_inventorylist alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_inventorylist alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ydj_poorderentry alter column fmtrlimage nvarchar(max) not null;
alter table t_ydj_poorderentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ydj_poorderentry alter column fmulfile nvarchar(max) not null;
alter table t_ydj_poorderentry alter column fmulfile_txt nvarchar(max) not null;
alter table t_coo_inpourmoney alter column fimage nvarchar(max) not null;
alter table t_coo_inpourmoney alter column fimage_txt nvarchar(max) not null;
alter table t_ydj_orderentry alter column fmulfile nvarchar(max) not null;
alter table t_ydj_orderentry alter column fmulfile_txt nvarchar(max) not null;
alter table t_ydj_orderentry alter column fmtrlimage nvarchar(max) not null;
alter table t_ydj_orderentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_aft_manage alter column fquestionimage nvarchar(max) not null;
alter table t_aft_manage alter column fquestionimage_txt nvarchar(max) not null;
alter table t_stk_otherstockoutentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_otherstockoutentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_pur_returnnoticeentry alter column fmtrlimage nvarchar(max) not null;
alter table t_pur_returnnoticeentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_invcomputelog alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_invcomputelog alter column fmtrlimage_txt nvarchar(max) not null;
alter table ser_ydj_merchantorder alter column fimage nvarchar(max) not null;
alter table ser_ydj_merchantorder alter column fimage_txt nvarchar(max) not null;
alter table t_ste_registfee alter column fevidence nvarchar(max) not null;
alter table t_ste_registfee alter column fevidence_txt nvarchar(max) not null;
alter table t_sal_returnnoticeentry alter column fmtrlimage nvarchar(max) not null;
alter table t_sal_returnnoticeentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_coo_chargemoney alter column fimage nvarchar(max) not null;
alter table t_coo_chargemoney alter column fimage_txt nvarchar(max) not null;
alter table t_stk_invtransferentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_invtransferentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_otherstockinreqentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_otherstockinreqentry alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ydj_poorderentry_chg alter column fmtrlimage nvarchar(max) not null;
alter table t_ydj_poorderentry_chg alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_ydj_poorderentry_chg alter column fmulfile nvarchar(max) not null;
alter table t_ydj_poorderentry_chg alter column fmulfile_txt nvarchar(max) not null;
alter table t_ydj_orderentry_chg alter column fmulfile nvarchar(max) not null;
alter table t_ydj_orderentry_chg alter column fmulfile_txt nvarchar(max) not null;
alter table t_ydj_orderentry_chg alter column fmtrlimage nvarchar(max) not null;
alter table t_ydj_orderentry_chg alter column fmtrlimage_txt nvarchar(max) not null;
alter table t_stk_otherstockinentry alter column fmtrlimage nvarchar(max) not null;
alter table t_stk_otherstockinentry alter column fmtrlimage_txt nvarchar(max) not null;



