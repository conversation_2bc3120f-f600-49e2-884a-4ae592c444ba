/*
 * ���ű���Ҫ������ͼ����
 */

---------------------���۳��ⱨ����ͼ ��ʼ------------------------
/****** Object:  View [dbo].[view_stockoutreport]���۳��ⱨ����ͼ    Script Date: 2022/4/1 14:46:05 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sysobjects WHERE name = N'view_stockoutreport')
	DROP VIEW  [view_stockoutreport]
GO

 create view [dbo].[view_stockoutreport]
 as
 (
   select distinct t1.fmainorgid,t1.fbilltype 'fbilltype',t1.fbillno 'fno',t2.fseq 'FSeq',t1.fstatus 'fstatus',isnull(t1.fdate,'1900-01-01 00:00:01') 'fdate',
 ISNULL(CONVERT(varchar(100),t1.fapprovedate, 23),'') 'fapprovedate', isnull(so1.fwithin,'') 'fwithin', t1.fshipping<PERSON><PERSON> 'fshippingbillno', 
 t2.fmaterialid 'fmaterialid', t2.fcustomdesc 'fcustomdesc',isnull(soent.fresultbrandid,'') 'fresultbrandid',t2.fbizplanqty 'fbizplanqty',t2.fbizqty 'fbizqty',
 t2.fbizunitid 'fbizunitid',isnull(soent.famount,0) 'begdiscountamount',t2.famount 'enddiscountamount',convert(nvarchar(10),convert(NUMERIC(18, 2),isnull(soent.fdistrateraw*10,'0')))+'%' 'fdistrateraw',isnull(s1.fnumber,'') 'storenumber',
 isnull(s1.fname,'') 'storename',t2.fsourcebillno 'fsourcebillno', isnull(soent.fclosestatus,'') 'fclosestatus', isnull(so1.fstatus,'') 'orderfstatus',
 isnull(stuff((select';' + s.fname + ' ' + convert(nvarchar(10),convert(decimal,od1.fratio)) +'%' from t_ydj_orderduty od1 left join t_bd_staff s on s.fid=od1.fdutyid where so1.fid = od1.fid  for xml path('')),1,1,''),'') 'fname',
 t1.fcustomerid 'fcustomerid',isnull(t1.fconsignee,'') 'fconsignee',t1.fphone 'fphone',t1.faddress 'faddress', isnull(auxpro.fname,'') fattrinfo,
 isnull(so1.fdealamount,0) fdealamount,isnull(so1.freceivable,0) freceivable,isnull(so1.factrefundamount,0)factrefundamount,isnull(t1.fdescription,'') fdescription
 from t_stk_sostockout t1
 left join t_stk_sostockoutentry t2 on t1.fid=t2.fid 
 left join t_ydj_orderentry soent on t2.fsourceentryid=soent.fentryid
 left join t_ydj_order so1 on soent.fid = so1.fid 
 left join t_ydj_orderduty oduty on so1.fid=oduty.fid
 left join t_bd_staff staff on oduty.fdutyid = staff.fid
 left join t_bd_department d1 on t1.fsodeptid=d1.fid
 left join t_bas_store s1 on d1.fstoreid = s1.fnumber
 left join T_BD_AUXPROPVALUE auxpro ON auxpro.fid = t2.fattrinfo 
 union
 select distinct t1.fmainorgid,t1.fbilltype 'fbilltype',t1.fbillno 'fbillno',t2.fseq 'FSeq',t1.fstatus 'fstatus',isnull(t1.fdate,'1900-01-01 00:00:01') 'fdate',
 ISNULL(CONVERT(varchar(100),t1.fapprovedate, 23),'') 'fapprovedate', '' 'fwithin', t1.fshippingbillno 'fshippingbillno', 
 t2.fmaterialid 'fmaterialid', t2.fcustomdesc 'fcustomdesc','' 'fresultbrandid',t2.fbizplanqty 'fbizplanqty',t2.fbizqty 'fbizqty',
 t2.fbizunitid 'fbizunitid','0' 'begdiscountamount',t2.famount 'enddiscountamount',convert(nvarchar(10),convert(NUMERIC(18, 2),'0'))+'%' 'fdistrateraw',isnull(s1.fnumber,'') 'storenumber',
 isnull(s1.fname,'') 'storename','' 'fsourcebillno','' 'fclosestatus', '' 'orderfstatus',
 isnull(stuff((select';' + s.fname + ' ' + convert(nvarchar(10),convert(decimal,od1.fratio)) +'%' from t_ydj_orderduty od1 left join t_bd_staff s on s.fid=od1.fdutyid where so1.fid = od1.fid  for xml path('')),1,1,''),'') 'fname',
 t1.fcustomerid 'fcustomerid',isnull(dbstaff.fname,'') 'fconsignee','' 'fphone','' 'faddress',isnull(auxpro.fname,'') fattrinfo,
 isnull(so1.fdealamount,0) fdealamount,isnull(so1.freceivable,0) freceivable,isnull(so1.factrefundamount,0)factrefundamount,isnull(t1.fdescription,'') fdescription
 from t_stk_otherstockout t1
 left join t_stk_otherstockoutentry t2 on t1.fid=t2.fid 
 left join t_ydj_orderentry soent on t2.fsourceentryid=soent.fentryid
 left join t_ydj_order so1 on soent.fid = so1.fid 
 left join t_ydj_orderduty oduty on so1.fid=oduty.fid
 left join t_bd_staff staff on oduty.fdutyid = staff.fid
 left join t_bd_staff dbstaff on t1.fstaffid = dbstaff.fid
 left join t_bd_department d1 on t1.fdeptid=d1.fid
 left join t_bas_store s1 on d1.fstoreid = s1.fnumber
 left join T_BD_AUXPROPVALUE auxpro ON auxpro.fid = t2.fattrinfo 
 )

GO


---------------------���۳��ⱨ����ͼ ����------------------------


IF  EXISTS (SELECT * FROM sysobjects WHERE name = N'V_STK_INVENTORYLIST')
	DROP VIEW  V_STK_INVENTORYLIST
GO		   
create view [dbo].[V_STK_INVENTORYLIST]  
as  
(  
SELECT   i.*, id.freserveqty, id.fstockreserveqty, id.fusableqty,   
                id.fstockusableqty, id.fintransitqty, id.fstockintransitqty  
FROM      dbo.T_STK_INVENTORYLIST AS i WITH (nolock) LEFT OUTER JOIN  
                dbo.T_STK_INVENTORYLIST_EXTENDDATA AS id WITH (nolock) ON i.fid = id.fid  
)  
  