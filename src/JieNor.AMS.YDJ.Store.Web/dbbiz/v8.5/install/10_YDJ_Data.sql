/*
 * 本脚本主要用于添加业务系统预置的数据脚本
 * init by <PERSON><PERSON><PERSON><PERSON>. at 2021-06-11
 */

--在此处定义系统预置的数据脚本，比如：辅助资料

--门店生成部门任务
delete from t_bas_task where fid='befce5d01deb4cea8ed486e50d849089';
insert into t_bas_task (fid,fformid,fmainorgid,ftaskparameter,fnumber,fname,ftasklabel,fpluginid,fpluginid_txt,fcondition,fexecuteplan,fdescription,fsysmessage,fstartstatus,fforbidstatus,fispreset)
values('befce5d01deb4cea8ed486e50d849089','bas_task','821347239912935425','{"topCompanyId":"821347239912935425"}','RW_SYS_StoreCreateDept','门店生成部门','门店生成部门','StoreCreateDeptTask','门店生成部门','','* 0/5 * * * ?','每5分钟执行一次','0','ew_start003','0','0');

--添加辅助资料：跟进记录对象类型
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='710528766530686976';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='710528766530686976';

INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'710528766530686976', N'bd_enumdata', N'跟进记录对象类型', N'跟进记录对象类型', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'销售管理', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N' ', 0, N'0', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype01', 0, N'710528766530686976', N'跟进记录', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype02', 0, N'710528766530686976', N'初建档案', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype03', 0, N'710528766530686976', N'邀约进店', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype04', 0, N'710528766530686976', N'意向报价', N' ', 4, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype05', 0, N'710528766530686976', N'收订金', N' ', 5, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype06', 0, N'710528766530686976', N'指派量尺', N' ', 6, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype07', 0, N'710528766530686976', N'邀约量尺', N' ', 7, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype08', 0, N'710528766530686976', N'提交量尺', N' ', 8, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype09', 0, N'710528766530686976', N'指派设计', N' ', 9, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype10', 0, N'710528766530686976', N'提交设计', N' ', 10, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype11', 0, N'710528766530686976', N'邀约看方案', N' ', 11, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype12', 0, N'710528766530686976', N'签订合同', N' ', 12, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype13', 0, N'710528766530686976', N'收货款', N' ', 13, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype14', 0, N'710528766530686976', N'名片分享', N' ', 14, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype15', 0, N'710528766530686976', N'案例分享', N' ', 15, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype16', 0, N'710528766530686976', N'费用记录', N' ', 16, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype17', 0, N'710528766530686976', N'阶段推进', N' ', 17, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype18', 0, N'710528766530686976', N'失效关闭', N' ', 18, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype19', 0, N'710528766530686976', N'报备商机', N' ', 19, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype20', 0, N'710528766530686976', N'成单关闭', N' ', 20, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype21', 0, N'710528766530686976', N'删除销售成员', N' ', 21, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype22', 0, N'710528766530686976', N'新增销售成员', N' ', 22, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype23', 0, N'710528766530686976', N'更换销售成员', N' ', 23, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype24', 0, N'710528766530686976', N'回收', N' ', 24, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype25', 0, N'710528766530686976', N'更换负责人', N' ', 25, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype26', 0, N'710528766530686976', N'派单', N' ', 26, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype27', 0, N'710528766530686976', N'接单', N' ', 27, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype28', 0, N'710528766530686976', N'预约', N' ', 28, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
-- 反馈
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype29', 0, N'710528766530686976', N'回访', N' ', 29, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype30', 0, N'710528766530686976', N'完工', N' ', 30, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype31', 0, N'710528766530686976', N'拒单', N' ', 31, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype32', 0, N'710528766530686976', N'拒单驳回', N' ', 32, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype33', 0, N'710528766530686976', N'结算完成', N' ', 33, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype34', 0, N'710528766530686976', N'变更', N' ', 34, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'objecttype35', 0, N'710528766530686976', N'自动关闭', N' ', 35, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO


--添加辅助资料：户型枚举
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='2770abfbd5c84b4389992966fa7ce859';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='2770abfbd5c84b4389992966fa7ce859';

INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fmainorgid], [fname_py], [fname_py2], [fmodule], [fmoduleorder], [fvisible], [fbizruleid], [fflowinstanceid], [ftranid], [fmainorgid_txt], [fmainorgid_pid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fchaindataid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [ffromchaindataid], [fchangestatus], [fprintcount], [fprintid], [fprintdate], [ffromtranid], [froottranid], [fparenttranid], [ftoptranid], [fnextprocnode], [fprimitiveid]) VALUES (N'2770abfbd5c84b4389992966fa7ce859', N'bd_enumdata', N'户型枚举', N'户型枚举', N' ', N' ', NULL, N' ', NULL, N'1', N'B', N' ', NULL, N'0', N' ', NULL, N'0', N' ', N' ', N'基础管理', 0, N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_01', 0, N'2770abfbd5c84b4389992966fa7ce859', N'0', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_02', 0, N'2770abfbd5c84b4389992966fa7ce859', N'1', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_03', 0, N'2770abfbd5c84b4389992966fa7ce859', N'2', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_04', 0, N'2770abfbd5c84b4389992966fa7ce859', N'3', N' ', 4, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_05', 0, N'2770abfbd5c84b4389992966fa7ce859', N'4', N' ', 5, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_06', 0, N'2770abfbd5c84b4389992966fa7ce859', N'5', N' ', 6, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_07', 0, N'2770abfbd5c84b4389992966fa7ce859', N'6', N' ', 7, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_08', 0, N'2770abfbd5c84b4389992966fa7ce859', N'7', N' ', 8, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_09', 0, N'2770abfbd5c84b4389992966fa7ce859', N'8', N' ', 9, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [FTranId], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'house_type_10', 0, N'2770abfbd5c84b4389992966fa7ce859', N'9', N' ', 10, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ')
GO

--添加辅助资料：可销范围
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='abe138b2351f8bc5b039b13f6c4e04c2';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='abe138b2351f8bc5b039b13f6c4e04c2';

INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'abe138b2351f8bc5b039b13f6c4e04c2', N'bd_enumdata', N'可销范围', N'可销范围', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'销售管理', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N' ', 0, N'0', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'permrange_01', 0, N'abe138b2351f8bc5b039b13f6c4e04c2', N'仓库', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
--添加辅助资料：可销对象
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='36bde784f8d511c078e49791312c6c3a';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='36bde784f8d511c078e49791312c6c3a';

INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'36bde784f8d511c078e49791312c6c3a', N'bd_enumdata', N'可销对象', N'可销对象', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'销售管理', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N' ', 0, N'0', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'permobj_01', 0, N'36bde784f8d511c078e49791312c6c3a', N'销售人员', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

--添加辅助资料：控制类型
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='f9f28c3e310c17beb3da84c9f83e28b1';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='f9f28c3e310c17beb3da84c9f83e28b1';

INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'f9f28c3e310c17beb3da84c9f83e28b1', N'bd_enumdata', N'控制类型', N'控制类型', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'销售管理', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N' ', 0, N'0', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'control_type_01', 0, N'f9f28c3e310c17beb3da84c9f83e28b1', N'允销', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

--添加辅助资料：部门类型
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='9c8d18ce302209e4bed841ff93eee170';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='9c8d18ce302209e4bed841ff93eee170';

INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2])
VALUES (N'9c8d18ce302209e4bed841ff93eee170', N'bd_enumdata', N'部门类型', N'部门类型', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'基础管理', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N' ', 0, N'0', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'dept_type_01', 0, N'9c8d18ce302209e4bed841ff93eee170', N'总部', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'dept_type_02', 0, N'9c8d18ce302209e4bed841ff93eee170', N'分公司', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'dept_type_03', 0, N'9c8d18ce302209e4bed841ff93eee170', N'门店', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

-- 将渠道类型设置为可见
UPDATE [dbo].[T_BD_ENUMDATA] set fvisible='1' where fnumber = '渠道类型';

-- 将销售机会的类型 ftype 改为默认值是个人，即 2
UPDATE [dbo].[T_YDJ_CUSTOMERRECORD] set ftype='2' where ftype in ('', ' ');

-- 将客户的客户类型 ftype 改为默认值是个人，即 customertype_00
UPDATE [dbo].[T_YDJ_CUSTOMER] set ftype='customertype_00' where ftype in ('', ' ');

-- 意向品类
delete from t_bd_enumdata where fid='e9b4d79f7eb4464c8529d9a59419b8e4';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('e9b4d79f7eb4464c8529d9a59419b8e4','bd_enumdata','意向品类','1','B','0','销售管理',1,'1','0');

-- 系统消息改为站内消息；添加通知方式：企业微信，并重新排序
delete from t_bd_enumdata where fid='9759b271e357469089b542ccba79023d';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('9759b271e357469089b542ccba79023d','bd_enumdata','通知方式','1','B','0','系统预置',0,'0','0');
delete from t_bd_enumdataentry where fid = '9759b271e357469089b542ccba79023d';
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_message001','9759b271e357469089b542ccba79023d',1,'0','1','','站内消息');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_message002','9759b271e357469089b542ccba79023d',3,'0','1','','邮件');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('ew_message003','9759b271e357469089b542ccba79023d',2,'0','1','','企业微信');

-- 添加客户来源：小程序
delete from t_bd_enumdataentry where fentryid='cussource_08' and fid='bcf7483b775446c18f7b1c68a389c034';
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('cussource_08','bcf7483b775446c18f7b1c68a389c034',8,'0','1','','会员版小程序');

-- 添加优惠券类型：商品券
delete from t_bd_enumdata where fid='7941a6cf25f7c0c00bf6b740d0719305';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7941a6cf25f7c0c00bf6b740d0719305','bd_enumdata','优惠券类型','1','B','0','系统预置',0,'0','0');
delete from t_bd_enumdataentry where fid = '7941a6cf25f7c0c00bf6b740d0719305';
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('coupontype_01','7941a6cf25f7c0c00bf6b740d0719305',1,'0','1','','商品券');


-- 预设客户分类：零售客户，经销商
delete from t_bd_enumdata where fid='cb20d0434e264f9281b1e77fecc28996';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('cb20d0434e264f9281b1e77fecc28996','bd_enumdata','客户分类','1','B','0','销售管理',1,'1','0');
delete from t_bd_enumdataentry where fentryid in ('custtype_01', 'custtype_02','custtype_03', 'custtype_04','custtype_05', 'custtype_06','custtype_07');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('custtype_01','cb20d0434e264f9281b1e77fecc28996',1,'0','1','','零售客户');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('custtype_02','cb20d0434e264f9281b1e77fecc28996',2,'0','1','','经销商');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('custtype_03','cb20d0434e264f9281b1e77fecc28996',3,'0','1','','商超（沃尔玛、百安居、嘉荣等）');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('custtype_04','cb20d0434e264f9281b1e77fecc28996',4,'0','1','','电器（国美、苏宁、京东等）');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('custtype_05','cb20d0434e264f9281b1e77fecc28996',5,'0','1','','家装');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('custtype_06','cb20d0434e264f9281b1e77fecc28996',6,'0','1','','地产/物业');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('custtype_07','cb20d0434e264f9281b1e77fecc28996',7,'0','1','','异业（家博会、定制等）');

-- 将[已转客户]状态隐藏（删除），并新增[成单关闭]状态。
delete t_bd_enumdataentry where fentryid in ('chance_status_05', 'chance_status_06');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('chance_status_06','cfb7a910d8934a20b452e6c4af94b5d3',6,'0','1','','成单关闭');
-- 将历史[已转客户]状态的数据改为[成单关闭]
update t_ydj_customerrecord set fchancestatus='chance_status_06' where fchancestatus='chance_status_05';
-- 将历史[成单关闭]商机阶段的数据改为[成单关闭]
update t_ydj_customerrecord set fchancestatus='chance_status_06' where fphase='customerrecord_phase_05';

--修改辅助资料：空间和风格的预设值为0
select * into T_BD_ENUMDATAENTRY_b1028 from t_bd_enumdataentry;
update t_bd_enumdataentry set fispreset=0 where fid='2db5b72c650541e981d1b643fed9d096';
update t_bd_enumdataentry set fispreset=0 where fid='f789c296a06247ffad978acb945a3fa0';

--销售机会 添加单据类型 保证只有一条销售机会类型
delete from t_bd_billtype where fid in('ydj_customerrecord_01') OR fname = '销售机会地址必录';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_customerrecord_01','bd_billtype','XSJH_SYS_01','标准销售机会','0','2021-10-25 00:00:00','0','E','0','0','','',N'未发布','','',N'系统预置','ydj_customerrecord','','1','');


--在《辅助资料》中增加预置系统的辅助资料【国内电信号段】
DELETE from t_bd_enumdata where fid='7C18C076-73B6-4E44-8627-173806AA4B66';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid)
VALUES('7C18C076-73B6-4E44-8627-173806AA4B66','bd_enumdata','国内电信号段','1','B','0','系统预置',4,'1','0');
delete from t_bd_enumdataentry WHERE fid='7C18C076-73B6-4E44-8627-173806AA4B66';
--电信
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_1','7C18C076-73B6-4E44-8627-173806AA4B66',1,'0','1','电信','133');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_2','7C18C076-73B6-4E44-8627-173806AA4B66',2,'0','1','电信','149');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_3','7C18C076-73B6-4E44-8627-173806AA4B66',3,'0','1','电信','153');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_4','7C18C076-73B6-4E44-8627-173806AA4B66',4,'0','1','电信','173');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_5','7C18C076-73B6-4E44-8627-173806AA4B66',5,'0','1','电信','177');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_6','7C18C076-73B6-4E44-8627-173806AA4B66',6,'0','1','电信','180');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_7','7C18C076-73B6-4E44-8627-173806AA4B66',7,'0','1','电信','181');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_8','7C18C076-73B6-4E44-8627-173806AA4B66',8,'0','1','电信','189');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_9','7C18C076-73B6-4E44-8627-173806AA4B66',9,'0','1','电信','199');
--联通
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_10','7C18C076-73B6-4E44-8627-173806AA4B66',10,'0','1','联通','130');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_11','7C18C076-73B6-4E44-8627-173806AA4B66',11,'0','1','联通','131');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_12','7C18C076-73B6-4E44-8627-173806AA4B66',12,'0','1','联通','132');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_13','7C18C076-73B6-4E44-8627-173806AA4B66',13,'0','1','联通','145');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_14','7C18C076-73B6-4E44-8627-173806AA4B66',14,'0','1','联通','155');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_15','7C18C076-73B6-4E44-8627-173806AA4B66',15,'0','1','联通','156');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_16','7C18C076-73B6-4E44-8627-173806AA4B66',16,'0','1','联通','166');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_17','7C18C076-73B6-4E44-8627-173806AA4B66',17,'0','1','联通','171');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_18','7C18C076-73B6-4E44-8627-173806AA4B66',18,'0','1','联通','175');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_19','7C18C076-73B6-4E44-8627-173806AA4B66',19,'0','1','联通','176');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_20','7C18C076-73B6-4E44-8627-173806AA4B66',20,'0','1','联通','185');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_21','7C18C076-73B6-4E44-8627-173806AA4B66',21,'0','1','联通','186');

--移动
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_23','7C18C076-73B6-4E44-8627-173806AA4B66',23,'0','1','移动','134');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_24','7C18C076-73B6-4E44-8627-173806AA4B66',24,'0','1','移动','135');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_25','7C18C076-73B6-4E44-8627-173806AA4B66',25,'0','1','移动','137');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_26','7C18C076-73B6-4E44-8627-173806AA4B66',26,'0','1','移动','138');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_27','7C18C076-73B6-4E44-8627-173806AA4B66',27,'0','1','移动','139');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_28','7C18C076-73B6-4E44-8627-173806AA4B66',28,'0','1','移动','147');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_29','7C18C076-73B6-4E44-8627-173806AA4B66',29,'0','1','移动','150');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_30','7C18C076-73B6-4E44-8627-173806AA4B66',30,'0','1','移动','151');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_31','7C18C076-73B6-4E44-8627-173806AA4B66',31,'0','1','移动','152');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_32','7C18C076-73B6-4E44-8627-173806AA4B66',32,'0','1','移动','157');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_33','7C18C076-73B6-4E44-8627-173806AA4B66',33,'0','1','移动','158');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_34','7C18C076-73B6-4E44-8627-173806AA4B66',34,'0','1','移动','159');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_35','7C18C076-73B6-4E44-8627-173806AA4B66',35,'0','1','移动','136');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_36','7C18C076-73B6-4E44-8627-173806AA4B66',36,'0','1','移动','172');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_37','7C18C076-73B6-4E44-8627-173806AA4B66',37,'0','1','移动','178');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_38','7C18C076-73B6-4E44-8627-173806AA4B66',38,'0','1','移动','182');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_39','7C18C076-73B6-4E44-8627-173806AA4B66',39,'0','1','移动','183');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_40','7C18C076-73B6-4E44-8627-173806AA4B66',40,'0','1','移动','184');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_41','7C18C076-73B6-4E44-8627-173806AA4B66',41,'0','1','移动','187');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_42','7C18C076-73B6-4E44-8627-173806AA4B66',42,'0','1','移动','188');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('phoneNum_43','7C18C076-73B6-4E44-8627-173806AA4B66',43,'0','1','移动','198');


--将案例库 发布状态：未发送改为未发布，已发送改为已发布。
UPDATE T_BD_ENUMDATAENTRY SET fenumitem = '未发布' WHERE fenumitem = '未发送' AND fid IN (  SELECT fid FROM dbo.T_BD_ENUMDATA WHERE  fname='发布状态')
UPDATE T_BD_ENUMDATAENTRY SET fenumitem = '已发布' WHERE fenumitem = '已发送' AND fid IN (  SELECT fid FROM dbo.T_BD_ENUMDATA WHERE  fname='发布状态')


--在所有预置脚本执行后，统一修正禁用状态 和 企业ID
update t_bd_enumdata set fforbidstatus='0' where fforbidstatus='';
update t_bd_enumdataentry set fenmainorgid='0' where fenmainorgid='';


--添加辅助资料：经销商等级
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='35071eda4d3f0abf3de815753a39d7fc';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='35071eda4d3f0abf3de815753a39d7fc';
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'35071eda4d3f0abf3de815753a39d7fc', N'bd_enumdata', N'经销商等级', N'经销商等级', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'经销商管理', 0, N'1', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'agentrank01', 0, N'35071eda4d3f0abf3de815753a39d7fc', N'等级A', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'agentrank02', 0, N'35071eda4d3f0abf3de815753a39d7fc', N'等级B', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'agentrank03', 0, N'35071eda4d3f0abf3de815753a39d7fc', N'等级C', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

--添加辅助资料：货币
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='2527d5a6cdd91cae0f83cef484487d1d';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='2527d5a6cdd91cae0f83cef484487d1d';
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'2527d5a6cdd91cae0f83cef484487d1d', N'bd_enumdata', N'货币', N'货币', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'基础管理', 0, N'1', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'currency01', 0, N'2527d5a6cdd91cae0f83cef484487d1d', N'CNY，人民币元，中国，￥', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'currency02', 0, N'2527d5a6cdd91cae0f83cef484487d1d', N'USD，美元，美国，＄', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'currency03', 0, N'2527d5a6cdd91cae0f83cef484487d1d', N'JPY，日元，日本，円', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

--添加辅助资料：区域经理
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='92df89f10a5f448e90b83a7c191a6aef';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='92df89f10a5f448e90b83a7c191a6aef';
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'92df89f10a5f448e90b83a7c191a6aef', N'bd_enumdata', N'区域经理', N'区域经理', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'经销商管理', 0, N'1', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'regiomanager01', 0, N'92df89f10a5f448e90b83a7c191a6aef', N'区域经理1', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'regiomanager02', 0, N'92df89f10a5f448e90b83a7c191a6aef', N'区域经理2', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'regiomanager03', 0, N'92df89f10a5f448e90b83a7c191a6aef', N'区域经理3', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

--添加辅助资料：组织节点类型
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='c4c61d5f825850ba65db447f0b51861c';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='c4c61d5f825850ba65db447f0b51861c';
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'c4c61d5f825850ba65db447f0b51861c', N'bd_enumdata', N'组织节点类型', N'组织节点类型', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'基础管理', 0, N'1', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'orgnodetype01', 0, N'c4c61d5f825850ba65db447f0b51861c', N'总部', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'orgnodetype02', 0, N'c4c61d5f825850ba65db447f0b51861c', N'分公司', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'orgnodetype03', 0, N'c4c61d5f825850ba65db447f0b51861c', N'区域', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'orgnodetype04', 0, N'c4c61d5f825850ba65db447f0b51861c', N'经销商', N' ', 4, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'orgnodetype05', 0, N'c4c61d5f825850ba65db447f0b51861c', N'门店', N' ', 5, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

--添加辅助资料：商场性质
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='84d871a9f86e9ba4c9dc33759bfb0d4d';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='84d871a9f86e9ba4c9dc33759bfb0d4d';
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES (N'84d871a9f86e9ba4c9dc33759bfb0d4d', N'bd_enumdata', N'商场性质', N'商场性质', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'基础管理', 0, N'1', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'malltype01', 0, N'84d871a9f86e9ba4c9dc33759bfb0d4d', N'商场店', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'malltype02', 0, N'84d871a9f86e9ba4c9dc33759bfb0d4d', N'街边店', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES (N'malltype03', 0, N'84d871a9f86e9ba4c9dc33759bfb0d4d', N'创新渠道', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO

 
--转单申请单 添加单据类型 保证只有一条转单申请
delete from t_bd_billtype where fid in('ydj_transferorderapply_01') OR fname = '标准转单申请';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_transferorderapply_01','bd_billtype','BTZDSQ_SYS_01','标准转单申请','0','2022-01-11 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_transferorderapply','','1','');

--销售合同 添加单据类型 保证只有一条销售转单
delete from t_bd_billtype where fid in('ydj_saletransferorder_01') OR fname = '销售转单';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_saletransferorder_01','bd_billtype','XSZD_SYS_01','销售转单','0','2022-01-11 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_order','','1','');



--其他应付单 添加单据类型:借贷其他应付
delete from t_bd_billtype where fid='ydj_payreceipt_01';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_payreceipt_01','bd_billtype','JDQTYF_SYS_01','借贷其他应付','0','2022-01-11 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_payreceipt','','1','');

--其他应付单 添加单据类型:转单其他应付
delete from t_bd_billtype where fid='ydj_payreceipt_02';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_payreceipt_02','bd_billtype','ZDQTYF_SYS_02','转单其他应付','0','2022-01-11 00:00:00','0','E','0','0','','',N'未发布','','',N'系统预置','ydj_payreceipt','','1','');

--其他应付单 添加单据类型:售后其他应付
delete from t_bd_billtype where fid='ydj_payreceipt_03';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_payreceipt_03','bd_billtype','SHQTYF_SYS_03','售后其他应付','0','2022-01-11 00:00:00','0','E','0','0','','',N'未发布','','',N'系统预置','ydj_payreceipt','','1','');

--其他应收单 添加单据类型:借贷其他应收
delete from t_bd_billtype where fid='ydj_collectreceipt_01';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_collectreceipt_01','bd_billtype','JDQTYS_SYS_01','借贷其他应收','0','2022-01-11 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_collectreceipt','','1','');

--其他应收单 添加单据类型:转单其他应收
delete from t_bd_billtype where fid='ydj_collectreceipt_02';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_collectreceipt_02','bd_billtype','ZDQTYS_SYS_02','转单其他应收','0','2022-01-11 00:00:00','0','E','0','0','','',N'未发布','','',N'系统预置','ydj_collectreceipt','','1','');

--其他应收单 添加单据类型:售后其他应收
delete from t_bd_billtype where fid='ydj_collectreceipt_03';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_collectreceipt_03','bd_billtype','SHQTYS_SYS_03','售后其他应收','0','2022-01-11 00:00:00','0','E','0','0','','',N'未发布','','',N'系统预置','ydj_collectreceipt','','1','');



--辅助资料入库类型
delete from t_bd_enumdata where fid='798603128604856320';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible)
values('798603128604856320','','入库类型','0','','0','系统预置',1,'1');

delete from t_bd_enumdataentry where fentryid in('798603128604856321');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem)
values('798603128604856321','798603128604856320',0,'0','0','','售后维修');


--辅助资料出库类型
delete from t_bd_enumdata where fid='798589124356476928';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible)
values('798589124356476928','','出库类型','0','','0','系统预置',1,'1');

delete from t_bd_enumdataentry where fentryid in('798589124356476929');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem)
values('798589124356476929','798589124356476928',0,'0','0','','报废');

delete from t_bd_enumdataentry where fentryid in('798589124356476930');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem)
values('798589124356476930','798589124356476928',0,'0','0','','员工领用');

delete from t_bd_enumdataentry where fentryid in('798589124356476931');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem)
values('798589124356476931','798589124356476928',0,'0','0','','售后维修');

--添加字符串表达式语法解析默认参数
if not exists(select 1 from t_sys_systemprofile where fid='798499983828586497' and fkey = 'ydj_goodscorrelatedparms_parameter')
begin
INSERT INTO [dbo].[T_SYS_SYSTEMPROFILE] ([fid] ,[FFormId] ,[fcategory] ,[fkey] ,[fvalue] ,[fdesc] ,[fmainorgid])
VALUES('798499983828586497','','fw','ydj_goodscorrelatedparms_parameter','{"id":"","fstringseparators":"and,or,in,like,*,$,&,&&","fregularexpression":"KD9pcykoPzw9XFspKC4qKSg/PVxdPSkoLisp","fstringcontentseparators":"]="}','用于字符串公式解析','791801682743922689')
end



--采购订单 添加单据类型:摆场订单
delete from t_bd_billtype where fid in('ydj_purchaseorder_bc') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_purchaseorder_bc','bd_billtype','BCDD_SYS_01','摆场订单','0','2022-02-08 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_purchaseorder','','0','');

--采购订单 添加单据类型:备货订单
delete from t_bd_billtype where fid in('ydj_purchaseorder_bh') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_purchaseorder_bh','bd_billtype','BHDD_SYS_01','备货订单','0','2022-02-18 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_purchaseorder','','0','');

--采购订单 添加单据类型:总部手工单（禁用状态=1）
delete from t_bd_billtype where fid in('ydj_purchaseorder_zb') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_purchaseorder_zb','bd_billtype','ZBSGD_SYS_01','总部手工单','0','2022-03-14 00:00:00','1','E','1','0','','',N'未发布','','',N'系统预置','ydj_purchaseorder','','0','');

--采购订单 添加单据类型:期初采购订单
delete from t_bd_billtype where fid in('ydj_purchaseorder_qc') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_purchaseorder_qc','bd_billtype','QCCGDD_SYS_01','期初采购订单','0','2022-03-14 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_purchaseorder','','0','');

--采购申请单 添加单据类型:摆场订单
delete from t_bd_billtype where fid in('ydj_pur_reqorder_bc') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_pur_reqorder_bc','bd_billtype','BCDD_SYS_01','摆场订单','0','2022-02-08 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','pur_reqorder','','0','');

--合同 添加单据类型:大客户销售合同
delete from t_bd_billtype where fid in('ydj_order_major') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_order_major','bd_billtype','DKHXSHT_SYS_01','大客户销售合同','0','2022-02-19 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_order','','0','');

--合同 添加单据类型:期初销售合同
delete from t_bd_billtype where fid in('ydj_order_qc') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_order_qc','bd_billtype','QCXSHT_SYS_01','期初销售合同','0','2022-04-07 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_order','','0','');

--合同 添加单据类型:v6定制柜合同
delete from t_bd_billtype where fid in('ydj_order_vsix') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_order_vsix','bd_billtype','V6DZGHT_SYS_01','v6定制柜合同','0','2022-08-29 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_order','','0','');

--辅助资料 客户类型 送达方
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fentryid]='customertype_03';
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'customertype_03', 0, '96a51c6412014864be18a6475aae8993', N'送达方', N' ', 3, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')

GO

--售后维修单 添加单据类型 标准维修单
delete from t_bd_billtype where fid in('aft_repairorder_01') OR fname = '标准维修单';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('aft_repairorder_01','bd_billtype','BZWXD_SYS_01','标准维修单','0','2022-02-23 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','aft_repairorder','','1','');


--辅助资料 税率
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2])
select [fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2] from
    (
    select '' as [fid], '' as [FFormId], '' as [fnumber], '' as [fname], '' as [fdescription], '' as [fcreatorid], '' as [fcreatedate], '' as [fmodifierid], '' as [fmodifydate], '' as [fispreset], '' as [fstatus], '' as [fapproveid], '' as [fapprovedate], '' as [fforbidstatus], '' as [fforbidid], '' as [fforbiddate], '' as [fchangestatus], '' as [fnextprocnode], '' as [fmainorgid], '' as [fmainorgid_txt], '' as [fmainorgid_pid], '' as [fprimitiveid], '' as [fbizruleid], '' as [fflowinstanceid], '' as [ftranid], '' as [ffromtranid], '' as [froottranid], '' as [fprintcount], '' as [fprintid], '' as [fprintdate], '' as [fsendstatus], '' as [fdataorigin], '' as [fpublishcid], '' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fsenddate], '' as [fdownloaddate], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fmodule], '' as [fmoduleorder], '' as [fvisible], '' as [fparenttranid], '' as [ftoptranid], '' as [fname_py], '' as [fname_py2]
    union select N'55902411714E45ABBB1599882580C61F', N'bd_enumdata', N'税率', N'税率', N' ', N' ', NULL, N' ', NULL, N'1', N'B', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'销售管理', 1, N'1', N' ', N' ', N' ', N' '
    ) as t where t.fid != '' and not exists (select 1 from [T_BD_ENUMDATA] b where b.fid = t.fid)

    GO

delete from  [T_BD_ENUMDATAENTRY] where fid = '55902411714E45ABBB1599882580C61F'
--辅助资料 税率 明细
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid])
select [fentryid], [FSeq], [fid],[fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid] from
    (
    select '' as [fentryid],'' as [FSeq],'' as [fid],'' as [fenumid],'' as [fenumitem], '' as [fgroup], '' as [forder], '' as [fdisabled], '' as [fispreset], '' as [fenmainorgid], '' as [fpublishcid],'' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fsendstatus], '' as [fdataorigin], '' as [fsenddate], '' as [fdownloaddate], '' as [fmodifydate], '' as [ftranid], '' as [fparenttranid], '' as [ftoptranid]
    union select N'rate_03', 0, '55902411714E45ABBB1599882580C61F',N'0.03', N'0.03', N' ', 1, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_06', 0, '55902411714E45ABBB1599882580C61F',N'0.06', N'0.06', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_07', 0, '55902411714E45ABBB1599882580C61F',N'0.07', N'0.07', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_09', 0, '55902411714E45ABBB1599882580C61F',N'0.09', N'0.09', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_10', 0, '55902411714E45ABBB1599882580C61F',N'0.10', N'0.10', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_11', 0, '55902411714E45ABBB1599882580C61F',N'0.11', N'0.11', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_12', 0, '55902411714E45ABBB1599882580C61F',N'0.12', N'0.12', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_13', 0, '55902411714E45ABBB1599882580C61F',N'0.13', N'0.13', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_16', 0, '55902411714E45ABBB1599882580C61F',N'0.16', N'0.16', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'rate_17', 0, '55902411714E45ABBB1599882580C61F',N'0.17', N'0.17', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    ) as t where t.[fentryid] != '' and not exists(select 1 from [T_BD_ENUMDATAENTRY] b where b.[fentryid] = t.[fentryid] and b.[fid]='55902411714E45ABBB1599882580C61F')


--辅助资料 入库类型 明细
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid])
select [fentryid], [FSeq], [fid],[fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid] from
    (
    select '' as [fentryid],'' as [FSeq],'' as [fid],'' as [fenumid],'' as [fenumitem], '' as [fgroup], '' as [forder], '' as [fdisabled], '' as [fispreset], '' as [fenmainorgid], '' as [fpublishcid],'' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fsendstatus], '' as [fdataorigin], '' as [fsenddate], '' as [fdownloaddate], '' as [fmodifydate], '' as [ftranid], '' as [fparenttranid], '' as [ftoptranid]
    union select N'aft_repairorder_in', 0, '798603128604856320',N'aft_repairorder_01', N'售后维修', N' ', 1, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    ) as t where t.[fentryid] != '' and not exists(select 1 from [T_BD_ENUMDATAENTRY] b where b.[fentryid] = t.[fentryid] and b.[fid]='798603128604856320')


--辅助资料 入库类型 明细
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid])
select [fentryid], [FSeq], [fid],[fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid] from
    (
    select '' as [fentryid],'' as [FSeq],'' as [fid],'' as [fenumid],'' as [fenumitem], '' as [fgroup], '' as [forder], '' as [fdisabled], '' as [fispreset], '' as [fenmainorgid], '' as [fpublishcid],'' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fsendstatus], '' as [fdataorigin], '' as [fsenddate], '' as [fdownloaddate], '' as [fmodifydate], '' as [ftranid], '' as [fparenttranid], '' as [ftoptranid]
    union select N'aft_repairorder_out', 0, '799608436357730304',N'aft_repairorder_01', N'售后维修', N' ', 1, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    ) as t where t.[fentryid] != '' and not exists(select 1 from [T_BD_ENUMDATAENTRY] b where b.[fentryid] = t.[fentryid] and b.[fid]='799608436357730304' and t.fenumitem = b.fenumitem)



--添加【费用项目】基础资料默认初始化数据   后续更新客户环境时，需要获取到慕思总部组织Id来更新脚本中的组织Id
INSERT [dbo].[T_YDJ_EXPENSEITEM] ([fid],[FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [ftype], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fisbrokerage] )
select [fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [ftype], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fisbrokerage] from
    (
    select '' as [fid], '' as [FFormId], '' as [fnumber], '' as [fname], '' as [fdescription], '' as [fcreatorid], '' as [fcreatedate], '' as [fmodifierid], '' as [fmodifydate], '' as [fispreset], '' as [fstatus], '' as [fapproveid], '' as [fapprovedate], '' as [fforbidstatus], '' as [fforbidid], '' as [fforbiddate], '' as [fchangestatus], '' as [fnextprocnode], '' as [fmainorgid], '' as [fmainorgid_txt], '' as [fmainorgid_pid], '' as [fprimitiveid], '' as [fbizruleid], '' as [fflowinstanceid], '' as [ftranid], '' as [ffromtranid], '' as [froottranid], '' as [fprintcount], '' as [fprintid], '' as [fprintdate], '' as [fsendstatus], '' as [fdataorigin], '' as [fpublishcid], '' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fsenddate], '' as [fdownloaddate], '' as [fchaindataid], '' as [ffromchaindataid], '' as [ftype], '' as [fparenttranid], '' as [ftoptranid], '' as [fname_py], '' as [fname_py2], '' as [fisbrokerage]
    union select N'EXPENSEITEM_ZDWLFY',N'ydj_expenseitem', N'EXPENSEITEM_ZDWLFY', N'转单往来费用', N' ', N'804396121043636230', GETDATE(), N'804396121043636230', GETDATE(), N'0', N'E', N'804396121043636230', GETDATE(), N'0', N' ', NULL, N'0', N' ', N'804289816098377728', N' ', N' ', N' ', N' ', N' ', N'816681473301155841', N' ', N' ', 0, N' ', NULL, N'未发布', N' ', N'804289816098377728', N'慕思健康睡眠股份有限公司', N'230300441701912576', NULL, NULL, N' ', N' ', N'expensetype_01', N'816681473301155845', N'816681473301155843', N'Z', N'ZDWLFY', N'0'
    UNION SELECT N'EXPENSEITEM_WXWLFY',N'ydj_expenseitem', N'EXPENSEITEM_WXWLFY', N'维修往来费用', N' ', N'804396121043636230', GETDATE(), N'804396121043636230', GETDATE(), N'0', N'E', N'804396121043636230', GETDATE(), N'0', N' ', NULL, N'0', N' ', N'804289816098377728', N' ', N' ', N' ', N' ', N' ', N'816681473301155841', N' ', N' ', 0, N' ', NULL, N'未发布', N' ', N'804289816098377728', N'慕思健康睡眠股份有限公司', N'230300441701912576', NULL, NULL, N' ', N' ', N'expensetype_01', N'816681473301155845', N'816681473301155843', N'Z', N'ZDWLFY', N'0'
    ) as t WHERE t.[FFormId] != '' and not exists(
    select 1 from [T_YDJ_EXPENSEITEM] e where e.fid = t.fid and e.fnumber = t.fnumber
    )

--添加【费用项目】基础资料默认初始化数据 借贷项费用
INSERT [dbo].[T_YDJ_EXPENSEITEM] ([fid],[FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [ftype], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fisbrokerage] )
select [fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [ftype], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fisbrokerage] from
    (
    select '' as [fid], '' as [FFormId], '' as [fnumber], '' as [fname], '' as [fdescription], '' as [fcreatorid], '' as [fcreatedate], '' as [fmodifierid], '' as [fmodifydate], '' as [fispreset], '' as [fstatus], '' as [fapproveid], '' as [fapprovedate], '' as [fforbidstatus], '' as [fforbidid], '' as [fforbiddate], '' as [fchangestatus], '' as [fnextprocnode], '' as [fmainorgid], '' as [fmainorgid_txt], '' as [fmainorgid_pid], '' as [fprimitiveid], '' as [fbizruleid], '' as [fflowinstanceid], '' as [ftranid], '' as [ffromtranid], '' as [froottranid], '' as [fprintcount], '' as [fprintid], '' as [fprintdate], '' as [fsendstatus], '' as [fdataorigin], '' as [fpublishcid], '' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fsenddate], '' as [fdownloaddate], '' as [fchaindataid], '' as [ffromchaindataid], '' as [ftype], '' as [fparenttranid], '' as [ftoptranid], '' as [fname_py], '' as [fname_py2], '' as [fisbrokerage]
    union select N'EXPENSEITEM_JDXFY',N'ydj_expenseitem', N'EXPENSEITEM_JDXFY', N'借贷项费用', N' ', N'804396121043636230', GETDATE(), N'804396121043636230', GETDATE(), N'0', N'E', N'804396121043636230', GETDATE(), N'0', N' ', NULL, N'0', N' ', N'0', N' ', N' ', N' ', N' ', N' ', N'816681473301155841', N' ', N' ', 0, N' ', NULL, N'未发布', N' ', N'0', N'慕思健康睡眠股份有限公司', N'230300441701912576', NULL, NULL, N' ', N' ', N'expensetype_01', N'816681473301155845', N'816681473301155843', N'Z', N'ZDWLFY', N'0'
    ) as t WHERE t.[FFormId] != '' and not exists(
    select 1 from [T_YDJ_EXPENSEITEM] e where e.fid = t.fid and e.fnumber = t.fnumber
    )




----------(辅助资料)生产要求-------------------
delete T_BD_ENUMDATA where fname  = '生产要求'

insert into T_BD_ENUMDATA(fid,fname,fispreset,fforbidstatus,fprintcount,fmodule,fmoduleorder,fvisible,fmainorgid)
values('804357957398695936','生产要求',0,0,0,'销售管理',1,1,'0')

delete T_BD_ENUMDATAENTRY where fid='804357957398695936'

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804357957398695937',0,'要求尺寸精确，按实际尺寸',1,0,0,'0','804357957398695936')

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804357957398695938',0,'生产放在同一张床上',2,0,0,'0','804357957398695936')

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804357957398695939',0,'客户同意拼接',3,0,0,'0','804357957398695936')

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804357957398695940',0,'与床配套',4,0,0,'0','804357957398695936')

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804357957398695941',0,'客户同意此配置',5,0,0,'0','804357957398695936')

----------(辅助资料)生产要求-------------------

----------(辅助资料)家纺套件要求-------------------
delete T_BD_ENUMDATA where fname  = '家纺套件要求'

insert into T_BD_ENUMDATA(fid,fname,fispreset,fforbidstatus,fprintcount,fmodule,fmoduleorder,fvisible,fmainorgid)
values('804358279747735552','家纺套件要求',0,0,0,'销售管理',1,1,'0')

delete T_BD_ENUMDATAENTRY where fid='804358279747735552'

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804358279747735553',0,'床单改床笠',1,0,0,'0','804358279747735552')

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804358279747735554',0,'大改小',2,0,0,'0','804358279747735552')

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804358279747735555',0,'特制',3,0,0,'0','804358279747735552')

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('804358279747735556',0,'大改小+床单改床笠',4,0,0,'0','804358279747735552')

----------(辅助资料)家纺套件要求-------------------



--库存调拨单 添加单据类型 标准要货调拨
delete from t_bd_billtype where fid in('stk_transfer_bzyhdb') OR fname = '标准要货调拨';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('stk_transfer_bzyhdb','bd_billtype','BZYHDB_SYS_02','标准要货调拨','0','2022-03-09 00:00:00','1','E','0','0','','',N'未发布','B','BZYHDB',N'系统预置','stk_inventorytransfer','','1','');

--删除单据类型 【支持图纸上传】
delete from T_BD_BILLTYPE where fname = '支持图纸上传'

--大客户销售合同 【合作渠道】必录默认为 "保存必录"
    INSERT [dbo].[T_BD_BILLTYPEFIELDENTRY] ([FEntryId], [FSeq], [fgroupname], [ffieldid], [ffieldid_txt], [fmustinput], [flock], [flock_txt], [fautomemory], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'823588612359721063', 50, N'销售合同', N'fchannel', N'合作渠道', N'1', N' ', N' ', N'0', N'823588612342943843', N'823588612355526742', N'823588612351332373', N' ', N'ydj_order_major')

delete from t_bd_billtype where fid in('stk_inventoryverify_cskcpdd') OR fname = '初始库存盘点单';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('stk_inventoryverify_cskcpdd','bd_billtype','CSKCPDD_SYS_01','初始库存盘点单','0','2022-03-26 00:00:00','1','E','0','0','','',N'未发布','B','BZYHDB',N'系统预置','stk_inventoryverify','','1','');

-- 客户来源选项（自然进店、老客复购、老客介绍、异业带单、商场活动、小区团购、异业联盟活动、电话销售、设计师介绍、新零售、品牌管理部、慕思优家、非销活动）
delete from t_bd_enumdataentry where fentryid in ('164743697441886208','164743697441886209','164743697441886210','248802607286587394','248802607286587395');
delete from t_bd_enumdataentry where fentryid in ('164743697441886200','164743697441886201','164743697441886202','164743697441886203','164743697441886204','164743697441886205','164743697441886206','164743697441886207','164743697441886208','164743697441886209','164743697441886210','164743697441886211','164743697441886212');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886200','a72f040851454cdd8dc2bdef3ea39808',1,'0','1','','自然进店','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886201','a72f040851454cdd8dc2bdef3ea39808',2,'0','1','','老客复购','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886202','a72f040851454cdd8dc2bdef3ea39808',3,'0','1','','老客介绍','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886203','a72f040851454cdd8dc2bdef3ea39808',4,'0','1','','异业带单','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886204','a72f040851454cdd8dc2bdef3ea39808',5,'0','1','','商场活动','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886205','a72f040851454cdd8dc2bdef3ea39808',6,'0','1','','小区团购','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886206','a72f040851454cdd8dc2bdef3ea39808',7,'0','1','','异业联盟活动','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886207','a72f040851454cdd8dc2bdef3ea39808',8,'0','1','','电话销售','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886208','a72f040851454cdd8dc2bdef3ea39808',9,'0','1','','设计师介绍','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886209','a72f040851454cdd8dc2bdef3ea39808',10,'0','1','','新零售','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886210','a72f040851454cdd8dc2bdef3ea39808',11,'0','1','','品牌管理部','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886211','a72f040851454cdd8dc2bdef3ea39808',12,'0','1','','慕思优家','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('164743697441886212','a72f040851454cdd8dc2bdef3ea39808',13,'0','1','','非销活动','0');

----------(辅助资料)退货原因-------------------
delete T_BD_ENUMDATA where fid = '811175771971391488';

insert into T_BD_ENUMDATA(fid,fname,fispreset,fforbidstatus,fprintcount,fmodule,fmoduleorder,fvisible,fmainorgid)
values('811175771971391488','退货原因',1,0,0,'系统预置',1,1,'0');

delete T_BD_ENUMDATAENTRY where fid='811175771971391488';

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('811175771971391401',0,'产品质量',1,0,1,'0','811175771971391488');

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('811175771971391402',0,'尺寸不合适',2,0,1,'0','811175771971391488');

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('811175771971391403',0,'软硬度不适',3,0,1,'0','811175771971391488');

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('811175771971391404',0,'其他',4,0,1,'0','811175771971391488');
----------(辅助资料)退货原因-------------------


----------begin 库存控制维度-------------------
delete T_BD_ENUMDATA where fid = 'stk_inv_ctrl_item';
delete T_BD_ENUMDATAENTRY where fid='stk_inv_ctrl_item';

insert into T_BD_ENUMDATA(fid,fname,fispreset,fforbidstatus,fprintcount,fmodule,fmoduleorder,fvisible,fmainorgid)
values('stk_inv_ctrl_item','库存控制维度',1,0,0,'系统预置',1,1,'0');

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('stk_inv_ctrl_item_fstorehouseid',0,'仓库',4,0,1,'0','stk_inv_ctrl_item');

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('stk_inv_ctrl_item_fstorelocationid',0,'仓位',4,0,1,'0','stk_inv_ctrl_item');

insert into T_BD_ENUMDATAENTRY(fentryid,FSeq,fenumitem,forder,fdisabled,fispreset,fenmainorgid,fid)
values('stk_inv_ctrl_item_fstockstatus',0,'库存状态',4,0,1,'0','stk_inv_ctrl_item');

----------end 库存控制维度-------------------

-- 数据组件数据初始化

-- 销售业绩
delete from [T_SYS_DATAWIDGETLIST] where fid ='436841309874032646';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid, FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'436841309874032646','0', N'sys_datawidgetlist', N'No.0000000001', N'销售业绩', N'436841160921714688', N'ydj_achievement','1','E','0','812002499052572667',370,6,'812002499052572671','812002499052572669','X','XSYJ');

-- 常用功能
delete from [T_SYS_DATAWIDGETLIST] where fid ='436844848788672518';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid, FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'436844848788672518','0', N'sys_datawidgetlist', N'No.0000000002', N'常用功能', N'436844755448631302', N'sys_cfunction','1','E','0','812002499052572668',370,6,'812002499052572672','812002499052572670','C','CYGN');

-- 销售趋势
delete from [T_SYS_DATAWIDGETLIST] where fid ='436854111963582470';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid, FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'436854111963582470','0', N'sys_datawidgetlist', N'No.0000000003', N'销售趋势', N'436841160921714688', N'ydj_trend','1','E','0','812002499052572669',370,6,'812002499052572673','812002499052572671','X','XSQS');

-- 销售目标
delete from [T_SYS_DATAWIDGETLIST] where fid ='436857469252276230';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid, FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'436857469252276230','0', N'sys_datawidgetlist', N'No.0000000004', N'销售目标', N'436841160921714688', N'ydj_target','1','E','0','812002499052572671',370,6,'812002499052572675','812002499052572673','X','XSMB');

-- 待办任务
delete from [T_SYS_DATAWIDGETLIST] where fid ='436866127990231040';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid, FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'436866127990231040','0', N'sys_datawidgetlist', N'No.0000000005', N'待办任务', N'436844755448631302', N'dashboard_mytask','1','E','0','812002499052572672',370,12,'812002499052572676','812002499052572674','D','DBRW');

-- 部门间排名
delete from [T_SYS_DATAWIDGETLIST] where fid ='808663202715013126';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid,FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'808663202715013126','0', N'sys_datawidgetlist', N'No.0000000006', N'部门间排名', N'436841160921714688', N'ydj_rank','1','E','0','808663202715013121',370,3,'808663202715013125','808663202715013123','B','BMJPM');

-- 我的公告
delete from [T_SYS_DATAWIDGETLIST] where fid ='812002499056766976';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid, FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'812002499056766976','0', N'sys_datawidgetlist', N'No.0000000007', N'我的公告', N'436844755448631302', N'im_mynotice','1','E','0','812002499052572673',370,3,'812002499052572677','812002499052572675','W','WDDBRW');

-- 部门内排名
delete from [T_SYS_DATAWIDGETLIST] where fid ='812002637695291392';
INSERT  [dbo].[T_SYS_DATAWIDGETLIST] (fid,fmainorgid,FFormId,fnumber,fname,fwidgettypeid,fwidgetformid, fispreset,fstatus ,fforbidstatus ,ftranid ,fminheight,fdefwidth ,fparenttranid ,ftoptranid ,fname_py ,fname_py2) 
VALUES (N'812002637695291392','0', N'sys_datawidgetlist', N'No.0000000008', N'部门内排名', N'436841160921714688', N'ydj_innerpartrank','1','E','0','812002637691097089',370,3,'812002637691097093','812002637691097091','B','BMNPM');

-- 数据组件数据初始化


-- 后台统一更新《仓库》的【默认仓库状态】为可用---正式环境更新脚本
update t_ydj_storehouse set fstockid='311858936800219137' where fstockid='';

--渠道类型辅助资料-----------
delete T_BD_ENUMDATAENTRY where fid='d32850c2ec044ac89bacef73e1643ffd'

INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], 
[fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], 
[ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'channel_type_03', 0, N'd32850c2ec044ac89bacef73e1643ffd', N'商超（沃尔玛、百安居、嘉荣等）', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')

INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], 
[fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], 
[ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'channel_type_04', 0, N'd32850c2ec044ac89bacef73e1643ffd', N'电器（国美、苏宁、京东等）', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')

INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], 
[fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], 
[ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'channel_type_05', 0, N'd32850c2ec044ac89bacef73e1643ffd', N'家装', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')

INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], 
[fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], 
[ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'channel_type_06', 0, N'd32850c2ec044ac89bacef73e1643ffd', N'地产', N' ', 4, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')

INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], 
[fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], 
[ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'channel_type_07', 0, N'd32850c2ec044ac89bacef73e1643ffd', N'异业（家博会、定制等）', N' ', 5, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
--渠道类型辅助资料-----------


--辅助资料 维修类型
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2])
select [fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2] from
    (
    select '' as [fid], '' as [FFormId], '' as [fnumber], '' as [fname], '' as [fdescription], '' as [fcreatorid], '' as [fcreatedate], '' as [fmodifierid], '' as [fmodifydate], '' as [fispreset], '' as [fstatus], '' as [fapproveid], '' as [fapprovedate], '' as [fforbidstatus], '' as [fforbidid], '' as [fforbiddate], '' as [fchangestatus], '' as [fnextprocnode], '' as [fmainorgid], '' as [fmainorgid_txt], '' as [fmainorgid_pid], '' as [fprimitiveid], '' as [fbizruleid], '' as [fflowinstanceid], '' as [ftranid], '' as [ffromtranid], '' as [froottranid], '' as [fprintcount], '' as [fprintid], '' as [fprintdate], '' as [fsendstatus], '' as [fdataorigin], '' as [fpublishcid], '' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fsenddate], '' as [fdownloaddate], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fmodule], '' as [fmoduleorder], '' as [fvisible], '' as [fparenttranid], '' as [ftoptranid], '' as [fname_py], '' as [fname_py2]
    union select N'2ceca2f48b2040d4af82b08e16cd10ff', N'bd_enumdata', N'维修类型', N'维修类型', N' ', N' ', NULL, N' ', NULL, N'1', N'B', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'基础管理', 1, N'1', N' ', N' ', N' ', N' '
    ) as t where t.fid != '' and not exists (select 1 from [T_BD_ENUMDATA] b where b.fid = t.fid)


--辅助资料 维修类型 明细
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid])
select [fentryid], [FSeq], [fid],[fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid] from
    (
    select '' as [fentryid],'' as [FSeq],'' as [fid],'' as [fenumid],'' as [fenumitem], '' as [fgroup], '' as [forder], '' as [fdisabled], '' as [fispreset], '' as [fenmainorgid], '' as [fpublishcid],'' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fsendstatus], '' as [fdataorigin], '' as [fsenddate], '' as [fdownloaddate], '' as [fmodifydate], '' as [ftranid], '' as [fparenttranid], '' as [ftoptranid]
    union select N'repairtype01', 0, '2ceca2f48b2040d4af82b08e16cd10ff',N'上门维修', N'上门维修', N' ', 1, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'repairtype02', 0, '2ceca2f48b2040d4af82b08e16cd10ff',N'返厂维修', N'返厂维修', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    ) as t where t.[fentryid] != '' and not exists(select 1 from [T_BD_ENUMDATAENTRY] b where b.[fentryid] = t.[fentryid] and b.[fid]='2ceca2f48b2040d4af82b08e16cd10ff')


--辅助资料 维修状态
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2])
select [fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2] from
    (
    select '' as [fid], '' as [FFormId], '' as [fnumber], '' as [fname], '' as [fdescription], '' as [fcreatorid], '' as [fcreatedate], '' as [fmodifierid], '' as [fmodifydate], '' as [fispreset], '' as [fstatus], '' as [fapproveid], '' as [fapprovedate], '' as [fforbidstatus], '' as [fforbidid], '' as [fforbiddate], '' as [fchangestatus], '' as [fnextprocnode], '' as [fmainorgid], '' as [fmainorgid_txt], '' as [fmainorgid_pid], '' as [fprimitiveid], '' as [fbizruleid], '' as [fflowinstanceid], '' as [ftranid], '' as [ffromtranid], '' as [froottranid], '' as [fprintcount], '' as [fprintid], '' as [fprintdate], '' as [fsendstatus], '' as [fdataorigin], '' as [fpublishcid], '' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fsenddate], '' as [fdownloaddate], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fmodule], '' as [fmoduleorder], '' as [fvisible], '' as [fparenttranid], '' as [ftoptranid], '' as [fname_py], '' as [fname_py2]
    union select N'b573df0a487e1968b047d47e71818bcc', N'bd_enumdata', N'维修状态', N'维修状态', N' ', N' ', NULL, N' ', NULL, N'1', N'B', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'基础管理', 1, N'1', N' ', N' ', N' ', N' '
    ) as t where t.fid != '' and not exists (select 1 from [T_BD_ENUMDATA] b where b.fid = t.fid)


--辅助资料 维修状态 明细
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid])
select [fentryid], [FSeq], [fid],[fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid] from
    (
    select '' as [fentryid],'' as [FSeq],'' as [fid],'' as [fenumid],'' as [fenumitem], '' as [fgroup], '' as [forder], '' as [fdisabled], '' as [fispreset], '' as [fenmainorgid], '' as [fpublishcid],'' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fsendstatus], '' as [fdataorigin], '' as [fsenddate], '' as [fdownloaddate], '' as [fmodifydate], '' as [ftranid], '' as [fparenttranid], '' as [ftoptranid]
    union select N'repairstatus01', 0, 'b573df0a487e1968b047d47e71818bcc',N'未维修', N'未维修', N' ', 1, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'repairstatus02', 0, 'b573df0a487e1968b047d47e71818bcc',N'已收货', N'已收货', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'repairstatus03', 0, 'b573df0a487e1968b047d47e71818bcc',N'维修中', N'维修中', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'repairstatus04', 0, 'b573df0a487e1968b047d47e71818bcc',N'维修完成', N'维修完成', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'repairstatus05', 0, 'b573df0a487e1968b047d47e71818bcc',N'已发货', N'已发货', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    ) as t where t.[fentryid] != '' and not exists(select 1 from [T_BD_ENUMDATAENTRY] b where b.[fentryid] = t.[fentryid] and b.[fid]='b573df0a487e1968b047d47e71818bcc')

--采购订单 添加单据类型:大客户采购订单
delete from t_bd_billtype where fid in('ydj_purchaseorder_type') ;
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('ydj_purchaseorder_type','bd_billtype','DKHCGDD_SYS_01','大客户采购订单','0','2022-05-17 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_purchaseorder','','0','');


update t set fauxseriesid_txt=x.fname
    from t_bd_material t 
inner join t_ydj_series x on x.fid =t.fauxseriesid
where t.fauxseriesid_txt='' or t.fauxseriesid_txt=' '

--采购入库单设置只允许关联生成
update t_bd_billtype set fcreatebypush = 1,fcreatebypushmode=1 where fbizobject='stk_postockin'
--销售出库单设置只允许关联生成
update t_bd_billtype set fcreatebypush = 1,fcreatebypushmode=1 where fbizobject='stk_sostockout'

-- 新增总部的经销商自建商品的默认编码规则
delete from t_bas_nosetting where fnumber='SELFPRODUCT'
    insert into t_bas_nosetting(fid,FFormId,fnumber,fname,fnote,fmainorgid,fispreset,fisdefault, fworkobject, fbillreset, fconstart, fsetvalue, fclength, fdstart, fdatasource, fdlength, fdataformat, fserlength, faddorgno,fforbidstatus)
select fid,'bas_billnosetting','SELFPRODUCT', '经销商自建商品', '总部设置的默认规则', fid, '0', '0', 'ydj_product', '0', '1', 'Z1', '2', '0', '', '', '', '10', '0','0' from T_BAS_ORGANIZATION
where forgtype=1


--来源渠道添加总部下发类型----
delete T_BD_ENUMDATAENTRY where fentryid = '164743697441886213'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164743697441886213','a72f040851454cdd8dc2bdef3ea39808',14,'0','1','','总部下发');
--来源渠道添加总部下发类型----

--删除之前客户来源新增的并且向来源渠道预置两项
delete from t_bd_enumdataentry where fentryid='cussource_09' and fid='bcf7483b775446c18f7b1c68a389c034';
delete from t_bd_enumdataentry where fentryid='cussource_10' and fid='bcf7483b775446c18f7b1c68a389c034';
delete T_BD_ENUMDATAENTRY where fentryid = '164743697441886214'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164743697441886214','a72f040851454cdd8dc2bdef3ea39808',15,'0','1','','金管家增值服务');
delete T_BD_ENUMDATAENTRY where fentryid = '164743697441886215'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('164743697441886215','a72f040851454cdd8dc2bdef3ea39808',16,'0','1','','金管家售后服务');

--装修进度添加
delete T_BD_ENUMDATAENTRY where fentryid in('renovation_09','renovation_10','renovation_11')
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('renovation_09','f00772dcfb674965aeb9f6a42c772c7f',9,'0','1','未装修');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('renovation_10','f00772dcfb674965aeb9f6a42c772c7f',10,'0','1','装修中');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('renovation_11','f00772dcfb674965aeb9f6a42c772c7f',11,'0','1','已装修');
--装修进度添加


--需求类型------
DELETE from t_bd_enumdata where fid='860106413254639617';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid)
VALUES('860106413254639617','','需求类型','1','B','0','系统预置',1,'1','0');

delete T_BD_ENUMDATAENTRY where fid ='860106413254639617'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('requirement_01','860106413254639617',1,'0','1','需求类型1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('requirement_02','860106413254639617',2,'0','1','需求类型2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('requirement_03','860106413254639617',3,'0','1','需求类型3');
--需求类型------


--产品类型------
DELETE from t_bd_enumdata where fid='860106413254639628';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid)
VALUES('860106413254639628','','产品类型','1','B','0','系统预置',1,'1','0');

delete T_BD_ENUMDATAENTRY where fid ='860106413254639628'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('product_01','860106413254639628',1,'0','1','产品类型1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('product_02','860106413254639628',2,'0','1','产品类型2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('product_03','860106413254639628',3,'0','1','产品类型3');
--产品类型------



--预算范围------
DELETE from t_bd_enumdata where fid='86010641325463962548';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid)
VALUES('86010641325463962548','','预算范围','1','B','0','系统预置',1,'1','0');

delete T_BD_ENUMDATAENTRY where fid ='86010641325463962548'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('budget_01','86010641325463962548',1,'0','1','预算范围1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('budget_02','86010641325463962548',2,'0','1','预算范围2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('budget_03','86010641325463962548',3,'0','1','预算范围3');
--预算范围------


--防撞组别------
DELETE from t_bd_enumdata where fid='86010641325463962637';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid)
VALUES('86010641325463962637','','防撞组别','1','B','0','系统预置',1,'1','0');

delete T_BD_ENUMDATAENTRY where fid ='86010641325463962637'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('collision_01','86010641325463962637',1,'0','1','防撞组别1');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('collision_02','86010641325463962637',2,'0','1','防撞组别2');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('collision_03','86010641325463962637',3,'0','1','防撞组别3');
--防撞组别------


--部门简称历史数据修复，使用关联门店的简称赋值给部门简称
UPDATE T_BD_DEPARTMENT SET fshortname = bs.fshortname
    FROM T_BD_DEPARTMENT AS de
JOIN T_BAS_STORE AS bs ON bs.fnumber = de.fstoreid  AND de.fmainorgid=bs.fagentid
WHERE bs.fstatus = 'E'


-- 慕思收支记录历史数据修复(任务Id 33008)，慕思只有销售合同付款和客户充值 
-- 用途=订单付款,扣款   
UPDATE T_COO_INCOMEDISBURSE SET fdeptid = yo.fdeptid
    FROM T_COO_INCOMEDISBURSE AS idb
LEFT JOIN T_YDJ_ORDER AS yo ON yo.fbillno = idb.fsourcenumber AND yo.fmainorgid = idb.fmainorgid
WHERE (fpurpose = 'bizpurpose_02' OR  fpurpose = 'bizpurpose_06') AND fsourceformid = 'ydj_order'

-- 用途=账户充值,其它扣款
UPDATE T_COO_INCOMEDISBURSE SET fdeptid = ISNULL(bs.fdeptid,'') ,fstaffid = ISNULL(bs.fid,'')
    FROM T_COO_INCOMEDISBURSE AS idb
LEFT JOIN T_BD_STAFF AS bs ON idb.fcreatorid = bs.flinkuserid AND bs.fmainorgid = idb.fmainorgid
WHERE (fpurpose = 'bizpurpose_01' OR  fpurpose = 'bizpurpose_03') AND fsourceformid = 'ydj_customer'

    GO


--扫描任务状态------
DELETE from t_bd_enumdata where fid='86010641325463967812';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid)
VALUES('86010641325463967812','','扫描任务状态','1','B','0','系统预置',1,'1','0');

delete T_BD_ENUMDATAENTRY where fid ='86010641325463967812'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('ftaskstatus_01','86010641325463967812',1,'0','1','待作业');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('ftaskstatus_02','86010641325463967812',2,'0','1','已下载');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('ftaskstatus_03','86010641325463967812',3,'0','1','作业中');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('ftaskstatus_04','86010641325463967812',3,'0','1','已完成');

--扫描任务状态------

--商机关闭原因------
DELETE from t_bd_enumdata where fid='87238541325463962548';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid)
VALUES('87238541325463962548','','商机关闭原因','1','B','0','系统预置',1,'1','0');

delete T_BD_ENUMDATAENTRY where fid ='87238541325463962548'
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('businessclose_01','87238541325463962548',1,'0','1','无法联系客户');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('businessclose_02','87238541325463962548',2,'0','1','客户无意向');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('businessclose_03','87238541325463962548',3,'0','1','客户已购买');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('businessclose_04','87238541325463962548',4,'0','1','已转其他人');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenumitem) values('businessclose_05','87238541325463962548',5,'0','1','其他');
--商机关闭原因------


--关联流程初始化数据脚本
INSERT [dbo].[T_YDJ_LINKPROGRESS] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fbizobject], [flinknode], [flinknode_txt], [flinkweight], [flinkweight_txt], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fdatasourceterminal])
SELECT [fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fbizobject], [flinknode], [flinknode_txt], [flinkweight], [flinkweight_txt], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fdatasourceterminal]
FROM
    (
    SELECT '' AS [fid], '' AS [FFormId], '' AS [fnumber], '' AS [fname], '' AS [fdescription], '' AS [fcreatorid], NULL AS [fcreatedate], '' AS [fmodifierid], NULL AS [fmodifydate], '' AS [fispreset], '' AS [fstatus], '' AS [fapproveid], NULL AS [fapprovedate], '' AS [fforbidstatus], '' AS [fforbidid], null AS [fforbiddate], '' AS [fchangestatus], '' AS [fnextprocnode], '' AS [fmainorgid], '' AS [fmainorgid_txt], '' AS [fmainorgid_pid], '' AS [fprimitiveid], '' AS [fbizruleid], '' AS [fflowinstanceid], '' AS [ftranid], '' AS [ffromtranid], '' AS [froottranid], '' AS [fprintcount], '' AS [fprintid], null AS [fprintdate], '' AS [fsendstatus], '' AS [fdataorigin], '' AS [fpublishcid], '' AS [fpublishcid_txt], '' AS [fpublishcid_pid], NULL AS [fsenddate], NULL AS [fdownloaddate], '' AS [fchaindataid], '' AS [ffromchaindataid], '' AS [fbizobject], '' AS [flinknode], '' AS [flinknode_txt], '' AS [flinkweight],'' AS  [flinkweight_txt], '' AS [fparenttranid], '' AS [ftoptranid], '' AS [fname_py], '' AS [fname_py2], '' AS [fdatasourceterminal]
    UNION SELECT N'488370648930979846', N'ydj_linkprogress', N'sys.order.001', N'销售合同关联流程', N'系统预置数据', N'', CAST(N'2019-09-09T15:30:36.180' AS DateTime), N'822165420588011656', CAST(N'2022-07-08T16:09:27.357' AS DateTime), N'1', N'E', N'0', NULL, N'0', N' ', NULL, N'0', N' ', N'0', N' ', N' ', N' ', N' ', N' ', N'488370648930979841', N' ', N' ', 0, N' ', NULL, N'未发布', N'本地', N'0', N'', N'', NULL, NULL, N' ', N' ', N'ydj_order', N'flinkpro', N'流程状态', N'flinkweight', N'流程权重', N'488370648930979845', N'488370648930979843', N'X', N'XSHTGLLC', N' '
    ) AS t WHERE t.fid != '' AND not exists (SELECT 1 FROM [dbo].[T_YDJ_LINKPROGRESS] l WHERE t.fid = l.fid)


--关联流程明细初始化数据脚本
INSERT [dbo].[T_YDJ_LINKPROGRESSENTRY] ([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid])
SELECT [fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid] FROM
    (
    SELECT '' AS [fentryid], 0 AS [FSeq], '' AS [fnodename], '' AS [fbranchname], 0 AS [fbranchweight], 0 AS [fnodeweight], 0 AS [fsumweight], '' AS [flinkbizobject], '' AS [flinksourceformid], '' AS [flinksourceformid_txt], '' AS [flinkbillno], '' AS [flinkbillno_txt], '' AS [flinkentryid], '' AS [flinkentryid_txt], '' AS [ftranid], '' AS [fparenttranid], '' AS [ftoptranid], '' AS [fdataorigin], '' AS [fid]
    UNION SELECT N'488379274156445702', 1, N'已申购', N'入库', 10, 10, 100010, N'pur_reqorder', N'fsourceformid', N'源单类型', N'fsourcebillno', N'源单编号', N'fsourceentryid', N'源单明细内码', N'488379274156445697', N'488379274156445701', N'488379274156445699', N' ', N'488370648930979846'
    UNION SELECT N'488390928617312286', 2, N'已下采购', N'入库', 10, 20, 100020, N'ydj_purchaseorder', N'fsourceformid_e', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488390928617312257', N'488390928617312277', N'488390928617312267', N' ', N'488370648930979846'
    UNION SELECT N'488390928617312287', 3, N'已通知入库', N'入库', 10, 30, 100030, N'pur_receiptnotice', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488390928617312259', N'488390928617312279', N'488390928617312269', N' ', N'488370648930979846'
    UNION SELECT N'488390928617312288', 4, N'已入库', N'入库', 10, 40, 100040, N'stk_postockin', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488390928617312261', N'488390928617312281', N'488390928617312271', N' ', N'488370648930979846'
    UNION SELECT N'488390928617312289', 5, N'已发货', N'出库', 30, 10, 300010, N'sal_deliverynotice', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售订单编号', N'fsoorderentryid', N'销售订单分录内码', N'488390928617312263', N'488390928617312283', N'488390928617312273', N' ', N'488370648930979846'
    UNION SELECT N'488390928617312289', 5, N'已发货', N'出库', 30, 10, 300010, N'sal_deliverynotice', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售订单编号', N'fsoorderentryid', N'销售订单分录内码', N'488390928617312263', N'488390928617312283', N'488390928617312273', N' ', N'488370648930979846'
    UNION SELECT N'488672************', 7, N'已排单', N'排单', 20, 10, 200010, N'stk_scheduleplanbill', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488672835007549441', N'488672835007549445', N'488672835007549443', N' ', N'488370648930979846'
    ) AS t WHERE t.[fentryid] != '' AND NOT EXISTS(SELECT 1 FROM [T_YDJ_LINKPROGRESSENTRY] le WHERE le.[fentryid] = t.[fentryid])

--添加 财务关账/反关账 菜单
INSERT [dbo].[T_SYS_MENUITEM] ([fmenuid], [FFormId], [fname], [ficon], [furl], [fgroupid], [fhelpcode], [forder], [fdomaintype], [fbillformid], [fbillcaption], [fenablerac], [fname_py], [fname_py2], [fparameter], [fpermititemid], [fhidden], [fisadminfunc])
SELECT [fmenuid], [FFormId], [fname], [ficon], [furl], [fgroupid], [fhelpcode], [forder], [fdomaintype], [fbillformid], [fbillcaption], [fenablerac], [fname_py], [fname_py2], [fparameter], [fpermititemid], [fhidden], [fisadminfunc] FROM (
    SELECT '' AS [fmenuid], '' AS [FFormId], '' AS [fname], '' AS [ficon], '' AS [furl], '' AS [fgroupid], '' AS [fhelpcode], '' AS [forder], '' AS [fdomaintype], '' AS [fbillformid], '' AS [fbillcaption], '' AS [fenablerac], '' AS [fname_py], '' AS [fname_py2], '' AS [fparameter], '' AS [fpermititemid], '' AS [fhidden], '' AS [fisadminfunc]
    UNION SELECT N'869175331248144384', N'sys_menuitem', N'财务关账/反关账', N' ', N' ', N'869174944503955456', N'gzafgz', 3, N'dynamic', N'ydj_closedaccounts', N'财务关账/反关账', N'1', N'C', N'CWGZ/FGZ', N' ', N'fw_view', N'0', N'0'
    ) AS t WHERE t.[fmenuid] != '' AND not EXISTS (SELECT 1 FROM dbo.T_SYS_MENUITEM m WHERE m.[fmenuid] = t.[fmenuid])

--辅助资料 关账校验规则 
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2])
select [fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2] from
    (
    select '' as [fid], '' as [FFormId], '' as [fnumber], '' as [fname], '' as [fdescription], '' as [fcreatorid], '' as [fcreatedate], '' as [fmodifierid], '' as [fmodifydate], '' as [fispreset], '' as [fstatus], '' as [fapproveid], '' as [fapprovedate], '' as [fforbidstatus], '' as [fforbidid], '' as [fforbiddate], '' as [fchangestatus], '' as [fnextprocnode], '' as [fmainorgid], '' as [fmainorgid_txt], '' as [fmainorgid_pid], '' as [fprimitiveid], '' as [fbizruleid], '' as [fflowinstanceid], '' as [ftranid], '' as [ffromtranid], '' as [froottranid], '' as [fprintcount], '' as [fprintid], '' as [fprintdate], '' as [fsendstatus], '' as [fdataorigin], '' as [fpublishcid], '' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fsenddate], '' as [fdownloaddate], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fmodule], '' as [fmoduleorder], '' as [fvisible], '' as [fparenttranid], '' as [ftoptranid], '' as [fname_py], '' as [fname_py2]
    union select N'e459687544ff474aad244e07bead2448', N'bd_enumdata', N'closingcheckrules', N'关账校验规则', N' ', N' ', NULL, N' ', NULL, N'1', N'B', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'财务管理', 1, N'1', N' ', N' ', N' ', N' '
    ) as t where t.fid != '' and not exists (select 1 from [T_BD_ENUMDATA] b where b.fid = t.fid)


--辅助资料 关账校验规则 明细  fentryid 的组成规则是 枚举标识|具体表单FormId|同表单不同业务场景的区分标识
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid])
select [fentryid], [FSeq], [fid],[fenumid],[fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid] from
    (
    select '' as [fentryid],'' as [FSeq],'' as [fid],'' as [fenumid],'' as [fenumitem], '' as [fgroup], '' as [forder], '' as [fdisabled], '' as [fispreset], '' as [fenmainorgid], '' as [fpublishcid],'' as [fpublishcid_txt], '' as [fpublishcid_pid], '' as [fchaindataid], '' as [ffromchaindataid], '' as [fsendstatus], '' as [fdataorigin], '' as [fsenddate], '' as [fdownloaddate], '' as [fmodifydate], '' as [ftranid], '' as [fparenttranid], '' as [ftoptranid]
    union select N'hasnotauditcollection|coo_incomedisburse|sk', 0, 'e459687544ff474aad244e07bead2448',N'hasnotauditcollection', N'当前组织存在未审核的收款单', N' ', 10, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'hasnotauditReceiverefund|coo_incomedisburse|sktk', 0, 'e459687544ff474aad244e07bead2448',N'hasnotauditReceiverefund', N'当前组织存在未审核的收款退款单', N' ', 20, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'hasnotauditpayment|coo_incomedisburse|fk', 0, 'e459687544ff474aad244e07bead2448',N'hasnotauditpayment', N'当前组织存在未审核的付款单', N' ', 30, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'hasnotauditpaymentRefund|coo_incomedisburse|fktk', 0, 'e459687544ff474aad244e07bead2448',N'hasnotauditpaymentRefund', N'当前组织存在未审核的付款退款', N' ', 40, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'hasnotauditFeespayable|ste_registfee', 0, 'e459687544ff474aad244e07bead2448',N'hasnotauditFeespayable', N'当前组织存在未审核的费用应付单', N' ', 50, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'hasnotauditpayreceipt|ydj_collectreceipt', 0, 'e459687544ff474aad244e07bead2448',N'hasnotauditpayreceipt', N'当前组织存在未审核的其他应收单', N' ', 60, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    union select N'hasnotauditcollectreceipt|ydj_payreceipt', 0, 'e459687544ff474aad244e07bead2448',N'hasnotauditcollectreceipt', N'当前组织存在未审核的其他应付单', N' ', 70, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' '
    ) as t where t.[fentryid] != '' and not exists(select 1 from [T_BD_ENUMDATAENTRY] b where b.[fentryid] = t.[fentryid] and b.[fid]='e459687544ff474aad244e07bead2448')

--创建财务--关账记录表
    IF OBJECT_ID(N'T_YDJ_CLOSEDACCOUNTSLIST', N'U') IS NULL
BEGIN

CREATE TABLE [dbo].[T_YDJ_CLOSEDACCOUNTSLIST]
(
    [fid] [NVARCHAR](100) DEFAULT (' ') NOT NULL,
    [FFormId] [NVARCHAR](100) DEFAULT (' ') NOT NULL,
    [fagentid] [NVARCHAR](100) DEFAULT (' ') NOT NULL,
    [flastclosuredate] [DATETIME] NULL,
    [fbusinessoperations] [NVARCHAR](100) DEFAULT (' ') NOT NULL,
    [foptime] [DATETIME] NULL,
    [fopuserid] [NVARCHAR](100) DEFAULT (' ') NOT NULL,
    [fmainorgid] [NVARCHAR](100) DEFAULT (' ') NOT NULL,
    CONSTRAINT [PK_YDJ_CLOSEDACCOUNTSLIST]
    PRIMARY KEY CLUSTERED ([fid] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON,
        ALLOW_PAGE_LOCKS = ON
        ) ON [PRIMARY]
    ) ON [PRIMARY];
END;

--定价对象辅助资料
delete from t_bd_enumdata where fid='be74c23539624203acad64eda5ac71c9';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('be74c23539624203acad64eda5ac71c9','bd_enumdata','定价对象','1','B','0','系统预置',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('priceobj_01','priceobj_02','priceobj_03');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('priceobj_01','be74c23539624203acad64eda5ac71c9',1,'0','1','','商品','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('priceobj_02','be74c23539624203acad64eda5ac71c9',2,'0','1','','系列','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('priceobj_03','be74c23539624203acad64eda5ac71c9',3,'0','1','','商品类别','0');

--报价类型辅助资料增加【经销报价】
delete from t_bd_enumdataentry where fentryid='quote_type_03';
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('quote_type_03','99222505751543559a216725d96d6094',3,'0','1','','经销报价','0');

--报价类型辅助资料增加【二级分销报价】
delete from t_bd_enumdataentry where fentryid='quote_type_04';
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem,fenmainorgid) values('quote_type_04','99222505751543559a216725d96d6094',4,'0','1','','二级分销报价','0');

--销售合同增【二级分销合同】单据类型
delete from t_bd_billtype where fid in('order_billtype_03')
--insert into t_bd_billtype(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
--values('order_billtype_03','bd_billtype','BTXSHT_SYS_03','二级分销合同','0','2022-10-26 00:00:00','1','E','0','0','','',N'未发布','','',N'系统预置','ydj_order','','0','');

--服务状态
delete from t_bd_enumdata where fid='4ad9d1898c0442f5b2829466b2da1161';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('4ad9d1898c0442f5b2829466b2da1161','bd_enumdata','服务状态','1','B','0','系统预置',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('sersta01','sersta02','sersta03','sersta04','sersta05','sersta06','sersta07','sersta08','sersta09','sersta10','sersta11','sersta12','sersta13','sersta14');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta01','4ad9d1898c0442f5b2829466b2da1161',1,'0','1','','待派单');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta02','4ad9d1898c0442f5b2829466b2da1161',2,'0','1','','待预约');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta03','4ad9d1898c0442f5b2829466b2da1161',3,'0','1','','待完工');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta04','4ad9d1898c0442f5b2829466b2da1161',4,'0','1','','待评价');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta05','4ad9d1898c0442f5b2829466b2da1161',5,'0','1','','已关闭');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('sersta06','4ad9d1898c0442f5b2829466b2da1161',6,'0','1','','已取消');

delete from T_BD_ENUMDATAENTRY where fentryid in('customerscore_06','customerscore_07','customerscore_08','customerscore_09','customerscore_10');

--售后状态
delete from t_bd_enumdata where fid='901e070681fd48739e89929fc1f74656';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('901e070681fd48739e89929fc1f74656','bd_enumdata','售后状态','1','B','0','客服管理',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('aft_service_01','aft_service_02','aft_service_03','aft_service_04','aft_service_05','aft_service_06');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_01','901e070681fd48739e89929fc1f74656',1,'0','1','','待处理');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_02','901e070681fd48739e89929fc1f74656',2,'0','1','','处理中');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_03','901e070681fd48739e89929fc1f74656',3,'0','1','','总部待审核');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_04','901e070681fd48739e89929fc1f74656',4,'0','1','','总部已审核');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_05','901e070681fd48739e89929fc1f74656',5,'0','1','','已完成');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_service_06','901e070681fd48739e89929fc1f74656',6,'0','1','','已关闭');

update t_bd_enumdataentry set fenumitem='送货问题' where fentryid='afterquestion_type_03';

--处理结论
delete from t_bd_enumdata where fid='7cad95d5201140a5b2eb178248e90d01';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('7cad95d5201140a5b2eb178248e90d01','bd_enumdata','处理结论','1','B','0','系统预置',4,'1','0');
delete from t_bd_enumdataentry where fentryid in('res_type_01','res_type_02','res_type_03','res_type_04','res_type_05','res_type_06','res_type_07');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_01','7cad95d5201140a5b2eb178248e90d01',1,'0','1','','维修');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_02','7cad95d5201140a5b2eb178248e90d01',2,'0','1','','退换');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_03','7cad95d5201140a5b2eb178248e90d01',3,'0','1','','退货');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_04','7cad95d5201140a5b2eb178248e90d01',4,'0','1','','返厂');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_05','7cad95d5201140a5b2eb178248e90d01',5,'0','1','','补件');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_07','7cad95d5201140a5b2eb178248e90d01',7,'0','1','','赔偿');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('res_type_06','7cad95d5201140a5b2eb178248e90d01',6,'0','1','','解释');

--将预置的采购入库单的单据类型【标准采购入库】的【采购单价】的锁定性调整为锁定
INSERT [dbo].[T_BD_BILLTYPEFIELDENTRY] ([FEntryId], [FSeq], [fgroupname], [ffieldid], [ffieldid_txt], [fmustinput], [flock], [flock_txt], [fautomemory], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'923588612359721063', 1, N'入库明细', N'fpoprice', N'采购单价', N'0', N'-1', N'全部锁定', N'0', N'816440023116288001', N'816440023116288005', N'816440023116288003', N' ', N'poinstock_billtype_01');
--售后类型
delete from t_bd_enumdata where fid='C171369ACCAE422BB353BA329D6B1B0E';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('C171369ACCAE422BB353BA329D6B1B0E','bd_enumdata','售后类型','1','B','0','客服管理',1,'1','0');
delete from t_bd_enumdataentry where fentryid in('aft_servicetype_01','aft_servicetype_02');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_servicetype_01','C171369ACCAE422BB353BA329D6B1B0E',1,'0','1','','维修');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('aft_servicetype_02','C171369ACCAE422BB353BA329D6B1B0E',2,'0','1','','退货');


--添加 期末处理  菜单分组
INSERT INTO [dbo].[T_SYS_MENUGROUP] ([fgroupid] ,[FFormId] ,[fname] ,[ficon] ,[fmoduleid] ,[forder] ,[furl] ,[fdomaintype] ,[fbillformid] ,[fname_py] ,[fname_py2] ,[fismenu] ,[fhelpcode] ,[fbillcaption] ,[fenablerac] ,[fparameter] ,[fpermititemid] ,[fhidden],[fisadminfunc])
SELECT [fgroupid] ,[FFormId] ,[fname] ,[ficon] ,[fmoduleid] ,[forder] ,[furl] ,[fdomaintype] ,[fbillformid] ,[fname_py] ,[fname_py2] ,[fismenu] ,[fhelpcode] ,[fbillcaption] ,[fenablerac] ,[fparameter] ,[fpermititemid] ,[fhidden],[fisadminfunc] FROM
    (
    SELECT ''AS [fgroupid] ,''AS [FFormId] ,''AS [fname] ,''AS [ficon] ,''AS [fmoduleid] ,''AS [forder] ,''AS [furl] ,''AS [fdomaintype] ,''AS [fbillformid] ,''AS [fname_py] ,''AS [fname_py2] ,''AS [fismenu] ,''AS [fhelpcode] ,''AS [fbillcaption] ,''AS [fenablerac] ,''AS [fparameter] ,''AS [fpermititemid] ,''AS [fhidden],''AS [fisadminfunc]
    UNION SELECT N'869174944503955456',	N'sys_menugroup',	N'期末处理'	 ,N''	,N'402237213653471232',	200	,N'',N'',N'',N'Q',N'QMCL',0,N'',N'',0,N'',N'', 	0	,0
    )AS t  WHERE t.[fgroupid] != '' AND not EXISTS (SELECT 1 FROM dbo.[T_SYS_MENUGROUP] m WHERE m.[fgroupid] = t.[fgroupid])

update T_BD_ENUMDATAENTRY set fenumitem='回访' where fentryid='objecttype29';
update T_BD_ENUMDATAENTRY set fenumitem='转单' where fentryid='objecttype31';
update T_BD_ENUMDATAENTRY set fenumitem='取消' where fentryid='objecttype32';
update T_BD_ENUMDATAENTRY set fenumitem='评价' where fentryid='objecttype33';
delete from t_bas_filterscheme where fbillformid='ydj_service';
delete from T_SEC_ROLEFUNCACL  where fbizobjid='ydj_service';
delete from t_bd_enumdataentry where fentryid='vist_canal_06';
delete from t_bd_enumdata where fid='b058b5acafed4091a0bf2e178330b68f';
insert into t_bd_enumdata (fid,fformid,fname,fispreset,fstatus,fforbidstatus,fmodule,fmoduleorder,fvisible,fmainorgid) values('b058b5acafed4091a0bf2e178330b68f','bd_enumdata','回访方式','1','B','0','客服管理',1,'1','0');
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fgroup,fenumitem) values('vist_canal_06','b058b5acafed4091a0bf2e178330b68f',6,'0','1','','客户主动评价');
delete from t_bd_enumdataentry where fentryid='vist_canal_07';
insert into t_bd_enumdataentry (fentryid,fid,forder,fdisabled,fispreset,fenmainorgid,fgroup,fenumitem) values('vist_canal_07','b058b5acafed4091a0bf2e178330b68f',7,'0','1','0','','400回访');

-- 采购退货单-单据类型-历史采购退货
delete from t_bd_billtype where fid in('postockreturn_billtype_02') OR fname = '历史采购退货';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('postockreturn_billtype_02','bd_billtype','CGTH_SYS_02','历史采购退货','0','2021-10-25 00:00:00','0','E','0','0','','',N'未发布','','',N'系统预置','stk_postockreturn','','1','');

-- 历史数据修复， 《采购入库单.总部合同号》取上游《采购订单.总部合同号》
update t_stk_postockinentry set fhqderno = c.fhqderno from t_stk_postockinentry as a 
left join t_stk_postockin as b on a.fid = b.fid
    left join t_ydj_purchaseorder as c on b.fsourcenumber = c.fbillno and b.fmainorgid = c.fmainorgid
where ISNULL(c.fhqderno,'') <> ''


--即时库存校正计划任务，执行频率为：每天凌晨0:30和中午12:30触发
--delete from t_bas_task where fid='7ab97c0c0c9745edaf099c2999d8e117';
--insert into t_bas_task (fid,fformid,fmainorgid,fnumber,fname,ftasklabel,fpluginid,fpluginid_txt,fcondition,fexecuteplan,fdescription,fsysmessage,fstartstatus,fforbidstatus,fispreset) 
--values('7ab97c0c0c9745edaf099c2999d8e117','bas_task','0','RW_SYS_CorrectTask','即时库存校正任务','即时库存校正任务','CorrectTask','即时库存校正','','0 30 0,12 * * ?','每天凌晨0:30和中午12:30触发','1','ew_start003','0','1');

--服务单逾期自动关闭服务
insert into t_bas_task (fid,fformid,fmainorgid,fnumber,fname,ftasklabel,fpluginid,fpluginid_txt,fcondition,fexecuteplan,fdescription,fsysmessage,fstartstatus,fforbidstatus,fispreset)
values('7ab97c0c0c9745edaf099c2999d8e118','bas_task','0','RW_SYS_ServiceAutoClose','服务单逾期自动关闭','服务单逾期自动关闭','serviceautoclose','服务单逾期自动关闭','','0 30 0 * * ?','每天凌晨0:30触发','1','ew_start003','0','1');

--预置自动审核采购变更单计划任务脚本
delete from t_bas_task where fid='957586975594119174';
insert into t_bas_task (fid,fformid,fmainorgid,fnumber,fname,ftasklabel,fpluginid,fpluginid_txt,fcondition,fexecuteplan,fdescription,fsysmessage,fstartstatus,fforbidstatus,fispreset)
values('957586975594119174','bas_task','0','RW_SYS_PurorderchgAutoAudit','金蝶变更单显示为已提交未自动更新','金蝶变更单显示为已提交未自动更新','purorderchgautoaudit','金蝶变更单显示为已提交未自动更新','','0 0/60 6-23 * * ?','每天6-23点,每一小时执行一次','1','ew_start002','0','1');

-- 盘点单-单据类型-期初盘点单
delete from t_bd_billtype where fid in('inventoryverify_billtype_02') OR fname = '期初盘点单';
insert into t_bd_billtype
(fid,fformid,fnumber,fname,fcreatorid,fcreatedate,fispreset,fstatus,fforbidstatus,fmainorgid,fmainorgid_txt,fmainorgid_pid,fsendstatus,fname_py,fname_py2,fdescription,fbizobject,fcoderule,fisdefault,fparamset)
values('inventoryverify_billtype_02','bd_billtype','QCPDD_SYS_02','期初盘点单','0','2022-10-25 00:00:00','0','E','0','0','','',N'未发布','','',N'系统预置','stk_inventoryverify','','1','');

--隐藏以下菜单
update T_SYS_MENUITEM set fhidden='1' where fmenuid in(
                                                       '261579778027556864',-- 销售收支明细
                                                       '262525755429359616',-- 采购收支明细
                                                       '241164275668881408',-- 销售资金总览
                                                       '241164856785506304',-- 销售资金明细
                                                       '262525078103789568',-- 采购资金总览
                                                       '262526430821355520',-- 采购资金明细
                                                       '240562086944575488'-- 销售意向
    );
--费用应付单和总部费用池查询菜单顺序对调
update T_SYS_MENUITEM set forder='10' where fmenuid='880107286772842496';
update T_SYS_MENUITEM set forder='16' where fmenuid='196694424074457088';

--服务单辅助属性改为按中文存储后字段长度调整
alter table [dbo].[T_YDJ_SERVICEPRODUCT] alter column fattrinfo nvarchar(1000);

--服务单预置快捷过滤方案
INSERT INTO t_bas_filterscheme([fid], [FFormId], [fname], [fuserid], [fbillformid], [ffilterdata], [ffilterstring], [fshare], [fshareuser], [fshareuser_txt], [fispreset], [forder], [FSumExpr], [fmainorgid], [fname_py], [fname_py2]) VALUES (N'919532850285641729', N'bas_filterscheme', N'待预约', N' ', N'ydj_service', N'{"selectedFieldKeys":[],"dynamicParams":[],"id":"919532850285641729","name":"待预约","userId":"905110145272643586","mainOrgId":"822148199987941388","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":true,"isShare":false,"shareUser":"","shareUser_txt":"","order":2,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":"","id":"fserstatus.fenumitem","operator":"=","value":"待预约","replaceId":"","replaceValue":"","isCompareToField":false,"rightBracket":"","logic":"","rowIndex":0,"memo":""}],"filterString":"","customFilter":"","sumExpression":"","linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', N' ', N'0', N' ', N' ', N'1', 2, N' ', N' ', N' ', N' ');
INSERT INTO t_bas_filterscheme([fid], [FFormId], [fname], [fuserid], [fbillformid], [ffilterdata], [ffilterstring], [fshare], [fshareuser], [fshareuser_txt], [fispreset], [forder], [FSumExpr], [fmainorgid], [fname_py], [fname_py2]) VALUES (N'919532943508242433', N'bas_filterscheme', N'待完工', N' ', N'ydj_service', N'{"selectedFieldKeys":[],"dynamicParams":[],"id":"919532943508242433","name":"待完工","userId":"905110145272643586","mainOrgId":"822148199987941388","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":true,"isShare":false,"shareUser":"","shareUser_txt":"","order":3,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":"","id":"fserstatus.fenumitem","operator":"=","value":"待完工","replaceId":"","replaceValue":"","isCompareToField":false,"rightBracket":"","logic":"","rowIndex":0,"memo":""}],"filterString":"","customFilter":"","sumExpression":"","linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', N' ', N'0', N' ', N' ', N'1', 3, N' ', N' ', N' ', N' ');
INSERT INTO t_bas_filterscheme([fid], [FFormId], [fname], [fuserid], [fbillformid], [ffilterdata], [ffilterstring], [fshare], [fshareuser], [fshareuser_txt], [fispreset], [forder], [FSumExpr], [fmainorgid], [fname_py], [fname_py2]) VALUES (N'919533201357275139', N'bas_filterscheme', N'待评价', N' ', N'ydj_service', N'{"selectedFieldKeys":[],"dynamicParams":[],"id":"919533201357275139","name":"待评价","userId":"905110145272643586","mainOrgId":"822148199987941388","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":true,"isShare":false,"shareUser":"","shareUser_txt":"","order":4,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":"","id":"fserstatus.fenumitem","operator":"=","value":"待评价","replaceId":"","replaceValue":"","isCompareToField":false,"rightBracket":"","logic":"","rowIndex":0,"memo":""}],"filterString":"","customFilter":"","sumExpression":"","linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', N' ', N'0', N' ', N' ', N'1', 4, N' ', N' ', N' ', N' ');
INSERT INTO t_bas_filterscheme([fid], [FFormId], [fname], [fuserid], [fbillformid], [ffilterdata], [ffilterstring], [fshare], [fshareuser], [fshareuser_txt], [fispreset], [forder], [FSumExpr], [fmainorgid], [fname_py], [fname_py2]) VALUES (N'919534483736363009', N'bas_filterscheme', N'已关闭', N' ', N'ydj_service', N'{"selectedFieldKeys":[],"dynamicParams":[],"id":"","name":"已关闭","userId":"905110145272643586","mainOrgId":"822148199987941388","billFormId":null,"pageCount":0,"pageIndex":0,"isPreset":true,"isShare":false,"shareUser":"","shareUser_txt":"","order":5,"customerFilter":{},"dataTitle":null,"filterTitle":null,"filterData":[{"leftBracket":"","id":"fserstatus.fenumitem","operator":"=","value":"已关闭","replaceId":"","replaceValue":"","isCompareToField":false,"rightBracket":"","logic":"","rowIndex":0,"memo":""}],"filterString":"","customFilter":"","sumExpression":"","linkMenuItems":[],"colVisible":[],"orderBy":[],"orderByString":""}', N' ', N'0', N' ', N' ', N'1', 5, N' ', N' ', N' ', N' ');

--辅助资料服务类型变更
delete from T_BD_ENUMDATAENTRY where fentryid in('fres_type_04','fres_type_05','fres_type_06');
update T_BD_ENUMDATAENTRY set fenumitem='送装',forder='2' where fentryid='fres_type_01';
update T_BD_ENUMDATAENTRY set fenumitem='增值',forder='1' where fentryid='fres_type_02';
update T_BD_ENUMDATAENTRY set fenumitem='售后' where fentryid='fres_type_03';

--销售合同关联流程预置
INSERT INTO T_YDJ_LINKPROGRESS([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fbizobject], [flinknode], [flinknode_txt], [flinkweight], [flinkweight_txt], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fdatasourceterminal]) VALUES (N'488370648930979846', N'ydj_linkprogress', N'sys.order.001', N'销售合同关联流程', N'系统预置数据', N'', '2019-09-09 15:30:36.180', N'841327239404716038', '2022-08-14 16:19:22.773', N'1', N'E', N'841327239404716038', '2022-08-14 16:19:22.783', N'0', N'859524618373038086', '2022-07-20 16:47:02.510', N'0', N' ', N'0', N' ', N' ', N' ', N' ', N' ', N'488370648930979841', N' ', N' ', 0, N' ', NULL, N'未发布', N'本地', N'0', N'', N'', NULL, NULL, N' ', N' ', N'ydj_order', N'flinkpro', N'流程状态', N'flinkweight', N'流程权重', N'488370648930979845', N'488370648930979843', N'X', N'XSHTGLLC', N' ');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488379274156445702', 1, N'已申购', N'入库', 10, 10, 100010, N'pur_reqorder', N'fsourceformid', N'源单类型', N'fsourcebillno', N'源单编号', N'fsourceentryid', N'源单明细内码', N'488379274156445697', N'488379274156445701', N'488379274156445699', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488390928617312286', 2, N'已下采购', N'入库', 10, 20, 100020, N'ydj_purchaseorder', N'fsourceformid_e', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488390928617312257', N'488390928617312277', N'488390928617312267', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488390928617312287', 3, N'已通知入库', N'入库', 10, 30, 100030, N'pur_receiptnotice', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488390928617312259', N'488390928617312279', N'488390928617312269', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488390928617312288', 4, N'已入库', N'入库', 10, 40, 100040, N'stk_postockin', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488390928617312261', N'488390928617312281', N'488390928617312271', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488390928617312289', 5, N'已发货', N'出库', 30, 10, 300010, N'sal_deliverynotice', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售订单编号', N'fsoorderentryid', N'销售订单分录内码', N'488390928617312263', N'488390928617312283', N'488390928617312273', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488390928617312290', 6, N'已出库', N'出库', 30, 20, 300020, N'stk_sostockout', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售订单编号', N'fsoorderentryid', N'销售订单分录内码', N'488390928617312265', N'488390928617312285', N'488390928617312275', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488390928617312291', 7, N'已退货', N'出库', 30, 40, 300040, N'stk_sostockreturn', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售订单编号', N'fsoorderentryid', N'销售订单分录内码', N'862737162717888513', N'862737162717888517', N'862737162717888515', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488672************', 8, N'已排单', N'排单', 20, 10, 200010, N'stk_scheduleplanbill', N'fsourceformid', N'来源单类型', N'fsoorderno', N'销售合同编号', N'fsoorderentryid', N'销售合同分录内码', N'488672835007549441', N'488672835007549445', N'488672835007549443', N' ', N'488370648930979846');
INSERT INTO T_YDJ_LINKPROGRESSentry([fentryid], [FSeq], [fnodename], [fbranchname], [fbranchweight], [fnodeweight], [fsumweight], [flinkbizobject], [flinksourceformid], [flinksourceformid_txt], [flinkbillno], [flinkbillno_txt], [flinkentryid], [flinkentryid_txt], [ftranid], [fparenttranid], [ftoptranid], [fdataorigin], [fid]) VALUES (N'488672835007549447', 9, N'已安装', N'完工', 40, 20, 400020, N'ydj_service', N'fsourceformid', N'来源单类型', N'fsourcebillno', N'来源单编号', N'fsourceentryid', N'来源单分录内码', N'922445846607560705', N'922445846607560709', N'922445846607560707', N' ', N'488370648930979846');

--添加广东省-中山市-石岐街道
delete T_BD_ENUMDATAENTRY where fentryid = '442000118' ;
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'442000118', 0, N'208b665d355d446f890fcc46568a3784', N'石岐街道', N'4420', 1878, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
;

--添加吉林省-长春市-高新区
delete T_BD_ENUMDATAENTRY where fentryid = '220184' ;
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'220184', 0, N'208b665d355d446f890fcc46568a3784', N'高新区', N'2201', 534, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ');

--添加吉林省-长春市-汽开区
delete T_BD_ENUMDATAENTRY where fentryid = '220185' ;
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'220185', 0, N'208b665d355d446f890fcc46568a3784', N'汽开区', N'2201', 535, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ');

--添加吉林省-长春市-北湖开发区
delete T_BD_ENUMDATAENTRY where fentryid = '220186' ;
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'220186', 0, N'208b665d355d446f890fcc46568a3784', N'北湖开发区', N'2201', 536, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ');

--添加吉林省-长春市-净月开发区
delete T_BD_ENUMDATAENTRY where fentryid = '220187' ;
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'220187', 0, N'208b665d355d446f890fcc46568a3784', N'净月开发区', N'2201', 537, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ');

--添加吉林省-长春市-莲花区
delete T_BD_ENUMDATAENTRY where fentryid = '220188' ;
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'220188', 0, N'208b665d355d446f890fcc46568a3784', N'莲花区', N'2201', 538, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ');

--添加吉林省-长春市-范家屯
delete T_BD_ENUMDATAENTRY where fentryid = '220189' ;
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) 
VALUES (N'220189', 0, N'208b665d355d446f890fcc46568a3784', N'范家屯', N'2201', 539, N'0', N'1', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ');


--添加辅助资料：一级库存维度
DELETE FROM [dbo].[T_BD_ENUMDATA] WHERE [fid]='77b88bae838248dbbe9b6ab077c3f2fe';
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fid]='77b88bae838248dbbe9b6ab077c3f2fe';
INSERT [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount],
[fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], 
[fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible],
[fparenttranid], [ftoptranid], [fname_py], [fname_py2]) VALUES 
(N'77b88bae838248dbbe9b6ab077c3f2fe', N'bd_enumdata', N'一级库存维度', N'一级库存维度', N' ', N' ',
NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N'', N'1', N'0', N' ', N' ', N' ', N' ', N' ', 
0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'经销商管理', 0, N'1', N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'inventorydimension01', 0, N'c3f81541e25944c0861777662f6c919e', N'有库存且一级已授权给二级', N' ', 1, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'inventorydimension02', 0, N'74ffaa25f3d34c239cb185b913673fad', N'有库存且一级已授权给二级', N' ', 2, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
INSERT [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fenumid]) VALUES 
(N'inventorydimension03', 0, N'01a8706fa0d34c31ba3c025ee7b53850', N'有库存但总部未授权给一级', N' ', 3, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N' ')
GO
       
	--费用项目类型-新增脚本
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fentryid] IN (
    'EXP_FWZJ_001', 'EXP_WYGLF_001', 'EXP_DF_001', 'EXP_SF_001',
    'EXP_TXF_001', 'EXP_TXF_002', 'EXP_CXF_001', 'EXP_XSYJ_001',
    'EXP_MTGG_006', 'EXP_MTGG_007', 'EXP_SXF_001', 'EXP_BGF_004',
    'EXP_BGF_001', 'EXP_BXF_001', 'EXP_BXF_002', 'EXP_BXF_004',
    'EXP_ZGJYJF_004', 'EXP_AQXFF_001', 'EXP_YYWZC_002', 'EXP_SBZJ_001',
    'EXP_SBWXBF_001'
    );
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_FWZJ_001', 0, N' ', N'房屋租金', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_WYGLF_001', 0, N' ', N'办公室/厂房物业费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_DF_001', 0, N' ', N'电费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_SF_001', 0, N' ', N'水费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_TXF_001', 0, N' ', N'电话费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_TXF_002', 0, N' ', N'网络费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_CXF_001', 0, N' ', N'主动营销', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_XSYJ_001', 0, N' ', N'线下佣金', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_MTGG_006', 0, N' ', N'户外广告', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_MTGG_007', 0, N' ', N'框架广告', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_SXF_001', 0, N' ', N'手续费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_BGF_004', 0, N' ', N'清洁用品', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_BGF_001', 0, N' ', N'办公用品', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_BXF_001', 0, N' ', N'财产险', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_BXF_002', 0, N' ', N'责任险', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_BXF_004', 0, N' ', N'商品保险', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_ZGJYJF_004', 0, N' ', N'外训费用', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_AQXFF_001', 0, N' ', N'安全消防费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_YYWZC_002', 0, N' ', N'罚款滞纳金', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_SBZJ_001', 0, N' ', N'设备租金', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'EXP_SBWXBF_001', 0, N' ', N'电梯年审/维保费', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1250456442760200192');

--收支记录-新增退款业务类型
IF NOT EXISTS (SELECT 1 FROM [dbo].[T_BD_ENUMDATA] WHERE [fid] = N'1252645945730007040')
BEGIN
INSERT INTO [dbo].[T_BD_ENUMDATA] ([fid], [FFormId], [fnumber], [fname], [fdescription], [fcreatorid], [fcreatedate], [fmodifierid], [fmodifydate], [fispreset], [fstatus], [fapproveid], [fapprovedate], [fforbidstatus], [fforbidid], [fforbiddate], [fchangestatus], [fnextprocnode], [fmainorgid], [fmainorgid_txt], [fmainorgid_pid], [fprimitiveid], [fbizruleid], [fflowinstanceid], [ftranid], [ffromtranid], [froottranid], [fprintcount], [fprintid], [fprintdate], [fsendstatus], [fdataorigin], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fsenddate], [fdownloaddate], [fchaindataid], [ffromchaindataid], [fmodule], [fmoduleorder], [fvisible], [fparenttranid], [ftoptranid], [fname_py], [fname_py2], [fdatasourceterminal], [fmanagemodel]) VALUES (N'1252645945730007040', N' ', N' ', N'退款业务类型', N' ', N' ', NULL, N' ', NULL, N'0', N' ', N' ', NULL, N'0', N' ', NULL, N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', N' ', 0, N' ', NULL, N' ', N' ', N' ', N' ', N' ', NULL, NULL, N' ', N' ', N'销售管理', 1, N'1', N' ', N' ', N' ', N' ', N' ', N' ');
END
GO
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fentryid] IN ('refund_sar', 'refund_cr', 'refund_sr', 'refund_arr');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'refund_sar', 0, N' ', N'销售退款', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1252645945730007040');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'refund_cr', 0, N' ', N'客户充值退款', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1252645945730007040');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'refund_sr', 0, N' ', N'销售退差', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1252645945730007040');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'refund_arr', 0, N' ', N'售后退货退款', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'1252645945730007040');

--款项说明-新增脚本
DELETE FROM [dbo].[T_BD_ENUMDATAENTRY] WHERE [fentryid] IN ('payment_dp', 'payment_pdr');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'payment_dp', 0, N' ', N'定金', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'790610086597890048');
INSERT INTO [dbo].[T_BD_ENUMDATAENTRY] ([fentryid], [FSeq], [fenumid], [fenumitem], [fgroup], [forder], [fdisabled], [fispreset], [fenmainorgid], [fpublishcid], [fpublishcid_txt], [fpublishcid_pid], [fchaindataid], [ffromchaindataid], [fsendstatus], [fdataorigin], [fsenddate], [fdownloaddate], [fmodifydate], [ftranid], [fparenttranid], [ftoptranid], [fid]) VALUES (N'payment_pdr', 0, N' ', N'销售退差', N' ', 0, N'0', N'0', N'0', N' ', N' ', N' ', N' ', N' ', N' ', N' ', NULL, NULL, NULL, N' ', N' ', N' ', N'790610086597890048');
