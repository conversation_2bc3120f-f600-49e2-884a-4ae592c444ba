/**
 * @ sourceURL=/fw/js/ydj/ms/ms_membermanagementandanalysis.js
 */
; (function () {
    var ms_membermanagementandanalysis = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //初始化事件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            var canLogin = that.Model.uiData["fcanlogin"];
            var url = that.Model.uiData["furl"];
            if (canLogin && url != "" && url != null && url != undefined) {
                // 打开
                window.open(url);
            }
            else {
                that.Model.showForm({
                    formId: 'ms_member_login',
                    param: { openStyle: Consts.openStyle.modal },
                    domainType: Consts.domainType.dynamic
                });
            }
            
            setTimeout(function () {
                that.Model.close();
            },
                1000);
        };

        return _child;
    })(BasePlugIn);
    window.ms_membermanagementandanalysis = ms_membermanagementandanalysis;
})();