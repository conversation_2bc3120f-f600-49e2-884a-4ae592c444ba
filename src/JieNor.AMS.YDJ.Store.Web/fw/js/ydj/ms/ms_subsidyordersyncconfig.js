(function () {
    var ms_subsidyordersyncconfig = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.flag = true;
        };
        __extends(_child, _super);
     

        //初始化编辑页面插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            if (that.Model.viewModel.domainType == Consts.domainType.bill && !that.Model.pkid) {
                that.Initfentry();
            }
           
        };
        _child.prototype.Initfentry = function () {
            var that = this;
            yiAjax.p('/bill/ms_subsidyordersyncconfig?operationno=getdefaultform', null, function (r) {
                var res = r.operationResult;
                if (res.srvData) {
                    var rowDatas = that.Model.getValue({ id: 'fbizobjecttry' });
                    var newRowDatas = [];
                    if (res.srvData && res.srvData.length > 0) {
                        for (var i = 0; i < res.srvData.length; i++) {
                            newRowDatas.push({ fbizobject: res.srvData[i], fissync: false, fsyncdate: null });
                        }
                        that.Model.setValue({ id: 'fbizobjecttry', value: newRowDatas });
                        that.Model.refreshEntry({ id: 'fbizobjecttry' });
                    }
                   
                }
               
            }, null, null, null, { async: false });
        }


        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fagentid':
                    e.result.filterString = "  fid<>'" + that.Model.getValue({ id: "id" }) + "' or (fisreseller='1' and fforbidstatus='0')";
                    break;
            }
        };
        return _child;
    })(BasePlugIn);
    window.ms_subsidyordersyncconfig = window.ms_subsidyordersyncconfig || ms_subsidyordersyncconfig;
})();