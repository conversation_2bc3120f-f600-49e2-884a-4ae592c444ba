
; (function () {
    var grafana_js = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }

        __extends(_child, _super);

        _child.prototype.onInitialized = function (e) {
            var that = this;
            var pageid = that.Model.viewModel.pageId;
            $("#" + pageid).append('<div id="btnf' + pageid + '" class="btncs"><img src="../../../fw/images/computer.png" width="30" height="30" title="点击全屏展示" /></div>');
            $("#" + pageid).append('<iframe id="mapData' + pageid + '" src="https://mp.yidaohome.com:3300/d/10Vp1MHVk/jiang-su-sheng-da-ping-bao-biao?orgId=1&theme=dark&refresh=1d&kiosk" width="100%" frameborder=0></iframe>');
            //更新维度动态列
            var iframe = $("#" + that.Model.viewModel.pageId).find("#mapData" + pageid);
            setInterval(function () {
                iframe.attr('height', window.innerHeight - 150)
            }, 1000);

            $("#btnf" + pageid).bind("click", function () {
                that.launchFullscreen(document.getElementById("mapData" + pageid));
            })
        };

        _child.prototype.launchFullscreen = function (element) {
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
        }


        return _child;
    })(BasePlugIn);
    window.grafana_js = window.grafana_js || grafana_js;
})();