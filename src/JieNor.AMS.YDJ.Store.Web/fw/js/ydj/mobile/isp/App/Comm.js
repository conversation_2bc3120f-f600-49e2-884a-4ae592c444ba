/*全局常量*/
var T_User = ["id", "fphone", "fname", "fbirth", "fsex", "fprofield", "fprovince", "fcity", "fregion", "fimage", "fidcard", "fidcar1", "fidcar2", "fidcar3", "fworkyears", "fapprovestatus", "fcheckstatus"];/*用户表*/
var authmsg = "您非当前订单执行人无法操作该订单";


/*localStorage  Start*/
var mobilePhone = localStorage.getItem("Phone");/*用户手机号（登陆名）*/
var password = localStorage.getItem("Pwd");/*用户密码*/
var MasterId = localStorage.getItem("Id");/*师傅Id*/
var UseId = localStorage.getItem("userId");/*登陆者id*/
var MasterAuth = localStorage.getItem("Mauth");/*用户是否认证*/
var FileConfig = localStorage.getItem("FileConfig");/**文件上传的配置**/
var Openid = localStorage.getItem("MicroOpenid");/*微信用户openid*/
/*localStorage  End*/


/*全局变量*/
var RegCollection = {
    Mobile: /^[1]{1}[3,4,5,7,8,9]{1}[0-9]{9}$/
};
var Pixel = {
    W: document.documentElement.clientWidth,
    H: window.innerHeight
};
var IE_BrowserVersion = 10;
if (navigator.appName.toLowerCase() == 'microsoft internet explorer') {
    IE_BrowserVersion = parseInt(navigator.appVersion.split(";")[1].replace(/[ ]/g, "").toLocaleLowerCase().replace('msie', ''));
};
var GetUrl = window.location.protocol + '//' + window.location.host;
var u = navigator.userAgent,
	app = navigator.appVersion;
var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1;
var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
var getCodeStatus = true;/*验证码重复获取验证*/
var Scroll = null;/*滚动条*/


if (isiOS) {
    var _Xmlhttp = null;
    if (window.ActiveXObject) {
        _Xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest) {
        _Xmlhttp = new XMLHttpRequest();
    }
    _Xmlhttp.abort();
}

//追加class   AddClass([String/Object],[String])([标签ID/标签本身Object],[Class名])
var AddClass = function () {
    var o = DomTag.call(this, arguments[0]);
    var cn = arguments[1];
    var re = new RegExp("(\\s*|^)" + cn + "\\b", "g");
    o.className += o.className ? (re.test(o.className) ? "" : " " + cn) : cn;
};
//Ajax([Object]{url:[String],data:[Object],callback:[Function(ServerInfo:[String])],timeout:[Int],aysn:[Boolen],error:[Function([Int],[String])]})({url:[请求地址],<可选参数(未填写时为Get请求)>data:[数据],callback:[回调方法([服务端返回的字符串])],timeout:[超时时间],aysn:[是否异步],<可选参数>error:[错误回调方法([Http错误码],[错误信息])]})
var Ajax = function () {
    var n = arguments[0];
    var d = { url: null, data: null, callback: null, timeout: 0, contentType: null, aysn: true, error: null, isLoad: true, header: null };
    ParameterInit(d, n);
    if (n.isLoad) {
        Message.Loading();
    }
    var url = n.url;
    var data = n.data;
    var callback = n.callback;
    var timeout = n.timeout;
    var aysn = n.aysn;
    var error = n.error;
    var contentType = n.contentType;
    var header = n.header;
    var _Xmlhttp = null;
    if (window.ActiveXObject) {
        _Xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
    }
    else if (window.XMLHttpRequest) {
        _Xmlhttp = new XMLHttpRequest();
    }
    if (_Xmlhttp != null) {
        if (data != null) {//Post
            _Xmlhttp.open('POST', url, aysn);
            if (data instanceof FormData) {
                if (header != null) {
                    for (var o in header) {
                        _Xmlhttp.setRequestHeader(o, header[o]);
                    }
                }
                _Xmlhttp.send(data);
            } else {
                if (contentType == null) {
                    _Xmlhttp.setRequestHeader("CONTENT-TYPE", "application/json;charset=utf-8");
                    _Xmlhttp.send(JSON.stringify(data));
                }
            }
        }
        else//Get
        {
            _Xmlhttp.open('GET', url, aysn);
            _Xmlhttp.send();
        }
        if (timeout > 0) {
            var i = 0;
            var clearTime = setInterval(function () {
                i++;
                if (i == timeout) {
                    clearInterval(clearTime);
                    _Xmlhttp.abort();
                }
            }, 1000);
        }
        _Xmlhttp.onreadystatechange = function () {
            if (this.readyState == 4) {
                if (this.status == 200) {
                    if (n.isLoad) {
                        Message.Remove();
                    }
                    if (clearTime) {
                        clearInterval(clearTime);
                    }
                    var _result = this.responseText;
                    if (String(_result).isNullOrEmpty()) {
                        return;
                    }
                    _result = JSON.parse(_result);
                    if (!String(_result.operationResult).isNullOrEmpty()) {
                        if (_result.operationResult.complexMessage.errorMessages.length > 0) {
                            Message.Alert(_result.operationResult.complexMessage.errorMessages.join("<br/>"));
                            return;
                        }
                        if (!String(_result.responseStatus).isNullOrEmpty()) {
                            Message.Alert(_result.responseStatus.message);
                            return;
                        }
                    }
                    callback(_result);
                }
                else {
                    if (n.isLoad) {
                        Message.Remove();
                    }
                    if (this.status == 401 && window.location.href.toLocaleLowerCase().indexOf("/login.html") == -1) {
                        _Xmlhttp.abort();
                        Redirect('/views/ydj/mobile/isp/App/login.html');
                        //var TryInt = 0;
                        //var TryLogin = setInterval(function () {
                        //    if (TryInt >= 4) {
                        //        clearInterval(TryLogin);
                        //    }
                        //    Ajax({
                        //        isLoad: false,
                        //        url: '/auth/credentials?format=json',
                        //        data: { userName: localStorage.getItem("Phone"), password: localStorage.getItem("Pwd") },
                        //        callback: function () {
                        //            var Json = arguments[0];
                        //            if (!String(Json.sessionId).isNullOrEmpty()) {
                        //                window.location.reload();
                        //                clearInterval(TryLogin);
                        //            }
                        //            else {
                        //                TryInt++;
                        //            }
                        //        }, error: function () {
                        //            TryInt++;
                        //        }
                        //    });
                        //}, 2000);
                        return;
                    }
                    if (this.status == 403) {
                        var _result = JSON.parse(this.responseText);
                        if (!String(_result.responseStatus).isNullOrEmpty()) {
                            Message.Alert(_result.responseStatus.message);
                            return;
                        }
                    }
                    if (error) {
                        if (String(this.responseText).isNullOrEmpty()) {
                            Message.Alert("网络连接中断");
                            return;
                        }
                        error(this.status, JSON.parse(this.responseText));
                    }
                    else {
                        Message.Alert("网络错误：" + this.status);
                    }
                }
            }
        }
    }
};
//APP接口
var Andoggy = {
    isLoad: false,
    locationInfo: "",
    exec: function (action, args) {
        if (isAndroid) {
            var request = "andoggy:" + action[0] + ":" + action[1];
            prompt(request, args)
        } else if (isiOS) {
            var request = "andoggy::" + action[0] + "::" + action[1] + "::" + args;
            document.location = request
        }
    },

    /**
     根据不同平台调用对应插件名
     **/
    getPluginMethod: function (pluginname, methodname) {
        var arr = new Array(2)
        if (isAndroid) {
            arr[0] = ("AD" + pluginname);
        } else {
            arr[0] = ("ID" + pluginname);
        }
        arr[1] = methodname;
        return arr
    },

    /**
  获取企业码
 **/
    getEnterpriseCode: function (callback) {
        var params = {
            adparams: {
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        var arr = new Array(2)
        arr[0] = "CommonPlugin";
        arr[1] = "getEnterpriseCode";
        Andoggy.exec(arr, paramsJson)
    },
    //获取极光推送设备id
    GetDriverID: function (callback) {
        var params = {
            adparams: {
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        var arr = new Array(2)
        arr[0] = "CommonPlugin";
        arr[1] = "getRegistrationID";
        Andoggy.exec(arr, paramsJson)
    },
    /**
     params:
     barcolor : 背景颜色 #ffffff
     textcolor : 字体颜色
     **/
    setTitleBarColor: function (bgcolor, forecolor) {
        var params = {
            adparams: {
                "bgcolor": bgcolor,
                "forecolor": forecolor
            }
        };
        var paramsJson = JSON.stringify(params);
        var arr = Andoggy.getPluginMethod("BasicPlugin", "setNavigationColors");
        Andoggy.exec(arr, paramsJson)
    },


    //    /**
    //     页面加载完成
    //     html 请求链接: eg: http://www.baidu.com?oid=xxx&name=xxx
    //    **/
    //    onPageLoaded : function(url){
    //                      
    //    },


    /**
     params说明
     pageurl : 页面链接
     data: 额外参数:eg : oid
     shownvg:是否显示标题栏(务必带上这个参数)
     needlocation:是否需要定位功能(默认否)
     titlename:页面标题
     jumpfinish:跳转到其他页面是是否需要销毁本身
	 **/
    pushController: function (pageurl, data, shownvg, needlocation, titlename, jumpfinish) {
        var params = {
            adparams: {
                "pageurl": pageurl,
                "data": data,
                "shownvg": shownvg,
                "needlocation": needlocation,
                "titlename": titlename,
                "jumpfinish": jumpfinish
            }
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "pushController");
        Andoggy.exec(methods, paramsJson)
    },


    /**
     params说明
     [{"name":"首页","iconImgUrl":"http:xxx","contentUrl":"http:xxx"}]
     **/
    pushTabBarController: function (contentarray) {
        var params = {
            adparams: contentarray
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("ExtraPlugin", "pushTabBarController");
        Andoggy.exec(methods, paramsJson)
    },

    /**
     多照片选择
     **/
    chooseImage: function (callback) {
        var params = {
            adparams: {
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        var arr = new Array(2)
        arr[0] = "ImageLoaderPlugin";
        arr[1] = "chooseImage";
        Andoggy.exec(arr, paramsJson)
    },
    finishPage: function () {
        var params = {
            adparams: {
            }
        };
        var paramsJson = JSON.stringify(params);
        var arr = new Array(2)
        arr[0] = "CommonPlugin";
        arr[1] = "finishPage";
        Andoggy.exec(arr, paramsJson)
    },

    /**
     返回上一级页面
     **/
    goBack: function () {
        var params = {
            adparams: {
            }
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "goBack");
        Andoggy.exec(methods, paramsJson);
    },

    /**
     弹出消息对话框
     **/
    makeDialog: function (c) {
        var params = {
            adparams: {
                "msg": c
            }
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "makeDialog");
        Andoggy.exec(methods, paramsJson);
    },


    /**
     获取app版本号
     callback:接收版本信息的方法
     return eg:ios: {"VersionName":"1.0.1","VersionCode":"1.0"}
     **/
    getVersionInfo: function (callback) {
        var params = {
            adparams: {
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "getVersionInfo");
        Andoggy.exec(methods, paramsJson);
    },



    /**
     获取系统版本号
     callback : 接收参数的回调方法
     return eg:ios: {"OSVersion":"7.0"}
     **/
    getOSVersion: function (callback) {
        var params = {
            adparams: {
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "getOSVersion")
        Andoggy.exec(methods, paramsJson)
    },

    /**
     打电话
     params : number:电话号码
     **/
    makePhoneCall: function (number) {
        var params = {
            adparams: {
                "number": number
            }
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "makePhoneCall")
        Andoggy.exec(methods, paramsJson)
    },



    /**
     开始定位服务（前提已开启页面定位功能）
     **/
    startLocationUpdate: function () {
        var params = {
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("LocationPlugin", "startLocationUpdate")
        Andoggy.exec(methods, paramsJson)
    },


    /**
     关闭定位服务
     **/
    stopLocationUpdate: function () {
        var params = {

        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("LocationPlugin", "stopLocationUpdate")
        Andoggy.exec(methods, paramsJson)
    },


    /**
     获取当前地理位置（baidu）
     return : {"longitude":"10.0000","latitude":"20.2200"}
     **/
    onReceiveLocation: function (info) {
        this.locationInfo = info;
    },


    /**
     接收定位错误信息
     **/
    onLocationError: function (error) {
        return error
    },

    onPageResume: function () {
        if (!String(localStorage.getItem("FunEvent")).isNullOrEmpty()) {
            if (typeof (TabExec) == 'function') {
                Function(String(localStorage.getItem("FunEvent")))();
            }
            localStorage.removeItem("FunEvent");
            return;
        }
        if (String(localStorage.getItem("Reload")).Boolean()) {
            this.reloadPage();
            localStorage.removeItem("Reload");
        }
    },


    /**
     手动刷新页面
     **/
    reloadPage: function () {
        var params = {

        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "reloadPage")
        Andoggy.exec(methods, paramsJson)
    },

    /**
     获取手机图片
     callback : 接受资源的函数
    **/
    takePhoto: function (callback) {
        var params = {
            adparams: {
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("PhotoPlugin", "takePhoto")
        Andoggy.exec(methods, paramsJson)
    },

    /**
     清理缓存
    **/
    clearWebCache: function () {
        var params = {

        };
        var paramsJson = JSON.stringify(params);
        var methods = Andoggy.getPluginMethod("BasicPlugin", "clearWebCache")
        Andoggy.exec(methods, paramsJson)
    },

    /**
      添加日历提醒事件
      name: 用户账号，此处填用户登录电话号码
      title: 事件标题
      desc: 事件描述
      time : 事件开始和终止时间（2017-08-23 19:00:00),日期不能早于当前时间，至少当前事件往后10分钟
      location:事件地址
      callback:回调，接受String类型参数，返回操作结果 ： 安卓:{"type":"0","value":"与苍老师秘密会谈"},ios:{"type":"1","value":"EA000000..."}
    **/
    addCalendarEvent: function (name, title, desc, time, location, callback) {
        var params = {
            adparams: {
                "name": name,
                "title": title,
                "desc": desc,
                "time": time,
                "location": location,
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        //                            var methods = Andoggy.getPluginMethod(,);
        var arr = new Array(2)
        arr[0] = "CalendarAlarmPlugin";
        arr[1] = "createAlarmEvent";
        Andoggy.exec(arr, paramsJson)
    },


    /**
      根据事件标题删除提示事件
      title:android传事件的title：例：与苍老师秘密会谈；ios传事件id，例：A3082203-F29D-4E1A-851B-23C068160FF1:0E2729DC-B12B-48EE-A798-4D41D7F2B726
      callback:回调，返回操作结果
     **/
    removeCalendarEvent: function (title, callback) {
        var params = {
            adparams: {
                "title": title,
                "callback": callback
            }
        };
        var paramsJson = JSON.stringify(params);
        var arr = new Array(2)
        arr[0] = "CalendarAlarmPlugin";
        arr[1] = "deleteAlarmEvent";
        Andoggy.exec(arr, paramsJson)
    }

};
//获取子集 Children([String/Object],[String])([标签ID/标签本身Object],[TagName])
var Children = function () {
    var o = arguments[0];// DomTag.call(this, arguments[0]);
    var tag = arguments[1];
    var nl = [];
    var st = false;
    var child = o.childNodes;
    for (var i = 0; i < child.length; i++) {
        if (typeof (child[i].tagName) == "string") {
            if (tag) {
                if (tag == "*" && child[i].tagName.toLowerCase() != "script") {
                    nl.push(child[i]);
                }
                else if (child[i].tagName.toLowerCase() == tag) {
                    nl.push(child[i]);
                }
            }
            else {
                nl.push(child[i]);
            }
        }
    }
    return nl;
};
//创建Dom CreateDom([String])(标签TagName)
var CreateDom = function () {
    return document.createElement(arguments[0]);
};
//初始化（创建）本地所有表
var CreateMessageTable = function () {
    var sql = new WebSql();
    /*尝试删除 Start*/
    try {
        sql.Drop("UserInfo");
    } catch (e) {

    }
    /*尝试删除 End*/
    sql.Create("UserInfo", T_User);/*创建用户表*/
};
//查找Dom对象   DomTag([String/Object])([标签ID/标签本身Object])
var DomTag = function () {
    var d = arguments[0];
    if (IE_BrowserVersion <= 8) {
        return typeof (d) == 'string' ? document.getElementById(d) : d;
    }
    if (d) {
        var _type = typeof (d);
        if (_type === "object") {
            return d;
        }
        if (_type === "string") {
            var _frist = d.substr(0, 1);
            if (_frist === "." || _frist === "#" || _frist === "[") {
                return document.querySelectorAll(d);
            }
            return document.getElementById(d);
        }
    }
    return null;
};
//下拉框展开
function DropDownOpen(sltElement) {
    var event;
    event = document.createEvent('MouseEvents');
    event.initMouseEvent('mousedown', true, true, window);
    sltElement.dispatchEvent(event);
};
//图片（处理）上传
var FileUpload = function () {
    var Method = function () {
        this.Source = null;//input file
        this.Resources = null;//资源
        this.ImageTag = null;//img标签
        this.UploadObject = null;//上传的对象
        this.MIME = null;//文件类型
        this.Name = null;//文件名
        this.Quality = 100;
        this.Base64 = null;
        this.Callback = null;
        this.imgSize = null;
    };
    var ResourcesLoad = function () {
        var arg = arguments[0];
        return URL.createObjectURL(arg) || webkitURL.createObjectURL(arg) || window.URL.createObjectURL(arg);
    };
    Method.prototype = {
        //加载file数据且初始化文件信息
        Load: function () {
            var source = this.Source = arguments[0];
            if (source.files.length == 0) {
                return;
            }
            var _files = source.files;
            if (_files.length == 1) {//单文件
                this.Resources = ResourcesLoad(_files[0]);
                this.Name = _files[0].name;
                this.MIME = _files[0].type;
                return;
            }
            //多文件
            this.Resources = [];
            this.Name = [];
            this.MIME = [];
            this.FormatImgList();
            for (var i = 0; i < _files.length; i++) {
                this.Resources.push(ResourcesLoad(_files[i]));
                this.Name.push(_files[i].name);
                this.MIME.push(_files[i].type);
            }
        },
        FormatImgList: function () {
            var inl = this.ImageTag.parentNode.parentNode.getElementsByTagName("img");
            var o = [];
            var b = false;
            for (var i = 0; i < inl.length; i++) {
                if (inl[i] == this.ImageTag) {
                    b = true;
                }
                if (b) {
                    o.push(inl[i]);
                }
            }
            this.ImageTag = o;
        },
        //Base64转blob
        Base64UrlToBlob: function () {
            var urlData = arguments[0];
            var mime = arguments[1];
            var bytes = window.atob(urlData.split(',')[1]);        //去掉url的头，并转换为byte
            //处理异常,将ascii码小于0的转换为大于0
            var ab = new ArrayBuffer(bytes.length);
            var ia = new Uint8Array(ab);
            for (var i = 0; i < bytes.length; i++) {
                ia[i] = bytes.charCodeAt(i);
            }
            return new Blob([ab], { type: mime });
        },
        //图片处理并上传
        ImagesFormat: function () {
            var _this = this;
            var Quality = this.Quality;
            var Resources = this.Resources;
            var MIME = this.MIME;
            if (Resources) {
                if (Resources instanceof Array) {
                    var len = Resources.length > this.ImageTag.length ? this.ImageTag.length : Resources.length;
                    for (var i = 0; i < len; i++) {
                        (function (_i) {
                            var img = new Image();
                            img.src = Resources[_i];
                            img.onload = function () {
                                var base64 = _this.Canvas(this).toDataURL(_this.MIME[_i], Quality / 100);
                                _this.ImageTag[_i].src = base64;
                                var blob = _this.Base64UrlToBlob(base64, _this.MIME[_i]);
                                _this.UploadObject = new FormData();
                                _this.UploadObject.append("file", blob, _this.Name[_i]);
                                _this.UploadObject.append("name", _this.Name[_i])
                                _this.UploadToServer(_this.ImageTag[_i]);
                            }
                        })(i)
                    }
                }
                else {
                    var img = new Image();
                    img.src = Resources;
                    img.onload = function () {
                        var base64 = _this.Canvas(this).toDataURL(MIME, Quality / 100);
                        if (_this.ImageTag) {
                            _this.ImageTag.src = _this.Base64 = base64;
                        }
                        var blob = _this.Base64UrlToBlob(base64, MIME);
                        _this.UploadObject = new FormData();
                        _this.UploadObject.append("file", blob, _this.Name);
                        _this.UploadObject.append("name", _this.Name)
                        _this.UploadToServer();
                    }
                }
            }
        },
        //图片裁切
        ImagesCut: function () {
            var _this = this;
            var Resources = this.Resources instanceof Array ? this.Resources[0] : this.Resources;
            var MIME = this.MIME instanceof Array ? this.MIME[0] : this.MIME;
            var Name = this.Name instanceof Array ? this.Name[0] : this.Name;
            var Quality = this.Quality;
            var div = CreateDom("div");
            div.id = "imgPanel";
            var fh = (Pixel.H - Pixel.W) / 2;
            var d = CreateDom("div");
            d.style.height = fh + "px";
            div.appendChild(d);
            d = CreateDom("div");
            d.style.height = fh + "px";
            d.style.bottom = "0px";
            div.appendChild(d);
            var to = CreateDom("p");
            var a = CreateDom("em");
            a.innerText = "\u53d6\u6d88";
            a.onclick = function () {
                if (DomTag("imgPanel")) {
                    document.getElementsByTagName("body")[0].removeChild(DomTag("imgPanel"));
                }
            };
            to.appendChild(a);
            a = CreateDom("em");
            a.innerText = "\u5b8c\u6210";
            a.onclick = function () {
                _this.CutImage(_this);
                if (DomTag("imgPanel")) {
                    document.getElementsByTagName("body")[0].removeChild(DomTag("imgPanel"));
                }
            };
            to.appendChild(a);
            div.appendChild(to);
            var imgpanel = CreateDom("div");
            imgpanel.className = "showimgs";
            imgpanel.style.height = Pixel.H + "px";
            var img = CreateDom("img");
            img.style.marginBottom = fh + "px";
            img.style.marginTop = fh + "px";
            img.src = Resources;
            img.style.width = "100%";
            var _im = new Image();
            _im.src = Resources;
            _im.onload = function () {
                var _thisw = this.width;
                var _thish = this.height;
                _this.imgSize = {
                    W: this.width,
                    H: this.height
                }
            }
            imgpanel.appendChild(img);
            div.appendChild(imgpanel);
            var startX = 0, endX = 0, thisw = 0, thish = 0, baseScale = 0;
            imgpanel.addEventListener("touchstart", function (e) {
                var e = e || window.event;
                var targetlen = e.targetTouches;
                if (targetlen.length > 1) {
                    startX = Math.abs(targetlen[0].pageX - targetlen[1].pageX);
                }
            });
            imgpanel.addEventListener("touchmove", function (e) {
                var e = e || window.event;
                var targetlen = e.targetTouches;
                if (targetlen.length > 1) {
                    endX = Math.abs(targetlen[0].pageX - targetlen[1].pageX);
                    var scale = endX - startX;
                    if (thisw + scale < Pixel.W || (thisw + scale) / baseScale < Pixel.W) {
                        return;
                    }
                    Children(this, "img")[0].style.width = thisw + scale + "px";
                    Children(this, "img")[0].style.height = (thisw + scale) / baseScale + "px";
                }
            });
            imgpanel.addEventListener("touchend", function () {
                thisw = Children(this, "img")[0].offsetWidth;
            });
            document.getElementsByTagName("body")[0].appendChild(div);
        },
        CutImage: function () {
            var _this = arguments[0];
            var _MIME = _this.MIME;
            var _Quality = _this.Quality;
            var _Name = _this.Name;
            var cvs = CreateDom('canvas');
            var ctx = cvs.getContext('2d');
            var W = Pixel.W;
            var imgp = DomTag(".showimgs")[0];

            var img = imgp.getElementsByTagName("img")[0];
            var sx = parseInt(imgp.scrollLeft * this.imgSize.W / img.offsetWidth);
            var sy = parseInt(imgp.scrollTop * this.imgSize.H / img.offsetHeight);
            var cut = parseInt(this.imgSize.W / img.offsetWidth * W);
            cvs.width = W;
            cvs.height = W;
            var _img = new Image();
            _img.src = _this.Resources;
            ctx.drawImage(_img, sx, sy, cut, cut, 0, 0, W, W);
            var base64 = cvs.toDataURL(_MIME, _Quality / 100);
            if (_this.ImageTag) {
                _this.ImageTag.src = _this.Base64 = base64;
            }
            var blob = _this.Base64UrlToBlob(base64, _MIME);
            //使用ajax发送
            _this.UploadObject = new FormData();
            _this.UploadObject.append("file", blob, _Name);
            _this.UploadObject.append("name", _Name);
            this.UploadToServer();
        },
        //裁切块
        InitCutBlock: function () {
            if (DomTag("imgCut") != null) {
                //DomTag("imgCut").addEventListener('touchstart',
                //function (event) {
                //    event.preventDefault();
                //    var W = DomTag("imgCut").offsetWidth;
                //    var L = DomTag("imgCut").offsetLeft;
                //    var X = event.touches[0].clientX;
                //    var Y = event.touches[0].clientY;
                //    if (X > W + L - 30 && Y > W + L - 30) {
                //        DomTag("imgCut").style.backgroundColor = 'rgba(255,0,0,0.6)';
                //        document.ontouchmove = function (event) {
                //            if (event.touches[0].clientX - L >= 70) {
                //                DomTag("imgCut").style.width = event.touches[0].clientX - L + "px";
                //                DomTag("imgCut").style.height = event.touches[0].clientX - L + "px";
                //            }
                //        }
                //    }
                //    else {
                //        document.ontouchmove = function (event) {

                //            DomTag("imgCut").style.left = event.touches[0].clientX - W / 2 + "px";
                //            DomTag("imgCut").style.top = event.touches[0].clientY - W / 2 + "px";
                //        }
                //    }
                //    document.ontouchend = function (event) {
                //        DomTag("imgCut").style.backgroundColor = 'rgba(0,0,0,0.5)';
                //        document.ontouchmove = null;
                //    }
                //});
            }
        },
        //Canvas处理图片
        Canvas: function () {
            var _source = arguments[0];
            var cvs = CreateDom("canvas");
            var ctx = cvs.getContext('2d');
            var W = _source.width / 2;
            cvs.width = W;
            cvs.height = W / _source.width * _source.height;
            ctx.drawImage(_source, 0, 0, W, W / _source.width * _source.height);
            return cvs;
        },
        //上传至服务器
        UploadToServer: function () {
            var _this = this;
            var data = this.UploadObject;
            var Img = arguments[0] ? arguments[0] : this.ImageTag;
            if (data == null || !(data instanceof FormData)) {
                return;
            }
            if (String(FileConfig).isNullOrEmpty()) {
                Message.Alert("上传组件配置信息获取失败");
                return;
            }
            var Config = JSON.parse(FileConfig);
            Ajax({
                header: { "sysCode": Config.sysCode, "authCode": Config.authCode },
                url: Config.fsApiUrl + '/FileInfo/AjaxUpload',
                data: data,
                callback: function () {
                    var _result = arguments[0];
                    Img.setAttribute("data-fileId", _result.fileId);
                    _this.Callback(_result.fileId);
                },
                error: function () {
                    console.log(arguments[0] + arguments[1]);
                }
            });
        },
    };
    return {
        //参数1 input file  参数2 img元素 参数3 裁切模式（-1不惨切 ,0按屏幕宽度缩放,1自定义裁剪） 参数4是否保存本地base64 参数5 回调fileid
        Upload: function () {
            var arg = arguments;
            var Source = arg[0];//input file
            if (String(Source.value).isNullOrEmpty()) {
                return;
            }
            var Img = arg[1];//img标签
            var CutType = arg[2];//裁切模式
            var CallBack = arg[3];
            var F = new Method();
            F.ImageTag = Img;
            F.Callback = CallBack;
            switch (parseInt(CutType)) {
                case -1://非图片或者无需裁剪的上传
                    F.UploadObject = new FormData();
                    if (Source.files.length > 0) {
                        for (var i = 0; i < Source.files.length; i++) {
                            F.UploadObject.append("file[]", Source.files[i], Source.files[i].name);
                            F.UploadObject.append("name[]", Source.files[i].name)
                        }
                    }
                    else {
                        F.UploadObject.append("file", Source.files[0], Source.files[0].name);
                        F.UploadObject.append("name", Source.files[i].name)
                    }
                    F.UploadToServer();
                    return;
                case 0:
                    F.Load(Source);
                    F.ImagesFormat();
                    return;
                case 1:
                    F.Load(Source);
                    F.ImagesCut();
                    break;
                default:

            }
        }
    };
}();
//获取验证码
var GetCode = function () {
    var Dom = arguments[0];
    var Phone = arguments[1] ? arguments[1] : document.getElementsByName("mobilePhone")[0].value;
    if (!RegPattern(Phone, "^[1]{1}[3,4,5,7,8,9]{1}[0-9]{9}$", "手机号码格式错误")) {
        return;
    }
    if (getCodeStatus) {
        Ajax({
            isLoad: false,
            url: '/sms/code?mobilePhone=' + Phone + "&format=json",
            callback: function () {
                getCodeStatus = false;
                var Json = arguments[0];
                if (!String(Json.operationResult.isSuccess).Boolean()) {
                    Message.Alert("验证码获取失败");
                    return;
                }
                var i = 59;
                Dom.innerHTML = "再次获取(60)";
                AddClass(Dom, "gray");
                var SID = setInterval(function () {
                    Dom.innerHTML = "再次获取(" + i + ")";
                    if (i == 0) {
                        Dom.innerHTML = "获取验证码";
                        getCodeStatus = true;
                        RemoveClass(Dom, "gray");
                        clearInterval(SID);
                    }
                    i--;
                }, 1000);
            }
        });
    }
};
//自定义查找属性
var GetDomByAttr = function () {
    var attr = arguments[0];
    var tag = arguments[1];
    var val = arguments[2];
    var _nl = document.getElementsByTagName(tag || "*");
    var _arr = new Array();
    for (var i = 0; i < _nl.length; i++) {
        var _attrVal = _nl[i].getAttribute(attr);
        if (_attrVal) {
            if (val) {
                if (val == _attrVal) {
                    _arr.push({ dom: _nl[i], attr: _attrVal });
                }
            }
            else {
                _arr.push({ dom: _nl[i], attr: _attrVal });
            }
        }
    }
    return _arr;
}
//获取基础资料参数 FromID,Key,CallBack
var GetEnum = function () {
    var FormId = arguments[0];
    var Word = arguments[1];
    var Call = arguments[2];
    Ajax({
        url: '/bill/' + FormId + '?operationno=querycombo&format=json',
        data: { simpledata: { fieldkey: Word } },
        callback: function () {
            var _json = arguments[0];
            Call(_json.operationResult.srvData.data);
        }
    });
};
//获取图片[参数]
var GetImages = function () {
    var dom = arguments[0];
    var arg = arguments[1];
    if (String(arg).Trim().isNullOrEmpty()) {
        return;
    };
    GetServerImages(dom, arg);
};
//请求用户（师傅）数据
var GetMasterData = function () {
    var Phone = arguments[0];
    var IsEnd = arguments[1];
    Ajax({
        url: "/bill/ydj_master?operationno=getdata&format=json",
        data: {
            fromId: "ydj_master",
            operationNo: "getdata",
            simpleData: { "phone": Phone, "driver": localStorage.getItem("DriverID") }
        },
        callback: function () {
            var Json = arguments[0];
            if (String(Json.operationResult.isSuccess).Boolean()) {
                var result = Json.operationResult.srvData ? Json.operationResult.srvData.uidata : null;
                if (result == null) {
                    Message.Alert("数据获取失败");
                    return;
                }
                var content = [result["id"]];
                for (var i = 1; i < T_User.length; i++) {
                    var _v = result[T_User[i]];
                    content.push(typeof (_v) == 'string' ? _v : JSON.stringify(_v));
                }
                localStorage.setItem("Mauth", String(result["fapprovestatus"].id).Trim());
                var sql = new WebSql();
                sql.Delete("UserInfo");
                sql.Insert("UserInfo", T_User, content);
                if (typeof (IsEnd) == "function") {
                    IsEnd(result);
                    return;
                }
                localStorage.setItem("Id", result["id"]);
                if (isAndroid || isiOS) {
                    var params = {
                        "pageelements": [
                        {
                            "name": "首页",
                            "iconImgUrl": GetUrl + "/fw/css/ydj/mobile/isp/App/images/home.png",
                            "contentUrl": GetUrl + "/views/ydj/mobile/isp/App/home.html?shownvg=0&intab=1",
                            "intab": "1"
                        },
                        {
                            "name": "我的任务",
                            "iconImgUrl": GetUrl + "/fw/css/ydj/mobile/isp/App/images/task.png",
                            "contentUrl": GetUrl + "/views/ydj/mobile/isp/App/OLS/my_task.html?shownvg=0&intab=1",
                            "intab": "1"
                        }
                        ],
                        "tintcolor": "#1d89eb",
                        "jumpfinish": "1"
                    };
                    Andoggy.pushTabBarController(params);
                    return;
                }
                Message.Confirm('<p class="center">登陆成功</p>', function () { Redirect('/views/ydj/mobile/isp/App/home.html'); }, function () { Redirect("/views/ydj/mobile/isp/App/OLS/my_task.html"); }, '首页', '我的任务');
            }
        }, error: function () {
            var Json = arguments[1];
            Message.Alert(Json.responseStatus.message);
        }
    });
};
//获得地址栏参数   GetQueryString([String])([键])
var GetQueryString = function () {
    var name = String(arguments[0]);
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
};
//获取服务器上的图片
var GetServerImages = function () {
    var dom = arguments[0];
    var arg = arguments[1];
    if (String(FileConfig).isNullOrEmpty()) {
        FileConfig = localStorage.getItem("FileConfig");
    }
    var Config = JSON.parse(FileConfig);
    if (dom.tagName.toLocaleLowerCase() == "img") {
        dom.src = Config.fsApiUrl + '/FileInfo/File/' + arg;
        dom.setAttribute("data-fileId", arg);
        if (dom.getAttribute("data-big") == null) {
            dom.onclick = function () {
                ShowImg(this.src);
            }
        }
        return;
    }
    else {
        var ic = dom.getElementsByTagName("img");
        //循环子集，并实例
        var _d = ic[0];
        while (_d.parentNode.id != dom.id) {
            _d = _d.parentNode;
        };
        for (var i = 0; i < arg.length; i++) {
            if (!String(ic[i]).isNullOrEmpty()) {
                ic[i].src = Config.fsApiUrl + '/FileInfo/File/' + arg[i];
                if (ic[i].getAttribute("data-big") == null) {
                    ic[i].onclick = function () {
                        ShowImg(this.src);
                    }
                }
            } else {
                var nd = _d.cloneNode(true);
                nd.getElementsByTagName("img")[0].src = Config.fsApiUrl + '/FileInfo/File/' + arg[i];
                if (nd.getElementsByTagName("img")[0].getAttribute("data-big") == null) {
                    nd.getElementsByTagName("img")[0].onclick = function () {
                        ShowImg(this.src);
                    }
                }
                dom.appendChild(nd);
            }
        }
    }
};
//获取微信用户的openid
var GetToken = function () {
    var Even = arguments[0];
    var code = GetQueryString("code");
    if (!String(Openid).isNullOrEmpty()) {
        //请求文件服务器配置
        if (String(FileConfig).isNullOrEmpty()) {
            Ajax({
                isLoad: false,
                data: {
                    DomainType: "dynamic",
                    OperationNo: "getsysinfo",
                    FormId: "sys_mainfw",
                    openid: openId
                },
                url: '/Wechat/transfer?format=json',
                callback: function () {
                    var Json = arguments[0];
                    localStorage.setItem("FileConfig", JSON.stringify(Json.operationResult.srvData.fileServerInfo));
                }
            });
        }
        //判断用户是否绑定
        Ajax({
            isLoad: false,
            data: {
                DomainType: "dynamic",
                OperationNo: "isBindPhone",
                FormId: "wx_micromember",
                simpleData: { openid: Openid },
                openid: Openid
            },
            url: '/Wechat/transfer?format=json',
            callback: function () {
                var Json = arguments[0];
                var b = false;
                var _phone = "";
                if (String(Json.operationResult.isSuccess).Boolean()) {
                    b = true;
                    _phone = Json.operationResult.simpleMessage;
                }
                Even(Openid, b, _phone);
            }
        });
        return;
    }
    if (String(code).isNullOrEmpty()) {
        Message.Alert("未授权无法获取用户信息");
        return;
    }
    Ajax({
        url: "/Wechat/GetOpenId?code=" + GetQueryString("code") + "&format=json", callback: function () {
            var arg = arguments[0];
            if (!String(arg.error).isNullOrEmpty()) {
                window.location.href = arg.url;
            }
            localStorage.setItem("MicroOpenid", arg.openId);
            //请求文件服务器配置
            if (String(FileConfig).isNullOrEmpty()) {
                Ajax({
                    isLoad: false,
                    data: {
                        DomainType: "dynamic",
                        OperationNo: "getsysinfo",
                        FormId: "sys_mainfw",
                        openid: arg.openId
                    },
                    url: '/Wechat/transfer?format=json',
                    callback: function () {
                        var Json = arguments[0];
                        localStorage.setItem("FileConfig", JSON.stringify(Json.operationResult.srvData.fileServerInfo));
                        Even(arg.openId, arg.isbind, arg.phone);
                    }, error: function () {
                        var result = arguments[1];
                        try {
                            Message.Alert(result.responseStatus.message)
                        } catch (e) {
                            Message.Alert("获取文件服务器配置出错");
                        }
                    }
                });
            }
        }, error: function () {
            var result = arguments[1];
            try {
                Message.Alert(result.responseStatus.message)
            } catch (e) {
                Message.Alert("获取openid时出错");
            }
        }
    });
};
//获取文件上传的配置
var GetUploadConfig = function () {
    Ajax({
        isLoad: false,
        url: '/dynamic/sys_mainfw?operationno=getsysinfo&format=json',
        data: {},
        callback: function () {
            var Json = arguments[0];
            localStorage.setItem("FileConfig", JSON.stringify(Json.operationResult.srvData.fileServerInfo));
        }
    });
};
//页面元素赋值 data-bind
var HtmlSetData = function () {
    this.id = null;
};
//JSON转URL   JSONToUrlEncode([String/Object],[String])([字符串/JSON对象],<可选参数>[要转换的Key]);
var JSONToUrlEncode = function () {
    var _Json = arguments[0];
    if (_Json == null) {
        return '';
    }
    var _Key = arguments[1];
    var _jsType = String(typeof (_Json)).toLocaleLowerCase();
    var _uri = '';
    if (_jsType === 'string' || _jsType === 'number' || _jsType === 'boolean') {
        _uri += "&" + _Key + "=" + encodeURIComponent(_Json);
    } else {
        for (var _j in _Json) {
            var _k = _Key == null ? _j : _Key + (_Json instanceof Array ? "[" + _j + "]" : "." + _j);
            _uri += '&' + JSONToUrlEncode(_Json[_j], _k);
        }
    }
    return _uri.substr(1);
};
//App登陆方法
var Login = function () {
    var _postData = arguments[0];/*{userName: xxx, password:xxx}*/
    _postData.deviceId = localStorage.getItem("DriverID");
    Ajax({
        url: '/auth/credentials?format=json',
        data: _postData,
        callback: function () {
            var Json = arguments[0];
            if (!String(Json.sessionId).isNullOrEmpty()) {
                localStorage.setItem("userId", Json.userId);//登陆者id
                /*记录用户名密码自动登陆*/
                localStorage.setItem("Phone", _postData.userName);
                localStorage.setItem("Pwd", _postData.password);
                //获取文件上传配置
                GetUploadConfig();
                GetMasterData(_postData.userName);
            }
            else {
                Message.Alert(Json.responseStatus.message);
            }
        },
        error: function () {
            localStorage.removeItem("Phone");
            localStorage.removeItem("Pwd");
            var httpcode = arguments[0];
            var Json = arguments[1];
            switch (httpcode) {
                case 401:
                    if (!String(Json.responseStatus).isNullOrEmpty()) {
                        if (Json.responseStatus.message.indexOf("密码不正确") > -1) {
                            Message.Confirm('你密码输错啦，是不是忘记密码了？', null, function () { Redirect('/views/ydj/mobile/isp/App/SetPwd.html', 'mobilePhone=' + _postData.userName + '&Set=true'); }, '再试一次', '找回密码');
                            return;
                        }
                        Message.Alert(Json.responseStatus.message);
                    }
                    break;
                case 500:
                    if (Json.responseStatus.message) {
                        Message.Alert(Json.responseStatus.message);
                    } else {
                        Message.Alert("用户名不存在");
                    }
                    break;
                default:
                    Message.Alert(Json.responseStatus.message);
                    break;
            }
        }
    })
};
//退出登录
var LoginOut = function () {
    try {
        Ajax({
            url: '/auth/logout',
            data: {},
            callback: function () {
                Redirect("/views/ydj/mobile/isp/App/login.html");
            },
            error: function () {
                if (arguments[0] == 302) {
                    Redirect("/views/ydj/mobile/isp/App/login.html");
                }
            }
        });
        localStorage.clear();//清除localStorage
        var sql = new WebSql();
        sql.Delete("UserInfo");
    } catch (e) {

    }
};
//弹出框
var Message = function () {
    var Fun = function () {
        this.Msg = null;//提示内容
        this.Timer = 30000;//关闭时间
        this.BtnName = null;
        this.CloseEvent = null;//关闭事件
        this.EnsureEvent = null;//确定事件
        this.EnsureName = null;//确定按钮名称
        this.CloseName = null;
    };
    var Filter = {
        Create: function () {
            var div = document.createElement("div");
            div.id = "filter";
            div.style.height = Pixel.H + "px";
            return div;
        },
        Remove: function () {
            if (document.getElementById("filter") != undefined) {
                document.getElementsByTagName("body")[0].removeChild(document.getElementById("filter"));
            }
        }
    }
    Fun.prototype.Box = function () {
        var FunCloseEvent = this.CloseEvent;
        var FunClose = this.Remove;
        var div = document.createElement("div");
        div.id = "msg";
        var _div = document.createElement("div");
        _div.innerHTML = this.Msg;
        div.appendChild(_div);
        var p = document.createElement("p");
        p.className = "tool";
        var _input = document.createElement("em");
        _input.className = "close"
        _input.innerHTML = this.CloseName != null ? this.CloseName : "\u786e\u5b9a";
        _input.onclick = function () {
            FunClose();
            if (FunCloseEvent != null) {
                FunCloseEvent();
            }
        }
        p.appendChild(_input);
        div.appendChild(p);
        this.Create(div);
    }
    Fun.prototype.Confirm = function () {
        var FunCloseEvent = this.CloseEvent;
        var FunClose = this.Remove;
        var FunConfirmEvent = this.EnsureEvent;
        var div = document.createElement("div");
        div.id = "msg";
        var _div = document.createElement("div");
        _div.innerHTML = this.Msg;
        div.appendChild(_div);
        var p = document.createElement("p");
        p.className = "tool";
        var _input = document.createElement("em");
        _input.className = "ensure";
        _input.innerHTML = this.EnsureName != null ? this.EnsureName : "\u786e\u5b9a";
        _input.onclick = function () {
            FunClose();
            if (FunConfirmEvent != null) {
                FunConfirmEvent();
            }

        }
        p.appendChild(_input);
        _input = document.createElement("em");
        _input.className = "close";
        _input.innerHTML = this.CloseName != null ? this.CloseName : "\u53d6\u6d88";
        _input.onclick = function () {
            FunClose();
            if (FunCloseEvent != null) {
                FunCloseEvent();
            }

        }
        p.appendChild(_input);
        div.appendChild(p);
        this.Create(div);
    }
    Fun.prototype.Loading = function () {
        var _html = this.Msg == null ? '\u52a0\u8f7d\u4e2d\u002e\u002e\u002e' : this.Msg;
        var FunClose = this.Remove;
        var div = document.createElement("div");
        div.id = "Loading";
        div.innerHTML = '<div><em class="c1"></em><em class="c2"></em><em class="c3"></em></div><p>' + _html + '</p>';
        this.Create(div);
        if (this.Timer > 0) {
            setTimeout(function () {
                if (document.getElementById("Loading") != undefined) {
                    Filter.Remove();
                    document.getElementsByTagName("body")[0].removeChild(document.getElementById("Loading"));
                }
            }, this.Timer);
        }
    }
    Fun.prototype.Alert = function () {
        var _html = this.Msg == null ? '没有更多数据' : this.Msg;
        var div = document.createElement("div");
        div.id = "alert";
        div.className = "text-center";
        div.innerHTML = _html;
        this.Create(div, true);
    };
    Fun.prototype.Error = function () {
        var _html = this.Msg;
        var div = document.getElementById("Error");
        if (div == undefined) {
            div = document.createElement("div");
            div.id = "Error";
        }
        div.innerHTML = _html;
        document.getElementsByTagName("body")[0].appendChild(div);
    }
    Fun.prototype.Create = function () {
        this.Remove();
        document.getElementsByTagName("body")[0].appendChild(arguments[0]);
        arguments[0].style.marginTop = -(arguments[0].offsetHeight / 2) + "px";
        if (arguments[1] == undefined) {
            document.getElementsByTagName("body")[0].appendChild(Filter.Create());
        }
    }
    Fun.prototype.Remove = function () {
        Filter.Remove();
        if (document.getElementById("msg") != undefined) {
            document.getElementsByTagName("body")[0].removeChild(document.getElementById("msg"));
        }
        if (document.getElementById("Loading") != undefined) {
            document.getElementsByTagName("body")[0].removeChild(document.getElementById("Loading"));
        }
    }

    return {
        Box: function () {
            var FunEvent = new Fun();
            FunEvent.Msg = arguments[0];
            if (arguments[1] != undefined) {
                FunEvent.CloseEvent = arguments[1];
            }
            FunEvent.CloseName = arguments[2] != undefined ? arguments[2] : null;
            FunEvent.Box();
        },
        Confirm: function () {
            var FunEvent = new Fun();
            FunEvent.Msg = arguments[0];
            if (arguments[1] != undefined) {
                FunEvent.EnsureEvent = arguments[1];
            }
            if (arguments[2] != undefined) {
                FunEvent.CloseEvent = arguments[2];
            }
            FunEvent.EnsureName = arguments[3] != undefined ? arguments[3] : null;
            FunEvent.CloseName = arguments[4] != undefined ? arguments[4] : null;
            FunEvent.Confirm();
        },
        Loading: function () {
            var FunEvent = new Fun();
            FunEvent.Msg = arguments[0];
            FunEvent.Loading();
        },

        Alert: function () {
            var FunEvent = new Fun();
            FunEvent.Msg = arguments[0];
            FunEvent.Timer = arguments[1] ? arguments[1] : 3000;
            FunEvent.Alert();
            setTimeout(function () {
                if (document.getElementById("alert") != undefined) {
                    document.getElementsByTagName("body")[0].removeChild(document.getElementById("alert"));
                }
            }, FunEvent.Timer);
        },
        Error: function () {
            var FunEvent = new Fun();
            FunEvent.Msg = arguments[0];
            FunEvent.Error();
        },
        Remove: function () {
            var FunEvent = new Fun();
            FunEvent.Remove();
        }
    };
}();
//数据排序   OrderBy([Array/HtmlCollection],[String],[Boolean],[String/Object])([数组/Html集合],<可选参数>[要排序的字段],<可选参数>[排序规则 true升序/false降序],<可选参数>[标签ID/标签本身Object])
var OrderBy = function () {
    var arr = arguments[0];//数据源
    var sortWord = arguments[1];//排序字段
    var sortRule = arguments[2] == null ? true : arguments[2];//排序规则 true升序/false降序
    if (arr instanceof Array) {//String、Number、Object数组
        switch (String(typeof (arr[0])).toLocaleLowerCase()) {
            case 'object':
                if (sortRule) {
                    arr.sort(function (a, b) { return a[sortWord] - b[sortWord]; });
                }
                else {
                    arr.sort(function (a, b) { return b[sortWord] - a[sortWord]; });
                }
                break;
            case 'number':
                if (sortRule) {
                    arr.sort(function (a, b) { return a - b; });
                }
                else {
                    arr.sort(function (a, b) { return b - a; });
                }
                break;
            case 'string':
                if (sortRule) {
                    arr.sort(function (a, b) { return a.localeCompare(b); });
                }
                else {
                    arr.sort(function (a, b) { return b.localeCompare(a); });
                }
                break;
            default:
                arr.sort();
                break;

        }
        return arr;
    }
    else {
        if (arguments[3]) {
            var Dom = DomTag.call(this, arguments[3]);
            var _arr = new Array();
            for (var i = 0; i < arr.length; i++) {
                _arr.push(arr[i]);
            }
            if (sortRule) {
                _arr.sort(function (a, b) { return a.getAttribute(sortWord) - b.getAttribute(sortWord); });
            }
            else {
                _arr.sort(function (a, b) { return b.getAttribute(sortWord) - a.getAttribute(sortWord); });
            }
            for (var j = 0; j < _arr.length; j++) {
                Dom.appendChild(_arr[j]);
            }
        }
    }
};
//参数处理      ParameterInit([Object],[Object])([默认对象],[新对象])
var ParameterInit = function () {
    var d = arguments[0];//默认
    var n = arguments[1];
    for (var i in d) {
        if (!n.hasOwnProperty(i)) {
            n[i] = d[i];
        }
    }
};
//页面跳转
var Redirect = function () {
    var url = arguments[0];
    var params = arguments[1];
    var finsh = arguments[2] ? arguments[2] : 0;
    if (isAndroid || isiOS) {
        if (!String(params).isNullOrEmpty()) {
            params = "1&" + params;
        } else {
            params = "1";
        }
        Andoggy.pushController(GetUrl + url, params, false, "1", "", finsh);
    }
    else {
        if (!String(params).isNullOrEmpty()) {
            window.location.href = url + "?" + params;
        } else {
            window.location.href = url;
        }
    }
};
//正则表达式验证
var RegPattern = function () {
    var params = arguments;//所有参数
    var p = params[0];
    var v = null;//值
    var r = null;//正则字符串
    var m = null;//错误提示
    var b = false;
    if (typeof (p) == "string") {//判断是字符串还是dom对象
        v = p;
        r = params[1];
        m = params[2];
    }
    else {
        v = p.value || p.innerText;
        r = p.getAttribute("yi-pattern");
        m = p.getAttribute("yi-error");
        b = true;
    }
    v = String(v).Trim();
    if (v.isNullOrEmpty()) {
        return false;
    }
    if (String(r).isNullOrEmpty()) {
        return false;
    }
    var reg = new RegExp(r);
    if (!reg.test(v)) {
        if (b) {
            p.setAttribute("yi-validate", false);
        }
        Message.Alert(m);
        return false;
    }
    else {
        if (b) {
            p.setAttribute("yi-validate", true);
            if (!String(p.getAttribute("yi-event")).isNullOrEmpty()) {
                Function(p.getAttribute("yi-event") + "('" + v + "')")();
            }
        }
        return true;
    }
};
//删除class   RemoveClass([String/Object],[String])([标签ID/标签本身Object],[Class名])
var RemoveClass = function () {
    var o = DomTag.call(this, arguments[0]);
    var cn = arguments[1];
    var re = new RegExp("(\\s*|^)" + cn + "\\b", "g");
    var sName = o.className;
    o.className = sName.replace(re, "");
};
//密码明密文切换
var ShowPwd = function () {
    var _this = arguments[0];
    var nl = _this.parentNode.getElementsByTagName("input");
    if (_this.className.indexOf('ion-ios-eye-outline') > -1) {
        _this.className = _this.className.replace("ion-ios-eye-outline", "ion-ios-eye");
        AddClass(nl[1], "hide");
        nl[0].value = nl[1].value;
        RemoveClass(nl[0], "hide");
    }
    else {
        _this.className = _this.className.replace("ion-ios-eye", "ion-ios-eye-outline");
        AddClass(nl[0], "hide");
        nl[1].value = nl[0].value;
        RemoveClass(nl[1], "hide");
    }
};
//显示大图
var ShowImg = function () {
    var startX = 0, endX = 0, thisw = 0, thish = 0, baseScale = 0;// parseFloat(originalWidth/originalHeight),
    var _ip = DomTag("imgExplorer");
    if (_ip == null) {
        _ip = CreateDom("div");
        _ip.id = "imgExplorer";
        _ip.className = "hide";
        document.body.appendChild(_ip);
    }
    _ip.innerHTML = "";
    _ip.appendChild(CreateDom("span"));
    var _img = CreateDom("img");
    _img.src = arguments[0];
    _img.onload = function () {
        thisw = this.width;
        thish = this.height;
        baseScale = parseFloat(thisw / thish);
        RemoveClass(this.parentNode, "hide");
        this.style.width = thisw + "px";
        this.style.height = thish + "px";
        if (thisw < Pixel.W) {
            this.style.marginLeft = (Pixel.W - thisw) / 2 + "px";
        }
        else {
            this.style.width = Pixel.W + "px";
            this.style.height = Pixel.W / baseScale + "px";
        }
        if (thish < Pixel.H) {
            this.style.marginTop = (Pixel.H - thish) / 2 + "px";
        }
    }
    _ip.addEventListener("touchstart", function (e) {
        var e = e || window.event;
        var targetlen = e.targetTouches;
        if (targetlen.length > 1) {
            startX = Math.abs(targetlen[0].pageX - targetlen[1].pageX);
        }
    });
    _ip.addEventListener("touchmove", function (e) {
        var e = e || window.event;
        var targetlen = e.targetTouches;
        if (targetlen.length > 1) {
            endX = Math.abs(targetlen[0].pageX - targetlen[1].pageX);
            var scale = endX - startX;
            Children(this, "img")[0].style.width = thisw + scale + "px";
            Children(this, "img")[0].style.height = (thisw + scale) / baseScale + "px";
            if (thisw + scale < Pixel.W) {
                Children(this, "img")[0].style.marginLeft = (Pixel.W - thisw - scale) / 2 + "px";
            } else {
                Children(this, "img")[0].style.marginLeft = "0px";
            }
            if ((thisw + scale) / baseScale < Pixel.H) {
                Children(this, "img")[0].style.marginTop = (Pixel.H - ((thisw + scale) / baseScale)) / 2 + "px";
            }
            else {
                Children(this, "img")[0].style.marginTop = "0px";
            }
        }
    });
    _ip.addEventListener("touchend", function () {
        thisw = Children(this, "img")[0].offsetWidth;
    });
    _ip.onclick = function () {
        AddClass(this, "hide");
    }
    _ip.appendChild(_img);

};
//设置值
var SetVal = function () {
    var dom = arguments[0];
    var val = arguments[1];
    if (typeof (dom) == "string") {
        id = val;
        return;
    }
    switch (dom.tagName.toLocaleLowerCase()) {
        case 'img':
            GetImages(dom, val);
            break;
        case 'input':
            if (dom.type == 'date') {
                String(val).ToDate().ToString("yyyy-MM-dd");
                return;
            }
            dom.value = val;
            break;
        case 'select':
            var n = dom.options;
            for (var i = 0; i < n.length; i++) {
                if (n[i].innerText == val) {
                    n[i].selected;
                }
            }
            break;
        default:
            dom.innerText = val;
            break;

    }
};
//触摸删除事件
var TouchInit = function () {
    var nl = DomTag("[yi-touth='true']");
    for (var i = 0; i < nl.length; i++) {
        nl[i].addEventListener('touchstart',
        function (event) {
            var touch = event.touches[0];
            this.setAttribute("data-star", touch.pageX);
            this.setAttribute("data-end", touch.pageX);
        });
        nl[i].addEventListener('touchmove',
        function (event) {
            this.setAttribute("data-end", event.targetTouches[0].pageX);
        });
        nl[i].addEventListener('touchend',
        function (event) {
            var star = this.getAttribute("data-star");
            var end = this.getAttribute("data-end");
            if (star - end > 50) {
                AddClass(this, "cur");
                AddClass(this.getElementsByTagName("i")[0], "cur");
            }
            if (end - star > 50) {
                RemoveClass(this, "cur");
                RemoveClass(this.getElementsByTagName("i")[0], "cur");
            }
        });
    }
};
//本地账号密码尝试登陆
var TryLogin = function () {
    if (!String(mobilePhone).isNullOrEmpty()) {
        if (!String(password).isNullOrEmpty()) {
            //登陆方法
            Login({
                userName: mobilePhone, password: password
            });
            return;
        };
    };
    Redirect('/views/ydj/mobile/isp/App/login.html');
};
//WebSQL
var WebSql = function () {
    var DB_Name = arguments[0] == null ? 'WebSql' : arguments[0];//数据库名称
    var DB_Des = '\u672c\u5730\u6570\u636e\u5b58\u50a8';//数据库描述
    var DB_Size = arguments[1] == null ? 2 * 1024 : parseInt(arguments[1]);//数据库大小
    this.DB = openDatabase(DB_Name, '1.0', DB_Des, DB_Size);//数据库对象
};

/*prototype Start*/
//移除前后空格   String.Trim()
String.prototype.Trim = function () {
    return this.replace(/(^\s*)|(\s*$)/g, "");
};
//判断字符串为空或者Null   String.isNullOrEmpty()
String.prototype.isNullOrEmpty = function () {
    if (this.replace(/\s/g, "").length == 0 || this.Trim().toLocaleLowerCase() === 'null' || this.Trim().toLocaleLowerCase() === 'undefined') {
        return true;
    }
    return false;
};
//Null替换为空   String.NullReplaceEmpty()
String.prototype.NullReplaceEmpty = function () {
    if (this.Trim().toLocaleLowerCase() === 'null' || this.Trim().toLocaleLowerCase() === 'undefined') {
        return "";
    }
    return this;
};
//判断中间是否含有   String.Contains([String])([包含的字符串])
String.prototype.Contains = function () {
    if (this.indexOf(arguments[0]) != -1) {
        return true;
    }
    return false;
};
//String转bool   String.Boolean()
String.prototype.Boolean = function () {
    if (this.toLocaleLowerCase() == 'true') {
        return true;
    }
    return false;
};
//String转Date   String.ToDate()
String.prototype.ToDate = function () {
    if (this.toLocaleLowerCase().indexOf("date") != -1) {
        return eval("new " + this.replace(RegExp("/", "g"), ""));
    }
    var _val = this.replace(new RegExp("-", 'g'), '/');
    return new Date(_val);
};
//String转数组   String.ToArray([String])(<可选参数>[分割字符])
String.prototype.ToArray = function () {
    return this.split(arguments[0] === undefined ? ',' : arguments[0]);
};
//格式填充   String.Format([Array]/[String],[String]......)([数组]/[替换的字符串],<可选参数>[替换的字符串]......)
String.prototype.Format = function () {
    if (arguments.length == 0) {
        return this;
    };
    var nl = arguments[0] instanceof Array ? arguments[0] : arguments;
    for (var s = this, i = 0; i < nl.length; i++) {
        s = s.replace(new RegExp("\\{" + i + "\\}", "g"), nl[i]);
    }
    return s;
};
//创建（追加）Dom结构   String.CreateHtml([Object/String])(<可选参数>[追加的HTMLDom或DomID])
String.prototype.CreateHtml = function () {
    var isAdd = false;
    var dom = null;
    var TagName = null;
    var cd = null;
    if (arguments[0]) {
        isAdd = true;
        dom = typeof (arguments[0]) == 'string' ? document.getElementById(arguments[0]) : arguments[0];
        TagName = dom.tagName.toLocaleLowerCase();
    }
    if (this.indexOf('<tr') == 0) {
        if (IE_BrowserVersion == 10) {
            cd = document.createElement('table');
            cd.innerHTML = this;
            if (isAdd) {
                dom.appendChild(cd.firstChild.firstChild);
            } else {
                return cd.firstChild.firstChild;
            }
        }
        else {
            var tr = document.createElement("tr");
            var re = /<td[^>]*>(.*)<\/td>/;
            var arr = this.match(new RegExp(/<td[^>]*>(.*?)<\/td>/g));
            for (var i = 0; i < arr.length; i++) {
                var td = document.createElement("td");
                td.innerHTML = arr[i].substring(arr[i].indexOf('>') + 1, arr[i].length - 5);
                tr.appendChild(td);
            }
            if (isAdd) {
                if (TagName === 'table') {
                    var tbody = document.createElement("tbody");
                    tbody.appendChild(tr);
                    dom.appendChild(tbody);
                }
                else {
                    dom.appendChild(tr);
                }
            } else {
                if (TagName === 'table') {
                    var tbody = document.createElement("tbody");
                    tbody.appendChild(tr);
                    return tbody;
                }
                else {
                    return tr;
                }
            }
        }
    }
    else {
        var _temp = document.createElement(TagName);
        _temp.innerHTML = this;
        if (isAdd) {
            while (_temp.childNodes.length > 0) {
                dom.appendChild(_temp.childNodes[0]);
            }
        }
        else {
            return _temp.childNodes;
        }
    }
};
//地址栏参数(多段字符串)转Json对象   String.ToJSON([String],[String])(<可选参数>[分割多个键值对],<可选参数>[分割键和值])
String.prototype.ToJSON = function () {
    if (this == null) {
        return {};
    }
    var str = this;
    var format = '{';
    var arr = str.split(arguments[0] === undefined ? "&" : arguments[0]);
    for (var i = 0; i < arr.length; i++) {
        format += '"' + arr[i].split(arguments[1] === undefined ? "=" : arguments[1])[0] + '":"' + arr[i].split(arguments[1] === undefined ? "=" : arguments[1])[1] + '",';
    }
    if (arr.length > 0) {
        format = format.substr(0, format.length - 1);
    }
    format += '}';
    return JSON.parse(format);
};
//Json字符串转地址栏参数(多段字符串)   String.JsonStrToUrlParams()
String.prototype.JsonStrToUrlParams = function () {
    var J = JSON.parse(this.replace(new RegExp("'", "g"), '"'));
    if (typeof (J) != "object") {
        return;
    }
    var _arr = [];
    for (var i in J) {
        _arr.push(i + "=" + J[i]);
    }
    return _arr.join("&");
}
//补零   String.FillZero()
String.prototype.FillZero = function () {
    return parseInt(this) > 9 ? this : "0" + this;
};
//字符串转16进制字符串   String.ToHex()
String.prototype.ToHex = function () {
    var Arr = new Array();
    for (var i = 0; i < this.length; i++) {
        Arr.push(this[i].charCodeAt(0).toString(16));
    }
    return Arr.join(' ');
};
//16进制字符串转字符串   String.HexToStr()
String.prototype.HexToStr = function () {
    var Arr = new Array();
    var _Str = this.replace(/\s/g, "");
    while (_Str.length >= 2) {
        Arr.push(String.fromCharCode(parseInt(_Str.substr(0, 2), 16)));
        _Str = _Str.substr(2, _Str.length);
    }
    return Arr.join('');
};
//时间格式化成String   Date.ToString([String],[Boolean])([格式字符串],<可选参数>[符合初始日期替换空,应赋值为True])
Date.prototype.ToString = function () {
    var _this = this;
    var ref = null;
    var BackEmpty = false;
    var Month = ["\u4e00", "\u4e8c", "\u4e09", "\u56db", "\u4e94", "\u516d", "\u4e03", "\u516b", "\u4e5d", "\u5341", "\u5341\u4e00", "\u5341\u4e8c"];
    var _year = _this.getFullYear();
    var _month = String(_this.getMonth() + 1).FillZero();
    var _day = String(_this.getDate()).FillZero();
    var _reg = _year + "-" + _month + "-" + _day;
    if (arguments[0]) {
        ref = arguments[0];
        ref = ref.replace('yyyy', _year);
        ref = ref.replace('MM', _month);
        ref = ref.replace('Mchina', Month[parseInt(_month) - 1]);
        ref = ref.replace('dd', _day);
        ref = ref.replace('HH', String(_this.getHours()).FillZero());
        ref = ref.replace('mm', String(_this.getMinutes()).FillZero());
        ref = ref.replace('ss', String(_this.getSeconds()).FillZero());
    }
    else {
        ref = _reg;
    }
    BackEmpty = (_reg === '1901-01-01' || _reg === '0001-01-01' || _reg === '2001-01-01') ? true : false;
    if (String(arguments[1]).Boolean() && BackEmpty) {
        return "";
    }
    return ref;
};
/*Function prototype */
WebSql.prototype = {
    //创建表   Create([String],[Array])([表名],[字段集合])
    Create: function () {
        var db = this.DB;
        var tname = arguments[0];
        var tword = arguments[1];
        if (db != null) {
            db.transaction(function (tx) {
                tx.executeSql('create table if not exists ' + tname + '(' + tword.join(',') + ')');
            });
        }
    },
    //删除表   Drop([String])([表名])
    Drop: function () {
        var db = this.DB;
        var tname = arguments[0];
        if (db != null) {
            db.transaction(function (tx) {
                tx.executeSql('drop table ' + tname);
            });
        }
    },
    //插入数据   Insert([String],[Array],[Array])([表名],[字段集合],[对应的字段数据集合])
    Insert: function () {
        var db = this.DB;
        var tname = arguments[0];
        var tword = arguments[1];
        var ins_data = arguments[2];
        var Colume = new Array();
        for (var i = 0; i < tword.length; i++) {
            Colume.push('?');
        }
        if (db != null) {
            db.transaction(function (tx) {
                tx.executeSql('insert into ' + tname + '(' + tword.join(',') + ')' + ' values(' + Colume.join(',') + ')', ins_data, function () { }, function (tx, error) {
                    console.log(error.message);
                });
            });
        }
    },
    //删除数据   Delete([String],[String])([表名],<可选参数>[Where条件])
    Delete: function () {
        var db = this.DB;
        var tname = arguments[0];
        var twhere = arguments[1];
        var ins_data = [];
        if (twhere != null) {
            ins_data.push(twhere.split('=')[1]);
            twhere = twhere.split('=')[0] + "= ? ";
        }
        if (db != null) {
            db.transaction(function (tx) {
                tx.executeSql('delete  from ' + tname + (twhere == null ? '' : ' where ' + twhere), ins_data, function () { }, function (tx, error) { /*console.log(error.message);*/
                });
            });
        }
    },
    //修改数据   Insert([String],[Array],[Array],[String])([表名],[字段集合],[对应的字段数据集合],<可选参数>[Where条件])
    Update: function () {
        var db = this.DB;
        var tname = arguments[0];
        var tword = arguments[1];
        var ins_data = arguments[2];
        var twhere = arguments[3];
        if (twhere != null) {
            ins_data.push(twhere.split('=')[1]);
            twhere = twhere.split('=')[0] + "= ? ";
        }
        var Colume = new Array();
        for (var i = 0; i < tword.length; i++) {
            Colume.push(tword[i] + '= ? ');
        }
        if (db != null) {
            db.transaction(function (tx) {
                tx.executeSql('update  ' + tname + ' set ' + Colume.join(',') + (twhere == null ? '' : ' where ' + twhere), ins_data, null, function (tx, error) {
                    console.log(error.message);
                });
            }, function () {
                console.log(arguments[1]);
            });
        }

    },
    //查询数据返回对象集合   Select([String],[Array],[String],[Function])([表名],[字段集合],[Where条件,无条件时为Null],[回调函数])
    Select: function () {
        var db = this.DB;
        var tname = arguments[0];
        var tword = arguments[1];
        var twhere = arguments[2];
        var ins_data = [];
        if (twhere != null && twhere.indexOf('=') > -1) {
            ins_data.push(twhere.split('=')[1]);
            twhere = twhere.split('=')[0] + "= ? ";
        }
        var CallBack = arguments[3];
        var Error = arguments[4];
        if (db != null) {
            db.transaction(function (tx) {
                tx.executeSql('select ' + tword.join(',') + ' from ' + tname + (twhere == null ? '' : ' where ' + twhere), ins_data, function (tx, results) {
                    var Rows = results.rows;
                    var Len = Rows.length;
                    var Arr = new Array();
                    for (var i = 0; i < Len; i++) {
                        var _item = Rows.item(i);
                        var temp = new Object();
                        for (var j in _item) {
                            temp[j] = _item[j];
                        }
                        Arr.push(temp);
                    }
                    CallBack(Arr);
                }, function () {
                    Error ? Error("WebSql Error:" + arguments[1].message) : null;
                });
            })
        }
    }
};
HtmlSetData.prototype = {
    Format: function () {
        var arg = arguments;
        var attr = arg[0].attr;//当前标签的自定义属性值
        var dom = arg[0].dom;//当前标签
        var data = arg[1];
        var Arr = attr.split(' ');//判断是否是组合字段
        if (Arr.length > 1) {
            var val = [];
            for (var i = 0; i < Arr.length; i++) {
                var dv = data[Arr[i]];
                val.push(this.BackObject(dv));
            }
            this.SetVal(dom, val);
            return;
        }
        this.SetVal(dom, this.BackObject(data[attr]));
    },
    BackObject: function () {
        var arg = arguments[0];
        if (typeof (arg) == 'string') {
            if (arg.indexOf("{") == 0) {
                return JSON.parse(arg);
            }
        }
        return arg;
    },
    SetVal: function () {
        var arg = arguments;
        var dom = arg[0];
        var dompa = String(dom.getAttribute("data-placeholder"));
        var data = arg[1];
        if (dom == null || typeof (dom) == 'string' || String(data).isNullOrEmpty()) {
            return;
        }
        if (typeof (data) == "number" || typeof (data) == "int") {
            dom.innerText = data;
        }
        if (typeof (data) == "string") {
            switch (dom.tagName.toLocaleLowerCase()) {
                case 'img':
                    GetImages(dom, data);
                    break;
                case 'input':
                    if (dom.type == 'date' || String(dom.getAttribute("data-type")).NullReplaceEmpty() == "date") {
                        data = String(data).ToDate().ToString("yyyy-MM-dd");
                    }
                    dom.value = data;
                    break;
                default:
                    dom.innerText = data;
                    break
            }
        }
        else if (data instanceof Array) {
            var val = [];
            for (var i = 0; i < data.length; i++) {
                val.push(data[i]["fname"]);
            }
            var s = val.join(' ');
            if (s.isNullOrEmpty()) {
                s = dompa.isNullOrEmpty() ? '' : dompa;
            }
            switch (dom.tagName.toLocaleLowerCase()) {
                case 'input':
                    dom.value = s;
                    break;
                default:
                    dom.innerText = s;
                    break
            }
        }
        else if (typeof (data) == "object") {
            if (dom.tagName.toLocaleLowerCase() == "img") {
                if (!String(data.id).isNullOrEmpty()) {
                    GetImages(dom, data.id);
                }
                return;
            }
            if (data["id"] == null) {
                return;
            }
            var s = String(data["fname"]).isNullOrEmpty() ? String(data["name"]).isNullOrEmpty() ? "" : data["name"] : data["fname"];
            if (s.isNullOrEmpty()) {

                s = dompa.isNullOrEmpty() ? "" : dompa;
            }
            var v = data["id"].split(',');
            switch (dom.tagName.toLocaleLowerCase()) {
                case 'input':
                    dom.value = s;
                    break;
                case 'select':
                    if (!String(dom.getAttribute("data-ch_event")).isNullOrEmpty()) {
                        GetEnum(FromId, dom.getAttribute("data-bind"), function () {
                            dom.innerHTML = "";
                            var _nl = arguments[0];

                            var op = CreateDom("option");
                            op.innerText = "请选择";
                            op.value = "0";
                            op.disabled = true;
                            op.selected = "selected";
                            //循环数据
                            for (var i = 0; i < _nl.length; i++) {
                                var op = CreateDom("option");
                                op.innerText = _nl[i].name;
                                if (v.indexOf(_nl[i].id) > -1) {//ES5.0
                                    op.selected = "selected";
                                }
                                op.value = _nl[i].id;
                                dom.appendChild(op);
                            }

                            dom.onchange = function () {
                                new Function(dom.getAttribute("data-ch_event") + "('" + this.value + "','" + this.options[this.selectedIndex].text + "')")();
                            }
                        });
                    }
                    break;
                default:
                    if (!String(data.name).isNullOrEmpty()) {
                        dom.innerText = data.name;
                        return;
                    } else if (!String(data.fname).isNullOrEmpty()) {
                        dom.innerText = data.fname;
                        return;
                    }
                    dom.innerText = s;
                    break
            }
        }
    },
    Init: function () {
        var Data = arguments[0];//数据源
        var DomColl = GetDomByAttr('data-bind');//所有
        if (Data == null) {
            return;
        }
        this.id = Data['id'];
        for (var i = 0; i < DomColl.length; i++) {
            this.Format(DomColl[i], Data);
        }
    }
};
/*prototype End*/
/**/
(function () {
    //input hover 及oninput
    var nl = DomTag('[yi-hover="true"]');
    for (var i = 0; i < nl.length; i++) {
        (function (_this) {
            var _clear = _this.parentNode.getElementsByTagName("i")[0];
            _clear.onclick = function () {
                _this.value = "";
            }
        })(nl[i]);
        nl[i].oninput = function () {
            var _this = this;
            MaxLength(_this);
            ClearStatus(_this);
        };
        nl[i].onblur = function () {
            var _clear = this.parentNode.getElementsByTagName("i")[0];
            setTimeout(function () {
                AddClass(_clear, "hide");
            }, 10);
            RegPattern(this);
        };
        nl[i].onfocus = function () {
            var _this = this;
            setTimeout(function () {
                ClearStatus(_this);
            }, 10);
        };
    };
    //Tab切换
    nl = DomTag('[data-tab="tab-content"]');
    for (var i = 0; i < nl.length; i++) {
        var _tcoll = Children(nl[i]);
        var length = _tcoll.length;
        var _titem = Children(nl[i].parentNode, "dd");
        var _tabitem = [];
        for (var j = 0; j < _titem.length; j++) {
            if (_titem[j].getAttribute("tab-content")) {
                _tabitem.push(_titem[j]);
            }
        }
        for (var j = 0; j < _tabitem.length; j++) {
            (function (_j, _len) {
                _tcoll[j].onclick = function () {
                    for (var k = 0; k < _len; k++) {
                        if (k == _j) {
                            AddClass(_tcoll[k], "active");
                            RemoveClass(_tabitem[k], "hide");
                        }
                        else {
                            RemoveClass(_tcoll[k], "active");
                            AddClass(_tabitem[k], "hide");
                        }
                    }
                };
            })(j, _tabitem.length);
        }
    }
    //执行提交验证事件
    var _button = document.getElementsByTagName("button");
    for (var i = 0; i < _button.length; i++) {
        var _e = _button[i].getAttribute("yi-event");
        if (_e) {
            _button[i].onclick = function () {
                var _this = this;
                var attr = _this.getAttribute("yi-name");
                var JsonStr = "{";
                var _input = DomTag('[yi-name="' + attr + '"]')
                for (var o = 0; o < _input.length - 1; o++) {
                    var _dom = _input[o];
                    if (_dom.getAttribute("yi-name") == attr) {
                        if (_dom.getAttribute("yi-pattern")) {
                            if (!String(_dom.getAttribute("yi-validate")).Boolean()) {
                                Message.Alert(_dom.getAttribute("yi-error"));
                                return;
                            }
                        }
                        if (_dom.className.indexOf("hide") > -1) {
                            _dom.value = _dom.parentNode.getElementsByTagName("input")[1].value;
                        }
                        JsonStr += "\"" + _dom.name + "\":\"" + _dom.value + "\",";
                    }
                }
                if (JsonStr.length > 0) {
                    JsonStr = JsonStr.substr(0, JsonStr.length - 1);
                }
                JsonStr += "}";
                Function(_this.getAttribute("yi-event") + "(" + JsonStr + ")")();
            }
        }
    }
    //页面滚动条
    var Scroll = DomTag('Scroll');
    if (Scroll) {
        var calc = 44;
        if (!String(Scroll.getAttribute("data-calc")).isNullOrEmpty()) {
            calc = parseInt(Scroll.getAttribute("data-calc"));
        }
        Scroll.style.height = Pixel.H - calc + "px";
    }
    //最大长度
    var MaxLength = function () {
        var arg = arguments[0];
        var max = arg.getAttribute("maxlength");
        if (max) {
            if (arg.value.length > parseInt(max)) {
                arg.value = arg.value.slice(0, parseInt(max));
            }
        }
    };
    //清除按钮状态
    var ClearStatus = function () {
        var arg = arguments[0];
        var _clear = arg.parentNode.getElementsByTagName("i")[0];
        if (arg.value.length > 0) {
            RemoveClass(_clear, "hide");
        }
        else {
            AddClass(_clear, "hide");
        }
    };
})();


function numbernine() {
    var str = arguments[0];
    if (isNaN(parseInt(str))) {
        return "0";
    }
    return parseInt(str) > 99 ? "99<sup>+</sup>" : str;
}