/*
 * description：预留单业务控制插件
 * author：
 * create date：
 * modify by：宋纪强
 * modify date：2018-12-19
 * remark:
 *@ sourceURL=/fw/js/ydj/stk/stk_reservebill.js
*/
; (function () {
    var stk_reservebill = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.entryId = 'fentity';

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

            //根据预留明细控制预留对象是否可编辑
            var entryData = that.Model.getEntryData({ id: that.entryId });
            if (entryData) {
                var parentIds = [];
                for (var i = 0; i < entryData.length; i++) {
                    if (!$.trim(entryData[i].fparentid)) {
                        parentIds.push(entryData[i].id);
                    }
                }
                for (var i = 0; i < parentIds.length; i++) {
                    var childQtySum = that.getChildQtySum(entryData, parentIds[i]);
                    if (childQtySum !== 0) {
                        that.Model.setEnable({ id: 'freserveobjecttype', value: false });
                        that.Model.setEnable({ id: 'freserveobjectid', value: false });
                        break;
                    }
                }
            }
        };

        //根据父行Id汇总子行数量
        _child.prototype.getChildQtySum = function (entryData, parentId) {
            var childQtySum = 0;
            for (var i = 0; i < entryData.length; i++) {
                var entry = entryData[i];
                if ($.trim(entry.fparentid) === parentId) {
                    var dir = entry.fdirection.id;
                    if (dir === '0') {
                        childQtySum += entry.fbizqty;
                    } else if (dir === '1') {
                        childQtySum -= entry.fbizqty;
                    }
                }
            }
            return childQtySum;
        }

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.entryId:
                    var isEdit = $.trim(that.Model.getValue({ id: 'fsourcenumber' })) === '';
                    e.result = { multiselect: true, allowAdd: isEdit, allowDel: isEdit, allowEdit: isEdit };
                    break;
            }
        };

        //表格控件里新增子行按钮的显示与隐藏（只对树形表格有效）
        _child.prototype.onHideAddChildButton = function (e) {
            e.result = true;
        };

        //是否隐藏新增行按钮，可由业务插件决定
        _child.prototype.onHideAddButton = function (e) {
            this.hideNewOrDelete(e);
        };

        //是否隐藏删除按钮，可由业务插件决定
        _child.prototype.onHideDeleteButton = function (e) {
            this.hideNewOrDelete(e);
        };

        //子明细行是否隐藏新增删除按钮
        _child.prototype.hideNewOrDelete = function (e) {
            var rowData = this.Model.getEntryRowData({ id: e.id, row: e.row });
            if (rowData && $.trim(rowData.fparentid)) {
                e.result = true;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            switch (e.id) {
                case 'freservedateto_d':
                case 'fstorehouseid':
                case 'fstorelocationid':
                case 'fstockstatus':
                    //当前行如果是预留转移，则不允许编辑
                    var rowData = that.Model.getEntryRowData({ id: 'fdetail', row: e.row, prow: e.prow });
                    if (!rowData || !rowData.fopdesc) {
                        return;
                    }

                    var fopdesc = $.trim(rowData.fopdesc.id);
                    if (fopdesc === '1' || fopdesc === '2') {
                        e.result.enabled = false;
                        return;
                    }                     
            }
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbizqty':
                    if ($.trim(e.value)) {
                        var parentQty = yiMath.toNumber(e.value);
                        var entryData = that.Model.getEntryData({ id: that.entryId });
                        var childQtySum = that.getChildQtySum(entryData, e.row);
                        if (parentQty < childQtySum) {
                            e.value = childQtySum;
                            e.result = true;
                            yiDialog.warn('主明细行预留数量 ' + parentQty + '，不能小于子明细行预留数量汇总 ' + childQtySum + '！');
                        }
                    }
                    break;
            }
        };

        ////表格行点击事件
        _child.prototype.onEntryCellClick = function (e) {
            var that = this;
            switch (e.fieldId) {
                case 'fsourcebillno': {
                    var isFiledNoVisible = false;
                    var param = {
                        simpleData: {
                            filed: "fsourcebillno",
                        }
                    };
                    yiAjax.p('/list/stk_reservebill?operationno=Checkfiledvisble', param,
                        function (r) {
                            var data = r.operationResult;
                            if (data.srvData) {
                                isFiledNoVisible = true;
                                yiDialog.warn('您没有该单据的查看权限！');
                            }
                        }, null, null, null, { async: false });

                    if (isFiledNoVisible) {
                        e.result = true;
                    }
                    break;
                }
            }
        };
        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'manualrelease':
                    if (isSuccess) {
                        that.Model.refresh();
                    }
                    break;
            }
        };



        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'save':
                    var isOk = that.Model.validForm();
                    if (!isOk) {
                        e.result = true;
                        return;
                    }
                    var errorMsgs = [];
                    var entry = that.Model.getEntryData({ id: 'fentity' });
                    for (var i = 0; i < entry.length; i++) {
                        // 商品为空，跳过        
                        if (!$.trim(entry[i].fmaterialid.id)) continue;
                        
                        if (entry[i].freservedateto && entry[i].freservedateto < new Date().ToString('yyyy-MM-dd')) {
                            // e.result = true;
                            // yiDialog.warn('【预留日期至】小于系统当前日期，无法预留！');
                            // return;
                            
                            errorMsgs.push('第【'+(i+1)+'】行【预留日期至】小于系统当前日期，无法预留！');
                        }
                    }

                    if (errorMsgs.length > 0) {
                        e.result = true;
                        yiDialog.warn(errorMsgs.join('<br/>'));
                        return;
                    }

                    break;
                case 'checkfiledvisble': {
                    e.result = true;
                    break;
                }

                case 'manualrelease':
                case 'reserveborrow':
                    that.setSelRows(e);
                    break;
            }
        };

        

        // 设置选中行
        _child.prototype.setSelRows = function (e) {
            var that = this;
            // bill：设置选中行
            if (that.formContext.domainType === Consts.domainType.bill) {
                // 设置当前选中行
                var selRows = that.Model.getSelectRows({ id: that.entryId });
                if (selRows && selRows.length > 0) {
                    var selectedRows = [];
                    for (var i = 0; i < selRows.length; i++) {
                        selectedRows.push({
                            PKValue: that.Model.pkid,
                            BillNo: '',
                            EntryPkValue: selRows[i].pkid,
                            EntityKey: that.entryId,

                        });
                    }
                    e.selRows = selectedRows;
                    if (!e.param) {
                        e.param = {};
                    }
                    e.param.selRows = JSON.stringify(selectedRows);
                } else {
                    yiDialog.warn('请选择一条数据后再执行操作！');
                    e.result = true;
                }
            }
            // 其他模式下：判断是否选择多单
            else {
                var selRows = that.Model.getSelectedRowsByOpCode({ opCode: e.opcode });
                var pkids = [];
                if (selRows && selRows.length > 0) {
                    var selectedRows = [];
                    for (var i = 0; i < selRows.length; i++) {
                        var pkid = selRows[i].PKValue;
                        if (pkids.indexOf(pkid) === -1) {
                            pkids.push(pkid);
                        }

                        if (pkids.indexOf(pkid) === 0) {
                            selectedRows.push(selRows[i]);
                        }
                    }

                    if (pkids.length > 1) {
                        yiDialog.warn('不允许多张单据同时操作，只有第一张单据可继续操作！');
                        
                        // 设置第一张单据
                        e.selRows = selectedRows;
                        if (!e.param) {
                            e.param = {};
                        }
                        e.param.selRows = JSON.stringify(selectedRows);
                    }
                } else {
                    yiDialog.warn('请选择一条数据后再执行操作！');
                    e.result = true;
                }
            }
        }



        //表格行删除前事件：设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdetail':
                    var fopdesc = that.Model.getValue({ id: "fopdesc", row: e.row, prow: e.prow });
                    {
                        if (fopdesc && fopdesc == '3') {
                            e.result = true;
                            yiDialog.warn('预留转出或预留转入行，不能进行删除！');
                        }
                    }
                    break;
            }
        };


        //表格行创建后事件
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdetail':
                    var fmaterialid = that.Model.getValue({ id: "fmaterialid_d", row: e.prow });
                    that.Model.setValue({ id: "fmaterialid_d", value: fmaterialid, row: e.row, prow: e.prow });

                    var fbizunitid = that.Model.getValue({ id: "fbizunitid", row: e.prow });
                    that.Model.setValue({ id: "fbizunitid_d", value: fbizunitid, row: e.row, prow: e.prow });

                    var funitid = that.Model.getValue({ id: "funitid", row: e.prow });
                    that.Model.setValue({ id: "funitid_d", value: funitid, row: e.row, prow: e.prow });

                    that.Model.setValue({ id: "ftemptranceid", value: Math.random().toString(36), row: e.row, prow: e.prow });

                    var fdefautdate = that.Model.getValue({ id: "fdefautdate" });
                    if (fdefautdate) {
                        that.Model.setValue({ id: "freservedateto_d", value: fdefautdate, row: e.row, prow: e.prow });
                    }
                    break;
            }
        };

        _child.prototype.onFieldLabelClick = function (e) {
            var that = this;
            switch (e.id) {
                case 'fsourcenumber': {
                    var isFiledNoVisible = false;
                    var param = {
                        simpleData: {
                            filed: "fsourcenumber",
                        }
                    };
                    yiAjax.p('/list/stk_reservebill?operationno=Checkfiledvisble', param,
                        function (r) {
                            var data = r.operationResult;
                            if (data.srvData) {
                                isFiledNoVisible = true;
                                yiDialog.warn('您没有该单据的查看权限！');
                            }
                        }, null, null, null, { async: false });

                    if (isFiledNoVisible) {
                        e.cancel = true;
                    }
                    break;
                }
            }
        }




        return _child;
    })(BillPlugIn);
    window.stk_reservebill = window.stk_reservebill || stk_reservebill;
})();