/*
    其他出库单插件
    <reference path="/fw/js/basepage.js" />
    @ sourceURL=/fw/js/ydj/stk/stk_otherstockout.js
 */
; (function () {
    var stk_otherstockout = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            that.isqueryinventory = false;
            _super.call(this, args);

            that.remindnumbers = "";
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//商品信息
        _child.prototype.notGetFIFOStock = {};//不获取库位推荐

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:
                    break;
            }
        };

        _child.prototype.onBillInitialized = function (args) {
            var that = this;

            var fstatus = that.Model.getValue({ id: 'fstatus' });
            var fcancelstatus = that.Model.getValue({ id: "fcancelstatus" });
            if (fstatus.id == 'D' && fcancelstatus == false) {
                that.Model.setEnable({ id: '#tbDeliveryTask', value: true });
            }
            else {
                that.Model.setEnable({ id: '#tbDeliveryTask', value: false });
            }
            that.setDirectSale();
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    //重新计算表头字段值
                    that.culFbill();
                    break;
            }
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            switch (e.opcode) {
                //标准定制
                case 'showstandardcustom':
                    e.result = true;
                    that.showstandardcustom();
                    break;

                //非标定制
                case 'showunstandardcustom':
                    e.result = true;
                    that.showunstandardcustom();
                    break;
                case 'querybarcode'://条码联查
                    that.querybarcode(e);
                    break;
                case 'saveaudit':
                case 'rejectflow':
                case 'auditflow':
                    e.result = true;
                    var checkorder = that.checkOrder(e.opcode.toLowerCase());
                    if (Consts.isdirectsale && (e.opcode.toLowerCase() == 'saveaudit' || e.opcode.toLowerCase() == 'auditflow')) {
                        yiDialog.c('审核通过后自动同步总部且不可反审核，确定继续审核？', function () {
                            if (checkorder && that.remindnumbers) {
                                yiDialog.d({
                                    id: 'remindnumbers',
                                    type: 1,
                                    resize: false,
                                    maxmin: false,
                                    title: '系统提示',
                                    content: that.remindnumbers,
                                    area: ['400px', '200px'],
                                    btn: ['确定'],
                                    yes: function (index, layero) {
                                        layer.close(index);
                                    }
                                });
                            } else {
                                var param = e.param;
                                param.formId = "stk_otherstockout";
                                that.Model.invokeFormOperation({
                                    id: 'tbSave',
                                    opcode: e.opcode.toLowerCase(),
                                    param: param
                                });
                            }
                        }, function () {
                            e.result = true;
                            return;
                        });
                    }
                    else {
                        if (checkorder && that.remindnumbers) {
                            yiDialog.d({
                                id: 'remindnumbers',
                                type: 1,
                                resize: false,
                                maxmin: false,
                                title: '系统提示',
                                content: that.remindnumbers,
                                area: ['400px', '200px'],
                                btn: ['确定'],
                                yes: function (index, layero) {
                                    layer.close(index);
                                }
                            });
                        } else {
                            var param = e.param;
                            param.formId = "stk_otherstockout";
                            that.Model.invokeFormOperation({
                                id: 'tbSave',
                                opcode: e.opcode.toLowerCase(),
                                param: param
                            });
                        }
                    }
                    break;
            }
        };

        //检查订单是否满足条件
        _child.prototype.checkOrder = function (opname) {
            var that = this;
            var isremind = false;

            var selectedRows;
            if (that.Model.viewModel.domainType == Consts.domainType.bill) {
                selectedRows = [{ pkValue: that.Model.pkid }];
            }
            if (that.Model.viewModel.domainType == Consts.domainType.list) {
                selectedRows = that.Model.getSelectRows();
                if (!selectedRows || selectedRows.length <= 0) {
                    yiDialog.mt({ msg: '请至少选择一条数据。', skinseq: 2 });
                    return;
                }
            }

            var ids = [];
            for (var i = 0; i < selectedRows.length; i++) {
                ids.push(selectedRows[i].pkValue);
            }
            var param = {
                simpleData: {
                    formId: 'stk_otherstockout',
                    Ids: ids.join(","),
                    opname: opname
                }
            };

            yiAjax.p('/bill/stk_otherstockout?operationno=verifyproduct', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (res.isSuccess && srvData) {
                    isremind = true;
                    that.remindnumbers = srvData;
                }
            }, null, null, null, { async: false });
            return isremind;
        };


        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    //商品为空时，不允许编辑
                    productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
                    if (!productId) {
                        e.result.enabled = false;
                        return;
                    }
                    break;
            }
        };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {

                //数量单价的变化，影响金额
                case 'fqty':
                case 'fprice':
                    that.culNum({ name: e.id, rowId: e.row, value: e.value });
                    break;
                //计算总件数
                case 'fpackageqty':
                    that.culFbill();
                    break;
                //计算总立方数
                case 'fcubeqty':
                    that.culFbill();
                    break;
                case 'fmaterialid':
                case 'fstockdeptid':
                    // that.SetDefaultStockInfo(false, e.row)
                    break;
            }

            // 库位推荐
            var isqueryinventory = e && e.ctx && e.ctx.isqueryinventory;//如果是从库存综合查询选中的数据，不需要做库位推荐
            switch (e.id.toLowerCase()) {
                case 'fmaterialid':
                // case 'fattrinfo':
                //case 'funitid':
                //case 'fstockunitid':
                case 'fownertype':
                case 'fownerid':
                case 'fmtono':
                case 'flotno':
                    if (isqueryinventory == false || isqueryinventory == undefined) {
                        that.getFIFOStock(e.row);
                    }
                    if (isqueryinventory == true) {
                        that.isqueryinventory = true;//设置标记，以便从库存综合查询返回填充fqty时不要做库位推荐
                    }
                    break;
                case 'fqty':
                    if (that.isqueryinventory == false && (isqueryinventory == false || isqueryinventory == undefined)) {
                        if (!that.notGetFIFOStock.hasOwnProperty(e.row)) {
                            that.getFIFOStock(e.row);
                        }
                    }
                    break;
                case 'fstorehouseid':
                    if (that.isqueryinventory == false && (isqueryinventory == false || isqueryinventory == undefined)) {
                        // 更改仓库时，需要清空标记，以便修改数量时，触发推荐
                        if (that.notGetFIFOStock.hasOwnProperty(e.row)) {
                            delete that.notGetFIFOStock[e.row];
                        }
                    }
                    break;
                case 'fstorelocationid':
                    if (that.isqueryinventory == false && (isqueryinventory == false || isqueryinventory == undefined)) {
                        // 清空仓位时，需要清空标记，以便修改数量时，触发推荐
                        if (that.notGetFIFOStock.hasOwnProperty(e.row) && e.value.id === '') {
                            delete that.notGetFIFOStock[e.row];
                        }
                    }
                    break;
                case "fstorehouseid_h":
                    var fentity = that.Model.getEntryData({ id: 'fentity' });
                    if (fentity.length == 0) {
                        return;
                    }
                    for (let i = 0; i < fentity.length; i++) {
                        that.Model.setValue({ id: "fstorehouseid", value: e.value.id, row: fentity[i].id });
                        that.Model.setValue({ id: "fstorelocationid", value: "", row: fentity[i].id });//清空仓位
                    }
                    break;
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        //仓位禁止批录，需求#35670 
                        if (e.fieldId.toLowerCase() === 'fstorehouseid') {
                            e.copyFields = ["fstockstatus"];
                            e.clearFields = ["fstorelocationid"];
                        }
                        break;
                }
            }
        };

        // //获取默认的仓库
        // _child.prototype.SetDefaultStockInfo = function (isMatChange, rowIndex) {
        //     var that = this;
        //     var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
        //     if (!deptid) {
        //         //没有设置部门，不需要请求获取数据
        //         return;
        //     }
        //     if (isMatChange) {
        //         var fstorehouseid = that.Model.getSimpleValue({ id: 'fstorehouseid', row: rowIndex });
        //         if (fstorehouseid) {
        //             //修改物料时，如果已经有仓库，不需要再设置默认的
        //             return;
        //         }
        //     }

        //     yiAjax.p('/bill/ydj_storehouse?operationno=getdefaultstockinfo&srcformid=stk_otherstockout&deptid=' + deptid, null,
        //         function (r) {
        //             var data = r.operationResult;
        //             if (data.isSuccess) {
        //                 //库存参数中启用了部门仓库控制
        //                 var stockInfo = data.srvData;
        //                 if (stockInfo) {
        //                     //设置默认仓库
        //                     if (isMatChange) {
        //                         that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: stockInfo.id });
        //                     }
        //                     else {
        //                         var ds = that.Model.getEntryData({ id: 'fentity' });
        //                         for (var i = 0, j = ds.length; i < j; i++) {
        //                             that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: stockInfo.id });
        //                         }
        //                     }
        //                 }
        //                 else {
        //                     //清空仓库
        //                     if (isMatChange) {
        //                         that.Model.setValue({ id: 'fstorehouseid', row: rowIndex, value: "" });
        //                     }
        //                     else {
        //                         var ds = that.Model.getEntryData({ id: 'fentity' });
        //                         for (var i = 0, j = ds.length; i < j; i++) {
        //                             that.Model.setValue({ id: 'fstorehouseid', row: ds[i].id, value: '' });
        //                         }
        //                     }
        //                 }
        //             } else {

        //             }
        //         }, null, null, null, { async: false }
        //     );

        // }

        //重新计算明细
        _child.prototype.culFbill = function (e) {
            var that = this;
            var ds = that.Model.getEntryData({ id: that.fentity });
            var totalpackageQty = 0;
            var totalcubeQty = 0;
            if (ds && ds.length > 0) {
                for (var i = 0, l = ds.length; i < l; i++) {
                    totalpackageQty += yiMath.toNumber(ds[i].fpackageqty);
                    totalcubeQty += yiMath.toNumber(ds[i].fcubeqty);
                }
            }
            that.Model.setValue({ id: 'ftotalpackageqty', value: totalpackageQty });
            that.Model.setValue({ id: 'ftotalcubeqty', value: totalcubeQty });
        }


        //标准定制
        _child.prototype.showstandardcustom = function () {
            var that = this;
            //选中行
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            };
            if (ds) {
                ////选中行
                //判断物料是否启用 选配类别 
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且未勾选非标定制
                if (Isenableselectioncategory && !fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框 
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或勾选了非标定制，不允许标准定制！');
                    return;
                }
            }
        };

        //非标定制
        _child.prototype.showunstandardcustom = function () {
            var that = this;
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行非标定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('非标定制不支持多选！');
                return;
            }
            if (ds) {
                //判断选中行物料是否启用 选配类别
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //允许选配
                var fispresetprop = ds[0].data.fispresetprop;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且 勾选非标定制
                if (fispresetprop && fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或未勾选非标定制，不允许非标准定制！');
                    return;
                }
            }
        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    that.aftersetauxProp(e);
                    that.getFIFOStock(e.row);
                    break;
            }
        };

        //标准定制、非标定制后根据属性、属性值 匹配带出配件
        _child.prototype.aftersetauxProp = function (e) {
            var that = this;
            if (!e.value.fentity) {
                return;
            }
            var attrinfo = e.value;
            var attrinfoNo = attrinfo.fentity.filter(o => o.fvaluename == "无");
            that.attrinfoNew = [];
            if (attrinfoNo) {
                attrinfoNo.forEach(o => { that.attrinfoNew.push(o.fauxpropid.fname) });
            }
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            var productId = $.trim(rowData.fmaterialid && rowData.fmaterialid.id);
            if (attrinfo.fentity && attrinfo.fentity.length > 0) {
                var isCheckCustom = false;
                var ds = that.Model.getSelectRows({ id: that.fentity });
                if (ds.length > 0) {
                    var rowid = ds[0].data.id;
                    isCheckCustom = rowid == rowData.id;
                }
                if (isCheckCustom || rowData.fsuitcombnumber) isCheckCustom = true;

                if (isCheckCustom) {
                    that.Model.invokeFormOperation({
                        id: 'doaddparts_custom',
                        opcode: 'doaddparts_custom',
                        param: {
                            formId: 'stk_inventoryverify',
                            rows: JSON.stringify(attrinfo),
                            currentrow: JSON.stringify(rowData),
                            productId: productId,
                            rowId: e.row,
                            domainType: 'dynamic'
                        }
                    });
                }
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'doaddparts_custom':
                    if (isSuccess && srvData) {
                        //如果匹配到标准品商品id则直接更新商品
                        if (srvData.standardId && srvData.standardId != undefined) {
                            //// 触发获取商品其他信息
                            var row = that.Model.getEntryRowData({ id: that.fentity, row: srvData.rowId });
                            that.Model.setValue({ id: 'fmaterialid', row: srvData.rowId, value: srvData.standardId });
                            that.Model.setValue({ id: 'fdostandard', row: srvData.rowId, value: 1 });
                        }
                    }
                    break;
                case 'getfifostock':
                    that.fillFIFOStock(srvData);
                    break;
                //case 'isexsitbarcode':
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息!");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;
            }
        };

        _child.prototype.culNum = function (opt) {
            var that = this;
            //行对象
            var row = that.Model.getEntryRowData({ id: that.fentity, row: opt.rowId });
            if (!row) return;
            //数量
            var qty = yiMath.toNumber(row.fqty);
            //单价
            var price = yiMath.toNumber(row.fprice);
            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;
            //金额设值
            that.Model.setValue({ id: 'famount', row: opt.rowId, value: amount });
        };


        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fstorehouseid_h':
                    if (Consts.isdirectsale) {
                        e.result.filterString = " fwarehousetype in ('warehouse_02','warehouse_01','warehouse_04') ";
                    }
                    break;
                //仓库：按库存参数中控制可选仓库范围
                case 'fstorehouseid':
                    var deptid = that.Model.getSimpleValue({ id: 'fstockdeptid' });
                    var srcPara = {
                        formid: 'stk_otherstockout',
                        deptid: deptid,
                    };
                    if (Consts.isdirectsale) {
                        e.result.filterString = " fwarehousetype in ('warehouse_02','warehouse_01') ";
                    }
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
                //商品基础资料
                case 'fmaterialid':
                    var fbilltypeid = that.Model.getValue({ "id": "fbilltype" })
                    var billtypeNo = fbilltypeid ? fbilltypeid.fnumber : "";
                    var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
                    var srcPara = {
                        billtypeid: fbilltypeid,
                        billtypeNo: billtypeNo,
                        billtypeName: billtypeName
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
            }
        };

        //行选中事件
        _child.prototype.onSelectedRowsChanged = function (e) {
            var that = this;
            if (!e.id) return;

            switch (e.id.toLowerCase()) {
                case "list":
                    if (!e.data && e.data.length <= 0) return;

                    var filterdata = e.data.filter(x => x.fstatus == 'D' && x.fcancelstatus == '0');
                    if (filterdata.length > 0 && filterdata.length == e.data.length) {
                        that.Model.setEnable({ id: '#tbDeliveryTask', value: true });
                    }
                    else {
                        that.Model.setEnable({ id: '#tbDeliveryTask', value: false });
                    }
                    break;
            }
        };

        //条码联查
        _child.prototype.querybarcode = function (e) {
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: that.fentity });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('请先选中行再操作!');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查!');
            //    return;
            //}
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].data.fmaterialid.id });
            }
            //JSON.stringify(datas)
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'stk_otherstockout',
                    'fsourcenumber': fbillno,
                    'fmaterialid': JSON.stringify(datas),
                }
            });
        };


        //获取先进先出库位
        _child.prototype.getFIFOStock = function (rowId) {
            if (!rowId) {
                return;
            }
            var that = this;
            var vm = that.Model.viewModel;
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: rowId });
            if (!rowData) {
                return;
            }

            if (rowData.fbizqty <= 0) {
                return;
            }

            var fisnofifostock = rowData.fisnofifostock;
            if (fisnofifostock) {
                return;
            }

            // 设置了仓库和仓位的，不触发获取
            if (rowData.fstorehouseid && rowData.fstorehouseid.id.trim() && rowData.fstorelocationid && rowData.fstorelocationid.id.trim()) {
                return;
            }

            that.Model.invokeFormOperation({
                id: 'getfifostock',
                opcode: 'getfifostock',
                param: {
                    formId: vm.formId,
                    rowData: JSON.stringify(rowData),
                    rowId: rowId
                }
            });

            if (that.notGetFIFOStock.hasOwnProperty(rowId)) {
                delete that.notGetFIFOStock[rowId];
            }
        }

        //填充推荐库位
        _child.prototype.fillFIFOStock = function (srvData) {
            var that = this;
            if (srvData && srvData.rowDatas) {

                var rowDatas = srvData.rowDatas;
                var firstRowData = rowDatas[0];
                var rowId = srvData.rowId;

                //先清理标记位，避免死循环
                that.notGetFIFOStock[rowId] = "";

                that.Model.setValue({ id: 'fstorehouseid', value: firstRowData.fstorehouseid, row: rowId });
                that.Model.setValue({ id: 'fstorelocationid', value: firstRowData.fstorelocationid, row: rowId });
                that.Model.setValue({ id: 'fstockstatus', value: firstRowData.fstockstatus, row: rowId });
                that.Model.setValue({ id: 'fstockqty', value: firstRowData.fstockqty, row: rowId });
                that.Model.setValue({ id: 'fqty', value: firstRowData.fqty, row: rowId });

                if (rowDatas.length > 1) {
                    for (var i = rowDatas.length - 1; i > 0; i--) {
                        var rowData = rowDatas[i];
                        var id = that.Model.addRow({ id: that.fentity, row: rowId, data: rowData });

                        that.notGetFIFOStock[id] = "";
                    }
                }
            }
        }



        // 库存查询设置了标准字段后的回调，每行返回了rownumber
        _child.prototype.queryInventoryCallback = function (e) {
            var srvData = e.srvData;
            var that = this;

            //恢复标记位
            that.isqueryinventory = false;

            if (!srvData || !srvData.length) return;
        }

        //设置必录标签
        _child.prototype.setDirectSale = function (e) {
            var that = this;
            if (Consts.isdirectsale) {
                that.Model.setVisible({ id: '.store', value: true }); //显示门店
                that.Model.setVisible({ id: '.zyclass', value: true }); //显示
                setTimeout(function () {
                    that.setFieldMustFlag({ id: "ftype", caption: "出库类型", must: true });
                    //that.setFieldMustFlag({ id: "fstocktype", caption: "出库仓库", must: true });
                    that.setFieldMustFlag1({ id: "fstorehouseid_h", caption: "出库仓库", must: true });
                }, 500);
                that.Model.setEnable({ id: "fstorehouseid", value: false });
            } else {
                that.Model.setVisible({ id: '.store', value: false }); //隐藏门店
                that.Model.setVisible({ id: '.zyclass', value: false }); //显示
            }
        };

        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };

        //设置必录标签
        _child.prototype.setFieldMustFlag1 = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };

        //表单操作前触发
        _child.prototype.onBeforeDoOperation = function (e) {
            var that = this;
            if (Consts.isdirectsale) {
                switch (e.opcode.toLowerCase()) {
                    case 'queryinventory':
                        //debugger;
                        //仓库可用控制
                        //调出仓库
                        var fstorehouseid_h = that.Model.getSimpleValue({ id: 'fstorehouseid_h' });
                        if (fstorehouseid_h != "") {
                            var filterString = "fstorehouseid='" + fstorehouseid_h + "'";
                            e.result = { 'filterString': filterString };
                        }
                        break;
                }
            }
        };

        return _child;
    })(BasePlugIn);
    window.stk_otherstockout = window.stk_otherstockout || stk_otherstockout;
})();