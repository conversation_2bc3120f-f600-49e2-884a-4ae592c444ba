///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/stk_inventorylist.js
*/
; (function () {
    var rpt_stocksummary = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            //所有可用于订单支付的账户信息
            that.accounts = [];
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //表格行双击后事件
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;
            //如果列表页面行双击
            if (e.id === 'list') {
                e.result = true;
                //yiDialog.mt({ msg: '库存余额不允许修改！', skinseq: 1 });

                //调用商品收发明细表
                var invFlexId = '';
                if (e.data && e.data.finvflexid) {
                    invFlexId = e.data.finvflexid;
                }
                that.Model.showListReport({ formId: 'rpt_stockdetail', openStyle: 'modal', param: { invFlexId: invFlexId, forceRecreate: true } });
            }
        };


        return _child;
    })(ListReportPlugin);
    window.rpt_stocksummary = window.rpt_stocksummary || rpt_stocksummary;
})();