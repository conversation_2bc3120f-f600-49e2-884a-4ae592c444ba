///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_purchaseplan_filter.js
*/
; (function () {
    var rpt_purchaseplan_filter = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);


        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
           var myDate = new Date();
            var year = myDate.getFullYear();
            var month = myDate.getMonth() + 1;
            var lastDate = new Date(year, month, 0).getDate();
            month = month < 10 ? '0' + month : month;
            that.Model.setValue({ id: 'fdatefrom', value: [year, month, '01'].join("-") });
            that.Model.setValue({ id: 'fdateto', value: [year, month, lastDate].join("-") });
        };
        
        return _child;
    })(BasePlugIn);
    window.rpt_purchaseplan_filter = window.rpt_purchaseplan_filter || rpt_purchaseplan_filter;
})();