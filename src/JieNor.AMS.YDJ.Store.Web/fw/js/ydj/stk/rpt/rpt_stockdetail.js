///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_stockdetail.js
*/
; (function () {
    var rpt_stockdetail = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //表格单元格点击事件
        _child.prototype.onEntryCellClick = function (e) {
            var that = this;
            if (e.id === 'list' && e.fieldId === 'flinkbillno') {
                e.result = true;
                var res = {
                    formId: e.data.flinkformid,
                    pkids: [e.data.flinkbillinterid],
                    openStyle:Consts.openStyle.modal 
                    
                };
                this.Model.showForm(res);
            }
        };

        //表格单元格格式化事件
        _child.prototype.onFieldValueFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'flinkbillno':
                    e.value = '<span style="color:#529DE3;cursor:pointer;">' + e.value + '</span>';
                    e.cancel = true; //取消平台的默认格式化显示值
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.rpt_stockdetail = window.rpt_stockdetail || rpt_stockdetail;
})();