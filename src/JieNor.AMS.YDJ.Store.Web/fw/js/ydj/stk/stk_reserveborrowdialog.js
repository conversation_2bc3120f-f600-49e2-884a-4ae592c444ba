/**
 * 预留借贷业务插件
 * @ sourceURL=/fw/js/ydj/stk/stk_reserveborrowdialog.js
 */
; (function () {
    var stk_reserveborrowdialog = (function (_super) {
        //构造函数
        var _child = function (args) {
            _super.call(this, args);
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentry';

        //插件初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbizreleaseqty':
                    // 【本次需释放预留数量】不允许大于【预留数量】
                    if ($.trim(e.value)) {
                        var newVal = yiMath.toNumber(e.value);
                        var oldVal = that.Model.getValue({ id: 'fbizreleaseqty', row: e.row });
                        var qty = that.Model.getValue({ id: 'fbizqty', row: e.row });

                        if (newVal < 0) {
                            e.value = oldVal;
                            e.result = true;
                            yiDialog.warn('【本次需释放预留数量】不允许小于零！');
                            return;
                        }

                        if (newVal > qty) {
                            e.value = oldVal;
                            e.result = true;
                            yiDialog.warn('【本次需释放预留数量】不允许大于【预留数量】！');
                            return;
                        }
                    }
                    break;
            }
        };


        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //取消
                case 'tbcancel':
                    that.Model.setReturnData({});
                    break;
                //确定
                case 'tbconfirm':
                    that.confirmRelease(e);
                    break;
            }
        };

        //确定释放
        _child.prototype.confirmRelease = function (e) {
            var that = this;
            var param = {};

            var isOk = that.Model.validForm();
            if (!isOk) {
                e.result = true;
                return;
            }

            param.billData = JSON.stringify(that.Model.uiData);
            that.Model.setReturnData({ param: param });
        };


        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fborroworderid':
                    var rowdata = that.Model.getEntryRowData({ id: 'fentry', row: e.row });

                    e.result.simpleData = {
                        'fmaterialid': rowdata.fmaterialid.id,
                        'fcustomdesc': rowdata.fcustomdesc,
                        'fmtono': rowdata.fmtono,
                        'fattrinfo_e': rowdata.fattrinfo_e.id,
                        'funitid': rowdata.funitid.id,
                        'fentryid': rowdata.fdemandentryid
                    };
                    break;
            }
        };
        

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'g_record': //批录按钮 
                        if (e.fieldId.toLowerCase() === 'fborroworderid') {
                            //取消平台的批录逻辑
                            e.cancel = true;
                        }
                        break;
                }
            }
        };

        return _child;
    })(BasePlugIn);
    window.stk_reserveborrowdialog = window.stk_reserveborrowdialog || stk_reserveborrowdialog;
})();