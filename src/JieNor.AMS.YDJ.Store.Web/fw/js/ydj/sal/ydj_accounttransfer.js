/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/sal/ydj_accounttransfer.js
 */
; (function () {
    var ydj_accounttransfer = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            that.accountFieldKeys = [];
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //初始化页面插件
        _child.prototype.onBillInitialized = function (args) {

        };
  
        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            if (e.id === 'famount') {
                var famount = $.trim(e.value).split('.');
                if (famount && famount.length === 2 && famount[1].length > 2) {
                    e.value = yiMath.toNumber(yiMath.toDecimal(e.value, 2));
                    e.result = true;
                    yiDialog.mt({ msg: '转账金额只能输入两位小数点！', skinseq: 2 });
                }
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                    //收支明细
                case 'incomedisburse':
                    e.result = true;
                    that.Model.showForm({
                        formId: 'coo_incomedisburserptsal',
                        domainType: Consts.domainType.report,
                        cp: {
                            sourceId: that.Model.pkid
                        }
                    });
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            switch (e.id) {
                case 'fcustomerid':
                case 'fdisburseaccount':
                case 'fincomeaccount':
                    var customerId = $.trim(that.Model.getSimpleValue({ id: 'fcustomerid' }));
                    if (customerId) {
                        if (e.id === 'fcustomerid') {
                            that.accountFieldKeys.push('fdisburseaccount', 'fincomeaccount');
                        } else {
                            that.accountFieldKeys.push(e.id);
                        }
                        that.Model.invokeFormOperation({
                            id: 'LoadAccountBalance',
                            opcode: 'LoadAccountBalance',
                            selectedRows: [{ PKValue: customerId }],
                            param: {
                                formId: 'ydj_customer',
                                domainType: Consts.domainType.bill
                            }
                        });
                    } else {
                        that.Model.setValue({ id: 'fdisbursebalance', value: 0 });
                        that.Model.setValue({ id: 'fincomeabalance', value: 0 });
                    }
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'confirm':
                    if (isSuccess) {
                        that.Model.refresh();
                    }
                    break;
                case 'loadaccountbalance':
                    if (that.accountFieldKeys.length > 0 && srvData && srvData.length > 0) {
                        for (var j = 0; j < that.accountFieldKeys.length; j++) {
                            var accountId = that.Model.getSimpleValue({ id: that.accountFieldKeys[j] });
                            if (accountId) {
                                for (var i = 0; i < srvData.length; i++) {
                                    if (srvData[i].accountId === accountId) {
                                        that.Model.setValue({
                                            id: that.accountFieldKeys[j] === 'fdisburseaccount' ? 'fdisbursebalance' : 'fincomeabalance',
                                            value: srvData[i].balance
                                        });
                                        break;
                                    }
                                }
                            } else {
                                that.Model.setValue({
                                    id: that.accountFieldKeys[j] === 'fdisburseaccount' ? 'fdisbursebalance' : 'fincomeabalance',
                                    value: 0
                                });
                            }
                        }
                        that.accountFieldKeys = [];
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_accounttransfer = window.ydj_accounttransfer || ydj_accounttransfer;
})();