/**
 * 购物车
 * @ sourceURL=/fw/js/ydj/sal/sal_shopcart.js
 */
; (function () {
    var sal_shopcart = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //编辑页面初始化事件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

        };

        return _child;
    })(BasePlugIn);
    window.sal_shopcart = sal_shopcart;
})();