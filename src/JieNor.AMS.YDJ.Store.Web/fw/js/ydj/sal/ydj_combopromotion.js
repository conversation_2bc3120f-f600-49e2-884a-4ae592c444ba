///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/fin/ydj_combopromotion.js
*/
; (function () {
    var ydj_combopromotion = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
            
            that.PartListData = [];
            that.FirstDelete = true;
        };
        __extends(_child, _super);

        _child.prototype.productEntryId = 'fproductentry';

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            
            that.initVisible();
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;

            switch (e.id.toLowerCase()) {
                //标准定制
            case 'btnstandardcustom':
                e.result = true;
                that.showstandardcustom(that.productEntryId);
                break;
            }
        };

        //字段值变化时的操作
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;

            that.fieldValueChangedSetVisible(e.id);
            
            switch (e.id.toLowerCase()) {
                case 'fmaterialid':
                    that.getSalPrice([e.row]);
                    //如果是勾选了选配标记的商品，直接根据选配配件映射找到配件商品（无需根据条件）
                    that.TakeTGCpartsByStandard(e);
                    break;
                case 'fattrinfo':
                    that.getSalPrice([e.row]);
                    break;
                case 'fqty':                        
                    that.calculateqtyBypart(e);
                    break;
            }
        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    that.getSalPrice([e.row]);
                    that.aftersetauxProp(e);
                    break;
            }
        };
        
        // // 库存查询选择完数据后触发的事件
        // _child.prototype.queryInventoryCallback = function (e) {
        //     var that = this;

        //     if (e.entityKey !== that.productEntryId) return;

        //     var changedRowIdxObj = e.changedRowIdxObj;
        //     if (!changedRowIdxObj) return;

        //     var rows = [];
        //     for (var row in changedRowIdxObj) {
        //         rows.push(row);
        //     }

        //     that.getSalPrice(rows);
        // };


        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fqty':
                    if ((typeof e.value === 'number' && e.value % 1 !== 0) || e.value <= 0) {
                        var oldValue = that.Model.getValue({ id: "fqty", row: e.row });
                        e.result = true;
                        e.value = oldValue;
                        yiDialog.warn('赠送数量必须为整数且大于0');

                        return;
                    }
                    break;
            }
        }

        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            switch (e.opcode.toLowerCase()) {
                case "publish":
                case "unpublish":
                    {
                        if (!isSuccess) return;
                        var pageId = that.Model.viewModel.pageId;

                        //刷新当前页面
                        var currPage = Index.getPage(pageId);
                        if (currPage) {
                            currPage.refresh({ resetpage: false });
                        }

                        //刷新父页面
                        if (that.Model.viewModel.domainType === Consts.domainType.bill) {
                            if (currPage && currPage.parentPageId) {
                                var listPage = Index.getPage(currPage.parentPageId);
                                if (listPage && $.isFunction(listPage.refresh)) {
                                    listPage.refresh({ resetpage: false });
                                }
                            }
                        }
                    }
                    break;
                case 'getprices':
                    that.getPricesAfterDoOperation(e);
                    break;
                case 'tgcpartbystandard':
                    that.DoTGCparts(e);
                    break;
                case 'doaddparts_custom':
                    that.doaddparts_customAfterDoOperation(e);
                    break;
                //添加配件
                case 'doaddparts':
                    that.doaddpartsAfterDoOperation(e);
                    break;
            }
        }

        //表单元素被单击后
        _child.prototype.onElementClick = function (e) {
            var that = this;
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
            }
        };

        // 字段值变化时设置可见性
        _child.prototype.fieldValueChangedSetVisible = function (fieldId) {
            var that = this;
            switch (fieldId.toLowerCase()) {
                case "fdeptscope":
                    that.initVisible();
                    break;
            }
        }
        

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this; 
            var product = that.Model.getEntryRowData({ id: that.productEntryId, row: e.row });

            // 配件不允许修改
            if (product && product.fpartscombnumber != '' && !product.fiscombmain) {
                //只需要锁定 辅助属性, 销售数量, 销售单位, 基本数量, 基本单位
                switch (e.id.toLowerCase()) {
                    case 'fattrinfo':
                    case 'fmaterialid':
                    case 'funitid':
                    case 'fqty':
                        e.result.enabled = false;
                        return;
                }
            }
        };

        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;
            switch (e.formId) {
                case 'ydj_product':
                    //填充数据。
                    var data = e.data;
                    if (!data || data.length == 0) {
                        return;
                    }
                    var auxEntry = [];
                    var addRowData = {};
                    var partdata;
                    if (that.PartListData.length > 0) {
                        partdata = that.PartListData.find(o => o["fmaterialid"] == data[0].fbillhead_id);
                    }
                    var row = that.Model.addRow({ id: that.productEntryId, row: partdata.id });
                    var parentrow = that.Model.getEntryRowData({ id: that.productEntryId, row: partdata.id });
                    if (partdata) {
                        addRowData = {
                            'fmaterialid': data[0].fbillhead_id,
                            'fqty': partdata.fqty,
                            'funitid': partdata.funitid
                        };
                        that.Model.setValue({ id: 'fmaterialid', row: row, value: data[0].fbillhead_id });
                        that.Model.setValue({ id: 'fpartqty', row: row, value: partdata.fqty });
                        //如果是铁架床则算是自动带出配件。
                        if (partdata.parttype == "铁架床") {
                            that.Model.setValue({ id: 'fisautopartflag', row: row, value: 1 });
                        }
                        if (partdata.attrinfo) {
                            for (var a = 0; a < partdata.attrinfo.length; a++) {
                                auxEntry.push({
                                    fauxpropid: {
                                        id: partdata.attrinfo[a].fauxpropid,
                                        fname: partdata.attrinfo[a].fname,
                                        fnumber: partdata.attrinfo[a].fnumber
                                    },
                                    fvalueid: partdata.attrinfo[a].fvalueid,
                                    fvaluename: partdata.attrinfo[a].fvaluename,
                                    fvaluenumber: partdata.attrinfo[a].fvaluenumber
                                });
                            }
                        }
                    }
                    else {
                        addRowData = {
                            'fmaterialid': data[0].fbillhead_id,
                            'fqty': 1,
                            'funitid': partdata.funitid
                        };
                        that.Model.setValue({ id: 'fmaterialid', row: row, value: data[0].fbillhead_id });
                        that.Model.setValue({ id: 'fpartqty', row: row, value: 1 });
                    }

                    // 触发获取商品其他信息
                    that.Model.setValue({
                        id: 'fmaterialid', row: row, value: {
                            id: data[0].fbillhead_id,
                            fnumber: data[0].fnumber,
                            fname: data[0].fname,
                            funitid: partdata["funitid"]
                        }
                    });
                    var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                    //对业务单据辅助属性字段设置
                    that.Model.setValue(auxPropArgs);
                    //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                    var Newid = yiCommon.uuid(32);
                    //如果父商品 已经有了组合号就不必再生成组合号
                    if (parentrow.fpartscombnumber) {
                        Newid = parentrow.fpartscombnumber;
                    } else {
                        Newid = yiCommon.uuid(32);
                        that.Model.setValue({ id: 'fpartscombnumber', row: partdata.id, value: Newid });
                        //给主商品打上主商品标识 区分
                        that.Model.setValue({ id: 'fiscombmain', row: partdata.id, value: 1 });
                    }
                    that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                    that.Model.setValue({ id: 'fparttype', row: row, value: partdata.parttype });
                    //根据主商品的数量计算配件数量
                    that.calculatePartQty(parentrow.fqty, parentrow.id);
                    break;
            }
        };
        

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.productEntryId:
                    that.deletingProductRow(e);
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            switch (e.id.toLowerCase()) {
                case that.productEntryId:
                    that.deletedProductRow(e);
                    break;
            }
        };

        //初始化可见性
        _child.prototype.initVisible = function () {
            var that = this;

            var pageId = that.Model.viewModel.pageId;
            // 显示【促销门店】单据体
            var fdeptscope = that.Model.getSimpleValue({ id: 'fdeptscope' });
            if (fdeptscope === 'deptscope_02') {
                $(`#${pageId}_fdeptentry`).css({ visibility: 'unset', width: '100%', position: 'unset', left: 'unset', zIndex: 'unset' })
            } else {
                $(`#${pageId}_fdeptentry`).css({ visibility: 'hidden', width: '100%', position: 'absolute', left: '-9999px', zIndex:'-9999' })
            }
        }

        //标准定制
        _child.prototype.showstandardcustom = function (entityKey) {
            var that = this;
            //选中行
            var ds = that.Model.getSelectRows({ id: entityKey });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            };
            if (ds) {
                //判断物料是否启用 选配类别 
                var fisunstandard = false;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且未勾选非标定制
                if (Isenableselectioncategory && !fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框 
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    } else {
                        //弹出“标准定制-套件”功能框
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配，不允许标准定制！');
                    return;
                }
            }
        };

        //取零售价
        _child.prototype.getSalPrice = function (rows) {
            if (!rows || !rows.length) return;

            var that = this;

            var productInfos = [];
            for (var i = 0; i < rows.length; i++) {
                var row = rows[i];
                var productEntry = that.Model.getEntryRowData({ id: that.productEntryId, row: row });

                if (!productEntry) continue;

                var fmaterialid = productEntry.fmaterialid.id;
                var funitid = productEntry.funitid.id;
                var bizDate = new Date();

                var entities = [];
                var propEntry = productEntry.fattrinfo && productEntry.fattrinfo.fentity;
                if (propEntry) {
                    for (var i = 0; i < propEntry.length; i++) {
                        entities.push({
                            valueId: propEntry[i].fvalueid,
                            auxPropId: propEntry[i].fauxpropid.id
                        });
                    }
                }

                productInfos.push({
                    clientId: productEntry.id,
                    productId: fmaterialid,
                    unitId: funitid,
                    bizDate: bizDate,
                    attrInfo: {
                        id: '',
                        entities: entities
                    },
                    fetchSpecialPrice: false    // 不查询特价
                });
            }

            if (productInfos.length === 0) return;

            var productInfoJson = JSON.stringify(productInfos);

            that.Model.invokeFormOperation({
                id: 'getprices',
                opcode: 'getprices',
                async: true, //采用异步请求，因耗时过长，后续优化
                param: {
                    productInfos: productInfoJson,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });
        }

        //取价操作后的处理逻辑
        _child.prototype.getPricesAfterDoOperation = function (e) {
            var that = this;

            var srvData = e.result.operationResult.srvData;
            if (!srvData) return;

            var message = '';
            var productEntrys = that.Model.getEntryData({ id: that.productEntryId });

            for (var i = 0, l = srvData.length; i < l; i++) {

                var lm = srvData[i];
                lm.salPrice = lm.salPrice || 0;

                var rowData = {};
                var seq = 0;
                for (var n = 0, m = productEntrys.length; n < m; n++) {
                    if (productEntrys[n].id == lm.clientId) {
                        rowData = productEntrys[n];
                        seq = n + 1;
                        break;
                    }
                }

                // 价格匹配成功，则赋值
                if (lm.success) {

                    that.Model.setValue({ id: 'fsalprice', row: lm.clientId, value: lm.salPrice });

                    if (lm.count > 1) {
                        message += '第{0}行商品【{1}】匹配到{2}个价格。<br/>'.format(seq, rowData.fmaterialid.fname, lm.count);
                    }

                } else {
                    that.Model.setValue({ id: 'fsalprice', row: lm.clientId, value: 0 });
                }
            }

            if (message) {
                yiDialog.warn(message);
            }
        };

        _child.prototype.TakeTGCpartsByStandard = function (e) {
            var that = this;

            var rowdata = that.Model.getEntryRowData({ id: that.productEntryId, row: e.row });
            //勾选了配件标记但是 未勾选允许选配（即标准品）
            if (rowdata.fmaterialid.fispartflag && !rowdata.fmaterialid.fispresetprop) {
                that.Model.invokeFormOperation({
                    id: 'tgcpartbystandard',
                    opcode: 'tgcpartbystandard',
                    param: {
                        formId: 'ydj_order',
                        fproductid: e.value.id,
                        rowId: e.row,
                        domainType: 'dynamic'
                    }
                });
            }
        }

        _child.prototype.DoTGCparts = function (r) {
            var that = this;
            var res = r.result.operationResult;
            var srvData = res.srvData;
            if (res.isSuccess) {
                if (srvData.length >= 1) {
                    var TGCParts = srvData.filter(o => o.fmaterialid != "" && o.parttype == "铁架床")
                    //如果满足条件的铁架床配件有多个则需要弹框出来供客户选择
                    if (TGCParts && TGCParts.length > 1) {
                        var fmaterialList = [];
                        for (var i = 0; i < srvData.length; i++) {
                            fmaterialList.push(srvData[i].fmaterialid);
                        }
                        that.PartListData = srvData;
                        var filterString = "fid in ('{0}')"
                            .format(fmaterialList.join("','"));

                        var fdeliverid = that.Model.getValue({ "id": "fdeliverid" });
                        var srcPara = {
                            deliverid: fdeliverid ? fdeliverid.id : ""
                        };
                        that.Model.showSelectForm({
                            formId: 'ydj_product',
                            selectMul: false,
                            dynamicParam: {
                                filterString: filterString
                            },
                            srcPara: JSON.stringify(srcPara)
                        });
                        return;
                    }

                    for (var a = 0; a < srvData.length; a++) {
                        var auxEntry = [];
                        if (srvData[a].attrinfo) {
                            for (var i = 0; i < srvData[a].attrinfo.length; i++) {
                                auxEntry.push({
                                    fauxpropid: {
                                        id: srvData[a].attrinfo[i].fauxpropid,
                                        fname: srvData[a].attrinfo[i].fname,
                                        fnumber: srvData[a].attrinfo[i].fnumber
                                    },
                                    fvalueid: srvData[a].attrinfo[i].fvalueid,
                                    fvaluename: srvData[a].attrinfo[i].fvaluename,
                                    fvaluenumber: srvData[a].attrinfo[i].fvaluenumber
                                });
                            }
                        }
                        //【配件商品编码】【配件商品名称】【配件辅助属性】【数量】
                        addRowData = {
                            'fmaterialid': srvData[a].fmaterialid,
                            'fqty': srvData[a].fqty,
                            'fpartqty': srvData[a].fqty,
                        };
                        var parentrow = that.Model.getEntryRowData({ id: that.productEntryId, row: srvData[a].id });
                        var row = that.Model.addRow({ id: that.productEntryId, row: srvData[a].id, opctx: { ignore: true } });
                        that.Model.setValue({ id: 'fmaterialid', row: row, value: srvData[a].fmaterialid, opctx: { ignore: true } });
                        that.Model.setValue({ id: 'fpartqty', row: row, value: srvData[a].fqty });
                        //自动带出配件标识
                        that.Model.setValue({ id: 'fisautopartflag', row: row, value: 1 });
                        // that.Model.setValue({ id: 'fresultbrandid', row: row, value: fresultbrandid });
                        // 触发获取商品其他信息
                        that.Model.setValue({
                            id: 'fmaterialid', row: row, value: {
                                id: srvData[a].fmaterialid,
                                fnumber: srvData[a].fnumber,
                                fname: srvData[a].fname,
                            }, opctx: { ignore: true }
                        });
                        var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                        //对业务单据辅助属性字段设置
                        that.Model.setValue(auxPropArgs);
                        //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                        var Newid = yiCommon.uuid(32);
                        //如果父商品 已经有了组合号就不必再生成组合号
                        if (parentrow && parentrow.fpartscombnumber) {
                            Newid = parentrow.fpartscombnumber;
                        } else {
                            Newid = yiCommon.uuid(32);
                            that.Model.setValue({ id: 'fpartscombnumber', row: srvData[0].id, value: Newid });
                            //给主商品打上主商品标识 区分
                            that.Model.setValue({ id: 'fiscombmain', row: srvData[0].id, value: 1 });
                        }
                        that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                        that.Model.setValue({ id: 'fparttype', row: row, value: srvData[a].parttype });
                        //根据主商品的数量计算配件数量
                        that.calculatePartQty(parentrow.fqty, parentrow.id);
                    }
                }
            }
        };

        _child.prototype.calculatePartQty = function (mainqty, row) {
            var that = this;
            var product = that.Model.getEntryRowData({ id: that.productEntryId, row: row });
            var ds = that.Model.getEntryData({ id: that.productEntryId });
            //如果当前修改明细是配件商品，修改数量后要翻倍配件的销售数量
            if (product.fpartscombnumber) {
                //找到对应的子件明细
                for (var a = 0; a < ds.length; a++) {
                    //有配件组合号 但是不是配件主商品则为配件商品
                    if (ds[a].fpartscombnumber == product.fpartscombnumber && !ds[a].fiscombmain) {
                        //配件数量（元数量）
                        var fpartqty = yiMath.toNumber(ds[a].fpartqty);
                        var fsalprice = yiMath.toNumber(ds[a].fsalprice);
                        that.Model.setValue({ id: "fqty", row: ds[a].id, value: mainqty * fpartqty, tgChange: false });
                        that.Model.setValue({ id: 'fsalprice', row: ds[a].id, value: fsalprice * mainqty * fpartqty });
                    }
                }
            }

        };

        //标准定制、非标定制后根据属性、属性值 匹配带出配件
        _child.prototype.aftersetauxProp = function (e) {
            var that = this;
            if (!e.value.fentity) {
                return;
            }
            var attrinfo = e.value;
            var attrinfoNo = attrinfo.fentity.filter(o => o.fvaluename == "无");
            that.attrinfoNew = [];
            if (attrinfoNo) {
                attrinfoNo.forEach(o => { that.attrinfoNew.push(o.fauxpropid.fname) });
            }
            var rowData = that.Model.getEntryRowData({ id: that.productEntryId, row: e.row });
            if (!rowData) return;
            var productId = "";
            if (rowData.hasOwnProperty('fmaterialid')) {//存在该属性，则解析值
                productId = $.trim(rowData.fmaterialid && rowData.fmaterialid.id);
            }
            if (productId == "") return;

            if (attrinfo.fentity && attrinfo.fentity.length > 0) {
                var isCheckCustom = false;
                var ds = that.Model.getSelectRows({ id: that.productEntryId });
                if (ds.length > 0) {
                    var rowid = ds[0].data.id;
                    isCheckCustom = rowid == rowData.id;
                }
                if (isCheckCustom) {
                    that.Model.invokeFormOperation({
                        id: 'doaddparts_custom',
                        opcode: 'doaddparts_custom',
                        param: {
                            formId: 'ydj_order',
                            rows: JSON.stringify(attrinfo),
                            currentrow: JSON.stringify(rowData),
                            productId: productId,
                            rowId: e.row,
                            domainType: 'dynamic'
                        }
                    });
                }
            }

        };


        _child.prototype.doaddparts_customAfterDoOperation = function (e) {
            var that = this;
            
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            if (isSuccess && srvData) {
                //如果匹配到标准品商品id则直接更新商品
                if (srvData.standardId && srvData.standardId != undefined) {
                    // 触发获取商品其他信息
                    var row = that.Model.getEntryRowData({ id: that.productEntryId, row: srvData.rowId });
                    that.Model.setValue({ id: 'fmaterialid', row: srvData.rowId, value: srvData.standardId });
                }
                else {
                    if (that.attrinfoNew.length > 0) {
                        //如果将支撑杆改为无，则将同配件号的支撑杆类型明细行删除掉
                        var ds = that.Model.getSelectRows({ id: that.productEntryId });
                        if (ds && ds.length > 0) {
                            that.DelEntryByNoting(ds);
                        }
                    }
                    if (srvData.length >= 1) {
                        //返回了配件也有可能存在一种情况，之前满足两条配件的条件现在修改属性，只满足了一条记录
                        //最简单的方法先删后生成
                        that.clearOtherParts();

                        var TGCParts = srvData.filter(o => o.fmaterialid != "" && o.parttype == "铁架床")
                        //如果满足条件的铁架床配件有多个则需要弹框出来供客户选择
                        if (TGCParts && TGCParts.length > 1) {
                            var fmaterialList = [];
                            for (var i = 0; i < srvData.length; i++) {
                                fmaterialList.push(srvData[i].fmaterialid);
                            }
                            that.PartListData = srvData;
                            var filterString = "fid in ('{0}')"
                                .format(fmaterialList.join("','"));
                            var billtype = that.Model.getValue({ id: 'fbilltype' });
                            var srcPara = {
                                SrcFormId: 'ydj_order',
                                billtypeNo: billtype?.id,
                                billtypeName: billtype?.fname
                            };
                            that.Model.showSelectForm({
                                formId: 'ydj_product',
                                selectMul: false,
                                dynamicParam: {
                                    filterString: filterString
                                },
                                srcPara: JSON.stringify(srcPara)
                            });
                            return;
                        }

                        for (var a = 0; a < srvData.length; a++) {
                            var auxEntry = [];
                            if (srvData[a].attrinfo) {
                                for (var i = 0; i < srvData[a].attrinfo.length; i++) {
                                    auxEntry.push({
                                        fauxpropid: {
                                            id: srvData[a].attrinfo[i].fauxpropid,
                                            fname: srvData[a].attrinfo[i].fname,
                                            fnumber: srvData[a].attrinfo[i].fnumber
                                        },
                                        fvalueid: srvData[a].attrinfo[i].fvalueid,
                                        fvaluename: srvData[a].attrinfo[i].fvaluename,
                                        fvaluenumber: srvData[a].attrinfo[i].fvaluenumber
                                    });
                                }
                            }
                            var addRowData = {};
                            //【配件商品编码】【配件商品名称】【配件辅助属性】【数量】
                            addRowData = {
                                'fmaterialid': srvData[a].fmaterialid,
                                'fqty': srvData[a].fqty,
                                'fpartqty': srvData[a].fqty,
                            };
                            var parentrow = that.Model.getEntryRowData({ id: that.productEntryId, row: srvData[a].id });
                            //to do 如果已经添加了【气压杆】配件那么就不再重复生成 气压杆的配件了。
                            var ds = that.Model.getSelectRows({ id: that.productEntryId });
                            if (!that.CheckParts(ds, srvData[a].parttype)) {
                                continue;
                            }
                            var row = that.Model.addRow({ id: that.productEntryId, row: srvData[a].id });
                            that.Model.setValue({ id: 'fmaterialid', row: row, value: srvData[a].fmaterialid });
                            that.Model.setValue({ id: 'fpartqty', row: row, value: srvData[a].fqty });
                            //自动带出配件标识
                            that.Model.setValue({ id: 'fisautopartflag', row: row, value: 1 });

                            //// 触发获取商品其他信息
                            that.Model.setValue({
                                id: 'fmaterialid', row: row, value: {
                                    id: srvData[a].fmaterialid,
                                    fnumber: srvData[a].fnumber,
                                    fname: srvData[a].fname,
                                }
                            });

                            var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                            //对业务单据辅助属性字段设置
                            that.Model.setValue(auxPropArgs);
                            //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                            var Newid = yiCommon.uuid(32);
                            //如果父商品 已经有了组合号就不必再生成组合号
                            if (parentrow && parentrow.fpartscombnumber) {
                                Newid = parentrow.fpartscombnumber;
                            } else {
                                Newid = yiCommon.uuid(32);
                                that.Model.setValue({ id: 'fpartscombnumber', row: srvData[a].id, value: Newid });
                                //给主商品打上主商品标识 区分
                                that.Model.setValue({ id: 'fiscombmain', row: srvData[a].id, value: 1 });
                            }
                            that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                            that.Model.setValue({ id: 'fparttype', row: row, value: srvData[a].parttype });

                            //根据主商品的数量计算配件数量
                            that.calculatePartQty(parentrow.fqty, parentrow.id);
                            //如果是床箱底板配件且允许定制，则需要在此明细记录定制说明：
                            if (srvData[a].parttype == "床箱底板" && srvData[a].Iscustom) {
                                //获取主商品辅助属性
                                var mainrow = that.Model.getEntryRowData({ id: that.productEntryId, row: parentrow.id });
                                var attrinfo = mainrow.fattrinfo.fentity;
                                var part = attrinfo.find(o => o.fauxpropid.fname == "床架其它定制");
                                if (part) {
                                    if (parentrow.fseltypeid && parentrow.fseltypeid.fname != undefined) {
                                        var str = "床架型号:" + parentrow.fseltypeid.fname + ";床架其它定制：" + part.fvaluename;
                                        that.Model.setValue({ id: 'fcustomdes_e', row: row, value: str });
                                    }
                                }
                            }
                        }
                    }
                    //如果没返回数据 即修改属性使其不满足标准品带出商品条件，需要删除之前已经带出过的配件
                    else if (srvData.length == 0) {
                        that.clearOtherParts();
                    }
                }
            }
        };


        //如果将支撑杆改为无，则将同配件号的支撑杆类型明细行删除掉
        _child.prototype.DelEntryByNoting = function (row) {
            var that = this;
            var arrType = ['支撑杆', '气压杆', '床箱底板'];
            var arrNoType = that.attrinfoNew;
            var arrHasType = [];
            var delRow = [];
            var parentrow = row[0].data;
            if (parentrow && parentrow.fpartscombnumber) {
                if (arrNoType.length > 0) {
                    var entryData = that.Model.getEntryData({ id: that.productEntryId });
                    for (var i = 0, j = entryData.length; i < j; i++) {
                        //找出同配件编码 且设置 配件类型为无的配件(自动带出)
                        if (parentrow.fpartscombnumber == entryData[i].fpartscombnumber && arrNoType.indexOf(entryData[i].fparttype && entryData[i].fisautopartflag) > -1) {
                            delRow.push(entryData[i].id);
                        }
                    }
                    //避免删除后0条分录的操作
                    if (delRow.length == entryData.length) {
                        that.Model.addRow({ id: that.productEntryId });
                    }
                    for (var i = 0; i < delRow.length; i++) {
                        var row = delRow[i];
                        that.FirstDelete = false;
                        that.Model.deleteRow({ id: that.productEntryId, row: row, opctx: { ignore: true } });
                    }
                }
            }
        };
        

        //切换商品清除其配件 以及处理配件组合号
        _child.prototype.clearPart = function (row) {
            var that = this;
            var delRow = [];
            var parentrow = that.Model.getEntryRowData({ id: that.productEntryId, row: row });
            //避免库存查询 异步处理时parentrow为undefined 报错
            if (!parentrow || !parentrow.fiscombmain) return;
            var entryData = that.Model.getEntryData({ id: that.productEntryId });
            for (var i = 0, j = entryData.length; i < j; i++) {
                //找出同配件编码 且设置 配件类型为无的配件(自动带出)
                if (parentrow.fpartscombnumber == entryData[i].fpartscombnumber && parentrow.id != entryData[i].id) {
                    delRow.push(entryData[i].id);
                }
            }
            //避免删除后0条分录的操作
            if (delRow.length == entryData.length) {
                that.Model.addRow({ id: that.productEntryId });
            }
            for (var i = 0; i < delRow.length; i++) {
                var row = delRow[i];
                that.Model.deleteRow({ id: that.productEntryId, row: row, opctx: { ignore: true } });
            }
            if (delRow.length > 0) {
                that.Model.setValue({ id: 'fpartscombnumber', row: parentrow.id, value: '' });
            }
        };

        //校验是否有重复的配件
        _child.prototype.CheckParts = function (row, partsname) {
            var that = this;
            var bool = true;
            if (partsname == "铁架床") return true;
            var allrows = that.Model.getEntryData({ id: that.productEntryId });
            bool = allrows.some(o => o.fparttype == partsname && o.fpartscombnumber == row[0].data.fpartscombnumber)
            return !bool;
        };

        _child.prototype.doaddarttr = function (partsname) {
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.productEntryId });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行添加选配！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('添加选配不支持多选！');
                return;
            };
            if (!that.CheckParts(ds, partsname)) {
                yiDialog.warn('当前商品已添加配件[' + partsname + '], 不允许重复添加同类型的配件!');
                return;
            }
            if (ds) {
                //获取选中行商品 的产品类别 从而匹配 选配配件映射里的维度。
                var fcategoryid = ds[0].data.fcategoryid.id;
                //判断选中行 商品档案  是否勾选了【是否可添加配件】
                //直接在后台处理匹配逻辑
                that.doaddparts(partsname, fcategoryid, JSON.stringify(ds[0].data));
            }
        };

        _child.prototype.doaddparts = function (partsname, fcategoryid, rows) {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'doaddparts',
                opcode: 'doaddparts',
                param: {
                    formId: 'ydj_order',
                    partsname: partsname,
                    fcategoryid: fcategoryid,
                    rows: rows,
                    domainType: 'dynamic'
                }
            });
        };

        _child.prototype.doaddpartsAfterDoOperation = function (e) {
            var that = this;
            
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            if (isSuccess) {
                //直接将放回的配件添加到明细行
                if (srvData && srvData.length == 1) {
                    var auxEntry = [];
                    if (srvData[0].attrinfo) {
                        for (var a = 0; a < srvData[0].attrinfo.length; a++) {
                            auxEntry.push({
                                fauxpropid: {
                                    id: srvData[0].attrinfo[a].fauxpropid,
                                    fname: srvData[0].attrinfo[a].fname,
                                    fnumber: srvData[0].attrinfo[a].fnumber
                                },
                                fvalueid: srvData[0].attrinfo[a].fvalueid,
                                fvaluename: srvData[0].attrinfo[a].fvaluename,
                                fvaluenumber: srvData[0].attrinfo[a].fvaluenumber
                            });
                        }
                    }
                    var addRowData = {};
                    //【配件商品编码】【配件商品名称】【配件辅助属性】【数量】
                    addRowData = {
                        'fmaterialid': srvData[0].fmaterialid,
                        'fqty': srvData[0].fqty
                    };
                    var parentrow = that.Model.getEntryRowData({ id: that.productEntryId, row: srvData[0].id });
                    var fresultbrandid = parentrow.fresultbrandid;
                    var row = that.Model.addRow({ id: that.productEntryId, row: srvData[0].id });
                    that.Model.setValue({ id: 'fmaterialid', row: row, value: srvData[0].fmaterialid });
                    that.Model.setValue({ id: 'fpartqty', row: row, value: srvData[0].fqty });
                    // 触发获取商品其他信息
                    that.Model.setValue({
                        id: 'fmaterialid', row: row, value: {
                            id: srvData[0].fmaterialid,
                            fnumber: srvData[0].fnumber,
                            fname: srvData[0].fname,
                        }
                    });
                    var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                    //对业务单据辅助属性字段设置
                    that.Model.setValue(auxPropArgs);
                    //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                    var Newid = yiCommon.uuid(32);
                    //如果父商品 已经有了组合号就不必再生成组合号
                    if (parentrow.fpartscombnumber) {
                        Newid = parentrow.fpartscombnumber;
                    } else {
                        Newid = yiCommon.uuid(32);
                        that.Model.setValue({ id: 'fpartscombnumber', row: srvData[0].id, value: Newid });
                        //给主商品打上主商品标识 区分
                        that.Model.setValue({ id: 'fiscombmain', row: srvData[0].id, value: 1 });
                    }
                    that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                    that.Model.setValue({ id: 'fparttype', row: row, value: srvData[0].parttype });
                    //根据主商品的数量计算配件数量
                    that.calculatePartQty(parentrow.fqty, parentrow.id);
                }
                //如果是多个配件的话，需要弹出商品选择框
                else if (srvData && srvData.length > 1) {
                    var fmaterialList = [];
                    for (var i = 0; i < srvData.length; i++) {
                        fmaterialList.push(srvData[i].fmaterialid);
                    }
                    that.PartListData = srvData;
                    var filterString = "fid in ('{0}')"
                        .format(fmaterialList.join("','"));

                    that.Model.showSelectForm({
                        formId: 'ydj_product',
                        selectMul: false,
                        dynamicParam: {
                            filterString: filterString
                        }
                    });
                }
                else {
                    yiDialog.warn('当前商品没有可添加的配件!');
                    return false;
                }
            }
        }

        //如果没返回数据 即修改属性使其不满足标准品带出商品条件，需要删除之前已经带出过的配件
        _child.prototype.clearOtherParts = function () {
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.productEntryId });
            if (ds.length > 0) {
                var rowid = ds[0].data.id;
                var fpartscombnumber = ds[0].data.fpartscombnumber,
                    fiscombmain = ds[0].data.fiscombmain,
                    fisautopartflag = ds[0].data.fisautopartflag;
                var entryData = that.Model.getEntryData({ id: that.productEntryId });
                //自动带出的配件
                var rows = entryData.filter(e => e.fpartscombnumber == fpartscombnumber && e.id != rowid && !e.fiscombmain && e.fisautopartflag);
                //所有配件
                var rowall = entryData.filter(e => e.fpartscombnumber == fpartscombnumber && e.id != rowid && !e.fiscombmain);
                var delRow = [];
                for (var i = 0, j = rows.length; i < j; i++) {
                    //找出同配件编码 且设置 配件类型为无的配件
                    delRow.push(rows[i].id);
                }
                //避免删除后0条分录的操作
                if (delRow.length == entryData.length) {
                    that.Model.addRow({ id: that.productEntryId });
                }
                for (var i = 0; i < delRow.length; i++) {
                    var row = delRow[i];
                    that.Model.deleteRow({ id: that.productEntryId, row: row, opctx: { ignore: true } });
                }
                //如果所有的配件都是自动带出的 删除完配件后要将主商品配件组合号置为空
                if (rowall.length == rows.length) {
                    that.Model.setValue({ id: 'fpartscombnumber', row: ds[0].data.id, value: '' });
                }
            }
        };

        // 删除商品行前事件
        _child.prototype.deletingProductRow = function (e) {
            var that = this;

            if (e.opctx && e.opctx.ignore) return;

            var entryData = that.Model.getEntryData({ id: that.productEntryId });

            var fpartscombnumber = that.Model.getValue({ id: 'fpartscombnumber', row: e.row });
            var fparttype = that.Model.getValue({ id: 'fparttype', row: e.row });
            //如果是通过选配自动携带的配件 不允许删除
            var fisautopartflag = that.Model.getValue({ id: 'fisautopartflag', row: e.row });
            if (fpartscombnumber && fisautopartflag && fparttype != '铁架床') {
                e.result = true;
                yiDialog.mt({ msg: '当前配件通过选配自动携带不允许删除，请在选配界面中进行调整!', skinseq: 2 });
                return;
            }
            var delRow = [];
            var fiscombmain = that.Model.getValue({ id: 'fiscombmain', row: e.row });
            //如果是删除配件的话不需要给提示
            if (fpartscombnumber && fiscombmain && entryData.some(item => item.id === e.row)) {
                delRow = [];
                // if (that.FirstDelete) {
                e.confirmMessage = '删除的当前商品行存在配件商品，关联的配件商品将会一并删除，请确认是否整套删除？';
                // }
            }
        }

        // 删除商品行后事件
        _child.prototype.deletedProductRow = function (e) {
            var that = this;

            var fpartscombnumber = e.delRow.fpartscombnumber;
            var fiscombmain = e.delRow.fiscombmain;
            var fisautopartflag = e.delRow.fisautopartflag;
            var rowid = e.delRow.id;
            var delRow = [];

            //处理删除关联组合号的操作 
            if (fpartscombnumber) {
                //点击”确定”时把所有相同【套件组合号】的商品行删除
                var entryData = that.Model.getEntryData({ id: that.productEntryId });
                //如果当前删除行是一个配件而不是主商品则不进行批量删除
                if (fiscombmain) {
                    for (var i = 0, j = entryData.length; i < j; i++) {
                        //如果删除的商品是套件，则删除相关的套件商品
                        if (fpartscombnumber == entryData[i].fpartscombnumber) {
                            delRow.push(entryData[i].id);
                        }
                    }
                    //避免删除后0条分录的操作
                    if (delRow.length == entryData.length) {
                        that.Model.addRow({ id: that.productEntryId });
                    }
                    for (var i = 0; i < delRow.length; i++) {
                        var row = delRow[i];
                        that.FirstDelete = false;
                        that.Model.deleteRow({ id: that.productEntryId, row: row, opctx: { ignore: true } });
                    }
                }
                //1) 如果主商品只有一个配件时, 删除该配件会清空主商品的【配件组合号】
                //2) 如果主商品对应关联多个配件时, 删除其中一个配件会不会清空主商品的【配件组合号】
                else {
                    var rows = entryData.filter(e => e.fpartscombnumber == fpartscombnumber && e.id != rowid && !e.fiscombmain);
                    var rowall = entryData.filter(e => e.fpartscombnumber == fpartscombnumber);
                    if (rows.length == 0) {
                        //清空配件组合号
                        for (var i = 0; i < rowall.length; i++) {
                            that.Model.setValue({ id: 'fpartscombnumber', row: rowall[i].id, value: '' });
                        }
                    }
                }
            }
        };

        //修改配件主商品时 根据配件组合号 翻倍配件的数量
        _child.prototype.calculateqtyBypart = function (e) {
            var that = this;
            var mainqty = e.value;
            that.calculatePartQty(mainqty, e.row);
        };

        return _child;
    })(BillPlugIn);
    window.ydj_combopromotion = window.ydj_combopromotion || ydj_combopromotion;
})();