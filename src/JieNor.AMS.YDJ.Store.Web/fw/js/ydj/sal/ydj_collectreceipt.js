/*
 *@ sourceURL=/fw/js/ydj/ste/ydj_collectreceipt.js
*/
;(function () {
    var ydj_collectreceipt = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        _child.prototype.entryId = "fentry";

        //初始化页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            debugger
            var fsourcetype = that.Model.getValue({id: "fsourcetype"});
            if (fsourcetype.id) {
                that.Model.setEnable({id: "fwithin", value: false})
            } else {
                that.Model.setEnable({id: "fwithin", value: true})
            }

            that.FieldSetting();
            that.GetAgentInfo();
            that.GetOrderInfo();
        };

        _child.prototype.GetAgentInfo = function () {
            var that = this;
            var companyid = Consts.loginCompany.id;
            var fstatus = that.Model.getSimpleValue({id: 'fstatus'});
            var fmainorgid = that.Model.getSimpleValue({id: 'fmainorgid'});
            if (fstatus != '') {
                companyid = fmainorgid;
            }
            that.Model.invokeFormOperation({
                id: 'getagentinfo',
                opcode: 'getagentinfo',
                param: {
                    formId: 'bas_agent',
                    companyid: companyid
                }
            });
        }
        _child.prototype.GetOrderInfo = function () {
            var that = this;
            var fsourcenumber = that.Model.getSimpleValue({id: 'fsourcenumber'});
            var fid = that.Model.getSimpleValue({id: 'id'})
            if (fsourcenumber != '' && fid != '') {
                that.Model.invokeFormOperation({
                    id: 'getorderinfo',
                    opcode: 'getorderinfo',
                    param: {
                        formId: 'ydj_collectreceipt',
                        fsourcenumber: fsourcenumber,
                        fid: fid
                    }
                });
            }
        }

        //根据单据类型设置字段显示或隐藏，必录
        _child.prototype.FieldSetting = function (billtype) {
            var that = this;
            var type = billtype;
            if (!type) type = that.Model.getValue({id: 'fbilltype'});

            var visibleFlag = true;
            if (type && type.fname == "退差应收单") {
                visibleFlag = false;//退差应收单 银行字段隐藏,否则默认显示
            }
            that.Model.setVisible({id: '.mybankid', value: visibleFlag});
        };

        //字段值改变前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fnontaxamount':
                case 'ftaxamount':
                case 'totalamount':
                case 'nonloanamount':
                case 'fnontaxamountcurrency':
                case 'ftaxamountcurrency':
                case 'fqty':
                    var fexpenseitem = that.Model.getValue({id: 'fexpenseitem', row: e.row})
                    if (!fexpenseitem || fexpenseitem.id == '') {
                        e.value = null;
                        e.result = true;
                        yiDialog.warn('请先选择费用项目！');
                    }
                    break;
            }

            if (e.id.toLowerCase() == "fqty" || e.id.toLowerCase() == "totalamount") {
                var fexpenseitemtype = that.Model.getValue({id: 'fexpenseitemtype', row: e.row});
                if (fexpenseitemtype == '费用支出' || fexpenseitemtype.id == "expensetype_02") {
                    if (e.value >= 0) {
                        //这个负数不提示，用户手动输入1，自动变成-1
                        e.value = -e.value;
                    }
                }
            }
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'forderno':
                    debugger
                    that.GetOrderInfo();
                    break;
                case 'frelatetype':
                    that.Model.setValue({id: "frelatecusid", value: ""});
                    break;
                case 'fqty':
                case 'fprice':
                    var value = 0;
                    if (e.id.toLowerCase() == "fqty") {
                        value = that.Model.getValue({id: 'fprice', row: e.row});
                    } else {
                        value = that.Model.getValue({id: 'fqty', row: e.row});
                    }
                    that.Model.setValue({id: 'totalamount', row: e.row, value: value * e.value});
                    break;
                case 'totalamount':
                    var fqty = that.Model.getValue({id: 'fqty', row: e.row});
                    that.Model.setValue({id: 'fprice', row: e.row, value: fqty == 0 ? 0 : e.value / fqty});
                    that.sumtaxamount();
                    break;
                case 'ftaxrate':
                case 'fnontaxamount':
                    var rowData = that.Model.getEntryRowData({id: that.entryId, row: e.row});
                    if (rowData) {
                        var fnontaxamount = yiMath.toNumber(rowData.fnontaxamount);
                        var ftaxrate = yiMath.toNumber(rowData.ftaxrate.fnumber);
                        if (fnontaxamount * ftaxrate == 0 && rowData.ftaxamount == 0) {
                            //当税金额由0更新为0时，不会走字段值改变逻辑，但需更新总金额
                            that.Model.setValue({id: 'totalamount', row: rowData.id, value: fnontaxamount});
                        } else {
                            //税金额= 不含税金额*税率
                            that.Model.setValue({id: 'ftaxamount', row: rowData.id, value: fnontaxamount * ftaxrate});
                        }
                        //不含税金额本位币 = 不含税金额
                        that.Model.setValue({id: 'fnontaxamountcurrency', row: rowData.id, value: fnontaxamount});
                    }
                    break;
                case 'ftaxamount':
                    var rowData = that.Model.getEntryRowData({id: that.entryId, row: e.row});
                    if (rowData) {
                        var ftaxamount = yiMath.toNumber(rowData.ftaxamount);
                        var fnontaxamount = yiMath.toNumber(rowData.fnontaxamount);
                        //总金额= 不含税金额+税金额
                        that.Model.setValue({id: 'totalamount', row: rowData.id, value: ftaxamount + fnontaxamount});
                        //税额本位币 = 税金额
                        that.Model.setValue({id: 'ftaxamountcurrency', row: rowData.id, value: ftaxamount});
                    }
                    break;
                case 'fmaterialid':
                    if (e.value && e.value.fnumber == "") {
                        that.Model.setValue({id: 'fmtrlnumber', row: e.row, value: ""});
                    }
                    break;
                case 'fbilltype':
                    that.FieldSetting(e.value);
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.entryId:
                    that.sumtaxamount();
                    break;
            }
        }

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'refund':
                    e.result = true;
                    var status = that.Model.uiData.fstatus.id;
                    if (status != 'E') {
                        yiDialog.warn('单据未审核，请审核后在操作！');
                        return;
                    }
                    var fsumtaxamount = that.Model.getValue({id: 'fsumtaxamount'});
                    if (fsumtaxamount == 0) {
                        yiDialog.warn('总金额为0，无法进行退款！');
                        return;
                    }

                    if (status == 'E') {
                        //var type = that.Model.getValue({ id: 'fbilltype' });
                        var frelatetype = that.Model.getValue({id: 'frelatetype'});
                        //if (!type || type.fname != "退差应收单" || !frelatetype || frelatetype.fname != "客户") {
                        //yiDialog.warn('对不起，仅允许【单据类型】为退差应收单且【往来单位类型】为客户才允许退款。');
                        if (!frelatetype || frelatetype.fname != "客户") {
                            yiDialog.warn('对不起，仅允许【往来单位类型】为客户才允许退款。');
                        } else {
                            that.Model.invokeFormOperation({
                                id: 'LoadSettleInfo',
                                opcode: 'LoadSettleInfo',
                                selectedRows: [{PKValue: that.Model.pkid}],
                                param: {
                                    settleType: e.opcode
                                }
                            });
                        }
                    }
                    break;
                case 'pull':
                    var fsourcenumber = that.Model.getValue({id: 'fsourcenumber'});
                    if (fsourcenumber == '') {
                        that.Model.setValue({id: 'frelatemanid', value: ""});
                        that.Model.setValue({id: 'ftrainingdept', value: ""});
                    }
                    break;
                case 'receipt':
                    e.result = true;
                    var frelatetype = that.Model.getValue({id: 'frelatetype'});
                    if (!frelatetype || frelatetype.fname != "客户") {
                        yiDialog.warn('对不起，仅允许【往来单位类型】为客户才允许收款。');
                        return;
                    }
                    var fsumtaxamount = that.Model.getValue({id: 'fsumtaxamount'});
                    if (fsumtaxamount <= 0) {
                        yiDialog.warn('对不起，总金额大于0才允许收款。');
                        return;
                    }
                    that.Model.invokeFormOperation({
                        id: 'LoadSettleInfo',
                        opcode: 'LoadSettleInfo',
                        selectedRows: [{PKValue: that.Model.pkid}],
                        param: {
                            settleType: e.opcode
                        }
                    });
                    break;
            }
        };

        //总部相关字段 当【登录账号所属经销商.经营类型=直营】默认可见，反之不显示
        _child.prototype.renewalHeadquart = function () {
            debugger;
            var that = this;
            var enable = true;
            //直营
            if (that.fmanagemodel === '1') {
                enable = true;
            } else {
                enable = false;
            }
            that.Model.setVisible({id: '.renewal-info-head', value: enable});
            that.Model.setVisible({id: 'fresultbrandid', value: enable});
        }

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            switch (e.opcode) {
                case 'getorderinfo':
                    if (srvData) {
                        //that.Model.setValue({id: "forderno", value: srvData.orderno});
                        that.Model.setValue({ id: 'forderno', value: { id: srvData.fid, fnumber: srvData.orderno, fname: srvData.orderno } });
                        that.Model.setValue({id: "fisinvoic", value: srvData.fisinvoiceneed});
                    }
                    break;
                case 'getagentinfo':
                    if (srvData && srvData.fmanagemodel) {
                        //经销商经营模式
                        that.fmanagemodel = srvData.fmanagemodel;
                    }
                    that.renewalHeadquart();
                    break;
                case 'loadsettleinfo':
                    if (isSuccess && $.isPlainObject(srvData)) {
                        if (srvData.fsettletype === '退款') {
                            that.Model.showForm({
                                formId: 'coo_settledyn',
                                param: {openStyle: Consts.openStyle.modal},
                                cp: $.extend({}, srvData, {
                                    bizFields: [
                                        {id: 'fsourcenumber', cation: '单据编号'},
                                        {id: 'forderno', cation: '合同编号'},
                                    ],
                                    fimage: {id: '', name: ''},
                                    callback: function (result) {
                                        if (result && result.isSuccess) {
                                            that.Model.refresh();
                                        }
                                    }
                                })
                            });
                        } else {
                            that.showSettleDialog(srvData);
                        }
                    }
                    break;
            }
        }

        //汇总表头总金额
        _child.prototype.sumtaxamount = function () {
            var that = this;
            var ds = that.Model.getEntryData({id: that.entryId});
            if (ds) {
                var fsumtaxamount = 0;
                for (var i = 0, l = ds.length; i < l; i++) {
                    fsumtaxamount += ds[i].totalamount
                }
                //表头总金额=明细总金额汇总
                that.Model.setValue({id: "fsumtaxamount", value: fsumtaxamount});
            }
        };

        //显示结算对话框
        _child.prototype.showSettleDialog = function (settleInfo) {
            var that = this;
            that.Model.showForm({
                formId: 'coo_settledyn',
                param: {openStyle: Consts.openStyle.modal},
                cp: $.extend({}, settleInfo, {
                    bizFields: [
                        {id: 'fsourcenumber', cation: '单据编号'},
                    ],
                    fsettletype: '收款',
                    fway: {id: 'payway_01'},//账户支付
                    fpurpose: 'bizpurpose_03',//其他扣款
                    fimage: {id: '', name: ''},
                    callback: function (result) {
                        if (result && result.isSuccess) {
                            that.Model.refresh();
                        }
                    }
                })
            });
        };

        return _child;
    })(BillPlugIn);
    window.ydj_collectreceipt = window.ydj_collectreceipt || ydj_collectreceipt;
})();