/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/sal/ydj_saleintention.js
 */
; (function () {
    var ydj_saleintention = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.filesavemsg = [];//全局存储信息
            that.result = [];
            that.succArr = [];
            that.nowRow = '';//当前被双击的行
            that.suentry = [];
            //用于商品扫码
            that.oldValue = '',
            that.barcodeInfo = [];
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//订单信息
        _child.prototype.fdentity = 'fdrawentity';//图纸信息
        _child.prototype.dutyEntryId = 'fdutyentry';//销售员信息
        //默认折扣率
        _child.prototype.defdis = 10;

        //初始化页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //初始化的时候，初始化不用计算
            //that.onBillInitProduct('init');
            //获取门店系统参数本地缓存数据
            var param = JSON.parse(localStorage.getItem("storesysparam"));
            if (!param) {
                //本地缓存不存在则请求
                that.Model.invokeFormOperation({
                    id: 'loadstoresysparam',
                    opcode: 'loadstoresysparam',
                    param: {
                        'formId': 'bas_storesysparam',
                        'domainType': 'parameter'
                    }
                });
            }
            else {
                //设置外部带单必填校验
                that.setChannelRequired();
            }

            //初始化库存参数
            that.initStockSysParam();

            //协同信息显示隐藏
            that.setCoorInfoVisible();
            //销售员信息
            that.procSaleManOp();
            //如果是新增，下推，则需要自动将下推过来的导购员加到导购员明细表格中
            if (that.formContext.status == 'new' || that.formContext.status == 'push') {
                that.procStaff();
                that.procEntrySaleDept();
            }

            //应收定金锁定性控制
            that.collectAmountDisable();
        };

        //初始化库存参数
        _child.prototype.initStockSysParam = function () {
            var that = this;
            //获取库存管理系统参数本地缓存数据
            var stockSysParam = JSON.parse(localStorage.getItem("stocksysparam"));
            if (!stockSysParam) {
                //本地缓存不存在则请求
                that.Model.invokeFormOperation({
                    id: 'loadstocksysparam',
                    opcode: 'loadstocksysparam',
                    param: {
                        'formId': 'stk_stockparam',
                        'domainType': 'parameter'
                    }
                });
            }
            else {
                //根据库存管理系统[新增明细自动携带部门到货主]参数初始化明细行的货主及货主类型
                that.initDeptToOwnerId(stockSysParam, true);
            }
        }

        //当部门改变时，初始化明细行的货主及货主类型
        _child.prototype.setOwnerIdsByDeptChanged = function () {
            var that = this;
            var stockSysParam = JSON.parse(localStorage.getItem("stocksysparam"));
            that.initDeptToOwnerId(stockSysParam, false);
        }

        //新增明细行时初始化该行的货主及货主类型
        _child.prototype.setDeptToOwnerId = function (row) {
            var that = this;
            var stockSysParam = JSON.parse(localStorage.getItem("stocksysparam"));
            if (false == that.isAutoSetDeptToOwnerId(stockSysParam, false)) {
                return;
            }
            var deptId = that.Model.getValue({ "id": "fdeptid" });
            if (!deptId || $.trim(deptId.id) == "") {
                return;
            }
            that.setOwnerIdAndOwnerType(deptId, row);
        }

        //设置明细节的货主和货主类型
        _child.prototype.setOwnerIdAndOwnerType = function (deptId, row) {
            var that = this;
            that.Model.setValue({ "id": "fownerid", "value": deptId, "row": row });
            that.Model.setValue({ "id": "fownertype", "value": { "id": "ydj_dept", "fnumber": "部门", "fname": "部门" }, "row": row });
        }

        //根据库存管理系统[新增明细自动携带部门到货主]参数初始化明细行的货主及货主类型
        _child.prototype.initDeptToOwnerId = function (stockSysParam, isInitBill) {
            var that = this;
            var entityKey = that.fentity;
            //如果表单不是新增不必初始化明细行的货主及货主类型
            //或者根据库存管理系统[新增明细自动携带部门到货主]参数判断是否可以设置新增明细行的货主及货主类型
            if (false == that.isAutoSetDeptToOwnerId(stockSysParam, isInitBill)) {
                return;
            }
            var deptId = that.Model.getValue({ "id": "fdeptid" });
            if (!deptId || $.trim(deptId.id) == "") {
                return;
            }
            var entryDatas = that.Model.getEntryData({ id: entityKey });
            if (entryDatas == null || entryDatas.length <= 0) {
                return;
            }
            for (var i = 0; i < entryDatas.length; i++) {
                var entryData = entryDatas[i];
                var entryOwnerId = entryData["fownerid"];
                var entryOwnerType = entryData["fownertype"];

                if (entryOwnerId != null && $.trim(entryOwnerId.id).length > 0) {
                    continue;
                }

                var entryOwnerTypeId = entryOwnerType ? $.trim(entryOwnerType.id) : "";
                if (entryOwnerType != null && entryOwnerTypeId.length > 0 && entryOwnerTypeId != "ydj_dept") {
                    continue;
                }

                var row = entryData.id;
                that.setOwnerIdAndOwnerType(deptId, row);
            }
        }

        //根据库存管理系统[新增明细自动携带部门到货主]参数判断是否可以设置新增明细行的货主及货主类型
        _child.prototype.isAutoSetDeptToOwnerId = function (stockSysParam, isInitBill) {
            var that = this;
            //如果是初始化表单，则判断是否是新增页面，如果不是，则不需要设置明细行
            if (isInitBill && that.formContext.status != 'new' && that.formContext.status != 'push') {
                return false;
            }
            var deptToOwnerId = stockSysParam ? stockSysParam.fdepttoownerid : null;
            //[新增明细自动携带部门到货主]参数没有设置则不需要设置明细行
            if (deptToOwnerId == null || deptToOwnerId.length <= 0) {
                return false;
            }
            var formIds = deptToOwnerId.split(",");
            //[新增明细自动携带部门到货主]参数没有包含当前表单则不需要设置明细行
            if (formIds == null || formIds.length <= 0 || formIds.indexOf(that.Model.viewModel.formId) == -1) {
                return false;
            }
            return true;
        }

        //应收定金锁定性控制
        _child.prototype.collectAmountDisable = function () {
            var that = this;
            var bizstatus = $.trim(that.Model.getValue({ id: 'fcustomerstatus' })),
                fispushorder = that.Model.getSimpleValue({ id: 'fcustomerstatus' }),
                fcancelstatus = that.Model.getSimpleValue({ id: 'fcancelstatus' });
            setTimeout(function () {
                if (bizstatus != '已协同' && !fispushorder && !fcancelstatus) {
                    that.Model.removeAttr({ id: "[name=fcollectamount]", random: 'readonly' });
                } else {
                    that.Model.setAttr({ id: '[name=fcollectamount]', random: 'readonly', value: 'readonly' });
                }
            }, 100);
        }

        //设置协同信息显示隐藏
        _child.prototype.setCoorInfoVisible = function () {
            var that = this;
            var bizstatus = $.trim(that.Model.getValue({ id: 'fcustomerstatus' }));
            that.Model.setVisible({ id: '.coord-info', value: bizstatus == '已协同' ? true : false });
            that.Model.setAttr({ id: '.coo-tools', random: 'class', value: bizstatus == '已协同' ? 'coo-tools collapse' : 'coo-tools expand' });
        }

        //设置外部带单必填校验
        _child.prototype.setChannelRequired = function () {
            var that = this;
            var param = JSON.parse(localStorage.getItem("storesysparam"));
            if (!param.fischannelnullable) {
                that.Model.setVisible({ id: '.channel-required', value: true });
                that.Model.setAttr({ id: '[name=fchannel]', random: 'required', value: 'required' });
            }
        }

        //表格单元格格式化事件
        _child.prototype.onFieldValueFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fdistrate':
                    //对折扣字段值进行换算显示，比如：9折显示为0.9
                    if (e.value) {
                        e.value = yiMath.toDecimal(e.value / 10, 2);
                        e.cancel = true;
                    }
                    break;
            }
        };

        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {
            var that = this;
            var productData = [];
            if (param == 'change') {//改变某一行

                var rowData = that.Model.getEntryRowData({ id: that.fentity, row: params.attrinfo.row });

                if (JSON.stringify(rowData) == '{}') {
                    return;
                }
                //如果商品没值，就不用取价，直接设置为0
                if (params.attrinfo.id == 'fmaterialid' && params.attrinfo.value && !$.trim(params.attrinfo.value.id)) {
                    that.Model.setValue({ id: 'fprice', row: rowData.id, value: 0 });
                    return;
                }
                //如果商品没值，所携带的辅助属性肯定也为空，直接设置为0
                if (params.attrinfo.id == 'fattrinfo' && rowData.fmaterialid && !$.trim(rowData.fmaterialid.id)) {
                    that.Model.setValue({ id: 'fprice', row: rowData.id, value: 0 });
                    return;
                }
                //如果商品属于套件商品，则不取价
                if ($.trim(rowData.fsuitid.id) != '') {
                    return;
                }
                var reAttrinofEntry = [];//按照接口，重新组合
                var tempAttr = [];
                if (rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length > 0) {
                    tempAttr = rowData.fattrinfo.fentity;
                }

                for (var n = 0, m = tempAttr.length; n < m; n++) {
                    reAttrinofEntry.push({
                        valueId: tempAttr[n].fvalueid,
                        auxPropId: tempAttr[n].fauxpropid.id
                    })
                }

                //销售单位
                var salUnitId = $.trim(rowData.fbizunitid && rowData.fbizunitid.id);

                productData.push({
                    clientId: rowData.id,
                    productId: rowData.fmaterialid && rowData.fmaterialid.id,
                    unitId: salUnitId,
                    bizDate: that.Model.getValue({ id: 'fdate' }),
                    length: rowData.flength,
                    width: rowData.fwidth,
                    thick: rowData.fthick,
                    customerId: that.Model.getSimpleValue({ id: 'fcustomerid' }),
                    // stockStatus:rowData.fstockstatus&& rowData.fstockstatus.id,
                    attrInfo: {
                        id: '',
                        entities: reAttrinofEntry
                    }
                });



            } else if (param == 'changeall') {//改变客户的时候，取价
                var reGridData = that.Model.getValue({ id: that.fentity });
                for (var i = 0, l = reGridData.length; i < l; i++) {
                    var rowData = reGridData[i];

                    if (!rowData.fmaterialid || !$.trim(rowData.fmaterialid.id)) {
                        continue;
                    }
                    //如果商品属于套件商品，则不取价
                    if ($.trim(rowData.fsuitid.id) != '') {
                        continue;
                    }
                    var reAttrinofEntry = [];//按照接口，重新组合
                    var tempAttr = [];
                    if (rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length > 0) {
                        tempAttr = rowData.fattrinfo.fentity;
                    }

                    for (var n = 0, m = tempAttr.length; n < m; n++) {
                        reAttrinofEntry.push({
                            valueId: tempAttr[n].fvalueid,
                            auxPropId: tempAttr[n].fauxpropid.id
                        })
                    }

                    //销售单位
                    var salUnitId = $.trim(rowData.fbizunitid && rowData.fbizunitid.id);

                    productData.push({
                        clientId: rowData.id,
                        productId: rowData.fmaterialid.id,
                        unitId: salUnitId,
                        bizDate: that.Model.uiData.forderdate,
                        length: rowData.flength,
                        width: rowData.fwidth,
                        thick: rowData.fthick,
                        customerId: that.Model.uiData.fcustomerid.id,
                        // stockStatus: rowData.fstockstatus.id,
                        attrInfo: {
                            id: '',
                            entities: reAttrinofEntry
                        }
                    });
                }
            }
            if (productData.length == 0) {
                return;
            }

            productData = JSON.stringify(productData);

            that.Model.invokeFormOperation({
                id: param,

                opcode: 'getprices',
                //option: cvtParams,
                param: {
                    productInfos: productData,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });
        }

        //报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {
            var that = this;
            var flag = true;
            that.alertModel = e;
            var productData = [];
            var reAttrinofEntry = [];//按照接口，重新组合 
            var fentry = that.alertModel.Model.uiData.fentity;
            for (var n = 0, m = fentry.length; n < m; n++) {
                var lm = fentry[n];
                if (lm.fisselect) {//被选中的辅助属性行

                    if (!lm.fvalueid) {//辅助属性行需要填满信息才能查询
                        flag = false;
                    }
                    reAttrinofEntry.push({
                        valueId: lm.fvalueid,
                        auxPropId: lm.fauxpropid.id
                    })
                }

            }
            // var stockStatus = that.Model.getSimpleValue({id:'fstockstatus',row:e.formContext.cp.flexRow});
            productData.push({
                clientId: '',
                productId: that.alertModel.Model.uiData.fmaterialid.id,
                bizDate: that.Model.uiData.fdate,//业务日期
                length: that.alertModel.Model.uiData.flength,
                width: that.alertModel.Model.uiData.fwidth,
                thick: that.alertModel.Model.uiData.fthick,
                customerId: that.Model.uiData.fcustomerid.id,
                // stockStatus: stockStatus,
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            productData = JSON.stringify(productData);
            if (flag) {
                that.Model.invokeFormOperation({
                    id: 'onPriceSerch',

                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productData,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            } else {
                yiDialog.mt({ msg: '辅助属性信息不全，无法查询价格。', skinseq: 2 });
            }


        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':

                    that.onBillInitProduct('change', { attrinfo: e });
                    break;
            }
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:
                case this.fdentity:
                    e.result = { multiselect: false };
                    break;
                case this.dutyEntryId:
                    e.result = { multiselect: false, rownumbers: false };
                    break;
            }
        };

        //表格行新增前事件，设置 e.result=true 表示不让新增
        _child.prototype.onEntryRowCreating = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var isPushOrder = that.Model.getValue({ id: 'fispushorder' });
                    //已下推合同不允许修改
                    if (isPushOrder) {
                        e.result = true;
                        e.message = '已下推销售合同，不允许新增行！';
                        return;
                    }
                    break;
            }
        };

        //表格行创建后事件
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    if (that.barcodeInfo.length > 0) {
                        that.Model.setValue({ id: 'fmaterialid', value: that.barcodeInfo[0].productId, row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fattrinfo', value: that.barcodeInfo[0].currentAux ? that.barcodeInfo[0].currentAux.attrinfo : '', row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'funitid', value: that.barcodeInfo[0].unitId ? that.barcodeInfo[0].unitId.id : '', row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fbizunitid', value: that.barcodeInfo[0].unitId ? that.barcodeInfo[0].unitId.id : '', row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fsuitid', value: that.barcodeInfo[0].suitId, row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fissplit', value: that.barcodeInfo[0].isSplit, row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fisgiveaway', value: that.barcodeInfo[0].isGiveAway, row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'famount', value: that.barcodeInfo[0].amount, row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fqty', value: that.barcodeInfo[0].qty, row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fbizqty', value: that.barcodeInfo[0].qty, row: e.row, tgChange: false });
                        that.Model.setValue({ id: 'fdistrate', value: that.barcodeInfo[0].isGiveAway ? 0 : 10, row: e.row });
                        that.Model.setValue({ id: 'fsuitentryid_e', value: that.barcodeInfo[0].suitEntryId, row: e.row, tgChange: false });
                        //如果套件商品的单价为0且不为赠品，则获取价目表上的价格
                        if (that.barcodeInfo[0].price == 0 && !that.barcodeInfo[0].isGiveAway) {
                            that.onBillInitProduct('change', { attrinfo: e });
                        }
                        else {
                            that.Model.setValue({ id: 'fprice', value: that.barcodeInfo[0].price, row: e.row });
                        }
                        that.barcodeInfo.splice(0, 1);
                    }
                    that.setDeptToOwnerId(e.row);
                    //用于库存查询
                    // if($.trim(e.rowData.fmaterialid.id) != '' && $.trim(e.rowData.fstockstatus.id) != ''){
                    //     that.onBillInitProduct('change',{attrinfo:e});
                    // }
                    break;
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var isPushOrder = that.Model.getValue({ id: 'fispushorder' });
                    //已下推合同不允许修改
                    if (isPushOrder) {
                        e.result = true;
                        e.message = '已下推销售合同，不允许删除行！';
                        return;
                    }
                    var sourceEntryId = $.trim(that.Model.getValue({ id: 'fsourceentryid_e', row: e.rowId }));
                    //销售订单，只要是协同的，商品不允许删除，不允许新增，表体全部锁住
                    if (sourceEntryId) {
                        e.result = true;
                        yiDialog.mt({ msg: '协同商品不允许删除！', skinseq: 2 });
                    }
                    break;
                case that.dutyEntryId:
                    var isMain = that.Model.getValue({ id: 'fismain', row: e.row });
                    if (isMain) {
                        e.result = true;
                        yiDialog.mt({ msg: '主要导购员不允许删除！', skinseq: 2 });
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var suitId = e.delRow.fsuitid.id,
                        isSplit = e.delRow.fissplit,
                        delRow = [];
                    if (isSplit) {
                        var entryData = that.Model.getEntryData({ id: that.fentity });
                        for (var i = 0, j = entryData.length; i < j; i++) {
                            //如果删除的商品是套件且不可拆卖，则删除相关的套件商品
                            if (suitId == entryData[i].fsuitid.id) {
                                delRow.push(entryData[i].id);
                            }
                        }
                        //避免删除后0条分录的操作
                        if (delRow.length == entryData.length) {
                            that.Model.addRow({ id: that.fentity });
                        }
                        if (delRow.length > 0) {
                            that.Model.deleteRow({ id: that.fentity, row: delRow[0] });
                        }
                    }
                    //重新计算表头字段值
                    that.calculateSettlement();
                    break;
                case that.dutyEntryId:
                    //导购员信息
                    that.calculateStaff();
                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var isMain = that.Model.getValue({ id: 'fbilltypeid' });
            var isPushOrder = that.Model.getValue({ id: 'fispushorder' });
            var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
            var _id = e.id.toLowerCase();
            //销售订单，只要是协同的，所有表头字段锁住 包括表体 ,除了单价
            switch (_id) {
                //销售订单，只要是协同的，商品不允许删除，不允许新增，表体全部锁住
                case 'fmaterialid':
                case 'fcustomdes_e':
                case 'fattrinfo':
                case 'fqty':
                case 'famount':
                case 'fnote':
                case 'fsynnote':
                    if (_id == 'fmaterialid') {
                        //如果套件不可拆卖，则锁定字段
                        if (e.value.fissplit) {
                            e.result.enabled = false;
                            return;
                        }
                    }
                    if (_id == 'fcustomdes_e') {
                        //如果商品不允许定制，则锁定字段
                        if (!e.value.fcustom) {
                            e.result.enabled = false;
                            return;
                        }
                    }
                    //已下推合同不允许修改
                    if (isPushOrder) {
                        e.result.enabled = false;
                        return;
                    }
                    //协同销售且报价确认后不允许修改
                    var sourceEntryId = $.trim(that.Model.getValue({ id: 'fsourceentryid_e', row: e.row }));
                    var bizStatus = $.trim(that.Model.getSimpleValue({ id: 'fbizstatus' }));
                    var _bizStatus = ['business_status_05', 'business_status_09', 'business_status_10'];
                    if (sourceEntryId && $.inArray(bizStatus, _bizStatus) !== -1) {
                        e.result.enabled = false;
                        return;
                    }
                    //辅助属性字段商品为空时，不允许编辑
                    if (_id == 'fattrinfo') {

                        //如果套件不可拆卖，则锁定字段
                        if (e.value.fissplit) {
                            e.result.enabled = false;
                            return;
                        }
                        var productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
                        var _fispresetprop = $.trim(that.Model.getSimpleValue({ id: 'fispresetprop', row: e.row }));
                        if (!productId) {
                            e.result.enabled = false;
                            return;
                        }
                        if (_fispresetprop == 'true') {
                            e.result.enabled = true;
                            return;
                        } else {
                            e.result.enabled = false;
                            return;
                        }
                        //如果默认辅助属性为空则不允许编辑

                    }
                    break;
                case 'fnote_d':
                    //如果明细中的 fsourceentryid 字段不为空，则不能出现删除按钮，也不能编辑
                    if (e.value && e.value.fsourceentryid) {
                        e.result.enabled = false;
                    }
                case 'fprice':
                    //如果商品为赠品，则锁定
                    if (e.value.fisgiveaway) {
                        e.result.enabled = false;
                        return;
                    }
                    e.result.enabled = that.checkPriceEdit(e)
                    break;
                case 'fdistrate':
                    //如果商品为赠品，则锁定
                    if (e.value.fisgiveaway) {
                        e.result.enabled = false;
                    }
                    break;
                case 'fdutyid':
                    //导购员信息
                    var isMain = that.Model.getValue({ id: 'fismain', row: e.row });
                    if (isMain) {
                        e.result.enabled = false;
                    }
                    break;
            }
        };

        //检查价格是否可以修改
        _child.prototype.checkPriceEdit = function (e) {
            var that = this;
            var isPushOrder = that.Model.getValue({ id: 'fispushorder' });
            var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
            //var productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
            ////商品为空时，不允许编辑
            //if (!productId) {
            //    return false;
            //}
            //已下推合同不允许修改
            if (isPushOrder) {
                return false;
            }
            //销售订单，业务状态=待出库时，价格不能修改锁住
            var status = that.Model.getValue({ id: 'fbizstatus' });
            if (status && status.id == 'business_status_09') {

                return false;
            }
            //判断门店系统参数是否启用了意向单零售价可编辑
            if (storeParam) {
                if (!storeParam.fissaleusable) {
                    //如果定制说明不为空则解锁零售价
                    if (that.Model.getValue({ id: 'fcustomdes_e', row: e.row }) != '' &&
                        (that.Model.getSimpleValue({ id: 'fstatus' }) != 'D' && that.Model.getSimpleValue({ id: 'fstatus' }) != 'E')) {
                        return true;
                    }
                    return false;
                }
            }
            //状态为提交或者审批的时候，零售价需要锁住。
            var fstatus = that.Model.getValue({ id: 'fstatus' });
            if (fstatus) {
                if (fstatus.id == 'D' || fstatus.id == 'E') {
                    return false;
                }

            }

            return true;
        };

        //表单元素被单击后
        _child.prototype.onElementClick = function (e) {
            var that = this;
            if (!e.id) { return; }
            switch (e.id.toLowerCase()) {
                case 'show-quote':
                    that.showQuoteDialog();
                    break;
            }
        };

        //显示报价明细对话框
        _child.prototype.showQuoteDialog = function (e) {
            var that = this;
            //组装树形明细数据包
            var ds = that.Model.getEntryData({ id: that.fentity });
            if (!ds || ds.length <= 0) {
                yiDialog.mt({ msg: '商品明细为空，无法编辑报价明细！', skinseq: 2 });
                return;
            }
            var treeData = [];
            for (var i = 0; i < ds.length; i++) {
                //主物料
                treeData.push({
                    id: ds[i].id,
                    FSeq: ds[i].FSeq,
                    fparentid: '',
                    flevel: 0,
                    fexpanded: true,
                    fisleaf: false,
                    fproductid: ds[i].fmaterialid,
                    fproductname: '',
                    fmatchresult: '',
                    fisadjust: false,
                    funitid: { id: '', fnumber: '', fname: '' },
                    fqty: ds[i].fqty,
                    fbizunitid: { id: '', fnumber: '', fname: '' },
                    fbizqty: ds[i].fbizqty,
                    fattrinfo: ds[i].fattrinfo,
                    flength: '',
                    fwidth: '',
                    fthick: '',
                    fprice: ds[i].fprice,
                    famount: ds[i].famount,
                    fdescription: ds[i].fnote
                });
                //子物料
                if (ds[i].fdetail) {
                    for (var j = 0; j < ds[i].fdetail.length; j++) {
                        treeData.push({
                            id: ds[i].fdetail[j].id,
                            FSeq: ds[i].fdetail[j].FSeq,
                            fparentid: ds[i].id,
                            flevel: 1,
                            fexpanded: false,
                            fisleaf: true,
                            fproductid: ds[i].fdetail[j].fproductid_sub,
                            fproductname: ds[i].fdetail[j].fproductname_sub,
                            fmatchresult: '',
                            fisadjust: ds[i].fdetail[j].fisadjust_sub,
                            funitid: ds[i].fdetail[j].funitid_sub,
                            fqty: ds[i].fdetail[j].fqty_sub,
                            fbizunitid: ds[i].fdetail[j].fbizunitid_sub,
                            fbizqty: ds[i].fdetail[j].fbizqty_sub,
                            fattrinfo: ds[i].fdetail[j].fattrinfo_sub,
                            flength: ds[i].fdetail[j].flength_sub,
                            fwidth: ds[i].fdetail[j].fwidth_sub,
                            fthick: ds[i].fdetail[j].fthick_sub,
                            fprice: ds[i].fdetail[j].fprice_sub,
                            famount: ds[i].fdetail[j].famount_sub,
                            fdescription: ds[i].fdetail[j].fdescription_sub
                        });
                    }
                }
            }
            that.Model.showForm({
                formId: 'ydj_salequote',
                param: { openStyle: Consts.openStyle.modal },
                cp: {
                    fproductentry: treeData,
                    callback: function (result) {
                        if (result && result.newTreeData) {
                            var newData = result.newTreeData;
                            //更新子明细数据源
                            for (var i = 0; i < ds.length; i++) {
                                ds[i].fdetail.length = 0;
                                var seq = 1;
                                for (var j = 0; j < newData.length; j++) {
                                    if (ds[i].id === newData[j].fparentid) {
                                        ds[i].fdetail.push({
                                            id: newData[j].id,
                                            FSeq: seq,
                                            fproductid_sub: newData[j].fproductid,
                                            fproductname_sub: newData[j].fproductname,
                                            fisadjust_sub: newData[j].fisadjust,
                                            funitid_sub: newData[j].funitid,
                                            fqty_sub: newData[j].fqty,
                                            fbizunitid_sub: newData[j].fbizunitid,
                                            fbizqty_sub: newData[j].fbizqty,
                                            fattrinfo_sub: newData[j].fattrinfo,
                                            flength_sub: newData[j].flength,
                                            fwidth_sub: newData[j].fwidth,
                                            fthick_sub: newData[j].fthick,
                                            fprice_sub: newData[j].fprice,
                                            famount_sub: newData[j].famount,
                                            fdescription_sub: newData[j].fdescription
                                        });
                                        seq++;
                                    }
                                }
                            }
                        }
                    }
                }
            });
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'upthe':
                    that.initUpload();
                    e.result = true;
                    break;
                //结算
                //收款
                //退款
                case 'gathering':
                case 'receipt':
                case 'refund':
                    e.result = true;
                    that.Model.invokeFormOperation({
                        id: 'LoadSettleInfo',
                        opcode: 'LoadSettleInfo',
                        selectedRows: [{ PKValue: that.Model.pkid }],
                        param: {
                            settleType: e.opcode
                        }
                    });
                    break;
                //收支明细
                case 'incomedisburse':
                    e.result = true;
                    //that.Model.showForm({
                    //    formId: 'coo_incomedisburserptsal',
                    //    domainType: Consts.domainType.report,
                    //    param: { openStyle: Consts.openStyle.modal },
                    //    cp: {
                    //        sourceId: that.Model.pkid
                    //    }
                    //});
                    //改成直接打开收支记录列表
                    var filterString = "fsourceid='{0}' or exists(select 1 from t_ydj_order o where o.fid=fsourceid and o.fsourcenumber='{1}' and o.fsourcetype='ydj_saleintention' and o.fmainorgid='{2}')"
                        .format(that.Model.pkid, that.Model.getValue({ "id": "fbillno" }), Consts.loginCompany.id);
                    that.Model.showForm({
                        formId: 'coo_incomedisburse',
                        domainType: Consts.domainType.list,
                        param: {
                            openStyle: Consts.openStyle.modal,
                            filterstring: filterString
                        }
                    });
                    break;
                //选择设计方案
                case 'selectdesigner':
                    e.result = true;
                    var pValue = '';
                    var billno = that.Model.getValue({ id: 'fbillno' });
                    if ($.trim(billno) != '') {
                        pValue = "or fsourcenumber='" + billno + "'";
                    }
                    that.Model.showSelectForm({
                        formId: 'ydj_designscheme',
                        selectMul: false,
                        dynamicParam: {
                            formId: 'ydj_saleintention',
                            filterString: "(fsourcenumber='' " + pValue + ") and fcustomerid='" + that.Model.getSimpleValue({ id: 'fcustomerid' }) + "'" + " and fdeptid='" + that.Model.getSimpleValue({ id: 'fdeptid' }) + "'"
                        }
                    });
                    break;
                //选择量尺记录
                case 'selectscale':
                    e.result = true;
                    var pValue = '';
                    var billno = that.Model.getValue({ id: 'fbillno' });
                    if ($.trim(billno) != '') {
                        pValue = "or fsourcenumber='" + billno + "'";
                    }
                    e.result = true;
                    that.Model.showSelectForm({
                        formId: 'ydj_scalerecord',
                        selectMul: false,
                        dynamicParam: {
                            formId: 'ydj_saleintention',
                            filterString: "fsourcenumber=''" + pValue + "and fcustomerid='" + that.Model.getSimpleValue({ id: 'fcustomerid' }) + "'"
                        }
                    });
                    break;
                //导购员信息
                case 'salemember':
                    e.result = true;
                    that.Model.showPopForm({ popup: 'staff-info' });
                    break;
                case 'staffconfirm':
                    e.result = true;
                    var staffOk = that.checkStaff();
                    if (staffOk) {
                        that.Model.hidePopForm({ popup: 'staff-info' });
                    }
                    break;
                //跟进记录
                case 'followerrecord':
                    e.result = true;
                    var sourceId = that.Model.getSimpleValue({ "id": "id" });
                    var sourceNumber = that.Model.getSimpleValue({ "id": "fbillno" });
                    var sourceFormId = that.Model.viewModel.formId;
                    var customerId = that.Model.getSimpleValue({ "id": "fcustomerid" });
                    var ruleId = "{0}2ydj_followerrecord".format(sourceFormId);
                    var isHiddenNewBtn = false;
                    var isHiddenDeleteBtn = false;
                    var filterString = "";

                    if (customerId && customerId.length > 0) {
                        filterString = " fcustomerid='{0}' ".format(customerId);
                    } else {
                        filterString = " fsourcetype='{0}' and fsourcenumber='{1}' ".format(sourceFormId, sourceNumber);
                    }

                    that.Model.showForm({
                        formId: 'ydj_followerrecord',
                        domainType: Consts.domainType.list,
                        openStyle: 'Modal',
                        param: {
                            openStyle: Consts.openStyle.modal,
                            filterstring: filterString
                        },
                        cp: {
                            sourceFormId: sourceFormId,
                            sourceId: sourceId,
                            ruleId: ruleId,
                            isHiddenNewBtn: isHiddenNewBtn,
                            isHiddenDeleteBtn: isHiddenDeleteBtn
                        }
                    });
                    break;
                //退单
                case "chargeback":
                    e.result = true;
                    //先前端检查一下是否已退单
                    var fchargebackstatus = that.Model.getSimpleValue({ 'id': 'fchargebackstatus' });
                    if (fchargebackstatus == '1') {
                        yiDialog.warn('已是已退单状态！');
                        return;
                    }
                    //弹窗事件
                    that.yiDialogChargeback();
                    //点击事件中，需要给value设置默认值"空格"
                    //yiDialog.p({
                    //    title: "退单原因",
                    //    value: " ",
                    //    formType: 2,//富文本字段
                    //    ok: function (v) {
                    //        v = $.trim(v);
                    //        if (v.length == 0) {
                    //            yiDialog.error('退单原因不能为空！');

                    //            return false;
                    //        }
                    //        that.Model.invokeFormOperation({
                    //            id: 'chargeback',
                    //            opcode: 'chargeback',
                    //            selectedRows: [{ PKValue: that.Model.pkid }],
                    //            param: {
                    //                chargebackReason: v
                    //            }
                    //        });
                    //    }
                    //})
                    break;


                case 'manualrelease':
                    that.setSelRows(e);
                    break;
                case 'reserveinventory':
                    //设置当前选中行                                       
                    var selRows = that.Model.getSelectRows({ id: 'fentity' });
                    if (selRows && selRows.length > 0) {
                        var selectedRows = [];
                        for (var i = 0; i < selRows.length; i++) {
                            selectedRows.push({
                                PKValue: that.Model.pkid,
                                BillNo: '',
                                EntryPkValue: selRows[i].pkid,
                                EntityKey: 'fentity',

                            });
                        }
                        e.selRows = selectedRows;
                        if (!e.param) {
                            e.param = {};
                        }
                        e.param.selRows = JSON.stringify(selectedRows);
                    }
                    break;
            }
        };

        // 设置选中行
        _child.prototype.setSelRows = function (e) {
            var that = this;
            // bill：设置选中行
            if (that.formContext.domainType === Consts.domainType.bill) {
                // 设置当前选中行
                var selRows = that.Model.getSelectRows({ id: that.fentity });
                if (selRows && selRows.length > 0) {
                    var selectedRows = [];
                    for (var i = 0; i < selRows.length; i++) {
                        selectedRows.push({
                            PKValue: that.Model.pkid,
                            BillNo: '',
                            EntryPkValue: selRows[i].pkid,
                            EntityKey: that.fentity,

                        });
                    }
                    e.selRows = selectedRows;
                    if (!e.param) {
                        e.param = {};
                    }
                    e.param.selRows = JSON.stringify(selectedRows);
                } else {
                    yiDialog.warn('请选择一条数据后再执行操作！');
                    e.result = true;
                }
            }
            // 其他模式下：判断是否选择多单
            else {
                var selRows = that.Model.getSelectedRowsByOpCode({ opCode: e.opcode });
                var pkids = [];
                if (selRows && selRows.length > 0) {
                    var selectedRows = [];
                    for (var i = 0; i < selRows.length; i++) {
                        var pkid = selRows[i].PKValue;
                        if (pkids.indexOf(pkid) === -1) {
                            pkids.push(pkid);
                        }

                        if (pkids.indexOf(pkid) === 0) {
                            selectedRows.push(selRows[i]);
                        }
                    }

                    if (pkids.length > 1) {
                        yiDialog.warn('不允许多张单据同时操作，只有第一张单据可继续操作！');
                        
                        // 设置第一张单据
                        e.selRows = selectedRows;
                        if (!e.param) {
                            e.param = {};
                        }
                        e.param.selRows = JSON.stringify(selectedRows);
                    }
                } else {
                    yiDialog.warn('请选择一条数据后再执行操作！');
                    e.result = true;
                }
            }
        }

        _child.prototype.checkStaff = function () {
            //debugger
            var that = this;
            var ratioSum = 0;
            var data = that.Model.getEntryData({ id: that.dutyEntryId });

            if (data && data.length > 0) {
                for (var i = 0, l = data.length; i < l; i++) {
                    ratioSum += yiMath.toNumber(data[i].fratio);
                }
            }
            //that.Model.setValue({ id: 'famount_ed', value: 0, row: data[i].id });
            if (ratioSum != 100) {
                yiDialog.warn('销售员分配比例总和必须等于100%');
                return false;
            }

            return true;
        };

        //计算导购员比例
        _child.prototype.calculateStaffRatio = function (e) {
            var that = this;
            var data = that.Model.getEntryData({ id: that.dutyEntryId });
            if (!data || data.length < 1 || data.length > 2) return;

            //只有一个导购员时，如果输入的比例大于100，则自动设置为100
            if (data.length === 1) {
                if (e.value > 100) {
                    data[0].fratio = 100;
                    that.Model.refreshEntry({ id: that.dutyEntryId });
                    return;
                }
            }


            //有两个导购员时，修改其中一个导购员的比例后，自动计算另外一个导购员的比例
            if (data.length === 2) {
                if (e.value >= 100) {
                    e.value = 100;
                }
                for (var i = 0; i < data.length; i++) {
                    if (data[i].id === e.row) {
                        data[i].fratio = e.value;
                    } else {
                        data[i].fratio = 100 - e.value;
                    }
                }
                that.Model.refreshEntry({ id: that.dutyEntryId });
            }
        };

        _child.prototype.yiDialogChargeback = function () {
            var that = this;
            //点击事件中，需要给value设置默认值"空格"
            yiDialog.p({
                title: "退单原因",
                value: " ",
                formType: 2,//富文本字段
                ok: function (v) {
                    v = $.trim(v);
                    if (v.length == 0) {
                        yiDialog.warn('退单原因不能为空！');
                        that.yiDialogChargeback();
                        return false;
                    }
                    that.Model.invokeFormOperation({
                        id: 'chargeback',
                        opcode: 'chargeback',
                        selectedRows: [{ PKValue: that.Model.pkid }],
                        param: {
                            chargebackReason: v
                        }
                    });
                }
            })
        };

        //处理导购员自动更新到人员明细表格中
        _child.prototype.procStaff = function (staff) {
            var that = this;
            if (staff === undefined) staff = that.Model.getValue({ id: 'fstaffid' });
            if (!staff) return;
            var data = that.Model.getEntryData({ id: that.dutyEntryId }),
                mainRowId = '';
            if (data) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].fismain) {
                        mainRowId = data[i].id;
                        break;
                    }
                }
            }
            //如果存在主要导购员ID，更新主要导购员字段值
            if (mainRowId) {
                that.Model.setValue({ id: 'fdutyid', value: staff, row: mainRowId });
            } else {
                //添加主要导购员明细行
                if (staff && $.trim(staff.id)) {
                    that.Model.addRow({
                        id: that.dutyEntryId,
                        data: { fismain: true, fdutyid: staff, fratio: 100, famount_ed: 0 }
                    });
                }
            }

            //按比例重新计算分摊金额
            that.calculateStaff();
        };

        //计算导购员信息
        _child.prototype.calculateStaff = function () {
            var that = this,
                data = that.Model.getEntryData({ id: that.dutyEntryId }),
                sumAmount = yiMath.toNumber(that.Model.getValue({ id: 'ffbillamount' }));
            if (!data || data.length <= 0) { return; }
            for (var i = 0, l = data.length; i < l; i++) {
                var ratio = yiMath.toNumber(data[i].fratio);
                if (ratio <= 0) {
                    that.Model.setValue({ id: 'famount_ed', value: 0, row: data[i].id });
                    continue;
                }
                //金额 = 订单总额 * 行所占百分比
                data[i].famount_ed = yiMath.toDecimal(sumAmount * ratio / 100, 2);
            }
            that.Model.refreshEntry({ id: that.dutyEntryId });

            that.procSaleManOp();
        };

        //处理明细销售部门
        _child.prototype.procEntrySaleDept = function () {
            var that = this;
            var dept = that.Model.getValue({ id: 'fdeptid' });
            if (dept && dept.id) {
                var ds = that.Model.getEntryData({ id: that.productEntryId });
                if (ds) {
                    for (var i = 0; i < ds.length; i++) {
                        if (!ds[i].fdeptid_e) continue;
                        //明细销售部门为空时，默认等于单据头的销售部门
                        if (!$.trim(ds[i].fdeptid_e.id)) {
                            that.Model.setValue({ id: 'fdeptid_e', value: dept.id, row: ds[i].id });
                        }
                    }
                }
            }
        };

        //显示或隐藏导购员信息操作
        _child.prototype.procSaleManOp = function () {
            var that = this,
                ds = that.Model.getEntryData({ id: that.dutyEntryId }),
                sales = 0;
            if (ds) {
                for (var i = 0; i < ds.length; i++) {
                    if ($.trim(ds[i].fdutyid.id)
                        && yiMath.toNumber(ds[i].fratio) > 0
                        && yiMath.toNumber(ds[i].famount_ed) > 0) {
                        sales++;
                    }
                }
            }
            that.Model.setVisible({ id: '.salemember', value: sales > 1 });
        };

        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;
            switch (e.formId) {
                case 'ydj_designscheme':
                    //填充数据。
                    var data = e.data;
                    if (!data) {
                        return;
                    }
                    else if (data.length > 1) {
                        yiDialog.mt({ msg: '请选择一条数据。', skinseq: 2 });
                        return;
                    }
                    that.Model.setValue({ id: 'fdesignscheme', value: data[0].fnumber });
                    e.result = true;
                    break;
                case 'ydj_scalerecord':
                    //填充数据。
                    var data = e.data;
                    if (!data) {
                        return;
                    }
                    else if (data.length > 1) {
                        yiDialog.mt({ msg: '请选择一条数据。', skinseq: 2 });
                        return;
                    }
                    that.Model.setValue({ id: 'fscalerecord', value: data[0].fbillno });
                    e.result = true;
                    break;
                case 'ydj_productbarcode':
                    e.result = true;
                    //填充数据。
                    var data = e.data;
                    if (!data) {
                        return;
                    }
                    else if (data.length > 1) {
                        yiDialog.mt({ msg: '请选择一条数据。', skinseq: 2 });
                        return;
                    }
                    that.oldValue = data[0].ftranid;
                    that.Model.setValue({ id: 'fscancode', value: data[0].ftranid });
                    break;
            }

        };

        //显示结算对话框
        _child.prototype.showSettleDialog = function (settleInfo) {
            var that = this;
            settleInfo.freceiptstatus = $.trim(settleInfo.freceiptstatus).toLowerCase();
            settleInfo.fbilltypeid = $.trim(settleInfo.fbilltypeid).toLowerCase();
            settleInfo.fbizstatus = $.trim(settleInfo.fbizstatus).toLowerCase();
            that.Model.showForm({
                formId: 'ydj_salesettledyn',
                param: { openStyle: Consts.openStyle.modal },
                cp: {
                    fsourceid: settleInfo.fsourceid,
                    fsourcenumber: settleInfo.fsourcenumber,
                    fissyn: settleInfo.fissyn,
                    synBankNum: settleInfo.synBankNum,
                    fcustomerid: settleInfo.fcustomerid,
                    funsettleamount: settleInfo.funsettleamount,
                    fsettledamount: settleInfo.fsettledamount,
                    funconfirmamount: settleInfo.funconfirmamount,
                    fallaccounts: settleInfo.fallaccounts,
                    fdeptid: settleInfo.fdeptid,
                    fcontactunittype: settleInfo.fcontactunittype,
                    fcontactunitid: settleInfo.fcontactunitid,
                    fisunifiedcashier: settleInfo.fisunifiedcashier,
                    fimage: { id: '', name: '' },
                    callback: function (result) {
                        if (result && result.isSuccess) {
                            that.Model.refresh();
                        }
                    }
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            switch (e.opcode) {
                //销售意向下推销售合同
                case 'salecontract':
                    if (isSuccess && srvData) {
                        var saleId = $.trim(srvData.saleId);
                        if (saleId) {
                            that.Model.showBill({
                                formId: 'ydj_order',
                                pkids: [saleId]
                            });
                        } else {
                            that.Model.invokeFormOperation({
                                id: 'tbSale',
                                opcode: 'push',
                                param: { ruleid: "ydj_saleintention2ydj_order" }
                            });
                        }
                    }
                    break;
                //查看量尺
                case 'lookscale':
                    if (isSuccess && srvData) {
                        if (srvData.length == 1) {
                            that.Model.showBill({
                                formId: 'ydj_scalerecord',
                                pkids: [srvData[0]]
                            });
                        } else if (srvData.length > 1) {
                            that.Model.showList({
                                formId: 'ydj_scalerecord',
                                param: { openStyle: Consts.openStyle.modal },
                                filterstring: "fid in('" + srvData.join("','") + "')"
                            });
                        }
                    }
                    break;
                //查看设计方案
                case 'lookdesigner':
                    if (isSuccess && srvData) {
                        if (srvData.length == 1) {
                            that.Model.showBill({
                                formId: 'ydj_designscheme',
                                pkids: [srvData[0]]
                            });
                        } else if (srvData.length > 1) {
                            that.Model.showList({
                                formId: 'ydj_designscheme',
                                filterstring: "fid in('" + srvData.join("','") + "')"
                            });
                        }
                    }
                    break;
                //列表页面“结算”操作，显示结算对话框
                case 'loadsettleinfo':
                    if (isSuccess && $.isPlainObject(srvData)) {
                        if (srvData.fsettletype === '退款') {
                            that.Model.showForm({
                                formId: 'coo_settledyn',
                                param: { openStyle: Consts.openStyle.modal },
                                cp: $.extend({}, srvData, {
                                    bizFields: [
                                        { id: 'fsourcenumber', cation: '销售单号' },
                                        { id: 'fsettlemainname', cation: '客户' },
                                        { id: 'freceivable', cation: '应收定金' },
                                        { id: 'fsettledamount', cation: '确认已收' }
                                    ],
                                    fimage: { id: '', name: '' },
                                    callback: function (result) {
                                        if (result && result.isSuccess) {
                                            that.Model.refresh();
                                        }
                                    }
                                })
                            });
                        } else {
                            that.showSettleDialog(srvData);
                        }
                    }
                    break;
                //列表操作
                case 'accase':
                case 'cancelaccase':
                case 'transmit':
                case 'canceltransmit':
                    if (that.formContext.domainType === Consts.domainType.list && isSuccess) {
                        that.Model.refresh();
                    }
                    break;
                case 'getprices':
                    if (e.id == 'init' || e.id == 'change' || e.id == 'changeall') {

                        if (!srvData) {
                            //yiDialog.mt({msg:' 无相关查询数据', skinseq: 2});
                            return;
                        }

                        var str = '';
                        var gridData = that.Model.getValue({ id: that.fentity });

                        for (var i = 0, l = srvData.length; i < l; i++) {
                            var lm = srvData[i];
                            var rowData = {};
                            var num = 0;
                            for (var n = 0, m = gridData.length; n < m; n++) {
                                if (gridData[n].id == lm.clientId) {//获取对于行
                                    rowData = gridData[n];
                                    num = n + 1;
                                }
                            }

                            if (lm.success) {//价格匹配成功，则赋值
                                //当count大于1时，提示用户" 第{client}行，{productName},有{count}个项匹配，请谨慎使用匹配的数据" 价目表上获取不到商品名的，前端获取。

                                //	最匹配的销售价(零售价)赋值  ，非自备料，即使查到了零售价数据，也不赋值
                                that.Model.setValue({ id: 'fprice', row: lm.clientId, value: lm.salPrice });

                                if (lm.count > 1) {

                                    var theName = rowData.fmaterialid && rowData.fmaterialid.fname;
                                    str += '第{0}行，{1},有{2}个项匹配 <br/>'.format(num, theName, lm.count);
                                }
                            } else {//价格匹配不成功
                                //yiDialog.mt({msg:'价格匹配不成功', skinseq: 2});
                                if (e.id == 'change' || e.id == 'changeall') {//查不到数据，就赋值为0

                                    that.Model.setValue({ id: 'fprice', row: lm.clientId, value: 0 });
                                    //str+='第{0}行数据无相关查询数据<br/>'.format(num,theName,lm.count);
                                }
                            }
                        }


                        if (str.length > 0) {
                            yiDialog.mt({ msg: str, skinseq: 2 });

                        }
                    } else if (e.id == 'onPriceSerch') {

                        if (srvData && srvData[0] && that.alertModel.Model) {

                            that.alertModel.Model.setValue({ id: 'fprice', value: srvData[0].salPrice });
                        }

                    }
                    break;
                case 'loadstoresysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var storesysparam = {};
                        for (var data in srvData) {
                            storesysparam[data] = srvData[data];
                        }
                        localStorage.setItem("storesysparam", JSON.stringify(storesysparam));
                        //设置外部带单必填校验
                        that.setChannelRequired();
                    }
                    break;
                case 'loadstocksysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var stockSysParam = {};
                        for (var data in srvData) {
                            stockSysParam[data] = srvData[data];
                        }
                        localStorage.setItem("stocksysparam", JSON.stringify(stockSysParam));
                        that.initDeptToOwnerId(stockSysParam, true);
                    }
                    break;
                case 'getproductinfo':
                    if (isSuccess) {
                        that.oldValue = '';
                        if (srvData && srvData.productInfos.length > 0) {
                            that.barcodeInfo = srvData.productInfos;
                            var productInfos = copyArr(srvData.productInfos),
                                delRow = [],
                                flag = false,
                                entryData = that.Model.getEntryData({ id: that.fentity });
                            //拷贝数组
                            function copyArr(arr) {
                                var res = []
                                for (var i = 0; i < arr.length; i++) {
                                    res.push(arr[i])
                                }
                                return res;
                            }
                            //要删除的数据行
                            for (var i = 0; i < entryData.length; i++) {
                                if (entryData[i].fsuitid.id == that.barcodeInfo[0].suitId) {
                                    flag = true;
                                }
                                if ($.trim(entryData[i].fmaterialid.id) == '') {
                                    delRow.push(entryData[i].id);
                                }
                            }
                            //遍历要删除的数据行
                            for (var i = 0; i < delRow.length; i++) {
                                //如果只剩一条明细则直接清空表格
                                if (entryData.length == 1) {
                                    entryData.length = 0;
                                    that.Model.refreshEntry({ id: that.fentity });
                                }
                                else {
                                    that.Model.deleteRow({ id: that.fentity, row: delRow[i] });
                                }
                            }
                            if (flag) {
                                var hasRowIds = [],
                                    oldRowIds = [];

                                for (var i = 0, j = productInfos.length; i < j; i++) {
                                    oldRowIds.push(productInfos[i].suitEntryId);
                                }
                                //如果明细中已经存在相同的套件，则不新增行，直接递增套件商品的数量
                                for (var i = 0; i < entryData.length; i++) {
                                    for (var j = 0; j < that.barcodeInfo.length; j++) {
                                        if (entryData[i].fsuitid.id == that.barcodeInfo[j].suitId &&
                                            entryData[i].fsuitentryid_e == that.barcodeInfo[j].suitEntryId) {
                                            var qty = that.Model.getValue({ id: 'fqty', row: entryData[i].id });
                                            that.Model.setValue({ id: 'fqty', row: entryData[i].id, value: qty + that.barcodeInfo[j].qty });
                                            hasRowIds.push(that.barcodeInfo[j].suitEntryId);
                                        }
                                    }
                                }

                                var temp = []; //临时数组1  
                                var temparray = [];//临时数组2
                                for (var i = 0; i < hasRowIds.length; i++) {
                                    temp[hasRowIds[i]] = true;
                                }
                                for (var i = 0; i < oldRowIds.length; i++) {
                                    if (!temp[oldRowIds[i]]) {
                                        temparray.push(oldRowIds[i]);
                                    }
                                }
                                if (temparray.length > 0) {
                                    that.barcodeInfo.length = 0;
                                    for (var i = 0; i < temparray.length; i++) {
                                        for (var j = 0; j < productInfos.length; j++) {
                                            if (temparray[i] == productInfos[j].suitEntryId) {
                                                that.barcodeInfo.push(productInfos[j]);
                                            }
                                        }
                                    }
                                }
                                if (entryData.length != productInfos.length) {
                                    for (var i = 0, j = that.barcodeInfo.length; i < j; i++) {
                                        that.Model.addRow({ id: that.fentity })
                                    }
                                }
                                return;
                            }
                            //插入返回的商品信息
                            for (var i = 0, j = that.barcodeInfo.length; i < j; i++) {
                                that.Model.addRow({ id: that.fentity })
                            }
                        }
                    }
                    break;
            }
        };

        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            switch (e.entryId) {
                case 'fdrawentity':
                    //如果明细中的 fsourceentryid 字段不为空，则不能出现删除按钮
                    if (e.data && $.trim(e.data.fsourceentryid)) {
                        e.cancel = true;
                        return e.result = [{
                            id: 'download',
                            text: '下载'
                        }];
                    }
                    break;
            }
            //列表操作列
            if (e && $.trim(e.id) === 'foperate' && e.data) {
                e.cancel = true;
                e.result = [];
                if ($.trim(e.data.freceiptstatus).toLowerCase() !== 'receiptstatus_type_03') {
                    if (e.data.fbizstatus === 'business_status_09') {
                        e.result.push({ id: 'gathering', text: '结算' });
                    }
                }
                var bizStatus = $.trim(e.data.fbizstatus);
                if (bizStatus === 'business_status_02') {
                    e.result.push({ id: 'accase', text: '受理' });
                }
                if (bizStatus === 'business_status_07') {
                    e.result.push({ id: 'transmit', text: '报价确认' });
                    e.result.push({ id: 'cancelaccase', text: '取消受理' });
                }
                if (bizStatus === 'business_status_05') {
                    e.result.push({ id: 'canceltransmit', text: '取消报价确认' });
                }
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            if (e.id == that.fdentity) {
                switch (e.btnid.toLowerCase()) {
                    case 'download':
                        var rowData = that.Model.getEntryRowData({ id: that.fdentity, row: e.row });
                        var fileId = $.trim(rowData.ffileid.id);
                        if (fileId) {
                            that.Model.downloadFile({ fileIds: fileId, fileNames: $.trim(rowData.ffilename) });
                        }
                        break;
                    case 'delete':
                        that.Model.deleteRow({ id: that.fdentity, row: e.row });
                        break;
                }
            }
            //列表操作
            switch (e.btnid.toLowerCase()) {
                //列表页面“结算”操作，加载结算信息
                case 'gathering':
                    if (that.formContext.domainType === Consts.domainType.list) {
                        that.Model.invokeFormOperation({
                            id: 'LoadSettleInfo',
                            opcode: 'LoadSettleInfo',
                            selectedRows: e.selectedRows
                        });
                    }
                    break;
                case 'accase':
                case 'cancelaccase':
                case 'transmit':
                case 'canceltransmit':
                    that.Model.invokeFormOperation({
                        id: e.btnid,
                        opcode: e.btnid,
                        selectedRows: e.selectedRows
                    });
                    break;
            }
        };

        //文件上传
        _child.prototype.initUpload = function () {
            var that = this;
            that.Model.showForm({
                formId: 'bd_uploadfile',
                param: {
                    openStyle: Consts.openStyle.modal
                },
                cp: {
                    callback: function (result) {
                        if (!result || !result.files) return;
                        var files = result.files;
                        var fileIds = files.id.split(',');
                        var fileNames = files.name.split(',');
                        var fileSizes = files.sizes.split(',');
                        for (var i = 0, l = fileIds.length; i < l; i++) {
                            var data = {
                                ffileid: { id: fileIds[i], name: fileNames[i] }, //文件Id
                                ffilename: fileNames[i], //文件名
                                ffileformat: yiCommon.getFileExt(fileNames[i]), //文件格式
                                ffilesize: (fileSizes && fileSizes[i]) ? fileSizes[i] : 0, //文件大小
                                fuploader: Consts.loginUserDisplayName, //上传人
                                fuploaderid: Consts.loginUserId, //上传人Id
                                fuptime: new Date().ToString('yyyy-MM-dd HH:mm:ss'),  //上传时间
                                fnote_d: '', //备注
                            };
                            that.Model.addRow({ id: that.fdentity, data: data });
                        }
                        that.Model.refreshEntry({ id: that.fdentity });
                    }
                }
            });
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fqty': 
                case 'fprice':
                case 'fdistrate':
                case 'fdistamount_e':
                    ////计算商品明细相关字段
                    //that.productEntryChange({ name: e.id, rowId: e.row, value: e.value });
                    break;
                case 'fdealprice':
                    //计算结算信息
                    that.calculateSettlement();
                    break;

                case 'fmaterialid':

                    //更换商品设置默认折扣为10
                    that.Model.setValue({ id: 'fdistrate', row: e.row, value: 10 });
                    //更换商品定制说明和数量清空
                    that.Model.setValue({ id: 'fcustomdes_e', row: e.row, value: '' });
                    that.Model.setValue({ id: 'fqty', row: e.row, value: 0 });

                    that.onBillInitProduct('change', { attrinfo: e });
                    break;
                case 'fbizunitid':
                    that.onBillInitProduct('change', { attrinfo: e });
                    break;
                // case 'fstockstatus':
                //     var _productid=that.Model.getValue({id:'fmaterialid',row:e.row});
                //     if(_productid && $.trim(_productid.id)){
                //         that.onBillInitProduct('changeall',{attrinfo:e});
                //     }
                //     break;
                case 'fcustomerid':
                    that.onBillInitProduct('changeall', { attrinfo: e });
                    break;
                case 'fcustomerstatus':
                    //协同信息显示隐藏
                    that.setCoorInfoVisible();
                    that.collectAmountDisable();
                    break;
                case 'fcustomdes_e':
                    var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                    if (storeParam) {
                        if (!storeParam.fisorderusable) {
                            if ($.trim(e.value) != '' &&
                                (that.Model.getSimpleValue({ id: 'fstatus' }) != 'D' && that.Model.getSimpleValue({ id: 'fstatus' }) != 'E')) {
                                //如果定制说明不为空则零售价字段可编辑
                                that.Model.setEnable({ id: 'fprice', row: e.row, value: true });
                            }
                            else {//如果去掉定制说明则重新取价锁定零售价字段
                                that.Model.setEnable({ id: 'fprice', row: e.row, value: false });
                                that.onBillInitProduct('change', { attrinfo: e });
                            }
                        }
                    }
                    break;
                case 'fcustom':
                    //如果不允许定制则禁用定制说明字段
                    that.Model.setEnable({ id: 'fcustomdes_e', row: e.row, value: e.value == true ? true : false });
                    break;
                case 'fscancode':
                    //解码逻辑
                    if (e.value == '') {
                        that.Model.invokeFormOperation({
                            id: 'getproductinfo',
                            opcode: 'GetProductInfo',
                            param: {
                                barcode: that.oldValue,
                                codeType: 'barcode',
                                formId: 'ydj_productbarcode',
                                domainType: 'dynamic'
                            }
                        });
                    }
                    break;
                case 'fstaffid':
                    //将导购员自动填充到人员明细表格中
                    that.procStaff(e.value);
                    break;
                case 'fratio':
                    //计算导购员比例
                    that.calculateStaffRatio(e);
                    //计算导购员信息
                    that.calculateStaff(e.value);
                    break;
                case 'ffbillamount':
                    //计算导购员信息
                    that.calculateStaff(e.value);
                    break;
                case 'fdeptid':
                    that.setOwnerIdsByDeptChanged();
                    break;
            }
        };

        //字段按钮点击事件
        _child.prototype.onFieldButtonClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id) {
                case 'fscancode':
                    //显示套件列表对话框的逻辑
                    that.Model.showSelectForm({
                        formId: 'ydj_productbarcode',
                        selectMul: false,
                        dynamicParam: {
                            filterString: "fbillformid='ydj_suit'"
                        }
                    });
                    break;
            }
        };

        //商品明细改变时：重新计算明细
        _child.prototype.productEntryChange = function (opt) {
            var that = this;

            //行对象
            var row = that.Model.getEntryRowData({ id: that.fentity, row: opt.rowId });

            //数量
            var qty = yiMath.toNumber(row.fbizqty);

            //单价
            var price = yiMath.toNumber(row.fprice);

            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;

            //折扣额
            var distAmount = yiMath.toNumber(row.fdistamount_e);

            //折扣率
            var distRate = yiMath.toNumber(row.fdistrate);

            //如果当前修改的是“折扣额”，则自动反算“折扣率”
            if (opt.name === 'fdistamount_e') {
                //折扣率 =（表体折扣额 - 表体金额）/（-1 * 表体金额）
                if ((-1 * amount) !== 0) {
                    distRate = (distAmount - amount) / (-1 * amount);
                    distRate *= that.defdis;
                }
            }
            distRate = distRate > 0 ? distRate : that.defdis;

            //如果当前修改的不是“折扣额”，则根据折扣率计算“折扣额”，否则以用户输入的“折扣额”为准
            if (opt.name !== 'fdistamount_e') {
                //表体折扣额 = 表体金额 -（(表体金额 * 表体折扣率）/ 10)
                distAmount = amount - ((amount * distRate) / that.defdis);
            }

            //表体成交单价默认等于单价
            var dealPrice = price, dealAmount = 0;

            //表体成交单价 =（表体金额 - 表体折扣额）/ 表体数量
            if (qty !== 0) {
                dealPrice = (amount - distAmount) / qty;
            }

            //表体成交金额 = 表体数量 * 表体成交单价
            dealAmount = qty * dealPrice;

            dealPrice = isNaN(dealPrice) ? 0 : dealPrice;
            dealAmount = isNaN(dealAmount) ? 0 : dealAmount;

            //更新字段值
            that.Model.setValue({ id: 'famount', row: opt.rowId, value: amount });
            that.Model.setValue({ id: 'fdistrate', row: opt.rowId, value: yiMath.toDecimal(distRate, 2) });
            that.Model.setValue({ id: 'fdistamount_e', row: opt.rowId, value: distAmount });
            that.Model.setValue({ id: 'fdealprice', row: opt.rowId, value: dealPrice });
            that.Model.setValue({ id: 'fdealamount_e', row: opt.rowId, value: dealAmount });

            //计算结算信息
            that.calculateSettlement();
        };

        //计算结算信息
        _child.prototype.calculateSettlement = function () {
            var that = this,
                ds = that.Model.getEntryData({ id: that.fentity }),
                billAmount = 0;
            if (ds) {
                for (var i = 0; i < ds.length; i++) {
                    billAmount += yiMath.toNumber(ds[i].fbizqty * ds[i].fdealprice);
                }
            }
            that.Model.setValue({ id: 'ffbillamount', value: billAmount });
        };

        return _child;
    })(BasePlugIn);
    window.ydj_saleintention = window.ydj_saleintention || ydj_saleintention;
})();