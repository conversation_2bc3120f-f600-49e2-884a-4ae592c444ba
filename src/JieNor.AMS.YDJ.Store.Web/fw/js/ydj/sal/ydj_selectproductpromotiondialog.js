///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/sal/ydj_selectproductpromotiondialog.js
*/
; (function () {
    var ydj_selectproductpromotiondialog = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //取消
                case 'tbcancel':
                    that.Model.setReturnData({});
                    break;
                //确定
                case 'tbconfirm':
                    that.confirm(e);
                    break;
            }
        };

        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case "fqty":
                    if (e.value < 1) {
                        debugger;
                        var oldValue = that.Model.getValue({ id: "fqty", row: e.row });
                        e.result = true;
                        e.value = oldValue;
                        yiDialog.warn('赠送商品数量必须大于0');

                        return;
                    }
                    break;
            }
        }

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var rowId = e.row;

            var promotion = that.getSelectedPromotion();
            if (e.entityKey === 'fgiftentry') {
                switch (e.id.toLowerCase()) {
                    case 'fqty':
                        e.result.enabled = promotion?.fgiftqtyedit.id === '1';
                        return;
                }
            }

        };

        // 表格选中行变化时触发
        _child.prototype.onSelectedRowsChanged = function (e) {
            var that = this;
            var entityKey = e.id.toLowerCase();

            switch (entityKey) {
                case 'fgiftentry':
                    {
                        var promotion = that.getSelectedPromotion();
                        var fgiftmultselect = promotion?.fgiftmultselect.id;

                        // 只允许单选
                        if (fgiftmultselect !== '1') {
                            var gifts = that.Model.getSelectRows({ id: 'fgiftentry' });
                            if (gifts.length > 1) {
                                yiDialog.warn('当前列表不允许多选！');

                                var rows = [gifts[0].pkid];
                                that.Model.setSelectRows({ id: 'fgiftentry', rows: rows });
                            }
                        }
                    }
                    break;
            }
        }

        // 获取选中的商品促销
        _child.prototype.getSelectedPromotion = function (e) {
            var that = this;

            var promotions = that.Model.getSelectRows({ id: 'fpromotionentry' });
            var promotion = null;
            if (promotions.length >= 1) {
                promotion = promotions[0].data;
            }

            return promotion;
        }

        // 确认操作
        _child.prototype.confirm = function (e) {
            var that = this;

            var promotion = that.getSelectedPromotion();
            if (!promotion) {
                e.result = true;
                yiDialog.warn('请选择商品促销！');
                return;
            }

            // 必须勾选赠送商品
            var gifts = that.Model.getSelectRows({ id: 'fgiftentry' });
            if (gifts.length < 1) {
                e.result = true;
                yiDialog.warn('请选择赠送商品！');
                return;
            }

            var retData = {
                fpromotionid: promotion.fpromotionid.id,
                fgiftentry: []
            };

            for (var i = 0; i < gifts.length; i++) {
                var gift = gifts[i].data;

                retData.fgiftentry.push({
                    fmaterialid: gift.fmaterialid.id,
                    fattrinfo: gift.fattrinfo.id,
                    funitid: gift.funitid.id,
                    fqty: gift.fqty
                });
            }

            that.Model.setReturnData(retData);
        }

        return _child;
    })(BillPlugIn);
    window.ydj_selectproductpromotiondialog = window.ydj_selectproductpromotiondialog || ydj_selectproductpromotiondialog;
})();