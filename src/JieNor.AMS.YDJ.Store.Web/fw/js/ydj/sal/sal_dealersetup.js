/*
 * description:账户设置（经销商）控制插件
 * author:
 * create date:
 * modify by:
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/sal/sal_dealersetup.js
*/
; (function () {
    var sal_dealersetup = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.dealerEntryId = 'fentry';

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.dealerEntryId:
                    e.result = { rownumbers: false, multiselect: false };
                    break;
            }
        };

        //初始化编辑页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            //隐藏其他账户信息，只保存贷款
            that.Initfentry();
        };

        //隐藏其他账户信息，只保存贷款
        _child.prototype.Initfentry = function (e) {
            debugger;
            var that = this;
            var rowDatas = that.Model.getValue({ id: 'fentry' });
            var newRowDatas = [];
            if (rowDatas && rowDatas.length > 0) {
                for (var i = 0; i < rowDatas.length; i++) {
                    var fpurpose = rowDatas[i].fpurpose;
                    if (fpurpose && fpurpose.fname && fpurpose.fname == "货款") {
                        newRowDatas.push(rowDatas[i]);
                        break;
                    }
                }
            }
            if (newRowDatas.length == 1) {
                that.Model.setValue({ id: 'fentry', value: newRowDatas });
                that.Model.refreshEntry({ id: 'fentry' });
            }

        }

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fispayment':
                case 'fisbalance':
                    var isSetup = that.Model.getValue({ id: 'fissetup' });
                    if (isSetup) {
                        e.result.enabled = false;
                    }
                    break;
            }
        };

        return _child;
    })(ParameterPlugIn);
    window.sal_dealersetup = window.sal_dealersetup || sal_dealersetup;
})();