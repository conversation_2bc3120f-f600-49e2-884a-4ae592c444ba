///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/sal/sal_returnnotice.js
*/
; (function () {
    var sal_returnnotice = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            if (e.id === 'factualreturnamount') {
                var planreturnamount = that.Model.getValue({id:'fplanreturnamount'});

                var _fsourcenumber = that.Model.getValue({id:'fsourcenumber'});
                if((e.value > planreturnamount ) && $.trim(_fsourcenumber) ){
                    yiDialog.mt({msg:'实退货款金额不允许大于应退货款金额！', skinseq: 2});
                    e.value = planreturnamount;
                    e.result = true;
                }
            }
        };

        return _child;
    })(BasePlugIn);
    window.sal_returnnotice = window.sal_returnnotice || sal_returnnotice;
})();