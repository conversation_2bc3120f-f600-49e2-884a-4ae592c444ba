
/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/pur/ydj_purchaseorder.js
 */
; (function () {
    var ydj_historybill = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            that.deliveridChangeFlag = 0;
            that.billtypeChangeFlag = 0;
            that.podeptidChangeFlag = 0;
            that.loadflag = true;

            that.PartListData = [];
            that.FirstDelete = true;
            that.attrinfoNew = [];

            _super.call(this, args);

            that.InitEntry = [];

            //单据类型对应的参数设置信息，数据结构对应的表单模型为：ydj_purchaseorder_param.mdl.html
            that.billTypeParamSet = {};
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentry = 'fentry';//商品信息

        //页面初始化后触发的事件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            var fstatus = that.Model.getSimpleValue({ id: 'fstatus' });
            that.Model.setEnable({ id: 'fstatus', value: false })
            if (fstatus == '' || fstatus == 'undefind' || fstatus == null) {
                that.Model.setValue({ id: 'fstatus', value: 'B' });
            }
            debugger;
            var fphone = that.Model.getSimpleValue({ id: 'fphone' });
            if (fphone != '' && fphone != 'undefind' && fphone != null) {
                that.Model.setValue({ id: "fphone_e", value: fphone });
            }
            //如果是编辑/查看界面
            if (that.Model.pkid) {
                that.getFirstOrderTime(); 
            }

            var customerid = that.Model.getSimpleValue({ id: 'fcustomerid' });
            if (customerid != '' && customerid != 'undefind' && customerid != null) {
                that.Model.setValue({ id: "fcustomerid", value: customerid });
            }
        };

        _child.prototype.getFirstOrderTime = function (e) {
            var that = this;
            var fphone = that.Model.getSimpleValue({ id: 'fphone' });
            if (fphone != '' && fphone != 'undefind' && fphone != null) {
                that.Model.invokeFormOperation({
                    id: 'selectffirstordertime',
                    opcode: 'selectffirstordertime',
                    param: {
                        fphone: fphone
                    }
                });
            }
        };

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fphone_e':
                    var fphone = that.Model.getSimpleValue({ id: 'fphone_e' });
                    if (fphone != '' && fphone != 'undefind' && fphone != null) {
                        that.Model.setValue({ id: "fphone", value: fphone });
                    }
                    break;
            }
        };

        //根据客户查客户联系人 将客户联系人 默认地址的客户数据带出
        //_child.prototype.GetCustomerByPhone = function (phone) {

        //    var that = this;
        //    var contactobj = {};
        //    var param = {
        //        simpleData: {
        //            formId: 'ydj_customer',
        //            fphone: phone,
        //            domainType: 'dynamic'
        //        }
        //    };
        //    yiAjax.p('/bill/ydj_customer?operationno=getcustomerbyphone', param, function (r) {
        //        that.Model.unblockUI({ id: '#page#' });
        //        var res = r.operationResult;
        //        var srvData = r.operationResult.srvData;
        //        if (res.isSuccess) {
        //            contactobj = srvData;
        //        }
        //    }, null, null, null, { async: false });
        //    return contactobj;
        //};

        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case "selectffirstordertime":
                    console.log(srvData);
                    if (isSuccess & srvData != null && srvData != '') {
                        that.Model.setValue({ id: 'ffirstordertime', value: srvData });
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_historybill = window.ydj_historybill || ydj_historybill;
})();