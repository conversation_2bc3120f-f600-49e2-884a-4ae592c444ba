/// <reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/ydj/ste/ydj_dashboard.js
; (function () {
    var ydj_dashboard = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);
        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
		_child.prototype.taskEntryId = 'ftaskentry';
		_child.prototype.allocationEntryId = 'fallocationentry';
		_child.prototype.participateEntryId = 'fparticipateentry';
        //初始化动态表单插件
        _child.prototype.onInitialized = function (args) {
            this.Model.setVisible({ id: '.page-menu-list', value: false });
            this.Model.setVisible({ id: '.Btn-menu', value: false });

            var operationno = ["sysoverview", "Loadrptdata", "goalinfo", "Loadrptdata"],
                dtType = ["本月", "本周", "本月", "本月"],
                module = ["performance", "tend", "golds", "rank", "task"];
            for (var i = 0; i < module.length; i++) {
                if (module[i] == "golds") {
                    this.conmonAjax(operationno[i], dtType[i], module[i], "部门");
                } else if (module[i] == "rank") {
                    this.conmonAjax(operationno[i], dtType[i], module[i], "check_01");
                } else {
                    this.conmonAjax(operationno[i], dtType[i], module[i]);
                }
            }
            
            this.showMes();

            //显示我的待办任务子页面
            this.Model.showForm({
                formId: 'dashboard_mytask',
                openStyle: 'inContainer',
                param: { containerId: 'mytask' },
                cp: {

                }
            });
        };
        
        _child.prototype.showMes = function (data) {
            this.Model.showForm({
                formId: 'im_mynotice', openStyle: 'inContainer', param: { containerId: 'sys-content' }, cp: {}
            });
        };
        //销售趋势数据渲染
        _child.prototype.initLine = function (data) {
        	if(data.rptGridDataSource.length === 0){
        		this.Model.setVisible({id:'.tendency',value:false});
        		this.Model.setVisible({id:'.tendency_nodata',value:true});
        	}else{
        		if (data.chartDataSource) {
        			this.Model.setVisible({id:'.tendency',value:true});
        			this.Model.setVisible({id:'.tendency_nodata',value:false});
	                var option = data.chartDataSource.rpt_balance.option;
	                yiChart.init(this.Model.getEleMent({ id: '.tendency' }), option);
	            }
        	}
        };
        //目标数据处理及渲染
        _child.prototype.initPie = function (data,type) {
            var goal = 0, realcomplete = 0, returnmoney = 0,fnot=0;
            for (var i = 0; i < data.length; i++) {
                returnmoney += data[i]["回款"];
                goal += data[i]["目标"];
                realcomplete += data[i]["实际完成"];
            }
            
            this.Model.setText({ id: '.{0}-goal'.format(type), value: yiMath.toMoneyFormat(goal, 0)?yiMath.toMoneyFormat(goal, 0):0 + '元' });
            this.Model.setText({ id: '.{0}-realcomplete'.format(type), value: yiMath.toMoneyFormat(realcomplete, 0)?yiMath.toMoneyFormat(realcomplete, 0):0   + '元' });
            var performance = goal == 0 ? 0 : ((realcomplete / goal) * 100).toFixed(2);
            //performance=65;//模拟数据
            if(type=='owner'){
                
                if(performance<100){
                    fnot=100-performance;
                }
                var option = {
                    tooltip: {
                        show:false,
                        trigger: 'item',
                        formatter: "{a} <br/>{b}: {c} ({d}%)"
                    },
                    color:["#FB8D3C","#f2f2f2"],
                    
                    series: [
                        {
                            name:'个人完成率',
                            type:'pie',
                            radius: ['70%', '85%'],
                            avoidLabelOverlap: false,
                            hoveranination:false,
                            silent:true,//设置没有动态效果。
                            label: {
                                
                                normal: {
                                    show: true,
                                    position: 'center',
                                    formatter:function (argument) {
                                        var html;
                                        html='完成率\r\n\r\n'+'{0}%'.format(performance);
                                        return html;
                                    },
                                    textStyle:{
                                       fontSize: 15,
                                        color:'#FB8D3C'
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    show: false
                                }
                            },
                            data:[
                                {value:performance, name:'个人完成率'},
                                {value:fnot, name:'无-1'},
                            ]
                        }
                    ]
                };
                
                yiChart.init(this.Model.getEleMent({ id: '.owner' }), option);
            }else if(type=='depart'){
                
                if(performance<100){
                    fnot=100-performance;
                }
                var option = {
                    tooltip: {
                        show:false,
                        trigger: 'item',
                        formatter: "{a} <br/>{b}: {c} ({d}%)"
                    },
                    color:["#62C576","#f2f2f2"],
                    
                    series: [
                        {
                            name:'部门完成率',
                            type:'pie',
                            radius: ['70%', '85%'],
                            avoidLabelOverlap: false,
                            hoveranination:false,
                            silent:true,
                            label: {
                                normal: {
                                    show: true,
                                    position: 'center',
                                    formatter:function (argument) {
                                        var html;
                                        html='完成率\r\n\r\n'+'{0}%'.format(performance);
                                        return html;
                                    },
                                    textStyle:{
                                       fontSize: 15,
                                        color:'#62C576'
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    show: false
                                }
                            },
                            data:[
                                {value:performance, name:'部门完成率'},
                                {value:fnot, name:'无-2'},
                            ]
                        }
                    ]
                };
                
                yiChart.init(this.Model.getEleMent({ id: '.depart' }), option);
            }else if(type == 'target'){
                
                if(performance<100){
                    fnot=100-performance;
                }
                var option = {
                    tooltip: {
                        show:false,
                        trigger: 'item',
                        formatter: "{a} <br/>{b}: {c} ({d}%)"
                    },
                    color:["#00ABEF","#f2f2f2"],
                    
                    series: [
                        {
                            name:'企业完成率',
                            type:'pie',
                            radius: ['70%', '85%'],
                            avoidLabelOverlap: false,
                            hoveranination:false,
                            silent:true,
                            label: {
                                normal: {
                                    show: true,
                                    position: 'center',
                                    formatter:function (argument) {
                                        var html;
                                        html='完成率\r\n\r\n'+'{0}%'.format(performance);
                                        return html;
                                    },
                                    textStyle:{
                                       fontSize: 15,
                                        color:'#00ABEF'
                                    }
                                }
                            },
                            labelLine: {
                                normal: {
                                    show: false
                                }
                            },
                            data:[
                                {value:performance, name:'企业完成率'},
                                {value:fnot, name:'无-3'},
                            ]
                        }
                    ]
                };
                
                yiChart.init(this.Model.getEleMent({ id: '.target' }), option);
            }
            
        };
        //销售排名数据渲染
        _child.prototype.initCylinder = function (data) {
        	if(data.rptGridDataSource.length === 0){
        		this.Model.setVisible({id:'.ranking',value:false});
        		this.Model.setVisible({id:'.ranking_nodata',value:true});
        	}else{
        		if (data.chartDataSource) {
        			this.Model.setVisible({id:'.ranking',value:true});
        			this.Model.setVisible({id:'.ranking_nodata',value:false});
	                var option = data.chartDataSource.rpt_salesrpt.option;
	                yiChart.init(this.Model.getEleMent({ id: '.ranking' }), option);
	            }
        	}
            
        };
        //销售业绩数据渲染
        _child.prototype.initPerformance = function (data) {
            var that = this;
            
            //销售线索  销售机会 客户 量尺=新增初尺+新增复尺 销售意向  收取定金 销售合同 收取货款
            var re_key = ['线索', '商机', '客户', '量尺','意向', '订金', '合同' , '货款'];
            var re_strKey = ['newleads', 'newcusr', 'newcust', 'newsrecord','newintention', 'cashamount', 'odamount' , 'recvamount'];
            var re_elem=['leads','customerrecord','customer','scalerecord','saleintention','ordermoney','order','payment'];
            for (var i = 0; i < re_strKey.length; i++) {
                for (var m=0,n=data.length;m<n; m++) {
                    var lm=data[m];
                    if (lm&& lm.id==re_strKey[i]) {
                        if(lm.id=='cashamount' || lm.id=='odamount' ||  lm.id=='recvamount'){
                            
                            if(lm.itemVal==parseInt(lm.itemVal)){
                                lm.itemVal=yiMath.toMoneyFormat(lm.itemVal,0)?yiMath.toMoneyFormat(lm.itemVal,0):0;
                            }else{
                                lm.itemVal=(yiMath.toMoneyFormat(lm.itemVal)==0)?0:yiMath.toMoneyFormat(lm.itemVal);
                            }
                            
                            
                        }
                        that.Model.setText({ id: '.' + re_elem[i], value:lm.itemVal });
                        that.Model.setAttr({ id: '.' + re_elem[i], random: 'formId', value: lm.formId });
                        that.Model.setAttr({ id: '.' + re_elem[i], random: 'pkId', value:lm.pkId });
                    }
                }
            }
        };

        _child.prototype.conmonAjax = function (operationno, dtType, module) {
            var that = this;
            if (!operationno) return;
            var formId, url, pacheket,pacheket_2,pacheket_3;
            that.Model.blockUI({ id: '#page#' });
            
            switch(module){
                case 'tend':
                    url = '/report/rpt_balance?operationno=' + operationno; 
                break;
                case 'rank':
                    url = '/report/rpt_salesrpt?operationno=' + operationno;
                break;
                default:
                    formId = "ydj_dashboard";
                    url = '/dynamic/' + formId + '?operationno=' + operationno;
                break;
            }
            
            if (operationno == 'goalinfo' && arguments[3]) {

                pacheket = { simpledata: { dtType: dtType, depart: '部门' } };
                
                pacheket_2 = { simpledata: { dtType: dtType, depart: '自己' } };

                pacheket_3 = { simpledata: { dtType: dtType, depart: '本企业' } };
            } else if (operationno == 'Loadrptdata' && arguments[3]) {
                pacheket = { simpledata: { dtType: dtType, khType: arguments[3] } }
            } else {
                pacheket = { simpledata: { dtType: dtType } }
            }
            

            yiAjax.p(url, pacheket, function (r) {
                
                //隐藏遮罩层
                that.Model.unblockUI({ id: '#page#' });
                
                if (r.operationResult.isSuccess) {
                    var result = r.operationResult.srvData;
                    if (!result) {
                        return;
                    }
                    switch (module) {
                        case 'performance':
                            that.initPerformance(result);
                            break;
                        case 'tend':
                            that.initLine(result);
                            break;
                        case 'golds':
                        
                            that.initPie(result,'depart');
                            if(pacheket_2){
                                getSelf(url,pacheket_2);
                            }
                            break;
                        case 'rank':
                            that.initCylinder(result);
                            break;
                    }
                }
            }, function (m) {
                that.Model.unblockUI({ id: '#page#' });
                yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
            });

            function getSelf(url,pacheket_2){
                
                yiAjax.p(url, pacheket_2, function (r) {
                    
                    //隐藏遮罩层
                    that.Model.unblockUI({ id: '#page#' });
    
                    if (r.operationResult.isSuccess) {
                        var result = r.operationResult.srvData;
                        
                        if (!result) {
                            return;
                        }
                        switch (module) {
                            case 'golds':
                                that.initPie(result,'owner');
                                getCompany(url,pacheket_3)
                            break;
                        }
                    }
                }, function (m) {
                    that.Model.unblockUI({ id: '#page#' });
                    yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
                });
            }

            function getCompany(url,pacheket_3){
                
                yiAjax.p(url, pacheket_3, function (r) {
                    
                    //隐藏遮罩层
                    that.Model.unblockUI({ id: '#page#' });
    
                    if (r.operationResult.isSuccess) {
                        var result = r.operationResult.srvData;
                        
                        if (!result) {
                            return;
                        }
                        switch (module) {
                            case 'golds':
                                that.initPie(result,'target');
                                
                            break;
                        }
                    }
                }, function (m) {
                    that.Model.unblockUI({ id: '#page#' });
                    yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
                });
            }
        };

        //为下拉框准备，数据变化时触发的事件
        _child.prototype.onElementChange = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'rankchange'://改变排名时请求后台数据
                    var filterKey = that.Model.getText({ id: '.active.rank' });
                    var checktype = that.Model.getEleMent({ id: '.check_model option:selected' }).val();
                    that.conmonAjax('Loadrptdata', filterKey, 'rank', checktype);
                    break;
            }
        };
        //点击操作
        _child.prototype.onElementClick = function (e) {
            var that = this;
            var operationno, filterKey, depart, checktype;
            if (!e.id) return;
            switch (e.id) {
                case 'performance':
                    that.Model.removeClass({ id: '.performance', value: 'active' });
                    that.Model.addClass({ id: '.performance.' + e.param.dtType, value: 'active' });
                    filterKey = that.Model.getText({ id: '.active.performance' });
                    operationno = 'sysoverview';
                    break;
                case 'tend':
                    that.Model.removeClass({ id: '.tend', value: 'active' });
                    that.Model.addClass({ id: '.tend.' + e.param.dtType, value: 'active' });
                    filterKey = that.Model.getText({ id: '.active.tend' });
                    operationno = 'Loadrptdata';
                    break;
                case 'golds':
                    if (e.param.elem) {
                        that.Model.removeClass({ id: '.golds-button', value: 'active' });
                        that.Model.addClass({ id: '.golds-button.' + e.param.dtType, value: 'active' });
                    } else {
                        that.Model.removeClass({ id: '.golds', value: 'active' });
                        that.Model.addClass({ id: '.golds.' + e.param.dtType, value: 'active' });
                    }
                    filterKey = that.Model.getText({ id: '.active.golds' });
                    depart = that.Model.getText({ id: '.active.golds-button' })
                    operationno = 'goalinfo';
                    break;
                case 'rank':
                    that.Model.removeClass({ id: '.rank', value: 'active' });
                    that.Model.addClass({ id: '.rank.' + e.param.dtType, value: 'active' });
                    filterKey = that.Model.getText({ id: '.active.rank' });
                    checktype = that.Model.getEleMent({ id: '.check_model option:selected' }).val();
                    operationno = 'Loadrptdata';
                    break;
                case 'task':
                    operationno = 'taskinfo';
                    filterKey = e.param.filterKey;
                    break;
                case 'showPage':
                    var formId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'formId' });
                    var pkId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'pkId' });
                    var timeArea = that.Model.getText({ id: '.active.performance' });
                    if (formId) {
                        if (pkId.length > 0) {
                            that.param = { openStyle: "default" };
                            var url = '/list/' + formId + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: that.param };
                            params.selectedRows.push({ PKValue: pkId });
                            yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                        } else {
                            var url = '/list/' + formId + '?operationno=dtForQuery&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: { openStyle: "default", dtType: timeArea } };
                            yiAjax.p(url, params, function (r) {
                                Index.openForm({
                                    formId: formId,
                                    domainType: Consts.domainType.list,
                                    param: { filterstring: "fcreatedate >='" + r.operationResult.srvData[0] + "' and fcreatedate <'" + r.operationResult.srvData[1] + "'" }
                                });
                            });
                        }
                    }
                    break;
                    
                case 'showscale'://量尺
                    var formId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'formId' });
                    var pkId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'pkId' });
                    var timeArea = that.Model.getText({ id: '.active.performance' });
                    if (formId) {
                        if (pkId.length > 0) {
                            that.param = { openStyle: "default" };
                            var url = '/list/' + formId + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: that.param };
                            params.selectedRows.push({ PKValue: pkId });
                            yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                        } else {
                            var url = '/list/' + formId + '?operationno=dtForQuery&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: { openStyle: "default", dtType: timeArea } };
                            yiAjax.p(url, params, function (r) {
                                Index.openForm({
                                    formId: formId,
                                    domainType: Consts.domainType.list,
                                    param: { filterstring: "fscaledate >='" + r.operationResult.srvData[0] + "' and fscaledate <'" + r.operationResult.srvData[1] + "'" }
                                });
                            });
                        }
                    }
                    break;
                
                case 'showcashPage'://定金
                    var formId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'formId' });
                    var pkId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'pkId' });
                    var timeArea = that.Model.getText({ id: '.active.performance' });
                    if (formId) {
                        if (pkId.length > 0) {
                            that.param = { openStyle: "default" };
                            var url = '/list/' + formId + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: that.param };
                            params.selectedRows.push({ PKValue: pkId });
                            yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                        } else {
                            var url = '/list/' + formId + '?operationno=dtForQuery&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: { openStyle: "default", dtType: timeArea } };
                            yiAjax.p(url, params, function (r) {
                                Index.openForm({
                                    formId: formId,
                                    domainType: Consts.domainType.list,
                                    param: { filterstring: "fdate >='" + r.operationResult.srvData[0] + "' and fdate <'" + r.operationResult.srvData[1] + "' and fcollectedamount>0" }
                                });
                            });
                        }
                    }
                    break;
                case 'showneworderPage'://合同
                    var formId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'formId' });
                    var pkId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'pkId' });
                    var timeArea = that.Model.getText({ id: '.active.performance' });
                    if (formId) {
                        if (pkId.length > 0) {
                            that.param = { openStyle: "default" };
                            var url = '/list/' + formId + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: that.param };
                            params.selectedRows.push({ PKValue: pkId });
                            yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                        } else {
                            var url = '/list/' + formId + '?operationno=dtForQuery&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: { openStyle: "default", dtType: timeArea } };
                            yiAjax.p(url, params, function (r) {
                                Index.openForm({
                                    formId: formId,
                                    domainType: Consts.domainType.list,
                                    param: { filterstring: "forderdate >='" + r.operationResult.srvData[0] + "' and forderdate <'" + r.operationResult.srvData[1] + "'" }
                                });
                            });
                        }
                    }
                    break;
                case 'showpaymentPage'://货款
                    var formId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'formId' });
                    var pkId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'pkId' });
                    var timeArea = that.Model.getText({ id: '.active.performance' });
                    if (formId) {
                        if (pkId.length > 0) {
                            that.param = { openStyle: "default" };
                            var url = '/list/' + formId + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: that.param };
                            params.selectedRows.push({ PKValue: pkId });
                            yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                        } else {
                            var url = '/list/' + formId + '?operationno=dtForQuery&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: { openStyle: "default", dtType: timeArea } };
                            yiAjax.p(url, params, function (r) {
                                Index.openForm({
                                    formId: formId,
                                    domainType: Consts.domainType.list,
                                    param: { filterstring: "forderdate >='" + r.operationResult.srvData[0] + "' and forderdate <'" + r.operationResult.srvData[1] + "' and freceivable-fcollectedamount>0" }
                                });
                            });
                        }
                    }
                    break;
                    
                

                case 'showscaletype01Page':
                    var formId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'formId' });
                    var pkId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'pkId' });
                    var timeArea = that.Model.getText({ id: '.active.performance' });
                    if (formId) {
                        if (pkId.length > 0) {
                            that.param = { openStyle: "default" };
                            var url = '/list/' + formId + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: that.param };
                            params.selectedRows.push({ PKValue: pkId });
                            yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                        } else {
                            var url = '/list/' + formId + '?operationno=dtForQuery&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: { openStyle: "default", dtType: timeArea } };
                            yiAjax.p(url, params, function (r) {
                                Index.openForm({
                                    formId: formId,
                                    domainType: Consts.domainType.list,
                                    param: { filterstring: "fscaledate >='" + r.operationResult.srvData[0] + "' and fscaledate <'" + r.operationResult.srvData[1] + "' and fscaletype ='scale_type01'" }
                                });
                            });
                        }
                    }
                    break;
                case 'showscaletype02Page':
                    var formId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'formId' });
                    var pkId = that.Model.getAttr({ id: '.' + e.param.filterKey, random: 'pkId' });
                    var timeArea = that.Model.getText({ id: '.active.performance' });
                    if (formId) {
                        if (pkId.length > 0) {
                            that.param = { openStyle: "default" };
                            var url = '/list/' + formId + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: tthat.param };
                            params.selectedRows.push({ PKValue: pkId });
                            yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                        } else {
                            var url = '/list/' + formId + '?operationno=dtForQuery&pageId=' + that.Model.viewModel.pageId;
                            var params = { selectedRows: [], simpleData: { openStyle: "default", dtType: timeArea } };
                            yiAjax.p(url, params, function (r) {
                                Index.openForm({
                                    formId: formId,
                                    domainType: Consts.domainType.list,
                                    param: { filterstring: "fscaledate >='" + r.operationResult.srvData[0] + "' and fscaledate <'" + r.operationResult.srvData[1] + "' and fscaletype ='scale_type02'" }
                                });
                            });
                        }
                    }
                    break;
            };

            if (operationno) {
                if (e.id === "golds" && depart && filterKey) {
                    that.conmonAjax(operationno, filterKey, e.id, depart);
                } else if (e.id === "rank" && checktype && filterKey) {
                    that.conmonAjax(operationno, filterKey, e.id, checktype);
                } else {
                    that.conmonAjax(operationno, filterKey, e.id);
                }
            };
        };


        return _child;
    })(BasePlugIn);
    window.ydj_dashboard = window.ydj_dashboard || ydj_dashboard;
})();