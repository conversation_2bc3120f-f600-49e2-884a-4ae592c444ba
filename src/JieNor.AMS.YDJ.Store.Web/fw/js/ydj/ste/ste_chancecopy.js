//销售机会（复制机会）控制插件
; (function () {
    var ste_chancecopy = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
        
        
        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'cancel':
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
					args.result = true;
					var dutyId =that.Model.getSimpleValue({id:'fdutyid'}); 
					if ($.trim(dutyId) == '') {
                        yiDialog.mt({ msg: '请选择员工！', skinseq: 2 });
					}
					else{
                        that.Model.invokeFormOperation({
                            id: 'tbChancecopy',
                            opcode: 'chancecopy',
                            param: {
                                formId: 'ydj_customerrecord',
                                domainType: 'bill',
                                optype: '2',
                                'id': that.Model.uiData.billId,
                                'dutyId': dutyId
                            }
                        });
					}
                    break;
            }
        };
		
		
		//操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            switch (e.opcode) {
                case 'chancecopy':
                    if(isSuccess){
                        //关闭当前弹窗
                        that.Model.close();
                        //var pkid = e.result.id;
                        //that.Model.showForm({
                        //    formId: 'ydj_customerrecord',
                        //    param: { openStyle: Consts.openStyle.modal },
                        //    pkids: [pkid]
                        //});
                    }
                break;
            }
        }
        
	
        return _child;
    })(BasePlugIn);
    window.ste_chancecopy = window.ste_chancecopy || ste_chancecopy;
})();