(function () {
    var rpt_ordertobuy = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);


        //表单初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;
           
        }
       
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase())
            {
                case 'pushpurorder'://下推采购
                    e.result = true;
                    var rows = that.Model.getSelectRows();
                    ////var rows = that.Model.getSelectRows();
                    var rowIds = [];
                    if (rows.length > 0) {
                        rows.forEach(function (row) {
                            //rowIds.push(row.data["fenid"]);
                            rowIds.push({ Id: row.data["fenid"] });
                        });
                    }
                    //debugger;
                    //that.Model.invokeFormOperation({
                    //    id: 'pushpurorder',
                    //    opcode: 'pushpurorder',
                    //    param: {
                    //        formId: 'rpt_ordertobuy',
                    //        'rowIds': rowIds
                    //    }
                    //});


                    var para = {
                        simpleData: {
                            fbillno: "",
                            rowIds: JSON.stringify(rowIds)
                        }
                    }
                    yiAjax.p('/bill/ydj_order?operationno=checkswjorder', para,
                        function (r) {
                            var data = r.operationResult;
                            var issuccess = data.isSuccess;
                            var mes = data.srvData;
                            debugger
                            if (issuccess) {
                                that.Model.invokeFormOperation({
                                    id: 'pushpurorderswj',
                                    opcode: 'pushpurorderswj',
                                    param: {
                                        formId: 'rpt_ordertobuy',
                                        'rowIds': rowIds
                                    }
                                });
                            } else if (mes.length > 0) {
                                yiDialog.warn(mes);
                                return;
                            } else {
                                that.Model.invokeFormOperation({
                                    id: 'pushpurorder',
                                    opcode: 'pushpurorder',
                                    param: {
                                        formId: 'rpt_ordertobuy',
                                        'rowIds': rowIds
                                    }
                                });
                            }
                        })


                    break;
            }
        }

        //表格单元格点击事件
        _child.prototype.onEntryCellClick = function (e) {
            var that = this;
            if (e.id === 'list') {
                var filter = JSON.parse(that.Model.viewModel.getCustomFilterData());
                if (e.fieldId === 'fbillno') {
                    e.result = true;
                    if (!e.data) return;
                    var billno = e.data.fbillno;
                    that.Model.invokeFormOperation({
                        id: 'orderdetail',
                        opcode: 'showDetail',
                        param: {
                            domainType: Consts.domainType.bill,
                            formId: 'ydj_order',
                            billNos: billno
                        }
                    });
                }
                else if (e.fieldId === 'invqty') {
                    e.result = true;
                    if (!e.data || e.data.invqty <= 0) return;
                    debugger;
                    var filterstring = " fmaterialid='" + e.data.fproductid + "') and fattrinfo_e='" + e.data.fattrinfo + "' and fcustomdesc=N'" + e.data.fcustomdesc + "' and fmtono=''  ";
                    var param = {
                        'formId': 'ydj_storehouse',
                        "simpleData": {
                            "fwarehousetype": filter.fwarehousetype.id,
                            "fstorehouseid": filter.fstorehouseid.id
                        }
                    };
                    //不参与统计的仓库，就不显示在点开的详情中
                    yiAjax.p('/bill/ydj_storehouse?operationno=getcalculate', param, function (r) {
                        var res = r.operationResult
                        if (res.isSuccess & res.srvData != null) {
                            var storehouseids = [];
                            var ids = res.srvData.split(',');
                            for (var i = 0; i < ids.length; i++) {
                                storehouseids.push("'" + ids[i] + "'");
                            }
                            filterstring = filterstring + "and fstorehouseid in ({0})".format(storehouseids.join(','));
                        }
                    }, null, null, null, { async: false });
                    that.Model.showList({
                        formId: 'stk_inventorylist',
                        filterstring: filterstring,
                        openStyle: Consts.openStyle.modal
                    });
                }
            }
           
        }
        _child.prototype.onFieldValueFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fbillno':
                case 'invqty':
                    e.cancel = true;
                    e.value = '<span style="color:#529DE3;cursor:pointer;">{0}</span>'.format(e.value);
                    break;
            }
        }

        return _child;
    })(BasePlugIn);
    window.rpt_ordertobuy = window.rpt_ordertobuy || rpt_ordertobuy;
})();