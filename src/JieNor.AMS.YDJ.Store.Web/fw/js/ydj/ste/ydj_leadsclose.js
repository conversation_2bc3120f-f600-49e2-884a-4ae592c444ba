//销售线索（关闭）控制插件
; (function () {
    var ydj_leadsclose = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
                //关闭窗口
             	case 'cancel':
                    args.result = true;
                    that.Model.close();
                break;
                //确认关闭
                case 'confirm':
                    args.result = true;
                    var formID = that.Model.uiData.formid;
                    var parentViewModel = Index.getPage(that.formContext.cp.parentPageId);
                    if(parentViewModel.domainType == 'bill'){
                        var selectedRows = [{PKValue:parentViewModel.Model.uiData.id}]
                    }
                    else{
                        var listId = parentViewModel.Model.getSelectRows({}),
                            selectedRows = [];
                        for(var i=0,j=listId.length;i<j;i++){
                            selectedRows.push({PKValue:listId[i].pkValue});
                        }
                    }
                    yiDialog.c(formID == 'ydj_leads' ? '关闭后，销售线索不允许还原，确认关闭该线索？' : '关闭后，销售机会不允许还原，确认关闭该销售机会？', function () {
                     debugger
                        that.Model.invokeFormOperation({
                            id: 'tbLeadsClose',
                            opcode: parentViewModel.formId == 'ydj_leads' ? 'leadsclose' : 'customerrecordclose',
                            selectedRows: selectedRows,
                            param: {
                                'formId': formID,
                                'closeReason': that.Model.getValue({ id: 'fclosereason' }),
                                'closeEnum': that.Model.getValue({ id: 'fbusinessclos' }).id,
                                'closeEnumName': that.Model.getValue({ id: 'fbusinessclos' }).fname
                            }
                        });
                    });
                break;
            }
        }
        

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            switch (e.opcode) {
                case 'leadsclose':
                case 'customerrecordclose':
                    if(isSuccess){
                        var newRow = {
							flag: 'Y'
						};
                        that.Model.setReturnData({ newRow: newRow });
                        //关闭当前弹窗
                        that.Model.close();
                    }
                break;
            }
        }
        
	
        return _child;
    })(BasePlugIn);
    window.ydj_leadsclose = window.ydj_leadsclose || ydj_leadsclose;
})();