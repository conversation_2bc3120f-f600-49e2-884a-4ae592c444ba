
; (function () {
    var ydj_customercontact = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);

        };
        __extends(_child, _super);

        //页面初始化
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {

            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {

            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'modify':
                    debugger;
                    var row = that.Model.getSelectRows({ id: "fbillhead" });
                    if (row && row.length > 0) {
                        that.param = { openStyle: "inContainer", containerId: 'ydj_customercontact' };
                        var url = '/list/' + "ydj_customercontact" + '?operationno=modify&pageId=' + that.Model.viewModel.pageId;
                        var params = { selectedRows: [], simpleData: that.param };
                        params.selectedRows.push({ PKValue: row[0].pkValue });
                        yiAjax.p(url, params, null, null, null, $(that.pageSelector));
                    }
                    break;
                case 'save':
                    debugger;
                    e.result = true;
                    var customer = '';
                    var parentModel = that.Model.getParentModel();
                    var parentModelObj = JSON.parse(parentModel.viewModel.dynamicParam);
                    customer = parentModelObj.customer;
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: {
                            formId: 'ydj_customercontact',
                            customer: customer
                        }
                    });
                    break;
                //更换负责人
            }
        }
   
        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            switch (e.opcode) {
                case 'save':
                    if (isSuccess) {
                        //关闭当前弹窗
                        that.Model.close();
                        that.Model.refresh();
                    }
                    break;
            }
        }
        return _child;
    })(BillPlugIn);
    window.ydj_customercontact = window.ydj_customercontact || ydj_customercontact;
})();