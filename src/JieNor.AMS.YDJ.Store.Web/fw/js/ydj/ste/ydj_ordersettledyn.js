///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/pur/ydj_ordersettledyn.js
*/
;(function () {
    var ydj_ordersettledyn = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

            //所有可用于订单支付的账户信息
            that.accounts = [];
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        //联合开单信息
        _child.prototype.dutyEntryId = 'fdutyentry';
        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //处理表单渲染事件：可以在此方法里，对页面做动态化渲染处理
        _child.prototype.onPageViewRendering = function () {
            var that = this;

        };

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            that.renewalHeadquart();
            that.HidePaymentdesc();

            var cp = that.formContext.cp;
            if (!cp) return;
            //显示源单信息
            that.Model.setText({id: '.fsourcenumber', value: cp.fsourcenumber});
            that.Model.setText({id: '.fcustomerid', value: cp.fcustomerid.fname});
            that.Model.setText({id: '.fsrcstoreid', value: cp.fsrcstoreid});
            that.Model.setText({id: '.funsettleamount', value: yiMath.toDecimal(cp.funsettleamount, 2)});
            that.Model.setText({id: '.fsettledamount', value: yiMath.toDecimal(cp.fsettledamount, 2)});
            that.Model.setValue({id: 'fdeptid', value: cp.fdeptid});
            that.Model.setValue({id: 'fstaffid', value: cp.fstaffid});
            that.Model.setText({
                id: '.freceivabletobeconfirmed',
                value: yiMath.toDecimal(cp.freceivabletobeconfirmed, 2)
            });
            that.Model.setValue({id: 'famount', value: cp.funsettleamount});
            that.Model.setValue({id: 'fsumamount', value: cp.fsumamount});
            that.Model.setText({id: '.freceivable', value: yiMath.toDecimal(cp.freceivable, 2)})
            if (cp.fcontactunittype) {
                that.Model.setValue({id: 'fcontactunittype', value: cp.fcontactunittype});
                if (cp.fcontactunittype.id == 'contactunittype_02') {
                    that.Model.setVisible({id: '.marketsettleno', value: true});
                }
            }

            if (cp.fcontactunitid) {
                that.Model.setValue({id: 'fcontactunitid', value: cp.fcontactunitid});
            }

            if (cp.fisunifiedcashier) {
                that.Model.setEnable({"id": "fcontactunitid", value: false});
                that.Model.setEnable({"id": "fcontactunittype", value: false});
            }

            //初始化账户数据源
            var fallaccounts = cp.fallaccounts
            if (fallaccounts && fallaccounts.length > 0) {
                //下拉框数据源
                var accountData = [];
                for (var i = 0; i < fallaccounts.length; i++) {
                    var account = fallaccounts[i];
                    if (account.accountName != "货款") {
                        continue;
                    }
                    //是否可用于订单支付
                    if (account.canUseInOrderPay) {
                        that.accounts.push({
                            accountId: account.accountId,
                            accountName: account.accountName,
                            balance: account.balance,
                            canRecharge: account.canRecharge
                        });
                        accountData.push({
                            id: account.accountId,
                            name: account.accountName
                        });
                    }
                }
                that.Model.setComboData({id: 'faccount', data: accountData});
                if (accountData.length > 0) {
                    that.Model.setValue({"id": "faccount", value: accountData[0]});
                } else {
                    var wayDatas = that.Model.viewModel.uiComboData.fway;
                    if (Consts.isdirectsale){
                        wayDatas=that.Model.viewModel.uiComboData.frefundway;
                    }
                    if (wayDatas && wayDatas.length > 0) {
                        Datas = wayDatas.map(function (x) {
                            return {"id": x.id, "name": x.name, number: x.number};
                        });
                        var payData = cloneDatas.find(function (x) {
                            return "payway_01" == x.id
                        });
                        if (payData) {
                            payData.disable = true;
                        }
                        if (Consts.isdirectsale){
                            that.Model.setComboData({id: 'frefundway', data: cloneDatas});
                        }
                        else
                        {
                            that.Model.setComboData({id: 'fway', data: cloneDatas});
                        }
                        var payData = cloneDatas.find(function (x) {
                            return "payway_04" == x.id
                        });
                        if (!payData) {
                            payData = cloneDatas[0];
                        }
                        if (Consts.isdirectsale){
                            that.Model.setValue({"id": "frefundway", "value": payData});
                        }
                        else
                        {
                            that.Model.setValue({"id": "fway", "value": payData});
                        }

                    }
                }
            }

            //显示对方银行账号
            if (cp.fissyn) {
                if (cp.synBankNum && cp.synBankNum.length > 0) {
                    var bankComboData = [{id: '', name: '&nbsp;'}];
                    for (var i = 0; i < cp.synBankNum.length; i++) {
                        bankComboData.push({
                            id: cp.synBankNum[i].accountId,
                            name: '{0} - {1} - {2}'.format(cp.synBankNum[i].bankNum, cp.synBankNum[i].accountName, cp.synBankNum[i].bankName)
                        });
                    }
                    //***************** - 宋纪强 - 招商银行
                    that.Model.setComboData({id: 'fsynbankid', data: bankComboData});
                }
                that.Model.setVisible({id: '.syn-banknum', value: true});
                that.Model.setVisible({id: '.my-banknum', value: false});
            } else {
                that.Model.setVisible({id: '.syn-banknum', value: false});
                that.Model.setVisible({id: '.my-banknum', value: true});
            }
            //当货款金额大于0时，默认选择账户支付
            if (cp != null && cp.fallaccounts.length > 0 && cp.fallaccounts[0].balance > 0) {
                if (Consts.isdirectsale){
                    that.Model.setValue({"id": "frefundway", value: "payway_01"});
                }else{
                    that.Model.setValue({"id": "fway", value: "payway_01"});
                }
            }

            that.DealDutyEntry(true);

            $(".receiptno").bind("keyup", function (e) {
                //that.Model.setValue({ id: "freceiptno", value: this.value.replace(/[^0-9A-Za-z]+$/, '') });
                //平台方法会触发值改变事件，放弃使用
                $(".receiptno").val(this.value.replace(/[^0-9A-Za-z]+/g, ''));
            });

            that.hideOrShowDutyEntryField(that.dutyEntryId, 'fdeptperfratio');
        };

        //总部相关字段 当【登录账号所属经销商.经营类型=直营】默认可见，反之不显示
        _child.prototype.renewalHeadquart = function () {
            debugger
            var that = this;
            var enable = true;
            //直营
            if (Consts.isdirectsale) {
                enable = true;
            } else {
                enable = false;
            }
            that.Model.setVisible({id: '.renewal-info-head', value: !enable});
            that.Model.setVisible({id: '.renewal-info-head-refund', value: enable});
        }

        //表单字段标签点击后触发
        _child.prototype.onFieldLabelClick = function (e) {
            switch (e.id.toLowerCase()) {
                case 'fcontactunitid':
                    //辅助资料没有单独的视图文件，不允许点击标签打开，所以这里取消平台标准通用逻辑
                    e.cancel = true;
                    break;
            }
        };

        _child.prototype.HidePaymentdesc = function () {
            var paynum = $("select[name='paymentdesc']").find('option').length;
            if (paynum < 1) {
                $(".hidepaycs").hide();
                $("select[name='paymentdesc']").attr('required', false)
            }
        }

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            if (e.id === 'famount') {
                var famount = $.trim(e.value).split('.');
                if (famount && famount.length === 2 && famount[1].length > 2) {
                    e.value = yiMath.toNumber(yiMath.toDecimal(e.value, 2));
                    e.result = true;
                    yiDialog.mt({msg: '结算金额只能输入两位小数点！', skinseq: 2});
                }

                var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                var canexcess = storeParam ? storeParam.fcanexcess : false;

                if (canexcess) {
                    return;
                }

                var unSettleAmount = yiMath.toNumber(that.Model.getValue({id: 'funsettleamount'}));
                var way = that.Model.getSimpleValue({"id": "fway"});
                var frefundway = that.Model.getSimpleValue({"id": "frefundway"});
                debugger
                if (Consts.isdirectsale){
                    way = frefundway;
                }
                if (way === "payway_01") {
                    var accountId = that.Model.getSimpleValue({id: 'faccount'});
                    var balance = 0;
                    if (accountId && that.accounts && that.accounts.length > 0) {
                        for (var i = 0; i < that.accounts.length; i++) {
                            if (that.accounts[i].accountId === accountId) {
                                balance = that.accounts[i].balance;
                                break;
                            }
                        }
                    }
                    unSettleAmount = unSettleAmount > balance ? balance : unSettleAmount;
                }

                e.value = yiMath.toNumber(e.value);
                if (e.value > unSettleAmount) {
                    e.value = unSettleAmount;
                    e.result = true;
                    yiDialog.mt({msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2});
                }
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            e.id = e.id.toLowerCase();
            //汇总本次计算额
            if (e.id === 'famount' || e.id.indexOf('settleaccount_type') != -1) {
                var sumAmount = yiMath.toNumber(that.Model.getValue({id: 'famount'}));
                for (var i = 0; i < that.accounts.length; i++) {
                    sumAmount += yiMath.toNumber(that.Model.getValue({id: that.accounts[i].accountId}));
                }
                that.Model.setText({id: '.fsettleamount', value: yiMath.toDecimal(sumAmount, 2)});
                that.Model.setValue({id: 'fsettleamount', value: yiMath.toDecimal(sumAmount, 2)});
            }

            switch (e.id) {
                case "fway":
                case "frefundway":
                    if (e.value.id != "payway_06"
                        && e.value.id != "payway_07"
                        && e.value.id != "payway_08"
                        && e.value.id != "payway_11") {
                        that.Model.setValue({id: "fmybankid", value: ""});
                        that.Model.setValue({id: "fsynbankid", value: ""});
                        that.Model.setValue({id: "fsynbankname", value: ""});
                        that.Model.setValue({id: "fsynbanknum", value: ""});
                        that.Model.setValue({id: "fsynaccountname", value: ""});
                    }
                    that.Model.setValue({id: 'fpayeraccount', value: ""});
                    break;
                case 'faccount':
                    debugger
                    var accountId = $.trim(e.value.id);
                    var balance = 0;
                    var topup = false;
                    if (accountId && that.accounts && that.accounts.length > 0) {
                        for (var i = 0; i < that.accounts.length; i++) {
                            if (that.accounts[i].accountId === accountId) {
                                balance = that.accounts[i].balance;
                                topup = that.accounts[i].canRecharge;

                                break;
                            }
                        }
                    }
                    that.Model.setHtml({id: '.y-account-balance', value: '余额 ' + yiMath.toDecimal(balance, 2)});
                    //that.Model.setVisible({ id: '.y-account-topup', value: topup });
                    that.Model.setAttr({
                        id: '.y-account-topup',
                        random: 'data-param',
                        value: "accountId:'" + accountId + "'"
                    });

                    var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                    var canexcess = storeParam ? storeParam.fcanexcess : false;

                    if (canexcess) {
                        return;
                    }

                    var unSettleAmount = yiMath.toNumber(that.Model.getValue({id: 'funsettleamount'}));
                    //判断只有账户付款才校验客户账户余额
                    var way = that.Model.getSimpleValue({"id": "fway"});
                    var frefundway = that.Model.getSimpleValue({"id": "frefundway"});
                    if (Consts.isdirectsale){
                        way = frefundway;
                    }
                    if (way === "payway_01") {
                        unSettleAmount = unSettleAmount > balance ? balance : unSettleAmount;
                    }
                    var famount = that.Model.getValue({"id": "famount"});
                    if (famount > unSettleAmount) {
                        that.Model.setValue({"id": "famount", "value": unSettleAmount});
                        yiDialog.mt({msg: '由于输入的金额不满足当前支付条件已自动更新！', skinseq: 2});
                    }

                    break;
                case 'famount':
                    that.calculateStaff();
                //that.checkInvoiceNumber();
                case 'fratio':
                case 'famount_ed':
                    that.calculateStaff(e);
                    break;
                case 'fdutyid':
                    //将销售员部门自动填充到人员明细表格中
                    var fdeptid = that.GetDeptByStaff(e.value.id);
                    if (fdeptid && fdeptid != "") {
                        that.Model.setValue({id: 'fdeptid_ed', value: fdeptid, row: e.row, tgChange: false});
                    }
                    break;
                case 'freceiptno':
                    //that.checkInvoiceNumber();
                    break;
                case 'fdeptperfratio':
                    that.calculateStaffAmountAndRatio(e, null, 'fdeptperfratio');
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //充值
                case 'recharge':
                    e.result = true;
                    that.topUp(e);
                    break;
                //取消
                case 'settlecancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //确定
                case 'settleconfirm':
                    debugger
                    e.result = true;
                    var staffOk = that.checkStaff();
                    var deptPerfRatioOk = that.checkStaffRatioIsOneHundredPercent('fdeptperfratio');
                    if (staffOk && deptPerfRatioOk) {
                        //克隆一份当前页面的数据包
                        var cloneData = that.Model.clone();
                        var way = $.trim(cloneData.fway.id);
                        if (Consts.isdirectsale){
                            way= $.trim(cloneData.frefundway.id);
                        }
                        if (!way && !Consts.isdirectsale) {
                            yiDialog.warn('支付方式不能为空！');
                            return;
                        }
                        var frefundway = $.trim(cloneData.frefundway.id);
                        if (!frefundway && Consts.isdirectsale) {
                            yiDialog.warn('收款方式不能为空！');
                            return;
                        }
                        var accounts = [];
                        var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                        var enableMustInputBankId = storeParam ? storeParam.fenablemustinputbankid : true;

                        //处理收款账号(对方银行账号)信息
                        if (way === 'payway_06' ||
                            way === 'payway_07' ||
                            way === 'payway_08' ||
                            (way === 'payway_11' && enableMustInputBankId)) {
                            if (cloneData.fissyn) {
                                var synBankId = $.trim(cloneData.fsynbankid.id);
                                if (!synBankId) {
                                    yiDialog.warn('请选择收款账号！');
                                    return;
                                }
                                var banks = cloneData.synBankNum;
                                if (banks && banks.length > 0) {
                                    for (var i = 0; i < banks.length; i++) {
                                        if ($.trim(banks[i].accountId) === synBankId) {
                                            cloneData.fsynbankname = banks[i].bankName;
                                            cloneData.fsynbanknum = banks[i].bankNum;
                                            cloneData.fsynaccountname = banks[i].accountName;
                                            break;
                                        }
                                    }
                                }
                            } else {
                                if (!$.trim(cloneData.fmybankid.id)) {
                                    yiDialog.warn('请选择银行账号！');
                                    return;
                                }
                            }
                        } else if (way === 'payway_01') {
                            var accountId = that.Model.getSimpleValue({"id": "faccount"});
                            if (!accountId || $.trim(accountId) == "") {
                                yiDialog.warn('请选择账户！');
                                return;
                            }
                            if (accountId && that.accounts && that.accounts.length > 0) {
                                for (var i = 0; i < that.accounts.length; i++) {
                                    if (that.accounts[i].accountId === accountId) {
                                        accounts.push({
                                            accountId: that.accounts[i].accountId,
                                            accountName: that.accounts[i].accountName
                                        });
                                        break;
                                    }
                                }
                            }
                        }

                        //本次结算额
                        var settleAmount = yiMath.toNumber(cloneData.fsettleamount);
                        if (settleAmount < 0.01) {
                            yiDialog.mt({msg: '本次结算额必须大于0！', skinseq: 2});
                            return;
                        }
                        //合同结算收款时是否必须上传凭证
                        var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
                        var image = that.Model.getSimpleValue({id: 'fimage'});
                        var way = that.Model.getSimpleValue({id: 'fway'});//支付方式-非直营
                        var frefundway = that.Model.getSimpleValue({id: 'frefundway'});//收款方式-直营
                        if (storeParam) {
                            //支付方式==账户支付、现金时不需要验证凭证上传
                            if (storeParam.fhasvoucherwhenreceivable && $.trim(image) == '' && $.trim(way) != 'payway_01' && $.trim(way) != 'payway_05' && !Consts.isdirectsale) {
                                yiDialog.mt({msg: '请上传收款凭证！', skinseq: 2});
                                return;
                            }
                            //直营校验收款方式
                            else if (storeParam.fhasvoucherwhenreceivable && $.trim(image) == '' && $.trim(frefundway) != 'payway_01' && $.trim(frefundway) != 'payway_05' && Consts.isdirectsale){
                                yiDialog.mt({msg: '请上传收款凭证！', skinseq: 2});
                                return;
                            }
                        }

                        //待结算金额
                        var canexcess = storeParam ? storeParam.fcanexcess : false;
                        if (!canexcess) {
                            var unSettleAmount = yiMath.toNumber(cloneData.funsettleamount);
                            if (settleAmount > unSettleAmount) {
                                yiDialog.mt({msg: '本次结算额不允许大于待结算金额！', skinseq: 2});
                                return;
                            }
                        }

                        var paymentdesc = $.trim(cloneData.paymentdesc.id);
                        var paynum = $("select[name='paymentdesc']").find('option').length;
                        if (!paymentdesc && paynum > 0) {
                            yiDialog.warn('款项说明不能为空！');
                            return;
                        }

                        var receiptno = that.Model.getValue({id: 'freceiptno'});
                        if (receiptno) {
                            var money = that.Model.getValue({id: 'famount'});
                            that.Model.invokeFormOperation({
                                id: 'checkinvoicenumber',
                                opcode: 'checkinvoicenumber',
                                param: {
                                    'formId': 'coo_incomedisburse',
                                    'freceiptno': receiptno,
                                    'fmoney': money
                                }
                            });
                        } else {
                            //当<销售合同允许超额收款>且<订单超额收款自动转账户余额>开启时
                            var istransfer = storeParam ? storeParam.fistransfer : false;
                            if (canexcess && istransfer) {
                                //支付方式==账户支付,待结算金额为0,时返回
                                if ($.trim(way) == 'payway_01' && yiMath.toNumber(cloneData.funsettleamount) == 0) {
                                    yiDialog.warn('对不起，系统已开启<订单超额自动转账户余额>，订单待结算金额为0，无需再从账户扣款！');
                                    return;
                                }
                                //确认已收
                                var freceivable = yiMath.toNumber(cloneData.freceivable);
                                //收款待确认
                                var receivabletobeconfirmed = yiMath.toNumber(cloneData.freceivabletobeconfirmed);
                                //订单总金额
                                var fsumamount = yiMath.toNumber(cloneData.fsumamount);
                                //【当前收款金额】+【确认已收】 + 【收款待确认】> 【订单总额】
                                if (settleAmount + freceivable + receivabletobeconfirmed > fsumamount) {
                                    var transferamount = yiMath.toDecimal(settleAmount + freceivable + receivabletobeconfirmed - fsumamount, 2);
                                    var isalltransfer = false;
                                    if (freceivable + receivabletobeconfirmed >= fsumamount) {
                                        transferamount = settleAmount;
                                        isalltransfer = true;
                                    }
                                    var msg = '当前订单已超额收款，是否将超额的' + transferamount + '元自动转入账户余额？';
                                    //支付方式==账户支付,改变提示信息，不生成账户收款的收支记录
                                    if ($.trim(way) == 'payway_01') {
                                        msg = '当前订单已超额收款，超额的' + transferamount + '元将不再从账户扣款！';
                                        isalltransfer = false;
                                    }
                                    yiDialog.c(msg, function () {
                                        // 确定 
                                        that.Model.invokeFormOperation({
                                            id: 'tbSettle',
                                            opcode: 'Settle',
                                            billData: [cloneData],
                                            param: {
                                                accounts: JSON.stringify(accounts),
                                                disableTransaction: true,
                                                transferamount: transferamount,
                                                isalltransfer: isalltransfer
                                            }
                                        });
                                    }, function () {
                                        // 取消
                                    }, '温馨提示');
                                    return;
                                }
                            }

                            that.Model.invokeFormOperation({
                                id: 'tbSettle',
                                opcode: 'Settle',
                                billData: [cloneData],
                                param: {
                                    accounts: JSON.stringify(accounts),
                                    disableTransaction: true
                                }
                            });
                        }
                    }
                    break;
            }
        };

        //充值
        _child.prototype.topUp = function (e) {
            var that = this;

            //客户唯一性报备启用【来源门店】时，校验【来源门店】
            var validStore = that.validSourceStore();
            if (!validStore.rsVal) {
                yiDialog.mt({msg: '对不起，客户未设置来源门店，禁止收款和退款。', skinseq: 2});
                return;
            }

            var customerId = $.trim(that.Model.getSimpleValue({id: 'fcustomerid'}));
            if (customerId) {
                that.Model.showForm({
                    formId: 'coo_inpourdialog',
                    param: {openStyle: Consts.openStyle.modal},
                    cp: {
                        fusagetype: {
                            id: e.param.accountId,
                            fnumber: e.param.accountId,
                            fname: ''
                        },
                        pkid: customerId,
                        formId: 'ydj_customer',
                        callback: function (result) {

                        },
                        validStore: validStore
                    }
                });
            }
        };

        //校验用户来源门店
        _child.prototype.validSourceStore = function () {
            var that = this;
            var validResult = {
                rsVal: true,
                mustFlag: false,
                storeId: ""
            };
            var storesysparam = JSON.parse(localStorage.getItem("storesysparam"));
            if (storesysparam.hasOwnProperty('fcustomerunique')) {
                var customerunique = storesysparam.fcustomerunique;
                var mustFlag = customerunique.indexOf("store") >= 0;
                var srcstoreid = $.trim(that.Model.getSimpleValue({id: 'fsrcstoreid'}));
                validResult.mustFlag = mustFlag;
                validResult.storeId = srcstoreid;
                if (mustFlag && (!srcstoreid || srcstoreid == "")) {
                    validResult.rsVal = false;
                }
            }
            return validResult;
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'settle':
                    if (isSuccess) {
                        //设置对话框的返回数据
                        that.Model.setReturnData({isSuccess: true});
                        //关闭对话框
                        that.Model.close();
                    }
                    break;
                case 'checkinvoicenumber':
                    if (isSuccess && srvData) {
                        that.confirming = false;
                        yiDialog.c("【收款小票号】录入重复，您确定继续吗？", function () {
                            if (that.confirming) return;
                            that.confirming = true;

                            that.checkInvoiceNumberPass();
                        }, null, '温馨提示');
                    } else {
                        that.checkInvoiceNumberPass();
                    }
                    break;
            }
        };

        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fexpenseentry':
                    e.result = {multiselect: false, rownumbers: false};
                    break;
            }
        };

        //根据员工带出其主岗位对应的部门(通过mdl配置带出的部门不正确，需要是员工明细勾选的主岗位)
        _child.prototype.GetDeptByStaff = function (fid) {
            var that = this;
            var fdeptid = "";
            var param = {
                simpleData: {
                    formId: 'coo_incomedisburse',
                    fid: fid,
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/coo_incomedisburse?operationno=getdeptbyfid', param, function (r) {
                that.Model.unblockUI({id: '#page#'});
                var res = r.operationResult;
                var srvData = r.operationResult.srvData;
                if (!res.isSuccess) {
                    fdeptid = "";
                } else {
                    fdeptid = srvData["fdeptid"];
                }
            }, null, null, null, {async: false});
            return fdeptid;
        };

        //联合开单数据处理主岗位数据
        _child.prototype.DealDutyEntry = function (isadd, e) {
            var that = this;
            var dutyEntry = that.Model.getEntryData({id: that.dutyEntryId});
            if (isadd) {
                // 固定首行数据
                that.Model.addRow({
                    id: that.dutyEntryId, data: {
                        fismain: true,
                        fdutyid: that.Model.getValue({id: 'fstaffid'}),
                        fdeptid_ed: that.Model.getValue({id: 'fdeptid'}),
                        fratio: 100,
                        famount_ed: that.Model.getValue({id: 'famount'}),
                        fdeptperfratio: 100
                    }
                });
                for (var i = 0; i < dutyEntry.length; i++) {
                    if (dutyEntry[i].fismain) {
                        var row = dutyEntry[i].id;
                        that.Model.setEnable({id: 'fdutyid', row: row, value: false});
                        that.Model.setEnable({id: 'fdeptid_ed', row: row, value: false});
                        break;
                    }
                }
            } else {
                //修改首行数据
                for (var i = 0; i < dutyEntry.length; i++) {
                    if (dutyEntry[i].fismain) {
                        var row = dutyEntry[i].id;
                        if (e && e.id == "fstaffid") {
                            that.Model.setValue({id: 'fdutyid', row: row, value: e.value.id, tgChange: true});
                        }
                        if (e && e.id == "fdeptid") {
                            that.Model.setValue({id: 'fdeptid_ed', row: row, value: e.value.id});
                        }
                        break;
                    }
                }
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    var isMain = that.Model.getValue({id: 'fismain', row: e.row});
                    if (isMain) {
                        e.result = true;
                        yiDialog.mt({msg: '主要销售员不允许删除！', skinseq: 2});
                        return;
                    }
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    that.calculateStaff("delete");
                    that.calculateStaffAmountAndRatio("delete", null, "fdeptperfratio");
                    break;
            }
        }

        //计算销售员金额比例行联动
        _child.prototype.calculateStaff = function (e) {
            var that = this,
                data = that.Model.getEntryData({id: that.dutyEntryId}),
                sumAmount = yiMath.toNumber(that.Model.getValue({id: 'famount'}));
            if (!data || data.length <= 0) {
                return;
            }

            //只有一个导购员时，自动设置为订单总额
            if (data.length === 1) {
                data[0].famount_ed = sumAmount;
                data[0].fratio = 100;
                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }
            //有两个导购员时，修改其中一个导购员的金额或比例后，自动计算另外一个导购员的金额和比例
            if (e && data.length === 2) {
                if (e == "delete") {
                    //删除行小于等于2个人的时候需要做计算给到第一个人，即100%减去第2个人给到第1个人
                    for (var i = 0; i < data.length; i++) {
                        if (i == 0) {
                            data[i].fratio = 100 - data[1].fratio;
                        }
                        data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        //金额字段可编辑，自动反算比例
                        if (e.id == "famount_ed") {
                            if (sumAmount <= 0) continue;
                            e.value = e.value > sumAmount || e.value < 0 ? sumAmount : e.value;
                            if (data[i].id === e.row) {
                                data[i].famount_ed = e.value;
                            } else {
                                data[i].famount_ed = sumAmount - e.value;
                            }
                            data[i].fratio = yiMath.toDecimal(data[i].famount_ed / sumAmount * 100, 2);
                        }
                        //比例字段可编辑，并可自动反算金额
                        if (e.id == "fratio") {
                            e.value = e.value > 100 || e.value < 0 ? 100 : e.value;
                            if (data[i].id === e.row) {
                                data[i].fratio = e.value;
                            } else {
                                data[i].fratio = 100 - e.value;
                            }
                            data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                        }
                    }
                }
                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }

            var useAmt = sumAmount;
            var useRatio = 100.00;
            //单行金额或者比例计算
            for (var i = 0, l = data.length; i < l; i++) {
                //金额字段可编辑，自动反算比例
                if (e && e.id == "famount_ed") {
                    if (sumAmount <= 0) continue;
                    //行所占百分比 = 金额/订单总额 * 100
                    data[i].famount_ed = data[i].famount_ed > sumAmount || data[i].famount_ed < 0 ? 0 : data[i].famount_ed;
                    data[i].fratio = yiMath.toDecimal(data[i].famount_ed / sumAmount * 100, 2);
                } else {
                    var ratio = yiMath.toNumber(data[i].fratio);
                    //金额 = 订单总额 * 行所占百分比
                    data[i].fratio = ratio > 100 || ratio < 0 ? 0 : data[i].fratio;
                    data[i].famount_ed = yiMath.toDecimal(sumAmount * data[i].fratio / 100, 2);
                }
                useAmt -= yiMath.toDecimal(data[i].famount_ed, 2);
                useRatio -= yiMath.toDecimal(data[i].fratio, 2);
            }

            //处理比例/金额尾差分配给主销售员
            var mainrow = data.findIndex(item => item.fismain === true);
            if (useAmt > 0) data[mainrow].famount_ed = yiMath.toDecimal(yiMath.toNumber(data[mainrow].famount_ed) + yiMath.toNumber(useAmt), 2);
            if (useRatio > 0) data[mainrow].fratio = yiMath.toDecimal(yiMath.toNumber(data[mainrow].fratio) + yiMath.toNumber(useRatio), 2);

            that.Model.refreshEntry({id: that.dutyEntryId});
        };

        //检查销售员比例和金额是否填写正确
        _child.prototype.checkStaff = function () {
            var that = this;
            var ratioSum = 0;
            var amountSum = 0;
            var data = that.Model.getEntryData({id: that.dutyEntryId});
            if (data && data.length > 0) {
                for (var i = 0, l = data.length; i < l; i++) {
                    ratioSum = yiMath.toNumber(yiMath.toDecimal(ratioSum, 2));
                    ratioSum += yiMath.toNumber(data[i].fratio);

                    amountSum += yiMath.toNumber(yiMath.toDecimal(data[i].famount_ed, 2));
                }
            }
            if (ratioSum != 100) {
                yiDialog.warn('销售员分配比例总和必须等于100%');
                return false;
            }
            //处理0.1+0.2=0.30000000000000004有IEEE 754双精度浮点数标准造成的，在进行多次小数运算后会累积微小的精度误差。
            //使用小数点后六位
            amountSum = yiMath.toNumber(yiMath.toDecimal(amountSum, 6));
            var sumAmount = yiMath.toNumber(yiMath.toDecimal(that.Model.getValue({id: 'famount'}), 6));
            if (amountSum != sumAmount) {
                yiDialog.warn('销售员分配金额总和必须等于收款金额！');
                return false;
            }
            return true;
        };

        //检查收款小票号跟金额
        _child.prototype.checkInvoiceNumber = function () {
            var that = this;
            var receiptno = that.Model.getValue({id: 'freceiptno'});
            if (receiptno) {
                var money = that.Model.getValue({id: 'famount'});
                that.Model.invokeFormOperation({
                    id: 'checkinvoicenumber',
                    opcode: 'checkinvoicenumber',
                    param: {
                        'formId': 'coo_incomedisburse',
                        'freceiptno': receiptno,
                        'fmoney': money
                    }
                });
            }
        };

        //检查收款小票号跟金额通过
        _child.prototype.checkInvoiceNumberPass = function () {
            var that = this;
            var cloneData = that.Model.clone();
            var accounts = [];
            var way = $.trim(cloneData.fway.id);
            var frefundway = $.trim(cloneData.frefundway.id);
            if (Consts.isdirectsale){
                //直营用收款方式
                way = frefundway;
            }
            if (way === 'payway_01') {
                var accountId = that.Model.getSimpleValue({"id": "faccount"});
                if (accountId && that.accounts && that.accounts.length > 0) {
                    for (var i = 0; i < that.accounts.length; i++) {
                        if (that.accounts[i].accountId === accountId) {
                            accounts.push({
                                accountId: that.accounts[i].accountId,
                                accountName: that.accounts[i].accountName
                            });
                            break;
                        }
                    }
                }
            }

            //当<销售合同允许超额收款>且<订单超额收款自动转账户余额>开启时
            var storeParam = JSON.parse(localStorage.getItem("storesysparam"));
            var canexcess = storeParam ? storeParam.fcanexcess : false;
            var istransfer = storeParam ? storeParam.fistransfer : false;
            //本次结算额
            var settleAmount = yiMath.toNumber(cloneData.fsettleamount);
            if (canexcess && istransfer) {
                //支付方式==账户支付,待结算金额为0,时返回
                if ($.trim(way) == 'payway_01' && yiMath.toNumber(cloneData.funsettleamount) == 0) {
                    yiDialog.warn('对不起，系统已开启<订单超额自动转账户余额>，订单待结算金额为0，无需再从账户扣款！');
                    return;
                }
                //确认已收
                var freceivable = yiMath.toNumber(cloneData.freceivable);
                //收款待确认
                var receivabletobeconfirmed = yiMath.toNumber(cloneData.freceivabletobeconfirmed);
                //订单总金额
                var fsumamount = yiMath.toNumber(cloneData.fsumamount);
                //【当前收款金额】+【确认已收】 + 【收款待确认】> 【订单总额】
                if (settleAmount + freceivable + receivabletobeconfirmed > fsumamount) {
                    var transferamount = yiMath.toDecimal(settleAmount + freceivable + receivabletobeconfirmed - fsumamount, 2);
                    var isalltransfer = false;
                    if (freceivable + receivabletobeconfirmed >= fsumamount) {
                        transferamount = settleAmount;
                        isalltransfer = true;
                    }
                    var msg = '当前订单已超额收款，是否将超额的' + transferamount + '元自动转入账户余额？';
                    //支付方式==账户支付,改变提示信息，不生成账户收款的收支记录
                    if ($.trim(way) == 'payway_01') {
                        msg = '当前订单已超额收款，超额的' + transferamount + '元将不再从账户扣款！';
                        isalltransfer = false;
                    }
                    yiDialog.c(msg, function () {
                        // 确定 
                        that.Model.invokeFormOperation({
                            id: 'tbSettle',
                            opcode: 'Settle',
                            billData: [cloneData],
                            param: {
                                accounts: JSON.stringify(accounts),
                                disableTransaction: true,
                                transferamount: transferamount,
                                isalltransfer: isalltransfer
                            }
                        });
                    }, function () {
                        // 取消
                    }, '温馨提示');
                    return;
                }
            }

            that.Model.invokeFormOperation({
                id: 'tbSettle',
                opcode: 'Settle',
                billData: [cloneData],
                param: {
                    accounts: JSON.stringify(accounts),
                    disableTransaction: true
                }
            });
        };

        //计算直营的导购的业绩金额和业绩比例
        _child.prototype.calculateStaffAmountAndRatio = function (e, amount, ratio) {
            var that = this;
            data = that.Model.getEntryData({id: that.dutyEntryId});
            sumAmount = yiMath.toNumber(that.Model.getValue({id: 'fsumamount'}));
            if (!data || data.length <= 0) {
                return;
            }

            //只有一个导购员时，自动设置为订单总额
            if (data.length === 1) {
                //data[0][amount] = sumAmount;
                data[0][ratio] = 100;

                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }
            //有两个导购员时，修改其中一个导购员的金额或比例后，自动计算另外一个导购员的金额和比例
            if (e && data.length === 2) {
                if (e == "delete") {
                    //删除行小于等于2个人的时候需要做计算给到第一个人，即100%减去第2个人给到第1个人
                    for (var i = 0; i < data.length; i++) {
                        if (i == 0) {
                            data[i][ratio] = 100 - data[1][ratio];
                        }
                        //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                    }
                } else {
                    for (var i = 0; i < data.length; i++) {
                        //金额字段可编辑，自动反算比例
                        if (e.id === amount) {
                            if (sumAmount <= 0) continue;
                            e.value = e.value > sumAmount || e.value < 0 ? sumAmount : e.value;
                            if (data[i].id === e.row) {
                                data[i][amount] = e.value;
                            } else {
                                data[i][amount] = sumAmount - e.value;
                            }
                            data[i][ratio] = yiMath.toDecimal(data[i][amount] / sumAmount * 100, 2);
                        }
                        //比例字段可编辑，并可自动反算金额
                        if (e.id === ratio) {
                            e.value = e.value > 100 || e.value < 0 ? 100 : e.value;
                            if (data[i].id === e.row) {
                                data[i][ratio] = e.value;
                            } else {
                                data[i][ratio] = 100 - e.value;
                            }
                            //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                        }
                    }
                }

                that.Model.refreshEntry({id: that.dutyEntryId});
                return;
            }

            var useAmt = sumAmount;
            var useRatio = 100.00;
            //单行金额或者比例计算
            for (var i = 0, l = data.length; i < l; i++) {
                //金额字段可编辑，自动反算比例
                if (e && e.id === amount) {
                    if (sumAmount <= 0) continue;
                    //行所占百分比 = 金额/订单总额 * 100
                    //data[i][amount] = data[i][amount] > sumAmount || data[i][amount] < 0 ? 0 : data[i][amount];
                    data[i][ratio] = yiMath.toDecimal(data[i][amount] / sumAmount * 100, 2);
                } else {
                    var ratio = yiMath.toNumber(data[i].fratio);
                    //金额 = 订单总额 * 行所占百分比
                    data[i][ratio] = ratio > 100 || ratio < 0 ? 0 : data[i][ratio];
                    //data[i][amount] = yiMath.toDecimal(sumAmount * data[i][ratio] / 100, 2);
                }
                //useAmt -= yiMath.toDecimal(data[i][amount], 2);
                useRatio -= yiMath.toDecimal(data[i][ratio], 2);
            }

            //处理比例/金额尾差分配给主销售员
            var mainrow = data.findIndex(item => item.fismain === true);
            //if (useAmt > 0) data[mainrow][amount] = yiMath.toDecimal(yiMath.toNumber(data[mainrow][amount]) + yiMath.toNumber(useAmt), 2);
            if (useRatio > 0) data[mainrow][ratio] = yiMath.toDecimal(yiMath.toNumber(data[mainrow][ratio]) + yiMath.toNumber(useRatio), 2);

            that.Model.refreshEntry({id: that.dutyEntryId});

        }

        //检查销售员比例和金额是否填写正确
        _child.prototype.checkStaffRatioIsOneHundredPercent = function (ratio) {
            debugger
            var that = this;
            var ratioSum = 0;
            //直营模式下面才进行下面的判断
            if (!Consts.isdirectsale) {
                return true;
            }
            var data = that.Model.getEntryData({id: that.dutyEntryId});
            if (data && data.length > 0) {
                for (var i = 0, l = data.length; i < l; i++) {
                    ratioSum = yiMath.toNumber(yiMath.toDecimal(ratioSum, 2));
                    ratioSum += yiMath.toNumber(data[i][ratio]);

                }
            }
            var errormsg = '';
            if (ratioSum != 100) {
                var errormsg = '';
                switch (ratio.toLocaleLowerCase()) {
                    //部门业绩比例
                    case 'fdeptperfratio':
                        errormsg = '部门业绩比例总和必须等于100%！';
                        break;
                }
                if (errormsg && errormsg !== '') {
                    yiDialog.warn(errormsg);
                }
                return false;
            }
            if (errormsg && errormsg !== '') {
                yiDialog.warn(errormsg);
            }
            return true;
        };

        //直营的时候需要把联合开单的比例显示，经销商隐藏隐藏
        _child.prototype.hideOrShowDutyEntryField = function (dutyEntryId, fieldIdArray) {
            var that = this;
            var dutyEntry = that.Model.getEntryData({id: dutyEntryId});
            var fieldIdList = fieldIdArray.split(',');
            if (Consts.isdirectsale) {
                if (dutyEntry && dutyEntry.length > 0) {
                    for (var i = 0; i < dutyEntry.length; i++) {
                        for (var j = 0; j < fieldIdList.length; j++) {
                            that.Model.setVisible({id: fieldIdList[i], value: true});
                        }
                    }
                }
            } else {
                if (dutyEntry && dutyEntry.length > 0) {
                    for (var i = 0; i < dutyEntry.length; i++) {
                        for (var j = 0; j < fieldIdList.length; j++) {
                            that.Model.setVisible({id: fieldIdList[i], value: false});
                        }
                    }
                }
            }
        }

        return _child;
    })(BasePlugIn);
    window.ydj_ordersettledyn = window.ydj_ordersettledyn || ydj_ordersettledyn;
})();