///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_orderbalance_filter.js
*/
; (function () {
    var rpt_orderbalance_filter = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);


        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            // var myDate = new Date();
            // var year = myDate.getFullYear();
            // var month = myDate.getMonth() + 1;
            // var lastDate = new Date(year, month, 0).getDate();
            // month = month < 10 ? '0' + month : month;
            // that.Model.setValue({ id: 'fdatefrom', value: [year, month, '01'].join("-") });
            // that.Model.setValue({ id: 'fdateto', value: [year, month, lastDate].join("-") });
        };

        
        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            var fldId = e.id.toLowerCase();
            switch (fldId) {
                case 'fdatefrom':
                    var fdateto = that.Model.getValue({ id: 'fdateto' });
                    var diffDays = that.calDiffDays(e.value, fdateto)

                    if (diffDays > 93) {
                        e.result = true;
                        // e.value = that.formatDate(that.setDateMonth(fdateto, 3));
                        // that.Model.setValue({ id: fldId, value: that.setDateMonth(fdateto, 3) });
                        yiDialog.warn('创建时间范围超过3个月，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    
                    if (!e.value || !fdateto) {
                        e.result = true;
                        yiDialog.warn('没有选择创建时间范围，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    break;
                case 'fdateto':
                    var fdatefrom = that.Model.getValue({ id: 'fdatefrom' });
                    var diffDays = that.calDiffDays(fdatefrom, e.value)

                    if (diffDays > 93) {
                        e.result = true;
                        // e.value = that.formatDate(that.setDateMonth(fdatefrom, -3));
                        // that.Model.setValue({ id: fldId, value: that.setDateMonth(fdatefrom, -3) });
                        yiDialog.warn('创建时间范围超过3个月，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    
                    if (!e.value || !diffDays) {
                        e.result = true;
                        yiDialog.warn('没有选择创建时间范围，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    break;
                case 'fdatefrom_delivery':
                    var fdateto_delivery = that.Model.getValue({ id: 'fdateto_delivery' });
                    var diffDays = that.calDiffDays(e.value, fdateto_delivery)

                    if (diffDays > 93) {
                        e.result = true;
                        // e.value = that.formatDate(that.setDateMonth(fdateto_delivery, 3));
                        // that.Model.setValue({ id: fldId, value: that.setDateMonth(fdateto_delivery, 3) });
                        yiDialog.warn('合同交货日期范围超过3个月，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    
                    if (!e.value || !fdateto_delivery) {
                        e.result = true;
                        yiDialog.warn('没有选择合同交货日期范围，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    break;
                case 'fdateto_delivery':
                    var fdatefrom_delivery = that.Model.getValue({ id: 'fdatefrom_delivery' });
                    var diffDays = that.calDiffDays(fdatefrom_delivery, e.value)

                    if (diffDays > 93) {
                        e.result = true;
                        // e.value = that.formatDate(that.setDateMonth(fdatefrom_delivery, -3));
                        // that.Model.setValue({ id: fldId, value: that.setDateMonth(fdatefrom_delivery, -3) });
                        yiDialog.warn('合同交货日期范围超过3个月，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    
                    if (!e.value || !fdatefrom_delivery) {
                        e.result = true;
                        yiDialog.warn('没有选择合同交货日期范围，查询效率会降低，建议一次查询不要超过3个月！');
                        return;
                    }
                    break;
            }
        }

        _child.prototype.calDiffDays = function (dtStart, dtEnd) {
            var date1 = new Date(dtStart);
            var date2 = new Date(dtEnd);

            var Difference_In_Time = date2.getTime() - date1.getTime();

            var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24); 

            return Difference_In_Days;
        }

        _child.prototype.setDateMonth = function (date, month) {
            // 创建新的Date对象以避免修改原日期
            var result = new Date(date);
            // 获取当前月份并减去month
            result.setMonth(result.getMonth() - month);
            // 处理跨年情况（setMonth会自动处理）
            return result;
        }

        _child.prototype.formatDate = function (date, format = 'YYYY-MM-DD') {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
          
            return format
              .replace('YYYY', year)
              .replace('MM', month)
              .replace('DD', day)
              .replace('HH', hours)
              .replace('mm', minutes)
              .replace('ss', seconds);
        }
        
        return _child;
    })(BasePlugIn);
    window.rpt_orderbalance_filter = window.rpt_orderbalance_filter || rpt_orderbalance_filter;
})();