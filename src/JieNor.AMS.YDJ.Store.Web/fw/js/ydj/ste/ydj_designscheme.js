/// <reference path="/fw/js/basepage.js" />
//@ sourceURL=/fw/js/ste/ydj_designscheme.js
; (function () {
    var ydj_designscheme = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            
            
            that.nowRow='';//当前被双击的行
            
        };
        //继承 BasePlugIn
        __extends(_child, _super);
        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'ffileentry';
        _child.prototype.fversion = 'ffileversionentity';
        _child.prototype.designerEntryId = 'fdesignerentry';
        
        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            var designerId = that.Model.getValue({id:'fstylistid'});
                data = that.Model.getEntryData({ id: that.designerEntryId });
            if(designerId.fname != '' && (data.length == 0 ||data.length == 1)){
                
                that.procDesigner(designerId);
            }

            //设置超链接字段
            var imgUrl = that.Model.getValue({id:'fimgurl'});
            
            that.Model.setVisible({id:'.imgurl',value:($.trim(imgUrl))?true:false});
            that.Model.setVisible({id:'.urlspan',value:($.trim(imgUrl))?false:true});

            that.Model.setAttr({id:'.imgurl',random:'href',value:imgUrl});
            
        }
        
        //创建明细表格
        _child.prototype.onCreateGrid = function (args) {
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case this.designerEntryId:
                case this.fversion:
                    args.result = { multiselect: false, rownumbers: false };
                    break;
            }
        };
        
        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //设计成员
                case 'designerinfo':
                    e.result = true;
                    that.Model.showPopForm({ popup: 'designer-info' });
                    break;
                case 'designconfirm':
                    e.result = true;
                    that.Model.hidePopForm({ popup: 'designer-info' });
                    break;
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.designerEntryId:
                    var isMain = that.Model.getValue({ id: 'fismain', row: e.row });
                    if (isMain) {
                        e.result = true;
                        yiDialog.mt({ msg: '主要设计师不允许删除！', skinseq: 2 });
                    }
                    break;
            }
        };
        
        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fstylistid':
                    //将设计师自动填充到人员明细表格中
                    that.procDesigner(e.value);
                break;
                case 'fdesignertype':
                    var data = that.Model.getEntryData({ id: that.designerEntryId });
                    
                    var num=0;
                    for(var i=0,l=data.length; i<l; i++){
                        if(data[i].id == e.row){
                            num=i;
                           
                            break;
                        }
                    }
                    
                    //不是主要设计师不能选择主设计师字段
                    if(e.value.id == 'design_type_01' && num!=0){
                        yiDialog.mt({ msg: '非主设计师不允许选择该分工类型！', skinseq: 2 });
                        setTimeout(function() {
                            that.Model.setValue({ id: 'fdesignertype', row: e.row, value:  { id: '', fnumber: '', fname: '' } });
                        }, 10);
                    }
                    break;
                //选择文件最终稿
                case 'fisfinaldraft':
                    if(e.value){
                        var _fileName = that.Model.getValue({id:'ffilename',row:e.row});
                        var entity=that.Model.getEntryData({id:that.fentity})
                        //判断文件名是否相同，是则设置最终稿
                        for (var k = 0,l=entity.length;k<l;k++) {
                            var _entityName = entity[k].ffilename;
                            if(_fileName == _entityName){
                                that.Model.setValue({id:'fisfinaldraft',row:entity[k].id,value:false});
                            }
                        }
                        that.Model.setValue({id:'fisfinaldraft',row:e.row,value:true});
                    }
                    
                    break;
            }
        }

        //处理设计师自动更新到设计成员明细表格中
        _child.prototype.procDesigner = function (designer) {
            var that = this,
                data = that.Model.getEntryData({ id: that.designerEntryId }),
                mainRowId = '';
            if (data) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].fismain) {
                        mainRowId = data[i].id;
                        break;
                    }
                }
            }
            
            //如果存在主要设计师ID，更新主要设计师字段值
            if (mainRowId) {
                
                that.Model.setValue({ id: 'fdesignerid', value: designer, row: mainRowId });
                that.Model.setValue({ id: 'fdesignertype', value: {
                    id:'design_type_01',
                    fnumber:'主设计师',
                    fname:'主设计师',
                    fenumitem:'主设计师'
                }, row: mainRowId });
            } else {
                
                //添加主要设计师明细行
                if (designer && $.trim(designer.id)) {
                    that.Model.addRow({
                        id: that.designerEntryId,
                        data: { fismain: true,
                                fdesignertype:{
                                    id:'design_type_01'
                                },
                                fdesignerid: designer,
                                fratio: 100 }
                    });
                    
                }
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdesignerid':
                    //设计师信息
                    var isMain = that.Model.getValue({ id: 'fismain', row: e.row });
                    if (isMain) {
                        e.result.enabled = false;
                    }
                    break;
                case 'fdesignertype':
                    //设计师信息
                    var isMain = that.Model.getValue({ id: 'fismain', row: e.row });
                    if (isMain) {
                        e.result.enabled = false;
                    }
                    break;
            }
        }

        //表单元素被单击后
        _child.prototype.onElementClick = function (e) {
        	var that=this;
            var status=that.Model.getSimpleValue({id:'fstatus'});
            switch (e.id.toLowerCase()) {
                case 'glyphicon'://点击上传,用于文件的多版本上传
		            if(status =='D'||status =='E'){//提交或者审核状态不允许点击功能按钮
		        		return;
		        	}
                    that.initUpLoad();
                    break;
                case 'sfile'://打开版本菜单
                	if(status =='D'||status =='E'){//提交或者审核状态不允许点击功能按钮
		        		return;
		        	}
                    this.Model.setStyle({ id: '.version-menu', value: { 'display': 'block' } });
                    break;
                case 'cfile'://关闭版本菜单
                	
                    this.Model.setStyle({ id: '.version-menu', value: { 'display': 'none' } });
                    break;
                case 'last'://上一版本，便于切换各个版本
                	var dataSource=that.Model.getEntryData({id:that.fentity});//当前数据源
                	
                	var rowId='',lm='';//行id
                	for(var i=0,l=dataSource.length; i<l; i++){
                		if(that.nowRow && that.nowRow == dataSource[i].id){
                			if(i == 0){//如果是第一个，就跳到最后一个版本
                				rowId=dataSource[dataSource.length-1].id ;
                				lm=dataSource[dataSource.length-1];
                			}else{
                				rowId=dataSource[i-1].id ;
                				lm=dataSource[i-1];
                			}
                		}
                	}
                	that.nowRow=rowId;
                	//渲染数据
            		that.Model.refreshEntry({id:that.fversion,prow:rowId});
            		// row
            		if(lm){//设置文件的详情和对应的文件id以便下载
            			var img= that.showDetailAndVersion(lm.ffilename);
	           			var src='<img src="'+img+'"/>';
	           			that.Model.setHtml({id:'.ver-img',value:src});
            			that.Model.setAttr({id:'.filedown',random:'attachid',value:lm.ffileid});
            			that.Model.setText({id:'.ver-id',value:lm.ffilename});
	           			that.Model.setText({id:'.ver-size',value:lm.ffilesize});
	           			that.Model.setText({id:'.ver-id-t',value:lm.ffilename});
            		}
                    break;
                case 'next'://下一版本，便于切换各个版本
               		var dataSource=that.Model.getEntryData({id:that.fentity});//当前数据源
               		
                	var rowId='',lm='';
                	for(var i=0,l=dataSource.length; i<l; i++){
                		if(that.nowRow && that.nowRow == dataSource[i].id){
                			if(i == dataSource.length-1){//如果是最后一个，就跳到第一个版本
                				rowId=dataSource[0].id ;
                				lm=dataSource[0];
                			}else{
                				rowId=dataSource[i+1].id ;
                				lm=dataSource[i+1];
                			}
                		}
                	}
                	that.nowRow=rowId;
                	//渲染数据
            		that.Model.refreshEntry({id:that.fversion,prow:rowId});
            		// row
            		if(lm){//设置文件的详情和对应的文件id以便下载
            			var img= that.showDetailAndVersion(lm.ffilename);
	           			var src='<img src="'+img+'"/>';
	           			that.Model.setHtml({id:'.ver-img',value:src});
            			that.Model.setAttr({id:'.filedown',random:'attachid',value:lm.ffileid});
            			that.Model.setText({id:'.ver-id',value:lm.ffilename});
	           			that.Model.setText({id:'.ver-size',value:lm.ffilesize});
	           			that.Model.setText({id:'.ver-id-t',value:lm.ffilename});
            		}
                    break;
                case 'downthe'://下载版本，针对当前版本下载
                    var fileId = that.Model.getAttr({ id: '.filedown', random: 'attachid' });
                    var fileName = that.Model.getText({ id: '.ver-id-t' });
                    yiCommon.downloadFile({
                        data: {
                            fileIds: fileId,
                            fileNames: fileName
                        }
                    });
                	break;
                case 'upthe'://上传，针对当前版本上传
                	if(status =='D'||status =='E'){//提交或者审核状态不允许点击功能按钮
		        		return;
		        	}
                	that.initUpLoad(true,that.nowRow);
                	
                	break;
                case 'downall'://批量下载，
                    //选中行
                    var allFile = that.Model.getSelectRows({ id: that.fentity });
                    //当前表单所有数据
                    var dataSource = that.Model.getEntryData({ id: that.fentity });
                    if (allFile.length == 0) {
                        yiDialog.warn('请先选择要下载的文件！');
                    } else {
                        var fileIds = [];
                        var fileNames = [];
                        for (var i = 0, l = allFile.length; i < l; i++) {
                            var theId = allFile[i];
                            for (var s = 0, k = dataSource.length; s < k; s++) {
                                if (theId.pkid == dataSource[s].id) {
                                    fileIds.push(dataSource[s].ffileid);
                                    fileNames.push(dataSource[s].ffilename);
                                    break;
                                }
                            }
                        }
                        if (fileIds) {
                            //批量下载的时候，进行一个判断处理，如果队列中只有一个，则直接下载文件，如果队列中有多个，则打包下载
                            yiCommon.downloadFile({
                                data: {
                                    fileIds: fileIds.join(),
                                    fileNames: fileNames.join()
                                }
                            });
                        }
                    }
                	break;
                case 'deleteall'://批量删除
                	if(status =='D'||status =='E'){//提交或者审核状态不允许点击功能按钮
		        		return;
		        	}
                	//选中行
                	var allFile= that.Model.getSelectRows({id:that.fentity});
                	var length=allFile.length;
                	if(allFile.length == 0){
                        yiDialog.mt({ msg: '请先选择要删除的文件', skinseq: 2 });
                    }else{
                        for(var i=0,l=length; i<l; i++){
                            var reEntry=that.Model.getSelectRows({id:that.fentity});
                            if(reEntry.length>0){
                                that.Model.deleteRow({id:that.fentity,row:reEntry[0].pkid});
                            }
                            
                        }
                        //删除后统计文件数量
                        var filenum= that.Model.getEntryData({ id: that.fentity }).length;
                        
                        that.Model.setValue({ id: 'ffilenumber',value: filenum});
                        var ids= that.Model.getSelectRows({id:that.fentity});
                        var leng=ids.length;//选中了多少项。
                        that.Model.setHtml({id:'.file-opera-group .count span',value:leng});
                    }
                	break;
                case 'fverdel'://删除版本
                	if(status =='D'||status =='E'){//提交或者审核状态不允许点击功能按钮
		        		return;
		        	}
                	var fileid=that.Model.getAttr({id:'.more-detail',random:'attachid'});
                	that.Model.deleteRow({id:that.fversion,row:fileid});
                	that.Model.setStyle({ id: '.version-menu', value: { 'display': 'none' } });
                	break;
                case 'mark-main'://设置成主版本
                	if(status =='D'||status =='E'){//提交或者审核状态不允许点击功能按钮
		        		return;
		        	}
                	//获得当前版本的
                	var fileid=that.Model.getAttr({id:'.more-detail',random:'attachid'});
                	var dataOne=that.Model.getEntryData({id:that.fentity});//当前数据源
					
					var res=[];//当前数据源
               		
                	var rowId='',lm='';
                	for(var i=0,l=dataOne.length; i<l; i++){
                		if(that.nowRow && that.nowRow == dataOne[i].id){
                			res=dataOne[i];
                		}
                	}
                	res=res.ffileversionentity;
                	
                	var dataSource=that.Model.getEntryData({id:that.fversion,prow:that.nowRow });//当前数据源
					//dataSource.length=0;
					if($.trim(fileid)){
						
						for(var i=0,l=dataSource.length; i<l; i++){
							if(dataSource[i].id==fileid){
								dataSource[i].ffileismain='是';
							}else{
								dataSource[i].ffileismain='否';
							}
	                    	
	                    }
					}
					that.Model.refreshEntry({id:that.fversion,prow:that.nowRow });
                	that.Model.setStyle({ id: '.version-menu', value: { 'display': 'none' } });
                	break;
            }
        };
        _child.prototype.onEntrySelectAll = function(e){
        	var ids= this.Model.getSelectRows({id:this.fentity});
        	var leng=ids.length;//选中了多少项。
        	this.Model.setHtml({id:'.file-opera-group .count span',value:leng});
        }
        _child.prototype.onEntryRowClick = function (e) {
        	var that=this;
        	var ids= that.Model.getSelectRows({id:that.fentity});
        	var leng=ids.length;//选中了多少项。
        	that.Model.setHtml({id:'.file-opera-group .count span',value:leng});
        	that.Model.setStyle({ id: '.version-menu', value: { 'display': 'none' } });
        	if(e.id == that.fversion){//e.id == that.fversion
        		//现在没有直接获取
        		var dataSource=that.Model.getEntryData({id:that.fentity});
        		
        		for(var i=0,l=dataSource.length; i<l; i++){
        			if(dataSource[i].id == that.nowRow){
        				var lm=dataSource[i][that.fversion];
        				
        				for(var s=0,k=lm.length; s<k; s++){
        					if(lm[s].id == e.row){
        						
        						if(lm[s].ffileismain == '是'){//如果当前是主版本，则不允许删除
        							
        							that.Model.setStyle({ id: '.delete-one', value: { 'display': 'none' } });
        						}else{
        							
        							that.Model.setStyle({ id: '.delete-one', value: { 'display': 'block' } });
        						}
	        					//设置文件的详情和对应的文件id以便下载
		            			var img= that.showDetailAndVersion(lm[s].ffilename_es);
			           			var src='<img src="'+img+'"/>';
			           			that.Model.setHtml({id:'.ver-img',value:src});
		            			that.Model.setAttr({id:'.filedown',random:'attachid',value:lm[s].ffileid_es});
		            			that.Model.setText({id:'.ver-id',value:lm[s].ffilename_es});
			           			that.Model.setText({id:'.ver-size',value:lm[s].ffilesize_es});
			           			that.Model.setText({id:'.ver-id-t',value:lm[s].ffilename_es});
			           			//对应的子表单id
			           			that.Model.setAttr({id:'.more-detail',random:'attachid',value:lm[s].id});
            					
        					}
        				}
        			}
        		}
        		
        	}
        };
        
        //表单元素被双击后
        _child.prototype.onEntryRowDblClick = function (e) {
            var that=this;
            switch(e.id){
                case 'ffileentry':
                    if(e.id == that.fversion){
                        
                        return;
                    }
                    e=$.extend({},e,{name:'filedetails'});
                    //Popform(e);//显示在点击的周围
                    that.nowRow=e.row;//为了得到当前双击行，必须要一个变量得到双击行的rowId
                    //渲染数据
                    that.Model.refreshEntry({id:that.fversion,prow:e.row});
                    var dataSource=that.Model.getEntryData({id:that.fentity});
                    if(!dataSource){//如果不存在，返回
                        return;
                    }
                    for(var i=0,l=dataSource.length; i<l; i++){
                        var lm=dataSource[i];
                        if(lm.id == e.row){//设置文件的详情和对应的文件id以便下载
                            var img= that.showDetailAndVersion(lm.ffilename);
                            var src='<img src="'+img+'"/>';
                            that.Model.setHtml({id:'.ver-img',value:src});
                            that.Model.setAttr({id:'.filedown',random:'attachid',value:lm.ffileid});
                            that.Model.setText({id:'.ver-id',value:lm.ffilename});
                            that.Model.setText({id:'.ver-size',value:lm.ffilesize});
                            that.Model.setText({id:'.ver-id-t',value:lm.ffilename});
                        }
                    }
                    break;
                default: 
                    return;
                    break;
            }
        	
            //that.Model.setValue({id:''})
        };

        //文件详情和历史版本
        _child.prototype.showDetailAndVersion = function (name) {
			name=name.split('.');
			
            var fileSuffix = name[name.length-1].toLocaleLowerCase();

            var thumbnail = '/fw/images/';
            switch (fileSuffix) {
                case 'bmp':
                    thumbnail += 'bmp.png';
                    break;
                case 'eps':
                    thumbnail += 'eps.png';
                    break;
                case 'fla':
                    thumbnail += 'fla.png';
                    break;
                case 'gif':
                    thumbnail += 'gif.png';
                    break;
                case 'ind':
                    thumbnail += 'ind.png';
                    break;
                case 'jpg':
                    thumbnail += 'jpg.png';
                    break;
                case 'jsf':
                    thumbnail += 'jsf.png';
                    break;
                case 'pdf':
                    thumbnail += 'pdf.png';
                    break;
                case 'png':
                    thumbnail += 'png.png';
                    break;
                case 'ppt':
                    thumbnail += 'ppt.png';
                    break;
                case 'pptx':
                    thumbnail += 'ppt.png';
                    break;
                case 'psd':
                    thumbnail += 'psd.png';
                    break;
                case 'rar':
                    thumbnail += 'rar.png';
                    break;
                case 'txt':
                    thumbnail += 'txt.png';
                    break;
                case 'vsd':
                    thumbnail += 'vsd.png';
                    break;
                case 'doc':
                    thumbnail += 'word.png';
                    break;
                case 'docx':
                    thumbnail += 'ppt.png';
                    break;
                case 'xls':
                    thumbnail += 'xls.png';
                    break;
                case 'xlsx':
                    thumbnail += 'xls.png';
                    break;
                case 'zip':
                    thumbnail += 'zip.png';
                    break;
                default:
                    thumbnail += 'image.jpg';
                    break;
            }
			return thumbnail;
			
        };
        
        //文件上传，多个文件上传和单个文件上传
        _child.prototype.refreshFileversion = function (_child,rowid) {
            var that=this;
            var anoArr=[];
            var dataSource=that.Model.getEntryData({id:that.fversion,prow:rowid});
            dataSource=dataSource?dataSource:[];
            anoArr=anoArr.concat(dataSource);
            anoArr.push(_child)//推送子表单信息
            
            
            dataSource.length=0;
            for(var i=0,l=anoArr.length; i<l; i++){
                if(anoArr[i]!=_child){
                    anoArr[i].ffileismain='否';
                }else{//把最新上传的设置成主版本
                    anoArr[i].ffileismain='是';
                }
                dataSource.push(anoArr[i]);
            }
            
            that.Model.refreshEntry({id:that.fversion,prow:rowid});
        }
        //文件上传，多个文件上传和单个文件上传
        _child.prototype.initUpLoad = function (iCol, rowid) {
             var that=this;
            
            var cp = {
                allowExt: 'doc,xls,ppt,docx,xlsx,pptx,txt,pdf,rar,zip,arg,gz,z,bmp,gif,jpg,jpeg,pic,png,tif,eve,dhp,dwg',
                limit:(iCol && rowid)?1:9,
                callback: function (result) {
                    
                    if (!result || !result.files) return;
                    
                    var _reFileId=result.files.id.split(',');
                    var _reFileName=result.files.name.split(',');
                    
                    var _reFileSize=result.files.sizes.split(',');
 
                    for (var i = 0, l = _reFileId.length; i < l; i++) {
                        
                        var _parent = { "ffilestype": that.filetype(_reFileName[i]),
                         "ffilename": _reFileName[i],
                          "ffileid":_reFileId[i], 
                          "ffilesize": (_reFileSize && _reFileSize[i])?_reFileSize[i]:0,
                           "fsettime": "0", 
                           "foperationid": "", 
                           "flastupdate": new Date().ToString('yyyy-MM-dd HH:mm:ss'),
                            "ffileversionentity": [] 
                        };
                        var _child = { 
                            "ffilename_es": _reFileName[i], 
                            "ffileid_es": _reFileId[i], 
                            "fdate_es": new Date().ToString('yyyy-MM-dd HH:mm:ss'),
                             "fsettime": "0", "ffilesize_es": (_reFileSize && _reFileSize[i])?_reFileSize[i]:0,
                              "foperationid_es": "", "ffiledescription_es": "", 
                              "ffileismain": "是" 
                        };
                        if ( iCol == null) {
                            _parent["ffileversionentity"] = [_child] ;
                            
                            
                        }
                        
                        if(iCol && rowid){
                            
                            that.refreshFileversion(_child,rowid);
                            
                            return;
                        }else{
                            that.Model.addRow({ id: that.fentity, data: _parent });
                        }
                    }
                    //渲染数据
                    that.Model.refreshEntry({ id: that.fentity });
                    
                }
            };
            
            //弹出流程发布对话框
            that.Model.showForm({
                formId: 'bd_uploadfile',
                param: {
                    openStyle: Consts.openStyle.modal
                },
                cp: cp
            });
            
        };
		
		
        _child.prototype.filetype = function() {
            var _p = ["jpg", "png"];
            var _e = "dwg";
            var _val = arguments[0];
            _val = _val.split('.');
            _val = _val[_val.length - 1].toLocaleLowerCase();

            if (_p.indexOf(_val) > -1) {//ES5.0
                return { id: "Effect_Drawing", fname: "效果图",fnumber:"效果图",fenumitem:"效果图" };
            }
            if (_e == _val) {
                return { id: "Primeval_Drawing", fname: "原始图纸",fnumber:"原始图纸",fenumitem:"原始图纸"  };
            }
            return { id: "Other", fname: "其他" ,fnumber:"其他",fenumitem:"其他"};
        };
        return _child;
    })(BasePlugIn);
    window.ydj_designscheme = window.ydj_designscheme || ydj_designscheme;
})();