/*
 * description:销售发票档案业务控制插件
 * author:
 * create date:
 * modify by: linus.
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/ste/ste_saleinvoice.js
*/
; (function () {
    var ste_saleinvoice = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.EntryId = 'fentity';
        
        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            //如果是新增，下推加载单据类型参数设置
            if (that.formContext.status == 'new' || that.formContext.status == 'push') {
                that.loadBillTypeParamSet();
            }
            //兼容下推过来的明细数据没有自动计算到表头的问题
            if(that.formContext.status == 'push'){
                var entryData = that.Model.getValue({id:that.EntryId});
                var amount = 0;
                for(var i=0,j=entryData.length;i<j;i++){
                    amount += entryData[i].ftaxamount;
                }
                that.Model.setValue({id:'fsumtaxamount',value:amount});
            }
        }

        //加载单据类型参数设置
        _child.prototype.loadBillTypeParamSet = function (billTypeId) {
            var that = this;
            var typeChange = true;
            if (billTypeId === undefined) {
                billTypeId = $.trim(that.Model.getSimpleValue({ id: 'fbilltype' }));
                typeChange = false;
            }
            if (that.formContext.status === 'push') {
                typeChange = true;
            }
            if (!billTypeId) return;
            that.Model.invokeFormOperation({
                id: 'getparamset',
                opcode: 'getparamset',
                opctx: { typeChange: typeChange },
                param: {
                    domainType: Consts.domainType.bill,
                    formId: 'bd_billtype',
                    billTypeId: billTypeId
                }
            });
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'ftaxrate':
                    var val = e.value;
                    if (val > 100 || val < 0) {
                        yiDialog.mt({ msg: '税率范围为[0, 100]！', skinseq: 2 });
                        setTimeout(function () {
                            that.Model.setValue({ id: 'ftaxrate', value: 0 });
                        }, 10);
                    }
                    break;
                case 'fbilltype':
                    if (that.formContext.status == 'new' || that.formContext.status == 'push') {
                        that.loadBillTypeParamSet(e.value.id);
                    }
                    break;
                case 'fcustomerid':
                    var dataEntry = that.Model.getEntryData({ id: that.EntryId});
                    dataEntry.length = 0;
                    //刷新表格
                    that.Model.refreshEntry({ id: that.EntryId });
                    break;
            }
        }

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
                case 'pull':
                    args.result = true;
                    var customerId = that.Model.getSimpleValue({id:'fcustomerid'});
                    var dataEntry = that.Model.getValue({id:that.EntryId});
                    var sourceNumber = '';
                    for(var i=0,j=dataEntry.length;i<j;i++){
                        sourceNumber += " and fbillno<>'"+dataEntry[i].fsourcenumber+"'"
                    }
                    that.Model.showSelectForm({
                        formId:'ydj_order',
                        selectMul: true,
                        dynamicParam: {
                            formId: 'ste_saleinvoice', 
                            filterString: "fstatus='E' and fcustomerid='"+customerId+"'"+sourceNumber
                        }
                    });
                    break;
            }
        }

        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;
            switch (e.formId) {
                case 'ydj_order':
                    //填充数据。
                    var data=e.data;
                    if(!data){
                        return;
                    }
                    for(var i=0,j=data.length;i<j;i++){
                        var ftaxamount = 0;
                        if(typeof(data[i].fsumamount) == 'number'){
                            ftaxamount = data[i].fsumamount;
                        }
                        else{
                            ftaxamount = yiMath.toNumber(data[i].fsumamount.replace(/,/gi,''));
                        }
                        var addRowData = {};
                        addRowData = {
                            'fsourcetype':{
                                fname:'销售合同',
                                fnumber:'ydj_order',
                                id:'ydj_order'
                            },
                            'fsourcenumber':data[i].fbillno,
                            'ftaxamount':ftaxamount
                        };
                        that.Model.addRow({id:that.EntryId,data:addRowData});
                    }
                    //填充表头数据
                    if($.trim(that.Model.getValue({id:'faddress'})) == ''){
                        that.Model.setValue({id:'faddress',value:data[0].faddress});
                    }
                    if($.trim(that.Model.getValue({id:'fphone'})) == ''){
                        that.Model.setValue({id:'fphone',value:data[0].fphone});
                    }
                    e.result = true;
                    break;
            }

        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.EntryId:
                    e.result = { multiselect:false };
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'getparamset':
                    if(!srvData){
                        return;
                    }
                    that.Model.setValue({id:'ftaxrate',value:srvData.ftaxrate});
                    break;
            }
        }

        return _child;
    })(BillPlugIn);
    window.ste_saleinvoice = window.ste_saleinvoice || ste_saleinvoice;
})();