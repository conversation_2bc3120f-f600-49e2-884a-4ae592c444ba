// <reference path="/fw/js/basepage.js" />
/* @ sourceURL=/fw/js/ydj/bas/ydj_position.js */
; (function () {
    var ydj_position = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            if (that.checkIsAuth(e)) {
                that.setIframeUrl(e);
            }
        }

        _child.prototype.checkIsAuth = function (e) {
            var that = this;
            var IsAuth = false;
            var param = {
                'formId': 'ms_authcenterparam',
                'domainType': 'parameter'
            };
            yiAjax.p('/bill/ms_authcenterparam?operationno=LoadAuthParam', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (!res.isSuccess) {
                    IsAuth = false;
                }
                else {
                    if (res.srvData) {
                        IsAuth = res.srvData["fisuseauth"];
                    }
                }
            }, null, null, null, { async: false });
            return IsAuth;
        };
      
        //设置认证站点iframe地址
        _child.prototype.setIframeUrl = function (e) {
            var that = this;
            //如果是从 业务表单选择 即存在父页面 不能跳转到认证，同时隐藏新增、删除按钮
            var parentPageId = Index.getPage(that.Model.viewModel.parentPageId);
            if (parentPageId) {
                that.Model.setEnable({ id: '#tbNew', value: false });
                that.Model.setEnable({ id: '#tbCopy', value: false });
                that.Model.setEnable({ id: '#tbSubmit', value: false });
                that.Model.setEnable({ id: '#tbUnsubmit', value: false });
                that.Model.setEnable({ id: '#tbAudit', value: false });
                that.Model.setEnable({ id: '#tbUnaudit', value: false });
                that.Model.setEnable({ id: '#tbForbid', value: false });
                that.Model.setEnable({ id: '#tbUnforbid', value: false });
                that.Model.setEnable({ id: '#tbDelete', value: false });
                that.Model.setEnable({ id: '#tbImportExcelData', value: false });
                that.Model.setEnable({ id: '#tbSave', value: false });
                return;
            }

            Index.openForm({
                formId: 'sec_setiframeurl',
                domainType: 'bill',
                formid: "ydj_position",
                cp: { formid: "ydj_position", opcode: "modify", pageid: that.Model.viewModel.pageId}
            });
        };

        _child.prototype.onEntryRowDblClick = function (e) {
            debugger;
            var that = this;
            if (that.checkIsAuth(e)) {
                var parentPageId = Index.getPage(that.Model.viewModel.pageId);
                if (!parentPageId) {
                    //不存在父页面 替换原有的方式 双击不再打开修改页面而是打开iframe
                    e.result = true;
                    //that.setIframeUrl(e);
                }
            }
        }

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            debugger;
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'new':
                case 'copy':
                case 'delete':
                case 'forbid':
                case 'unforbid': 
                case 'importexceldata':
                    if (that.checkIsAuth(e)) {
                        //替换原有的方式 双击不再打开修改页面而是打开iframe
                        e.result = true;
                        that.setIframeUrl(e);
                    }
                    break;
            }
        }

        return _child;
    })(BasePlugIn);
    window.ydj_position = window.ydj_position || ydj_position;
})();