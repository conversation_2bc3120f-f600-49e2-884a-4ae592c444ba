(function () {
    var bas_addressselect = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.flag = true;
        };
        __extends(_child, _super);

        _child.prototype.onInitialized = function (args) {
            var that = this;

            var cp = that.formContext.cp;
            if (!cp) return;
            that.Model.setValue({ id: 'fprovince', value: cp.fprovince });
            that.Model.setValue({ id: 'fcity', value: cp.fcity });
            that.Model.setValue({ id: 'fregion', value: cp.fregion });

        }

        _child.prototype.onMenuItemClick = function (args) {
            debugger;
            var that = this;

            switch (args.opcode) {
                case "filterconfirm":
                    args.result = true;
                    that.Model.setReturnData({
                        isSuccess: true,
                        fprovince: that.Model.getValue({ id: "fprovince" }),
                        fcity: that.Model.getValue({ id: "fcity" }),
                        fregion: that.Model.getValue({ id: "fregion" }),
                        frow: that.formContext.cp.frow
                    });
                    that.Model.close();
                    break;
            }
        }

        return _child;
    })(BasePlugIn);
    window.bas_addressselect = window.bas_addressselect || bas_addressselect;
})();