///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/bas/bas_agentlevelfilterparam.js
*/
; (function () {
    var bas_agentlevelfilterparam = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        _child.prototype.agentEntry = 'fentry';

        //��ʼ��ҳ����
        _child.prototype.onInitialized = function (e) {
            var that = this;
        };

        //�ֶ�ֵ�ı��
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fagentid':
                    that.getAgentDistributorNumberAndName(e);
                    break;
                default:
                    break;
            }
        }

        _child.prototype.getAgentDistributorNumberAndName = function (e) {
            var that = this;
            var rowId = e.row;
            var fagentId = e.value.id;
            var param = {
                formId: "bas_agentlevelfilterparam",
                rowId: rowId,
                fagentId: fagentId
            };
            e.result = true;
            that.Model.invokeFormOperation(
                {
                    id: "getdistributornumberandname",
                    opcode: "getdistributornumberandname",
                    formId: "bas_agentlevelfilterparam",
                    param: param
                });
        }

        //�����ɹ��󴥷����¼�
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            switch (e.opcode) {
                case 'getdistributornumberandname':
                    that.assignmentDistributorNumberAndName(isSuccess, srvData);
                    break;
            }
        }

        _child.prototype.assignmentDistributorNumberAndName = function (isSuccess, srvData) {
            var that = this;
            if (isSuccess) {
                if (srvData.isOk) {
                    var agentEntry = that.Model.getEntryData({ id: that.agentEntry });
                    var rowId = srvData.rowId;
                    for (var i = 0; i < agentEntry.length; i++) {
                        if (agentEntry[i].id == rowId) {
                            var distributorNumber = srvData.distributorNumber;
                            var distributorName = srvData.distributorName;
                            that.Model.setValue({ id: 'fdistributorsnumber', value: distributorNumber, row: agentEntry[i].id });
                            that.Model.setValue({ id: 'fdistributorsname', value: distributorName, row: agentEntry[i].id });
                        }
                    }
                }
                else {
                    var message = srvData.message;
                    yiDialog.warn(message);
                }
            }
        }
        return _child;
    })(ParameterPlugIn);
    window.bas_agentlevelfilterparam = window.bas_agentlevelfilterparam || bas_agentlevelfilterparam;
})();