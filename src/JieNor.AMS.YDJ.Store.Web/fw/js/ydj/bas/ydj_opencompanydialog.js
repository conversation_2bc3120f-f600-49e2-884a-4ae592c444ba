; (function () {
    var ydj_opencompanydialog = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
       
        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            
            yiAjax.g('/company/register/list.json?phone=' +'' , null,
                function (r) {
                    
                    if (r && r.operationResult && r.operationResult.isSuccess) {
                        var srvData = r.operationResult.srvData;
                        if (srvData && srvData.siteHost && srvData.siteHost.length>0) {
                            
                            var optHtml='';
                            for (var i = 0; i < srvData.siteHost.length; i++) {
                                var siteData = srvData.siteHost[i];
                                optHtml += '<option value="{0}">{1}</option>'.format(siteData.id, siteData.siteName);
                            }
                            
                            that.Model.getEleMent({id:'[name=fservicelineid]'}).html(optHtml).select2({ placeholder: '请选择服务线路', allowClear: true }).select2('val', '');
                        }
                    }
                    
                },
                function (m) {
                    yiCommon.showError(yiCommon.extract(m));
                }
            );
        };

        //模型数据初始化完毕
        _child.prototype.onModelDataCreated = function (e) {
            
        };
        
        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            
            switch (e.id.toLowerCase()) {
                //开通企业
                case 'tbconfirm':
                    var data = {};
                    data.param = {};
                    data.param.serviceLineId = that.Model.getSimpleValue({ id: 'fservicelineid' });
                    data.param.initAdminPwd = that.Model.getSimpleValue({ id: 'finitadminpwd' });
                    
                    //校验收集的数据
                    if (!data.param.serviceLineId) {
                        e.result = true;
                        yiDialog.mt({ msg: '请选择站点开通线路！', skinseq: 2 });
                        return;
                    }

                    if (!data.param.initAdminPwd) {
                        e.result = true;
                        yiDialog.mt({ msg: '请提供系统初始化管理员密码！', skinseq: 2 });
                        return;
                    }
                    
                    that.Model.setReturnData(data);
                    
                    break;
                //取消
                case 'tbcancel':
                    that.Model.setReturnData({});
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_opencompanydialog = window.ydj_opencompanydialog || ydj_opencompanydialog;
})();