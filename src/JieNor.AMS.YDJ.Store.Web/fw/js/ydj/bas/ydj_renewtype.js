(function () {
    var fbizorgids = {};
    var ydj_renewtype = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            that.flag = true;

        };
        __extends(_child, _super);

        _child.prototype.ischange = false;
        //_child.prototype.fbizorgids = [];


        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //编辑时处理相关字段的隐藏和显示
            var packData = that.Model.getEntryData({ id: "fentry" });
            for (var i = 0; i < packData.length; i++) {
                that.Model.setValue({ id: 'fcarea', value: packData[i].fprovince_s["fname"] + packData[i].fcity_s["fname"] + packData[i].fregion_s["fname"], row: packData[i].id });
            }
            var isv6 = that.Model.getSimpleValue({ id: "fisv6" });
            if (isv6) {
                that.Model.setVisible({ id: '.categoryentryentry', value: true });
            } else {
                that.Model.setVisible({ id: '.categoryentryentry', value: false });
            }
            debugger
            fbizorgids = JSON.parse(JSON.stringify(that.Model.getValue({ id: "fbizorgid" })));
        }

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            if (!e.row) return;
            switch (e.id.toLowerCase()) {
                case "fbizorgid":
                    debugger
                    var fentry = that.Model.getEntryData({ id: "fentry" });
                    var fenableaddress_gb = that.Model.getSimpleValue({ id: "fenableaddress_gb" });
                    if (fentry && fentry.length > 0 && fenableaddress_gb === true) {
                        var filteredObjects = fentry.filter(item => item.fagentid.id != '');
                        if (filteredObjects && filteredObjects.length > 0) {
                            that.showSubmitChangeDialog();
                        }
                    }
                    break;

            }
        }

        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fisv6':
                    var isv6 = that.Model.getSimpleValue({ id: "fisv6" });
                    if (isv6) {
                        that.Model.setVisible({ id: '.categoryentryentry', value: true });
                    } else {
                        that.Model.setVisible({ id: '.categoryentryentry', value: false });
                    }
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            var fbilltypeid = that.Model.getValue({ "id": "fbilltype" })
            var billtypeNo = fbilltypeid ? fbilltypeid.fnumber : "";
            var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
            switch (e.id.toLowerCase()) {
                //表头-所属经销名称
                case 'fbizorgid':
                    var fmainorgid = that.Model.getSimpleValue({ id: "fmainorgid" });
                    //【所选组织名称】可选组织类型=经销商（引用当前组织下可选的组织，可选：本组织+本组织的下级二级分销组织）
                    if (Consts.loginCompany?.id === fmainorgid) {
                        e.result.filterString = "fparentid='" + fmainorgid + "' or fid='" + fmainorgid + "'";
                    }
                    break;
                //表体-所属经销名称
                case 'fagentid':
                    var fbizorgid = that.Model.getSimpleValue({ id: "fbizorgid" });
                    if (fbizorgid == '') {
                        yiDialog.mt({ msg: '请先选择【所属组织名称】再添加国补经销商白名单信息！', skinseq: 2 });
                        return;
                    }

                    //①当【所属组织名称-已选组织类型=总部】：此处可选经销商=总部组织下全部经销商信息（引用《经销商》业务表单）。
                    var fbizorgid = that.Model.getValue({ id: "fbizorgid" });
                    if (fbizorgid && fbizorgid.forgtype === '1') {
                        //所属总部组织id
                        e.result.filterString = "ftopcompanyid='" + fbizorgid.ftopcompanyid + "'";
                    }
                    //②当【所属组织名称 - 已选组织类型=经销商】，此处可选经销商 = 本经销商下全部经销商信息（引用《经销商》业务表单，如：当前创建账户归属的经销商 + 本经销商的下级二级分销商）。
                    else if (fbizorgid && fbizorgid.forgtype === '4') {
                        e.result.filterString = "fparentid='" + Consts.loginCompany?.id + "' or fid='" + Consts.loginCompany?.id + "'";
                    }

                    break;
            }
        };

        //显示变更对话框
        _child.prototype.showSubmitChangeDialog = function () {
            var that = this;
            var html = '\
            <div class="form-group row">\
                <div class="col-md-12" style="font-size: 16px;">\
                    修改所属组织名称, 会清空所有国补白名单信息，是否要继续操作？\
                </div>\
            </div>';
            yiDialog.d({
                type: 1,
                content: html,
                resize: false,
                title: '提示',
                area: ['450px', '200px'],
                btn: ['取消', '确定'],
                btncls: ['', '0'],
                yes: function (index, layero) {
                    debugger
                    that.Model.setValue({ id: 'fbizorgid', value: fbizorgids });
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    debugger
                    var fentry = that.Model.getEntryData({ id: "fentry" });
                    if (fentry && fentry.length > 0) {
                        for (var i = 0; i < fentry.length; i++) {
                            that.Model.deleteRow({ id: "fentry", row: fentry[i].id });
                        }
                        yiDialog.warn('国补白名单信息已清空！');
                        fbizorgids = JSON.parse(JSON.stringify(that.Model.getValue({ id: "fbizorgid" })));;
                    }
                    layer.close(index);
                    that.ischange = true;
                    return false;
                },
                success: function (layero, index) {

                }
            });

        };

        //选择地区
        _child.prototype.selectarea = function (e) {
            var that = this;
            var rowData = that.Model.getEntryRowData({ id: 'fentry', row: e.row });
            var cp = {
                fusagetype: {
                    id: rowData.id
                },
                pkid: that.Model.pkid,
                formId: 'ydj_renewtype',
                callback: function (result) {
                    if (!result || !result.newRow) { return; }
                    if (result.oldIded) {
                        that.oldId = result.oldIded;
                    }
                    that.setTableData(result.newRow);
                }
            };

            //选择地区对话框
            that.Model.showForm({
                formId: 'ydj_areadialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };

        //渲染联系人信息表格
        _child.prototype.setTableData = function (tableData) {
            //此处给表格赋值
            var that = this;
            that.Model.setValue({ id: 'fcarea', value: tableData.fprovince["fname"] + tableData.fcity["fname"] + tableData.fregion["fname"], row: tableData.fusagetype["id"] });
            that.Model.setValue({ id: 'fprovince_s', value: { id: tableData.fprovince["id"], fnumber: tableData.fprovince["fnumber"], fname: tableData.fprovince["fname"] }, row: tableData.fusagetype["id"] });
            that.Model.setValue({ id: 'fcity_s', value: { id: tableData.fcity["id"], fnumber: tableData.fcity["fnumber"], fname: tableData.fcity["fname"] }, row: tableData.fusagetype["id"] });
            that.Model.setValue({ id: 'fregion_s', value: { id: tableData.fregion["id"], fnumber: tableData.fregion["fnumber"], fname: tableData.fregion["fname"] }, row: tableData.fusagetype["id"] });
        };
        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.btnid.toLowerCase()) {
                case 'selectarea':
                    that.selectarea(e);
                    break;
            }
        };
        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            if (that.formContext.domainType == 'bill') {
                var flag = that.Model.getValue({ id: 'id' });
                if (!flag || flag == '') {
                    return e.result = [
                        {
                            id: 'selectarea',
                            text: '选择地区',
                            disabled: true
                        }
                    ];
                }
            }
        };
        return _child;
    })(BasePlugIn);
    window.ydj_renewtype = window.ydj_renewtype || ydj_renewtype;
})();