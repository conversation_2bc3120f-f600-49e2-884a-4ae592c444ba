///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/bas/ydj_building.js
*/
; (function () {
    var ydj_building = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.SetImageInfoVisible();
        };
        //设置图片隐藏属性
        _child.prototype.SetImageInfoVisible = function (e) {
            debugger;
            var that = this;
            var fimage = that.Model.getValue({ id: 'fimage' });
            var hasimage = false;
            if (fimage && fimage.id.length > 0) hasimage = true;
            //如果有图片，则默认展开
            setTimeout(function () {
                debugger;
                that.Model.setAttr({ id: '.y_building_tools', random: 'class', value: hasimage ? 'y_building_tools collapse' : 'y_building_tools expand' });
                that.Model.setAttr({ id: '.y_building_portlet', random: 'style', value: hasimage ? 'display:block' : 'display:none' });
            }, 10);
        };

        return _child;
    })(BasePlugIn);
    window.ydj_building = window.ydj_building || ydj_building;
})();