/**
 * 库存校验结果业务插件
 * @ sourceURL=/fw/js/ydj/bas/ydj_productdelistingdialog.js
 */
; (function () {
    var ydj_productdelistingdialog = (function (_super) {
        var _child = function (args) {
            _super.call(this, args);
        };
        __extends(_child, _super);

        //插件初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;
            var cp = that.formContext.cp;
            that.Model.refreshEntry({ id: "fentry", data: cp.fentry });

            var msg = that.Model.getSimpleValue({ "id": "fmessage" });
            $(".fmessage").text(msg);

            var domaintype = that.Model.getSimpleValue({ "id": "fdomaintype" });
            var parentformid = that.Model.getSimpleValue({ "id": "fparentformid" });

            if (parentformid == "ydj_order") {
                this.Model.setVisible({ id: "fwarnqty", value: true });
                this.Model.setVisible({ id: "fcansalqty", value: true });
                this.Model.setVisible({ id: "fpurqty", value: false });

                this.Model.setVisible({ id: "fpurbillno", value: false });
                if (domaintype != "list") {
                    this.Model.setVisible({ id: "fsalbillno", value: false });
                }
                else {
                    this.Model.setVisible({ id: "fsalbillno", value: true });
                }
            }
            else {
                this.Model.setVisible({ id: "fwarnqty", value: false });
                this.Model.setVisible({ id: "fcansalqty", value: false });
                this.Model.setVisible({ id: "fpurqty", value: true });

                this.Model.setVisible({ id: "fsalbillno", value: false });
                if (domaintype != "list") {
                    this.Model.setVisible({ id: "fpurbillno", value: false });
                }
                else {
                    this.Model.setVisible({ id: "fpurbillno", value: true });
                }
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //确定
                case 'tbpageconfirm':
                    e.result = true;
                    that.Model.close();
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_productdelistingdialog = window.ydj_productdelistingdialog || ydj_productdelistingdialog;
})();