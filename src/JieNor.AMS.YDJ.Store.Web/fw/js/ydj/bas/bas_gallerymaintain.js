// 本地商品图库维护弹窗插件
; (function () {
    var bas_gallerymaintain = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//初始化编辑页面插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            //设置商品id
			that.Model.setValue({id:'fproductid',value:that.Model.uiData.productId});
        };
		
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'cancel':
                    //点击取消
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
                    //调用商品图库保存
					args.result = true;
					that.Model.invokeFormOperation({
						id: 'tbSave',
						opcode: 'save',
						param:{
							formId: 'ydj_commoditygallery'
						}
					});
					break;
            }
        };
        
        //操作返回数据
        _child.prototype.onAfterDoOperation = function (e) {
			var that = this;
            if (!e || !e.opcode) return;
            var isSuccess = e.result.operationResult.isSuccess;
            switch (e.opcode) {
                case 'save':
                    if(isSuccess){
                        //保存成功关闭弹窗
                        that.Model.close();
                    }
                    break;
            }
        };
		
        return _child;
    })(BasePlugIn);
    window.bas_gallerymaintain = window.bas_gallerymaintain || bas_gallerymaintain;
})();