// <reference path="/fw/js/basepage.js" />
/* @ sourceURL=/fw/js/ydj/bas/ydj_staff.js */
; (function () {
    var ydj_staff = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;

            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        _child.prototype.entry = 'fentity';

        //初始化事件
        _child.prototype.onInitialized = function (e) {
            debugger;
            var that = this;
            that.onLockField();
            if (that.checkIsAuth(e)) {
                that.setIframeUrl(e);

            }
            var dept = that.Model.getSimpleValue({ id: 'fdeptid' });
            if ($.trim(dept) != '') {
                var entry = that.Model.getValue({ id: that.entry });
                if (entry.length > 0)
                    for (var i = 0; i < entry.length; i++) {
                        if ($.trim(entry[i].fdeptid_e.id) == '') {
                            that.Model.setValue({ id: 'fdeptid_e', value: dept, row: entry[i].id });
                        }
                    }
            }
        }

        _child.prototype.checkIsAuth = function (e) {
            var that = this;
            var IsAuth = false;
            var param = {
                'formId': 'ms_authcenterparam',
                'domainType': 'parameter'
            };
            yiAjax.p('/bill/ms_authcenterparam?operationno=LoadAuthParam', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (!res.isSuccess) {
                    IsAuth = false;
                }
                else {
                    if (res.srvData) {
                        IsAuth = res.srvData["fisuseauth"];
                    }
                }
            }, null, null, null, { async: false });
            return IsAuth;
        };

        _child.prototype.onLockField = function () {
            var that = this;
            var fmanagemodel = Consts.isdirectsale;
            //如果是非直营的话 隐藏本次新增字段 ehr员工
            if (!fmanagemodel) {
                that.Model.setVisible({ id: '.managemodel', value: false });
            }
            //that.Model.setVisible({ id: '.managemodel', value: true });
        }


        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            ;
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fismain':
                    // that.setStrictTrack(e);
                    //console.log(e);
                    that.setIsMain(e);
                    //e.result = false;
                    break;
            }
        };

        //双击选择
        _child.prototype.onEntryRowDblClick = function (e) {
            ;
            var that = this;
            if (that.checkIsAuth(e)) {
                var parentPageId = Index.getPage(that.Model.viewModel.pageId);
                if (!parentPageId) {
                    //不存在父页面 替换原有的方式 双击不再打开修改页面而是打开iframe
                    e.result = true;
                    //that.setIframeUrl(e);
                }
            }
        };

        //设置认证站点iframe地址
        _child.prototype.setIframeUrl = function (e) {
            var that = this;
            //如果是从 业务表单选择 即存在父页面 不能跳转到认证，同时隐藏新增、删除等按钮
            var parentPageId = Index.getPage(that.Model.viewModel.parentPageId);
            if (parentPageId) {
                that.Model.setEnable({ id: '#tbNew', value: false });
                that.Model.setEnable({ id: '#tbCopy', value: false });
                that.Model.setEnable({ id: '#tbSubmit', value: false });
                that.Model.setEnable({ id: '#tbUnsubmit', value: false });
                that.Model.setEnable({ id: '#tbAudit', value: false });
                that.Model.setEnable({ id: '#tbUnaudit', value: false });
                that.Model.setEnable({ id: '#tbForbid', value: false });
                that.Model.setEnable({ id: '#tbUnforbid', value: false });
                that.Model.setEnable({ id: '#tbDelete', value: false });
                that.Model.setEnable({ id: '#tbImportExcelData', value: false });
                that.Model.setEnable({ id: '#tbSave', value: false });
                return;
            }

            Index.openForm({
                formId: 'sec_setiframeurl',
                domainType: 'bill',
                formid: "ydj_staff",
                cp: { formid: "ydj_staff", opcode: "modify", pageid: that.Model.viewModel.pageId }
            });
        };

        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            ;
            var opcode = args.opcode.toLowerCase();
            switch (opcode) {
                case 'save':
                    args.result = true;
                    var fphone = that.Model.getValue({ id: "fphone" });
                    var flinkuserid = that.Model.getValue({ id: "flinkuserid" });
                    if (!fphone)//如果电话为空，则不判断,直接执行保存
                    {
                        args.result = false
                        return;
                    }

                    if (flinkuserid && flinkuserid.id)//如果有关联账号，则不判断，直接执行保存
                    {
                        args.result = false
                        return;
                    }
                    that.Model.invokeFormOperation({
                        id: 'checklinkuserid',
                        opcode: 'checklinkuserid',
                        param: {
                            formId: 'ydj_staff',
                            fphone: fphone
                        }
                    });
                    break;
                case 'new':
                case 'copy':
                case 'delete':
                case 'forbid':
                case 'unforbid':
                case 'importexceldata':
                    if (that.checkIsAuth(args)) {
                        //替换原有的方式 双击不再打开修改页面而是打开iframe
                        args.result = true;
                        that.setIframeUrl(args);
                    }
                    break;
                case 'generateuser':
                    args.result = true;
                    var flinkuserid = that.Model.getValue({ id: "flinkuserid" });
                    if (flinkuserid && flinkuserid.id) {
                        yiDialog.mt({ msg: '当前员工已绑定用户！', skinseq: 2 });
                        return;
                    }
                    if (!that.Model.pkid) {
                        yiDialog.mt({ msg: '请先保存单据！', skinseq: 2 });
                        return;
                    }
                    var params = {
                        simpleData: {
                            formId: "ydj_staff",
                            fphone: that.Model.getValue({ id: "fphone" })
                        }
                    };
                    yiAjax.p('/dynamic/ydj_staff?operationno=checkuserexists', params, function (r) {
                        var srvData = r.operationResult.srvData;
                        if (r.operationResult.isSuccess) {
                            that.Model.showForm({
                                formId: 'ydj_stafftouserdialog',
                                param: {
                                    openStyle: Consts.openStyle.modal,
                                },
                                cp: {
                                    fsex: that.Model.getValue({ id: "fsex" }),
                                    fid: that.Model.pkid,
                                    fphone: that.Model.getValue({ id: "fphone" }),
                                    fname: that.Model.getValue({ id: "fname" }),
                                    callback: function (result) {
                                        debugger
                                        that.Model.refresh();
                                        if (result && result.isSuccess) {
                                            //刷新当前单据
                                            that.Model.refresh();

                                        }
                                    }
                                }
                            });
                        }
                        else {
                            yiDialog.mt({ msg: srvData, skinseq: 2 });
                        }
                    }, null, null, null, { async: false });

                    break;
            }
        }

        //当表头主岗位勾上时，其他岗位上的主岗位设置为 false
        _child.prototype.setIsMain = function (e) {
            // false -> true
            // 其他列设置为 false，然后把此列设置到 fmainpositionid
            // true -> false
            // fmainpositionid = ""

            var that = this;
            if (!e.value) {
                that.Model.setValue({ id: "fmainpositionid", value: "" });
                return;
            }
            var ds = that.Model.getEntryData({ id: that.entry });
            for (var i = 0, j = ds.length; i < j; i++) {
                var rowid = ds[i].id;
                if (rowid == e.row) {
                    continue;
                }
                that.Model.setValue({ id: 'fismain', row: ds[i].id, value: false });
            }
            that.Model.setValue({ id: "fmainpositionid", value: that.Model.getValue({ id: 'fpositionid', row: e.row }) });
        };

        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;

            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'checklinkuserid':
                    if (srvData) {//如果关联的账号在其他店，则弹出窗口询问是否关联其他店的账号。
                        yiDialog.c('执行自动关联账号时，该账号存在于其他门店，是否关联?', function () {
                            that.Model.invokeFormOperation({
                                id: 'tbSave',
                                opcode: 'save',
                                param: {
                                    formId: 'ydj_staff',
                                    islink: '1'
                                }
                            });
                        }, function () {
                            that.Model.invokeFormOperation({
                                id: 'tbSave',
                                opcode: 'save',
                                param: {
                                    formId: 'ydj_staff',
                                    islink: '0'
                                }
                            });
                        })
                    } else {
                        that.Model.invokeFormOperation({
                            id: 'tbSave',
                            opcode: 'save',
                            param: {
                                formId: 'ydj_staff',
                                islink: '1'
                            }
                        });
                    }
                    break;
            }
        }

        return _child;
    })(BasePlugIn);
    window.ydj_staff = window.ydj_staff || ydj_staff;
})();