/**
 * @ sourceURL=/fw/js/ydj/bas/ydj_deliveryfactory.js
 */
; (function () {
    var ydj_deliveryfactory = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        }
        __extends(_child, _super);

        //编辑页面初始化事件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            var ftype = that.Model.getSimpleValue({ id: 'ftype' });
            that.Model.invokeFormOperation({
				id: 'getorgtype',
				opcode: 'getorgtype',
                param: {
                    formId: 'ydj_deliveryfactory',
                    ftype: ftype
                }
            });
           
            
                               
        };
		_child.prototype.onAfterDoOperation = function (e) {
			var that = this;
			if (!e || !e.opcode) return;
			switch (e.opcode) {
				case 'getorgtype':				
					var resdata = e.result.operationResult.srvData;
                    if (resdata && resdata.length > 0) {

                        that.Model.setValue({ id: 'ftype', value: resdata });																		
					}
					break;				
			}
		};
        return _child;
    })(BasePlugIn);
    window.ydj_deliveryfactory = window.ydj_deliveryfactory || ydj_deliveryfactory;
})();