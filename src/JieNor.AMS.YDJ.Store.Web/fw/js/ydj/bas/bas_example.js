/**
 * 表单模型示例
 * @ sourceURL=/fw/js/bas/bas_example.js
 */
; (function () {
    var bas_example = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            //在构造函数中定义自身特有的成员
        };
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.entityId = 'fentity';

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //编辑页面初始化事件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

            //测试代码
            var row1 = that.Model.addRow({
                id: 'ftestentity',
                data: {
                    fbizfieldsrc: { id: 'basedata', fnumber: 'basedata', fname: '基础资料' },
                    fbizfieldtype: { id: 'ydj_unit', fnumber: 'ydj_unit', fname: '单位' },
                    fbizfieldvalue: { id: '380839483869892610', fnumber: 'DW0403', fname: '箱' }
                }
            });

            var row2 = that.Model.addRow({
                id: 'ftestentity',
                data: {
                    fbizfieldsrc: { id: 'enumdata', fnumber: 'enumdata', fname: '辅助资料' },
                    fbizfieldtype: { id: 'd7c48f02fe37411795625c6e55a379ce', fnumber: 'd7c48f02fe37411795625c6e55a379ce', fname: '结算方式' },
                    fbizfieldvalue: { id: 'settle_type_003', fnumber: 'settle_type_003', fname: '支付宝' }
                }
            });

            var row3 = that.Model.addRow({
                id: 'ftestentity',
                data: {
                    fbizfieldsrc: { id: 'text', fnumber: 'text', fname: '文本' },
                    fbizfieldtype: { id: '$sys_text$', fnumber: '$sys_text$', fname: '文本' },
                    fbizfieldvalue: '测试'
                }
            });

            setTimeout(function () {

                //简单值
                that.Model.setValue({ id: 'fbizfieldvalue', value: '380839924682854402', row: row1 });
                that.Model.setValue({ id: 'fbizfieldvalue', value: 'settle_type_004', row: row2 });
                that.Model.setValue({ id: 'fbizfieldvalue', value: 'setValue1', row: row3 });

                //复杂值
                setTimeout(function () {
                    that.Model.setValue({ id: 'fbizfieldvalue', value: { id: '380840982968668162', fnumber: 'DW0404', fname: '车' }, row: row1 });
                    that.Model.setValue({ id: 'fbizfieldvalue', value: { id: 'settle_type_002', fnumber: 'settle_type_002', fname: '银行' }, row: row2 });
                    that.Model.setValue({ id: 'fbizfieldvalue', value: 'setValue1', row: row3 });

                    //清空值
                    setTimeout(function () {
                        that.Model.setValue({ id: 'fbizfieldvalue', value: '', row: row1 });
                        that.Model.setValue({ id: 'fbizfieldvalue', value: '', row: row2 });
                        that.Model.setValue({ id: 'fbizfieldvalue', value: '', row: row3 });
                    }, 3000);

                }, 3000);

            }, 5000);

            setTimeout(function () {
                that.Model.setSelectRows({ id: 'ftestentity', rows: [row1, row2, row3] });

                that.Model.setValue({ id: 'frichtext', value: '测试富文本字段赋值' });

            }, 3000);
        };

        //下拉框数据源加载后触发
        _child.prototype.onAfterDataSourceLoad = function (e) {
            var that = this;
            switch (e.id) {
                case 'fbizfieldvalue':

                    ////测试代码
                    ////对下拉框数据源进行干预
                    //var data = e.data;
                    //if (data && data.length > 0) {

                    //    //对其中的某一项进行禁用
                    //    for (var i = 0; i < data.length; i++) {
                    //        if (data[i].id === 'settle_type_003') {
                    //            data[i].disable = true;
                    //        }
                    //    }

                    //    //或者对其中的数据进行增删改
                    //    data = data.splice(1, 1);
                    //}

                    break;
            }
        };

        //字段设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'ftext':

                    break;
            }
        };

        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbilltype':
                    e.value
                    break;
            }
        };

        //辅助属性编辑或查看页面渲染前事件
        _child.prototype.onFlexViewRendering = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':

                    break;
            }
        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':

                    break;
            }
        };

        //元素点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'mytest':
                    //发起一个后端请求
                    that.Model.invokeFormOperation({
                        id: 'myop',
                        opcode: 'myop',
                        param: {
                            name: '测试',
                            number: 'test'
                        }
                    });
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbasedata':
                    //e.result.filterString = "fname=@fname";
                    //e.result.params = [
                    //    { fieldId: 'fname', pValue: 'c001' }
                    //];
                    break;
                case 'fbizfieldvalue':
                    //e.result.filterString = "fisbaseunit=@fisbaseunit";
                    //e.result.params = [
                    //    { fieldId: 'fisbaseunit', pValue: '1' }
                    //];
                    break;
            }
        };

        //表格行自定义操作按钮事件
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'foperate':

                    break;
            }
        };

        //字段按钮点击事件
        _child.prototype.onFieldButtonClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fscancode':

                    break;
            }
        };

        //表格按钮点击事件
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;

            // e.id 单据体标识
            // e.btnid 批录按钮标识为 g_record
            // e.fieldId 字段标识

            switch (e.btnid.toLowerCase()) {
                case 'g_record': //批录按钮

                    //取消某个字段的批录功能
                    if (e.fieldId.toLowerCase() === 'ftest') {

                        //取消平台的批录逻辑
                        e.cancel = true;
                    }
                    break;
            }
        };

        //表格行点击事件
        _child.prototype.onEntryRowClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    break;
            }
        };

        //表格行双击事件
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    break;
            }
        };

        //表格单元格点击事件
        _child.prototype.onEntryCellClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    break;
            }
        };

        //表格行创建前事件
        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    break;
            }
        };

        //表格行创建后事件
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    break;
            }
        };

        //表格行删除前事件：设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    break;
            }
        };

        //表格行删除后事件
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    break;
            }
        };

        //表格快粘贴前事件
        _child.prototype.onGridBeforePaste = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fentity':

                    //可以通过该参数取消平台的粘贴逻辑
                    e.cancel = true;

                    //当前被鼠标单击后激活行所在的行号，如果为空则表示当前表格没有被单击激活
                    var row = e.row;

                    //当前被鼠标单击后激活行所在的行数据对象，如果为空则表示当前表格没有被单击激活
                    var rowData = e.rowData;

                    //当前被鼠标单击后激活列所在的字段标识，如果为空则表示当前表格没有被单击激活
                    var fieldKey = e.fieldKey;

                    //粘贴板中的原始文本内容
                    var clipboardText = e.clipboardText;

                    //平台解析后的数据（按\n得到行数据，再从行数据按\t得到列数据），数据结构为二维数组，比如：[["","",""],["","",""]]
                    var parsedData = e.parsedData;

                    console.log(row);
                    console.log(rowData);
                    console.log(fieldKey);
                    console.log(clipboardText);
                    console.log(parsedData);

                    break;
            }
        };

        //获取表格行编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'ftext_e':

                    break;
            }
        };

        //表单操作点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'myop':
                    e.result = true; //设置为 true 标识由业务插件接管

                    break;
            }
        };

        //操作前触发的事件
        onBeforeDoOperation = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'myop':

                    break;
            }
        };

        //操作后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'myop':

                    break;
            }
        };

        //加载下拉字段面板数据
        _child.prototype.onLoadDropData = function (e) {
            var that = this;
            if (!e.id) { return; }
            switch (e.id.toLowerCase()) {
                case 'ffieldmodel':
                case 'fmulfieldmodel':
                    var bizFormId = that.Model.getSimpleValue({ id: 'fbizobject' });
                    if (bizFormId) {
                        getBizFormField({
                            domainType: that.formContext.domainType,
                            formId: that.formContext.formId,
                            bizFormId: bizFormId,
                            success: e.callback
                        });
                    } else {
                        e.callback([]);
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.bas_example = bas_example;
})();