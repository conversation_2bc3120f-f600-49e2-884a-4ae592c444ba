(function () {
    var ydj_staffdialog = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };

        //继承 BasePlugIn
        __extends(_child, _super);

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdeptid':
                    //门店改变时，清空员工/负责人字段
                    //that.Model.setValue({ id: 'fstaffid', value: '' });
                    //that.Model.setValue({ id: 'fdutyid', value: '' });
                    break;
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                // 员工/负责人
                case 'fstaffid':
                case 'fdutyid':
                    var deptId = that.Model.getSimpleValue({ id: 'fdeptid' });
                    if ($.trim(deptId).length === 0) {
                        return;
                    }
                    e.result.filterString = "fdeptid=@fdeptid or (exists (select 1 from t_bd_staffentry se where se.fdeptid=@fdeptid and se.fid=fid) )";
                    e.result.params = [
                        { fieldId: 'fdeptid', pValue: deptId }
                    ];
                    break;
                case 'fdeptid':
                    var dutyId = that.Model.getSimpleValue({ id: 'fdutyid' });
                    if ($.trim(dutyId).length === 0) {
                        return;
                    }
                    e.result.filterString = " fid in (SELECT fdeptid FROM t_bd_staffentry WHERE fid=@fdeptid)";
                    e.result.params = [
                        { fieldId: 'fdeptid', pValue: dutyId }
                    ];
                    break;
                    
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //取消
                case 'cancel':
                    e.result = true;
                    that.Model.close();
                    break;
                //确认
                case 'confirm':
                    e.result = true;
                    var cloneData = that.Model.clone();
                    that.Model.setReturnData({ 'data': cloneData });
                    //关闭当前弹窗
                    that.Model.close();
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_staffdialog = window.ydj_staffdialog || ydj_staffdialog;
})();