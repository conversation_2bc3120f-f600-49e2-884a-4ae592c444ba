/*
 * description:商品调价单业务控制插件
 * author:fsj
 * create date:
 * modify by:
 * modify date:
 * remark:
 * @ sourceURL=/fw/js/ydj/bas/bas_priceadjust.js
*/
; (function () {
    var bas_priceadjust = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentry = 'fentry';

        //初始化页面
        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            if ($.trim(that.Model.uiData.id) == '') {
                that.purpriceChangeOp();
                that.salpriceChangeOp();
                that.definedpriceChangeOp();
            }

            // 如果是下推，则锁定【调价方式】
            if (that.formContext.status === "push") {
                that.Model.setEnable({ id: 'fadjustmode', value: false });
            }
        }

        //创建明细表格
        _child.prototype.onCreateGrid = function (args) {
            var that = this;
            if (!args.id) return;
            switch (args.id.toLowerCase()) {
                case that.fentry:
                    args.result = { multiselect: false, rownumbers: false };
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //取整规则和数字回避字段变化时
                case 'froundingrule':
                case 'favoidnumber':
                    //重新计算调价明细
                    that.purpriceChangeOp();
                    that.salpriceChangeOp();
                    that.definedpriceChangeOp();
                    break;
                case 'fpurprice_percent':
                    //重新计算调价明细中的调后采购价
                    that.purpriceChangeOp();
                    break;
                case 'fsalprice_param':
                case 'fsalprice_percent':
                    //重新计算调价明细中的调后零售价
                    that.salpriceChangeOp();
                    break;
                case 'fdefinedprice_param':
                case 'fdefinedprice_percent':
                    //重新计算调价明细中的调后经销价
                    that.definedpriceChangeOp();
                    break;
                //// 商品变更，获取销售价目表
                //case 'fproductid':
                //    if (e.value.id) {
                //        // 获取销售价目表
                //        that.Model.invokeFormOperation({
                //            id: 'getprice',
                //            opcode: 'getprice',
                //            param: {
                //                formId: 'ydj_price',
                //                domainType: 'dynamic',
                //                products: ['']
                //            }
                //        });
                //    } else {
                //        // 清空
                //        that.Model.setValue({ id: 'fid_e', row: e.row, value: '' });
                //        that.Model.setValue({ id: 'fproductbillno', row: e.row, value: '' });
                //        that.Model.setValue({ id: 'fmtrlmodel', row: e.row, value: '' });

                //        that.Model.setValue({ id: 'funitid', row: e.row, value: '' });

                //        that.Model.setValue({ id: 'fcategoryid', row: e.row, value: '' });

                //        that.Model.setValue({ id: 'fbrandid', row: e.row, value: '' });
                //        that.Model.setValue({ id: 'fsalprice', row: e.row, value: '' });
                //        that.Model.setValue({ id: 'fsalprice_adjust', row: e.row, value: '' });
                //        that.Model.setValue({ id: 'fdefinedprice', row: e.row, value: '' });
                //        that.Model.setValue({ id: 'fdefinedprice_adjust', row: e.row, value: '' });
                //    }
                //    break;
                //case 'fproductid':
                //    break;
            }
        }

        //调价明细计算（调后采购价）
        _child.prototype.purpriceChangeOp = function (e) {
            var that = this;
            ds = that.Model.getEntryData({ id: that.fentry });
            if (!ds || ds.length <= 0) { return; }
            var roundingRule = that.Model.getValue({ id: 'froundingrule' }),
                avoidNumber = that.Model.getValue({ id: 'favoidnumber' }).split(","),
                purpricePercent = that.Model.getValue({ id: 'fpurprice_percent' });
            //调后采购价 = 采购价 * 比例
            for (var i = 0, l = ds.length; i < l; i++) {
                if (roundingRule.id == 'roundingrule_01') {
                    ds[i].fpurprice_adjust = Math.ceil(ds[i].fpurprice * purpricePercent / 100);
                    //回避数字
                    var avoid = that.Model.getValue({ id: 'favoidnumber' });
                    if ($.trim(avoid) != '') {
                        for (var j = 0, k = avoidNumber.length; j < k; j++) {
                            var num = ds[i].fpurprice_adjust % 10;
                            var flag = avoidNumber.indexOf(num.toString());
                            if (flag != -1) {
                                ds[i].fpurprice_adjust++;
                            }
                        }
                    }
                }
                else if (roundingRule.id == 'roundingrule_02') {
                    ds[i].fpurprice_adjust = Math.ceil(ds[i].fpurprice * purpricePercent / 1000) * 10;
                }
                else if (roundingRule.id == 'roundingrule_03') {
                    ds[i].fpurprice_adjust = Math.ceil(ds[i].fpurprice * purpricePercent / 10000) * 100;
                }
                else {
                    ds[i].fpurprice_adjust = yiMath.toNumber(ds[i].fpurprice * purpricePercent / 100);
                }
            }

            that.Model.refreshEntry({ id: that.fentry });
        }

        //调价明细计算（调后零售价）
        _child.prototype.salpriceChangeOp = function (e) {
            var that = this;
            ds = that.Model.getEntryData({ id: that.fentry });
            if (!ds || ds.length <= 0) { return; }
            var roundingRule = that.Model.getValue({ id: 'froundingrule' }),
                avoidNumber = that.Model.getValue({ id: 'favoidnumber' }).split(','),
                salpricePercent = that.Model.getValue({ id: 'fsalprice_percent' }),
                salpriceParam = that.Model.getValue({ id: 'fsalprice_param' }),
                selectData;
            //调后零售价 = 零售价（采购价） * 比例
            for (var i = 0, l = ds.length; i < l; i++) {
                //根据价格对象来计算调后零售价
                if (salpriceParam.id == 'salprice_01') {
                    selectData = ds[i].fsalprice;
                }
                else if (salpriceParam.id == 'salprice_02') {
                    selectData = ds[i].fpurprice;
                } else {
                    return;
                }
                //计算调后价格
                if (roundingRule.id == 'roundingrule_01') {
                    ds[i].fsalprice_adjust = Math.ceil(selectData * salpricePercent / 100);
                    //回避数字
                    var avoid = that.Model.getValue({ id: 'favoidnumber' });
                    if ($.trim(avoid) != '') {
                        for (var j = 0, k = avoidNumber.length; j < k; j++) {
                            var num = ds[i].fsalprice_adjust % 10;
                            var flag = avoidNumber.indexOf(num.toString());
                            if (flag != -1) {
                                ds[i].fsalprice_adjust++;
                            }
                        }
                    }
                }
                else if (roundingRule.id == 'roundingrule_02') {
                    ds[i].fsalprice_adjust = Math.ceil(selectData * salpricePercent / 1000) * 10;
                }
                else if (roundingRule.id == 'roundingrule_03') {
                    ds[i].fsalprice_adjust = Math.ceil(selectData * salpricePercent / 10000) * 100;
                }
                else {
                    ds[i].fsalprice_adjust = yiMath.toNumber(selectData * salpricePercent / 100);
                }
            }
            that.Model.refreshEntry({ id: that.fentry });
        }

        //调价明细计算（调后经销价）
        _child.prototype.definedpriceChangeOp = function (e) {
            var that = this;
            ds = that.Model.getEntryData({ id: that.fentry });
            if (!ds || ds.length <= 0) { return; }
            var roundingRule = that.Model.getValue({ id: 'froundingrule' }),
                avoidNumber = that.Model.getValue({ id: 'favoidnumber' }).split(','),
                definedpricePercent = that.Model.getValue({ id: 'fdefinedprice_percent' }),
                definedpriceParam = that.Model.getValue({ id: 'fdefinedprice_param' }),
                selectData;
            //调后经销价 = 经销价（零售价、采购价） * 比例
            for (var i = 0, l = ds.length; i < l; i++) {
                //根据价格对象来计算调后零售价
                if (definedpriceParam.id == 'definedprice_01') {
                    selectData = ds[i].fdefinedprice;//经销价
                }
                else if (definedpriceParam.id == 'definedprice_02') {
                    selectData = ds[i].fsalprice;//零售价
                }
                else if (definedpriceParam.id == 'definedprice_03') {
                    selectData = ds[i].fpurprice;//采购价
                } else {
                    return;
                }
                //计算调后价格
                if (roundingRule.id == 'roundingrule_01') {
                    ds[i].fdefinedprice_adjust = Math.ceil(selectData * definedpricePercent / 100);
                    //回避数字
                    var avoid = that.Model.getValue({ id: 'favoidnumber' });
                    if ($.trim(avoid) != '') {
                        for (var j = 0, k = avoidNumber.length; j < k; j++) {
                            var num = ds[i].fdefinedprice_adjust % 10;
                            var flag = avoidNumber.indexOf(num.toString());
                            if (flag != -1) {
                                ds[i].fdefinedprice_adjust++;
                            }
                        }
                    }
                }
                else if (roundingRule.id == 'roundingrule_02') {
                    ds[i].fdefinedprice_adjust = Math.ceil(selectData * definedpricePercent / 1000) * 10;
                }
                else if (roundingRule.id == 'roundingrule_03') {
                    ds[i].fdefinedprice_adjust = Math.ceil(selectData * definedpricePercent / 10000) * 100;
                }
                else {
                    ds[i].fdefinedprice_adjust = yiMath.toNumber(selectData * definedpricePercent / 100);
                }
            }
            that.Model.refreshEntry({ id: that.fentry });
        }

        return _child;
    })(BasePlugIn);
    window.bas_priceadjust = window.bas_priceadjust || bas_priceadjust;
})();