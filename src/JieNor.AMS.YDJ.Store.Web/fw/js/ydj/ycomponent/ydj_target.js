/*
@ sourceURL=/fw/js/ydj/ycomponent/ydj_target.js
*/
; (function () {
    var ydj_target = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //初始化事件
        _child.prototype.onInitialized = function (args) {
            var that = this;

            //查询权限数据
            that.queryPerms();
        };

        //查询权限数据
        _child.prototype.queryPerms = function () {
            var that = this;
            var rptFormId = that.Model.viewModel.formId;
            var perms = [
                { formId: rptFormId, permId: "myself" },
                { formId: rptFormId, permId: "mydepartment" },
                { formId: rptFormId, permId: "mysubordinates" },
                { formId: rptFormId, permId: "mycompany" }
            ];
            that.Model.invokeFormOperation({
                id: 'haspermissions',
                opcode: 'haspermissions',
                param: {
                    formId: 'sys_mainfw',
                    domainType: Consts.domainType.dynamic,
                    permissionInfos: JSON.stringify(perms)
                }
            });
        };

        //元素点击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id) {
                case 'golds':
                    that.Model.removeClass({ id: '.golds', value: 'active' });
                    that.Model.addClass({ id: '.golds.' + e.param.dtType, value: 'active' });
                    that.queryData();
                    break;
            };
        };

        _child.prototype.showStoreSaleReport = function () {
            var that = this;
            var dtType = that.Model.getText({ id: '.active.golds' });
            var dataPermId = that.Model.getSimpleValue({ id: "fpermselect" });

            //如果是本人,不用跳转
            if (dataPermId == "myself") {
                return;
            }
            
            var param = { dtType: dtType, dataPermId: dataPermId, sender: "ydj_target", forceRecreateRpt: true };
            that.Model.showListReport({ formId: 'rpt_storesalesconversionrate', openStyle: 'modal', param: param });
        }

        //字段值变化事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case "fpermselect":
                    that.queryData();
                    break;
            }
        };

        //查询报表数据
        _child.prototype.queryData = function () {
            var that = this;
            var dtType = that.Model.getText({ id: '.active.golds' });
            var dataPerm = that.Model.getValue({ id: "fpermselect" });
            var rptFormId = that.Model.viewModel.formId;
            var asslatitudes = [
                { id: "fal_001", fname: "开单额" },
                { id: "fal_002", fname: "意向额" },
                { id: "fal_003", fname: "商机数" }
            ]

            for (var i = 0; i < asslatitudes.length; i++) {
                var evalType = asslatitudes[i];
                that.Model.invokeFormOperation({
                    id: 'goalinfo',
                    opcode: 'goalinfo',
                    param: {
                        formId: 'ydj_dashboard',
                        domainType: Consts.domainType.dynamic,
                        dtType: dtType,
                        dataPermId: dataPerm.id,
                        rptFormId: rptFormId,
                        evalType: evalType.id
                    },
                    opctx: {
                        evalType: evalType
                    }
                });
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'haspermissions':
                    that.setPermComboData(srvData);
                    break;
                case 'goalinfo':
                    if (isSuccess) {
                        var evalType = e.opctx.evalType;
                        that.renderChart(srvData, evalType);
                    }
                    break;
            }
        };

        //设置权限下拉框数据源
        _child.prototype.setPermComboData = function (srvData) {
            var that = this;
            var perms = srvData && srvData.permissionInfos;
            if (perms) {
                var comboData = [];
                for (var i = 0; i < perms.length; i++) {
                    comboData.push({
                        id: perms[i].permId,
                        name: perms[i].permCaption
                    });
                }
                that.Model.setComboData({ id: 'fpermselect', data: comboData });
                if (comboData.length > 0) {
                    that.Model.setValue({
                        id: 'fpermselect',
                        value: {
                            id: comboData[0].id,
                            fname: comboData[0].name,
                            fnumber: comboData[0].name
                        }
                    });
                    that.Model.setVisible({ id: '.permselect', value: true });
                }
            }
        };

        //渲染图表
        _child.prototype.renderChart = function (srvData, evalType) {
            var that = this;
            var title = evalType.fname;
            var targetClass = ".{0} ".format(evalType.id);
            var goal = 0, realcomplete = 0, fnot = 0;
            for (var i = 0; i < srvData.length; i++) {
                goal += srvData[i]["目标"];
                realcomplete += srvData[i]["实际完成"];
            }

            if (!title) {
                title = "开单额";
            }

            that.Model.setText({ id: targetClass+".targetTitle", value: title });
            that.Model.setText({ id: targetClass+'.target-goal', value: yiMath.toMoneyFormat(goal, 2, 0) || 0 });
            that.Model.setText({ id: targetClass+'.target-realcomplete', value: yiMath.toMoneyFormat(realcomplete, 2, 0) || 0 });

            var performance = goal == 0 ? 0 : yiMath.toMoneyFormat(((realcomplete / goal) * 100), 2, 0);
            if (performance < 100) {
                fnot = 100 - performance;
            }
            var name = '{0}完成率'.format(title);

            var option = {
                tooltip: {
                    show: false,
                    trigger: 'item',
                    formatter: "{a} <br/>{b}: {c} ({d}%)"
                },
                color: ["#FB8D3C", "#f2f2f2"],
                series: [
                    {
                        name: name,
                        type: 'pie',
                        radius: ['70%', '85%'],
                        avoidLabelOverlap: false,
                        hoveranination: false,
                        silent: true, //设置没有动态效果。
                        label: {
                            normal: {
                                show: true,
                                position: 'center',
                                formatter: function (argument) {
                                    var html;
                                    html = '完成率\r\n\r\n' + '{0}%'.format(performance);
                                    return html;
                                },
                                textStyle: {
                                    fontSize: 15,
                                    color: '#FB8D3C'
                                }
                            }
                        },
                        labelLine: {
                            normal: {
                                show: false
                            }
                        },
                        data: [
                            { value: performance, name: name },
                            { value: fnot, name: '无' },
                        ]
                    }
                ]
            };

            var $chart = that.Model.getEleMent({ id: targetClass + '.targetReport' });
            yiChart.init($chart, option);
            //只有意向额或者商机时才需要绑定点击事件
            if ((evalType.id == "fal_002" || evalType.id == "fal_003")) {
                $chart.unbind("click");
                $chart.click(function () {
                    that.showStoreSaleReport();
                    return false;
                });
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_target = window.ydj_target || ydj_target;
})();