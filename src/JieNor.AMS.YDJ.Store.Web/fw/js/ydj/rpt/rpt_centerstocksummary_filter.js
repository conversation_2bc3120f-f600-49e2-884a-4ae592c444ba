///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt_centerstocksummary_filter.js
*/
; (function () {
    var rpt_centerstocksummary_filter = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************

        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //初始化事件
        _child.prototype.onInitialized = function () {
            var that = this;
            debugger
            //that.Model.invokeFormOperation({
            //    id: 'tbGetCategory',
            //    opcode: 'GetCategory',
            //    param: {
            //        formId:"rpt_agentstocksummary"
            //    }
            //});
            var param = {
                simpleData: {
                    formId: 'rpt_centerstocksummary',
                    fcategoryid: '',
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/rpt_centerstocksummary?operationno=gettopcategory', param, function (r) {
                debugger
                var objsId = "";
                var objsval = "";
                var res = r.operationResult;
                if (res.srvData) {

                    for (var i = 0; i < res.srvData.length; i++) {
                        objsId += res.srvData[i].fid + ",";
                        objsval += res.srvData[i].fname + ",";
                    }
                    objsId = objsId.substring(0, objsId.length - 1);
                    objsval = objsval.substring(0, objsval.length - 1);

                    var objs = { id: objsId, name: objsval };

                    that.Model.setValue({ id: 'fcategoryid', value: objs });
                }
            }, null, null, null, { async: false });

        }

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fcategoryid':
                    e.result.filterString = "  fparentid=(select  fid from ser_ydj_category where fname='产品类') and fforbidstatus='0' and fmainorgid='821347239912935425' or fid='othercategory' ";
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            debugger;
            switch (e.opcode) {
                case 'querydata':
                    if (srvData != null) {
                        debugger
                        var objs = { id: 'fcategoryid_01,fcategoryid_02,fcategoryid_03', name: '床垫类,床架类,其他类' };
                        that.Model.setValue({ id: 'fcategoryid', value: objs });
                        //that.fnotoutspotratio = srvData.notOutSpotRatio;
                        //that.foutspotratio = srvData.outspotratio;
                    }
                    break;
            }
        };

        return _child;
    })(ListReportPlugin);
    window.rpt_centerstocksummary_filter = window.rpt_centerstocksummary_filter || rpt_centerstocksummary_filter;
})();