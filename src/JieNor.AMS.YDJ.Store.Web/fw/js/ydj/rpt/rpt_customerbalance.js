///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/rpt/rpt_customerbalance.js
*/
; (function () {
    var rpt_customerbalance = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //表格单元格点击事件
        _child.prototype.onEntryCellClick = function (e) {
            var that = this;
            var clickFields = ["fcurrentreceipt", "fcurrentrefund", "fcurrentstockout", "fcurrentreturn", "fotherreceipt", "fcurrentbalance"];
            var filter = JSON.parse(that.Model.viewModel.getCustomFilterData());
            if (e.id === 'list' && clickFields.indexOf(e.fieldId) >= 0) {
                e.result = true;
                that.Model.showListReport({
                    formId: 'rpt_customerbalancedetail',
                    openStyle: 'modal',
                    param: {
                        fcustomerid: e.data.fcustomerid,
                        fdatefrom: filter.fdatefrom,
                        fdateto: filter.fdateto,
                        fieldId: e.fieldId
                    }
                });
            }
        };

        //表格单元格格式化事件
        _child.prototype.onFieldValueFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fcurrentreceipt':
                case 'fcurrentrefund':
                case 'fcurrentstockout':
                case 'fcurrentreturn':
                case 'fotherreceipt':
                case 'fcurrentbalance':
                    e.value = '<span style="color:#529DE3;cursor:pointer;">' + yiMath.toDecimal(e.value, 2) + '</span>';
                    e.cancel = true; //取消平台的默认格式化显示值
                    break;
            }
        };

        return _child;
    })(ListReportPlugin);
    window.rpt_customerbalance = window.rpt_customerbalance || rpt_customerbalance;
})();