//运维问题（转开发）控制插件
; (function () {
    var ydj_todeveloper = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化编辑页面插件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            debugger
            that.setDefaultDeveloper();
        }

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            debugger
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode.toLowerCase()) {
                case 'cancel':
                    args.result = true;
                    that.Model.close();
                    break;
                case 'confirm':
                    args.result = true;
                    var developerid = that.Model.getValue({ id: 'fdeveloper' }).id;
                    var developername = that.Model.getValue({ id: 'fdeveloper' }).fname;
                    var parentViewModel = Index.getPage(that.formContext.cp.parentPageId);

                     //从运维问题上操作
                    if (parentViewModel.formId == 'ydj_maintenance') {
                        parentViewModel.Model.invokeFormOperation({
                            id: 'todeveloper',
                            opcode: 'todeveloper',
                            param: {
                                'formId': 'ydj_maintenance',
                                'developerid': developerid,
                                'developername': developername
                            }
                        });

                        that.Model.close();
                    }
                    break;
            }
        };


        //设置默认开发人员
        _child.prototype.setDefaultDeveloper = function () {
            var that = this;
            var datas = [];
            yiAjax.p('/bill/ydj_maintenance?operationno=getdefaultdeveloper', null, function (r) {

                var res = r.operationResult;
                if (res.isSuccess) {
                    datas = res.srvData;
                }
            }, null, null, null, { async: false });

            debugger
            if (datas) {
                that.Model.setValue({ id: 'fdeveloper', value: { id: datas.Id, fnumber:datas.Number, fname: datas.Name} });
            }
        }


        return _child;
    })(BasePlugIn);
    window.ydj_todeveloper = window.ydj_todeveloper || ydj_todeveloper;
})();