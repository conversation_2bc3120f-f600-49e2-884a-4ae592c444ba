///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/coo/coo_chargemoney.js
*/
; (function () {
    var coo_chargemoney = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************


        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (e) {

        };

        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            //列表操作列
            if (e && $.trim(e.id) === 'foperate' && e.data) {
                e.cancel = true;
                e.result = [];
                if (e.data.fissyn === 'Y') {
                    var businessStatus = $.trim(e.data.fbusinessstatus).toLowerCase();
                    switch (businessStatus) {
                        case 'inpour_status_01':
                            e.result.push({ id: 'reducemoney', text: '确认扣款' });
                            break;
                        case 'inpour_status_02':
                            e.result.push({ id: 'cancelreduce', text: '取消扣款' });
                            break;
                    }
                }
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            //列表操作
            switch (e.btnid.toLowerCase()) {
                case 'reducemoney':
                case 'cancelreduce':
                    that.Model.invokeFormOperation({
                        id: e.btnid,
                        opcode: e.btnid,
                        selectedRows: e.selectedRows
                    });
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                //列表操作
                case 'reducemoney':
                case 'cancelreduce':
                    if (that.formContext.domainType === Consts.domainType.list && isSuccess) {
                        that.Model.refresh();
                    }
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.coo_chargemoney = window.coo_chargemoney || coo_chargemoney;
})();