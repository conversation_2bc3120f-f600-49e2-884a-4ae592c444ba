; (function () {
    var coo_synadjust = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//初始化编辑页面插件
        _child.prototype.onInitialized  = function (args) {
        	var that = this;
        	var serviceType = that.Model.uiData.serviceType;
        	if(serviceType == '供应商'){
        		that.Model.setVisible({id:'.y-supplier',value:true});
        		that.Model.setVisible({id:'.y-customer',value:false});
        	}else if(serviceType == '客户'){
        		that.Model.setVisible({id:'.y-supplier',value:false});
        		that.Model.setVisible({id:'.y-customer',value:true});
        	}
        };
		
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'adjustcancel':
                    //点击取消关闭协同发布弹窗
                    args.result = true;
                    that.Model.close();
                    break;
                case 'adjustconfirm':
                    //保存发布操作
                    args.result = true;
                	that.adjustConfirm();
                    break;
            }
        };
        
        //绑定调整操作
        _child.prototype.adjustConfirm = function (args) {
        	var that = this;
            var customer = that.Model.getValue({id:'fcustomerid'}),
        		supplier = that.Model.getValue({id:'fsupplierid'});
        	var message,
        		serviceType = that.Model.uiData.serviceType;
        	if(serviceType == '供应商'){
        		var message = true;
        	}else{
        		var message = false;
        	}
        	if(customer.fname == '' && supplier.fname == ''){
        		if(message == true){
        			yiDialog.mt({msg:'请选择关联供应商资料！', skinseq: 2});
        		}else{
        			yiDialog.mt({msg:'请选择关联客户资料！', skinseq: 2});
        		}
        	}else{
	        	var	companyId = that.Model.uiData.companyId,
	    			serviceType = that.Model.uiData.serviceType,
	    			fid = that.Model.uiData.synfid;
	        	var CusorSupId,
	        		CusorSupName;
	        	if(serviceType == '供应商'){
	        		CusorSupId = supplier.id;
	        		CusorSupName = supplier.fname;
	        	}else if(serviceType == '客户'){
	        		CusorSupId = customer.id;
	        		CusorSupName = customer.fname;
	        	}
	        	that.Model.invokeFormOperation({
	        	    opcode: 'setCompanyRelationStatus',
	        	    param: {
	        	        formId: 'coo_company',
	        	        domainType: 'dynamic',
	        	        optype: '3',
	        	        id: fid,
	        	        relationid: CusorSupId,
	        	    }
	        	});
	        	//var url = '/bill/coo_company?operationno=synCompanyRepalce',
	            //param = {
	            //    simpledata: {
				//	 	id: fid,
				//	 	relationid: CusorSupId,
				//	 	relationname: CusorSupName
	            //    }
	           	//}
	            //yiAjax.p(url, param, function (r) {
            	//	if(r.operationResult.isSuccess == true){
            	//		that.Model.close();
            	//		yiDialog.mt({msg:'调整绑定成功！', skinseq: 1});
            	//	}
	            //});
        	}
        }
        
		
        return _child;
    })(BasePlugIn);
    window.coo_synadjust = window.coo_synadjust || coo_synadjust;
})();