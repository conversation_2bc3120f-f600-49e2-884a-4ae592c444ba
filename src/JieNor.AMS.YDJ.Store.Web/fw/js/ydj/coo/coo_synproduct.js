; (function () {
    var coo_synproduct = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);
		
		//初始化编辑页面插件
        _child.prototype.onInitialized  = function (args) {
        	var that = this;
        	var synType = that.Model.getSimpleValue({ id: 'flocalproduct' });
			that.localTypeOp(synType);
        };
		
        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.autobuildEntryId = 'fautobuildentry';
        _child.prototype.manualassEntryId = 'fmanualassentry';
		
		//字段值改变时
		_child.prototype.onFieldValueChanged = function (e) {
            var that = this;
			switch (e.id.toLowerCase()) {
			    case 'flocalproduct':
			        that.localTypeOp(e.value);
			        break;
			}
        };
		
		//如果已经关联了企业则按钮变为查看详情
        _child.prototype.localTypeOp = function (synType) {
            var that = this;
            var synType = that.Model.getSimpleValue({ id: 'flocalproduct' });
        	if(synType == 'localproduct_01'){
        		that.Model.setVisible({ id: '.y-autobuild', value: true });
        		that.Model.setVisible({ id: '.y-manualass', value: false });
        		that.Model.resizeEntryWidth({ id: that.autobuildEntryId });
        		that.autoBuild();
        	}else if(synType == 'localproduct_02'){
        		that.Model.setVisible({ id: '.y-autobuild', value: false });
        		that.Model.setVisible({ id: '.y-manualass', value: true });
        		that.Model.resizeEntryWidth({ id: that.manualassEntryId });
        		that.manualassAss();
        	}
        };
		
        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.autobuildEntryId:
                    e.result = { rownumbers: false };
                    break;
               	case this.manualassEntryId:
               		e.result = { rownumbers: false };
                    break;
            }
        };
		
		//处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
             	case 'syncancel':
                    //点击取消关闭同步商品弹窗
                    args.result = true;
                    that.Model.close();
                    break;
                case 'synproduct':
                    //保存发布操作
                    args.result = true;
                	that.synProduct();
                    break;
            }
        };
		
		
		//查询所有未协同的协同商品(自动生成操作)
		_child.prototype.autoBuild = function (e) {
			var that = this;
			//列表数据对象
    		var autobuildEntryData = that.Model.getEntryData({ id: that.autobuildEntryId});
    		var manualassEntryData = that.Model.getEntryData({ id: that.manualassEntryId});
    		var selectObj = that.Model.getSelectRows({id:that.manualassEntryId});
			selectObj.length = 0;
			var url = '/bill/coo_product?operationno=getSynProduct',
            param = {
                simpledata: {
				 	
                }
            };
            yiAjax.p(url, param, function (r) {
		        var sendobj = r.operationResult.srvData;
		        if(sendobj.length == 0){
		        	yiDialog.mt({msg:'没有找到未协同的协同商品！', skinseq: 2});
		        }else{
		        	//避免搜索时不断增加数据清空数据表格
	        		if (autobuildEntryData) {
	                    //清空数据源
	                    autobuildEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.autobuildEntryId });
	                }
	        		if (manualassEntryData) {
	                    //清空数据源
	                    manualassEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.manualassEntryId });
	                }
	        		var autobuildlist = [];
	        		for(var i=0;i<sendobj.length;i++){
	        			autobuildlist.push({"fid":sendobj[i].fid,"fautosynproduct":sendobj[i].fname});
	                	autobuildEntryData.push(autobuildlist[i]);
	        		}
	        		//渲染数据
	                that.Model.refreshEntry({id:that.autobuildEntryId});
		        }
		    },
            function (m) {
                that.Model.unblockUI({ id: '#page#' });
                yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
            });
		};
	
		//查询所有未协同的协同商品(手动关联操作)
		_child.prototype.manualassAss = function (e) {
			var that = this;
			//列表数据对象
    		var manualassEntryData = that.Model.getEntryData({ id: that.manualassEntryId});
    		var autobuildEntryData = that.Model.getEntryData({ id: that.autobuildEntryId});
    		var selectObj = that.Model.getSelectRows({id:that.autobuildEntryId});
			selectObj.length = 0;
			var url = '/bill/coo_product?operationno=getSynProduct',
            param = {
                simpledata: {
				 	
                }
            };
            yiAjax.p(url, param, function (r) {
		        var sendobj = r.operationResult.srvData;
		        if(sendobj.length == 0){
		        	yiDialog.mt({msg:'没有找到未协同的协同商品！', skinseq: 2});
		        }else{
		        	//避免搜索时不断增加数据清空数据表格
	        		if (manualassEntryData) {
	                    //清空数据源
	                    manualassEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.manualassEntryId });
	                }
	        		//避免搜索时不断增加数据清空数据表格
	        		if (autobuildEntryData) {
	                    //清空数据源
	                    autobuildEntryData.length = 0;
	                    //刷新表格
	                    that.Model.refreshEntry({ id: that.autobuildEntryId });
	                }
	        		var manualasslist = [];
	        		for(var i=0;i<sendobj.length;i++){
	        			manualasslist.push({"fid":sendobj[i].fid,"fmanuasynproduct":sendobj[i].fname});
	                	manualassEntryData.push(manualasslist[i]);
	        		}
	        		//渲染数据
	                that.Model.refreshEntry({id:that.manualassEntryId});
		        }
        		
		    },
            function (m) {
                that.Model.unblockUI({ id: '#page#' });
                yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
            });
		};
		
		//同步操作
		_child.prototype.synProduct = function (e) {
			var that = this;
			var synType = that.Model.getSimpleValue({ id: 'flocalproduct' });
			if(synType === 'localproduct_01'){
				var synData = [];
				var unitData = [];
				var Hasunit = 0;
				var autobuildData = that.Model.getSelectRows({id:that.autobuildEntryId});
				if(autobuildData.length == 0){
					yiDialog.mt({msg:'请选择要同步的协同商品！', skinseq: 2});
				}else{
					for(var i = 0;i < autobuildData.length;i++){
						if(!autobuildData[i].data.funit){
							yiDialog.mt({msg:'请选择同步商品的单位！', skinseq: 2});
						}else if(autobuildData[i].data.funit){
							if(!autobuildData[i].data.funit.id){
								yiDialog.mt({msg:'请选择同步商品的单位！', skinseq: 2});
							}else{
								Hasunit++;
								var SynsId = autobuildData[i].data.fid;
								synData.push(SynsId);
								var UnitsId = autobuildData[i].data.funit.id;
								unitData.push(UnitsId);
							}
						}
					}
					if(Hasunit == autobuildData.length){
						var url = '/bill/ydj_product?operationno=synToNewProduct',
			            param = {
			                simpledata: {
			                	synproduct: synData.join(","),
							 	unit: unitData.join(",")
			                }
			            };
			            yiDialog.c('确定执行自动生成本地商品操作？',function(){
			            	yiAjax.p(url, param, function (r) {
			            		if(r.operationResult.isSuccess == true){
			            			yiDialog.mt({msg:'自动生成本地商品成功！', skinseq: 1});
			            		}
				            },
			                function (m) {
			                    that.Model.unblockUI({ id: '#page#' });
			                    yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
			                });
			            });
					}
				}
				
			}else if(synType === 'localproduct_02'){
				var synData = [];
				var proData = [];
				var Hasunit = 0;
				var manualassData = that.Model.getSelectRows({id:that.manualassEntryId});
				if(manualassData.length == 0){
					yiDialog.mt({msg:'请选择要同步的协同商品！', skinseq: 2});
				}else{
					for(var i = 0;i < manualassData.length;i++){
						if(!manualassData[i].data.fmanualocalproduct){
							yiDialog.mt({msg:'请选择要关联的本地商品！', skinseq: 2});
						}else if(manualassData[i].data.fmanualocalproduct){
							if(!manualassData[i].data.fmanualocalproduct.id){
								yiDialog.mt({msg:'请选择要关联的本地商品！', skinseq: 2});
							}else{
								Hasunit++;
								var SynsId = manualassData[i].data.fid;
								synData.push(SynsId);
								var UnitsId = manualassData[i].data.fmanualocalproduct.id;
								proData.push(UnitsId);
							}
						}
					}
					if(Hasunit == manualassData.length){
						var url = '/bill/ydj_product?operationno=synToProduct',
			            param = {
			                simpledata: {
			                	syn: synData.join(","),
							 	pro: proData.join(",")
			                }
			            };
			            yiDialog.c('确定执行手动关联本地商品操作？',function(){
			            	yiAjax.p(url, param, function (r) {
			            		if(r.operationResult.isSuccess == true){
			            			yiDialog.mt({msg:'关联本地商品成功！', skinseq: 1});
			            		}
				            },
			                function (m) {
			                    that.Model.unblockUI({ id: '#page#' });
			                    yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
			                });
			            });
					}
				}
				
			}
		}
		
        return _child;
    })(BasePlugIn);
    window.coo_synproduct = window.coo_synproduct || coo_synproduct;
})();