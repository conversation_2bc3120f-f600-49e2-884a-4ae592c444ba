/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ste/ser_reward.js
 */
; (function () {
    var ser_reward = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
            
        };
        //继承 BasePlugIn
        __extends(_child, _super);
        
        _child.prototype.onBillInitialized = function(args){
        	this.data=this.Model.clone();
        	this.Model.setVisible({id:'.y-rewardstype',value:false});
        	this.Model.setVisible({id:'.y-punishtype',value:false});
        	//初始新增的单据，产品问题和服务问题是不显示的。只有勾选了问题类别之后才显示下面板块产品问题或服务问题板块
        	//this.Model.pkid存在，则不是新增页面
        	if(!this.Model.pkid){
        		//新增页面，奖励类型默认奖励,显示奖励方式和并奖励奖金
        		//this.Model.setValue({id:'frewardpunish',value:{id:'rewandtype001'}});
          		this.Model.setVisible({id:'.y-rewardstype',value:true});
        	}else{
        		
        		if(this.data.frewardpunish && this.data.frewardpunish.id){
        			this.serstatus(this.data.frewardpunish.id);
        		}
        		
        		
        	}
        	
        }
        
        _child.prototype.serstatus = function(status,params){
        	
        	if(status){
        		switch(status){
    				case 'reward01'://奖励
    					this.Model.setVisible({id:'.y-rewardstype',value:true});
    					this.Model.setVisible({id:'.y-punishtype',value:false});
    				break;
    				case 'reward02'://处罚
    					this.Model.setVisible({id:'.y-rewardstype',value:false});
    					this.Model.setVisible({id:'.y-punishtype',value:true});
    					break;
        		    case 'reward03'://退回罚款
        		        this.Model.setVisible({ id: '.y-rewardstype', value: true });
        		        this.Model.setVisible({ id: '.y-punishtype', value: false });
                        break;
    			}
        	}
        	if(params){
	        	//在变化状态的时候，所有值清空
	        	this.Model.setValue({id:'frewardtype',value:{id:''}});//奖励方式
	        	this.Model.setValue({id:'frewardmoney',value:''});//并奖励现金
	        	this.Model.setValue({id:'fpunishtype',value:{id:''}});//处罚方式
	        	this.Model.setValue({id:'fpunishmoney',value:''});//并处罚现金
        	}
        }
        
        _child.prototype.onFieldValueChanged = function(e){
        	
        	var that = this;
            switch (e.id.toLowerCase()) {
            	case 'frewardpunish':
            		//变化状态
                	this.serstatus(e.value.id,'change');
                	
                break;
                 
            }
        }
        
        return _child;
    })(BasePlugIn);
    window.ser_reward = window.ser_reward || ser_reward;
})();