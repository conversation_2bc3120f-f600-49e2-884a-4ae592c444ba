///<reference path="../../js/consts.js" />
/*
@ sourceURL=/fw/js/sys/sys_musisereditor.js
*/
; (function () {
    var sys_musisereditor = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        __extends(_child, _super);

        //控件初始化之后
        _child.prototype.onInitialized = function (e) {
            var that = this;

            //当前页面上所有字段的可用性 = 父页面字段的可用性
            var cp = that.formContext.cp;
            if (cp && cp.enable === false) {
                var fields = that.Model.uiForm.getAllFields();
                if (fields) {
                    for (var i = 0; i < fields.length; i++) {
                        that.Model.setEnable({ id: fields[i], value: cp.enable });
                    }
                }
            }
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                //确定
                case 'confirm':
                    e.result = true;
                    if (!that.Model.validForm()) return;
                    var cp = that.formContext.cp;
                    var parentVM = Index.getPage(cp.parentPageId);
                    if (parentVM) {
                        var uiData = that.Model.clone();
                        var bizObjMap = $.trim(uiData.fbizobjmap.id);
                        if (!bizObjMap) {
                            yiDialog.warn('请选择SAP业务对象映射！');
                            return;
                        }
                        //删除非存储字段
                        delete uiData.id;
                        delete uiData.parentPageId;
                        delete uiData.paramFormId;
                        delete uiData.rowId;
                        delete uiData.fieldKey;
                        //回填服务配置信息
                        parentVM.Model.setValue({
                            id: cp.fieldKey,
                            row: cp.rowId,
                            value: {
                                id: JSON.stringify(uiData),
                                name: '慕思数据集成服务配置'
                            }
                        });
                    }
                    //关闭对话框
                    that.Model.close();
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.sys_musisereditor = window.sys_musisereditor || sys_musisereditor;
})();