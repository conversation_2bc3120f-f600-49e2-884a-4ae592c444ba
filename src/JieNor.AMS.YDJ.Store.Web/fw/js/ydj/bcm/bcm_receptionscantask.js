/**
 * 条码主档业务插件
 * @ sourceURL=/fw/js/ydj/bcm/bcm_barcodemaster.js
 */
; (function () {
    var bcm_receptionscantask = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //页面视图渲染时触发
        _child.prototype.onPageViewRendering = function (e) {
            var that = this;
            var controlOptions = ["new", "modify", "view", "push"];

            if (that.formContext && that.formContext.status && controlOptions.indexOf(that.formContext.status) >= 0) {
                if (that.uiForm && that.uiForm.uiMeta) {
                    yiAjax.p('/dynamic/bcm_receptionscantask?operationno=QueryFieldsPermission', null, function (r) {
                        var srvData = r.operationResult.srvData;
                        if (srvData) {
                            var uiMeta = that.uiForm.uiMeta;
                            if (uiMeta.fsupplierorderno) {
                                uiMeta.fsupplierorderno.allowShowOrHide = srvData.isPermissionAgent;
                            }
                            if (uiMeta.fprice) {
                                uiMeta.fprice.allowShowOrHide = srvData.isVisibleAmountRole;
                                uiMeta.fprice.editable = srvData.isVisibleAmountRole;
                            }
                            if (uiMeta.famount) {
                                uiMeta.famount.allowShowOrHide = srvData.isVisibleAmountRole;
                                uiMeta.famount.editable = srvData.isVisibleAmountRole;
                            }
                        }
                    }, null, null, null, { async: false });
                }
            }
        };

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.loadTraceEntrys();

            if (that.Model.pkid) {
                that.isHavePackOrder();
            }
            that.LockReceiptionLogisticNo();
            that.LoadSourceBillSupplier();
            var task_type = that.Model.getSimpleValue({ id: "ftask_type" });
            if (Consts.isdirectsale && task_type =='stk_postockin') {
                that.Model.setVisible({ id: '[menu="push2poinstock"]', value: true });
            } else {
                that.Model.setVisible({ id: '[menu="push2poinstock"]', value: false });
            }
        };

        //操作前触发的事件
        _child.prototype.onBeforeDoOperation = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'save':
                    var taskstatus = that.Model.getSimpleValue({ id: 'ftaskstatus' });
                    if (taskstatus != 'ftaskstatus_01') {
                        yiDialog.mt({ msg: '当前收货任务正在作业中或已完成，不允许修改数据，谢谢！', skinseq: 3 });
                        e.cancel = true;
                    }
                    //明细数据
                    var traceEntrys = that.Model.getEntryData({ id: 'fscannedentity' });
                    traceEntrys.length = 0; //保存时清空追溯明细(追溯明细仅展示，传入会影响保存)
                    break;
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;

            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;

            switch (e.id) {
                case 'querybybarcode':
                    that.fillTraceEntrys(srvData);
                    break;
                case 'tbcompletetask':
                case 'tbcontinuetask':
                    that.Model.refresh();
                    break;
                case 'IsExistPackOrder':
                    ;
                    //已经存在对应的《包装清单》时, 将对应的《包装清单》在弹出的视窗中打开表单
                    if (isSuccess) {
                        var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
                        var filterString = " fsourcetype ='{0}' and fsourcenumber='{1}' and fmainorgid = '{2}'"
                            .format('bcm_receptionscantask', fbillno, Consts.loginCompany.id);
                        that.Model.showForm({
                            formId: 'bcm_packorder',
                            domainType: Consts.domainType.list,
                            param: {
                                openStyle: Consts.openStyle.modal,
                                filterstring: filterString
                            }
                        });
                    } else {
                        //不存在对应的《包装清单》时, 系统自动弹出全新的《包装清单》新增界面,
                        that.Model.invokeFormOperation({
                            id: 'push',
                            opcode: 'push',
                            param: {
                                'ruleId': "bcm_receptionscantask2bcm_packorder"
                            }
                        });
                    }
                    break;
                case 'ishavepackorder':
                    if (isSuccess) {
                        //已存在则锁定【本次作业数量】
                        that.Model.setEnable({ id: "fcurrworkqty", value: false });
                        that.Model.setEnable({ id: "freceptionno", value: false });//收货单号
                        that.Model.setEnable({ id: "flogisticsno", value: false });//物流单号
                        that.Model.setEnable({ id: "fsenddate", value: false });//工厂发货日期
                        that.Model.setEnable({ id: "fdescription", value: false });//备注
                    }
                    break;
                case 'loadsupplier':
                    if (isSuccess) {
                        that.Model.setValue({ id: "fsupplierid", value: srvData });
                        that.Model.setEnable({ id: "#tbPull", value: true });//解锁选单按钮
                    }
                    break;
                case 'LoadInStockData':
                    if (isSuccess) {
                        that.showStockDialog(srvData);
                    }
                    break;
            }

        };

        //填充属性明细
        _child.prototype.fillTraceEntrys = function (srvData) {
            var that = this;
            //明细数据
            var traceEntrys = that.Model.getEntryData({ id: 'fscannedentity' });
            traceEntrys.length = 0; //先清空
            ;
            var fentity = srvData.data || [];

            if (fentity && fentity.length > 0) {
                for (var i = 0; i < fentity.length; i++) {
                    fentity[i]["fnote_d"] = fentity[i]["fdescription"];
                    //fentity[i].["fmaterialid_fname"] = fentity[i].fmaterialName;
                    fentity[i]["fmaterialid_fname_b"] = fentity[i].fmaterialid_fname_b;
                    fentity[i]["fseltypeid_b"] = fentity[i].fseltypeid_b;

                    traceEntrys.push(fentity[i]);
                }
            }
            that.Model.refreshEntry({ id: 'fscannedentity' });
            //  console.log(that.Model.getEntryData({ id: 'fscannedentity' }));

        };

        //根据条码加载追溯明细
        _child.prototype.loadTraceEntrys = function () {
            var that = this;

            var fbillno = that.Model.getSimpleValue({ id: 'fbillno' });
            if (!fbillno) {
                //清空明细数据
                that.Model.deleteEntryData({ id: 'fscannedentity' });
                return;
            }

            //根据条码请求加载条码扫描记录
            //var filterString = "fisparentbarcode='1' and fscantaskformid ='{0}' and fscantaskbillno ='{1}' and fmainorgid = '{2}' order by fopdatetime ".format("bcm_receptionscantask", fbillno, Consts.loginCompany.id);
            //平台包调整，带排序语句会报错
            var filterString = "fisparentbarcode='1' and fscantaskformid ='{0}' and fscantaskbillno ='{1}' and fmainorgid = '{2}' ".format("bcm_receptionscantask", fbillno, Consts.loginCompany.id);

            that.Model.invokeFormOperation({
                id: 'querybybarcode',
                opcode: 'querydata',
                filterString: filterString,
                param: {
                    formId: 'bcm_scanresult',
                    domainType: Consts.domainType.list,
                }
            });

        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fcurrworkqty':
                    ;
                    //需求4967：【扫描任务明细.待作业数量】=【扫描任务明细.本次作业数量】-【扫描任务明细.已作业数量】
                    var currworkqty = that.Model.getValue({ id: 'fcurrworkqty', row: e.row });
                    var workedqty = that.Model.getValue({ id: 'fworkedqty', row: e.row });
                    var waitworkqty = currworkqty - workedqty;

                    that.Model.setValue({ id: 'fwaitworkqty', value: waitworkqty, row: e.row });
                    that.Model.setValue({ id: 'fqty', value: currworkqty, row: e.row });
                    break;
            };
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode.toLowerCase()) {
                case 'addcode'://打码
                    e.result = true;
                    var taskentity = that.Model.getEntryData({ id: 'ftaskentity' });
                    if (taskentity.length == 0) return;
                    var sourceType = taskentity[0].fsourceformid;
                    if (sourceType.id == 'ydj_purchaseorder') {
                        that.addcode(e);
                    } else {
                        yiDialog.mt({ msg: '当前收货扫描任务源单非采购订单，不允许打码!', skinseq: 3 });
                    }
                    break;
                case 'linkbarcode'://条码联查
                    e.result = true;
                    var selectData = that.Model.getSelectRows({ id: 'ftaskentity' });
                    if (selectData.length == 0) {
                        yiDialog.mt({ msg: '未选中行，请您选择后再进行联查操作 !', skinseq: 3 });
                        return;
                    }
                    ;
                    var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
                    var lineNos = new Array(selectData.length);
                    for (var i = 0; i < selectData.length; i++) {
                        lineNos[i] = "'" + selectData[i].data.FSeq + "'";
                    }
                    var strLineNos = lineNos.join(",");
                    var filterString = " fsourcetype ='{0}' and fsourcenumber='{1}' and fmainorgid = '{2}' and fsourcelinenumber in ({3})"
                        .format('bcm_receptionscantask', fbillno, Consts.loginCompany.id, strLineNos);
                    that.Model.showForm({
                        formId: 'bcm_barcodemaster',
                        domainType: Consts.domainType.list,
                        param: {
                            openStyle: Consts.openStyle.modal,
                            filterstring: filterString
                        }
                    });
                    break;
                case 'pull':
                    that.pull(e);
                    break;
                case 'push2poinstock':
                    e.result = true;

                    that.Model.invokeFormOperation({
                        id: 'LoadInStockData',
                        opcode: 'LoadInStockData',
                        selectedRows: [{ PKValue: that.Model.pkid }],
                        param: {
                            'rowIds': that.getSelRowData()
                        }
                    });
                    break;
            };
        };

        //选单前事件
        _child.prototype.onBeforPull = function (e) {

            var that = this;
            e.sourceFormId = 'ydj_purchaseorder';
        };

        //打码请求判断包装清单是否存在接口
        _child.prototype.addcode = function (e) {
            var that = this;
            e.result = true;

            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            that.Model.invokeFormOperation({
                id: 'IsExistPackOrder',
                opcode: 'IsExistPackOrder',
                param: {
                    'formId': 'bcm_packorder',
                    'fsourcetype': 'bcm_receptionscantask',
                    'fsourcenumber': fbillno,
                }
            });
        };

        //判断明细行是否关联包装清单
        _child.prototype.isHavePackOrder = function () {
            var that = this;

            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            that.Model.invokeFormOperation({
                id: 'ishavepackorder',
                opcode: 'IsExistPackOrder',
                param: {
                    'formId': 'bcm_packorder',
                    'fsourcetype': 'bcm_receptionscantask',
                    'fsourcenumber': fbillno,
                }
            });
        };

        //判断明细行是否存在【已作业数】>0的行，存在则锁定【发货单号】【物流单号】
        _child.prototype.LockReceiptionLogisticNo = function () {
            var that = this;

            var entryData = that.Model.getEntryData({ id: 'ftaskentity' });
            if (entryData == null || entryData.length <= 0) return;

            for (var i = 0; i < entryData.length; i++) {
                if (entryData[i].fworkedqty > 0) {
                    that.Model.setEnable({ id: "freceptionno", value: false });//收货单号
                    that.Model.setEnable({ id: "flogisticsno", value: false });//物流单号
                    that.Model.setEnable({ id: "fsenddate", value: false });//工厂发货日期
                    that.Model.setEnable({ id: "fdescription", value: false });//备注
                }
            }
        };

        // 选单
        _child.prototype.pull = function (e) {
            var that = this;
            ;
            var taskType = that.Model.getSimpleValue({ id: "ftask_type" });
            if (taskType == "stk_sostockreturn" || taskType == "stk_otherstockin") {
                yiDialog.mt({ msg: '收货扫描任务暂时仅允许选单采购订单，不支持选单销售退货单或其它入库单，请到对应单据中打码生成收货任务，谢谢！', skinseq: 3 });
                e.result = true;
                return;
            }
            var fsupplierid = that.Model.getSimpleValue({ id: "fsupplierid" });
            if (fsupplierid) {
                e.param.fsupplierid = fsupplierid;
            }
        };

        //获取源单供应商
        _child.prototype.LoadSourceBillSupplier = function () {
            var that = this;

            var supplierid = that.Model.getSimpleValue({ id: "fsupplierid" });
            var taskType = that.Model.getSimpleValue({ id: "ftask_type" });
            if (!supplierid && taskType == "stk_postockin") {
                that.Model.setEnable({ id: "#tbPull", value: false });//先锁定选单按钮

                var currBillNo = that.Model.getSimpleValue({ id: "fbillno" });
                that.Model.invokeFormOperation({
                    id: 'loadsupplier',
                    opcode: 'loadsupplier',
                    param: {
                        formId: 'bcm_receptionscantask',
                        currBillNo: currBillNo
                    }
                });
            }
        };

        //显示结算对话框
        _child.prototype.showStockDialog = function (obj) {
            var that = this;
            that.Model.showForm({
                formId: 'bcm_recinstockdialog',
                param: { openStyle: Consts.openStyle.modal },
                cp: {
                    //freceptionid: obj.freceptionid,
                    //freceptionentryid: obj.freceptionentryid,
                    //fpoid: obj.fpoid,
                    //fpobillno: obj.fpobillno,
                    //fpoentryid: obj.fpoentryid,
                    //fmaterialid: obj.fmaterialid,
                    //fattrinfo: obj.fattrinfo,
                    //fattrinfo_e: obj.fattrinfo_e,
                    //fcustomdes_e: obj.fcustomdes_e,
                    //funitid: obj.funitid,
                    //fstorehouseid: obj.fstorehouseid,
                    //fstorelocationid: obj.fstorelocationid,
                    //fcurrworkqty: obj.fcurrworkqty,
                    //fwaitinstockqty: obj.fwaitinstockqty,
                    //finstockqty: obj.finstockqty,
                    //fbizqty: obj.fbizqty,
                    'data': obj,
                    parentPageId: that.pageId,
                    callback: function (result) {
                        if (result && result.isSuccess) {
                            that.Model.refresh();
                        }
                    }
                }
            });
        }

        //返回选中行，不勾选时为全选返回全部行ID
        _child.prototype.getSelRowData = function () {
            var that = this;
            var rows = that.Model.getSelectRows({ id: 'ftaskentity' });
            var rowIds = [];
            rows.forEach(function (row) {
                rowIds.push({ Id: row["pkid"] });
            });
            if (rows.length == 0) {//不勾选时默认全选
                var mdl = that.Model.getEntryData({ id: 'ftaskentity' });
                for (var i = 0; i < mdl.length; i++) {
                    rowIds.push({ Id: mdl[i].id });
                }
            }
            return rowIds;
        }


        return _child;
    })(BasePlugIn);
    window.bcm_receptionscantask = window.bcm_receptionscantask || bcm_receptionscantask;
})();