// 包装清单业务插件
; (function () {
    var bcm_packorderinit = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.packsourceEntry = 'fentity';

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            debugger;
            var that = this;
            //根据业务类型显示隐藏明细列

            //检查《待包装明细》打包类型
            var entryData = that.Model.getEntryData({ id: that.packsourceEntry });
            if (entryData != null && entryData.length > 0) {
                for (var i = 0; i < entryData.length; i++) {
                    var row = entryData[i];
                    if (row.fpacktype.id == '1') {
                        //标准类型时设置【包数/件数】不可编辑
                        that.Model.setEnable({ id: 'fpackcount', value: false, row: row.id }); 
                         
                    } 
                }
            }
        };

        _child.prototype.canPackage = function (e) {
            var that = this;
            var canpack = true;
            var sourceEntryData = that.Model.getEntryData({ id: 'fentity' });//待包装明细
            var Msg = "";
            for (var i = 0; i < sourceEntryData.length; i++) {
                //先判断是否存在{【打包类型】=标准 AND 【包数/件数】!=1}的商品行，若存在则抛出弹框提醒：“第XX行商品[xx]，打包类型】=标准，但是【包数/件数】不为1，请确认是否更新【包数/件数】为1？”
                if (sourceEntryData[i].fpacktype.id == '1' && sourceEntryData[i].fpackcount != '1') {
                    var rowNo = i + 1;
                    Msg += "第【" + rowNo + "】行商品【" + sourceEntryData[i].fmaterialid_fnumber + "】 ";
                }
            }
            if (Msg.length > 1) {
                canpack = false;
                Msg += "【打包类型】=标准，但是【包数/件数】不为1，请确认是否更新【包数/件数】为1！";
            }

            if (!canpack) {
                yiDialog.c(Msg, function () {
                    var param = e.param;
                    param.formId = "bcm_packorderinit";
                    //点是，则将对应行【包数/件数】更新为1，并且调用一次<保存>的操作。
                    that.Model.invokeFormOperation({
                        id: 'tbSave',
                        opcode: 'save',
                        param: param
                    });
                }, function () {
                });
            }
            e.result = true;
            return canpack;
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            debugger;
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                //打包操作
                case 'createpackageinit':
                    e.result = true;
                    //var parentViewModel = Index.getPage(that.Model.viewModel.parentPageId),
                    //    autobcinstock = parentViewModel.Model.getValue({ id: 'fautobcinstock' });
                    var packData = [],
                        selectData = that.Model.getSelectRows({ id: 'fentity' });
                    if (selectData.length == 0) {
                        yiDialog.mt({ msg: '请勾选要打包的商品！', skinseq: 3 });
                        return;
                    }
                    if (!that.canPackage(e)) return;

                    var checkData = [];
                    for (var i = 0; i < selectData.length; i++) {
                        checkData.push(selectData[i].data);
                    }
                    if (!that.checkPackCount(checkData)) {
                        yiDialog.mt({ msg: '标准件的包数/件数只能为1，非标准件的包数/件数不能为1，请检查！', skinseq: 3 });
                        return;
                    }
                    for (var i = 0, j = selectData.length; i < j; i++) {
                        //如果父单据勾选了打码收货后同步入库，则仓库，库存状态必填
                        //if(parentViewModel.formId!='bcm_packorder'){
                        //    if(autobcinstock){
                        //        if(($.trim(selectData[i].data.fstorehouseid.id)=='') || ($.trim(selectData[i].data.fstockstatus.id)=='')){
                        //            yiDialog.mt({ msg: '仓库、库存状态是必填字段！', skinseq: 2 });
                        //            return;
                        //        }
                        //    }
                        //}
                        if (selectData[i].data.fbizpackqty > selectData[i].data.fbizremainqty) {
                            //数据不合法，不继续操作
                            return;
                        }
                        var obj = {};
                        //如果本次包装数量为0则不进行打包处理
                        if (selectData[i].data.fbizpackqty > 0) {
                            obj = {
                                sourceFormId: selectData[i].data.fsourceformid.id,
                                sourceInterId: selectData[i].data.fsourceinterid,
                                sourceEntryId: selectData[i].data.fsourceentryid,
                                qty: selectData[i].data.fbizpackqty,
                                packType: selectData[i].data.fpacktype.id,
                                packCount: selectData[i].data.fpackcount,
                                stockId: selectData[i].data.fstorehouseid.id,
                                stockplaceId: selectData[i].data.fstorelocationid.id,
                                stockStatusId: selectData[i].data.fstockstatus.id,
                                bizpackqty: selectData[i].data.fbizpackqty,
                                suiteflag: '',
                                tjno: selectData[i].data.ftjno,
                                pjno: selectData[i].data.fpjno,
                                sfno: selectData[i].data.fsfno,
                                materialid: selectData[i].data.fmaterialid.id,
                                sourceseq: selectData[i].data.fsourceseq,
                                entrynote: selectData[i].data.fentrynote,
                                seq: selectData[i].data.FSeq
                            }
                            packData.push(obj);
                        }
                    }
                    //if(packData.length == 0){
                    //    yiDialog.mt({ msg: '您选择的打包数据填写不完整，请填写完整！', skinseq: 2 });
                    //    return;
                    //}
                    that.Model.invokeFormOperation({
                        id: 'tbCreatepackage',
                        opcode: 'createpackageinit',
                        param: {
                            packData: JSON.stringify(packData),
                            pkid: that.Model.uiData.id
                        }
                    });
                    break;
                //删除操作
                case 'deletepackage':
                    e.result = true;
                    yiDialog.c('将会删除选中数据包码相同的数据，确定删除？', function () {
                        var entryData = that.Model.getEntryData({ id: 'fpackentity' }),//已包装明细
                            sourceEntryData = that.Model.getEntryData({ id: 'fentity' }),//待包装明细
                            selectData = that.Model.getSelectRows({ id: 'fpackentity' });//选择的已包装明细行数据
                        var deleteArr = [];//存储删除的条码的临时数组
                        for (var i = 0, j = selectData.length; i < j; i++) {
                            var selectBC = selectData[i].data.fpackgroup_p;//选择删除的已包装明细行的包码
                            var deleteID = selectData[i].data.fsourceentryid_p;//选择删除的已包装明细行的源单内码
                            for (var k = entryData.length - 1; k >= 0; k--) {
                                //如果包码等于明细的包码
                                if (selectData[i].data && entryData[k] && selectBC == entryData[k].fpackgroup_p) {
                                    if (deleteArr.length == 0 || JSON.stringify(deleteArr).indexOf(JSON.stringify({
                                        'packgroup': entryData[k].fpackgroup_p,
                                        'sourceentryid': entryData[k].fsourceentryid_p,
                                        'qty': entryData[k].fqty_p
                                    })) == -1) {
                                        deleteArr.push({
                                            'packgroup': entryData[k].fpackgroup_p,
                                            'sourceentryid': entryData[k].fsourceentryid_p,
                                            'qty': entryData[k].fqty_p
                                        });
                                    }
                                    //如果是相同的包码则从已包明细里删除
                                    entryData.splice(k, 1);
                                }
                            }
                        }
                        //反填待包装明细的数量
                        for (var a = 0, b = sourceEntryData.length; a < b; a++) {
                            for (var i = 0, j = deleteArr.length; i < j; i++) {
                                var bizremainqty = that.Model.getValue({ id: 'fbizremainqty', row: sourceEntryData[a].id });//可包装数量
                                var bizpackedqty = that.Model.getValue({ id: 'fbizpackedqty', row: sourceEntryData[a].id });//已包装数量
                                //
                                if (deleteArr[i].sourceentryid == sourceEntryData[a].fsourceentryid) {
                                    var packgroup = deleteArr[i].packgroup.substr(0, 1);
                                    that.Model.setValue({ id: 'fbizremainqty', row: sourceEntryData[a].id, value: bizremainqty + deleteArr[i].qty });
                                    that.Model.setValue({ id: 'fbizpackedqty', row: sourceEntryData[a].id, value: bizpackedqty - deleteArr[i].qty });
                                }
                            }
                        }
                        that.Model.refreshEntry({ id: that.packorderentry });
                    });
                    break;
                //审核
                case 'auditflow':
                    if (that.Model.viewModel.domainType != 'list') {
                        var sourceEntryData = that.Model.getEntryData({ id: 'fentity' });//待包装明细
                        for (var i = 0; i < sourceEntryData.length; i++) {
                            var fbizremainqty = sourceEntryData[i].fbizremainqty;//可包装数量
                            if (fbizremainqty > 0) {
                                var rowNo = i + 1;
                                yiDialog.a("第【" + rowNo + "】行明细还有可包装的数量，请检查！");
                                e.result = true;
                                return;
                            }
                        }
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //包数/件数，本次包装数量
                case 'fpackcount':
                case 'fbizpackqty':
                case 'fbizremainqty':
                    var packtype = that.Model.getValue({ id: 'fpacktype', row: e.row });//打包类型
                    var packcount = that.Model.getValue({ id: 'fpackcount', row: e.row });//包数/件数
                    var bizpackqty = that.Model.getValue({ id: 'fbizpackqty', row: e.row });//本次包装数量
                    var bizremainqty = that.Model.getValue({ id: 'fbizremainqty', row: e.row });//可包装数量
                    //如果本次包装数量大于可包装数量，则设置本次包装数量为0
                    if (packtype.id && bizpackqty > bizremainqty) {
                        yiDialog.mt({ msg: '本次包装数量不能大于可包装数量！', skinseq: 2 });
                        setTimeout(function () {
                            that.Model.setValue({ id: 'fbizpackqty', row: e.row, value: 0 });
                        }, 10);
                    }
                    //else if(packtype.id && packtype.id == '3' &&
                    //    bizpackqty > bizremainqty){
                    //    yiDialog.mt({ msg: '本次包装数量不能大于可包装数量！', skinseq: 2 });
                    //    setTimeout(function(){
                    //        that.Model.setValue({id:'fbizpackqty',row:e.row,value:0});
                    //    },10);
                    //}
                    //如果类型为一包多件，且本次包装数量除以包数/件数不为整数
                    if (packtype.id && packtype.id == '3' && Math.round(bizpackqty / packcount) != bizpackqty / packcount) {
                        yiDialog.mt({ msg: '不符合规则,请检查录入合法性！如果类型为一包多件，本次包装数量除以包数/件数要为整数。', skinseq: 2 });
                        setTimeout(function () {
                            that.Model.setValue({ id: 'fbizpackqty', row: e.row, value: 0 });
                        }, 10);
                    }
                    var fbizqty = bizremainqty - bizpackqty;
                    that.Model.setValue({ id: 'fbizqty', row: e.row, value: fbizqty });
                    break;
                case 'fpacktype':
                case 'fmaterialid_fpiece':
                case 'fmaterialid_fbag':
                    var packtype = that.Model.getValue({ id: 'fpacktype', row: e.row });//打包类型
                    if (packtype.id == '1') {
                        that.Model.setEnable({ id: 'fpackcount', value: false, row: e.row });
                        that.Model.setValue({ id: 'fpackcount', row: e.row, value: 1 });

                    } else {
                        if (packtype.id == '2') {
                            var fbag = that.Model.getValue({ id: 'fmaterialid_fbag', row: e.row });
                            that.Model.setValue({ id: 'fpackcount', row: e.row, value: fbag });
                            //if (fbag > 0) {
                            //    that.Model.setEnable({ id: 'fpackcount', value: false, row: e.row });
                            //}
                        } else {
                            if (packtype.id == '3') {
                                var fpiece = that.Model.getValue({ id: 'fmaterialid_fpiece', row: e.row });
                                that.Model.setValue({ id: 'fpackcount', row: e.row, value: fpiece });
                                //if (fpiece > 0) {
                                //    that.Model.setEnable({ id: 'fpackcount', value: false, row: e.row });
                                //}
                            }
                        }
                        that.Model.setEnable({ id: 'fpackcount', value: true, row: e.row });
                        //that.Model.setValue({ id: 'fpackcount', row: e.row, value: 0 });
                    }
                    //var packtype = that.Model.getValue({ id: 'fpacktype', row: e.row });//打包类型

                    break;
                case 'fmaterialid_fpackagtype':
                    var fpackagtype = that.Model.getValue({ id: 'fmaterialid_fpackagtype', row: e.row });
                    var fpacktype = that.Model.getValue({ id: 'fpacktype', row: e.row });
                    if (fpacktype == '')
                        that.Model.setValue({ id: 'fpacktype', row: e.row, value: fpackagtype });
                    break;

            }
        }


        //操作返回数据
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            if (!e || !e.opcode) return;
            var isSuccess = e.result.operationResult.isSuccess,
                srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'createpackageinit':
                    if (isSuccess) {
                        that.Model.refresh();
                    }
                    break;
            }
        };

        //检查标准类型包装数量
        _child.prototype.checkPackCount = function (datas) {
            if (datas == null || datas.length <= 0) return;

            for (var i = 0; i < datas.length; i++) {
                debugger;
                if (datas[i].fpacktype.id == "1" && datas[i].fpackcount > 1) {
                    return false;
                }
                else if (datas[i].fpacktype.id != "1" && datas[i].fpackcount == 1) {
                    return false;
                }
            }

            return true;
        };

        return _child;
    })(BasePlugIn);
    window.bcm_packorderinit = window.bcm_packorderinit || bcm_packorderinit;
})();