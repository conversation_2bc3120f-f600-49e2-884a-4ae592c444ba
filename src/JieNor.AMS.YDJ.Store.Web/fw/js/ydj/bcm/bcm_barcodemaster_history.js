/**
 * ��������ҵ����
 * @ sourceURL=/fw/js/ydj/bcm/bcm_barcodemaster_history.js
 */
; (function () {
    var bcm_barcodemaster_history = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //��ʼ���༭ҳ����
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.loadTraceEntrys();
             //���ع鵵��ť
            that.Model.setVisible({ id: '[menu=tbArching]', value: false });
            that.Model.setVisible({ id: '[opcode=Archiving]', value: false });
            if (that.Model.viewModel.domainType == Consts.domainType.bill)
            {
            }
        }; 

        //����ǰ�������¼�
        _child.prototype.onBeforeDoOperation = function (e) {
            var that = this;
            switch (e.opcode) {
                case 'save':
                    //��ϸ����
                    var traceEntrys = that.Model.getEntryData({ id: 'ftraceentity' });
                    traceEntrys.length = 0; //����ʱ���׷����ϸ(׷����ϸ��չʾ�������Ӱ�챣��)
                    break;
            }
        };

        //�����ɹ��󴥷����¼�
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;

            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.id) {
                case 'querybybarcode':
                    that.fillTraceEntrys(srvData);
                    break;
            }
        };

        //���������ϸ
        _child.prototype.fillTraceEntrys = function (srvData) {
            var that = this;

            //��ϸ����
            var traceEntrys = that.Model.getEntryData({ id: 'ftraceentity' });
            traceEntrys.length = 0; //�����

            if (srvData) {
                var fentity = srvData.data || [];
                if (fentity && fentity.length > 0) {
                    for (var i = 0; i < fentity.length; i++) {
                        fentity[i].fremark = fentity[i].fdescription;
                        fentity[i].fstorehousename = fentity[i].fstorehouseid_fname;
                        fentity[i].fstorelocationname = fentity[i].fstorelocationid_fname;
                        traceEntrys.push(fentity[i]);
                    }
                }
                that.Model.refreshEntry({ id: 'ftraceentity' });
                that.Model.viewModel.Model.dataChanged = false;//׷����ϸ�����չʾ�����ҳ�������޸�״̬��Ϊfalse,����ƽ̨js�жϻ�Ӱ����������
            }
        };

        //�����������׷����ϸ
        _child.prototype.loadTraceEntrys = function () {
            var that = this;

            var pkid = that.Model.uiData.id;
            if (!pkid) {
                //�����ϸ����
                that.Model.deleteEntryData({ id: 'ftraceentity' });
                return;
            }
            //�������������������ɨ���¼
            that.Model.invokeFormOperation({
                id: 'querybybarcode',
                opcode: 'querydata',
                //filterString: "fbarcode='" + pkid + "' order by fopdatetime",
                filterString: "fbarcode='" + pkid + "' ",//ƽ̨�����������������ᱨ��
                param: {
                    formId: 'bcm_scanresult_history',
                    domainType: Consts.domainType.list,
                }
            });
        };

        return _child;
    })(BasePlugIn);
    window.bcm_barcodemaster_history = window.bcm_barcodemaster_history || bcm_barcodemaster_history;
})();