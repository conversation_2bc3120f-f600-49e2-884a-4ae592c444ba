/**
 * 条码主档业务插件
 * @ sourceURL=/fw/js/ydj/bcm/bcm_transfertask.js
 */
(function () {
    var bcm_transfertask = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //that.loadTraceEntrys();
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;

            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.id) {
                case 'querybybarcode':
                    that.fillTraceEntrys(srvData);
                    break;
                case 'tbcompletetask':
                case 'tbcontinuetask':
                    that.Model.refresh();
                    break;
            }
        };

        //填充属性明细
        _child.prototype.fillTraceEntrys = function (srvData) {
            var that = this;
            //明细数据
            var traceEntrys = that.Model.getEntryData({ id: 'fscannedentity' });
            traceEntrys.length = 0; //先清空
            debugger;
            var fentity = srvData.data || [];

            if (fentity && fentity.length > 0) {
                for (var i = 0; i < fentity.length; i++) {
                    fentity[i]["fnote_d"] = fentity[i]["fdescription"];
                    //fentity[i].["fmaterialid_fname"] = fentity[i].fmaterialName;

                    traceEntrys.push(fentity[i]);
                }
            }
            that.Model.refreshEntry({ id: 'fscannedentity' });
            //  console.log(that.Model.getEntryData({ id: 'fscannedentity' }));

        };

        //根据条码加载追溯明细
        _child.prototype.loadTraceEntrys = function () {
            var that = this;

            var fbillno = that.Model.getSimpleValue({ id: 'fbillno' });
            if (!fbillno) {
                //清空明细数据
                that.Model.deleteEntryData({ id: 'fscannedentity' });
                return;
            }

            //根据条码请求加载条码扫描记录
            //var filterString = "fsourceformid ='{0}' and fsourcebillno ='{1}' and fmainorgid = '{2}' order by fopdatetime ".format("bcm_transfertask", fbillno, Consts.loginCompany.id);
            //平台包调整，带排序语句会报错
            var filterString = "fsourceformid ='{0}' and fsourcebillno ='{1}' and fmainorgid = '{2}' ".format("bcm_transfertask", fbillno, Consts.loginCompany.id);

            that.Model.invokeFormOperation({
                id: 'querybybarcode',
                opcode: 'querydata',
                filterString: filterString,
                param: {
                    formId: 'bcm_scanresult',
                    domainType: Consts.domainType.list,
                    endfilterString: filterString
                }
            });

        };

        return _child;
    })(BasePlugIn);
    window.bcm_transfertask = window.bcm_transfertask || bcm_transfertask;
})();