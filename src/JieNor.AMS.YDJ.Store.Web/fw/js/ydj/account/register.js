var checks = {
    consts: {
        //页面初始化时自动从后端读取
        phoneReg: /^[1]{1}[3,4,5,6,7,8,9]{1}[0-9]{9}$/,
        loginUrl: '',
        code: '1234',
        phone: '',
        siteHost: null,
        regCompany: null
    },

    checkPhone: function ($phone) {  /*手机号验证*/
        var $pError = $("#pError");
        var $display = $(".display-hide");
        if ($.trim($phone.val()) === "") {
            $phone.addClass("input_error");
            $pError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            if (checks.consts.phoneReg.test($.trim($phone.val())) === false) {
                $phone.addClass("input_error");
                $pError.show().removeClass().html("手机号格式不正确！").addClass("alerts");
                return false;
            } else {
                $phone.removeClass("input_error");
                $pError.hide();
                return true;
            }
        }
    },

    checkRecode: function ($code) {  /*验证码验证*/
        var recError = $("#recError");
        if ($.trim($code.val()) === "") {
            $code.addClass("input_error");
            recError.show().removeClass().html("该项为必填项！").addClass("alerts");
            return false;
        } else {
            $code.removeClass("input_error");
            recError.hide();
            return true;
        }
    },

    check1: function () {  /*鼠标离开时判断是否满足验证要求*/
        $(document).on("blur", "#txtPhone", function () {  //手机号
            checks.checkPhone($(this));
            checks.enableNextBtn();
        });
        $(document).on("blur", "#txtCode", function () { //验证码
            checks.checkRecode($(this));
            checks.enableNextBtn();
        });
        $(document).on("change", "input[name=agreeclause]", function () { //验证码
            checks.checkRecode($(this));
            checks.enableNextBtn();
        });
    },

    check2: function () {   /*登录,注册提交*/
        var b = true;
        var $code = $("#txtCode");
        var $phone = $("#txtPhone");

        //注册处理
        $(document).on("click", "#next", function () {

            var phone = $.trim($phone.val());
            var code = $.trim($code.val());
            var agree = $("input[name=agreeclause]").attr('checked');
            if (!phone || !code || !agree) {
                return;
            }

            var a = [];//用来存放b的数组
            var flag = true;

            $phone.each(function () {
                if (checks.checkPhone($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $code.each(function () {
                if (checks.checkRecode($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            for (var i = 0; i < a.length; i++) {
                if (a[i] === false) {
                    flag = false;
                }
            }
            checks.consts.phone = phone;
            checks.consts.code = code;
            if (flag === true) {
                //验证码校验
                yiAjax.g('/sms/code/validate.json?mobilePhone={0}&authCode={1}'.format(checks.consts.phone, checks.consts.code), {},
                    function (r) {
                        if (r && r.operationResult && r.operationResult.isSuccess) {

                            //设置向导背景色
                            checks.setGuideBg(2);

                            $(".alert").hide();
                            //判断是否存在所属企业
                            checks.consts.phone = $.trim($phone.val());
                            checks.consts.code = $.trim($code.val());
                            $('form.get-code').hide();
                            $('form.enterprise-message').show();
                            $('.content .step').eq(1).css('background', '#7ECAFF');

                            //todo:加载注册服务通道
                            var optHtml = '<option value="">请选择服务线路</option>';
                            if (checks.consts.siteHost) {
                                for (var i = 0; i < checks.consts.siteHost.length; i++) {
                                    var siteData = checks.consts.siteHost[i];
                                    optHtml += '<option value="{0}" data-url="{2}">{1}</option>'.format(siteData.id, siteData.siteName, siteData.url);
                                }
                            }
                            $("#cmbServiceLine").html(optHtml).select2({ placeholder: '请选择服务线路', allowClear: true });
                        } else {
                            yiCommon.showError('验证码错误，请重新输入！', '', $('.login-form.get-code'));
                        }
                    },
                    function (m) {

                        //提示错误信息
                        yiCommon.showError(yiCommon.extract(m));

                    }, false, $("body")
                );


            } else {
                yiCommon.showError('请填写以下信息', '', $('.login-form.get-code'));
            }
            function getUserInfo() {
                return {
                    userName: $.trim($userName.val()),
                    phone: $.trim($phone.val()),
                    authCode: $.trim($code.val()),
                }
            }
        });

    },
    //获取验证码
    check3: function () {
        ///*验证码的动态获取*/

        $(".authcode").on('click', function () {
            //如果勾选了邀请码注册，则不允许添加获取验证码
            if ($('#ckInviteCode').is(':checked')) {
                return;
            }
            checks.sendValidateCode();
        });
    },

    checkEnterpriseName: function ($enterpriseName) {  /*企业名称*/
        var $enError = $('#enError');
        if ($.trim($enterpriseName.val()) === "") {
            $enterpriseName.addClass('input_error');
            $enError.show().removeClass().html("企业名称不能为空！").addClass("alerts");
            return false;
        } else {
            $enterpriseName.removeClass('input_error');
            $enError.hide();
            return true;
        }
    },
    checkEnterpriseContact: function ($enterpriseContact) {  /*企业联系人*/
        var $ecError = $('#ecError');
        if ($.trim($enterpriseContact.val()) === "") {
            $enterpriseContact.addClass('input_error');
            $ecError.show().removeClass().html("企业联系人不能为空！").addClass("alerts");
            return false;
        } else {
            $enterpriseContact.removeClass('input_error');
            $ecError.hide();
            return true;
        }
    },
    checkLocation: function ($location) {  /*所在地*/
        var $locError = $('#locError');
        if ($.trim($location.val()) === "") {
            $location.addClass('input_error');
            $locError.show().removeClass().html("所在地不能为空！").addClass("alerts");
            return false;
        } else {
            $location.removeClass('input_error');
            $locError.hide();
            return true;
        }
    },
    checkEnterprisescale: function ($enterprisescale) {  /*企业人员规模*/
        var $esError = $('#esError');
        if ($.trim($enterprisescale.val()) === "") {
            $enterprisescale.addClass('input_error');
            $esError.show().removeClass().html("企业人员规模不能为空！").addClass("alerts");
            return false;
        } else {
            $enterprisescale.removeClass('input_error');
            $esError.hide();
            return true;
        }
    },

    checkPwd: function ($pwd) {  /*密码验证*/
        var $pwError = $('#pwError');

        if ($.trim($pwd.val()) === "") {
            $pwd.addClass('input_error');
            $pwError.show().removeClass().html("请输入密码！").addClass("alerts");
            return false;
        } else {
            var reg = /^\S{6,16}$/;
            if (reg.test($.trim($pwd.val())) === false) {
                $pwd.addClass('input_error');
                $pwError.show().removeClass().html("新密码长度必须为 6-16 位！").addClass("alerts");
                return false;
            } else {
                $pwd.removeClass('input_error');
                $pwError.hide();
                return true;
            }
        }
    },

    checkRepwd: function ($repwd) {  /*密码确认验证*/
        var $repwError = $('#repwError');

        if ($.trim($repwd.val()) === "") {
            $repwd.addClass('input_error');
            $repwError.show().removeClass().html("请再次输入密码！").addClass("alerts");
            return false;
        } else {
            var reg = /^\S{6,16}$/;
            if ($.trim($repwd.val()) != $.trim($("#txtPassword").val())) {
                $repwd.addClass('input_error');
                $repwError.show().removeClass().html("两次输入密码不一致，请重新输入！").addClass("alerts");
                return false;
            } else {
                $repwd.removeClass('input_error');
                $repwError.hide();
                return true;
            }
        }

    },

    check4: function () {  /*鼠标离开时判断是否满足验证要求*/
        $("#txtEnterpriseName").on("blur", function () {  //企业名称
            checks.checkEnterpriseName($(this));
        });
        $("#txtEnterpriseContact").on("blur", function () {  //企业联系人
            checks.checkEnterpriseContact($(this));
        });
        $("#txtLocation").on("blur", function () {  //所在地
            checks.checkLocation($(this));
        });
        $("#txtEnterprisescale").on("blur", function () {  //企业人员规模
            checks.checkEnterprisescale($(this));
        });
        $("#txtPassword").on("blur", function () {  //密码
            checks.checkPwd($(this));
        });
        $("#txtRePassword").on("blur", function () {  //密码确认
            checks.checkRepwd($(this));
        });
    },

    check5: function () {   /*登录,注册提交*/
        var b = true;
        var $enterpriseName = $("#txtEnterpriseName");
        var $enterpriseContact = $("#txtEnterpriseContact");
        var $location = $("#txtLocation");
        var $enterprisescale = $("#cmbEnterpiseScale");
        var $pwd = $("#txtPassword");
        var $repwd = $("#txtRePassword");
        var $serviceLine = $("#cmbServiceLine");

        //注册处理
        $(document).on("click", "#register-enterprise", function () {
            var a = [];//用来存放b的数组
            var flag = true;

            $enterpriseName.each(function () {
                if (checks.checkEnterpriseName($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $enterpriseContact.each(function () {
                if (checks.checkEnterpriseContact($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $location.each(function () {
                if (checks.checkLocation($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $enterprisescale.each(function () {
                if (checks.checkEnterprisescale($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $pwd.each(function () {
                if (checks.checkPwd($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            $repwd.each(function () {
                if (checks.checkRepwd($(this)) === false) { b = false; } else {
                    b = true;
                }
                a.push(b)
            });
            for (var i = 0; i < a.length; i++) {
                if (a[i] === false) {
                    flag = false;
                }
            }
            if (flag === true) {

                yiAjax.p('/company/register', getCompanyRegInfo(),
                    function (r) {
                        //提示用户
                        if (r.operationResult.isSuccess == true) {

                            //设置向导背景色
                            checks.setGuideBg(3);

                            $(".alert").hide();
                            $('form.enterprise-message').hide();
                            $('form.register-success').show();
                            $('.enterprise_phone').text(checks.consts.phone);
                            //var i = 60;
                            //$('.time-minus').html(i);
                            //var timmer = setInterval(function () {
                            //    i--;
                            //    $('.time-minus').html(i);
                            //    if (i == 0) {
                            //        clearInterval(timmer);
                            //    }
                            //}, 1000);

                            //后端返回的线路主机头
                            var host = $.trim(r.operationResult.srvData);
                            //自动补全 host 最后的斜杠
                            if (host && host[host.length - 1] !== '/') {
                                host += '/';
                            }
                            var loginUrl = host + 'login.html?userName=' + checks.consts.phone;
                            $('#divTimeGo a').attr('href', loginUrl);
                            setTimeout(function () {
                                $('#divCompanyInitMsg').text('初始化完成。');
                                $('#divTimeGo').show();

                                var $times = $('.time-minus');
                                var times = parseInt($times.text());
                                var intervalId = setInterval(function () {
                                    if (times === 0) {
                                        clearInterval(intervalId);
                                        window.location = loginUrl;
                                        return;
                                    }
                                    times--;
                                    $times.text(times);
                                }, 1000);

                            }, 2000);
                        }
                    },
                    function (m) {

                        //提示错误信息
                        yiCommon.showError(yiCommon.extract(m));

                    }, false, $("body")
                );
            } else {
                yiCommon.showError('请填写以下信息', '', $('.enterprise-message'));
            }
            function getCompanyRegInfo() {
                var location = $("#txtLocation").citypicker('getCode').split('/');
                debugger;
                return {
                    userNumber: $.trim($("#txtPhone").val()),
                    userName: $.trim($enterpriseContact.val()),
                    phoneNumber: $.trim($("#txtPhone").val()),
                    password: $.trim($pwd.val()),

                    userName: $.trim($("#txtPhone").val()),
                    validCode: $.trim($("#txtCode").val()),
                    displayName: $.trim($enterpriseContact.val()),

                    contacterName: $.trim($enterpriseContact.val()),
                    ContacterPhone: $.trim($("#txtPhone").val()),

                    companyName: $.trim($enterpriseName.val()),
                    companyScale: $.trim($enterprisescale.select2('val')),
                    province: $.trim(location[0] || ''),
                    city: $.trim(location[1] || ''),
                    region: $.trim(location[2] || ''),
                    productHostId: $.trim($serviceLine.select2('val')),
                }
            }
        });

    },

    //显示服务条款
    check6: function () {
        $(".clausebtn").on('click', function () {
            yiAjax.gf('/views/ydj/account/clause.html', {}, function (html) {
                yiDialog.d({
                    type: 1,
                    resize: false,
                    content: html,
                    title: '服务条款',
                    area: '60%',
                    btn: ['确定']
                });
            });
        });
    },

    //发送验证码
    sendValidateCode: function () {
        if (!checks.checkPhone($("#txtPhone"))) {
            return;
        }
        var phoneNo = $.trim($("#txtPhone").val());
        yiAjax.g('/sms/code?format=json&mobilePhone={0}'.format(phoneNo), {},
            function (r) {
                if (r && r.operationResult && r.operationResult.isSuccess) {
                    checks.consts.code = r.operationResult.srvData;
                    yiCommon.showError('验证码发送成功！', 'success', $('.login-form.get-code'));
                }
            },
            function (m) {
                yiCommon.showError(yiCommon.extract(m));
            }, false, $("body")
        );
    },

    //根据输入手机号和验证码来觉得是否启用下一步操作按钮
    enableNextBtn: function () {
        var phone = $.trim($("#txtPhone").val());
        var code = $.trim($("#txtCode").val());
        var agree = $("input[name=agreeclause]").attr('checked');
        if (checks.consts.phoneReg.test(phone) && code && agree) {
            $('#next').removeClass('blue-btn-lock');
        } else {
            $('#next').addClass('blue-btn-lock');
        }
    },

    //获取注册服务通道
    getRegisterHost: function (callback) {
        checks.consts.regCompany = null;
        var phone = $.trim($('#txtPhone').val());
        if (!checks.consts.phoneReg.test(phone)) {
            return;
        }
        yiAjax.g('/company/register/list.json?phone=' + phone, null,
            function (r) {
                if (r && r.operationResult && r.operationResult.isSuccess) {
                    var srvData = r.operationResult.srvData;
                    if (srvData) {
                        checks.consts.siteHost = srvData.siteHost;
                        checks.consts.regCompany = srvData.regCompany;
                    }
                }
                callback(phone);
            },
            function (m) {
                yiCommon.showError(yiCommon.extract(m));
            }, false, $("body")
        );
    },

    //生成已注册过的企业列表
    buildRegCompany: function (phone) {
        var $regCompany = $('.registered-company');
        var regCompany = checks.consts.regCompany;
        if (regCompany && regCompany.length > 0) {
            var html = '';
            for (var i = 0; i < regCompany.length; i++) {
                var siteUrl = $.trim(regCompany[i].siteUrl);
                //自动补全 siteUrl 最后的斜杠
                if (siteUrl && siteUrl[siteUrl.length - 1] !== '/') {
                    siteUrl += '/';
                }
                html += '<li><a href="{0}login.html?userName={1}">{2}</a></li>'
                    .format(siteUrl, phone, regCompany[i].companyName);
            }
            $regCompany.find('tt').text(phone);
            $regCompany.find('ul').html(html);
            $regCompany.show();
        } else {
            $regCompany.hide();
        }
    },

    //处理邀请码注册逻辑
    handleInviteCode: function () {

        //手机号事件
        $('#txtPhone').on('change', function () {
            //根据手机号获取已注册的企业列表
            checks.getRegisterHost(checks.buildRegCompany);
        });

        //使用邀请码注册企业复选框事件
        $('#ckInviteCode').on('change', function () {
            var divInviteCode = $('#divInviteCode');
            var divAuthCode = $('.authcode');
            var txtInviteCode = $('#txtInviteCode');
            var txtPhone = $('#txtPhone');
            if ($(this).is(':checked')) {
                divInviteCode.show();
                txtInviteCode.focus();
                txtPhone.prop({ disabled: true, placeholder: '填写邀请码后自动填充手机号' }).css({ 'background-color': '#e5e5e5' });
                divAuthCode.addClass('authcode-lock');
                $('#pError').hide();
            } else {
                divInviteCode.hide();
                txtInviteCode.val('');
                txtPhone.prop({ disabled: false, placeholder: '请填写手机号' }).css({ 'background': 'rgba(0, 0, 0, 0) none repeat scroll 0 0' });
                divAuthCode.removeClass('authcode-lock');
                $('.login-form.get-code .alert').hide();
            }
        });

        //邀请码文本框事件
        $('#txtInviteCode').on('change', function () {
            var txtPhone = $('#txtPhone');
            var txtEnterpriseName = $('#txtEnterpriseName');
            var txtEnterpriseContact = $('#txtEnterpriseContact');
            var errorContainer = $('.login-form.get-code');
            var inviteCode = $.trim($(this).val());
            if (inviteCode && inviteCode.length === 6) {

                //提示进度信息
                yiCommon.showError('正在获取邀请企业信息，请稍等 . . .', 'success', errorContainer);

                //ajax 根据邀请码后端获取邀请企业信息
                yiAjax.g('/company/invite/{0}?format=json'.format(inviteCode), null, function (r) {
                    if (r && r.operationResult && r.operationResult.isSuccess && r.operationResult.srvData) {

                        var srvData = r.operationResult.srvData;
                        txtPhone.val($.trim(srvData.inviteCompanyContactPhone)).blur();
                        txtEnterpriseName.val($.trim(srvData.inviteCompanyName));
                        txtEnterpriseContact.val($.trim(srvData.inviteCompanyContactId));

                        //根据手机号获取已注册的企业列表
                        checks.getRegisterHost(function (phone) {

                            //如果手机号没有注册过企业，则发送短信验证码，否则无需发送
                            if (!checks.consts.regCompany || checks.consts.regCompany.length <= 0) {
                                checks.sendValidateCode();
                            }

                            //显示已注册的企业列表
                            checks.buildRegCompany(phone);
                        });

                    } else {
                        //清空相关字段值
                        yiCommon.showError('邀请码不存在或已过期，请重新输入！', '', errorContainer);
                        txtPhone.val('');
                        txtEnterpriseName.val('');
                        txtEnterpriseContact.val('');
                    }

                }, function (m) {
                    yiCommon.showError(yiCommon.extract(m));
                }, false, $("body"));

            } else {

                //清空相关字段值
                yiCommon.showError('请输入6位数的邀请码！', '', errorContainer);
                txtPhone.val('');
                txtEnterpriseName.val('');
                txtEnterpriseContact.val('');
            }

            $('#pError').hide();
        });
    },

    //设置流程
    setGuideBg: function (step) {
        if (step == 2) {
            $('.process_ul').find('.process2').addClass('active');
            $('.process-tab-box').children('.process-tab').eq(0).addClass('active');
        }
        else {
            $('.process_ul').find('.process3').addClass('active');
            $('.process-tab-box').children('.process-tab').eq(1).addClass('active');
        }
    },

    init: function () {
        this.handleInviteCode();
        this.check1();
        this.check2();
        this.check3();
        this.check4();
        this.check5();
        this.check6();
        //获取系统信息
        yiAjax.p('/authuser/hostconfig', {},
            function (r) {
                if (r && r.operationResult && r.operationResult.srvData) {
                    var srvd = r.operationResult.srvData;
                    if (!srvd) { return; }
                    //产品名称
                    if ($.trim(srvd.productName)) {
                        document.title = srvd.productName + ' - 企业注册';
                    }
                    //版权信息
                    if ($.trim(srvd.serviceProvider)) {
                        $('.copyright').html('Copyright &copy; 2016 - {0} {1}'.format(srvd.endYear, srvd.serviceProvider));
                    }
                    //登录页logo
                    // if ($.trim(srvd.icon)) {
                    //     $('.logo').html('<img src="../../fw/icons/login/'+ srvd.icon +'"/>');
                    // }
                }
            }
        );
        // 动画
        function animate() {
            $(".register-box").animate({ "opacity": "1", "margin-right": "20px" }, 1200);
            $(".ydj-diagram").find(".bottomside").animate({ "opacity": "1", "bottom": "0" }, 1200);
            setTimeout(function () {
                $(".ydj-diagram").find(".connection").animate({ "width": "120px" }, 500);
            }, 1200);
            setTimeout(function () {
                $(".ydj-diagram").find(".subject").animate({ "bottom": "0" }, 500);
                $(".ydj-diagram").find(".diagrammes").animate({ "bottom": "190px", "right": "0", "opacity": "1" }, 500);
            }, 1700);
        }
        animate();
    }
};
Metronic.init();
checks.init();