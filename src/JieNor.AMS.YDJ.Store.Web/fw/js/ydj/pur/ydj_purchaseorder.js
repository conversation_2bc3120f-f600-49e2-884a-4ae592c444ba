
/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/pur/ydj_purchaseorder.js
 */
; (function () {
    var ydj_purchaseorder = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            that.deliveridChangeFlag = 0;
            that.billtypeChangeFlag = 0;
            that.podeptidChangeFlag = 0;
            that.loadflag = true;
            that.combineChangeFlag = true;

            that.PartListData = [];
            that.FirstDelete = true;
            that.attrinfoNew = [];
            that.isParentProduct = false

            _super.call(this, args);

            that.InitEntry = [];

            that.PieceInitEntry = [];

            //单据类型对应的参数设置信息，数据结构对应的表单模型为：ydj_purchaseorder_param.mdl.html
            that.billTypeParamSet = {};
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//商品信息
        _child.prototype.fdentity = 'fdrawentity';//图纸信息
        //送达方ID
        _child.prototype.fdeliverids = [];
        //默认折扣率
        _child.prototype.defdis = 10;
        //部门授权商品ID
        _child.prototype.productids = "";
        //门店对应城市
        _child.prototype.storeCity = "";
        //采购员
        _child.prototype.fpostaffid = "";
        //采购门店
        _child.prototype.fpodeptid = "";
        //根据业绩品牌找到送达方ID
        _child.prototype.fdeliveridsBySerieid = [];
        //是否变更促销数据
        _child.prototype.ischangepromotion = false;

        //初始化事件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            if (!Consts.isSecondOrg) {
                that.Model.setVisible({
                    id: "#tbQueryFirstInventory", value: false
                });

            }
        };
        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fdentity:
                    e.result = { multiselect: false };
                    break;
            }
        };

        //获取变更前的采购数量用于判断采购数量只能减少
        _child.prototype.getAlterEntry = function () {
            var that = this;

            //for (var i = 0; i < rows.length; i++) {
            //    that.InitEntry.push({ id: rows[i].id, fsuitcombnumber: rows[i].fsuitcombnumber, fsofacombnumber: rows[i].fsofacombnumber, fname: rows[i].fmaterialid.fname, fbizqty: rows[i].fbizqty });
            //}

            //如果不是 采购变更单只允许减少的场景则无须初始化的时候查询变更前的数量
            if (!that.isCanchangeQty()) return;

            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            if (!fbillno) return;

            var param = {
                simpleData: {
                    formId: 'ydj_purchaseorder',
                    fbillno: fbillno,
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/ydj_purchaseorder?operationno=getalterentry', param, function (r) {
                ;
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (res.isSuccess) {
                    var data = res.srvData;
                    if (data.length > 0) {
                        var rows = data[0].fentity;
                        that.InitEntry = [];
                        var rowsall = that.Model.getEntryData({ id: that.fentity });
                        for (var i = 0; i < rows.length; i++) {
                            var fname = rowsall.find(o => o.id == rows[i].Id).fmaterialid.fname;
                            that.InitEntry.push({ id: rows[i].Id, fsuitcombnumber: rows[i].fsuitcombnumber, fsofacombnumber: rows[i].fsofacombnumber, fname: fname, fbizqty: rows[i].fbizqty });
                        }
                    }
                    /*that.InitEntry = */
                }
            }, null, null, null, { async: false });
            /*that.InitEntry = that.Model.getEntryData({ id: that.fentity });*/
        }

        //页面初始化后触发的事件
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.fpostaffid = that.Model.getValue({ id: 'fpostaffid' });
            that.fpodeptid = that.Model.getValue({ id: 'fpodeptid' });
            if (that.fpostaffid.id != '') {
                that.podeptidChangeFlag = 1;
            }
            //如果初始化检测到商品明细有数据则获取价格
            if (that.formContext.status == 'push') {
                that.onBillInitProduct('init');
            }

            //获取采购系统参数
            that.Model.invokeFormOperation({
                id: 'loadpursysparam',
                opcode: 'loadpursysparam',
                param: {
                    'formId': 'pur_systemparam',
                    'domainType': 'parameter'
                }
            });

            //加载单据类型参数设置
            that.loadBillTypeParamSet();
            //设置协同信息显示隐藏
            that.setCoorInfoVisible();

            //产品选配设置
            that.Model.selsettings = {
                fentity: "fentity",
                fmaterialid: "fmaterialid",
                fbizqty: "fbizqty",
                fattrinfo: "fattrinfo",
                fsuitcombnumber: "fsuitcombnumber",
                fsuitproductid: "fsuitproductid",
                fprice: "fprice",
                fsuitdescription: "fsuitdescription",
                fsubqty: "fsubqty",
                funstdtypestatus: "funstdtypestatus",
                funstdtype: "funstdtype"
            };

            that.purDatacontrol();

            that.setBillTypeVal();

            //存在非标审核明细，则锁定单据编号
            that.setBillNoEdit();
            //存在明细 总部变更状态 为 提交至总部，锁定保存
            that.setSaveEdit();

            //查询匹配的送达方
            that.GetDeliverBySerieid();

            //设置商品行"是否非标","成交单价"锁定
            that.setUnstdTypeEdit();
            //记录初始的明细数量
            that.getAlterEntry();

            that.procSubmitQHOp();

            //总部手工单锁定规则
            that.zbBillSetRule();

            that.swjDesigner();

            that.renewalOrder();


            that.setDropShipMentVisible();
            that.showOrHideInitiateChangeAapplyBtn();
            that.setQueryFirstInventoryVisible();
            that.setChangeBtnVisible();

            that.setDirectSale();
        };


        //三维家--v6定制柜设置显示隐藏
        _child.prototype.swjDesigner = function () {
            var that = this;
            var fthirdsource = that.Model.getValue({ id: "fthirdsource" });
            var fomsservice = that.Model.getValue({ id: "fomsservice" });
            if (fthirdsource != '三维家' || fomsservice) {
                that.Model.setVisible({ id: '.v6-swj-swjorderstate', value: false });
            }
            else if (fthirdsource == '三维家' || !fomsservice) {
                that.Model.setVisible({ id: '.v6-swj-swjorderstate', value: true });
            }
            else {
                that.Model.setVisible({ id: '.v6-swj-swjorderstate', value: false });
            }

            if (fomsservice) {
                that.Model.setVisible({ id: '#btnPartsListQuery', value: true });   //部件清单查询
                that.Model.setVisible({ id: '#btnquodetails', value: true });   //报价明细
            }
            else {
                that.Model.setVisible({ id: '#btnPartsListQuery', value: false });   //部件清单查询
                that.Model.setVisible({ id: '#btnquodetails', value: false });   //报价明细
            }
        }


        _child.prototype.zbBillSetRule = function () {
            var that = this;
            var fields = that.Model.uiForm.getAllFields();
            var billtype = that.Model.getSimpleValue({ "id": "fbilltypeid" });
            var noLockFields = ["fpostaffid", "fpodeptid", "fresultbrandid"];
            if (Consts.isdirectsale) {
                noLockFields.push('fsupplierid');
            }
            if (fields && billtype == 'ydj_purchaseorder_zb') {
                for (var i = 0; i < fields.length; i++) {
                    if (noLockFields.indexOf(fields[i]) > -1) {
                        continue;
                    }
                    that.Model.setEnable({ id: fields[i], value: false });
                }
                that.Model.uiForm.uiMeta.fdescription.useAnyUnLock = false;
                //var rows = that.Model.getEntryData({ id: that.fentity });
                //if (rows.length == 0) return;
                //for (var i = 0; i < rows.length; i++) {
                //    if (rows[i].fresultbrandid=='') {
                //        that.Model.setEnable({ id:"fresultbrandid", row: rows[i], value: true });
                //    }
                //}
            }
        }

        //处理【提交总部】【提交一级分销】菜单的显示和隐藏
        _child.prototype.procSubmitQHOp = function () {
            var that = this;

            if (Consts.isSecondOrg) {

                //如果是二级分销商用户，则隐藏【提交总部】和【非标审批】按钮
                that.Model.setVisible({ id: '[menu=submithq]', value: false });
                //that.Model.setVisible({ id: '[menu=unstdtypeaudit]', value: false });
            } else {

                //如果不是二级分销商用户，则隐藏【提交一级经销】按钮
                that.Model.setVisible({ id: '[menu=submitagent]', value: false });
            }
        };

        //商品明细行的商品为套件商品时(商品基础资料勾选上选配套件), 该行商品自动锁定"是否非标", 不允许编辑
        _child.prototype.setUnstdTypeEdit = function () {
            var that = this;

            var rows = that.Model.getEntryData({ id: that.fentity });
            if (rows.length == 0) return;

            for (var i = 0; i < rows.length; i++) {
                //是否套件商品
                if (rows[i].fissuitflag) {
                    that.Model.setEnable({ id: 'funstdtype', row: rows[i], value: false });
                }
                //商品=自建商品时，需要能够编辑成交单价
                if (rows[i].fmaterialid.id.trim() != '' && rows[i].fmaterialid.fmainorgid.id == Consts.loginCompany.id) {
                    that.Model.setEnable({ id: 'fdealprice', row: rows[i], value: true });
                }
            }
        };

        //查询匹配的送达方
        _child.prototype.GetDeliverBySerieid = function () {
            var that = this;
            var status = that.Model.getSimpleValue({ id: 'fstatus' });
            if (status == 'E')
                return;

            var rows = that.Model.getEntryData({ id: that.fentity });
            if (rows.length == 0) return;

            var serieid = (rows[0]["fresultbrandid"]).id;
            var deptid = that.Model.getValue({ id: 'fdeptid' }).id;

            if (serieid == '' || deptid == '')
                return;

            that.Model.invokeFormOperation({
                id: 'getdeliverbyserieid',
                opcode: 'getdeliverbyserieid',
                param: {
                    serieid: serieid,
                    deptid: deptid
                }
            });

        }

        //设置保存状态
        _child.prototype.setSaveEdit = function () {
            var that = this;
            var fbilltypeid = that.Model.getValue({ "id": "fbilltypeid" });
            //只要存在一行商品 【总部变更行状态】是已提交至总部
            var has = that.hashqorderchg();
            if (has & !(fbilltypeid && fbilltypeid.id == "ydj_purchaseorder_zb")) {
                that.Model.setEnable({ id: '#tbSave', value: false });
                that.Model.setEnable({ id: '#tbSaveSubmit', value: false });
                that.Model.setEnable({ id: '#tbSaveAudit', value: false });
            }
        };

        //根据明细是否存在非标审核的明细，存在则锁定单据编号不可编辑
        _child.prototype.setBillNoEdit = function () {
            var that = this;
            var rows = that.Model.getEntryData({ id: that.fentity });
            //待审批 02
            if (rows.some(e => e.hasOwnProperty('funstdtypestatus'))) {
                if (rows.some(e => e.funstdtypestatus.id != '' && e.funstdtypestatus.id != '01')) {
                    that.Model.setEnable({ id: 'fbillno', value: false });
                } else {
                    that.Model.setEnable({ id: 'fbillno', value: true });
                }
            }
        };

        //得到门店城市
        _child.prototype.getStoreCity = function () {
            var that = this;
            var stortData = that.Model.getValue({ id: "fstore" });
            if (stortData && stortData.id && stortData.id.length > 0) {
                that.Model.invokeFormOperation({
                    id: 'getpurstorecity',
                    opcode: 'getpurstorecity',
                    param: {
                        id: stortData.id
                    }
                });
            }
        }

        //重新设置单据类型值
        _child.prototype.setBillTypeVal = function () {
            var that = this;
            that.loadflag = true;
            //保存下拉值
            var para = [];
            //用于过滤重复数据
            var paraIndex = [];
            var fsourceid = that.Model.getValue({ id: 'fsourcenumber' })
            //记录选中值
            var oldfid = that.Model.getValue({ id: 'fbilltypeid' }).id;
            if (fsourceid == '') { //非下推的过滤大客户采购
                var data = $("select[name='fbilltypeid']").find("option");
                $.each(data, function (i, item) {
                    if (item.text != '大客户采购订单' && paraIndex.indexOf(item.value) == -1) {
                        para.push({ id: item.value, name: item.text });
                        paraIndex.push(item.value);
                    }
                });
                that.Model.setComboData({ id: 'fbilltypeid', data: para });
                //设置回选中值
                that.Model.setValue({ id: 'fbilltypeid', value: oldfid });
            }
            that.loadflag = false;
        }

        _child.prototype.purDatacontrol = function () {
            var that = this;
            var forderno = that.Model.getValue({ id: 'forderno' });
            if (forderno != '') {
                that.Model.setAttr({ id: '[name=fbilltypeid]', random: 'readonly', value: 'readonly' });
                that.Model.setEnable({ id: 'fdeliverid', value: false });
            }
        }

        //焕新订单--字段按钮显示隐藏，禁用
        _child.prototype.renewalOrder = function () {
            var that = this;

            var frenewalflag = that.Model.getValue({ id: "frenewalflag" });
            var value = false;
            if (frenewalflag) {
                value = true;
            }

            that.Model.setVisible({ id: '.renewal-info', value: value });
            var fpiecesendtag = that.Model.getValue({ id: "fpiecesendtag" });
            if (fpiecesendtag || frenewalflag) {
                that.Model.setVisible({ id: '.dropshipping-info-tag', value: true });
            }

            var renewalrectag = that.Model.getValue({ id: "frenewalrectag" });
            var value1 = false;
            if (renewalrectag) {
                value1 = true;
            }
            that.Model.setVisible({ id: '.renewal-info-ystag', value: value1 });
            that.Model.setVisible({ id: '.renewal-info-ys', value: value1 });
            that.Model.setVisible({ id: 'fsubrecdealamount_gb', value: value1 });
            that.Model.setVisible({ id: 'fsubrecsumamount_gb', value: value1 });
            that.Model.setVisible({ id: 'fsubrenewseq', value: value1 });

            if (frenewalflag && !Consts.isSecondOrg) {
                that.Model.setVisible({ id: '.issubmithq', value: true });
                that.Model.setVisible({ id: '.secondorderno', value: true });
            }
            else {
                that.Model.setVisible({ id: '.issubmithq', value: false });
                that.Model.setVisible({ id: '.secondorderno', value: false });
            }

            var entry = that.Model.getEntryData({ id: 'fentity' });
            for (var i = 0, j = entry.length; i < j; i++) {
                var data = entry[i];
                that.Model.setVisible({ id: 'fsubsidyamount', row: data.id, value: value });
                that.Model.setVisible({ id: 'fretailprice', row: data.id, value: value });
                that.Model.setVisible({ id: 'fretailamount', row: data.id, value: value });
                that.Model.setVisible({ id: 'fretaildealprice', row: data.id, value: value });
                that.Model.setVisible({ id: 'fretaildealamount', row: data.id, value: value });
            }

            that.Model.setVisible({ id: '[menu=renewsubmithq]', value: value });
            that.Model.setVisible({ id: '[menu=submithq]', value: !value });
            that.Model.setVisible({ id: '[menu=change]', value: !value });
            that.Model.setVisible({ id: '[menu=submitchange]', value: !value });
            that.Model.setVisible({ id: '[menu=save]', value: !value });
            that.Model.setVisible({ id: '[menu=submitflow]', value: !value });
            that.Model.setVisible({ id: '[menu=auditflow]', value: !value });
        }

        //设置协同信息显示隐藏
        _child.prototype.setCoorInfoVisible = function () {
            var that = this;
            var bizstatus = $.trim(that.Model.getValue({ id: 'fsupplierstatus' }));
            setTimeout(function () {
                that.Model.setVisible({ id: '.y-pur-related', value: bizstatus == '已协同' ? true : false });
                that.Model.setAttr({ id: '.coo-tools', random: 'class', value: bizstatus == '已协同' ? 'coo-tools collapse' : 'coo-tools expand' });
            }, 10);
        }

        //加载单据类型参数设置
        _child.prototype.loadBillTypeParamSet = function (billTypeId) {
            var that = this;
            var typeChange = true;
            if (billTypeId === undefined) {
                typeChange = false;
                billTypeId = $.trim(that.Model.getSimpleValue({ id: 'fbilltypeid' }));
            }
            if (!billTypeId) return;
            that.Model.invokeFormOperation({
                id: 'getparamset',
                opcode: 'getparamset',
                opctx: { typeChange: typeChange },
                param: {
                    domainType: Consts.domainType.bill,
                    formId: 'bd_billtype',
                    billTypeId: billTypeId
                }
            });
        };

        //表格单元格格式化事件
        _child.prototype.onFieldValueFormat = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fdistrate':
                    //对折扣字段值进行换算显示，比如：9折显示为0.9
                    if (e.value) {
                        e.value = yiMath.toDecimal(e.value / 10, 2);
                        e.cancel = true;
                    }
                    break;
            }
        };

        //初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param, params) {
            var that = this;
            var productData = [];
            if (param == 'init' && that.formContext.status == 'push') {//初始化
                var reGridData = that.Model.getValue({ id: that.fentity });
                for (var i = 0, l = reGridData.length; i < l; i++) {//零售价，经销价，采购价其一小于等于零的时候，请求
                    var rowData = reGridData[i];

                    // 如果是套件商品，则不取价
                    if (rowData.fissuitflag) return;

                    var reAttrinofEntry = [];//按照接口，重新组合
                    var tempAttr = (rowData && rowData.fattrinfo && rowData.fattrinfo.fentity) ? rowData.fattrinfo.fentity : [];
                    for (var n = 0, m = tempAttr.length; n < m; n++) {
                        reAttrinofEntry.push({
                            valueId: tempAttr[n].fvalueid,
                            auxPropId: tempAttr[n].fauxpropid.id
                        })
                    }
                    if (yiMath.toNumber(rowData.fprice) <= 0 || yiMath.toNumber(rowData.fcostprice) <= 0 || yiMath.toNumber(rowData.factualcostprice) <= 0) {

                        //采购单位
                        var purUnitId = $.trim(rowData.fbizunitid && rowData.fbizunitid.id);

                        productData.push({
                            clientId: rowData.id,
                            productId: rowData.fmaterialid.id,
                            unitId: purUnitId,
                            bizDate: that.Model.getValue({ id: 'fdate' }),
                            supplierId: that.Model.getSimpleValue({ id: 'fsupplierid' }),
                            attrInfo: {
                                id: '',
                                entities: reAttrinofEntry
                            }
                        })
                    }
                }

                if (productData.length == 0) {//初始化的时候，商品都有对应的值，则不查询。
                    return;
                }

            } else if (param == 'change') {//改变某一行
                var rowData = that.Model.getEntryRowData({ id: that.fentity, row: params.attrinfo.row });
                if (!rowData) return;
                if (JSON.stringify(rowData) == '{}') {
                    return;
                }

                // 如果是套件商品，则不取价
                if (rowData.fissuitflag) return;

                ;
                //如果商品没值，就不用取价，直接设置为0
                if (params.attrinfo.id == 'fmaterialid' && params.attrinfo.value && !$.trim(params.attrinfo.value.id)) {
                    that.Model.setValue({ id: 'fprice', row: rowData.id, value: 0 });
                    that.Model.setValue({ id: 'fsalprice', row: rowData.id, value: 0 });
                    return;
                }
                //如果商品没值，所携带的辅助属性肯定也为空，直接设置为0
                if (params.attrinfo.id == 'fattrinfo' && rowData.fmaterialid && !$.trim(rowData.fmaterialid.id)) {
                    that.Model.setValue({ id: 'fprice', row: rowData.id, value: 0 });
                    that.Model.setValue({ id: 'fsalprice', row: rowData.id, value: 0 });
                    return;
                }


                var reAttrinofEntry = [];//按照接口，重新组合
                var tempAttr = [];
                if (rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length > 0) {
                    tempAttr = rowData.fattrinfo.fentity;
                }

                for (var n = 0, m = tempAttr.length; n < m; n++) {
                    reAttrinofEntry.push({
                        valueId: tempAttr[n].fvalueid,
                        auxPropId: tempAttr[n].fauxpropid.id
                    })
                }

                //采购单位
                var purUnitId = $.trim(rowData.fbizunitid && rowData.fbizunitid.id);

                productData.push({
                    clientId: rowData.id,
                    productId: rowData.fmaterialid.id,
                    unitId: purUnitId,
                    bizDate: that.Model.getValue({ id: 'fdate' }),
                    supplierId: that.Model.getSimpleValue({ id: 'fsupplierid' }),
                    length: rowData.flength,
                    width: rowData.fwidth,
                    thick: rowData.fthick,
                    attrInfo: {
                        id: '',
                        entities: reAttrinofEntry
                    }
                });


            } else if (param == 'changeall') {//改变客户的时候，取价
                var reGridData = that.Model.getValue({ id: that.fentity });
                for (var i = 0, l = reGridData.length; i < l; i++) {
                    var rowData = reGridData[i];

                    if (!$.trim(rowData.fmaterialid.id)) {
                        continue;
                    }
                    var reAttrinofEntry = [];//按照接口，重新组合
                    var tempAttr = [];
                    if (rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length > 0) {
                        tempAttr = rowData.fattrinfo.fentity;
                    }

                    for (var n = 0, m = tempAttr.length; n < m; n++) {
                        reAttrinofEntry.push({
                            valueId: tempAttr[n].fvalueid,
                            auxPropId: tempAttr[n].fauxpropid.id
                        })
                    }

                    //采购单位
                    var purUnitId = $.trim(rowData.fbizunitid && rowData.fbizunitid.id);

                    productData.push({
                        clientId: rowData.id,
                        productId: rowData.fmaterialid.id,
                        unitId: purUnitId,
                        bizDate: that.Model.getValue({ id: 'fdate' }),
                        supplierId: that.Model.getSimpleValue({ id: 'fsupplierid' }),
                        length: rowData.flength,
                        width: rowData.fwidth,
                        thick: rowData.fthick,
                        attrInfo: {
                            id: '',
                            entities: reAttrinofEntry
                        }
                    });
                }
            }
            if (productData.length === 0) return;

            var option = { pull: params && params.pull };

            //性能优化：如果明细行大于100，则做分批请求，后端取价接口在超过 225 行明细时会挂掉
            var pageSize = 100;
            if (productData.length > pageSize) {
                var batchs = Math.ceil(productData.length / pageSize);
                for (var i = 1; i <= batchs; i++) {
                    var pageData = yiCommon.pagingData({ data: productData, pageSize: pageSize, pageIndex: i });
                    that.invokeGetPrice(param, pageData, option);
                }
            } else {
                that.invokeGetPrice(param, productData, option);
            }
        }

        //调用取价接口
        _child.prototype.invokeGetPrice = function (opId, productInfos, option) {
            var that = this;
            this.Model.invokeFormOperation({
                id: opId,
                opcode: 'getprices',
                opctx: { option: option },
                param: {
                    formId: 'ydj_price',
                    domainType: 'dynamic',
                    priceFlag: 4,
                    productInfos: JSON.stringify(productInfos)
                }
            });

            // 取总部零售价
            for (var i = 0; i < productInfos.length; i++) {
                productInfos[i].isHqPrice = true;
            }
            var productInfoJsonHq = JSON.stringify(productInfos);
            that.Model.invokeFormOperation({
                id: opId,
                opcode: 'getprices',
                async: true, //采用异步请求，因耗时过长，后续优化
                opctx: { param: opId, productInfos: productInfoJsonHq, isHqPrice: true },
                param: {
                    productInfos: productInfoJsonHq,
                    formId: 'ydj_price',
                    domainType: 'dynamic'
                }
            });
        };

        //报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {
            var that = this;
            var flag = true;
            that.alertModel = e;
            var productData = [];
            var reAttrinofEntry = [];//按照接口，重新组合 
            var fentry = that.alertModel.Model.uiData.fentity;
            for (var n = 0, m = fentry.length; n < m; n++) {
                var lm = fentry[n];
                if (lm.fisselect) {//被选中的辅助属性行

                    if (!lm.fvalueid) {//辅助属性行需要填满信息才能查询
                        flag = false;
                    }
                    reAttrinofEntry.push({
                        valueId: lm.fvalueid,
                        auxPropId: lm.fauxpropid.id
                    })
                }

            }

            productData.push({
                clientId: '',
                productId: that.alertModel.Model.uiData.fmaterialid.id,
                bizDate: that.Model.uiData.fdate,//订单日期
                supplierId: that.Model.uiData.fsupplierid.id,
                length: that.alertModel.Model.uiData.flength,
                width: that.alertModel.Model.uiData.fwidth,
                thick: that.alertModel.Model.uiData.fthick,
                attrInfo: {
                    id: '',
                    entities: reAttrinofEntry
                }
            });

            productData = JSON.stringify(productData);
            if (flag) {
                that.Model.invokeFormOperation({
                    id: 'onPriceSerch',

                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productData,
                        priceFlag: 4,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            } else {
                yiDialog.mt({ msg: '辅助属性信息不全，无法查询价格。', skinseq: 2 });
            }


        };

        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            if (!e || !e.formId) return;
            var that = this;
            ;
            switch (e.formId) {
                case 'ydj_product':
                    //填充数据。
                    var data = e.data;
                    if (!data || data.length == 0) {
                        return;
                    }
                    var auxEntry = [];
                    var addRowData = {};
                    var partdata;
                    if (that.PartListData.length > 0) {
                        partdata = that.PartListData.find(o => o["fmaterialid"] == data[0].fbillhead_id);
                    }
                    var row = that.Model.addRow({ id: that.fentity, row: partdata.id, opctx: { ignore: true } });
                    var parentrow = that.Model.getEntryRowData({ id: that.fentity, row: partdata.id });
                    if (parentrow) {
                        var fresultbrandid = parentrow.fresultbrandid;
                        if (partdata) {
                            addRowData = {
                                'fmaterialid': data[0].fbillhead_id,
                                'fqty': partdata.fqty,
                                'fbizqty': partdata.fqty,
                                'funitid': partdata.funitid,
                                'fbizunitid': partdata.funitid
                            };
                            that.Model.setValue({ id: 'fproductid', row: row, value: data[0].fbillhead_id, opctx: { ignore: true } });
                            //that.Model.setValue({ id: 'fqty', row: row, value: partdata.fqty });
                            //that.Model.setValue({ id: 'fbizqty', row: row, value: partdata.fqty });
                            that.Model.setValue({ id: 'fpartqty', row: row, value: partdata.fqty });
                            //如果是铁架床则算是自动带出配件。
                            if (partdata.parttype == "铁架床") {
                                that.Model.setValue({ id: 'fisautopartflag', row: row, value: 1 });
                            }

                            //that.Model.setValue({ id: 'funitid', row: row, value: partdata.funitid });
                            //that.Model.setValue({ id: 'fbizunitid', row: row, value: partdata.funitid });
                            if (partdata.attrinfo) {
                                for (var a = 0; a < partdata.attrinfo.length; a++) {
                                    auxEntry.push({
                                        fauxpropid: {
                                            id: partdata.attrinfo[a].fauxpropid,
                                            fname: partdata.attrinfo[a].fname,
                                            fnumber: partdata.attrinfo[a].fnumber
                                        },
                                        fvalueid: partdata.attrinfo[a].fvalueid,
                                        fvaluename: partdata.attrinfo[a].fvaluename,
                                        fvaluenumber: partdata.attrinfo[a].fvaluenumber
                                    });
                                }
                            }
                        }
                        else {
                            addRowData = {
                                'fmaterialid': data[0].fbillhead_id,
                                'fqty': 1,
                                'fbizqty': 1,
                                'funitid': partdata.funitid,
                                'fbizunitid': partdata.funitid
                            };
                            that.Model.setValue({ id: 'fproductid', row: row, value: data[0].fbillhead_id, opctx: { ignore: true } });
                            //that.Model.setValue({ id: 'fqty', row: row, value: 1 });
                            //that.Model.setValue({ id: 'fbizqty', row: row, value: 1 });
                            that.Model.setValue({ id: 'fpartqty', row: row, value: 1 });
                            //that.Model.setValue({ id: 'fqty', row: row, value: partdata.funitid });
                            //that.Model.setValue({ id: 'fbizqty', row: row, value: partdata.funitid });
                        }

                        //var row = that.Model.addRow({ id: that.fentity, data: addRowData });
                        //// 触发获取商品其他信息
                        that.Model.setValue({
                            id: 'fmaterialid', row: row, value: {
                                id: data[0].fbillhead_id,
                                fnumber: data[0].fnumber,
                                fname: data[0].fname,
                                fbizunitid: partdata["funitid"]
                            }, opctx: { ignore: true }
                        });
                        var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                        //对业务单据辅助属性字段设置
                        that.Model.setValue(auxPropArgs);
                        //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                        var Newid = yiCommon.uuid(32);
                        //如果父商品 已经有了组合号就不必再生成组合号
                        if (parentrow.fpartscombnumber) {
                            Newid = parentrow.fpartscombnumber;
                        } else {
                            Newid = yiCommon.uuid(32);
                            that.Model.setValue({ id: 'fpartscombnumber', row: partdata.id, value: Newid });
                            //给主商品打上主商品标识 区分
                            that.Model.setValue({ id: 'fiscombmain', row: partdata.id, value: 1 });
                        }
                        that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                        that.Model.setValue({ id: 'fparttype', row: row, value: partdata.parttype });
                        that.Model.setValue({ id: 'fresultbrandid', row: row, value: fresultbrandid });
                        //根据主商品的数量计算配件数量
                        that.calculatePartQty(parentrow.fbizqty, parentrow.id);
                        //触发业务插件中的辅助属性字段值变化事件
                        //that.plugInProxy.invoke(eventConst.onFlexFieldValueChanged, auxPropArgs);
                    }
                    break;
            }
        };

        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    if (!(e.opctx && e.opctx.ignore)) {
                        var sourceEntryId = $.trim(that.Model.getValue({ id: 'fsourceentryid_e', row: e.row }));
                        if (sourceEntryId) {
                            e.result = true;
                            yiDialog.warn('下推的商品明细不允许修改辅助属性！');
                            return;
                        }
                    }
                    that.onBillInitProduct('change', { attrinfo: e });
                    that.aftersetauxProp(e);
                    break;
            }
        };
        //标准定制、非标定制后根据属性、属性值 匹配带出配件
        _child.prototype.aftersetauxProp = function (e) {
            var attrinfo = e.value;
            var that = this;
            if (!attrinfo || !attrinfo.fentity) return;
            var attrinfoNo = attrinfo.fentity.filter(o => o.fvaluename == "无");
            that.attrinfoNew = [];
            if (attrinfoNo) {
                attrinfoNo.forEach(o => { that.attrinfoNew.push(o.fauxpropid.fname) });
            }
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            if (!rowData) return;
            var productId = $.trim(rowData.fmaterialid && rowData.fmaterialid.id);
            if (attrinfo.fentity && attrinfo.fentity.length > 0) {
                var isCheckCustom = false;
                var ds = that.Model.getSelectRows({ id: that.fentity });
                if (ds.length > 0) {
                    var rowid = ds[0].data.id;
                    isCheckCustom = rowid == rowData.id
                }
                if (isCheckCustom || rowData.fsuitcombnumber) isCheckCustom = true;

                if (isCheckCustom) {
                    that.Model.invokeFormOperation({
                        id: 'doaddparts_custom',
                        opcode: 'doaddparts_custom',
                        param: {
                            formId: 'ydj_purchaseorder',
                            rows: JSON.stringify(attrinfo),
                            currentrow: JSON.stringify(rowData),
                            productId: productId,
                            fdeliverid: that.Model.getSimpleValue({ id: 'fdeliverid' }),
                            rowId: e.row,
                            domainType: 'dynamic'
                        }
                    });
                }
            }
        };

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    that.deletingProductRow(e);
                    break;
            }
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;

            switch (e.id.toLowerCase()) {
                case that.fentity:
                    var fpartscombnumber = e.delRow.fpartscombnumber,
                        fsuitcombnumber = e.delRow.fsuitcombnumber,
                        fsofacombnumber = e.delRow.fsofacombnumber,
                        fcombinenumber = e.delRow.fcombinenumber,
                        fiscombmain = e.delRow.fiscombmain,
                        fisautopartflag = e.delRow.fisautopartflag,
                        rowid = e.delRow.id
                    delRow = [];

                    var entryData = that.Model.getEntryData({ id: that.fentity });
                    if (fsofacombnumber) {
                        //点击”确定”时把所有相同【套件组合号】的商品行删除
                        for (var i = 0, j = entryData.length; i < j; i++) {
                            if (e.opctx && e.opctx.promotion) {
                                //如果删除的商品是套件，则删除相关的套件商品
                                if (fsofacombnumber == entryData[i].fsofacombnumber && entryData[i].fcombinenumber == "") {
                                    //如果删除所有关联的沙发组合商品,包括当前商品行
                                    delRow.push(entryData[i].id);
                                }
                            } else {
                                //如果删除的商品是套件，则删除相关的套件商品
                                if (fsofacombnumber == entryData[i].fsofacombnumber) {
                                    //如果删除所有关联的沙发组合商品,包括当前商品行
                                    delRow.push(entryData[i].id);
                                }
                            }
                        }
                        //避免删除后0条分录的操作
                        if (delRow.length == entryData.length) {
                            that.Model.addRow({ id: that.fentity });
                        }
                        for (var i = 0; i < delRow.length; i++) {
                            var row = delRow[i];
                            that.FirstDelete = false;
                            that.Model.deleteRow({ id: that.fentity, row: row, opctx: { ignore: true } });
                        }
                    }

                    if (fcombinenumber) {
                        var checkdeleteProduct = $('input[name=deleteproduct]:checked').val();

                        for (var i = 0, j = entryData.length; i < j; i++) {
                            if (fcombinenumber == entryData[i].fcombinenumber) {
                                if (checkdeleteProduct == "删除商品") {
                                    that.Model.setValue({ id: "fpromotion", row: entryData[i].id, value: "" }); //促销活动名称
                                    that.Model.setValue({ id: "fcombinenumber", row: entryData[i].id, value: "" }); //组合促销编号
                                    that.Model.setValue({ id: 'fcombineremark', row: entryData[i].id, value: "" });//组合描述
                                    that.Model.setValue({ id: 'fcombinepriority', row: entryData[i].id, value: "" });//促销活动优先级
                                    that.Model.setValue({ id: 'fcombinerate', row: entryData[i].id, value: "" });//折扣率
                                    that.Model.setValue({ id: "fcombineqty", row: entryData[i].id, value: "" });//套餐组合基数
                                }
                                else {
                                    delRow.push(entryData[i].id);
                                }
                            }
                        }
                        for (var i = 0; i < delRow.length; i++) {
                            var row = delRow[i];
                            that.FirstDelete = false;
                            that.Model.deleteRow({ id: that.fentity, row: row, opctx: { ignore: true } });
                        }
                    }

                    //处理删除关联组合号的操作
                    if (fpartscombnumber) {
                        //点击”确定”时把所有相同【套件组合号】的商品行删除
                        if (fiscombmain) {
                            for (var i = 0, j = entryData.length; i < j; i++) {
                                if (e.opctx && e.opctx.promotion) {
                                    //如果删除的商品是套件，则删除相关的套件商品
                                    if (fpartscombnumber == entryData[i].fpartscombnumber && entryData[i].fcombinenumber == "") {
                                        delRow.push(entryData[i].id);
                                    }
                                } else {
                                    //如果删除的商品是套件，则删除相关的套件商品
                                    if (fpartscombnumber == entryData[i].fpartscombnumber && e.delRow.fcombinenumber == entryData[i].fcombinenumber) {
                                        delRow.push(entryData[i].id);
                                    }
                                }
                            }
                            for (var i = 0; i < delRow.length; i++) {
                                var row = delRow[i];
                                that.FirstDelete = false;
                                that.Model.deleteRow({ id: that.fentity, row: row, opctx: { ignore: true } });
                            }
                        }
                        //1) 如果主商品只有一个配件时, 删除该配件会清空主商品的【配件组合号】
                        //2) 如果主商品对应关联多个配件时, 删除其中一个配件会不会清空主商品的【配件组合号】
                        else {
                            var rows = entryData.filter(e => e.fpartscombnumber == fpartscombnumber && e.id != rowid && !e.fiscombmain);
                            var rowall = entryData.filter(e => e.fpartscombnumber == fpartscombnumber);
                            if (rows.length == 0) {
                                //清空配件组合号
                                for (var i = 0; i < rowall.length; i++) {
                                    that.Model.setValue({ id: 'fpartscombnumber', row: rowall[i].id, value: '' });
                                }
                            }
                        }
                    }

                    //处理删除套件组合号的操作
                    if (fsuitcombnumber) {
                        //点击”确定”时把所有相同【套件组合号】的商品行删除
                        for (var i = 0, j = entryData.length; i < j; i++) {
                            if (e.opctx && e.opctx.promotion) {
                                //如果删除的商品是套件，则删除相关的套件商品
                                if (fsuitcombnumber == entryData[i].fsuitcombnumber && entryData[i].fcombinenumber == "") {
                                    delRow.push(entryData[i].id);
                                }
                            } else {
                                //如果删除的商品是套件，则删除相关的套件商品
                                if (fsuitcombnumber == entryData[i].fsuitcombnumber) {
                                    delRow.push(entryData[i].id);
                                }
                            }
                        }
                        for (var i = 0; i < delRow.length; i++) {
                            var row = delRow[i];
                            that.FirstDelete = false;
                            that.Model.deleteRow({ id: that.fentity, row: row, opctx: { ignore: true } });
                        }
                    }

                    //避免删除后0条分录的操作
                    if (delRow.length == entryData.length) {
                        that.Model.addRow({ id: that.fentity });
                    }
                    //重新计算表头字段值
                    that.calculateSettlement();
                    break;
            }
        };

        //选单后执行的操作
        _child.prototype.onAfterPull = function (e) {
            var that = this;
            var entrys = e.uidata.fentity;
            if (entrys && entrys.length > 0) {
                setTimeout(function () {
                    for (var i = 0, j = entrys.length; i < j; i++) {
                        var row = entrys[i].id;
                        var price = entrys[i].fprice;
                        var salPrice = entrys[i].fsalprice;
                        //采购单价或销售单价为0时，才取价
                        if (price < 1 || salPrice < 1) {
                            that.onBillInitProduct('change', { attrinfo: { row: row }, pull: true });
                        }
                    }
                }, 100);
            }
        };

        //表格选中行变化时触发
        _child.prototype.onSelectedRowsChanged = function () {
            var that = this;
            that.onEntryRowClick();
        };

        //选中行元素控制
        _child.prototype.NonStandardElControl = function (e) {
            var that = this;
            var rows = that.Model.getSelectRows({ id: that.fentity });
            if (rows.length == 0) return;
            var isLockunstdtype = true;
            for (var i = 0; i < rows.length; i++) {
                var data = rows[i].data;
                //非标审批控制元素
                if (data.funstdtypestatus == null) continue;
                var status = data.funstdtypestatus.fname;
                if (status == "待审批" ||
                    status == "审批通过" ||
                    status == "终审" ||
                    status == "驳回") {
                    isLockunstdtype = false;
                    that.Model.setEnable({ id: 'fattrinfo', row: data.id, value: false });
                    that.Model.setEnable({ id: 'fmaterialid', row: data.id, value: false });
                } else {
                    that.Model.setEnable({ id: 'fattrinfo', row: data.id, value: true });
                    that.Model.setEnable({ id: 'fmaterialid', row: data.id, value: true });
                }
            }
            that.Model.setEnable({ id: '#btnUnStandardCustom', value: isLockunstdtype });
            that.Model.setEnable({ id: '#btnUnbindContract', value: isLockunstdtype });
        }

        //选中行事件
        _child.prototype.onEntryRowClick = function (e) {
            var that = this;
            var rows = that.Model.getSelectRows({ id: that.fentity });
            var fbilltypeid = that.Model.getValue({ "id": "fbilltypeid" });
            var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
            var status = that.Model.getSimpleValue({ id: 'fstatus' });
            var leng = rows.length;//选中了多少项。
            if (leng > 0) {
                if ((status != 'D' && status != 'E') || fchangestatus == '1') {
                    if (that.isCanchangeQty() || (fbilltypeid && fbilltypeid.id == "ydj_purchaseorder_zb")) return;

                    //如果存在 有一行有"销售合同编号"也是要执行反灰锁定
                    if (rows.some(e => e.data.fsoorderno != '')) {
                        that.Model.setEnable({ id: '#btnStandardCustom', value: false });
                        that.Model.setEnable({ id: '#btnUnStandardCustom', value: false });
                        that.Model.setEnable({ id: '#btnSuitCustom', value: false });
                        that.Model.setEnable({ id: '#btnQYG', value: false });
                        that.Model.setEnable({ id: '#btnZCG', value: false });
                        that.Model.setEnable({ id: '#btnCXDB', value: false });
                        that.Model.setEnable({ id: '#btnbindcombnumber', value: false });
                        that.Model.setEnable({ id: '#btnunbindcombnumber', value: false });
                        that.Model.setEnable({ id: '#btnUnbindContract', value: false });
                    }
                    else {
                        that.Model.setEnable({ id: '#btnStandardCustom', value: true });
                        that.Model.setEnable({ id: '#btnUnStandardCustom', value: true });
                        that.Model.setEnable({ id: '#btnSuitCustom', value: true });
                        that.Model.setEnable({ id: '#btnQYG', value: true });
                        that.Model.setEnable({ id: '#btnZCG', value: true });
                        that.Model.setEnable({ id: '#btnCXDB', value: true });
                        that.Model.setEnable({ id: '#btnbindcombnumber', value: true });
                        that.Model.setEnable({ id: '#btnunbindcombnumber', value: true });
                        that.Model.setEnable({ id: '#btnUnbindContract', value: true });

                    }
                    that.NonStandardElControl();
                }

                //套件头不允许非标审批
                if (status != 'D' && status != 'E') {
                    if (rows.some(e => e.data.hasOwnProperty('funstdtypestatus'))) {
                        if (rows.some(e => e.data.fsuitcombnumber != '' && e.data.fissuitflag)) {
                            that.Model.setEnable({ id: '#tbUnstdTypeAudit', value: false });
                        } else {
                            that.Model.setEnable({ id: '#tbUnstdTypeAudit', value: true });
                        }
                    }
                }
            }
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            if (!e.row) return;
            switch (e.id.toLowerCase()) {
                //case 'funstdtype':
                //    var oriValue = that.Model.getSimpleValue({ id: "funstdtype", row: e.row });
                //    if (oriValue) {
                //        e.value = true;
                //        e.result = true;
                //    }
                //    break;
                case 'fdeliverid': //送达方发生变动
                    var fdeliverid = that.Model.getValue({ id: 'fdeliverid' });
                    //校验采购订单是否有收支记录
                    var fsupplierid = that.Model.getValue({ id: 'fsupplierid' });
                    var parm = {
                        simpledata: {
                            billno: that.Model.getValue({ id: 'fbillno' }),
                            supplierid: fsupplierid?.id,
                            deliverid: e.value?.id
                        }
                    };
                    yiAjax.p('/dynamic/ydj_purchaseorder?operationno=canchangedeliverid', parm, function (r) {
                        if (r) {
                            if (!r.operationResult.srvData) {
                                yiDialog.warn('当前订单已生成下游收支记录，不允许更改送达方；如需更改，请先与财务确认删除所有收支记录，再更改送达方。');
                                e.value = fdeliverid;
                                e.result = true;
                            }
                        }
                    }, null, null, null, { async: false });

                    if (that.deliveridChangeFlag == 0) {//没办法知道是页面加载时的值更新还是用户交互导致的值更新，用标记位来判断，避免新增或复制或下推表单时就出现提示
                        that.deliveridChangeFlag = 1;
                        break;
                    }

                    if (fdeliverid && e.value && fdeliverid.id == e.value.id) {
                        break;
                    }

                    that.onClearProductEntry(e);
                    break;
                case 'fbilltypeid'://单据类型发生变动   
                    if (that.loadflag)//首次加载不调用
                        return;
                    //如果是单据类型重载不需要提示
                    if (e.isreload) return;
                    that.onClearProductEntry(e);
                    break;
                case 'fpostaffid':
                    that.fpostaffid = that.Model.getSimpleValue({ id: 'fpostaffid' })
                    break;
                case 'fpodeptid'://采购部门发生变动  
                    if (that.podeptidChangeFlag == 0) {//没办法知道是页面加载时的值更新还是用户交互导致的值更新，用标记位来判断，避免新增或复制或下推表单时就出现提示
                        that.podeptidChangeFlag = 1;
                        break;
                    }
                    var fbilltypeid = that.Model.getValue({ "id": "fbilltypeid" })
                    if (fbilltypeid && fbilltypeid.fname == "摆场订单") {
                        that.onClearProductEntry(e);
                    }
                    break;
                case 'fbizqty':
                    if (e.opctx && e.opctx.ignore) return;

                    if (!e.tgChange) return;
                    //var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
                    var product = that.InitEntry.find(o => o.id == e.row);
                    /*if (!product || (product.fsuitcombnumber && product.fsuitcombnumber != '')) return;*/
                    //如果 采购变更时控制采购数量只能减少
                    if (product && that.isCanchangeQty()) {
                        var oldqty = product.fbizqty;
                        if (product.fsuitcombnumber.trim() != '' || product.fsofacombnumber.trim() != '') {
                            if (oldqty != e.value && e.value != 0) {
                                if (product.fsofacombnumber.trim() != '') {
                                    //找出之前当前沙发号的所有商品，数量一起回滚
                                    var sofaPorducts = that.InitEntry.filter(o => o.fsofacombnumber == product.fsofacombnumber);
                                    if (sofaPorducts.length > 1) {
                                        for (var i = 0; i < sofaPorducts.length; i++) {
                                            that.Model.setValue({ id: "fbizqty", row: sofaPorducts[i].id, value: sofaPorducts[i].fbizqty, opctx: { ignore: true } });
                                        }
                                    }
                                }
                                e.result = true;
                                yiDialog.mt({ msg: "商品数量只允许修改为0!", skinseq: 2 });
                                e.value = oldqty;
                            }
                            //如果是从0 再切换回原数量则 其他沙发也一起还原回原沙发数量
                            else if (oldqty == e.value && e.value != 0) {
                                if (product.fsofacombnumber.trim() != '') {
                                    //找出之前当前沙发号的所有商品，数量一起回滚
                                    var sofaPorducts = that.InitEntry.filter(o => o.fsofacombnumber == product.fsofacombnumber);
                                    if (sofaPorducts.length > 1) {
                                        for (var i = 0; i < sofaPorducts.length; i++) {
                                            that.Model.setValue({ id: "fbizqty", row: sofaPorducts[i].id, value: sofaPorducts[i].fbizqty, opctx: { ignore: true } });
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            //如果是增加了数量 则要提示
                            if (oldqty < e.value) {
                                e.result = true;
                                yiDialog.mt({ msg: "商品" + product.fname + "修改后数量 " + e.value + "大于原采购数量" + oldqty + ", 采购变更时只允许减少数量, 不允许比原采购数量多!", skinseq: 2 });
                                e.value = oldqty;
                                //return;
                            }
                        }
                    }

                    var fpiecesendtag = that.Model.getValue({ id: "fpiecesendtag" });
                    var pieceproduct = that.PieceInitEntry.find(o => o.id == e.row);
                    if (pieceproduct && fpiecesendtag) {
                        var oldqty = pieceproduct.fbizqty;
                        if (pieceproduct.fsuitcombnumber.trim() != '' || pieceproduct.fsofacombnumber.trim() != '') {
                            if (oldqty != e.value && e.value != 0) {
                                if (pieceproduct.fsofacombnumber.trim() != '') {
                                    //找出之前当前沙发号的所有商品，数量一起回滚
                                    var sofaPorducts = that.PieceInitEntry.filter(o => o.fsofacombnumber == pieceproduct.fsofacombnumber);
                                    if (sofaPorducts.length > 1) {
                                        for (var i = 0; i < sofaPorducts.length; i++) {
                                            that.Model.setValue({ id: "fbizqty", row: sofaPorducts[i].id, value: sofaPorducts[i].fbizqty, opctx: { ignore: true } });
                                        }
                                    }
                                }
                                e.result = true;
                                yiDialog.mt({ msg: "商品数量只允许修改为0!", skinseq: 2 });
                                e.value = oldqty;
                            }
                            //如果是从0 再切换回原数量则 其他沙发也一起还原回原沙发数量
                            else if (oldqty == e.value && e.value != 0) {
                                if (pieceproduct.fsofacombnumber.trim() != '') {
                                    //找出之前当前沙发号的所有商品，数量一起回滚
                                    var sofaPorducts = that.PieceInitEntry.filter(o => o.fsofacombnumber == pieceproduct.fsofacombnumber);
                                    if (sofaPorducts.length > 1) {
                                        for (var i = 0; i < sofaPorducts.length; i++) {
                                            that.Model.setValue({ id: "fbizqty", row: sofaPorducts[i].id, value: sofaPorducts[i].fbizqty, opctx: { ignore: true } });
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            //如果是增加了数量 则要提示
                            if (oldqty < e.value) {
                                e.result = true;
                                yiDialog.mt({ msg: "商品" + pieceproduct.fname + "修改后数量 " + e.value + "大于原采购数量" + oldqty + ", 一件代发采购时只允许减少数量, 不允许比原采购数量多!", skinseq: 2 });
                                e.value = oldqty;
                                //return;
                            }
                        }
                    }
                    //product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
                    //var fcombinenumber = product.fcombinenumber;
                    //if (!fcombinenumber || fcombinenumber == '') return;
                    //var oldqty = that.Model.getValue({ id: 'fbizqty', row: e.row });
                    //var fbaseqty = that.Model.getValue({ id: 'fcombineqty', row: e.row });
                    //var newqty = e.value;
                    //if (newqty % fbaseqty != 0) {
                    //    e.result = true;
                    //    yiDialog.mt({ msg: "添加的数量必须为套餐组合基数的倍数, 请重新添加数量 !", skinseq: 2 });
                    //    e.value = oldqty;
                    //}
                    var oldfbizqty = that.Model.getValue({ id: 'fbizqty', row: e.row })
                    var fiswaterredlinefail = that.Model.getValue({ id: "fiswaterredlinefail" });
                    if (fiswaterredlinefail && e.value > oldfbizqty) {
                        e.result = true;
                        yiDialog.mt({ msg: '当前采购订单已经因库存水位线状态不符条件提交总部失败，不允许新增商品行或者增加采购数量，谢谢！', skinseq: 3 });
                        e.value = oldfbizqty;
                    }
                    break;
                case 'fmaterialid':
                    if (e.opctx && e.opctx.ignore) return;
                    //如果该商品在《选配配件映射》单据体- 配件映射 直接是匹配到了【配件商品名称】时, 要报错被配置的配件不可以单独下
                    var fentity = that.Model.getEntryData({ id: 'fentity' });
                    var newproduct = e.value.id;
                    if (fentity && newproduct) {
                        var oldproduct = that.Model.getEntryRowData({ id: 'fentity', row: e.row });
                        if (!that.TakeTGCparts(e.value.id)) {
                            e.value = oldproduct.fmaterialid;
                            e.result = true;
                            yiDialog.warn('当前商品为配件不允许单独下单, 必须与主商品配套下单 ! ! !');
                            return;
                        }
                    }
                    break;
                case 'fsupplierid':
                    var fsupplierid = that.Model.getValue({ id: 'fsupplierid' });
                    //校验采购订单是否有收支记录
                    var fbillno = that.Model.getValue({ id: 'fbillno' });
                    var parm = {
                        simpledata: {
                            fsourcenumber: fbillno
                        }
                    };
                    yiAjax.p('/dynamic/ydj_purchaseorder?operationno=checkhasincomedisburse', parm, function (r) {
                        if (r) {
                            if (r.operationResult.srvData) {
                                var srvData = r.operationResult.srvData;
                                if (srvData == null) {
                                    return;
                                }
                                if (srvData.flag) {
                                    yiDialog.warn('当前订单已生成下游收支记录，不允许更改供应商；如需更改，请先与财务确认删除所有收支记录，再更改供应商。');
                                    e.value = fsupplierid;
                                    e.result = true;

                                }
                            }
                        }
                    }, null, null, null, { async: false });
                    break;
            }
        };
        ////表格行创建后事件
        //_child.prototype.onEntryRowCreated = function (e) {
        //    var that = this;
        //    switch (e.id.toLowerCase()) {
        //        case 'fentity':
        //            var fbilltypeid = that.Model.getValue({ id: 'fbilltypeid' });
        //            if (fbilltypeid.fname != "摆场订单") {
        //                that.podeptidChangeFlag = 1;
        //                that.deliveridChangeFlag = 1;
        //            }
        //            break;
        //    }
        //};

        _child.prototype.TakeTGCparts = function (productid) {
            var that = this;
            var CanTake = true;
            var param = {
                simpleData: {
                    formId: 'ydj_purchaseorder',
                    fproductid: productid,
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/ydj_purchaseorder?operationno=checktgcpart', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (!res.isSuccess) {
                    CanTake = false;
                } else {
                    CanTake = true;
                }
            }, null, null, null, { async: false });
            return CanTake;
        }

        _child.prototype.TakeTGCpartsByStandard = function (e) {
            var that = this;
            //如果是库存查询带出的商品不需要走铁架床带出配件逻辑
            var isqueryinventory = e && e.ctx && e.ctx.isqueryinventory;
            if (isqueryinventory) return;

            var iscombine = e && e.opctx && e.opctx.iscombine;
            if (iscombine) return;

            var rowdata = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            //勾选了配件标记但是 未勾选允许选配（即标准品）
            if (rowdata.fmaterialid.fispartflag && !rowdata.fmaterialid.fispresetprop) {
                that.Model.invokeFormOperation({
                    id: 'tgcpartbystandard',
                    opcode: 'tgcpartbystandard',
                    param: {
                        formId: 'ydj_purchaseorder',
                        fproductid: e.value.id,
                        rowId: e.row,
                        domainType: 'dynamic'
                    }
                });
            }
        }

        _child.prototype.onClearProductEntry = function (e) {
            var that = this;
            var entry = that.Model.getEntryData({ id: 'fentity' });
            if (!entry) {
                return;
            }
            var hasEntry = [];//商品明细是否有商品
            for (var i = 0; i < entry.length; i++) {
                if (entry[i].fmaterialid.id != '') {
                    hasEntry.push(true);
                }
                else {
                    hasEntry.push(false);
                }
            }
            var index = hasEntry.indexOf(true);
            if (hasEntry.indexOf(true) <= -1) {
                return;
            }
            var msg = '';
            //;
            var fbilltypeid = that.Model.getValue({ id: 'fbilltypeid' });
            switch (e.id.toLowerCase()) {
                case 'fdeliverid': //送达方发生变动
                    msg = '修改送达方后会清空所有商品行, 是否要继续操作？';
                    break;
                case 'fbilltypeid'://单据类型发生变动
                    msg = '修改单据类型, 会清空所有商品行, 是否要继续操作？';
                    fbilltypeid = e.value;
                    break;
                case 'fpodeptid'://采购部门发生变动  
                    var msg = '修改采购部门后会清空商品行, 是否要继续操作？';
                    break;
            }

            //配置对话框
            e.dialog = {
                title: '提示', //该参数可选
                message: msg,
                confirm: function () {

                    //加载授权商品
                    that.ArrangingChange();
                    //清空商品明细
                    that.clearProdAll();

                },
                cancel: function () {

                    //获取旧值
                    var oldValue = that.Model.getValue({ id: e.id, row: e.row });
                    e.value = oldValue;
                    that.Model.setValue({ id: 'fpostaffid', value: that.fpostaffid });
                    e.result = true;
                }
            };
        }


        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            //下推的采购订单明细不允许修改
            //var sourceEntryId = $.trim(that.Model.getValue({ id: 'fsourceentryid_e', row: e.row }));
            //if (sourceEntryId) {
            //    e.result.enabled = false;
            //    return;
            //}
            var status = that.Model.getSimpleValue({ id: 'fstatus' });
            var billtypeid = that.Model.getSimpleValue({ "id": "fbilltypeid" });
            if (billtypeid == 'ydj_purchaseorder_zb') return;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });

            // 如果是套件则不允许编辑价格
            if (product && product.fissuitflag && e.id.toLowerCase() === 'fprice') {
                e.result.enabled = false;
                return;
            }

            if (e.id.toLowerCase() === 'fprice' && status != 'D' && status != 'E') {
                if (product && product.fmaterialid.id) {
                    //经销商自建，允许编辑，否则不允许编辑
                    if (product && product.fmaterialid.fmainorgid && product.fmaterialid.fmainorgid.id == Consts.loginCompany.id) {
                        e.result.enabled = true;
                    } else if (that.Model.getValue({ "id": "fbilltypeid" }) && that.Model.getValue({ "id": "fbilltypeid" }).fname == "期初采购订单") {
                        e.result.enabled = true;//期初采购订单，允许编辑价格
                    } else {
                        e.result.enabled = false;
                    }
                }
            }

            if ((e.id.toLowerCase() == "fdealprice" || e.id.toLowerCase() == "fdealamount_e" || e.id.toLowerCase() == "fdistrate") && status != 'D' && status != 'E') {
                // 非总部商品 且 商品=自建商品时，需要能够编辑成交单价,成交金额  
                //没有商品时不允许编辑成交单价,成交金额  折扣率
                if (typeof (product.fmaterialid.fmainorgid) == "undefined") {
                    e.result.enabled = false;
                    return;
                }
                //alert("商品企业id" + product.fmaterialid.fmainorgid.id.toString() + ",登录组织ID" + Consts.loginCompany.id.toString() + "顶级组织ID" + Consts.topcompanyid.toString());
                if ((product.fmaterialid.fmainorgid.id == Consts.loginCompany.id) && (product.fmaterialid.fmainorgid.id.toString() != Consts.topcompanyid.toString())) {
                    e.result.enabled = true;
                    return;
                } else if ((e.id.toLowerCase() == "fdealprice" || e.id.toLowerCase() == "fdealamount_e") && that.Model.getValue({ "id": "fbilltypeid" }) && that.Model.getValue({ "id": "fbilltypeid" }).fname == "期初采购订单" && status != 'D' && status != 'E') {
                    e.result.enabled = true;//期初采购订单，允许编辑价格
                }
                else { e.result.enabled = false; }
            }

            // 配件不允许修改
            if (product && product.fpartscombnumber && product.fpartscombnumber != '' && !product.fiscombmain) {
                //只需要锁定 辅助属性, 销售数量, 销售单位, 基本数量, 基本单位
                switch (e.id.toLowerCase()) {
                    case 'fattrinfo':
                    case 'fmaterialid':
                    case 'fbizqty':
                    case 'fbizunitid':
                    case 'fqty':
                    case 'funitid':
                    case 'fresultbrandid':
                        e.result.enabled = false;
                        return;
                }
            }

            //并且套件的子件也不允许修改
            if (product && product.fsuitcombnumber != '' && !product.fissuitflag) {
                //只需要锁定 辅助属性, 销售数量, 销售单位, 基本数量, 基本单位
                switch (e.id.toLowerCase()) {
                    case 'fattrinfo':
                    case 'fmaterialid':
                    case 'fbizqty':
                    case 'fbizunitid':
                    case 'fqty':
                    case 'funitid':
                    case 'fresultbrandid':
                        e.result.enabled = false;
                        return;
                }
            }
            //沙发组合号绑定后不允许再更新商品 
            if (product && product.fsofacombnumber != '') {
                switch (e.id.toLowerCase()) {
                    case 'fmaterialid':
                        //case 'fattrinfo':
                        //case 'fbizqty':
                        //case 'fbizunitid':
                        //case 'fqty':
                        //case 'funitid':
                        //case 'fresultbrandid':

                        e.result.enabled = false;
                        return;
                }
            }

            ////B2B促销逻辑
            ////总部合同状态！=“驳回”
            //if (e.id.toLowerCase() == 'fbizqty') {
            //    if (product.fpartscombnumber != '' && product.fcombinenumber != '') {
            //        var fhqderstatus = that.Model.getSimpleValue({ id: 'fhqderstatus' });
            //        if (fhqderstatus && fhqderstatus != "" && fhqderstatus != '05') {
            //            e.result.enabled = false;
            //            return;
            //        }
            //    }
            //}

            var publishStatus = $.trim(that.Model.getSimpleValue({ id: 'fpublishstatus' }));
            var chargebackStatus = $.trim(that.Model.getSimpleValue({ id: 'fchargebackstatus' }));
            //采购订单，发布状态 fpublishstatus=='publish_status_02' 的时候，全部锁住
            switch (e.id.toLowerCase()) {
                case 'fbizqty':
                case 'fbizunitid':
                case 'fnote':
                    //合同编号不为空时锁定行，除了采购数量, 采购单位, 备注 
                    if (product && product.fsoorderno != ''
                        && publishStatus != 'publish_status_02'
                        && status != 'D'
                        && status != 'E') {
                        e.result.enabled = true;
                        return;
                    }
                    break;
                case 'fqty':
                case 'fmaterialid':
                    //有套件组合码，不允许更改商品
                    if (product && product.fsuitcombnumber) {
                        e.result.enabled = false;
                        return;
                    }
                //不结束继续下面的逻辑
                case 'fcustomdes_e':
                case 'fprice':
                case 'fdistrate':
                case 'fdistamount_e':
                case 'fnote':
                    if (publishStatus === 'publish_status_02' && chargebackStatus === '0') {
                        e.result.enabled = false;
                        return;
                    }
                    if (e.id.toLowerCase() === 'fqty') {
                        var isCustom = that.Model.getValue({ id: 'fmaterialid_iscustom', row: e.row });
                        if (isCustom) {
                            e.result.enabled = false;
                            return;
                        }
                    }
                    break;
                case 'fnote_d':
                    if (publishStatus === 'publish_status_02' && chargebackStatus === '0') {
                        e.result.enabled = false;
                        return;
                    }
                    //如果明细中的 fsourceentryid 字段不为空，则不能出现删除按钮，也不能编辑
                    if (e.value && $.trim(e.value.fsourceentryid)) {
                        e.result.enabled = false;
                    }
                    break;
                case 'funstdtype':
                    // 如果勾选上【是否非标】后, 不允许取消勾选, 只允许删除
                    var fispresetprop = that.Model.getSimpleValue({ id: "fispresetprop", row: e.row });
                    var fcustom = that.Model.getSimpleValue({ id: "fcustom", row: e.row });
                    if (status == 'D' || status == 'E') {
                        e.result.enabled = false;
                    } else {
                        if (!fispresetprop && !fcustom) {
                            e.result.enabled = false;
                        } else {
                            e.result.enabled = true;
                        }
                    }

                    //商品为套件商品时(商品基础资料勾选上选配套件), 该行商品自动锁定"是否非标", 不允许编辑
                    var issuit = that.Model.getSimpleValue({ id: 'fissuitflag', row: e.row });
                    if (issuit) {
                        e.result.enabled = false;
                    }

                    if (e.value.funstdtype) {
                        e.result.enabled = false;
                    }
                    break;
                case 'fattrinfo':
                    if (publishStatus === 'publish_status_02' && chargebackStatus === '0') {
                        e.result.enabled = false;
                        return;
                    }
                    //商品为空时，不允许编辑
                    productId = $.trim(that.Model.getSimpleValue({ id: 'fmaterialid', row: e.row }));
                    if (!productId) {
                        e.result.enabled = false;
                        return;
                    }

                    break;
                case 'fselsuiterequire'://家纺套件要求
                    ;
                    //当 商品类别层级中存在“床品套件” 且系列编码为A1 且勾选了【允许定制】则可以编辑
                    var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
                    var productid = product.fmaterialid.id;
                    var fseries = "";
                    fseries = product.fseriesid.id;//新增有值
                    if (product.fmaterialid.fseriesid) {
                        fseries = product.fmaterialid.fseriesid.id;//修改有值
                    }
                    var fcustom = product.fcustom;
                    var CanEdit = that.CanEditSelsuit(productid, fseries, fcustom);
                    if (CanEdit) {
                        e.result.enabled = true;
                    } else {
                        that.Model.setValue({ id: 'fselsuiterequire', row: e.row, value: '' });
                        e.result.enabled = false;
                    }
                    break;
            }

            //放在原逻辑之后
            if (product && product.fmaterialid.id != '' && product.fpartscombnumber == '') {
                var fpackqty = product.fpackqty;
                if (e.id.toLowerCase() == "fbizqty" || e.id.toLowerCase() == "fqty") {
                    if (product.fsoorderno != '') {
                        //商品的采购件数 大于1可编辑
                        if (fpackqty > 1) {
                            e.result.enabled = true;
                        } else {
                            e.result.enabled = false;
                        }
                    }
                }
                //else {
                //    e.result.enabled = false;
                //}  


                if (product && product.fsoorderno != '') {
                    if (e.id.toLowerCase() == "fselsuiterequire") {
                        //当 商品类别层级中存在“床品套件” 且系列编码为A1 且勾选了【允许定制】则可以编辑
                        var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
                        var productid = product.fmaterialid.id;
                        var fseries = "";
                        fseries = product.fseriesid.id;//新增有值
                        if (product.fmaterialid.fseriesid) {
                            fseries = product.fmaterialid.fseriesid.id;//修改有值
                        }
                        var fcustom = product.fcustom;
                        var CanEdit = that.CanEditSelsuit(productid, fseries, fcustom);
                        if (CanEdit) {
                            e.result.enabled = true;
                        } else {
                            that.Model.setValue({ id: 'fselsuiterequire', row: e.row, value: '' });
                            e.result.enabled = false;
                        }
                    }
                    else {
                        if (e.id.toLowerCase() == "fprice")//过滤掉采购单价控制，采购单价已有参数管理来控制
                            return;

                        if ((e.id.toLowerCase() == "fdealprice") && status != 'D' && status != 'E') {
                            //商品=自建商品时，需要能够编辑成交单价
                            if (product.fmaterialid.fmainorgid.id == Consts.loginCompany.id) {
                                e.result.enabled = true;
                                return;
                            } else if (that.Model.getValue({ "id": "fbilltypeid" }) && that.Model.getValue({ "id": "fbilltypeid" }).fname == "期初采购订单") {
                                e.result.enabled = true;//期初采购订单，允许编辑价格
                                return;
                            }
                        }

                        e.result.enabled = false;
                    }
                    return;
                }
            }

            //《采购订单》表头的【备注】, 商品明细行的【采购数量】【备注】字段允许编辑 , 并且只允许减少数量, 《采购订单》剩余其他字段皆锁定
            if (this.isCanchangeQty(e)) {
                if (product && product.fmaterialid.id != '') {
                    //if (e.id.toLowerCase() == "fbizqty")
                    //{
                    //    //套件、沙发 不允许修改数量
                    //    if (product.fsuitcombnumber != '' || product.fsofacombnumber != '') {
                    //        e.result.enabled = false;
                    //    }
                    //    else {
                    //        e.result.enabled = true;
                    //    }
                    //}
                    //else
                    if (e.id.toLowerCase() == "fdescription_e" || e.id.toLowerCase() == "fbizqty") {
                        e.result.enabled = true;
                    }
                    else {
                        e.result.enabled = false;
                    }
                }
            }

            var funstdtypestatus = that.Model.getSimpleValue({ id: 'funstdtypestatus', row: e.row });
            if (e.id.toLowerCase() == "fcustomdes_e" && funstdtypestatus != '') {
                e.result.enabled = false;
                return;
            }
        };

        _child.prototype.CanEditSelsuit = function (productid, fseries, fcustom) {
            var that = this;
            var CanEdit = true;
            var param = {
                simpleData: {
                    formId: 'ydj_purchaseorder',
                    fproductid: productid,
                    fseries: fseries,
                    fcustom: fcustom,
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/ydj_purchaseorder?operationno=checkselsuit', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (!res.isSuccess) {
                    CanEdit = false;
                } else {
                    CanEdit = true;
                }
            }, null, null, null, { async: false });
            return CanEdit;
        };

        //表单元素被单击后
        // _child.prototype.onElementClick = function (e) {
        //     var that = this;
        //     if (!e.id) { return; }
        //     switch (e.id.toLowerCase()) {
        //         case 'show-quote':
        //             that.showQuoteDialog();
        //             break;
        //     }
        // };

        //显示报价明细对话框
        // _child.prototype.showQuoteDialog = function (e) {
        //     var that = this;

        //     //组装树形明细数据包
        //     var ds = that.Model.getEntryData({ id: that.fentity });
        //     if (!ds || ds.length <= 0) {
        //         yiDialog.mt({ msg: '商品明细为空，无法查看报价明细！', skinseq: 2 });
        //         return;
        //     }
        //     var treeData = [];
        //     for (var i = 0; i < ds.length; i++) {

        //         //如果没有填写商品则跳过
        //         if (!ds[i].fmaterialid || !$.trim(ds[i].fmaterialid.id)) continue;
        //         var attrinfo = '';
        //         //给辅助属性赋值
        //         if(ds[i].fattrinfo.fentity && ds[i].fattrinfo.fentity.length > 0){
        //             attrinfo = ds[i].fattrinfo.fname;
        //         }
        //         //主物料
        //         treeData.push({
        //             id: ds[i].id,
        //             FSeq: ds[i].FSeq,
        //             fparentid: '',
        //             flevel: 0,
        //             fexpanded: true,
        //             fisleaf: false,
        //             fproductid: ds[i].fmaterialid.fname,
        //             funitid: ds[i].funitid.fname,
        //             fattrinfo: attrinfo,
        //             flength: '',
        //             fwidth: '',
        //             fthick: '',
        //             fqty: ds[i].fqty,
        //             fprice: ds[i].fprice,
        //             famount: ds[i].famount,
        //             fdescription: ds[i].fnote
        //         });
        //         //子物料
        //         if (ds[i].fdetail) {
        //             for (var j = 0; j < ds[i].fdetail.length; j++) {
        //                 treeData.push({
        //                     id: ds[i].fdetail[j].id,
        //                     FSeq: ds[i].fdetail[j].FSeq,
        //                     fparentid: ds[i].id,
        //                     flevel: 1,
        //                     fexpanded: false,
        //                     fisleaf: true,
        //                     fproductid: ds[i].fdetail[j].fproductid_sub,
        //                     funitid: ds[i].fdetail[j].funitid_sub,
        //                     fattrinfo: ds[i].fdetail[j].fattrinfo_sub,
        //                     flength: ds[i].fdetail[j].flength_sub,
        //                     fwidth: ds[i].fdetail[j].fwidth_sub,
        //                     fthick: ds[i].fdetail[j].fthick_sub,
        //                     fqty: ds[i].fdetail[j].fqty_sub,
        //                     fprice: ds[i].fdetail[j].fprice_sub,
        //                     famount: ds[i].fdetail[j].famount_sub,
        //                     fdescription: ds[i].fdetail[j].fdescription_sub
        //                 });
        //             }
        //         }
        //     }
        //     if (treeData.length <= 0) {
        //         yiDialog.mt({ msg: '商品明细为空，无法查看报价明细！', skinseq: 2 });
        //         return;
        //     }
        //     that.Model.showForm({
        //         formId: 'ydj_purchasequote',
        //         param: { openStyle: Consts.openStyle.modal },
        //         cp: {
        //             fproductentry: treeData,
        //             callback: function (result) {

        //             }
        //         }
        //     });
        // };

        _child.prototype.generateUUID = function () {
            var d = new Date().getTime();
            var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
            });
            return uuid;
        };

        _child.prototype.checkcategory = function (catagoryarr) {
            var that = this;
            var CanBind = true;
            var param = {
                simpleData: {
                    formId: 'ydj_purchaseorder',
                    fcategoryid: catagoryarr.join(','),
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/ydj_order?operationno=checkcategory', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (!res.isSuccess) {
                    yiDialog.warn("当前勾选的商品不为沙发类, 不允许绑定！");
                    CanBind = false;
                } else {
                    CanBind = true;
                }
            }, null, null, null, { async: false });
            return CanBind
        };

        //套件选配
        _child.prototype.showsuitcustom = function () {
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行套件选配！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('套件选配不支持多选！');
                return;
            };
            if (ds) {
                var row = ds[0];
                //判断物料是否启用 选配套件
                var fissuitflag = row.data.fissuitflag;
                var fsuitproductid = row.data.fsuitproductid;
                var fsuitcombnumber = row.data.fsuitcombnumber;
                var fproductid = row.data.fmaterialid;
                var entries = that.Model.getEntryData({ id: that.fentity });

                var propIdList = [];
                for (var i = 0; i < entries.length; i++) {
                    var ei = entries[i];
                    if (!ei.fissuitflag && ei.fsuitcombnumber == fsuitcombnumber && ei.fattrinfo && ei.fattrinfo.fentity) {
                        propIdList = propIdList.concat(ei.fattrinfo.fentity.map(function (x) { return x.fvalueid; }));
                    }
                }
                var canchange = that.isCanchangeQty();

                if (fissuitflag && fproductid && fproductid.id) { //套件时，可选配
                    //调用 < 套件选配 >
                    var param = {
                        formId: "sel_selectionform",
                        rowid: row.pkid,
                        fproductid: fproductid.id,
                        fissuitflag: fissuitflag,
                        fsuitproductid: fsuitproductid.id,
                        fsuitcombnumber: fsuitcombnumber,
                        propidlist: propIdList.join(","),
                        canchange: canchange
                    };

                    that.Model.invokeFormOperation({
                        id: 'sel_selectionform',
                        opcode: 'showsuiteselection',
                        param: param
                    });
                } else if (fsuitproductid && fsuitcombnumber && fsuitproductid.id) { //【套件商品】与【套件组合号】皆不为空时, 会执行 < 套件选配 >
                    //找到套件行再执行
                    //var entries = that.Model.getEntryData({ id: that.fentity });
                    var suiteRow = null;
                    for (var i = 0; i < entries.length; i++) {
                        var entry = entries[i];
                        if (entry.fissuitflag && entry.fsuitcombnumber == fsuitcombnumber) {
                            suiteRow = entry;
                            break;
                        }
                    }

                    var param = {
                        formId: "sel_selectionform",
                        rowid: suiteRow.id,
                        fproductid: suiteRow.fmaterialid.id,
                        fissuitflag: suiteRow.fissuitflag,
                        fsuitproductid: suiteRow.fsuitproductid.id,
                        fsuitcombnumber: suiteRow.fsuitcombnumber,
                        propidlist: propIdList.join(","),
                        canchange: canchange
                    };

                    that.Model.invokeFormOperation({
                        id: 'sel_selectionform',
                        opcode: 'showsuiteselection',
                        param: param
                    });
                } else {
                    yiDialog.warn('当前商品不为套件, 不允许进行套件选配！');
                }
            }
        };

        //标准定制
        _child.prototype.showstandardcustom = function () {
            var that = this;
            //选中行
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            };
            if (ds) {
                var attrinfo = ds[0].data.fattrinfo;
                //var rowData = that.Model.getEntryRowData({ id: that.fentity, row: attrinfo.row });
                ////选中行
                //判断物料是否启用 选配类别 
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且未勾选非标定制
                if (Isenableselectioncategory && !fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框 
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    } else {
                        //弹出“标准定制-套件”功能框
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或勾选了非标定制，不允许标准定制！');
                    return;
                }
            }
        };

        //非标定制
        _child.prototype.showunstandardcustom = function () {
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行非标定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            }
            if (ds) {
                //判断选中行物料是否启用 选配类别
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且 勾选非标定制
                if (Isenableselectioncategory && fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fmaterialid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    } else {
                        //弹出“标准定制-套件”功能框
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或未勾选非标定制，不允许非标准定制！');
                    return;
                }
            }
        };

        //绑定沙发组合号
        _child.prototype.bindcombnum = function () {
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            var catagoryarr = [];
            ds.forEach(e => {
                if (e.data.fmaterialid.id != "" && e.data.fcategoryid && e.data.fcategoryid.id) {
                    catagoryarr.push(e.data.fcategoryid.id);
                } else if (e.data.fmaterialid.id != "" && e.data.fmaterialid && e.data.fmaterialid.fcategoryid && e.data.fmaterialid.fcategoryid.id) {
                    catagoryarr.push(e.data.fmaterialid.fcategoryid.id);
                }
            });
            if (catagoryarr.length < 2) {
                yiDialog.warn('当前商品明细未勾选多行有效商品, 无法进行组合绑定, 请勾选多行重新绑定！');
                return;
            }
            if (ds.some(e => e.data.fsofacombnumber != '' || e.data.fsuitcombnumber != '' || e.data.fpartscombnumber != '')) {
                yiDialog.warn('当前勾选的商品行已绑定其他组合号, 不允许重复绑定！');
                return;
            }
            if (ds.some(e => (e.data.fcombinenumber != '' && e.data.fcombinenumber != undefined))) {
                yiDialog.warn('当前勾选的商品行已绑定其他组合号, 不允许重复绑定!');
                return;
            }
            if (!that.checkcategory(catagoryarr)) {
                return;
            }
            var Newid = yiCommon.uuid(32);
            ds.forEach(e => {
                if (e.data.fmaterialid.id != "") {
                    that.Model.setValue({ id: 'fsofacombnumber', row: e.data.id, value: Newid });
                }
            });
        };

        //解绑沙发组合号
        _child.prototype.unbindcombnum = function () {
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length < 2) {
                yiDialog.warn('当前商品明细未勾选多行, 无法进行组合解绑, 请勾选多行重新解绑！');
                return;
            }
            if (ds.some(e => e.data.fsofacombnumber == '' && e.data.fmaterialid.id != "")) {
                yiDialog.warn('当前勾选的商品行没有组合号, 不允许解绑！');
                return;
            }
            //先判断解绑时是否勾选了不止一种组合号的商品
            var hasother = ds.every(ele => ele.data.fsofacombnumber === ds[0].data.fsofacombnumber);
            if (!hasother) {
                yiDialog.warn('当前勾选的商品行存在不同沙发组合号, 不允许解绑！');
                return;
            }
            //解绑的时候 要验证是否勾选完所有相同的 组合号的商品
            var allrows = that.Model.getEntryData({ id: that.fentity });
            //取未勾选的商品行 即差集
            var norow = allrows.filter(x => ds.every(y => y.data.id != x.id));
            if (norow.some(a => a.fsofacombnumber == ds[0].data.fsofacombnumber)) {
                yiDialog.warn('当前未勾选上所有相同沙发组合号的行, 不允许解绑！');
                return;
            }
            ds.forEach(e => {
                that.Model.setValue({ id: 'fsofacombnumber', row: e.data.id, value: '' });
            });
        };

        //促销活动
        _child.prototype.addpromotion = function () {
            var that = this;
            //点击时要校验当前供应商是否为总部供应商 (供应商里的销售组织不为空, 则表示为总部供应商)
            var fsupplierid = that.Model.getValue({ id: "fsupplierid" });
            var forgid = that.Model.getValue({ id: "forgid" });
            var status = that.Model.getSimpleValue({ id: 'fstatus' });
            var fhqderstatus = that.Model.getSimpleValue({ id: 'fhqderstatus' });
            var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
            //同时兼容新增和保存的效果
            if (!(forgid && forgid.id) && !(fsupplierid && fsupplierid.forgid && fsupplierid.forgid.id)) {
                yiDialog.warn('当前供应商不为总部供应商, 不允许向非总部供应商进行添加促销活动 !');
                return;
            }
            var fsupplierid = that.Model.getValue({ id: "fdeliverid" });
            if (!(fsupplierid && fsupplierid.id)) {
                yiDialog.warn('请先选择送达方!');
                return;
            }
            if (status == 'D' || status == 'E') {
                yiDialog.warn('当前订单已提交或已审核，不允许操作!');
                return;
            } else {
                if (fhqderstatus == "02") {
                    yiDialog.warn('当前订单已提交至总部或已终审，不允许操作!');
                    return;
                }
                else if (fhqderstatus == "03") {
                    yiDialog.warn('当前订单已提交至总部或已终审，不允许操作!');
                    return;
                }

            }
            //弹出《添加促销活动》界面 
            //var param = {
            //    formId: "sel_promotionform"
            //};

            //that.Model.invokeFormOperation({
            //    id: 'sel_promotionform',
            //    opcode: 'addpromotion',
            //    param: param
            //});

            that.Model.showForm({
                formId: 'sel_promotionform',
                param: {
                    openStyle: Consts.openStyle.modal
                },
                cp: {
                    callback: function (result) {
                        if (result && result.isSuccess) {
                            //刷新当前单据
                            //that.Model.refresh();
                        }
                    }
                }
            });

        };

        //重新排序
        _child.prototype.resetsort = function () {
            var that = this;
            var rows = that.Model.getEntryData({ id: that.fentity });
            rows.sort(function (a, b) {
                if (a.fsofacombnumber !== b.fsofacombnumber) {
                    return a.fsofacombnumber.localeCompare(b.fsofacombnumber);
                }
                else if (a.fcombinenumber !== b.fcombinenumber) {

                    if (a.fcombinenumber < b.fcombinenumber) {
                        return 1;
                    }
                    if (a.fcombinenumber > b.fcombinenumber) {
                        return -1;
                    }

                    //return a.fcombinenumber.localeCompare(b.fcombinenumber)
                }
                else if (a.fpartscombnumber !== b.fpartscombnumber) {
                    return a.fpartscombnumber.localeCompare(b.fpartscombnumber)
                }
                else if (a.fiscombmain !== b.fiscombmain) {
                    return b.fiscombmain - a.fiscombmain;
                }
                else if (a.fsuitcombnumber !== b.fsuitcombnumber) {
                    return a.fsuitcombnumber.localeCompare(b.fsuitcombnumber)
                }
                else if (a.fissuitflag !== b.fissuitflag) {
                    return b.fissuitflag - a.fissuitflag;
                } else {
                    return 1;
                }
            });

            that.Model.refreshEntry({ id: that.fentity });
        };

        //如果将支撑杆改为无，则将同配件号的支撑杆类型明细行删除掉
        _child.prototype.DelEntryByNoting = function (row) {
            var that = this;
            var arrNoType = that.attrinfoNew;
            var delRow = [];
            var parentrow = row.leng > 0 ? row[0].data : '';
            if (parentrow && parentrow.fpartscombnumber) {
                if (arrNoType.length > 0) {
                    var entryData = that.Model.getEntryData({ id: that.fentity });
                    for (var i = 0, j = entryData.length; i < j; i++) {
                        //找出同配件编码 且设置 配件类型为无的配件
                        if (parentrow.fpartscombnumber == entryData[i].fpartscombnumber && arrNoType.indexOf(entryData[i].fparttype) > -1 && entryData[i].fisautopartflag) {
                            delRow.push(entryData[i].id);
                        }
                    }
                    //避免删除后0条分录的操作
                    if (delRow.length == entryData.length) {
                        that.Model.addRow({ id: that.fentity, opctx: { ignore: true } });
                    }
                    for (var i = 0; i < delRow.length; i++) {
                        var row = delRow[i];
                        that.FirstDelete = false;
                        that.Model.deleteRow({ id: that.fentity, row: row, opctx: { ignore: true } });
                    }
                }
            }
        };

        //切换商品清除其配件 以及处理配件组合号
        _child.prototype.clearPart = function (row) {
            var that = this;
            var delRow = [];
            var parentrow = that.Model.getEntryRowData({ id: that.fentity, row: row });
            if (!parentrow.fiscombmain) return;
            var entryData = that.Model.getEntryData({ id: that.fentity });
            for (var i = 0, j = entryData.length; i < j; i++) {
                //找出同配件编码 且设置 配件类型为无的配件(自动带出)
                if (parentrow.fpartscombnumber == entryData[i].fpartscombnumber && parentrow.id != entryData[i].id) {
                    delRow.push(entryData[i].id);
                }
            }
            //避免删除后0条分录的操作
            if (delRow.length == entryData.length) {
                that.Model.addRow({ id: that.fentity, opctx: { ignore: true } });
            }
            for (var i = 0; i < delRow.length; i++) {
                var row = delRow[i];
                that.Model.deleteRow({ id: that.fentity, row: row, opctx: { ignore: true } });
            }
            if (delRow.length > 0) {
                that.Model.setValue({ id: 'fpartscombnumber', row: parentrow.id, value: '' });
            }
        };

        //校验是否有重复的配件
        _child.prototype.CheckParts = function (row, partsname) {
            var that = this;
            if (partsname == "铁架床") return true;
            var bool = true;
            var allrows = that.Model.getEntryData({ id: that.fentity });
            bool = allrows.some(o => o.fparttype == partsname && row[0] && o.fpartscombnumber == row[0].data.fpartscombnumber)
            return !bool;
        };

        _child.prototype.doaddarttr = function (partsname) {
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行添加选配！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('添加选配不支持多选！');
                return;
            };
            if (!that.CheckParts(ds, partsname)) {
                yiDialog.warn('当前商品已添加配件[' + partsname + '], 不允许重复添加同类型的配件！');
                return;
            }
            if (ds) {
                //获取选中行商品 的产品类别 从而匹配 选配配件映射里的维度。
                var fcategoryid = ds[0].data.fcategoryid.id;
                //判断选中行 商品档案  是否勾选了【是否可添加配件】
                //直接在后台处理匹配逻辑
                that.doaddparts(partsname, fcategoryid, JSON.stringify(ds[0].data));
            }
        };

        _child.prototype.doaddparts = function (partsname, fcategoryid, rows) {
            var that = this;
            that.Model.invokeFormOperation({
                id: 'doaddparts',
                opcode: 'doaddparts',
                param: {
                    formId: 'ydj_purchaseorder',
                    partsname: partsname,
                    fcategoryid: fcategoryid,
                    rows: rows,
                    domainType: 'dynamic'
                }
            });
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //文件上传
                case 'upthe':
                    var status = that.Model.getSimpleValue({ id: 'fstatus' });
                    if (status == 'D' || status == 'E') {
                        yiDialog.warn('单据已提交或已审核，不允许上传文件！');
                    } else {
                        that.initUpload();
                    }
                    e.result = true;
                    break;
                //结算
                case 'payment':
                case 'refund':
                    that.Model.invokeFormOperation({
                        id: 'LoadSettleInfo',
                        opcode: 'LoadSettleInfo',
                        selectedRows: [{ PKValue: that.Model.pkid }],
                        param: {
                            settleType: e.opcode
                        }
                    });
                    e.result = true;
                    break;
                //收支明细
                case 'incomedisburse':
                    e.result = true;
                    //that.Model.showForm({
                    //    formId: 'coo_incomedisburserptpur',
                    //    domainType: Consts.domainType.report,
                    //    param: { openStyle: Consts.openStyle.modal },
                    //    cp: {
                    //        sourceId: that.Model.pkid
                    //    }
                    //});
                    //改成直接打开收支记录列表
                    that.Model.showForm({
                        formId: 'coo_incomedisburse',
                        domainType: Consts.domainType.list,
                        param: {
                            openStyle: Consts.openStyle.modal,
                            filterstring: "fsourceid='" + that.Model.pkid + "'"
                        }
                    });
                    break;
                case 'save':
                case 'savesubmit':
                case 'saveaudit':
                    e.result = true;
                    var fstatus = that.Model.getValue({ id: "fstatus" });
                    if (that.ischangepromotion) {
                        yiDialog.mt({ msg: '当前修改数量后，促销数据计算完毕，请稍后再操作保存！', skinseq: 2 });
                        return;
                    }
                    ;
                    that.checkUnstdPrice(e);

                    var param = e.param;
                    var isUploadDraw = false;
                    if (that.billTypeParamSet) {
                        param.formId = "ydj_purchaseorder";
                        param.fstatus = fstatus?.id
                        isUploadDraw = that.billTypeParamSet.fisuploaddraw === true;
                        if (isUploadDraw) {
                            var drawEntryData = that.Model.getEntryData({ id: 'fdrawentity' });
                            if (drawEntryData.length == 0) {
                                yiDialog.mt({ msg: '请上传图纸信息！', skinseq: 2 });
                                return;
                            }
                        }
                        that.Model.invokeFormOperation({
                            id: 'tbSave',
                            opcode: 'save',
                            param: param
                        });
                    }

                    break;

                //套件选配
                case 'showsuitcustom':
                    e.result = true;
                    that.showsuitcustom();
                    break;
                //标准定制
                case 'showstandardcustom':
                    e.result = true;
                    that.showstandardcustom();
                    break;

                //非标定制
                case 'showunstandardcustom':
                    e.result = true;
                    that.showunstandardcustom();
                    break;

                //添加配件【气压杆】
                case 'addqyg':
                    e.result = true;
                    that.doaddarttr('气压杆');
                    break;
                case 'addzcg':
                    e.result = true;
                    that.doaddarttr('支撑杆');
                    break;
                case 'addcxdb':
                    e.result = true;
                    that.doaddarttr('床箱底板');
                    break;
                //绑定沙发组合号
                case 'bindcombnum':
                    e.result = true;
                    that.bindcombnum();
                    break;
                //解绑沙发组合号
                case 'unbindcombnum':
                    e.result = true;
                    that.unbindcombnum();
                    break;
                case 'resetsort':
                    e.result = true;
                    that.resetsort();
                    break;
                case 'addpromotion':
                    e.result = true;
                    that.addpromotion();
                    break;
                case 'showselection':
                    e.result = true;
                    var param = {
                        formId: "mt_selectionform"
                    };
                    var rows = that.Model.getSelectRows({ id: that.Model.selectionSettings.fentity });
                    if (rows && rows.length) {
                        var row = rows[0];
                        param.rowid = row.pkid;
                        param.materialid = row["data"][that.Model.selectionSettings.fmaterialid]["id"];
                        if (!param.materialid) {
                            yiDialog.warn('请先选择商品！');
                            e.result = true;
                            return;
                        }

                        //选配码
                        param.selectionnumber = row["data"][that.Model.selectionSettings.fselectionnumber] || "";
                        var suiteselectionid = "";
                        if (row["data"][that.Model.selectionSettings.fforsuiteselectionid]) {
                            suiteselectionid = row["data"][that.Model.selectionSettings.fforsuiteselectionid]["id"] || "";
                        }
                        //选配方案ID
                        param.suiteselectionid = suiteselectionid;
                        param.issuite = row["data"][that.Model.selectionSettings.fissuit] ? 1 : 0;
                    } else {
                        yiDialog.warn('请先选中商品明细行！');
                        e.result = true;
                        return;
                    }

                    that.Model.invokeFormOperation({
                        id: 'showselection',
                        opcode: 'showselection',
                        param: param
                    });
                    break;
                case 'selectionproc':
                    that.selectionProc(e);
                    break;
                case 'submitchange':
                    e.result = true;
                    that.showSubmitChangeDialog();
                    break;
                //提交总部
                case 'submithq':
                    that.showSubmitHqDialog(e);
                    break;
                case 'unstdtypeaudit':
                    e.result = true;
                    that.unstdTypeAudit();
                    break;
                case 'bizclose':
                    e.result = true;
                    if (that.isCanchangeQty()) {
                        var products = that.Model.getSelectRows({ id: that.fentity });
                        if (products.some(o => !o.data.fsuitcombnumber || !o.data.fsofacombnumber)) {
                            yiDialog.warn('向总部采购变更时，只允许减少数量不允许关闭!');
                            return;
                        }
                    }

                    /*if (!product || (product.fsuitcombnumber && product.fsuitcombnumber != '')) return;*/

                    //《采购订单》如果是向总部采购的订单, 不允许随便<关闭>与<反关闭>只可以在变更中进行操作, 
                    //判断向总部采购的逻辑为根据表头【供应商】对应《供应商》基础资料里面的【销售组织】果不为空, 即为向总部采购的订单
                    //var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
                    var fsupplierid = that.Model.getValue({ id: "fsupplierid" });
                    if (fsupplierid && fsupplierid.forgid && fsupplierid.forgid.id) {
                        //38294，变更状态下也不允许主动点
                        /*if (fchangestatus != '1') {*/
                        yiDialog.warn('当前采购订单为向总部采购的订单, 不允许进行关闭!');
                        return;
                        //}
                    }
                    //只要存在一行商品是已提交至总部 就不允许关闭反关闭
                    var has = that.hashqorderchg();
                    //var fhqderchgstatus = that.Model.getSimpleValue({ id: 'fhqderchgstatus' });
                    if (has) {
                        yiDialog.warn('当前采购订单已提交至总部，不允许进行关闭反关闭！');
                        return;
                    }
                    var rows = that.Model.getSelectRows({ id: that.fentity });
                    var rowIds = [];
                    rows.forEach(function (row) {
                        rowIds.push(row["pkid"]);
                    });
                    yiDialog.c('确认关闭选中商品行？', function () {
                        that.Model.invokeFormOperation({
                            id: 'tbBizClose',
                            opcode: 'bizclose',
                            param: {
                                'id': that.Model.getValue({ id: 'id' }),
                                'rowIds': rowIds
                            }
                        });
                    });
                    break;
                case 'unclose':
                    e.result = true;
                    //《采购订单》如果是向总部采购的订单, 不允许随便<关闭>与<反关闭>只可以在变更中进行操作, 
                    //判断向总部采购的逻辑为根据表头【供应商】对应《供应商》基础资料里面的【销售组织】果不为空, 即为向总部采购的订单
                    //var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
                    var fsupplierid = that.Model.getValue({ id: "fsupplierid" });
                    if (fsupplierid && fsupplierid.forgid && fsupplierid.forgid.id) {
                        //38294，变更状态下也不允许主动点
                        //if (fchangestatus != '1') {
                        yiDialog.warn('当前采购订单为向总部采购的订单, 不允许进行反关闭!');
                        return;
                        //}
                    }
                    //只要存在一行商品是已提交至总部 就不允许关闭反关闭
                    var has = that.hashqorderchg();
                    //var fhqderchgstatus = that.Model.getSimpleValue({ id: 'fhqderchgstatus' });
                    if (has) {
                        yiDialog.warn('当前采购订单已提交至总部，不允许进行关闭反关闭！');
                        return;
                    }
                    var rows = that.Model.getSelectRows({ id: that.fentity });
                    var rowIds = [];
                    rows.forEach(function (row) {
                        rowIds.push(row["pkid"]);
                    });
                    yiDialog.c('确认反关闭选中商品行？', function () {
                        that.Model.invokeFormOperation({
                            id: 'tbUnClose',
                            opcode: 'unclose',
                            param: {
                                'id': that.Model.getValue({ id: 'id' }),
                                'rowIds': rowIds
                            }
                        });
                    });
                    break;
                case 'unchange':
                    //总部采购订单且 变更状态为变更已提交不允许取消变更
                    var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
                    var fsupplierid = that.Model.getValue({ id: "fsupplierid" });
                    var fbilltype = that.Model.getValue({ id: 'fbilltypeid' });
                    //期初采购订单 不需要校验 变更已提交不允许取消变更
                    if (fsupplierid && fsupplierid.forgid && fsupplierid.forgid.id && fbilltype.fname != "期初采购订单") {
                        if (fchangestatus == '3') {
                            yiDialog.warn('当前采购订单为向总部采购的订单,变更已提交不允许取消变更!');
                            e.result = true;
                            return;
                        }
                    }
                    break;
                case 'unbindcontract'://解绑合同产品
                    ;
                    e.result = true;
                    var rows = that.Model.getSelectRows({ id: that.fentity });
                    if (rows.length == 0) {
                        yiDialog.warn('请选择一条数据再进行解绑操作！');
                        return;
                    }
                    var rowIds = [];
                    rows.forEach(function (row) {
                        rowIds.push(row["pkid"]);
                    });
                    var hasSourceBill = false;
                    for (var i = 0; i < rows.length; i++) {
                        if (rows[i].data.fsoorderentryid.trim().length > 0) {
                            hasSourceBill = true;
                        }
                    };
                    if (hasSourceBill) {
                        yiDialog.c('合同商品被解绑后会变为自由库存，是否继续', function () {
                            that.Model.invokeFormOperation({
                                id: 'btnUnbindContract',
                                opcode: 'UnbindContract',
                                param: {
                                    'id': that.Model.getValue({ id: 'id' }),
                                    'rowIds': rowIds
                                }
                            });
                        });
                    } else {
                        return;
                    }
                    break;
                case 'querybarcode'://条码联查
                    that.querybarcode(e);
                    break;
                case 'addcode'://打码
                    that.addcode(e);
                    break;
                case 'submitflow':
                    if (that.Model.viewModel.domainType === 'list') {
                        var rows = that.Model.getAllSelectedRows();
                        //存在一行 变更中的就要提示
                        var param = {
                            simpleData: {
                                rows: JSON.stringify(rows.map(o => o.pkValue))
                            }
                        };
                        yiAjax.p('/bill/ydj_purchaseorder?operationno=getsalepromotioninfo', param, function (r) {
                            that.Model.unblockUI({ id: '#page#' });
                            var res = r;
                            that.showListPromotionDialog(res);

                            e.result = true;
                        }, null, null, null, { async: false });
                    }
                    break;
                case 'redirectionswj':
                    e.result = true;
                    var swjdetailurl = that.Model.getValue({ id: "fswjdetailurl" });
                    window.open(swjdetailurl);
                    //window.location.href = "http://www.baidu.com";
                    break;
                case 'partslistquery':
                    e.result = true;
                    var selectrows = that.Model.getSelectRows({ id: "fentity" });
                    if (selectrows.length != 1) {
                        yiDialog.mt({ msg: '请勾选一行商品再进行部件清单查询，谢谢！', skinseq: 2 });
                        return;
                    }

                    that.Model.invokeFormOperation({
                        id: 'btnPartsListQuery',
                        opcode: 'partslistquery',
                        param: {
                            'omsbillno': selectrows[0].data.fomsbillno
                        }
                    });
                    break;
                case 'quodetails'://报价明细（OMS）
                    e.result = true;
                    var rows = that.Model.getSelectRows({ id: "fentity" });
                    if (rows.length === 0 || rows.length > 1) {
                        yiDialog.warn('请勾选一行商品再进行报价明细查询，谢谢！');
                        return;
                    }


                    var omsbillno = that.Model.getSelectRows({ id: "fentity" })[0].data.fomsbillno;

                    //自测使用
                    //omsbillno = "5007377240521006L";

                    if (omsbillno == "") {
                        yiDialog.mt({ msg: '对不起，定制OMS单号为空，禁止此操作！', skinseq: 2 });
                        return;
                    }

                    yiAjax.p('/bill/ydj_purchaseorder?operationno=getomsconfig', '',
                        function (r) {
                            var data = r.operationResult;
                            var issuccess = data.isSuccess;
                            var mes = data.srvData;
                            if (issuccess) {
                                var height = window.screen.availHeight;
                                var width = (window.screen.availWidth);
                                var url = mes + omsbillno;

                                //url = "https://wx.derucci.com:8093/#/design/oms_quotation_info_kingde?platform=oms&token=kingde&omsbillno=5007377240521006L";
                                var conent = '<iframe src="{0}" style="width:100%;height:98%";border:none;"></iframe>'.format(url);
                                yiDialog.d({
                                    id: 'oms_dialog',
                                    type: 1,
                                    closeBtn: 1,
                                    resize: true,
                                    content: conent,
                                    title: '报价明细',
                                    area: [(width * 0.8) + 'px', (height * 0.8) + 'px']
                                });
                            } else {
                                yiDialog.warn("未获取到OMS地址配置信息，跳转失败！");
                                return;
                            }
                        })

                    break;
                case 'initiatechangeapply':
                    e.result = true;
                    if (that.Model.dataChanged === true) {
                        yiDialog.a('当前单据数据已修改，请先保存单据，再执行此操作！');
                        return;
                    }
                    if (that.domainType === 'bill' && !that.Model.pkid) {
                        yiDialog.a('请先保存单据，再执行此操作！');
                        return;
                    }
                    that.Model.invokeFormOperation({
                        id: 'initiatechangeapply',
                        opcode: 'initiatechangeapply',
                        param: {
                            'formId': 'ydj_purchaseorder',
                            'id': that.Model.getValue({ id: 'id' }),
                            'ruleid': "ydj_purchaseorder2ydj_purchaseorderapply_chg"
                        }
                    });
                    break;
                case 'querychangeapply':
                    e.result = true;
                    //显示变更申请单列表
                    that.showChangeApplyFormList(e);
                    break;

                case 'pushfeedback':
                    e.result = true;
                    var fstatus = that.Model.getSimpleValue({ id: "fstatus" });
                    if (fstatus == "E") {
                        var selectrows = that.Model.getSelectRows({ id: that.fentity });
                        if (selectrows.length != 1) {
                            yiDialog.mt({ msg: '请勾选一行商品！', skinseq: 2 });
                            return;
                        }

                        that.Model.invokeFormOperation({
                            id: 'tbPushFeedback',
                            opcode: 'pushfeedback',
                            param: {
                                'rowIds': that.getSelRowData(),
                                'FSeq': selectrows[0].data.FSeqs
                            }
                        });
                    }
                    else {
                        yiDialog.mt({ msg: '对不起，售后反馈单下推失败,采购订单必须是已审核状态!', skinseq: 2 });
                        return;
                    }
                    break;
            }
        };

        //返回选中行，不勾选时为全选返回全部行ID
        _child.prototype.getSelRowData = function () {
            var that = this;
            var rows = that.Model.getSelectRows({ id: that.fentity });
            var rowIds = [];
            rows.forEach(function (row) {
                rowIds.push({ Id: row["pkid"] });
            });
            if (rows.length == 0) {//不勾选时默认全选
                var mdl = that.Model.getEntryData({ id: that.fentity });
                for (var i = 0; i < mdl.length; i++) {
                    rowIds.push({ Id: mdl[i].id });
                }
            }
            return rowIds;
        }

        //判断明细中是否存在提交总部
        _child.prototype.hashqorderchg = function () {
            var that = this;
            var has = false;
            var Entryrows = that.Model.getEntryData({ id: that.fentity });
            if (that.Model.viewModel.domainType === 'list') {
                var rows = that.Model.getAllSelectedRows();
                //存在一行 变更中的就要提示
                var param = {
                    simpleData: {
                        formId: 'ydj_order',
                        rows: JSON.stringify(rows.map(o => o.pkValue)),
                        domainType: 'dynamic'
                    }
                };
                yiAjax.p('/bill/ydj_purchaseorder?operationno=CheckHasHqChg', param, function (r) {
                    that.Model.unblockUI({ id: '#page#' });
                    var res = r.operationResult;
                    if (!res.isSuccess) {
                        has = false;
                    } else {
                        has = true;
                    }
                }, null, null, null, { async: false });
            } else {
                has = Entryrows.some(o => o.fhqderchgstatus_e.id == '01');
            }
            return has;
        }



        //条码联查
        _child.prototype.querybarcode = function (e) {
            var that = this;
            e.result = true;

            var selRows = that.Model.getSelectRows({ id: that.fentity });
            if (!selRows || selRows.length < 1) {
                yiDialog.warn('未选中行！');
                return;
            }
            //if (selRows.length > 1) {
            //    yiDialog.warn('只允许勾选一行进行条码联查! ! !');
            //    return;
            //}
            var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
            if (!$.trim(fbillno)) {
                yiDialog.warn("当前商品未查询到对应条码信息！");
                return;
            }
            var datas = [];
            for (var i = 0; i < selRows.length; i++) {
                datas.push({ seldata: selRows[i].pkid });
            }
            //JSON.stringify(datas)
            that.Model.invokeFormOperation({
                id: 'isexsitbarcode',
                opcode: 'isexsitbarcode',
                param: {
                    'formId': 'bcm_barcodemaster',
                    'fsourcetype': 'ydj_purchaseorder',
                    'fsourcenumber': fbillno,
                    'fsourcelinenumber': JSON.stringify(datas),
                }
            });
        },

            //打码请求判断包装清单是否存在接口
            _child.prototype.addcode = function (e) {
                var that = this;
                e.result = true;

                var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
                that.Model.invokeFormOperation({
                    id: 'IsExistPackOrder',
                    opcode: 'IsExistPackOrder',
                    param: {
                        'formId': 'bcm_packorder',
                        'fsourcetype': 'ydj_purchaseorder',
                        'fsourcenumber': fbillno,
                    }
                });
            },

            //计价过程查询
            _child.prototype.selectionProc = function (e) {
                var that = this;
                e.result = true;

                var billPkid = that.Model.pkid;
                if (!billPkid) {
                    yiDialog.warn('请保存单据后再执行该操作！');
                    return;
                }

                var selRows = that.Model.getSelectRows({ id: that.fentity });
                if (!selRows || selRows.length < 1) {
                    yiDialog.warn('请先选择一行商品明细！');
                    return;
                }

                //目前只支持同时查看一行明细的计价过程
                var entryId = selRows[0].pkid;
                if (!entryId) {
                    yiDialog.warn('所选的第一行商品明细尚未保存，暂无计价过程数据！');
                    return;
                }

                that.Model.invokeFormOperation({
                    id: 'selectionproc',
                    opcode: 'selectionproc',
                    param: {
                        billPkid: billPkid,
                        entryId: entryId
                    }
                });
            },


            //显示结算对话框
            _child.prototype.showSettleDialog = function (settleInfo) {
                var that = this;
                settleInfo.fpaystatus = $.trim(settleInfo.fpaystatus).toLowerCase();
                settleInfo.fbilltypeid = $.trim(settleInfo.fbilltypeid).toLowerCase();
                settleInfo.fbizstatus = $.trim(settleInfo.fbizstatus).toLowerCase();
                that.Model.showForm({
                    formId: 'ydj_purchasesettledyn',
                    param: { openStyle: Consts.openStyle.modal },
                    cp: {
                        fsourceid: settleInfo.fsourceid,
                        fsourcenumber: settleInfo.fsourcenumber,
                        fissyn: settleInfo.fissyn,
                        synBankNum: settleInfo.synBankNum,
                        fsupplierid: settleInfo.fsupplierid,
                        funsettleamount: settleInfo.funsettleamount,
                        fsettledamount: settleInfo.fsettledamount,
                        funconfirmamount: settleInfo.funconfirmamount,
                        fallaccounts: settleInfo.fallaccounts,
                        ffbillamount: settleInfo.ffbillamount,
                        fdeptid: settleInfo.fdeptid,
                        fimage: { id: '', name: '' },
                        callback: function (result) {
                            if (result && result.isSuccess) {
                                //刷新当前单据
                                that.Model.refresh();
                            }
                        }
                    }
                });
            };


        //显示提交变更对话框
        _child.prototype.showSubmitChangeDialog = function () {
            var that = this;
            var billno = that.Model.getSimpleValue({ id: "fbillno" });
            //弹窗
            var html = '\
            <div class="form-group row">\
                <div class="col-md-12">\
                    <textarea class="form-control" placeholder="请填写变更申请" \
                        style="width:410px;max-width:410px;min-width:410px;height:130px;max-height:130px;min-height:130px;"> </textarea>\
                </div>\
            </div>';
            yiDialog.d({
                type: 1,
                content: html,
                resize: false,
                title: '请填写变更申请',
                area: ['450px', '270px'],
                btn: ['取消', '确定'],
                btncls: ['', '0'],
                yes: function (index, layero) {
                    layer.close(index);
                },
                btn2: function (index, layero) {
                    var v = $(layero).find('textarea').val();
                    if (v.length === 0) {
                        yiDialog.warn('变更申请原因不能为空！');
                        return false;
                    }
                    that.Model.invokeFormOperation({
                        id: 'submitchange',
                        opcode: 'submitchange',
                        billData: JSON.stringify([that.Model.uiData]),
                        selectedRows: [{ PKValue: that.Model.pkid }],
                        param: {
                            changeReason: v
                        }
                    });
                    layer.close(index);
                    return false;
                },
                success: function (layero, index) {

                }
            });
        };

        //将采购变更单 提交总部 也需要填写变更原因
        _child.prototype.showSubmitHqDialog = function (e) {
            ;
            var that = this;
            if (that.Model.viewModel.domainType === 'list') {
                var rows = that.Model.getAllSelectedRows();
                //存在一行 变更中的就要提示
                var rows_chg = rows.find(o => o.data.fchangestatus == '1');
                if (rows_chg) {
                    yiDialog.warn('当前勾选数据中, 存在变更中的采购订单, 变更中的采购订单请在编辑页面进行提交总部！');
                    e.result = true;
                    return;
                }
            }
            //只有【变更状态】= "变更中" 且 【总部合同状态】= "已终审" 且 【总部变更状态】不等于"提交至总部"时 才需要填写变更原因
            var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
            var fhqderstatus = that.Model.getSimpleValue({ id: 'fhqderstatus' });
            //var fhqderchgstatus = that.Model.getSimpleValue({ id: 'fhqderchgstatus' });
            //明细存在总部变更状态为提交总部
            var has = that.hashqorderchg();

            var combineChangeFlag = false;
            //需要判断促销数据是否改变了，如果变更状态下，改变了，要做提示
            var rowsall = that.Model.getEntryData({ id: that.fentity });
            if (rowsall != null) {
                for (var i = 0; i < rowsall.length; i++) {
                    if (rowsall[i].fcombinenumber) {
                        var initEntryItem = that.InitEntry.find(o => o.id == rowsall[i].id);
                        if (initEntryItem != null && rowsall[i].fbizqty != initEntryItem.fbizqty) {
                            combineChangeFlag = true;
                        }
                    }
                }
            }

            if (fchangestatus == '1' && fhqderstatus == '03' && !has) {
                e.result = true;

                if (combineChangeFlag) {
                    yiDialog.c("变更组合促销套餐活动中商品的采购数量，将不再享受优惠折扣，确认是否提交总部？", function () {
                        yiDialog.p({
                            title: "请填写变更申请",
                            value: " ",
                            formType: 2,
                            ok: function (v) {
                                v = $.trim(v);
                                if (v.length === 0) {
                                    yiDialog.warn('变更申请原因不能为空！');
                                    return false;
                                }
                                that.Model.invokeFormOperation({
                                    id: 'submithq',
                                    opcode: 'submithq',
                                    billData: JSON.stringify([that.Model.uiData]),
                                    selectedRows: [{ PKValue: that.Model.pkid }],
                                    param: {
                                        changeReason: v
                                    }
                                });
                            }
                        })
                        return;
                    }, function () {
                        //不提交，返回
                        e.result = true;
                        return;
                    });
                } else {

                    //点击事件中，需要给value设置默认值"空格"
                    yiDialog.p({
                        title: "请填写变更申请",
                        value: " ",
                        formType: 2,
                        ok: function (v) {
                            v = $.trim(v);
                            if (v.length === 0) {
                                yiDialog.warn('变更申请原因不能为空！');
                                return false;
                            }
                            that.Model.invokeFormOperation({
                                id: 'submithq',
                                opcode: 'submithq',
                                billData: JSON.stringify([that.Model.uiData]),
                                selectedRows: [{ PKValue: that.Model.pkid }],
                                param: {
                                    changeReason: v
                                }
                            });
                        }
                    })

                }
            }
            else {
                if (that.Model.viewModel.domainType != 'list') {
                    var entryRows = that.Model.getEntryData({ id: that.fentity });
                    var hascombine = entryRows.some(o => o.fcombinenumber != '');//存在有套餐的数据，才做校验
                    if (hascombine) {
                        e.result = true;
                        var id = that.Model.getValue({ id: "id" });
                        var para = {
                            formId: "ydj_purchaseorder",
                            id: id,
                            domainType: that.Model.viewModel.domainType

                        }
                        that.QueryIsSubmitHQ(e, para);
                    } else {
                        //因为没设置e.result=true;程序会正常提交。不需要再额外提交一次，导致重复提交的问题
                        //debugger
                        //that.Model.invokeFormOperation({
                        //    id: 'submithq',
                        //    opcode: 'submithq',
                        //    billData: JSON.stringify([that.Model.uiData]),
                        //    selectedRows: [{ PKValue: that.Model.pkid }],
                        //    param: {
                        //    }
                        //});
                    }
                }
                else {
                    //列表上操作，只能去后台校验是否存在活动，再去判断了
                    var rows = that.Model.getAllSelectedRows();
                    var ids = "";
                    for (var i = 0; i < rows.length; i++) {
                        ids += rows[i].pkValue + ","
                    }
                    // 移除最后一个逗号
                    if (ids.endsWith(',')) {
                        ids = ids.slice(0, -1);
                    }

                    var para = {
                        formId: "ydj_purchaseorder",
                        id: ids,
                        domainType: that.Model.viewModel.domainType
                        //simpleData: {
                        //    formId: "ydj_purchaseorder",
                        //    id: ids,
                        //    domainType: that.Model.viewModel.domainType
                        //}
                    }

                    that.QueryIsSubmitHQ(e, para);
                }
            }


        };

        _child.prototype.QueryIsSubmitHQ = function (e, para) {
            var that = this;

            e.result = true;
            that.Model.invokeFormOperation({
                id: 'CheckPromotion',
                opcode: 'checkpromotion',
                billData: JSON.stringify([that.Model.uiData]),
                selectedRows: [{ PKValue: that.Model.pkid }],
                param: para
            });
            return;
        }

        //列表弹窗
        _child.prototype.showListPromotionDialog = function (e, pageId) {
            ;
            var that = this;
            var res = e.operationResult;
            var isSuccess = res.isSuccess;
            var srvData = res.srvData.matchCombine;
            var type = res.srvData.type;
            if (srvData && srvData.length == 0) {
                that.Model.invokeFormOperation({
                    id: 'submit',
                    opcode: 'submit',
                    param: {
                        secendconfirm: "1"
                    }
                });
                return;
            }
            if (isSuccess && srvData != null && srvData != '' && srvData != 'undefind') {
                if (type == "hasCombine") {

                    var liProduct = "";
                    var fbillno = "";
                    if (srvData != null && srvData != '' && srvData != 'undefind') {
                        for (var i = 0; i < srvData.length; i++) {
                            fbillno += srvData[i] + "、";
                        }
                        fbillno = fbillno.substr(0, fbillno.length - 1);
                        liProduct += "<li >" + fbillno + "采购订单中的商品正在参与组合促销活动，在列表提交无法参加活动，若要参加活动请进入单据明细提交，请确认要参加活动吗?</li>";
                    }
                    //弹窗
                    var html = '<div class="form-group row"><div class="col-md-12">';
                    html += '' + liProduct + '';
                    html += '</div> </div>';

                    yiDialog.d({
                        type: 1,
                        content: html,
                        resize: false,
                        title: '提示',
                        area: ['500px', '300px'],
                        btn: ['确定', '不参与仍要提交'],
                        btncls: ['', '0'],
                        yes: function (index, layero) {
                            layer.close(index);
                            return false;
                        },
                        btn2: function (index, layero) {
                            layer.close(index);
                            that.Model.invokeFormOperation({
                                id: 'submit',
                                opcode: 'submit',
                                param: {
                                    secendconfirm: "1"
                                }
                            });
                            return true;
                        },
                        success: function (layero, index) {

                        }
                    });
                } else {

                    var liProduct = "";
                    var fbillno = "";
                    if (srvData != null && srvData != '' && srvData != 'undefind') {
                        for (var i = 0; i < srvData.length; i++) {
                            fbillno += srvData[i] + "、";
                        }
                        fbillno = fbillno.substr(0, fbillno.length - 1);
                        liProduct += "<li >" + fbillno + "采购订单中的商品正在参与组合促销活动，但是还需加购其他商品凑单才能享受折扣优惠，确定要参与活动吗？</li>";
                    }
                    //弹窗
                    var html = '<div class="form-group row"><div class="col-md-12">';
                    html += '' + liProduct + '';
                    html += '</div> </div>';

                    yiDialog.d({
                        type: 1,
                        content: html,
                        resize: false,
                        title: '提示',
                        area: ['500px', '300px'],
                        btn: ['确定', '不参与仍要提交'],
                        btncls: ['', '0'],
                        yes: function (index, layero) {
                            layer.close(index);
                            return false;
                        },
                        btn2: function (index, layero) {
                            layer.close(index);
                            that.Model.invokeFormOperation({
                                id: 'submit',
                                opcode: 'submit',
                                param: {
                                    secendconfirm: "1"
                                }
                            });
                            return true;
                        },
                        success: function (layero, index) {

                        }
                    });
                }
            }
        };

        //组合促销活动凑单提示框
        _child.prototype.showSalePromotionDialog = function (e) {
            ;
            var that = this;
            var res = e.result.operationResult;
            var isSuccess = res.isSuccess;
            var srvData = res.srvData;

            var simpleMessage = e.result.operationResult.optionData.autoSave;
            if (simpleMessage == "autosubmit" || simpleMessage == "autoaudit") {
                srvData = res.optionData.save;
            }
            if (srvData == null || srvData == 'underfind' || srvData == '') {
                srvData = e.result.operationResult.optionData.SrvData;
            }
            if (isSuccess && srvData != null && srvData != '' && srvData != 'undefind') {
                var liProduct = "";
                var combineid = "";
                var combineProduct = [];
                if (srvData != null && srvData != '' && srvData != 'undefind') {
                    liProduct += "<div style='padding:20px;'> <li style='float:left;width:5%'>序号</li>    <li style='text-align:center'>内容 </li> </div>";
                    for (var i = 0; i < srvData.length; i++) {
                        //var otherProduct = "";
                        for (var j = 0; j < srvData[i].otherCombine.length; j++) {
                            //otherProduct += srvData[i].otherCombine[j].qty + "  " + srvData[i].otherCombine[j].funitname + "“" + srvData[i].otherCombine[j].combineproduct + "”,";
                            combineProduct.push({ combinenumber: srvData[i].combinenumber, product: srvData[i].otherCombine[j].fproductid });
                            //combineProduct.push({ combinenumber: srvData[i].combinenumber, product: srvData[i].otherCombine[j].fproductid });
                        }

                        var productno = "";
                        for (var j = 0; j < srvData[i].productno.length; j++) {
                            productno += "“" + srvData[i].productno[j] + "”" + "、";
                        }
                        productno = productno.substr(0, productno.length - 1);
                        liProduct += "<div style='padding:20px;'>";
                        liProduct += "<li style='float:left;width:8%' >" + (i + 1) + "</li>";
                        //liProduct += "<li style='float:left;width:90%' >" + productno + "商品与" + srvData[i].activityname + "“" + srvData[i].groupdesc + "”" + "组合套餐匹配，再采购" + otherProduct + "享受" + srvData[i].rate / 10 + "折优惠" + "</li>";
                        liProduct += "<li style='float:left;width:90%' >" + productno + "商品与" + srvData[i].activityname + "“" + srvData[i].groupdesc + "”" + "组合套餐匹配，再选择该套餐中其他商品凑成符合活动规则的组合套餐，可享受折扣优惠"
                            + "<a href='#' class='dynamic-link' id='p" + that.formContext.pageId + "-" + [i] + "' data-id='p" + that.formContext.pageId + "-" + [i] + "' > 查看凑单商品明细>></a></li>";
                        liProduct += "</div>";//
                        combineid += srvData[i].combineid + ",";
                    }

                }
                //弹窗
                var html = '<div class="form-group row"><div class="col-md-12">';
                html += '' + liProduct + '';
                html += '</div> </div>';

                yiDialog.d({
                    type: 1,
                    content: html,
                    resize: false,
                    title: '组合促销活动凑单提示',
                    area: ['90%', '70%'],
                    btn: ['不参与仍要提交', '去凑单'],
                    btncls: ['', '0'],
                    yes: function (index, layero) {

                        //that.Model.invokeFormOperation({
                        //    id: 'tbSave',
                        //    opcode: 'save',
                        //    param: param
                        //});

                        that.Model.invokeFormOperation({
                            id: 'submit',
                            opcode: 'submit',
                            param: {
                                secendconfirm: "1"
                            }
                        });

                        if (simpleMessage == "autoaudit") {
                            that.Model.invokeFormOperation({
                                id: 'autoaudit',
                                opcode: 'auditflow',
                                param: {
                                    autoaudit: "true"
                                }
                            });
                        }

                        layer.close(index);
                        return false;
                    },
                    btn2: function (index, layero) {
                        layer.close(index);
                        //显示我的待办任务子页面
                        that.dataRange(e, combineid, combineProduct);
                        return true;
                    },
                    success: function (layero, index) {

                    }
                });

                for (var i = 0; i < srvData.length; i++) {
                    document.getElementById('p' + that.formContext.pageId + "-" + [i] + '').addEventListener('click', function (event) {
                        if (event.target.classList.contains('dynamic-link')) {
                            const dynamicId = event.target.dataset.id;

                            var arr = [];
                            arr.push(srvData[(dynamicId.split('-')[1])]);
                            mymethon(arr)

                            console.log('点击了动态链接，ID:', dynamicId);
                        }
                    });
                }
                //$("#prodInfo").click(function () { mymethon(srvData) });

                function mymethon(srvData) {
                    that.combineProduct(srvData);
                }
            }
        };

        _child.prototype.combineProduct = function (srvData) {

            var content = ["<div class=\"portlet - body form\">",
                "<div class=\"portlet-body form\">",
                "<div class=\"form - body\">",
                "<table class=\"table\">",
                "<thead><tr><th style='width:50px;'>序号</th><th>商品编码</th><th>商品名称</th><th>子组号</th><th>套餐组合基数</th><th>采购数量</th><th>建议凑单数量</th></tr></tr></thead>",
                "<tbody>"];


            for (var i = 0; i < srvData.length; i++) {
                for (var j = 0; j < srvData[i].otherCombine.length; j++) {
                    var prod = srvData[i].otherCombine[j];
                    content.push("<tr><td>" + (j + 1) + "</td><td>" + prod.fproductno + "</td><td>" + prod.combineproduct + "</td><td>"
                        + prod.fgroupnumber + "</td><td>" + prod.fbaseqty + "</td><td>" + prod.fbizqty + "</td><td>" + prod.qty + "</td></tr>");
                }

            }
            content.push("</tbody></table></div></div></div>");

            yiDialog.d({
                type: 1,
                closeBtn: 1,
                resize: true,
                maxmin: true,
                content: content.join(""),
                title: '查看凑单商品明细',
                area: ['60%', '40%'],
                btn: ['关闭'],
                btncls: ['0'],
                yes: function (index, layero) {
                    layer.close(index);
                }
            });
        }


        //打开数据范围对话框
        _child.prototype.dataRange = function (e, combineid, combineProduct) {
            ;
            var that = this;
            var fcity = that.Model.getValue({ id: 'fcity' });
            var fsourcenumber = that.Model.getValue({ id: 'fsourcenumber' });

            var fdeliverid = that.Model.getValue({ id: 'fdeliverid' });
            var fbilltypeid = that.Model.getValue({ id: 'fbilltypeid' });

            var fproductid = "";
            if (combineProduct.length > 0) {
                for (var i = 0; i < combineProduct.length; i++) {
                    fproductid += combineProduct[i].product + ",";
                }
                fproductid = fproductid.substr(0, fproductid.length - 1);
            }


            combineid = combineid != "" ? combineid.substr(0, combineid.length - 1) : "";
            that.Model.showForm({
                openStyle: 'modal',
                formId: 'ydj_purchaseorder_promotion',
                cp: {
                    fcity: fcity,
                    fsourcenumber: fsourcenumber,
                    combineid: combineid,
                    fproductid: fproductid,
                    combineProduct: combineProduct,
                    fdeliverid: fdeliverid.id,
                    fbilltypeid: fbilltypeid.id,
                    purorderid: that.Model.getValue({ id: 'id' })
                }
            });
        };

        $('.related-tabbable').on('click', function (event) {
            event.preventDefault()
            $(this).tab('show')
        });

        //获取促销活动
        _child.prototype.showPurchaseOrder = function (model) {
            var that = this;
            var srvData = null;

            var fbillno = model.getValue({ id: 'fbillno' });
            var fcity = model.getValue({ id: 'fcity' });
            var fresultbrandid = model.getValue({ id: 'fresultbrandid' });
            var fdeliverid = model.getValue({ id: 'fdeliverid' });

            var param = {
                simpleData: {
                    formId: 'ydj_purchaseorder',
                    param: {
                        fbillno: fbillno,
                        fcity: fcity,
                        fresultbrandid: fresultbrandid,
                        fdeliverid: fdeliverid
                    }
                }
            };
            yiAjax.p('/bill/ydj_purchaseorder?operationno=showpurchaseorder', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                srvData = r.operationResult.srvData;
            }, null, null, null, { async: false });
            return srvData;
        };

        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            switch (e.entryId) {
                case 'fdrawentity':
                    //如果明细中的 fsourceentryid 字段不为空，则不能出现删除按钮
                    if (e.data && $.trim(e.data.fsourceentryid)) {
                        e.cancel = true;
                        return e.result = [{
                            id: 'download',
                            text: '下载'
                        }];
                    }
                    break;
            }
            //列表操作列
            if (e && $.trim(e.id) === 'foperate' && e.data) {
                e.cancel = true;
                e.result = [];
                if ($.trim(e.data.fpaystatus).toLowerCase() !== 'paystatus_type_03') {
                    e.result.push({ id: 'payment', text: '结算' });
                }
                var bizStatus = $.trim(e.data.fbizstatus);
                switch (bizStatus) {
                    case 'business_status_01':
                        e.result.push({ id: 'submitsynergy', text: '发送' });
                        break;
                    case 'business_status_02':
                        e.result.push({ id: 'repealsynergy', text: '撤销发送' });
                        break;
                    case 'business_status_05':
                        e.result.push({ id: 'pricefirm', text: '确认' });
                        break;
                    case 'business_status_09':
                        e.result.push({ id: 'cancelconfirm', text: '取消确认' });
                        break;
                }
            }
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            //业务类型
            var billTypeId = '';
            switch (e.id.toLowerCase()) {
                //送达方
                case 'fdeliverid':
                    //var param = {
                    //    simpleData: {
                    //        formId: 'ydj_purchaseorder'
                    //    }
                    //};
                    //if (that.fdeliverids.length == 0) {
                    //    yiAjax.p('/bill/ydj_purchaseorder?operationno=searchdeliverids', param, function (r) {

                    //        var res = r.operationResult;
                    //        if (res.isSuccess) {
                    //            that.fdeliverids = res.srvData;
                    //        }
                    //    }, null, null, null, { async: false });
                    //}
                    ;
                    var fsourcetype = that.Model.getValue({ id: 'fsourcetype' });
                    //按匹配的ID过滤
                    if (that.fdeliveridsBySerieid.length > 0 && fsourcetype.fnumber == "ydj_order") {
                        var fids = "'无'";
                        for (var i = 0; i < that.fdeliveridsBySerieid.length; i++) {
                            fids += ",'" + that.fdeliveridsBySerieid[i] + "'";
                        }
                        e.result.filterString = " fid in (" + fids + ")  ";
                    }
                    //BUG 23175 去掉城市过滤
                    //else {
                    //    var fpodeptid = that.Model.getValue({ id: 'fpodeptid' });
                    //    //部门不为空时进入匹配
                    //    if (that.storeCity.length > 0 && fpodeptid.fname != "") {
                    //        e.result.filterString = "fcity='" + that.storeCity + "'";
                    //    }
                    //}



                    break;
                //导购员
                case 'fstaffid':
                    var deptId = that.Model.getSimpleValue({ id: 'fdeptid' });
                    e.result.filterString = "fdeptid<>'' and fdeptid=@fdeptid";
                    e.result.params = [
                        { fieldId: 'fdeptid', pValue: deptId }
                    ];
                    break;
                //业绩品牌
                case 'fresultbrandid':
                    ;
                    var fseriesid = '', fauxseriesid = '', fproductid = '';
                    var fattrinfo = '';
                    var fcustomdes_e = '';
                    var product = that.Model.getSelectRows({ id: that.fentity });
                    if (product.length > 0) {
                        fseriesid = product[0].data.fseriesid.id;//新增有值
                        fauxseriesid = product[0].data.fauxseriesid.id
                        if (product[0].data.fmaterialid.fseriesid) {
                            fseriesid = product[0].data.fmaterialid.fseriesid.id;//修改有值
                        }
                        if (product[0].data.fmaterialid.fauxseriesid) {
                            fauxseriesid = product[0].data.fmaterialid.fauxseriesid.id;//修改有值
                        }
                        fproductid = product[0].data.fmaterialid.id;
                        //获取辅助属性
                        fattrinfo = JSON.stringify(product[0].data.fattrinfo.fentity);
                        fcustomdes_e = product[0].data.fcustomdes_e;
                    }
                    var fbilltype = that.Model.getSimpleValue({ id: 'fbilltypeid' });
                    var fdeliverid = that.Model.getSimpleValue({ id: 'fdeliverid' });
                    var deptid = that.Model.getSimpleValue({ id: 'fpodeptid' });
                    e.result.simpleData = {
                        fbilltype: fbilltype,
                        fseriesid: fseriesid,
                        fauxseriesid: fauxseriesid,
                        deptid: deptid,
                        fdeliverid: fdeliverid,
                        fproductid: fproductid,
                        fcustomdes: fcustomdes_e,
                        fattrinfo: fattrinfo
                    };
                    break;
                //商品基础资料
                case 'fmaterialid':
                    if (that.billTypeParamSet) {
                        var category = that.billTypeParamSet.fcategoryid;
                        if (category) {
                            var categoryId = $.trim(category.id);
                            if (categoryId) {
                                var categoryIds = categoryId.split(',');
                                //根据产品类别Id过滤
                                e.result.filterString = "fcategoryid in ('" + categoryIds.join("','") + "')";
                            }
                        }
                    }

                    var fsupplierid = that.Model.getValue({ id: 'fsupplierid' });
                    var deptId = that.Model.getSimpleValue({ id: 'fpodeptid' });
                    var fbilltypeid = that.Model.getValue({ "id": "fbilltypeid" });
                    var fdeliverid = that.Model.getValue({ "id": "fdeliverid" });
                    var billtypeNo = fbilltypeid ? fbilltypeid.fnumber : "";
                    var billtypeName = fbilltypeid ? fbilltypeid.fname : "";
                    var srcPara = {
                        billtypeNo: billtypeNo,
                        billtypeName: billtypeName,
                        deptId: deptId,
                        deliverid: fdeliverid ? fdeliverid.id : "",
                        deliverno: fdeliverid ? fdeliverid.fnumber : "",
                        delivername: fdeliverid ? fdeliverid.fname : "",
                        supplierid: fsupplierid ? fsupplierid.id : "",
                        supplierNo: fsupplierid ? fsupplierid.id : "",
                        supplierName: fsupplierid ? fsupplierid.id : "",
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
                //采购部门
                case 'fpodeptid':
                    var fbilltypeid = that.Model.getValue({ id: 'fbilltypeid' });
                    if (fbilltypeid && fbilltypeid.fname == '摆场订单') {
                        e.result.filterString = "fdeptstroe='1'";
                    }
                    break;
            }
        };

        //表格按钮点击
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.btnid.toLowerCase()) {
                case 'g_record': //批录按钮

                    //取消某个字段的批录功能
                    if (e.fieldId.toLowerCase() === 'fresultbrandid') {

                        //取消平台的批录逻辑
                        e.cancel = true;
                    }
                    //采购单位批录
                    if (e.fieldId.toLowerCase() === 'fbizunitid') {
                        var recordVal = e.data.funitid.fname;
                        for (var i = 0; i < e.datas.length; i++) {
                            var currRow = e.datas[i];
                            var funitname = currRow.funitid.fname;
                            if (funitname && funitname != recordVal) {
                                yiDialog.warn('下方明细行存在不一致的【基本单位】，不允许批录！');
                                e.cancel = true;
                                break;
                            }
                        }
                    }
                    break;
            }
            if (e.id == that.fdentity) {
                var rowData = that.Model.getEntryRowData({ id: that.fdentity, row: e.row });
                switch (e.btnid.toLowerCase()) {
                    case 'download':
                        var fileId = $.trim(rowData.ffileid.id);
                        if (fileId) {
                            that.Model.downloadFile({ fileIds: fileId, fileNames: $.trim(rowData.ffilename) });
                        }
                        break;
                    case 'delete':
                        var status = that.Model.getSimpleValue({ id: 'fstatus' });
                        if (status == 'D' || status == 'E') {
                            yiDialog.warn('单据已提交或已审核，不允许删除文件！');
                            return;
                        }
                        var uploaderId = $.trim(rowData.fuploaderid);
                        if (uploaderId && uploaderId === Consts.loginUserId) {
                            //删除权限只有上传人有
                            that.Model.deleteRow({ id: that.fdentity, row: e.row });
                        } else {
                            yiDialog.warn('该文件不是您自己上传的，无法删除！');
                        }
                        break;
                }
            }
            //列表操作
            switch (e.btnid.toLowerCase()) {
                case 'submitsynergy':
                case 'repealsynergy':
                case 'pricefirm':
                case 'cancelconfirm':
                    that.Model.invokeFormOperation({
                        id: e.btnid,
                        opcode: e.btnid,
                        selectedRows: e.selectedRows
                    });
                    break;
            }
        };

        //文件上传
        _child.prototype.initUpload = function () {
            var that = this;
            that.Model.showForm({
                formId: 'bd_uploadfile',
                param: {
                    openStyle: Consts.openStyle.modal
                },
                cp: {
                    callback: function (result) {
                        if (!result || !result.files) return;
                        var files = result.files;
                        var fileIds = files.id.split(',');
                        var fileNames = files.name.split(',');
                        var fileSizes = files.sizes.split(',');
                        for (var i = 0, l = fileIds.length; i < l; i++) {
                            var data = {
                                ffileid: { id: fileIds[i], name: fileNames[i] }, //文件Id
                                ffilename: fileNames[i], //文件名
                                ffileformat: yiCommon.getFileExt(fileNames[i]), //文件格式
                                ffilesize: (fileSizes && fileSizes[i]) ? fileSizes[i] : 0, //文件大小
                                fuploader: Consts.loginUserDisplayName, //上传人
                                fuploaderid: Consts.loginUserId, //上传人Id
                                fuptime: new Date().ToString('yyyy-MM-dd HH:mm:ss'),  //上传时间
                                fnote_d: '', //备注
                            };
                            that.Model.addRow({ id: that.fdentity, data: data, opctx: { ignore: true } });
                        }
                        that.Model.refreshEntry({ id: that.fdentity });
                    }
                }
            });
        };

        //针对科凡单独临时增加主产品必录控制逻辑
        _child.prototype.kfMainProductRequired = function (e) {
            var that = this;
            var mainProduct = $.trim(that.Model.getSimpleValue({ id: 'fmainproduct' }));
            if (mainProduct) return;

            var billTypeIds = [
                '57a315b073c243b8ad432264fc47ffb5',
                '65d76357525d4b63a5512d925d13cde0',
                '6e11119c96934338a7838e087b7b57f0',
                '9192cf6412994bb895db7ff75e144bf9',
                '16de43221d6b4a098bf62c64c41a2584',
                '1ad28e551fbd4c37a5acaaa9ce97a728',
                'eb6f46773bdf4540b0feb59cb21dc4eb'
            ];
            var $billType = that.Model.getEleMent({ id: 'fmainproduct', way: Consts.eleFetchWay.name });
            var $parent = $billType.parent();
            var $label = $parent.siblings('.control-label');
            var labelText = '主产品';

            var billTypeId = that.Model.getSimpleValue({ id: 'fbilltypeid' });
            if (billTypeIds.indexOf(billTypeId) >= 0) {
                $billType.attr('required', true);
                $label.attr('style', 'color: rgb(239, 46, 55);').html('<span class="required">*</span>' + labelText);
                $parent.addClass('has-error');

                yiDialog.warn('主产品字段不能为空！');
                e.cancel = true;

            } else {
                $billType.removeAttr('required');
                $label.removeAttr('style').html(labelText);
                $parent.removeClass('has-error');
            }
        };

        _child.prototype.EnableSelsuiterequire = function (e) {
            var that = this;
            var rowData = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            //.当 对应行【商品类别】的所有层级中, 有一个层级等于”床品套件” 且 对应行【系列】=”DeRUCCI Home” 且 对应行勾选上【允许定制】时, 允许编辑, 否则清空锁定
            if (rowData && rowData.fcategoryid.fname == "床品套件" &&
                rowData.fseriesid.fname == "DeRUCCI Home" &&
                rowData.fcustom
            ) {
                that.Model.setEnable({ id: 'fselsuiterequire', row: e.row, value: true });
            } else {
                that.Model.setEnable({ id: 'fselsuiterequire', row: e.row, value: false });
            }
        };

        //单据类型为摆场类型，销售部门发生变动时
        _child.prototype.ArrangingChange = function () {
            var that = this;
            var fbilltypeid = that.Model.getValue({ id: 'fbilltypeid' });
            var dept = that.Model.getValue({ id: 'fpodeptid' });
            if (fbilltypeid.fname == "摆场订单") {
                that.Model.invokeFormOperation({
                    id: 'GetProductByOrgid',
                    opcode: 'GetProductByOrgid',
                    param: {
                        formId: 'ydj_purchaseorder',
                        deptid: dept.id
                    }
                });
            }
        };

        //送达方值变更
        _child.prototype.DeliverChange = function () {
            var that = this;
            var deliver = that.Model.getValue({ id: 'fdeliverid' });
            if (deliver) {
                that.Model.invokeFormOperation({
                    id: 'GetSupplierByDeliverId',
                    opcode: 'GetSupplierByDeliverId',
                    param: {
                        formId: 'ydj_purchaseorder',
                        deliverid: deliver.id
                    }
                });
            }
        };

        //清空商品行
        _child.prototype.clearProdAll = function () {
            var that = this;
            //清空明细数据
            that.Model.deleteEntryData({ id: that.fentity });
            that.Model.addRow({ id: that.fentity, opctx: { ignore: true } });
        };

        //处理业绩品牌
        _child.prototype.setResultBrand = function (e) {

            var that = this;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            //var deptid = that.Model.getSimpleValue({ id: 'fdeptid' })
            var fdeliverid = that.Model.getSimpleValue({ id: 'fdeliverid' });
            var fbilltype = that.Model.getValue({ "id": "fbilltypeid" }).fname;
            var fpodeptid = "";
            //只有 单据类型为摆场订单时才需要传采购部门到后台，后台判断如果采购部门不为空则是摆场逻辑（其他逻辑不需要传采购部门）
            //if (fbilltype.fname == "摆场订单") {
            //0727 调整：非摆场订单也可能会传部门，也要考虑门店授权。
            fpodeptid = that.Model.getSimpleValue({ id: 'fpodeptid' });
            //}
            if (fdeliverid != null && fdeliverid != "") {
                //to do 根据后台返回只授权的品牌
                that.Model.invokeFormOperation({
                    id: 'checkorderresultbrand',
                    opcode: 'checkorderresultbrand',
                    param: {
                        fdeliverid: fdeliverid,
                        deptid: fpodeptid,
                        fbilltype: fbilltype,
                        row: e.row,
                        formId: 'ydj_purchaseorder',
                        domainType: 'dynamic'
                    }
                });
            }
        };

        //字段值发生变化后触发的事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            if (!e.row) return;
            switch (e.id.toLowerCase()) {
                case 'fselsuiterequire':
                    var rowData = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
                    that.Model.setValue({ id: 'fcustomdes_e', row: e.row, value: rowData.fselsuiterequire.fname });
                    break;
                case 'fdeptid':
                    //门店改变时，清空导购员字段
                    that.Model.setValue({ id: 'fstaffid', value: '' });
                    break;
                case 'fbilltypeid':
                    //加载单据类型参数设置
                    that.loadBillTypeParamSet(e.value.id);
                    break;
                case 'fmaterialid':
                    that.Model.setValue({ id: 'fselsuiterequire', row: e.row, value: '' });
                    that.Model.setValue({ id: 'fcustomdes_e', row: e.row, value: '' });
                    that.EnableSelsuiterequire(e);
                    //更换商品设置默认折扣为10
                    that.Model.setValue({ id: 'fdistrate', row: e.row, value: 10 });
                    //更换商品定制说明和数量清空
                    that.Model.setValue({ id: 'fcustomdes_e', row: e.row, value: '' });
                    that.Model.setValue({ id: 'fqty', row: e.row, value: 0 });
                    //加载商品信息
                    var productId = $.trim(e.value.id);
                    var enable = false;
                    if (productId) {
                        enable = true;
                        that.Model.invokeFormOperation({
                            id: 'loadproduct',
                            opcode: 'loadproduct',
                            selectedRows: [{ PKValue: productId }],
                            param: {
                                formId: 'ydj_product',
                                domainType: Consts.domainType.bill,
                                rowId: e.row
                            }
                        });
                    }
                    that.getParentProduct(e);
                    //更换商品辅助属性要清空
                    that.Model.setValue({ id: 'fattrinfo', row: e.row, value: { fentity: [] } });
                    that.Model.setValue({ id: 'fattrinfo_e', row: e.row, value: { fentity: [] } });
                    that.onBillInitProduct('change', { attrinfo: e });
                    //清空选配相关：选配码、所属套件、所属套件选配码、组合说明、套件组合说明
                    that.Model.setValue({ id: 'fselectionnumber', row: e.row, value: "" });
                    that.Model.setValue({ id: 'fforproductid', row: e.row, value: "" });
                    that.Model.setValue({ id: 'fforsuiteselectionid', row: e.row, value: "" });
                    that.Model.setValue({ id: 'fdescription_suite', row: e.row, value: "" });
                    that.Model.setValue({ id: 'fpackagedescription', row: e.row, value: "" });
                    //切换商品 清除当前商品的所有配件
                    that.clearPart(e.row);
                    //to do 根据销售部门对应门店的经销商授权，处理业绩品牌取值逻辑。
                    that.setResultBrand(e);

                    //检查是否更新套件描述
                    that.checkSuiteDesc(e);
                    //如果是勾选了选配标记的商品，直接根据选配配件映射找到配件商品（无需根据条件）
                    //if (e.opctx && e.opctx.promotion) {
                    //    return;
                    //}
                    that.TakeTGCpartsByStandard(e);
                    ;
                    break;
                case 'fbizqty':         // 销售数量
                    //计算商品明细相关字段
                    //that.productEntryChange({ name: e.id, rowId: e.row, value: e.value });
                    //修改套件销售数量 应该根据套件子件数量翻倍
                    that.calculateqtyBysuit(e);
                    that.calculateqtyBypart(e);
                    that.calculateqtyBysofa(e);
                    that.calculateqtyBypromotion(e);
                    break;
                case 'fresultbrandid':
                    that.linkResultbrandBysuit(e);
                    break;
                case 'fbizunitid':
                    that.onBillInitProduct('change', { attrinfo: e });
                    break;

                case 'fsupplierid':
                    var billtype = that.Model.getSimpleValue({ "id": "fbilltypeid" });
                    if (billtype == 'ydj_purchaseorder_zb' && Consts.isdirectsale) {
                        return;
                    } else {
                        that.onBillInitProduct('changeall', { attrinfo: e });
                    }
                    break;
                case 'fsupplierstatus':
                    //是否显示协同信息面板
                    that.setCoorInfoVisible();
                    break;
                // case 'fqty':
                // case 'fprice':
                // case 'fdistrate':
                // case 'fdistamount_e':
                //计算商品明细相关字段
                //that.productEntryChange({ name: e.id, rowId: e.row, value: e.value });
                //break;
                case 'ffbillamount':
                    that.dealAmount();
                    break;
                case 'fdealamount_e':
                    //计算结算信息
                    that.calculateSettlement();
                    //if (e.value != 0) {
                    //    var fmaterialid = that.Model.getValue({ id: 'fmaterialid', row: e.row });
                    //    if (fmaterialid.fmainorgid.id == Consts.loginCompany.id) {
                    //        //商品=自建商品时，当成交金额编辑时，反算成交单价  
                    //        var qty = that.Model.getValue({ id: 'fbizqty', row: e.row });
                    //        //BUG39739 采购订单录入自建商品的成交单价自动被清空商品数据，在rule.json中实现了，故把这个注释掉，以免除不尽的时候相互联动
                    //        //that.Model.setValue({ id: 'fdealprice', row: e.row, value: (e.value / yiMath.toNumber(qty)).toFixed(6) });
                    //    }
                    //}
                    break;

                case 'fdeliverid':
                    that.DeliverChange();
                    break;
                case 'fprice':
                    that.calculateDistrate(e);
                    break;
                case 'funstdtype':
                    that.unstdtypeFieldValueChanged(e);
                    break;
                case 'fstore':
                    that.getStoreCity();
                    break;
                case 'fpodeptid'://采购部门发生变动  
                    that.podeptidChangeFlag = 1;
                    break;
            }
        };

        //【是否非标】字段值变化时的逻辑处理onGetEditorState
        _child.prototype.unstdtypeFieldValueChanged = function (e) {
            ;
            var that = this;

            //如果是非标定制，则【零售价】直接设置为0，不需要取价
            if (e.value) {
                that.Model.setValue({ id: 'fprice', row: e.row, value: 0 });
            }
            //当 单据类型 = "期初采购订单" 时,   单据体-商品明细 勾选上 "是否非标" = "是" 时, 不需要执行非标的锁定对应行字段逻辑
            var fbilltype = that.Model.getValue({ id: 'fbilltypeid' });
            if (fbilltype.fname != "期初采购订单") {
                //非标时锁定，否则不锁定
                //锁定或解锁【零售价】、【成交单价】、【成交金额】
                that.Model.setEnable({ id: 'fprice', row: e.row, value: !e.value });
                that.Model.setEnable({ id: 'fdealprice', row: e.row, value: !e.value });
                that.Model.setEnable({ id: 'fdealamount_e', row: e.row, value: !e.value });
            }
        };

        //根据套件组合号联动业绩品牌
        _child.prototype.linkResultbrandBysuit = function (e) {
            var that = this;
            var fresultbrandid = e.value;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            var ds = that.Model.getEntryData({ id: that.fentity });
            if (product.fsuitcombnumber) {
                //找到对应的子件明细
                for (var a = 0; a < ds.length; a++) {
                    //有套件组合号 但是不是套件商品则为套件的子件
                    if (ds[a].fsuitcombnumber == product.fsuitcombnumber && !ds[a].fissuitflag) {
                        that.Model.setValue({ id: 'fresultbrandid', row: ds[a].id, value: fresultbrandid });
                    }
                }
            }
            if (product.fpartscombnumber) {
                for (var a = 0; a < ds.length; a++) {
                    //有套件组合号 但是不是套件商品则为套件的子件
                    if (ds[a].fpartscombnumber == product.fpartscombnumber && !ds[a].fiscombmain) {
                        that.Model.setValue({ id: 'fresultbrandid', row: ds[a].id, value: fresultbrandid });
                    }
                }
            }
            that.Model.refreshEntry({ id: that.fentity });
        };

        //当【采购单价】不为0时，【折扣率】=成交单价*10/采购单价
        _child.prototype.calculateDistrate = function (e) {
            var that = this;

            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            if (product && product.fprice > 0 && product.fdealprice > 0) {
                product.fdistrate = product.fdealprice * 10 / product.fprice;
            }
        };

        //根据套件翻倍子件的销售数量
        _child.prototype.calculateqtyBysuit = function (e) {

            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            var suitqty = e.value;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            var ds = that.Model.getEntryData({ id: that.fentity });
            //如果当前修改明细是套件商品，修改数量后要翻倍子件的销售数量
            if (product.fsuitcombnumber) {
                //找到对应的子件明细
                for (var a = 0; a < ds.length; a++) {
                    //有套件组合号 但是不是套件商品则为套件的子件
                    if (ds[a].fsuitcombnumber == product.fsuitcombnumber && !ds[a].fissuitflag) {
                        var fsubqty = yiMath.toNumber(ds[a].fsubqty);
                        var fprice = yiMath.toNumber(ds[a].fprice);
                        var fdealprice = yiMath.toNumber(ds[a].fdealprice);
                        //that.Model.setValue({ id: "fqty", row: ds[a].id, value: suitqty * fsubqty, tgChange: false });//数量
                        that.Model.setValue({ id: "fbizqty", row: ds[a].id, value: suitqty * fsubqty, tgChange: false });//
                        that.Model.setValue({ id: 'famount', row: ds[a].id, value: fprice * suitqty * fsubqty });
                        that.Model.setValue({ id: 'fdealamount_e', row: ds[a].id, value: fdealprice * suitqty * fsubqty });
                    }
                }
            }

        };

        //计算促销商品数量
        _child.prototype.calculateqtyBypromotion = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            var fcombinenumber = product.fcombinenumber;
            if (!fcombinenumber || fcombinenumber == '') return;

            //如果总部合同状态是已终审，并且当前在变更中，就不走促销逻辑
            var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
            var fhqderstatus = that.Model.getSimpleValue({ id: 'fhqderstatus' });
            if (fchangestatus == '1' && fhqderstatus == '03')
                return;
            that.ischangepromotion = true;
            //首先定义一个倍数
            that.Model.invokeFormOperation({
                id: 'savecombineinfo',
                opcode: 'savecombineinfo',
                formId: 'ydj_purchaseorder',
                billData: JSON.stringify([that.Model.uiData]),
                selectedRows: [{ PKValue: that.Model.pkid }],
                //selectedRows: [{ PKValue: that.Model.pkid }],
                param: {
                    formId: 'ydj_purchaseorder',
                    combinenumber: fcombinenumber,
                    pkid: that.Model.pkid,
                    parentPageId: that.pageId,
                    stype: 'purorder'
                }
            });
            return;
            var entryData = that.Model.getEntryData({ id: that.fentity });

            for (var i = 0; i < entryData.length; i++) {
                if (fcombinenumber == entryData[i].fcombinenumber) {
                    var curfbaseqty = that.Model.getValue({ id: 'fcombineqty', row: entryData[i].id });
                    var havemushproduct = entryData.filter(c => c.fmaterialid.id == entryData[i].fmaterialid.id && c.fcombinenumber == entryData[i].fcombinenumber);
                    if (havemushproduct.length > 0) {
                        var haveorder = havemushproduct.find(c => c.fsoorderno != "");
                        if (haveorder != undefined) {
                            that.Model.setValue({ id: 'fqty', value: mul * curfbaseqty, row: entryData[i].id, tgChange: false });
                            var havenoorder = havemushproduct.filter(c => c.fsoorderno == "");
                            if (havenoorder.length > 0) {
                                for (var j = 0; j < havenoorder.length; j++) {
                                    that.Model.setValue({ id: "fpromotion", row: havenoorder[j].id, value: "" }); //促销活动名称
                                    that.Model.setValue({ id: "fcombinenumber", row: havenoorder[j].id, value: "" }); //组合促销编号
                                    that.Model.setValue({ id: 'fcombineremark', row: havenoorder[j].id, value: "" });//组合描述
                                    that.Model.setValue({ id: 'fcombinepriority', row: havenoorder[j].id, value: "" });//促销活动优先级
                                    that.Model.setValue({ id: 'fcombinerate', row: havenoorder[j].id, value: "" });//折扣率
                                    that.Model.setValue({ id: "fcombineqty", row: havenoorder[j].id, value: "" });//套餐组合基数
                                }
                            }
                        } else {
                            var flag = false;
                            for (var x = 0; x < havemushproduct.length; x++) {
                                if (flag) {
                                    that.Model.setValue({ id: "fpromotion", row: havemushproduct[x].id, value: "" }); //促销活动名称
                                    that.Model.setValue({ id: "fcombinenumber", row: havemushproduct[x].id, value: "" }); //组合促销编号
                                    that.Model.setValue({ id: 'fcombineremark', row: havemushproduct[x].id, value: "" });//组合描述
                                    that.Model.setValue({ id: 'fcombinepriority', row: havemushproduct[x].id, value: "" });//促销活动优先级
                                    that.Model.setValue({ id: 'fcombinerate', row: havemushproduct[x].id, value: "" });//折扣率
                                    that.Model.setValue({ id: "fcombineqty", row: havemushproduct[x].id, value: "" });//套餐组合基数
                                }

                                that.Model.setValue({ id: 'fqty', value: mul * curfbaseqty, row: entryData[i].id, tgChange: false });
                                flag = true;
                            }
                        }
                    }
                }
            }
        };

        //修改配件主商品时 根据配件组合号 翻倍配件的数量
        _child.prototype.calculateqtyBysofa = function (e) {
            //if (!e.tgChange) return;
            if (e.opctx && e.opctx.ignore) return;
            var that = this;
            var mainqty = e.value;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
            var fsofacombnumber = product.fsofacombnumber;
            if (fchangestatus == "1" && fsofacombnumber && mainqty == "0") {
                //如果沙发组合号数量修改为0 则其它沙发一并修改为0
                var entryData = that.Model.getEntryData({ id: that.fentity });
                for (var i = 0; i < entryData.length; i++) {
                    if (fsofacombnumber == entryData[i].fsofacombnumber) {
                        //tgChange 会导致不会联动更新金额
                        //that.Model.setValue({ id: "fbizqty", row: entryData[i].id, value: mainqty, tgChange: false });
                        that.Model.setValue({ id: "fbizqty", row: entryData[i].id, value: mainqty, opctx: { ignore: true } });
                    }
                }
            }
        };

        //修改配件主商品时 根据配件组合号 翻倍配件的数量
        _child.prototype.calculateqtyBypart = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            var mainqty = e.value;
            that.calculatePartQty(mainqty, e.row);
        };

        _child.prototype.calculatePartQty = function (mainqty, row) {
            var that = this;
            var product = that.Model.getEntryRowData({ id: that.fentity, row: row });
            var ds = that.Model.getEntryData({ id: that.fentity });
            //如果当前修改明细是配件商品，修改数量后要翻倍配件的销售数量
            if (product.fpartscombnumber) {
                //找到对应的子件明细
                for (var a = 0; a < ds.length; a++) {
                    //有配件组合号 但是不是配件主商品则为配件商品
                    if (ds[a].fpartscombnumber == product.fpartscombnumber && !ds[a].fiscombmain && ds[a].fcombinenumber == product.fcombinenumber) {
                        //配件数量（元数量）
                        var fpartqty = yiMath.toNumber(ds[a].fpartqty);
                        var fprice = yiMath.toNumber(ds[a].fprice);
                        var fdealprice = yiMath.toNumber(ds[a].fdealprice);
                        //that.Model.setValue({ id: "fqty", row: ds[a].id, value: suitqty * fsubqty, tgChange: false });//数量
                        that.Model.setValue({ id: "fbizqty", row: ds[a].id, value: mainqty * fpartqty, tgChange: false });//
                        that.Model.setValue({ id: 'famount', row: ds[a].id, value: fprice * mainqty * fpartqty });
                        that.Model.setValue({ id: 'fdealamount_e', row: ds[a].id, value: fdealprice * mainqty * fpartqty });
                    }
                }
            }

        };

        //计算成交金额
        _child.prototype.dealAmount = function (e) {
            var that = this;
            var dealAmount = that.Model.getValue({ id: 'ffbillamount' });
            var billAmount = that.Model.getValue({ id: 'fdealamount' });
            var entryData = that.Model.getEntryData({ id: that.fentity });
            if (entryData.length == 0) {
                return;
            }
            for (var i = 0, j = entryData.length; i < j; i++) {
                if (i < j - 1 || j == 1) {
                    that.Model.setValue({ id: 'fdealprice', row: entryData[i].id, value: yiMath.toNumber((dealAmount * (entryData[i].famount / billAmount)) / entryData[i].fbizqty).toFixed(2) });
                    that.Model.setValue({ id: 'fdealamount_e', row: entryData[i].id, value: yiMath.toNumber(dealAmount * (entryData[i].famount / billAmount)).toFixed(2) });
                }
                else if (i == j - 1) {
                    that.Model.setValue({ id: 'fdealprice', row: entryData[i].id, value: yiMath.toNumber((dealAmount * (entryData[i].famount / billAmount)) / entryData[i].fbizqty).toFixed(2) });
                    that.Model.setValue({ id: 'fdealamount_e', row: entryData[i].id, value: yiMath.toNumber(dealAmount * (entryData[i].famount / billAmount)).toFixed(2) });
                }
            }
        }

        //操作前触发的事件
        _child.prototype.onBeforeDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            switch (e.opcode) {
                case 'save':
                    //that.kfMainProductRequired(e);
                    break;
            }
        };

        _child.prototype.setResultBrandData = function (srvData) {
            ;;
            var that = this;
            //var ds = that.Model.getSelectRows({ id: that.fentity });
            //if (ds.length == 0)
            //    return;

            var ds = that.Model.getEntryRowData({ id: that.fentity, row: srvData.rowid });
            if (!ds) return;

            //如果是配件或者套件的子件则不需要走业绩品牌系列逻辑，直接继承主商品的业绩品牌
            if (ds.fpartscombnumber != "" && !ds.fiscombmain)
                return;
            if (ds.fsuitcombnumber != "" && !ds.fissuitflag)
                return;

            var brandid = that.Model.getValue({ id: 'fresultbrandid', row: ds.id });
            //如果是b2b促销过来的，如果业绩品牌为空，就正常走逻辑，否则促销功能里面已经做了赋值，不做额外修改
            if ((ds.fcombinenumber != "" || ds.fsourcebillno != "") && brandid.id)
                return;

            var resultSeriesObj = JSON.parse(srvData.resultSeriescode);
            //当前企业对应经销商所有授权（城市+实控人）
            var resultSeriesObj_agent = JSON.parse(srvData.resultSeriescode_agent);

            //如果没有门店授权或经销商授权 业绩品牌置为空 
            if (resultSeriesObj == null || resultSeriesObj_agent == null) {
                that.Model.setValue({ id: 'fresultbrandid', row: ds.id, value: '' });
                return;
            }

            var series = '', fauxseriesid = '', resultseries = '';
            series = ds.fseriesid.fnumber;
            if (!series) {
                series = ds.fmaterialid?.fseriesid?.fnumber;
            }
            fauxseriesid = ds.fauxseriesid ? ds.fauxseriesid.id : "";//新增有值
            var fauxseriesids = (fauxseriesid || '').trim().split(',').filter(o => that.checkIsresultBrand(o));

            var brandcode = resultSeriesObj.filter(o => o["fnumber"] != "M1" && o["fnumber"] != "Z1");
            var brandcode_M1 = resultSeriesObj.filter(o => o["fnumber"] == "M1");
            //当经销商仅代理了Z2时
            //75244 【250491】 【慕思现场4.10-4.14】新渠道系列拆分Z2 慕思经典-甄选 ，Z5 慕思经典-优选
            var brandcode_Z2 = resultSeriesObj_agent.filter(o => o["fnumber"] == "Z2" || o["fnumber"] == "Z5");

            //【单据类型】= “摆场订单” 时
            var fbilltype = that.Model.getValue({ id: 'fbilltype' });
            //如果是摆场订单 返回的resultSeriesObj为 采购部门对应门店 商品授权表 的仅授权 
            //如果不是摆场订单 返回的resultSeriesObj 为送达方品牌授权 的仅授权；两种情况数据不同但是处理业绩品牌的带出逻辑、锁定逻辑 相同所以不作区分

            //1、如果授权系列 排除 M1、Z1 仅剩A1  则视为仅代理A1
            if (brandcode.length == 1 && brandcode[0].fnumber == "A1") {
                that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: false });
                resultseries = brandcode[0].fserieid;
            }
            //2、如果授权系列 排除 M1、Z1 仅剩Y1  则视为仅代理Y1
            else if (brandcode.length == 1 && brandcode[0].fnumber == "Y1") {
                that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: false });
                resultseries = brandcode[0].fserieid;
            }
            //3、如果授权系列 排除 M1、Z1 为空 则视为仅代理M1
            else if (brandcode_M1 && brandcode_M1 != null && brandcode_M1.length > 0 && brandcode.length == 0) {
                that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: false });
                resultseries = brandcode_M1[0].fserieid;
            }
            //4、其他情况为当前组织代理了 多系列的情况
            else {
                //业绩品牌
                //1. 默认为空
                //2.可以选到 有授权品牌(城市 + 实控人) 不受门店授权的限制
                //3.不可以选到通配品牌(Z1) 或 慕思助眠(M1)  或 慕思经典 - 新渠道(Z2)
                //4.系列增加 "业绩品牌" 标识, 只可以选到勾选"业绩品牌"的系列
                if (series == "Z1" || series == "M1") {
                    that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: true });
                    resultseries = '';
                }
                //正常商品 (非通配品牌 或 非慕思助眠  或 非慕思经典-新渠道)
                else {
                    //如果当前商品系列有授权，则默认带出系列为业绩品牌
                    var seriesid = resultSeriesObj.find(item => item.fnumber === series);
                    if (seriesid) {
                        that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: false });
                        resultseries = seriesid.fserieid;
                    }
                    //否则默认带出商品的附属品牌
                    else {
                        //that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: false });
                        //resultseries = fauxseriesid;
                        //如果附属品牌也没有授权，或者附属品牌为空，则设置为商品系列，不可编辑
                        if (!fauxseriesids || resultSeriesObj.filter(o => fauxseriesids.indexOf(o["fserieid"]) > -1).length == 0 || fauxseriesids.length == 0) {
                            that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: false });
                            resultseries = ds.fseriesid.id;
                        } else {
                            that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: false });
                            var fauxseriesid_has = resultSeriesObj.find(o => fauxseriesids.lastIndexOf(o.fserieid) > -1);
                            if (fauxseriesid_has) resultseries = fauxseriesid_has.fserieid;
                        }
                    }
                }
            }
            //如果系列没有勾选业绩品牌则直接置为空
            if (!that.checkIsresultBrand(resultseries)) {
                resultseries = '';
            }
            that.Model.setValue({ id: 'fresultbrandid', row: srvData.rowid, value: resultseries });
            //如果带出业绩品牌为空 锁定业绩品牌没有意义
            if (resultseries == '') {
                that.Model.setEnable({ id: 'fresultbrandid', row: srvData.rowid, value: true });
            }
        };

        _child.prototype.checkIsresultBrand = function (id) {
            var that = this;
            var IsresultBrand = true;
            if (id == "") return false;
            var param = {
                simpleData: {
                    formId: 'ydj_order',
                    id: id,
                    domainType: 'dynamic'
                }
            };
            yiAjax.p('/bill/ydj_order?operationno=checkisresultbrand', param, function (r) {
                that.Model.unblockUI({ id: '#page#' });
                var res = r.operationResult;
                if (!res.isSuccess) {
                    IsresultBrand = false;
                } else {
                    IsresultBrand = true;
                }
            }, null, null, null, { async: false });
            return IsresultBrand;
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;


            switch (e.opcode) {
                case 'getdeliverbyserieid':
                    if (isSuccess) {

                        if (srvData != null && srvData.length > 1) {
                            that.fdeliveridsBySerieid = srvData;
                            var fsourcetype = that.Model.getValue({ id: 'fsourcetype' });
                            if (fsourcetype.fnumber == "ydj_order") {
                                that.Model.setEnable({ id: 'fdeliverid', value: true });
                            }
                        }
                    }
                    break;
                case 'loadpursysparam':
                    if (isSuccess && srvData) {
                        //设置本地缓存数据
                        var pursysparam = {};
                        for (var data in srvData) {
                            pursysparam[data] = srvData[data];
                        }
                        localStorage.setItem("pursysparam", JSON.stringify(pursysparam));

                        //变更中只允许减少数量 
                        if (that.isCanchangeQty(e)) {
                            that.Model.setEnable({ id: '#btnStandardCustom', value: false });
                            that.Model.setEnable({ id: '#btnUnStandardCustom', value: false });
                            /*that.Model.setEnable({ id: '#btnSuitCustom', value: false });*/
                            that.Model.setEnable({ id: '#tbUnstdTypeAudit', value: false });
                            that.Model.setEnable({ id: '#btnQYG', value: false });
                            that.Model.setEnable({ id: '#btnZCG', value: false });
                            that.Model.setEnable({ id: '#btnCXDB', value: false });
                            that.Model.setEnable({ id: '#btnbindcombnumber', value: false });
                            that.Model.setEnable({ id: '#btnunbindcombnumber', value: false });

                            that.Model.setEnable({ id: 'fdate', value: false });
                            that.Model.setEnable({ id: 'fpickdate', value: false });
                            that.Model.setEnable({ id: 'fdeliveryplandate', value: false });
                            that.Model.setEnable({ id: 'fpostaffid', value: false });
                            that.Model.setEnable({ id: 'fpodeptid', value: false });
                            that.Model.setEnable({ id: 'fsupplieraddr', value: false });
                            that.Model.setEnable({ id: 'ffbillamount', value: false });
                            that.Model.setEnable({ id: 'fdescription', value: true });
                        }
                        //if (storesysparam.hasOwnProperty('fcanwholedis')) {
                        //    that.showOrHideDiscount();
                        //}

                        //// 勾选了[合同收款时允许扣减佣金]的销售管理参数，控制《销售合同》界面显示【已扣佣金】字段 
                        //that.Model.setVisible({ id: '.reducedbrokerage', value: storesysparam.hasOwnProperty('fallowreducebrokerage') && storesysparam.fallowreducebrokerage });

                    }
                    break;
                case 'getpurstorecity':
                    if (isSuccess) {
                        that.storeCity = srvData;
                    }
                    break;
                case 'getproductbyorgid': {//得到门部组织授权商品ID
                    ;
                    if (isSuccess) {
                        that.productids = srvData;
                    }
                    break;
                }
                case 'checkorderresultbrand':
                    ;
                    if (isSuccess & srvData != null) {
                        if (srvData.rowid && srvData.rowid != "") {
                            that.setResultBrandData(srvData);
                        }
                    }
                    break;
                //加载单据类型参数设置
                case 'getparamset':
                    that.billTypeParamSet = srvData || {};
                    that.procLogicByBillType(e);
                    break;
                case 'loadproduct':
                    if ($.isPlainObject(srvData)) {
                        //如果是定制商品，则数量为1，且数量不允许编辑
                        if (srvData.uidata.fiscustom) {
                            that.Model.setValue({ id: 'fqty', row: optData.rowId, value: 1 });
                            that.Model.setEnable({ id: 'fqty', row: optData.rowId, value: false });
                        } else {
                            that.Model.setValue({ id: 'fqty', row: optData.rowId, value: that.Model.getValue({ id: 'fbizqty', row: optData.rowId }) });
                            that.Model.setEnable({ id: 'fqty', row: optData.rowId, value: true });
                        }
                        //创建状态下，允许编辑【成交单价】--产品人:lql,开发:Oscar5,时间:2022-04-05
                        var status = that.Model.getSimpleValue({ id: 'fstatus' });
                        if (status != "") {
                            //商品=自建商品时，需要能够编辑成交单价
                            var fmaterialid = that.Model.getValue({ id: 'fmaterialid', row: optData.rowId });
                            if (fmaterialid.fmainorgid.id == Consts.loginCompany.id) {
                                that.Model.setEnable({ id: 'fdealprice', row: optData.rowId, value: true });
                            } else {
                                that.Model.setEnable({ id: 'fdealprice', row: optData.rowId, value: false });
                            }
                        }
                    }
                    break;
                //列表页面“结算”操作，显示结算对话框
                case 'loadsettleinfo':
                    if (isSuccess && $.isPlainObject(srvData)) {
                        if (srvData.fsettletype === '退款') {
                            that.Model.showForm({
                                formId: 'coo_settledyn',
                                param: { openStyle: Consts.openStyle.modal },
                                cp: $.extend({}, srvData, {
                                    bizFields: [
                                        { id: 'fsourcenumber', cation: '采购订单' },
                                        { id: 'fsettlemainname', cation: '供应商' },
                                        { id: 'fsettledamount', cation: '已结算金额' },
                                    ],
                                    fimage: { id: '', name: '' },
                                    callback: function (result) {
                                        if (result && result.isSuccess) {
                                            that.Model.refresh();
                                        }
                                    }
                                })
                            });
                        } else {
                            that.showSettleDialog(srvData);
                        }
                    }
                    break;
                //列表操作
                case 'submitsynergy':
                case 'repealsynergy':
                case 'pricefirm':
                case 'cancelconfirm':
                    if (that.formContext.domainType === Consts.domainType.list && isSuccess) {
                        that.Model.refresh();
                    }
                    break;
                //
                case 'checkcategory':
                    if (!isSuccess) {
                        yiDialog.warn("当前勾选的商品不为沙发类, 不允许绑定！");
                        return false;
                    }
                    break;
                case 'doaddparts_custom':
                    if (isSuccess && srvData) {
                        //如果匹配到标准品商品id则直接更新商品
                        if (srvData.standardId && srvData.standardId != undefined) {
                            //// 触发获取商品其他信息
                            var row = that.Model.getEntryRowData({ id: that.fentity, row: srvData.rowId });
                            that.Model.setValue({ id: 'fmaterialid', row: srvData.rowId, value: srvData.standardId });
                        } else {
                            if (that.attrinfoNew.length > 0) {
                                //如果将支撑杆改为无，则将同配件号的支撑杆类型明细行删除掉
                                var ds = that.Model.getSelectRows({ id: that.fentity });
                                that.DelEntryByNoting(ds);
                            }
                            if (srvData.length >= 1) {
                                //返回了配件也有可能存在一种情况，之前满足两条配件的条件现在修改属性，只满足了一条记录
                                //最简单的方法先删后生成
                                that.clearOtherParts();
                                //如果包含铁架床 但是配件商品已停产的话 则需要给出提示
                                if (srvData.some(o => o.fmaterialid == "" && o.parttype == "铁架床")) {
                                    yiDialog.c('配件未授权或已停产, 无法进行配套下单, 请与总部联系沟通 ! ! !', function () {
                                        that.Model.deleteRow({ id: that.fentity, row: srvData[0].id });
                                    });
                                    return false;
                                }
                                var TGCParts = srvData.filter(o => o.fmaterialid != "" && o.parttype == "铁架床")
                                //如果满足条件的铁架床配件有多个则需要弹框出来供客户选择
                                if (TGCParts && TGCParts.length > 1) {
                                    var fmaterialList = [];
                                    for (var i = 0; i < srvData.length; i++) {
                                        fmaterialList.push(srvData[i].fmaterialid);
                                    }
                                    that.PartListData = srvData;
                                    var filterString = "fid in ('{0}')"
                                        .format(fmaterialList.join("','"));

                                    var fdeliverid = that.Model.getValue({ "id": "fdeliverid" });
                                    var srcPara = {
                                        deliverid: fdeliverid ? fdeliverid.id : ""
                                    };
                                    that.Model.showSelectForm({
                                        formId: 'ydj_product',
                                        selectMul: false,
                                        dynamicParam: {
                                            filterString: filterString
                                        },
                                        srcPara: JSON.stringify(srcPara)
                                        //simpleData : {
                                        //    srcPara: JSON.stringify(srcPara)
                                        //}
                                    });
                                    return;
                                }

                                for (var a = 0; a < srvData.length; a++) {
                                    var auxEntry = [];
                                    if (srvData[a].attrinfo) {
                                        for (var i = 0; i < srvData[a].attrinfo.length; i++) {
                                            auxEntry.push({
                                                fauxpropid: {
                                                    id: srvData[a].attrinfo[i].fauxpropid,
                                                    fname: srvData[a].attrinfo[i].fname,
                                                    fnumber: srvData[a].attrinfo[i].fnumber
                                                },
                                                fvalueid: srvData[a].attrinfo[i].fvalueid,
                                                fvaluename: srvData[a].attrinfo[i].fvaluename,
                                                fvaluenumber: srvData[a].attrinfo[i].fvaluenumber
                                            });
                                        }
                                    }
                                    var addRowData = {};
                                    //【配件商品编码】【配件商品名称】【配件辅助属性】【数量】
                                    addRowData = {
                                        'fmaterialid': srvData[a].fmaterialid,
                                        'fqty': srvData[a].fqty,
                                        'fbizqty': srvData[a].fqty,
                                        'fpartqty': srvData[a].fqty,
                                    };
                                    var parentrow = that.Model.getEntryRowData({ id: that.fentity, row: srvData[a].id });
                                    var fresultbrandid = parentrow.fresultbrandid.id;
                                    //to do 如果已经添加了【气压杆】配件那么就不再重复生成 气压杆的配件了。
                                    var ds = that.Model.getSelectRows({ id: that.fentity });
                                    if (!that.CheckParts(ds, srvData[a].parttype)) {
                                        continue;
                                    }
                                    var row = that.Model.addRow({ id: that.fentity, row: srvData[a].id, opctx: { ignore: true } });
                                    that.Model.setValue({ id: 'fmaterialid', row: row, value: srvData[a].fmaterialid, opctx: { ignore: true } });
                                    //that.Model.setValue({ id: 'fqty', row: row, value: srvData[a].fqty });
                                    //that.Model.setValue({ id: 'fbizqty', row: row, value: srvData[a].fqty });
                                    that.Model.setValue({ id: 'fpartqty', row: row, value: srvData[a].fqty });
                                    //自动带出配件标识
                                    that.Model.setValue({ id: 'fisautopartflag', row: row, value: 1 });
                                    that.Model.setValue({ id: 'fresultbrandid', row: row, value: fresultbrandid });
                                    // var row = that.Model.addRow({ id: that.fentity, data: addRowData });

                                    //// 触发获取商品其他信息
                                    that.Model.setValue({
                                        id: 'fmaterialid', row: row, value: {
                                            id: srvData[a].fmaterialid,
                                            fnumber: srvData[a].fnumber,
                                            fname: srvData[a].fname,
                                        }, opctx: { ignore: true }
                                    });
                                    //Isunstdtype：非标定制 没找到配件时 根据主商品型号 获取型号档案中配置的床箱底板商品
                                    if (srvData[a].Isunstdtype && srvData[a].Isunstdtype == "1") {
                                        that.Model.setValue({ id: 'funstdtype', row: row, value: true });
                                    }
                                    var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                                    //对业务单据辅助属性字段设置
                                    that.Model.setValue(auxPropArgs);
                                    //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                                    var Newid = yiCommon.uuid(32);
                                    /* var parentrow = that.Model.getEntryRowData({ id: that.fentity, row: srvData[0].id });*/
                                    //如果父商品 已经有了组合号就不必再生成组合号
                                    if (parentrow && parentrow.fpartscombnumber) {
                                        Newid = parentrow.fpartscombnumber;
                                    } else {
                                        Newid = yiCommon.uuid(32);
                                        that.Model.setValue({ id: 'fpartscombnumber', row: srvData[0].id, value: Newid });
                                        //给主商品打上主商品标识 区分
                                        that.Model.setValue({ id: 'fiscombmain', row: srvData[0].id, value: 1 });
                                    }
                                    //如果父商品有套件组合号也加入到套件组合里
                                    if (parentrow && parentrow.fsuitcombnumber) {
                                        that.Model.setValue({ id: 'fsuitcombnumber', row: srvData[a].id, value: parentrow.fsuitcombnumber });
                                    }
                                    that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                                    that.Model.setValue({ id: 'fparttype', row: row, value: srvData[a].parttype });
                                    //根据主商品的数量计算配件数量
                                    that.calculatePartQty(parentrow.fbizqty, parentrow.id);
                                    //如果是床箱底板配件且允许定制，则需要在此明细记录定制说明：
                                    if (srvData[a].parttype == "床箱底板" && srvData[a].Iscustom) {
                                        //获取主商品辅助属性
                                        var mainrow = that.Model.getEntryRowData({ id: that.fentity, row: parentrow.id });
                                        var attrinfo = mainrow.fattrinfo.fentity;
                                        var part = attrinfo.find(o => o.fauxpropid.fname == "床架其它定制");
                                        if (part) {
                                            if (parentrow.fseltypeid && parentrow.fseltypeid.fname != undefined) {
                                                var str = "床架型号:" + parentrow.fseltypeid.fname + ";床架其它定制：" + part.fvaluename;
                                                that.Model.setValue({ id: 'fcustomdes_e', row: row, value: str });
                                            }
                                        }
                                    }
                                }
                            }
                            else if (srvData.length == 0) {
                                that.clearOtherParts();
                            }
                        }
                    }
                    break;
                //添加配件
                case 'doaddparts':
                    if (isSuccess) {
                        //直接将放回的配件添加到明细行
                        if (srvData && srvData.length == 1) {
                            var auxEntry = [];
                            if (srvData[0].attrinfo) {
                                for (var a = 0; a < srvData[0].attrinfo.length; a++) {
                                    auxEntry.push({
                                        fauxpropid: {
                                            id: srvData[0].attrinfo[a].fauxpropid,
                                            fname: srvData[0].attrinfo[a].fname,
                                            fnumber: srvData[0].attrinfo[a].fnumber
                                        },
                                        fvalueid: srvData[0].attrinfo[a].fvalueid,
                                        fvaluename: srvData[0].attrinfo[a].fvaluename,
                                        fvaluenumber: srvData[0].attrinfo[a].fvaluenumber
                                    });
                                }
                            }
                            var addRowData = {};
                            //【配件商品编码】【配件商品名称】【配件辅助属性】【数量】
                            addRowData = {
                                'fmaterialid': srvData[0].fmaterialid,
                                'fqty': srvData[0].fqty,
                                'fbizqty': srvData[0].fqty
                            };
                            var parentrow = that.Model.getEntryRowData({ id: that.fentity, row: srvData[0].id });
                            var fresultbrandid = parentrow.fresultbrandid;
                            var row = that.Model.addRow({ id: that.fentity, row: srvData[0].id, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fmaterialid', row: row, value: srvData[0].fmaterialid });
                            //that.Model.setValue({ id: 'fqty', row: row, value: srvData[0].fqty });
                            //that.Model.setValue({ id: 'fbizqty', row: row, value: srvData[0].fqty });
                            that.Model.setValue({ id: 'fpartqty', row: row, value: srvData[0].fqty });
                            that.Model.setValue({ id: 'fresultbrandid', row: row, value: fresultbrandid });
                            //var row = that.Model.addRow({ id: that.fentity, data: addRowData });

                            //// 触发获取商品其他信息
                            that.Model.setValue({
                                id: 'fmaterialid', row: row, value: {
                                    id: srvData[0].fmaterialid,
                                    fnumber: srvData[0].fnumber,
                                    fname: srvData[0].fname,
                                }
                            });
                            var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                            //对业务单据辅助属性字段设置
                            that.Model.setValue(auxPropArgs);
                            //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                            var Newid = yiCommon.uuid(32);
                            //如果父商品 已经有了组合号就不必再生成组合号
                            if (parentrow.fpartscombnumber) {
                                Newid = parentrow.fpartscombnumber;
                            } else {
                                Newid = yiCommon.uuid(32);
                                that.Model.setValue({ id: 'fpartscombnumber', row: srvData[0].id, value: Newid });
                                //给主商品打上主商品标识 区分
                                that.Model.setValue({ id: 'fiscombmain', row: srvData[0].id, value: 1 });
                            }
                            that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                            that.Model.setValue({ id: 'fparttype', row: row, value: srvData[0].parttype });
                            //根据主商品的数量计算配件数量
                            that.calculatePartQty(parentrow.fbizqty, parentrow.id);
                        }
                        //如果是多个配件的话，需要弹出商品选择框
                        else if (srvData && srvData.length > 1) {
                            var fmaterialList = [];
                            for (var i = 0; i < srvData.length; i++) {
                                fmaterialList.push(srvData[i].fmaterialid);
                            }
                            that.PartListData = srvData;
                            var filterString = "fid in ('{0}')"
                                .format(fmaterialList.join("','"));

                            var fdeliverid = that.Model.getValue({ "id": "fdeliverid" });
                            var srcPara = {
                                deliverid: fdeliverid ? fdeliverid.id : ""
                            };
                            that.Model.showSelectForm({
                                formId: 'ydj_product',
                                selectMul: false,
                                dynamicParam: {
                                    filterString: filterString
                                },
                                srcPara: JSON.stringify(srcPara)
                                //simpleData: {
                                //    srcPara: JSON.stringify(srcPara)
                                //}
                            });
                        }
                        else {
                            yiDialog.warn('当前商品没有可添加的配件！');
                            return false;
                        }
                    }
                    break;

                case 'getprices':
                    if (e.id == 'init' || e.id == 'change' || e.id == 'changeall') {
                        if (!srvData) {
                            //yiDialog.mt({msg:' 无相关查询数据', skinseq: 2});
                            return;
                        }
                        var isHqPrice = e.opctx && e.opctx.isHqPrice;
                        var str = '';
                        var gridData = that.Model.getValue({ id: that.fentity });
                        var pull = e.opctx && e.opctx.option && e.opctx.option.pull; //是否选单后触发的取价

                        ;

                        for (var i = 0, l = srvData.length; i < l; i++) {
                            var lm = srvData[i];
                            var rowData = {};
                            var num = 0;
                            for (var n = 0, m = gridData.length; n < m; n++) {
                                if (gridData[n].id == lm.clientId) {//获取对于行
                                    rowData = gridData[n];
                                    num = n + 1;
                                }
                            }

                            var fsourceentryid_e = $.trim(that.Model.getSimpleValue({ id: 'fsourceentryid_e', row: lm.clientId }));

                            if (lm.PriceMsg) {
                                str += "第{0}行{1}".format(num, lm.PriceMsg);
                            }


                            if (lm.success) {//价格匹配成功，则赋值
                                //当count大于1时，提示用户" 第{client}行，{productName},有{count}个项匹配，请谨慎使用匹配的数据" 价目表上获取不到商品名的，前端获取。

                                if (pull) {

                                    //前端价格为0的时候才用后端返回的价格（避免手工录入的价格被覆盖）
                                    var price = that.Model.getValue({ id: 'fprice', row: lm.clientId });
                                    if (price < 1) {
                                        //如果是后来勾选的非标，价格应该为0,未勾选非标的才走原逻辑
                                        if (!rowData.funstdtype) {
                                            that.Model.setValue({ id: 'fprice', row: lm.clientId, value: lm.purPrice });
                                        }
                                    }
                                    var salPrice = that.Model.getValue({ id: 'fsalprice', row: lm.clientId });
                                    if (salPrice < 1 && fsourceentryid_e.length <= 0) {
                                        that.Model.setValue({ id: 'fsalprice', row: lm.clientId, value: lm.salPrice });
                                    }

                                } else {

                                    if (isHqPrice) {
                                        that.Model.setValue({ id: 'fhqretailprice', row: lm.clientId, value: lm.salPrice, tgChange: false });
                                    } else {
                                        //	最匹配的销售价(零售价)赋值  ，非自备料，即使查到了零售价数据，也不赋值
                                        if (!rowData.funstdtype) {
                                            that.Model.setValue({ id: 'fprice', row: lm.clientId, value: lm.purPrice });
                                            that.Model.setValue({ id: 'fdealprice', row: lm.clientId, value: lm.purPrice });
                                        }

                                        if (fsourceentryid_e.length <= 0) {
                                            that.Model.setValue({ id: 'fsalprice', row: lm.clientId, value: lm.salPrice });
                                        }
                                    }
                                }

                                if (lm.count > 1) {
                                    var theName = rowData.fproductid && rowData.fproductid.fname;
                                    str += '第{0}行，{1},有{2}个项匹配 <br/>'.format(num, theName, lm.count);
                                }


                            } else {//价格匹配不成功
                                //yiDialog.mt({msg:'价格匹配不成功', skinseq: 2});
                                // 价格匹配不成功，则赋值为0
                                if (isHqPrice) {
                                    that.Model.setValue({ id: 'fhqretailprice', row: lm.clientId, value: 0, tgChange: false });
                                } else {
                                    if (e.id == 'change' || e.id == 'changeall') {//查不到数据，就赋值为0

                                        if (!pull) {

                                            that.Model.setValue({ id: 'fprice', row: lm.clientId, value: 0 });
                                            that.Model.setValue({ id: 'fdealprice', row: lm.clientId, value: 0 });
                                            //str+='第{0}行数据无相关查询数据<br/>'.format(num,theName,lm.count);

                                            if (fsourceentryid_e.length <= 0) {
                                                that.Model.setValue({ id: 'fsalprice', row: lm.clientId, value: 0 });
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (str.length > 0) {
                            yiDialog.warn(str);
                        }

                        //当用户登录的组织，对应的经销商档案【大客户渠道】=是，采购订单的慕思商品价格只能由慕思中台下发赋值，因此，需做如下处理：
                        //①当手工新增采购订单，选择慕思总部商品时，不允许依据总部采购价目表带出价格。
                        //注意：若选择的是非慕思自建商品，允许依据当前组织的自建采购价目表带出价格。
                        if (that.isParentProduct) {
                            that.Model.setValue({ id: 'fdealprice', row: lm.clientId, value: 0 });
                            that.Model.setValue({ id: 'fprice', row: lm.clientId, value: 0 });
                        }


                    } else if (e.id == 'onPriceSerch') {

                        if (srvData && srvData[0] && that.alertModel.Model) {

                            that.alertModel.Model.setValue({ id: 'fprice', value: srvData[0].purPrice });

                            var fsourceentryid_e = $.trim(that.alertModel.Model.getSimpleValue({ id: 'fsourceentryid_e' }));
                            if (fsourceentryid_e.length <= 0) {
                                that.alertModel.Model.setValue({ id: 'fsalprice', value: srvData[0].salPrice });
                            }
                        }
                        //当用户登录的组织，对应的经销商档案【大客户渠道】=是，采购订单的慕思商品价格只能由慕思中台下发赋值，因此，需做如下处理：
                        //①当手工新增采购订单，选择慕思总部商品时，不允许依据总部采购价目表带出价格。
                        //注意：若选择的是非慕思自建商品，允许依据当前组织的自建采购价目表带出价格。
                        if (that.isParentProduct) {
                            that.Model.setValue({ id: 'fdealprice', row: lm.clientId, value: 0 });
                            that.Model.setValue({ id: 'fprice', row: lm.clientId, value: 0 });
                        }
                    }

                    break;
                case 'getsupplierbydeliverid':
                    if (isSuccess) {
                        that.Model.setValue({ id: 'fsupplierid', value: srvData });
                    }
                    break;
                case 'getsuitedata':
                    that.updateSuiteDesc(e);
                    break;
                //case 'isexsitbarcode':
                //    //if (!isSuccess) {
                //    //    yiDialog.warn("当前商品未查询到对应条码信息！");
                //    //    return false;
                //    //}
                //    //弹出《条码主档》列表界面
                //    
                //    var filterString = "fmainorgid = '{0}' and fid in ({1})"
                //        .format(Consts.loginCompany.id, srvData.split(','));
                //    that.Model.showForm({
                //        formId: 'bcm_barcodemaster',
                //        domainType: Consts.domainType.list,
                //        param: {
                //            openStyle: Consts.openStyle.modal,
                //            filterstring: filterString
                //        }
                //    });
                //    break;
                case 'isexistpackorder':
                    //已经存在对应的《包装清单》时, 将对应的《包装清单》在弹出的视窗中打开表单
                    if (isSuccess) {
                        var fbillno = $.trim(that.Model.getSimpleValue({ id: 'fbillno' }));
                        var filterString = " fsourcetype ='{0}' and fsourcenumber='{1}' and fmainorgid = '{2}'"
                            .format('ydj_purchaseorder', fbillno, Consts.loginCompany.id);
                        that.Model.showForm({
                            formId: 'bcm_packorder',
                            domainType: Consts.domainType.list,
                            param: {
                                openStyle: Consts.openStyle.modal,
                                filterstring: filterString
                            }
                        });
                    } else {
                        //不存在对应的《包装清单》时, 系统自动弹出全新的《包装清单》新增界面,
                        that.Model.invokeFormOperation({
                            id: 'push',
                            opcode: 'push',
                            param: {
                                'ruleId': "ydj_purchaseorder2bcm_packorder"
                            }
                        });
                    }
                    break;
                case 'tgcpartbystandard':
                    that.DoTGCparts(e);
                    break;
                case 'save':
                    var simpleMessage = e.result.operationResult.optionData.autoSave;
                    if (simpleMessage == "autosubmit" || simpleMessage == "autoaudit") {

                        that.showSalePromotionDialog(e);
                        if (srvData != null && srvData != '' && srvData != 'undefind') {
                            e.result = true;
                        }
                    }
                    break;
                case 'submitflow':
                    if (that.Model.viewModel.domainType != 'list') {
                        if (isSuccess) {

                            var pageId = e.result.pageId;
                            that.showSalePromotionDialog(e);
                            if (srvData != null && srvData != '' && srvData != 'undefind') {
                                e.result = true;
                            }
                        }
                    }

                    break;
                case 'stockup':
                    if (isSuccess) {
                        var OrderType = "1";
                        that.swjsinglesignon(OrderType);
                    }
                    break;
                case 'swingfield':
                    if (isSuccess) {
                        var OrderType = "2";
                        that.swjsinglesignon(OrderType);
                    }
                    break;
                case 'checkpromotion':

                    var data = e.result;
                    var message = "";
                    if (srvData == null) {
                        e.result = true;
                        return;
                    }
                    debugger
                    //message = srvData.mes;
                    //if (that.formContext.domainType == "list") {
                    //    message = srvData.mes.join('\r\n');
                    //} else {
                    message = srvData.mes ? (srvData.mes.length > 0 ? srvData.mes[0] : "") : "";
                    //}
                    if (isSuccess && srvData.code == "10000" && that.formContext.domainType == "list") {
                        //列表上操作时，失败的提示，正常的自动提交
                        var pkvalue = [];
                        var successid = srvData.successid;
                        if (successid.length == 0) {
                            e.result = true;
                            return;
                        }
                        for (var i = 0; i < successid.length; i++) {
                            pkvalue.push({
                                PKValue: successid[i]
                            })
                        }
                        that.Model.invokeFormOperation({
                            id: 'submithq',
                            opcode: 'submithq',
                            billData: JSON.stringify([that.Model.uiData]),
                            selectedRows: pkvalue,
                            param: {
                            }
                        });
                        return;
                    } else if (isSuccess && srvData.code == "10000" && that.formContext.domainType != "list") {
                        that.Model.invokeFormOperation({
                            id: 'submithq',
                            opcode: 'submithq',
                            billData: JSON.stringify([that.Model.uiData]),
                            selectedRows: [{ PKValue: that.Model.pkid }],
                            param: {
                            }
                        });
                        return;
                    }
                    else if (!isSuccess && (srvData.code == "10002" || srvData.code == "10003")) {
                        if (srvData.code == "10002" && that.formContext.domainType != "list") {
                            //e.result = true;

                            //var successid = srvData.successid;
                            //做提示
                            yiDialog.c(message, function () {
                                //确定继续提交，提交总部

                                that.Model.invokeFormOperation({
                                    id: 'submithq',
                                    opcode: 'submithq',
                                    billData: JSON.stringify([that.Model.uiData]),
                                    selectedRows: [{ PKValue: that.Model.pkid }],
                                    param: {
                                    }
                                });
                                return;
                            }, function () {
                                //不提交，返回
                                e.result = true;
                                return;
                            });
                        } else if (srvData.code == "10002" && that.formContext.domainType == "list") {
                            e.result = true;
                            //列表上操作时，失败的提示，正常的自动提交
                            var pkvalue = [];
                            var successid = srvData.successid;
                            if (successid.length == 0) {
                                e.result = true;
                                return;
                            }
                            for (var i = 0; i < successid.length; i++) {
                                pkvalue.push({
                                    PKValue: successid[i]
                                })
                            }
                            that.Model.invokeFormOperation({
                                id: 'submithq',
                                opcode: 'submithq',
                                billData: JSON.stringify([that.Model.uiData]),
                                selectedRows: pkvalue,
                                param: {
                                }
                            });
                            return;
                        } else {
                            //异常
                            e.result = true;
                            yiDialog.warn("数据返回异常，请检查数据！");
                            return;
                        }
                    }
                    else {//不提交，数据异常
                        e.result = true;
                        //不出现因为促销问题导致的提交失败
                        //that.Model.invokeFormOperation({
                        //    id: 'submithq',
                        //    opcode: 'submithq',
                        //    billData: JSON.stringify([that.Model.uiData]),
                        //    selectedRows: [{ PKValue: that.Model.pkid }],
                        //    param: {
                        //    }
                        //});
                        //yiDialog.warn(message);
                        return;
                    }
                    break;
                case 'savecombineinfo':
                    if (that.formContext.status != 'new') {
                        that.ischangepromotion = false;
                        return;
                    }
                    if (isSuccess && srvData.length > 0 && that.formContext.status == 'new') {

                        var parentrows = that.Model.getEntryData({ id: that.fentity });

                        for (var i = 0; i < parentrows.length; i++) {
                            var fproduct1 = parentrows[i].fmaterialid;
                            if (!fproduct1 || $.trim(fproduct1.id) == "") {
                                that.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });
                            }
                        }
                        for (var i = 0; i < srvData.length; i++) {
                            for (var j = 0; j < parentrows.length; j++) {
                                if (i == j && srvData[i].fmaterialid == parentrows[j].fmaterialid.id) {
                                    that.Model.setValue({ id: "fbizqty", row: parentrows[j].id, value: srvData[i].fbizqty, opctx: { ignore: true, iscombine: true } });//采购数量
                                    that.Model.setValue({ id: "fpromotion", row: parentrows[j].id, value: srvData[i].fpromotion }); //促销活动名称
                                    that.Model.setValue({ id: "fcombinenumber", row: parentrows[j].id, value: srvData[i].fcombinenumber }); //组合促销编号
                                    that.Model.setValue({ id: 'fcombineremark', row: parentrows[j].id, value: srvData[i].fcombineremark });//组合描述
                                    that.Model.setValue({ id: 'fcombinepriority', row: parentrows[j].id, value: srvData[i].fcombinepriority });//促销活动优先级
                                    that.Model.setValue({ id: 'fcombinerate', row: parentrows[j].id, value: srvData[i].fcombinerate });//折扣率
                                    that.Model.setValue({ id: "fcombineqty", row: parentrows[j].id, value: srvData[i].fcombineqty });//套餐组合基数
                                    that.Model.setValue({ id: "fgroupnumber", row: parentrows[j].id, value: srvData[i].fgroupnumber });//套餐组合基数
                                    break;
                                }
                            }
                        }
                        //for (var i = 0; i < srvData.length; i++) {
                        //    var row = parentrows.filter(c => c.fmaterialid.id == srvData[i].fmaterialid && c.fsoorderno == "");
                        //    if (row != undefined && row.length > 0) {
                        //        if (srvData[i].fgroupnumber == 0) {
                        //            that.Model.setValue({ id: "fpromotion", row: row[0].id, value: srvData[i].fpromotion }); //促销活动名称
                        //            that.Model.setValue({ id: "fcombinenumber", row: row[0].id, value: srvData[i].fcombinenumber }); //组合促销编号
                        //            that.Model.setValue({ id: 'fcombineremark', row: row[0].id, value: srvData[i].fcombineremark });//组合描述
                        //            that.Model.setValue({ id: 'fcombinepriority', row: row[0].id, value: srvData[i].fcombinepriority });//促销活动优先级
                        //            that.Model.setValue({ id: 'fcombinerate', row: row[0].id, value: srvData[i].fcombinerate });//折扣率
                        //            that.Model.setValue({ id: "fcombineqty", row: row[0].id, value: srvData[i].fcombineqty });//套餐组合基数
                        //            that.Model.setValue({ id: "fgroupnumber", row: row[0].id, value: srvData[i].fgroupnumber });//套餐组合基数
                        //        } else {
                        //            that.Model.setValue({ id: "fbizqty", row: row[0].id, value: srvData[i].fbizqty, opctx: { ignore: true } });//采购数量
                        //            that.Model.setValue({ id: "fpromotion", row: row[0].id, value: srvData[i].fpromotion }); //促销活动名称
                        //            that.Model.setValue({ id: "fcombinenumber", row: row[0].id, value: srvData[i].fcombinenumber }); //组合促销编号
                        //            that.Model.setValue({ id: 'fcombineremark', row: row[0].id, value: srvData[i].fcombineremark });//组合描述
                        //            that.Model.setValue({ id: 'fcombinepriority', row: row[0].id, value: srvData[i].fcombinepriority });//促销活动优先级
                        //            that.Model.setValue({ id: 'fcombinerate', row: row[0].id, value: srvData[i].fcombinerate });//折扣率
                        //            that.Model.setValue({ id: "fcombineqty", row: row[0].id, value: srvData[i].fcombineqty });//套餐组合基数
                        //            that.Model.setValue({ id: "fgroupnumber", row: row[0].id, value: srvData[i].fgroupnumber });//套餐组合基数
                        //        }
                        //    }
                        //}
                        var addRows = [];
                        if (srvData.length > parentrows.length) {
                            //拆行
                            for (var i = srvData.length; i > 0; i--) {
                                if (i > parentrows.length) {
                                    let index = i - 1;
                                    if (srvData[index].fmaterialid) {
                                        var exists = parentrows.filter(a =>
                                            a.fmaterialid == srvData[index].fmaterialid &&
                                            a.fcombinenumber == "" &&
                                            a.fresultbrandid == srvData[index].fresultbrandid &&
                                            a.fsourceinterid == srvData[index].fsourceinterid);
                                        if (exists.length > 0) {
                                            that.Model.setValue({ id: 'fbizqty', row: exists[0].id, value: parseInt(exists[0].fbizqty) + parseInt(srvData[index].fbizqty) });
                                        } else {
                                            var rowitem = {
                                                "fmaterialid": srvData[index].fmaterialid,
                                                "fresultbrandid": srvData[index].fresultbrandid,
                                                "fgoodschanneltype": srvData[index].fgoodschanneltype,
                                                "fsoorderno": srvData[index].fsoorderno,
                                                "forderentryid": srvData[index].forderentryid,
                                                "fsourceformid": "ydj_order",
                                                "fsourcebillno": srvData[index].fsourcebillno,
                                                "fsourceinterid": srvData[index].fsourceinterid,
                                                "fsoorderentryid": srvData[index].fsoorderentryid,
                                                "fsoorderinterid": srvData[index].fsoorderinterid,
                                                "fbizqty": srvData[index].fbizqty,
                                                "fattrinfo": srvData[index].fattrinfo,
                                                "fsaledeptid": srvData[index].fsaledeptid,
                                                "fcustomer": srvData[index].fcustomer,
                                                "fphone": srvData[index].fphone,
                                                "fentrystaffid": srvData[index].fentrystaffid,
                                                "fpartscombnumber": srvData[index].fpartscombnumber,
                                                "fpartqty": srvData[index].fpartqty,
                                                "fparttype": srvData[index].fparttype,
                                                "fiscombmain": srvData[index].fiscombmain
                                            };
                                            addRows.unshift(rowitem);
                                        }
                                    }
                                }
                            }
                        }
                        else {
                            var nocombines = srvData.filter(a => a.fcombinenumber == "");
                            for (var i = 0; i < nocombines.length; i++) {
                                var exists = parentrows.filter(a =>
                                    a.fmaterialid.id == nocombines[i].fmaterialid &&
                                    a.fcombinenumber == "" &&
                                    a.fresultbrandid.id == nocombines[i].fresultbrandid &&
                                    a.fsourceinterid == nocombines[i].fsourceinterid);
                                if (exists.length > 0) {
                                    that.Model.setValue({ id: 'fbizqty', row: exists[0].id, value: parseInt(nocombines[i].fbizqty) });
                                }
                            }
                        }
                        for (var i = 0; i < addRows.length; i++) {
                            var rowno = that.Model.addRow({ id: that.fentity, opctx: { ignore: true } });
                            that.Model.setValue({ id: "fmaterialid", row: rowno, value: addRows[i].fmaterialid, opctx: { ignore: true, iscombine: true } });
                            that.Model.setValue({ id: 'fresultbrandid', row: rowno, value: addRows[i].fresultbrandid });//业绩品牌
                            that.Model.setValue({ id: 'fgoodschanneltype', row: rowno, value: addRows[i].fgoodschanneltype });//渠道类型
                            that.Model.setValue({ id: 'fsoorderno', row: rowno, value: addRows[i].fsoorderno });//销售合同编号
                            that.Model.setValue({ id: 'forderentryid', row: rowno, value: addRows[i].forderentryid });
                            that.Model.setValue({ id: 'fsourceformid', row: rowno, value: addRows[i].fsourceformid });
                            that.Model.setValue({ id: 'fsourcebillno', row: rowno, value: addRows[i].fsourcebillno });
                            that.Model.setValue({ id: 'fsourceinterid', row: rowno, value: addRows[i].fsourceinterid });
                            that.Model.setValue({ id: 'fsoorderentryid', row: rowno, value: addRows[i].fsoorderentryid });
                            that.Model.setValue({ id: 'fsoorderinterid', row: rowno, value: addRows[i].fsoorderinterid });
                            that.Model.setValue({ id: 'fbizqty', row: rowno, value: addRows[i].fbizqty, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fattrinfo', row: rowno, value: addRows[i].fattrinfo, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fsaledeptid', row: rowno, value: addRows[i].fsaledeptid, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fcustomer', row: rowno, value: addRows[i].fcustomer, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fphone', row: rowno, value: addRows[i].fphone, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fentrystaffid', row: rowno, value: addRows[i].fentrystaffid, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fpartscombnumber', row: rowno, value: addRows[i].fpartscombnumber, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fpartqty', row: rowno, value: addRows[i].fpartqty, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fparttype', row: rowno, value: addRows[i].fparttype, opctx: { ignore: true } });
                            that.Model.setValue({ id: 'fiscombmain', row: rowno, value: addRows[i].fiscombmain, opctx: { ignore: true } });
                        }
                    }

                    that.ischangepromotion = false;
                    break;
                case 'partslistquery':
                    if (isSuccess && srvData) {
                        var url = srvData;
                        console.log(url);
                        if (url == null || url.trim() == "") {
                            yiDialog.error("获取地址失败，请检查相关数据！");

                        } else {
                            //that.openWin(url, "查询总部余额", window.innerWidth * 0.8, window.innerHeight * 0.9);
                            var conent = '<iframe src="{0}" style="width:100%;height:100%;border:none;"></iframe>'.format(url);
                            $("#test").html(conent)
                            yiDialog.d({
                                id: 'partslistquery',
                                type: 1,
                                closeBtn: 1,
                                resize: true,
                                content: conent,
                                title: '部件清单查询',
                                area: ['80%', '90%']
                            });
                        }
                    }

                    break;
            }
        };
        //调用三维家单点登录接口
        _child.prototype.swjsinglesignon = function (OrderType) {
            var that = this;

            var redObj = {
                "OrderType": OrderType,
                "Source": '金蝶'
            }
            var url = '/swjapi/sso',
                param = {
                    custdata: JSON.stringify(redObj)
                };
            //请求企业数据
            yiAjax.p(url, param, function (r) {
                if (r.success) {
                    window.open(r.data);
                }
            });
        }

        _child.prototype.onCompleteDoOperation = function (e) {
            var that = this;
            ;
            switch (e.opcode) {
                case 'loadproduct':
                case 'getprices':
                case 'checkorderresultbrand':
                case 'checkcategory':
                case 'doaddparts_custom':
                case 'doaddparts':
                case 'getsuitedata':
                case 'tgcpartbystandard':
                    var entryDatas = that.Model.getEntryData({ id: that.fentity });
                    if (entryDatas) {
                        //判断最后一行是否空行
                        var lastRowIsEmpty = false;
                        var rowCount = entryDatas.length;
                        var matId = entryDatas[rowCount - 1].fmaterialid.id;
                        if (!matId) {
                            lastRowIsEmpty = true;
                        }
                        if (!lastRowIsEmpty) {
                            //新增一行空行
                            that.Model.addRow({ id: that.fentity, opctx: { ignore: true } });
                        }
                        //将滚动条拉至最下面
                        var entryHtml = that.Model.getEleMent({ id: 'fentity', way: Consts.eleFetchWay.id });
                        if (entryHtml.length > 0) {
                            var entryHtmlToArray = Array.from(entryHtml);
                            for (var i = 0; i < entryHtmlToArray.length; i++) {
                                var childs = entryHtmlToArray[i].querySelectorAll(".slick-viewport-right");
                                if (childs.length > 0) {
                                    for (var j = 0; j < childs.length; j++) {
                                        if (childs[j].scrollTop != childs[j].scrollHeight) {
                                            childs[j].scrollTop = childs[j].scrollHeight;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (e.opcode == 'tgcpartbystandard') {


                        if (e.opctx && e.opctx.childPageId) {
                            var childPage = Index.getPage(e.opctx.childPageId);
                            if (childPage) {
                                childPage.plugInProxy.invoke("SetTimes", e);
                            }
                        }
                    }
                    break;
            }
        };

        _child.prototype.DoTGCparts = function (r) {
            var that = this;
            var res = r.result.operationResult;
            var srvData = res.srvData;
            if (res.isSuccess) {
                if (srvData.length >= 1) {
                    //如果包含铁架床 但是配件商品已停产的话 则需要给出提示
                    if (srvData.some(o => o.fmaterialid == "" && o.parttype == "铁架床")) {
                        yiDialog.c('配件未授权或已停产, 无法进行配套下单, 请与总部联系沟通 ! ! !', function () {
                            that.Model.deleteRow({ id: that.fentity, row: srvData[0].id });
                        });
                        return false;
                    }
                    var TGCParts = srvData.filter(o => o.fmaterialid != "" && o.parttype == "铁架床")
                    //如果满足条件的铁架床配件有多个则需要弹框出来供客户选择
                    if (TGCParts && TGCParts.length > 1) {
                        var fmaterialList = [];
                        for (var i = 0; i < srvData.length; i++) {
                            fmaterialList.push(srvData[i].fmaterialid);
                        }
                        that.PartListData = srvData;
                        var filterString = "fid in ('{0}')"
                            .format(fmaterialList.join("','"));

                        var fdeliverid = that.Model.getValue({ "id": "fdeliverid" });
                        var srcPara = {
                            deliverid: fdeliverid ? fdeliverid.id : ""
                        };
                        that.Model.showSelectForm({
                            formId: 'ydj_product',
                            selectMul: false,
                            dynamicParam: {
                                filterString: filterString
                            },
                            srcPara: JSON.stringify(srcPara)
                        });
                        return;
                    }

                    for (var a = 0; a < srvData.length; a++) {
                        var auxEntry = [];
                        if (srvData[a].attrinfo) {
                            for (var i = 0; i < srvData[a].attrinfo.length; i++) {
                                auxEntry.push({
                                    fauxpropid: {
                                        id: srvData[a].attrinfo[i].fauxpropid,
                                        fname: srvData[a].attrinfo[i].fname,
                                        fnumber: srvData[a].attrinfo[i].fnumber
                                    },
                                    fvalueid: srvData[a].attrinfo[i].fvalueid,
                                    fvaluename: srvData[a].attrinfo[i].fvaluename,
                                    fvaluenumber: srvData[a].attrinfo[i].fvaluenumber
                                });
                            }
                        }
                        //【配件商品编码】【配件商品名称】【配件辅助属性】【数量】
                        addRowData = {
                            'fmaterialid': srvData[a].fmaterialid,
                            'fqty': srvData[a].fqty,
                            'fbizqty': srvData[a].fqty,
                            'fpartqty': srvData[a].fqty,
                        };
                        var parentrow = that.Model.getEntryRowData({ id: that.fentity, row: srvData[a].id });
                        var fresultbrandid = parentrow.fresultbrandid.id;
                        var row = that.Model.addRow({ id: that.fentity, row: srvData[a].id, opctx: { ignore: true } });
                        that.Model.setValue({ id: 'fmaterialid', row: row, value: srvData[a].fmaterialid, opctx: { ignore: true } });
                        that.Model.setValue({ id: 'fpartqty', row: row, value: srvData[a].fqty });
                        //自动带出配件标识
                        that.Model.setValue({ id: 'fisautopartflag', row: row, value: 1 });
                        that.Model.setValue({ id: 'fresultbrandid', row: row, value: fresultbrandid });
                        //// 触发获取商品其他信息
                        that.Model.setValue({
                            id: 'fmaterialid', row: row, value: {
                                id: srvData[a].fmaterialid,
                                fnumber: srvData[a].fnumber,
                                fname: srvData[a].fname,
                            }, opctx: { ignore: true }
                        });
                        var auxPropArgs = { id: 'fattrinfo', row: row, value: { fentity: auxEntry } };
                        //对业务单据辅助属性字段设置
                        that.Model.setValue(auxPropArgs);
                        //将原本商品行与新增的配件 设置同一【配件组合号】进行关联
                        var Newid = yiCommon.uuid(32);
                        //如果父商品 已经有了组合号就不必再生成组合号
                        if (parentrow && parentrow.fpartscombnumber) {
                            Newid = parentrow.fpartscombnumber;
                        } else {
                            Newid = yiCommon.uuid(32);
                            that.Model.setValue({ id: 'fpartscombnumber', row: srvData[0].id, value: Newid });
                            //给主商品打上主商品标识 区分
                            that.Model.setValue({ id: 'fiscombmain', row: srvData[0].id, value: 1 });
                        }
                        //如果父商品有套件组合号也加入到套件组合里
                        if (parentrow && parentrow.fsuitcombnumber) {
                            that.Model.setValue({ id: 'fsuitcombnumber', row: srvData[a].id, value: parentrow.fsuitcombnumber });
                        }
                        //如果父商品有套件组合号也加入到套件组合里
                        if (parentrow && parentrow.fcombinenumber) {

                            that.Model.setValue({ id: "fpromotion", row: row, value: parentrow.fpromotion }); //促销活动名称
                            that.Model.setValue({ id: "fcombinenumber", row: row, value: parentrow.fcombinenumber }); //组合促销编号
                            that.Model.setValue({ id: 'fcombineremark', row: row, value: parentrow.fcombineremark });//组合描述
                            that.Model.setValue({ id: 'fcombinepriority', row: row, value: parentrow.fcombinepriority });//促销活动优先级
                            that.Model.setValue({ id: 'fcombinerate', row: row, value: parentrow.fcombinerate });//折扣率
                            that.Model.setValue({ id: "fcombineqty", row: row, value: parentrow.fcombineqty });//套餐组合基数

                            //that.Model.setValue({ id: 'fsuitcombnumber', row: srvData[a].id, value: parentrow.fsuitcombnumber });
                        }
                        that.Model.setValue({ id: 'fpartscombnumber', row: row, value: Newid });
                        that.Model.setValue({ id: 'fparttype', row: row, value: srvData[a].parttype });
                        //根据主商品的数量计算配件数量
                        that.calculatePartQty(parentrow.fbizqty, parentrow.id);
                    }
                }
            }
        };

        //如果没返回数据 即修改属性使其不满足标准品带出商品条件，需要删除之前已经带出过的配件
        _child.prototype.clearOtherParts = function () {
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.fentity });
            if (ds.length > 0) {
                var rowid = ds[0].data.id;
                var fpartscombnumber = ds[0].data.fpartscombnumber,
                    fiscombmain = ds[0].data.fiscombmain,
                    fisautopartflag = ds[0].data.fisautopartflag;
                var entryData = that.Model.getEntryData({ id: that.fentity });
                var rows = entryData.filter(e => e.fpartscombnumber == fpartscombnumber && e.id != rowid && !e.fiscombmain && e.fisautopartflag);
                var rowall = entryData.filter(e => e.fpartscombnumber == fpartscombnumber && e.id != rowid && !e.fiscombmain);
                var delRow = [];
                for (var i = 0, j = rows.length; i < j; i++) {
                    //找出同配件编码 且设置 配件类型为无的配件
                    delRow.push(rows[i].id);
                }
                //避免删除后0条分录的操作
                if (delRow.length == entryData.length) {
                    that.Model.addRow({ id: that.fentity, opctx: { ignore: true } });
                }
                for (var i = 0; i < delRow.length; i++) {
                    var row = delRow[i];
                    that.Model.deleteRow({ id: that.fentity, row: row, opctx: { ignore: true } });
                }
                //如果所有的配件都是自动带出的 删除完配件后要将主商品配件组合号置为空
                if (rowall.length == rows.length) {
                    that.Model.setValue({ id: 'fpartscombnumber', row: ds[0].data.id, value: '' });
                }
            }
        };

        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            switch (e.id.toLowerCase()) {
                case that.fentity:
                    if (that.isCanchangeQty(e)) {
                        e.result = true;
                        yiDialog.mt({ msg: '向总部采购变更, 不允许新增商品, 如果需采购新商品, 请创建新采购订单向总部采购！', skinseq: 2 });
                        return;
                    }

                    //采购订单总部手工单商品行不允许操作新增&修改&删除动作，仅能通过接口对接做数据更新；
                    var billtype = that.Model.getSimpleValue({ "id": "fbilltypeid" });
                    if (billtype == 'ydj_purchaseorder_zb') {
                        e.result = true;
                        if (Consts.isdirectsale) {
                            return;
                        }
                        yiDialog.mt({ msg: '总部手工单不允许<新增&修改&删除>商品行!', skinseq: 3 });
                        return;
                    }

                    //采购订单总部手工单商品行不允许操作新增&修改&删除动作，仅能通过接口对接做数据更新；
                    var fpiecesendtag = that.Model.getValue({ "id": "fpiecesendtag" });
                    if (fpiecesendtag) {
                        e.result = true;
                        yiDialog.mt({ msg: '当【一件代发标记】=是，不允许新增商品行!', skinseq: 3 });
                        return;
                    }
                    var fiswaterredlinefail = that.Model.getValue({ "id": "fiswaterredlinefail" });
                    if (fiswaterredlinefail) {
                        e.result = true;
                        yiDialog.mt({ msg: '当前采购订单已经因库存水位线状态不符条件提交总部失败，不允许新增商品行或者增加采购数量，谢谢！', skinseq: 3 });
                        return;
                    }
                    break;
            }
        };

        //判断是否勾选“向总部采购变更时, 只允许减少数量” 且《采购订单》表头的【变更状态】=”变更中” 且 表头的【供应商】对应《供应商》的销售组织不为空
        _child.prototype.isCanchangeQty = function (e) {
            var that = this;
            var CanchangeQty = false;
            var fchangestatus = that.Model.getSimpleValue({ id: 'fchangestatus' });
            var purSysParam = JSON.parse(localStorage.getItem("pursysparam"));
            if (purSysParam) {
                if (fchangestatus == "1") {
                    var fsupplierid = that.Model.getValue({ id: "fsupplierid" });
                    if (fsupplierid && fsupplierid.forgid && fsupplierid.forgid.id) {
                        if (purSysParam.fallowchangeqty === undefined || purSysParam.fallowchangeqty == true) {
                            CanchangeQty = true;
                        }
                    }
                }
            }
            return CanchangeQty;
        };

        //根据单据类型处理一些业务逻辑
        _child.prototype.procLogicByBillType = function (e) {
            var that = this;
            //显示或隐藏图纸信息
            var isUploadDraw = false;
            var customerRequired = false;
            if (that.billTypeParamSet) {
                isUploadDraw = that.billTypeParamSet.fisuploaddraw === true;
                customerRequired = that.billTypeParamSet.fcustomerrequired === true;
                //设置默认供应商
                var supplier = that.billTypeParamSet.fsupplierid;
                if (supplier && $.trim(supplier.id) && !$.trim(that.Model.pkid)) {
                    that.Model.setValue({ id: 'fsupplierid', value: supplier });
                }
                if (e.opctx.typeChange || !$.trim(that.Model.pkid)) {
                    var enableNotice = that.billTypeParamSet.fenablenotice === true;
                    that.Model.setValue({ id: 'fenablenotice', value: enableNotice });
                }
                var bizType = that.billTypeParamSet.fbiztype && that.billTypeParamSet.fbiztype.id ? $.trim(that.billTypeParamSet.fbiztype.id) : "";
                switch (bizType) {
                    case "biztype_01":
                        that.Model.setValue({ "id": 'ftype', value: "order_type_01" });
                        break;
                    case "biztype_02":
                        that.Model.setValue({ "id": 'ftype', value: "order_type_02" });
                        break;
                    default:
                        that.Model.setValue({ "id": 'ftype', value: "order_type_01" });
                        break;
                }

            }
            // if (!isUploadDraw && !$.trim(that.Model.pkid)) {
            //     var drawData = that.Model.getEntryData({ id: that.fdentity });
            //     drawData.length = 0;
            //     that.Model.refreshEntry({ id: that.fdentity });
            // }
            // that.Model.setVisible({ id: '.draw-info', value: isUploadDraw });
            that.Model.resizeEntryWidth({ id: that.fdentity });

        };

        //商品明细改变时：重新计算明细
        _child.prototype.productEntryChange = function (opt) {
            var that = this;

            //行对象
            var row = that.Model.getEntryRowData({ id: that.fentity, row: opt.rowId });

            //数量
            var qty = yiMath.toNumber(row.fbizqty);

            //单价
            var price = yiMath.toNumber(row.fprice);

            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;

            //折扣额
            var distAmount = yiMath.toNumber(row.fdistamount_e);

            //折扣率
            var distRate = yiMath.toNumber(row.fdistrate);

            //如果当前修改的是“折扣额”，则自动反算“折扣率”
            if (opt.name === 'fdistamount_e') {
                //折扣率 =（表体折扣额 - 表体金额）/（-1 * 表体金额）
                if ((-1 * amount) !== 0) {
                    distRate = (distAmount - amount) / (-1 * amount);
                    distRate *= that.defdis;
                }
            }
            distRate = distRate > 0 ? distRate : that.defdis;

            //如果当前修改的不是“折扣额”，则根据折扣率计算“折扣额”，否则以用户输入的“折扣额”为准
            if (opt.name !== 'fdistamount_e') {
                //表体折扣额 = 表体金额 -（(表体金额 * 表体折扣率）/ 10)
                distAmount = amount - ((amount * distRate) / that.defdis);
            }

            //表体成交单价默认等于单价
            var dealPrice = price, dealAmount = 0;

            //表体成交单价 =（表体金额 - 表体折扣额）/ 表体数量
            if (qty !== 0) {
                dealPrice = (amount - distAmount) / qty;
            }

            //表体成交金额 = 表体数量 * 表体成交单价
            dealAmount = qty * dealPrice;

            dealPrice = isNaN(dealPrice) ? 0 : dealPrice;
            dealAmount = isNaN(dealAmount) ? 0 : dealAmount;

            //更新字段值
            that.Model.setValue({ id: 'famount', row: opt.rowId, value: amount });
            that.Model.setValue({ id: 'fdistrate', row: opt.rowId, value: yiMath.toDecimal(distRate, 6) });
            that.Model.setValue({ id: 'fdistamount_e', row: opt.rowId, value: distAmount });
            that.Model.setValue({ id: 'fdealprice', row: opt.rowId, value: dealPrice });
            that.Model.setValue({ id: 'fdealamount_e', row: opt.rowId, value: dealAmount });

            //计算结算信息
            that.calculateSettlement();
        };

        //计算结算信息
        _child.prototype.calculateSettlement = function () {
            var that = this,
                ds = that.Model.getEntryData({ id: that.fentity }),
                billAmount = 0;
            if (ds) {
                for (var i = 0; i < ds.length; i++) {
                    if (ds[i].fcooeditstatus !== "3") {
                        billAmount += yiMath.toNumber(ds[i].fdealamount_e);
                    }
                }
            }
            that.Model.setValue({ id: 'ffbillamount', value: billAmount, tgChange: false });
        };

        // 非标审核
        _child.prototype.unstdTypeAudit = function () {
            var that = this;
            var seledtedRows = that.Model.getSelectRows({ id: that.fentity });
            if (seledtedRows.length === 0) {
                yiDialog.mt({ msg: '请选择商品行再操作！', skinseq: 2 });
                return;
            }

            var orderId = that.Model.uiData.id;
            if (!orderId) {
                yiDialog.mt({ msg: '请先保存后再操作！', skinseq: 2 });
                return;
            }

            var rowIds = [];

            for (var i = 0; i < seledtedRows.length; i++) {
                rowIds.push(seledtedRows[i].pkid);
            }

            that.Model.invokeFormOperation({
                id: 'UnstdTypeAudit',
                opcode: 'UnstdTypeAudit',
                param: {
                    'formId': 'ydj_purchaseorder',
                    'id': orderId,
                    'entryIds': rowIds.join(",")
                }
            });
        };

        // 删除商品行前事件
        _child.prototype.deletingProductRow = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;

            if (that.isCanchangeQty(e)) {
                e.result = true;
                yiDialog.mt({ msg: '向总部采购变更, 不允许删除商品行, 如果该商品无需采购时, 请进行关闭！', skinseq: 2 });
                return;
            }
            if (e.opctx) {
                if (e.opctx.promotion) {
                    return;
                }
            }

            //采购订单总部手工单商品行不允许操作新增&修改&删除动作，仅能通过接口对接做数据更新；
            var billtype = that.Model.getSimpleValue({ "id": "fbilltypeid" });
            if (billtype == 'ydj_purchaseorder_zb') {
                e.result = true;
                if (Consts.isdirectsale) {
                    return;
                }
                yiDialog.mt({ msg: '总部手工单不允许<新增&修改&删除>商品行!', skinseq: 3 });
                return;
            }

            //采购订单总部手工单商品行不允许操作新增&修改&删除动作，仅能通过接口对接做数据更新；
            var fpiecesendtag = that.Model.getValue({ "id": "fpiecesendtag" });
            if (fpiecesendtag) {
                e.result = true;
                yiDialog.mt({ msg: '当【一件代发标记】=是，不允许删除商品行!', skinseq: 3 });
                return;
            }


            var entryData = that.Model.getEntryData({ id: that.fentity });
            var sofacombnumber = that.Model.getValue({ id: 'fsofacombnumber', row: e.row });
            if (sofacombnumber) {
                //e.result = true;
                //yiDialog.mt({ msg: '当前商品未进行解绑沙发组合号, 不允许删除 ! ! !', skinseq: 2 });
                //return;
                e.confirmMessage = '删除的当前商品行为沙发组合商品，关联的沙发组合商品将会一并删除，请确认是否一起删除？ (您也可以尝试解绑后单独删除)？';
            }

            var combinenumber = that.Model.getValue({ id: 'fcombinenumber', row: e.row });
            if (combinenumber) {
                e.confirmMessage = '\<div>当前商品为组合促销商品, 如果删除该商品会将其他相同套餐组合的商品一并删除, 是否继续删除 ?</br>\
                                    <input type="radio" name="deleteproduct" value="删除商品" checked="checked">删除商品\
                                    <input type="radio" name="deleteproduct" value="删除促销套餐" >删除促销套餐\
                                    </div >';
            }

            var suitcombnumber = that.Model.getValue({ id: 'fsuitcombnumber', row: e.row });
            //是否套件商品
            var issuit = that.Model.getValue({ id: 'fissuitflag', row: e.row });
            //如果有套件组合号但不是套件商品，说明是套件的子件商品，不允许删除
            if (suitcombnumber && !issuit) {
                e.result = true;
                yiDialog.mt({ msg: '当前商品行为子件商品不允许删除，请在套件选配界面中进行调整！', skinseq: 2 });
                return;
            }
            var fpartscombnumber = that.Model.getValue({ id: 'fpartscombnumber', row: e.row });
            //如果是通过选配自动携带的配件 不允许删除
            var fisautopartflag = that.Model.getValue({ id: 'fisautopartflag', row: e.row });
            if (fpartscombnumber && fisautopartflag) {
                e.result = true;
                yiDialog.mt({ msg: '当前配件通过选配自动携带不允许删除，请在选配界面中进行调整！', skinseq: 2 });
                return;
            }
            var delRow = [];
            if (suitcombnumber && entryData.some(item => item.id === e.row)) {
                //【套件组合号】不为空时
                // if (that.FirstDelete) {
                e.confirmMessage = '删除的当前商品行为套件商品，关联的子件商品将会一并删除，请确认是否整套删除？';
                // }
            }
            var fiscombmain = that.Model.getValue({ id: 'fiscombmain', row: e.row });
            if (fpartscombnumber && fiscombmain && entryData.some(item => item.id === e.row)) {
                delRow = [];
                //if (that.FirstDelete) {
                e.confirmMessage = '删除的当前商品行存在配件商品，关联的配件商品将会一并删除，请确认是否整套删除？';
                //}
            }
            ////下推的采购订单明细不允许删除
            //var sourceEntryId = $.trim(that.Model.getValue({ id: 'fsourceentryid_e', row: e.row }));
            //if (sourceEntryId) {
            //    e.result = true;
            //    yiDialog.warn('下推的商品明细不允许删除！');
            //    return;
            //}
            //采购订单，发布状态  fpublishstatus=='publish_status_02' 的时候，全部锁住
            var publishStatus = $.trim(that.Model.getSimpleValue({ id: 'fpublishstatus' }));
            var chargebackStatus = $.trim(that.Model.getSimpleValue({ id: 'fchargebackstatus' }));
            if (publishStatus === 'publish_status_02' && chargebackStatus === '0') {
                e.result = true;
                yiDialog.mt({ msg: '已经建立协同关系，不允许删除商品明细！', skinseq: 2 });
                return;
            }
        };

        // 检查是否需要更新套件描述
        _child.prototype.checkSuiteDesc = function (e) {
            var that = this;
            var rowdata = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            if (rowdata.fsuitcombnumber) { //原行有套件组合码，需要更新套件说明
                that.Model.invokeFormOperation({
                    id: 'getsuitedata',
                    opcode: 'getsuitedata',
                    param: {
                        'formId': 'ydj_order',
                        'domainType': 'parameter',
                        'fsuitcombnumber': rowdata.fsuitcombnumber,
                        'fsuitproductid': rowdata.fsuitproductid.id
                    }
                });
            }
        };

        // 更新是否需要更新套件描述
        _child.prototype.updateSuiteDesc = function (e) {
            ;
            var that = this;
            if (e.result.operationResult.isSuccess) {
                var fsuitcombnumber = e.result.operationResult.srvData.fsuitcombnumber;
                var suite = e.result.operationResult.srvData.suite;
                var propSuiteCount = eval("(" + e.result.operationResult.srvData.propsuitecount + ")");
                var entries = that.Model.getEntryData({ id: that.fentity });

                if (!entries) return;

                var suiteRow = {};
                var partRows = [];

                for (var i = 0; i < entries.length; i++) {
                    var r = entries[i];
                    if (r.fsuitcombnumber == fsuitcombnumber) {
                        if (r.fissuitflag) {
                            suiteRow = r;
                        } else {
                            partRows.push(r);
                        }
                    }
                }

                var groupObj = {};
                var prex = "gp_";
                var sumQty = 0;
                partRows.map(function (x) {
                    sumQty += x.fsubqty;
                    var partprop = "";
                    if (suite && suite.fentity && suite.fentity.length) {
                        for (var i = 0; i < suite.fentity.length; i++) {
                            if (x.fmaterialid.id == suite.fentity[i].fpartproductid) {
                                partprop = suite.fentity[i].fpartprop_ref;
                                break;
                            }
                        }
                    }
                    if (!partprop) return;
                    var gKey = prex + partprop.fname;
                    if (!groupObj[gKey]) {
                        groupObj[gKey] = {
                            fauxpropid: {
                                id: partprop.id,
                                fname: partprop.fname,
                                fnumber: partprop.fnumber
                            },
                            fvalueid: 0,
                            fvaluename: 0,
                            fvaluenumber: 0
                        }
                    }
                    var gValue = groupObj[gKey].fvalueid || 0;
                    gValue = gValue + x.fqty;
                    groupObj[gKey].fvalueid = gValue;
                    groupObj[gKey].fvaluename = gValue;
                    groupObj[gKey].fvaluenumber = gValue;
                });
                var propSuiteCountValue = {
                    fauxpropid: {
                        id: propSuiteCount.id,
                        fname: propSuiteCount.fname,
                        fnumber: propSuiteCount.fnumber
                    },
                    fvalueid: sumQty,
                    fvaluename: sumQty,
                    fvaluenumber: sumQty
                }

                var suiteAuxEntry = [propSuiteCountValue];
                for (var p in groupObj) {
                    if (p.startsWith(prex)) {
                        suiteAuxEntry.push(groupObj[p]);
                    }
                }
                that.Model.setValue({ id: 'fattrinfo', row: suiteRow.id, value: { fentity: suiteAuxEntry } });
            }
        }

        // 非标价格校验
        _child.prototype.checkUnstdPrice = function (e) {
            var that = this;
            var productEntry = that.Model.getEntryData({ id: that.fentity });
            if (productEntry == null || productEntry.length <= 0) {
                return;
            }

            var orderItem = {
                id: that.Model.pkid,
                fentity: []
            };
            for (var i = 0; i < productEntry.length; i++) {
                var funstdtype = productEntry[i].funstdtype;
                if (!funstdtype) continue;

                var funstdtypestatus = productEntry[i].funstdtypestatus;
                if (funstdtypestatus == null || funstdtypestatus.id != "02") continue;

                var fprice = productEntry[i].fprice;
                if (fprice > 0) continue;

                var id = productEntry[i].id
                var entryitem = {
                    id: id,
                    funstdtype: funstdtype,
                    funstdtypestatus: funstdtypestatus.id,
                    fprice: fprice
                };
                orderItem.fentity.push(entryitem);
            }

            // 有符合条件的明细行时，新增参数，用于判断校验
            if (orderItem.fentity.length > 0) {
                var data = [];
                data.push(orderItem);
                e.param.unstdData = JSON.stringify(data);
            }
        };

        //表格快粘贴前触发
        _child.prototype.onGridBeforePaste = function (e) {
            ;
            switch (e.fieldKey.toLowerCase()) {
                case "fbizunitid"://采购单位
                    var funitname = e.rowData.funitid.fname;
                    if (funitname && funitname != e.clipboardText.trim()) {
                        yiDialog.warn('【采购单位】需与【基本单位】保持一致！');
                        e.cancel = true;
                    }
                    break;
            }
        };

        // 获取经销商是否勾选大客户渠道和商品是否是总部商品
        _child.prototype.getParentProduct = function (e) {

            var that = this;
            var rowdata = that.Model.getEntryRowData({ id: that.fentity, row: e.row });
            if (rowdata.fmaterialid != null) {
                var parm = {
                    simpledata: {
                        fmaterialid: rowdata.fmaterialid.id,
                    }
                };
                yiAjax.p('/bill/ydj_purchaseorder?operationno=getparentproduct', parm, function (r) {
                    if (r) {
                        if (r.operationResult.srvData) {
                            that.isParentProduct = r.operationResult.srvData["isParentProduct"];
                        }
                    }
                }, null, null, null, { async: false });
            }
        };


        //设置一件代发显示隐藏
        _child.prototype.setDropShipMentVisible = function () {

            var that = this;
            debugger
            var flag = false;
            var fomsservice = that.Model.getValue({ id: "fomsservice" });
            if (fomsservice) {
                flag = false;
            } else {
                var parm = {
                };
                yiAjax.p('/bill/ydj_order?operationno=getagentcustomchannel', parm, function (r) {
                    if (r) {
                        if (r.operationResult?.srvData) {
                            var fcustomchannel = r.operationResult.srvData["fcustomchannel"];
                            if (fcustomchannel == "1") {
                                flag = true;
                            }
                        }
                    }
                }, null, null, null, { async: false });
            }

            that.Model.setVisible({ id: '.dropshipping-info-tag', value: flag });//一口价


            var fpiecesendtag = that.Model.getValue({ id: "fpiecesendtag" });
            if (fpiecesendtag) {
                var rows = that.Model.getEntryData({ id: that.fentity });
                for (var i = 0; i < rows.length; i++) {
                    that.PieceInitEntry.push({ id: rows[i].id, fsuitcombnumber: rows[i].fsuitcombnumber, fsofacombnumber: rows[i].fsofacombnumber, fname: rows[i].fmaterialid.fname, fbizqty: rows[i].fbizqty });
                }
            }
        }

        _child.prototype.showChangeApplyFormList = function (e) {
            var that = this;
            that.Model.showForm({
                formId: 'ydj_purchaseorderapply_chg',
                domainType: Consts.domainType.list,
                param: {
                    openStyle: Consts.openStyle.modal,
                    filterstring: "fsourceid='" + that.Model.pkid + "'"
                }
            });
        }

        //显示或者隐藏发起变更申请按钮
        _child.prototype.showOrHideInitiateChangeAapplyBtn = function () {
            var that = this;
            var isSecondOrg = Consts.isSecondOrg;
            if (!isSecondOrg) {
                that.Model.setVisible({ id: '[menu=initiatechangeapply]', value: false });
            }

        }
        //设置查询一级总部停产及自建商品库存按钮显示隐藏
        _child.prototype.setQueryFirstInventoryVisible = function () {
            var that = this;
            var flag = false;
            if (Consts.isSecondOrg) {
                yiAjax.p('/bill/ydj_purchaseorder?operationno=getagentcanqueryinventory', {}, function (r) {
                    if (r) {
                        if (r.operationResult?.srvData) {
                            var fisqueryhqdisproductinventory = r.operationResult.srvData["fisqueryhqdisproductinventory"];
                            if (fisqueryhqdisproductinventory == "1") {
                                flag = true;
                            }
                        }
                    }
                }, null, null, null, { async: false });
            }
            that.Model.setVisible({ id: '#tbQueryFirstInventory', value: flag });//一口价
        }

        //设置变更按钮显示隐藏
        _child.prototype.setChangeBtnVisible = function () {
            var that = this;
            if (Consts.isSecondOrg) {
                //变更按钮
                that.Model.setVisible({ id: '[menu=change]', value: false });
                that.Model.setVisible({ id: '[menu=submitchange]', value: false });
                that.Model.setVisible({ id: '[menu=unchange]', value: false });
            }
        }


        //设置直营相关显示隐藏
        _child.prototype.setDirectSale = function () {
            var that = this;
            if (Consts.isdirectsale) {
                that.Model.setVisible({ id: '[menu=pushfeedback]', value: true });
                that.Model.setVisible({ id: '.storeid', value: true });
                //that.Model.setEnable({ id: 'fsupplierid', value: true });
                if (that.Model.getSimpleValue({ id: "fbilltypeid" }) == 'ydj_purchaseorder_zb') {
                    setTimeout(function () {
                        that.setFieldMustFlag({ id: "fstoreid", caption: "门店名称", must: false });
                        //that.setFieldMustFlag({ id: "fsupplierid", caption: "供应商", must: false });
                    }, 500);
                } else {
                    setTimeout(function () {
                        that.setFieldMustFlag({ id: "fstoreid", caption: "门店名称", must: true });
                    }, 500);
                }
            } else {
                that.Model.setVisible({ id: '[menu=pushfeedback]', value: false });
                that.Model.setVisible({ id: '.storeid', value: false });
                setTimeout(function () {
                    that.setFieldMustFlag({ id: "fstoreid", caption: "门店名称", must: false });
                }, 500);
            }
        }
        //设置必录标签
        _child.prototype.setFieldMustFlag = function (e) {
            var that = this;
            var elem = that.Model.getEleMent({ id: '[name=' + e.id + ']' });
            if (elem) {
                var $label = elem.parent().parent().siblings('.control-label');
                if ($label) {
                    if (e.must) {
                        that.Model.setAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html('<span class="required">*</span>' + e.caption);
                    } else {
                        that.Model.removeAttr({ id: e.id, random: 'required', value: 'required', way: 2 });
                        $label.html(e.caption);
                    }
                }
            }
        };

        return _child;
    })(BasePlugIn);
    window.ydj_purchaseorder = window.ydj_purchaseorder || ydj_purchaseorder;
})();