//<reference path="/fw/js/platform/mvvm/baseplugin.js" />
//@ sourceURL=/fw/js/sec/sec_datarange.js
; (function () {

    var ydj_purchaseorder_promotion = (function (_super) {

        var _child = function (args) {
            var self = this;
            this.fbillno; this.fcity; this.fresultbrandid; this.fdeliverid; this.parentpageid; this.combineid; this.fbilltypeid; this.purorderid;
            this.times;
            this.combine = [];
            this.parentid = [];
            _super.call(self, args);
        };

        __extends(_child, _super);

        var orderInfo = [];
        //表格选中行变化时触发
        _child.prototype.SetTimes = function (e) {
            var that = this;

            that.times = parseInt(that.times) + 1;
        }

        //表格选中行变化时触发
        _child.prototype.onSelectedRowsChanged = function (e) {
            ;
            var that = this;
            if (e.id == 'fproductentry') {

                //切换子件时，先结束右边表格的编辑状态（这样就会触发非标值的字段值变化事件）
                that.Model.endEditEntry({ id: "forderentry" });
                if (e.data.length == 0) return;
                var prod = e.data[0].fproductid;
                var fbillno = e.data[0].fbillnumber;
                //var forderentry = orderInfo.find(o => o.fproductid == prod && o.fbillno == fbillno);
                //if (forderentry != undefined) {
                //    that.initProp(e.data[0].id, forderentry);
                //}
                var selectRows = that.Model.getSelectRows({ id: "fproductentry" });
                if (selectRows.length == 0) {
                    that.Model.deleteEntryData({ id: "forderentry" });
                    return;
                }
                that.Model.deleteEntryData({ id: "forderentry" });
                for (var i = 0; i < selectRows.length; i++) {
                    var saleOrderItem = orderInfo.find(o => o.id == selectRows[i].data.forderid);
                    //设置组合商品行
                    that.Model.addRow({ id: "forderentry", data: saleOrderItem });
                }
                that.Model.refreshEntry({ id: 'forderentry' });
            }
        };

        //初始化商品
        _child.prototype.initProp = function (fentityrow, orderentity) {
            var that = this;
            var entity = that.Model.getEntryRowData({ id: "fproductentry", row: fentityrow });
            if (!entity) return;

            //清空组合商品行
            that.Model.deleteEntryData({ id: "forderentry" });
            //设置组合商品行
            that.Model.addRow({ id: "forderentry", data: orderentity });

            that.Model.refreshEntry({ id: 'forderentry' });
        };


        //初始化动态表单插件
        _child.prototype.onInitialized = function (args) {
            ;
            var that = this;
            that.times = 0;
            that.fcity = that.formContext.cp.fcity;
            that.fproductid = that.formContext.cp.fproductid;
            that.combineid = that.formContext.cp.combineid;
            that.fsourcenumber = that.formContext.cp.fsourcenumber;
            that.parentpageid = that.Model.viewModel.cp.parentPageId;
            that.combineProduct = that.Model.viewModel.cp.combineProduct;
            that.fdeliverid = that.Model.viewModel.cp.fdeliverid;
            that.fbilltypeid = that.Model.viewModel.cp.fbilltypeid;
            that.purorderid = that.Model.viewModel.cp.purorderid;



            //兼容多个单据，通过单据里设置
            var parentPage = Index.getPage(that.parentpageid);
            that.Model.selsettings = parentPage.Model.selsettings;
            //已经转采购了，不允许再次绑定销售合同的数据
            //if (that.fsourcenumber == "") {
            //填充未采购销售合同
            FillPurChaseInfo();
            //}

            //填充促销合同数据
            FillCombineInfo();

            function FillPurChaseInfo() {
                that.Model.blockUI({ id: '#page#' });
                var param = {
                    simpleData: {
                        //fcity: that.fcity == "" ? "" : that.fcity.id,
                        fproductid: that.fproductid == "" ? "" : that.fproductid,
                        //fdeliverid: that.fdeliverid,
                        //fbilltypeid: that.fbilltypeid,
                        purorderid: that.purorderid
                    }
                };


                yiAjax.p('/bill/ydj_purchaseorder?operationno=showpurchaseorder', param, function (r) {
                    that.Model.unblockUI({ id: '#page#' });
                    srvData = r.operationResult.srvData;

                    that.Model.deleteEntryData({ id: 'fproductentry' });
                    if (srvData.combineProduct != null) {
                        for (var i = 0; i < srvData.combineProduct.length; i++) {
                            var data = srvData.combineProduct[i];

                            that.Model.addRow({ id: 'fproductentry', data: data });
                        }
                        if (srvData.orderInfo.length > 0) {
                            var order = srvData.orderInfo[srvData.orderInfo.length - 1];
                            that.Model.addRow({ id: 'forderentry', data: order });
                            orderInfo = srvData.orderInfo;
                        }
                    }
                }, null, null, null, { async: false });
            }

            function FillCombineInfo() {

                that.Model.blockUI({ id: '#page#' });
                var combineid = that.combineid;
                var paramcombine = {
                    simpleData: {
                        combineid: combineid,
                        purorderid: that.purorderid
                    }
                };

                yiAjax.p('/bill/ydj_purchaseorder?operationno=showcombineinfo', paramcombine, function (r) {
                    that.Model.unblockUI({ id: '#page#' });
                    srvData = r.operationResult.srvData;
                    that.Model.deleteEntryData({ id: 'fbdfldentity' });

                    for (var i = 0; i < srvData.length; i++) {
                        that.combine.push(srvData[i]);
                    }
                    that.updateCombineQty(that.parentid);

                    for (var i = 0; i < that.combine.length; i++) {
                        that.Model.addRow({ id: 'fbdfldentity', data: that.combine[i] });
                    }

                    var rows = that.Model.getEntryData({ id: "fbdfldentity" });
                    var row = [];
                    for (var i = 0; i < rows.length; i++) {
                        if (that.combine[i].fpurorderqty > 0) {
                            row.push(rows[i].id);
                            that.Model.setEnable({ id: "", value: false, row: rows[i].id });
                        }
                    }

                    that.Model.setSelectRows({ id: 'fbdfldentity', rows: row });

                }, null, null, null, { async: false });
            }

            $("#fedit_tab").removeClass("active");
        };

        //_child.prototype.onGetEditorState = function (e) {
        //    var that = this;
        //    var qty = $.trim(that.Model.getSimpleValue({ id: 'fpurorderqty', row: e.row }));
        //    if (qty > 0) {
        //        e.result.enabled = false;
        //    }
        //}


        //表单元素单击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id) {
                case 'check':
                    that._check = that.Model.getEleMent({ id: '.checked' });
                    that._class = that.Model.getAttr({ id: that._check[0].children, random: 'class' });
                    if (this.field[this._class] && this.field[this._class][0]) {
                        var data = this.field[this._class][0];
                        this.Model.setValue({ id: this._class, value: { fname: data.name, fnumber: data.name, id: data.id } });
                    }
                    break;
            }
            //阻止事件冒泡
            e.e.stopPropagation();
            e.e.preventDefault();
        };


        //旧的未采购订单
        //_child.prototype.GetOrderProduct = function (fbillno) {
        //    var that = this;
        //    var param = {
        //        simpleData: {
        //            fbillno: fbillno
        //        }
        //    };
        //    yiAjax.p('/bill/ydj_order?operationno=getorderentrybyno', param, function (r) {
        //        that.Model.unblockUI({ id: '#page#' });
        //        var srvData = r.operationResult.srvData;
        //        for (var i = 0; i < srvData.length; i++) {
        //            var parentPage = Index.getPage(that.parentpageid);
        //            var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
        //            parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: srvData[i] });//促销商品
        //        }
        //    }, null, null, null, { async: false });
        //};

        //未采购订单的商品明细
        _child.prototype.OrderProduct = function (dsProduct) {
            ;
            var that = this;
            var muchcombine = [];
            for (var i = 0; i < that.combineProduct.length; i++) {
                if (muchcombine.indexOf(that.combineProduct[i].combinenumber) < 0) {
                    muchcombine.push(that.combineProduct[i].combinenumber);
                }
            }

            var newdsProduct = [];

            if (muchcombine.length > 0) {
                //不循环加，只加一行
                for (var i = 0; i < 1; i++) {
                    var currentCombineAll = that.combine.filter(c => c.fcombinenumber == muchcombine[i]);
                    if (dsProduct.length > 0) {
                        //在明细行添加数据
                        that.saveCombineInfoByOrder(currentCombineAll, dsProduct);

                        for (var j = 0; j < dsProduct.length; j++) {
                            var combinefilter = currentCombineAll.find(c => c.fproductid == dsProduct[j].data.fproductid);
                            if (combinefilter != undefined) {
                                //newcombine.push({ fproductid: dsProduct[j].fproductid, combine: muchcombine[i] });
                            } else {
                                newdsProduct.push(dsProduct[i]);
                            }
                        }
                        dsProduct = newdsProduct;
                    }
                }
                that.resetsort();
            }
        };

        _child.prototype.resetsort = function () {
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var rows = parentPage.Model.getEntryData({ id: "fentity" });
            rows.sort(function (a, b) {
                if (a.fsofacombnumber !== b.fsofacombnumber) {
                    return a.fsofacombnumber.localeCompare(b.fsofacombnumber);
                }
                else if (a.fcombinenumber !== b.fcombinenumber) {
                    if (a.fcombinenumber < b.fcombinenumber) {
                        return 1;
                    }
                    if (a.fcombinenumber > b.fcombinenumber) {
                        return -1;
                    }
                }
                else if (a.fpartscombnumber !== b.fpartscombnumber) {
                    return a.fpartscombnumber.localeCompare(b.fpartscombnumber)
                }
                else if (a.fiscombmain !== b.fiscombmain) {
                    return b.fiscombmain - a.fiscombmain;
                }
                else if (a.fsuitcombnumber !== b.fsuitcombnumber) {
                    return a.fsuitcombnumber.localeCompare(b.fsuitcombnumber)
                }
                else if (a.fissuitflag !== b.fissuitflag) {
                    return b.fissuitflag - a.fissuitflag;
                } else {
                    return 1;
                }
            });

            parentPage.Model.refreshEntry({ id: "fentity" });
        };

        _child.prototype.GetOrderProduct = function (currentCombineAll, dsProduct) {
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });


            for (var i = 0; i < parentrows.length; i++) {

                var fproduct1 = parentrows[i].fmaterialid;
                if (!fproduct1 || $.trim(fproduct1.id) == "") {
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id });
                }
            }

            //先判断子组号
            var combinenumber = [];
            var number = [];
            for (var j = 0; j < currentCombineAll.length; j++) {
                let index = combinenumber.indexOf(currentCombineAll[j].fgroupnumber)
                if (index < 0) {
                    combinenumber.push(currentCombineAll[j].fgroupnumber);
                }
            }

            var newProduct = [];
            for (var i = 0; i < dsProduct.length; i++) {
                var combineprod = currentCombineAll.find(c => c.fproductid == dsProduct[i].data.fproductid);
                if (combineprod != undefined) {
                    let index = number.indexOf(combineprod.fgroupnumber)
                    if (index < 0) {
                        number.push(combineprod.fgroupnumber);
                    }
                }

                var prodplus = newProduct.find(o => o.productid == dsProduct[i].data.fproductid);
                if (prodplus == undefined) {

                    if (combineprod != undefined) {
                        newProduct.push({ productid: dsProduct[i].data.fproductid, fqty: dsProduct[i].data.fbizqty, fbaseqty: combineprod.fbaseqty });
                    }
                } else {
                    prodplus.fqty += dsProduct[i].data.fbizqty;
                }
            }

            for (var i = 0; i < parentrows.length; i++) {
                var compfind = currentCombineAll.find(o => o.fproductid == parentrows[i].fmaterialid.id);
                if (compfind != undefined) {
                    var prodplus = newProduct.find(o => o.productid == compfind.fproductid);
                    if (prodplus == undefined) {
                        newProduct.push({ productid: parentrows[i].fmaterialid.id, fqty: parentrows[i].fbizqty, fbaseqty: compfind.fbaseqty });
                    } else {
                        prodplus.fqty += parentrows[i].fbizqty;
                    }
                    let index = number.indexOf(compfind.fgroupnumber)
                    if (index < 0) {
                        number.push(compfind.fgroupnumber);
                    }
                }
            }

            var integerArray = [];
            for (var i = 0; i < newProduct.length; i++) {
                integerArray.push(Math.trunc(newProduct[i].fqty / newProduct[i].fbaseqty));
            }
            //先计算整数倍
            var minqty = number.length >= combinenumber.length ? Math.min(...integerArray) : 0;
            //没匹配上活动
            if (minqty <= 0) {
                for (var i = 0; i < dsProduct.length; i++) {
                    var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                    var comp = currentCombineAll.find(o => o.fproductid == dsProduct[i].data.fproductid);
                    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: dsProduct[i].data.fproductid, opctx: { ignore: true } });//促销商品
                    parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: dsProduct[i].data.fresultbrandid });//业绩品牌
                    parentPage.Model.setValue({ id: 'fgoodschanneltype', row: rowno, value: dsProduct[i].data.fchanneltype });//渠道类型
                    parentPage.Model.setValue({ id: 'fchannel', row: rowno, value: dsProduct[i].data.fchannel });//合作渠道
                    parentPage.Model.setValue({ id: 'fsoorderno', row: rowno, value: dsProduct[i].data.fbillnumber });//销售合同编号
                    parentPage.Model.setValue({ id: 'fsourceentryid_e', row: rowno, value: dsProduct[i].data.forderentryid });
                    parentPage.Model.setValue({ id: 'fsourceformid_e', row: rowno, value: "ydj_order" });
                    parentPage.Model.setValue({ id: 'fsourcebillno_e', row: rowno, value: dsProduct[i].data.fbillnumber });
                    parentPage.Model.setValue({ id: 'fsourceinterid_e', row: rowno, value: dsProduct[i].data.forderid });
                    parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno, value: dsProduct[i].data.forderentryid });
                    parentPage.Model.setValue({ id: 'fsoorderinterid', row: rowno, value: dsProduct[i].data.forderid });
                    parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: dsProduct[i].data.fbizqty });
                    parentPage.Model.setValue({ id: 'fattrinfo', row: rowno, value: dsProduct[i].data.fattrinfo, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno, value: dsProduct[i].data.fsaledeptid, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fcustomer', row: rowno, value: dsProduct[i].data.fcustomer, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fphone_e', row: rowno, value: dsProduct[i].data.fphone, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno, value: dsProduct[i].data.fentrystaffid, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fsourcetype', value: "ydj_order" });//源单类型
                    parentPage.Model.setValue({ id: 'fsourcenumber', value: dsProduct[i].data.fbillnumber });//源单类型
                    parentPage.Model.setValue({ id: 'fbizruleid', value: "ydj_order2ydj_purchaseorder" });//
                }

            } else {

                that.ComeparedsProduct(newProduct, dsProduct, currentCombineAll, minqty);
            }
        }

        //匹配到活动
        _child.prototype.ComeparedsProduct = function (newProduct, dsProduct, currentCombineAll, minqty) {
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });


            var productid = [];
            for (var i = 0; i < dsProduct.length; i++) {
                let index = productid.indexOf(dsProduct[i].data.fproductid)
                if (index < 0) {
                    productid.push(dsProduct[i].data.fproductid);
                }
            }

            for (var i = 0; i < productid.length; i++) {
                var ds = dsProduct.filter(c => c.data.fproductid == productid[i]);
                var parentrow = parentrows.filter(c => c.fmaterialid.id == productid[i]);
                var comp = currentCombineAll.find(o => o.fproductid == productid[i]);
                var qty = 0;
                if (parentrow.length > 0) {
                    for (var j = 0; j < parentrow.length; j++) {
                        qty += parentrow[j].fbizqty;
                    }
                }

                var dsqty = 0;
                var upqty = 0;
                var flag = true;
                for (var x = 0; x < ds.length; x++) {
                    dsqty += ds[x].data.fbizqty;
                    var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: ds[x].data.fproductid });//促销商品
                    parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: ds[x].data.fresultbrandid });//业绩品牌
                    parentPage.Model.setValue({ id: 'fgoodschanneltype', row: rowno, value: ds[x].data.fchanneltype });//渠道类型
                    parentPage.Model.setValue({ id: 'fchannel', row: rowno, value: ds[x].data.fchannel });//合作渠道
                    parentPage.Model.setValue({ id: 'fsoorderno', row: rowno, value: ds[x].data.fbillnumber });//销售合同编号
                    parentPage.Model.setValue({ id: 'fsourceentryid_e', row: rowno, value: ds[x].data.forderentryid });
                    parentPage.Model.setValue({ id: 'fsourceformid_e', row: rowno, value: "ydj_order" });
                    parentPage.Model.setValue({ id: 'fsourcebillno_e', row: rowno, value: ds[x].data.fbillnumber });
                    parentPage.Model.setValue({ id: 'fsourceinterid_e', row: rowno, value: ds[x].data.forderid });
                    parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno, value: ds[x].data.forderentryid });
                    parentPage.Model.setValue({ id: 'fsoorderinterid', row: rowno, value: ds[x].data.forderid });
                    parentPage.Model.setValue({ id: 'fattrinfo', row: rowno, value: ds[x].data.fattrinfo, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno, value: ds[x].data.fsaledeptid, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fcustomer', row: rowno, value: ds[x].data.fcustomer, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fphone_e', row: rowno, value: ds[x].data.fphone, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno, value: ds[x].data.fentrystaffid, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fsourcetype', value: "ydj_order" });//源单类型
                    parentPage.Model.setValue({ id: 'fsourcenumber', value: ds[x].data.fbillnumber });//源单类型
                    parentPage.Model.setValue({ id: 'fbizruleid', value: "ydj_order2ydj_purchaseorder" });//
                    if (!flag) {
                        parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: ds[x].data.fbizqty, opctx: { ignore: true } });//采购数量
                    }
                    if (flag) {
                        upqty += ds[x].data.fbizqty
                        if (upqty <= comp.fbaseqty * minqty) {
                            parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: comp.fname }); //促销活动名称
                            parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: comp.fcombinenumber }); //组合促销编号
                            parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: comp.fcombinedesc });//组合描述
                            parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: comp.fpriority });//促销活动优先级
                            parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: comp.fcombinerate });//折扣率
                            parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: comp.fbaseqty });//套餐组合基数
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: ds[x].data.fbizqty, opctx: { ignore: true } });//采购数量
                            if (upqty == comp.fbaseqty * minqty) flag = false;

                        } else {
                            parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: comp.fname }); //促销活动名称
                            parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: comp.fcombinenumber }); //组合促销编号
                            parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: comp.fcombinedesc });//组合描述
                            parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: comp.fpriority });//促销活动优先级
                            parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: comp.fcombinerate });//折扣率
                            parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: comp.fbaseqty });//套餐组合基数
                            var sumupqty = 0;
                            for (var j = 0; j < x; j++) {
                                sumupqty += ds[j].data.fbizqty;
                            }

                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: comp.fbaseqty * minqty - sumupqty, opctx: { ignore: true } });//采购数量
                            var rowno1 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                            parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno1, value: ds[x].data.fproductid, opctx: { ignore: true } });//促销商品
                            parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno1, value: ds[x].data.fresultbrandid });//业绩品牌
                            parentPage.Model.setValue({ id: 'fgoodschanneltype', row: rowno1, value: ds[x].data.fchanneltype });//渠道类型
                            parentPage.Model.setValue({ id: 'fchannel', row: rowno1, value: ds[x].data.fchannel });//合作渠道
                            parentPage.Model.setValue({ id: 'fsoorderno', row: rowno1, value: ds[x].data.fbillnumber });//销售合同编号
                            parentPage.Model.setValue({ id: 'fsourceentryid_e', row: rowno1, value: ds[x].data.forderentryid });
                            parentPage.Model.setValue({ id: 'fsourceformid_e', row: rowno1, value: "ydj_order" });
                            parentPage.Model.setValue({ id: 'fsourcebillno_e', row: rowno1, value: ds[x].data.fbillnumber });
                            parentPage.Model.setValue({ id: 'fsourceinterid_e', row: rowno1, value: ds[x].data.forderid });
                            parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno1, value: ds[x].data.forderentryid });
                            parentPage.Model.setValue({ id: 'fsoorderinterid', row: rowno1, value: ds[x].data.forderid });
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno1, value: ds[x].data.fbizqty - (comp.fbaseqty * minqty - sumupqty), opctx: { ignore: true } });//采购数量
                            parentPage.Model.setValue({ id: 'fattrinfo', row: rowno1, value: ds[x].data.fattrinfo, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno1, value: ds[x].data.fsaledeptid, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fcustomer', row: rowno1, value: ds[x].data.fcustomer, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fphone_e', row: rowno1, value: ds[x].data.fphone, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno1, value: ds[x].data.fentrystaffid, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fsourcetype', value: "ydj_order" });//源单类型
                            parentPage.Model.setValue({ id: 'fsourcenumber', value: ds[x].data.fbillnumber });//源单类型
                            parentPage.Model.setValue({ id: 'fbizruleid', value: "ydj_order2ydj_purchaseorder" });//
                            flag = false;
                        }
                    }
                }

                //var resiproduct = parentrows.filter(c => c.fmaterialid.id == productid[i] && c.fsoorderno == "");
                var resiproduct = parentrows.filter(c => c.fmaterialid.id == productid[i] && c.fcombinenumber == "");
                if (resiproduct.length > 0) {
                    for (var y = 0; y < resiproduct.length; y++) {
                        if (dsqty < comp.fbaseqty * minqty) {
                            parentPage.Model.setValue({ id: 'fbizqty', row: resiproduct[y].id, value: comp.fbaseqty * minqty - dsqty, opctx: { ignore: true } });//采购数量
                            parentPage.Model.setValue({ id: "fpromotion", row: resiproduct[y].id, value: comp.fname }); //促销活动名称
                            parentPage.Model.setValue({ id: "fcombinenumber", row: resiproduct[y].id, value: comp.fcombinenumber }); //组合促销编号
                            parentPage.Model.setValue({ id: 'fcombineremark', row: resiproduct[y].id, value: comp.fcombinedesc });//组合描述
                            parentPage.Model.setValue({ id: 'fcombinepriority', row: resiproduct[y].id, value: comp.fpriority });//促销活动优先级
                            parentPage.Model.setValue({ id: 'fcombinerate', row: resiproduct[y].id, value: comp.fcombinerate });//折扣率
                            parentPage.Model.setValue({ id: "fcombineqty", row: resiproduct[y].id, value: comp.fbaseqty });//套餐组合基数
                            if (resiproduct[y].fbizqty > comp.fbaseqty * minqty - dsqty) {
                                var resirowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: resirowno, value: productid[i], opctx: { ignore: true } });//促销商品
                                parentPage.Model.setValue({ id: 'fbizqty', row: resirowno, value: resiproduct[y].fbizqty - comp.fbaseqty * minqty - dsqty, opctx: { ignore: true } });//采购数量

                            }
                        }
                    }
                }

            }
            var exprod = [];
            for (var i = 0; i < parentrows.length; i++) {
                var comp = currentCombineAll.filter(o => o.fproductid == parentrows[i].fmaterialid.id);
                var prod = productid.filter(c => c == parentrows[i].fmaterialid.id);
                if (prod.length <= 0 && comp.length > 0) {
                    exprod.push(parentrows[i]);
                }
            }

            if (exprod.length > 0) {
                for (var i = 0; i < exprod.length; i++) {
                    var comp = currentCombineAll.find(o => o.fproductid == exprod[i].fmaterialid.id);
                    if (exprod[i].fbizqty >= comp.fbaseqty * minqty) {
                        parentPage.Model.setValue({ id: 'fbizqty', row: exprod[i].id, value: comp.fbaseqty * minqty, opctx: { ignore: true } });//采购数量
                        parentPage.Model.setValue({ id: "fpromotion", row: exprod[i].id, value: comp.fname }); //促销活动名称
                        parentPage.Model.setValue({ id: "fcombinenumber", row: exprod[i].id, value: comp.fcombinenumber }); //组合促销编号
                        parentPage.Model.setValue({ id: 'fcombineremark', row: exprod[i].id, value: comp.fcombinedesc });//组合描述
                        parentPage.Model.setValue({ id: 'fcombinepriority', row: exprod[i].id, value: comp.fpriority });//促销活动优先级
                        parentPage.Model.setValue({ id: 'fcombinerate', row: exprod[i].id, value: comp.fcombinerate });//折扣率
                        parentPage.Model.setValue({ id: "fcombineqty", row: exprod[i].id, value: comp.fbaseqty });//套餐组合基数
                        if (exprod[i].fbizqty > comp.fbaseqty * minqty) {
                            var exprowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                            parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: exprowno, value: exprod[i].fmaterialid.id, opctx: { ignore: true } });//促销商品
                            parentPage.Model.setValue({ id: 'fbizqty', row: exprowno, value: exprod[i].fbizqty.fbizqty - comp.fbaseqty * minqty, opctx: { ignore: true } });//采购数量
                        }
                    }
                }
            }

            var newrows = parentPage.Model.getEntryData({ id: "fentity" });
            for (var i = 0; i < newrows.length; i++) {
                var newfproduct = newrows[i].fmaterialid;
                if (!newfproduct || $.trim(newfproduct.id) == "") {
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: newrows[i].id });
                }
            }
        }


        //匹配到活动
        _child.prototype.ComepareCombine = function (newProduct, dsProduct, currentCombineAll, minqty) {
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
            var baseArray = [];
            var resiArray = [];

            parentrows = parentrows.filter(c => c.fcombinenumber == "");

            for (var i = 0; i < newProduct.length; i++) {
                //基础的数据
                baseArray.push({ product: newProduct[i].productid, baseqty: newProduct[i].fqty });
                //剩余的数据
                if (newProduct[i].fqty - newProduct[i].fbaseqty * minqty > 0) {
                    resiArray.push({ product: newProduct[i].productid, baseqty: newProduct[i].fqty - newProduct[i].fbaseqty * minqty });
                }
            }

            var newbasearray = [];
            for (var i = 0; i < baseArray.length; i++) {
                var prod = dsProduct.find(c => c.data.fproductid == baseArray[i].product);
                if (prod == undefined) continue;
                var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                var comp = currentCombineAll.find(o => o.fproductid == baseArray[i].product);
                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: prod.data.fproductid, opctx: { ignore: true } });//促销商品
                parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: prod.data.fresultbrandid });//业绩品牌
                parentPage.Model.setValue({ id: 'fsoorderno', row: rowno, value: prod.data.fbillnumber });//销售合同编号
                parentPage.Model.setValue({ id: 'fsourceentryid_e', row: rowno, value: prod.data.forderentryid });
                parentPage.Model.setValue({ id: 'fsourceformid_e', row: rowno, value: "ydj_order" });
                parentPage.Model.setValue({ id: 'fsourcebillno_e', row: rowno, value: prod.data.fbillnumber });
                parentPage.Model.setValue({ id: 'fsourceinterid_e', row: rowno, value: prod.data.forderid });
                parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno, value: prod.data.forderentryid });
                parentPage.Model.setValue({ id: 'fsoorderinterid', row: rowno, value: prod.data.forderid });
                parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno, value: prod.data.fsaledeptid, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fcustomer', row: rowno, value: prod.data.fcustomer, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fphone_e', row: rowno, value: prod.data.fphone, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno, value: prod.data.fentrystaffid, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fsourcetype', value: "ydj_order" });//源单类型
                parentPage.Model.setValue({ id: 'fsourcenumber', value: prod.data.fbillnumber });//源单类型
                parentPage.Model.setValue({ id: 'fbizruleid', value: "ydj_order2ydj_purchaseorder" });//

                if (comp != undefined) {
                    var resiprod = resiArray.filter(c => c.product == baseArray[i].product);
                    if (resiprod.length > 0 && resiprod != undefined) {
                        if (comp.fbaseqty > prod.data.fbizqty) { //套餐基数小于商品数量，说明满足套餐，切原商品要拆单
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: prod.data.fbizqty, opctx: { ignore: true } });//采购数量
                            newbasearray.push({ product: baseArray[i].product, fqty: prod.data.fbizqty });
                        } else {
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: comp.fbaseqty * minqty, opctx: { ignore: true } });//采购数量
                        }
                    } else {
                        var parentrow = parentrows.find(c => c.fmaterialid.id == baseArray[i].product && c.fsoorderno == "");
                        if (parentrow != undefined) {
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: prod.data.fbizqty, opctx: { ignore: true } });//采购数量
                        } else {
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: comp.fbaseqty * minqty, opctx: { ignore: true } });//采购数量
                        }
                    }
                    parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: comp.fname }); //促销活动名称
                    parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: comp.fcombinenumber }); //组合促销编号
                    parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: comp.fcombinedesc });//组合描述
                    parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: comp.fpriority });//促销活动优先级
                    parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: comp.fcombinerate });//折扣率
                    parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: comp.fbaseqty });//套餐组合基数
                }
            }

            for (var i = 0; i < resiArray.length; i++) {
                var comp1 = currentCombineAll.find(o => o.fproductid == resiArray[i].product);
                var prod1 = dsProduct.find(c => c.data.fproductid == resiArray[i].product);
                if (prod1 == undefined) continue;
                var arraybase = newbasearray.find(c => c.product == resiArray[i].product);
                if (arraybase == undefined) {
                    var parentrow1 = parentrows.find(c => c.fmaterialid.id == resiArray[i].product && c.fsoorderno == "");
                    if (parentrow1 != undefined && parentrow1.fbizqty == resiArray[i].baseqty) continue;
                    var rowno1 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                    parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno1, value: prod1.data.fproductid, opctx: { ignore: true } });//促销商品
                    parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno1, value: prod1.data.fresultbrandid });//业绩品牌
                    parentPage.Model.setValue({ id: 'fsoorderno', row: rowno1, value: prod1.data.fbillnumber });//销售合同编号
                    parentPage.Model.setValue({ id: 'fsourceentryid_e', row: rowno1, value: prod1.data.forderentryid });
                    parentPage.Model.setValue({ id: 'fsourceformid_e', row: rowno1, value: "ydj_order" });
                    parentPage.Model.setValue({ id: 'fsourcebillno_e', row: rowno1, value: prod1.data.fbillnumber });
                    parentPage.Model.setValue({ id: 'fsourceinterid_e', row: rowno1, value: prod1.data.forderid });
                    parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno1, value: prod1.data.forderentryid });
                    parentPage.Model.setValue({ id: 'fsoorderinterid', row: rowno1, value: prod1.data.forderid });
                    parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno1, value: prod1.data.fsaledeptid, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fcustomer', row: rowno1, value: prod1.data.fcustomer, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fphone_e', row: rowno1, value: prod1.data.fphone, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno1, value: prod1.data.fentrystaffid, opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fsourcetype', value: "ydj_order" });//源单类型
                    parentPage.Model.setValue({ id: 'fsourcenumber', value: prod1.data.fbillnumber });//源单类型
                    parentPage.Model.setValue({ id: 'fbizruleid', value: "ydj_order2ydj_purchaseorder" });//
                    if (prod1.data.fbizqty > comp1.fbaseqty * minqty) {
                        parentPage.Model.setValue({ id: 'fbizqty', row: rowno1, value: prod1.data.fbizqty - comp1.fbaseqty * minqty, opctx: { ignore: true } });//采购数量
                    } else {
                        if (parentrow1 != undefined) {
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno1, value: resiArray[i].baseqty - parentrow1.fbizqty, opctx: { ignore: true } });//采购数量
                        } else {
                            parentPage.Model.setValue({ id: 'fbizqty', row: rowno1, value: resiArray[i].baseqty, opctx: { ignore: true } });//采购数量
                        }
                    }
                }
            }

            for (var i = 0; i < newbasearray.length; i++) {
                var findrows = parentrows.find(c => c.fmaterialid.id == newbasearray[i].product && c.fcombinenumber == "" && c.fsoorderno == "");
                if (findrows != undefined) {
                    var resi = resiArray.find(c => c.product == newbasearray[i].product);
                    if (resi != undefined) {
                        var comp2 = currentCombineAll.find(o => o.fproductid == newbasearray[i].product);

                        parentPage.Model.setValue({ id: 'fbizqty', row: findrows.id, value: resi.baseqty, opctx: { ignore: true } });//采购数量

                        var rowno2 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                        parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno2, value: newbasearray[i].product, opctx: { ignore: true } });//促销商品
                        parentPage.Model.setValue({ id: "fpromotion", row: rowno2, value: comp2.fname }); //促销活动名称
                        parentPage.Model.setValue({ id: "fcombinenumber", row: rowno2, value: comp2.fcombinenumber }); //组合促销编号
                        parentPage.Model.setValue({ id: 'fcombineremark', row: rowno2, value: comp2.fcombinedesc });//组合描述
                        parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno2, value: comp2.fpriority });//促销活动优先级
                        parentPage.Model.setValue({ id: 'fcombinerate', row: rowno2, value: comp2.fcombinerate });//折扣率
                        parentPage.Model.setValue({ id: "fcombineqty", row: rowno2, value: comp2.fbaseqty });//套餐组合基数
                        parentPage.Model.setValue({ id: 'fbizqty', row: rowno2, value: newbasearray[i].fqty - resi.baseqty, opctx: { ignore: true } });//采购数量
                    }

                }
            }

            for (var i = 0; i < parentrows.length; i++) {
                //说明已经勾选了子组号，满足打促销活动标签的条件
                if (baseArray.length > 0) {
                    var comp = currentCombineAll.find(o => o.fproductid == parentrows[i].fmaterialid.id);
                    if (comp != undefined) {
                        var rows = parentrows.filter(c => c.fmaterialid.id == parentrows[i].fmaterialid.id);
                        var sumqty = 0;
                        if (rows != undefined && rows.length > 0) {
                            for (var j = 0; j < rows.length; j++) {
                                sumqty += rows[j].fbizqty;
                            }
                            if (sumqty - comp.fbaseqty * minqty == 0) {
                                parentPage.Model.setValue({ id: "fpromotion", row: parentrows[i].id, value: comp.fname }); //促销活动名称
                                parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[i].id, value: comp.fcombinenumber }); //组合促销编号
                                parentPage.Model.setValue({ id: 'fcombineremark', row: parentrows[i].id, value: comp.fcombinedesc });//组合描述
                                parentPage.Model.setValue({ id: 'fcombinepriority', row: parentrows[i].id, value: comp.fpriority });//促销活动优先级
                                parentPage.Model.setValue({ id: 'fcombinerate', row: parentrows[i].id, value: comp.fcombinerate });//折扣率
                                parentPage.Model.setValue({ id: "fcombineqty", row: parentrows[i].id, value: comp.fbaseqty });//套餐组合基数
                            }
                        }
                    }
                }
            }

        }


        //验证数据有效性
        _child.prototype.ValidataCombine = function (combine, dscomBine, muchcombine) {
            ;
            var that = this;
            var message = "";

            for (var x = 0; x < muchcombine.length; x++) {
                var currentCombineAll = combine.filter(c => c.fcombinenumber == muchcombine[x]);
                var currentdsCombine = dscomBine.filter(c => c.data.fcombinenumber == muchcombine[x]);
                var number = [];
                var combinenumber = [];


                for (var i = 0; i < currentdsCombine.length; i++) {
                    //套餐组合基数 大于 采购数量
                    //if (currentdsCombine[i].data.fbaseqty > currentdsCombine[i].data.fqty) {
                    //    message += currentdsCombine[0].data.fcombinename + "采购数量不能小于套餐组合基数</br>";
                    //}


                    let index = number.indexOf(currentdsCombine[i].data.fgroupnumber)
                    if (index < 0) {
                        number.push(currentdsCombine[i].data.fgroupnumber);
                    }
                }

                for (var j = 0; j < currentCombineAll.length; j++) {
                    let index = combinenumber.indexOf(currentCombineAll[j].fgroupnumber)
                    if (index < 0) {
                        combinenumber.push(currentCombineAll[j].fgroupnumber);
                    }

                }

                if (number.length < combinenumber.length) {
                    let diff = combinenumber.filter(e => !number.includes(e));

                    message += "组合促销活动编码为“" + muchcombine[x] + "”的套餐，已选择的商品未包含子组号" + diff.toString() + "，请从子组号" + diff.toString() + "中选择商品凑成组合套餐，享受活动折扣优惠 。</br>";
                }
                if (message.length > 0) {
                    return message;
                }
                for (var i = 0; i < number.length; i++) {
                    let qty = 0;
                    let gpItem = currentdsCombine.forEach(item => {
                        // item为对象数组中的每个对象，此处根据title进行分组
                        if (item.data.fgroupnumber == number[i]) {
                            qty += parseInt(item.data.fpurorderqty);
                        }
                    })
                    var baseqty = currentCombineAll.filter(a => a.fgroupnumber == number[i])[0].fbaseqty;
                    if (qty < baseqty) {
                        message += "组合促销活动编码为“" + muchcombine[x] + "”的套餐，子组号" + number[i] + "中的商品采购数量合计，小于套餐组合基数，不符合促销活动规则，请继续凑单。</br>";
                    }
                }


                //var intersection = number.filter(val => combinenumber.indexOf(val) > -1);
                //if (intersection.length < combinenumber.length) {
                //    message += currentdsCombine[0].data.fcombinename + "套餐商品不完整，请选择完整的套餐商品</br>";
                //}
            }

            return message;
        }

        //保存并关闭调用这里。新凑单逻辑
        _child.prototype.GetCurrentCombine = function (currentCombineAll, dscomBine) {
            ;
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
            var baseArray = [];
            var resiArray = [];
            var oldArray = [];
            var orderArray = [];
            var addArray = [];
            var resiAddArray = [];
            for (var i = 0; i < parentrows.length; i++) {

                var compfind = currentCombineAll.find(o => o.fproductid == parentrows[i].fmaterialid.id && parentrows[i].fcombinenumber == "");
                if (compfind != undefined) {
                    var findds = dscomBine.find(c => c.data.fproductid == parentrows[i].fmaterialid.id);
                    if (findds != undefined) {
                        findds.data.fqty = parseInt(findds.data.fqty) + parentrows[i].fbizqty;
                        parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[i].id, value: "" });
                        if (!(parentrows[i].fsoorderno != undefined && parentrows[i].fsoorderno.length > 0)) {
                            oldArray.push(parentrows[i]);
                            that.parentid.push({ fproductid: parentrows[i].fmaterialid.id, id: parentrows[i].id });
                            parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id, opctx: { promotion: true } });
                        } else {
                            orderArray.push(parentrows[i]);
                        }
                    } else {
                        if (parentrows[i].fbizqty >= compfind.fbaseqty) {
                            compfind.fqty = parentrows[i].fbizqty;
                            parentPage.Model.setValue({ id: "fcombinenumber", row: parentrows[i].id, value: "" });
                            if (!(parentrows[i].fsoorderno != undefined && parentrows[i].fsoorderno.length > 0)) {
                                dscomBine.push({ pkid: parentrows[i].fmaterialid.id, data: compfind });
                                oldArray.push(parentrows[i]);
                                that.parentid.push({ fproductid: parentrows[i].fmaterialid.id, id: parentrows[i].id });
                                parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: parentrows[i].id, opctx: { promotion: true } });
                            } else {
                                orderArray.push(parentrows[i]);
                            }
                        }
                    }
                }
            }

            var integerArray = [];
            for (var i = 0; i < dscomBine.length; i++) {
                integerArray.push(Math.trunc(dscomBine[i].data.fqty / dscomBine[i].data.fbaseqty));
            }
            //先计算整数倍
            var minqty = Math.min(...integerArray);

            for (var i = 0; i < dscomBine.length; i++) {
                //基础的数据
                baseArray.push({ product: dscomBine[i].data.fproductid, groupno: dscomBine[i].data.fgroupnumber, baseqty: dscomBine[i].data.fbaseqty });
                //剩余的数据
                if (dscomBine[i].data.fqty - dscomBine[i].data.fbaseqty * minqty > 0) {
                    resiArray.push({ product: dscomBine[i].data.fproductid, groupno: dscomBine[i].data.fgroupnumber, baseqty: dscomBine[i].data.fqty - dscomBine[i].data.fbaseqty * minqty });
                }
            }
            for (var i = 0; i < baseArray.length; i++) {
                var comp = currentCombineAll.find(o => o.fproductid == baseArray[i].product);
                var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                addArray.push(rowno);
                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid, opctx: { ignore: true, promotion: true } });//促销商品
                parentPage.Model.setValue({ id: "fpromotion", row: rowno, value: comp.fname }); //促销活动名称
                parentPage.Model.setValue({ id: "fcombinenumber", row: rowno, value: comp.fcombinenumber }); //组合促销编号
                parentPage.Model.setValue({ id: 'fcombineremark', row: rowno, value: comp.fcombinedesc });//组合描述
                parentPage.Model.setValue({ id: 'fcombinepriority', row: rowno, value: comp.fpriority });//促销活动优先级
                parentPage.Model.setValue({ id: 'fcombinerate', row: rowno, value: comp.fcombinerate });//折扣率
                parentPage.Model.setValue({ id: "fcombineqty", row: rowno, value: comp.fbaseqty });//套餐组合基数
                parentPage.Model.setValue({ id: "fpartqty", row: rowno, value: comp.fpartqty });//配件数量
                if (comp.fpartqty > 0) {
                    parentPage.Model.setValue({ id: "fiscombmain", row: rowno, value: true });//配件主商品
                }

                var rownumber = 0;
                var row = parentrows.filter(c => c.fmaterialid.id == baseArray[i].product && c.fsoorderno != "");
                if (row != undefined) {
                    for (var j = 0; j < row.length; j++) {
                        rownumber += row[j].fbizqty;
                    }
                    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: baseArray[i].baseqty * minqty - rownumber, opctx: { ignore: true } });//采购数量
                } else {
                    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: baseArray[i].baseqty * minqty, opctx: { ignore: true } });//采购数量
                }

                var oldrow = oldArray.find(o => o.fmaterialid.id == baseArray[i].product);
                if (oldrow != undefined) {
                    parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: oldrow["fresultbrandid"].id });//业绩品牌
                    parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno, value: oldrow["fsaledeptid"].id });//销售部门

                    parentPage.Model.setValue({ id: 'fsuitcombnumber', row: rowno, value: oldrow["fsuitcombnumber"], opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fpartscombnumber', row: rowno, value: oldrow["fpartscombnumber"], opctx: { ignore: true } });
                    parentPage.Model.setValue({ id: 'fsofacombnumber', row: rowno, value: oldrow["fsofacombnumber"], opctx: { ignore: true } });

                    parentPage.Model.setValue({ id: 'fpartqty', row: rowno, value: oldrow["fpartqty"], opctx: { ignore: true } });
                    if (oldrow["fpartqty"] > 0) {
                        parentPage.Model.setValue({ id: "fiscombmain", row: rowno, value: true });//配件主商品
                    }
                }
                var orderrow = orderArray.filter(o => o.fmaterialid.id == baseArray[i].product);
                if (orderrow != undefined) {
                    for (var j = 0; j < orderrow.length; j++) {
                        parentPage.Model.setValue({ id: "fpromotion", row: orderrow[j].id, value: comp.fname }); //促销活动名称
                        parentPage.Model.setValue({ id: "fcombinenumber", row: orderrow[j].id, value: comp.fcombinenumber }); //组合促销编号
                        parentPage.Model.setValue({ id: 'fcombineremark', row: orderrow[j].id, value: comp.fcombinedesc });//组合描述
                        parentPage.Model.setValue({ id: 'fcombinepriority', row: orderrow[j].id, value: comp.fpriority });//促销活动优先级
                        parentPage.Model.setValue({ id: 'fcombinerate', row: orderrow[j].id, value: comp.fcombinerate });//折扣率
                        parentPage.Model.setValue({ id: "fcombineqty", row: orderrow[j].id, value: comp.fbaseqty });//套餐组合基数
                    }
                }

            }

            //这个方法会把多的数量减少
            for (var i = 0; i < resiArray.length; i++) {
                var comp1 = currentCombineAll.find(o => o.fproductid == resiArray[i].product);
                var rowno1 = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                addArray.push(rowno1);
                resiAddArray.push(rowno1);
                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno1, value: comp1.fproductid, opctx: { ignore: true, promotion: true } });//促销商品
                parentPage.Model.setValue({ id: that.Model.selsettings.fbizqty, row: rowno1, value: resiArray[i].baseqty, tgChange: false });//数量
                parentPage.Model.setValue({ id: "fbizqty", row: rowno1, value: resiArray[i].baseqty });//采购数量
                var oldrow1 = oldArray.find(o => o.fmaterialid.id == resiArray[i].product);
                //if (oldrow1 != undefined) {
                //    parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno1, value: oldrow1["fresultbrandid"].id });//业绩品牌
                //}
                var orderrow1 = orderArray.filter(o => o.fmaterialid.id == resiArray[i].product);
                if (orderrow1 != undefined) {
                    for (var j = 0; j < orderrow1.length; j++) {
                        var sfqty = parentPage.Model.getValue({ id: "fbizqty", row: orderrow1[j].id });//采购数量
                        if (sfqty > resiArray[i].baseqty) {
                            parentPage.Model.setValue({ id: "fbizqty", row: orderrow1[j].id, value: (sfqty - resiArray[i].baseqty), tgChange: false });//采购数量
                            //补进来的行不需要加源单信息，拆行出来的，需要加源单信息
                            parentPage.Model.setValue({ id: 'fsoorderno', row: rowno1, value: orderrow1[j].fsoorderno });//
                            parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno1, value: orderrow1[j].fsoorderentryid });//
                            parentPage.Model.setValue({ id: "fsoorderinterid", row: rowno1, value: orderrow1[j].fsoorderinterid });//
                            parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno1, value: orderrow1[j].fresultbrandid.id });//业绩品牌

                            parentPage.Model.setValue({ id: 'fsourcebillno_e', row: rowno1, value: orderrow1[j].fsourcebillno_e });//
                            parentPage.Model.setValue({ id: 'fsourceentryid_e', row: rowno1, value: orderrow1[j].fsourceentryid_e });//
                            parentPage.Model.setValue({ id: "fsourceformid_e", row: rowno1, value: orderrow1[j].fsourceformid_e });//
                            parentPage.Model.setValue({ id: "fsourceinterid_e", row: rowno1, value: orderrow1[j].fsourceinterid_e });//

                            parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno1, value: orderrow1[j].fsaledeptid, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fcustomer', row: rowno1, value: orderrow1[j].fcustomer, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fphone_e', row: rowno1, value: orderrow1[j].fphone_e, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno1, value: orderrow1[j].fentrystaffid, opctx: { ignore: true } });

                            parentPage.Model.setValue({ id: 'fsuitcombnumber', row: rowno1, value: orderrow1[j].fsuitcombnumber, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fpartscombnumber', row: rowno1, value: orderrow1[j].fpartscombnumber, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fsofacombnumber', row: rowno1, value: orderrow1[j].fsofacombnumber, opctx: { ignore: true } });
                            parentPage.Model.setValue({ id: 'fpartqty', row: rowno1, value: orderrow1[j].fpartqty, opctx: { ignore: true } });
                            if (orderrow1[j].fpartqty > 0) {
                                parentPage.Model.setValue({ id: "fiscombmain", row: rowno1, value: true });//配件主商品
                            }
                        }
                        parentPage.Model.setValue({ id: "fpromotion", row: orderrow1[j].id, value: comp1.fname }); //促销活动名称
                        parentPage.Model.setValue({ id: "fcombinenumber", row: orderrow1[j].id, value: comp1.fcombinenumber }); //组合促销编号
                        parentPage.Model.setValue({ id: 'fcombineremark', row: orderrow1[j].id, value: comp1.fcombinedesc });//组合描述
                        parentPage.Model.setValue({ id: 'fcombinepriority', row: orderrow1[j].id, value: comp1.fpriority });//促销活动优先级
                        parentPage.Model.setValue({ id: 'fcombinerate', row: orderrow1[j].id, value: comp1.fcombinerate });//折扣率
                        parentPage.Model.setValue({ id: "fcombineqty", row: orderrow1[j].id, value: comp1.fbaseqty });//套餐组合基数
                    }
                }
            }

            var newrows = parentPage.Model.getEntryData({ id: "fentity" });
            var rows = newrows.filter(c => (c.fbizqty == 0 && c.fcombinenumber != "") || (addArray.indexOf(c.id) >= 0 && c.fbizqty == 0 && c.fcombinenumber == "" && c.fsoorderno == ""));
            var rows1 = newrows.filter(c => (resiAddArray.indexOf(c.id) >= 0 && c.fcombinenumber == "" && c.fsoorderno == ""));//新增进来的行，但是又没有凑单，干掉
            if (rows.length > 0) {
                for (var i = 0; i < rows.length; i++) {
                    parentPage.Model.setValue({ id: "fcombinenumber", row: rows[i].id, value: "" });
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: rows[i].id, opctx: { ignore: true } });
                }
            }
            if (rows1.length > 0) {
                for (var i = 0; i < rows1.length; i++) {
                    parentPage.Model.setValue({ id: "fcombinenumber", row: rows1[i].id, value: "" });
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: rows1[i].id, opctx: { ignore: true } });
                }
            }

            for (var i = 0; i < newrows.length; i++) {
                var newfproduct = newrows[i].fmaterialid;
                if (!newfproduct || $.trim(newfproduct.id) == "") {
                    parentPage.Model.deleteRow({ id: that.Model.selsettings.fentity, row: newrows[i].id, opctx: { ignore: true } });
                }
            }

            newrows.sort(function (a, b) {
                if (a.fsofacombnumber !== b.fsofacombnumber) {
                    return a.fsofacombnumber.localeCompare(b.fsofacombnumber);
                }
                else if (a.fcombinenumber !== b.fcombinenumber) {
                    if (a.fcombinenumber < b.fcombinenumber) {
                        return 1;
                    }
                    if (a.fcombinenumber > b.fcombinenumber) {
                        return -1;
                    }
                }
                else if (a.fpartscombnumber !== b.fpartscombnumber) {
                    return a.fpartscombnumber.localeCompare(b.fpartscombnumber)
                }
                else if (a.fiscombmain !== b.fiscombmain) {
                    return b.fiscombmain - a.fiscombmain;
                }
                else if (a.fsuitcombnumber !== b.fsuitcombnumber) {
                    return a.fsuitcombnumber.localeCompare(b.fsuitcombnumber)
                }
                else if (a.fissuitflag !== b.fissuitflag) {
                    return b.fissuitflag - a.fissuitflag;
                } else {
                    return 1;
                }
            });

            parentPage.Model.refreshEntry({ id: that.Model.selsettings.fentity });
        };

        //勾选促销套餐明细
        _child.prototype.GetCombineProduct = function (dscomBine, opcode) {
            ;
            var that = this;
            var muchcombine = [];
            for (var i = 0; i < dscomBine.length; i++) {
                if (muchcombine.indexOf(dscomBine[i].data.fcombinenumber) < 0) {
                    muchcombine.push(dscomBine[i].data.fcombinenumber);
                }
            }
            var index = 0;
            var t1 = $("#promotions").children();
            for (var i = 0; i < t1.length; i++) {
                //
                if (t1[i].className == "active") {
                    index = i;
                }
                //console.log(t1[i]);
            }

            if (muchcombine.length > 0) {
                //验证数据是否正确
                var message = that.ValidataCombine(that.combine, dscomBine, muchcombine);
                if (message.length > 0) {
                    return yiDialog.a(message);
                }

                for (var i = 0; i < muchcombine.length; i++) {
                    var currentCombineAll = that.combine.filter(c => c.fcombinenumber == muchcombine[i]);
                    var currentdsCombine = dscomBine.filter(c => c.data.fcombinenumber == muchcombine[i] && c.data.fpurorderqty > 0);
                    //在明细行添加数据
                    //that.GetCurrentCombine(currentCombineAll, currentdsCombine);
                    that.saveCombineInfoByProduct(currentCombineAll, currentdsCombine);

                }
                var parentPage = Index.getPage(that.parentpageid);

                setTimeout(
                    function () {
                        that.Model.invokeFormOperation({
                            id: 'savecombineinfo',
                            opcode: 'savecombineinfo',
                            formId: 'ydj_purchaseorder',
                            billData: JSON.stringify([parentPage.Model.uiData]),
                            selectedRows: [{ PKValue: parentPage.Model.pkid }],
                            //selectedRows: [{ PKValue: that.Model.pkid }],
                            param: {
                                formId: 'ydj_purchaseorder',
                                combinenumber: muchcombine.toString(),
                                pkid: parentPage.Model.pkid,
                                parentPageId: that.parentpageid,
                                stype: 'combine'
                                //changeReason: v
                            }
                        });
                    }, 3000)
            }

            yiDialog.mt({ msg: '促销商品添加成功！', skinseq: 1 });

            if (message.length <= 0) {
                switch (opcode) {
                    //保存
                    case 'pagesave':
                        that.updateCombineQty(that.parentid);
                        for (var i = 0; i < that.combine.length; i++) {
                            that.Model.addRow({ id: 'fbdfldentity', data: that.combine[i] });
                        }

                        var rows = that.Model.getEntryData({ id: "fbdfldentity" });
                        var row = [];
                        for (var i = 0; i < rows.length; i++) {
                            if (that.combine[i].fpurorderqty > 0) {
                                row.push(rows[i].id);
                                //that.Model.setEnable({ id: "", value: false, row: rows[i].id });
                            }
                        }
                        that.Model.setSelectRows({ id: 'fbdfldentity', rows: row });
                        break;
                    //保存并关闭
                    case 'pagesaveclose':
                        that.Model.close();
                        break;
                }
            }
        };

        _child.prototype.saveCombineInfoByProduct = function (currentCombineAll, dscomBine) {
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
            var baseArray = [];
            var oldArray = [];
            var addArray = [];

            //筛选出新增勾选的商品，然后新增行，接着走后端逻辑
            for (var i = 0; i < dscomBine.length; i++) {
                var findds = parentrows.find(c => c.fmaterialid.id == dscomBine[i].data.fproductid);
                if (findds) {
                    if (findds.fsoorderinterid != "") {
                        addArray.push(dscomBine[i]);
                    } else {
                        oldArray.push(dscomBine[i]);
                    }
                } else {
                    addArray.push(dscomBine[i]);
                }
            }

            for (var i = 0; i < dscomBine.length; i++) {
                //基础的数据
                baseArray.push({ product: dscomBine[i].data.fproductid, groupno: dscomBine[i].data.fgroupnumber, baseqty: dscomBine[i].data.fbaseqty });
            }
            var integerArray = [];
            for (var i = 0; i < dscomBine.length; i++) {
                integerArray.push(Math.trunc(dscomBine[i].data.fqty / dscomBine[i].data.fbaseqty));
            }
            //先计算整数倍
            var minqty = Math.min(...integerArray);
            if (minqty == 0) minqty = 1;
            for (var i = 0; i < addArray.length; i++) {
                var comp = currentCombineAll.find(o => o.fproductid == addArray[i].data.fproductid);

                var existaQty = 0;
                //var findds = parentrows.find(c => c.fmaterialid.id == addArray[i].data.fproductid && c.fsoorderinterid != "");
                var finds = parentrows.filter(c => c.fmaterialid.id == addArray[i].data.fproductid && c.fsoorderinterid != "");
                if (finds && finds.length > 0) {
                    for (var k = 0; k < finds.length; k++) {
                        existaQty += parseInt(finds[k].fbizqty);
                    }
                }
                //var findds = parentrows.find(c => c.fmaterialid.id == addArray[i].data.fproductid && c.fsoorderinterid != "");
                if (parseInt(addArray[i].data.fpurorderqty) - existaQty == 0) {
                    continue;
                }
                var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: comp.fproductid, opctx: { childPageId: that.Model.getPageId() } });//促销商品
                parentPage.Model.setValue({ id: "fpartqty", row: rowno, value: comp.fpartqty });//配件数量
                if (comp.fpartqty > 0) {
                    parentPage.Model.setValue({ id: "fiscombmain", row: rowno, value: true });//配件主商品
                }
                var existaQty = 0;
                //var findds = parentrows.find(c => c.fmaterialid.id == addArray[i].data.fproductid && c.fsoorderinterid != "");
                var finds = parentrows.filter(c => c.fmaterialid.id == addArray[i].data.fproductid && c.fsoorderinterid != "");
                if (finds && finds.length > 0) {
                    for (var k = 0; k < finds.length; k++) {
                        existaQty += parseInt(finds[k].fbizqty);
                    }
                }
                if (parseInt(addArray[i].data.fpurorderqty) - existaQty >= 0) {
                    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: parseInt(addArray[i].data.fpurorderqty) - existaQty });//采购数量
                } else
                    parentPage.Model.setValue({ id: "fbizqty", row: rowno, value: parseInt(addArray[i].data.fpurorderqty) });//采购数量

            }

            for (var i = 0; i < oldArray.length; i++) {
                var rowItem = parentrows.filter(c => c.fmaterialid.id == oldArray[i].data.fproductid);
                let parentRowCount = 0;
                for (var j = 0; j < rowItem.length; j++) {
                    parentRowCount += parseInt(rowItem[j].fbizqty);
                }
                if (parentRowCount == oldArray[i].data.fpurorderqty) {
                    continue;
                }
                rowItem = rowItem.sort(function (a, b) {
                    // 先按 优先级 字段排序
                    if (a.fprice > b.fprice) {
                        return -1;
                    }
                    if (a.fprice < b.fprice) {
                        return 1;
                    }
                    return 0;
                });
                //parentPage.Model.setValue({ id: "fbizqty", row: rowItem.id, value: parseInt(oldArray[i].data.fpurorderqty) + rowItem.fqty });//采购数量
                parentPage.Model.setValue({ id: "fbizqty", row: rowItem[0].id, value: parseInt(oldArray[i].data.fpurorderqty) });//采购数量
            }
            //let index = 0;
            //if (that.times != addArray.length) {
            //    do {
            //        setTimeout(
            //            function () {
            //                console.log('123')
            //            }, 500)
            //        index++;
            //    } while (that.times != addArray.length);
            //}

            //
            //return;
            return;
        }

        _child.prototype.saveCombineInfoByOrder = function (currentCombineAll, dsProduct) {
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });

            for (var i = 0; i < dsProduct.length; i++) {
                var rowno = parentPage.Model.addRow({ id: that.Model.selsettings.fentity });
                parentPage.Model.setValue({ id: that.Model.selsettings.fmaterialid, row: rowno, value: dsProduct[i].data.fproductid });//促销商品
                parentPage.Model.setValue({ id: 'fresultbrandid', row: rowno, value: dsProduct[i].data.fresultbrandid });//业绩品牌
                parentPage.Model.setValue({ id: 'fgoodschanneltype', row: rowno, value: dsProduct[i].data.fchanneltype });//渠道类型
                parentPage.Model.setValue({ id: 'fchannel', row: rowno, value: dsProduct[i].data.fchannel, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fsoorderno', row: rowno, value: dsProduct[i].data.fbillnumber });//销售合同编号
                parentPage.Model.setValue({ id: 'fsourceentryid_e', row: rowno, value: dsProduct[i].data.forderentryid });
                parentPage.Model.setValue({ id: 'fsourceformid_e', row: rowno, value: "ydj_order" });
                parentPage.Model.setValue({ id: 'fsourcebillno_e', row: rowno, value: dsProduct[i].data.fbillnumber });
                parentPage.Model.setValue({ id: 'fsourceinterid_e', row: rowno, value: dsProduct[i].data.forderid });
                parentPage.Model.setValue({ id: 'fsoorderentryid', row: rowno, value: dsProduct[i].data.forderentryid });
                parentPage.Model.setValue({ id: 'fsoorderinterid', row: rowno, value: dsProduct[i].data.forderid });
                parentPage.Model.setValue({ id: 'fbizqty', row: rowno, value: dsProduct[i].data.fbizqty });
                parentPage.Model.setValue({ id: 'fattrinfo', row: rowno, value: dsProduct[i].data.fattrinfo, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fsaledeptid', row: rowno, value: dsProduct[i].data.fsaledeptid, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fcustomer', row: rowno, value: dsProduct[i].data.fcustomer, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fphone_e', row: rowno, value: dsProduct[i].data.fphone, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fentrystaffid', row: rowno, value: dsProduct[i].data.fentrystaffid, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fpartscombnumber', row: rowno, value: dsProduct[i].data.fpartscombnumber, opctx: { ignore: true } });
                parentPage.Model.setValue({ id: 'fsourcetype', value: "ydj_order" });//源单类型
                parentPage.Model.setValue({ id: 'fsourcenumber', value: dsProduct[i].data.fbillnumber });//源单类型
                parentPage.Model.setValue({ id: 'fbizruleid', value: "ydj_order2ydj_purchaseorder" });//
                if (dsProduct[i].data.fpartscombnumber) {
                    parentPage.Model.setValue({ id: "fiscombmain", row: rowno, value: true });//配件主商品
                }
            }
            setTimeout(function () {
                that.Model.invokeFormOperation({
                    id: 'savecombineinfo',
                    opcode: 'savecombineinfo',
                    formId: 'ydj_purchaseorder',
                    billData: JSON.stringify([parentPage.Model.uiData]),
                    selectedRows: [{ PKValue: parentPage.Model.pkid }],
                    //selectedRows: [{ PKValue: that.Model.pkid }],
                    param: {
                        formId: 'ydj_purchaseorder',
                        combinenumber: currentCombineAll[0].fcombinenumber,
                        pkid: parentPage.Model.pkid,
                        parentPageId: that.parentpageid,
                        stype: 'order'
                        //changeReason: v
                    }
                });
            }, 3000)


        }

        //更新套餐明细的已采购数量
        _child.prototype.updateCombineQty = function (parentid) {
            var that = this;
            var parentPage = Index.getPage(that.parentpageid);
            var parentrows = parentPage.Model.getEntryData({ id: "fentity" });

            that.Model.deleteEntryData({ id: 'fbdfldentity' });
            var row = [];
            //
            for (var i = 0; i < that.combine.length; i++) {
                var sumQty = 0;
                that.combine[i].fpurorderqty = 0;
                var prod = parentrows.filter(c => c.fmaterialid.id == that.combine[i].fproductid);
                var oldprod = parentid.filter(c => c.fproductid == that.combine[i].fproductid);
                if (prod != undefined) {
                    for (var j = 0; j < prod.length; j++) {
                        if (oldprod.length > 0) {
                            for (var x = 0; x < oldprod.length; x++) {
                                if (prod[j].id != oldprod[x].id) {
                                    sumQty += prod[j].fbizqty;
                                }
                            }
                        } else {
                            sumQty += prod[j].fbizqty;
                        }
                    }
                    //这里按优先级设置采购数量，如果已经被赋值过采购数量，那就不再做采购数量的赋值，建议采购数量也就等于套餐组合基数
                    if (row.indexOf(that.combine[i].fproductid) > -1) {
                        that.combine[i].fpurorderqty = 0;
                        that.combine[i].fqty = that.combine[i].fbaseqty;
                    } else {
                        that.combine[i].fpurorderqty = sumQty;
                        if (sumQty - that.combine[i].fqty >= 0) {
                            that.combine[i].fqty = 0;
                        } else {
                            that.combine[i].fqty = that.combine[i].fbaseqty - sumQty;
                        }
                    }
                    row.push(that.combine[i].fproductid);
                }
            }
            //
            that.combine.sort(function (a, b) {
                // 先按 优先级 字段排序
                if (a.fpriority < b.fpriority) {
                    return -1;
                }
                if (a.fpriority > b.fpriority) {
                    return 1;
                }

                // 如果 field1 字段相同，则按 field2 字段排序
                if (a.fpurorderqty < b.fpurorderqty) {
                    return 1;
                }
                if (a.fpurorderqty > b.fpurorderqty) {
                    return -1;
                }

                if (a.fgroupnumber < b.fgroupnumber) {
                    return -1;
                }
                if (a.fgroupnumber > b.fgroupnumber) {
                    return 1;
                }

                return 0;
            });
        }


        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            if (e.opctx && e.opctx.ignore) return;
            switch (e.id.toLowerCase()) {
                case 'fqty':
                    var oldqty = that.Model.getValue({ id: 'fqty', row: e.row });
                    if (parseInt(e.value) <= 0) {
                        e.result = true;
                        yiDialog.mt({ msg: "不允许为0与负数", skinseq: 2 });
                        e.value = oldqty;
                    }
                    break;
                case 'fpurorderqty':
                    var parentPage = Index.getPage(that.parentpageid);
                    var parentrows = parentPage.Model.getEntryData({ id: "fentity" });
                    var entry = that.Model.getEntryRowData({ id: 'fbdfldentity', row: e.row });
                    var matEntry = parentrows.filter(a => a.fmaterialid.id == entry.fproductid);
                    let qty = 0;
                    for (var i = 0; i < matEntry.length; i++) {
                        qty += matEntry[i].fbizqty;
                    }
                    var oldqty = that.Model.getValue({ id: 'fpurorderqty', row: e.row });
                    if (parseInt(e.value) < parseInt(qty)) {
                        e.result = true;
                        yiDialog.mt({ msg: "已采购商品，采购数量不允许减少。", skinseq: 2 });
                        e.value = oldqty;
                    }
                    break;
            }

        }

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;

            var index = 0;
            var t1 = $("#promotions").children();
            for (var i = 0; i < t1.length; i++) {
                //
                if (t1[i].className == "active") {
                    index = i;
                }
                //console.log(t1[i]);
            }

            var ds = that.Model.getSelectRows({ id: "fproductentry" });
            //促销活动套餐
            var dscomBine = that.Model.getSelectRows({ id: "fbdfldentity" });
            if (ds.length == 0 && dscomBine.length == 0) yiDialog.warn('请勾选未采购销售合同或者组合促销套餐');
            ////不允许同商品选择多个销售合同
            //for (var i = 0; i < ds.length; i++) {
            //    var prditem = ds.find(c => c.data.fproductid == ds[i].data.fproductid && c.data.id != ds[i].data.id);
            //    if (prditem && index == 0) {
            //        e.result = true;
            //        yiDialog.warn("同一个商品，当前列表不允许多选！");
            //        return;
            //    }
            //}
            //通过销售合同单号查询合同明细商品
            if (ds.length > 0 && index == 0) {
                that.OrderProduct(ds);

                switch (e.opcode.toLowerCase()) {
                    //保存
                    case 'pagesave':

                        break;
                    //保存并关闭
                    case 'pagesaveclose':
                        that.Model.close();
                        break;
                }
            }
            if (dscomBine.length > 0 && index == 1) {
                that.GetCombineProduct(dscomBine, e.opcode.toLowerCase());
            }

            e.result = true;
        };

        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'savecombineinfo':
                    if (isSuccess) {

                        var parentPage = Index.getPage(that.parentpageid);
                        parentPage.Model.refreshEntry({ id: that.Model.selsettings.fentity });
                        //that.Model.close();
                        //that.Model.refresh();
                    }
                    break;
            }
        }

        return _child;

    })(BasePlugIn);

    window.ydj_purchaseorder_promotion = window.ydj_purchaseorder_promotion || ydj_purchaseorder_promotion;

})();