; (function () {
    var ydj_purchaseinvoice = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.EntryId = 'fentity';
        _child.prototype.Fbillhead = 'fbillhead';

        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;
            //如果是新增，下推加载单据类型参数设置
            if (that.formContext.status == 'new' || that.formContext.status == 'push') {
                that.loadBillTypeParamSet();
            }
            //兼容下推过来的明细数据没有自动计算到表头的问题
            if (that.formContext.status == 'push') {
                debugger;
                //获取供应商默认税率（不通过下推带出，因为会导致两个相同的供应商合并）
                var ftaxrate =that.Model.getSimpleValue({ id: "ftaxrate" });
                var entryData = that.Model.getValue({ id: that.EntryId });
                var fsumtaxamount = 0;
                var famounts = 0;
                var ftaxamounts = 0;
                for (var i = 0, j = entryData.length; i < j; i++) {
                    fsumtaxamount = Number(Number(fsumtaxamount).toFixed(2)) + Number(Number(entryData[i].fsumtaxamount_a).toFixed(2));
                    famounts = Number(Number(famounts).toFixed(2)) + Number(Number(entryData[i].famount_a).toFixed(2));
                    ftaxamounts = Number(Number(ftaxamounts).toFixed(2)) + Number(Number(entryData[i].ftaxamount).toFixed(2));
                    that.Model.setValue({ id: 'ftaxrate_a', value: ftaxrate, row: entryData[i].id });
                    //ftaxamounts += entryData[i].ftax;
                }
                that.Model.setValue({ id: 'fsumtaxamount', value: fsumtaxamount });
                that.Model.setValue({ id: 'famount', value: famounts });
                that.Model.setValue({ id: 'ftax', value: ftaxamounts });
                //that.Model.setValue({ id: 'ftax', value: ftaxamounts });
            }
        }

        //加载单据类型参数设置
        _child.prototype.loadBillTypeParamSet = function (billTypeId) {
            var that = this;
            var typeChange = true;
            if (billTypeId === undefined) {
                billTypeId = $.trim(that.Model.getSimpleValue({ id: 'fbilltype' }));
                typeChange = false;
            }
            if (that.formContext.status === 'push') {
                typeChange = true;
            }
            if (!billTypeId) return;
            that.Model.invokeFormOperation({
                id: 'getparamset',
                opcode: 'getparamset',
                opctx: { typeChange: typeChange },
                param: {
                    domainType: Consts.domainType.bill,
                    formId: 'bd_billtype',
                    billTypeId: billTypeId
                }
            });
        };

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            debugger;
            var that = this;
            var fsumtaxamount = 0;
            var famount = 0;
            var ftaxamount = 0;
            switch (e.id.toLowerCase()) {
                case that.EntryId:
                    var dataEntry = that.Model.getValue({ id: that.EntryId });
                    //重新计算表头字段值
                    for (var i = 0; i < dataEntry.length; i++) {
                        fsumtaxamount += dataEntry[i].fsumtaxamount_a;
                    }
                    that.Model.setValue({ id: 'fsumtaxamount', value: fsumtaxamount });
                    for (var i = 0; i < dataEntry.length; i++) {
                        famount += dataEntry[i].famount_a;
                    }
                    that.Model.setValue({ id: 'famount', value: famount });
                    for (var i = 0; i < dataEntry.length; i++) {
                        ftaxamount += dataEntry[i].ftaxamount;
                    }
                    that.Model.setValue({ id: 'ftax', value: ftaxamount });
                    break;
            }
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            debugger;
            var that = this;
            if (!args.opcode) return;
            switch (args.opcode) {
                case 'pull':
                    args.result = true;
                    var customerId = that.Model.getSimpleValue({ id: 'fcustomerid' });
                    var dataEntry = that.Model.getValue({ id: that.EntryId });
                    var sourceNumber = '';
                    for (var i = 0, j = dataEntry.length; i < j; i++) {
                        sourceNumber += " and fbillno<>'" + dataEntry[i].fsourcenumber + "'"
                    }
                    that.Model.showSelectForm({
                        formId: 'ydj_purchaseorder',
                        selectMul: true,
                        dynamicParam: {
                            formId: 'ydj_purchaseinvoice',
                            filterString: "fstatus='E'and fpinvoicemount<fdealamount and fsupplierid='" + customerId + "'" + sourceNumber
                        }
                    });
                    break;
            }
        }
        //多选列表选择后的数据。
        _child.prototype.onAfterSelectFormData = function (e) {
            debugger;
            var that = this;
            var rows = that.Model.uiData;

            if (!e || !e.formId) return;

            switch (e.formId) {
                case 'ydj_purchaseorder':
                    //填充数据。
                    var data = e.data;
                    if (!data) {
                        return;
                    }
                    //定义表头的价税合计与不含税金额，税额
                    for (var i = 0, j = data.length; i < j; i++) {
                        var fsumtaxamount = data[i].ffbillamount - data[i].fpinvoicemount; //价税合计
                        var famount = (data[i].ffbillamount - data[i].fpinvoicemount) / (1 + rows.ftaxrate / 100);  //不含税金额
                        //var famount = (data[i].ffbillamount - data[i].fpinvoicemount) / (1 + rows["ftax"] / 100);  //不含税金额
                        var ftaxamount = fsumtaxamount - famount; //税额
                        var addRowData = {};
                        addRowData = {
                            'fsourcetype': {
                                fname: '采购订单',
                                fnumber: 'ydj_purchaseorder',
                                id: 'ydj_purchaseorder'
                            },
                            'fsourcenumber': data[i].fbillno,
                            'fpostaffid': {
                                id: data[i].fpostaffid,
                                fnumber: data[i].fpostaffid,
                                fname: data[i].fpostaffid_fname
                            },
                            'fpodeptid': {
                                id: data[i].fpodeptid,
                                fnumber: data[i].fpodeptid,
                                fname: data[i].fpodeptid_fname
                            },
                            'fsumtaxamount_a': fsumtaxamount,
                            'famount_a': famount,
                            'ftaxrate_a': rows.ftaxrate,
                            'ftaxamount': ftaxamount,
                        };
                        that.Model.addRow({ id: that.EntryId, data: addRowData });
                        //fsumtaxamounts += fsumtaxamount;
                        //famounts += famount;
                        //ftaxamounts += ftaxamount;
                    }
                    //that.Model.setValue({ id: 'fsumtaxamount_a', value: fsumtaxamount });
                    //that.Model.setValue({ id: 'famount_a', value: famounts });
                    var dataEntry = that.Model.getValue({ id: that.EntryId });
                    //重新计算表头字段值
                    var fsumtaxamount_mx = 0;
                    var famount_mx = 0;
                    var ftaxamount_mx = 0;
                    for (var i = 0; i < dataEntry.length; i++) {
                        fsumtaxamount_mx += dataEntry[i].fsumtaxamount_a;
                    }
                    that.Model.setValue({ id: 'fsumtaxamount', value: fsumtaxamount_mx });
                    for (var i = 0; i < dataEntry.length; i++) {
                        famount_mx += dataEntry[i].famount_a;
                    }
                    that.Model.setValue({ id: 'famount', value: famount_mx });
                    for (var i = 0; i < dataEntry.length; i++) {
                        ftaxamount_mx += dataEntry[i].ftaxamount;
                    }
                    that.Model.setValue({ id: 'ftax', value: ftaxamount_mx });
                    break;
                    //that.Model.setValue({ id: 'ftax', value: ftaxamounts });

                    //填充表头数据
                    //if ($.trim(that.Model.getValue({ id: 'faddress' })) == '') {
                    //    that.Model.setValue({ id: 'faddress', value: data[0].faddress });
                    //}
                    //if ($.trim(that.Model.getValue({ id: 'fphone' })) == '') {
                    //    that.Model.setValue({ id: 'fphone', value: data[0].fphone });
                    //}
                    e.result = true;
                    break;
            }

        };
        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            var dataEntry = that.Model.getEntryData({ id: that.EntryId });
            var fsumtaxamount = 0;
            var famount = 0;
            var ftaxamount = 0;
            debugger
            switch (e.id.toLowerCase()) {
                case 'ftaxrate_a':
                    var val = e.value;
                    if (val > 100 || val < 0) {
                        yiDialog.mt({ msg: '税率范围为[0, 100]！', skinseq: 2 });
                        setTimeout(function () {
                            that.Model.setValue({ id: 'ftaxrate_a', value: 0, row: e.row  });
                        }, 10);
                        //that.Model.setValue({ id: 'ftaxrate_a', value: 0, row:e.row });
                    }
                    break;
                //切换供应商应该清空明细 以及表头数据
                case 'fcustomerid':
                    that.Model.deleteEntryData({ id: that.EntryId });
                    that.Model.setValue({ id: 'fsumtaxamount', value: 0 });
                    that.Model.setValue({ id: 'famount', value: 0 });
                    that.Model.setValue({ id: 'ftax', value: 0 });
                    break;
                case 'fsumtaxamount_a':

                    for (var i = 0; i < dataEntry.length; i++) {
                        fsumtaxamount += dataEntry[i].fsumtaxamount_a;
                    }
                    that.Model.setValue({ id: 'fsumtaxamount', value: fsumtaxamount });
                    break;
                case 'famount_a':
                    for (var i = 0; i < dataEntry.length; i++) {
                        famount += dataEntry[i].famount_a;
                    }
                    that.Model.setValue({ id: 'famount', value: famount });
                    //var rows = that.Model.getSelectRows({ id: that.EntryId });
                    //that.Model.setValue({ id: 'fsumtaxamount_a', value: rows[0].data["famount_a"] + rows[0].data["ftaxamount"] });
                    break;
                case 'ftaxamount':
                    for (var i = 0; i < dataEntry.length; i++) {
                        ftaxamount += dataEntry[i].ftaxamount;
                    }
                    that.Model.setValue({ id: 'ftax', value: ftaxamount });
                    //var rows = that.Model.getSelectRows({ id: that.EntryId });
                    //that.Model.setValue({ id: 'fsumtaxamount_a', value: rows[0].data["famount_a"] + rows[0].data["ftaxamount"] });
                    break;
            }
        }
        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.EntryId:
                    e.result = { multiselect: false };
                    break;
            }
        };

        return _child;
    })(BillPlugIn);
    window.ydj_purchaseinvoice = window.ydj_purchaseinvoice || ydj_purchaseinvoice;
})();