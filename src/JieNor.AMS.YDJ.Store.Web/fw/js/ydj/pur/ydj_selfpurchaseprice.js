/*
 * description:商品价目业务控制插件
 * author:
 * create date:
 * modify by:
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/bas/ydj_price.js
*/
; (function () {
    var ydj_selfpurchaseprice = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        _child.prototype.entry = 'fentry';

        //业务插件内容写在此
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.hideOrShowEntryField();
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e || !e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.entry:
                    e.result = { multiselect: false };
                    break;
            }
        };

        //表格按钮点击事件
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.btnid.toLowerCase()) {
                case 'g_record': //批录按钮
                    {
                        //取消某个字段的批录功能
                        switch (e.fieldId.toLowerCase()) {
                            case 'funitid_e':
                                //取消平台的批录逻辑
                                e.cancel = true;
                                break;
                        }
                    }
                    break;
            }
            switch (e.id.toLowerCase()) {
                case that.entry:
                    var rowData = that.Model.getEntryRowData({ id: that.entry, row: e.row });
                    switch (e.btnid.toLowerCase()) {
                        //确认
                        case 'confirm':
                            that.Model.invokeFormOperation({
                                id: 'Confirm',
                                opcode: 'Confirm',
                                opname: '确认',
                                selectedRows: [{ PKValue: that.Model.pkid,entrypkvalue: rowData.id, EntityKey: 'fentry'}],
                                param: {
                                    formId: 'ydj_selfpurchaseprice',
                                    domainType: Consts.domainType.bill
                                }
                            });
                            break;
                        //取消确认
                        case 'cancelconfirm':
                            that.Model.invokeFormOperation({
                                id: 'CancelConfirm',
                                opcode: 'CancelConfirm',
                                opname: '取消确认',
                                selectedRows: [{ PKValue: that.Model.pkid,entrypkvalue: rowData.id, EntityKey: 'fentry'}],
                                param: {
                                    formId: 'ydj_selfpurchaseprice',
                                    domainType: Consts.domainType.bill
                                }
                            });
                            break;
                    }
                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var status = $.trim(that.Model.getSimpleValue({ id: 'fconfirmstatus', row: e.row }));
            switch (e.id.toLowerCase()) {
            	case 'fproductid_e'://商品
            	case 'fattrinfo'://辅助属性
                case 'fprice'://含税单价
                case 'ftaxrate'://税率
                case 'fpurprice'://采购价
                case 'fstartdate_e'://生效日期
                case 'fexpiredate_e'://失效日期
                    if(status == '2'){
                    	e.result.enabled = false;
                        return;
                    }
                    break;
            }
        }

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            var listId = that.Model.getSelectRows({});
            var domainType = that.formContext.domainType;
            if (!args.opcode) return;
            switch (args.opcode) {
                case 'recreateprice':
                case 'commonchangeprice':
                    if (listId.length === 0) {
                        args.result = true;
                        yiDialog.mt({ msg: '请先选择数据！', skinseq: 4 });
                        return;
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'ftype':
                    that.hideOrShowEntryField(e.value.id);
                    break;
            }
        };

        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;
            var Status = e.data.fconfirmstatus;
            if (e.oper || !e.data) {
                return;
            } else {
                e.cancel = true;
                if (Status && Status.id === '1') {
                    return e.result = [{
                        id: 'confirm',
                        text: '确认',
                        rowid: e.row
                    }];
                } else if (Status && Status.id === '2') {
                    return e.result = [{
                        id: 'cancelconfirm',
                        text: '取消确认',
                        rowid: e.row
                    }];
                }
            }
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                //确认
                case 'confirm':
                case 'cancelconfirm':
                    if (isSuccess) {
                        //刷新页面
                        that.Model.refresh();
                    }
                    break;
            }
        };

        //根据报价类型显示或隐藏部分列
        _child.prototype.hideOrShowEntryField = function (type) {
            var that = this;
            type = type || that.Model.getSimpleValue({ id: 'ftype' });
            type = $.trim(type);
            if (type) {
                var visible = type === 'quote_type_02';
                that.Model.setVisible({
                    id: ['flengthmax', 'flengthmin', 'fwidthmax', 'fwidthmin', 'fthickmax', 'fthickmin'],
                    value: visible,
                    batch: true
                });
            }
        };



        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {                 
                //商品基础资料
                case 'fproductid_e':
                    var fsupplierid = that.Model.getValue({ "id": "fsupplierid" })
                    var srcPara = {                       
                        supplierid: fsupplierid ? fsupplierid.id : "",
                        supplierNo: fsupplierid ? fsupplierid.id : "",
                        supplierName: fsupplierid ? fsupplierid.id : "",
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
            }
        };




        return _child;
    })(BillPlugIn);
    window.ydj_selfpurchaseprice = window.ydj_selfpurchaseprice || ydj_selfpurchaseprice;
})();