/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/ydj/pur/ydj_purchaseorder.js
 */
; (function () {
    var pur_poreturndialog = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.fentity = 'fentity';//退货明细

        //初始化页面插件
        _child.prototype.onInitialized = function (e) {
            var that = this;


        };

        //模型数据初始化完毕
        _child.prototype.onModelDataCreated = function (e) {
            //if (!e.data) {
            //    e.data = {};
            //}
            //var that = this;
            //var parentModel = that.Model.getParentModel();
            //var parentProductDetail = parentModel.getEntryData({ id: 'fentity' });
            //var myReturnEntryDetail = $.extend(true, [], parentProductDetail);
            //e.data.fentity = myReturnEntryDetail;
            //for (var row = 0; row < myReturnEntryDetail.length; row++) {
            //    var rowObj = myReturnEntryDetail[row];
            //    rowObj.fqty = 0;
            //    rowObj.fpoentryid = rowObj.id;
            //    rowObj.fpointerid = parentModel.uiData.id;
            //    rowObj.famount = rowObj.fqty * rowObj.fprice;
            //}
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.fentity:
                    e.result = { multiselect: true };
                    break;
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //退货
                case 'tbconfirm':
                    var data = {};
                    data.param = {};
                    data.param.returnType = that.Model.getSimpleValue({ id: 'freturntype' });
                    data.param.returnData = [];
                    data.param.refundAmount = that.Model.getSimpleValue({ id: 'frefundamount' });
                    data.param.totalAmount = that.Model.getSimpleValue({ id: 'ftotalamount' });
                    data.param.returnReason = that.Model.getSimpleValue({ id: 'freturnreason' });

                    var selRows = that.Model.getSelectRows({ id: 'fentity' });
                    if (!selRows || selRows.length === 0) {
                        e.result = true;
                        yiDialog.mt({ msg: '请至少选择一行需要退货的商品明细！', skinseq: 2 });
                        return;
                    }
                    debugger 
                    var feedbackentryqty = 0;
                    var outreturnQty = 0;
                    //收集当前页面的数据
                    for (var row = 0; row < selRows.length; row++) {
                        var returnObj = {};
                        returnObj.pkValue = selRows[row].data.fpostockininterid;
                        returnObj.entityKey = 'fentity';
                        returnObj.entryPkValue = selRows[row].data.fpostockinentryid;
                        returnObj.returnQty = selRows[row].data.fqty;
                        outreturnQty = outreturnQty + returnObj.returnQty;
                        returnObj.returnNote = selRows[row].data.fnote;
                        returnObj.feedbackinterid = selRows[row].data.ffeedbackinterid;
                        returnObj.feedbackentryid = selRows[row].data.ffeedbackentryid;
                        returnObj.feedbackentryqty = selRows[row].data.ffeedbackentryqty;
                        feedbackentryqty = selRows[row].data.ffeedbackentryqty;
                        if (returnObj.returnQty <= 0) {
                            e.result = true;
                            yiDialog.mt({ msg: '选择退货的明细行，退货数量必须大于0！', skinseq: 2 });
                            return;
                        }
                        //if (Consts.isdirectsale && returnObj.feedbackinterid) {
                        //    if (returnObj.returnQty != (selRows[row].data.finstockqty - selRows[row].data.freturnqty)) {
                        //        e.result = true;
                        //        yiDialog.mt({ msg: '退货数量需等于售后商品数量，请修改！', skinseq: 2 });
                        //        return;
                        //    }
                        //}

                        if (returnObj.returnQty > selRows[row].data.finstockqty - selRows[row].data.freturnqty) {
                            e.result = true;
                            yiDialog.mt({ msg: '选择退货的明细行，退货数量不能超过可退数量（入库数量-已退货数量）！', skinseq: 2 });
                            return;
                        }

                        data.param.returnData.push(returnObj);
                    }

                    if (Consts.isdirectsale && returnObj.feedbackinterid) {
                        if (feedbackentryqty != outreturnQty) {
                            e.result = true;
                            yiDialog.mt({ msg: '退货数量需等于售后商品数量，请修改！', skinseq: 2 });
                            return;
                        }
                    }
                    //校验收集的数据
                    if (!data.param.returnType) {
                        e.result = true;
                        yiDialog.mt({ msg: '请选择退货方式！', skinseq: 2 });
                        return;
                    }

                    data.param.returnData = JSON.stringify(data.param.returnData);

                    that.Model.setReturnData(data);

                    if (data.param.returnType === 'postockreturn_scenetype_02' && data.param.refundAmount > data.param.totalAmount) {
                        e.dialog = { simpleMessage: '确定实际退款金额要超过本次所退货物的总金额吗？', title: '警告' }
                        return;
                    }
                    break;
                //取消
                case 'tbcancel':
                    that.Model.setReturnData({});
                    break;
            }
        };


        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                //     //数量单价的变化，影响金额
                // case 'fqty':
                // case 'fprice':
                //     var qty = that.Model.getSimpleValue({ id: 'fqty', row: e.row });
                //     var price = that.Model.getSimpleValue({ id: 'fprice', row: e.row });
                //     that.Model.setValue({ id: 'famount', row: e.row, value: qty * price });

                //     //触发分录行点击事件实现求和显示金额汇总
                //     that.onEntryRowClick({ id: 'fentity', row: e.row });
                //     break;
                case 'freturntype':
                    that.onEntryRowClick({ id: 'fentity', row: e.row });
                    break;
            }
        };

        //表格选中行变化时触发
        _child.prototype.onSelectedRowsChanged = function () {
            var that = this;
            that.onEntryRowClick();
        };

        //触发分录行点击事件实现求和显示金额汇总
        _child.prototype.onEntryRowClick = function (args) {
            var that = this;
            var selRows = that.Model.getSelectRows({ id: 'fentity' });
            if (!selRows || selRows.length === 0) {
                selRows = [];
            }
            var dTotalAmt = 0;
            var returnType = that.Model.getSimpleValue({ id: 'freturntype' });
            for (var i = 0; i < selRows.length; i++) {
                if (selRows[i].data && returnType === 'postockreturn_scenetype_02') {
                    dTotalAmt += selRows[i].data.famount;
                }
            }
            that.Model.setValue({ id: 'FTotalAmount', value: dTotalAmt });
            that.Model.setValue({ id: 'frefundamount', value: dTotalAmt });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'getprices':

                    break;
            }
        };


        return _child;
    })(BasePlugIn);
    window.pur_poreturndialog = window.pur_poreturndialog || pur_poreturndialog;
})();