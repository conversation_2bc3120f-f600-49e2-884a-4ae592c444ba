using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
//using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using ServiceStack.Web;

namespace JieNor.AMS.YDJ.WeiXin.AppService
{
    [InjectService]
    public class WeiXinRequestInterceptor : IServiceInterceptor
    {
        public int Order
        {
            get
            {
                return 10;
            }
        }


        [InjectProperty]
        protected ILogServiceEx Logger { get; set; }

        public void Execute(IRequest req, IResponse res)
        {
            //

            //this.Logger?.Info("request url :" + req.AbsoluteUri);
            //this.Logger?.Info("request body data:" + req.GetRawBody());
        }
    }
}
