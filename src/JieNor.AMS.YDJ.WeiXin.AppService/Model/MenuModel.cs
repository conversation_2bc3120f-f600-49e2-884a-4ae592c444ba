namespace JieNor.AMS.YDJ.WeiXin.AppService
{
    public class MenuModel
    {
        public string id { get; set; }
        /// <summary>
        /// 菜单名
        /// </summary>
        public string name { get; set; }
        /// <summary>
        ///链接
        /// </summary>
        public string url { get; set; }
        /// <summary>
        /// 父级
        /// </summary>
        public string parentid { get; set; }
        /// <summary>
        /// 菜单层级
        /// </summary>
        public int level { get; set; }
        /// <summary>
        /// 组id
        /// </summary>
        public GroupModel group { get; set; }
        /// <summary>
        /// 菜单类型
        /// </summary>
        public menuType menuType { get; set; }
        /// <summary>
        /// 用于菜单排序
        /// </summary>
        public int sort { get; set; }
        /// <summary>
        /// 响应类型View/Click
        /// </summary>
        public int responseType { get; set; }

        public MenuModel()
        {
            group = new GroupModel();
        }

    }
    public enum menuType
    {
        普通菜单 = 0,
        个性化菜单 = 1
    }
}
