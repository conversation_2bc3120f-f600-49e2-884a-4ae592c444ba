using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.Containers;

namespace JieNor.AMS.YDJ.WeiXin.AppService.Weixin.Cache
{
    //public interface IContainerCacheStragegy : IBaseCacheStrategy
    //{
    //}

    public interface IContainerItemCollection : IDictionary<string, IBaseContainerBag>
    {
    }

    public class ContainerItemCollection : Dictionary<string, IBaseContainerBag>, IContainerItemCollection
    {
    }


    public interface IContainerCacheStragegy : /*IContainerCacheStragegy,*/ IBaseCacheStrategy<string, IContainerItemCollection>
    //where TContainerBag : class, IBaseContainerBag
    {
        void UpdateContainerBag(string key, IBaseContainerBag containerBag);
    }
}
