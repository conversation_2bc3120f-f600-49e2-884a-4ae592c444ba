using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.User;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.Entities;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.CommonAPIs;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.Entities;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.Entities.Menu;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.OAuth;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.Groups;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP;
using System;


namespace JieNor.AMS.YDJ.WeiXin.AppService
{
    public class WeiXinHelper
    {
        //private readonly static MemberMessage mm = new MemberMessage();
        #region Token
        /// <summary>
        /// 使用完整的应用凭证获取Token
        /// </summary>
        /// <returns></returns>
        public static string GetServerAccessToken
        {
            get
            {
                string ServerAppId = WebConfig.GetServerappId;
                string ServerAppSecret = WebConfig.GetServerappSecret;
                return AccessTokenContainer.TryGetAccessToken(ServerAppId, ServerAppSecret);
            }
        }
        #endregion

        #region 获取用户信息
        /// <summary>
        /// 新关注用户追加到后台
        /// </summary>
        /// <param name="accessToken"></param>
        /// <param name="openId"></param>
        /// <returns></returns>
        public static void AddUserToBackStage(string accessToken, string openId, string shareOpenId = null)
        {
            //UserInfoJson userInfo = UserApi.Info(accessToken, openId);

            //var wxInvoker = this.TryResolve<IWeiXinInvoker>();
            //var resp = wxInvoker.Invoke(new CallerContext()
            //{
            //    UserId = "wxserver",
            //    UserName = "",
            //}, new CommonBillDTO()// CommonFormDTO()
            //{
            //    FormId = "wx_micromember",
            //    OperationNo = "Save",
            //    BillData =
            //    //BillData="{\"f1\":\"a\"}",
            //    //SimpleData = new System.Collections.Generic.Dictionary<string, string>() { }
            //});

            //(resp as DynamicDTOResponse).OperationResult.SrvData


            //var dbService = this.TryResolve<IDBService>();
            //var dm = this.TryResolve<IDataManager>();
            //var pkService = this.TryResolve<IDataEntityPkService>();
            //var userCtx = this.TryResolve<UserContext>();
            //var metaService = this.TryResolve<IMetaModelService>();

            //var hForm = metaService.LoadFormModel(userCtx, "ydj_wx_userbinding");
            //var userBindObj = hForm.GetDynamicObjectType(userCtx).CreateInstance() as DynamicObject;
            ////userBindObj["fusername"] = reqDto.UserName;
            //userBindObj["echoStr"] = "";

            ////写数据库的过程
            //pkService.AutoSetPrimaryKey(userCtx, userBindObj, hForm.GetDynamicObjectType(userCtx));
            //dm.DataEntityType = hForm.GetDynamicObjectType(userCtx);
            //dm.Save(userBindObj);
            //Model.Micro use = new Model.Micro();
            //use.Id = userInfo.openid;
            //use.Mname = userInfo.nickname;
            //use.MLang = userInfo.language;
            //use.Mimg = userInfo.headimgurl;
            ////use.CTime = Comm.TimeStampToDateTime(userInfo.subscribe_time);//关注时间
            //use.Sex = (Comm.Emun.Sex)userInfo.sex;
            //use.Province = userInfo.province;
            //use.Country = userInfo.country;
            //use.City = userInfo.city;
            //BLL.Micro.Add(use);
        }
        /// <summary>
        /// 获取关注者OpenId列表
        /// </summary>
        /// <param name="accessToken"></param>
        /// <param name="next_openid"></param>
        /// <returns></returns>
        public static List<string> GetOpenIDList(string accessToken, string next_openid = "")
        {
            List<string> OpenIdList = new List<string>();
            do
            {
                OpenIdResultJson OpenIdResult = UserApi.Get(accessToken, next_openid);
                if (OpenIdResult.data != null)
                {
                    OpenIdList.AddRange(OpenIdResult.data.openid);
                }
                next_openid = OpenIdResult.next_openid;
            }
            while (!string.IsNullOrEmpty(next_openid));
            return OpenIdList;
        }


        /// <summary>
        /// 获取AccessToken
        /// </summary>
        /// <param name="appId"></param>
        /// <param name="secret"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        public static OAuthAccessTokenResult GetAccessToken(string appId, string secret, string code)
        {
            return OAuthApi.GetAccessToken(appId, secret, code);
        }
        /// <summary>
        /// 创建分组
        /// </summary>
        /// <param name="Name">分组名称</param>
        /// <param name="Number">返回分组编码</param>
        /// <returns>True/False</returns>
        public static bool CreateGroup(string Name, out int Number)
        {

            CreateGroupResult result = GroupsApi.Create(GetServerAccessToken, Name);//创建分组
            if (result.group != null)
            {
                Number = result.group.id;
                return true;
            }
            Number = 0;
            return false;
        }
        /// <summary>
        /// 移动用户分组
        /// </summary>
        /// <param name="MicroID">MicroID</param>
        /// <param name="GroupNumber">分组编号</param>
        /// <returns></returns>
        public static bool MoveUserGroup(string MicroID, int GroupNumber)
        {
            WxJsonResult result = GroupsApi.MemberUpdate(GetServerAccessToken, MicroID, GroupNumber);
            if (result.errmsg == "ok")
            {
                return true;
            }
            return false;
        }

        #endregion

        #region （个性化）菜单
        /// <summary>
        /// 创建（个性化）菜单
        /// </summary>
        /// <returns></returns>
        public static string CreateMenu(List<MenuModel> list)
        {
            if (list.Count == 0)
            {
                return "暂无可推送菜单";
            }
            var accessToken = GetServerAccessToken;
            WxJsonResult s1 = CommonApi.DeleteMenu(accessToken);//删除现有菜单
            string _Result = string.Empty;
            List<MenuModel> pop = list.Where(t => t.menuType == menuType.普通菜单).ToList();
            List<MenuModel> diy = list.Where(t => t.menuType == menuType.个性化菜单).ToList();
            #region 普通菜单推送
            _Result = "普通菜单推送结果如下：";
            ButtonGroup btnColl = new ButtonGroup();
            foreach (var one in pop.Where(t => t.level == 1).OrderBy(s => s.sort))
            {
                if (one.responseType == 0)
                {
                    SubButton zbtn = new SubButton();
                    zbtn.name = one.name;
                    foreach (var two in pop.Where(t => t.level == 2 && t.parentid == one.id).OrderBy(s => s.sort))
                    {
                        string Url = two.url;
                        if (Url.IndexOf(WebConfig.WebUrl) > -1)
                        {
                            Url = OAuthApi.GetAuthorizeUrl(WebConfig.GetServerappId, Url, "", OAuthScope.snsapi_base);
                        }
                        zbtn.sub_button.Add(new SingleViewButton()
                        {
                            url = Url,
                            name = two.name
                        });
                    }
                    btnColl.button.Add(zbtn);
                }
                else
                {
                    string Url = one.url;
                    if (Url.IndexOf(WebConfig.WebUrl) > -1)
                    {
                        Url = OAuthApi.GetAuthorizeUrl(WebConfig.GetServerappId, Url, "", OAuthScope.snsapi_base);
                    }
                    btnColl.button.Add(new SingleViewButton()
                    {
                        url = Url,
                        name = one.name
                    });
                }
            }
            WxJsonResult result1 = CommonApi.CreateMenu(accessToken, btnColl);
            _Result += "菜单推送：" + (result1.errmsg == "ok" ? "成功" : "失败");
            #endregion
            #region 个性化菜单
            List<MenuModel> group = diy.Distinct(new MenuComparint()).ToList();//去重复的组
            if (group.Count > 0)
            {
                _Result = "个性化菜单推送结果如下：";
                foreach (var item in group)
                {
                    ConditionalButtonGroup BtnGroup = new ConditionalButtonGroup();
                    foreach (var One in list.Where(t => t.level == 1 && t.group.id == item.group.id).OrderBy(s => s.sort))//遍历权限一级菜单
                    {
                        if (One.responseType == 0)//有二级菜单
                        {
                            SubButton SubBtn = new SubButton();
                            SubBtn.name = One.name;
                            foreach (var Two in list.Where(t => t.level == 2 && t.parentid == One.id).OrderBy(s => s.sort))//遍历一级菜单下耳机菜单
                            {
                                if (Two.responseType == 1)//View
                                {
                                    SubBtn.sub_button.Add(new SingleViewButton()
                                    {
                                        name = Two.name,
                                        url = OAuthApi.GetAuthorizeUrl(WebConfig.GetServerappId, Two.url, "", OAuthScope.snsapi_base)
                                    });
                                }
                            }
                            BtnGroup.button.Add(SubBtn);
                        }
                        else//没有二级菜单
                        {
                            BtnGroup.button.Add(new SingleViewButton()
                            {
                                name = One.name,
                                url = OAuthApi.GetAuthorizeUrl(WebConfig.GetServerappId, One.url, "", OAuthScope.snsapi_base)
                            });
                        }
                    }
                    MenuMatchRule MMR = new MenuMatchRule();
                    MMR.group_id = item.group.id;
                    BtnGroup.matchrule = MMR;
                    if (BtnGroup.button.Count > 0)
                    {
                        CreateMenuConditionalResult result = CommonApi.CreateMenuConditional(accessToken, BtnGroup);
                        _Result += "角色" + item.group.name + "菜单推送：" + (result.menuid > 0 ? "成功" : "失败");
                    }
                }
            }
            #endregion
            return _Result;
        }




        #endregion


        #region 推送模板消息

        public static Weixin.MP.AdvancedAPIs.TemplateMessage.SendTemplateMessageResult PutMessage(string openId, string templateId, string url, object data)
        {
            var appid = WebConfig.GetServerappId;
            var secret = WebConfig.GetServerappSecret;
            var accessToken = AccessTokenContainer.TryGetAccessToken(appid, secret);
            return TemplateApi.SendTemplateMessage(accessToken, openId, templateId, "#FF0000", url, data);
        }



        #endregion



        public static DateTime TimeStampToDateTime(long timeStamp)
        {
            DateTime dtStart = TimeZone.CurrentTimeZone.ToLocalTime(new DateTime(1970, 1, 1));
            long lTime = long.Parse(timeStamp + "0000000");
            TimeSpan toNow = new TimeSpan(lTime);
            DateTime dtResult = dtStart.Add(toNow);
            return dtResult;
        }
    }
}
