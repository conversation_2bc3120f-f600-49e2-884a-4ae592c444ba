using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.DataTransferObject.Rpt
{


    /// <summary>
    /// 库存查询参数
    /// </summary>
   public  class StockSynthesizeQueryParaInfo
    {
        private int _PageIndex;
        /// <summary>
        /// 当前页码，默认第1页
        /// </summary>
        public int PageIndex
        {
            get { return _PageIndex; }
            set { _PageIndex = value < 1 ? 1 : value; }
        }

        private int _PageSize;
        /// <summary>
        /// 每页条数，默认每页10条
        /// </summary>
        public int PageSize
        {
            get { return _PageSize; }
            set { _PageSize = value < 1 ? 10 : value; }
        }

        /// <summary>
        /// 仓库ID
        /// </summary>
        public string StorehouseId { get; set; }

        /// <summary>
        /// 品类ID
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 品牌ID集合
        /// </summary>
        public List<string> BrandIds { get; set; }

        /// <summary>
        /// 风格ID集合
        /// </summary>
        public List<string> StyleIds { get; set; }

        /// <summary>
        /// 空间ID集合
        /// </summary>
        public List<string> SpaceIds { get; set; }

        ///// <summary>
        ///// 最低价
        ///// </summary>
        //public decimal MinPrice { get; set; }

        ///// <summary>
        ///// 最高价
        ///// </summary>
        //public decimal MaxPrice { get; set; }

        /// <summary>
        /// 系列id
        /// </summary>
        public string SeriesId { get; set; }

        /// <summary>
        /// 是否排除物流跟踪号商品  0不排除 1排除 默认传0
        /// </summary>
        public int IsFilterFmtono { get; set; }

        /// <summary>
        /// 商品id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 库存状态id
        /// </summary>
        public string StockStatusId { get; set; }

        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }


        /// <summary>
        /// 来源业务单据标识（从哪个业务单据上查询库存）
        /// </summary>
        public string SrcFormId { get; set; }


        /// <summary>
        /// 单据类型名称
        /// </summary>
        public string BillTypeName { get; set; }

        /// <summary>
        /// 单据类型编码
        /// </summary>
        public string BillTypeNo { get; set; }



        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// 排序依据：按什么规则排序，比如按价格排序
        /// </summary>
        public string Sortby { get; set; }

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        private string _Sortord = "desc";

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        public string Sortord
        {
            get { return _Sortord; }
            set { _Sortord = string.IsNullOrWhiteSpace(value) ? "desc" : value; }
        }

        public DateTime Orderdate { get; set; }
        /// <summary>
        /// 查询数据类型 self:本组织数据，parentorg:上级组织
        /// </summary>
        public string SelectDataType { get; set; } = "self";
    }
}
