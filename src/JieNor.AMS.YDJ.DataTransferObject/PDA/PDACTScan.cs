using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.DataTransferObject.PDA
{
    public class PDACTScan
    {
        public string pda { get; set; }
        /// <summary>
        /// 调拨类型
        /// </summary>
        public string fbilltype { get; set; }

        public string ftype { get; set; }

        public List<Entry> Entrys { get; set; }

    }

    public class Entry
    {
        /// <summary>
        /// 商品
        /// </summary>
        public string fmaterialid { get; set; }

        /// <summary>
        /// 商品编码
        /// </summary>
        public string materialcode { get; set; }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string materialname { get; set; }

        /// <summary>
        /// 辅助属性
        /// </summary>
        public string fattrinfo { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string fcustomdesc { get; set; }

        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string fmtono { get; set; }

        /// <summary>
        /// 货主类型
        /// </summary>
        public string fownertype { get; set; }

        /// <summary>
        /// 货主
        /// </summary>
        public string fownerid { get; set; }

        /// <summary>
        /// 调拨数量
        /// </summary>
        public string ftransqty { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string fstorehouseid { get; set; }

        /// <summary>
        /// 仓位
        /// </summary>
        public string fstorelocationid { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public string fstockstatus { get; set; }

        /// <summary>
        /// 条码数量
        /// </summary>
        public string fbarqty { get; set; }

        /// <summary>
        /// 调入仓库
        /// </summary>
        public string finstorehouseid { get; set; }

        /// <summary>
        /// 调入仓位
        /// </summary>
        public string finstorelocationid { get; set; }

        public List<ScanBarCodeInfo> Barcodeinfos { get; set; }
    }
    public class ScanBarCodeInfo
    {

        /// <summary>
        /// 条码
        /// </summary>
        public string barcode { get; set; }

        public string isunpacking { get; set; }

        public int packingqty { get; set; } = -1;
        ///// <summary>
        ///// 扫描人
        ///// </summary>
        //public string scanuser { get; set; }
        ///// <summary>
        ///// 扫描时间
        ///// </summary>
        //public string scantime { get; set; }
    }
}
