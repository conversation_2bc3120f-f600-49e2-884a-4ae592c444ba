using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.DataTransferObject.IncomeDisburse
{
    /// <summary>
    /// 收支纪录实体定义
    /// </summary>
    public class IncomeDisburseRecord
    {
        public IncomeDisburseRecord()
        {
            this.SettleDate = DateTime.Now;
            this.AccountSettleInfo = new List<AccountSettleInfo>();
        }

        /// <summary>
        /// 交易方向：收还是支，必传
        /// </summary>
        public string Direction { get; set; }

        /// <summary>
        /// 业务方向：收还是支
        /// </summary>
        public string BizDirection { get; set; }

        /// <summary>
        /// 支付场景类型：充值，扣款，必传
        /// </summary>
        public string SceneType { get; set; }

        /// <summary>
        /// 不传时默认为当前日期：可不传
        /// </summary>
        public DateTime SettleDate { get; set; }

        /// <summary>
        /// 结算代理类型（指向代收单位类型），可不传
        /// </summary>
        public string SettleAgentType { get; set; }

        /// <summary>
        /// 结算代理内码（指向代收单位），可不传
        /// </summary>
        public string SettleAgentId { get; set; }

        /// <summary>
        /// 交易主体业务类型：必传
        /// </summary>
        public string TranFormId { get; set; }

        /// <summary>
        /// 交易主体主键：必传
        /// </summary>
        public string TranBillId { get; set; }

        /// <summary>
        /// 关联业务单据类型：可不传
        /// </summary>
        public string LinkFormId { get; set; }
        /// <summary>
        /// 关联业务单据内码：可不传
        /// </summary>
        public string LinkBillId { get; set; }
        /// <summary>
        /// 关联业务单据编码：可不传
        /// </summary>
        public string LinkBillNo { get; set; }

        /// <summary>
        /// 关联业务交易流水号：可不传
        /// </summary>
        public string LinkTranId { get; set; }

        /// <summary>
        /// 交易凭据文件id,可不传
        /// </summary>
        public string AttachId { get; set; }

        /// <summary>
        /// 是否自动确认本记录：可不传
        /// </summary>
        public bool AutoConfirm { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 我方银行账号基础资料Id
        /// </summary>
        public string MyBankId { get; set; }

        /// <summary>
        /// 对方银行账号基础资料Id
        /// </summary>
        public string SynBankId { get; set; }

        /// <summary>
        /// 对方开户行
        /// </summary>
        public string SynBankName { get; set; }

        /// <summary>
        /// 对方银行卡号
        /// </summary>
        public string SynBankNum { get; set; }

        /// <summary>
        /// 对方银行账户名称
        /// </summary>
        public string SynAccountName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 卖场结算号
        /// </summary>
        public string MarketSettleNo { get; set; }

        /// <summary>
        /// 对方账号
        /// </summary>
        public string OtherPartyAccount { set; get; }

        /// <summary>
        /// 所有账户的结算信息
        /// </summary>
        public IList<AccountSettleInfo> AccountSettleInfo { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 手工单号
        /// </summary>
        public string fwithin { get; set; }

        /// <summary>
        /// 款项说明
        /// </summary>
        public string paymentdesc { get; set; }

        /// <summary>
        /// 客户收款账号
        /// </summary>
        public string Cusacount { get; set; }

        /// <summary>
        /// 收款小票号
        /// </summary>
        public string ReceiptNo { get; set; }

        /// <summary>
        /// 联合开单
        /// </summary>
        public List<OrderJoinStaffModel> JoinStaffs { get; set; }

        /// <summary>
        /// 业务日期(取源单合同对应字段)
        /// </summary>
        public string OrderDate { get; set; }

        /// <summary>
        /// 单据类型(取源单合同对应字段)
        /// </summary>
        public string OrderBilltype { get; set; }
        
        /// <summary>
        /// 合同编号
        /// </summary>
        public string OrderNo { get; set; }
    }

    /// <summary>
    /// 联合开单数据模型    fdutyentry
    /// </summary>
    public class OrderJoinStaffModel
    {
        public string Id { get; set; }

        /// <summary>
        /// 主要负责    fismain
        /// </summary>
        public bool IsMain { get; set; }

        /// <summary>
        /// 销售员     fdutyid
        /// </summary>
        public string DutyId { get; set; }

        /// <summary>
        /// 销售员部门     fdeptid
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 比例%     fratio
        /// </summary>
        public decimal Ratio { get; set; }

        /// <summary>
        /// 金额      famount
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注      fdescription
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 业绩部门比例
        /// </summary>
        public decimal DeptPerfRatio { set; get; }
    }



    /// <summary>
    /// 联合开单数据模型    fdutyentry
    /// </summary>
    public class OrderRefundJoinStaffModel
    {
        public string id { get; set; }

        /// <summary>
        /// 主要负责    fismain
        /// </summary>
        public bool fismain { get; set; }

        /// <summary>
        /// 销售员     fdutyid
        /// </summary>
        public string fdutyid { get; set; }

        /// <summary>
        /// 销售员部门     fdeptid
        /// </summary>
        public string fdeptid_ed { get; set; }

        /// <summary>
        /// 比例%     fratio
        /// </summary>
        public decimal fratio { get; set; }

        /// <summary>
        /// 金额      famount
        /// </summary>
        public decimal famount_ed { get; set; }

        /// <summary>
        /// 备注      fdescription
        /// </summary>
        public string fdescription_ed { get; set; }
        
        /// <summary>
        /// 部分分成比例
        /// </summary>
        public decimal fdeptperfratio { set; get; }
    }

    /// <summary>
    /// 账户结算信息
    /// </summary>
    public class AccountSettleInfo
    {
        /// <summary>
        /// 账户名称：非必填
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// 账户内码
        /// </summary>
        public string AccountId { get; set; }

        /// <summary>
        /// 结算方式（支付方式）：账户支付时，要求账户id必填，否则账户id为空
        /// </summary>
        public string SettleType { get; set; }

        /// <summary>
        /// 银行卡号：只有结算方式为银联时才需要提供
        /// </summary>
        public string BankCardNo { get; set; }

        /// <summary>
        /// 结算金额
        /// </summary>
        public decimal Amount { get; set; }
    }
}
