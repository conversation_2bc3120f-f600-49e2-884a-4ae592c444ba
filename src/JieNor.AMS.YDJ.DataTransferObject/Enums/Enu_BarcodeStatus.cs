using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.DataTransferObject.Enums
{
    /// <summary>
    /// 条码状态枚举
    /// </summary>
    public enum Enu_BarcodeStatus : int
    {
        /// <summary>
        /// 等待入库
        /// </summary>
        WaitingInstock = 4,
        /// <summary>
        /// 已入库
        /// </summary>
        Instock = 1,

        /// <summary>
        /// 已备货，已占用，使用中
        /// </summary>
        Inused=5,

        /// <summary>
        /// 已出库
        /// </summary>
        Outstock=2,
    }
}
