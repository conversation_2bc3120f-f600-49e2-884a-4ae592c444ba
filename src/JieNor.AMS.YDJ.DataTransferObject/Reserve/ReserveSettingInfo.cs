using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.DataTransferObject.Reserve
{



    /// <summary>
    /// 预留设置信息
    /// </summary>
    public class ReserveSettingInfo
    {



        /// <summary>
        /// 预留对象类型
        /// </summary>
        public string freserveobjecttype { get; set; }

        /// <summary>
        /// 预留对象
        /// </summary>
        public string freserveobjectid { get; set; }


        /// <summary>
        /// 预留说明
        /// </summary>
        public string fdescription { get; set; }

        /// <summary>
        /// 预留需求单标识
        /// </summary>
        public string fdemandformid { get; set; }


        /// <summary>
        /// 预留需求单编号
        /// </summary>
        public string fdemandbillno { get; set; }

        /// <summary>
        /// 预留需求单id
        /// </summary>
        public string fdemandbillpkid { get; set; }


        /// <summary>
        /// 源单状态
        /// </summary>
        public string fsourcestatus { get; set; }


        /// <summary>
        /// 销售意向：预留X天后，如未成交则自动释放；
        /// 销售合同：按交货日期+X天作为预留日期
        /// </summary>
        public int freserveday { get; set; }


        public DateTime fdefautdate { get; set; }


        /// <summary>
        /// 预留需求明细信息
        /// </summary>
        public List<ReserveSettingDemandInfo> DemandEntry { get; set; } = new List<ReserveSettingDemandInfo>();


        /// <summary>
        /// 对应的预留需求单数据包（DynamicObject ）
        /// </summary>
        public object DemandBillData { get; set; }


        /// <summary>
        /// 出现货自动预留
        /// </summary>
        public bool AutoReserveWhen { get; set; }


        /// <summary>
        /// 对应的预留单id
        /// </summary>
        public string freservepkid { get; set; }

        /// <summary>
        /// 对应的预留单数据包（DynamicObject ）
        /// </summary>
        public object ReserveBillData { get; set; }


        /// <summary>
        /// 预留需求明细信息所对应的即时库存信息（DynamicObjectCollection）
        /// </summary>
        public object InventoryList { get; set; }



        /// <summary>
        /// 预留需求明细信息所对应的所有预留信息（DynamicObjectCollection）
        /// </summary>
        public object AllReserveEntrys
        {
            get; set;
        }


        /// <summary>
        /// 仓库可销范围控制  
        /// </summary>
        public bool AllowsalesControl { get; set; }

        /// <summary>
        /// 自动预留：哪些需求明细行做自动预留,不指定，则所有行都自动预留
        /// </summary>
        public List<string> SelectEntryRow { get; set; }


        /// <summary>
        /// 当前选中的需求明细行id信息（按明细行操作时用到）
        /// </summary>
        public List<string> feditrows { get; set; }

        /// <summary>
        /// 源需求单作废状态
        /// </summary>
        public bool fcancelstatus { get; set; }



        /// <summary>
        /// 销售合同--采购入库关联关系
        /// </summary>
        public List<SO_StkIn_Map> SO_StkIn_Maps { get; set; }


        /// <summary>
        /// 销售合同行--预留转移映射关系
        /// </summary>
        public List<SO_ReserveTransfer_Map> SO_ReserveTransfer_Maps { get; set; }

        /// <summary>
        /// 销售员ID
        /// </summary>
        public string fstaffid { get; set; }

        /// <summary>
        /// 门店名称ID
        /// </summary>
        public string fdeptid { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        public string faddress { get; set; }

        /// <summary>
        /// 手工单号
        /// </summary>
        public string fmanualnumber { get; set; }

        /// <summary>
        /// 源单备注
        /// </summary>
        public string fsourcemark { get; set; }

    }




    /// <summary>
    /// 预留设置-----需求明细信息
    /// </summary>
    public class ReserveSettingDemandInfo
    {



        /// <summary>
        /// 物料id
        /// </summary>
        public string fmaterialid { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialNo { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }


        /// <summary>
        /// 辅助属性
        /// </summary>
        public string fattrinfo { get; set; }
        /// <summary>
        /// 辅助属性-扩展
        /// </summary>
        public string fattrinfo_e { get; set; }
        /// <summary>
        /// 辅助属性 固定名称
        /// </summary>
        public string fname_e { get; set; }


        /// <summary>
        /// 定制说明
        /// </summary>
        public string fcustomdesc { get; set; }



        /// <summary>
        /// 基本计量单位
        /// </summary>
        public string funitid { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string fbizunitid { get; set; }


        /// <summary>
        /// 预留量
        /// </summary>
        public decimal fbizqty { get; set; }

        /// <summary>
        /// 需求数量
        /// </summary>
        public decimal fbizplanqty { get; set; }

        /// <summary>
        /// 预留日期至
        /// </summary>
        public DateTime freservedateto { get; set; }

        /// <summary>
        /// 可预留量
        /// </summary>
        public decimal fbizcanreserveqty { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public decimal fbizinventoryqty { get; set; }

        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string fmtono { get; set; }

        /// <summary>
        /// 基本单位预留量
        /// </summary>
        public decimal fqty { get; set; }

        /// <summary>
        /// 基本单位可预留量
        /// </summary>
        public decimal fcanreserveqty { get; set; }

        /// <summary>
        /// 基本单位库存数量
        /// </summary>
        public decimal finventoryqty { get; set; }

        /// <summary>
        /// 基本单位需求数量（源单上的需求数量）
        /// </summary>
        public decimal fplanqty { get; set; }

        ///// <summary>
        ///// 基本单位需求数量（前端显示）
        ///// </summary>
        //public decimal fdisplayplanqty { get; set; }

        ///// <summary>
        ///// 需求数量（前端显示）
        ///// </summary>
        //public decimal fbizdisplayplanqty { get; set; }

        /// <summary>
        /// 基本单位出库数（创建、已提交、重新审核、已审核）
        /// </summary>
        public decimal foutqty { get; set; }

        /// <summary>
        /// 基本单位调拨数（创建、已提交、重新审核、已审核）
        /// </summary>
        public decimal ftransferqty { get; set; }

        /// <summary>
        /// 基本单位需求数量（去掉预留转移后的需求量，比如销售合同，如果已经下推销售出库单，则会做预留转移，这时候真实的需求量应该为数量减去转移到下游出库单的数量）
        ///
        /// 对于销售合同，需要比较出库量与转移量，扣减数量大的
        /// </summary>
        public decimal RealDemandQty
        {
            get
            {
                decimal qty = fplanqty, transferQty = 0, outQty = foutqty + ftransferqty;

                if (this.TraceEntry != null && this.TraceEntry.Count > 0)
                {
                    transferQty = (this.TraceEntry.Where(f => f.fdirection_d == "1" && f.fopdesc == "3" && !f.freservenote.Contains("库存调拨"))?.ToList()?.Sum(f => f.fqty_d)).GetValueOrDefault();
                }

                qty -= transferQty > outQty ? transferQty : outQty;

                return qty;
            }
        }


        /// <summary>
        /// 对应的需求单的需求明细内码
        /// </summary>
        public string fdemandentryid { get; set; }


        /// <summary>
        /// 对应的需求单的需求明细数据包（DynamicObject ）
        /// </summary>
        public object DemandRowData { get; set; }

        /// <summary>
        /// 对应的预留单的需求明细行id
        /// </summary>
        public string freserveentryid { get; set; }


        /// <summary>
        /// 默认仓库 
        /// </summary>
        public string fstorehouseid { get; set; }


        /// <summary>
        ///  默认库存状态
        /// </summary>
        public string fstockstatus { get; set; }



        /// <summary>
        /// 预留信息（预留跟踪信息）
        /// </summary>
        public List<ReserveSettingTraceInfo> TraceEntry { get; set; } = new List<ReserveSettingTraceInfo>();


        /// <summary>
        /// 源需求单行关闭状态
        /// </summary>
        public string fclosestatus { get; set; }


        /// <summary>
        /// 销售员id
        /// </summary>
        public string fstaffid { get; set; }


        /// <summary>
        /// 业绩品牌
        /// </summary>
        public string fresultbrandid { get; set; }
    }


    /// <summary>
    ///  预留设置-----需求明细的预留信息
    /// </summary>
    public class ReserveSettingTraceInfo
    {
        public ReserveSettingTraceInfo()
        {
        }


        public string Id { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string fstorehouseid { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public string fstockstatus { get; set; }

        /// <summary>
        /// 预留量
        /// </summary>
        public decimal fbizqty_d { get; set; }

        /// <summary>
        /// 基本单位预留量
        /// </summary>
        public decimal fqty_d { get; set; }

        /// <summary>
        /// 预留日期至
        /// </summary>
        public DateTime freservedateto_d { get; set; }


        /// <summary>
        /// 预留方向： '0':'增','1':'减'
        /// </summary>
        public string fdirection_d { get; set; }

        /// <summary>
        /// 可预留量
        /// </summary>
        public string fbizcanreserveqty_d { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public decimal fbizinventoryqty_d { get; set; }

        /// <summary>
        /// 基本单位可预留量
        /// </summary>
        public decimal fcanreserveqty_d { get; set; }

        /// <summary>
        /// 基本单位库存数量
        /// </summary>
        public decimal finventoryqty_d { get; set; }



        /// <summary>
        /// 预留操作   '0':'增加预留','1':'减少预留','2':'手工释放','3':'预留转移','4':'单据关闭释放','5':'单据作废释放','6':'自动释放'
        /// </summary>
        public string fopdesc { get; set; }



        /// <summary>
        /// 预留说明
        /// </summary>
        public string freservenote { get; set; }


        /// <summary>
        /// 操作日期
        /// </summary>
        public DateTime foptime { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string fopuserid { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        public string fmaterialid_d { get; set; }


        /// <summary>
        /// 计量单位
        /// </summary>
        public string fbizunitid_d { get; set; }


        /// <summary>
        /// 基本计量单位
        /// </summary>
        public string funitid_d { get; set; }


        /// <summary>
        /// 对应的预留单的预留跟踪行PKID
        /// </summary>
        public string freservetrancepkid { get; set; }

        /// <summary>
        /// 预留提供方---预留来源对象
        /// </summary>
        public string ffromformid { get; set; }

        /// <summary>
        /// 预留提供方---来源单编号
        /// </summary>
        public string ffrombillno { get; set; }

        /// <summary>
        /// 预留提供方---来源单id
        /// </summary>
        public string ffrombillpkid { get; set; }


        /// <summary>
        /// 行关闭状态
        /// </summary>
        public string fclosestatus_d { get; set; }


        /// <summary>
        /// 旧的预留日期
        /// </summary>
        public string freservedateto_old { get; set; }
    }

    /// <summary>
    /// 销售合同--采购入库关联关系
    /// </summary>
    public class SO_StkIn_Map
    {
        public string forderid { get; set; }

        public string forderno { get; set; }

        public string forderenid { get; set; }

        public decimal fqty_so { get; set; }

        public decimal fbizqty_so { get; set; }

        public string fstkinid { get; set; }

        public string fstkinno { get; set; }

        public string fstkinenid { get; set; }

        public decimal fqty_in { get; set; }

        public decimal fbizqty_in { get; set; }

        public string fstorehouseid { get; set; }

        public string fstorelocationid { get; set; }

        public string fstockstatus { get; set; }

        public string fclosestatus { get; set; }

        public DateTime fdeliverydate { get; set; }

        /// <summary>
        /// 剩余数量
        /// </summary>
        public decimal BalanceQty
        {
            get
            {
                var qty = fqty_in;
                qty -= Trances.Sum(s => s.Value);
                return qty;
            }
        }

        public Dictionary<ReserveSettingTraceInfo, decimal> Trances = new Dictionary<ReserveSettingTraceInfo, decimal>();
    }

    /// <summary>
    /// 销售合同行与预留转移映射关系
    /// </summary>
    public class SO_ReserveTransfer_Map
    {
        /// <summary>
        /// 转入合同
        /// </summary>
        public string ftoorderid { get; set; }

        /// <summary>
        /// 转入明细行
        /// </summary>
        public string ftoorderentryid { get; set; }

        /// <summary>
        /// 转出合同
        /// </summary>
        public string ffromorderid { get; set; }

        /// <summary>
        /// 转出明细行
        /// </summary>
        public string ffromorderentryid { get; set; }

        public decimal fbizreservetransferinqty { get; set; }

        public decimal fbizreservetransferoutqty { get; set; }

        /// <summary>
        /// 预留转入数量
        /// </summary>
        public decimal freservetransferinqty { get; set; }

        /// <summary>
        /// 预留转出数量
        /// </summary>
        public decimal freservetransferoutqty { get; set; }

        /// <summary>
        /// 采购入库明细行ids
        /// </summary>
        public HashSet<string> PoInEnIds { get; set; } = new HashSet<string>();

        /// <summary>
        /// 实际预留数量（用于记录本次入库后还货数量）
        /// </summary>
        public decimal RealReserveQty { get; set; }

        /// <summary>
        /// 待转移数量
        /// </summary>
        public decimal BeTransferQty
        {
            get
            {
                var inQty = freservetransferinqty;
                var outQty = freservetransferoutqty;

                var qty = inQty - outQty;
                return qty;
            }
        }
    }
}
