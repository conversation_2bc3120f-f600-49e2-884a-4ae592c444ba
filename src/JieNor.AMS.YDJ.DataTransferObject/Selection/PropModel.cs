using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.DataTransferObject
{
    /// <summary>
    /// 属性模型
    /// </summary>
    public class PropModel
    {
        /// <summary>
        /// 属性ID
        /// </summary>
        public string PropId { get; set; } = string.Empty;

        /// <summary>
        /// 属性编码
        /// </summary>
        public string PropNumber { get; set; } = string.Empty;

        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropName { get; set; } = string.Empty;

        /// <summary>
        /// 属性值来源：基础资料、辅助资料、文本
        /// </summary>
        public string PropValueSrc { get; set; } = string.Empty;

        /// <summary>
        /// 属性值类型ID：与属性值来源配套使用，目前仅包括（基础资料表单ID、辅助资料类别ID）
        /// </summary>
        public string PropValueType { get; set; } = string.Empty;

        /// <summary>
        /// 属性值数据类型：1 字符、2 数值
        /// </summary>
        public string PropValueDataType { get; set; } = "1";

        /// <summary>
        /// 属性值是否必录
        /// </summary>
        public bool IsMust { get; set; }

        /// <summary>
        /// 属性值是否允许非标录入
        /// </summary>
        public bool AllowCustom { get; set; }
        //选配范围 上下限
        public decimal RangeMax { get; set; }

        public decimal RangeMin { get; set; }

        /// <summary>
        /// 属性值列表
        /// </summary>
        public List<PropValueModel> ValueList { get; set; } = new List<PropValueModel>();
    }
}
