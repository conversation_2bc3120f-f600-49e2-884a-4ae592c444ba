using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.OpValidation.InventoryTransfer
{
    public class OrderQtyValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            // 所有的商品ID
            var materialIds = dataEntities?.SelectMany(t =>
            {
                var entrys = t["fentity"] as DynamicObjectCollection;
                var _productIds = entrys
                .Select(entry => Convert.ToString(entry["fmaterialid"]))
                .Where(productId => !productId.IsNullOrEmptyOrWhiteSpace());
                return _productIds;
            })
            ?.Distinct()
            ?.ToList();

            // 批量加载商品信息
            DynamicObjectCollection productObjs = null;
            if (materialIds != null && materialIds.Any())
            {
                productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", materialIds, "fnumber,fname");
            }

            ValidationResult result = new ValidationResult();
            foreach (var data in dataEntities)
            {
                var currId = Convert.ToString(data["id"]);
                var fsourcenumber = Convert.ToString(data["fsourcenumber"]);
                if (Convert.ToString(data["fsourcetype"]) == "ydj_order" && !fsourcenumber.IsNullOrEmptyOrWhiteSpace())
                {
                    //1.获取上游销售合同中的明细
                    var salOrder = this.Context.LoadBizDataByFilter("ydj_order", $"fbillno='{fsourcenumber}'").FirstOrDefault();
                    var salOrderId = Convert.ToString(salOrder["id"]);
                    var orderEntrys = salOrder["fentry"] as DynamicObjectCollection;

                    //2.查找相同销售合同源单中的库存调拨单累计物料数量
                    var filter = "";
                    if (!currId.IsNullOrEmptyOrWhiteSpace())
                    {
                        filter = $" AND A.fid!='{currId}'";
                    }
                    string sql = $@"SELECT C.fentryid,sum(B.fqty) sumTransQty
                                    FROM T_STK_INVTRANSFER AS A WITH(NOLOCK) 
                                    INNER JOIN T_STK_INVTRANSFERENTRY AS B WITH(NOLOCK) ON A.fid=B.fid 
                                    INNER JOIN T_YDJ_ORDERENTRY AS C WITH(NOLOCK) ON B.fsourceentryid=C.fentryid 
                                    INNER JOIN T_YDJ_ORDER AS D WITH(NOLOCK) ON C.fid=D.fid 
                                    WHERE A.fstatus<>'E' AND A.fcancelstatus='0' AND D.fid='{salOrderId}' {filter}
                                    GROUP BY C.fentryid";
                    var otherTransOrders = this.DBService.ExecuteDynamicObject(this.Context, sql);

                    foreach (var odEnt in orderEntrys)
                    {
                        var currEntryId = Convert.ToString(odEnt["id"]);
                        //3.查找当前库存调拨单中的关联的明细
                        var linkEntrys = (data["fentity"] as DynamicObjectCollection).Where(i => Convert.ToString(i["fsourceentryid"]).EqualsIgnoreCase(currEntryId));
                        if (linkEntrys == null || !linkEntrys.Any())
                        {
                            continue;
                        }

                        //销售数量
                        var salQty = Convert.ToDecimal(odEnt["fqty"]);
                        //其他调拨单累计调拨数量
                        var otherTransQty = 0M;
                        if (otherTransOrders.Any(t => Convert.ToString(t["fentryid"]).EqualsIgnoreCase(currEntryId)))
                        {
                            otherTransQty = Convert.ToDecimal(otherTransOrders.FirstOrDefault(t => Convert.ToString(t["fentryid"]).EqualsIgnoreCase(currEntryId))["sumTransQty"]);
                        }

                        //本次调拨数量
                        var currTransQty = linkEntrys.Select(t => Convert.ToDecimal(t["fqty"])).Sum();

                        //判断本次调拨数量是否大于销售合同可调拨数量（销售数量-累计已调拨数量）
                        if (currTransQty > salQty - otherTransQty)
                        {
                            var fseqs = linkEntrys.Select(t => Convert.ToString(t["FSeq"])).ToList();
                            var materialid = Convert.ToString(odEnt["fproductid"]);
                            var materialObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(materialid));
                            if (materialObj == null) continue;

                            var lineNoStr = $"第{string.Join("行、第", fseqs)}行";
                            var errMsg = $@"{lineNoStr}商品【{Convert.ToString(materialObj["fname"])}】的调拨数量不允许超过销售数量!超额信息(已调拨数量[{Math.Round(currTransQty + otherTransQty, 2)}]大于销售数量[{Math.Round(salQty, 2)}])，关联单据:销售合同{fsourcenumber}；";

                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = errMsg,
                                DataEntity = data,
                            });
                        }
                    }
                }
            }
            return result;
        }
    }
}
