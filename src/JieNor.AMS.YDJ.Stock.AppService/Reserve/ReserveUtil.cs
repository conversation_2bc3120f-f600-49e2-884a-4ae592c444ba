using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveDialog;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.Stock.AppService.Inv;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 预留帮助类
    /// </summary>
    public class ReserveUtil
    {
        static bool IsUpdateDB = true;


        /// <summary>
        /// 即时库存中最小匹配维度字段
        /// </summary>
        private static string[] InvFlexFieldKeys = new string[]
        {
            "fmaterialid",
            "fattrinfo_e",
            "fcustomdesc",
            "funitid",
            "fmtono"
        };

        /// <summary>
        /// 出库类单据
        /// </summary>
        private static HashSet<string> OutStockFormIds = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "stk_sostockout",
            "stk_otherstockout",
            "stk_inventorytransfer"
        };

        /// <summary>
        /// 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandFormId"></param>
        /// <returns></returns>
        public static ConvertRule GetSettingMapInfo(UserContext ctx, string demandFormId)
        {
            var rule = new ConvertRule();
            switch (demandFormId?.ToLower())
            {
                case "ydj_saleintention":
                    rule.ActiveEntityKey = "fentity";
                    rule.FieldMappings = new List<FieldMapObject>() {
                    new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid" },
                    new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                    new FieldMapObject(){Id="fattrinfo_e",Name="辅助属性扩展",MapType=0,SrcFieldId="fattrinfo_e"},
                    new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdes_e"},
                    new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                    new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fbizunitid"},
                    new FieldMapObject(){Id="fplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                    new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                    new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                    new FieldMapObject(){Id="fdisplayplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                    };
                    break;

                case "ydj_order":
                    rule.ActiveEntityKey = "fentry";
                    rule.FieldMappings = new List<FieldMapObject>() {
                     new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fproductid"},
                     new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                     new FieldMapObject(){Id="fattrinfo_e",Name="辅助属性扩展",MapType=0,SrcFieldId="fattrinfo_e"},
                     new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdes_e"},
                     new FieldMapObject(){Id="fresultbrandid",Name="业绩品牌",MapType=0,SrcFieldId="fresultbrandid"},
                     new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                     new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fbizunitid"},
                     new FieldMapObject(){Id="fplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty+freturnqty"},//-foutqty
                     new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                     new FieldMapObject(){Id="fclosestatus",Name="行关闭状态",MapType=0,SrcFieldId="fclosestatus_e"},
                     new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                     new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                     new FieldMapObject(){Id="fdisplayplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty+freturnqty"},
                    };
                    break;
                case "stk_sostockout":
                    rule.ActiveEntityKey = "fentity";
                    rule.FieldMappings = new List<FieldMapObject>() {
                     new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                     new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                     new FieldMapObject(){Id="fattrinfo_e",Name="辅助属性扩展",MapType=0,SrcFieldId="fattrinfo_e"},
                     new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                     new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                     new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                     new FieldMapObject(){Id="fplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                     new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                     new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                    new FieldMapObject(){Id="fdisplayplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                    };
                    break;
                // 其它出库单
                case "stk_otherstockout":
                    rule.ActiveEntityKey = "fentity";
                    rule.FieldMappings = new List<FieldMapObject>() {
                        new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                        new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                        new FieldMapObject(){Id="fattrinfo_e",Name="辅助属性扩展",MapType=0,SrcFieldId="fattrinfo_e"},
                        new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                        new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                        new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                        new FieldMapObject(){Id="fplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                        new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                        new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                        new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                        new FieldMapObject(){Id="fdisplayplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                    };
                    break;
                // 库存调拨单
                case "stk_inventorytransfer":
                    rule.ActiveEntityKey = "fentity";
                    rule.FieldMappings = new List<FieldMapObject>() {
                        new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                        new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                        new FieldMapObject(){Id="fattrinfo_e",Name="辅助属性扩展",MapType=0,SrcFieldId="fattrinfo_e"},
                        new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                        new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                        new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                        new FieldMapObject(){Id="fplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                        new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                        new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                        new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                        new FieldMapObject(){Id="fdisplayplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                    };
                    break;
                // 采购退货单
                case "stk_postockreturn":
                    rule.ActiveEntityKey = "fentity";
                    rule.FieldMappings = new List<FieldMapObject>() {
                        new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                        new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                        new FieldMapObject(){Id="fattrinfo_e",Name="辅助属性扩展",MapType=0,SrcFieldId="fattrinfo_e"},
                        new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                        new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                        new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                        new FieldMapObject(){Id="fplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                        new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                        new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                        new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                        new FieldMapObject(){Id="fdisplayplanqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                    };
                    break;
                default:
                    throw new BusinessException($"表单标识[{demandFormId}]未配置预留设置！");
            }

            return rule;
        }



        /// <summary>
        /// 更新销售合同的预留量
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="reserveBills"></param>
        public static void UpdateOrderReserveQty(UserContext ctx, IEnumerable<DynamicObject> reserveBills)
        {
            var sql = new List<string>();
            foreach (var item in reserveBills)
            {
                var srcForm = item["fsourcetype"]?.ToString();
                if (srcForm.IsNullOrEmptyOrWhiteSpace() || !srcForm.EqualsIgnoreCase("ydj_order"))
                {
                    continue;
                }

                var srcBillId = item["fsourcepkid"]?.ToString();
                sql.Add(@"/*dialect*/update t set fbizreserveqty=isnull(x.fbizqty,0) ,freserveqty=isnull(x.fqty,0)
                        from t_ydj_orderentry as t 
                        left join (
                        select a.fid,a.fentryid,b.fqty,b.fbizqty
                        from t_ydj_orderentry a
                        inner join t_stk_reservebillentry b on a.fentryid=b.fsourceentryid  and a.fid=b.fsourceinterid
                        where b.fid='{0}'
                        ) x on t.fid=x.fid and t.fentryid =x.fentryid 
                        where t.fid='{1}' ".Fmt(item["Id"], srcBillId));
            }

            if (sql.Count > 0)
            {
                var dbSvc = ctx.Container.GetService<IDBServiceEx>();
                dbSvc.ExecuteBatch(ctx, sql);
            }

            // 清缓存
            var invSvc = ctx.Container.GetService<IInventoryService>();
            invSvc.ClearExtendDataCache(ctx);
        }





        /// <summary>
        /// 获取预留设置数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm">预留需求单模型</param>
        /// <param name="billDatas">预留需求单数据包</param>
        /// <returns>预留设置数据</returns>
        public static ReserveSettingInfo GetReserveSettingInfo(UserContext ctx, IOperationResult opResult, HtmlForm demandForm, DynamicObject billDatas, OperateOption option)
        {
            var result = GetReserveSettingInfo(ctx, opResult, demandForm, new List<DynamicObject>() { billDatas }, option);

            return result.FirstOrDefault();
        }



        /// <summary>
        /// 获取预留设置数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm">预留需求单模型</param>
        /// <param name="demandBillDatas">预留需求单数据包</param>
        /// <returns>预留设置数据</returns>
        public static List<ReserveSettingInfo> GetReserveSettingInfo(UserContext ctx, IOperationResult opResult, HtmlForm demandForm, IEnumerable<DynamicObject> demandBillDatas, OperateOption option)
        {
            UpdateDBSchema(ctx);

            //可预留到仓库仓位
            var profileService = ctx.Container.GetService<ISystemProfile>();
            var fallowsalescontrol = profileService.GetSystemParameter(ctx, "stk_stockparam", "fallowsalescontrol", false);
            var autoReserve = profileService.GetSystemParameter(ctx, "bas_storesysparam", "foutspotautores", false);

            //销售意向单预留天数
            var reserveDay = 0;
            if (demandForm.Id.EqualsIgnoreCase("ydj_saleintention"))
            {
                reserveDay = profileService.GetSystemParameter(ctx, "bas_storesysparam", "freserveday", 0);
            }
            else if (demandForm.Id.EqualsIgnoreCase("ydj_order") || demandForm.Id.EqualsIgnoreCase("stk_sostockout"))
            {
                reserveDay = profileService.GetSystemParameter(ctx, "stk_stockparam", "freserveday", 30);
            }
            ctx.Container.GetService<LoadReferenceObjectManager>().Load(ctx, demandForm.GetDynamicObjectType(ctx), demandBillDatas.ToArray(), false);

            var allReserveBills = GetReserveBillData(ctx, demandForm, demandBillDatas.ToList());

            IBizExpressionEvaluator exprEngine = ctx.Container.GetService<IBizExpressionEvaluator>();
            IBizExpressionContext exprCtx = ctx.Container.GetService<IBizExpressionContext>();
            IBizExpression expression = ctx.Container.GetService<IBizExpression>();

            var reserveSettingInfos = new List<ReserveSettingInfo>();
            var cvtRule = ReserveUtil.GetSettingMapInfo(ctx, demandForm.Id);

            //自动预留逻辑：哪些行自动预留
            var selectEntryRow = new List<string>();
            var hasSelectEntryRow = option.TryGetVariableValue("selectEntryRow", out selectEntryRow);

            var maps = new List<SO_StkIn_Map>();
            bool hasMaps = option.TryGetVariableValue("so_stkin_maps", out maps);

            var so_reservetransfer_maps = new List<SO_ReserveTransfer_Map>();
            bool hasSoReserveTransferMaps = option.TryGetVariableValue("so_reservetransfer_maps", out so_reservetransfer_maps);

            var soorder_qty_maps = new Dictionary<string, OrderQty>();
            if (demandForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                var orderEnIds = demandBillDatas
                    .SelectMany(s => (DynamicObjectCollection)s["fentry"])
                    .Select(s => Convert.ToString(s["id"]))
                    .ToList();
                soorder_qty_maps = GetOrderQty(ctx, orderEnIds);
            }

            var stockStatusMap = GetStockStatusMpa(ctx);

            foreach (var demandBill in demandBillDatas)
            {
                var id = demandBill["Id"]?.ToString();
                var reserveBill = allReserveBills.FirstOrDefault(f => id.EqualsIgnoreCase(f["fsourcepkid"]?.ToString()));

                var reserveSettingData = new ReserveSettingInfo();
                reserveSettingData.freserveday = reserveDay;
                reserveSettingData.AllowsalesControl = fallowsalescontrol;
                reserveSettingData.ReserveBillData = reserveBill;
                reserveSettingData.DemandBillData = demandBill;
                reserveSettingData.AutoReserveWhen = autoReserve;

                //自动预留逻辑：哪些行自动预留
                if (demandForm.Id.EqualsIgnoreCase("ydj_order") && hasSelectEntryRow)
                {
                    reserveSettingData.SelectEntryRow = selectEntryRow;
                }

                if (hasMaps)
                {
                    reserveSettingData.SO_StkIn_Maps = maps;
                }

                if (hasSoReserveTransferMaps)
                {
                    reserveSettingData.SO_ReserveTransfer_Maps = so_reservetransfer_maps;
                }

                reserveSettingInfos.Add(reserveSettingData);

                SetReserveSettingInfo(ctx, opResult, demandForm, reserveSettingData, cvtRule, exprEngine, exprCtx, expression, option, soorder_qty_maps, stockStatusMap);
            }

            return reserveSettingInfos;
        }

        internal static void DebugInfo(UserContext userCtx, List<ReserveSettingInfo> reserveSettingInfos)
        {
            try
            {
                // 记录日志
                var companyId = userCtx.Company;
                var company = userCtx.Companys
                    .FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(companyId));

                StringBuilder info = new StringBuilder();
                info.AppendLine();
                info.AppendLine("ReserveSettingInfos:" + reserveSettingInfos?.ToJson());
                info.AppendLine("UserContext:" + new Dictionary<string, object>()
                {
                    { "CompanyId", companyId },
                    { "CompanyNumber", company?.CompanyNumber },
                    { "CompanyName", company?.CompanyName },
                    { "UserId", userCtx.UserId },
                    { "DisplayName", userCtx.DisplayName }
                }.ToJson());

                DebugUtil.WriteLogToFile(info.ToString(), $"ReserveSettingInfo/{DateTime.Now.Hour}");
            }
            catch (Exception e)
            {
            }

        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="opResult"></param>
        /// <param name="demandForm"></param>
        /// <param name="reserveSettingData"></param>
        /// <param name="cvtRule"></param>
        /// <param name="exprEngine"></param>
        /// <param name="exprCtx"></param>
        /// <param name="expression"></param>
        /// <param name="option"></param>
        /// <param name="soorder_qty_maps">销售合同关联的数量</param>
        /// <param name="stockStatusMap">库存状态映射表</param>
        private static void SetReserveSettingInfo(UserContext ctx, IOperationResult opResult, HtmlForm demandForm,
            ReserveSettingInfo reserveSettingData, ConvertRule cvtRule, IBizExpressionEvaluator exprEngine,
            IBizExpressionContext exprCtx, IBizExpression expression, OperateOption option,
            Dictionary<string, OrderQty> soorder_qty_maps, Dictionary<string, string> stockStatusMap)
        {
            DynamicObject demandBill = reserveSettingData.DemandBillData as DynamicObject;
            var reserveBill = reserveSettingData.ReserveBillData as DynamicObject;
            var so_stkin_maps = reserveSettingData.SO_StkIn_Maps;
            var so_reservetransfer_maps = reserveSettingData.SO_ReserveTransfer_Maps;

            BizDynamicDataRow dcRow = new BizDynamicDataRow(demandForm);
            exprCtx.BizData = demandBill;
            exprCtx.BindSetField(new TrySetValueHandler(dcRow.TrySetMember));
            exprCtx.BindGetField(new TryGetValueHandler(dcRow.TryGetMember));

            FillReserveSettingHead(ctx, reserveSettingData, demandForm, demandBill, reserveBill);

            var activeEntity = demandForm.GetEntryEntity(cvtRule.ActiveEntityKey);
            var demandEntrys = activeEntity.DynamicProperty?.GetValue<DynamicObjectCollection>(demandBill);
            foreach (var demandEntryRow in demandEntrys)
            {
                var matFld = cvtRule.FieldMappings.FirstOrDefault(f => f.Id.EqualsIgnoreCase("fmaterialid"));
                var fld = demandForm.GetField(matFld.SrcFieldId) as HtmlBaseDataField;
                var matObj = fld.RefDynamicProperty.GetValue<DynamicObject>(demandEntryRow);
                if (matObj == null || Convert.ToBoolean(matObj["fsuiteflag"]))
                {
                    //套件不做预留
                    continue;
                }

                // 库存调拨单的仓位调拨不做预留
                if (demandForm.Id.EqualsIgnoreCase("stk_inventorytransfer"))
                {
                    var fsourcetype = Convert.ToString(demandBill["fsourcetype"]);
                    // 源单类型!=销售合同时，不做预留
                    if (!fsourcetype.EqualsIgnoreCase("ydj_order"))
                    {
                        string fstorehouseid = Convert.ToString(demandEntryRow["fstorehouseid"]);
                        string fstorehouseidto = Convert.ToString(demandEntryRow["fstorehouseidto"]);

                        if (fstorehouseid.EqualsIgnoreCase(fstorehouseidto))
                        {
                            continue;
                        }
                    }
                }

                //填充需求明细行信息
                FillReserveSettingEntityData(ctx, cvtRule, demandForm, dcRow, demandEntryRow, reserveSettingData, exprEngine, exprCtx, expression, soorder_qty_maps);
            }

            var inventoryList = ReserveUtil.MatchInventoryList(ctx, reserveSettingData);
            var allReserveEntrys = ReserveUtil.GetReserveEntrys(ctx, reserveSettingData);

            IEnumerable<DynamicObject> matchInventoryList = inventoryList;
            IEnumerable<DynamicObject> matchReserveEntrys = allReserveEntrys;

            // 忽略不推荐的仓库
            option.TryGetVariableValue("ignoreNoFIFOStock", out bool ignoreNoFIFOStock);
            if (ignoreNoFIFOStock)
            {
                var ignoreStoreHouseIds = InventoryUtil.GetNotFIFOStoreHouseIds(ctx);
                if (ignoreStoreHouseIds.Any())
                {
                    matchInventoryList = matchInventoryList.Where(s =>
                        !ignoreStoreHouseIds.Contains(Convert.ToString(s["fstorehouseid"])));
                    matchReserveEntrys = matchReserveEntrys.Where(s =>
                        !ignoreStoreHouseIds.Contains(Convert.ToString(s["fstorehouseid"])));
                }
            }

            reserveSettingData.InventoryList = inventoryList;
            reserveSettingData.AllReserveEntrys = allReserveEntrys;

            //var stockDetailDatas = new List<StockDetailModel>();
            //option.TryGetVariableValue("isFIFO", out bool isFIFO);
            //if (isFIFO)
            //{
            //    // 获取商品收发明细表
            //    stockDetailDatas = InventoryUtil.GetStockDetailData(ctx, reserveSettingData);
            //}

            // 记录是否有出错
            List<string> errorMessages = new List<string>();

            var reserveEntrys = reserveBill?["fentity"] as DynamicObjectCollection;

            var settingEntryRows = reserveSettingData.DemandEntry;
            // 销售合同以出现货为优先处理
            if (reserveSettingData.fdemandformid.EqualsIgnoreCase("ydj_order"))
            {
                settingEntryRows = settingEntryRows.OrderByDescending(s =>
                    Convert.ToBoolean((s.DemandRowData as DynamicObject)?["fisoutspot"])).ToList();
            }
 
            foreach (var settingEntryRow in settingEntryRows)
            {
                //匹配上一次设置的需求明细相关字段值
                var reserveEntry = reserveEntrys?.FirstOrDefault(o =>
                {
                    return Convert.ToString(o["fsourceentryid"]).EqualsIgnoreCase(Convert.ToString(settingEntryRow.fdemandentryid));
                });

                //存在对应的预留单行，则先按预留单上信息填充
                FillReserveSettingTranceData(settingEntryRow, reserveEntry);

                // 出现货
                var fisoutspot = false;
                if (reserveSettingData.fdemandformid.EqualsIgnoreCase("ydj_order"))
                {
                    var demandRow = settingEntryRow.DemandRowData as DynamicObject;

                    fisoutspot = Convert.ToBoolean(demandRow["fisoutspot"]);
                }

                if (reserveEntry != null)
                {
                    if (!IsAutoUpdateReserveRow(ctx, opResult, demandForm, reserveSettingData, settingEntryRow, option))
                    {
                        continue;
                    }

                    if (so_stkin_maps != null && so_stkin_maps.Count > 0)
                    {
                        //采购入库的自动预留功能
                        AddPOrderReserveInfo(ctx, demandForm, reserveSettingData, settingEntryRow, so_stkin_maps, so_reservetransfer_maps);
                    }
                    else
                    {
                        //自动更新预留：基本单位需求数量跟基本单位预留数量不一致，说明源单相关数量有修改，需要更新对应预留
                        if (settingEntryRow.fqty > settingEntryRow.RealDemandQty)//需求量减少了
                        {
                            var fplanqty = settingEntryRow.fqty - settingEntryRow.RealDemandQty;//基本单位需求数量减少的数量
                            UpdateReserveQtyWhenDemandQtyReduce(ctx, settingEntryRow, fplanqty);
                        }
                        else
                        {
                            // 销售合同且出现货时，不考虑【忽略不推荐的仓库】
                            if (fisoutspot)
                            {
                                UpdateReserveQtyWhenDemandQtyIncrease(ctx, opResult, demandForm, reserveSettingData, settingEntryRow, inventoryList, allReserveEntrys, true, errorMessages);
                            }
                            else
                            {
                                UpdateReserveQtyWhenDemandQtyIncrease(ctx, opResult, demandForm, reserveSettingData, settingEntryRow, matchInventoryList, matchReserveEntrys, true, errorMessages);
                            }
                        }
                    }
                }
                else
                {
                    if (!IsAutoAddReserveRow(ctx, opResult, demandForm, reserveSettingData, settingEntryRow, option))
                    {
                        continue;
                    }
                    if (so_stkin_maps != null && so_stkin_maps.Count > 0)
                    {
                        //采购入库的自动预留功能
                        AddPOrderReserveInfo(ctx, demandForm, reserveSettingData, settingEntryRow, so_stkin_maps, so_reservetransfer_maps);
                    }
                    else
                    {
                        // 销售合同且出现货时，不考虑【忽略不推荐的仓库】
                        if (fisoutspot)
                        {
                            //自动生成对应的预留跟踪记录：找到可用量>0的仓库，设置预留量
                            UpdateReserveQtyWhenDemandQtyIncrease(ctx, opResult, demandForm, reserveSettingData, settingEntryRow, inventoryList, allReserveEntrys, false, errorMessages);
                        }
                        else
                        {
                            //自动生成对应的预留跟踪记录：找到可用量>0的仓库，设置预留量
                            UpdateReserveQtyWhenDemandQtyIncrease(ctx, opResult, demandForm, reserveSettingData, settingEntryRow, matchInventoryList, matchReserveEntrys, false, errorMessages);
                        }

                    }
                }  
            }

            foreach (var settingEntryRow in reserveSettingData.DemandEntry)
            {
                settingEntryRow.freservedateto = reserveSettingData.fdefautdate;
                settingEntryRow.fcanreserveqty = settingEntryRow.TraceEntry.Sum(f => Convert.ToDecimal(f.fcanreserveqty_d));
                settingEntryRow.fbizcanreserveqty = settingEntryRow.TraceEntry.Sum(f => Convert.ToDecimal(f.fbizcanreserveqty_d));
                if (settingEntryRow.TraceEntry.Count > 0)
                {
                    foreach (var item in settingEntryRow.TraceEntry)
                    {
                        if (item.freservedateto_d == DateTime.MinValue)
                        {
                            item.freservedateto_d = reserveSettingData.fdefautdate;
                        }
                    }
                }
            }

            ReserveUtil.FillReserveSettingInvQty(ctx, reserveSettingData);
        }


        /// <summary>
        /// 采购入库的自动预留
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm"></param>
        /// <param name="reserveSettingData"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="so_stkin_maps"></param>
        private static void AddPOrderReserveInfo(UserContext ctx, HtmlForm demandForm, ReserveSettingInfo reserveSettingData,
            ReserveSettingDemandInfo settingEntryRow, List<SO_StkIn_Map> so_stkin_maps,
            List<SO_ReserveTransfer_Map> so_reservetransfer_maps)
        {
            // 判断需求明细行是否存在转出（借出，即A）记录
            var reserveTransfers = so_reservetransfer_maps
                .Where(f => settingEntryRow.fdemandentryid == f.ffromorderentryid && f.BeTransferQty > 0).ToList();
            // 有：把借出的转回来
            if (reserveTransfers.Any())
            {
                foreach (var reserveTransfer in reserveTransfers)
                {
                    var poInstocks = so_stkin_maps.Where(f => reserveTransfer.ftoorderentryid == f.forderenid).ToList();
                    if (!poInstocks.Any())
                    {
                        return;
                    }

                    var reserveQty = reserveTransfer.BeTransferQty;
                    reserveTransfer.RealReserveQty = AddPOrderReserveInfo(ctx, settingEntryRow, poInstocks, reserveQty, true);

                    //记录转移记录与哪些采购入库明细行相关
                    foreach (var poInstock in poInstocks)
                    {
                        foreach (var traceInfo in settingEntryRow.TraceEntry)
                        {
                            if (poInstock.Trances.ContainsKey(traceInfo))
                            {
                                reserveTransfer.PoInEnIds.Add(poInstock.fstkinenid);
                            }
                        }
                    }
                }
            }
            //  没有：按原逻辑
            else
            {
                var poInstock = so_stkin_maps.Where(f => settingEntryRow.fdemandentryid == f.forderenid).ToList();
                if (!poInstock.Any())
                {
                    return;
                }

                var reserveQty = settingEntryRow.RealDemandQty;
                AddPOrderReserveInfo(ctx, settingEntryRow, poInstock, reserveQty, false);
            }
        }

        /// <summary>
        /// 采购入库的自动预留
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="poInstock"></param>
        /// <param name="beReserveQty">待预留的数量</param>
        /// <param name="isReturn">是否还货</param>
        private static decimal AddPOrderReserveInfo(UserContext ctx, ReserveSettingDemandInfo settingEntryRow, List<SO_StkIn_Map> poInstock, decimal beReserveQty, bool isReturn)
        {
            // 总预留数量
            decimal sumReserveQty = 0;

            var fplanqty = settingEntryRow.RealDemandQty;
            foreach (var item in poInstock)
            {
                //待预留的数量
                if (beReserveQty <= 0)
                {
                    break;
                }

                //剩余预留数量
                var balanceReserveQty = fplanqty - GetReserveTranceRowQty(settingEntryRow.TraceEntry);
                if (balanceReserveQty <= 0)
                {
                    break;
                }

                //采购入库数量
                var poQty = item.BalanceQty;
                // 此采购入库需预留数量
                var thisReserveQty = poQty > beReserveQty ? beReserveQty : poQty;
                if (thisReserveQty > balanceReserveQty)
                {
                    thisReserveQty = balanceReserveQty;
                }

                if (thisReserveQty <= 0)
                {
                    continue;
                }

                string reserveNote = isReturn ?
                    "销售合同{0}关联的采购入库单{1}还货预留".Fmt(item.forderno, item.fstkinno) :
                    "关联的采购入库单{0}预留".Fmt(item.fstkinno);

                var row = CreateTranceEntryRow(ctx, item, settingEntryRow, thisReserveQty, reserveNote);

                // 记录
                item.Trances.Add(row, thisReserveQty);
                beReserveQty -= thisReserveQty;
                sumReserveQty += thisReserveQty;
            }

            CalclateDemandEntryRowQty(settingEntryRow);
            return sumReserveQty;
        }

        /// <summary>
        /// 获取销售合同的出库、调拨数量（销售出库单、库存调拨单）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="orderEnIds"></param>
        /// <returns></returns>
        internal static Dictionary<string, OrderQty> GetOrderQty(UserContext ctx, IEnumerable<string> orderEnIds)
        {
            IDBService dbService = ctx.Container.GetService<IDBService>();

            string sql = $@"
    select soe.fsoorderentryid as forderenid, SUM(soe.fqty) as foutqty
    from t_stk_sostockoutentry soe
    inner join t_stk_sostockout so on so.fid = soe.fid 
    where so.fcancelstatus = '0' and soe.fsoorderentryid in ({orderEnIds.JoinEx(",", true)})
    group by soe.fsoorderentryid";

            var outQtys = dbService.ExecuteDynamicObject(ctx, sql);

            // 查询合同的单据类型上的【合同调拨出库自动更新单据状态】（fautoalterstatus）
            sql = $@"
    select te.fentryid, t.fbilltype
    from t_ydj_order t with(nolock) 
    inner join t_ydj_orderentry te with(nolock) on t.fid=te.fid
    where te.fentryid in ({orderEnIds.JoinEx(",", true)})
    ";

            var orderMeta = ctx.Container.GetService<IMetaModelService>().LoadFormModel(ctx, "ydj_order");

            var orderBillTypes = dbService.ExecuteDynamicObject(ctx, sql);
            var billTypeParams = ctx.Container.GetService<IBillTypeService>()
                .GetBillTypeParamSet(ctx, orderMeta,
                new HashSet<string>(orderBillTypes.Select(s => Convert.ToString(s["fbilltype"]))));

            List<string> sqls = new List<string>();

            var fautoalterstatusTrueOrderEntryIds = new List<string>();
            var fautoalterstatusFalseOrderEntryIds = new List<string>();

            foreach (var billTypeParam in billTypeParams)
            {
                var fautoalterstatus = Convert.ToBoolean(billTypeParam.Value["fautoalterstatus"]);
                if (fautoalterstatus)
                {
                    fautoalterstatusTrueOrderEntryIds.AddRange(
                        orderBillTypes
                            .Where(s => Convert.ToString(s["fbilltype"]).EqualsIgnoreCase(billTypeParam.Key))
                            .Select(s => Convert.ToString(s["fentryid"]))
                        );
                }
                else
                {
                    fautoalterstatusFalseOrderEntryIds.AddRange(
                        orderBillTypes
                            .Where(s => Convert.ToString(s["fbilltype"]).EqualsIgnoreCase(billTypeParam.Key))
                            .Select(s => Convert.ToString(s["fentryid"]))
                    );
                }
            }

            // 【合同调拨出库自动更新单据状态】=true时，库存调拨单作为转移量
            if (fautoalterstatusTrueOrderEntryIds.Any())
            {
                sql = $@"
    select te.fsourceentryid as forderenid, SUM(te.fqty) as ftransferqty
    from T_STK_INVTRANSFERENTRY te
    inner join T_STK_INVTRANSFER t on te.fid = t.fid 
    where t.fcancelstatus = '0' and te.fsourceformid='ydj_order' and te.fsourceentryid in ({orderEnIds.JoinEx(",", true)})
    group by te.fsourceentryid";
                sqls.Add(sql);
            }

            // 【合同调拨出库自动更新单据状态】=false时，未审核的库存调拨单作为转移量
            if (fautoalterstatusFalseOrderEntryIds.Any())
            {
                sql = $@"
    select te.fsourceentryid as forderenid, SUM(te.fqty) as ftransferqty
    from T_STK_INVTRANSFERENTRY te
    inner join T_STK_INVTRANSFER t on te.fid = t.fid 
    where t.fstatus<>'E' and t.fcancelstatus = '0' and te.fsourceformid='ydj_order' and te.fsourceentryid in ({orderEnIds.JoinEx(",", true)})
    group by te.fsourceentryid";
                sqls.Add(sql);
            }

            var transferQtys = dbService.ExecuteDynamicObjectByUnion(ctx, sqls);

            var dic = new Dictionary<string, OrderQty>();
            foreach (var item in outQtys)
            {
                string forderenid = Convert.ToString(item["forderenid"]);
                decimal foutqty = Convert.ToDecimal(item["foutqty"]);

                if (!dic.TryGetValue(forderenid, out var orderQty))
                {
                    orderQty = new OrderQty();
                    dic[forderenid] = orderQty;
                }

                orderQty.OutQty = foutqty;
            }

            foreach (var item in transferQtys)
            {
                string forderenid = Convert.ToString(item["forderenid"]);
                decimal ftransferqty = Convert.ToDecimal(item["ftransferqty"]);

                if (!dic.TryGetValue(forderenid, out var orderQty))
                {
                    orderQty = new OrderQty();
                    dic[forderenid] = orderQty;
                }

                orderQty.TransferQty = ftransferqty;
            }

            return dic;

        }

        private static ReserveSettingTraceInfo CreateTranceEntryRow(UserContext ctx, SO_StkIn_Map poStkInInfo, ReserveSettingDemandInfo settingEntryRow, decimal qty, string reserveNote)
        {
            var row = new ReserveSettingTraceInfo();
            row.Id = Guid.NewGuid().ToString();
            row.fdirection_d = "0";
            row.fopdesc = "0";
            row.freservenote = reserveNote;
            row.foptime = DateTime.Now;
            row.fopuserid = ctx.UserId;
            row.ffromformid = "stk_postockin";
            row.ffrombillno = poStkInInfo.fstkinno;
            row.ffrombillpkid = poStkInInfo.fstkinenid;

            row.fclosestatus_d = settingEntryRow.fclosestatus;

            row.fmaterialid_d = settingEntryRow.fmaterialid;
            row.fbizunitid_d = settingEntryRow.fbizunitid;
            row.funitid_d = settingEntryRow.funitid;

            row.fstockstatus = poStkInInfo.fstockstatus;
            row.fstorehouseid = poStkInInfo.fstorehouseid;

            row.fqty_d = qty;
            row.fbizqty_d = qty;

            row.freservedateto_old = "";

            settingEntryRow.TraceEntry.Add(row);

            return row;
        }

        /// <summary>
        /// 是否自动预留（新增）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="opResult"></param>
        /// <param name="demandForm"></param>
        /// <param name="reserveSettingData"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        private static bool IsAutoAddReserveRow(UserContext ctx, IOperationResult opResult, HtmlForm demandForm, ReserveSettingInfo reserveSettingData, ReserveSettingDemandInfo settingEntryRow, OperateOption option)
        {
            bool isPoStockInAuto = reserveSettingData.SO_StkIn_Maps != null && reserveSettingData.SO_StkIn_Maps.Any();

            if (settingEntryRow.fclosestatus == "3" || settingEntryRow.fclosestatus == "4" || settingEntryRow.fclosestatus == $"{(int)CloseStatus.Whole}")
            {
                //订单已经关闭，不需要再更新预留
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} 订单已经关闭，不需要再更新预留", "采购入库自动预留");
                }
                return false;
            }

            if (IsTransferLocation(demandForm, settingEntryRow))
            {
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} IsTransferLocation=true", "采购入库自动预留");
                }
                return false;
            }

            var updateReserve = false;
            var isUpdate = option.TryGetVariableValue("manualupdate", out updateReserve);//手动更新标记
            if (updateReserve)
            {
                //手动更新--从预留设置页面进入的
                if (reserveSettingData.SelectEntryRow != null)
                {
                    return reserveSettingData.SelectEntryRow.Contains(settingEntryRow.fdemandentryid);
                }
                return true;
            }

            if (demandForm.Id.EqualsIgnoreCase("ydj_order") && settingEntryRow.freservedateto < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day))
            {
                var msg = "销售合同{0}的【交货日期+预留天数】小于系统当前日期，无法进行预留！".Fmt(reserveSettingData.fdemandbillno);
                if (!opResult.ComplexMessage.WarningMessages.Any(f => f.EqualsIgnoreCase(msg)))
                {
                    opResult.ComplexMessage.WarningMessages.Add(msg);
                }
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} {msg}", "采购入库自动预留");
                }
                return false;
            }

            if (demandForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                var obj = settingEntryRow.DemandRowData as DynamicObject;
                if (obj == null)
                {
                    return true;
                }
                var fisoutspot = Convert.ToBoolean(obj["fisoutspot"]);
                if (fisoutspot && reserveSettingData.AutoReserveWhen)
                {
                    //销售合同出现货的行
                    return true;
                }
                else
                {
                    if (isPoStockInAuto)
                    {
                        DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} 非出现货", "采购入库自动预留");
                    }
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 是否自动预留（更新）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="opResult"></param>
        /// <param name="demandForm"></param>
        /// <param name="reserveSettingData"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        private static bool IsAutoUpdateReserveRow(UserContext ctx, IOperationResult opResult, HtmlForm demandForm, ReserveSettingInfo reserveSettingData, ReserveSettingDemandInfo settingEntryRow, OperateOption option)
        {
            bool isPoStockInAuto = reserveSettingData.SO_StkIn_Maps != null && reserveSettingData.SO_StkIn_Maps.Any();

            if (settingEntryRow.fclosestatus == "3" || settingEntryRow.fclosestatus == "4" || settingEntryRow.fclosestatus == $"{(int)CloseStatus.Whole}")
            {
                // 如果还有预留量，则全部释放
                if (settingEntryRow.fqty > 0)
                {
                    return true;
                }

                //订单已经关闭，不需要再更新预留
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} 订单已经关闭，不需要再更新预留", "采购入库自动预留");
                }
                return false;
            }

            if (settingEntryRow.fqty == settingEntryRow.RealDemandQty)
            {
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} 预留数量=需求数量", "采购入库自动预留");
                }
                return false;
            }

            var updateReserve = false;
            var isUpdate = option.TryGetVariableValue("updateReserve", out updateReserve);
            if (!isUpdate || !updateReserve)
            {
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} updateReserve=false", "采购入库自动预留");
                }
                return false;
            }

            if (IsTransferLocation(demandForm, settingEntryRow))
            {
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} IsTransferLocation=true", "采购入库自动预留");
                }
                return false;
            }

            isUpdate = option.TryGetVariableValue("manualupdate", out updateReserve);//手动更新标记
            if (updateReserve)
            {
                //手动更新--从预留设置页面进入的
                if (reserveSettingData.SelectEntryRow != null)
                {
                    return reserveSettingData.SelectEntryRow.Contains(settingEntryRow.fdemandentryid);
                }
                return true;
            }

            if (demandForm.Id.EqualsIgnoreCase("ydj_order") && settingEntryRow.freservedateto < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day))
            {
                var msg = "销售合同{0}的【交货日期+预留天数】小于系统当前日期，无法进行预留！".Fmt(reserveSettingData.fdemandbillno);
                if (!opResult.ComplexMessage.WarningMessages.Any(f => f.EqualsIgnoreCase(msg)))
                {
                    opResult.ComplexMessage.WarningMessages.Add(msg);
                }
                if (isPoStockInAuto)
                {
                    DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} {msg}", "采购入库自动预留");
                }
                return false;
            }

            //销售合同，如果最后一次预留跟踪是预留释放，且源单的需求明细行数量未改变，则不需要更新
            if (demandForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                var obj = settingEntryRow.DemandRowData as DynamicObject;
                if (obj == null)
                {
                    return true;
                }

                var snapShot = obj.GetDataEntitySnapshot();
                if (snapShot != null && snapShot.ContainsKey("fqty"))
                {
                    var oldValue = Convert.ToDecimal(snapShot["fqty"].InitialValue);
                    var newValue = Convert.ToDecimal(obj["fqty"]);
                    var last = settingEntryRow.TraceEntry.OrderByDescending(f => f.foptime).ThenByDescending(f => f.Id).FirstOrDefault();
                    if (oldValue == newValue && last != null && (last.fopdesc == "2" || last.fopdesc == "6"))
                    {
                        if (isPoStockInAuto)
                        {
                            DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} 销售合同，如果最后一次预留跟踪是预留释放，且源单的需求明细行数量未改变，则不需要更新", "采购入库自动预留");
                        }
                        return false;
                    }
                }

                // 合同提交变更时，如果【需求数量】 < 【预留量】，表示需要减少预留
                if (Convert.ToString((reserveSettingData.DemandBillData as DynamicObject)?["fchangestatus"]).EqualsIgnoreCase("3") && settingEntryRow.fqty > settingEntryRow.RealDemandQty)
                {
                    return true;
                }

                var fisoutspot = Convert.ToBoolean(obj["fisoutspot"]);
                if (fisoutspot && reserveSettingData.AutoReserveWhen)
                {
                    //销售合同出现货的行
                    return true;
                }
                else
                {
                    if (isPoStockInAuto)
                    {
                        DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} 非出现货", "采购入库自动预留");
                    }
                    return false;
                }
            }

            return true;
        }



        /// <summary>
        /// 是否仓位调拨
        /// </summary>
        /// <param name="demandForm"></param>
        /// <param name="settingEntryRow"></param>
        /// <returns></returns>
        private static bool IsTransferLocation(HtmlForm demandForm, ReserveSettingDemandInfo settingEntryRow)
        {
            //// 如果是库存调拨单的仓位调拨，就不做预留
            //if (demandForm.Id.EqualsIgnoreCase("stk_inventorytransfer"))
            //{
            //    var demandRow = settingEntryRow.DemandRowData as DynamicObject;
            //    var fstorehouseid = Convert.ToString(demandRow?["fstorehouseid"]);
            //    var fstorehouseidto = Convert.ToString(demandRow?["fstorehouseidto"]);

            //    if (fstorehouseid.EqualsIgnoreCase(fstorehouseidto))
            //    {
            //        return true;
            //    }
            //}

            return false;
        }

        /// <summary>
        /// 需求数量减少，相应的减少预留
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="fplanqty">基本单位需求数量减少的数量</param> 
        private static List<ReserveSettingTraceInfo> UpdateReserveQtyWhenDemandQtyReduce(UserContext ctx, ReserveSettingDemandInfo settingEntryRow, decimal fplanqty)
        {
            var beAdd = new List<ReserveSettingTraceInfo>();

            //按仓库+库存状态分组（按预留量排序，预留量少的，优先更新预留）
            var grp = settingEntryRow.TraceEntry.GroupBy(f => $"{f.fstorehouseid.Trim()}_{f.fstockstatus.Trim()}")
                .OrderBy(f => GetReserveTranceRowQty(f.ToList())).ToList();
            foreach (var item in grp)
            {
                if (fplanqty <= 0)
                {
                    break;
                }

                if (!item.ToList().Any())
                {
                    continue;
                }

                //当前预留量
                var qty = GetReserveTranceRowQty(item.ToList());
                if (qty <= 0)
                {
                    continue;
                }

                var beReduce = qty > fplanqty ? fplanqty : qty;//减少的预留量

                var newTrance = item.ToList().First().ToJson().FromJson<ReserveSettingTraceInfo>();
                newTrance.fqty_d = beReduce;
                newTrance.fbizqty_d = beReduce;
                newTrance.fdirection_d = "1";//预留减少
                newTrance.fopdesc = "1";//预留减少
                newTrance.freservenote = "自动更新预留：需求数量减少，相应的减少预留";

                newTrance.Id = Guid.NewGuid().ToString();
                newTrance.foptime = DateTime.Now;
                newTrance.fopuserid = ctx.UserId;
                newTrance.freservetrancepkid = "";

                newTrance.freservedateto_old = "";
                newTrance.freservedateto_d = DateTime.MinValue;

                settingEntryRow.TraceEntry.Add(newTrance);

                fplanqty -= beReduce;
            }

            CalclateDemandEntryRowQty(settingEntryRow);

            return beAdd;
        }


        /// <summary>
        /// 需求数量增加，相应的增加预留
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="inventoryList">即时库存数据</param>
        /// <param name="allReserveEntrys">所有对应的预留数据</param>
        /// <param name="reserveSettingData">预留设置信息</param> 
        private static void UpdateReserveQtyWhenDemandQtyIncrease(UserContext ctx, IOperationResult opResult, HtmlForm demandForm,
            ReserveSettingInfo reserveSettingData, ReserveSettingDemandInfo settingEntryRow,
            IEnumerable<DynamicObject> inventoryList, IEnumerable<DynamicObject> allReserveEntrys,
            bool isUpdate, List<string> errorMessages)
        {
            var fplanqty = settingEntryRow.RealDemandQty - settingEntryRow.fqty;//基本单位需求数量的增加量
            if (fplanqty <= 0)
            {
                return;
            }

            //所有匹配上的及时库存数据
            var invObjs = FindInvDataByInvFlexFieldKey(ctx, reserveSettingData, inventoryList, settingEntryRow);
            invObjs = invObjs?.OrderByDescending(o => Convert.ToDecimal(o["fqty"])).ToList();//先从库存最大的预留
            var inventoryQty = invObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));

            //所有已关联的预留数据
            var allReserveObjs = FindInvDataByInvFlexFieldKey(ctx, reserveSettingData, allReserveEntrys, settingEntryRow);
            var allReserveQty = allReserveObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));

            //本单关联的总的预留量（注意不要加 fsourceentryid 过滤：因为有多行商品一样的数据，这里要按商品相同的汇总）
            var currentEntryData = settingEntryRow.DemandRowData as DynamicObject;
            var sourceEntryId = reserveSettingData.fdemandformid.EqualsIgnoreCase("ydj_order")
                ? Convert.ToString(currentEntryData?["fsourceentryid_e"])
                : Convert.ToString(currentEntryData?["fsourceentryid"]);
            decimal? currentReserveQty = 0M;
            //if (!sourceEntryId.IsNullOrEmptyOrWhiteSpace())
            {
                var currentReserveObjs = allReserveObjs.Where(f => reserveSettingData.fdemandbillpkid.EqualsIgnoreCase(f["fsourceinterid"]?.ToString())).ToList();
                var currentBillData = reserveSettingData.DemandBillData as DynamicObject;
                var srcFormId = Convert.ToString(currentBillData["fsourcetype"]);
                var srcBillNo = Convert.ToString(currentBillData["fsourcenumber"]);
                if (!srcFormId.IsNullOrEmptyOrWhiteSpace() && !srcBillNo.IsNullOrEmptyOrWhiteSpace())
                {
                    var except = allReserveObjs.Where(f => srcFormId.EqualsIgnoreCase(Convert.ToString(f["fsourceformid"])) && srcBillNo.Contains(Convert.ToString(f["fsourcenumber"]))).ToList();
                    currentReserveObjs.AddRange(except);
                }
                currentReserveQty = currentReserveObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));
            }


            //总的可预留量 =（即时库存数量 - 所有已关联的总的预留量）+ 本单关联的总的预留量
            var canReserveQty = (inventoryQty.GetValueOrDefault() - allReserveQty.GetValueOrDefault()) + currentReserveQty.GetValueOrDefault();
            if (invObjs == null || invObjs.Count == 0 || canReserveQty <= 0)
            {
                return;
            }

            CreateReserveTrance(ctx, opResult, reserveSettingData, settingEntryRow, invObjs, allReserveObjs, isUpdate);

            if (OutStockFormIds.Contains(demandForm.Id))
            {
                if (settingEntryRow.fplanqty != settingEntryRow.fqty)
                {
                    var demandEntryRow = settingEntryRow.DemandRowData as DynamicObject;
                    errorMessages.Add($"第{demandEntryRow?["fseq"]}行商品 {(demandEntryRow?["fmaterialid_ref"] as DynamicObject)?["fnumber"]} 上的库存数量与预留数量不一致，无法操作！");
                }
            }
        }

        private static void CalclateDemandEntryRowQty(ReserveSettingDemandInfo settingEntryRow)
        {
            if (settingEntryRow.TraceEntry == null)
            {
                settingEntryRow.TraceEntry = new List<ReserveSettingTraceInfo>();
            }
            settingEntryRow.fqty = settingEntryRow.TraceEntry.Sum(x =>
            {
                if (x.fdirection_d == "0")
                {
                    return x.fqty_d;
                }
                else
                {
                    return x.fqty_d * -1;
                }
            });
            settingEntryRow.fbizqty = settingEntryRow.TraceEntry.Sum(x =>
            {
                if (x.fdirection_d == "0")
                {
                    return x.fbizqty_d;
                }
                else
                {
                    return x.fbizqty_d * -1;
                }
            });
        }

        internal static decimal GetReserveTranceRowQty(IEnumerable<ReserveSettingTraceInfo> tranceRows)
        {
            var qty = (tranceRows?.Where(f => f.fdirection_d == "0")?.Sum(x => x.fqty_d)).GetValueOrDefault()
                - (tranceRows?.Where(f => f.fdirection_d == "1")?.Sum(x => x.fqty_d)).GetValueOrDefault();

            return qty;
        }

        /// <summary>
        /// 增加预留跟踪记录
        /// </summary>
        /// <param name="reserveSettingData"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="invObjs">即时库存数据</param>
        /// <param name="allReserveObjs">预留数据</param>
        private static void CreateReserveTrance(UserContext ctx, IOperationResult opResult,
            ReserveSettingInfo reserveSettingData, ReserveSettingDemandInfo settingEntryRow,
            List<DynamicObject> invObjs, List<DynamicObject> allReserveObjs, bool isUpdate)
        {
            var fplanqty = settingEntryRow.RealDemandQty - settingEntryRow.fqty;//基本单位需求数量的增加量
            var stockId = settingEntryRow.fstorehouseid;
            var stockStatus = settingEntryRow.fstockstatus;
            var key = GetKey(ctx,settingEntryRow, stockId);

            //按仓库汇总的即时库存及预留量（不考虑仓位，在过程中要做判断：有些单据预留设置了仓位，有些没有设置仓位）
            // 比如 即时库存仓库X有1个（在01仓位），A单 预留了1个（仓库为X，仓位为空），现在在B单上预留到01仓位，应该是预留不了的，因为库存量已经被预留到A单了
            var invQtyByStock = invObjs
                .GroupBy(f => GetKey(f))
                .ToDictionary(f => f.Key, f => f.ToList().Sum(o => Convert.ToDecimal(o["fqty"])));
            var resQtyByStock = allReserveObjs
                .GroupBy(f => GetKey(f))
                .ToDictionary(f => f.Key, f => f.ToList().Sum(o => Convert.ToDecimal(o["fqty"])));

            var sortInvObjs = new List<DynamicObject>();

            //优先级1. 按预留单据的仓库、库存状态匹配
            var first = invObjs.Where(f => stockId.EqualsIgnoreCase(Convert.ToString(f["fstorehouseid"]))
                                           && stockStatus.EqualsIgnoreCase(Convert.ToString(f["fstockstatus"])))
                .OrderByDescending(o => Convert.ToDecimal(o["fqty"]));
            sortInvObjs.AddRange(first);

            //优先级2. 按预留单据的仓库匹配（排除当前库存状态）
            first = invObjs.Where(f => stockId.EqualsIgnoreCase(Convert.ToString(f["fstorehouseid"]))
                                       && !stockStatus.EqualsIgnoreCase(Convert.ToString(f["fstockstatus"]))
                )
                .OrderByDescending(o => Convert.ToDecimal(o["fqty"]));
            sortInvObjs.AddRange(first);

            // 销售合同（非出现货）：所有仓库都能预留
            var fisoutspot = false;
            if (reserveSettingData.fdemandformid.EqualsIgnoreCase("ydj_order"))
            {
                var demandRow = settingEntryRow.DemandRowData as DynamicObject;

                // 出现货
                fisoutspot = Convert.ToBoolean(demandRow["fisoutspot"]);
                if (!fisoutspot)
                {
                    sortInvObjs.AddRange(invObjs.Except(sortInvObjs).OrderByDescending(o => Convert.ToDecimal(o["fqty"])));//其余的优先预留库存量大的
                }
            }

            decimal haveQty = 0;//累计预留数量
            //预留到仓库：一个仓库一个仓库的设置
            foreach (var invRow in sortInvObjs)
            {
                //即时库存的仓库、库存状态
                stockId = Convert.ToString(invRow["fstorehouseid"]);
                stockStatus = Convert.ToString(invRow["fstockstatus"]);

                var reserveQty = CreateReserveTranceByHouse(ctx, reserveSettingData, settingEntryRow, invObjs, allReserveObjs,
                    fplanqty, haveQty, isUpdate, stockId, stockStatus, invQtyByStock, resQtyByStock);

                haveQty += reserveQty;

                if (haveQty >= fplanqty)
                {
                    break;
                }
            }

            CalclateDemandEntryRowQty(settingEntryRow);

            // 销售合同且出现货，且没有可预留量，添加一行预留量为0的跟踪明细行
            if (haveQty == 0 && fisoutspot)
            {
                var row = CreateTranceEntryRow(ctx, reserveSettingData, settingEntryRow, 0,
                    0, 0, isUpdate);
                row.fstorehouseid = stockId;
                row.fstockstatus = stockStatus;
            }
        }

        /// <summary>
        /// 根据自动推荐增加预留跟踪记录
        /// </summary>
        /// <param name="reserveSettingData"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="invObjs">即时库存数据</param>
        /// <param name="allReserveObjs">预留数据</param>
        private static void CreateReserveTranceByFIFO(UserContext ctx, ReserveSettingInfo reserveSettingData,
            ReserveSettingDemandInfo settingEntryRow, List<DynamicObject> invObjs, List<DynamicObject> allReserveObjs, List<StockDetailModel> stockDetailDatas, bool isUpdate)
        {
            var fplanqty = settingEntryRow.RealDemandQty - settingEntryRow.fqty;//基本单位需求数量的增加量

            //按仓库汇总的即时库存及预留量（不考虑仓位，在过程中要做判断：有些单据预留设置了仓位，有些没有设置仓位）
            // 比如 即时库存仓库X有1个（在01仓位），A单 预留了1个（仓库为X，仓位为空），现在在B单上预留到01仓位，应该是预留不了的，因为库存量已经被预留到A单了
            var invQtyByStock = invObjs
                .GroupBy(f => GetKey(f))
                .ToDictionary(f => f.Key, f => f.ToList().Sum(o => Convert.ToDecimal(o["fqty"])));
            var resQtyByStock = allReserveObjs
                .GroupBy(f => GetKey(f))
                .ToDictionary(f => f.Key, f => f.ToList().Sum(o => Convert.ToDecimal(o["fqty"])));

            decimal haveQty = 0;//累计预留数量
            foreach (var invRow in stockDetailDatas)
            {
                if (invRow.fbaseqty <= 0)
                {
                    continue;
                }

                var reserveQty = CreateReserveTranceByHouse(ctx, reserveSettingData, settingEntryRow, invObjs, allReserveObjs,
                    fplanqty, haveQty, isUpdate, invRow.fstorehouseid, invRow.fstockstatus, invQtyByStock, resQtyByStock, invRow.fbaseqty);

                haveQty += reserveQty;
                invRow.fbaseqty -= reserveQty;

                if (haveQty >= fplanqty)
                {
                    break;
                }
            }
        }

        /// <summary>
        /// 根据仓库、库存状态生成预留跟踪记录
        /// </summary>
        /// <param name="maxReserveQty">不为空时，表示指定最大预留量</param>
        /// <returns>返回预留数</returns>
        private static decimal CreateReserveTranceByHouse(UserContext ctx, ReserveSettingInfo reserveSettingData,
            ReserveSettingDemandInfo settingEntryRow, List<DynamicObject> invObjs, List<DynamicObject> allReserveObjs,
            decimal fplanqty, decimal haveQty, bool isUpdate, string stockId, string stockStatus,
            Dictionary<string, decimal> invQtyByStock, Dictionary<string, decimal> resQtyByStock, decimal? maxReserveQty = null)
        {
            //还需要做预留的数量=需求数量 - 本次累计的预留量
            var leaveQty = fplanqty - haveQty;
            if (leaveQty <= 0)
            {
                return 0;
            }

            //本仓库库位的即时库存数量
            var xx = FindDataByStockAndStatus(invObjs, stockId, stockStatus);
            var inventoryQty = xx?.Sum(o => Convert.ToDecimal(o["fqty"]));

            //所有已关联的总的预留量
            var yy = FindDataByStockAndStatus(allReserveObjs, stockId, stockStatus);
            var allReserveQty = yy?.Sum(o => Convert.ToDecimal(o["fqty"]));

            //本单关联的总的预留量（注意不要加 fsourceentryid 过滤：因为有多行商品一样的数据，这里要按商品相同的汇总）
            var currentEntryData = settingEntryRow.DemandRowData as DynamicObject;
            var sourceEntryId = reserveSettingData.fdemandformid.EqualsIgnoreCase("ydj_order")
                ? Convert.ToString(currentEntryData?["fsourceentryid_e"])
                : Convert.ToString(currentEntryData?["fsourceentryid"]);
            decimal? currentReserveQty = 0M;
            //if (!sourceEntryId.IsNullOrEmptyOrWhiteSpace())
            {
                var currentReserveObjs =
                    yy?.Where(f => reserveSettingData.fdemandbillpkid.EqualsIgnoreCase(f["fsourceinterid"]?.ToString())).ToList() ?? new List<DynamicObject>();
                var currentBillData = reserveSettingData.DemandBillData as DynamicObject;
                var srcFormId = Convert.ToString(currentBillData["fsourcetype"]);
                var srcBillNo = Convert.ToString(currentBillData["fsourcenumber"]);
                if (!srcFormId.IsNullOrEmptyOrWhiteSpace() && !srcBillNo.IsNullOrEmptyOrWhiteSpace())
                {
                    var except = allReserveObjs
                        .Where(f => srcFormId.EqualsIgnoreCase(Convert.ToString(f["fsourceformid"]))
                                    && srcBillNo.Contains(Convert.ToString(f["fsourcenumber"])))
                        .ToList();
                    currentReserveObjs.AddRange(except);
                }

                currentReserveQty = currentReserveObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));
            }

            //当前行之外的，跟当前行商品一致的其他需求行已经设置的预留量（比如有两行一模一样的商品行，另外一行设置了预留，则计算到当前行的可用量时，需要考虑进去）
            var otherReserveQty = GetOtherReserveQty(reserveSettingData, settingEntryRow, stockId, stockStatus);

            //当前需求行的预留跟踪中，该仓库已经设置预留量
            var currentTranceObj = settingEntryRow.TraceEntry
                .Where(f => f.fstockstatus == stockStatus && f.fstorehouseid == stockId).ToList();
            var currentTranceQty = GetReserveTranceRowQty(currentTranceObj);

            //当前行的可预留量 = 即时库存数量 - 所有已关联的总的预留量 + 本单关联的总的预留量 - 【当前行之外的，跟当前行商品一致的其他需求行已经设置的预留量】 - 【当前行该仓库已设置的预留量】
            var canReserveQty = (inventoryQty.GetValueOrDefault() - allReserveQty.GetValueOrDefault()) +
                                currentReserveQty.GetValueOrDefault() - otherReserveQty - currentTranceQty;

            //看整个仓库的可预留量
            var key = GetKey(ctx,settingEntryRow, stockId);
            if (stockId.IsNullOrEmptyOrWhiteSpace() == false)
            {
                if (!invQtyByStock.TryGetValue(key, out var invQtyX))
                {
                    invQtyByStock[key] = invQtyX;
                }

                if (!resQtyByStock.TryGetValue(key, out var resQtyX))
                {
                    resQtyByStock[key] = resQtyX;
                }

                var stkCanQty = invQtyX - resQtyX + currentReserveQty.GetValueOrDefault();
                if (stkCanQty < canReserveQty)
                {
                    canReserveQty = stkCanQty;
                }
            }

            if (canReserveQty <= 0)
            {
                return 0;
            }

            //本仓库、库位的预留数量
            decimal qty = leaveQty > canReserveQty ? canReserveQty : leaveQty;
            if (maxReserveQty.HasValue && qty > maxReserveQty)
            {
                qty = maxReserveQty.Value;
            }

            var row = CreateTranceEntryRow(ctx, reserveSettingData, settingEntryRow, inventoryQty.GetValueOrDefault(),
                canReserveQty, qty, isUpdate);
            row.fstorehouseid = stockId;
            row.fstockstatus = stockStatus;

            row.fqty_d = qty;
            row.fbizqty_d = qty;

            haveQty += qty;

            // 累加仓库预留数
            if (!resQtyByStock.ContainsKey(key))
            {
                resQtyByStock[key] = 0;
            }

            resQtyByStock[key] += qty;

            return qty;
        }


        /// <summary>
        /// 当前行之外的，跟当前行商品一致的其他行已经设置的预留量（比如有两行一模一样的商品行，另外一行设置了预留，则计算到当前行的可用量时，需要考虑进去）
        /// </summary>
        /// <param name="reserveSettingData"></param>
        /// <param name="settingEntryRow">当前要处理的需求明细行</param>
        /// <param name="stockId"></param>
        /// <param name="stockStatus"></param>
        /// <returns></returns>
        private static decimal GetOtherReserveQty(ReserveSettingInfo reserveSettingData, ReserveSettingDemandInfo settingEntryRow, string stockId = null, string stockStatus = null)
        {
            var mapRows = reserveSettingData.DemandEntry.Where(f => f.fdemandentryid != settingEntryRow.fdemandentryid
                                                                    && f.fmaterialid == settingEntryRow.fmaterialid
                                                                    && f.fmtono == settingEntryRow.fmtono
                                                                    && f.fattrinfo_e == settingEntryRow.fattrinfo_e
                                                                    && f.fcustomdesc == settingEntryRow.fcustomdesc
                                                                    && f.funitid == settingEntryRow.funitid
            );
            if (mapRows == null || !mapRows.Any())
            {
                return 0;
            }

            var tranceRows = mapRows.SelectMany(f => f.TraceEntry);

            //库存状态要么相等，要么预留跟踪记录中的库存状态为空
            tranceRows = tranceRows?.Where(o => stockStatus.IsNullOrEmptyOrWhiteSpace() || o.fstockstatus.EqualsIgnoreCase(stockStatus));

            //仓库要么相等，要么需求明细中的仓库为空
            tranceRows = tranceRows?.Where(o => stockId.IsNullOrEmptyOrWhiteSpace() || o.fstorehouseid.EqualsIgnoreCase(stockId));

            if (tranceRows == null || !tranceRows.Any())
            {
                return 0;
            }

            var result = GetReserveTranceRowQty(tranceRows);

            return result;
        }

        private static ReserveSettingTraceInfo CreateTranceEntryRow(UserContext ctx, ReserveSettingInfo reserveSettingData, ReserveSettingDemandInfo settingEntryRow,
            decimal inventoryQty, decimal canReserveQty, decimal qty, bool isUpdate)
        {
            var row = new ReserveSettingTraceInfo();
            row.Id = Guid.NewGuid().ToString();
            row.fdirection_d = "0";
            row.fopdesc = "0";
            row.freservenote = "新增预留";
            if (isUpdate)
            {
                row.freservenote = "自动更新预留：需求数量增加，相应的增加预留";
            }
            row.foptime = DateTime.Now;
            row.fopuserid = ctx.UserId;
            row.ffromformid = "stk_inventorylist";//默认来源即时库存
            row.ffrombillno = "";
            row.ffrombillpkid = "";
            row.freservetrancepkid = "";

            row.fclosestatus_d = settingEntryRow.fclosestatus;

            row.fmaterialid_d = settingEntryRow.fmaterialid;
            row.fbizunitid_d = settingEntryRow.fbizunitid;
            row.funitid_d = settingEntryRow.funitid;

            row.finventoryqty_d = inventoryQty;
            row.fcanreserveqty_d = canReserveQty;
            row.fqty_d = qty;
            row.fbizqty_d = qty;

            row.freservedateto_old = "";

            settingEntryRow.TraceEntry.Add(row);

            return row;
        }


        /// <summary>
        /// 按仓库、库位、库存状态找到
        /// </summary>
        /// <param name="entrys"></param>
        /// <returns></returns>
        private static IEnumerable<DynamicObject> FindDataByStockAndStatus(List<DynamicObject> entrys, string stockId, string stockStatus)
        {
            //库存状态要么相等，要么预留跟踪记录中的库存状态为空
            var temps = entrys?.Where(o => stockStatus.IsNullOrEmptyOrWhiteSpace() || Convert.ToString(o["fstockstatus"]).EqualsIgnoreCase(stockStatus));

            //仓库要么相等，要么需求明细中的仓库为空
            temps = temps?.Where(o => stockId.IsNullOrEmptyOrWhiteSpace() || Convert.ToString(o["fstorehouseid"]).EqualsIgnoreCase(stockId));

            return temps;
        }

        /// <summary>
        /// 填充预留设置--预留跟踪明细信息
        /// </summary>
        /// <param name="settingEntry"></param>
        /// <param name="reserveEntry">对应预留单行数据</param>
        private static void FillReserveSettingTranceData(ReserveSettingDemandInfo settingEntry, DynamicObject reserveEntry)
        {
            if (reserveEntry == null) return;

            settingEntry.fqty = Convert.ToDecimal(reserveEntry["fqty"]);
            settingEntry.fbizqty = Convert.ToDecimal(reserveEntry["fbizqty"]);
            settingEntry.freserveentryid = reserveEntry["Id"]?.ToString();

            //填充预留跟踪记录   
            var traceEns = reserveEntry["fdetail"] as DynamicObjectCollection;
            if (traceEns != null && traceEns.Count > 0)
            {
                foreach (var item in traceEns)
                {
                    var row = new ReserveSettingTraceInfo();
                    row.fstorehouseid = Convert.ToString(item["fstorehouseid"]);
                    row.fstockstatus = Convert.ToString(item["fstockstatus"]);

                    row.fbizqty_d = Convert.ToDecimal(item["fbizqty_d"]);
                    row.fqty_d = Convert.ToDecimal(item["fqty_d"]);

                    row.fdirection_d = item["fdirection_d"]?.ToString();
                    row.fopdesc = item["fopdesc"]?.ToString();
                    row.freservenote = item["freservenote"]?.ToString();
                    if (item["foptime"] != null)
                    {
                        row.foptime = Convert.ToDateTime(item["foptime"]);
                    }
                    row.fopuserid = item["fopuserid"]?.ToString();

                    var freservedateto_d = item["freservedateto_d"] == null
                        ? settingEntry.freservedateto
                        : Convert.ToDateTime(item["freservedateto_d"]);

                    row.freservedateto_d = freservedateto_d;
                    row.freservedateto_old = freservedateto_d.ToString("yyyy-MM-dd");

                    row.ffromformid = Convert.ToString(item["ffromformid"]);
                    row.ffrombillno = Convert.ToString(item["ffrombillno"]);
                    row.ffrombillpkid = Convert.ToString(item["ffrombillpkid"]);
                    row.freservetrancepkid = Convert.ToString(item["Id"]);

                    row.fmaterialid_d = Convert.ToString(item["fmaterialid_d"]);
                    row.fbizunitid_d = Convert.ToString(item["fbizunitid_d"]);
                    row.funitid_d = Convert.ToString(item["funitid_d"]);

                    row.fclosestatus_d = settingEntry.fclosestatus;

                    row.Id = Guid.NewGuid().ToString();

                    settingEntry.TraceEntry.Add(row);
                }
            }

            CalclateDemandEntryRowQty(settingEntry);
        }

        /// <summary>
        /// 填充预留设置表头信息
        /// </summary>
        /// <param name="reserveSettingData"></param>
        /// <param name="demandForm"></param>
        /// <param name="demandBill"></param>
        /// <param name="reserveDay"></param>
        /// <param name="reserveBill"></param>
        private static void FillReserveSettingHead(UserContext ctx, ReserveSettingInfo reserveSettingData, HtmlForm demandForm, DynamicObject demandBill, DynamicObject reserveBill)
        {
            reserveSettingData.fdemandformid = demandForm.Id;
            reserveSettingData.fdemandbillpkid = demandBill["Id"]?.ToString();
            reserveSettingData.fsourcestatus = demandBill["fstatus"]?.ToString();
            reserveSettingData.fdemandbillno = demandForm.GetNumberField()?.DynamicProperty?.GetValue<string>(demandBill);
            if (reserveBill != null)
            {
                reserveSettingData.freserveobjecttype = reserveBill["freserveobjecttype"]?.ToString();
                reserveSettingData.freserveobjectid = reserveBill["freserveobjectid"]?.ToString();
                reserveSettingData.fdescription = reserveBill["fdescription"]?.ToString();
                reserveSettingData.freservepkid = reserveBill["Id"]?.ToString();
            }
            else
            {
                if (demandForm.Id.EqualsIgnoreCase("ydj_saleintention") || demandForm.Id.EqualsIgnoreCase("ydj_order") || demandForm.Id.EqualsIgnoreCase("stk_sostockout"))
                {
                    reserveSettingData.freserveobjecttype = "ydj_customer";
                    reserveSettingData.freserveobjectid = demandBill["fcustomerid"]?.ToString();
                    //reserveSettingData.fdescription = demandBill["fdescription"]?.ToString();
                }
            }
            if (demandForm.Id.EqualsIgnoreCase("ydj_order") || demandForm.Id.EqualsIgnoreCase("stk_sostockout"))
            {
                if (demandForm.Id.EqualsIgnoreCase("ydj_order"))
                {
                    reserveSettingData.fstaffid = demandBill["fstaffid"]?.ToString();
                    reserveSettingData.fdeptid = demandBill["fdeptid"]?.ToString();
                }
                else if (demandForm.Id.EqualsIgnoreCase("stk_sostockout"))
                {
                    reserveSettingData.fstaffid = demandBill["fsostaffid"]?.ToString();
                    reserveSettingData.fdeptid = demandBill["fsodeptid"]?.ToString();
                }
                var provinceInfo = demandBill["fprovince_ref"] as DynamicObject;
                var cityInfo = demandBill["fcity_ref"] as DynamicObject;
                var regionInfo = demandBill["fregion_ref"] as DynamicObject;
                var detailAddress = provinceInfo?["fenumitem"]?.ToString() + cityInfo?["fenumitem"]?.ToString() + regionInfo?["fenumitem"]?.ToString() + demandBill["faddress"]?.ToString();
                reserveSettingData.faddress = detailAddress;
                reserveSettingData.fmanualnumber = demandBill["fwithin"]?.ToString();
                reserveSettingData.fsourcemark = demandBill["fdescription"]?.ToString();
            }

            SetDefaultReserveDate(ctx, reserveSettingData, demandForm, demandBill);

            SetDemandBillCancelStatus(reserveSettingData, demandForm, demandBill);
        }



        /// <summary>
        /// 填充预留设置--需求明细
        /// </summary>
        /// <param name="cvtRule"></param>
        /// <param name="demandForm"></param>
        /// <param name="dcRow"></param>
        /// <param name="demandEntryRow">预留需求单的需求明细行数据包</param>
        /// <param name="soorder_qty_maps">销售合同关联的数量</param>
        private static void FillReserveSettingEntityData(UserContext ctx, ConvertRule cvtRule, HtmlForm demandForm, BizDynamicDataRow dcRow,
                                                DynamicObject demandEntryRow, ReserveSettingInfo reserveSettingData,
                                                IBizExpressionEvaluator exprEngine, IBizExpressionContext exprCtx, IBizExpression expression, Dictionary<string, OrderQty> soorder_qty_maps)
        {
            var settingEntryRow = new ReserveSettingDemandInfo();
            reserveSettingData.DemandEntry.Add(settingEntryRow);
            settingEntryRow.fdemandentryid = demandEntryRow["id"]?.ToString();
            settingEntryRow.DemandRowData = demandEntryRow;
            settingEntryRow.freservedateto = reserveSettingData.fdefautdate;
            var bdProvide = ctx.Container.GetService<IBaseFormProvider>();
            var fstaffid = bdProvide.GetMyStaff(ctx)?.Id;
            settingEntryRow.fstaffid = fstaffid.IsNullOrEmptyOrWhiteSpace() ? "####" : fstaffid;//如果用户不关联员工，认为无权限

            foreach (var map in cvtRule.FieldMappings)
            {
                if (map.Id.IsNullOrEmptyOrWhiteSpace() || map.SrcFieldId.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                object srcFieldVal = null;
                switch (map.MapType)
                {
                    case (int)Enu_FieldMapType.Expression:
                        dcRow.ActiveDataObject = demandEntryRow;

                        expression.ExpressionText = map.SrcFieldId;
                        var vars = exprEngine.GetNameExpression(expression);
                        string strExprTxt = map.SrcFieldId;
                        foreach (var varName in vars)
                        {
                            strExprTxt = strExprTxt.Replace(varName, varName.Replace(".", "_"));
                        }
                        expression.ExpressionText = strExprTxt;
                        srcFieldVal = exprEngine.Eval(expression, exprCtx);
                        break;
                    default:
                        var srcField = demandForm.GetField(map.SrcFieldId);
                        if (srcField == null) continue;
                        srcFieldVal = srcField.DynamicProperty?.GetValue(demandEntryRow);
                        break;
                }
                SetValue(settingEntryRow, map.Id, srcFieldVal);
            }


            /*
             * Feat#4220
             * 采购入库单特殊处理：基于按{ 销售合同明细行的【销售数量】- 销售合同明细行的【预留量】- 销售合同明细行关联的销售出库明细的【实发数量】累加 } 的 差额（即大于0） 进行判断。

            （注意：这里的【实发数量】累计，取的是所有数据状态（重新审核、创建、已提交、已审核）且未作废的销售出库单）
            
             Feat#4461
            1、《销售合同-预留设置.需求明细》的【需求数量】应考虑已退货数，需改按以下规则调整逻辑：
            a、取按{ 销售合同明细行的【销售数量】4- 销售合同明细行关联的销售出库明细的【实发数量】累加4 +销售合同明细行的【销售已退换数量】} 的 差额（即大于0） 
            （注意：这里的【实发数量】累计，取的是所有数据状态（重新审核、创建、已提交、已审核）且未作废的销售出库单）
            b、采购入库关联预留同样要考虑。

            预留改造：新增库存调拨数量
             */

            if (soorder_qty_maps != null && soorder_qty_maps.Count > 0)
            {
                if (soorder_qty_maps.TryGetValue(settingEntryRow.fdemandentryid, out var value))
                {
                    settingEntryRow.foutqty = value.OutQty;
                    settingEntryRow.ftransferqty = value.TransferQty;
                }
            }
        }


        private static void SetValue(ReserveSettingDemandInfo settingEntryRow, string targetKey, object srcFieldVal)
        {
            switch (targetKey)
            {
                case "fmaterialid":
                    settingEntryRow.fmaterialid = Convert.ToString(srcFieldVal);
                    break;
                case "fattrinfo":
                    settingEntryRow.fattrinfo = Convert.ToString(srcFieldVal);
                    break;
                case "fattrinfo_e":
                    settingEntryRow.fattrinfo_e = Convert.ToString(srcFieldVal);
                    break;
                case "fcustomdesc":
                    settingEntryRow.fcustomdesc = Convert.ToString(srcFieldVal);
                    break;
                case "fresultbrandid":
                    settingEntryRow.fresultbrandid = Convert.ToString(srcFieldVal);
                    break;
                case "funitid":
                    settingEntryRow.funitid = Convert.ToString(srcFieldVal);
                    break;
                case "fbizunitid":
                    settingEntryRow.fbizunitid = Convert.ToString(srcFieldVal);
                    break;
                case "fplanqty":
                    settingEntryRow.fplanqty = srcFieldVal == null ? 0 : Convert.ToDecimal(srcFieldVal);
                    break;
                case "fmtono":
                    settingEntryRow.fmtono = Convert.ToString(srcFieldVal);
                    break;
                case "fclosestatus":
                    settingEntryRow.fclosestatus = Convert.ToString(srcFieldVal);
                    break;
                case "fstorehouseid":
                    settingEntryRow.fstorehouseid = Convert.ToString(srcFieldVal);
                    break;
                case "fstockstatus":
                    settingEntryRow.fstockstatus = Convert.ToString(srcFieldVal);
                    break;
            }
        }





        /// <summary>
        /// 填充预留设置的即时库存、可预留量
        /// </summary>
        /// <param name="reserveSettingData">预留设置数据</param>
        private static void FillReserveSettingInvQty(UserContext ctx, ReserveSettingInfo reserveSettingData)
        {
            if (reserveSettingData.DemandEntry == null || !reserveSettingData.DemandEntry.Any())
            {
                return;
            }

            var svcMetas = ctx.Container.GetService<IMetaModelService>();
            var reserveSttingMeta = svcMetas.LoadFormModel(ctx, "stk_reservedialog");

            var inventoryList = reserveSettingData.InventoryList as DynamicObjectCollection;
            var reserveEntrys = reserveSettingData.AllReserveEntrys as DynamicObjectCollection;
            if (inventoryList == null)
            {
                inventoryList = MatchInventoryList(ctx, reserveSettingData);
                if (inventoryList == null)
                {
                    return;
                }
            }

            //根据需求明细获取所有的预留数据
            if (reserveEntrys == null)
            {
                reserveEntrys = GetReserveEntrys(ctx, reserveSettingData);
            }

            //按仓库汇总的即时库存及预留量（不考虑仓位，在过程中要做判断：有些单据预留设置了仓位，有些没有设置仓位）
            // 比如 即时库存仓库X有1个（在01仓位），A单 预留了1个（仓库为X，仓位为空），现在在B单上预留到01仓位，应该是预留不了的，因为库存量已经被预留到A单了
            var invQtyByStock = inventoryList
                .GroupBy(f => { return GetKey(f); })
                .ToDictionary(f => f.Key, f => f.ToList().Sum(o => Convert.ToDecimal(o["fqty"])));
            var resQtyByStock = reserveEntrys
                .GroupBy(f => { return GetKey(f); })
                .ToDictionary(f => f.Key, f => f.ToList().Sum(o => Convert.ToDecimal(o["fqty"])));

            //根据需求明细获取即时库存数据  
            foreach (var settingEntryRow in reserveSettingData.DemandEntry)
            {
                //即时库存数量
                var invObjs = FindInvDataByInvFlexFieldKey(ctx, reserveSettingData, inventoryList, settingEntryRow);
                var inventoryQty = invObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));
                settingEntryRow.finventoryqty = inventoryQty.GetValueOrDefault();

                //所有已关联的总的预留量
                var allReserveObjs = FindInvDataByInvFlexFieldKey(ctx, reserveSettingData, reserveEntrys, settingEntryRow);
                var allReserveQty = allReserveObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));

                //本单关联的总的预留量（注意不要加 fsourceentryid 过滤：因为有多行商品一样的数据，这里要按商品相同的汇总）
                var currentReserveObjs = allReserveObjs.Where(f => reserveSettingData.fdemandbillpkid.EqualsIgnoreCase(f["fsourceinterid"]?.ToString())).ToList();
                var currentBillData = reserveSettingData.DemandBillData as DynamicObject;
                var srcFormId = Convert.ToString(currentBillData["fsourcetype"]);
                var srcBillNo = Convert.ToString(currentBillData["fsourcenumber"]);
                if (!srcFormId.IsNullOrEmptyOrWhiteSpace() && !srcBillNo.IsNullOrEmptyOrWhiteSpace())
                {
                    var except = allReserveObjs.Where(f => srcFormId.EqualsIgnoreCase(Convert.ToString(f["fsourceformid"])) && srcBillNo.Contains(Convert.ToString(f["fsourcenumber"]))).ToList();
                    currentReserveObjs.AddRange(except);
                }
                var currentReserveQty = currentReserveObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));

                //当前行之外的，跟当前行商品一致的其他行已经设置的预留量（比如有两行一模一样的商品行，另外一行设置了预留，则计算到当前行的可用量时，需要考虑进去）
                var otherReserveQty = GetOtherReserveQty(reserveSettingData, settingEntryRow);

                //可预留量 =（即时库存数量 - 所有已关联的总的预留量）+ 本单关联的总的预留量 - 【当前行之外的，跟当前行商品一致的其他行已经设置的预留量】
                var canReserveQty = (inventoryQty.GetValueOrDefault() - allReserveQty.GetValueOrDefault()) + currentReserveQty.GetValueOrDefault() - otherReserveQty;
                settingEntryRow.fcanreserveqty = canReserveQty > 0 ? canReserveQty : 0;

                //预留跟踪信息
                var tranceRows = settingEntryRow.TraceEntry;
                if (tranceRows != null && tranceRows.Count > 0)
                {
                    foreach (var tranceRow in tranceRows)
                    {
                        var stockId = tranceRow.fstorehouseid;
                        var stockStatus = tranceRow.fstockstatus;

                        var key = GetKey(ctx,settingEntryRow, stockId);

                        //即时库存数量
                        var xx = FindDataByStockAndStatus(invObjs, tranceRow);
                        inventoryQty = xx?.Sum(o => Convert.ToDecimal(o["fqty"]));
                        tranceRow.finventoryqty_d = inventoryQty.GetValueOrDefault();

                        //对应仓库所有已关联的总的预留量
                        var yy = FindDataByStockAndStatus(allReserveObjs, tranceRow);
                        allReserveQty = yy?.Sum(o => Convert.ToDecimal(o["fqty"]));

                        //本单对应仓库关联的总的预留量 
                        var zz = FindDataByStockAndStatus(currentReserveObjs, tranceRow);
                        currentReserveQty = zz?.Sum(o => Convert.ToDecimal(o["fqty"]));

                        //当前行之外的，跟当前行商品一致的其他需求行已经设置的预留量（比如有两行一模一样的商品行，另外一行设置了预留，则计算到当前行的可用量时，需要考虑进去）
                        otherReserveQty = GetOtherReserveQty(reserveSettingData, settingEntryRow, stockId, stockStatus);

                        //可预留量 =（即时库存数量 - 对应仓库所有已关联的总的预留量）+ 本单对应仓库关联的总的预留量-当前行之外的，跟当前行商品一致的其他行已经设置的预留量
                        canReserveQty = (inventoryQty.GetValueOrDefault() - allReserveQty.GetValueOrDefault()) + currentReserveQty.GetValueOrDefault() - otherReserveQty;

                        //看整个仓库的可预留量
                        if (stockId.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            if (!invQtyByStock.TryGetValue(key, out var invQtyX))
                            {
                                invQtyByStock[key] = invQtyX;
                            }
                            if (!resQtyByStock.TryGetValue(key, out var resQtyX))
                            {
                                resQtyByStock[key] = resQtyX;
                            }

                            var stkCanQty = invQtyX - resQtyX + currentReserveQty.GetValueOrDefault();
                            if (stkCanQty < canReserveQty || canReserveQty < 0)
                            {
                                canReserveQty = stkCanQty;
                            }
                        }

                        tranceRow.fcanreserveqty_d = canReserveQty > 0 ? canReserveQty : 0;

                        //如果是自动增加的行，则预留数量不能大于可预留量
                        if (tranceRow.freservetrancepkid.IsNullOrEmptyOrWhiteSpace()
                            // 增加预留才判断
                            && tranceRow.fdirection_d.EqualsIgnoreCase("0")
                            && tranceRow.fqty_d > tranceRow.fcanreserveqty_d)
                        {
                            //本仓库、库位的预留数量
                            decimal qty = tranceRow.fqty_d > tranceRow.fcanreserveqty_d ? tranceRow.fcanreserveqty_d : tranceRow.fqty_d;

                            tranceRow.fqty_d = qty;
                            tranceRow.fbizqty_d = qty;

                            // 累加仓库预留数
                            if (!resQtyByStock.ContainsKey(key))
                            {
                                resQtyByStock[key] = 0;
                            }
                            resQtyByStock[key] += qty;
                        }

                        // 如果是自动增加的行，且预留量=0，则可预留量=0（因为是推荐预留）
                        if (tranceRow.freservetrancepkid.IsNullOrEmptyOrWhiteSpace()
                            && tranceRow.fqty_d == 0)
                        {
                            tranceRow.fcanreserveqty_d = 0;
                        }

                        // 如果是采购入库自动预留，可预留量=0，记录日志
                        if (tranceRow.freservetrancepkid.IsNullOrEmptyOrWhiteSpace() &&
                            tranceRow.ffromformid.EqualsIgnoreCase("stk_postockin") && tranceRow.fcanreserveqty_d == 0)
                        {
                            DebugUtil.WriteLogToFile($"fentryid={settingEntryRow?.fdemandentryid} 可预留量=0", "采购入库自动预留");
                        }
                    }
                }
            }
        }

        private static string GetKey(DynamicObject f)
        {
            List<string> values = new List<string>();
            foreach (var key in InvFlexFieldKeys)
            {
                var value = Convert.ToString(f[key]).Trim();
                values.Add(value);
            }

            var fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim();
            values.Add(fstorehouseid);
            return values.JoinEx(":", false);
        }

        private static string GetKey(UserContext ctx, ReserveSettingDemandInfo settingEntryRow, string fstorehouseid)
        {
            List<string> values = new List<string>();
            foreach (var key in InvFlexFieldKeys)
            {
                var value = GetFieldValue(settingEntryRow, key).Trim();
                values.Add(value); 
            }

            values.Add(fstorehouseid);
            return values.JoinEx(":", false);
        }


        /// <summary>
        /// 填充预留设置的即时库存、可预留量
        /// </summary>
        /// <param name="reserveSettingData">预留设置数据</param>
        public static void FillReserveSettingQty(UserContext ctx, DynamicObject reserveSettingData)
        {
            UpdateDBSchema(ctx);

            var svcMetas = ctx.Container.GetService<IMetaModelService>();
            var reserveSttingMeta = svcMetas.LoadFormModel(ctx, "stk_reservedialog");

            var demandForm = reserveSettingData["fdemandformid"].ToString();
            var demandBillId = reserveSettingData["fdemandbillpkid"]?.ToString();
            DynamicObject demandBill = ctx.LoadBizDataById(demandForm, demandBillId);

            var settingInfo = Convert2SettingInfo(ctx, reserveSettingData, demandBill);
            var inventoryList = MatchInventoryList(ctx, settingInfo);
            if (inventoryList == null)
            {
                return;
            }

            //根据需求明细获取所有的预留数据
            var reserveEntrys = GetReserveEntrys(ctx, settingInfo);
            var reserveEntryDt = reserveEntrys.DynamicCollectionItemPropertyType;

            var dataEntitySet = new ExtendedDataEntitySet();
            dataEntitySet.Parse(ctx, new List<DynamicObject> { reserveSettingData }, reserveSttingMeta);

            //根据需求明细获取即时库存数据
            var demandEntrys = dataEntitySet.FindByEntityKey("fentry");
            if (demandEntrys == null || !demandEntrys.Any())
            {
                return;
            }

            //按仓库汇总的即时库存及预留量（不考虑仓位，在过程中要做判断：有些单据预留设置了仓位，有些没有设置仓位）
            // 比如 即时库存仓库X有1个（在01仓位），A单 预留了1个（仓库为X，仓位为空），现在在B单上预留到01仓位，应该是预留不了的，因为库存量已经被预留到A单了
            var invQtyByStock = inventoryList.GroupBy(f => GetKey(f))
                .ToDictionary(s => s.Key, s => s.Sum(o => Convert.ToDecimal(o["fqty"])));
            var resQtyByStock = reserveEntrys.GroupBy(f => GetKey(f))
                .ToDictionary(s => s.Key, s => s.Sum(o => Convert.ToDecimal(o["fqty"])));

            foreach (var entry in demandEntrys)
            {
                var settingDemandInfo = Convert2SettingDemandInfo(entry.DataEntity);

                //即时库存数量
                var invObjs = FindInvDataByInvFlexFieldKey(ctx, settingInfo, inventoryList, settingDemandInfo);
                var inventoryQty = invObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));
                entry.DataEntity["finventoryqty"] = inventoryQty;

                //所有已关联的总的预留量
                var allReserveObjs = FindInvDataByInvFlexFieldKey(ctx, settingInfo, reserveEntrys, settingDemandInfo);
                var allReserveQty = allReserveObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));

                //本单关联的总的预留量（注意不要加 fsourceentryid 过滤：因为有多行商品一样的数据，这里要按商品相同的汇总）
                var fdemandbillpkid = settingInfo.fdemandbillpkid;
                var currentReserveObjs = allReserveObjs.Where(f => fdemandbillpkid.EqualsIgnoreCase(Convert.ToString(f["fsourceinterid"])))?.ToList();
                var currentBillData = settingInfo.DemandBillData as DynamicObject;
                var srcFormId = Convert.ToString(currentBillData["fsourcetype"]);
                var srcBillNo = Convert.ToString(currentBillData["fsourcenumber"]);
                if (!srcFormId.IsNullOrEmptyOrWhiteSpace() && !srcBillNo.IsNullOrEmptyOrWhiteSpace())
                {
                    var except = allReserveObjs.Where(f => srcFormId.EqualsIgnoreCase(Convert.ToString(f["fsourceformid"])) && srcBillNo.Contains(Convert.ToString(f["fsourcenumber"]))).ToList();
                    currentReserveObjs.AddRange(except);
                }
                var currReserveQty = currentReserveObjs?.Sum(o => Convert.ToDecimal(o["fqty"]));

                //可预留量 =（即时库存数量 - 所有已关联的总的预留量）+ 本单关联的总的预留量
                var canReserveQty = (inventoryQty.GetValueOrDefault() - allReserveQty.GetValueOrDefault()) + currReserveQty.GetValueOrDefault();

                entry.DataEntity["fcanreserveqty"] = canReserveQty > 0 ? canReserveQty : 0;

                //预留跟踪信息
                var tranceRows = entry.DataEntity["fdetail"] as DynamicObjectCollection;
                if (tranceRows != null && tranceRows.Count > 0)
                {
                    var fentryid = Convert.ToString(entry["id"]);
                    foreach (var tranceRow in tranceRows)
                    {
                        var isNewTrance = Convert.ToString(tranceRow["freservetrancepkid"]).IsNullOrEmptyOrWhiteSpace();

                        var tranceInfo = Convert2SettingTranceInfo(entry.DataEntity, tranceRow);
                        //即时库存数量
                        var xx = FindDataByStockAndStatus(invObjs, tranceInfo);
                        inventoryQty = xx?.Sum(o => Convert.ToDecimal(o["fqty"]));
                        tranceRow["finventoryqty_d"] = inventoryQty.GetValueOrDefault();

                        //所有已关联的总的预留量
                        var yy = FindDataByStockAndStatus(allReserveObjs, tranceInfo);
                        allReserveQty = yy?.Sum(o => Convert.ToDecimal(o["fqty"]));

                        //本行关联的总的预留量 
                        var zz = FindDataByStockAndStatus(currentReserveObjs, tranceInfo).Where(s => Convert.ToString(s["fentryid"]).EqualsIgnoreCase(fentryid));
                        currReserveQty = zz?.Sum(o => Convert.ToDecimal(o["fqty"]));

                        //可预留量 =（即时库存数量 - 所有已关联的总的预留量）+ 本单关联的总的预留量
                        canReserveQty = (inventoryQty.GetValueOrDefault() - allReserveQty.GetValueOrDefault()) + currReserveQty.GetValueOrDefault();

                        //看整个仓库的可预留量（可能是其他单预留时，只预留到仓库，没有指定到仓位，则当前单如果预留到仓位，则加总起来，不能超过按仓库统计的总库存量）
                        var stockId = Convert.ToString(tranceRow["fstorehouseid"]);
                        var key = GetKey(ctx,settingDemandInfo, stockId);
                        if (stockId.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            if (!invQtyByStock.TryGetValue(key, out var invQtyX))
                            {
                                invQtyByStock[key] = invQtyX = 0;
                            }
                            if (!resQtyByStock.TryGetValue(key, out var resQtyX))
                            {
                                resQtyByStock[key] = resQtyX = 0;
                            }

                            var stkCanQty = invQtyX - resQtyX + currReserveQty.GetValueOrDefault();
                            if (stkCanQty < canReserveQty)
                            {
                                canReserveQty = stkCanQty;
                            }
                        }

                        canReserveQty = canReserveQty > 0 ? canReserveQty : 0;

                        var qty = Convert.ToDecimal(tranceRow["fqty_d"]);

                        //如果是自动增加的行，则预留数量不能大于可预留量
                        if (isNewTrance)
                        {
                            //本仓库、库位的预留数量
                            qty = qty > canReserveQty ? canReserveQty : qty;

                            // 累加仓库预留数
                            if (!resQtyByStock.ContainsKey(key))
                            {
                                resQtyByStock[key] = 0;
                            }
                            resQtyByStock[key] += qty;
                        }

                        tranceRow["fqty_d"] = qty;
                        tranceRow["fbizqty_d"] = qty;

                        tranceRow["fcanreserveqty_d"] = canReserveQty;
                    }
                }
            }
        }


        public static ReserveSettingInfo Convert2SettingInfo(UserContext ctx, DynamicObject reserveSettingData, DynamicObject demandBill)
        {
            ReserveSettingInfo settingInfo = new ReserveSettingInfo();
            settingInfo.fdemandformid = reserveSettingData["fdemandformid"]?.ToString();
            settingInfo.fdemandbillpkid = reserveSettingData["fdemandbillpkid"]?.ToString();
            settingInfo.fdemandbillno = reserveSettingData["fdemandbillno"]?.ToString();
            settingInfo.freserveday = Convert.ToInt32(reserveSettingData["freserveday"]);
            settingInfo.freserveobjecttype = reserveSettingData["freserveobjecttype"]?.ToString();
            settingInfo.freserveobjectid = reserveSettingData["freserveobjectid"]?.ToString();
            settingInfo.fdescription = reserveSettingData["fdescription"]?.ToString();
            settingInfo.freservepkid = reserveSettingData["freservepkid"]?.ToString();
            settingInfo.fcancelstatus = Convert.ToBoolean(reserveSettingData["fcancelstatus"]);
            settingInfo.feditrows = Convert.ToString(reserveSettingData["feditrows"]).FromJson<List<string>>();

            settingInfo.fstaffid = reserveSettingData["fstaffid"]?.ToString();
            settingInfo.fdeptid = reserveSettingData["fdeptid"]?.ToString();
            settingInfo.faddress = reserveSettingData["faddress"]?.ToString();
            settingInfo.fmanualnumber = reserveSettingData["fmanualnumber"]?.ToString();
            settingInfo.fsourcemark = reserveSettingData["fsourcemark"]?.ToString();

            var profileService = ctx.Container.GetService<ISystemProfile>();
            var fallowsalescontrol = profileService.GetSystemParameter(ctx, "stk_stockparam", "fallowsalescontrol", false);
            var autoReserve = profileService.GetSystemParameter(ctx, "bas_storesysparam", "foutspotautores", false);
            settingInfo.AllowsalesControl = fallowsalescontrol;
            settingInfo.AutoReserveWhen = autoReserve;

            var demandData = ctx.LoadBizDataById(settingInfo.fdemandformid, settingInfo.fdemandbillpkid);
            settingInfo.DemandBillData = demandData;
            settingInfo.fsourcestatus = demandData["fstatus"]?.ToString();

            var settingEntrys = reserveSettingData["fentry"] as DynamicObjectCollection;
            if (settingEntrys == null || settingEntrys.Count == 0)
            {
                return settingInfo;
            }

            var bdProvide = ctx.Container.GetService<IBaseFormProvider>();
            var fstaffid = bdProvide.GetMyStaff(ctx)?.Id;
            foreach (var settingEntryRow in settingEntrys)
            {
                var demandEntryRow = Convert2SettingDemandInfo(settingEntryRow);
                demandEntryRow.fstaffid = fstaffid.IsNullOrEmptyOrWhiteSpace() ? "####" : fstaffid;//如果用户不关联员工，认为无权限
                settingInfo.DemandEntry.Add(demandEntryRow);

                var logTrancEns = settingEntryRow["fdetail"] as DynamicObjectCollection;
                if (logTrancEns == null || logTrancEns.Count == 0)
                {
                    continue;
                }

                foreach (var tranceRow in logTrancEns)
                {
                    var trance = Convert2SettingTranceInfo(settingEntryRow, tranceRow);

                    demandEntryRow.TraceEntry.Add(trance);
                }
            }

            settingInfo.fdefautdate = settingInfo.DemandEntry.Max(f => f.freservedateto);

            return settingInfo;
        }


        private static ReserveSettingDemandInfo Convert2SettingDemandInfo(DynamicObject settingEntryRow)
        {
            var demandInfo = new ReserveSettingDemandInfo();
            demandInfo.fmaterialid = settingEntryRow["fmaterialid"]?.ToString();
            demandInfo.fattrinfo = settingEntryRow["fattrinfo"]?.ToString();
            demandInfo.fattrinfo_e = settingEntryRow["fattrinfo_e"]?.ToString();
            demandInfo.fcustomdesc = settingEntryRow["fcustomdesc"]?.ToString();
            demandInfo.funitid = settingEntryRow["funitid"]?.ToString();
            demandInfo.fbizunitid = settingEntryRow["fbizunitid"]?.ToString();
            demandInfo.fbizqty = Convert.ToDecimal(settingEntryRow["fbizqty"]);
            demandInfo.fbizplanqty = Convert.ToDecimal(settingEntryRow["fbizplanqty"]);

            demandInfo.fresultbrandid = settingEntryRow["fresultbrandid"]?.ToString();

            if (!settingEntryRow["freservedateto"].IsNullOrEmptyOrWhiteSpace())
            {
                demandInfo.freservedateto = Convert.ToDateTime(settingEntryRow["freservedateto"]);
            }
            demandInfo.fbizcanreserveqty = Convert.ToDecimal(settingEntryRow["fbizcanreserveqty"]);
            demandInfo.fbizinventoryqty = Convert.ToDecimal(settingEntryRow["fbizinventoryqty"]);
            demandInfo.fmtono = settingEntryRow["fmtono"]?.ToString();
            demandInfo.fqty = Convert.ToDecimal(settingEntryRow["fqty"]);
            demandInfo.fcanreserveqty = Convert.ToDecimal(settingEntryRow["fcanreserveqty"]);
            demandInfo.finventoryqty = Convert.ToDecimal(settingEntryRow["finventoryqty"]);
            demandInfo.fplanqty = Convert.ToDecimal(settingEntryRow["fplanqty"]);
            demandInfo.fdemandentryid = settingEntryRow["fdemandentryid"]?.ToString();
            demandInfo.freserveentryid = settingEntryRow["freserveentryid"]?.ToString();
            demandInfo.fclosestatus = settingEntryRow["fclosestatus"]?.ToString();

            return demandInfo;
        }

        private static ReserveSettingTraceInfo Convert2SettingTranceInfo(DynamicObject reserveEntry, DynamicObject tranceRow)
        {
            var tranceInfo = new ReserveSettingTraceInfo();

            tranceInfo.fstorehouseid = Convert.ToString(tranceRow["fstorehouseid"]);
            tranceInfo.fstockstatus = Convert.ToString(tranceRow["fstockstatus"]);

            tranceInfo.fbizqty_d = Convert.ToDecimal(tranceRow["fbizqty_d"]);
            tranceInfo.fqty_d = Convert.ToDecimal(tranceRow["fqty_d"]);

            tranceInfo.freservedateto_d = Convert.ToDateTime(tranceRow["freservedateto_d"]);
            tranceInfo.fdirection_d = Convert.ToString(tranceRow["fdirection_d"]);
            tranceInfo.fopdesc = Convert.ToString(tranceRow["fopdesc"]);
            tranceInfo.freservenote = Convert.ToString(tranceRow["freservenote"]);
            tranceInfo.foptime = Convert.ToDateTime(tranceRow["foptime"]);
            tranceInfo.fopuserid = Convert.ToString(tranceRow["fopuserid"]);
            tranceInfo.ffromformid = Convert.ToString(tranceRow["ffromformid"]);
            tranceInfo.ffrombillno = Convert.ToString(tranceRow["ffrombillno"]);
            tranceInfo.ffrombillpkid = Convert.ToString(tranceRow["ffrombillpkid"]);
            tranceInfo.freservetrancepkid = Convert.ToString(tranceRow["freservetrancepkid"]);

            tranceInfo.fmaterialid_d = Convert.ToString(tranceRow["fmaterialid_d"]);
            tranceInfo.fbizunitid_d = Convert.ToString(tranceRow["fbizunitid_d"]);
            tranceInfo.funitid_d = Convert.ToString(tranceRow["funitid_d"]);
            tranceInfo.Id = Convert.ToString(tranceRow["Id"]);
            tranceInfo.fclosestatus_d = Convert.ToString(tranceRow["fclosestatus_d"]);

            if (tranceInfo.fmaterialid_d.IsNullOrEmptyOrWhiteSpace())
            {
                tranceInfo.fmaterialid_d = Convert.ToString(reserveEntry["fmaterialid"]);
            }
            if (tranceInfo.funitid_d.IsNullOrEmptyOrWhiteSpace())
            {
                tranceInfo.funitid_d = Convert.ToString(reserveEntry["funitid"]);
            }
            if (tranceInfo.fbizunitid_d.IsNullOrEmptyOrWhiteSpace())
            {
                tranceInfo.fbizunitid_d = Convert.ToString(reserveEntry["fbizunitid"]);
            }

            return tranceInfo;
        }


        /// <summary>
        /// 按仓库、库存状态找到
        /// </summary>
        /// <param name="entrys"></param>
        /// <param name="tranceRow">预留跟踪记录</param> 
        /// <returns></returns>
        private static IEnumerable<DynamicObject> FindDataByStockAndStatus(List<DynamicObject> entrys, ReserveSettingTraceInfo tranceRow)
        {
            var stockId = tranceRow.fstorehouseid;
            var stockStatus = tranceRow.fstockstatus;

            //库存状态要么相等，要么预留跟踪记录中的库存状态为空
            var temps = entrys?.Where(o => stockStatus.IsNullOrEmptyOrWhiteSpace() || Convert.ToString(o["fstockstatus"]).EqualsIgnoreCase(stockStatus));

            //仓库要么相等，要么需求明细中的仓库为空
            temps = temps?.Where(o => stockId.IsNullOrEmptyOrWhiteSpace() || Convert.ToString(o["fstorehouseid"]).EqualsIgnoreCase(stockId));

            return temps;
        }



        /// <summary>
        /// 按库存维度找到库存数据（不包括仓库、库位）
        /// </summary>
        /// <param name="entrys"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="qtyFieldKey"></param>
        /// <returns></returns>
        private static List<DynamicObject> FindInvDataByInvFlexFieldKey(UserContext ctx, ReserveSettingInfo reserveSettingInfo, IEnumerable<DynamicObject> invDatas, ReserveSettingDemandInfo settingEntryRow)
        {
            var temps = FindDataByInvFlexFieldKey(ctx, invDatas, settingEntryRow);

            if (reserveSettingInfo.AllowsalesControl && !settingEntryRow.fstaffid.IsNullOrEmptyOrWhiteSpace()
                && (reserveSettingInfo.fdemandformid.EqualsIgnoreCase("ydj_order") || reserveSettingInfo.fdemandformid.EqualsIgnoreCase("stk_sostockout")))
            {
                //如果是订单，按销售员可销售仓库控制
                var svc = ctx.Container.GetService<ISalesControl>();
                var ctrlDatas = svc.GetCanSalHousePerm(ctx);
                var currCtrlStkDatas = ctrlDatas?.Where(f => f.Item3.SplitKey().Contains(reserveSettingInfo.fdemandformid, StringComparer.OrdinalIgnoreCase))?.ToList(); //当前单据设置的可用仓控制信息
                if (currCtrlStkDatas == null || currCtrlStkDatas.Count == 0)
                {
                    return temps;
                }

                var stocks = currCtrlStkDatas.Where(f => f.Item1 == settingEntryRow.fstaffid).Select(f => f.Item2).ToList();
                temps = temps.Where(o =>
                {
                    var tempValue = o["fstorehouseid"]?.ToString();
                    if (tempValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        tempValue = " ";
                    }
                    return stocks.Contains(tempValue);
                }).ToList();
            }

            return temps;
        }


        /// <summary>
        /// 按库存维度找到（不包括仓库、库位）
        /// </summary>
        /// <param name="entrys"></param>
        /// <param name="settingEntryRow"></param>
        /// <param name="qtyFieldKey"></param>
        /// <returns></returns>
        private static List<DynamicObject> FindDataByInvFlexFieldKey(UserContext ctx, IEnumerable<DynamicObject> entrys, ReserveSettingDemandInfo settingEntryRow)
        {
            var temps = entrys.Select(o => o).ToList(); 
            foreach (var fieldKey in InvFlexFieldKeys)
            {
                var value = GetFieldValue(settingEntryRow, fieldKey);
                temps = temps.Where(o =>
                {
                    var tempValue = o[fieldKey]?.ToString();
                    if (tempValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        tempValue = " ";
                    }
                    if (fieldKey.EqualsIgnoreCase("fmtono"))
                    {
                        //物流跟踪号要么相等，要么库存表中的物流跟踪号为空
                        return tempValue.EqualsIgnoreCase(value) || tempValue.IsNullOrEmptyOrWhiteSpace();
                    }
                    else
                    {
                        return tempValue.EqualsIgnoreCase(value);
                    }

                }).ToList();
            }
            return temps;
        }

        private static string GetFieldValue(ReserveSettingDemandInfo settingEntryRow, string fldKey)
        {
            string fldVal = null;
            switch (fldKey)
            {
                case "fmaterialid":
                    fldVal = settingEntryRow.fmaterialid;
                    break;
                case "fattrinfo":
                    fldVal = settingEntryRow.fattrinfo;
                    break;
                case "fattrinfo_e":
                    fldVal = settingEntryRow.fattrinfo_e;
                    break;
                case "fcustomdesc":
                    fldVal = settingEntryRow.fcustomdesc;
                    break;
                case "funitid":
                    fldVal = settingEntryRow.funitid;
                    break;
                case "fbizunitid":
                    fldVal = settingEntryRow.fbizunitid;
                    break;
                case "fmtono":
                    fldVal = settingEntryRow.fmtono;
                    break;
                case "fresultbrandid":
                    fldVal = settingEntryRow.fresultbrandid;
                    break;
            }

            return fldVal.IsNullOrEmptyOrWhiteSpace() ? " " : fldVal;
        }




        /// <summary>
        /// 根据需求明细获取所有的预留数据
        /// </summary>
        /// <param name="dataEntity">预留设置--需求明细</param> 
        /// <returns></returns>
        private static DynamicObjectCollection GetReserveEntrys(UserContext ctx, ReserveSettingInfo reserveSettingData)
        {
            if (reserveSettingData.DemandEntry == null || !reserveSettingData.DemandEntry.Any())
            {
                return null;
            }

            List<SqlParam> lstSqlParas = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, ctx.Company)
            };

            var index = 0;
            List<string> lstSqls = new List<string>();
            foreach (var reserveObj in reserveSettingData.DemandEntry)
            {
                var filter = new List<string>();
                foreach (var invFldKey in InvFlexFieldKeys)
                { 
                    var invFldVal = GetFieldValue(reserveObj, invFldKey);
                    filter.Add($"{invFldKey}=@{invFldKey}{index}");
                    lstSqlParas.Add(new SqlParam($"@{invFldKey}{index}", System.Data.DbType.String, invFldVal));
                }

                lstSqls.Add($"{GetReserveQtySql(ctx)} and {string.Join(" and ", filter)}");

                index++;
            }

            var dbSvc = ctx.Container.GetService<IDBService>();
            var reserveEntrys = dbSvc.ExecuteDynamicObjectByUnion(ctx, lstSqls, lstSqlParas);
            return reserveEntrys;
        }

        /// <summary>
        /// 获取预留量脚本
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static string GetReserveQtySql(UserContext userCtx)
        {
            const string sql =
                 @"
select re.fentryid,d.fdetailid, re.fsourceformid,r.fsourcenumber,re.fsourceinterid,re.fsourceentryid ,re.fmaterialid
    ,re.fattrinfo_e,re.fcustomdesc,re.funitid,re.fresultbrandid,re.fmtono,d.fstorehouseid,d.fstockstatus,
    case d.fdirection_d when '0' then d.fqty_d when '1' then d.fqty_d*-1 else 0 end as fqty ,
    case d.fdirection_d when '0' then d.fbizqty_d when '1' then d.fbizqty_d*-1 else 0 end as fbizqty 
from t_stk_reservebill r  
inner join t_stk_reservebillentry re on re.fid=r.fid
inner join t_stk_reservebilldetail d on re.fentryid=d.fentryid 
where r.fmainorgid=@fmainorgid and r.fstatus='E' and r.fcancelstatus='0' and re.fqty>0";

            return sql;
        }


        /// <summary>
        /// 根据需求明细获取所有的即时库存数据
        /// </summary>
        /// <param name="demandEntrys">需求明细数据</param>
        /// <returns></returns>
        private static DynamicObjectCollection MatchInventoryList(UserContext ctx, ReserveSettingInfo reserveSettingData)
        {
            if (reserveSettingData.DemandEntry == null || !reserveSettingData.DemandEntry.Any())
            {
                return null;
            }

            List<SqlParam> lstSqlParas = new List<SqlParam>();
            lstSqlParas.Add(new SqlParam($"@fmainorgid", System.Data.DbType.String, ctx.Company));

            var index = 0;
            List<string> lstSqls = new List<string>();
            foreach (var reserveObj in reserveSettingData.DemandEntry)
            {
                var filter = new List<string>();
                var fields = new List<string>();
                foreach (var invFldKey in InvFlexFieldKeys)
                { 
                    var invFldVal = GetFieldValue(reserveObj, invFldKey);
                    if (invFldKey.EqualsIgnoreCase("fmtono"))
                    {
                        //物流跟踪号要么相等，要么库存表中的物流跟踪号为空
                        filter.Add($"({invFldKey}=@{invFldKey}{index} or {invFldKey}='')");
                    }
                    else
                    {
                        filter.Add($"t0.{invFldKey}=@{invFldKey}{index}");
                    }
                    lstSqlParas.Add(new SqlParam($"@{invFldKey}{index}", System.Data.DbType.String, invFldVal));
                } 
                lstSqls.Add(@"select fid,{0},fstorehouseid,fstockstatus,fqty 
                                from t_stk_inventorylist as t0 
                                where t0.fmainorgid=@fmainorgid and fqty>0 and {1}
                              ".Fmt(string.Join(",", InvFlexFieldKeys), string.Join(" and ", filter)));
                index++;
            }

            var dbSvc = ctx.Container.GetService<IDBService>();
            var inventoryList = dbSvc.ExecuteDynamicObjectByUnion(ctx, lstSqls, lstSqlParas);
            return inventoryList;
        }





        /// <summary>
        /// 获取对应的预留单信息
        /// </summary>
        /// <param name="demandBillPKId">对应需求单id</param>
        /// <param name="demandForm">对应需求单模型标识</param>
        /// <returns></returns>
        public static DynamicObject GetReserveBillData(UserContext ctx, string demandForm, string demandBillPKId)
        {
            //预留单
            var reserveBillForm = ctx.Container.GetService<IMetaModelService>().LoadFormModel(ctx, "stk_reservebill");
            var reserveDt = reserveBillForm.GetDynamicObjectType(ctx);
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, reserveDt);
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, ctx.Company),
                new SqlParam("@fsourcetype", System.Data.DbType.String, demandForm),
                new SqlParam("@fsourcepkid", System.Data.DbType.String, demandBillPKId),
            };
            var sqlWhere = "fmainorgid=@fmainorgid and fsourcetype=@fsourcetype and fsourcepkid=@fsourcepkid";
            var dataReader = ctx.GetPkIdDataReader(reserveBillForm, sqlWhere, sqlParam);
            var reserveBill = dm.SelectBy(dataReader)?.OfType<DynamicObject>()?.FirstOrDefault();

            return reserveBill;
        }




        /// <summary>
        /// 获取对应的预留单信息
        /// </summary>
        /// <param name="demandForm">对应需求单模型</param>
        /// <param name="demandBills">对应需求单</param>
        /// <returns></returns>
        public static List<DynamicObject> GetReserveBillData(UserContext ctx, HtmlForm demandForm, IEnumerable<DynamicObject> demandBills)
        {
            var demandBillIds = demandBills.Select(s => s["id"].ToString());

            return GetReserveBillData(ctx, demandForm, demandBillIds);
        }




        /// <summary>
        /// 获取对应的预留单信息
        /// </summary>
        /// <param name="demandForm">对应需求单模型</param>
        /// <param name="demandBillIds">对应需求单内码</param>
        /// <returns></returns>
        public static List<DynamicObject> GetReserveBillData(UserContext ctx, HtmlForm demandForm, IEnumerable<string> demandBillIds)
        {
            if (demandBillIds.IsNullOrEmpty()) return new List<DynamicObject>();

            //预留单
            var reserveBillForm = ctx.Container.GetService<IMetaModelService>().LoadFormModel(ctx, "stk_reservebill");
            var reserveDt = reserveBillForm.GetDynamicObjectType(ctx);
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, reserveDt);
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, ctx.Company),
                new SqlParam("@fsourcetype", System.Data.DbType.String, demandForm.Id),
            };
            var sqlWhere = "fmainorgid=@fmainorgid and fsourcetype=@fsourcetype and fsourcepkid in ({0}) ".Fmt(demandBillIds.JoinEx(",", true));
            var dataReader = ctx.GetPkIdDataReader(reserveBillForm, sqlWhere, sqlParam);
            var reserveBill = dm.SelectBy(dataReader)?.OfType<DynamicObject>()?.ToList();

            return reserveBill;
        }

        /// <summary>
        /// 获取业务单据关联的预留单信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm">业务单据标识</param> 
        ///  <param name="demandBillDatas">业务单据</param> 
        /// <returns>key--业务单据id，value -- 预留单id</returns>
        public static Dictionary<string, string> GetReserveBillIDS(UserContext userCtx, string demandForm, IEnumerable<DynamicObject> demandBillDatas)
        {
            UpdateDBSchema(userCtx);

            var result = new Dictionary<string, string>();
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fsourcetype", System.Data.DbType.String, demandForm),
            };

            var ids = from p in demandBillDatas
                      select p["Id"].ToString();
            var sqlWhere = "select fid,fsourcepkid from t_stk_reservebill where fmainorgid=@fmainorgid and fsourcetype=@fsourcetype and fsourcepkid in ({0}) ".Fmt(ids.JoinEx(",", true));

            var svc = userCtx.Container.GetService<IDBService>();
            var datas = svc.ExecuteDynamicObject(userCtx, sqlWhere, sqlParam);
            if (datas != null)
            {
                foreach (var item in datas)
                {
                    result[item["fsourcepkid"].ToString()] = item["fid"].ToString();
                }
            }

            return result;
        }


        /// <summary>
        /// 根据预留单获取需求单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm"></param>
        /// <param name="reserveBills"></param>
        /// <returns></returns>
        public static List<DynamicObject> GetDemandBills(UserContext userCtx, HtmlForm demandForm,
            IEnumerable<DynamicObject> reserveBills)
        {
            var demandBillIds = reserveBills
                .Where(s => Convert.ToString(s["fsourcetype"]).EqualsIgnoreCase(demandForm.Id))
                .Select(s => Convert.ToString(s["fsourcepkid"])).ToList();

            return userCtx.LoadBizDataById(demandForm.Id, demandBillIds);
        }



        private static void SetDemandBillCancelStatus(ReserveSettingInfo reserveSettingData, HtmlForm demandForm, DynamicObject demandBill)
        {
            var formId = demandForm.Id.ToLowerInvariant();
            switch (formId)
            {
                case "ydj_saleintention":
                    if (demandBill["fpickdate"] != null)
                    {
                        reserveSettingData.fcancelstatus = Convert.ToBoolean(demandBill["fcancelstatus"]);
                    }
                    break;
                case "ydj_order":
                    if (demandBill["fdeliverydate"] != null)
                    {
                        reserveSettingData.fcancelstatus = Convert.ToBoolean(demandBill["fcancelstatus"]);
                    }
                    break;
                case "stk_sostockout":
                    if (demandBill["fdate"] != null)
                    {
                        reserveSettingData.fcancelstatus = Convert.ToBoolean(demandBill["fcancelstatus"]);
                    }
                    break;
                default:
                    break;
            }
        }


        /// <summary>
        /// 设置默认的预留日期至
        /// </summary>
        /// <param name="reserveSettingData"></param>
        /// <param name="demandForm"></param>
        /// <param name="demandBill"></param>
        private static void SetDefaultReserveDate(UserContext ctx, ReserveSettingInfo reserveSettingData, HtmlForm demandForm, DynamicObject demandBill)
        {
            reserveSettingData.fdefautdate =
                GetDefaultReserveDate(ctx, demandForm, demandBill, reserveSettingData.freserveday);
        }


        /// <summary>
        /// 获取默认的预留日期至
        /// </summary>
        /// <param name="demandForm"></param>
        /// <param name="demandBill"></param>
        /// <param name="reserveDay"></param>
        public static DateTime GetDefaultReserveDate(UserContext ctx, HtmlForm demandForm, DynamicObject demandBill, int reserveDay)
        {
            var formId = demandForm.Id.ToLowerInvariant();
            DateTime defautDate = DateTime.MinValue;

            switch (formId)
            {
                case "ydj_saleintention":
                    if (reserveDay > 0)
                    {
                        defautDate = DateTime.Now.AddDays(reserveDay);
                    }
                    else if (demandBill["fpickdate"] != null)
                    {
                        defautDate = Convert.ToDateTime(demandBill["fpickdate"]);
                    }
                    break;
                case "ydj_order":
                    if (demandBill["fdeliverydate"] != null)
                    {
                        defautDate = Convert.ToDateTime(demandBill["fdeliverydate"]).AddDays(reserveDay);
                    }
                    else
                    {
                        defautDate = DateTime.Now.AddDays(reserveDay);
                    }
                    break;
                case "stk_sostockout":
                    var dt = GetSrcBillDate(ctx, demandBill);
                    var dt2 = DateTime.Now;
                    if (demandBill["fdate"] != null)
                    {
                        dt2 = Convert.ToDateTime(demandBill["fdate"]);
                    }
                    if (dt > dt2)
                    {
                        defautDate = dt.AddDays(reserveDay);
                    }
                    else
                    {
                        defautDate = dt2.AddDays(reserveDay);
                    }
                    break;
                // 其它出库单、库存调拨单、采购退货单：【预留日期至】=【业务日期】
                case "stk_otherstockout":
                case "stk_inventorytransfer":
                case "stk_postockreturn":
                    defautDate = demandBill["fdate"] != null ? Convert.ToDateTime(demandBill["fdate"]) : DateTime.Today;
                    break;
                default:
                    break;
            }

            return defautDate;
        }


        private static DateTime GetSrcBillDate(UserContext ctx, DynamicObject demandBill)
        {
            DateTime dt = DateTime.Now;
            var fsourceformid = demandBill["fsourcetype"].ToString();
            if (fsourceformid.EqualsIgnoreCase("ydj_order"))
            {
                //获取上游单据信息
                var sql = @"
select a.fbillno,a.fdeliverydate ,c.fsourcebillno  
from t_ydj_order a 
inner join T_YDJ_ORDERENTRY  b on a.fid=b.fid 
inner join  T_STK_SOSTOCKOUTENTRY c on b.fentryid =c.fsourceentryid 
    and a.fbillno =c.fsourcebillno
    and c.fid='{0}' ".Fmt(demandBill["Id"]);
                var preBillDatas = ctx.Container.GetService<IDBService>().ExecuteDynamicObject(ctx, sql).FirstOrDefault();
                if (preBillDatas != null)
                {
                    dt = Convert.ToDateTime(preBillDatas["fdeliverydate"]);
                }
            }

            return dt;
        }


        /// <summary>
        /// 更新表结构
        /// </summary>
        private static void UpdateDBSchema(UserContext ctx)
        {
            if (!IsUpdateDB)
            {
                IsUpdateDB = false;
                return;
            }
            var svc = ctx.Container.GetService<IMetaModelService>();
            var inventoryForm = svc.LoadFormModel(ctx, "stk_inventorylist");
            var dm = ctx.Container.GetService<IDataManager>();
            dm.InitDbContext(ctx, inventoryForm.GetDynamicObjectType(ctx));

            var reserveForm = svc.LoadFormModel(ctx, "stk_reservebill");
            dm.InitDbContext(ctx, reserveForm.GetDynamicObjectType(ctx));
        }



        /// <summary>
        /// 获取库存状态的映射关系
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public static Dictionary<string, string> GetStockStatusMpa(UserContext ctx)
        {
            var result = new Dictionary<string, string>();
            var sql = @"  select fid,fprimitiveid  from t_ydj_stockstatus ";

            var dbSvc = ctx.Container.GetService<IDBService>();
            var datas = dbSvc.ExecuteDynamicObject(ctx, sql)?.ToList();
            if (datas != null)
            {
                foreach (var item in datas)
                {
                    result.Add(Convert.ToString(item["fid"]), Convert.ToString(item["fprimitiveid"]));
                }
            }

            return result;
        }

    }

    /// <summary>
    /// 销售合同数量
    /// </summary>
    internal class OrderQty
    {
        /// <summary>
        /// 出库数量
        /// </summary>
        public decimal OutQty { get; set; }

        /// <summary>
        /// 调拨数量
        /// </summary>
        public decimal TransferQty { get; set; }
    }

}
