using JieNor.AMS.YDJ.Core.Utils;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 预留设置或预留更新
    /// </summary>   
    public partial class ReserveService
    {



        /// <summary>
        /// 预留设置或预留更新
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option">参数选项，updateReserve等于true表示预留更新</param>
        /// <returns></returns>
        public IOperationResult SetOrUpdateReserve(UserContext ctx, HtmlForm demandForm, IEnumerable<DynamicObject> billDatas, OperateOption option)
        {
            Initialize(ctx);
            IOperationResult result = new OperationResult();
            //if (demandForm.Id.EqualsIgnoreCase("stk_inventorytransfer"))
            //{
            //    result = TransferBillReserve(ctx, demandForm, billDatas, option);
            //    result.IsSuccess = true;

            //    //库存调拨单调出需要预留
            //    //return result;
            //}

            if (demandForm.Id.EqualsIgnoreCase("stk_postockin"))
            {
                result = POStockInReserve(ctx, demandForm, billDatas, option);
                result.IsSuccess = true;

                return result;
            }

            var reserveSettingInfos = ReserveUtil.GetReserveSettingInfo(ctx, result, demandForm, billDatas, option);

            ReserveUtil.DebugInfo(ctx, reserveSettingInfos);

            var resultX = SetOrUpdateReserve(ctx, demandForm, reserveSettingInfos, option);

            result.MergeResult(resultX);

            result.IsSuccess = true;

            var xx = new List<string>(result.ComplexMessage.WarningMessages).Distinct().ToList();
            result.ComplexMessage.WarningMessages.Clear();
            result.ComplexMessage.WarningMessages.AddRange(xx);

            xx = new List<string>(result.ComplexMessage.ErrorMessages).Distinct().ToList();
            result.ComplexMessage.ErrorMessages.Clear();
            result.ComplexMessage.ErrorMessages.AddRange(xx);

            return result;
        }



        /// <summary>
        /// 更新销售合同对应的预留单的预留日期
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billForm">销售合同模型</param>
        /// <param name="demandBillDatas">销售合同数据包</param>
        /// <param name="option"></param>
        /// <returns></returns>
        public void  UpdateReserveDate(UserContext ctx, HtmlForm billForm, IEnumerable<DynamicObject> demandBillDatas, OperateOption option)
        {
            Initialize(ctx);
             
            if (!billForm.Id.EqualsIgnoreCase("ydj_order"))
            {  
                return  ;
            }

            //预留天数
            var profileService = ctx.Container.GetService<ISystemProfile>();             
            var reserveDay = profileService.GetSystemParameter(ctx, "stk_stockparam", "freserveday", 30);

            var beSave = new List<DynamicObject>();
            var allReserveBills = ReserveUtil.GetReserveBillData(ctx, billForm, demandBillDatas.ToList());
            foreach (var demandBill in demandBillDatas)
            {
                var id = demandBill["Id"]?.ToString();
                var reserveBill = allReserveBills.FirstOrDefault(f => id.EqualsIgnoreCase(f["fsourcepkid"]?.ToString()));
                if (reserveBill == null)
                {
                    continue;
                }

                //预留单--需求明细
                var reserveEntrys = reserveBill["fentity"] as DynamicObjectCollection;
                if (reserveEntrys == null || reserveEntrys.Count == 0)
                {
                    continue;
                }

                beSave.Add(reserveBill);

                DateTime reserveDate = GetReserveDate(reserveDay, demandBill);

                //更新预留日期
                foreach (var reserveEnRow in reserveEntrys)
                {
                    var qty = Convert.ToDecimal(reserveEnRow["fbizqty"]);
                    var reserveTranceRows = reserveBill["fdetail"] as DynamicObjectCollection;
                    if (qty <= 0 || reserveTranceRows == null || reserveTranceRows.Count == 0)
                    {
                        continue;
                    }

                    reserveEnRow["freservedateto"] = reserveDate;
                    foreach (var item in reserveTranceRows)
                    {
                        if (item["freservedateto_d"].IsNullOrEmptyOrWhiteSpace())
                        {
                            item["freservedateto_d"] = reserveDate;//以新的业务日期为准
                        }
                        else
                        {
                            var dateX = Convert.ToDateTime(item["freservedateto_d"]);
                            if (dateX < reserveDate)
                            {
                                item["freservedateto_d"] = reserveDate;//以新的业务日期为准
                            }
                        }
                    }
                }
            }

            if (beSave.Count > 0)
            {
                ctx.SaveBizData("stk_reservebill", beSave);
            }
        }

        /// <summary>
        /// 获取预留日期
        /// </summary>
        /// <param name="reserveDay"></param>
        /// <param name="demandBill"></param>
        /// <returns></returns>
        private static DateTime GetReserveDate(int reserveDay, DynamicObject demandBill)
        {
            DateTime reserveDate = DateTime.MinValue;
            if (demandBill["fdeliverydate"] != null)
            {
                reserveDate = Convert.ToDateTime(demandBill["fdeliverydate"]).AddDays(reserveDay);
            }
            else
            {
                reserveDate = DateTime.Now.AddDays(reserveDay);
            }

            return reserveDate;
        }





        /// <summary>
        /// 预留设置或预留更新
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm"></param>
        /// <param name="option"></param>
        /// <param name="reserveSettingInfos"></param>
        public IOperationResult SetOrUpdateReserve(UserContext ctx, HtmlForm demandForm, List<ReserveSettingInfo> reserveSettingInfos, OperateOption option)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            if (reserveSettingInfos == null || reserveSettingInfos.Count <= 0)
            {
                return result;
            }

            Initialize(ctx);

            //预留单数据包
            var reserveBills = new List<DynamicObject>();

            //预留对应的需求单数据包
            var demandBillDatas = new List<DynamicObject>();

            //源单数据包
            foreach (var reserveSettingInfo in reserveSettingInfos)
            {
                var demandBillData = reserveSettingInfo.DemandBillData as DynamicObject;
                if (demandBillData == null)
                {
                    if (!reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty())
                    {
                        DebugUtil.WriteLogToFile($"fdemandbillno={reserveSettingInfo.fdemandbillno} DemandBillData is null", "采购入库自动预留");
                    }
                    continue;
                }
                if (demandForm.Id.EqualsIgnoreCase("stk_sostockout") && "E".EqualsIgnoreCase(Convert.ToString(demandBillData["fstatus"])))
                {
                    //出库单已经审核的，不需要更新
                    continue;
                }

                //单据已经作废，不需要再更新预留
                if (reserveSettingInfo.fcancelstatus)
                {
                    if (!reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty())
                    {
                        DebugUtil.WriteLogToFile($"fdemandbillno={reserveSettingInfo.fdemandbillno} fcancelstatus={reserveSettingInfo.fcancelstatus}", "采购入库自动预留");
                    }
                    continue;
                }

                AddOrUpdateReserveBill(ctx, result, demandForm, demandBillData, demandBillDatas, reserveBills, reserveSettingInfo, option);
            }

            //更新源单信息
            this.UpdateSourceOrder(ctx, demandForm, demandBillDatas);

            //保存预留单  
            var para = new Dictionary<string, object>();
            para.Add("IgnoreCheckPermssion", true);
            para.Add("TopOrperationNo", "ExcelImport");
            if (reserveBills.Any())
            {
                //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
                var unitService = ctx.Container.GetService<IUnitConvertService>();
                unitService.ConvertByBasQty(ctx, this.ReserveBillFormMeta, reserveBills, option);

                var msg = reserveBills[0].DataEntityState.FromDatabase ? "预留单更新失败！" : "预留单生成失败！";
                var invokeResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(
                    ctx,
                    "stk_reservebill",
                    reserveBills,
                    "draft", para
                    );
                invokeResult?.ThrowIfHasError(true, msg);

                ReserveUtil.UpdateOrderReserveQty(ctx, reserveBills);

                var transferResult = TransferReserve(ctx, demandForm, reserveSettingInfos, option);
                result.MergeResult(transferResult);

                // 如果预留需求明细为空，删除预留单
                var emptyReserveBills = reserveBills
                    .Where(s => (s["fentity"] as DynamicObjectCollection).IsNullOrEmpty())?.ToList();
                if (!emptyReserveBills.IsNullOrEmpty())
                {
                    this.DeleteReserveBill(ctx, emptyReserveBills);
                }
            }

            result.IsSuccess = true;

            return result;
        }



        /// <summary>
        /// 预留设置或预留更新
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="demandForm"></param>
        /// <param name="demandBillData"></param>
        /// <param name="demandBillDatas"></param>
        /// <param name="reserveBills"></param>
        /// <param name="reserveSettingInfo"></param>
        private void AddOrUpdateReserveBill(UserContext ctx, IOperationResult result, HtmlForm demandForm,
            DynamicObject demandBillData, List<DynamicObject> demandBillDatas,
            List<DynamicObject> reserveBills, ReserveSettingInfo reserveSettingInfo, OperateOption option)
        {
            demandBillDatas.Add(demandBillData);

            DynamicObject reserveBill = GetOrAddReserveBill(ctx, reserveSettingInfo);

            //预留单--需求明细
            var reserveEntrys = reserveBill["fentity"] as DynamicObjectCollection;

            DeleteNoExistDemandRow(reserveSettingInfo, reserveEntrys);

            CheckDuplicateDemandEntry(reserveSettingInfo, reserveEntrys);

            var isUpdateDate = option.GetVariableValue("UpdateReserveDate", false);//是否更新预留日期
            foreach (var settingRow in reserveSettingInfo.DemandEntry)
            {
                var autoReserve = false;
                var isAuto = option.TryGetVariableValue("autoReserve", out autoReserve);
                if (isAuto && autoReserve)//自动预留功能，需要指定哪些明细行进行自动预留
                {
                    if (reserveSettingInfo.SelectEntryRow != null)
                    {
                        var beAuto = reserveSettingInfo.SelectEntryRow.Any(f => f.EqualsIgnoreCase(settingRow.fdemandentryid));
                        if (!beAuto)
                        {
                            if (!reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty())
                            {
                                DebugUtil.WriteLogToFile($"fentryid={settingRow.fdemandentryid} autoReserve=false", "采购入库自动预留");
                            }
                            continue;
                        }
                    }
                }

                if (settingRow.fclosestatus == "3" || settingRow.fclosestatus == "4" || settingRow.fclosestatus == $"{(int)CloseStatus.Whole}")
                {
                    if (!reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty())
                    {
                        DebugUtil.WriteLogToFile($"fentryid={settingRow.fdemandentryid} fclosestatus={settingRow.fclosestatus}", "采购入库自动预留");
                    }

                    // 如果有新增的预留需求跟踪明细行时，需要进行保存
                    if (!settingRow.TraceEntry.Any(s => s.freservetrancepkid.IsNullOrEmptyOrWhiteSpace()))
                    {
                        continue;
                    }
                }

                if (demandForm.Id.EqualsIgnoreCase("ydj_order") && settingRow.freservedateto < new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day))
                {
                    var msg = "销售合同{0}的【交货日期+预留天数】小于系统当前日期，无法进行预留！".Fmt(reserveSettingInfo.fdemandbillno);
                    if (!result.ComplexMessage.WarningMessages.Any(f => f.EqualsIgnoreCase(msg)))
                    {
                        result.ComplexMessage.WarningMessages.Add(msg);
                    }
                    if (!reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty())
                    {
                        DebugUtil.WriteLogToFile($"fentryid={settingRow.fdemandentryid} {msg}", "采购入库自动预留");
                    }
                    continue;
                }

                DynamicObject reserveEnRow = GetOrAddReserveDemandRow(reserveEntrys, settingRow);
                reserveEnRow["fsourceformid"] = demandForm.Id;
                reserveEnRow["fsourceinterid"] = reserveSettingInfo.fdemandbillpkid;
                reserveEnRow["fsourcebillno"] = reserveSettingInfo.fdemandbillno;

                var reserveTranceRows = reserveEnRow["fdetail"] as DynamicObjectCollection;

                //更新预留日期
                var qty = Convert.ToDecimal(reserveEnRow["fbizqty"]);
                if (isUpdateDate && qty > 0 && reserveTranceRows != null && reserveTranceRows.Count > 0)
                {
                    foreach (var item in reserveTranceRows)
                    {
                        if (item["freservedateto_d"].IsNullOrEmptyOrWhiteSpace())
                        {
                            item["freservedateto_d"] = settingRow.freservedateto;//以新的业务日期为准
                        }
                        else
                        {
                            var dateX = Convert.ToDateTime(item["freservedateto_d"]);
                            if (dateX < settingRow.freservedateto)
                            {
                                item["freservedateto_d"] = settingRow.freservedateto;//以新的业务日期为准
                            }
                        }
                    }
                }

                //当预留设置中删除预留跟踪时，增加类型为预留减少的跟踪记录
                AddTranceWhenDeleteSetting(ctx, settingRow, reserveTranceRows);

                foreach (var settingTranceRow in settingRow.TraceEntry)
                {
                    if (settingTranceRow.fqty_d == 0)
                    {
                        if (!reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty() && settingTranceRow.ffromformid.EqualsIgnoreCase("stk_postockin"))
                        {
                            DebugUtil.WriteLogToFile($"fentryid={settingRow.fdemandentryid} fqty_d=0", "采购入库自动预留");
                        }
                        continue;
                    }

                    var tranceId = settingTranceRow.freservetrancepkid;
                    var reserveTranceRow = reserveTranceRows.FirstOrDefault(f => !tranceId.IsNullOrEmptyOrWhiteSpace() && tranceId.EqualsIgnoreCase(f["Id"]?.ToString()));
                    if (reserveTranceRow == null)
                    {
                        AddReserveTranceRow(ctx, reserveTranceRows, settingTranceRow);

                        if (!reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty() && settingTranceRow.ffromformid.EqualsIgnoreCase("stk_postockin"))
                        {
                            DebugUtil.WriteLogToFile($"fentryid={settingRow.fdemandentryid} 预留成功", "采购入库自动预留");
                        }
                    }
                    else
                    {
                        UpdateReserveTranceRow(ctx, reserveTranceRows, reserveTranceRow, settingTranceRow);
                    }
                }

                //重新计算需求明细行的总的预留量
                CalculateReserveQty(reserveEnRow, reserveTranceRows);

                CheckReserveQty(reserveEnRow, reserveTranceRows);

                //将本次预留的仓库、仓位字段值反写到原单明细上
                UpdateDemandBillStockInfo(ctx, demandForm, demandBillData, reserveEnRow, reserveTranceRows, reserveSettingInfo.SO_StkIn_Maps);

                var fisoutspot = false;
                if (demandForm.Id.EqualsIgnoreCase("ydj_order"))
                {
                    if (settingRow.DemandRowData == null)
                    {
                        settingRow.DemandRowData =
                            (demandBillData["fentry"] as DynamicObjectCollection).FirstOrDefault(s =>
                                Convert.ToString(s["id"]).EqualsIgnoreCase(settingRow.fdemandentryid));
                    }

                    fisoutspot = Convert.ToBoolean((settingRow.DemandRowData as DynamicObject)?["fisoutspot"]);
                }

                // 非采购入库预留 且 销售合同且出现货，预留量 小于 需求数量
                if (reserveSettingInfo.SO_StkIn_Maps.IsNullOrEmpty() && fisoutspot && settingRow.fqty < settingRow.RealDemandQty)
                {
                    var grp = settingRow.TraceEntry.GroupBy(s => s.fstorehouseid).Where(s => ReserveUtil.GetReserveTranceRowQty(s) > 0);

                    var demandRow = settingRow.DemandRowData as DynamicObject;

                    var fstorehouseids = grp.Select(s => s.Key).ToList();
                    // 有预留量时，汇总仓库
                    if (!fstorehouseids.Any())
                    {
                        string stockId = Convert.ToString(demandRow?["fstorehouseid"]);
                        fstorehouseids.Add(stockId);
                    }

                    var storehouseObjs = ctx.LoadBizBillHeadDataById("ydj_storehouse", fstorehouseids, "fname");

                    string productName = Convert.ToString((demandRow?["fproductid_ref"] as DynamicObject)?["fname"]);

                    result.ComplexMessage.WarningMessages.Add(
                        $"仓库【{storehouseObjs.Select(s => s["fname"]?.ToString()).JoinEx("，", false)}】商品【{productName}】预留数量={settingRow.fqty:F},剩余未预留量={(settingRow.RealDemandQty - settingRow.fqty):F}");
                }
            }

            //删除预留量为零的预留跟踪信息
            DeleteZeroDemandRow(reserveBill, reserveEntrys);

            reserveBills.Add(reserveBill);
        }

        /// <summary>
        /// 检查重复需求明细行
        /// </summary>
        /// <param name="reserveSettingInfo"></param>
        /// <param name="reserveEntrys"></param>
        private void CheckDuplicateDemandEntry(ReserveSettingInfo reserveSettingInfo, DynamicObjectCollection reserveEntrys)
        {
            foreach (var enRow in reserveEntrys)
            {
                var fsourceentryid = Convert.ToString(enRow["fsourceentryid"]);
                if (fsourceentryid.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                // 存在新增的需求明细行，但源单明细行已存在于预留单里，抛出错误
                var settingRow = reserveSettingInfo.DemandEntry.FirstOrDefault(f =>
                    f.freserveentryid.IsNullOrEmptyOrWhiteSpace()
                    && fsourceentryid.EqualsIgnoreCase(f.fdemandentryid));

                if (settingRow != null)
                {
                    throw new BusinessException("存在重复的预留需求明细行，请关闭重新进入操作！");
                }
            }
        }


        /// <summary>
        /// 删除预留量为零的预留跟踪信息
        /// </summary>
        /// <param name="reserveBill"></param>
        private void DeleteZeroDemandRow(DynamicObject reserveBill, DynamicObjectCollection reserveEntrys)
        {
            var beDelEns = new List<DynamicObject>();
            foreach (var reserveEnRow in reserveEntrys)
            {
                var beDelTrances = new List<DynamicObject>();
                var reserveTranceRows = reserveEnRow["fdetail"] as DynamicObjectCollection;
                foreach (var item in reserveTranceRows)
                {
                    if (Convert.ToDecimal(item["fqty_d"]) <= 0)
                    {
                        beDelTrances.Add(item);
                    }
                }

                foreach (var item in beDelTrances)
                {
                    reserveTranceRows.Remove(item);
                }

                if (reserveTranceRows.Count == 0)
                {
                    beDelEns.Add(reserveEnRow);
                }
            }

            foreach (var item in beDelEns)
            {
                reserveEntrys.Remove(item);
            }
        }

        private DynamicObject GetOrAddReserveBill(UserContext ctx, ReserveSettingInfo reserveSettingInfo)
        {
            DynamicObject reserveBill = reserveSettingInfo.ReserveBillData as DynamicObject;
            if (reserveBill == null)
            {
                var reserveDt = ReserveBillFormMeta.GetDynamicObjectType(ctx);

                reserveBill = new DynamicObject(reserveDt);
                reserveBill["freserveobjecttype"] = reserveSettingInfo.freserveobjecttype;
                reserveBill["freserveobjectid"] = reserveSettingInfo.freserveobjectid;
                reserveBill["fdescription"] = reserveSettingInfo.fdescription;
                reserveBill["fsourcetype"] = reserveSettingInfo.fdemandformid;
                reserveBill["fsourcenumber"] = reserveSettingInfo.fdemandbillno;
                reserveBill["fsourcepkid"] = reserveSettingInfo.fdemandbillpkid;
                reserveBill["fstatus"] = BillStatus.E.ToString();

                reserveBill["fstaffid"] = reserveSettingInfo.fstaffid;
                reserveBill["fdeptid"] = reserveSettingInfo.fdeptid;
                reserveBill["faddress"] = reserveSettingInfo.faddress;
                reserveBill["fmanualnumber"] = reserveSettingInfo.fmanualnumber;
                reserveBill["fsourcemark"] = reserveSettingInfo.fsourcemark;

                reserveSettingInfo.ReserveBillData = reserveBill;
            }

            reserveBill["fsourcestatus"] = reserveSettingInfo.fsourcestatus;

            return reserveBill;
        }



        /// <summary>
        /// 将本次预留的仓库、仓位字段值反写到原单明细上
        /// </summary>
        /// <param name="demandForm"></param>
        /// <param name="demandBill"></param>
        /// <param name="reserveEnRow"></param>
        /// <param name="reserveTranceRows"></param>
        private void UpdateDemandBillStockInfo(UserContext ctx, HtmlForm demandForm, DynamicObject demandBill, DynamicObject reserveEnRow, DynamicObjectCollection reserveTranceRows, List<SO_StkIn_Map> soStkInMaps = null)
        {
            // 库存单据不反写
            if (demandForm.Id.EqualsIgnoreCase("stk_sostockout") || demandForm.Id.EqualsIgnoreCase("stk_otherstockout") || demandForm.Id.EqualsIgnoreCase("stk_inventorytransfer"))
            {
                return;
            }
            var cvtRule = ReserveUtil.GetSettingMapInfo(ctx, demandForm.Id);
            var sourceEntity = demandForm.GetEntryEntity(cvtRule.ActiveEntityKey);

            var demandEntryId = Convert.ToString(reserveEnRow["fsourceentryid"]);
            var demandEntrys = sourceEntity.DynamicProperty?.GetValue<DynamicObjectCollection>(demandBill);
            var demandRow = demandEntrys?.FirstOrDefault(o => !demandEntryId.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(o["id"]).EqualsIgnoreCase(demandEntryId));
            if (demandRow == null)
            {
                return;
            }

            if (reserveTranceRows != null && reserveTranceRows.Count > 0)
            {
                var qty = Convert.ToDecimal(reserveEnRow["fqty"]);
                if (qty <= 0)
                {
                    return;
                }

                DynamicObject xx = null;
                // 按仓库仓位分组，取预留量为正数的，设置源单的仓库仓位
                var grps = reserveTranceRows.GroupBy(s => new
                {
                    fstorehouseid = Convert.ToString(s["fstorehouseid"]).Trim(),
                    fstockstatus = Convert.ToString(s["fstockstatus"]).Trim()
                }).Where(s => SumReserveQty(s).Item1 > 0);

                // 没有预留量为正数的，不反写仓库
                if (!grps.Any())
                {
                    return;
                }

                // 判断最新的是否存在采购入库单的，如果有，用采购入库单上的仓库仓位反写
                if (soStkInMaps != null && soStkInMaps.Any() && demandForm.Id.EqualsIgnoreCase("ydj_order"))
                {
                    var maps = soStkInMaps.Where(s => s.forderenid.EqualsIgnoreCase(demandEntryId));
                    var soStkEnIds = maps.Select(s => s.fstkinenid).Distinct().ToList();
                    var soStkStorehouseIds = maps.Select(s => s.fstorehouseid).Distinct().ToList();
                    if (soStkEnIds.Any()
                        && reserveTranceRows.Any(s => soStkEnIds.Contains(Convert.ToString(s["ffrombillpkid"])))
                        && grps.Any(s => soStkStorehouseIds.Contains(s.Key.fstorehouseid))
                    )
                    {
                        var map = maps.First();

                        // 判断商品是否非标或定制商品
                        var matObj = demandRow["fproductid_ref"] as DynamicObject;
                        if (matObj != null && (Convert.ToBoolean(matObj["fispresetprop"]) || Convert.ToBoolean(matObj["funstdtype"])))
                        {
                            demandRow["fstorehouseid"] = map.fstorehouseid;
                            demandRow["fstorelocationid"] = map.fstorelocationid;
                            return;
                        }
                    }
                }

                var fstorehouseid = Convert.ToString(demandRow["fstorehouseid"]);
                var reserveStorehouseIds = grps.Select(s => s.Key.fstorehouseid).Distinct();
                // 如果源单仓库为空，或预留里没有包含此仓库时，反写仓库，库存状态
                if (fstorehouseid.IsNullOrEmptyOrWhiteSpace() || !reserveStorehouseIds.Contains(fstorehouseid))
                {
                    demandRow["fstorehouseid"] = reserveStorehouseIds.First();
                    demandRow["fstorelocationid"] = " ";
                    var reserveStockstatus = grps.Select(s => s.Key.fstockstatus).Distinct();
                    demandRow["fstockstatus"] = reserveStockstatus.First();
                }
            }
        }



        /// <summary>
        /// 重新计算需求明细行的总的预留量
        /// </summary>
        /// <param name="reserveEnRow"></param>
        /// <param name="reserveTranceRows"></param>
        private void CalculateReserveQty(DynamicObject reserveEnRow, DynamicObjectCollection reserveTranceRows)
        {
            if (reserveTranceRows == null || reserveTranceRows.Count == 0)
            {
                return;
            }

            var reserveQty = SumReserveQty(reserveTranceRows);

            reserveEnRow["fqty"] = reserveQty.Item1;
            reserveEnRow["fbizqty"] = reserveQty.Item2;
        }


        /// <summary>
        /// 汇总跟踪明细的预留量
        /// </summary>
        /// <param name="tranceRows"></param>
        private Tuple<decimal, decimal> SumReserveQty(IEnumerable<DynamicObject> tranceRows)
        {
            if (tranceRows == null) return Tuple.Create(0M, 0M);

            var qty = tranceRows.Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                          .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                      - tranceRows.Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                          .Sum(f => Convert.ToDecimal(f["fqty_d"]));

            var bizQty = tranceRows.Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                             .Sum(f => Convert.ToDecimal(f["fbizqty_d"]))
                         - tranceRows.Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                             .Sum(f => Convert.ToDecimal(f["fbizqty_d"]));

            return Tuple.Create(qty, bizQty);
        }

        /// <summary>
        /// 检查预留数量，避免出现负数
        /// </summary>
        /// <param name="reserveEnRow"></param>
        /// <param name="reserveTranceRows"></param>
        private void CheckReserveQty(DynamicObject reserveEnRow, DynamicObjectCollection reserveTranceRows)
        {
            // 避免出现错误数据
            var reserveQty = Convert.ToDecimal(reserveEnRow["fqty"]);
            if (reserveQty < 0)
            {
                throw new BusinessException("操作失败：出现负预留量！");
            }

            foreach (var grp in reserveTranceRows.GroupBy(f => new
            {
                fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
            }))
            {
                if (SumReserveQty(grp).Item1 < 0)
                {
                    throw new BusinessException("操作失败：出现负预留量！");
                }
            }
        }

        /// <summary>
        /// 获取对应的预留单--需求明细行
        /// </summary>
        /// <param name="reserveEntrys"></param>
        /// <param name="settingRow"></param>
        /// <returns></returns>
        private static DynamicObject GetOrAddReserveDemandRow(DynamicObjectCollection reserveEntrys, ReserveSettingDemandInfo settingRow)
        {
            var freserveentryid = settingRow.freserveentryid;
            var reserveEnRow = reserveEntrys.FirstOrDefault(f => !freserveentryid.IsNullOrEmptyOrWhiteSpace() && freserveentryid.EqualsIgnoreCase(f["Id"]?.ToString()));
            if (reserveEnRow == null)
            {
                reserveEnRow = reserveEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                reserveEnRow["fmaterialid"] = settingRow.fmaterialid;
                reserveEnRow["fattrinfo"] = settingRow.fattrinfo;
                reserveEnRow["fattrinfo_e"] = settingRow.fattrinfo_e;
                reserveEnRow["fcustomdesc"] = settingRow.fcustomdesc;
                reserveEnRow["fresultbrandid"] = settingRow.fresultbrandid;
                reserveEnRow["funitid"] = settingRow.funitid;
                reserveEnRow["fbizunitid"] = settingRow.fbizunitid;
                reserveEnRow["fqty"] = settingRow.fqty;
                reserveEnRow["fbizqty"] = settingRow.fbizqty;

                reserveEnRow["fmtono"] = settingRow.fmtono;

                reserveEnRow["fsourceentryid"] = settingRow.fdemandentryid;

                reserveEntrys.Add(reserveEnRow);
            }

            reserveEnRow["freservedateto"] = settingRow.freservedateto;//以新的业务日期为准

            return reserveEnRow;
        }


        /// <summary>
        /// 修改预留数量
        /// </summary>
        /// <param name="reserveTranceRows"></param>
        /// <param name="reserveTranceRow"></param>
        /// <param name="settingTranceRow"></param>
        private void UpdateReserveTranceRow(UserContext ctx, DynamicObjectCollection reserveTranceRows, DynamicObject reserveTranceRow, ReserveSettingTraceInfo settingTranceRow)
        {
            if (reserveTranceRow["freservedateto_d"] == null)
            {
                reserveTranceRow["freservedateto_d"] = settingTranceRow.freservedateto_d;//以新的业务日期为准
            }
            var oldQty = Convert.ToDecimal(reserveTranceRow["fqty_d"]);
            var newQty = settingTranceRow.fqty_d;
            if (oldQty == newQty)
            {
                reserveTranceRow["fstorehouseid"] = settingTranceRow.fstorehouseid;
                reserveTranceRow["fstockstatus"] = settingTranceRow.fstockstatus;
                reserveTranceRow["freservenote"] = settingTranceRow.freservenote;
                reserveTranceRow["foptime"] = settingTranceRow.foptime;
                reserveTranceRow["fopuserid"] = settingTranceRow.fopuserid;

                return;
            }

            var tranceRow = reserveTranceRow.Clone() as DynamicObject;
            tranceRow["foptime"] = DateTime.Now;
            tranceRow["fopuserid"] = ctx.UserId;
            if (oldQty > newQty)
            {
                tranceRow["fqty_d"] = oldQty - newQty;
                tranceRow["fbizqty_d"] = oldQty - newQty;
                tranceRow["fopdesc"] = "1";//减少预留
                tranceRow["fdirection_d"] = "1";
                tranceRow["freservenote"] = "在预留设置页面修改预留量，减少了预留";
            }
            else
            {
                tranceRow["fqty_d"] = newQty - oldQty;
                tranceRow["fbizqty_d"] = newQty - oldQty;
                tranceRow["fopdesc"] = "0";//增加预留
                tranceRow["fdirection_d"] = "0";
                tranceRow["freservenote"] = "在预留设置页面修改预留量，增加了预留";
            }

            reserveTranceRows.Add(tranceRow);
        }


        /// <summary>
        /// 新增预留跟踪记录
        /// </summary>
        /// <param name="reserveTranceRows"></param>
        /// <param name="settingTranceRow"></param>
        /// <returns></returns>
        private static void AddReserveTranceRow(UserContext ctx, DynamicObjectCollection reserveTranceRows, ReserveSettingTraceInfo settingTranceRow)
        {
            DynamicObject reserveTranceRow = reserveTranceRows.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
            reserveTranceRow["fbizqty_d"] = settingTranceRow.fbizqty_d;
            reserveTranceRow["fqty_d"] = settingTranceRow.fqty_d;
            reserveTranceRow["freservedateto_d"] = settingTranceRow.freservedateto_d;
            reserveTranceRow["fdirection_d"] = settingTranceRow.fdirection_d;
            reserveTranceRow["fopdesc"] = settingTranceRow.fopdesc;
            reserveTranceRow["fmaterialid_d"] = settingTranceRow.fmaterialid_d;
            reserveTranceRow["fbizunitid_d"] = settingTranceRow.fbizunitid_d;
            reserveTranceRow["funitid_d"] = settingTranceRow.funitid_d;
            reserveTranceRow["fstorehouseid"] = settingTranceRow.fstorehouseid;
            reserveTranceRow["fstockstatus"] = settingTranceRow.fstockstatus;
            reserveTranceRow["freservenote"] = settingTranceRow.freservenote;
            reserveTranceRow["foptime"] = DateTime.Now;
            reserveTranceRow["fopuserid"] = ctx.UserId;
            reserveTranceRow["ffrombillno"] = settingTranceRow.ffrombillno;
            reserveTranceRow["ffrombillpkid"] = settingTranceRow.ffrombillpkid;
            reserveTranceRow["ffromformid"] = settingTranceRow.ffromformid;

            reserveTranceRows.Add(reserveTranceRow);
        }


        /// <summary>
        /// 增加预留跟踪记录---当预留设置中删除预留跟踪时，增加类型为预留减少的跟踪记录
        /// </summary>
        /// <param name="settingDemandRow"></param>
        /// <param name="reserveTranceRows"></param>
        private static void AddTranceWhenDeleteSetting(UserContext ctx, ReserveSettingDemandInfo settingDemandRow, DynamicObjectCollection reserveTranceRows)
        {
            var beDelTrance = new List<DynamicObject>();
            var beAddTrance = new List<DynamicObject>();
            foreach (var item in reserveTranceRows)
            {
                if (item["freservedateto_d"] == null)
                {
                    item["freservedateto_d"] = settingDemandRow.freservedateto;//以新的业务日期为准
                }

                var tranceId = item["Id"].ToString();
                var settingTranceRow = settingDemandRow.TraceEntry.FirstOrDefault(f => !tranceId.IsNullOrEmptyOrWhiteSpace() && tranceId.EqualsIgnoreCase(f.freservetrancepkid));
                if (settingTranceRow == null)
                {
                    //如果该行是上一次被删除的行，直接删除掉
                    var freservenote = Convert.ToString(item["freservenote"]);
                    if (freservenote == "在预留设置页面删除预留跟踪行" || freservenote == "在预留设置页面删除了预留增加的跟踪行" || freservenote == "在预留设置页面删除了预留减少的跟踪行")
                    {
                        beDelTrance.Add(item);
                        continue;
                    }

                    var tranceRow = item.Clone() as DynamicObject;
                    tranceRow["fopdesc"] = "1";//预留减少
                    tranceRow["fdirection_d"] = "1";
                    tranceRow["foptime"] = DateTime.Now;
                    tranceRow["fopuserid"] = ctx.UserId;
                    tranceRow["freservenote"] = "在预留设置页面删除了预留增加的跟踪行";

                    if (item["fdirection_d"]?.ToString() == "1")//删除预留减少的行，则增加的是预留增加的跟踪行
                    {
                        tranceRow["fopdesc"] = "0";//增加预留
                        tranceRow["fdirection_d"] = "0";
                        tranceRow["freservenote"] = "在预留设置页面删除了预留减少的跟踪行";
                    }

                    beAddTrance.Add(tranceRow);
                }
            }
            foreach (var item in beAddTrance)
            {
                reserveTranceRows.Add(item);
            }
            foreach (var item in beDelTrance)
            {
                reserveTranceRows.Remove(item);
            }
        }


        /// <summary>
        /// 删除掉不存在的需求明细行（需求明细行被删除了或者被变更掉了）
        /// </summary>
        /// <param name="settingEns"></param>
        /// <param name="reserveEntrys"></param>
        private static void DeleteNoExistDemandRow(ReserveSettingInfo reserveSettingInfo, DynamicObjectCollection reserveEntrys)
        {
            var beDelRows = new List<DynamicObject>();
            foreach (var enRow in reserveEntrys)
            {
                var enPKID = enRow["Id"].ToString();
                var settingRow = reserveSettingInfo.DemandEntry.FirstOrDefault(f => !enPKID.IsNullOrEmptyOrWhiteSpace() && enPKID.EqualsIgnoreCase(f.freserveentryid));

                if (settingRow == null && (reserveSettingInfo.feditrows == null || reserveSettingInfo.feditrows.Contains(enPKID)))
                {
                    beDelRows.Add(enRow);
                }
            }

            foreach (var item in beDelRows)
            {
                reserveEntrys.Remove(item);
            }
        }




        /// <summary>
        /// 更新源单信息
        /// </summary>
        /// <param name="dataEntities"></param>
        private void UpdateSourceOrder(UserContext ctx, HtmlForm demandForm, IEnumerable<DynamicObject> dataEntities)
        {
            if (dataEntities == null || dataEntities.Count() == 0)
            {
                return;
            }

            var dm = ctx.Container.GetService<IDataManager>();
            var dt = demandForm.GetDynamicObjectType(ctx);
            dm.InitDbContext(ctx, dt);
            dm.Save(dataEntities);
        }






    }



}