using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Reserve;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 业务操作的库存校验
    /// </summary>
    public partial class ReserveService : IBizCheckInvService
    {
        /// <summary>
        /// 即时库存中最小匹配维度字段
        /// </summary>
        private static string[] InvFlexFieldKeys = new string[]
        {
            "fmaterialid",
            "fattrinfo_e",
            "fcustomdesc",
            "funitid",
            "fmtono"
        };

        public List<BizInvCheckResult> BizCheckInventory(UserContext ctx, HtmlForm invFormMeta, IEnumerable<DynamicObject> inventoryData, BizInvCheckPara checkPara, OperateOption option)
        {
            SetCheckFldMapInfo(ctx, invFormMeta, checkPara);

            var checkResult = new List<BizInvCheckResult>();

            var dbSvc = ctx.Container.GetService<IDBService>();
            var tempTblName = dbSvc.CreateTemporaryTableName(ctx);
            var pkidMaps = new Dictionary<string, string>();//表单主键Id映射关系

            //获取需要进行校验的数据
            var billDatas = GetBizBillDatas(ctx, invFormMeta, inventoryData, checkPara, tempTblName, pkidMaps);
            if (billDatas == null || billDatas.Count == 0)
            {
                return checkResult;
            }

            //取及时库存数据
            var invQtyDatas = GetInvQtyDatas(ctx, tempTblName);

            //取预留数据 
            var reserveQtyDatas = GetReserveQtyDatas(ctx, checkPara, tempTblName);

            //取在途数据
            var onWayQtyDatas = GetOnWayQtyDatas(ctx, checkPara, tempTblName);

            var stockStatusMap = ReserveUtil.GetStockStatusMpa(ctx);

            var ctrlStkDatas = new List<Tuple<string, string, string>>();
            if (checkPara.AllowsalesControl && (invFormMeta.Id.EqualsIgnoreCase("ydj_order") || invFormMeta.Id.EqualsIgnoreCase("stk_sostockout")))
            {
                //如果是订单，按销售员可销售仓库控制
                var svc = ctx.Container.GetService<ISalesControl>();
                ctrlStkDatas = svc.GetCanSalHousePerm(ctx);
            }

            //按控制维度进行数据汇总（按所有单合并校验，可能批量操作）
            var groupByFlds = GetGroupFldInfo(checkPara);
            var dataGroupKey = new DataEntityGroupKey(groupByFlds, billDatas.First().DynamicObjectType);
            var groupDatas = billDatas.GroupBy(o => dataGroupKey.GetKey(o)).ToList();

            var debugInfo = new BizCheckInvDebugInfo();
            try
            {
                debugInfo.CheckPara = checkPara;
                debugInfo.BillDatas = billDatas;
                debugInfo.InvQtyDatas = invQtyDatas;
                debugInfo.ReserveQtyDatas = reserveQtyDatas;
                debugInfo.OnWayQtyDatas = onWayQtyDatas;
                debugInfo.StockStatusMap = stockStatusMap;
                debugInfo.CtrlStkDatas = ctrlStkDatas;
                debugInfo.GroupByFlds = groupByFlds;
            }
            finally { }

            foreach (var grpData in groupDatas)
            {
                var invCheck = new BizInvCheckResult();
                checkResult.Add(invCheck);

                var debugItemInfo = new BizCheckInvDebugInfo.BizCheckInvDebugItemInfo();
                debugInfo.Items.Add(debugItemInfo);

                //var billPKIDS = grpData.ToList().Select(f => Convert.ToString(f["fbillhead_id"])).Distinct().ToList();
                //var billPKNOS = grpData.ToList().Select(f => Convert.ToString(f["fbillno"])).Distinct().ToList();

                //取即时库存
                var invObjs = invQtyDatas.ToList();

                //取预留数据--排除本单关联的预留及上游单据所关联的预留
                var reserveObjs = new List<DynamicObject>();
                if (checkPara.ReserveSource == 0)
                {
                    reserveObjs = GetReserveDatasWithIgnoreCurrentReserveBill(ctx, invFormMeta, reserveQtyDatas,
                        grpData.ToList(), pkidMaps);
                }
                else
                {
                    reserveObjs = GetReserveDatasWithIgnoreCurrentBill(ctx, invFormMeta, reserveQtyDatas, grpData.ToList(), pkidMaps);
                }

                //取在途数据--排除本单对应的在途
                var onWayObjs = GetOnWayDatas(invFormMeta, onWayQtyDatas, grpData.ToList(), pkidMaps);

                try
                {
                    debugItemInfo.InvDatas = invObjs?.ToList();
                    debugItemInfo.ReserveDatas = reserveObjs?.ToList();
                    debugItemInfo.OnWayDatas = onWayObjs?.ToList();
                }
                finally { }

                //按库存控制维度，匹配到对应的即时库存、预留、在途等数据
                FilterByCtrlInvFlexItem(groupByFlds, grpData, invCheck, stockStatusMap, ref invObjs, ref reserveObjs, ref onWayObjs);

                try
                {
                    debugItemInfo.FilterByCtrlInvFlexItem_InvDatas = invObjs?.ToList();
                    debugItemInfo.FilterByCtrlInvFlexItem_ReserveDatas = reserveObjs?.ToList();
                    debugItemInfo.FilterByCtrlInvFlexItem_OnWayDatas = onWayObjs?.ToList();
                }
                finally { }

                //即时库存、预留数据、在途数据，排除无权限的仓库
                FilterByCtrlStock(ctx, checkPara, invFormMeta, ctrlStkDatas, ref invObjs, ref reserveObjs, ref onWayObjs);

                try
                {
                    debugItemInfo.FilterByCtrlStock_InvDatas = invObjs?.ToList();
                    debugItemInfo.FilterByCtrlStock_ReserveDatas = reserveObjs?.ToList();
                    debugItemInfo.FilterByCtrlStock_OnWayDatas = onWayObjs?.ToList();
                }
                finally { }

                var qty_inv = invObjs?.Sum(f => Convert.ToDecimal(f["fqty"]));
                var qty_reserve = reserveObjs?.Sum(f => Convert.ToDecimal(f["fqty"]));
                var qty_onWay = onWayObjs?.Sum(f => Convert.ToDecimal(f["fqty"]));
                var qty_bill = grpData?.ToList()?.Sum(f => Convert.ToDecimal(f["fqty"]));
                if (invFormMeta.Id.EqualsIgnoreCase("ydj_order"))
                {
                    //#http://dmp.jienor.com:81/zentao/task-view-38891.html
                    //【预计出库数量】=【销售数量】-【销售已出库数】+【销售已退换数量】
                    var outqty_bill = grpData?.ToList()?.Sum(f => Convert.ToDecimal(f["foutqty"]));
                    var returnqty_bill2 = grpData?.ToList()?.Sum(f => Convert.ToDecimal(f["freturnqty"]));
                    qty_bill = qty_bill - outqty_bill + returnqty_bill2;
                }

                //可用数量：即时库存 - 预留量 + 在途量 - 当前单据数量 
                var qty = qty_inv.GetValueOrDefault() - qty_reserve.GetValueOrDefault() + qty_onWay.GetValueOrDefault() - qty_bill.GetValueOrDefault();

                invCheck.fqty = qty;
                invCheck.fqty_bill = qty_bill.GetValueOrDefault();
                invCheck.fqty_inv = qty_inv.GetValueOrDefault();
                invCheck.fqty_onWay = qty_onWay.GetValueOrDefault();
                invCheck.fqty_reserve = qty_reserve.GetValueOrDefault();
                invCheck.Level = checkPara.fctrlstrength.Id;

                invCheck.fstockstatus = invCheck.fstockstatus.IsNullOrEmpty() ? "" : invCheck.fstockstatus;
                invCheck.fstorehouseid = invCheck.fstorehouseid.IsNullOrEmpty() ? "" : invCheck.fstorehouseid;
                invCheck.fstorelocationid = invCheck.fstorelocationid.IsNullOrEmpty() ? "" : invCheck.fstorelocationid;

                var enRow = grpData?.ToList().First();
                if (enRow.DynamicObjectType.Properties.ContainsKey("fbizunitid"))
                {
                    invCheck.fbizunitid = Convert.ToString(enRow["fbizunitid"]);
                }

                if (qty < 0)
                {
                    if (invCheck.Level == "0")
                    {
                        invCheck.Message = "警告：库存不足！";
                    }
                    else
                    {
                        invCheck.Message = "错误：校验不通过，库存不足！";
                    }
                }
                else
                {
                    invCheck.Message = "库存校验通过！";
                }

                try
                {
                    debugItemInfo.CheckResult = invCheck;
                }
                finally { }
            }


            dbSvc.DeleteTempTableByName(ctx, tempTblName, true);

            if (!checkResult.Any(s => s.fqty < 0))
            {
                DebugLog(ctx, debugInfo);
            }

            return checkResult;
        }

        private void DebugLog(UserContext ctx, BizCheckInvDebugInfo debugInfo)
        {
            // try
            // {
            //     // 记录日志
            //     var companyId = ctx.Company;
            //     var company = ctx.Companys
            //         .FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(companyId));

            //     StringBuilder info = new StringBuilder();
            //     info.AppendLine();
            //     info.AppendLine("BizCheckInv:" + debugInfo?.ToJson());
            //     info.AppendLine("UserContext:" + new Dictionary<string, object>()
            //     {
            //         {"CompanyId", companyId},
            //         {"CompanyNumber", company?.CompanyNumber},
            //         {"CompanyName", company?.CompanyName},
            //         {"UserId", ctx.UserId},
            //         {"DisplayName", ctx.DisplayName}
            //     }.ToJson());

            //     DebugUtil.WriteLogToFile(info.ToString(), $"BizCheckInv/{DateTime.Now.Hour}");
            // }
            // catch
            // {
            // }
        }


        /// <summary>
        /// 按库存控制维度，匹配到对应的即时库存、预留、在途等数据
        /// </summary>
        /// <param name="groupByFlds"></param>
        /// <param name="grpData"></param>
        /// <param name="invCheck"></param>
        /// <param name="invObjs"></param>
        /// <param name="reserveObjs"></param>
        /// <param name="onWayObjs"></param>
        private void FilterByCtrlInvFlexItem(string[] groupByFlds, IGrouping<DataEntityGroupKey.Key, DynamicObject> grpData, BizInvCheckResult invCheck, Dictionary<string, string> stockStatusMap,
                                            ref List<DynamicObject> invObjs, ref List<DynamicObject> reserveObjs, ref List<DynamicObject> onWayObjs)
        {
            int pos = 0;
            foreach (var fldKey in groupByFlds)
            {
                var value = Convert.ToString(grpData.Key.Values[pos]).Trim();

                // 取值后，就移到下一位，避免后续操作时未执行到末尾就进入下一个循环
                pos++;

                SetCheckResultValue(invCheck, fldKey, value);

                //针对物流跟踪号、库存状态等字段，空值认为都可以匹配
                if (fldKey.EqualsIgnoreCase("fmtono")
                    //|| fldKey.EqualsIgnoreCase("fstorehouseid")
                    || fldKey.EqualsIgnoreCase("fstockstatus"))
                {
                    onWayObjs = onWayObjs.Where(f => f[fldKey].IsNullOrEmptyOrWhiteSpace() || Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(value)).ToList();
                    reserveObjs = reserveObjs.Where(f => f[fldKey].IsNullOrEmptyOrWhiteSpace() || Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(value)).ToList();
                    //if (fldKey.EqualsIgnoreCase("fstorehouseid"))
                    //{
                    //    if (reserveObjs.Any(f => f[fldKey].IsNullOrEmptyOrWhiteSpace()))
                    //    {
                    //        //如果预留单上没有指定仓库，则认为所有仓库适用，这时候，就不需要过滤及时库存的仓库了，所以continue
                    //        continue;
                    //    }
                    //}
                    if (fldKey.EqualsIgnoreCase("fstockstatus") && stockStatusMap != null && stockStatusMap.ContainsKey(value))
                    {
                        //经销商自己修改了系统预设的库存状态的
                        invObjs = invObjs.Where(f => f[fldKey].IsNullOrEmptyOrWhiteSpace() || Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(value)
                                                    || Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(stockStatusMap[value])).ToList();
                    }
                    else
                    {
                        invObjs = invObjs.Where(f => f[fldKey].IsNullOrEmptyOrWhiteSpace() || Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(value)).ToList();
                    }


                }
                else//其他字段，如商品、辅助属性、定制信息、计量单位、仓库、仓位，严格匹配
                {
                    invObjs = invObjs.Where(f => Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(value)).ToList();
                    reserveObjs = reserveObjs.Where(f => Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(value)).ToList();
                    onWayObjs = onWayObjs.Where(f => Convert.ToString(f[fldKey]).Trim().EqualsIgnoreCase(value)).ToList();
                }
            }
        }

        /// <summary>
        /// 获取在途数据
        /// </summary>
        /// <param name="invFormMeta">当前操作表单模型</param>
        /// <param name="onWayQtyDatas">所有在途数据</param>
        /// <param name="currentBillDatas">当前单据的当前操作行</param> 
        /// <returns></returns>
        private static List<DynamicObject> GetOnWayDatas(HtmlForm invFormMeta, List<DynamicObject> onWayQtyDatas,
                                            List<DynamicObject> currentBillDatas, Dictionary<string, string> pkidMaps)
        {
            var onWayObjs = onWayQtyDatas;
            foreach (var currentBillData in currentBillDatas)
            {
                //排除本单对应的在途
                var billId = pkidMaps[Convert.ToString(currentBillData["fbillhead_id"])];
                var except = onWayObjs?.Where(f => invFormMeta.Id.EqualsIgnoreCase(Convert.ToString(f["fformid"])) && billId.Contains(Convert.ToString(f["fid"])))?.ToList();
                onWayObjs = onWayObjs?.Except(except)?.ToList();
            }
            if (onWayObjs == null)
            {
                return new List<DynamicObject>();
            }
            return onWayObjs;
        }

        /// <summary>
        /// 获取预留数据（忽略当前预留单）
        /// </summary>
        /// <param name="invFormMeta">当前操作表单模型</param>
        /// <param name="reserveQtyDatas">所有预留数据</param>
        /// <param name="currentBillDatas">当前单据的当前操作行</param> 
        /// <returns></returns>
        private static List<DynamicObject> GetReserveDatasWithIgnoreCurrentReserveBill(UserContext ctx, HtmlForm invFormMeta,
            List<DynamicObject> reserveQtyDatas, List<DynamicObject> currentBillDatas, Dictionary<string, string> pkidMaps)
        {
            if (reserveQtyDatas == null || !reserveQtyDatas.Any())
            {
                return new List<DynamicObject>();
            }

            //取销售合同对应的销售出库单数据
            var tempTbl = "";
            var dbSvc = ctx.Container.GetService<IDBService>();
            List<DynamicObject> soOutDatas = null;
            if (invFormMeta.Id.EqualsIgnoreCase("ydj_order") && currentBillDatas?.Count > 0)
            {
                using (var tran = ctx.CreateTransaction())
                {
                    var soIds = from p in currentBillDatas
                                select pkidMaps[p["fbillhead_id"].ToString()];
                    var sql = @" select distinct t3.fbillno as fsooutno, t1.fsourceformid,t1.fsourcebillno,t1.fsoorderinterid
                                from t_stk_sostockoutentry t1 with(nolock)
                                inner join t_ydj_orderentry t2 with(nolock) on t1.fsourceentryid=t2.fentryid 
				                inner join t_ydj_order tx with(nolock) on t2.fid=tx.fid
                                inner join t_stk_sostockout t3 with(nolock) on t1.fid=t3.fid  
                                ";
                    if (currentBillDatas.Count > 30)
                    {
                        tempTbl = dbSvc.CreateTempTableWithDataList(ctx, soIds, false);
                        sql += "   where exists (select 1 from {0} x where t1.fsoorderinterid  = x.fid ) and t3.fcancelstatus=0 ".Fmt(tempTbl);
                    }
                    else
                    {
                        sql += "  where  t1.fsoorderinterid in ({0})  and t3.fcancelstatus = 0  ".Fmt(soIds.JoinEx(",", true));
                    }

                    soOutDatas = dbSvc.ExecuteDynamicObject(ctx, sql).ToList();

                    tran.Complete();
                }
            }

            var reserveObjs = reserveQtyDatas;
            foreach (var currentBillData in currentBillDatas)
            {
                var billNo = Convert.ToString(currentBillData["fbillno"]);
                //排除本单关联的预留（注意本单转移到下游的预留要重新算回来，不要排除）
                var except = reserveObjs?.Where(f => !"3".EqualsIgnoreCase(Convert.ToString(f["fopdesc"])) && invFormMeta.Id.EqualsIgnoreCase(Convert.ToString(f["fsourceformid"]))
                                                && billNo.Contains(Convert.ToString(f["fsourcenumber"])))?.ToList();
                reserveObjs = reserveObjs?.Except(except)?.ToList();

                reserveObjs = ExceptDownBillReserve(invFormMeta, soOutDatas, billNo, reserveObjs);

                //TODO：排除本单对应的客户的在其他单据所关联的预留
                //比如同一个客户下了两张销售合同，同时商品都一样，都做了预留，现在出库其中的一单，则认为另外一单的预留也可以在当前单出货

            }

            reserveObjs = ExceptSourceBillReserve(invFormMeta, currentBillDatas, reserveObjs);

            dbSvc.DeleteTempTableByName(ctx, tempTbl, true);

            return reserveObjs;
        }

        /// <summary>
        /// 排除本单对应的下游单所关联的预留：比如销售合同未做预留，同时下推了销售出库单，则销售出库单会做预留，这个时候修改销售合同，则销售合同关联的销售出库单的预留要剔除在外
        /// </summary>
        /// <param name="invFormMeta"></param>
        /// <param name="soOutDatas"></param>
        /// <param name="billNo"></param>
        /// <param name="reserveObjs"></param>
        /// <returns></returns>
        private static List<DynamicObject> ExceptDownBillReserve(HtmlForm invFormMeta, List<DynamicObject> soOutDatas, string billNo, List<DynamicObject> reserveObjs)
        {
            List<DynamicObject> except;
            if (invFormMeta.Id.EqualsIgnoreCase("ydj_order") && soOutDatas?.Count > 0)
            {
                var grpX = soOutDatas
                    .GroupBy(f => new { soNo = f["fsourcebillno"]?.ToString(), soOutNo = f["fsooutno"]?.ToString() }).ToList();
                foreach (var item in grpX)
                {
                    if (!item.Key.soNo.EqualsIgnoreCase(billNo))
                    {
                        continue;
                    }

                    except = reserveObjs?.Where(f => "stk_sostockout".EqualsIgnoreCase(Convert.ToString(f["fsourceformid"]))
                                                     && item.Key.soOutNo.Contains(Convert.ToString(f["fsourcenumber"])))
                        ?.ToList();
                    reserveObjs = reserveObjs?.Except(except)?.ToList();
                }
            }

            return reserveObjs;
        }

        /// <summary>
        /// 排除本单对应的上游单所关联的预留：比如销售合同做了预留，现在做销售出库单，则在销售合同上的预留也应该可以在当前出库单出货，即要把这个预留排除在外
        /// </summary>
        /// <param name="invFormMeta"></param>
        /// <param name="currentBillDatas"></param>
        /// <param name="reserveObjs"></param>
        /// <returns></returns>
        private static List<DynamicObject> ExceptSourceBillReserve(HtmlForm invFormMeta, List<DynamicObject> currentBillDatas, List<DynamicObject> reserveObjs)
        {
            var sourceTypeGrps = currentBillDatas.GroupBy(s => Convert.ToString(s["fsourcetype"]));
            foreach (var sourceTypeGrp in sourceTypeGrps)
            {
                var srcFormId = sourceTypeGrp.Key;
                if (srcFormId.IsNullOrEmptyOrWhiteSpace()) continue;

                var sourceEntryIdKey = invFormMeta.Id.EqualsIgnoreCase("ydj_order") ? "fsourceentryid_e" : "fsourceentryid";

                // 为了降低影响面，暂时只处理销售合同->库存调拨单的
                // 修复场景：销售合同明细行销售数量3，预留量3，库存量3，调拨出库2个，同时添加1行调拨量1个的相同商品时，由于目前算法是排除源单行的预留数据，导致库存可用量校验通过，导致超预留。
                if (srcFormId.EqualsIgnoreCase("ydj_order") && invFormMeta.Id.EqualsIgnoreCase("stk_inventorytransfer"))
                {
                    var sameSourceEntryGrps = sourceTypeGrp.GroupBy(s => Convert.ToString(s[sourceEntryIdKey]));
                    var fopdesc_addreserve = "0";
                    foreach (var sourceEntryGrp in sameSourceEntryGrps)
                    {
                        string sourceEtryId = sourceEntryGrp.Key;
                        if (sourceEtryId.IsNullOrEmptyOrWhiteSpace()) continue;

                        var except = reserveObjs?.Where(f => srcFormId.EqualsIgnoreCase(Convert.ToString(f["fsourceformid"])) && sourceEtryId.Contains(Convert.ToString(f["fsourceentryid"])))?.ToList();
                        if (!except.Any()) continue;

                        // 相同源单行的汇总库存数量（例如汇总调拨量）
                        var sameSourceStockSumQty = sourceEntryGrp.Sum(s => Convert.ToDecimal(s["fqty"]));
                        // 相同源单行的汇总预留数量
                        var sameSourceReserveSumQty = except.Sum(s => Convert.ToDecimal(s["fqty"]));
                        // 源单的预留数量大于关联源单的出库数量
                        if (sameSourceReserveSumQty > sameSourceStockSumQty)
                        {
                            //按仓库+库存状态维度进行数据汇总
                            var reserveGroupDatas = except.GroupBy(o => $"{o["fstorehouseid"]}_{o["fstockstatus"]}").ToList();
                            var stockGroupDatas = sourceEntryGrp.GroupBy(o => $"{o["fstorehouseid"]}_{o["fstockstatus"]}").ToList();

                            var dicStockQty = stockGroupDatas.ToDictionary(s => s.Key, s => s.Sum(o => Convert.ToDecimal(o["fqty"])));
                            var dicReserveQty = reserveGroupDatas.ToDictionary(s => s.Key, s => s.Sum(o => Convert.ToDecimal(o["fqty"])));

                            // 1.先按相同维度扣减
                            Dictionary<string, decimal> decreaseItems = new Dictionary<string, decimal>();
                            foreach (var key in dicReserveQty.Keys)
                            {
                                var reserveQty = dicReserveQty[key];
                                if (dicStockQty.TryGetValue(key, out var stockQty))
                                {
                                    if (stockQty <= 0 || reserveQty <= 0) continue;

                                    var beCecreaseQty = reserveQty > stockQty ? stockQty : reserveQty;

                                    var groupKeys = key.Split(new string[] { "_" }, StringSplitOptions.None);
                                    string fstorehouseid = Convert.ToString(groupKeys[0]);
                                    string fstockstatus = Convert.ToString(groupKeys[1]);

                                    var decreaseReserveObj = except.First().Clone() as DynamicObject;
                                    decreaseReserveObj["fdetailid"] = " ";
                                    decreaseReserveObj["fopdesc"] = "1";    // 减少预留
                                    decreaseReserveObj["freservenote"] = "用于排除源单预留数量使用";
                                    decreaseReserveObj["fqty"] = -beCecreaseQty;
                                    decreaseReserveObj["fstorehouseid"] = fstorehouseid;    //使用当前行的仓库
                                    decreaseReserveObj["fstockstatus"] = fstockstatus;      //使用当前行的库存状态
                                    reserveObjs.Add(decreaseReserveObj);

                                    decreaseItems[key] = beCecreaseQty;
                                }
                            }

                            foreach (var key in decreaseItems.Keys)
                            {
                                dicReserveQty[key] -= decreaseItems[key];
                                dicStockQty[key] -= decreaseItems[key];
                            }

                            // 2.再随机扣减
                            var balanceStockQty = dicStockQty.Values.Sum(s => s); // 剩余库存数量
                            foreach (var key in dicReserveQty.Keys)
                            {
                                var reserveQty = dicReserveQty[key];
                                if (reserveQty <= 0) continue;

                                var beCecreaseQty = reserveQty > balanceStockQty ? balanceStockQty : reserveQty;

                                var groupKeys = key.Split(new string[] { "_" }, StringSplitOptions.None);
                                string fstorehouseid = Convert.ToString(groupKeys[0]);
                                string fstockstatus = Convert.ToString(groupKeys[1]);

                                var decreaseReserveObj = except.First().Clone() as DynamicObject;
                                decreaseReserveObj["fdetailid"] = " ";
                                decreaseReserveObj["fopdesc"] = "1";    // 减少预留
                                decreaseReserveObj["freservenote"] = "用于排除源单预留数量使用";
                                decreaseReserveObj["fqty"] = -beCecreaseQty;
                                decreaseReserveObj["fstorehouseid"] = fstorehouseid;    //使用当前行的仓库
                                decreaseReserveObj["fstockstatus"] = fstockstatus;      //使用当前行的库存状态
                                reserveObjs.Add(decreaseReserveObj);

                                //dicReserveQty[key] -= beCecreaseQty;
                                balanceStockQty -= beCecreaseQty;
                            }

                        }
                        // 按正常逻辑处理
                        else
                        {
                            reserveObjs = reserveObjs?.Except(except)?.ToList();
                        }
                    }
                }
                else
                {
                    foreach (var currentBillData in sourceTypeGrp)
                    {
                        string sourceEtryId = Convert.ToString(currentBillData[sourceEntryIdKey]);
                        if (!srcFormId.IsNullOrEmptyOrWhiteSpace()
                            && !sourceEtryId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var except = reserveObjs?.Where(f => srcFormId.EqualsIgnoreCase(Convert.ToString(f["fsourceformid"])) && sourceEtryId.Contains(Convert.ToString(f["fsourceentryid"])))?.ToList();

                            reserveObjs = reserveObjs?.Except(except)?.ToList();
                        }
                    }
                }
            }

            return reserveObjs;
        }

        /// <summary>
        /// 获取预留数据（忽略当前单据）
        /// </summary>
        /// <param name="invFormMeta">当前操作表单模型</param>
        /// <param name="reserveQtyDatas">所有预留数据</param>
        /// <param name="currentBillDatas">当前单据的当前操作行</param> 
        /// <returns></returns>
        private static List<DynamicObject> GetReserveDatasWithIgnoreCurrentBill(UserContext ctx, HtmlForm invFormMeta,
            List<DynamicObject> reserveQtyDatas, List<DynamicObject> currentBillDatas,
            Dictionary<string, string> pkidMaps)
        {
            if (reserveQtyDatas == null || !reserveQtyDatas.Any())
            {
                return new List<DynamicObject>();
            }

            var billIds = from p in currentBillDatas
                          select pkidMaps[p["fbillhead_id"].ToString()];

            var reserveObjs = reserveQtyDatas.Where(s => !billIds.Contains(Convert.ToString(s["fid"]))).ToList();

            return reserveObjs;
        }

        ///// <summary>
        ///// 获取即时库存数据
        ///// </summary>
        ///// <param name="invFormMeta"></param>
        ///// <param name="invQtyDatas">即时库存数据</param>
        ///// <param name="ctrlStkDatas"></param>
        ///// <param name="currentBillDatas">当前单据的当前操作行</param>
        ///// <returns></returns>
        //private static List<DynamicObject> GetInvDatas(UserContext ctx, BizInvCheckPara checkPara, HtmlForm invFormMeta, List<DynamicObject> invQtyDatas, List<Tuple<string, string, string>> ctrlStkDatas, List<DynamicObject> currentBillDatas)
        //{
        //    var invObjs = invQtyDatas.ToList();
        //    var currCtrlStkDatas = ctrlStkDatas?.Where(f => f.Item3.SplitKey().Contains(invFormMeta.Id, StringComparer.OrdinalIgnoreCase))?.ToList(); //当前单据设置的可用仓控制信息
        //    if (currCtrlStkDatas == null || currCtrlStkDatas.Count == 0)
        //    {
        //        return invObjs;
        //    }

        //    var bdProvide = ctx.Container.GetService<IBaseFormProvider>();
        //    var fstaffid = bdProvide.GetMyStaff(ctx)?.Id;
        //    fstaffid = fstaffid.IsNullOrEmptyOrWhiteSpace() ? "####" : fstaffid;//如果用户不关联员工，认为无权限
        //    if (checkPara.AllowsalesControl && (invFormMeta.Id.EqualsIgnoreCase("ydj_order") || invFormMeta.Id.EqualsIgnoreCase("stk_sostockout")))
        //    {
        //        var stocks = currCtrlStkDatas.Where(f => f.Item1 == fstaffid).Select(f => f.Item2)?.ToList();

        //        invObjs = invObjs?.Where(o =>
        //        {
        //            var tempValue = o["fstorehouseid"]?.ToString();
        //            if (tempValue.IsNullOrEmptyOrWhiteSpace())
        //            {
        //                tempValue = " ";
        //            }
        //            return stocks.Contains(tempValue);
        //        })?.ToList();
        //    }
        //    if (invObjs == null)
        //    {
        //        invObjs = new List<DynamicObject>();
        //    }

        //    return invObjs;
        //}

        /// <summary>
        /// 按仓库可用控制，匹配到对应的即时库存、预留、在途等数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="checkPara"></param>
        /// <param name="invFormMeta"></param>
        /// <param name="ctrlStkDatas"></param>
        /// <param name="invObjs"></param>
        /// <param name="reserveObjs"></param>
        /// <param name="onWayObjs"></param>
        /// <returns></returns>
        private static void FilterByCtrlStock(UserContext ctx,
            BizInvCheckPara checkPara,
            HtmlForm invFormMeta,
            List<Tuple<string, string, string>> ctrlStkDatas,
            ref List<DynamicObject> invObjs, ref List<DynamicObject> reserveObjs, ref List<DynamicObject> onWayObjs)
        {
            var currCtrlStkDatas = ctrlStkDatas?.Where(f => f.Item3.SplitKey().Contains(invFormMeta.Id, StringComparer.OrdinalIgnoreCase))?.ToList(); //当前单据设置的可用仓控制信息
            if (currCtrlStkDatas == null || currCtrlStkDatas.Count == 0)
            {
                return;
            }

            var bdProvide = ctx.Container.GetService<IBaseFormProvider>();
            var fstaffid = bdProvide.GetMyStaff(ctx)?.Id;
            fstaffid = fstaffid.IsNullOrEmptyOrWhiteSpace() ? "####" : fstaffid;//如果用户不关联员工，认为无权限
            if (checkPara.AllowsalesControl && (invFormMeta.Id.EqualsIgnoreCase("ydj_order") || invFormMeta.Id.EqualsIgnoreCase("stk_sostockout")))
            {
                var stocks = currCtrlStkDatas.Where(f => f.Item1 == fstaffid).Select(f => f.Item2)?.ToList();

                invObjs = invObjs?.Where(o =>
                {
                    var tempValue = o["fstorehouseid"]?.ToString();
                    if (tempValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        tempValue = " ";
                    }
                    return stocks.Contains(tempValue);
                })?.ToList();

                reserveObjs = reserveObjs?.Where(o =>
                {
                    var tempValue = o["fstorehouseid"]?.ToString();
                    if (tempValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        tempValue = " ";
                    }
                    return stocks.Contains(tempValue);
                })?.ToList();

                onWayObjs = onWayObjs?.Where(o =>
                {
                    var tempValue = o["fstorehouseid"]?.ToString();
                    if (tempValue.IsNullOrEmptyOrWhiteSpace())
                    {
                        tempValue = " ";
                    }
                    return stocks.Contains(tempValue);
                })?.ToList();
            }
        }

        /// <summary>
        /// 获取分组汇总字段信息
        /// </summary>
        /// <param name="checkPara"></param>
        /// <returns></returns>
        private static string[] GetGroupFldInfo(BizInvCheckPara checkPara)
        {
            var groupBy = new List<string>(InvFlexFieldKeys);
            var flds = checkPara.fctrlfldkey.Id.SplitKey();
            foreach (var item in flds)
            {
                if (item.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                groupBy.Add(item.ToLower().Replace("stk_inv_ctrl_item_", ""));
            }

            groupBy = groupBy.Distinct().ToList();

            return groupBy.ToArray();
        }

        private List<DynamicObject> GetBizBillDatas(UserContext ctx, HtmlForm invFormMeta,
            IEnumerable<DynamicObject> inventoryData, BizInvCheckPara checkPara,
            string tempTblName, Dictionary<string, string> pkidMaps)
        {
            if (inventoryData == null || inventoryData.Count() == 0)
            {
                return new List<DynamicObject>();
            }

            var dbSvc = ctx.Container.GetService<IDBService>();

            //把当前要做校验的业务数据存贮到临时表
            var dm = ctx.Container.GetService<IDataManager>();
            dm.Option = OperateOption.Create();
            dm.Option.SetVariableValue("forceRecreate", true);
            dm.Option.SetAutoUpdateScheme(true);
            Dictionary<string, string> tableMaps = SaveBizBillData2TempTable(ctx, invFormMeta, inventoryData, dm, pkidMaps);

            //获取满足条件的数据到临时表
            SqlBuilderParameter para = new SqlBuilderParameter(ctx, invFormMeta);
            QueryObject queryObject = BuildQueryObject(ctx, invFormMeta, inventoryData, checkPara, tableMaps, para, pkidMaps);

            //拼sql语法
            var select = new StringBuilder();
            var currQueryMetaInfo = QueryService.GetHtmlFormQueryMetaInfo(ctx, invFormMeta);
            foreach (var queryFldItem in queryObject.QueryFlds)
            {
                var queryFldStr = queryFldItem.GetSelectString(currQueryMetaInfo, true);
                if (queryFldItem is QueryExprField)
                {
                    var yy = queryFldItem as QueryExprField;
                    queryFldStr = yy.GetSelectString(currQueryMetaInfo);
                }

                if (queryFldStr.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                var aliasName = checkPara.SrcFlds?.FirstOrDefault(f => queryFldItem.Key.EqualsIgnoreCase(f.SrcFieldId?.Trim()))?.Id?.Trim();
                if (select.Length > 0)
                {
                    select.Append(@",");
                }
                select.AppendLine(@"{0} {1}".Fmt(queryFldStr, aliasName.IsNullOrEmptyOrWhiteSpace() ? "" : aliasName));
            }
            if (invFormMeta.Id.EqualsIgnoreCase("ydj_order"))
            {
                select.Append(",t3.foutqty");
            }

            string sql = @" select {0}
                            into {1} 
                            {2}
                            {3}".Fmt(select, tempTblName, queryObject.SqlFrom, queryObject.SqlWhere);
            var dbSvcEx = ctx.Container.GetService<IDBServiceEx>();
            dbSvcEx.Execute(ctx, sql, para.DynamicParams);

            //套件的不管理库存，不做校验
            sql = @"select a.* from {0} a inner join t_bd_material b with(nolock) on a.fmaterialid=b.fid and b.fsuiteflag='0' ".Fmt(tempTblName);

            var datas = dbSvc.ExecuteDynamicObject(ctx, sql)?.ToList();

            //var ids = inventoryData.Select(f => Convert.ToString(f["Id"])).ToList();
            //var ids = pkidMaps.Keys.ToList();
            //dm.Delete(ids);
            // 改为异步删除
            //Task task = new Task(() =>
            //{
            //    var ids = pkidMaps.Keys.ToList();
            //    dm.Delete(ids);
            //});
            //ThreadWorker.QuequeTask(task, result =>
            //{
            //    if (result?.Exception != null)
            //    {
            //    }
            //});

            return datas;
        }


        /// <summary>
        /// 构建满足条件的查询对象
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="invFormMeta"></param>
        /// <param name="checkPara"></param>
        /// <param name="tableMaps"></param>
        /// <returns></returns>
        private static QueryObject BuildQueryObject(UserContext ctx, HtmlForm invFormMeta, IEnumerable<DynamicObject> inventoryData, BizInvCheckPara checkPara,
                                                    Dictionary<string, string> tableMaps, SqlBuilderParameter para, Dictionary<string, string> pkidMaps)
        {
            para.PageCount = 10000000;
            para.PageIndex = 1;
            para.NoIsolation = true;
            para.ReadDirty = true;

            //由于可以创建“数据范围”之外的单据，故此处查询需要跳过增加数据范围的筛选条件
            //举例：《库存调拨单》的数据范围为【发货部门】为本部门及下属部门时，可以创建发货部门为其他部门的单据
            para.EnableDataRowACL = false;

            para.SelectedFieldKeys.Add(invFormMeta.NumberFldKey);
            para.SelectedFieldKeys.Add("fsourcetype");
            if (invFormMeta.Id.EqualsIgnoreCase("ydj_order"))
            {
                para.SelectedFieldKeys.Add("fsourceinterid");
                para.SelectedFieldKeys.Add("fsourcenumber");
                para.SelectedFieldKeys.Add("fsourceentryid_e");
                para.SetFilter(new FilterRowObject()
                {
                    Id = "fisoutspot",//出现货
                    Operator = "=",
                    Value = "1",
                });
            }
            else
            {
                para.SelectedFieldKeys.Add("fsourceinterid");
                para.SelectedFieldKeys.Add("fsourcebillno");
                para.SelectedFieldKeys.Add("fsourceentryid");
            }

            var flds = checkPara.SrcFlds.Where(f => !f.Id.IsNullOrEmptyOrWhiteSpace()).ToList();
            foreach (var item in flds)
            {
                var str = item.Id.Trim();
                if (item.SrcFieldId.IsNullOrEmptyOrWhiteSpace())
                {
                    para.SelectedFieldKeys.Add(" ' ' as {1} ".Fmt(item.SrcFieldId.Trim(), str));
                }
                else
                {
                    var srcFldKey = item.SrcFieldId.SplitKey(@"+-*/");
                    foreach (var key in srcFldKey)
                    {
                        para.SelectedFieldKeys.Add(" {0} ".Fmt(key));
                    }
                }

            }

            if (checkPara.fcondition.IsNullOrEmptyOrWhiteSpace() == false)
            {
                var str = checkPara.fcondition;
                var index = checkPara.fcondition.IndexOf(",name:");
                if (index > 0)
                {
                    str = checkPara.fcondition.Substring(4, index - 4);
                }
                else
                {
                    System.Text.RegularExpressions.Regex.Unescape(checkPara.fcondition).Trim('"');
                }

                var filter = str.FromJson<List<FilterRowObject>>();
                if (filter.Any())
                {
                    para.SetFilter(filter.Where(f => f.Id.IsNullOrEmptyOrWhiteSpace() == false));
                }
            }

            //var ids = inventoryData.Select(f => "'{0}'".Fmt(Convert.ToString(f["Id"]))).ToList();
            var ids = pkidMaps.Keys.Select(f => "'{0}'".Fmt(f)).ToList();
            para.SetFilter(new FilterRowObject()
            {
                Id = "fid",
                Operator = "in",
                Value = string.Join(",", ids),
            });

            // 扩展：当业务单据为《库存调拨单》，【调出仓库】与【调入仓库】为同一仓库时，不校验可用量
            //if (invFormMeta.Id.EqualsIgnoreCase("stk_inventorytransfer") && checkPara.ReserveSource == 0)
            if (invFormMeta.Id.EqualsIgnoreCase("stk_inventorytransfer"))
            {
                if (checkPara.ReserveSource == 0)
                {
                    para.AppendFilterString("fstorehouseid<>fstorehouseidto");
                }
            }

            var queryObject = QueryService.BuilQueryObject(para);
            var xx = tableMaps.OrderByDescending(f => f.Key.Length).ToList();
            foreach (var tbl in xx)
            {
                queryObject.SqlFrom = queryObject.SqlFrom.Replace(tbl.Key, tbl.Value);
            }

            return queryObject;
        }


        /// <summary>
        /// 把当前要做校验的业务数据存贮到临时表
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="invFormMeta"></param>
        /// <param name="inventoryData"></param>
        /// <param name="dbSvc"></param>
        /// <returns></returns>
        private static Dictionary<string, string> SaveBizBillData2TempTable(UserContext ctx, HtmlForm invFormMeta, IEnumerable<DynamicObject> inventoryData,
                                                                            IDataManager dm, Dictionary<string, string> pkidMaps)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var tableMaps = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            var dt = HtmlParser.CloneFormModel(ctx, invFormMeta.Id, htmlEntity =>
            {
                if (htmlEntity.TableName.IsNullOrEmptyOrWhiteSpace())
                {
                    return "";
                }

                var temp = "tmp_" + htmlEntity.TableName.Substring(2);
                tableMaps[htmlEntity.TableName] = temp;

                return temp;
            });
            foreach (var item in invFormMeta.EntryList)
            {
                tableMaps[item.TableName] = "tmp_" + item.TableName.Substring(2);
            }

            var seqSrv = ctx.Container.GetService<IDataEntityPkService>();
            seqSrv.AutoSetPrimaryKey(ctx, inventoryData, invFormMeta.GetDynamicObjectType(ctx));

            var newDatas = new List<DynamicObject>();
            foreach (var oldBill in inventoryData)
            {
                var newBill = CopyPKID(ctx, invFormMeta, dt, oldBill, pkidMaps);

                newDatas.Add(newBill);
            }

            dm.InitDbContext(ctx, dt);

            //var ids = newDatas.Select(f => Convert.ToString(f["Id"])).ToList();
            //dm.Delete(ids);

            dm.Save(newDatas);

            dt = null;

            return tableMaps;
        }


        /// <summary>
        /// 拷贝数据并保持id一致
        /// </summary>
        /// <param name="invFormMeta"></param>
        /// <param name="dt"></param>
        /// <param name="oldBill"></param>
        /// <returns></returns>
        private static DynamicObject CopyPKID(UserContext ctx, HtmlForm invFormMeta, DynamicObjectType dt, DynamicObject oldBill, Dictionary<string, string> pkidMaps)
        {
            var seqSvc = ctx.Container.GetService<ISequenceService>();
            DynamicObject newBill = oldBill.Clone(dt, false, true) as DynamicObject;
            var newId = seqSvc.GetSequence<string>();
            pkidMaps[newId] = oldBill["Id"].ToString();
            newBill["Id"] = newId;
            var enInfos = invFormMeta.EntryList.Where(f => f is HtmlEntryEntity).ToList();
            foreach (var enInfo in enInfos)
            {
                if (enInfo is HtmlSubEntryEntity)
                {
                    continue;
                }
                var newEns = newBill[enInfo.PropertyName] as DynamicObjectCollection;
                var oldEns = oldBill[enInfo.PropertyName] as DynamicObjectCollection;
                if (newEns == null || oldEns == null)
                {
                    continue;
                }

                var subEnInfos = enInfo.SubEntryList;
                for (int i = 0; i < newEns.Count; i++)
                {
                    newId = seqSvc.GetSequence<string>();
                    newEns[i]["Id"] = newId;// oldEns[i]["Id"];

                    if (subEnInfos == null || subEnInfos.Count == 0)
                    {
                        continue;
                    }

                    foreach (var subEnInfo in subEnInfos)
                    {
                        var newSubEns = newEns[i][subEnInfo.PropertyName] as DynamicObjectCollection;
                        var oldSubEns = oldEns[i][subEnInfo.PropertyName] as DynamicObjectCollection;
                        if (newSubEns == null || oldSubEns == null)
                        {
                            continue;
                        }

                        for (int j = 0; j < newSubEns.Count; j++)
                        {
                            newId = seqSvc.GetSequence<string>();
                            newSubEns[j]["Id"] = newId;// oldSubEns[j]["Id"];
                        }
                    }
                }

            }

            return newBill;
        }


        /// <summary>
        /// 取即时库存数据，取数时先不考虑物流跟踪号及仓库信息（比如业务单据上没有物流跟踪号，则认为可以匹配所有物流跟踪号）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tembTableName"></param>
        /// <returns></returns>
        private List<DynamicObject> GetInvQtyDatas(UserContext ctx, string tembTableName)
        {
            var sql = @"
select distinct a.fid,a.fmaterialid,a.fattrinfo,a.fattrinfo_e,a.fcustomdesc,a.fmtono,a.funitid,a.fstorehouseid,a.fstorelocationid,a.fstockstatus,a.fqty
from t_stk_inventorylist a  with(nolock) 
inner join {0} b with(nolock) on a.fmaterialid = b.fmaterialid 
    and  a.fattrinfo_e  = b.fattrinfo_e
    and a.fcustomdesc = b.fcustomdesc 
    and a.funitid = b.funitid
where a.fmainorgid=@fmainorgid and a.fqty>0
                              ".Fmt(tembTableName);

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, ctx.Company),
            };

            var dbSvc = ctx.Container.GetService<IDBService>();
            var datas = dbSvc.ExecuteDynamicObject(ctx, sql, sqlParam)?.ToList();

            return datas;
        }

        /// <summary>
        /// 取预留数据，取数时先不考虑物流跟踪号及仓库信息（比如业务单据上没有物流跟踪号，则认为可以匹配所有物流跟踪号）
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tembTableName"></param>
        /// <returns></returns>
        private List<DynamicObject> GetReserveQtyDatas(UserContext ctx, BizInvCheckPara checkPara, string tembTableName)
        {
            if (!checkPara.freserveqty)
            {
                return new List<DynamicObject>();
            }

            var dbSvc = ctx.Container.GetService<IDBService>();

            // 预留来源：预留单
            if (checkPara.ReserveSource == 0)
            {
                // 增加跟踪明细行id，避免去重问题
                var sql = @"
select distinct d.fdetailid, re.fsourceformid,re.fsourceinterid,re.fsourceentryid,r.fsourcenumber ,re.fmaterialid
    ,re.fattrinfo,re.fattrinfo_e,re.fcustomdesc,re.funitid, re.fmtono,d.fstorehouseid,d.fstockstatus,d.fopdesc,d.freservenote,d.fnextreservepkid,
    case d.fdirection_d when '0' then d.fqty_d when '1' then d.fqty_d*-1 else 0 end as fqty 
from t_stk_reservebill r with(nolock) 
inner join t_stk_reservebillentry re  with(nolock) on re.fid=r.fid  and re.fqty >0 
inner join t_stk_reservebilldetail d with(nolock)  on re.fentryid=d.fentryid 
inner join {0} b with(nolock) on re.fmaterialid = b.fmaterialid 
    and re.fattrinfo_e  = b.fattrinfo_e
    and re.fcustomdesc = b.fcustomdesc 
    and re.funitid = b.funitid
where r.fmainorgid=@fmainorgid and r.fstatus='E' and r.fcancelstatus='0' 
".Fmt(tembTableName);

                var sqlParam = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, ctx.Company),
                };

                var datas = dbSvc.ExecuteDynamicObject(ctx, sql, sqlParam).ToList();

                return datas;
            }
            else if (checkPara.ReserveSource == 1)
            {
                string whereString = $@" and exists(select 1 from {tembTableName} tmp left join t_bd_auxpropvalue as att with(nolock) on att.fid = te.fattrinfo where tmp.fmaterialid=te.fmaterialid and tmp.fattrinfo_e = te.fattrinfo_e and tmp.fcustomdesc=te.fcustomdesc and tmp.funitid=te.funitid and tmp.fstorehouseid=te.fstorehouseid)";

                var sql = $@"
    -- 销售出库单
    select te.fmaterialid, te.fattrinfo, te.fattrinfo_e, te.fcustomdesc, te.funitid, te.fmtono, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fentryid, te.fid
    from t_stk_sostockout t with(nolock)
    inner join t_stk_sostockoutentry te with(nolock) on t.fid=te.fid
    where t.fmainorgid=@fmainorgid and t.fstatus<>'E' and t.fcancelstatus='0' {whereString}
    union all
    -- 其它出库单
    select te.fmaterialid, te.fattrinfo, te.fattrinfo_e, te.fcustomdesc, te.funitid, te.fmtono, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fentryid, te.fid
    from t_stk_otherstockout t with(nolock)
    inner join t_stk_otherstockoutentry te with(nolock) on t.fid=te.fid
    where t.fmainorgid=@fmainorgid and t.fstatus<>'E' and t.fcancelstatus='0' {whereString}
    union all
    -- 库存调拨单
    select te.fmaterialid, te.fattrinfo, te.fattrinfo_e, te.fcustomdesc, te.funitid, te.fmtono, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fentryid, te.fid
    from t_stk_invtransfer t with(nolock)
    inner join t_stk_invtransferentry te with(nolock) on t.fid=te.fid
    where t.fmainorgid=@fmainorgid and (t.fstatus in ('A', 'B', 'C') or (t.fstatus = 'D' and t.fisstockout='0')) and t.fcancelstatus='0' {whereString}
    union all
    -- 采购退货单
    select te.fmaterialid, te.fattrinfo, te.fattrinfo_e, te.fcustomdesc, te.funitid, te.fmtono, te.fstorehouseid, te.fstorelocationid, te.fstockstatus, te.fqty, te.fentryid, te.fid
    from t_stk_postockreturn t with(nolock)
    inner join t_stk_postockreturnentry te with(nolock) on t.fid=te.fid
    where t.fmainorgid=@fmainorgid and t.fstatus<>'E' and t.fcancelstatus='0' {whereString}
";

                var sqlParam = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, ctx.Company),
                };

                var datas = dbSvc.ExecuteDynamicObject(ctx, sql, sqlParam).ToList();

                return datas;
            }
            else
            {
                throw new BusinessException($"未实现预留来源={checkPara.ReserveSource}");
            }
        }


        /// <summary>
        /// 获取在途数量。
        /// 在途量包括未审核的其它入库单、其他出库单、销售退货单、调拨入库单，以及已审核的采购订单的（采购数量-采购入库数量-采购退款数量）+采购退换数量
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="tembTableName"></param>
        /// <returns></returns>
        private List<DynamicObject> GetOnWayQtyDatas(UserContext ctx, BizInvCheckPara checkPara, string tembTableName)
        {
            if (checkPara.fonwaybill == null || checkPara.fonwaybill.Id.IsNullOrEmptyOrWhiteSpace())
            {
                return new List<DynamicObject>();
            }

            var lstSql = new List<string>();
            var formId = checkPara.fonwaybill.Id.SplitKey().Distinct().ToList();
            foreach (var item in formId)
            {
                switch (item.ToLowerInvariant())
                {
                    case "stk_otherstockin":
                        lstSql.Add(@"
/*其它入库单*/
select 'stk_otherstockin' as fformid,r.fid,r.fbillno,e.fentryid, e.fmaterialid,e.fattrinfo,e.fattrinfo_e,e.fcustomdesc
    ,e.fmtono,e.funitid, e.fstorehouseid,e.fstockstatus,isnull(e.fqty ,0) as fqty
from t_stk_otherstockin r with(nolock) 
inner join t_stk_otherstockinentry e with(nolock) on e.fid=r.fid  
inner join {0} b on e.fmaterialid = b.fmaterialid 
    and e.fattrinfo_e  = b.fattrinfo_e
    and e.fcustomdesc = b.fcustomdesc 
    and e.funitid = b.funitid
where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
                                    ".Fmt(tembTableName));
                        break;
                    case "stk_sostockreturn":
                        lstSql.Add(@"
/*销售退货单*/
select 'stk_sostockreturn' as fformid,r.fid,r.fbillno,e.fentryid,e.fmaterialid,e.fattrinfo,e.fattrinfo_e,e.fcustomdesc
    ,e.fmtono,e.funitid, e.fstorehouseid,e.fstockstatus,isnull(e.fqty ,0) as fqty
from t_stk_sostockreturn r with(nolock) 
inner join t_stk_sostockreturnentry e with(nolock) on e.fid=r.fid  
inner join {0} b on e.fmaterialid = b.fmaterialid 
    and e.fattrinfo_e  = b.fattrinfo_e
    and e.fcustomdesc = b.fcustomdesc 
    and e.funitid = b.funitid
where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
".Fmt(tembTableName));
                        break;
                    case "stk_inventorytransfer":
                        lstSql.Add(@"
/*调拨入库*/
select 'stk_inventorytransfer' as fformid,r.fid,r.fbillno,e.fentryid,e.fmaterialid,e.fattrinfo,e.fattrinfo_e,e.fcustomdesc
    ,e.fmtono,e.funitid,e.fstorehouseidto as fstorehouseid,e.fstockstatusto as fstockstatus,isnull(e.fqty ,0)as fqty
from t_stk_invtransfer r with(nolock) 
inner join t_stk_invtransferentry e with(nolock) on e.fid=r.fid  
inner join {0} b on e.fmaterialid = b.fmaterialid 
    and e.fattrinfo_e  = b.fattrinfo_e
    and e.fcustomdesc = b.fcustomdesc 
    and e.funitid = b.funitid
where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 		            
union all
/*调拨出库*/
select 'stk_inventorytransfer' as fformid,r.fid,r.fbillno,e.fentryid,e.fmaterialid,e.fattrinfo,e.fattrinfo_e,e.fcustomdesc
    ,e.fmtono,e.funitid,e.fstorehouseid,e.fstockstatus,isnull(e.fqty ,0) * -1 as fqty
from t_stk_invtransfer r with(nolock) 
inner join t_stk_invtransferentry e with(nolock) on e.fid=r.fid  
inner join {0} b on e.fmaterialid = b.fmaterialid 
    and e.fattrinfo_e  = b.fattrinfo_e
    and e.fcustomdesc = b.fcustomdesc 
    and e.funitid = b.funitid
where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
".Fmt(tembTableName));
                        break;
                    case "stk_otherstockout":
                        lstSql.Add(@"
/*其它入库单*/
select 'stk_otherstockout' as fformid,r.fid,r.fbillno, e.fentryid, e.fmaterialid,e.fattrinfo,e.fattrinfo_e,e.fcustomdesc
    ,e.fmtono,e.funitid,e.fstorehouseid,e.fstockstatus,isnull(e.fqty ,0) * -1 as fqty
from t_stk_otherstockout r with(nolock) 
inner join t_stk_otherstockoutentry e with(nolock) on e.fid=r.fid  
inner join {0} b on e.fmaterialid = b.fmaterialid 
    and e.fattrinfo_e  = b.fattrinfo_e  
    and e.fcustomdesc = b.fcustomdesc 
    and e.funitid = b.funitid
where r.fmainorgid=@fmainorgid and r.fstatus<>'E' and r.fcancelstatus='0' 
".Fmt(tembTableName));
                        break;
                    case "ydj_purchaseorder":
                        lstSql.Add(@"
/*已审核的采购订单：采购数量 - 采购入库数量 - 采购退款数量 + 采购退换数量*/
select 'ydj_purchaseorder' as fformid,r.fid,r.fbillno,e.fentryid,e.fmaterialid,e.fattrinfoe.fattrinfo_e,
    ,e.fcustomdes_e as fcustomdesc,e.fmtono,e.funitid,'' as fstorehouseid, ' ' as fstockstatus
    ,isnull(e.fqty,0)-isnull(e.finstockqty,0)-isnull(e.frefundqty,0)+isnull(e.freturnqty,0) as fqty
from t_ydj_purchaseorder r with(nolock) 
inner join t_ydj_poorderentry e with(nolock) on e.fid=r.fid  
inner join {0} b on e.fmaterialid = b.fmaterialid 
    and e.fattrinfo_e  = b.fattrinfo_e
    and e.fcustomdes_e = b.fcustomdesc 
    and e.funitid = b.funitid
where r.fmainorgid=@fmainorgid and r.fstatus='E' and r.fcancelstatus='0' 
    and (e.fclosestatus='0' or e.fclosestatus='') 
".Fmt(tembTableName));
                        break;
                    default:
                        break;
                }
            }

            if (lstSql.Count == 0)
            {
                return new List<DynamicObject>();
            }
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, ctx.Company),
            };

            var dbSvc = ctx.Container.GetService<IDBService>();
            var datas = dbSvc.ExecuteDynamicObjectByUnion(ctx, lstSql, sqlParam)?.ToList();
            if (datas == null)
            {
                return new List<DynamicObject>();
            }

            return datas;
        }



        private void SetCheckResultValue(BizInvCheckResult checkResult, string fldKey, string value)
        {
            switch (fldKey)
            {
                case "fmaterialid":
                    checkResult.fmaterialid = value;
                    break;
                case "fattrinfo":
                    checkResult.fattrinfo = value;
                    break;
                case "fattrinfo_e":
                    checkResult.fattrinfo_e = value;
                    break;
                case "fcustomdesc":
                    checkResult.fcustomdesc = value;
                    break;
                case "funitid":
                    checkResult.funitid = value;
                    break;
                case "fmtono":
                    checkResult.fmtono = value;
                    break;
                case "fstorehouseid":
                    checkResult.fstorehouseid = value;
                    break;
                case "fstorelocationid":
                    checkResult.fstorelocationid = value;
                    break;
                case "fstockstatus":
                    checkResult.fstockstatus = value;
                    break;

            }
        }



        /// <summary>
        /// 取转移的预留量：比如销售合同下推销售出库单时，会把预留转移到销售出库单上，但是这个时候还没有进行预留转移，这里把即将转移的预留量计算出来
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="checkPara"></param>
        /// <param name="billDatas"></param>
        /// <param name="inventoryData"></param>
        /// <returns></returns>
        private List<DynamicObject> GetReserveQtyTransferDatas(UserContext ctx, HtmlForm invFormMeta, BizInvCheckPara checkPara, List<DynamicObject> billDatas, IEnumerable<DynamicObject> inventoryData)
        {
            var result = new List<DynamicObject>();

            var preBillReserveBills = GetPreReserveBillInfo(ctx, invFormMeta, inventoryData);
            if (preBillReserveBills == null || preBillReserveBills.Count == 0)
            {
                return result;
            }

            var reserveBillForm = ctx.Container.GetService<IMetaModelService>().LoadFormModel(ctx, "stk_reservebill");
            var preBillSet = new ExtendedDataEntitySet();
            preBillSet.Parse(ctx, preBillReserveBills, reserveBillForm);
            var preReserveEnRows = preBillSet.FindByEntityKey("fdetail");

            var qtyFldKey = "";
            var enKey = GetEntryKey(ctx, invFormMeta, checkPara, out qtyFldKey);
            var enPKFldKey = "{0}_id".Fmt(enKey);
            var invEntitySet = new ExtendedDataEntitySet();
            invEntitySet.Parse(ctx, inventoryData, invFormMeta);
            var allInvEnRows = invEntitySet.FindByEntityKey(enKey);

            foreach (var currentRow in billDatas)
            {
                //检查上游单据是否有预留，有预留才转移，上游单无预留，不需要管
                var billNo = Convert.ToString(currentRow[invFormMeta.NumberFldKey]);
                var exist = preReserveEnRows.Where(f => f.DataEntity != null && Convert.ToString(f.DataEntity["fopdesc"]) == "3"
                                                    && Convert.ToString(f["freservenote"]).IndexOf(billNo, StringComparison.OrdinalIgnoreCase) > -1).ToList();
                if (!exist.Any())
                {
                    continue;
                }

                var preBillEnId = Convert.ToString(currentRow["fsourceentryid"]);//上游单据行id
                var demandRows = exist.Select(f => f.DataEntity.Parent as DynamicObject).ToList();
                if (!demandRows.Any(f => Convert.ToString(f["fsourceentryid"]).EqualsIgnoreCase(preBillEnId)))
                {
                    continue;
                }

                var currentEntryId = Convert.ToString(currentRow[enPKFldKey]);
                var enRow = allInvEnRows.FirstOrDefault(f => currentEntryId.EqualsIgnoreCase(Convert.ToString(f["Id"]))).DataEntity;
                if (enRow == null)
                {
                    continue;
                }

                var xx = GetTransferQtyInfo(currentRow, enRow, qtyFldKey);
                if (xx != null)
                {
                    result.Add(xx);
                }
            }

            return result;
        }



        private string GetEntryKey(UserContext ctx, HtmlForm invFormMeta, BizInvCheckPara checkPara, out string qtyFldKey)
        {
            qtyFldKey = checkPara.SrcFlds?.FirstOrDefault(f => f.Id.EqualsIgnoreCase("fmaterialid"))?.SrcFieldId;
            if (qtyFldKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new Exception("参数错误，商品字段标识不存在");
            }

            var fld = invFormMeta.GetField(qtyFldKey);
            if (fld == null)
            {
                throw new Exception("参数错误，商品字段标识不存在");
            }

            var enMeta = invFormMeta.GetEntity(fld.EntityKey);
            if (enMeta == null)
            {
                throw new Exception("参数错误，商品字段标识不存在");
            }

            return enMeta.Id;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="result"></param>
        /// <param name="qtyFldKey"></param>
        /// <param name="currentRow">当前单据行信息</param>
        /// <param name="oldRow">原始数据行信息</param>
        private static DynamicObject GetTransferQtyInfo(DynamicObject currentRow, DynamicObject oldRow, string qtyFldKey)
        {
            if (oldRow.DataEntityState.FromDatabase)
            {
                //修改的行，取差量
                var xx = currentRow.Clone(false, false) as DynamicObject;
                var snapShot = oldRow.GetDataEntitySnapshot();
                if (snapShot != null && snapShot.ContainsKey(qtyFldKey))
                {
                    var oldValue = Convert.ToDecimal(snapShot[qtyFldKey].InitialValue);
                    var newValue = Convert.ToDecimal(oldRow[qtyFldKey]);
                    xx["fqty"] = newValue - oldValue;

                    return xx;
                }
            }
            else
            {
                //新增的行，取当前值
                var xx = currentRow.Clone(false, false) as DynamicObject;

                return xx;
            }

            return null;
        }



        /// <summary>
        /// 获取上游单据的预留单信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="invFormMeta"></param>
        /// <param name="inventoryData"></param>
        /// <returns></returns>
        private List<DynamicObject> GetPreReserveBillInfo(UserContext ctx, HtmlForm invFormMeta, IEnumerable<DynamicObject> inventoryData)
        {
            var result = new List<DynamicObject>();
            var sourceBillMeta = GetSourceFormMeta(ctx, inventoryData);
            if (sourceBillMeta == null)
            {
                return result;
            }

            var needTransfer = CheckNeedTransferBiz(ctx, invFormMeta, sourceBillMeta);
            if (needTransfer == false)
            {
                return result;
            }

            //获取上游单据信息
            var preBillDatas = GetSourceBillDatas(ctx, invFormMeta, sourceBillMeta, inventoryData);
            if (preBillDatas == null || preBillDatas.Count == 0)
            {
                return result;
            }

            //获取上游单据对应的预留单
            result = ReserveUtil.GetReserveBillData(ctx, sourceBillMeta, preBillDatas);

            return result;
        }




        private void SetCheckFldMapInfo(UserContext ctx, HtmlForm invFormMeta, BizInvCheckPara setting)
        {
            var profileService = ctx.Container.GetService<ISystemProfile>();
            var fallowsalescontrol = profileService.GetSystemParameter(ctx, "stk_stockparam", "fallowsalescontrol", false);
            setting.AllowsalesControl = fallowsalescontrol;

            switch (invFormMeta.Id)
            {
                case "ydj_saleintention":
                    setting.SrcFlds = new List<FieldMapObject>() {
                    new FieldMapObject(){Id="fmaterialid",Name="商品", MapType=0,SrcFieldId="fmaterialid" },
                    new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0, SrcFieldId="fattrinfo"},
                    new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                    new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0, SrcFieldId="fcustomdes_e"},
                    new FieldMapObject(){Id="funitid",Name="基本单位", MapType=0,SrcFieldId="funitid"},
                    new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0, SrcFieldId="fbizunitid"},
                    new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1, SrcFieldId="fqty"},
                    new FieldMapObject(){Id="fmtono",Name="物流跟踪号", MapType=0,SrcFieldId="fmtono"},
                    new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                    new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                    };
                    break;

                case "ydj_order":
                    setting.SrcFlds = new List<FieldMapObject>() {
                     new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fproductid"},
                     new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                     new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                     new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdes_e"},
                     new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                     new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fbizunitid"},
                     new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1,SrcFieldId="fqty+freturnqty"},
                     new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                     new FieldMapObject(){Id="fclosestatus",Name="行关闭状态",MapType=0,SrcFieldId="fclosestatus_e"},
                     new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                     new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                    new FieldMapObject(){Id="fstaffid",Name="销售员", MapType=0,SrcFieldId="fstaffid"},
                    new FieldMapObject(){Id="fstaffid_e",Name="销售人员", MapType=0,SrcFieldId="fstaff_e"},
                    };
                    break;
                case "stk_sostockout":
                    setting.SrcFlds = new List<FieldMapObject>() {
                     new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                     new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                     new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                     new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                     new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                     new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                     new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                     new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                     new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                     new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                    new FieldMapObject(){Id="fstaffid",Name="仓管员", MapType=0,SrcFieldId="fstockstaffid"},

                    };
                    break;
                case "stk_sostockreturn":// 销售退货单
                case "stk_postockin":// 采购入库单
                case "stk_postockreturn":// 采购退货单
                case "stk_otherstockin":// 其它入库单
                case "stk_otherstockinreq": // 其它入库通知单
                case "stk_otherstockout": // 其它出库单
                case "stk_otherstockoutreq": // 其它出库通知单
                    setting.SrcFlds = new List<FieldMapObject>() {
                     new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                     new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                     new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                     new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                     new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                     new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                     new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                     new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                     new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                     new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                    };
                    break;
                case "sal_deliverynotice":
                    setting.SrcFlds = new List<FieldMapObject>() {
                     new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                     new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                     new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                     new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                     new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                     new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                     new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                     new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                     new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                     new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},

                    };
                    break;
                case "stk_inventorytransfer":// 库存调拨单
                    if (setting.OperationNo.EqualsIgnoreCase("unaudit"))
                    {
                        setting.SrcFlds = new List<FieldMapObject>() {
                            new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                            new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfoto"},
                            new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                            new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcallupcustomdescto"},
                            new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                            new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                            new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                            new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtonoto"},
                            new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseidto"},
                            new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationidto"},
                            new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatusto"},
                        };
                    }
                    else
                    {
                        setting.SrcFlds = new List<FieldMapObject>() {
                            new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                            new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                            new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                            new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                            new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                            new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                            new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1,SrcFieldId="fqty"},
                            new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                            new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                            new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                            new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},
                        };
                    }

                    break;
                case "stk_inventorytransferreq":// 库存调拨通知单
                    setting.SrcFlds = new List<FieldMapObject>() {
                     new FieldMapObject(){Id="fmaterialid",Name="商品",MapType=0,SrcFieldId="fmaterialid"},
                     new FieldMapObject(){Id="fattrinfo",Name="辅助属性",MapType=0,SrcFieldId="fattrinfo"},
                     new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                     new FieldMapObject(){Id="fcustomdesc",Name="定制说明",MapType=0,SrcFieldId="fcustomdesc"},
                     new FieldMapObject(){Id="funitid",Name="基本单位",MapType=0,SrcFieldId="funitid"},
                     new FieldMapObject(){Id="fbizunitid",Name="库存单位",MapType=0,SrcFieldId="fstockunitid"},
                     new FieldMapObject(){Id="fqty",Name="需求数量",MapType=1,SrcFieldId="fplanqty"},
                     new FieldMapObject(){Id="fmtono",Name="物流跟踪号",MapType=0,SrcFieldId="fmtono"},
                     new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                     new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                    new FieldMapObject(){Id="fstockstatus",Name="库存状态", MapType=0,SrcFieldId="fstockstatus"},

                    };
                    break;
                case "stk_inventoryverify":// 盘点单
                    if (setting.OperationNo.EqualsIgnoreCase("unaudit"))
                    {
                        setting.SrcFlds = new List<FieldMapObject>() {
                            new FieldMapObject(){Id = "fmaterialid", Name = "商品", MapType = 0, SrcFieldId = "fmaterialid"},
                            new FieldMapObject(){Id = "fattrinfo", Name = "辅助属性", MapType = 0, SrcFieldId = "fattrinfo"},
                            new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                            new FieldMapObject(){Id = "fcustomdesc", Name = "定制说明", MapType = 0, SrcFieldId = "fcustomdesc"},
                            new FieldMapObject(){Id = "funitid", Name = "基本单位", MapType = 0, SrcFieldId = "funitid"},
                            new FieldMapObject(){Id = "fbizunitid", Name = "库存单位", MapType = 0, SrcFieldId = "fstockunitid"},
                            // 使用盘盈计算
                            new FieldMapObject(){Id = "fqty", Name = "需求数量", MapType = 1, SrcFieldId = "fpyqty"},
                            new FieldMapObject(){Id = "fmtono", Name = "物流跟踪号", MapType = 0, SrcFieldId = "fmtono"},
                            new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                            new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                            new FieldMapObject(){Id = "fstockstatus", Name = "库存状态", MapType = 0,SrcFieldId = "fstockstatus"}
                        };
                    }
                    else
                    {
                        setting.SrcFlds = new List<FieldMapObject>() {
                            new FieldMapObject(){Id = "fmaterialid", Name = "商品", MapType = 0, SrcFieldId = "fmaterialid"},
                            new FieldMapObject(){Id = "fattrinfo", Name = "辅助属性", MapType = 0, SrcFieldId = "fattrinfo"},
                            new FieldMapObject(){Id = "fattrinfo_e", Name = "辅助属性扩展", MapType = 0, SrcFieldId = "fattrinfo_e"},
                            new FieldMapObject(){Id = "fcustomdesc", Name = "定制说明", MapType = 0, SrcFieldId = "fcustomdesc"},
                            new FieldMapObject(){Id = "funitid", Name = "基本单位", MapType = 0, SrcFieldId = "funitid"},
                            new FieldMapObject(){Id = "fbizunitid", Name = "库存单位", MapType = 0, SrcFieldId = "fstockunitid"},
                            // 使用盘亏计算
                            new FieldMapObject(){Id = "fqty", Name = "需求数量", MapType = 1, SrcFieldId = "fpkqty"},
                            new FieldMapObject(){Id = "fmtono", Name = "物流跟踪号", MapType = 0, SrcFieldId = "fmtono"},
                            new FieldMapObject(){Id="fstorehouseid",Name="仓库", MapType=0,SrcFieldId="fstorehouseid"},
                            new FieldMapObject(){Id="fstorelocationid",Name="仓位", MapType=0,SrcFieldId="fstorelocationid"},
                            new FieldMapObject(){Id = "fstockstatus", Name = "库存状态", MapType = 0,SrcFieldId = "fstockstatus"}
                        };
                    }
                    break;
                default:
                    throw new BusinessException("该业务暂不支持库存校验！");
            }

        }





        /// <summary>
        /// 库存校验调试信息
        /// </summary>
        internal class BizCheckInvDebugInfo
        {
            public BizInvCheckPara CheckPara { get; set; }

            public List<DynamicObject> BillDatas { get; set; }

            /// <summary>
            /// 取及时库存数据
            /// </summary>
            public List<DynamicObject> InvQtyDatas { get; set; }

            /// <summary>
            /// 取预留数据
            /// </summary>
            public List<DynamicObject> ReserveQtyDatas { get; set; }

            /// <summary>
            /// 取在途数据
            /// </summary>
            public List<DynamicObject> OnWayQtyDatas { get; set; }


            public Dictionary<string, string> StockStatusMap { get; set; }

            public List<Tuple<string, string, string>> CtrlStkDatas { get; set; }

            public string[] GroupByFlds { get; set; }

            public List<BizCheckInvDebugItemInfo> Items { get; set; } = new List<BizCheckInvDebugItemInfo>();



            internal class BizCheckInvDebugItemInfo
            {
                public List<DynamicObject> InvDatas { get; set; }

                public List<DynamicObject> ReserveDatas { get; set; }

                public List<DynamicObject> OnWayDatas { get; set; }

                public List<DynamicObject> FilterByCtrlInvFlexItem_InvDatas { get; set; }

                public List<DynamicObject> FilterByCtrlInvFlexItem_ReserveDatas { get; set; }

                public List<DynamicObject> FilterByCtrlInvFlexItem_OnWayDatas { get; set; }

                public List<DynamicObject> FilterByCtrlStock_InvDatas { get; set; }

                public List<DynamicObject> FilterByCtrlStock_ReserveDatas { get; set; }

                public List<DynamicObject> FilterByCtrlStock_OnWayDatas { get; set; }

                public BizInvCheckResult CheckResult { get; set; }
            }
        }

    }






}