using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBill
{
    /// <summary>
    /// 预留单：保存
    /// </summary>
    [InjectService]
    [FormId("stk_reservebill")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if(e.DataEntitys ==null || e.DataEntitys.Length ==0)
            {
                return;
            }

            ReserveUtil.UpdateOrderReserveQty(this.Context , e.DataEntitys); 
        }



        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            //由于历史库存维度的辅助属性不正确，可能会对销售合同的辅助属性做修复，导致预留跟合同不一致，这里修复
            ThreadWorker.QuequeTask(() =>
            {
                var ctx = this.Context;
                var svc = this.Container.GetService<IDBServiceEx>();
                var sql = @"/*dialect*/ update t set fattrinfo=x.fattrinfo
                        from t_stk_reservebillentry as t 
					    inner join (
									select   d.fname,c.fcreatedate, c.fbillno,c.fsourcenumber ,a.fattrinfo,b.fentryid
									from t_ydj_orderentry a
									inner join t_stk_reservebillentry b on a.fentryid=b.fsourceentryid  and a.fid=b.fsourceinterid
									inner join t_stk_reservebill c on b.fid=c.fid
									inner join T_BAS_ORGANIZATION d on c.fmainorgid=d.fid
									where c.fsourcetype='ydj_order'  and  a.fattrinfo <> b.fattrinfo 
									) x on t.fentryid = x.fentryid";
                svc.Execute(ctx, sql);
            },
            (asynResult) =>
            {
                asynResult.HandleError();
            });

            
        }

    }




    /// <summary>
    /// 预留单：保存
    /// </summary>
    [InjectService]
    [FormId("stk_reservebill")]
    [OperationNo("draft")]
    public class Draft : AbstractOperationServicePlugIn
    {
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            ReserveUtil.UpdateOrderReserveQty(this.Context, e.DataEntitys);
        }

    }
}