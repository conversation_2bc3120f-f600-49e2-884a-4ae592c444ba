using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveBill
{
    /// <summary>
    /// 预留单：根据库存维度匹配预留单，并且打开预留单列表页面，列表中显示匹配到的预留单
    /// 如果匹配到多个则打开列表页面，如果只匹配到一个则打开编辑页面
    /// </summary>
    [InjectService]
    [FormId("stk_reservebill")]
    [OperationNo("showreservebyinvflex")]
    public class ShowReserveByInvFlex : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var fmaterialid = this.GetQueryOrSimpleParam<string>("fmaterialid");
            var fattrinfo = this.GetQueryOrSimpleParam<string>("fattrinfo");
            var fattrinfo_e = this.GetQueryOrSimpleParam<string>("fattrinfo_e");
            var fcustomdesc = this.GetQueryOrSimpleParam<string>("fcustomdesc");
            var fstorehouseid = this.GetQueryOrSimpleParam<string>("fstorehouseid");
            //var fstorelocationid = this.GetQueryOrSimpleParam<string>("fstorelocationid");
            var fstockstatus = this.GetQueryOrSimpleParam<string>("fstockstatus");
            var fmtono = this.GetQueryOrSimpleParam<string>("fmtono");
            var funitid = this.GetQueryOrSimpleParam<string>("funitid");

            //先对表结构进行构建，以免后面执行sql语句报某个表不存在的错误
            var inventoryForm = this.MetaModelService.LoadFormModel(this.Context, "stk_inventorylist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, inventoryForm.GetDynamicObjectType(this.Context));

            var sqlText = $@"
select distinct r.fid, r.fsourcetype, r.fsourcepkid from t_stk_reservebill r  
inner join t_stk_reservebillentry re on re.fid=r.fid 
inner join (
    select fentryid, fstorehouseid, fstockstatus, SUM(case when fdirection_d=0 then fqty_d else fqty_d * -1 end) fqty_d 
    from t_stk_reservebilldetail rd with(nolock)
    group by fentryid, fstorehouseid, fstockstatus
    having SUM(case when fdirection_d=0 then fqty_d else fqty_d * -1 end)>0
) rd on re.fentryid=rd.fentryid 
where r.fmainorgid=@fmainorgid and re.fqty >0 and r.fstatus='E' and r.fcancelstatus='0' 
    and re.fmaterialid='{fmaterialid}' 
    --and re.fattrinfo='{fattrinfo}' 
    and re.fattrinfo_e ='{fattrinfo_e}' 
    and re.fcustomdesc='{fcustomdesc}' and re.funitid='{funitid}'
    and (re.fmtono='{fmtono}' or re.fmtono='') ";

            if (!fstorehouseid.IsNullOrEmptyOrWhiteSpace())
            {
                sqlText += $" and rd.fstorehouseid='{fstorehouseid}'";
                sqlText += $" and rd.fstockstatus='{fstockstatus}'";
            }

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var reserveBills = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParam);

            var srvData = reserveBills.Select(s => new
            {
                fid = Convert.ToString(s["fid"]),
                fsourcetype = Convert.ToString(s["fsourcetype"]),
                fsourcepkid = Convert.ToString(s["fsourcepkid"])
            }).ToList();

            this.Result.IsSuccess = true;
            this.Result.SrvData = srvData;
        }
    }
}