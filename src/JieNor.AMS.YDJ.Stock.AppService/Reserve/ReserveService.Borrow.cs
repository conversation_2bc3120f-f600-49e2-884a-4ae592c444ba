using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 释放借货
    /// </summary>
    public partial class ReserveService : IReserveBorrowService
    {


        /// <summary>
        /// 释放借货
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="demandForm"></param>
        /// <param name="settingInfos"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult Borrow(UserContext userCtx, HtmlForm demandForm, List<ReserveBorrowSettingInfo> settingInfos, OperateOption option)
        {
            Initialize(userCtx);

            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            /*
             * 0、借出货预留单的源单必须是销售合同
             * 1、借入合同——确认借货数量
             * 2、借出预留单——部分释放
             * 3、借入合同——预留
             * 4、借出货预留单关联的销售合同——添加《预留转移记录》
             * 5、借入合同——添加《预留转移记录》
             */

            if (!demandForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                result.ComplexMessage.ErrorMessages.Add("借出的需求单必须是销售合同");
                return result;
            }

            var allDemandInfos = settingInfos.SelectMany(s => s.DemandInfos).ToList();
            if (allDemandInfos.Count == 0)
            {
                result.ComplexMessage.ErrorMessages.Add("没有需要借出的需求明细行");
                return result;
            }

            ISequenceService sequenceService = userCtx.Container.GetService<ISequenceService>();

            var reserveTransferEntity = demandForm.GetEntity("freservetransfer");
            var reserveTransferDt = reserveTransferEntity.DynamicObjectType;

            var borrowDemandEntryQtys = GetBorrowDemandEntryQtys(userCtx, settingInfos);

            // 借入合同--商品明细分组
            var borrowGrps = allDemandInfos.GroupBy(s => new
            {
                s.BorrowerDemandBillId,
                s.BorrowerDemandBillNo,
                s.BorrowerDemandEntryId
            }).ToList();

            // 借入需求单
            var borrowerDemandBillIds = allDemandInfos.Select(s => s.BorrowerDemandBillId).Distinct().ToList();
            var borrowerDemandBills = userCtx.LoadBizDataById(demandForm.Id, borrowerDemandBillIds);
            // 借入预留单
            var borrowerReserveBills = ReserveUtil.GetReserveBillData(userCtx, demandForm, borrowerDemandBillIds);
            // 借出预留单
            var creditorReserveBills = settingInfos.Select(s => s.CreditorReserveBill as DynamicObject).ToList();
            // 借出需求单
            var creditorDemandBills = settingInfos.Select(s => s.CreditorDemandBill as DynamicObject).ToList();

            // 借入释放设置
            ReserveReleaseSetting releaseSetting = GetBorrowReleaseSetting(settingInfos);

            List<string> releaseEntryIds = new List<string>();

            var productIds =
                settingInfos.SelectMany(s => s.DemandInfos.Select(d =>
                {
                    return d.MaterialId;
                })).Distinct().ToList();
            var products = userCtx.LoadBizDataById("ydj_product", productIds);

            var profileService = userCtx.Container.GetService<ISystemProfile>();
            var reserveDay = profileService.GetSystemParameter(userCtx, "stk_stockparam", "freserveday", 30);

            var beSaveReserveBillIds = new HashSet<string>();
            //var beSaveOrderIds = new HashSet<string>();
            var savedDemandBills = new List<DynamicObject>();

            foreach (var borrowGrp in borrowGrps)
            {
                var key = borrowGrp.Key;

                var borrowerDemandBill = borrowerDemandBills.FirstOrDefault(s =>
                    Convert.ToString(s["id"]).EqualsIgnoreCase(key.BorrowerDemandBillId));
                if (borrowerDemandBill == null)
                {
                    result.ComplexMessage.ErrorMessages.Add($"借入合同{key.BorrowerDemandBillNo}不存在");
                    continue;
                }

                var fstatus = Convert.ToString(borrowerDemandBill["fstatus"]);
                if (!fstatus.EqualsIgnoreCase(BillStatus.E.ToString()))
                {
                    result.ComplexMessage.ErrorMessages.Add($"借入合同{key.BorrowerDemandBillNo}数据状态不是已审核");
                    continue;
                }

                DateTime freservedateto = ReserveUtil.GetDefaultReserveDate(userCtx, demandForm, borrowerDemandBill, reserveDay);
                if (demandForm.Id.EqualsIgnoreCase("ydj_order") && freservedateto < DateTime.Today)
                {
                    result.ComplexMessage.WarningMessages.Add($"销售合同{borrowerDemandBill["fbillno"]}的【交货日期+预留天数】小于系统当前日期，无法进行预留");
                    continue;
                }

                var borrowerReserveSettingInfo = Convert2ReserveInfo(borrowerReserveBills, demandForm, borrowerDemandBill);

                var borrowerReserveBill = GetOrAddReserveBill(userCtx, borrowerReserveSettingInfo);
                // 新建的预留单，加入借方预留单里
                if (!borrowerReserveBill.DataEntityState.FromDatabase && Convert.ToString(borrowerReserveBill["id"]).IsNullOrEmptyOrWhiteSpace())
                {
                    borrowerReserveBill["id"] = sequenceService.GetSequence<string>();
                    borrowerReserveBills.Add(borrowerReserveBill);
                }
                var borrowerReserveEntrys = (DynamicObjectCollection)borrowerReserveBill["fentity"];
                var borrowerDemandEntrys = (DynamicObjectCollection)borrowerDemandBill["fentry"];

                #region 重新计算实际预留量

                /*
                    * i.如果（{【借货商品销售数量】-【借货商品已预留量】}）小于【本次需释放预留数量】，则借货数量取{【借货商品销售数量】-【借货商品已预留量】}的差值；
                    * ii.如果（{【借货商品销售数量】-【借货商品已预留量】}）大于【本次需释放预留数量】，则借货数量取【本次需释放预留数量】。
                    */

                var data = borrowDemandEntryQtys?.FirstOrDefault(s =>
                    Convert.ToString(s["fentryid"]).EqualsIgnoreCase(key.BorrowerDemandEntryId));
                // 没找到，就设置释放数量为0
                if (data == null)
                {
                    result.ComplexMessage.WarningMessages.Add($"借入合同{key.BorrowerDemandBillNo}实际预留量为0，无须借货");

                    continue;
                }

                var salQty = Convert.ToDecimal(data["fqty"]);
                var bizSalQty = Convert.ToDecimal(data["fbizqty"]);
                var reserveQty = Convert.ToDecimal(data["freserveqty"]);
                var bizReserveQty = Convert.ToDecimal(data["fbizreserveqty"]);
                var outQty = Convert.ToDecimal(data["foutqty"]);
                var bizQutQty = Convert.ToDecimal(data["fbizoutqty"]);

                var releaseQty = borrowGrp.Sum(s => s.ReleaseQty);
                var bizReleaseQty = borrowGrp.Sum(s => s.BizReleaseQty);

                var maxReleaseQty = salQty - (reserveQty + outQty);
                var bizMaxReleaseQty = bizSalQty - (bizReserveQty + bizQutQty);

                if (maxReleaseQty < releaseQty)
                {
                    releaseQty = maxReleaseQty;
                    bizReleaseQty = bizMaxReleaseQty;

                    foreach (var demandInfo in borrowGrp)
                    {
                        if (releaseQty <= 0)
                        {
                            demandInfo.ReleaseQty = 0;
                            demandInfo.BizReleaseQty = 0;
                            continue;
                        }

                        if (demandInfo.ReleaseQty > releaseQty)
                        {
                            demandInfo.ReleaseQty = releaseQty;
                            demandInfo.BizReleaseQty = bizReleaseQty;
                        }

                        // 减去需要释放量
                        releaseQty -= demandInfo.ReleaseQty;
                        bizReleaseQty -= demandInfo.BizReleaseQty;
                    }
                }

                #endregion

                foreach (var demandInfo in borrowGrp)
                {
                    var creditorDemandBill = creditorDemandBills.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.CreditorDemandBillId));
                    var creditorDemandEntry = (creditorDemandBill?["fentry"] as DynamicObjectCollection)?.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.CreditorDemandEntryId));
                    if (creditorDemandEntry == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"第{demandInfo.Seq}行：借出合同商品明细行不存在");
                        continue;
                    }

                    var borrowerDemandEntry = (borrowerDemandBill?["fentry"] as DynamicObjectCollection)?.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.BorrowerDemandEntryId));
                    if (borrowerDemandEntry == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"第{demandInfo.Seq}行：借入合同商品明细行不存在");
                        continue;
                    }

                    // 判断数据是否正确
                    var creditorProductId = Convert.ToString(creditorDemandEntry["fproductid"]);
                    var borrowerProductId = Convert.ToString(borrowerDemandEntry["fproductid"]);
                    if (!creditorProductId.EqualsIgnoreCase(demandInfo.MaterialId) ||
                        !borrowerProductId.EqualsIgnoreCase(demandInfo.MaterialId))
                    {
                        result.ComplexMessage.ErrorMessages.Add($"第{demandInfo.Seq}行：借入/借出合同的商品不一致");
                        continue;
                    }

                    if (demandInfo.ReleaseQty == 0)
                    {
                        //result.ComplexMessage.ErrorMessages.Add($"借入合同{demandInfo.BorrowerDemandBillNo}实际预留量为0，无须借货");
                        continue;
                    }

                    demandInfo.BorrowerDemandBill = borrowerDemandBill;
                    demandInfo.BorrowerDemandEntry = borrowerDemandEntrys.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.BorrowerDemandEntryId));

                    var product = products.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.MaterialId));

                    var borrowerReserveDemandInfo = Convert2ReserveDemandInfo(borrowerReserveEntrys, demandInfo, freservedateto);

                    var closeStatuses = new string[]
                    {
                        ((int) CloseStatus.Whole).ToString(),
                        ((int) CloseStatus.Auto).ToString(),
                        ((int) CloseStatus.Manual).ToString()
                    };

                    if (closeStatuses.Contains(borrowerReserveDemandInfo.fclosestatus))
                    {
                        result.ComplexMessage.WarningMessages.Add($"第{demandInfo.Seq}行：销售合同{demandInfo.BorrowerDemandBillNo}的商品明细行{product?["fnumber"]}已关闭，无法进行预留");
                        continue;
                    }

                    #region 释放预留

                    var creditorReserveBill = creditorReserveBills.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.CreditorReserveBillId));
                    if (creditorReserveBill == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"第{demandInfo.Seq}行：未找到借出合同{demandInfo.CreditorDemandBillNo}关联的预留单，无法操作");
                        continue;
                    }

                    var creditorReserveEnRow = ((DynamicObjectCollection)creditorReserveBill["fentity"]).FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.CreditorReserveEntryId));
                    var manualReleaseItem = releaseSetting.ManualReleaseItems.FirstOrDefault(s => s.ReserveEntryId == demandInfo.CreditorReserveEntryId);
                    if (creditorReserveEnRow == null || manualReleaseItem == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"第{demandInfo.Seq}行：未找到借出合同{demandInfo.CreditorDemandBillNo}关联的预留需求明细，无法操作");
                        continue;
                    }

                    // 设置释放数量
                    manualReleaseItem.ReleaseQty = demandInfo.ReleaseQty;
                    var opResult = ReleaseReserveRow(userCtx, releaseSetting, creditorReserveEnRow, product, manualReleaseItem);
                    if (!opResult.IsSuccess)
                    {
                        continue;
                    }

                    result.ComplexMessage.SuccessMessages.Add($"第{demandInfo.Seq}行：借出销售合同{demandInfo.CreditorDemandBillNo}商品{product?["fname"]}预留释放成功");

                    // 预留释放明细行
                    releaseEntryIds.Add(demandInfo.CreditorReserveEntryId);

                    beSaveReserveBillIds.Add(Convert.ToString(creditorReserveBill["id"]));

                    #endregion

                    #region 转移预留

                    var borrowerReserveEnRow = GetOrAddReserveDemandRow(borrowerReserveEntrys, borrowerReserveDemandInfo);
                    borrowerReserveEnRow["fsourceformid"] = demandForm.Id;
                    borrowerReserveEnRow["fsourceinterid"] = borrowerReserveSettingInfo.fdemandbillpkid;
                    borrowerReserveEnRow["fsourcebillno"] = borrowerReserveSettingInfo.fdemandbillno;

                    // 新的需求明细行
                    if (Convert.ToString(borrowerReserveEnRow["id"]).IsNullOrEmptyOrWhiteSpace())
                    {
                        borrowerReserveDemandInfo.freserveentryid = sequenceService.GetSequence<string>();
                        borrowerReserveEnRow["id"] = borrowerReserveDemandInfo.freserveentryid;
                    }

                    var borrowerReserveTranceRows = (DynamicObjectCollection)borrowerReserveEnRow["fdetail"];
                    var borrowerReserveTraceInfo = Convert2ReserveTraceInfo(demandInfo, freservedateto);

                    //新增预留跟踪记录
                    AddReserveTranceRow(userCtx, borrowerReserveTranceRows, borrowerReserveTraceInfo);

                    //重新计算需求明细行的总的预留量
                    CalculateReserveQty(borrowerReserveEnRow, borrowerReserveTranceRows);

                    //将本次预留的仓库、仓位字段值反写到原单明细上
                    UpdateDemandBillStockInfo(userCtx, demandForm, borrowerDemandBill, borrowerReserveEnRow, borrowerReserveTranceRows);

                    result.ComplexMessage.SuccessMessages.Add($"第{demandInfo.Seq}行：借入销售合同{demandInfo.BorrowerDemandBillNo}商品{product?["fname"]}预留成功");

                    #region 添加转移记录

                    #region 借出（转出）记录

                    // 转出记录
                    var creditorReserveTransferEntrys =
                        (DynamicObjectCollection)creditorDemandEntry["freservetransfer"];
                    var creditorReserveTransfer = creditorReserveTransferEntrys.FirstOrDefault(s =>
                        Convert.ToString(s["freservetransfertype"]).EqualsIgnoreCase("1")
                        && Convert.ToString(s["freservetransferorderentryid"]).EqualsIgnoreCase(demandInfo.BorrowerDemandEntryId)
                        );
                    if (creditorReserveTransfer == null)
                    {
                        creditorReserveTransfer = new DynamicObject(reserveTransferDt);
                        creditorReserveTransfer["freservetransfertype"] = "1";      // 转出
                        creditorReserveTransfer["freservetransferorderid"] = demandInfo.BorrowerDemandBillId;
                        creditorReserveTransfer["freservetransfercustomerid"] = demandInfo.BorrowerCustomerId;
                        creditorReserveTransfer["freservetransferproductid"] = demandInfo.MaterialId;
                        creditorReserveTransfer["freservetransferorderentryid"] = demandInfo.BorrowerDemandEntryId;   // 记录借方商品明细内码
                        creditorReserveTransfer["fbizreservetransferunitid"] = demandInfo.BizUnitId;
                        creditorReserveTransfer["fbizreservetransferinqty"] = 0;
                        creditorReserveTransfer["fbizreservetransferoutqty"] = demandInfo.BizReleaseQty;
                        creditorReserveTransfer["freservetransferunitid"] = demandInfo.UnitId;
                        creditorReserveTransfer["freservetransferinqty"] = 0;
                        creditorReserveTransfer["freservetransferoutqty"] = demandInfo.ReleaseQty;

                        creditorReserveTransferEntrys.Add(creditorReserveTransfer);
                    }
                    else
                    {
                        creditorReserveTransfer["fbizreservetransferoutqty"] = Convert.ToDecimal(creditorReserveTransfer["fbizreservetransferoutqty"]) + demandInfo.BizReleaseQty;
                        creditorReserveTransfer["freservetransferoutqty"] = Convert.ToDecimal(creditorReserveTransfer["freservetransferoutqty"]) + demandInfo.ReleaseQty;
                    }

                    savedDemandBills.Add(creditorDemandBill);

                    result.ComplexMessage.SuccessMessages.Add($"第{demandInfo.Seq}行：借出销售合同{demandInfo.CreditorDemandBillNo}添加预留转移记录成功");

                    #endregion

                    #region 借入（转入）记录

                    // 转出记录
                    var borrowerReserveTransferEntrys =
                        (DynamicObjectCollection)borrowerDemandEntry["freservetransfer"];
                    var borrowerReserveTransfer = borrowerReserveTransferEntrys.FirstOrDefault(s =>
                        Convert.ToString(s["freservetransfertype"]).EqualsIgnoreCase("2")
                        && Convert.ToString(s["freservetransferorderentryid"]).EqualsIgnoreCase(demandInfo.CreditorDemandEntryId)
                        );
                    if (borrowerReserveTransfer == null)
                    {
                        borrowerReserveTransfer = new DynamicObject(reserveTransferDt);
                        borrowerReserveTransfer["freservetransfertype"] = "2";      // 转入
                        borrowerReserveTransfer["freservetransferorderid"] = creditorDemandBill?["id"];
                        borrowerReserveTransfer["freservetransfercustomerid"] = creditorDemandBill?["fcustomerid"];
                        borrowerReserveTransfer["freservetransferproductid"] = demandInfo.MaterialId;
                        borrowerReserveTransfer["freservetransferorderentryid"] = demandInfo.CreditorDemandEntryId;   // 记录贷方商品明细内码
                        borrowerReserveTransfer["fbizreservetransferunitid"] = demandInfo.BizUnitId;
                        borrowerReserveTransfer["fbizreservetransferinqty"] = demandInfo.BizReleaseQty;
                        borrowerReserveTransfer["fbizreservetransferoutqty"] = 0;
                        borrowerReserveTransfer["freservetransferunitid"] = demandInfo.UnitId;
                        borrowerReserveTransfer["freservetransferinqty"] = demandInfo.ReleaseQty;
                        borrowerReserveTransfer["freservetransferoutqty"] = 0;

                        borrowerReserveTransferEntrys.Add(borrowerReserveTransfer);
                    }
                    else
                    {
                        borrowerReserveTransfer["fbizreservetransferinqty"] = Convert.ToDecimal(borrowerReserveTransfer["fbizreservetransferinqty"]) + demandInfo.BizReleaseQty;
                        borrowerReserveTransfer["freservetransferinqty"] = Convert.ToDecimal(borrowerReserveTransfer["freservetransferinqty"]) + demandInfo.ReleaseQty;
                    }

                    savedDemandBills.Add(borrowerDemandBill);

                    result.ComplexMessage.SuccessMessages.Add($"第{demandInfo.Seq}行：借入销售合同{demandInfo.BorrowerDemandBillNo}添加预留转移记录成功");
                    #endregion

                    #endregion

                    beSaveReserveBillIds.Add(Convert.ToString(borrowerReserveBill["id"]));

                    demandInfo.IsSuccessTransfer = true;

                    #endregion
                }
            }

            if (result.ComplexMessage.ErrorMessages.Any())
            {
                throw new BusinessException("释放借货失败：<br/>" + result.ComplexMessage.ErrorMessages.JoinEx("<br/>", false));
            }

            if (beSaveReserveBillIds.Count == 0)
            {
                return result;
            }

            var beSaveReserveBills = new List<DynamicObject>();
            beSaveReserveBills.AddRange(creditorReserveBills.Where(s => beSaveReserveBillIds.Contains(Convert.ToString(s["id"]))));
            beSaveReserveBills.AddRange(borrowerReserveBills.Where(s => beSaveReserveBillIds.Contains(Convert.ToString(s["id"]))));
            if (beSaveReserveBills.Count == 0)
            {
                return result;
            }

            // 保存需求单：仓库仓位、预留转移记录
            if (savedDemandBills.Any())
            {
                savedDemandBills = savedDemandBills.Distinct().ToList();

                var prepareSaveDataService =
                    userCtx.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(userCtx, demandForm, savedDemandBills.ToArray(), OperateOption.Create());

                this.UpdateSourceOrder(userCtx, demandForm, savedDemandBills);
            }

            var para = new Dictionary<string, object>();
            para.Add("IgnoreCheckPermssion", true);
            para.Add("TopOrperationNo", "ExcelImport");
            var invokeResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(
                userCtx,
                "stk_reservebill",
                beSaveReserveBills,
                "draft",
                para);
            invokeResult.ThrowIfHasError(true, "预留单保存失败");

            ReserveUtil.UpdateOrderReserveQty(userCtx, beSaveReserveBills);

            result.IsSuccess = true;
            return result;
        }

        /// <summary>
        /// 获取借方需求明细相关数量
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="settingInfos"></param>
        private List<DynamicObject> GetBorrowDemandEntryQtys(UserContext userCtx, List<ReserveBorrowSettingInfo> settingInfos)
        {
            var orderEntryIds = settingInfos.SelectMany(s => s.DemandInfos.Select(d => d.BorrowerDemandEntryId)).Distinct().ToList();
            if (orderEntryIds.Count == 0) return null;

            string sql = $@"
select oe.fentryid, oe.fqty, oe.fbizqty, freserveqty, fbizreserveqty,
ISNULL((
	select SUM(se.fqty) from t_stk_sostockout s with(nolock) 
	inner join t_stk_sostockoutentry se with(nolock) on s.fid=se.fid
	where s.fcancelstatus='0' and se.fsourceentryid=oe.fentryid
),0) as foutqty
, 
ISNULL((
	select SUM(se.fbizqty) from t_stk_sostockout s with(nolock) 
	inner join t_stk_sostockoutentry se with(nolock) on s.fid=se.fid
	where s.fcancelstatus='0' and se.fsourceentryid=oe.fentryid
),0) as fbizoutqty
from t_ydj_order o
inner join t_ydj_orderentry oe on o.fid=oe.fid
where oe.fentryid in ({orderEntryIds.JoinEx(",", true)})";

            var datas = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>());

            return datas.ToList();
        }

        /// <summary>
        /// 获取借货释放设置
        /// </summary>
        /// <param name="settingInfos"></param>
        /// <returns></returns>
        private ReserveReleaseSetting GetBorrowReleaseSetting(List<ReserveBorrowSettingInfo> settingInfos)
        {
            var releaseSetting = new ReserveReleaseSetting();
            releaseSetting.ReleaseWay = (int)Enu_ReleaseWay.Manual;
            releaseSetting.ReleaseType = (int)Enu_ReleaseType.Release;

            foreach (var settingInfo in settingInfos)
            {
                foreach (var demandInfo in settingInfo.DemandInfos)
                {
                    // 释放数量=0，不处理
                    if (demandInfo.ReleaseQty == 0)
                    {
                        continue;
                    }

                    var item = new ReserveManualReleaseItem();

                    item.ReserveId = settingInfo.CreditorReserveBillId;
                    item.SourceFormId = settingInfo.CreditorDemandBillId;
                    item.SourceInterId = demandInfo.CreditorDemandEntryId;

                    item.ReserveEntryId = demandInfo.CreditorReserveEntryId;
                    item.SourceEntryId = demandInfo.CreditorDemandEntryId;

                    item.ReleaseQty = demandInfo.ReleaseQty;

                    item.StoreHouseId = demandInfo.StoreHouseId;
                    item.StockStatusId = demandInfo.StockStatus;

                    item.Message = $"关联的销售合同{demandInfo.BorrowerDemandBillNo}借货，释放预留";

                    releaseSetting.ManualReleaseItems.Add(item);

                    // 预留单
                    if (settingInfo.CreditorDemandFormId.EqualsIgnoreCase("stk_reservebill"))
                    {
                        releaseSetting.SelectEntryRow.Add(item.ReserveEntryId);
                    }
                    else
                    {
                        releaseSetting.SelectEntryRow.Add(item.SourceEntryId);
                    }
                }
            }

            return releaseSetting;
        }

        private ReserveSettingInfo Convert2ReserveInfo(List<DynamicObject> reserveBills,
            HtmlForm demandForm, DynamicObject demandBill)
        {
            var demandBillId = Convert.ToString(demandBill["id"]);

            var reserveInfo = new ReserveSettingInfo();
            reserveInfo.DemandBillData = demandBill;
            reserveInfo.fcancelstatus = Convert.ToBoolean(demandBill["fcancelstatus"]);

            reserveInfo.ReserveBillData = reserveBills.FirstOrDefault(s =>
                Convert.ToString(s["fsourcepkid"]).EqualsIgnoreCase(demandBillId));

            reserveInfo.freserveobjecttype = "ydj_customer";
            reserveInfo.freserveobjectid = Convert.ToString(demandBill["fcustomerid"]);
            reserveInfo.fdescription = "";
            reserveInfo.fdemandformid = demandForm.Id;
            reserveInfo.fdemandbillno = Convert.ToString(demandBill["fbillno"]);
            reserveInfo.fdemandbillpkid = demandBillId;

            reserveInfo.fsourcestatus = Convert.ToString(demandBill["fstatus"]);

            return reserveInfo;
        }

        private ReserveSettingDemandInfo Convert2ReserveDemandInfo(DynamicObjectCollection reserveEntrys, ReserveBorrowDemandInfo demandInfo, DateTime reserveDateTo)
        {
            var reserveEnRow = reserveEntrys.FirstOrDefault(s =>
                Convert.ToString(s["fsourceentryid"]).EqualsIgnoreCase(demandInfo.BorrowerDemandEntryId)
            );

            var reserveDemandInfo = new ReserveSettingDemandInfo();

            reserveDemandInfo.freserveentryid = Convert.ToString(reserveEnRow?["id"]);
            reserveDemandInfo.fmaterialid = demandInfo.MaterialId;
            reserveDemandInfo.fattrinfo = demandInfo.AttrInfo;
            reserveDemandInfo.fattrinfo_e = demandInfo.AttrInfo_e;
            reserveDemandInfo.fcustomdesc = demandInfo.CustomDesc;
            reserveDemandInfo.fresultbrandid = demandInfo.ResultBrandId;
            reserveDemandInfo.funitid = demandInfo.UnitId;
            reserveDemandInfo.fbizunitid = demandInfo.BizUnitId;
            reserveDemandInfo.fqty = 0; // 默认0
            reserveDemandInfo.fbizqty = 0; // 默认0
            reserveDemandInfo.freservedateto = reserveDateTo;//以新的业务日期为准
            reserveDemandInfo.fmtono = demandInfo.Mtono;
            reserveDemandInfo.fdemandentryid = demandInfo.BorrowerDemandEntryId;
            reserveDemandInfo.fclosestatus = Convert.ToString((demandInfo.BorrowerDemandEntry as DynamicObject)?["fclosestatus_e"]);

            return reserveDemandInfo;
        }

        private ReserveSettingTraceInfo Convert2ReserveTraceInfo(ReserveBorrowDemandInfo demandInfo, DateTime reserveDateTo)
        {
            var traceInfo = new ReserveSettingTraceInfo();
            traceInfo.fbizqty_d = demandInfo.BizReleaseQty;
            traceInfo.fqty_d = demandInfo.ReleaseQty;

            traceInfo.freservedateto_d = reserveDateTo;
            traceInfo.fdirection_d = "0";
            traceInfo.fopdesc = "0";
            traceInfo.fmaterialid_d = demandInfo.MaterialId;
            traceInfo.fbizunitid_d = demandInfo.BizUnitId;
            traceInfo.funitid_d = demandInfo.UnitId;
            traceInfo.fstorehouseid = demandInfo.StoreHouseId;
            traceInfo.fstockstatus = demandInfo.StockStatus;
            traceInfo.freservenote = $"关联的销售合同{demandInfo.CreditorDemandBillNo}借货预留";
            traceInfo.ffrombillno = demandInfo.BorrowerDemandBillNo;
            traceInfo.ffrombillpkid = demandInfo.BorrowerDemandBillId;
            traceInfo.ffromformid = demandInfo.BorrowerDemandFormId;

            return traceInfo;
        }

        /// <summary>
        /// 添加或更新预留转移记录
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="settingInfos"></param>
        /// <returns></returns>
        private IOperationResult AddOrUpdateReserveTransferRecord(UserContext userCtx, List<ReserveBorrowSettingInfo> settingInfos, List<DynamicObject> borrowerDemandBills)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            HtmlForm orderForm = this.MetaModelService.LoadFormModel(userCtx, "ydj_order");
            var reserveTransferEntity = orderForm.GetEntity("freservetransfer");
            var reserveTransferDt = reserveTransferEntity.DynamicObjectType;

            List<DynamicObject> savedDemandBills = new List<DynamicObject>();

            foreach (var settingInfo in settingInfos)
            {
                var creditorDemandBill = settingInfo.CreditorDemandBill as DynamicObject;

                foreach (var demandInfo in settingInfo.DemandInfos)
                {
                    // 释放数量=0，不处理
                    if (demandInfo.ReleaseQty == 0)
                    {
                        continue;
                    }

                    if (!demandInfo.IsSuccessTransfer)
                    {
                        continue;
                    }

                    var creditorDemandEntry = (creditorDemandBill?["fentry"] as DynamicObjectCollection)?.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.CreditorDemandEntryId));
                    if (creditorDemandEntry == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"第{demandInfo.Seq}行：借出合同商品明细行不存在");
                        continue;
                    }

                    var borrowerDemandBill =
                        borrowerDemandBills.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.BorrowerDemandBillId));
                    var borrowerDemandEntry = (borrowerDemandBill?["fentry"] as DynamicObjectCollection)?.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(demandInfo.BorrowerDemandEntryId));
                    if (borrowerDemandEntry == null)
                    {
                        result.ComplexMessage.ErrorMessages.Add($"第{demandInfo.Seq}行：借入合同商品明细行不存在");
                        continue;
                    }

                    #region 借出（转出）记录

                    // 转出记录
                    var creditorReserveTransferEntrys =
                        (DynamicObjectCollection)creditorDemandEntry["freservetransfer"];
                    var creditorReserveTransfer = creditorReserveTransferEntrys.FirstOrDefault(s =>
                        Convert.ToString(s["freservetransfertype"]).EqualsIgnoreCase("1")
                        && Convert.ToString(s["freservetransferorderentryid"]).EqualsIgnoreCase(demandInfo.BorrowerDemandEntryId)
                        );
                    if (creditorReserveTransfer == null)
                    {
                        creditorReserveTransfer = new DynamicObject(reserveTransferDt);
                        creditorReserveTransfer["freservetransfertype"] = "1";      // 转出
                        creditorReserveTransfer["freservetransferorderid"] = demandInfo.BorrowerDemandBillId;
                        creditorReserveTransfer["freservetransfercustomerid"] = demandInfo.BorrowerCustomerId;
                        creditorReserveTransfer["freservetransferproductid"] = demandInfo.MaterialId;
                        creditorReserveTransfer["freservetransferorderentryid"] = demandInfo.BorrowerDemandEntryId;   // 记录借方商品明细内码
                        creditorReserveTransfer["fbizreservetransferunitid"] = demandInfo.BizUnitId;
                        creditorReserveTransfer["fbizreservetransferinqty"] = 0;
                        creditorReserveTransfer["fbizreservetransferoutqty"] = demandInfo.BizReleaseQty;
                        creditorReserveTransfer["freservetransferunitid"] = demandInfo.UnitId;
                        creditorReserveTransfer["freservetransferinqty"] = 0;
                        creditorReserveTransfer["freservetransferoutqty"] = demandInfo.ReleaseQty;

                        creditorReserveTransferEntrys.Add(creditorReserveTransfer);
                    }
                    else
                    {
                        creditorReserveTransfer["fbizreservetransferoutqty"] = Convert.ToDecimal(creditorReserveTransfer["fbizreservetransferoutqty"]) + demandInfo.BizReleaseQty;
                        creditorReserveTransfer["freservetransferoutqty"] = Convert.ToDecimal(creditorReserveTransfer["freservetransferoutqty"]) + demandInfo.ReleaseQty;
                    }

                    savedDemandBills.Add(creditorDemandBill);

                    result.ComplexMessage.SuccessMessages.Add($"第{demandInfo.Seq}行：借出销售合同{demandInfo.CreditorDemandBillNo}添加预留转移记录成功");

                    #endregion

                    #region 借入（转入）记录

                    // 转出记录
                    var borrowerReserveTransferEntrys =
                        (DynamicObjectCollection)borrowerDemandEntry["freservetransfer"];
                    var borrowerReserveTransfer = borrowerReserveTransferEntrys.FirstOrDefault(s =>
                        Convert.ToString(s["freservetransfertype"]).EqualsIgnoreCase("2")
                        && Convert.ToString(s["freservetransferorderentryid"]).EqualsIgnoreCase(demandInfo.CreditorDemandEntryId)
                        );
                    if (borrowerReserveTransfer == null)
                    {
                        borrowerReserveTransfer = new DynamicObject(reserveTransferDt);
                        borrowerReserveTransfer["freservetransfertype"] = "2";      // 转入
                        borrowerReserveTransfer["freservetransferorderid"] = creditorDemandBill?["id"];
                        borrowerReserveTransfer["freservetransfercustomerid"] = creditorDemandBill?["fcustomerid"];
                        borrowerReserveTransfer["freservetransferproductid"] = demandInfo.MaterialId;
                        borrowerReserveTransfer["freservetransferorderentryid"] = demandInfo.CreditorDemandEntryId;   // 记录贷方商品明细内码
                        borrowerReserveTransfer["fbizreservetransferunitid"] = demandInfo.BizUnitId;
                        borrowerReserveTransfer["fbizreservetransferinqty"] = demandInfo.BizReleaseQty;
                        borrowerReserveTransfer["fbizreservetransferoutqty"] = 0;
                        borrowerReserveTransfer["freservetransferunitid"] = demandInfo.UnitId;
                        borrowerReserveTransfer["freservetransferinqty"] = demandInfo.ReleaseQty;
                        borrowerReserveTransfer["freservetransferoutqty"] = 0;

                        borrowerReserveTransferEntrys.Add(borrowerReserveTransfer);
                    }
                    else
                    {
                        borrowerReserveTransfer["fbizreservetransferinqty"] = Convert.ToDecimal(borrowerReserveTransfer["fbizreservetransferinqty"]) + demandInfo.BizReleaseQty;
                        borrowerReserveTransfer["freservetransferinqty"] = Convert.ToDecimal(borrowerReserveTransfer["freservetransferinqty"]) + demandInfo.ReleaseQty;
                    }

                    savedDemandBills.Add(borrowerDemandBill);

                    result.ComplexMessage.SuccessMessages.Add($"第{demandInfo.Seq}行：借入销售合同{demandInfo.BorrowerDemandBillNo}添加预留转移记录成功");
                    #endregion
                }
            }

            var successMessages = result.ComplexMessage.SuccessMessages.Distinct().ToList();
            result.ComplexMessage.SuccessMessages.Clear();
            result.ComplexMessage.SuccessMessages.AddRange(successMessages);

            var errorMessages = result.ComplexMessage.ErrorMessages.Distinct().ToList();
            result.ComplexMessage.ErrorMessages.Clear();
            result.ComplexMessage.ErrorMessages.AddRange(errorMessages);

            var warnMessages = result.ComplexMessage.WarningMessages.Distinct().ToList();
            result.ComplexMessage.WarningMessages.Clear();
            result.ComplexMessage.WarningMessages.AddRange(warnMessages);

            if (errorMessages.Any())
            {
                return result;
            }

            if (savedDemandBills.Any())
            {
                savedDemandBills = savedDemandBills.Distinct().ToList();

                var prepareSaveDataService =
                    userCtx.Container.GetService<IPrepareSaveDataService>();
                prepareSaveDataService.PrepareDataEntity(userCtx, orderForm, savedDemandBills.ToArray(), OperateOption.Create());

                UpdateSourceOrder(userCtx, orderForm, savedDemandBills);
            }

            result.IsSuccess = true;
            return result;
        }


    }



}