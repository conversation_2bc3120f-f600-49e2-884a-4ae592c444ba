using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveDialog;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore.FormOp.FormService;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve
{
    /// <summary>
    /// 预留释放
    /// </summary>
    public partial class ReserveService
    {


        /// <summary>
        /// 预留释放
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="setting"></param>
        /// <param name="billForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult Release(UserContext userCtx, ReserveReleaseSetting setting, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option)
        {
            Initialize(userCtx);

            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            var invokeResult = this.ReleaseReserveBill(userCtx, setting, billForm, billDatas, option);
            result.MergeResult(invokeResult);

            result.IsSuccess = true;
            return result;
        }




        /// <summary>
        /// 释放预留单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="setting"></param>
        /// <param name="billForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="option"></param>
        private IOperationResult ReleaseReserveBill(UserContext userCtx, ReserveReleaseSetting setting, HtmlForm billForm, IEnumerable<DynamicObject> billDatas, OperateOption option)
        {
            IOperationResult result = new OperationResult();
            IEnumerable<DynamicObject> reserveBills = GetReserveBillDatas(userCtx, billForm, billDatas);

            if (reserveBills == null || reserveBills.Count() == 0)
            {
                return result;
            }

            var reserveEnRows = new List<DynamicObject>();

            foreach (var reserveBill in reserveBills)
            {
                var entrys = reserveBill["fentity"] as DynamicObjectCollection;
                foreach (var reserveEnRow in entrys)
                {
                    var enId = reserveEnRow["Id"]?.ToString();
                    var fsourcetype = reserveBill["fsourcetype"]?.ToString();
                    var fsourceentryid = reserveEnRow["fsourceentryid"]?.ToString();//对应的来源单据的需求明细行id
                    if (setting.SelectEntryRow != null && setting.SelectEntryRow.Count > 0)
                    {
                        //只操作某些需求明细行，比如销售合同上的行关闭时，自动释放预留功能
                        if (fsourcetype.EqualsIgnoreCase(billForm.Id) && !setting.SelectEntryRow.Any(f => f.EqualsIgnoreCase(fsourceentryid)))
                        {
                            continue;
                        }
                        else if (billForm.Id.EqualsIgnoreCase("stk_reservebill") && !setting.SelectEntryRow.Any(f => f.EqualsIgnoreCase(enId)))
                        {
                            continue;
                        }
                    }

                    reserveEnRows.Add(reserveEnRow);
                }
            }

            if (reserveEnRows.Any())
            {
                // 释放预留
                if (setting.ReleaseType == 0)
                {
                    foreach (var reserveEnRow in reserveEnRows)
                    {
                        var enId = reserveEnRow["Id"]?.ToString();
                        var matObj = reserveEnRow["fmaterialid_ref"] as DynamicObject;
                        if (setting.ManualReleaseItems != null && setting.ManualReleaseItems.Count > 0)
                        {
                            var items = setting.ManualReleaseItems.Where(s => s.ReserveEntryId.EqualsIgnoreCase(enId));
                            foreach (var item in items)
                            {
                                var opResult = ReleaseReserveRow(userCtx, setting, reserveEnRow, matObj, item);
                                result.MergeResult(opResult);
                            }
                        }
                        else
                        {
                            var opResult = ReleaseReserveRow(userCtx, setting, reserveEnRow, matObj, null);
                            result.MergeResult(opResult);
                        }

                        CheckReserveQty(reserveEnRow, (DynamicObjectCollection)reserveEnRow["fdetail"]);
                    }
                }
                // 取消释放
                else
                {
                    foreach (var reserveEnRow in reserveEnRows)
                    {
                        CancleReleaseReserve(userCtx, setting, reserveEnRow);
                        CheckReserveQty(reserveEnRow, (DynamicObjectCollection)reserveEnRow["fdetail"]);
                    }
                }
            }

            var para = new Dictionary<string, object>();
            para.Add("IgnoreCheckPermssion", true);
            para.Add("TopOrperationNo", "ExcelImport");
            var invokeResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(
                    userCtx,
                    "stk_reservebill",
                    reserveBills,
                    "draft",
                    para);

            if (invokeResult.IsSuccess)
            {
                var releaseType = setting.ReleaseType == (int)Enu_ReleaseType.Release ? Enu_ReleaseType.Release : Enu_ReleaseType.CancelRelease;

                var selectEntryIds = new List<string>();
                // 没有指定表示所有行
                if (setting.SelectEntryRow.IsNullOrEmpty())
                {
                    var cvtRule = ReserveUtil.GetSettingMapInfo(userCtx, billForm.Id);
                    var sourceEntity = billForm.GetEntryEntity(cvtRule.ActiveEntityKey);

                    foreach (var billData in billDatas)
                    {
                        var billEntrys = sourceEntity.DynamicProperty?.GetValue<DynamicObjectCollection>(billData);
                        selectEntryIds.AddRange(billEntrys.Select(s => Convert.ToString(s["id"])));
                    }
                }
                else
                {
                    selectEntryIds.AddRange(setting.SelectEntryRow);
                }

                UpdateOrderStockInfoByRelease(userCtx, billForm, billDatas, reserveBills, selectEntryIds, releaseType);
            }
            else
            {
                // 暂存方法调用以下方法，因此失败才执行，还原数据
                ReserveUtil.UpdateOrderReserveQty(userCtx, reserveBills);
            }

            result.IsSuccess = !(result.ComplexMessage.ErrorMessages.Any() || result.ComplexMessage.WarningMessages.Any());

            return result;
        }


        /// <summary>
        /// 取消释放
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="reserveEnRow"></param>
        private void CancleReleaseReserve(UserContext ctx, ReserveReleaseSetting setting, DynamicObject reserveEnRow)
        {
            var tranceRows = reserveEnRow["fdetail"] as DynamicObjectCollection;

            var beAdd = new List<DynamicObject>();

            //按仓库+库存状态分组
            var grp = tranceRows
                .GroupBy(f => $"{Convert.ToString(f["fstorehouseid"]).Trim()}_{Convert.ToString(f["fstockstatus"]).Trim()}")
                .ToList();
            foreach (var item in grp)
            {
                //该分组当前的预留数量，如果大于零，不需要取消预留释放
                var currentQty = item.ToList().Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                                     .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                            - item.ToList().Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                                .Sum(f => Convert.ToDecimal(f["fqty_d"]));
                if (currentQty > 0)
                {
                    continue;
                }

                //最后一次释放
                var lastReleaseRow = item.ToList()
                    .Where(f => setting.ReleaseWay.ToString() == f["fopdesc"]?.ToString())
                    .OrderByDescending(f => Convert.ToString(f["Id"]))
                    .FirstOrDefault();
                if (lastReleaseRow == null)
                {
                    continue;
                }

                //最后一次释放的释放数量
                var releaseQty = Convert.ToDecimal(lastReleaseRow["fqty_d"]);
                if (releaseQty == 0)
                {
                    continue;
                }

                var tranceRow = (DynamicObject)item.Last().Clone();
                tranceRow["foptime"] = DateTime.Now;
                tranceRow["fopuserid"] = ctx.UserId;
                tranceRow["freservenote"] = GetReleaseTips(setting);
                tranceRow["fopdesc"] = "0";//预留增加
                tranceRow["fdirection_d"] = "0";
                tranceRow["fqty_d"] = releaseQty;
                tranceRow["fbizqty_d"] = releaseQty;

                beAdd.Add(tranceRow);
            }

            foreach (var item in beAdd)
            {
                tranceRows.Add(item);
            }

            //重算需求明细行的预留数量
            CalculateReserveQty(reserveEnRow, tranceRows);
        }


        /// <summary>
        /// 校验释放数量
        /// </summary>
        /// <param name="reserveEnRow"></param>
        /// <param name="manualReleaseItem"></param>
        /// <returns></returns>
        private bool CheckReleaseQty(DynamicObject reserveEnRow, ReserveManualReleaseItem manualReleaseItem)
        {
            if (manualReleaseItem == null)
            {
                return true;
            }

            var tranceRows = (DynamicObjectCollection)reserveEnRow["fdetail"];

            //按仓库+仓位+库存状态分组
            var rows = tranceRows.Where(f =>
                Convert.ToString(f["fstorehouseid"]).Trim().EqualsIgnoreCase(manualReleaseItem.StoreHouseId)
                && Convert.ToString(f["fstockstatus"]).Trim().EqualsIgnoreCase(manualReleaseItem.StockStatusId)
                ).ToList();

            //该分组剩余的预留数量
            var leaveQty =
                rows.Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                    .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                -
                rows.Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                    .Sum(f => Convert.ToDecimal(f["fqty_d"]));

            manualReleaseItem.MaxReleaseQty = leaveQty;

            return manualReleaseItem.ReleaseQty <= leaveQty;
        }


        /// <summary>
        /// 释放预留：增加相应的预留减少跟踪记录
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="reserveEnRow">预留需求行</param>
        private IOperationResult ReleaseReserveRow(UserContext ctx, ReserveReleaseSetting setting, DynamicObject reserveEnRow, DynamicObject product, ReserveManualReleaseItem manualReleaseItem)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = false;

            string reserveEnId = Convert.ToString(reserveEnRow["id"]);
            DynamicObjectCollection tranceRows = (DynamicObjectCollection)reserveEnRow["fdetail"];

            if (manualReleaseItem != null)
            {
                if (!CheckReleaseQty(reserveEnRow, manualReleaseItem))
                {
                    result.ComplexMessage.ErrorMessages.Add(
                        $"商品【{product?["fname"]}】预留释放失败：【本次需释放预留数量】（{manualReleaseItem.ReleaseQty:F2}）大于【预留数量】（{manualReleaseItem.MaxReleaseQty:F2}），无法释放！");
                    return result;
                }

                if (manualReleaseItem.ReleaseQty <= 0)
                {
                    result.ComplexMessage.ErrorMessages.Add("商品【{0}】预留释放失败：【本次需释放预留数量】为0，无需做预留释放".Fmt(product?["fname"]));
                    return result;
                }
            }

            //当前预留数量
            var qty = Convert.ToDecimal(reserveEnRow["fqty"]);
            if (qty <= 0 || tranceRows == null || tranceRows.Count == 0)
            {
                return result;
            }

            var beAdd = new List<DynamicObject>();

            //按仓库+库存状态分组
            var grp = tranceRows
                .GroupBy(f => $"{Convert.ToString(f["fstorehouseid"]).Trim()}_{Convert.ToString(f["fstockstatus"]).Trim()}")
                .ToList();

            foreach (var item in grp)
            {
                //该分组剩余的预留数量
                var leaveQty =
                    item.ToList().Where(f => Convert.ToString(f["fdirection_d"]) == "0")
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]))
                    - item.ToList().Where(f => Convert.ToString(f["fdirection_d"]) == "1")
                        .Sum(f => Convert.ToDecimal(f["fqty_d"]));

                //需要释放的预留数量
                var releaseQty = leaveQty > qty ? qty : leaveQty;

                //自定义预留操作说明
                string customMessage = setting.Message;

                if (manualReleaseItem != null)
                {
                    // 设置为手动释放填写的数量
                    releaseQty = manualReleaseItem.ReleaseQty;
                    if (!manualReleaseItem.Message.IsNullOrEmptyOrWhiteSpace())
                    {
                        customMessage = manualReleaseItem.Message;
                    }

                    // 维度不一致，不处理
                    var keys = item.Key.SplitKey("_");
                    var fstorehouseid = keys.Count > 0 ? keys[0] : string.Empty;
                    var fstockstatus = keys.Count > 1 ? keys[1] : string.Empty;

                    if (!fstorehouseid.EqualsIgnoreCase(manualReleaseItem.StoreHouseId)
                        || !fstockstatus.EqualsIgnoreCase(manualReleaseItem.StockStatusId)
                    )
                    {
                        continue;
                    }
                }

                if (releaseQty <= 0)
                {
                    continue;
                }

                var tranceRow = (DynamicObject)item.Last().Clone();
                tranceRow["foptime"] = DateTime.Now;
                tranceRow["fopuserid"] = ctx.UserId;
                tranceRow["freservenote"] = GetReleaseTips(setting, customMessage);
                tranceRow["fopdesc"] = setting.ReleaseWay;//2--手工释放，4--单据关闭释放 ，5---单据作废释放，6---自动释放
                tranceRow["fdirection_d"] = "1";
                tranceRow["fqty_d"] = releaseQty;
                tranceRow["fbizqty_d"] = releaseQty;

                beAdd.Add(tranceRow);
            }

            foreach (var item in beAdd)
            {
                tranceRows.Add(item);
            }

            //重算需求明细行的预留数量
            CalculateReserveQty(reserveEnRow, tranceRows);

            if (beAdd.Any())
            {
                result.ComplexMessage.SuccessMessages.Add("商品【{0}】预留释放成功".Fmt(product?["fname"]));
                result.IsSuccess = true;
            }
            else
            {
                result.ComplexMessage.SuccessMessages.Add("商品【{0}】预留释放失败".Fmt(product?["fname"]));
            }

            return result;
        }


        private static string GetReleaseTips(ReserveReleaseSetting setting, string customMessage = null)
        {
            var tips = customMessage ?? setting.Message;
            if (tips.IsNullOrEmptyOrWhiteSpace())
            {
                if (setting.ReleaseType == 0)
                {
                    //2--手工释放，4--单据关闭释放 ，5---单据作废释放，6---自动释放
                    switch (setting.ReleaseWay)
                    {
                        case 2:
                            tips = "手工释放预留";
                            break;
                        case 4:
                            tips = "单据关闭释放预留";
                            break;
                        case 5:
                            tips = "单据作废释放预留";
                            break;
                        case 6:
                            tips = "自动释放预留";
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    switch (setting.ReleaseWay)
                    {
                        case 2:
                            tips = "手工取消释放预留";
                            break;
                        case 4:
                            tips = "单据反关闭预留增加";
                            break;
                        case 5:
                            tips = "单据反作废预留增加";
                            break;
                        case 6:
                            tips = "自动取消释放预留";
                            break;
                        default:
                            break;
                    }
                }
            }

            return tips;
        }



        /// <summary>
        /// 获取预留单信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billForm"></param>
        /// <param name="billDatas"></param>
        /// <param name="reserveBillForm"></param>
        /// <param name="dm"></param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> GetReserveBillDatas(UserContext userCtx, HtmlForm billForm, IEnumerable<DynamicObject> billDatas)
        {
            IEnumerable<DynamicObject> reserveBills = null;

            //基于预留单本身的手动释放
            if (billForm.Id.EqualsIgnoreCase("stk_reservebill"))
            {
                reserveBills = billDatas;
                userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, billForm.GetDynamicObjectType(userCtx), reserveBills, false);
            }
            else
            {
                //如果是基于来源单据的手动释放，则直接根据源单类型和源单编号进行整单释放
                var paramList = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                    new SqlParam("@fsourcetype", System.Data.DbType.String, billForm.Id)
                };
                var numberField = billForm.GetNumberField();
                var paramNames = new List<string>();
                var index = 1;
                foreach (var billData in billDatas)
                {
                    var sourceNumber = numberField.DynamicProperty?.GetValue<string>(billData);
                    paramNames.Add($"@sn{index}");
                    paramList.Add(new SqlParam($"@sn{index}", System.Data.DbType.String, sourceNumber));
                    index++;
                }
                var where = $"fmainorgid=@fmainorgid and fstatus='E' and fcancelstatus='0' and fsourcetype=@fsourcetype and fsourcenumber in ({string.Join(",", paramNames)})";

                var dm = userCtx.Container.GetService<IDataManager>();
                dm.InitDbContext(userCtx, ReserveBillFormMeta.GetDynamicObjectType(userCtx));
                var dataReader = userCtx.GetPkIdDataReader(ReserveBillFormMeta, where, paramList);
                reserveBills = dm.SelectBy(dataReader).OfType<DynamicObject>();
                userCtx.Container.GetService<LoadReferenceObjectManager>().Load(userCtx, ReserveBillFormMeta.GetDynamicObjectType(userCtx), reserveBills, false);
            }



            return reserveBills;
        }



        /// <summary>
        /// 释放预留时清空合同的仓库仓位
        /// </summary>
        private void UpdateOrderStockInfoByRelease(UserContext userCtx, HtmlForm demandForm, IEnumerable<DynamicObject> demandBills, IEnumerable<DynamicObject> reserveBills, IEnumerable<string> selectEntryIds, Enu_ReleaseType releaseType = Enu_ReleaseType.Release)
        {
            /*
             * 2、释放或取消释放预留时，对于源单类型为销售合同的，需要更新合同商品明细上的仓库仓位，需改按以下规则调整：
             * a、释放预留时，判断当前合同明细行已有下游关联的销售出库单，无需清空合同明细行上的仓库仓位；
             * b、取消释放预留时，判断当前合同明细行没有下游关联的销售出库单，需要反写更新合同明细行上的仓库仓位。
             * （注意：这里关联的是所有数据状态（重新审核、创建、已提交、已审核）且未作废的销售出库单）
             */
            if (!demandForm.Id.EqualsIgnoreCase("ydj_order"))
            {
                return;
            }

            if (demandBills.IsNullOrEmpty() || reserveBills.IsNullOrEmpty())
            {
                return;
            }

            var orderEnIds = reserveBills
                .SelectMany(s => (DynamicObjectCollection)s["fentity"])
                .Select(s => Convert.ToString(s["fsourceentryid"]))
                .ToList();
            if (selectEntryIds != null)
            {
                orderEnIds = orderEnIds.Where(s => selectEntryIds.Contains(s)).ToList();
            }

            // 判断是否关联下游销售出库单
            string sql = $@"
select distinct fsoorderentryid from t_stk_sostockout s with(nolock)
inner join t_stk_sostockoutentry se with(nolock) on s.fid=se.fid
where s.fmainorgid='{userCtx.Company}' and s.fcancelstatus='0' and se.fsoorderentryid in ({orderEnIds.JoinEx(",", true)})";

            var hasSoStockOutOrderEnIds = userCtx.ExecuteDynamicObject(sql, new List<SqlParam>())
                .Select(s => Convert.ToString(s["fsoorderentryid"]));

            foreach (var reserveBill in reserveBills)
            {
                var fsourcepkid = Convert.ToString(reserveBill["fsourcepkid"]);
                var demandBill =
                    demandBills.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(fsourcepkid));
                if (demandBill == null)
                {
                    continue;
                }

                var demandEntrys = demandBill["fentry"] as DynamicObjectCollection;

                //预留单--需求明细
                var reserveEnRows = reserveBill["fentity"] as DynamicObjectCollection;

                foreach (var reserveEnRow in reserveEnRows)
                {
                    var reserveTranceRows = reserveEnRow["fdetail"] as DynamicObjectCollection;
                    var fsourceentryid = Convert.ToString(reserveEnRow["fsourceentryid"]);
                    if (!orderEnIds.Contains(fsourceentryid))
                    {
                        continue;
                    }

                    var demandEntry = demandEntrys?.FirstOrDefault(s =>
                        Convert.ToString(s["id"]).EqualsIgnoreCase(fsourceentryid));
                    if (demandEntry == null)
                    {
                        continue;
                    }

                    // 判断当前预留量是否为0
                    var reserveQty = Convert.ToDecimal(reserveEnRow["fqty"]);

                    var isOut = hasSoStockOutOrderEnIds.Contains(fsourceentryid);

                    if (releaseType == Enu_ReleaseType.Release)
                    {
                        // 出现货，不清空
                        var isOutSpot = Convert.ToBoolean(demandEntry["fisoutspot"]);
                        if (isOutSpot)
                        {
                            continue;
                        }

                        // 有出库，不清空
                        if (isOut)
                        {
                            continue;
                        }

                        // 有预留量，更新为当前有预留量的仓库仓位
                        if (reserveQty > 0)
                        {
                            UpdateDemandBillStockInfo(userCtx, demandForm, demandBill, reserveEnRow, reserveTranceRows);
                            continue;
                        }

                        // 其他情况： 清空仓库仓位 
                        demandEntry["fstorehouseid"] = " ";
                        demandEntry["fstorelocationid"] = " ";
                    }
                    // 取消释放预留时，判断当前合同明细行没有下游关联的销售出库单，需要反写更新合同明细行上的仓库仓位。
                    else
                    {
                        if (!isOut)
                        {
                            UpdateDemandBillStockInfo(userCtx, demandForm, demandBill, reserveEnRow, reserveTranceRows);
                        }
                    }
                }
            }

            UpdateSourceOrder(userCtx, demandForm, demandBills);
        }


    }



}