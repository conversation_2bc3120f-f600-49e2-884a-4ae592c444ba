using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore;
using JieNor.AMS.YDJ.Stock.AppService.Common;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.DataTransferObject.Reserve;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Stock.AppService.Reserve.ReserveReleaseDialog
{
    /// <summary>
    /// 通用手动释放操作数据初始化
    /// </summary>
    [InjectService]
    [FormId("stk_reservereleasedialog")]
    [OperationNo("new")]
    public class New : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            var parentFormId = this.GetQueryOrSimpleParam<string>("parentFormId");
            var srcSelRows = this.GetQueryOrSimpleParam<string>("parentBillData").FromJson<List<SelectedRow>>();

            var parentForm = this.MetaModelService.LoadFormModel(this.Context, parentFormId);
            var parentBillId = srcSelRows.FirstOrDefault()?.PkValue;
            if (parentBillId.IsNullOrEmptyOrWhiteSpace() || parentBillId.IsEmptyPrimaryKey())
            {
                throw new BusinessException($"关联的{parentForm.Caption}不存在！");
            }

            Valid(parentForm, parentBillId);

            DynamicObject reserveBill;
            if (parentFormId.EqualsIgnoreCase("stk_reservebill"))
            {
                reserveBill = this.Context.LoadBizDataById("stk_reservebill", parentBillId);
            }
            else
            {
                reserveBill = ReserveUtil.GetReserveBillData(this.Context, parentFormId, parentBillId);
            }

            if (reserveBill == null)
            {
                throw new BusinessException("没有关联的预留单！");
            }

            var fsourcetype = Convert.ToString(reserveBill["fsourcetype"]);
            if (!fsourcetype.EqualsIgnoreCase("ydj_order") && !fsourcetype.EqualsIgnoreCase("ydj_saleintention"))
            {
                var sourceForm = this.MetaModelService.LoadFormModel(this.Context, fsourcetype);
                throw new BusinessException($"源单类型 {sourceForm.Caption} 不允许手动释放！");
            }

            //初始化数据
            this.InitData(e.DataEntitys[0], srcSelRows, reserveBill, parentFormId);

            CheckHasValidEntry(e.DataEntitys[0]);

            //根据基本单位数量自动反算关联业务单位数量字段（如库存单位，业务单位对应的数量）
            var unitService = this.Container.GetService<IUnitConvertService>();
            unitService.ConvertByBasQty(this.Context, this.HtmlForm, e.DataEntitys, this.Option);
        }

        /// <summary>
        /// 验证源单手动释放的校验是否通过
        /// </summary>
        /// <param name="parentForm"></param>
        /// <param name="parentBillId"></param>
        private void Valid(HtmlForm parentForm, string parentBillId)
        {
            var parentBill = this.Context.LoadBizDataById(parentForm.Id, parentBillId);
            if (parentBill == null) return;

            var manualrelease = parentForm.FormOperations.FirstOrDefault(s => s.Id.EqualsIgnoreCase("manualrelease"));
            if (manualrelease == null) return;

            List<IDataValidRule> lstRules = new List<IDataValidRule>();
            foreach (var validation in manualrelease.ServiceValidations)
            {
                var validationRule = this.Container.GetValidRuleService(validation.ValidationId);
                if (validationRule != null)
                {
                    validationRule.EntityKey = validation.EntityKey ?? manualrelease?.EntityKey ?? "fbillhead";
                    validationRule.Initialize(this.OperationContext.UserContext, validation);
                    if (validationRule is IBusinessValidRule)
                    {
                        validationRule.InitializeWithOperation(manualrelease);
                    }

                    lstRules.Add(validationRule);
                }
            }

            if (lstRules.Count == 0) return;

            var selectedRows = new List<SelectedRow>
            {
                new SelectedRow { PkValue = parentBillId, EntityKey = "fbillhead" }
            };
            var dataList = new[] { parentBill };

            var operCtx = this.Container.GetService<OperationContext>();
            operCtx.UserContext = this.Context;
            operCtx.Container = this.Container;
            operCtx.HtmlForm = parentForm;
            operCtx.HtmlOperation = manualrelease;
            operCtx.Option = this.Option;
            operCtx.DataEntities = dataList;
            operCtx.SelectedRows = selectedRows;
            operCtx.PageId = this.CurrentPageId;

            var validResult = ValidationManager.Validate(operCtx, parentForm, ref dataList, selectedRows, lstRules);
            if (!validResult.IsSuccess)
            {
                var strMsg = string.Join("\r\n", validResult.Errors.Take(50).Select(o => o.ErrorMessage));
                throw new BusinessException(strMsg);
            }
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        /// <param name="reserveReleaseData">预留释放</param>
        /// <param name="srcSelRows"></param>
        /// <param name="reserveBill"></param>
        /// <param name="parentFormId"></param>
        private void InitData(DynamicObject reserveReleaseData, List<SelectedRow> srcSelRows, DynamicObject reserveBill, string parentFormId)
        {
            FillEntityData(reserveReleaseData, srcSelRows, reserveBill, parentFormId);
        }

        /// <summary>
        /// 填充需求明细
        /// </summary>
        /// <param name="reserveReleaseData"></param>
        /// <param name="srcSelRows"></param>
        /// <param name="reserveBill"></param>
        /// <param name="parentFormId"></param>
        private void FillEntityData(DynamicObject reserveReleaseData, List<SelectedRow> srcSelRows, DynamicObject reserveBill, string parentFormId)
        {
            var entity = this.HtmlForm.GetEntryEntity("fentry");
            var entrys = entity.DynamicProperty.GetValue<DynamicObjectCollection>(reserveReleaseData);
            entrys.Clear();

            var selEnRows = srcSelRows?
                .Where(p => p.EntryPkValue.IsNullOrEmptyOrWhiteSpace() == false)
                .Select(s => s.EntryPkValue)
                .ToList();

            // 预留需求明细
            var reserveDemandEntrys = (DynamicObjectCollection)reserveBill["fentity"];

            foreach (var reserveDemandEntryRow in reserveDemandEntrys)
            {
                var fsourceentryid = Convert.ToString(reserveDemandEntryRow["fsourceentryid"]);
                var freserveentryid = Convert.ToString(reserveDemandEntryRow["id"]);

                if (selEnRows != null && selEnRows.Count > 0)
                {
                    // 父表单为预留单时，用需求明细行id比较；其他单据时，用源单明细行id比较
                    string pkValue = parentFormId.EqualsIgnoreCase("stk_reservebill") ? freserveentryid : fsourceentryid;

                    if (!selEnRows.Contains(pkValue))
                    {
                        continue;
                    }
                }

                // 跟踪明细
                var traceEntrys = (DynamicObjectCollection)reserveDemandEntryRow["fdetail"];
                if (traceEntrys.IsNullOrEmpty()) continue;

                //按仓库+仓位+库存状态分组
                var currentGrps = traceEntrys.GroupBy(f => new
                {
                    fstorehouseid = Convert.ToString(f["fstorehouseid"]).Trim(),
                    fstockstatus = Convert.ToString(f["fstockstatus"]).Trim(),
                }).ToList();

                var fmaterialid = reserveDemandEntryRow["fmaterialid"];
                var fattrinfo = reserveDemandEntryRow["fattrinfo"];
                var fattrinfo_e = reserveDemandEntryRow["fattrinfo_e"];
                var fcustomdesc = reserveDemandEntryRow["fcustomdesc"];
                var fresultbrandid = reserveDemandEntryRow["fresultbrandid"];
                var funitid = reserveDemandEntryRow["funitid"];
                var fbizunitid = reserveDemandEntryRow["fbizunitid"];
                var fmtono = reserveDemandEntryRow["fmtono"];

                var fsourceformid = reserveDemandEntryRow["fsourceformid"];
                var fsourceinterid = reserveDemandEntryRow["fsourceinterid"];
                var fsourcebillno = reserveDemandEntryRow["fsourcebillno"];
                var freservepkid = reserveBill["id"];

                foreach (var currentGrp in currentGrps)
                {
                    // 预留量
                    var fqty = currentGrp.Sum(x =>
                        (x["fdirection_d"]?.ToString() == "0"
                            ? Convert.ToDecimal(x["fqty_d"])
                            : Convert.ToDecimal(x["fqty_d"]) * -1));
                    var fbizqty = currentGrp.Sum(x =>
                        (x["fdirection_d"]?.ToString() == "0"
                            ? Convert.ToDecimal(x["fbizqty_d"])
                            : Convert.ToDecimal(x["fbizqty_d"]) * -1));

                    if (fqty <= 0 || fbizqty <= 0) continue;

                    var settingEntryRow = new DynamicObject(entity.DynamicObjectType);
                    entrys.Add(settingEntryRow);

                    settingEntryRow["fmaterialid"] = fmaterialid;
                    settingEntryRow["fattrinfo"] = fattrinfo;
                    settingEntryRow["fattrinfo_e"] = fattrinfo_e;
                    settingEntryRow["fcustomdesc"] = fcustomdesc;
                    settingEntryRow["fresultbrandid"] = fresultbrandid;
                    settingEntryRow["funitid"] = funitid;
                    settingEntryRow["fbizunitid"] = fbizunitid;
                    settingEntryRow["fmtono"] = fmtono;

                    settingEntryRow["fsourceformid"] = fsourceformid;
                    settingEntryRow["fsourceinterid"] = fsourceinterid;
                    settingEntryRow["fsourceentryid"] = fsourceentryid;
                    settingEntryRow["fsourcebillno"] = fsourcebillno;

                    settingEntryRow["freservepkid"] = freservepkid;
                    settingEntryRow["freserveentryid"] = freserveentryid;

                    settingEntryRow["fqty"] = fqty;
                    settingEntryRow["fbizqty"] = fbizqty;
                    settingEntryRow["fstorehouseid"] = currentGrp.Key.fstorehouseid;
                    settingEntryRow["fstockstatus"] = currentGrp.Key.fstockstatus;

                    settingEntryRow["freleaseqty"] = fqty;
                    settingEntryRow["fbizreleaseqty"] = fbizqty;
                }
            }
        }
         
        private void CheckHasValidEntry(DynamicObject data)
        {
            var entrys = (DynamicObjectCollection)data["fentry"];

            if (!entrys.Any(s => Convert.ToDecimal(s["fqty"]) > 0))
            {
                throw new BusinessException("无预留量可手动释放！");
            }
        }
    }


}