using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InvCloseAccount
{
    /// <summary>
    /// 关账
    /// </summary>
    [InjectService]
    [FormId("stk_invcloseaccount")]
    [OperationNo("closeaccount")]
    public class CloseAccount : AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
            e.PermItem = PermConst.PermssionItem_New;
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if(e.DataEntitys==null||
                e.DataEntitys.Length == 0)
            {
                throw new BusinessException("当前上下文数据不存在，无法关账！");
            }

            DateTime closeDate;
            if(!DateTime.TryParse( Convert.ToString( e.DataEntitys.FirstOrDefault()["fclosedate"]),out closeDate))
            {
                throw new BusinessException("请输入合法的关账日期后再执行此操作！");
            }
            if (closeDate.Year == DateTime.Now.Year && closeDate.Month == DateTime.Now.Month)
            {
                throw new BusinessException("本月还未结束，不允许进行关账！");
            }

            var distributedLock = $"CostCalulateLock:{this.Context.Company}";
            var cacheValue = this.CacheClient.Get<string>(distributedLock);
            if (!cacheValue.IsNullOrEmptyOrWhiteSpace())
            {
                //如果缓存里面有，则表示前一次的成本计算还在进行中
                throw new BusinessException("后台还在拼命的计算成本，请稍等！");
            }

            DateTime beginDate;
            var stkSvc = this.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
            var closePeriod = stkSvc.GetNearClosePeriod(this.Context);
            if (!closePeriod.HasValue)
            {
                var systemProfileService = this.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<ISystemProfile>();
                var iniDate = systemProfileService.GetSystemParameter<DateTime>(this.Context, "stk_invcompleteinit", "finitdate");
                beginDate = Convert.ToDateTime(iniDate).AddDays(1);
            }
            else 
            {
                beginDate = closePeriod.Value.AddDays (1);
            }

            var closeSvc = this.Container.GetService<IStockBaseService>();
            //获取最近关账日期
            var dtLatestCloseDate = closeSvc.GetLatestInventoryCloseDate(this.Context);
            if (dtLatestCloseDate.HasValue && dtLatestCloseDate.Value > closeDate)
            {
                throw new BusinessException($"当前设置的关账日期小于或等于上期关账日期：{dtLatestCloseDate.Value}");
            }
            var diffMonth = ((closeDate.Year - beginDate.Year) * 12) + closeDate.Month - beginDate.Month;
            var successCount = 0;//记录成功月份的次数
            for (int i = 0; i <= diffMonth; i++)
            { 
                var date = (new DateTime(beginDate.Year, beginDate.Month, 1)).AddMonths(i+1).AddDays(-1);                 
                //执行关账服务
                var result = closeSvc?.CloseAccount(this.Context, date, this.Option);
                this.Result.MergeResult(result);
                if (!result.IsSuccess) break;
                successCount++;
            }

            //当有月份成功关账，则后台异步执行成本计算逻辑
            if (successCount > 0)
            {
                //异步成本计算前，增加缓存锁
                CreateCacheLock(distributedLock);
                //后台更新成本
                Task task = new Task(() =>
                {
                    var calPara = new Core.DataEntity.CostCalculatePara()
                    {
                        EndDate = DateTime.Now,
                        IsWriteLog = false,
                        StockIn = true,
                        StockOut = true,
                    };

                    calPara.BeginDate = new DateTime(beginDate.Year, beginDate.Month, 1);
                    var svc = this.Container.GetService<Core.Interface.StockUpdate.ICostCalulateService>();
                    var costCalRes = svc.CostCalcByEnd(this.Context, calPara, this.Option);
                });
                ThreadWorker.QuequeTask(task, result =>
                {
                //异步任务处理完成，释放缓存的锁定标记
                this.CacheClient.Remove(distributedLock);

                    if (result?.Exception != null)
                    {
                    }
                });
            }

            this.AddRefreshPageAction();
        }

        /// <summary>
        /// 创建缓存锁
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <returns></returns>
        private void CreateCacheLock(string cacheKey)
        {
            // 获取流水号
            ISequenceService seqSvc = this.Container.GetService<ISequenceService>();
            var tranId = seqSvc.GetSequence<string>();

            // 缓存起来
            var ExpirySeconds = 30 * 60;//缓存失效时间：30分钟
            this.CacheClient.Set(cacheKey, tranId, TimeSpan.FromSeconds(ExpirySeconds));
        }
    }
}
