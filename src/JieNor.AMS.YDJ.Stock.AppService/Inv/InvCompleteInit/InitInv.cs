using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.CustomException;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InvCompleteInit
{
    /// <summary>
    /// 库存结束初始化
    /// </summary>
    [InjectService]
    [FormId("stk_invcompleteinit")]
    [OperationNo("initinv")]
    public class InitInv:AbstractOperationServicePlugIn
    {
        public override void OnCheckPermssion(OnCheckPermssionArgs e)
        {
            base.OnCheckPermssion(e);
            e.PermItem = PermConst.PermssionItem_New;
        }
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if(e.DataEntitys==null 
                || e.DataEntitys.Length == 0)
            {
                throw new BusinessException("当前上下文数据不可以为空！");
            }

            var systemProfileService = this.Container.GetService<ISystemProfile>();
            var initStatus = systemProfileService.GetSystemParameter(this.Context, this.HtmlForm.Id, "finitstatus", "");
            if (initStatus.EqualsIgnoreCase("invinitstatus_type_02"))
            {
                throw new BusinessException("当前库存系统已结束初始化，不需要再次操作！");
            }

            //初始化相关业务对象的存储模型
            var initStockBillMeta = this.MetaModelService.LoadFormModel(this.Context, "stk_initstockbill");
            var inventoryBalanceMeta = this.MetaModelService.LoadFormModel(this.Context, "stk_inventorybalance");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, initStockBillMeta.GetDynamicObjectType(this.Context));
            dm.InitDbContext(this.Context, inventoryBalanceMeta.GetDynamicObjectType(this.Context));

            //调用库存关账服务形成库存初始余额，检查系统不允许存在关账记录及未审核的初始库存单据
            var strCheckSql = $@"
select top 1 1
from t_stk_initstockbill
where fmainorgid=@currentCompanyId and fstatus<>'{BillStatus.E.ToString()}'
";
            var lstParams = new List<SqlParam>
            {
                new SqlParam("currentCompanyId", System.Data.DbType.String,this.Context.Company),
            };

            using(var reader=this.DBService.ExecuteReader(this.Context,strCheckSql, lstParams))
            {
                if (reader.Read())
                {
                    throw new BusinessException("存在未审核的初始库存单据，不允许结束初始化！");
                }
            }
                        
            //设置为库存结束初始化引起的关账服务
            var result = this.Container.GetService<IStockBaseService>()?.CreateInitInventoryBalance(this.Context, this.Option);
            this.Result.MergeResult(result);

            e.DataEntitys[0]["finitstatus"] = "invinitstatus_type_02";
            e.DataEntitys[0]["finitdate"] = result.SrvData;

            systemProfileService.SaveSystemParameter(this.Context,this .HtmlForm, e.DataEntitys.First(), this.Option); 
            this.AddRefreshPageAction();
        }

        
    }
}
