using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：取消调出
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("cancelallotout")]
    public class CancelAllotOut: AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            this.Result.MsgStyle = (int)Enu_MsgStyle.Dialog;  //弹窗提示
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                string fbillno = Convert.ToString(newData["fbillno"]);
                var sql = $"select 1 from t_bcm_transfertask where (ftask_type = 'transferout' or  ftask_type='transferin')and fsourcenumber = '{fbillno}'";
                var data = this.DBService.ExecuteDynamicObject(this.Context, sql);
                return data == null || !data.Any();
            }).WithMessage("当前库存调拨单【{0}】存在调出扫描任务或调入扫描任务, 不允许取消调出 !", (newData, oldData) => newData["fbillno"]));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                bool isstockout= Convert.ToBoolean(newData["fisstockout"]);
                return isstockout;
            }).WithMessage("当前库存调拨单【{0}】还未调出, 不允许取消调出 !", (newData, oldData) => newData["fbillno"]));
        }


        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null) return;
            foreach (var item in e.DataEntitys)
            {
                item["fisstockout"] = false;//已分步式调出
                var entitys = item["fentity"] as DynamicObjectCollection;
                foreach (var en in entitys)
                {
                    //en["fstockoutqty"] = 0;//分步式调出数量
                    //en["fstockoutbizqty"] = 0;//分步式调出基本单位数量
                    en["fstockoutdate"] = null;//分布式调出日期
                }
            }
            //保存
            this.Context.SaveBizData("stk_inventorytransfer", e.DataEntitys);
        }
    }
}
