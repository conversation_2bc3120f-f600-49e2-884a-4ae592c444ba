using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：获取库存调拨单调拨类型
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("getinvtransfertype")]
    public class GetInvTransferType: AbstractOperationServicePlugIn
    {
        protected UserContext AgentContext { get; set; }
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            var agentId = this.GetQueryOrSimpleParam<string>("agentid");//经销商ID
            this.AgentContext = this.Context.CreaeteaAgentDBContextWithUser(agentId);
            var svc = this.AgentContext.Container.GetService<IBillTypeService>();
            var billTypeInfos = svc.GetBillTypeInfors(this.AgentContext, this.HtmlForm);
             
            List<BillType> BillTypes = new List<BillType>();
            if (!billTypeInfos.Any())
            {
                this.Result.IsSuccess = false;
                this.Result.SrvData = BillTypes;
                this.Result.SimpleMessage = "返回数据为空,请检查对应参数。";
            }
            foreach (var item in billTypeInfos)
            { 
                var row = new BillType();
                row.billType = new
                {
                    id = item.fid,
                    fnumber = item.fnumber,
                    fname = item.fname
                };
                BillTypes.Add(row);
            }
            this.Result.IsSuccess = true;
            this.Result.SrvData = BillTypes;
            this.Result.SimpleMessage = "加载数据成功!";
        }
    }

    public class BillType
    {
        public object billType { get; set; }
    }
}
