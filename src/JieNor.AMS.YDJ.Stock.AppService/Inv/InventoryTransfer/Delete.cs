using JieNor.AMS.YDJ.Core.Helpers;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.AMS.YDJ.Store.AppService.Helper;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：删除
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            DirectHelper.Delete(this.Context, e.DataEntitys);
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var svc = this.Container.GetService<IReserveUpdateService>();
            var result = svc.DeleteReserve(this.Context, this.HtmlForm, e.DataEntitys, this.Option);

            this.Result.MergeResult(result);
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys.IsNullOrEmptyOrWhiteSpace() || !e.DataEntitys.Any())
            {
                return;
            }

            string mainorgid = this.Context.Company;
            IDBService service = this.DBService;
            foreach (var item in e.DataEntitys)
            {
                string sourcebillno = Convert.ToString(item["fsourcenumber"]);

                IndbqtyUpdateHelper.ndbqtyUpdate(this.Context, mainorgid, sourcebillno, service);
            }



        }

    }
}
