using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：校验是否需要同步
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("checkzyneedsync")]
    public class CheckZYNeedSync : AbstractOperationServicePlugIn
    {

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (!this.Context.IsDirectSale) return;

            var Ids = this.GetQueryOrSimpleParam<string>("Ids");
            if (!Ids.IsNullOrEmptyOrWhiteSpace())
            {
                string[] fids = Ids.Split(',');
                e.DataEntitys = this.Context.LoadBizDataById(this.HtmlForm.Id, fids.ToList()).ToArray();
            }
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            if (e.DataEntitys.Count() > 1)
            {
                throw new BusinessException("库存调拨单不允许批量操作，请单条操作！");
            }
            // 加载数据
            var refObjMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refObjMgr?.Load(this.Context, e.DataEntitys, true, this.HtmlForm, new List<string> { "fstorehouseid", "fstorehouseidto" });

            foreach (var orderItem in e.DataEntitys)
            {
                var outStore =Convert.ToString(orderItem["fstore"]);
                var inStore = Convert.ToString(orderItem["fstoreto"]);
                var entitys = orderItem["fentity"] as DynamicObjectCollection;
                bool allSameStore = true;
                //foreach (var entity in entitys)
                //{
                //    // 获取调出仓库和调入仓库的门店编号
                //    var storehouseOut = entity["fstorehouseid_ref"] as DynamicObject;
                //    var storehouseIn = entity["fstorehouseidto_ref"] as DynamicObject;
                //    var outStore = storehouseOut != null ? Convert.ToString(storehouseOut["fmulstore"]) : "";
                //    var inStore = storehouseIn != null ? Convert.ToString(storehouseIn["fmulstore"]) : "";

                //}
                // 若有一行不一致，则需要走接口
                if (outStore != inStore)
                {
                    allSameStore = false;
                }
                if (!allSameStore)
                {
                    // 走接口
                    this.Result.SrvData = true;
                }
                else
                {
                    // 所有明细调出仓库和调入仓库的门店都一致，不走接口
                    this.Result.SrvData = false;
                }
            }
        }
    }
}
