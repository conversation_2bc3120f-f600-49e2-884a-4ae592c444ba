using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Sal.SoStockReturn
{
    /// <summary>
    /// 销售退货单：打码
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("Pack")]
    public class Pack : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var data = e.DataEntitys.FirstOrDefault();
            this.Result.IsSuccess = true;
            if (data == null)
                return;
            var poBill = this.Context.LoadBizDataByFilter("bcm_packorder", $" fsourcetype = 'stk_sostockreturn' and fsourcenumber = '{Convert.ToString(data["fbillno"])}'").FirstOrDefault();
            if(poBill == null)
            {
                this.Result.IsSuccess = false;
                return;
            }
            var parentPageId = this.CurrentPageId.IsNullOrEmptyOrWhiteSpace() ? Guid.NewGuid().ToString("N") : this.CurrentPageId;
            var metaSetting = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "bcm_packorder");
            var action = this.Context.ShowSpecialForm(metaSetting, poBill, false, parentPageId, Enu_OpenStyle.Modal, Enu_DomainType.Bill);
            this.Result.HtmlActions.Add(action);
        }
    }
}
