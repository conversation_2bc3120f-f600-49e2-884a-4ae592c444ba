using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Stock.AppService.OpValidation.SoStockReturn;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Sal.SoStockReturn
{
    /// <summary>
    /// 销售退货单：反审核
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("UnAudit")]
    public class UnAudit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 预处理规则校验
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            IOrderService orderService = this.Container.GetService<IOrderService>();
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                return orderService.ChangeOrSubmitStatus(this.Context, newData);
            }).WithMessage("对不起，上游销售合同变更中，已禁止此操作！"));

            e.Rules.Add(new UnAuditValidation());
        }

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var soStockReturnService = this.Container.GetService<ISoStockReturnService>();
            soStockReturnService.InitService(this.Context, this.HtmlForm);
            soStockReturnService.WriteBackOrder(e.DataEntitys, true);

            //获取源头单据销售出库单信息
            var sostockoutNos = e.DataEntitys.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "stk_sostockout").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var sostockouts = this.Context.LoadBizDataByFilter("stk_sostockout", " fbillno in ('{0}') ".Fmt(string.Join("','", sostockoutNos)));

            //获取源头单据销售合同信息
            var fsourcenumbers = sostockouts.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "ydj_order").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var orders = this.Context.LoadBizDataByFilter("ydj_order", " fbillno in ('{0}') ".Fmt(string.Join("','", fsourcenumbers)));

            foreach (var order in orders)
            {
                Core.Helpers.DocumentStatusHelper.CalcOrderCloseStatus(order,this.Context);
            }

            if (orders.Count > 0)
            {
                this.Context.SaveBizData("ydj_order", orders);
            }
        }
    }
}
