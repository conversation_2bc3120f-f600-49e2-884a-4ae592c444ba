using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity.Barcode;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService
{
    /// <summary>
    /// 条码管理服务
    /// </summary>
    [InjectService]
    public class BarcodeMgrService : IBarcodeMgrService
    {

        /// <summary>
        /// 数据库访问服务
        /// </summary>
        [InjectProperty]
        protected IDBService DBService { get; set; }

        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected IMetaModelService MetaModelService { get; set; }

        /// <summary>
        /// 条码必须要匹配的维度属性
        /// </summary>
        private string[] MustMatchBarcodeFlexKeys = new string[]
        {
            "fmaterialid",
            "fattrinfo",
            "fcustomdesc"
        };

        /// <summary>
        /// 条码尽量要匹配的维度属性：当条码有值时，必须要求需求方有值且相等或为空时才可以匹配，但条码没值时，则不管需求方有没有值都可以匹配
        /// </summary>
        private string[] PossibleMatchBarcodeFlexKeys = new string[]
        {
            "fmtono",
            "flotno",
            "fownertype",
            "fownerid",
            "fstockstatus"
        };

        /// <summary>
        /// 条码自由匹配的维度属性：即使需求方信息与条码里这个维度信息不同，也可以将此条码给这个需求方
        /// </summary>
        private string[] FreeMatchBarcodeFlexKeys = new string[]
        {            
            "fstorehouseid",
            "fstorelocationid"            
        };

        private static readonly string CST_PropKey_RestQty = "__restQty";
        private static readonly string CST_PropKey_AllocQty = "__allocQty";
        
        /// <summary>
        /// 按库存分录明细关联的条码信息匹配实际发生量
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        /// <param name="matchOption"></param>
        /// <returns>
        /// |--Item1:所有传入条码解析结果（是否匹配，是否消耗等）
        /// |--Item2:具体匹配的明细情况
        ///     |--Array:0
        ///         |--Item1:消耗的条码对象
        ///         |--Item2:条码匹配的分录对象
        ///         |--Item3:条码匹配的单据对象
        /// </returns>
        public Tuple<Dictionary<string, ResolvedBarcodeView>, Dictionary<DynamicObject, Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView, Dictionary<string,object>,decimal>>>>> MatchInvBillRowQty(UserContext userCtx, 
            HtmlForm htmlForm, 
            IEnumerable<DynamicObject> dataEntities, 
            MatchBarcodeOption matchOption)
        {
            var activeEntity = htmlForm.GetEntryEntity(matchOption.ActiveEntityKey);

            //获得所有条码编码信息
            var lstAllBarcodeNumber = matchOption.AdditionalBarCode
                .Where(o => o.GetValue("barcode", "").IsNullOrEmptyOrWhiteSpace())
                .Select(o => o.GetValue("scanNumber", ""))
                .Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                .Distinct()
                .ToArray();
            var dctBarcodeNumberMap = new Dictionary<string, string>();
            if (lstAllBarcodeNumber.Any())
            {
                using (var tran = userCtx.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                {
                    var tempTableName = this.DBService.CreateTempTableWithDataList(userCtx, lstAllBarcodeNumber, false);
                    var strSql = $@"select u1.fid,u1.fnumber from t_bcm_barcodemaster u1
                                inner join {tempTableName} temp on temp.fid=u1.fnumber
                                where u1.fmainorgid=@currCompanyId
                                ";
                    using (var reader = this.DBService.ExecuteReader(userCtx, strSql, new SqlParam("currCompanyId", DbType.String, userCtx.Company)))
                    {
                        while (reader.Read())
                        {
                            var barcodeId = reader[0] as string;
                            var barcodeNumber = reader[1] as string;
                            dctBarcodeNumberMap[barcodeNumber] = barcodeId;
                        }
                    }

                    tran.Complete();

                    DBService.DeleteTempTableByName(userCtx, tempTableName, true); 
                }
            }

            //获得所有条码解析数据
            var lstAllBarCode = matchOption.AdditionalBarCode
                .Select(o => o.GetValue("barcode", ""))
                .Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                .Union(dctBarcodeNumberMap.Values)
                .Distinct()
                .ToList();
            var resolvedBarcodeData = this.ResolveBarcode(userCtx, lstAllBarCode, OperateOption.Create());

            //备码时，只能使用在库可用条码
            foreach (var kvpItem in resolvedBarcodeData)
            {
                if (kvpItem.Value.Status != Enu_BarcodeStatus.Instock
                    && kvpItem.Value.Status != Enu_BarcodeStatus.Inused)
                {
                    kvpItem.Value.ResolvedCode = Enu_BarcodeResolvedResult.InvalidStatus;
                }
            }
            
            //将条码反向关联至单据分录
            var allLinkedToBillBarcode = matchOption.AdditionalBarCode
                .Where(o=>!o.GetValue("fsourceinterid","").IsEmptyPrimaryKey())
                .Select(o=>
                {
                    var barcode = o.GetValue("barcode", "");
                    if (barcode.IsNullOrEmptyOrWhiteSpace())
                    {
                        var barcodeNumber = o.GetValue("scanNumber", "");
                        dctBarcodeNumberMap.TryGetValue(barcodeNumber, out barcode);
                    }
                    return Tuple.Create(o.GetValue("barcode", ""), o.GetValue("sourceInterId", ""));
                })
                .ToArray();

            var dctBillConsumeMap = new Dictionary<DynamicObject, Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>>();
            //var lstConsumedBarcodeItems = new List<ResolvedBarcodeView>();

            foreach (var dataEntity in dataEntities)
            {
                ExtendedDataEntitySet dataEntitySet = new ExtendedDataEntitySet();
                dataEntitySet.Parse(userCtx, new DynamicObject[] { dataEntity }, htmlForm);
                var linkDataEntities = dataEntitySet.FindByEntityKey(activeEntity.Id);

                var billPkId = dataEntity["id"] as string;

                //找到本单关联的所有可能用来分配的条码
                var allLinkedBarcode = lstAllBarCode.Select(o =>
                {
                    var existObj = allLinkedToBillBarcode.FirstOrDefault(k => k.Item1.EqualsIgnoreCase(o));
                    if (existObj == null || existObj.Item2 == null || existObj.Item2.EqualsIgnoreCase(billPkId))
                    {
                        return Tuple.Create(o, existObj != null && existObj.Item2 != null);
                    }
                    return null;
                })
                .Where(o => o != null)
                .ToArray();

                //精确找到关联的未分配完的条码解析数据，并将之分组为可消耗的分组
                //将一包多件的条码强制分到一组，方便后续分配时，优先保障原关联的条码记录不被清除
                var linkResolvedData = resolvedBarcodeData.Where(o => allLinkedBarcode.FirstOrDefault(k => k.Item1.EqualsIgnoreCase(o.Key)) != null
                                                                            && o.Value.ResolvedCode == Enu_BarcodeResolvedResult.Resolved)
                    .Select(o => o.Value);

                //获取用户强制要求本次匹配的条码
                var barcodeViewFixedList = new List<ResolvedBarcodeView[]>();

                //获取可自由匹配的条码，先初步整理好待处理的条码队列（相对合理），为了方便回溯，此队列作为原始队列一直不变
                var barcodeViewSortList = new List<ResolvedBarcodeView[]>();

                this.TryRearrangeBarcodeConsumeList(userCtx, linkResolvedData, allLinkedBarcode, out barcodeViewFixedList, out barcodeViewSortList);

                //按条码核销匹配，必须是整个条码的数据被耗用完，才可以认为此条码被消耗                
                var fixConsumeResult = this.ConsumeBarcode(userCtx, htmlForm, linkDataEntities, barcodeViewFixedList, matchOption);

                //lstConsumedBarcodeItems.AddRange(fixConsumeResult.SelectMany(o => o.Value.Select(k => k.Item1)));

                //固定匹配还未消化完的，需要使用新条码信息进行再次匹配
                var dRestUnmatchQty = fixConsumeResult.Sum(o => (decimal)o.Key[CST_PropKey_RestQty]);
                if (dRestUnmatchQty>0m)
                {
                    var preconsumeResult = this.ConsumeBarcode(userCtx, htmlForm, linkDataEntities, barcodeViewSortList.ToList(), matchOption);
                    var dPreRestUnmatchQty = preconsumeResult.Sum(o => (decimal)o.Key[CST_PropKey_RestQty]);
                    int iFlag = 1;
                    while (iFlag < barcodeViewSortList.Count && dPreRestUnmatchQty > 0m)
                    {
                        var barcodeDynamicList = barcodeViewSortList.Skip(iFlag).Union(barcodeViewSortList.Take(iFlag)).ToList();
                        if (barcodeDynamicList.Any() == false) break;

                        var currentConsumeResult = this.ConsumeBarcode(userCtx, htmlForm, linkDataEntities, barcodeDynamicList, matchOption);
                        var dCurrRestUnmatchQty = currentConsumeResult.Sum(o => (decimal)o.Key[CST_PropKey_RestQty]);
                        //消耗后的整个结果，没有呈现收敛，则退出
                        if (dPreRestUnmatchQty <= dCurrRestUnmatchQty)
                        {
                            break;
                        }
                        preconsumeResult = currentConsumeResult;
                        iFlag++;
                    }

                    //获得最优解：记录清楚当前单据实体与条码间的关联关系，留待后面一起提交数据库
                    foreach(var kvpItem in preconsumeResult)
                    {
                        var lstTupleItem = fixConsumeResult[kvpItem.Key] as List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>;
                        if (lstTupleItem == null)
                        {
                            lstTupleItem = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
                            fixConsumeResult[kvpItem.Key] = lstTupleItem;
                        }
                        lstTupleItem.AddRange(kvpItem.Value);
                    }
                    //lstConsumedBarcodeItems.AddRange(preconsumeResult.SelectMany(o => o.Value.Select(k => k.Item1)));
                }

                //提交条码匹配的实际发生量
                var demanQtyField = htmlForm.GetField(matchOption.DemandQtyFieldKey);
                var priceField= htmlForm.GetField(matchOption.PriceFieldKey);
                var actualQtyField = htmlForm.GetField(matchOption.QtyFieldKey);
                var actualAmountField = htmlForm.GetField(matchOption.AmountFieldKey);
                foreach(var kvpItem in fixConsumeResult)
                {
                    var demandQty = demanQtyField.DynamicProperty.GetValue<decimal>(kvpItem.Key.DataEntity);
                    var restQty = (decimal)kvpItem.Key[CST_PropKey_RestQty];
                    var dPrice = priceField.DynamicProperty.GetValue<decimal>(kvpItem.Key.DataEntity);
                    actualQtyField?.DynamicProperty?.SetValue(kvpItem.Key.DataEntity, demandQty - restQty);
                    actualAmountField.DynamicProperty.SetValue(kvpItem.Key.DataEntity, (demandQty - restQty) * dPrice);
                }
                dctBillConsumeMap[dataEntity] = fixConsumeResult;
            }

            //提交整个条码匹配结果：生成条码关联的扫描记录
            //this.CommitConsumeResult(userCtx, htmlForm, dctBillConsumeMap, matchOption);

            //处理条码状态
            foreach (var kvpItem in resolvedBarcodeData)
            {
                //还处于resolved的，全部置为Unmatch的，未配套
                if (kvpItem.Value.ResolvedCode == Enu_BarcodeResolvedResult.Resolved)
                {
                    kvpItem.Value.ResolvedCode = Enu_BarcodeResolvedResult.UnMatch;
                }
            }

            return Tuple.Create(resolvedBarcodeData, dctBillConsumeMap);
        }


        /// <summary>
        /// 解析指定条码关联的商品维度信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="barcodeList"></param>
        /// <param name="option">
        /// |--
        /// </param>
        /// <returns></returns>
        public Dictionary<string, ResolvedBarcodeView> ResolveBarcode(UserContext userCtx, IEnumerable<string> barcodeList, OperateOption option)
        {
            Dictionary<string, ResolvedBarcodeView> resolvedData = new Dictionary<string, ResolvedBarcodeView>();
            if (barcodeList == null || barcodeList.Any() == false) return resolvedData;

            //初始化解析结果为待处理
            foreach(var barcode in barcodeList)
            {
                resolvedData[barcode] = new ResolvedBarcodeView()
                {
                    Id = barcode,
                };
            }

            var barcodeMeta = this.MetaModelService.LoadFormModel(userCtx, "bcm_barcodemaster");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, barcodeMeta.GetDynamicObjectType(userCtx));
            var allBarcodeObjs = dm.Select(barcodeList)
                .OfType<DynamicObject>();

            var strSQL = $@"
select u1.fmaterialid,u1.fattrinfo,u1.fcustomdesc,u1.funitid,u1.fqty,u1.flotno,u1.fmtono,u1.fentryid
        ,u2.fstorehouseid,u2.fstorelocationid,u1.fstockstatus,u1.fownertype,u1.fownerid
        ,u1.fstockunitid,u1.fstockqty
        ,u2.fpackindex,u2.fpackcount,u2.fpackgroup
        ,u2.fnumber
        ,u2.fmainorgid
        ,u2.fid,u2.fforbidstatus,u2.fbizstatus
from t_bcm_mastermtrlentry u1
inner join t_bcm_barcodemaster u2 on u1.fid=u2.fid
";
            using(var tran = userCtx.CreateTransaction())
            {
                var tempTableName = "";
                if (barcodeList.IsGreaterThan(10))
                {
                    tempTableName = this.DBService.CreateTempTableWithDataList(userCtx, barcodeList.Distinct(),false);
                    strSQL += $@"inner join {tempTableName} temp on temp.fid=u2.fid";
                }
                else
                {
                    strSQL += $@"where u2.fid in ({barcodeList.JoinEx(",", true)})";
                }

                var barcodeInfo = this.DBService.ExecuteDynamicObject(userCtx, strSQL);
                tran.Complete();

                if (!tempTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    DBService.DeleteTempTableByName(userCtx, tempTableName, true);
                }

                if (barcodeInfo.Any() == false) return resolvedData;

                var independPackageInfo = barcodeInfo.Where(o => Convert.ToInt64(o["fpackcount"]) == 1)
                        .ToList();
                
                //一包多件时，直接将明细数据进行打包
                foreach (var item in independPackageInfo)
                {
                    var forbidStatus = Convert.ToString(item["fforbidstatus"]).EqualsIgnoreCase("1");

                    var bizStatus = Convert.ToString(item["fbizstatus"]);
                    if (forbidStatus)
                    {
                        this.CreateBarcodeResolvedItem(userCtx, item, resolvedData, Enu_BarcodeResolvedResult.Forbidden);
                    }
                    else
                    {
                        this.CreateBarcodeResolvedItem(userCtx, item, resolvedData, Enu_BarcodeResolvedResult.Resolved);
                    }
                    
                }

                //处理一件多包时的配套分析
                ComputeMtrlSetInfo(userCtx, resolvedData, barcodeInfo.Except(independPackageInfo)
                                .ToArray());
            }

            return resolvedData;
        }

        
        /// <summary>
        /// 对出入库来源单据分录按关联条码的信息进行拆分
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="sourceDataEntities"></param>
        /// <param name="matchOption"></param>
        /// <returns></returns>
        public IEnumerable<Dictionary<string, object>> SplitInoutStockRowByBarcode(UserContext userCtx, HtmlForm htmlForm, IEnumerable<Dictionary<string,object>> sourceDataEntities, MatchBarcodeOption matchOption)
        {
            var allBillPkIds = sourceDataEntities.Select(o => o.GetValue("fbillhead_id", ""))
                .Where(o=>!o.IsEmptyPrimaryKey())
                .Distinct();

            //获取关联条码信息
            var dctLinkBarCode = this.GetLinkBarcode(userCtx, htmlForm, allBillPkIds);

            var lstAllBarCode = dctLinkBarCode.SelectMany(o => o.Value);
            var resolvedBarcodeData = this.ResolveBarcode(userCtx, lstAllBarCode, OperateOption.Create());

            //将条码反向关联至单据分录
            var allLinkedToBillBarcode = dctLinkBarCode
                .SelectMany(o=>o.Value.Select(k=>Tuple.Create(k,o.Key)))
                .ToArray();

            var dctBillConsumeMap = new Dictionary<IGrouping<string,Dictionary<string,object>>, Dictionary<Dictionary<string,object>, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>>();
            //var lstConsumedBarcodeItems = new List<ResolvedBarcodeView>();

            foreach (var sourceBillGroup in sourceDataEntities.GroupBy(o=>o.GetValue("fbillhead_Id","") as string))
            {
                var billPkId = sourceBillGroup.Key;

                //找到本单关联的所有可能用来分配的条码
                var allLinkedBarcode = allLinkedToBillBarcode.Where(o => o.Item2.EqualsIgnoreCase(billPkId))
                    .Select(o => Tuple.Create(o.Item1, true))
                    .ToArray();

                //精确找到关联的未分配完的条码解析数据，并将之分组为可消耗的分组
                //将一包多件的条码强制分到一组，方便后续分配时，优先保障原关联的条码记录不被清除
                var linkResolvedData = resolvedBarcodeData.Where(o =>
                    {
                        return allLinkedBarcode.FirstOrDefault(k => k.Item1.EqualsIgnoreCase(o.Key)) != null
                                                                            && o.Value.ResolvedCode == Enu_BarcodeResolvedResult.Resolved;
                    })
                    .Select(o => o.Value);

                //获取用户强制要求本次匹配的条码
                var barcodeViewFixedList = new List<ResolvedBarcodeView[]>();

                //获取可自由匹配的条码，先初步整理好待处理的条码队列（相对合理），为了方便回溯，此队列作为原始队列一直不变
                var barcodeViewSortList = new List<ResolvedBarcodeView[]>();

                this.TryRearrangeBarcodeConsumeList(userCtx, linkResolvedData, allLinkedBarcode, out barcodeViewFixedList, out barcodeViewSortList);

                //按条码核销匹配，必须是整个条码的数据被耗用完，才可以认为此条码被消耗                
                var fixConsumeResult = this.ConsumeBarcode2(userCtx, htmlForm, sourceBillGroup, barcodeViewFixedList, matchOption);

                //lstConsumedBarcodeItems.AddRange(fixConsumeResult.SelectMany(o => o.Value.Select(k => k.Item1)));

                //固定匹配还未消化完的，需要使用新条码信息进行再次匹配
                var dRestUnmatchQty = fixConsumeResult.Sum(o => (decimal)o.Key[CST_PropKey_RestQty]);
                if (dRestUnmatchQty > 0m)
                {
                    var preconsumeResult = this.ConsumeBarcode2(userCtx, htmlForm, sourceBillGroup, barcodeViewSortList.ToList(), matchOption);
                    var dPreRestUnmatchQty = preconsumeResult.Sum(o => (decimal)o.Key[CST_PropKey_RestQty]);
                    int iFlag = 1;
                    while (iFlag < barcodeViewSortList.Count && dPreRestUnmatchQty > 0m)
                    {
                        var barcodeDynamicList = barcodeViewSortList.Skip(iFlag).Union(barcodeViewSortList.Take(iFlag)).ToList();
                        if (barcodeDynamicList.Any() == false) break;

                        var currentConsumeResult = this.ConsumeBarcode2(userCtx, htmlForm, sourceBillGroup, barcodeDynamicList, matchOption);
                        var dCurrRestUnmatchQty = currentConsumeResult.Sum(o => (decimal)o.Key[CST_PropKey_RestQty]);
                        //消耗后的整个结果，没有呈现收敛，则退出
                        if (dPreRestUnmatchQty <= dCurrRestUnmatchQty)
                        {
                            break;
                        }
                        preconsumeResult = currentConsumeResult;
                        iFlag++;
                    }

                    //获得最优解：记录清楚当前单据实体与条码间的关联关系，留待后面一起提交数据库
                    foreach (var kvpItem in preconsumeResult)
                    {
                        var lstTupleItem = fixConsumeResult[kvpItem.Key] as List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>;
                        if (lstTupleItem == null)
                        {
                            lstTupleItem = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
                            fixConsumeResult[kvpItem.Key] = lstTupleItem;
                        }
                        lstTupleItem.AddRange(kvpItem.Value);
                    }
                    //lstConsumedBarcodeItems.AddRange(preconsumeResult.SelectMany(o => o.Value.Select(k => k.Item1)));
                }
                
                dctBillConsumeMap[sourceBillGroup] = fixConsumeResult;
            }

            //来源单对象
            List<Dictionary<string, object>> lstSourceDataObjs = new List<Dictionary<string, object>>();

            var allBarcodeFlexItems = this.MustMatchBarcodeFlexKeys.Union(this.PossibleMatchBarcodeFlexKeys).Union(this.FreeMatchBarcodeFlexKeys).ToArray();
            var allBillFlexItems = allBarcodeFlexItems.ConvertFlexFieldKeys(matchOption.InvFlexFieldSetting);

            //根据实际匹配情况对源头数据进行拆分
            foreach(var kvpItem in dctBillConsumeMap)
            {
                foreach(var subKvpItem in kvpItem.Value)
                {
                    //按关联的条码及维度信息分组
                    var allSourceGroupsByFlex = subKvpItem.Value.GroupBy(o => o.Item2.GetValues(allBarcodeFlexItems));

                    foreach (var sourceGroupObj in allSourceGroupsByFlex)
                    {
                        var sourceObj = subKvpItem.Key.ToDictionary(k => k.Key, v => v.Value, StringComparer.OrdinalIgnoreCase);
                        int pos = 0;
                        foreach(var billFlexKey in allBillFlexItems)
                        {
                            sourceObj[billFlexKey] = sourceGroupObj.Key.Values[pos];
                            pos++;
                        }
                        sourceObj[matchOption.QtyFieldKey] = sourceGroupObj.Sum(o => o.Item3);
                        lstSourceDataObjs.Add(sourceObj);
                    }
                }
            }

            //处理条码状态
            foreach (var kvpItem in resolvedBarcodeData)
            {
                //还处于resolved的，全部置为Unmatch的，未配套
                if (kvpItem.Value.ResolvedCode == Enu_BarcodeResolvedResult.Resolved)
                {
                    kvpItem.Value.ResolvedCode = Enu_BarcodeResolvedResult.UnMatch;
                }
            }

            return lstSourceDataObjs;
        }

        /// <summary>
        /// 获取业务单据关联的条码信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        public Dictionary<string, IEnumerable<string>> GetLinkBarcode(UserContext userCtx, HtmlForm htmlForm, IEnumerable<object> billPkIds)
        {
            if (billPkIds == null
                || billPkIds.Any() == false) return new Dictionary<string, IEnumerable<string>>();

            var barcodeRecordMeta = this.MetaModelService.LoadFormModel(userCtx, "bcm_scanresult");

            var dctAllBarcode = new Dictionary<string, IEnumerable<string>>();
            using (var tran = userCtx.CreateTransaction())
            {
                var tempTableName = "";
                  var strSQL = $@"
select u1.fbarcode,u1.fsourceinterid
from {barcodeRecordMeta.BillHeadTableName} u1
";
                var allBillPkIds = billPkIds
                    .Distinct();
                if (allBillPkIds.IsGreaterThan(10))
                {
                    tempTableName = this.DBService.CreateTempTableWithDataList(userCtx, allBillPkIds,false);
                    strSQL += $@"
inner join {tempTableName} temp on temp.fid=u1.fsourceinterid
where u1.fsourceformid='{htmlForm.Id}'
";
                }
                else
                {
                    strSQL += $@"where u1.fsourceformid='{htmlForm.Id}' and u1.fsourceinterid in ({allBillPkIds.Select(o => $"'{o}'").JoinEx(",", false)})";
                }

                using (var reader = this.DBService.ExecuteReader(userCtx, strSQL))
                {
                    while (reader.Read())
                    {
                        var sourceInterId = reader[1] as string;
                        IEnumerable<string> lstItems = new List<string>();
                        if(!dctAllBarcode.TryGetValue(sourceInterId,out lstItems))
                        {
                            lstItems = new List<string>();
                            dctAllBarcode[sourceInterId] = lstItems;
                        }
                        ((List<string>)lstItems).Add(reader[0] as string);
                    }
                }
                tran.Complete();

                if (!tempTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    DBService.DeleteTempTableByName(userCtx, tempTableName, true);
                }
            }

            return dctAllBarcode;
        }


        /// <summary>
        /// 计算条码配套信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="resolvedData"></param>
        /// <param name="needCheckItems"></param>
        private void ComputeMtrlSetInfo(UserContext userCtx, Dictionary<string, ResolvedBarcodeView> resolvedData, DynamicObject[] needCheckItems)
        {
            if (needCheckItems == null
                || needCheckItems.Any() == false) return;

            DynamicObjectType dtTempInfo = needCheckItems.First().DynamicObjectType;

            //对于一件多包的条码进行齐套检查
            var dataGroupKey = new DataEntityGroupKey(new string[] { "fpackgroup", "fpackcount" }, dtTempInfo);
            var barcodeSetGroups = needCheckItems.GroupBy(o => dataGroupKey.GetKey(o));
            foreach (var group in barcodeSetGroups)
            {
                //记录各个序号的个数，最后计算最小齐套数
                var dctPackSet = new Dictionary<int, int>();
                var packCount = Convert.ToInt32(group.Key.Values[1]);
                for (int i = 1; i <= packCount; i++)
                {
                    dctPackSet[i] = 0;
                }

                foreach (var dataObj in group)
                {
                    var packIndex = Convert.ToInt32(dataObj["fpackindex"]);
                    dctPackSet[packIndex]++;
                }
                //获得最大套数，实际取所有包序号的最小数，就是这一批条码能满足的最大套数
                var maxMtrlSetQty = dctPackSet.Min(k => k.Value);

                //将条码全部标为已解释，并约定本次解析到的最大配套量
                group.ToList()
                    .ForEach(o =>
                    {
                        this.CreateBarcodeResolvedItem(userCtx, o, resolvedData, Enu_BarcodeResolvedResult.Resolved, maxMtrlSetQty);
                    });

                
            }
        }

        /// <summary>
        /// 根据条码数据对象创建条码解析对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="barcodeDataEntity"></param>
        /// <param name="resolvingData"></param>
        /// <param name="resolvedCode"></param>
        /// <param name="maxItemSet"></param>
        private void CreateBarcodeResolvedItem(UserContext userCtx, 
            DynamicObject barcodeDataEntity, 
            Dictionary<string,ResolvedBarcodeView> resolvingData, 
            Enu_BarcodeResolvedResult resolvedCode,
            int maxItemSet = 1)
        {
            if (barcodeDataEntity == null) return;

            InventoryFlexModel invFlexModel = InventoryFlexModel.GetInstance(userCtx);

            Dictionary<string, object> dctItem = new Dictionary<string, object>();
            dctItem["id"] = barcodeDataEntity["fentryid"] as string;
            
            DynamicProperty dp = null;
            foreach (var fieldKey in invFlexModel.InvFlexFieldKeys)
            {
                dctItem[fieldKey] = null;
                if(barcodeDataEntity.DynamicObjectType.Properties.TryGetValue(fieldKey,out dp))
                {
                    dctItem[fieldKey] = dp.GetValue(barcodeDataEntity);
                }
            }

            if (barcodeDataEntity.DynamicObjectType.Properties.TryGetValue(invFlexModel.QtyFieldKey, out dp))
            {
                dctItem[invFlexModel.QtyFieldKey] = dp.GetValue(barcodeDataEntity);
            }
            if (barcodeDataEntity.DynamicObjectType.Properties.TryGetValue(invFlexModel.StockQtyFieldKey, out dp))
            {
                dctItem[invFlexModel.StockQtyFieldKey] = dp.GetValue(barcodeDataEntity);
            }
            if (barcodeDataEntity.DynamicObjectType.Properties.TryGetValue(invFlexModel.MainOrgIdFieldKey, out dp))
            {
                dctItem[invFlexModel.MainOrgIdFieldKey] = dp.GetValue(barcodeDataEntity);
            }


            var barcodeId = barcodeDataEntity["fid"] as string;
            var barcode = barcodeDataEntity["fnumber"] as string;
            var packGroup = barcodeDataEntity["fpackgroup"] as string;
            var packCount = Convert.ToInt32(barcodeDataEntity["fpackcount"]);
            var packIndex = Convert.ToInt32(barcodeDataEntity["fpackindex"]);
            var stockId = barcodeDataEntity["fstorehouseid"] as string;
            var stockLocId = barcodeDataEntity["fstorelocationid"] as string;

            ResolvedBarcodeView resolvedResult = null;
            if(resolvingData.TryGetValue(barcodeId, out resolvedResult))
            {
                var bizStatus = Convert.ToString(barcodeDataEntity["fbizstatus"]);
                Enu_BarcodeStatus barCodeStatus;
                Enum.TryParse<Enu_BarcodeStatus>(bizStatus, out barCodeStatus);

                resolvedResult.Status = barCodeStatus;
                resolvedResult.ResolvedCode = resolvedCode;
                resolvedResult.BarCode = barcode;
                resolvedResult.PackCount = packCount;
                resolvedResult.PackIndex = packIndex;
                resolvedResult.PackGroup = packGroup;
                resolvedResult.MaxItemSet = maxItemSet;
                resolvedResult.StockId = stockId;
                resolvedResult.StockLocId = stockLocId;

                resolvedResult.ResolvedData.Add(dctItem);
            }
        }

        /// <summary>
        /// 消费条码信息，消费成功回置条码状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="entryDataObjs"></param>
        /// <param name="allResolvedBarcode"></param>
        /// <param name="matchOption"></param>
        private Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView,Dictionary<string,object>, decimal>>> ConsumeBarcode(UserContext userCtx, 
            HtmlForm htmlForm,
            ExtendedDataEntity[] entryDataObjs,
            IEnumerable<ResolvedBarcodeView[]> allResolvedBarcode,
            MatchBarcodeOption matchOption)
        {
            /**
             * 1、首先将输入的所有条码分成2类：强制优先匹配的条码和自由匹配的条码，2个队列。
             * 2、2个队列的条码的排序都遵循，越难匹配的优先级越高，也就是越要先进行匹配运算。每个条码匹配必须全部消耗完毕，才可以记为已使用
             * 3、条码里的商品信息与单据需求方匹配时，采用试算的机制，试算的数据暂时不更新回实际字段，待整个条码数据消耗完毕后再更新
             * 4、条码数据消耗完毕，将条码状态更新为已消耗，条码未消耗完毕，则保持原状态不变
             * ps:由于各类通知单上的库存维度，很多时候作不了数，因此在维度比较时采用以下原则：
             * a.强比较的维度包括（必须匹配，需求方必须有值）：商品+辅助属性+定制说明
             * b.弱比较的维度包括（尽可能匹配需求方的值，需求方没值，则都匹配）：物流跟踪号+批号+库存状态+货主类型+货主
             * b.自由比较的维度包括（可以不匹配，需求方有值时被覆盖）：仓库+仓位
             */

            var demandQtyField = htmlForm.GetField(matchOption.DemandQtyFieldKey);
            if (demandQtyField == null)
            {
                throw new ArgumentNullException("demandQtyFieldKey");
            }

            var dctResult = new Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>();
            for (var i = 0; i < entryDataObjs.Length; i++)
            {
                var dataObj = entryDataObjs[i];
                dataObj[CST_PropKey_RestQty] = demandQtyField.DynamicProperty.GetValue<decimal>(dataObj.DataEntity);
                dataObj[CST_PropKey_AllocQty] = 0m;                
                dctResult[dataObj] = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
            }
            
            var lstMustMatchBillFlexKeys = this.MustMatchBarcodeFlexKeys.ConvertFlexFieldKeys(matchOption.InvFlexFieldSetting)
                .Select(o => htmlForm.GetField(o).PropertyName)
                .ToArray();
            var lstPossibleMatchBillFlexKeys = this.PossibleMatchBarcodeFlexKeys.ConvertFlexFieldKeys(matchOption.InvFlexFieldSetting)
                .Select(o => htmlForm.GetField(o).PropertyName)
                .ToArray();

            
            foreach (var barcodeGroup in allResolvedBarcode)
            {
                
                if (barcodeGroup == null || barcodeGroup.Any() == false) continue;

                var packType = barcodeGroup.Length > 1 ? "1PCS:X" : "1PACK:X";

                //全部分配完毕
                var existRestEntryObjs = entryDataObjs.Where(o => (decimal)o[CST_PropKey_RestQty] > 0);
                if (!existRestEntryObjs.Any()) break;

                foreach (var barcodeView in barcodeGroup.OrderBy(o=>o.PackIndex))
                {
                    if (barcodeView.ResolvedData.Any() == false) continue;

                    bool bMatch = true;
                    int iSupplySet = 1;
                    if (packType == "1PCS:X")
                    {
                        iSupplySet = barcodeView.MaxItemSet;
                    }

                    var dctTryAllocateInfo = this.AllocateBarcodeData(
                        userCtx,
                        htmlForm,
                        barcodeView,
                        entryDataObjs,
                        lstMustMatchBillFlexKeys,
                        lstPossibleMatchBillFlexKeys,
                        matchOption,
                        out bMatch,
                        ref iSupplySet);

                    if (bMatch)
                    {
                        //顺利消耗的条码，提交数据合并至全局字典中
                        foreach (var kvpItem in dctTryAllocateInfo)
                        {
                            dctResult[kvpItem.Key].AddRange(kvpItem.Value);

                            //如果是一件多包时，通常这里只会分配给一个人
                            if (packType == "1PCS:X")
                            {
                                var allRestBarcodeViews = barcodeGroup.Skip(1).ToArray();
                                for(var i=0;i< allRestBarcodeViews.Length; i++)
                                {
                                    allRestBarcodeViews[i].ResolvedCode = Enu_BarcodeResolvedResult.Consumed;
                                    dctResult[kvpItem.Key].Add(Tuple.Create(allRestBarcodeViews[i], barcodeView.ResolvedData.First(), 0m));
                                }
                            }
                        }
                    }

                    //一件多包时，匹配一次后，无论成功与失败，都退出本次循环
                    if (packType == "1PCS:X")
                    {
                        break;
                    }
                }
            }


            return dctResult;
        }

        private Dictionary<ExtendedDataEntity,List<Tuple<ResolvedBarcodeView,Dictionary<string,object>,decimal>>> AllocateBarcodeData(UserContext userCtx,
            HtmlForm htmlForm,
            ResolvedBarcodeView barcodeView,
            ExtendedDataEntity[] entryDataObjs,
            string[] lstMustMatchBillFlexKeys,
            string[] lstPossibleMatchBillFlexKeys,
            MatchBarcodeOption matchOption,
            out bool bMatch,
            ref int iSupplySet)
        {
            var dctTryAllocateInfo = new Dictionary<ExtendedDataEntity, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>();
            bMatch = true;
            int iConsumeSet = 0;
            int iMaxSupplySet = iSupplySet;
            foreach (var mtrlItem in barcodeView.ResolvedData)
            {
                var mustFlexBarcodeValue = mtrlItem.GetValues(this.MustMatchBarcodeFlexKeys);
                var possibleFlexBarcodeValue = mtrlItem.GetValues(this.PossibleMatchBarcodeFlexKeys);
                
                //一件多包时存在多套时，按套消耗
                for (var step = 1; step <= iMaxSupplySet; step++)
                {
                    var existDataEntityObjs = entryDataObjs.Where(o => mustFlexBarcodeValue == o.DataEntity.GetValues(lstMustMatchBillFlexKeys)
                         && possibleFlexBarcodeValue.SetMatchMode(DataEntityGroupKey.Enu_MatchMode.PossibleMatch) == o.DataEntity.GetValues(lstPossibleMatchBillFlexKeys)
                         && (decimal)o[CST_PropKey_RestQty] > 0)
                     .ToArray();
                    //对应维度不匹配时，则此条码直接跳过
                    if (existDataEntityObjs.Any() == false)
                    {
                        bMatch = false;
                        break;
                    }

                    //取得条码能提供的数量
                    decimal dSupplyQty = (decimal)mtrlItem["fqty"];

                    //分配至待分配的单据分录行上
                    for (var i = 0; i < existDataEntityObjs.Length; i++)
                    {
                        var entryObj = existDataEntityObjs[i];

                        var restQty = (decimal)entryObj[CST_PropKey_RestQty];
                        var allocQty = (decimal)entryObj[CST_PropKey_AllocQty];
                        //临时分配完成
                        if (allocQty >= restQty) continue;

                        var maxAllocQty = Math.Min(dSupplyQty, restQty - allocQty);
                        dSupplyQty -= maxAllocQty;

                        entryObj[CST_PropKey_AllocQty] = allocQty + maxAllocQty;

                        var linkBarcodeItems = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
                        if (!dctTryAllocateInfo.TryGetValue(entryObj, out linkBarcodeItems))
                        {
                            linkBarcodeItems = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
                            dctTryAllocateInfo[entryObj] = linkBarcodeItems;
                        }

                        linkBarcodeItems.Add(Tuple.Create(barcodeView, mtrlItem, maxAllocQty));
                    }

                    //没有消耗完，则不再继续，说明本条码不匹配
                    if (dSupplyQty > 0m)
                    {
                        bMatch = false;
                        break;
                    }

                    iConsumeSet = step;
                }

                if (!bMatch)
                {
                    break;
                }
            }

            if (bMatch)
            {
                bMatch = this.CheckConsumeConstraint(userCtx, htmlForm, barcodeView, dctTryAllocateInfo.Keys.ToArray(), matchOption);
            }

            if (!bMatch)
            {
                //回滚预分配数据
                foreach (var kvpItem in dctTryAllocateInfo)
                {
                    var entryObj = kvpItem.Key;
                    entryObj[CST_PropKey_AllocQty] = 0m;
                }
                dctTryAllocateInfo.Clear();
                return dctTryAllocateInfo;
            }

            iSupplySet = iConsumeSet;
            //顺利消耗的条码，将试分配的数据从待分配的数据里减掉，并清掉已分配数据
            foreach (var kvpItem in dctTryAllocateInfo)
            {
                var entryObj = kvpItem.Key;
                entryObj[CST_PropKey_RestQty] = (decimal)entryObj[CST_PropKey_RestQty] - (decimal)entryObj[CST_PropKey_AllocQty];
                entryObj[CST_PropKey_AllocQty] = 0m;

                foreach(var tplItem in kvpItem.Value)
                {
                    tplItem.Item1.ResolvedCode = Enu_BarcodeResolvedResult.Consumed;
                }
            }

            return dctTryAllocateInfo;
        }


        /// <summary>
        /// 计算条码被消耗的权重
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="barcodeView"></param>
        /// <returns></returns>
        private decimal ComputeConsumedWeight(UserContext userCtx, ResolvedBarcodeView barcodeView)
        {
            /**
             * 对于一包多件的，必须采用最先消耗最多品类的包装条码，因为越多品类混合包装，被成功消耗的机率就越低，因此要越优先处理
             * 权重计算简单遵循如下函数：y=q+m*x+f1+f2+...fn,且m>=1000，
             *      1、q为包装里的商品总数量，
             *      2、m为品类权重因子，x为品类数，其中要求品类因子值够大，是为了达到品类优于商品种数m>=100
             *      3、f1为库存维度值对应权重因子
             *          a:物流跟踪号>批号>仓库>仓位>状态
             *               200     200   200  200   200
             * 当权重相同时，需要结合考虑动态回溯法取得需求方被匹配后以达到最小剩余量，则为相对较优
             */

            string[] flexKeys = new string[]
            {
                "fmtono",
                "flotno",
                "fstorehouseid",
                "fstorelocationid",
                "fstockstatus",
                "fownertype",
                "fownerid"
            };
            var dMtrlCategory = barcodeView.ResolvedData.GroupBy(o => Tuple.Create(o["fmaterialid"] as string, o["fattrinfo"] as string, o["fcustomdesc"] as string))
                .Select(o => {
                    //查找维度有值
                    decimal dPackSize = 0m;
                    decimal dFactor = 0m;

                    Dictionary<string, int> dctFlexItems = new Dictionary<string, int>();

                    foreach(var kvpItem in o)
                    {
                        dPackSize += Convert.ToDecimal(kvpItem["fqty"]);
                        foreach(var flexKey in flexKeys)
                        {
                            var flexValue = Convert.ToString(kvpItem.GetValue(flexKey, ""));
                            if (flexValue.IsEmptyPrimaryKey())
                            {
                                continue;
                            }
                            if (!dctFlexItems.ContainsKey(flexValue))
                            {
                                dctFlexItems[flexValue] = 1;
                            }
                            else
                            {
                                dctFlexItems[flexValue]++;
                            }
                        }
                    }

                    dFactor = dctFlexItems.Select(k => 200m * k.Value).Sum();

                    return new
                    {
                        Weight = dPackSize+dFactor
                    };
                })
                .ToList();

            return dMtrlCategory.Sum(o => o.Weight) + 100 * dMtrlCategory.Count;
        }

        /// <summary>
        /// 重建合理的条码消耗队列
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="allResolvedBarcode"></param>
        private void TryRearrangeBarcodeConsumeList(UserContext userCtx,
            IEnumerable<ResolvedBarcodeView> allResolvedBarcode,
            IEnumerable<Tuple<string, bool>> allLinkedBarcode,
            out List<ResolvedBarcodeView[]> barcodeViewFixedList,
            out List<ResolvedBarcodeView[]> barcodeViewSortList)
        {
            //整个条码序列将被分为必须匹配的与自由匹配的2个序列，这样在匹配不是最优解的时候，还留下手动调整的空间
            var barcodeGroups = allResolvedBarcode
                .GroupBy(o => Tuple.Create(o.PackCount == 1 ? "#Fixed" : o.PackGroup, o.PackCount, o.MaxItemSet))
                .ToArray();

            //获取用户强制要求本次匹配的条码
            barcodeViewFixedList = new List<ResolvedBarcodeView[]>();

            //获取可自由匹配的条码，先初步整理好待处理的条码队列（相对合理），为了方便回溯，此队列作为原始队列一直不变
            barcodeViewSortList = new List<ResolvedBarcodeView[]>();

            //先取得一包多件的数据进行计算权重
            var onePackXPcsGroups = barcodeGroups.Where(o => o.Key.Item1.EqualsIgnoreCase("#Fixed")).ToArray();
            foreach (var onePackXPcsGroup in onePackXPcsGroups)
            {
                foreach (var barcodeView in onePackXPcsGroup.OrderByDescending(o => this.ComputeConsumedWeight(userCtx, o)))
                {
                    var linkedBarcodeView = allLinkedBarcode.FirstOrDefault(o => o.Item1.EqualsIgnoreCase(barcodeView.Id));
                    var isFixedConsume = linkedBarcodeView?.Item2 == true;
                    if (isFixedConsume)
                    {
                        barcodeViewFixedList.Add(new ResolvedBarcodeView[] { barcodeView });
                    }
                    else
                    {
                        barcodeViewSortList.Add(new ResolvedBarcodeView[] { barcodeView });
                    }
                }
            }

            //再取一件多包的数据，并且按最大套数进行降序排列（本质上都遵循，越难消耗匹配完的，越优先处理，对于已和物流单据关联的条码放在强制消耗的队列中）
            var onePcsXPackGroups = barcodeGroups.Except(onePackXPcsGroups).ToArray();

            foreach (var barcodeGroup in onePcsXPackGroups.OrderByDescending(o => o.Key.Item3))
            {
                //计划作为固定消耗的条码信息：必须配套
                List<ResolvedBarcodeView> lstFixedGroup = new List<ResolvedBarcodeView>();
                List<ResolvedBarcodeView> lstFreeGroup = new List<ResolvedBarcodeView>();

                //临时记录一件多包时，有多少套条码已经被要求强制关联了
                var dctLinkedBarItems = new Dictionary<int, List<ResolvedBarcodeView>>();
                for (int packIndex = 1; packIndex <= barcodeGroup.Key.Item2; packIndex++)
                {
                    dctLinkedBarItems[packIndex] = barcodeGroup
                        .Where(o => o.PackIndex == packIndex
                                    && allLinkedBarcode.FirstOrDefault(k => k.Item1.EqualsIgnoreCase(o.Id) && k.Item2)?.Item2 == true).ToList();
                }
                var maxLinkedSet = dctLinkedBarItems.Min(o => o.Value.Count);
                if (maxLinkedSet > barcodeGroup.Key.Item3)
                {
                    maxLinkedSet = barcodeGroup.Key.Item3;
                }
                //将已经强制关联的套数的条码加入强制优先匹配队列，剩余的加入自由匹配队列
                for (int packIndex = 1; packIndex <= barcodeGroup.Key.Item2; packIndex++)
                {
                    var fixItems = dctLinkedBarItems[packIndex].Take(maxLinkedSet).ToArray();
                    lstFixedGroup.AddRange(fixItems);
                    lstFreeGroup.AddRange(barcodeGroup.Where(o => o.PackIndex == packIndex)
                        .Except(fixItems));
                }

                if (lstFixedGroup.Any())
                {
                    barcodeViewFixedList.Add(lstFixedGroup.ToArray());
                }
                if (lstFreeGroup.Any())
                {
                    barcodeViewSortList.Add(lstFreeGroup.ToArray());
                }
            }

        }



        /// <summary>
        /// 消费条码信息，消费成功回置条码状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="entryDataObjs"></param>
        /// <param name="allResolvedBarcode"></param>
        /// <param name="matchOption"></param>
        private Dictionary<Dictionary<string,object>, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>> ConsumeBarcode2(UserContext userCtx,
            HtmlForm htmlForm,
            IGrouping<string, Dictionary<string, object>> entryDataObjs,
            IEnumerable<ResolvedBarcodeView[]> allResolvedBarcode,
            MatchBarcodeOption matchOption)
        {
            /**
             * 1、首先将输入的所有条码分成2类：强制优先匹配的条码和自由匹配的条码，2个队列。
             * 2、2个队列的条码的排序都遵循，越难匹配的优先级越高，也就是越要先进行匹配运算。每个条码匹配必须全部消耗完毕，才可以记为已使用
             * 3、条码里的商品信息与单据需求方匹配时，采用试算的机制，试算的数据暂时不更新回实际字段，待整个条码数据消耗完毕后再更新
             * 4、条码数据消耗完毕，将条码状态更新为已消耗，条码未消耗完毕，则保持原状态不变
             * ps:由于各类通知单上的库存维度，很多时候作不了数，因此在维度比较时采用以下原则：
             * a.强比较的维度包括（必须匹配，需求方必须有值）：商品+辅助属性+定制说明
             * b.弱比较的维度包括（尽可能匹配需求方的值，需求方没值，则都匹配）：物流跟踪号+批号+库存状态+货主类型+货主
             * b.自由比较的维度包括（可以不匹配，需求方有值时被覆盖）：仓库+仓位
             */

            var demandQtyField = htmlForm.GetField(matchOption.DemandQtyFieldKey);
            if (demandQtyField == null)
            {
                throw new ArgumentNullException("demandQtyFieldKey");
            }

            var dctResult = new Dictionary<Dictionary<string, object>, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>();
            var billEntryObjs = entryDataObjs.ToArray();
            for (var i = 0; i < billEntryObjs.Length; i++)
            {
                var dataObj = billEntryObjs[i];
                dataObj[CST_PropKey_RestQty] = Convert.ToDecimal(dataObj[matchOption.DemandQtyFieldKey]);
                dataObj[CST_PropKey_AllocQty] = 0m;
                dctResult[dataObj] = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
            }

            var lstMustMatchBillFlexKeys = this.MustMatchBarcodeFlexKeys.ConvertFlexFieldKeys(matchOption.InvFlexFieldSetting)
                .Select(o => htmlForm.GetField(o).PropertyName)
                .ToArray();
            var lstPossibleMatchBillFlexKeys = this.PossibleMatchBarcodeFlexKeys.ConvertFlexFieldKeys(matchOption.InvFlexFieldSetting)
                .Select(o => htmlForm.GetField(o).PropertyName)
                .ToArray();


            foreach (var barcodeGroup in allResolvedBarcode)
            {

                if (barcodeGroup == null || barcodeGroup.Any() == false) continue;

                var packType = barcodeGroup.Length > 1 ? "1PCS:X" : "1PACK:X";

                //全部分配完毕
                var existRestEntryObjs = entryDataObjs.Where(o => (decimal)o[CST_PropKey_RestQty] > 0);
                if (!existRestEntryObjs.Any()) break;

                foreach (var barcodeView in barcodeGroup.OrderBy(o => o.PackIndex))
                {
                    if (barcodeView.ResolvedData.Any() == false) continue;

                    bool bMatch = true;
                    int iSupplySet = 1;
                    if (packType == "1PCS:X")
                    {
                        iSupplySet = barcodeView.MaxItemSet;
                    }

                    var dctTryAllocateInfo = this.AllocateBarcodeData2(userCtx,
                        htmlForm,
                        barcodeView,
                        entryDataObjs,
                        lstMustMatchBillFlexKeys,
                        lstPossibleMatchBillFlexKeys,
                        matchOption,
                        out bMatch,
                        ref iSupplySet);

                    if (bMatch)
                    {
                        //顺利消耗的条码，提交数据合并至全局字典中
                        foreach (var kvpItem in dctTryAllocateInfo)
                        {
                            dctResult[kvpItem.Key].AddRange(kvpItem.Value);

                            //如果是一件多包时，通常这里只会分配给一个人
                            if (packType == "1PCS:X")
                            {
                                var allRestBarcodeViews = barcodeGroup.Skip(1).ToArray();
                                for (var i = 0; i < allRestBarcodeViews.Length; i++)
                                {
                                    allRestBarcodeViews[i].ResolvedCode = Enu_BarcodeResolvedResult.Consumed;
                                    dctResult[kvpItem.Key].Add(Tuple.Create(allRestBarcodeViews[i], barcodeView.ResolvedData.First(), 0m));
                                }
                            }
                        }
                    }

                    //一件多包时，匹配一次后，无论成功与失败，都退出本次循环
                    if (packType == "1PCS:X")
                    {
                        break;
                    }
                }
            }


            return dctResult;
        }

        private Dictionary<Dictionary<string,object>, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>> AllocateBarcodeData2(
            UserContext userCtx,
            HtmlForm htmlForm,
            ResolvedBarcodeView barcodeView,
            IGrouping<string, Dictionary<string, object>> entryDataObjs,
            string[] lstMustMatchBillFlexKeys,
            string[] lstPossibleMatchBillFlexKeys,
            MatchBarcodeOption matchOption,
            out bool bMatch,
            ref int iSupplySet)
        {
            var dctTryAllocateInfo = new Dictionary<Dictionary<string, object>, List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>>();
            bMatch = true;
            int iConsumeSet = 0;
            int iMaxSupplySet = iSupplySet;
            foreach (var mtrlItem in barcodeView.ResolvedData)
            {
                var mustFlexBarcodeValue = mtrlItem.GetValues(this.MustMatchBarcodeFlexKeys);
                var possibleFlexBarcodeValue = mtrlItem.GetValues(this.PossibleMatchBarcodeFlexKeys);

                //一件多包时存在多套时，按套消耗
                for (var step = 1; step <= iMaxSupplySet; step++)
                {
                    var existDataEntityObjs = entryDataObjs.Where(o => mustFlexBarcodeValue == o.GetValues(lstMustMatchBillFlexKeys)
                         && possibleFlexBarcodeValue.SetMatchMode(DataEntityGroupKey.Enu_MatchMode.PossibleMatch) == o.GetValues(lstPossibleMatchBillFlexKeys)
                         && (decimal)o[CST_PropKey_RestQty] > 0)
                     .ToArray();
                    //对应维度不匹配时，则此条码直接跳过
                    if (existDataEntityObjs.Any() == false)
                    {
                        bMatch = false;
                        break;
                    }

                    //取得条码能提供的数量
                    decimal dSupplyQty = (decimal)mtrlItem["fqty"];

                    //分配至待分配的单据分录行上
                    for (var i = 0; i < existDataEntityObjs.Length; i++)
                    {
                        var entryObj = existDataEntityObjs[i];

                        var restQty = (decimal)entryObj[CST_PropKey_RestQty];
                        var allocQty = (decimal)entryObj[CST_PropKey_AllocQty];
                        //临时分配完成
                        if (allocQty >= restQty) continue;

                        var maxAllocQty = Math.Min(dSupplyQty, restQty - allocQty);
                        dSupplyQty -= maxAllocQty;

                        entryObj[CST_PropKey_AllocQty] = allocQty + maxAllocQty;

                        var linkBarcodeItems = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
                        if (!dctTryAllocateInfo.TryGetValue(entryObj, out linkBarcodeItems))
                        {
                            linkBarcodeItems = new List<Tuple<ResolvedBarcodeView, Dictionary<string, object>, decimal>>();
                            dctTryAllocateInfo[entryObj] = linkBarcodeItems;
                        }

                        linkBarcodeItems.Add(Tuple.Create(barcodeView, mtrlItem, maxAllocQty));
                    }

                    //没有消耗完，则不再继续，说明本条码不匹配
                    if (dSupplyQty > 0m)
                    {
                        bMatch = false;
                        break;
                    }

                    iConsumeSet = step;
                }

                if (!bMatch)
                {
                    break;
                }
            }

            //约束检查：例如回溯检查本条码的消耗对象如果存在多个，则看下这多个对象是否违背约束条件
            if (bMatch)
            {
                bMatch = this.CheckConsumeConstraint2(userCtx, htmlForm, barcodeView, dctTryAllocateInfo.Keys.ToArray(), matchOption);
            }

            if (!bMatch)
            {
                //回滚预分配数据
                foreach (var kvpItem in dctTryAllocateInfo)
                {
                    var entryObj = kvpItem.Key;
                    entryObj[CST_PropKey_AllocQty] = 0m;
                }
                dctTryAllocateInfo.Clear();
                return dctTryAllocateInfo;
            }

            iSupplySet = iConsumeSet;
            //顺利消耗的条码，将试分配的数据从待分配的数据里减掉，并清掉已分配数据
            foreach (var kvpItem in dctTryAllocateInfo)
            {
                var entryObj = kvpItem.Key;
                entryObj[CST_PropKey_RestQty] = (decimal)entryObj[CST_PropKey_RestQty] - (decimal)entryObj[CST_PropKey_AllocQty];
                entryObj[CST_PropKey_AllocQty] = 0m;

                foreach (var tplItem in kvpItem.Value)
                {
                    tplItem.Item1.ResolvedCode = Enu_BarcodeResolvedResult.Consumed;
                }
            }

            return dctTryAllocateInfo;
        }

        /// <summary>
        /// 回溯检查匹配后的数据集是否出现违反指定约束规则的情况
        /// 如：同一个条码消耗的不同分录在调拨场景下，要求其目的仓库必须一致
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="barcodeView"></param>
        /// <param name="entryDataObjs"></param>
        /// <param name="matchOption"></param>
        /// <returns></returns>
        private bool CheckConsumeConstraint(UserContext userCtx, HtmlForm htmlForm, ResolvedBarcodeView barcodeView, ExtendedDataEntity[] entryDataObjs, MatchBarcodeOption matchOption)
        {
            bool bMatch = true;
            if (matchOption.BackDateCheckStockId)
            {
                var stockIdFieldKey = matchOption.InvFlexFieldSetting.GetValue("fstorehouseid", "fstorehouseid");

                var stockIdField = htmlForm.GetField(stockIdFieldKey);
                if (stockIdField != null)
                {
                    var allStockIds = entryDataObjs.Select(o => stockIdField.DynamicProperty.GetValue<string>(o.DataEntity))
                        .Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                        .Distinct()
                        .ToArray();
                    bMatch = allStockIds.Length <= 1;
                }
            }

            return bMatch;
        }

        /// <summary>
        /// 回溯检查匹配后的数据集是否出现违反指定约束规则的情况
        /// 如：同一个条码消耗的不同分录在调拨场景下，要求其目的仓库必须一致
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="barcodeView"></param>
        /// <param name="dictionary"></param>
        /// <param name="matchOption"></param>
        /// <returns></returns>
        private bool CheckConsumeConstraint2(UserContext userCtx, HtmlForm htmlForm, ResolvedBarcodeView barcodeView, Dictionary<string, object>[] dictionary, MatchBarcodeOption matchOption)
        {
            bool bMatch = true;
            if(matchOption.BackDateCheckStockId)
            {
                var stockIdFieldKey = matchOption.InvFlexFieldSetting.GetValue("fstorehouseid", "fstorehouseid");
                var allStockIds = dictionary.Select(o => o.GetValue(stockIdFieldKey, ""))
                        .Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                        .Distinct()
                        .ToArray();
                bMatch = allStockIds.Length <= 1;
            }

            return bMatch;
        }

        /// <summary>
        /// 获取表单关联的扫描记录数据对象
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="dataEntity"></param>
        /// <param name="linkOption"></param>
        /// <returns></returns>
        public IEnumerable<DynamicObject> GetBillLinkScanResultData(UserContext userCtx, HtmlForm htmlForm, DynamicObject dataEntity, ScanResultLinkOption linkOption)
        {
            Dictionary<string, IEnumerable<object>> dctResult = new Dictionary<string, IEnumerable<object>>();
            ExtendedDataEntitySet ds = new ExtendedDataEntitySet();
            ds.Parse(userCtx, new DynamicObject[] { dataEntity }, htmlForm);
            var sourceBillIdField = htmlForm.GetField(linkOption.LinkBillIdFieldKey);
            var sourceFormIdField = htmlForm.GetField(linkOption.LinkFormIdFieldKey);

            DynamicProperty dpBillId, dpFormId;
            if (sourceBillIdField != null)
            {
                linkOption.ActiveEntityKey = sourceBillIdField.EntityKey;
                dpBillId = sourceBillIdField.DynamicProperty;
            }
            else
            {
                linkOption.ActiveEntityKey = "fbillhead";
                dpBillId = htmlForm.GetDynamicObjectType(userCtx).PrimaryKey;                
            }
            if (sourceFormIdField != null)
            {
                dpFormId = sourceFormIdField.DynamicProperty;
            }
            else
            {
                dpFormId = htmlForm.FormIdDynamicProperty;
            }

            if(dpBillId==null
                || dpFormId == null)
            {
                throw new ArgumentException("参数传递错误:linkFormIdFieldKey与linkBillIdFieldKey!");
            }

            var linkRowObjs = ds.FindByEntityKey(linkOption.ActiveEntityKey);
            foreach (var rowObj in linkRowObjs)
            {
                var linkFormId = dpFormId.GetValue<string>(rowObj.DataEntity);
                if (linkFormId.IsNullOrEmptyOrWhiteSpace()) continue;

                IEnumerable<object> lstPkIds = new List<object>();

                if (!dctResult.TryGetValue(linkFormId, out lstPkIds))
                {
                    lstPkIds = new List<object>();
                    dctResult[linkFormId] = lstPkIds;
                }
                var linkBillId = dpBillId.GetValue(rowObj.DataEntity);
                if (!lstPkIds.Contains(linkBillId) && !linkBillId.IsEmptyPrimaryKey())
                {
                    (lstPkIds as List<object>)?.Add(linkBillId);
                }
            }
            if (!dctResult.Any()) return new DynamicObject[] { };

            List<DynamicObject> lstBarcodeScanObjs = new List<DynamicObject>();

            var barcodeMasterMeta = this.MetaModelService.LoadFormModel(userCtx, "bcm_scanresult");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, barcodeMasterMeta.GetDynamicObjectType(userCtx));
            var tmbTbles = new List<string>();
            foreach (var kvpItem in dctResult)
            {
                using (var tran = userCtx.CreateTransaction())
                {
                    var strBarcodePkIdSql = $@"
                                                select u1.fid
                                                from t_bcm_scanresult u1
                                                ";
                    if (kvpItem.Value.IsGreaterThan(20))
                    {
                        var tempTableName = this.DBService.CreateTempTableWithDataList(userCtx, kvpItem.Value,false);
                        tmbTbles.Add(tempTableName);

                        strBarcodePkIdSql += $@"inner join {tempTableName} temp on temp.fid=u1.fsourceinterid
                                                where u1.fsourceformid=@sourceFormId
                                                ";
                    }
                    else
                    {
                        strBarcodePkIdSql += $@"where u1.fsourceformid=@sourceFormId and u1.fsourceinterid in ({kvpItem.Value.OfType<string>().JoinEx(",", true)})";
                    }

                    var reader = this.DBService.ExecuteReader(userCtx, strBarcodePkIdSql, new SqlParam("sourceFormId", DbType.String, kvpItem.Key));
                    var allScanObjs = dm.SelectBy(reader)
                        .OfType<DynamicObject>();
                    lstBarcodeScanObjs.AddRange(allScanObjs);

                    tran.Complete();
                }
            }

            if (tmbTbles.Count >0)
            {
                DBService.DeleteTempTableByName(userCtx, tmbTbles, true);
            }

            return lstBarcodeScanObjs;
        }
        
        /// <summary>
        /// 更新条码状态
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="htmlForm"></param>
        /// <param name="billDataEntity"></param>
        /// <param name="barcodeScanObjs"></param>
        /// <param name="status"></param>
        /// <param name="opName"></param>
        public void UpdateBarcodeStatus(UserContext userCtx, UpdateBarcodeTraceBill barcodeTraceInfo, IEnumerable<DynamicObject> barcodeScanObjs, Enu_BarcodeStatus status)
        {
            if (barcodeTraceInfo == null
                || barcodeScanObjs == null
                || barcodeScanObjs.Any() == false) return;

            var allBarcodePkIds = barcodeScanObjs.Select(o => o["fbarcode"] as string)
                .Where(o => !o.IsNullOrEmptyOrWhiteSpace())
                .Distinct();
            
            var barcodeMasterMeta = this.MetaModelService.LoadFormModel(userCtx, "bcm_barcodemaster");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, barcodeMasterMeta.GetDynamicObjectType(userCtx));
            var allBarcodeObjs = dm.Select(allBarcodePkIds)
                .OfType<DynamicObject>();

            var refMgrService = userCtx.Container.GetService<LoadReferenceObjectManager>();
            refMgrService.Load(userCtx, barcodeMasterMeta.GetDynamicObjectType(userCtx), allBarcodeObjs.ToArray(), true);

            var barcodeResultMeta = this.MetaModelService.LoadFormModel(userCtx, "bcm_scanresult");
            refMgrService.Load(userCtx, barcodeResultMeta.GetDynamicObjectType(userCtx), barcodeScanObjs.ToArray(), true);
            
            var bizStatusField = barcodeMasterMeta.GetField("fbizstatus") as HtmlSimpleSelectField;

            foreach (var barcodeObj in allBarcodeObjs)
            {
                var oldStatusName = bizStatusField.GetDisplayValue(userCtx, barcodeMasterMeta, barcodeObj, OperateOption.Create());

                barcodeObj["fbizstatus"] = ((int)status).ToString();

                var newStatusName = bizStatusField.GetDisplayValue(userCtx, barcodeMasterMeta, barcodeObj, OperateOption.Create());

                var strUpdateLog = $"{barcodeTraceInfo.OperationName}更新:";
                var linkScanResultObj = barcodeScanObjs.FirstOrDefault(o => (o["fbarcode"] as string).EqualsIgnoreCase(barcodeObj.GetPrimaryKeyValue<string>()));
                if (linkScanResultObj != null && !barcodeTraceInfo.UpdateStatusOnly)
                {
                    //设置条码最新的维度信息
                    this.UpdateBarcodeMasterFlexData(userCtx, barcodeMasterMeta, barcodeObj, barcodeResultMeta, linkScanResultObj, barcodeTraceInfo.Rollback, ref strUpdateLog);
                }

                //if (oldStatusName.EqualsIgnoreCase(newStatusName)) continue;

                //设置追溯记录
                var traceEntityObjs = barcodeObj["ftraceentity"] as DynamicObjectCollection;
                var traceRowObj = traceEntityObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                traceEntityObjs.Add(traceRowObj);
                traceRowObj["fsourcebizdate"] = barcodeTraceInfo.BillDate;
                traceRowObj["fsourceinterid"] = barcodeTraceInfo.BillId;
                traceRowObj["fsourcebillno"] = barcodeTraceInfo.BillNo;
                traceRowObj["fsourceformid"] = barcodeTraceInfo.FormId;

                traceRowObj["fstorehouseid"] = barcodeObj["fstorehouseid"];
                traceRowObj["fstorelocationid"] = barcodeObj["fstorelocationid"];

                traceRowObj["foperatorid"] = userCtx.UserId;
                traceRowObj["fopdatetime"] = DateTime.Now;
                                
                if (!oldStatusName.EqualsIgnoreCase(newStatusName))
                {
                    strUpdateLog += $"{ bizStatusField.Caption} ({ oldStatusName}->{ newStatusName});";
                }

                traceRowObj["fremark"] = strUpdateLog;
            }
            var prepareService = userCtx.Container.GetService<IPrepareSaveDataService>();
            prepareService.PrepareDataEntity(userCtx, barcodeMasterMeta, allBarcodeObjs.ToArray(), OperateOption.Create());
            dm.Save(allBarcodeObjs);
        }

        /// <summary>
        /// 根据条码的扫描记录更新条码对象的维度信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="barcodeMasterMeta"></param>
        /// <param name="barcodeObj"></param>
        /// <param name="scanResultMeta"></param>
        /// <param name="linkScanResultObj"></param>
        /// <param name="rollbackState">当前是否是回滚条码状态</param>
        /// <param name="strUpdateLog"></param>
        private void UpdateBarcodeMasterFlexData(UserContext userCtx, HtmlForm barcodeMasterMeta, DynamicObject barcodeObj, HtmlForm scanResultMeta, DynamicObject linkScanResultObj, bool rollbackState, ref string strUpdateLog)
        {
            if (barcodeObj == null
                || linkScanResultObj == null) return;

            ResolvedBarcodeView barcodeSnapViewData = null;
            if (rollbackState)
            {
                barcodeSnapViewData = Convert.ToString(linkScanResultObj["fbarcodesnap"]).FromJson<ResolvedBarcodeView>();
            }
            else
            {
                barcodeSnapViewData = Convert.ToString(linkScanResultObj["fbarcodenextsnap"]).FromJson<ResolvedBarcodeView>();
            }

            if (barcodeSnapViewData == null) return;

            var billDataService = userCtx.Container.GetService<IBillDataService>();

            var mtrlEntryObjs = barcodeObj["fentity"] as DynamicObjectCollection;

            var stockIdObj = billDataService.Load(userCtx, "ydj_storehouse", barcodeSnapViewData.StockId);
            if (stockIdObj!=null)
            {
                var oldStockIdObj = barcodeObj["fstorehouseid_ref"] as DynamicObject;
                if (!$"{oldStockIdObj?["fname"] ?? "N/A"}".EqualsIgnoreCase($"{stockIdObj?["fname"]}"))
                {
                    strUpdateLog += $"仓库({oldStockIdObj?["fname"] ?? "N/A"}->{stockIdObj["fname"]});";
                }
                var stockId = stockIdObj.GetPrimaryKeyValue();
                barcodeObj["fstorehouseid"] = stockId;
            }

            var stockLocIdObj = ((DynamicObjectCollection)stockIdObj["fentity"]).FirstOrDefault(o => o.GetPrimaryKeyValue<string>().EqualsIgnoreCase(barcodeSnapViewData.StockLocId));
            if (stockLocIdObj!=null)
            {
                var oldStockLocIdObj = barcodeObj["fstorelocationid_ref"] as DynamicObject;
                strUpdateLog += $"仓位({oldStockLocIdObj?["fnumber"] ?? "N/A"}->{stockLocIdObj["fnumber"]});";
                var stockLocId = stockLocIdObj.GetPrimaryKeyValue();
                barcodeObj["fstorelocationid"] = stockLocId;
            }
            
            
            foreach (var entryObj in mtrlEntryObjs)
            {
                var entryPkId = entryObj.GetPrimaryKeyValue<string>();
                var dctSnapData = barcodeSnapViewData.ResolvedData.FirstOrDefault(o => o.GetValue("id", "").ToString().EqualsIgnoreCase(entryPkId));
                if (dctSnapData == null) continue;

                //库存状态
                var stockStatusIdObj = billDataService.Load(userCtx, "ydj_stockstatus", dctSnapData["fstockstatus"] as string);
                var oldStockStatusIdObj = entryObj["fstockstatus_ref"] as DynamicObject;
                if (!$"{oldStockStatusIdObj?["fnumber"] ?? "N/A"}".EqualsIgnoreCase($"{stockStatusIdObj?["fnumber"]}"))
                {
                    strUpdateLog += $"库存状态({oldStockStatusIdObj?["fnumber"] ?? "N/A"}->{stockStatusIdObj["fnumber"]});";
                }
                var stockStatusId = stockStatusIdObj?.GetPrimaryKeyValue();
                entryObj["fstockstatus"] = stockStatusId;

                //货主类型
                var oldOwnerTypeField = barcodeMasterMeta.GetField("fownertype") as HtmlMulClassTypeField;
                var oldOwnerType = entryObj["fownertype"];
                var oldOwnerTypeName = oldOwnerTypeField.GetDisplayValue(userCtx, barcodeMasterMeta, entryObj, OperateOption.Create());
                var ownerType = dctSnapData["fownertype"] as string;
                entryObj["fownertype"] = ownerType;
                var ownerTypeName = oldOwnerTypeField.GetDisplayValue(userCtx, barcodeMasterMeta, entryObj, OperateOption.Create());
                if (!oldOwnerTypeName.EqualsIgnoreCase(ownerTypeName))
                {
                    strUpdateLog += $"货主类型({oldOwnerTypeName ?? "N/A"}->{ownerTypeName});";
                }

                //货主
                DynamicObject ownerIdObj = null;
                var ownerId = dctSnapData["fownerid"] as string;
                if (!ownerType.IsNullOrEmptyOrWhiteSpace())
                {
                    ownerIdObj = billDataService.Load(userCtx, ownerType, ownerId);
                }                
                var oldOwnerIdObj = entryObj["fownerid_ref"] as DynamicObject;
                if (!$"{oldOwnerIdObj?["fnumber"] ?? ""}".EqualsIgnoreCase($"{ownerIdObj?["fnumber"] ?? ""}"))
                {
                    strUpdateLog += $"货主({oldOwnerIdObj?["fnumber"] ?? "N/A"}->{ownerIdObj?["fnumber"]});";
                }
                entryObj["fownerid"] = ownerId;

                //批号
                var lotNo = dctSnapData["flotno"] as string;                
                var oldLotno = entryObj["flotno"] as string;
                if (!lotNo.EqualsIgnoreCase(oldLotno))
                {
                    strUpdateLog += $"批号({oldLotno ?? "N/A"}->{lotNo});";
                }
                entryObj["flotno"] = lotNo;

                //物流跟踪号
                var mtoNo = dctSnapData["fmtono"] as string;
                var oldMtono = entryObj["fmtono"] as string;
                if (!mtoNo.EqualsIgnoreCase(oldMtono))
                {
                    strUpdateLog += $"跟踪号({oldMtono ?? "N/A"}->{mtoNo});";
                }
                entryObj["fmtono"] = mtoNo;
            }

        }
    }
}
