using JieNor.AMS.YDJ.Core.DataEntity.Barcode;
using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Bcm.DeliveryNotice
{
    /// <summary>
    /// 处理条码的统一逻辑：发货通知类反审时，将扫描记录核验状态清空
    /// </summary>
    [InjectService]
    [FormId("(sal_deliverynotice|pur_returnnotice|stk_otherstockoutreq|stk_inventorytransferreq)")]
    [OperationNo("unaudit")]
    public class Unaudit : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 清空条码核验状态，变成重新作业的扫描任务
        /// </summary>
        /// <param name="e"></param>
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            if (e.DataEntitys == null) return;
            
            var barcodeMgrService = this.Container.GetService<IBarcodeMgrService>();

            var bizDateField = this.HtmlForm.GetField(this.HtmlForm.BizDateFldKey);
            var billNoField = this.HtmlForm.GetNumberField();

            List<DynamicObject> lstScanResultObjs = new List<DynamicObject>();

            foreach (var dataEntity in e.DataEntitys)
            {
                if (!this.IsEnableBarcode(dataEntity)) continue;

                var allScanObjs = barcodeMgrService.GetBillLinkScanResultData(this.Context, this.HtmlForm, dataEntity, new ScanResultLinkOption()
                {
                    ActiveEntityKey = "fbillhead"
                });

                if (!allScanObjs.Any()) continue;

                lstScanResultObjs.AddRange(allScanObjs);
            }
            foreach(var obj in lstScanResultObjs)
            {
                obj["fcheckuserid"] = "";
                obj["fcheckdatetime"] =null;
                obj["fbizstatus"] = "1";
            }
            var scanResultMeta = this.MetaModelService.LoadFormModel(this.Context, "bcm_scanresult");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, scanResultMeta.GetDynamicObjectType(this.Context));
            dm.Save(lstScanResultObjs);
        }


        /// <summary>
        /// 是否启用条码作业管理
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        protected virtual bool IsEnableBarcode(DynamicObject dataEntity)
        {
            return Convert.ToBoolean(dataEntity["fenablebarcode"]);
        }
    }
}
