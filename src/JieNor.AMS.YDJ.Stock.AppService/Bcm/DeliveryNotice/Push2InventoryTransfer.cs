using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Bcm.DeliveryNotice
{
    [InjectService]
    [FormId("stk_inventorytransferreq")]
    [OperationNo("push2inventorytransfer")]
    public class Push2InventoryTransfer : AbstractPushInOutStockForBCMServicePlugIn
    {
        protected override string GetConvertRuleId()
        {
            return "stk_inventorytransferreq2stk_inventorytransfer";
        }
    }
}
