using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Bcm.OutStock
{
    [InjectService]
    [FormId("(stk_otherstockout|stk_sostockout|stk_postockreturn)")]
    [OperationNo("audit")]
    public class Audit : AbstractUpdateBarcodeStatusServicePlugIn
    {
        protected override Enu_BarcodeStatus BarcodeStatus => Enu_BarcodeStatus.Outstock;

        protected override bool Rollback => false;

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            var datas = e.DataEntitys;
            var ids = datas.Select(f => f["Id"]?.ToString()).Distinct().ToList();
            List<string> sql = new List<string>();

            switch (this.HtmlForm.Id)
            {
                case "stk_sostockout":
                    //更新销售合同销售实发包数
                    sql.Add(@"/*dialect*/UPDATE yy SET yy.fbizoutpackqty =yy.fbizoutpackqty+zz.fdeliverypackageqty
                            FROM T_YDJ_ORDER xx
                            LEFT JOIN T_YDJ_ORDERENTRY yy ON yy.fid=xx.fid
                            INNER JOIN (
	                            SELECT b.fsoorderinterid,b.fsoorderentryid,SUM(b.fdeliverypackageqty) AS fdeliverypackageqty
	                            FROM t_stk_sostockout a
	                            LEFT JOIN t_stk_sostockoutentry b ON b.fid=a.fid
	                            WHERE 1=1 AND a.fmainorgid = '{0}' AND a.fid IN ('{1}')
	                            GROUP BY b.fsoorderinterid,b.fsoorderentryid
                            )zz ON zz.fsoorderinterid=xx.fid AND zz.fsoorderentryid = yy.fentryid
                            where xx.fmainorgid='{0}' ".Fmt(this.Context.BizOrgId, string.Join("','", ids)));
                    break;
                case "stk_postockreturn":
                    //更新采购订单采购退换包数
                    sql.Add(@"/*dialect*/UPDATE yy SET yy.fchangestockpackqty =yy.fchangestockpackqty+z.factualreturnpackqty
                            FROM T_YDJ_PURCHASEORDER xx
                            LEFT JOIN T_YDJ_POORDERENTRY yy ON yy.fid=xx.fid
                            INNER JOIN (
                            SELECT b.fpoorderinterid,b.fpoorderentryid,SUM(b.factualreturnpackqty) AS factualreturnpackqty
                            FROM t_stk_postockreturn a
                            LEFT JOIN t_stk_postockreturnentry b ON b.fid=a.fid
                            WHERE 1=1 AND a.fmainorgid = '{0}' AND a.fid IN ('{1}')
                            GROUP BY fpoorderinterid,fpoorderentryid
                            )z  ON z.fpoorderinterid=xx.fid AND z.fpoorderentryid = yy.fentryid
                            where xx.fmainorgid='{0}' ".Fmt(this.Context.BizOrgId, string.Join("','", ids)));
                    break;              
            }
            if (sql != null && sql.Count > 0)
            {
                var dbSvc = this.Container.GetService<IDBServiceEx>();
                dbSvc.ExecuteBatch(this.Context, sql);
            }
        }
    }
}
