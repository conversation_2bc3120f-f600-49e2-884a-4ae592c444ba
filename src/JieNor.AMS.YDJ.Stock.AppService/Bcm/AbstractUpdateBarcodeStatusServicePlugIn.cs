using JieNor.AMS.YDJ.Core.DataEntity.Barcode;
using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Bcm
{
    /// <summary>
    /// 更新条码状态的基类插件
    /// </summary>
    public abstract class AbstractUpdateBarcodeStatusServicePlugIn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 需要更新成什么状态
        /// </summary>
        protected abstract Enu_BarcodeStatus BarcodeStatus { get; }

        /// <summary>
        /// 回滚条码状态数据
        /// </summary>
        protected abstract bool Rollback { get; }
                
        /// <summary>
        /// 当前条码关联的主实体标识
        /// </summary>
        protected virtual string ActiveEntityKey
        {
            get
            {
                return "fentity";
            }
        }

        protected virtual string LinkBillIdFieldKey
        {
            get
            {
                return "fsourceinterid";
            }
        }

        protected virtual string LinkFormIdFieldKey
        {
            get
            {
                return "fsourceformid";
            }
        }

        /// <summary>
        /// 是否启用条码作业管理
        /// </summary>
        /// <param name="dataEntity"></param>
        /// <returns></returns>
        protected virtual bool IsEnableBarcode(DynamicObject dataEntity)
        {
            return Convert.ToBoolean(dataEntity["fenablebarcode"]);
        }
        
        /// <summary>
        /// 处理关联条码状态的更新
        /// </summary>
        /// <param name="e"></param>
        public sealed override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Any() == false) return;

            var barcodeMgrService = this.Container.GetService<IBarcodeMgrService>();
            var bizDateField = this.HtmlForm.GetField(this.HtmlForm.BizDateFldKey);
            var billNoField = this.HtmlForm.GetNumberField();
            foreach (var dataEntity in e.DataEntitys)
            {
                if (!this.IsEnableBarcode(dataEntity)) continue;

                var allScanObjs = barcodeMgrService.GetBillLinkScanResultData(this.Context, this.HtmlForm, dataEntity, new ScanResultLinkOption()
                {
                    ActiveEntityKey = this.ActiveEntityKey,
                    LinkBillIdFieldKey = this.LinkBillIdFieldKey,
                    LinkFormIdFieldKey = this.LinkFormIdFieldKey
                });

                if (!allScanObjs.Any()) continue;

                var bizDate = bizDateField?.DynamicProperty.GetValue<DateTime?>(dataEntity);

                UpdateBarcodeTraceBill barcodeOption = new UpdateBarcodeTraceBill(this.Rollback, false)
                {
                    FormId=this.HtmlForm.Id,
                    BillDate= bizDate.HasValue?bizDate.Value:DateTime.MinValue,
                    BillNo = billNoField?.DynamicProperty.GetValue<string>(dataEntity),
                    BillId=dataEntity["id"] as string,
                    OperationName=this.OperationName
                };
                                
                barcodeMgrService.UpdateBarcodeStatus(this.Context, barcodeOption, allScanObjs, this.BarcodeStatus);
            }
        }
    }
}
