using JieNor.AMS.YDJ.Core.Interface.BarcodeMgr;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Bcm.ScanResult
{
    [InjectService]
    [FormId("bcm_scanresult")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 增加删除校验规则
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            //var billNoField = this.HtmlForm.GetNumberField();
            //e.Rules.Add(this.RuleFor("fbillhead", dataObj => dataObj["fbizstatus"] as string)
            //    .IsTrue((dataObj, status) =>
            //    {
            //        return status.EqualsIgnoreCase("1");
            //    })
            //    .WithMessage("当前记录{0}已经过核验扫描，不允许删除！", (dataObj, bizStatus) => billNoField.DynamicProperty.GetValue<string>(dataObj)));
        }

        /// <summary>
        /// 操作前根据删除行为更新关联条码的状态
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            //if (e.DataEntitys == null) return;

            //Dictionary<DynamicObject,string> lstInstockingBarcodeIds = new Dictionary<DynamicObject, string>();
            //Dictionary<DynamicObject, string> lstInusedBarcodeIds = new Dictionary<DynamicObject, string>();

            //foreach(var dataEntity in e.DataEntitys)
            //{
            //    var scanSceneId = dataEntity["fscansceneid"] as string;
            //    switch (scanSceneId)
            //    {
            //        //上架入库时删除记录，需要同步删除条码主档
            //        case "1":
            //            lstInstockingBarcodeIds[dataEntity] = dataEntity["fbarcode"] as string;
            //            break;

            //        //发货准备，调拨
            //        case "2":
            //        case "3":
            //            lstInusedBarcodeIds[dataEntity] = dataEntity["fbarcode"] as string;
            //            break;
            //    }
            //}
            //var barcodeMasterMeta = this.MetaModelService.LoadFormModel(this.Context, "bcm_barcodemaster");
            //var dm = this.GetDataManager();
            //dm.InitDbContext(this.Context, barcodeMasterMeta.GetDynamicObjectType(this.Context));
            //if (lstInstockingBarcodeIds.Any())
            //{
            //    //直接作废条码主档
            //    var dctOption = new Dictionary<string, object>();
            //    var allInstockBillNos = lstInstockingBarcodeIds.Keys
            //        .Select(o => o["fsourcebillno"] as string)
            //        .Distinct()
            //        .JoinEx(",", false);
            //    dctOption["opDesc"] = $"收货{allInstockBillNos}扫描记录删除导致本条码被作废！";
            //    var result = this.Gateway.InvokeListOperation(this.Context, barcodeMasterMeta.Id,
            //        lstInstockingBarcodeIds.Values.Distinct().Select(pkId => new Framework.DataTransferObject.Poco.SelectedRow()
            //        {
            //            PkValue=pkId,
            //        }),
            //        "forbid",
            //        dctOption);
            //    this.Result.MergeResult(result);
            //}
            ////更新条码状态
            //if (lstInusedBarcodeIds.Any())
            //{
            //    var barcodeMgrService = this.Container.GetService<IBarcodeMgrService>();
            //    barcodeMgrService.UpdateBarcodeStatus(this.Context,)
            //}
        }
    }
}
