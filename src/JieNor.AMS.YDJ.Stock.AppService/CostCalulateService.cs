using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Enums;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework.CustomException;
using System.Data;
using JieNor.Framework.MetaCore;
using JieNor.Framework.Utils;


namespace JieNor.AMS.YDJ.Stock.AppService
{


    /// <summary>
    /// 成本计算
    /// </summary>
    [InjectService]
    public class CostCalulateService : ICostCalulateService
    {



        /// <summary>
        /// 获取最近成本计算的提示信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public string GetNearCostCalcPeriodMsg(UserContext ctx)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var sql = @"select fdesc from T_SYS_SYSTEMPROFILE  with(nolock) where fcategory ='fin' and fkey='costcalc' and fmainorgid ='{0}' ".Fmt(ctx.Company);
            var datas = dbSvc.ExecuteDynamicObject(ctx, sql);
            if (datas != null && datas.Count > 0)
            {
                return Convert.ToString(datas[0]["fdesc"]);
            }

            return "未进行过成本计算";
        }

        /// <summary>
        /// 获取最近成本计算期间
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public string GetNearCostCalcPeriod(UserContext ctx)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var sql = @"select fvalue from T_SYS_SYSTEMPROFILE with(nolock) where fcategory ='fin' and fkey='costcalc' and fmainorgid ='{0}' ".Fmt(ctx.Company);
            var datas = dbSvc.ExecuteDynamicObject(ctx, sql);
            if (datas != null && datas.Count > 0)
            {
                return Convert.ToString(datas[0]["fvalue"]);
            }

            return "";
        }


        /// <summary>
        /// 更新最新的成本计算期间
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public void AddOrUpdateCostCalcPeriod(UserContext ctx, DateTime? date)
        {
            var dbSvc = ctx.Container.GetService<IDBServiceEx>();
            var sql = @" delete from T_SYS_SYSTEMPROFILE where fcategory ='fin' and fkey='costcalc' and fmainorgid ='{0}' ; ".Fmt(ctx.Company);
            dbSvc.Execute(ctx, sql);

            if (date.HasValue && date.Value != DateTime.MinValue)
            {
                var month = date.Value.ToString("yyyy-MM");
                var calcMsg = "已计算的月份 {2} ：{0} {1} 进行了成本计算".Fmt(DateTime.Now, ctx.DisplayName, month);
                sql = @"insert into T_SYS_SYSTEMPROFILE(fmainorgid,fid,fcategory ,fkey ,fvalue,fdesc)
						 values('{0}','{1}','{2}','{3}','{4}','{5}')
						".Fmt(ctx.Company, Guid.NewGuid(), "fin", "costcalc", month, calcMsg);
                dbSvc.Execute(ctx, sql);
            }
        }






        /// <summary>
        /// 获取最近关账期间
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public DateTime? GetNearClosePeriod(UserContext ctx)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var sql = @"select fvalue from T_SYS_SYSTEMPROFILE  with(nolock) where fcategory ='stk' and fkey='closeperiod' and fmainorgid ='{0}' ".Fmt(ctx.Company);
            var datas = dbSvc.ExecuteDynamicObject(ctx, sql);
            if (datas != null && datas.Count > 0)
            {
                return Convert.ToDateTime(datas[0]["fvalue"]);
            }

            return null;
        }


        /// <summary>
        /// 更新最新的关账期间
        /// </summary>
        /// <param name="ctx"></param>
        /// <returns></returns>
        public void AddOrUpdateClosePeriod(UserContext ctx, DateTime? period)
        {
            var dbSvc = ctx.Container.GetService<IDBServiceEx>();
            var sql = @" delete from T_SYS_SYSTEMPROFILE where fcategory ='stk' and fkey='closeperiod' and fmainorgid ='{0}' ; ".Fmt(ctx.Company);
            dbSvc.Execute(ctx, sql);

            if (period.HasValue && period != DateTime.MinValue)
            {
                var date = new DateTime(period.Value.Year, period.Value.Month, period.Value.Day);
                sql = @"insert into T_SYS_SYSTEMPROFILE(fmainorgid,fid,fcategory ,fkey ,fvalue,fdesc)
						 values('{0}','{1}','{2}','{3}','{4}','{5}')
						".Fmt(ctx.Company, Guid.NewGuid(), "stk", "closeperiod", date, "{0}{1}进行关账".Fmt(DateTime.Now, ctx.DisplayName));
                dbSvc.Execute(ctx, sql);
            }
        }



        /// <summary>
        /// 重算成本：删除组织的库余额数据----用期初盘点单的成本更新库存余额----从初始化期间开始重算成本
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult CostCalcByEnd_Repeat(UserContext ctx, OperateOption option)
        {
            IOperationResult result = new OperationResult();

            result.ComplexMessage.SuccessMessages.Add("开始进行成本计算......");

            var calPara = new Core.DataEntity.CostCalculatePara()
            {
                EndDate = DateTime.Now,
                IsWriteLog = false,
                StockIn = true,
                StockOut = true,
                IniDate = GetStockIniDate(ctx),
            };
            if (calPara.IniDate.HasValue == false)
            {
                return result;
            }
            calPara.BeginDate = calPara.IniDate.Value;//从初始化期间开始算

            //先删除组织的库余额数据
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            var sql = "delete from T_STK_INVENTORYBALANCE where fmainorgid='{0}' ".Fmt(ctx.Company);
            dbSvcEX.Execute(ctx, sql);

            //用期初盘点单的成本更新库存余额
            sql = @"/*dialect*/
					insert into T_STK_INVENTORYBALANCE(fid,FFormId,fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,fqty,fstockunitid,fstockqty , famount,fcostprice,fcostamt,fbalancetype ,fclosedate)
					select newid () as fid, 'stk_inventorybalance' as FFormId ,'{0}' as fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,
							0 as fqty,fstockunitid,0 as fstockqty ,famount,fpdprice,case when b.fpyamount >0 and x.fdate< '{1}' then b.fpyamount when x.fdate< '{1}' then b.famount else 0 end as fpdamount,
							'inventorybalance_type_01' as fbalancetype,'{1}' as fclosedate
					from t_stk_invverifyentry b with(nolock) 
                    inner join t_stk_invverify x on b.fid=x.fid 
					inner join T_BD_BILLTYPE z on x.fbilltype=z.fid
					inner join (select max(b.fentryid) as fentryid
								from t_stk_invverifyentry b with(nolock) 
								inner join t_stk_invverify x on b.fid=x.fid 
								inner join T_BD_BILLTYPE z on x.fbilltype=z.fid
								where x.fmainorgid='{0}'  and fpdprice > 0  and (z.fid='inventoryverify_billtype_02' or z.fprimitiveid='inventoryverify_billtype_02') 
								group by fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
										flotno ,fmtono ,fownertype ,fownerid ,funitid, fstockunitid 
							   ) y on b.fentryid =y.fentryid

                        ".Fmt(ctx.Company, calPara.IniDate.Value);
            dbSvcEX.Execute(ctx, sql);

            //开始重算成本		
            DateTime startDate = calPara.BeginDate.Value;
            CostCalcAllPeriod(ctx, result, calPara, startDate, option);

            result.ComplexMessage.SuccessMessages.Add("成本计算完成！");

            return result;
        }


        public IOperationResult CostCalcByEnd_All(UserContext ctx, OperateOption option)
        {
            IOperationResult result = new OperationResult();

            result.ComplexMessage.SuccessMessages.Add("开始进行成本计算......");

            var calPara = new Core.DataEntity.CostCalculatePara()
            {
                EndDate = DateTime.Now,
                IsWriteLog = false,
                StockIn = true,
                StockOut = true,
                IniDate = GetStockIniDate(ctx),
            };
            if (calPara.IniDate.HasValue == false)
            {
                return result;
            }
            calPara.BeginDate = calPara.IniDate.Value;//从初始化期间开始算

            //先删除组织的库余额数据
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            var sql = "delete from t_stk_inventorybalance_repeat where fmainorgid='{0}' ".Fmt(ctx.Company);
            dbSvcEX.Execute(ctx, sql);

            //用期初盘点单的成本更新库存余额
            sql = @"/*dialect*/
					insert into t_stk_inventorybalance_repeat(fid,FFormId,fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,fqty,fstockunitid,fstockqty , famount,fcostprice,fcostamt,fbalancetype ,fclosedate)
					select newid () as fid, 'stk_inventorybalance' as FFormId ,'{0}' as fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,
							0 as fqty,fstockunitid,0 as fstockqty ,famount,fpdprice,case when b.fpyamount >0 and x.fdate< '{1}' then b.fpyamount when x.fdate< '{1}' then b.famount else 0 end as fpdamount,
							'inventorybalance_type_01' as fbalancetype,'{1}' as fclosedate
					from t_stk_invverifyentry b with(nolock) 
                    inner join t_stk_invverify x on b.fid=x.fid 
					inner join T_BD_BILLTYPE z on x.fbilltype=z.fid
					inner join (select max(b.fentryid) as fentryid
								from t_stk_invverifyentry b with(nolock) 
								inner join t_stk_invverify x on b.fid=x.fid 
								inner join T_BD_BILLTYPE z on x.fbilltype=z.fid
								where x.fmainorgid='{0}'  and fpdprice > 0  and (z.fid='inventoryverify_billtype_02' or z.fprimitiveid='inventoryverify_billtype_02') 
								group by fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
										flotno ,fmtono ,fownertype ,fownerid ,funitid, fstockunitid 
							   ) y on b.fentryid =y.fentryid

                        ".Fmt(ctx.Company, calPara.IniDate.Value);
            dbSvcEX.Execute(ctx, sql);

            //开始重算成本		
            DateTime startDate = calPara.BeginDate.Value;
            CostCalcAllPeriod_repeat(ctx, result, calPara, startDate, option);

            result.ComplexMessage.SuccessMessages.Add("成本计算完成！");

            return result;
        }

        private void CostCalcAllPeriod_repeat(UserContext ctx, IOperationResult result, CostCalculatePara calPara, DateTime startDate, OperateOption option)
        {
            var diffMonth = ((calPara.EndDate.Year - startDate.Year) * 12) + calPara.EndDate.Month - startDate.Month;
            for (int i = 0; i <= diffMonth; i++)
            {
                var nextMonth = startDate.AddMonths(i);
                if (i == 0)
                {
                    calPara.PeriodBeginDate = startDate;
                    calPara.PeriodEndDate = new DateTime(nextMonth.Year, nextMonth.Month, 1).AddMonths(1);
                }
                else
                {
                    calPara.PeriodBeginDate = new DateTime(nextMonth.Year, nextMonth.Month, 1);
                    calPara.PeriodEndDate = new DateTime(nextMonth.AddMonths(1).Year, nextMonth.AddMonths(1).Month, 1);
                }

                //计算对应期间的库存成本
                result.ComplexMessage.SuccessMessages.Add("  期间出入库成本计算开始：{0} -- {1}".Fmt(calPara.PeriodBeginDate.ToDate(), calPara.PeriodEndDate.ToDate()));
                CostCalculate_repeat(ctx, result, calPara, option);
                result.ComplexMessage.SuccessMessages.Add("  期间出入库成本计算结束：{0} -- {1}".Fmt(calPara.PeriodBeginDate.ToDate(), calPara.PeriodEndDate.ToDate()));

                //如果计算到了当前期间，则更新即时库存表的成本信息
                //if (calPara.PeriodBeginDate.Year == DateTime.Now.Year && calPara.PeriodBeginDate.Month == DateTime.Now.Month)
                //{
                //    result.ComplexMessage.SuccessMessages.Add("  开始更新即时库存成本信息");
                //    UpdateStockInvbalance(ctx, result, calPara);
                //    result.ComplexMessage.SuccessMessages.Add("  结束更新即时库存成本信息");
                //}
                //UpdateSalesOrderCostInfo(ctx, calPara.PeriodBeginDate, calPara.PeriodEndDate);
            }
        } 

        /// <summary>
        /// 成本计算重算，目的是滚动计算库存余额，但是不反写成本字段。
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="result"></param>
        /// <param name="calPara"></param>
        /// <param name="option"></param>
        private void CostCalculate_repeat(UserContext ctx, IOperationResult result, CostCalculatePara calPara, OperateOption option)
        {
            //期初结余数据
            var iniBalanceTbl = GetIniStockBalance(ctx, calPara);
            result.ComplexMessage.SuccessMessages.Add("    成功获取相关期初结余数据");

            //入库单据数据
            var stockInTbl = GetStockInDatas(ctx, calPara);
            result.ComplexMessage.SuccessMessages.Add("    成功获取相关入库单据数据");

            //更新入库单据的入库成本：部分入库单据，入库成本=期初结余成本
            UpdateStockInCost(ctx, calPara, iniBalanceTbl, stockInTbl);

            //反写源单入库成本 《销售退货单》《库存调拨单》上【单位成本】及【总成本】字段 
            //WriteBackStockInBillCost(ctx, calPara, iniBalanceTbl, stockInTbl);
            //result.ComplexMessage.SuccessMessages.Add("    成功更新源单入库成本信息（销售退货单、库存调拨单）");

            //入库成本计算
            var stockInBalanceTbl = CalcStockInCost(ctx, iniBalanceTbl, stockInTbl);
            result.ComplexMessage.SuccessMessages.Add("    完成入库成本计算");

            //获取出库数据
            var stockOutTbl = GetStockOutData(ctx, calPara);
            result.ComplexMessage.SuccessMessages.Add("    成功获取相关出库单据数据");

            //更新出库成本：部分出库单据的出库成本 = 入库成本计算后的结余成本
            CalculateStockOutCost(ctx, calPara, stockOutTbl, stockInBalanceTbl);

            //反写出库单据的成本信息
            //WriteBackStockOutBillCost(ctx, calPara, stockOutTbl);
            //result.ComplexMessage.SuccessMessages.Add("    成功更新出库单据的成本信息");

            //计算当前期间的成本结余 = 期初结余 + 当期入库 - 当期出库
            var endBalanceTbl = CalcCurrentPeriodStockCost(ctx, iniBalanceTbl, stockInBalanceTbl, stockOutTbl);
            result.ComplexMessage.SuccessMessages.Add("    完成出库成本计算");

            //更新当月的库存余额信息
            UpdateStockEndBalance_repeat(ctx, calPara, endBalanceTbl);
            result.ComplexMessage.SuccessMessages.Add("    成功更新当月的库存余额信息");

            //删除临时表（延迟删除，以便后台查临时表数据）
            var dbSvc = ctx.Container.GetService<IDBService>();
            dbSvc.DeleteTempTableByName(ctx, iniBalanceTbl, false);
            dbSvc.DeleteTempTableByName(ctx, stockInTbl, false);
            dbSvc.DeleteTempTableByName(ctx, stockOutTbl, false);
            dbSvc.DeleteTempTableByName(ctx, endBalanceTbl, false);
        }

        /// <summary>
        /// 更新当月的库存余额信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="stockOutTbl">出库单数据</param>
        /// <param name="stockInBalanceTbl">入库成本计算后的结余成本</param>
        private void UpdateStockEndBalance_repeat(UserContext ctx, CostCalculatePara calPara, string endBalanceTbl)
        {
            var nextMonth = calPara.PeriodEndDate;
            var closeDate = (new DateTime(nextMonth.Year, nextMonth.Month, 1)).AddDays(-1);//当月最后一天作为每月的关账日期 
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();

            var delFidTbl = CreateTempTableName(ctx, "tmpDelFid");
            var repeatSql = @"/*dialect*/
							--查询当期库存余额中要删除的库存维度重复的内码ID
							SELECT M.fid 
							INTO {3}
							FROM T_STK_INVENTORYBALANCE_repeat AS M WITH(NOLOCK) 
							INNER JOIN (
								SELECT MIN(fid) fid,b.fmainorgid,b.fmaterialid,b.fattrinfo_e,b.fcustomdesc,b.fstorehouseid,b.fstorelocationid,b.fstockstatus,b.flotno,b.fownertype,b.fownerid,b.funitid,b.fstockunitid,b.fmtono,b.fclosedate 
								FROM T_STK_INVENTORYBALANCE_repeat  b
								INNER JOIN {0} i on b.fmaterialid=i.fmaterialid and b.fattrinfo_e=i.fattrinfo_e 
															and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
															and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
															and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
															and b.fownertype=i.fownertype and b.fownerid=i.fownerid
															and b.funitid=i.funitid 
								WHERE b.fmainorgid='{1}' and b.fclosedate = '{2}'
								GROUP BY b.fmainorgid,b.fmaterialid,b.fattrinfo_e,b.fcustomdesc,b.fstorehouseid,b.fstorelocationid,b.fstockstatus,b.flotno,b.fownertype,b.fownerid,b.funitid,b.fstockunitid,b.fmtono,b.fclosedate
								HAVING COUNT(1)>1
							) AS T ON M.fmaterialid=T.fmaterialid and M.fattrinfo_e=T.fattrinfo_e 
									and M.fcustomdesc=T.fcustomdesc and M.fmtono=T.fmtono
									and M.fstorehouseid=T.fstorehouseid and M.fstorelocationid=T.fstorelocationid
									and M.fstockstatus=T.fstockstatus and M.flotno=T.flotno
									and M.fownertype=T.fownertype and M.fownerid=T.fownerid
									and M.funitid=T.funitid and M.fclosedate=T.fclosedate
									and M.fid!=T.fid
							".Fmt(endBalanceTbl, ctx.Company, closeDate, delFidTbl);
            dbSvcEX.Execute(ctx, repeatSql);

            var delSql = @"/*dialect*/
							--删除库存维度相同的重复数据
							DELETE FROM T_STK_INVENTORYBALANCE_repeat WHERE fid IN (SELECT fid FROM {0})
							".Fmt(delFidTbl);
            dbSvcEX.Execute(ctx, delSql);

            var opDes = $"{ctx.DisplayName ?? ctx.UserName ?? ctx.UserId}在{DateTime.Now}执行了更新当月的库存余额操作！";//操作描述
            var sql = @"/*dialect*/
                    --更新库存余额表
					update b set fqty = i.fqty , fstockqty = i.fstockqty, famount = i.famount, 
							 fcostprice = case when  i.fcostprice>0 then i.fcostprice else 0 end,
							 fcostamt = case when i.fcostamt>0 then i.fcostamt else 0 end,
							 fbeginqty =isnull(i.fbeginqty,0) ,fbeginstockqty=isnull(i.fbeginstockqty,0) ,
							 finicostprice=isnull(i.finicostprice,0),finicostamt=isnull(i.finicostamt,0),
							 finqty=isnull(i.finqty,0),finstockqty=isnull(i.finstockqty,0),
						     foutqty=isnull(i.foutqty,0),foutstockqty=isnull(i.foutstockqty,0),
							 fdescription='{3}'
					from T_STK_INVENTORYBALANCE_repeat  b
					inner join {0} i on b.fmaterialid=i.fmaterialid and b.fattrinfo_e=i.fattrinfo_e 
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid 
					where b.fmainorgid='{1}' and b.fclosedate = '{2}'
					".Fmt(endBalanceTbl, ctx.Company, closeDate, opDes.Replace("'", "''"));
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
					insert into T_STK_INVENTORYBALANCE_repeat(fid,FFormId,fmainorgid,fmaterialid  ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,fqty,fstockunitid,fstockqty , famount,fcostprice,fcostamt,fbalancetype ,fclosedate,
							fbeginqty,fbeginstockqty,finicostprice,finicostamt,finqty,finstockqty,foutqty,foutstockqty,fcreatedate,fdescription)
					select newid () as fid, FFormId , fmainorgid,fmaterialid  ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,
							fqty,fstockunitid,fstockqty ,famount,case when fcostprice>0 then fcostprice else 0 end as fcostprice,
							case when fcostamt>0 then fcostamt else 0 end as fcostamt,'inventorybalance_type_02' as fbalancetype,'{2}' as fclosedate,
							fbeginqty,fbeginstockqty,finicostprice,finicostamt,finqty,finstockqty,foutqty,foutstockqty,'{3}' as fcreatedate,'{4}' as fdescription
					from {0} b
					where not exists(select 1 from  T_STK_INVENTORYBALANCE_repeat i  with(nolock) 
										where b.fmaterialid=i.fmaterialid and b.fattrinfo_e=i.fattrinfo_e 
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid and i.fmainorgid='{1}' and i.fclosedate = '{2}' )
                         
                        ".Fmt(endBalanceTbl, ctx.Company, closeDate, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), opDes.Replace("'", "''"));
            dbSvcEX.Execute(ctx, sql);
        }


        /// <summary>
        /// 出入库成本计算：月末加权平均
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        /// <param name="option"></param>
        /// <returns></returns>
        public IOperationResult CostCalcByEnd(UserContext ctx, CostCalculatePara calPara, OperateOption option)
        {
            IOperationResult result = new OperationResult();
            if (calPara.EndDate > DateTime.Now)
            {
                throw new BusinessException("成本计算不能超过当前日期！");
            }
            result.ComplexMessage.SuccessMessages.Add("开始进行成本计算......");

            calPara.IniDate = GetStockIniDate(ctx);

            DateTime deleteDate = calPara.EndDate;
            var preDate = GetNearClosePeriod(ctx);
            if (preDate.HasValue && preDate.Value > deleteDate)
            {
                deleteDate = preDate.Value;
            }

            //如果计算的截止时间小于已结账日期，则计算到结账日期为止
            if (preDate.HasValue && preDate.Value > calPara.EndDate)
            {
                calPara.EndDate = preDate.Value;
            }

            DateTime startDate = GetCalBeginDate(ctx, result, calPara);

            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            var deleteSql = "delete from T_STK_INVENTORYBALANCE where fmainorgid='{0}' and fclosedate >='{1}' ".Fmt(ctx.Company, deleteDate);
            dbSvcEX.Execute(ctx, deleteSql);

            CostCalcAllPeriod(ctx, result, calPara, startDate, option);

            AddOrUpdateCostCalcPeriod(ctx, calPara.EndDate);

            result.ComplexMessage.SuccessMessages.Add("成本计算完成！");

            return result;
        }

        private void CostCalcAllPeriod(UserContext ctx, IOperationResult result, CostCalculatePara calPara, DateTime startDate, OperateOption option)
        {
            var diffMonth = ((calPara.EndDate.Year - startDate.Year) * 12) + calPara.EndDate.Month - startDate.Month;
            for (int i = 0; i <= diffMonth; i++)
            {
                var nextMonth = startDate.AddMonths(i);
                if (i == 0)
                {
                    calPara.PeriodBeginDate = startDate;
                    calPara.PeriodEndDate = new DateTime(nextMonth.Year, nextMonth.Month, 1).AddMonths(1);
                }
                else
                {
                    calPara.PeriodBeginDate = new DateTime(nextMonth.Year, nextMonth.Month, 1);
                    calPara.PeriodEndDate = new DateTime(nextMonth.AddMonths(1).Year, nextMonth.AddMonths(1).Month, 1);
                }

                //计算对应期间的库存成本
                result.ComplexMessage.SuccessMessages.Add("  期间出入库成本计算开始：{0} -- {1}".Fmt(calPara.PeriodBeginDate.ToDate(), calPara.PeriodEndDate.ToDate()));
                CostCalculate(ctx, result, calPara, option);
                result.ComplexMessage.SuccessMessages.Add("  期间出入库成本计算结束：{0} -- {1}".Fmt(calPara.PeriodBeginDate.ToDate(), calPara.PeriodEndDate.ToDate()));

                //如果计算到了当前期间，则更新即时库存表的成本信息
                if (calPara.PeriodBeginDate.Year == DateTime.Now.Year && calPara.PeriodBeginDate.Month == DateTime.Now.Month)
                {
                    result.ComplexMessage.SuccessMessages.Add("  开始更新即时库存成本信息");
                    UpdateStockInvbalance(ctx, result, calPara);
                    result.ComplexMessage.SuccessMessages.Add("  结束更新即时库存成本信息");
                }
                UpdateSalesOrderCostInfo(ctx, calPara.PeriodBeginDate, calPara.PeriodEndDate);
            }
        }


        private void CostCalculate(UserContext ctx, IOperationResult result, CostCalculatePara calPara, OperateOption option)
        {
            //期初结余数据
            var iniBalanceTbl = GetIniStockBalance(ctx, calPara);
            result.ComplexMessage.SuccessMessages.Add("    成功获取相关期初结余数据");

            //入库单据数据
            var stockInTbl = GetStockInDatas(ctx, calPara);
            result.ComplexMessage.SuccessMessages.Add("    成功获取相关入库单据数据");

            //更新入库单据的入库成本：部分入库单据，入库成本=期初结余成本
            UpdateStockInCost(ctx, calPara, iniBalanceTbl, stockInTbl);

            //反写源单入库成本 《销售退货单》《库存调拨单》上【单位成本】及【总成本】字段 
            WriteBackStockInBillCost(ctx, calPara, iniBalanceTbl, stockInTbl);
            result.ComplexMessage.SuccessMessages.Add("    成功更新源单入库成本信息（销售退货单、库存调拨单）");

            //入库成本计算
            var stockInBalanceTbl = CalcStockInCost(ctx, iniBalanceTbl, stockInTbl);
            result.ComplexMessage.SuccessMessages.Add("    完成入库成本计算");

            //获取出库数据
            var stockOutTbl = GetStockOutData(ctx, calPara);
            result.ComplexMessage.SuccessMessages.Add("    成功获取相关出库单据数据");

            //更新出库成本：部分出库单据的出库成本 = 入库成本计算后的结余成本
            CalculateStockOutCost(ctx, calPara, stockOutTbl, stockInBalanceTbl);

            //反写出库单据的成本信息
            WriteBackStockOutBillCost(ctx, calPara, stockOutTbl);
            result.ComplexMessage.SuccessMessages.Add("    成功更新出库单据的成本信息");

            //计算当前期间的成本结余 = 期初结余 + 当期入库 - 当期出库
            var endBalanceTbl = CalcCurrentPeriodStockCost(ctx, iniBalanceTbl, stockInBalanceTbl, stockOutTbl);
            result.ComplexMessage.SuccessMessages.Add("    完成出库成本计算");

            //更新当月的库存余额信息
            UpdateStockEndBalance(ctx, calPara, endBalanceTbl);
            result.ComplexMessage.SuccessMessages.Add("    成功更新当月的库存余额信息");

            //删除临时表（延迟删除，以便后台查临时表数据）
            var dbSvc = ctx.Container.GetService<IDBService>();
            dbSvc.DeleteTempTableByName(ctx, iniBalanceTbl, false);
            dbSvc.DeleteTempTableByName(ctx, stockInTbl, false);
            dbSvc.DeleteTempTableByName(ctx, stockOutTbl, false);
            dbSvc.DeleteTempTableByName(ctx, endBalanceTbl, false);
        }

        private void WriteBackStockInBillCost(UserContext ctx, CostCalculatePara calPara, string iniBalanceTbl, string stockInTbl)
        {
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            var sql = @"/*dialect*/
                    --调拨单
					update b set fcostprice=x.fcostprice,fcostamt=x.fcostamt
					from t_stk_invtransferentry b 
					inner join t_stk_invtransfer a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 	                     
					where x.FFormId='stk_inventorytransfer' 
					".Fmt(stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --销售退货单
					update b set fcostprice=x.fcostprice,fcostamt=x.fcostamt
					from t_stk_sostockreturnentry b 
					inner join t_stk_sostockreturn a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 	 
					where x.FFormId='stk_sostockreturn' 
					".Fmt(stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --采购入库单
					update b set fcostprice=x.fcostprice,fcostamt=x.fcostamt
					from t_stk_postockinentry b 
					inner join t_stk_postockin a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 	 
					where x.FFormId='stk_postockin' 
					".Fmt(stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --盘点单：总成本=单价成本*盘点数量
					update b set fcostprice=x.fcostprice,fcostamt=round(x.fcostprice * b.fpdqty,2)
					from t_stk_invverifyentry  b 
					inner join t_stk_invverify a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 	 
					where x.FFormId='stk_inventoryverify' 
					".Fmt(stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --其它入库单
					update b set fcostprice=x.fcostprice,fcostamt=x.fcostamt
					from t_stk_otherstockinentry b 
					inner join t_stk_otherstockin a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 	 
					where x.FFormId='stk_otherstockin' 
					".Fmt(stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --期初盘点单的成本
					update b set fcostprice=b.fpdprice,fcostamt=round(b.fpdprice * b.fpdqty,2)
					from t_stk_invverifyentry  b 
					inner join t_stk_invverify a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 	 
					where x.FFormId='stk_inventoryverify' and a.fbilltype='inventoryverify_billtype_02' 
					".Fmt(stockInTbl);
            dbSvcEX.Execute(ctx, sql);

        }




        /// <summary>
        /// 更新出库单的成本信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="stockOutTbl">出库单数据</param>
        /// <param name="stockInBalanceTbl">入库成本计算后的结余成本</param>
        private void WriteBackStockOutBillCost(UserContext ctx, CostCalculatePara calPara, string stockOutTbl)
        {
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            var sql = @"/*dialect*/
                    --调拨单
					update b set fcostprice=x.fcostprice,fcostamt=round(x.fqty * x.fcostprice,2)
					from t_stk_invtransferentry b 
					inner join t_stk_invtransfer a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 		  
					".Fmt(stockOutTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --盘点单：总成本=单价成本*盘点数量
					update b set fcostprice=x.fcostprice,fcostamt=round(b.fpdqty * x.fcostprice,2)
					from t_stk_invverifyentry b 
					inner join t_stk_invverify a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 		   
					".Fmt(stockOutTbl);
            dbSvcEX.Execute(ctx, sql);
            sql = @"/*dialect*/
                    --其它出库单 
					update b set fcostprice=x.fcostprice,fcostamt=round(x.fqty * x.fcostprice,2)
					from t_stk_otherstockoutentry b 
					inner join t_stk_otherstockout a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 		    
					".Fmt(stockOutTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/                   
					--采购退货单  
					update b set fcostprice=x.fcostprice,fcostamt=round(x.fqty * x.fcostprice,2)
					from t_stk_postockreturnentry b 
					inner join t_stk_postockreturn a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 		    
					".Fmt(stockOutTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --销售出库单
					update b set fcostprice=x.fcostprice,fcostamt=round(x.fqty * x.fcostprice,2)
					from t_stk_sostockoutentry b 
					inner join t_stk_sostockout a on a.fid=b.fid
					inner join {0} x on x.FFormId = a.FFormId and x.fid = a.fid and x.fentryid = b.fentryid 
					".Fmt(stockOutTbl);
            dbSvcEX.Execute(ctx, sql);
        }



        /// <summary>
        /// 更新当月的库存余额信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="stockOutTbl">出库单数据</param>
        /// <param name="stockInBalanceTbl">入库成本计算后的结余成本</param>
        private void UpdateStockEndBalance(UserContext ctx, CostCalculatePara calPara, string endBalanceTbl)
        {
            var nextMonth = calPara.PeriodEndDate;
            var closeDate = (new DateTime(nextMonth.Year, nextMonth.Month, 1)).AddDays(-1);//当月最后一天作为每月的关账日期 
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();

            var delFidTbl = CreateTempTableName(ctx, "tmpDelFid");
            var repeatSql = @"/*dialect*/
							--查询当期库存余额中要删除的库存维度重复的内码ID
							SELECT M.fid 
							INTO {3}
							FROM T_STK_INVENTORYBALANCE AS M WITH(NOLOCK) 
							INNER JOIN (
								SELECT MIN(fid) fid,b.fmainorgid,b.fmaterialid,b.fattrinfo_e,b.fcustomdesc,b.fstorehouseid,b.fstorelocationid,b.fstockstatus,b.flotno,b.fownertype,b.fownerid,b.funitid,b.fstockunitid,b.fmtono,b.fclosedate 
								FROM T_STK_INVENTORYBALANCE  b
								INNER JOIN {0} i on b.fmaterialid=i.fmaterialid and b.fattrinfo_e=i.fattrinfo_e 
															and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
															and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
															and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
															and b.fownertype=i.fownertype and b.fownerid=i.fownerid
															and b.funitid=i.funitid 
								WHERE b.fmainorgid='{1}' and b.fclosedate = '{2}'
								GROUP BY b.fmainorgid,b.fmaterialid,b.fattrinfo_e,b.fcustomdesc,b.fstorehouseid,b.fstorelocationid,b.fstockstatus,b.flotno,b.fownertype,b.fownerid,b.funitid,b.fstockunitid,b.fmtono,b.fclosedate
								HAVING COUNT(1)>1
							) AS T ON M.fmaterialid=T.fmaterialid and M.fattrinfo_e=T.fattrinfo_e 
									and M.fcustomdesc=T.fcustomdesc and M.fmtono=T.fmtono
									and M.fstorehouseid=T.fstorehouseid and M.fstorelocationid=T.fstorelocationid
									and M.fstockstatus=T.fstockstatus and M.flotno=T.flotno
									and M.fownertype=T.fownertype and M.fownerid=T.fownerid
									and M.funitid=T.funitid and M.fclosedate=T.fclosedate
									and M.fid!=T.fid
							".Fmt(endBalanceTbl, ctx.Company, closeDate, delFidTbl);
            dbSvcEX.Execute(ctx, repeatSql);

            var delSql = @"/*dialect*/
							--删除库存维度相同的重复数据
							DELETE FROM T_STK_INVENTORYBALANCE WHERE fid IN (SELECT fid FROM {0})
							".Fmt(delFidTbl);
            dbSvcEX.Execute(ctx, delSql);

            var opDes = $"{ctx.DisplayName ?? ctx.UserName ?? ctx.UserId}在{DateTime.Now}执行了更新当月的库存余额操作！";//操作描述
            var sql = @"/*dialect*/
                    --更新库存余额表
					update b set fqty = i.fqty , fstockqty = i.fstockqty, famount = i.famount, 
							 fcostprice = case when  i.fcostprice>0 then i.fcostprice else 0 end,
							 fcostamt = case when i.fcostamt>0 then i.fcostamt else 0 end,
							 fbeginqty =isnull(i.fbeginqty,0) ,fbeginstockqty=isnull(i.fbeginstockqty,0) ,
							 finicostprice=isnull(i.finicostprice,0),finicostamt=isnull(i.finicostamt,0),
							 finqty=isnull(i.finqty,0),finstockqty=isnull(i.finstockqty,0),
						     foutqty=isnull(i.foutqty,0),foutstockqty=isnull(i.foutstockqty,0),
							 fdescription='{3}'
					from T_STK_INVENTORYBALANCE  b
					inner join {0} i on b.fmaterialid=i.fmaterialid and b.fattrinfo_e=i.fattrinfo_e 
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid 
					where b.fmainorgid='{1}' and b.fclosedate = '{2}'
					".Fmt(endBalanceTbl, ctx.Company, closeDate, opDes.Replace("'", "''"));
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
					insert into T_STK_INVENTORYBALANCE(fid,FFormId,fmainorgid,fmaterialid  ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,fqty,fstockunitid,fstockqty , famount,fcostprice,fcostamt,fbalancetype ,fclosedate,
							fbeginqty,fbeginstockqty,finicostprice,finicostamt,finqty,finstockqty,foutqty,foutstockqty,fcreatedate,fdescription)
					select newid () as fid, FFormId , fmainorgid,fmaterialid  ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,
							fqty,fstockunitid,fstockqty ,famount,case when fcostprice>0 then fcostprice else 0 end as fcostprice,
							case when fcostamt>0 then fcostamt else 0 end as fcostamt,'inventorybalance_type_02' as fbalancetype,'{2}' as fclosedate,
							fbeginqty,fbeginstockqty,finicostprice,finicostamt,finqty,finstockqty,foutqty,foutstockqty,'{3}' as fcreatedate,'{4}' as fdescription
					from {0} b
					where not exists(select 1 from  T_STK_INVENTORYBALANCE i  with(nolock) 
										where b.fmaterialid=i.fmaterialid and b.fattrinfo_e=i.fattrinfo_e 
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid and i.fmainorgid='{1}' and i.fclosedate = '{2}' )
                         
                        ".Fmt(endBalanceTbl, ctx.Company, closeDate, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), opDes.Replace("'", "''"));
            dbSvcEX.Execute(ctx, sql);
        }




        /// <summary>
        /// 更新即时库存表的成本信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        private static void UpdateStockInvbalance(UserContext ctx, IOperationResult result, CostCalculatePara calPara)
        {
            var nextMonth = calPara.PeriodEndDate;
            var closeDate = (new DateTime(nextMonth.Year, nextMonth.Month, 1)).AddDays(-1);//当月最后一天作为每月的关账日期
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            var sql = @"/*dialect*/
                    --更新即时库存表
					update b set  fcostprice = i.fcostprice,fcostamt=i.fcostamt
					from T_STK_INVENTORYLIST  b
					inner join T_STK_INVENTORYBALANCE i on b.fmaterialid=i.fmaterialid 
                    and b.fattrinfo_e=i.fattrinfo_e
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid and i.fmainorgid=b.fmainorgid 
					where b.fmainorgid='{0}' and i.fclosedate = '{1}'
					".Fmt(ctx.Company, closeDate);
            dbSvcEX.Execute(ctx, sql);

            result.ComplexMessage.SuccessMessages.Add("成功更新即时库存表的成本信息");
        }


        /// <summary>
        /// 计算当前期间的成本结余
        /// </summary>
        /// <param name="ctx"></param>
        ///  <param name="iniBalanceTbl">期初成本</param>
        /// <param name="stockInBalanceTbl">入库成本</param>
        /// <param name="stockOutTbl">出库数据</param>
        /// <returns></returns>
        private string CalcCurrentPeriodStockCost(UserContext ctx, string iniBalanceTbl, string stockInBalanceTbl, string stockOutTbl)
        {
            //计算当期库存余额 = 期初结余 + 当期入库 - 当期出库；其中 stockInBalanceTbl 包含了 【期初结余 + 当期入库】
            var dbSvc = ctx.Container.GetService<IDBService>();
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();

            var endBalanceTbl = CreateTempTableName(ctx, "tmpEndBal");
            var sql = @"/*dialect*/
                    select  'stk_inventorybalance' as FFormId,fmainorgid,fmaterialid ,fattrinfo_e  ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid,
							sum(fqty) as fqty,sum(fstockqty) as fstockqty ,
							sum(famount) as famount,
							fcostamt =case when sum(fqty)>0 then round( sum(fcostamt) / sum(fqty) ,2) else 0 end * sum(fqty)  ,
							fcostprice = case when sum(fqty)>0 then round( sum(fcostamt) / sum(fqty) ,2) else 0 end,
							sum(fqty) * 0 as fbeginqty,sum(fqty) * 0 as fbeginstockqty,sum(fqty) * 0 as finicostprice,sum(fqty) * 0 as finicostamt,
							sum(finqty) as finqty,sum(finstockqty) as finstockqty,sum(foutqty) as foutqty,sum(foutstockqty) as foutstockqty
					into {0}
					from ( 
							select FFormId , fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
									flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid,
									fstockqty ,
									fqty,
									famount,
									fcostprice,
									fcostamt,
									finstockqty as finstockqty,
									finqty as finqty,
									fqty * 0 as foutqty,fqty * 0 as foutstockqty
							from {1}
							union all 
							select FFormId , fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
									flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid,
									fstockqty * -1 as fstockqty,
									fqty * -1 as fqty,
									famount * -1 as famount,
									fcostprice,
									fcostamt * -1 as fcostamt 		,
									fqty * 0 as finstockqty,
									fqty * 0 as finqty,
									fqty as foutqty,
									fstockqty as foutstockqty
							from {2}
						) b
					group by fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
								flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid
			 

					".Fmt(endBalanceTbl, stockInBalanceTbl, stockOutTbl);
            dbSvcEX.Execute(ctx, sql);


            sql = @"/*dialect*/
					--结余数量为零的，成本为零
					update {0} set fcostamt =0 where fqty <= 0 ".Fmt(endBalanceTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --单位结余成本为零的，取期初的结余单位成本
					update b set fcostprice = isnull(i.fcostprice,0)
					from {0} as b
					left join {1} i on b.fmaterialid=i.fmaterialid
                    and b.fattrinfo_e=i.fattrinfo_e
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid 
					where b.fcostprice=0 and i.fcostprice>0 ;                         
                        ".Fmt(endBalanceTbl, iniBalanceTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --单位结余成本为零的，取期初的结余单位成本（不管仓库仓位）
					update b set fcostprice = isnull(i.fcostprice,0)
					from {0} as b
					left join {1} i on b.fmaterialid=i.fmaterialid
                    and b.fattrinfo_e=i.fattrinfo_e
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid  
					where b.fcostprice=0 and i.fcostprice>0  ;                         
                        ".Fmt(endBalanceTbl, iniBalanceTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --期初数据 
					update b set fbeginqty = isnull(i.fendqty,0),fbeginstockqty = isnull(i.fendstockqty,0),finicostprice = isnull(i.fendcostprice,0),finicostamt = isnull(i.fendcostamt,0) 
					from {0} as b
					left join {1} i on b.fmaterialid=i.fmaterialid 
                    and b.fattrinfo_e=i.fattrinfo_e 
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid  
                        ".Fmt(endBalanceTbl, iniBalanceTbl);
            dbSvcEX.Execute(ctx, sql);

            try
            {
                var idxName = "idx_" + dbSvc.CreateTemporaryTableName(ctx);
                sql = @" create index {0} on {1}(fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
											flotno, fmtono, fownertype, fownerid, funitid) ;
					".Fmt(idxName, endBalanceTbl);
                dbSvcEX.Execute(ctx, sql);
            }
            catch (Exception)
            {
                return endBalanceTbl;
            }
            return endBalanceTbl;
        }





        /// <summary>
        /// 计算出库成本：部分出库单据，出库成本=入库成本计算后的结余成本
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="stockOutTbl">出库单数据</param>
        /// <param name="stockInBalanceTbl">入库成本计算后的结余成本</param>
        private void CalculateStockOutCost(UserContext ctx, CostCalculatePara calPara, string stockOutTbl, string stockInBalanceTbl)
        {
            var sql = @"/*dialect*/
                    --更新出库单据的出库成本
                    update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							fcostprice = isnull(i.fcostprice,0) 
		            from {0} b
		            inner join {1} i on b.fmaterialid=i.fmaterialid 
                    and b.fattrinfo_e=i.fattrinfo_e 
									            and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									            and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									            and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
									            and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									            and b.funitid=i.funitid  
		            where /* 销售出库单、其它出库单、调拨单（调出）按【结余单位成本】算 */
				            (b.FFormId='stk_otherstockout' or b.FFormId='stk_sostockout' or b.FFormId='stk_inventorytransfer'
			              /* 出库成本没有获取到的，是否取结余单位成本？如果不取，把条件 or b.fcostamt=0 去掉 */
			              or b.fcostamt=0) 
                         
                        ".Fmt(stockOutTbl, stockInBalanceTbl);

            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            dbSvcEX.Execute(ctx, sql);

            ///如果是初始化后的第一个期间，可能相关仓库仓位的期初成本为零，则取该商品的其他有成本的仓库的成本
            //if(calPara.IniDate.HasValue &&  (calPara.IniDate.Value.AddDays (1)==calPara.PeriodBeginDate 
            //	|| ( calPara.PeriodBeginDate.Year ==calPara.IniDate.Value.Year && calPara.PeriodBeginDate.Month == calPara.IniDate.Value.Month)))
            //         {
            sql = @"/*dialect*/
                    --更新出库单据的出库成本
                    update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							fcostprice = isnull(i.fcostprice,0) 
		            from {0} b
		            inner join {1} i on b.fmaterialid=i.fmaterialid
                    and b.fattrinfo_e=i.fattrinfo_e 
									            and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									            /* 不关联仓库仓位，以便取有成本的仓库成本 and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									            and b.fstockstatus=i.fstockstatus */ and b.flotno=i.flotno
									            and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									            and b.funitid=i.funitid  
		            where /* 销售出库单、其它出库单、调拨单（调出）按【结余单位成本】算 */
				            (b.FFormId='stk_otherstockout' or b.FFormId='stk_sostockout' or b.FFormId='stk_inventorytransfer' )
			              and b.fcostamt=0 and i.fcostprice>0  
                         
                        ".Fmt(stockOutTbl, stockInBalanceTbl);

            dbSvcEX.Execute(ctx, sql);
            //}

            sql = @"/*dialect*/
                    --更新出库单据的出库成本
                    update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							fcostprice = isnull(i.fcostprice,0) 
		            from {0} b
		            inner join {1} i on b.fmaterialid=i.fmaterialid
                    and b.fattrinfo_e=i.fattrinfo_e 
									            and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									            /* 不关联仓库仓位，以便取有成本的仓库成本 and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									            and b.fstockstatus=i.fstockstatus */ and b.flotno=i.flotno
									            and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									            and b.funitid=i.funitid  
		            where  b.fcostprice=0 and i.fcostprice > 0 
                         
                        ".Fmt(stockOutTbl, stockInBalanceTbl);

            dbSvcEX.Execute(ctx, sql);
        }



        /// <summary>
        /// 获取出库数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        /// <returns></returns>
        private string GetStockOutData(UserContext ctx, CostCalculatePara calPara)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();

            var tempTableName = CreateTempTableName(ctx, "tmpOut"); ;
            var sql = @"/*dialect*/
                    select FFormId ,fid,fentryid,fseq, fmainorgid,fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
				            flotno ,fmtono ,fownertype ,fownerid ,funitid,
				            fqty,fstockunitid,fstockqty ,famount,fcostprice,fcostamt   
		            into {0}
		            from (
				            --调拨单--调出--出库  【出库成本】=【基本单位调出数量】*【结余单位成本】
				            select a.FFormId ,a.fid,b.fentryid,b.fseq, a.fmainorgid,b.fmaterialid ,b.fattrinfo,b.fattrinfo_e ,b.fcustomdesc,b.fstorehouseid ,b.fstorelocationid ,b.fstockstatus ,
						            b.flotno ,b.fmtono ,b.fownertype ,b.fownerid ,b.funitid,
						            b.fstockoutbizqty as fqty,b.fstockunitid,b.fstockoutqty as fstockqty ,
						            b.famount as famount ,
						            0 fcostprice,
						            0 fcostamt   
				            from t_stk_invtransfer a with(nolock)
				            inner join t_stk_invtransferentry b with(nolock) on a.fid=b.fid
				            where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'

				            union all 

				            --盘点单--盘亏--出库  【出库成本】=【盘点金额】
				            select FFormId ,a.fid,fentryid,fseq,fmainorgid,fmaterialid ,fattrinfo ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						            flotno ,fmtono ,fownertype ,fownerid ,funitid,
						            fpkqty as fqty,fstockunitid,fbizpkqty fstockqty ,
						            famount as famount,
						            fpdprice as fcostprice,
						            famount as fcostamt   
				            from t_stk_invverify a with(nolock)
				            inner join t_stk_invverifyentry b with(nolock) on a.fid=b.fid
				            where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'

				            union all 

				            --其它出库单--出库  【出库成本】=【基本单位实发数量】*【结余单位成本】
				            select FFormId ,a.fid,fentryid,fseq,fmainorgid,fmaterialid ,fattrinfo ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						            flotno ,fmtono ,fownertype ,fownerid ,funitid,
						            fqty as fqty,fstockunitid,fstockqty ,
						            famount as famount ,
						            0 fcostprice,
						            0 fcostamt   
				            from t_stk_otherstockout a with(nolock)
				            inner join t_stk_otherstockoutentry b with(nolock) on a.fid=b.fid
				            where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'
 
				            union all 

				            --采购退货单--出库  【出库成本】=【成交金额】（折后价）
				            select FFormId ,a.fid,fentryid,fseq,fmainorgid,fmaterialid ,fattrinfo ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						            flotno ,fmtono ,fownertype ,fownerid ,funitid,
						            fqty as fqty,fstockunitid,fstockqty ,
						            famount,
						            fprice as fcostprice,
						            famount as fcostamt   
				            from t_stk_postockreturn a with(nolock)
				            inner join t_stk_postockreturnentry b with(nolock) on a.fid=b.fid
				            where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'
 
				            union all 

				            --销售出库单--出库   【出库成本】=【基本单位实发数量】*【结余单位成本】
				            select FFormId ,a.fid,fentryid,fseq,fmainorgid,fmaterialid ,fattrinfo ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						            flotno ,fmtono ,fownertype ,fownerid ,funitid,
						            fqty as fqty,fstockunitid,fstockqty ,
						            famount as famount ,
						            0 fcostprice,
						            0 fcostamt   
				            from t_stk_sostockout a with(nolock)
				            inner join t_stk_sostockoutentry b with(nolock) on a.fid=b.fid
				            where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'
			            ) x
                         
                        ".Fmt(tempTableName, ctx.Company, calPara.PeriodBeginDate, calPara.PeriodEndDate);

            dbSvcEX.Execute(ctx, sql);
            try
            {
                var idxName = "idx_" + dbSvc.CreateTemporaryTableName(ctx);
                dbSvcEX.Execute(ctx, " create index {0} on {1}(fid,fentryid) ;".Fmt(idxName, tempTableName));

                idxName = "idx_" + dbSvc.CreateTemporaryTableName(ctx);
                sql = @" create index {0} on {1}(fmaterialid ,fattrinfo ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
											flotno, fmtono, fownertype, fownerid, funitid) ;
					".Fmt(idxName, tempTableName);
                dbSvcEX.Execute(ctx, sql);
            }
            catch (Exception)
            {
                return tempTableName;
            }
            return tempTableName;
        }





        /// <summary>
        /// 入库成本计算
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        /// <returns></returns>
        private string CalcStockInCost(UserContext ctx, string iniBalanceTbl, string stockInTbl)
        {
            /*计算入库成本：
	            【结余成本】 = 【入库前结余成本】 + 【入库成本】
	            【结余数量】 = 【入库前结余数量】 + 【入库数量】
	            【结余单位成本】 = 【结余成本 】/ 【结余数量】							  
            */
            var dbSvc = ctx.Container.GetService<IDBService>();
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();

            var stockInBalanceTbl = CreateTempTableName(ctx, "tmpInBal");
            var sql = @"/*dialect*/
                    select  'stk_inventorybalance' as FFormId,fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
				            flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid,
				            sum(fqty) as fqty,sum(fstockqty) as fstockqty ,
				            sum(famount) as famount,
				            fcostamt = case when sum(fqty)>0 then sum(fcostamt) / sum(fqty) else 0 end  * sum(fqty)  ,
				            fcostprice = case when sum(fqty)>0 then sum(fcostamt) / sum(fqty) else 0 end,
							sum(finqty) as finqty,
							sum(finstockqty) as finstockqty,
							sum(fqty) * 0 as foutqty,
							sum(fqty) * 0 as foutstockqty
		            into {0}
		            from ( 
				            select FFormId , fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						            flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid,fstockqty ,
						            fqty,famount,fcostprice,fcostamt,fqty as finqty,fstockqty as finstockqty      
				            from {2}
				            union all 
				            select FFormId , fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						            flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid,fstockqty ,
						            fqty,famount,fcostprice,case when fbalancetype='inventorybalance_type_01' then fcostamt else fcostprice * fqty end as fcostamt,fqty * 0 as finqty,fstockqty * 0 as finstockqty     
				            from {1}
			            ) b
		            group by fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
					            flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid ;".Fmt(stockInBalanceTbl, iniBalanceTbl, stockInTbl);
            dbSvcEX.Execute(ctx, sql);


            sql = @"/*dialect*/--结余数量为零的，成本为零
		            update {0} set fcostamt =0 where fqty <= 0  ;".Fmt(stockInBalanceTbl);
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
                    --单位成本为零的，取结余单位成本
		            update b set fcostprice = isnull(i.fcostprice,0) 
		            from {0} as b
		            left join {1} i on b.fmaterialid=i.fmaterialid 
                    and b.fattrinfo_e=i.fattrinfo_e 
									            and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									            and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									            and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
									            and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									            and b.funitid=i.funitid  
		            where b.fcostprice=0 ;
                         
                        ".Fmt(stockInBalanceTbl, iniBalanceTbl);
            dbSvcEX.Execute(ctx, sql);

            try
            {
                var idxName = "idx_" + dbSvc.CreateTemporaryTableName(ctx);
                sql = @" create index {0} on {1}(fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
											flotno, fmtono, fownertype, fownerid, funitid) ;
					".Fmt(idxName, stockInBalanceTbl);
                dbSvcEX.Execute(ctx, sql);
            }
            catch (Exception)
            {
                return stockInBalanceTbl;
            }
            return stockInBalanceTbl;
        }



        /// <summary>
        /// 更新入库成本：调拨单，入库成本=期初结余成本
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        /// <returns></returns>
        private void UpdateStockInCost(UserContext ctx, CostCalculatePara calPara, string iniBalanceTbl, string stockInTbl)
        {
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            //库存调拨单需要用调出字段进行关联
            var sql = @"/*dialect*/
                    --更新入库金额：调拨单的入库成本 = 入库数量 * 【结余单位成本】
		            update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							            fcostprice = isnull(i.fcostprice,0) 
		            from {1} as b 
                    inner join t_stk_invtransferentry e with(nolock) on b.fid=e.fid and b.fentryid=e.fentryid 
		            left join {0} i on b.fmaterialid=i.fmaterialid 
                    and e.fattrinfo_e=i.fattrinfo_e 
									    and e.fcustomdesc=i.fcustomdesc and e.fmtono=i.fmtono
									    and e.fstorehouseid=i.fstorehouseid and e.fstorelocationid=i.fstorelocationid
									    and e.fstockstatus=i.fstockstatus and e.flotno=i.flotno
									    and e.fownertype=i.fownertype and e.fownerid=i.fownerid
									    and e.funitid=i.funitid  
		            where b.FFormId='stk_inventorytransfer' 
                         
                        ".Fmt(iniBalanceTbl, stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            //关联仓库仓位：没有成本信息的，从期初结余数据中取
            sql = @"/*dialect*/
                    --更新入库金额：调拨单的入库成本 = 入库数量 * 【结余单位成本】
		            update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							            fcostprice = isnull(i.fcostprice,0) 
		            from {1} as b
		            left join {0} i on b.fmaterialid=i.fmaterialid 
                    and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									    and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									    and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
									    and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									    and b.funitid=i.funitid  
		            where /* 单位入库成本没有获取到的，是否取结余单位成本？如果不取，把条件 or b.fcostamt=0 去掉 */
			                (b.fcostamt=0 or b.fcostprice=0) and i.fcostprice>0 and b.fupdate =1  
                         
                        ".Fmt(iniBalanceTbl, stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            //不关联仓库仓位：没有成本信息的，从其他仓库仓位取
            sql = @"/*dialect*/
                    --更新入库金额：入库成本 = 入库数量 * 【结余单位成本】
		            update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							            fcostprice = isnull(i.fcostprice,0) 
		            from {1} as b
		            inner join {0} i on b.fmaterialid=i.fmaterialid 
                    and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									    and b.flotno=i.flotno
									    and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									    and b.funitid=i.funitid  
		            where /* 单位入库成本没有获取到的，是否取结余单位成本？*/
			                b.fcostprice=0 and i.fcostprice>0 and b.fupdate =1  
                         
                        ".Fmt(iniBalanceTbl, stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            //没有结余单位成本的情况，从本期取（比如之前一直未有库存，本期开始有采购入库，然后做调拨，则这些调拨入库的期初结余成本都是空的，需要从本期取）
            sql = @"/*dialect*/
                    --更新入库金额：入库成本 = 入库数量 * 【结余单位成本】
		            update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							            fcostprice = isnull(i.fcostprice,0) 
		            from {0} as b
		            inner join ( select * from {0} with(nolock)
								 where fcostprice >0 
								) i on b.fmaterialid=i.fmaterialid 
                                        and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									    and b.flotno=i.flotno
									    and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									    and b.funitid=i.funitid  
		            where /* 单位入库成本没有获取到的，是否取结余单位成本？*/
			                b.fcostprice=0 /* and b.fupdate =1 */ 
                        ".Fmt(stockInTbl);
            dbSvcEX.Execute(ctx, sql);

            //还取不到入库成本的，从余额表取有单位成本的（不考虑仓库仓位）
            //禅道BUG#40692：增加仓库仓位考虑，不考虑会出问题
            sql = @"/*dialect*/
                    --更新入库金额：入库成本 = 入库数量 * 【结余单位成本】
		            update b set fcostamt =round( b.fqty * isnull(i.fcostprice,0) ,2),
							            fcostprice = isnull(i.fcostprice,0) 
		            from {1} as b
		            inner join {0} i  with(nolock) on b.fmaterialid=i.fmaterialid 
                                        and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									    and b.flotno=i.flotno
									    and b.fownertype=i.fownertype and b.fownerid=i.fownerid 
                                        and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid 
									    and b.funitid=i.funitid and i.fmainorgid='{2}'  
		            where /* 单位入库成本没有获取到的，是否取结余单位成本？*/
			                b.fcostprice=0 and b.fupdate =1 and i.fcostprice > 0  and i.fmainorgid='{2}' 
                         
                        ".Fmt("T_STK_INVENTORYBALANCE", stockInTbl, ctx.Company);
            dbSvcEX.Execute(ctx, sql);

        }

        /// <summary>
        /// 获取入库单据数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        /// <returns></returns>
        private string GetStockInDatas(UserContext ctx, CostCalculatePara calPara)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var tempTableName = CreateTempTableName(ctx, "tmpIn");

            var sql = @"/*dialect*/
                        select FFormId ,fid,fentryid,fseq, fmainorgid,fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
				                flotno ,fmtono ,fownertype ,fownerid ,funitid,
				                fqty,fstockunitid,fstockqty ,famount,fcostprice,fcostamt   ,fupdate
		                into {0}
		                from (
				                --采购入库单--入库    【入库成本】=【成交金额】（折后价）
				                select FFormId ,a.fid,fentryid,fseq, fmainorgid,fmaterialid ,fattrinfo, fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						                flotno ,fmtono ,fownertype ,fownerid ,funitid,
						                fqty as fqty,fstockunitid,fstockqty ,
						                famount,
						                case when fqty=0 then 0 else famount/fqty end as fcostprice,famount as fcostamt   ,1 as fupdate
				                from t_stk_postockin a with(nolock)
				                inner join t_stk_postockinentry b with(nolock) on a.fid=b.fid
				                where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'

				                union all 

				                --调拨单--调入   【入库成本】=【基本单位调入数量】*【结余单位成本】。
				                select a.FFormId ,a.fid,b.fentryid,b.fseq,a.fmainorgid,b.fmaterialid ,b.fattrinfoto as fattrinfo,b.fattrinfoto_e as fattrinfo_e  ,b.fcallupcustomdescto as fcustomdesc,b.fstorehouseidto as fstorehouseid ,
						                b.fstorelocationidto as fstorelocationid,b.fstockstatusto as fstockstatus ,
						                b.flotno as flotno ,b.fmtonoto as fmtono ,b.fownertypeto as fownertype ,b.fowneridto as fownerid ,b.funitid,
						                b.fstockinbizqty as fqty,b.fstockunitid,b.fstockinqty as fstockqty ,
						                b.famount,
						                b.fqty * 0 as fcostprice,b.fqty * 0 as  fcostamt   ,1 as fupdate
				                from t_stk_invtransfer a with(nolock)
				                inner join t_stk_invtransferentry b with(nolock) on a.fid=b.fid
				                where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'

				                union all 

				                --盘点单--盘盈   【入库成本】=【盘赢金额】
				                select a.FFormId ,a.fid,fentryid,fseq,a.fmainorgid,fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						                flotno ,fmtono ,fownertype ,fownerid ,funitid,
						                fpyqty fqty,fstockunitid,
										fbizpyqty as fstockqty,
						                case when (fbilltype='inventoryverify_billtype_02' or x.fprimitiveid='inventoryverify_billtype_02')  and fdate< '{4}' then 0 when fpyamount>0 then fpyamount else fpdprice * fpyqty end as famount,
						                case when fbilltype='inventoryverify_billtype_02' or x.fprimitiveid='inventoryverify_billtype_02'  then 0 else fpdprice end as fcostprice,
										case when (fbilltype='inventoryverify_billtype_02' or x.fprimitiveid='inventoryverify_billtype_02' ) and fdate< '{4}'  then 0 when fpyamount>0 then fpyamount else fpdprice * fpyqty end as fcostamt   ,
										case when (fbilltype='inventoryverify_billtype_02' or x.fprimitiveid='inventoryverify_billtype_02') and fdate< '{4}'  then 0 else 1 end as fupdate
				                from t_stk_invverify a with(nolock)
				                inner join t_stk_invverifyentry b with(nolock) on a.fid=b.fid
                                inner join T_BD_BILLTYPE x on a.fbilltype=x.fid
				                where a.fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'

				                union all 

				                --其它入库单--入库  【入库成本】=【金额】
				                select FFormId ,a.fid,fentryid,fseq,fmainorgid,fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
						                flotno ,fmtono ,fownertype ,fownerid ,funitid,
						                fqty as fqty,fstockunitid,fstockqty ,
						                famount,
						                case when fqty=0 then 0 else famount/fqty end as fcostprice,famount as fcostamt    ,1 as fupdate
				                from t_stk_otherstockin a with(nolock)
				                inner join t_stk_otherstockinentry b with(nolock) on a.fid=b.fid
				                where fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>= '{2}'  and fdate<  '{3}'
 
				                union all 

				                --销售退货单--入库  【入库成本】=【基本单位实退数量】*【对应出库单的成本价】
				                select FFormId ,a.fid,b.fentryid,b.fseq,a.fmainorgid,b.fmaterialid ,b.fattrinfo,b.fattrinfo_e ,b.fcustomdesc,b.fstorehouseid ,b.fstorelocationid ,b.fstockstatus ,
						                b.flotno ,b.fmtono ,b.fownertype ,b.fownerid ,b.funitid,
						                b.fqty as fqty,b.fstockunitid,b.fstockqty ,
						                b.famount,
						                isnull(x.fcostprice,0) as fcostprice,isnull(x.fcostprice,0) * b.fqty as fcostamt    ,1 as fupdate
				                from t_stk_sostockreturn a with(nolock)
				                inner join t_stk_sostockreturnentry b with(nolock) on a.fid=b.fid
								left join T_STK_SOSTOCKOUTENTRY x on b.fsooutstockentryid = x.fentryid and b.fsooutstockinterid =x.fid and b.fsourceformid ='stk_sostockout'
				                where a.fmainorgid ='{1}' and a.fstatus ='E' and a.fcancelstatus ='0' and a.fdate>= '{2}'  and a.fdate<  '{3}'
			                ) x  ;

                        ".Fmt(tempTableName, ctx.Company, calPara.PeriodBeginDate, calPara.PeriodEndDate, calPara.IniDate);

            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            dbSvcEX.Execute(ctx, sql);

            try
            {
                var idxName = "idx_" + dbSvc.CreateTemporaryTableName(ctx);
                dbSvcEX.Execute(ctx, " create index {0} on {1}(fid,fentryid) ;".Fmt(idxName, tempTableName));

                idxName = "idx_" + dbSvc.CreateTemporaryTableName(ctx);
                sql = @" create index {0} on {1}(fmaterialid ,fattrinfo ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
											flotno, fmtono, fownertype, fownerid, funitid) ;
					".Fmt(idxName, tempTableName);
                dbSvcEX.Execute(ctx, sql);
            }
            catch (Exception)
            {
                return tempTableName;
            }

            return tempTableName;
        }


        /// <summary>
        /// 期初金额
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="calPara"></param>
        /// <returns></returns>
        private string GetIniStockBalance(UserContext ctx, CostCalculatePara calPara)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var iniBalanceTbl = CreateTempTableName(ctx, "tmpIniBal");

            //获取离开始日期最近的关账日期
            var dateSql = @"/*dialect*/
							select max(fclosedate) as nearDate from T_STK_INVENTORYBALANCE with(nolock) where fmainorgid='{0}' and fclosedate<= '{1}'
							".Fmt(ctx.Company, calPara.PeriodBeginDate);
            var nearDate = "";
            using (var dr = dbSvc.ExecuteReader(ctx, dateSql))
            {
                if (dr.Read())
                {
                    nearDate = Convert.ToString(dr["nearDate"]);
                }
            }

            //初始化期间的，
            if (calPara.IniDate.Value.Year * 100 + calPara.IniDate.Value.Month + 10 == calPara.PeriodBeginDate.Year * 100 + calPara.PeriodBeginDate.Month + 10)
            {
                nearDate = calPara.IniDate.Value.ToString("yyy-MM-dd");
            }
            var sql = @"/*dialect*/
                        --期初金额 
                        select fbalancetype, FFormId , fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
		                        flotno ,fmtono ,fownertype ,fownerid ,funitid,
		                        fqty,fstockunitid,fstockqty ,max(famount) famount,max(fcostprice) fcostprice,max(fcostamt) fcostamt  ,
								fqty as fendqty,fstockqty as fendstockqty,max(fcostprice) as fendcostprice,max(fcostamt) as fendcostamt
                        into {0}
                        from T_STK_INVENTORYBALANCE with(nolock) 
						where fmainorgid='{1}' 
							and fclosedate = '{2}'
                        group by fbalancetype, FFormId , fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
		                        flotno ,fmtono ,fownertype ,fownerid ,funitid,
		                        fqty,fstockunitid,fstockqty 
                         
                        ".Fmt(iniBalanceTbl, ctx.Company, nearDate);

            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            dbSvcEX.Execute(ctx, sql);

            try
            {
                var idxName = "idx_" + dbSvc.CreateTemporaryTableName(ctx);
                sql = @" create index {0} on {1}(fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
											flotno, fmtono, fownertype, fownerid, funitid) ;
					".Fmt(idxName, iniBalanceTbl);
                dbSvcEX.Execute(ctx, sql);
            }
            catch (Exception)
            {
                return iniBalanceTbl;
            }
            return iniBalanceTbl;
        }


        private DateTime GetCalBeginDate(UserContext ctx, IOperationResult result, CostCalculatePara calPara)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();

            //检查是否已经结束初始化，未结束初始化不允许计算
            var stkSvc = ctx.Container.GetService<IStockBaseService>();
            if (!stkSvc.IsInitedInventory(ctx))
            {
                result.ComplexMessage.ErrorMessages.Add("库存还未结束初始化，不允许执行成本计算操作！");

                throw new BusinessException("库存还未结束初始化，不允许执行成本计算操作！");
            }

            //最近关账日期
            var closePeriod = GetNearClosePeriod(ctx);
            if (closePeriod.HasValue == false)
            {
                //没有过关账的，从初始化期间开始算
                return calPara.IniDate.Value;
            }

            if (calPara.EndDate.Year * 100 + calPara.EndDate.Month < closePeriod.Value.Year * 100 + closePeriod.Value.Month)
            {
                result.ComplexMessage.ErrorMessages.Add("成本计算月份不能小于已关账月份！");

                throw new BusinessException("成本计算月份不能小于已关账月份！");
            }

            //有过关账且未指定开始重算期间的，从关账期间开始算
            if (!calPara.BeginDate.HasValue)
            {
                return new DateTime(closePeriod.Value.Year, closePeriod.Value.Month, 1);
            }

            //最近成本计算日期
            var calPeriod = GetNearCostCalcPeriod(ctx);
            if (calPeriod.IsNullOrEmptyOrWhiteSpace())
            {
                //从未计算过的，从初始化期间开始算
                var systemProfileService = ctx.Container.BeginLifetimeScope(Guid.NewGuid().ToString()).GetService<ISystemProfile>();
                var iniDate = systemProfileService.GetSystemParameter<DateTime>(ctx, "stk_invcompleteinit", "finitdate");
                return Convert.ToDateTime(iniDate).AddDays(1);
            }

            DateTime beginDate = Convert.ToDateTime(calPeriod + "-01");

            //如果最近成本计算期间大于关账期间，则从关账期间开始重算
            if (beginDate > closePeriod.Value)
            {
                beginDate = closePeriod.Value;
            }

            //按用户指定的开始重算期间开始重算
            if (calPara.BeginDate.HasValue && calPara.BeginDate.Value < beginDate)
            {
                beginDate = calPara.BeginDate.Value;
            }

            if (calPara.EndDate < beginDate)
            {
                beginDate = calPara.EndDate;
            }

            return beginDate;
        }


        private DateTime? GetStockIniDate(UserContext ctx)
        {
            DateTime? miniDate = null;
            var systemProfileService = ctx.Container.GetService<ISystemProfile>();
            var dynamicObj = systemProfileService.GetSystemParameter(ctx, "stk_invcompleteinit");
            if (dynamicObj != null && !dynamicObj["finitdate"].IsNullOrEmpty())
            {
                miniDate = Convert.ToDateTime(dynamicObj["finitdate"]);
            }
            else
            {
                var dtMinimumDate = ctx.Container.GetService<IStockBaseService>()?.GetMinimumStockBillDate(ctx, true);
                if (dtMinimumDate.HasValue && dtMinimumDate.Value != DateTime.MinValue)
                {
                    miniDate = dtMinimumDate.Value.AddDays(-1);
                }
            }

            return miniDate;
        }


        /// <summary>
        /// 以期初盘点单的账存金额，更新期初库存余额
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="billIds">对应的期初盘点单数据</param>
        /// <param name="option"></param>
        /// <returns></returns>
        public void UpdateIniStockBalanceByVerify(UserContext ctx, List<string> billIds, OperateOption option)
        {
            DateTime? miniDate = GetStockIniDate(ctx);

            var closeDate = GetNearClosePeriod(ctx);
            if (closeDate.HasValue && miniDate.HasValue && closeDate.Value > miniDate.Value)
            {
                //不需要控制：期初盘点单保存时已经控制了不允许录入盘盈盘亏数量
                //throw new Exception("库存已经关账到{0}，不允许更新期初库存余额！请先反关账到初始化月份并进行反结束初始化后再用期初盘点单更新库存初始余额。");
            }

            var opDes = $"{ctx.DisplayName ?? ctx.UserName ?? ctx.UserId}在{DateTime.Now}执行了更新期初库存余额操作！";//操作描述
            var dbSvcEX = ctx.Container.GetService<IDBServiceEx>();
            var sql = @"/*dialect*/   
                    --更新库存余额表 以盘盈数量作为期初数量 x.fdate<= '{1}' 表示初始化盘点单
					update b set fqty = 0 , fstockqty = 0, famount = i.famount, fcostprice = i.fpdprice,fcostamt=case when i.fpyamount >0 and x.fdate< '{1}' then i.fpyamount when x.fdate< '{1}' then i.famount else 0 end
								 ,fdescription='{2}'
					from T_STK_INVENTORYBALANCE  b
					inner join t_stk_invverifyentry i on b.fmaterialid=i.fmaterialid 
                                                and b.fattrinfo_e=i.fattrinfo_e 
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid 
                    inner join t_stk_invverify x on i.fid=x.fid and b.fmainorgid=x.fmainorgid
					where i.fid in ({0}) and b.fclosedate = '{1}'  
					".Fmt(billIds.JoinEx(",", true), miniDate, opDes.Replace("'", "''"));
            dbSvcEX.Execute(ctx, sql);

            sql = @"/*dialect*/
					insert into T_STK_INVENTORYBALANCE(fid,FFormId,fmainorgid,fmaterialid  ,fattrinfo_e,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,fqty,fstockunitid,fstockqty , famount,fcostprice,fcostamt,fbalancetype ,fclosedate,fcreatedate,fdescription)
					select newid () as fid, 'stk_inventorybalance' as FFormId ,'{1}' as fmainorgid,fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,
							0 as fqty,fstockunitid,0 as fstockqty ,max(famount) famount,max(fpdprice) fpdprice,case when max(b.fpyamount) >0 and min(x.fdate)< '{2}' then max(b.fpyamount) when min(x.fdate)< '{2}' then max(b.famount) else 0 end as fpdamount,
							'inventorybalance_type_01' as fbalancetype,'{2}' as fclosedate,'{3}' as fcreatedate,'{4}' as fdescription
					from t_stk_invverifyentry b with(nolock) 
                    inner join t_stk_invverify x on b.fid=x.fid 
					where b.fid in ({0}) and  not exists(select 1 from  T_STK_INVENTORYBALANCE i  with(nolock)  
										where b.fmaterialid=i.fmaterialid 
                                                and b.fattrinfo_e=i.fattrinfo_e  
												and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
												and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
												and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
												and b.fownertype=i.fownertype and b.fownerid=i.fownerid
												and b.funitid=i.funitid and i.fmainorgid='{1}' and i.fclosedate = '{2}' )
                    group by fmaterialid ,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,
							flotno ,fmtono ,fownertype ,fownerid ,funitid,fstockunitid
                         
                        ".Fmt(billIds.JoinEx(",", true), ctx.Company, miniDate, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), opDes.Replace("'", "''"));
            dbSvcEX.Execute(ctx, sql);

            //更新期初成本信息后，自动重算成本信息
            Task task = new Task(() =>
            {
                var calPara = new Core.DataEntity.CostCalculatePara()
                {
                    EndDate = DateTime.Now,
                    IsWriteLog = false,
                    StockIn = true,
                    StockOut = true,
                    IniDate = GetStockIniDate(ctx),
                };
                if (calPara.IniDate.HasValue == false)
                {
                    return;
                }
                calPara.BeginDate = calPara.IniDate.Value;//从初始化期间开始算 	
                DateTime startDate = calPara.BeginDate.Value;
                IOperationResult result = new OperationResult();
                CostCalcAllPeriod(ctx, result, calPara, startDate, option);
            });
            ThreadWorker.QuequeTask(task, result =>
            {
                if (result?.Exception != null)
                {
                }
            });
        }


        /// <summary>
        /// 更新出入库单的成本信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="stkFormId">库存单据</param>
        /// <param name="billDatas">库单单据数据</param>
        /// <returns></returns>
        public void UpdateInvDataCostInfo(UserContext ctx, HtmlForm formMeta, List<DynamicObject> billDatas)
        {
            var extendedDataSet = new ExtendedDataEntitySet();
            extendedDataSet.Parse(ctx, billDatas, formMeta);
            var stockEntryObjs = extendedDataSet.FindByEntityKey("fentity");

            DataTable dtTemp = new DataTable();
            var flds = "fmaterialid ,fattrinfo,fattrinfo_e ,fcustomdesc,fstorehouseid ,fstorelocationid ,fstockstatus ,flotno ,fmtono ,fownertype ,fownerid ,funitid".SplitKey();
            foreach (var flexKey in flds)
            {
                string mapFldKey = flexKey.Trim();
                var mapField = formMeta.GetField(mapFldKey);
                if (mapField == null)
                {
                    continue;
                }

                dtTemp.Columns.Add(flexKey, typeof(string));
            }

            dtTemp.BeginLoadData();
            foreach (var invUpdateObj in stockEntryObjs)
            {
                var values = new List<string>();
                foreach (var flexKey in flds)
                {
                    string mapFldKey = flexKey.Trim();
                    var mapField = formMeta.GetField(mapFldKey);
                    if (mapField == null)
                    {
                        continue;
                    }

                    var str = mapField.GetValueEx(invUpdateObj.DataEntity).IsNullOrEmpty() ? " " : mapField.GetValueEx(invUpdateObj.DataEntity).ToString();
                    values.Add(str);
                }
                dtTemp.LoadDataRow(values.ToArray(), true);
            }
            dtTemp.EndLoadData();

            var dbSvc = ctx.Container.GetService<IDBService>();
            var tempTable = dbSvc.CreateTempTableWithDataTable(ctx, dtTemp, 2000);

            var sql = @" select b.* 
						from T_STK_INVENTORYLIST b with(nolock) 
						inner join {0} i on b.fmaterialid=i.fmaterialid 
                                        and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									    and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									    and b.fstockstatus=i.fstockstatus and b.flotno=i.flotno
									    and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									    and b.funitid=i.funitid  
						where b.fmainorgid ='{1}'  ".Fmt(tempTable, ctx.Company);

            var datas = dbSvc.ExecuteDynamicObject(ctx, sql).ToList();
            if (datas == null || datas.Count == 0)
            {
                return;
            }
            foreach (var invUpdateObj in stockEntryObjs)
            {
                var price = Convert.ToDecimal(invUpdateObj.DataEntity["fcostprice"]);
                if (price > 0) continue;//已经有值了，不再自动获取

                var find = datas;
                foreach (var flexKey in flds)
                {
                    string mapFldKey = flexKey;
                    var mapField = formMeta.GetField(mapFldKey);
                    if (mapField == null)
                    {
                        continue;
                    }

                    var str = mapField.GetValueEx(invUpdateObj.DataEntity).IsNullOrEmpty() ? " " : mapField.GetValueEx(invUpdateObj.DataEntity).ToString();
                    find = find.Where(f =>
                    {
                        var x = mapField.GetValueEx(f);
                        if (x.IsNullOrEmptyOrWhiteSpace())
                        {
                            x = " ";
                        }

                        return str.EqualsIgnoreCase(x.ToString());
                    }
                    ).ToList();
                }

                if (find.Any())
                {
                    price = Convert.ToDecimal(find[0]["fcostprice"]);
                    invUpdateObj.DataEntity["fcostprice"] = price;
                    invUpdateObj.DataEntity["fcostamt"] = MathUtil.Round(Convert.ToDecimal(invUpdateObj.DataEntity["fqty"]) * price, 2);

                    if (Convert.ToDecimal(invUpdateObj.DataEntity["fcostprice"]) == 0)
                    {
                        invUpdateObj.DataEntity["fprice"] = price;
                        invUpdateObj.DataEntity["famount"] = MathUtil.Round(Convert.ToDecimal(invUpdateObj.DataEntity["fqty"]) * price, 2);

                    }
                }

            }
        }




        /// <summary>
        /// 更新销售合同的成本信息.
        /// 调用时机：月末成本计算
        /// 【单位成本】=【结余单位成本】，【成本】=(【基本单位数量】-【基本单位退款数量】)*【结余单位成本】
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="beginDate">开始日期</param>
        /// <param name="endDate">截止日期</param>
        /// <returns></returns>
        public void UpdateSalesOrderCostInfo(UserContext ctx, DateTime beginDate, DateTime endDate)
        {
            var sql = @"select b.fsoorderinterid
						from t_stk_sostockout a
						inner join t_stk_sostockoutentry b on a.fid=b.fid
						where fmainorgid ='{2}'  and a.fstatus ='E' and a.fcancelstatus ='0' and fdate>='{0}' /*  and fdate<'{1}'  */ 
							".Fmt(beginDate, endDate, ctx.Company);

            var dbSvc = ctx.Container.GetService<IDBService>();
            var datas = dbSvc.ExecuteDynamicObject(ctx, sql);
            var soBillIds = datas?.Select(f => f["fsoorderinterid"].ToString())?.Distinct().ToList();

            if (soBillIds == null || soBillIds.Count == 0)
            {
                return;
            }

            var tempTable = dbSvc.CreateTempTableWithDataList(ctx, soBillIds);

            //用出库单关联库存余额表获取到结余成本 
            var closeDate = (new DateTime(endDate.Year, endDate.Month, 1)).AddDays(-1);//当月最后一天作为每月的关账日期 
            var sqlOut = @"select b.fsoorderinterid,b.fsoorderentryid,case when isnull(i.fcostprice,0) =0 then round(b.fcostprice,2) else isnull(i.fcostprice,0) end as  fcostprice
							from T_STK_SOSTOCKOUTENTRY b with(nolock) 
							inner join T_STK_SOSTOCKOUT a  with(nolock) on a.fid=b.fid  
							inner join {0} x on x.fid=b.fsoorderinterid
							left join T_STK_INVENTORYBALANCE i  with(nolock) on b.fmaterialid=i.fmaterialid 
                                        and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									    and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									    and b.fstockstatus=i.fstockstatus  and b.flotno=i.flotno 
									    and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									    and b.funitid=i.funitid  and i.fclosedate = '{1}' and i.fmainorgid=a.fmainorgid 
							where a.fstatus ='E' and a.fcancelstatus ='0' and b.fsourceformid ='ydj_order'  
 
							".Fmt(tempTable, closeDate);

            //销售合同未出库部分的成本信息
            var sqlUpdate = @"/*dialect*/
							--【单位成本】=【结余单位成本】，【成本】=(【基本单位数量】-【基本单位退款数量】)*【结余单位成本】
							update b set fcostprice = i.fcostprice ,fcost=round( (b.fqty-b.frefundqty) * i.fcostprice ,2)
							from t_ydj_orderentry as b
							inner join ({0}) i on b.fid=i.fsoorderinterid and b.fentryid=i.fsoorderentryid
							where i.fcostprice > 0  
							".Fmt(sqlOut);

            var dbEx = ctx.Container.GetService<IDBServiceEx>();
            dbEx.Execute(ctx, sqlUpdate, 100000);

            //本月创建的销售合同，没有成本的，取库存里的任意一个最新成本
            sqlUpdate = @"/*dialect*/
							--【单位成本】=【结余单位成本】，【成本】=(【基本单位数量】-【基本单位退款数量】)*【结余单位成本】
							update b set fcostprice = i.fcostprice ,fcost=round( (b.fqty-b.frefundqty) * i.fcostprice ,2)
							from t_ydj_orderentry as b
                            inner join t_ydj_order a  with(nolock) on a.fid=b.fid
							inner join T_STK_INVENTORYLIST i with(nolock) on b.fproductid=i.fmaterialid 
                                        and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdes_e=i.fcustomdesc and b.fmtono=i.fmtono and a.fmainorgid = i.fmainorgid 
							where b.fcostprice=0 and i.fcostprice > 0  and  a.fcreatedate>='{0}' and a.fcreatedate<'{1}' and a.fmainorgid ='{2}'   
							".Fmt(beginDate, endDate, ctx.Company);
            dbEx.Execute(ctx, sqlUpdate, 100000);


            dbSvc.DeleteTempTableByName(ctx, tempTable, 10);
        }

        /// <summary>
        /// 更新销售合同的成本信息.
        /// 调用时机：销售出库单审核、销售退货单审核
        /// 【单位成本】=【结余单位成本】，【成本】=(【基本单位数量】-【基本单位退款数量】)*【结余单位成本】
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="stkFormId">库存单据</param>
        /// <param name="billDatas">库单单据数据</param>
        /// <returns></returns>
        public void UpdateSalesOrderCostInfo(UserContext ctx, HtmlForm formMeta, List<DynamicObject> billDatas)
        {
            if (billDatas == null || billDatas.Count == 0)
            {
                return;
            }

            var soBillIds = billDatas.Select(f => f["Id"].ToString()).ToList();
            if (formMeta.Id.EqualsIgnoreCase("stk_sostockout"))
            {
                var extendedDataSet = new ExtendedDataEntitySet();
                extendedDataSet.Parse(ctx, billDatas, formMeta);
                var stockEntryObjs = extendedDataSet.FindByEntityKey("fentity");
                soBillIds = stockEntryObjs.Select(f => f.DataEntity["fsoorderinterid"]?.ToString()).Where(f => f.IsNullOrEmptyOrWhiteSpace() == false)?.ToList();
            }
            else if (formMeta.Id.EqualsIgnoreCase("stk_sostockreturn"))
            {
                var extendedDataSet = new ExtendedDataEntitySet();
                extendedDataSet.Parse(ctx, billDatas, formMeta);
                var stockEntryObjs = extendedDataSet.FindByEntityKey("fentity");
                soBillIds = stockEntryObjs.Select(f => f.DataEntity["fsoorderinterid"]?.ToString()).Where(f => f.IsNullOrEmptyOrWhiteSpace() == false)?.ToList();
            }

            if (soBillIds == null || soBillIds.Count == 0)
            {
                return;
            }

            var dbSvc = ctx.Container.GetService<IDBService>();
            var tempTable = dbSvc.CreateTempTableWithDataList(ctx, soBillIds);

            //用出库单关联及时库存获取到结余成本
            var sqlOut = @"select b.fsoorderinterid,b.fsoorderentryid,isnull(i.fcostprice,0) as  fcostprice
							from T_STK_SOSTOCKOUTENTRY b with(nolock) 
							inner join T_STK_SOSTOCKOUT a  with(nolock) on a.fid=b.fid  
							inner join {0} x on x.fid=b.fsoorderinterid
							left join T_STK_INVENTORYLIST i  with(nolock) on b.fmaterialid=i.fmaterialid
                                        and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdesc=i.fcustomdesc and b.fmtono=i.fmtono
									    and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									    and b.fstockstatus=i.fstockstatus  and b.flotno=i.flotno 
									    and b.fownertype=i.fownertype and b.fownerid=i.fownerid
									    and b.funitid=i.funitid and a.fmainorgid = i.fmainorgid 
							where a.fstatus ='E' and a.fcancelstatus ='0' and b.fsourceformid ='ydj_order' 
 
							".Fmt(tempTable);

            //销售合同未出库部分的成本信息
            var sqlUpdate = @"/*dialect*/
							--【单位成本】=【结余单位成本】，【成本】=(【基本单位数量】-【基本单位退款数量】)*【结余单位成本】
							update b set fcostprice = i.fcostprice ,fcost=round( (b.fqty-b.frefundqty) * i.fcostprice ,2)
							from t_ydj_orderentry as b
							inner join ({0}) i on b.fid=i.fsoorderinterid and b.fentryid=i.fsoorderentryid
							where i.fcostprice > 0  
							".Fmt(sqlOut);

            var dbEx = ctx.Container.GetService<IDBServiceEx>();
            dbEx.Execute(ctx, sqlUpdate, 100000);

            dbSvc.DeleteTempTableByName(ctx, tempTable, 10);
        }



        /// <summary>
        /// 更新销售合同的成本信息.
        /// 调用时机：小程序新增合同现货商品自动带出成本
        /// 【单位成本】=【库存成本】，【成本】=【销售数量】*【库存成本】
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="billDatas">合同单据数据</param>
        /// <returns></returns>
        public void UpdateSalesOrderCostInfo(UserContext ctx, List<DynamicObject> billDatas)
        {
            var billIds = billDatas?.Select(f => f["Id"].ToString())?.Distinct().ToList();
            if (billIds != null && billIds.Count > 0)
            {
                var dbSvc = ctx.Container.GetService<IDBService>();
                var sql = @"/*dialect*/update b set b.fcostprice=isnull(i.fcostprice,0),b.fcost=b.fbizqty*isnull(i.fcostprice,0)
							from t_ydj_orderentry b with(nolock) 
							inner join t_ydj_order a with(nolock) on a.fid=b.fid
							left join T_STK_INVENTORYLIST i with(nolock) on b.fproductid=i.fmaterialid
                                        and b.fattrinfo_e=i.fattrinfo_e 
									    and b.fcustomdes_e=i.fcustomdesc and b.funitid=i.funitid
										and b.fstockstatus=i.fstockstatus
									    and b.fstorehouseid=i.fstorehouseid and b.fstorelocationid=i.fstorelocationid
									    and a.fmainorgid = i.fmainorgid 
							where b.fisoutspot='1' and b.fcostprice=0 and a.fid in ({0}) 
						".Fmt(billIds.JoinEx(",", true));
                var datas = dbSvc.ExecuteDynamicObject(ctx, sql);
            }
        }




        static Random rd = new Random();
        private string CreateTempTableName(UserContext ctx, string startWith)
        {
            var dbSvc = ctx.Container.GetService<IDBService>();
            var seq = ctx.Container.GetService<ISequenceService>().GetSequence<string>();
            var tempTblName = startWith + seq.Substring(3, seq.Length - 3) + rd.Next(10, 99).ToString();
            tempTblName = dbSvc.ApplyPhysicalTempTableName(ctx, tempTblName);

            return tempTblName;
        }

        /// <summary>
        /// 从库存初始化日期或库存单据最小日期开始更新库存余额及成本
        /// 备注：本方法只作为后台方法调用，未开放给前端，如需调用请谨慎
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="option"></param>
        public void UpdateStockBalance(UserContext ctx, OperateOption option)
        {
            //使用异步方式，重算库存余额及成本信息
            Task task = new Task(() =>
            {
                var calPara = new Core.DataEntity.CostCalculatePara()
                {
                    EndDate = DateTime.Now,
                    IsWriteLog = false,
                    StockIn = true,
                    StockOut = true,
                    IniDate = GetStockIniDate(ctx),
                };
                if (calPara.IniDate.HasValue == false)
                {
                    return;
                }
                calPara.BeginDate = calPara.IniDate.Value;//从初始化期间开始算 	
                DateTime startDate = calPara.BeginDate.Value;
                IOperationResult result = new OperationResult();
                CostCalcAllPeriod(ctx, result, calPara, startDate, option);
            });
            ThreadWorker.QuequeTask(task, result =>
            {
                if (result?.Exception != null)
                {
                }
            });
        }
    }

}
