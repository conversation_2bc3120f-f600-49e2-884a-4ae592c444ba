using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Op
{
    /// <summary>
    /// 保存条码扫描记录
    /// </summary>
    [InjectService("savescanresult")]
    public class SaveScanResult : AbstractOperationService
    {
        /// <summary>
        /// 保存条码扫描记录
        /// </summary>
        /// <param name="dataEntities"></param>
        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            //仅提供条码与指定表单的扫描记录进行匹配保存
            if(dataEntities==null
                || dataEntities.IsGreaterThan(1))
            {
                throw new BusinessException("未指定当前条码保存所关联的扫描任务单据，检查传入参数：selectedRows！");
            }

            /**
             * 1、从simpledata里拿到本次保存的条码信息
             * 2、从simppledata里拿到当前是扫描作业第几个步骤的请求保存（收货分2步，发货分2步，调拨有3步）
             * 3、本次保存不做任何分析，只做扫描记录新增或更新
             */
        }
    }
}
