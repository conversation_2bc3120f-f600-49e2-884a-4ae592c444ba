using JieNor.AMS.YDJ.Store.AppService;
using JieNor.AMS.YDJ.Store.AppService.Service;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.StockWaterLevel
{
    /// <summary>
    /// 安全库存水位线：手动更新
    /// </summary>
    [InjectService]
    [FormId("rpt_safetystockwaterlevel")]
    [OperationNo("UpdateStockData")]
    public class UpdateStockData : AbstractOperationServicePlugIn 
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            var topCtx = this.Context.CreateTopOrgDBContext();
            var profileService = topCtx.Container.GetService<ISystemProfile>();
            var finventorywaterlevel = profileService.GetSystemParameter(topCtx, "pur_systemparam", "finventorywaterlevel", false);
            if (!finventorywaterlevel)
            {
                this.Result.ComplexMessage.WarningMessages.Add("总部未启用库存水位线控制功能（计算+校验）！");
                this.Result.SimpleMessage = null;
                return;
            }
            // 通过临时表生成数据
            var tmpTableName = string.Empty;
            var dataSql = string.Empty;
            try
            {
                string agentInfoSql = $@"/*dialect*/select b.fnumber,b.fname from t_bas_agent b (nolock) where fisreseller='0' and fagentstatus='1'
                                          and not exists (select 1 from t_bas_mac mac inner join t_bas_macentry macentry  on mac.fid=macentry.fid where macentry.fsubagentid=b.fid
                                        and mac.fforbidstatus='0' ) and b.fid = '{this.Context.Company}'
                                         group by  b.fnumber,b.fname";
                var agentInfo = this.Context.ExecuteDynamicObject(agentInfoSql, null).FirstOrDefault();
                if (agentInfo == null)
                {
                    throw new Exception("当前经销商不属于计算范围内！");
                }
                DynamicObjectCollection data = null;
                //1.查询企业采购订单和销售合同商品明细商品维度数据
                tmpTableName = this.DBService.CreateTemporaryTableName(this.Context); 

                var stockWaterLevelService = this.Context.Container.GetService<StockWaterLevelService>(); 
                var result = stockWaterLevelService?.StockWaterlevelCalculate(this.Context, this.Option);
            }
            finally
            {
                this.DBService.DeleteTempTableByName(this.Context, tmpTableName, true);
                //刷新页面
                this.AddRefreshPageAction();
            }
        }
    }
}
