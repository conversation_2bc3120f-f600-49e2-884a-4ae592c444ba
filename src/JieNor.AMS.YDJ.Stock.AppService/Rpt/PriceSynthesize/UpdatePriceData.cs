using JieNor.AMS.YDJ.Store.AppService;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.PriceSynthesize
{
    /// <summary>
    /// 综合价目表计算：手动更新
    /// </summary>
    [InjectService]
    [FormId("rpt_pricesynthesize")]
    [OperationNo("UpdatePriceData")]
    public class UpdatePriceData : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            // 通过临时表生成数据
            var tmpTableName = string.Empty;
            var dataSql = string.Empty;
            try
            {
                DynamicObjectCollection data = null;
                //选中行Id
                var rowIds = this.SelectedRows.Select(x => x.PkValue).ToList();
                if (rowIds != null && rowIds.Count > 0)
                {
                    //查询经销商商品数据
                    dataSql = $@"/*dialect*/select t1.fmaterialid,t1.fattrinfo_e,fcustomdesc,t1.funitid,t2.fmainorgid fproductorgid,
                                fpurfacprice,fpurdealprice,funifysaleprice,fsellprice,freprice,fterprice,funitcostprice
                                from t_ydj_pricesynthesize t1 with(nolock) 
                                inner join t_bd_material t2 with(nolock) on t1.fmaterialid = t2.fid
                                where t1.fmainorgid='{this.Context.Company}' and t1.fid in ({rowIds.JoinEx(",", true)})";
                    data = this.DBService.ExecuteDynamicObject(this.Context, dataSql);
                }
                else
                {
                    //1.查询企业采购订单和销售合同商品明细商品维度数据
                    tmpTableName = this.DBService.CreateTemporaryTableName(this.Context);
                    var tableStrSql = $@"/*dialect*/select * into {tmpTableName} from (
                                select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{this.Context.Company}' fmainorgid from t_ydj_poorderentry t1 with(nolock)
                                left join t_ydj_purchaseorder t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{this.Context.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e,t1.funitid,t3.fmainorgid
                                union
                                select t1.fproductid fmaterialid,t1.fattrinfo_e,t1.fcustomdes_e fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{this.Context.Company}' fmainorgid from t_ydj_orderentry t1 with(nolock)
                                left join t_ydj_order t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fproductid = t3.fid
                                where t2.fmainorgid='{this.Context.Company}'
                                group by t1.fproductid,t1.fattrinfo_e,t1.fcustomdes_e,t1.funitid,t3.fmainorgid
                                ) tab ";
                    this.DBService.ExecuteDynamicObject(this.Context, tableStrSql);

                    //1.查询企业盘点单/其他入库单/销售退货单商品明细商品维度数据
                    var insertSql = $@"/*dialect*/insert into {tmpTableName} 
                                (fmaterialid,fattrinfo_e,fcustomdesc,funitid,fproductorgid,fmainorgid)
                                (select* from (select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{this.Context.Company}' fmainorgid from t_stk_invverifyentry t1 with(nolock)
                                left join t_stk_invverify t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{this.Context.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid
                                union
                                select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{this.Context.Company}' fmainorgid from t_stk_otherstockinentry t1 with(nolock)
                                left join t_stk_otherstockin t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{this.Context.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid
                                union
                                select t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{this.Context.Company}' fmainorgid from t_stk_sostockreturnentry t1 with(nolock)
                                left join t_stk_sostockreturn t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fmaterialid = t3.fid
                                where t2.fmainorgid='{this.Context.Company}'
                                group by t1.fmaterialid,t1.fattrinfo_e,t1.fcustomdesc,t1.funitid,t3.fmainorgid
                                ) tab where not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=tab.fmaterialid and t4.fattrinfo_e=tab.fattrinfo_e and t4.funitid=tab.funitid and t4.fcustomdesc=tab.fcustomdesc))";
                    this.DBService.ExecuteDynamicObject(this.Context, insertSql);

                    //数据隔离规则SQL
                    var authPara = new DataQueryRuleParaInfo();
                    var tempView = this.Context.GetAuthProductDataPKID(authPara);

                    //2.查询企业销售价目商品明细商品维度数据
                    insertSql = $@"insert into {tmpTableName}
                                (fmaterialid,fattrinfo_e,fcustomdesc,funitid,fproductorgid,fmainorgid)
                                (select t1.fproductid fmaterialid,t1.fattrinfo_e,'' fcustomdesc,t1.funitid,t3.fmainorgid fproductorgid,'{this.Context.Company}' fmainorgid from t_ydj_priceentry t1 with(nolock)
                                left join t_ydj_price t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fproductid = t3.fid
                                where not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=t1.fproductid and t4.fattrinfo_e=t1.fattrinfo_e and t4.funitid=t1.funitid and t4.fcustomdesc='')
                                and t1.fconfirmstatus=2 and t2.fmainorgid in ('{this.Context.Company}','{this.Context.ParentCompanyId}','{this.Context.TopCompanyId}')
                                and t1.fstartdate <= getdate() and t1.fexpiredate >= getdate() --日期
                                and t1.fproductid in ({tempView})
                                group by t1.fproductid,t1.fattrinfo_e,t1.funitid,t3.fmainorgid)";
                    this.DBService.ExecuteDynamicObject(this.Context, insertSql);
                    //3.查询企业采购价目商品明细商品维度数据
                    var fmainorgids = this.Context.IsSecondOrg ? $@"'{this.Context.Company}'" : $@"'{this.Context.Company}','{this.Context.TopCompanyId}'";//二级分销不能看总部采购价
                    insertSql = $@"insert into {tmpTableName}
                                (fmaterialid,fattrinfo_e,fcustomdesc,funitid,fproductorgid,fmainorgid)
                                (select t1.fproductid_e fmaterialid,t1.fattrinfo_e,'' fcustomdesc,t1.funitid_e funitid,t3.fmainorgid fproductorgid,'{this.Context.Company}' fmainorgid from t_ydj_purchasepriceentry t1 with(nolock)
                                left join t_ydj_purchaseprice t2 with(nolock) on t1.fid=t2.fid
                                inner join t_bd_material t3 with(nolock) on t1.fproductid_e = t3.fid
                                where not exists(select 1 from {tmpTableName} t4 with(nolock) where t4.fmaterialid=t1.fproductid_e and t4.fattrinfo_e=t1.fattrinfo_e and t4.funitid=t1.funitid_e and t4.fcustomdesc='' )
                                and t1.fconfirmstatus=2 and t2.fmainorgid in ({fmainorgids})     
                                and t1.fstartdate_e <= getdate() and t1.fexpiredate_e >= getdate() --日期     
                                and t1.fproductid_e in ({tempView})
                                group by t1.fproductid_e,t1.fattrinfo_e,t1.funitid_e,t3.fmainorgid)";
                    this.DBService.ExecuteDynamicObject(this.Context, insertSql);
                }

                var priceCalculateService = this.Context.Container.GetService<PriceCalculateService>();
                this.Option.SetVariableValue("UpdatePrice_RowIds", rowIds);//更新勾选行数据
                var result = priceCalculateService?.PriceCalculate(this.Context, tmpTableName, this.Option, data);
            }
            finally
            {
                this.DBService.DeleteTempTableByName(this.Context, tmpTableName, true);
                //刷新页面
                this.AddRefreshPageAction();
            }
        }
    }
}
