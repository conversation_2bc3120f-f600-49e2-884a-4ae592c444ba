using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.DataEntity.Inventory;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Stock.AppService.Rpt.StockDetail
{
    /// <summary>
    /// 物料收发明细表：数据源准备
    /// </summary>
    /// <remarks>
    /// 报表取数实现逻辑：
    /// 1、根据输入起止日期范围，确定最接近开始日期的库存余额数据
    /// 2、根据起止日期范围以及其它过滤条件获得库存余额日期至结束日期间的所有符合条件的库存单据
    /// 3、将上述数据全部取到一个临时存储表里，并获得所有维度数据
    /// 4、将开始日期之前的数据按维度进行分组汇总得到各维度数据期初数
    /// 5、将开始日期与结束日期间的数据分别按收入与发出进行汇总，得到各维度的收入数与发出数
    /// 6、根据各维度的期初数，收入数，发出数，计算出期末数。
    /// </remarks>
    [InjectService]
    [FormId("rpt_stockdetail")]
    [OperationNo("QueryListReportData")]
    public class QueryListReportData : AbstractInventoryReportPlugIn
    {
        protected override string DetailLogTableName
        {
            get
            {
                var tableName = this.ParentPageSession?.ReportDataTableName as string;
                if (!tableName.IsNullOrEmptyOrWhiteSpace())
                {
                    return this.ParentPageSession.ReportDataTableName;
                }
                return base.DetailLogTableName;
            }
        }

        protected override void PrepareStockDetailData()
        {
            //如果是来自于汇总表联查请求，则不用再准备数据源
            var invFlexId = this.ParentPageSession?.InvFlexId as string;
            if (invFlexId.IsNullOrEmptyOrWhiteSpace())
            {
                base.PrepareStockDetailData();
            }
        }
        /// <summary>
        /// 检查当前过滤界面必须录入的信息
        /// </summary>
        protected override void CheckDataEnvironment()
        {
            if (this.CustomFilterObject.IsNullOrEmpty())
            {
                throw new BusinessException("过滤条件不可以为空！");
            }
            DateTime? dtDateFrom = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtDateFrom == null)
            {
                throw new BusinessException("过滤条件【业务日期从】必录！");
            }
            DateTime? dtDateTo = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtDateTo == null)
            {
                throw new BusinessException("过滤条件【业务日期至】必录！");
            }
            if (dtDateTo < dtDateFrom)
            {
                throw new BusinessException("过滤条件【业务日期至】不得小于【业务日期从】！");
            }


            //var billStatus = this.CustomFilterObject["fbillstatus"] as string;
            //if (billStatus.IsNullOrEmptyOrWhiteSpace())
            //{
            //    throw new BusinessException("过滤条件【单据状态】必录！");
            //}
        }

        /// <summary>
        /// 获得当前库存期间信息
        /// </summary>
        /// <param name="dtStart"></param>
        /// <param name="dtEnd"></param>
        protected override void TryGetInventoryPeriodDate(out DateTime? dtStart, out DateTime? dtEnd)
        {
            dtStart = (DateTime?)this.CustomFilterObject["fdatefrom"];
            if (dtStart.HasValue)
            {
                dtStart = dtStart.Value.DayBegin();
            }
            dtEnd = (DateTime?)this.CustomFilterObject["fdateto"];
            if (dtEnd.HasValue)
            {
                dtEnd = dtEnd.Value.DayEnd();
            }
        }

        //商品收发明细表【无发生额不显示】【期末无结存不显示】过滤
        protected override void OnPrepareReportQueryParameter(SqlBuilderParameter listQueryPara)
        {
            base.OnPrepareReportQueryParameter(listQueryPara);
            if (listQueryPara == null) return;
            if (this.CustomFilterObject == null) return;
            var notShowWhenNoTranField = this.CustomFilterFormMeta.GetField("fnotshowwhennotran");
            var notShowWhenNoEndingField = this.CustomFilterFormMeta.GetField("fnotshowwhennoending");
            var bNotShowWhenNoTran = notShowWhenNoTranField?.DynamicProperty?.GetValue<bool>(this.CustomFilterObject) == true;
            var bNotShowWhenNoEndings = notShowWhenNoEndingField?.DynamicProperty?.GetValue<bool>(this.CustomFilterObject) == true;
            //如果无发生额不显示为true，收入和发出不能为0
            if (bNotShowWhenNoTran)
            {
                //listQueryPara.FilterString = listQueryPara.FilterString.JoinFilterString("(fbizstockinqty!=0 or fbizstockoutqty!=0)");
                //基本单位字段过滤
                listQueryPara.FilterString = listQueryPara.FilterString.JoinFilterString("(fstockinqty!=0 or fstockoutqty!=0)");
            }
            //如果期末无结存不显示，结存不能为0
            if (bNotShowWhenNoEndings)
            {
                listQueryPara.FilterString = listQueryPara.FilterString.JoinFilterString("fendingqty!=0");
            }

            //库存综合查询携带过滤辅助属性，定制说明，基本单位维度进行查询
            var fattrinfo_e = this.ParentPageSession?.fattrinfo_e as string;
            if (!string.IsNullOrWhiteSpace(fattrinfo_e))
            {
                listQueryPara.FilterString = listQueryPara.FilterString.JoinFilterString($"fattrinfo_e = '{fattrinfo_e}' ");
            }
            var customdesc = this.ParentPageSession?.fcustomdesc as string;
            if (!string.IsNullOrWhiteSpace(customdesc))
            {
                listQueryPara.FilterString = listQueryPara.FilterString.JoinFilterString($"fcustomdesc='{customdesc}'");
            }
            var unitid = this.ParentPageSession?.funitid as string;
            if (!string.IsNullOrWhiteSpace(unitid))
            {
                listQueryPara.FilterString = listQueryPara.FilterString.JoinFilterString($"funitid='{unitid}'");
            }
        }


        /// <summary>
        /// 过滤库存业务单据
        /// </summary>
        /// <param name="stockUpdateSetting"></param>
        /// <param name="sqlBuilderParameter"></param>
        /// <param name="lstSqlSelFieldList"></param>
        protected override void FilterInventoryBillData(StockUpdateSetting stockUpdateSetting, SqlBuilderParameter sqlBuilderParameter, List<string> lstSqlSelFieldList)
        {
            this.BuildSqlParameterFilter(stockUpdateSetting, sqlBuilderParameter);
        }

        /// <summary>
        /// 过滤库存余额
        /// </summary>
        /// <param name="sqlBuilderParameter"></param>
        /// <param name="lstSqlSelFieldList"></param>
        protected override void FilterInventoryBalanceData(SqlBuilderParameter sqlBuilderParameter, List<string> lstSqlSelFieldList)
        {
            this.BuildSqlParameterFilter(null, sqlBuilderParameter);
        }

        /// <summary>
        /// 创建维度过滤条件
        /// </summary>
        /// <param name="sqlBuilderParameter"></param>
        private void BuildSqlParameterFilter(StockUpdateSetting stockUpdateSetting, SqlBuilderParameter sqlBuilderParameter)
        {
            var strFilter = "";
            var lstParams = new List<SqlParam>();

            //单据状态过滤
            #region 单据状态过滤条件处理

            //var billStatus = this.CustomFilterObject["fbillstatus"] as string;
            var billStatusField = sqlBuilderParameter.HtmlForm.GetField(sqlBuilderParameter.HtmlForm.BizStatusFldKey);
            if (billStatusField != null)
            {
                strFilter = $" {sqlBuilderParameter.HtmlForm.BizStatusFldKey}='E' ";
                if (sqlBuilderParameter.HtmlForm.Id.EqualsIgnoreCase("stk_inventorytransfer") && stockUpdateSetting.UpdInvServiceId.EqualsIgnoreCase("from"))
                {
                    strFilter = $" ({sqlBuilderParameter.HtmlForm.BizStatusFldKey}='E' or ({sqlBuilderParameter.HtmlForm.BizStatusFldKey}='D' and fisstockout='1' ) ) ";
                }
                //    var strStatusFilter = "";
                //    int flag = 1;
                //    foreach (var statusItem in billStatus.Split(','))
                //    {
                //        if (strStatusFilter.IsNullOrEmptyOrWhiteSpace())
                //        {
                //            strStatusFilter = $" {sqlBuilderParameter.HtmlForm.BizStatusFldKey}=@billStatus{flag} ";
                //        }
                //        else
                //        {
                //            strStatusFilter += $" or {sqlBuilderParameter.HtmlForm.BizStatusFldKey}=@billStatus{flag}";
                //        }
                //        lstParams.Add(new SqlParam($"billStatus{flag}", DbType.String, statusItem));

                //        flag++;
                //    }
                //    strFilter = strFilter.JoinFilterString(strStatusFilter);
            }
            #endregion


            if (string.IsNullOrWhiteSpace(Convert.ToString(this.CustomFilterObject["fmaterialidfrom"])) && !string.IsNullOrWhiteSpace(this.ParentPageSession?.fmaterialidfrom as string))
            {
                this.CustomFilterObject["fmaterialidfrom"] = this.ParentPageSession?.fmaterialidfrom as string;
                var matObj = this.Context.LoadBizDataById("bd_material", this.ParentPageSession?.fmaterialidfrom as string);
                this.CustomFilterObject["fmaterialidfrom_ref"] = matObj;
                this.CustomFilterObject["fmaterialidto"] = this.ParentPageSession?.fmaterialidfrom as string;
                this.CustomFilterObject["fmaterialidto_ref"] = matObj;
            }

            if (string.IsNullOrWhiteSpace(Convert.ToString(this.CustomFilterObject["fstorehouseidfrom"])) && !string.IsNullOrWhiteSpace(this.ParentPageSession?.fstorehouseidfrom as string))
            {
                this.CustomFilterObject["fstorehouseidfrom"] = this.ParentPageSession?.fstorehouseidfrom as string;
                var matObj = this.Context.LoadBizDataById("ydj_storehouse", this.ParentPageSession?.fstorehouseidfrom as string);
                this.CustomFilterObject["fstorehouseidfrom_ref"] = matObj;
                this.CustomFilterObject["fstorehouseidto"] = this.ParentPageSession?.fstorehouseidfrom as string;
                this.CustomFilterObject["fstorehouseidto_ref"] = matObj;
            }

            if (string.IsNullOrWhiteSpace(Convert.ToString(this.CustomFilterObject["fstorelocationidfrom"])) && !string.IsNullOrWhiteSpace(this.ParentPageSession?.fstorelocationidfrom as string))
            {
                this.CustomFilterObject["fstorelocationidfrom"] = this.ParentPageSession?.fstorelocationidfrom as string;
                DynamicObject storehouse = this.CustomFilterObject["fstorehouseidfrom_ref"] as DynamicObject;
                if (storehouse != null)
                {
                    var storelocations = storehouse["fentity"] as DynamicObjectCollection;
                    var entryItem = storelocations.Where(a => Convert.ToString(a["Id"]).Equals(Convert.ToString(ParentPageSession?.fstorelocationidfrom))).FirstOrDefault();

                    if (entryItem != null)
                    {
                        var storehouseFormMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_storehouse");
                        var storehouseItem = storehouseFormMeta.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;
                        storehouseItem["id"] = this.ParentPageSession?.fstorelocationidfrom;
                        storehouseItem["fnumber"] = entryItem["flocnumber"];
                        storehouseItem["fname"] = entryItem["flocname"];
                        this.CustomFilterObject["fstorelocationidfrom_ref"] = storehouseItem;
                        this.CustomFilterObject["fstorelocationidto"] = this.ParentPageSession?.fstorelocationidfrom as string;
                        this.CustomFilterObject["fstorelocationidto_ref"] = storehouseItem;
                    }
                }
            }

            //从库存综合查询点击库存量进来默认查询当月
            if (this.ParentPageFormId == "rpt_stocksynthesize" &&
               (DateTime?)this.CustomFilterObject["fdatefrom"] == DateTime.Today &&
               (DateTime?)this.CustomFilterObject["fdateto"] == DateTime.Today &&
               !string.IsNullOrWhiteSpace(this.ParentPageSession?.fdatefrom as string) &&
                !string.IsNullOrWhiteSpace(this.ParentPageSession?.fdateto as string))
            {
                DateTime? dtStart;
                DateTime? dtEnd;
                this.CustomFilterObject["fdatefrom"] = Convert.ToDateTime(this.ParentPageSession?.fdatefrom as string);
                this.CustomFilterObject["fdateto"] = Convert.ToDateTime(this.ParentPageSession?.fdateto as string);
                this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);
            }

            //物料过滤条件
            #region 物料过滤条件处理
            BuildFieldFilter(stockUpdateSetting, "fmaterialid", "fmaterialidfrom", "fmaterialidto", ref strFilter, lstParams);
            #endregion
            //仓库过滤
            BuildFieldFilter(stockUpdateSetting, "fstorehouseid", "fstorehouseidfrom", "fstorehouseidto", ref strFilter, lstParams);
            //仓位过滤
            BuildFieldFilter(stockUpdateSetting, "fstorelocationid", "fstorelocationidfrom", "fstorelocationidto", ref strFilter, lstParams);
            //物流跟踪号过滤
            BuildFieldFilter(stockUpdateSetting, "fmtono", "fmtonofrom", "fmtonoto", ref strFilter, lstParams);

            //送入查询对象实现过滤
            sqlBuilderParameter.FilterString = sqlBuilderParameter.FilterString.JoinFilterString(strFilter);
            sqlBuilderParameter.DynamicParams.AddRange(lstParams);

            // Feat#4968 【慕思-大商需求-118】在销售出库单表体增加按钮【库位推荐】，点此按钮时根据表体物料+仓库按先进先出原则推荐库位，并更新到表体
            // 《商品收发明细表》中《盘点单》目前取的是【盘点日期】，改为取商品明细行的【入库日期】。
            if (sqlBuilderParameter.HtmlForm.Id.EqualsIgnoreCase("stk_inventoryverify"))
            {
                var dateField = sqlBuilderParameter.HtmlForm.GetField(sqlBuilderParameter.HtmlForm.BizDateFldKey);

                var fsourcebilldate = sqlBuilderParameter.SelectedFieldKeys.FirstOrDefault(s =>
                      s.EqualsIgnoreCase($"{dateField.Id} as fsourcebilldate"));
                if (!fsourcebilldate.IsNullOrEmptyOrWhiteSpace())
                {
                    var idx = sqlBuilderParameter.SelectedFieldKeys.IndexOf(fsourcebilldate);//记录位置
                    sqlBuilderParameter.SelectedFieldKeys.Remove(fsourcebilldate);
                    //sqlBuilderParameter.SelectedFieldKeys.Add($"findate as fsourcebilldate");
                    sqlBuilderParameter.SelectedFieldKeys.Insert(idx, $"findate as fsourcebilldate");//插入到原本的位置

                    sqlBuilderParameter.FilterString = sqlBuilderParameter.FilterString.Replace($"{dateField.Id}", "findate");
                }
            }
        }

        private string BuildFieldFilter(StockUpdateSetting stockUpdateSetting, string flexFieldKey, string valueFromFieldKey, string valueToFieldKey, ref string strFilter, List<SqlParam> lstParams)
        {
            object valueNumberFrom = "";
            object valueNumberTo = "";
            var valueFieldFrom = this.CustomFilterFormMeta.GetField(valueFromFieldKey);
            var valueFieldTo = this.CustomFilterFormMeta.GetField(valueToFieldKey);
            string queryFromFieldKey = "";
            string queryToFieldKey = "";

            bool isFilterByNumber = false;

            if (valueFieldFrom != null)
            {
                if (valueFieldFrom is HtmlBaseDataField)
                {
                    var valueFromObj = (valueFieldFrom as HtmlBaseDataField).RefDynamicProperty.GetValue<DynamicObject>(this.CustomFilterObject);
                    if (valueFromObj != null)
                    {
                        valueNumberFrom = (valueFieldFrom as HtmlBaseDataField).GetRefNumberField(this.Context)?.DynamicProperty?.GetValue(valueFromObj);
                        isFilterByNumber = true;

                        queryFromFieldKey = (valueFieldFrom as HtmlBaseDataField).RefHtmlForm(this.Context).NumberFldKey;
                    }
                }
                else
                {
                    valueNumberFrom = valueFieldFrom.DynamicProperty?.GetValue(this.CustomFilterObject);
                }
            }
            if (valueFieldTo != null)
            {
                if (valueFieldTo is HtmlBaseDataField)
                {
                    var valueToObj = (valueFieldTo as HtmlBaseDataField).RefDynamicProperty.GetValue<DynamicObject>(this.CustomFilterObject);
                    if (valueToObj != null)
                    {
                        valueNumberTo = (valueFieldTo as HtmlBaseDataField).GetRefNumberField(this.Context)?.DynamicProperty?.GetValue(valueToObj);
                        isFilterByNumber = true;

                        queryToFieldKey = (valueFieldTo as HtmlBaseDataField).RefHtmlForm(this.Context).NumberFldKey;
                    }
                }
                else
                {
                    valueNumberTo = valueFieldTo.DynamicProperty?.GetValue(this.CustomFilterObject);
                }
            }
            if (!valueNumberFrom.IsNullOrEmptyOrWhiteSpace())
            {
                string strBillFieldKey = null;
                stockUpdateSetting?.InvFlexFieldSetting?.TryGetValue(flexFieldKey, out strBillFieldKey);
                if (strBillFieldKey.IsNullOrEmptyOrWhiteSpace()) strBillFieldKey = flexFieldKey;

                if (isFilterByNumber)
                {
                    strFilter = strFilter.JoinFilterString($"{strBillFieldKey}.{queryFromFieldKey}>=@{strBillFieldKey}ValueFrom");
                }
                else
                {
                    strFilter = strFilter.JoinFilterString($"{strBillFieldKey}>=@{strBillFieldKey}ValueFrom");
                }
                lstParams.Add(new SqlParam($"{strBillFieldKey}ValueFrom", DbType.String, valueNumberFrom));
            }

            if (!valueNumberTo.IsNullOrEmptyOrWhiteSpace())
            {
                string strBillFieldKey = null;
                stockUpdateSetting?.InvFlexFieldSetting?.TryGetValue(flexFieldKey, out strBillFieldKey);
                if (strBillFieldKey.IsNullOrEmptyOrWhiteSpace()) strBillFieldKey = flexFieldKey;

                if (isFilterByNumber)
                {
                    strFilter = strFilter.JoinFilterString($"{strBillFieldKey}.{queryToFieldKey}<=@{strBillFieldKey}ValueTo");
                }
                else
                {
                    strFilter = strFilter.JoinFilterString($"{strBillFieldKey}<=@{strBillFieldKey}ValueTo");
                }
                lstParams.Add(new SqlParam($"{strBillFieldKey}ValueTo", DbType.String, valueNumberTo));
            }

            return strFilter;
        }

        /// <summary>
        /// 计算库存数据
        /// </summary>
        protected override void ComputeInventoryData()
        {
            StringBuilder sb = new StringBuilder();
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            sw.Restart();
            //将当前数据源表中的数据进行加工生成报表最终要的数据格式
            DateTime? dtStart;
            DateTime? dtEnd;

            var invFlexId = this.ParentPageSession?.InvFlexId as string;
            if (!invFlexId.IsNullOrEmptyOrWhiteSpace())
            {
                dtStart = this.ParentPageSession.ReportStartDate;
                dtEnd = this.ParentPageSession.ReportEndDate;

                if (dtStart == null)
                {
                    throw new BusinessException("联查收发明细失败：请尝试重新打开物料收发汇总表后再试！");
                }
            }
            else
            {
                this.TryGetInventoryPeriodDate(out dtStart, out dtEnd);
            }
            sb.AppendLine("1:" + sw.ElapsedMilliseconds);
            //将每条明细的【总成本(加权平均)】关联查出来，并更新进临时表
            LinkUpdateDetailCostAMT();
            sb.AppendLine("2:" + sw.ElapsedMilliseconds);

            var invFlexModel = InventoryFlexModel.GetInstance(this.Context);

            StringBuilder sbInsertSql = new StringBuilder();
            StringBuilder sbSelectSql = new StringBuilder();
            StringBuilder sbInvFlexSubQuery = new StringBuilder();
            StringBuilder sbBeginingSubQuery = new StringBuilder();
            StringBuilder sbStockInSubQuery = new StringBuilder();
            StringBuilder sbStockOutSubQuery = new StringBuilder();

            var groupby = string.Join(",", invFlexModel.InvFlexFieldKeys);

            sbInsertSql.Append($"insert into {this.DataSourceTableName}(fid,fjnidentityid,fformid,flinkbilldate,flinkbillapprovedate,flinkformid,flinkbillno,flinkbillinterid,flinkbillentryid,fcostamt");
            sbSelectSql.Append($@"
select distinct row_number() over(order by {groupby},flinkbilldate,fupdatefactor,flinkbillinterid,flinkbillentryid,flinkbillapprovedate) as fid
                ,row_number() over(order by {groupby},flinkbilldate,fupdatefactor,flinkbillinterid,flinkbillentryid,flinkbillapprovedate) as fjnidentityid
                ,'{this.HtmlForm.Id}' as fformid,flinkbilldate,flinkbillapprovedate,flinkformid,flinkbillno,flinkbillinterid,flinkbillentryid,fcostamt");

            sbInvFlexSubQuery.Append($"select distinct {invFlexModel.MainOrgIdFieldKey} as fmainorgid,finvflexid");
            sbBeginingSubQuery.Append($"select {invFlexModel.MainOrgIdFieldKey} as fmainorgid,null as flinkbilldate,'' as flinkformid,'' as flinkbillno,'' as flinkbillinterid,'' as flinkbillentryid");
            sbStockInSubQuery.Append($"select {invFlexModel.MainOrgIdFieldKey} as fmainorgid,finvflexid,fcomputetype,(fupdatefactor * -1) as fupdatefactor,fsourcebilldate as flinkbilldate,fapprovedate as flinkbillapprovedate, fsourceformid as flinkformid,fsourcebillno as flinkbillno,fsourceinterid as flinkbillinterid,fsourceentryid as flinkbillentryid,fcostamt");
            sbStockOutSubQuery.Append($"select {invFlexModel.MainOrgIdFieldKey} as fmainorgid,finvflexid,fcomputetype,(fupdatefactor * -1) as fupdatefactor,fsourcebilldate as flinkbilldate,fapprovedate as flinkbillapprovedate, fsourceformid as flinkformid,fsourcebillno as flinkbillno,fsourceinterid as flinkbillinterid,fsourceentryid as flinkbillentryid,fcostamt");


            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                var flexField = this.HtmlForm.GetField(flexItem);
                if (flexField != null)
                {
                    if (flexItem.EqualsIgnoreCase("fattrinfo")) continue;
                    sbInsertSql.Append($",{flexItem}");
                    sbSelectSql.Append($",{flexItem}");
                }
                sbInvFlexSubQuery.Append($",{flexItem}");
                sbBeginingSubQuery.Append($",{flexItem}");
                sbStockInSubQuery.Append($",{flexItem}");
                sbStockOutSubQuery.Append($",{flexItem}");
            }
            sbInsertSql.AppendLine(",fbeginingqty,fbizbeginingqty,fstockinqty,fbizstockinqty,fstockoutqty,fbizstockoutqty,fendingqty,fbizendingqty)");

            sbSelectSql.AppendLine($@"
,case fcomputetype when '100' then fbeginingqty 
        else SUM(fbeginingqty + fstockinqty-fstockoutqty) 
                OVER(partition by finvflexid
                ORDER BY finvflexid,fcomputetype,flinkbilldate,fupdatefactor,flinkbillinterid,flinkbillentryid,flinkbillapprovedate) - fbeginingqty - fstockinqty + fstockoutqty end AS fbeginingqty");
            sbSelectSql.AppendLine($@"
,case fcomputetype when '100' then fbizbeginingqty 
        else SUM(fbizbeginingqty + fbizstockinqty-fbizstockoutqty) 
                OVER(partition by finvflexid
                ORDER BY finvflexid,fcomputetype,flinkbilldate,fupdatefactor,flinkbillinterid,flinkbillentryid,flinkbillapprovedate) - fbizbeginingqty - fbizstockinqty + fbizstockoutqty end AS fbizbeginingqty");
            sbSelectSql.AppendLine(",fstockinqty,fbizstockinqty,fstockoutqty,fbizstockoutqty");
            sbSelectSql.AppendLine($@"
,case fcomputetype when '100' then fbeginingqty+fstockinqty-fstockoutqty 
        else SUM(fbeginingqty + fstockinqty-fstockoutqty) 
                OVER(partition by finvflexid 
                ORDER BY finvflexid,fcomputetype,flinkbilldate,fupdatefactor,flinkbillinterid,flinkbillentryid,flinkbillapprovedate) end AS fendingqty");
            sbSelectSql.AppendLine($@"
,case fcomputetype when '100' then fbizbeginingqty+fbizstockinqty-fbizstockoutqty 
        else SUM(fbizbeginingqty + fbizstockinqty-fbizstockoutqty) 
                OVER(partition by finvflexid 
                ORDER BY finvflexid,fcomputetype,flinkbilldate,fupdatefactor,flinkbillinterid,flinkbillentryid,flinkbillapprovedate) end AS fbizendingqty");

            sbInvFlexSubQuery.AppendLine()
                .AppendLine($"from {this.DetailLogTableName} t0 ");

            sbBeginingSubQuery.Append($", sum({invFlexModel.QtyFieldKey}*fupdatefactor) as fbeginingqty")
                .Append($", sum({invFlexModel.StockQtyFieldKey}*fupdatefactor) as fbizbeginingqty")
                .Append(", 0 as fstockinqty")
                .Append(", 0 as fbizstockinqty")
                .Append(", 0 as fstockoutqty")
                .Append(", 0 as fbizstockoutqty")
                .Append($", sum({invFlexModel.QtyFieldKey}*fupdatefactor) as fendingqty")
                .Append($", sum({invFlexModel.StockQtyFieldKey}*fupdatefactor) as fbizendingqty")
                .Append($", sum(fcostamt) as fcostamt")
                .AppendLine()
                .AppendLine($"from {this.DetailLogTableName} t0 ")
                .AppendLine($"where fsourcebilldate<@startDate")
                .AppendLine($"group by fmainorgid,{groupby}");

            sbStockInSubQuery.Append(", 0 as fbeginingqty")
                .Append(", 0 as fbizbeginingqty")
                .Append($",sum({invFlexModel.QtyFieldKey}) as fstockinqty")
                .Append($",sum({invFlexModel.StockQtyFieldKey}) as fbizstockinqty")
                .Append(", 0 as fstockoutqty")
                .Append(", 0 as fbizstockoutqty")
                .Append(", 0 as fendingqty")
                .Append(", 0 as fbizendingqty")
                .AppendLine()
                .AppendLine($"from {this.DetailLogTableName} t0 ")
                .AppendLine($"where fsourcebilldate>=@startDate and fupdatefactor=1")
                .AppendLine($"group by fmainorgid,finvflexid,fcomputetype,fupdatefactor,fsourcebilldate,fapprovedate,fsourceformid,fsourcebillno,fsourceinterid,fsourceentryid,fcostamt,{groupby}");

            sbStockOutSubQuery.Append($",0 as fbeginingqty")
                .Append(", 0 as fbizbeginingqty")
                .Append(", 0 as fstockinqty")
                .Append(", 0 as fbizstockinqty")
                .Append($", sum({invFlexModel.QtyFieldKey}) as fstockoutqty")
                .Append($", sum({invFlexModel.StockQtyFieldKey}) as fbizstockoutqty")
                .Append(", 0 as fendingqty")
                .Append(", 0 as fbizendingqty")
                .AppendLine()
                .AppendLine($"from {this.DetailLogTableName} t0 ")
                .AppendLine($"where fsourcebilldate>=@startDate and fupdatefactor=-1")
                .AppendLine($"group by fmainorgid,finvflexid,fcomputetype,fupdatefactor,fsourcebilldate,fapprovedate,fsourceformid,fsourcebillno,fsourceinterid,fsourceentryid,fcostamt,{groupby}");

            var strSql = $@"
{sbInsertSql.ToString()}
{sbSelectSql.ToString()}
from 
(
    select u1.fmainorgid
            ,u1.finvflexid
            ,'100' as fcomputetype
            , -1 as fupdatefactor
            ,null as flinkbilldate
            ,null as flinkbillapprovedate
            ,'' as flinkformid
            ,'' as flinkbillno
            ,'' as flinkbillinterid
            ,'' as flinkbillentryid
            ,isnull(u2.fcostamt,0) as fcostamt
";
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                if (flexItem.EqualsIgnoreCase("fattrinfo")) continue;
                strSql += $@"            ,u1.{flexItem}";
            }
            strSql += $@"
            ,isnull(u2.fbeginingqty,0) as fbeginingqty
            ,isnull(u2.fbizbeginingqty,0) as fbizbeginingqty
            ,isnull(u2.fstockinqty,0) as fstockinqty
            ,isnull(u2.fbizstockinqty,0) as fbizstockinqty
            ,isnull(u2.fstockoutqty,0) as fstockoutqty
            ,isnull(u2.fbizstockoutqty,0) as fbizstockoutqty
            ,isnull(u2.fendingqty,0) as fendingqty
            ,isnull(u2.fbizendingqty,0) as fbizendingqty
    from
    ({sbInvFlexSubQuery.ToString()}) u1
    left join ({sbBeginingSubQuery.ToString()}) u2 on 1=1 ";
            foreach (var flexItem in invFlexModel.InvFlexFieldKeys)
            {
                if (flexItem.EqualsIgnoreCase("fattrinfo")) continue;
                strSql += $" and u1.{flexItem}=u2.{flexItem}";
            }

            strSql += $@"
    union all 
    {sbStockInSubQuery.ToString()}
    union all 
    {sbStockOutSubQuery.ToString()}
) m1";
            //40847 -客户需求 排除 商品为 选配套件，允许选配
            strSql += $@" where exists (select 1 from T_BD_MATERIAL TMATERIAL where m1.fmaterialid= TMATERIAL.fid and TMATERIAL.fsuiteflag = 0  )
{(invFlexId.IsNullOrEmptyOrWhiteSpace() ? "" : $"and  m1.finvflexid={invFlexId}")}
order by {groupby},flinkbilldate,flinkbillinterid,flinkbillapprovedate
";


            List<SqlParam> lstParams = new List<SqlParam>()
            {
                new SqlParam("startDate", DbType.DateTime,dtStart)
            };
            sb.AppendLine("3:" + sw.ElapsedMilliseconds);
            var recordCount = this.DBServiceEx.Execute(this.Context, strSql, lstParams);
            sb.AppendLine("4:" + sw.ElapsedMilliseconds);

            GetPurPriceCalculateTotal();
            sb.AppendLine("5:" + sw.ElapsedMilliseconds);
            var loger = this.Context.Container.GetService<ILogService>();
            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【商品收发明细报表】，内容:{4}".Fmt(this.Context.UserName,
                    this.Context.UserPhone, this.Context.Company,
                 DateTime.Now.ToString("HH:mm:ss"), sb.ToString()),
                "stockDetail");
            string logName = "stockDetail/" + DateTime.Now.ToString("yyyyMMddHHmmss");
            LogHelper.WriteLog(logName, sb.ToString());

        }

        /// <summary>
        /// 获取采购单价，并统计总额
        /// </summary>
        private void GetPurPriceCalculateTotal()
        {
            StringBuilder sb = new StringBuilder();
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            sw.Restart();
            //关联获取【采购单价(折前)】
            List<string> lstSql = new List<string>();
            string priceSql = $@"/*dialect*/UPDATE A 
                        SET 
                            A.fpoprice = CASE 
                                WHEN A.flinkformid IN ('stk_postockin','stk_postockreturn') THEN B.fpoprice
                                WHEN A.flinkformid = 'stk_sostockout' THEN B.fpurfacprice
                                WHEN A.flinkformid = 'stk_inventoryverify' THEN B.fpdprice
                            END,
                            A.fpopriceafter = CASE 
                                WHEN A.flinkformid IN ('stk_postockin','stk_postockreturn') THEN B.fprice 
                                ELSE A.fpopriceafter 
                            END
                        FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                        INNER JOIN (
                            -- 合并所有业务表，附带表单类型标识和字段
                            SELECT 'stk_postockin' AS flinkformid, fentryid, fpoprice ,'0' fpurfacprice,'0' fpdprice, fprice
                            FROM T_STK_POSTOCKINENTRY WITH(NOLOCK)
                            UNION ALL
                            SELECT 'stk_postockreturn', fentryid, fpoprice ,'0' fpurfacprice,'0' fpdprice, fprice
                            FROM T_STK_POSTOCKRETURNENTRY WITH(NOLOCK)
                            UNION ALL
                            SELECT 'stk_sostockout', fentryid,'0' fpoprice, fpurfacprice,'0' fpdprice , NULL AS fprice
                            FROM T_STK_SOSTOCKOUTENTRY WITH(NOLOCK)
                            UNION ALL
                            SELECT 'stk_inventoryverify', fentryid,'0' fpoprice,'0' fpurfacprice, fpdprice , NULL AS fprice
                            FROM T_STK_INVVERIFYENTRY WITH(NOLOCK)
                        ) AS B 
                        ON A.flinkbillentryid = B.fentryid 
                        AND A.flinkformid = B.flinkformid
                        WHERE A.flinkformid IN (
                            'stk_postockin', 
                            'stk_postockreturn', 
                            'stk_sostockout', 
                            'stk_inventoryverify'
                        );";
            this.DBServiceEx.Execute(this.Context, priceSql);
            sb.AppendLine("4-1:" + sw.ElapsedMilliseconds);

            List<string> synSqls = new List<string>();
            //将【采购单价(折前)】为0的数据，同步综合价目表【采购单价(折前)】
            //匹配维度：商品+辅助属性+定制说明+基本单位
            synSqls.Add($@"/*dialect*/UPDATE A SET A.fpoprice=ISNULL(B.fpurfacprice,0) 
                                     FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                     LEFT JOIN T_YDJ_PRICESYNTHESIZE AS B WITH(NOLOCK) ON B.fmainorgid='{this.Context.Company}' AND A.fmaterialid=B.fmaterialid AND A.fattrinfo=B.fattrinfo AND A.fcustomdesc=B.fcustomdesc AND A.funitid=B.funitid 
                                     WHERE A.fpoprice=0;");
            //将【采购单价(折后)】为0的数据，同步综合价目表【采购单价(折后)】
            //匹配维度：商品+辅助属性+定制说明+基本单位
            synSqls.Add($@"/*dialect*/UPDATE A SET A.fpopriceafter=ISNULL(B.fpurdealprice,0) 
                                     FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                     LEFT JOIN T_YDJ_PRICESYNTHESIZE AS B WITH(NOLOCK) ON B.fmainorgid='{this.Context.Company}' AND A.fmaterialid=B.fmaterialid AND A.fattrinfo=B.fattrinfo AND A.fcustomdesc=B.fcustomdesc AND A.funitid=B.funitid 
                                     WHERE A.fpopriceafter=0;");
            this.DBServiceEx.ExecuteBatch(this.Context, synSqls);
            sb.AppendLine("4-2:" + sw.ElapsedMilliseconds);

            //计算：【收入总成本(折前)】【发出总成本(折前)】【收入总成本(折后)】【发出总成本(折后)】
            string calculateSql = $@"/*dialect*/UPDATE {this.DataSourceTableName} SET fstockintotal=fpoprice*fbizstockinqty,fstockouttotal=fpoprice*fbizstockoutqty,fstockintotalafter=fpopriceafter*fbizstockinqty,fstockouttotalafter=fpopriceafter*fbizstockoutqty WHERE 1=1 ";
            this.DBServiceEx.Execute(this.Context, calculateSql);
            sb.AppendLine("4-3:" + sw.ElapsedMilliseconds);

            List<string> lstCostSqls = new List<string>();
            //当【收入】>0 时，【收入总成本(加权平均)】直接取对应业务单据上的【总成本(加权平均)】
            lstCostSqls.Add($@"/*dialect*/UPDATE {this.DataSourceTableName} SET fincostamt=fcostamt WHERE fstockinqty>0;");
            //当【发出】>0 时，【发出总成本(加权平均)】直接取对应业务单据上的【总成本(加权平均)】
            lstCostSqls.Add($@"/*dialect*/UPDATE {this.DataSourceTableName} SET foutcostamt=fcostamt WHERE fstockoutqty>0;");
            this.DBServiceEx.ExecuteBatch(this.Context, lstCostSqls);
            sb.AppendLine("4-4:" + sw.ElapsedMilliseconds);
            var loger = this.Context.Container.GetService<ILogService>();
            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，下发销售合同】，内容:{4}".Fmt(this.Context.UserName,
                    this.Context.UserPhone, this.Context.Company,
                 DateTime.Now.ToString("HH:mm:ss"), sb.ToString()),
                "stockDetail");
            string logName = "stockDetail/" + DateTime.Now.ToString("yyyyMMddHHmmss");
            LogHelper.WriteLog(logName, sb.ToString());
        }

        /// <summary>
        /// 获取采购单价，并统计总额
        /// </summary>
        private void GetPurPriceCalculateTotal_Old()
        {
            StringBuilder sb = new StringBuilder();
            Stopwatch sw = Stopwatch.StartNew();
            sw.Start();
            //关联获取【采购单价(折前)】
            List<string> lstSql = new List<string>();
            //处理采购入库单【采购单价】
            lstSql.Add($@"/*dialect*/UPDATE A SET A.fpoprice=B.fpoprice,A.fpopriceafter=B.fprice 
                                FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                INNER JOIN T_STK_POSTOCKINENTRY AS B WITH(NOLOCK) ON A.flinkbillentryid = B.fentryid
                                WHERE A.flinkformid = 'stk_postockin';");
            //处理采购退货单【采购单价】
            lstSql.Add($@"/*dialect*/UPDATE A SET A.fpoprice=B.fpoprice,A.fpopriceafter=B.fprice 
                                FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                INNER JOIN T_STK_POSTOCKRETURNENTRY AS B WITH(NOLOCK) ON A.flinkbillentryid = B.fentryid
                                WHERE A.flinkformid = 'stk_postockreturn';");
            //处理销售出库单【采购单价(折前)】
            lstSql.Add($@"/*dialect*/UPDATE A SET A.fpoprice=B.fpurfacprice 
                                FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                INNER JOIN T_STK_SOSTOCKOUTENTRY AS B WITH(NOLOCK) ON A.flinkbillentryid = B.fentryid
                                WHERE A.flinkformid = 'stk_sostockout';");
            //处理盘点单【基本单位盘点单价】
            lstSql.Add($@"/*dialect*/UPDATE A SET A.fpoprice=B.fpdprice 
                                FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                INNER JOIN T_STK_INVVERIFYENTRY AS B WITH(NOLOCK) ON A.flinkbillentryid = B.fentryid
                                WHERE A.flinkformid = 'stk_inventoryverify';");
            this.DBServiceEx.ExecuteBatch(this.Context, lstSql);
            sb.AppendLine("4-1:" + sw.ElapsedMilliseconds);

            List<string> synSqls = new List<string>();
            //将【采购单价(折前)】为0的数据，同步综合价目表【采购单价(折前)】
            //匹配维度：商品+辅助属性+定制说明+基本单位
            synSqls.Add($@"/*dialect*/UPDATE A SET A.fpoprice=ISNULL(B.fpurfacprice,0) 
                                     FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                     LEFT JOIN T_YDJ_PRICESYNTHESIZE AS B WITH(NOLOCK) ON B.fmainorgid='{this.Context.Company}' AND A.fmaterialid=B.fmaterialid AND A.fattrinfo=B.fattrinfo AND A.fcustomdesc=B.fcustomdesc AND A.funitid=B.funitid 
                                     WHERE A.fpoprice=0;");
            //将【采购单价(折后)】为0的数据，同步综合价目表【采购单价(折后)】
            //匹配维度：商品+辅助属性+定制说明+基本单位
            synSqls.Add($@"/*dialect*/UPDATE A SET A.fpopriceafter=ISNULL(B.fpurdealprice,0) 
                                     FROM {this.DataSourceTableName} AS A WITH(NOLOCK)
                                     LEFT JOIN T_YDJ_PRICESYNTHESIZE AS B WITH(NOLOCK) ON B.fmainorgid='{this.Context.Company}' AND A.fmaterialid=B.fmaterialid AND A.fattrinfo=B.fattrinfo AND A.fcustomdesc=B.fcustomdesc AND A.funitid=B.funitid 
                                     WHERE A.fpopriceafter=0;");
            this.DBServiceEx.ExecuteBatch(this.Context, synSqls);
            sb.AppendLine("4-2:" + sw.ElapsedMilliseconds);

            //计算：【收入总成本(折前)】【发出总成本(折前)】【收入总成本(折后)】【发出总成本(折后)】
            string calculateSql = $@"/*dialect*/UPDATE {this.DataSourceTableName} SET fstockintotal=fpoprice*fbizstockinqty,fstockouttotal=fpoprice*fbizstockoutqty,fstockintotalafter=fpopriceafter*fbizstockinqty,fstockouttotalafter=fpopriceafter*fbizstockoutqty WHERE 1=1 ";
            this.DBServiceEx.Execute(this.Context, calculateSql);
            sb.AppendLine("4-3:" + sw.ElapsedMilliseconds);

            List<string> lstCostSqls = new List<string>();
            //当【收入】>0 时，【收入总成本(加权平均)】直接取对应业务单据上的【总成本(加权平均)】
            lstCostSqls.Add($@"/*dialect*/UPDATE {this.DataSourceTableName} SET fincostamt=fcostamt WHERE fstockinqty>0;");
            //当【发出】>0 时，【发出总成本(加权平均)】直接取对应业务单据上的【总成本(加权平均)】
            lstCostSqls.Add($@"/*dialect*/UPDATE {this.DataSourceTableName} SET foutcostamt=fcostamt WHERE fstockoutqty>0;");
            this.DBServiceEx.ExecuteBatch(this.Context, lstCostSqls);
            sb.AppendLine("4-4:" + sw.ElapsedMilliseconds);
            var loger = this.Context.Container.GetService<ILogService>();
            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，下发销售合同】，内容:{4}".Fmt(this.Context.UserName,
                    this.Context.UserPhone, this.Context.Company,
                 DateTime.Now.ToString("HH:mm:ss"), sb.ToString()),
                "stockDetail");
            string logName = "stockDetail/" + DateTime.Now.ToString("yyyyMMddHHmmss");
            LogHelper.WriteLog(logName, sb.ToString());
        }

        /// <summary>
        /// 关联获取并更新临时表【总成本(加权平均)】
        /// </summary>
        private void LinkUpdateDetailCostAMT_Old()
        {
            List<string> lstCostSqls = new List<string>();
            foreach (var stockFormId in InventoryFlexModel.AllStockBillFormIds)
            {
                string entryTableName = "";
                switch (stockFormId)
                {
                    case "stk_postockin":
                        entryTableName = "t_stk_postockinentry";
                        break;
                    case "stk_postockreturn":
                        entryTableName = "t_stk_postockreturnentry";
                        break;
                    case "stk_sostockout":
                        entryTableName = "t_stk_sostockoutentry";
                        break;
                    case "stk_sostockreturn":
                        entryTableName = "t_stk_sostockreturnentry";
                        break;
                    case "stk_otherstockin":
                        entryTableName = "t_stk_otherstockinentry";
                        break;
                    case "stk_otherstockout":
                        entryTableName = "t_stk_otherstockoutentry";
                        break;
                    case "stk_inventorytransfer":
                        entryTableName = "t_stk_invtransferentry";
                        break;
                    case "stk_inventoryverify":
                        entryTableName = "t_stk_invverifyentry";
                        break;
                    default:
                        break;
                }
                if (!entryTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    lstCostSqls.Add($@"/*dialect*/UPDATE A SET A.fcostamt=B.fcostamt 
                                       FROM {this.DetailLogTableName} AS A
                                       INNER JOIN {entryTableName} AS B WITH(NOLOCK) ON A.fsourceinterid=B.fid AND A.fsourceentryid=B.fentryid 
                                       WHERE A.fsourceformid='{stockFormId}' ;");
                }
            }
            if (lstCostSqls.Any())
            {
                this.DBServiceEx.ExecuteBatch(this.Context, lstCostSqls);
            }
        }



        /// <summary>
        /// 关联获取并更新临时表【总成本(加权平均)】
        /// 优化：
        /// </summary>
        private void LinkUpdateDetailCostAMT()
        {
            List<string> formIds = new List<string>();
            List<string> lstCostSqls = new List<string>();
            foreach (var stockFormId in InventoryFlexModel.AllStockBillFormIds)
            {
                string entryTableName = "";
                switch (stockFormId)
                {
                    case "stk_postockin":
                        entryTableName = "t_stk_postockinentry";
                        break;
                    case "stk_postockreturn":
                        entryTableName = "t_stk_postockreturnentry";
                        break;
                    case "stk_sostockout":
                        entryTableName = "t_stk_sostockoutentry";
                        break;
                    case "stk_sostockreturn":
                        entryTableName = "t_stk_sostockreturnentry";
                        break;
                    case "stk_otherstockin":
                        entryTableName = "t_stk_otherstockinentry";
                        break;
                    case "stk_otherstockout":
                        entryTableName = "t_stk_otherstockoutentry";
                        break;
                    case "stk_inventorytransfer":
                        entryTableName = "t_stk_invtransferentry";
                        break;
                    case "stk_inventoryverify":
                        entryTableName = "t_stk_invverifyentry";
                        break;
                    default:
                        break;
                }
                if (!entryTableName.IsNullOrEmptyOrWhiteSpace())
                {
                    formIds.Add(stockFormId);
                    lstCostSqls.Add($@"SELECT '{stockFormId}' AS fsourceformid, fid, fentryid, fcostamt FROM {entryTableName} WITH(NOLOCK)");
                    //lstCostSqls.Add($@"/*dialect*/UPDATE A SET A.fcostamt=B.fcostamt 
                    //                   FROM {this.DetailLogTableName} AS A
                    //                   INNER JOIN {entryTableName} AS B WITH(NOLOCK) ON A.fsourceinterid=B.fid AND A.fsourceentryid=B.fentryid 
                    //                   WHERE A.fsourceformid='{stockFormId}' ;");
                }
            }
            if (lstCostSqls.Any())
            {
                // 构建检查索引是否存在的 SQL 语句
                string checkIndexSql = $@"/*dialect*/
                IF NOT EXISTS (
                    SELECT 1 
                    FROM sys.indexes i
                    JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
                    WHERE i.object_id = OBJECT_ID('{this.DetailLogTableName}')
                    AND ic.column_id IN (
                        SELECT c.column_id 
                        FROM sys.columns c 
                        WHERE c.object_id = OBJECT_ID('{this.DetailLogTableName}') 
                        AND c.name IN ('fsourceformid', 'fsourceinterid', 'fsourceentryid')
                    )
                    GROUP BY i.object_id, i.index_id
                    HAVING COUNT(DISTINCT ic.column_id) = 3
                )
                BEGIN
                    -- 构建创建索引的 SQL 语句
                    DECLARE @createIndexSql NVARCHAR(MAX);
                    SET @createIndexSql = 'CREATE INDEX IX_{this.DetailLogTableName}_fsourceformid_fsourceinterid_fsourceentryid ON {this.DetailLogTableName} (fsourceformid, fsourceinterid, fsourceentryid)';
                    EXEC sp_executesql @createIndexSql;
                END";

                // 执行检查并创建索引的 SQL 语句
                this.DBServiceEx.Execute(this.Context, checkIndexSql);

                string sql = $@"/*dialect*/UPDATE A
                SET A.fcostamt = B.fcostamt
                FROM {this.DetailLogTableName} AS A
                INNER JOIN ({string.Join(" UNION ALL  ", lstCostSqls)}
                ) AS B 
                ON A.fsourceformid = B.fsourceformid 
                AND A.fsourceinterid = B.fid 
                AND A.fsourceentryid = B.fentryid
                WHERE A.fsourceformid IN ('{string.Join("','", formIds)}');";
                this.DBServiceEx.Execute(this.Context, sql);
            }
        }
    }
}
