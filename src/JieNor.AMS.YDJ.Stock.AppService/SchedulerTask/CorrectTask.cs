//using System;
//using System.Collections.Generic;
//using System.ComponentModel;
//using System.Linq;
//using System.Threading.Tasks;
//using JieNor.Framework;
//using JieNor.Framework.IoC;
//using JieNor.Framework.Meta.Designer;
//using JieNor.Framework.Interface.BizTask;
//using JieNor.AMS.YDJ.Core.Interface.StockInit;

//namespace JieNor.AMS.YDJ.Stock.AppService.SchedulerTask
//{
//    /// <summary>
//    /// 即时库存校正任务
//    /// </summary>
//    //[InjectService(AliasName = "Task")]
//    [InjectService]
//    [TaskSvrId("correcttask")]
//    [Caption("即时库存校正")]
//    [TaskMultiInstance()]
//    [Browsable(false)]
//    public class CorrectTask : AbstractScheduleWorker
//    {
//        /// <summary>
//        /// 执行任务逻辑
//        /// </summary>
//        /// <returns></returns>
//        protected override async Task DoExecute()
//        {
//            var userCtx = this.UserContext;
//            var stockService = userCtx.Container.GetService<IStockBaseService>();

//            await Task.Run(() =>
//            {
//                stockService?.CorrectInventoryData(userCtx, this.Option);
//            });
//        }
//    }
//}