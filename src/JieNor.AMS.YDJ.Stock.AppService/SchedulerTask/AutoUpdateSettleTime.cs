using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Store.AppService.Utils;
using System.Globalization;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Stock.AppService.SchedulerTask
{
    /// <summary>
    /// 运维问题：计划任务定时从禅道获取自动更新运维问题的开发时间
    /// </summary>
    [InjectService]
    [TaskSvrId("autoupdatesettletime")]
    [Caption("自动更新运维问题开发时间")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class AutoUpdateSettleTime : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        {

            //获取未关闭 or 开发实际解决时间<提交给开发时间 or 开发预计解决时间为空 or 开发实际解决时间为空 的运维问题单据
            var odjs = this.UserContext.LoadBizDataByFilter("ydj_maintenance", " fquestionprogress!='q_progress_07' or factsettletime<fsubmitdeveloptime or festsettletime='' or factsettletime=''");
            List<DynamicObject> modifyodjs = new List<DynamicObject>(); //有变动的单据


            string ids = "";
            var zenpathnumber = string.Empty;
            foreach (var item in odjs)
            {
                zenpathnumber = $@"'{ Convert.ToString(item["fzenpathnumber"]).Trim()}',";
                ids = ids + zenpathnumber;
            }

            //去除最后一个逗号
            int lastCommaIndex = ids.LastIndexOf(",");
            if (lastCommaIndex >= 0)
            {
                ids = ids.Remove(lastCommaIndex, 1);
            }

            string mysql = string.Empty;
            var project = this.GetAppConfig("ms.zt.project");   //【所属项目】
            if (project.IsNullOrEmptyOrWhiteSpace())
            {
                this.WriteLog("更新失败！未配置运维问题所属禅道项目！请检查 host.config 配置 ms.zt.project！");
                return;
            }
            var dataBase = this.GetAppConfig("ms.zt.database"); //所属数据库
            if (dataBase.IsNullOrEmptyOrWhiteSpace())
            {
                this.WriteLog("更新失败！未配置运维问题所属禅道数据库！请检查 host.config 配置 ms.zt.database！");
                return;
            }

            //union 兼顾禅道上操作激活已关闭的任务(相关代码已注释，目前不考虑该场景)
            if (!ids.IsNullOrEmptyOrWhiteSpace())
            {
                mysql = $@"SELECT id, openedDate, deadline, finishedDate, status, closedDate, lastEditedDate,assignedTo
                                    FROM {dataBase}.zt_task
                                    WHERE id IN ({ids}) and project=(select id from {dataBase}.zt_project where name='{project}' and type = 'project' and deleted='0' order by openedDate desc limit 1) and deleted='0'";
                //mysql = $@"SELECT id, openedDate, deadline, finishedDate, status, closedDate, lastEditedDate,assignedTo
                //                    FROM {dataBase}.zt_task
                //                    WHERE id IN ({ids}) and project=(select id from {dataBase}.zt_project where name='{project}' and deleted='0' order by openedDate desc limit 1) and deleted='0'
                //                    UNION 
                //                    select * from
                //                    (SELECT id, openedDate, deadline, finishedDate, status, closedDate, lastEditedDate,assignedTo
                //                    FROM {dataBase}.zt_task t1
                //                    WHERE id NOT IN ({ids}) and project=(select id from {dataBase}.zt_project where name='{project}' and deleted='0' order by openedDate desc limit 1) and status!='closed' and deleted='0'
                //                    ORDER BY RAND()
                //                    LIMIT 200) as t";
            }
            else
            {
                //mysql = $@"SELECT id, openedDate, deadline, finishedDate, status, closedDate, lastEditedDate,assignedTo
                //                    FROM {dataBase}.zt_task t1
                //                    WHERE project=(select id from {dataBase}.zt_project where name='{project}' and deleted='0' order by openedDate desc limit 1) and status!='closed' and deleted='0'
                //                    ORDER BY RAND()
                //                    LIMIT 200";

            }


            //获取禅道对应运维任务的【截止时间】、【完成时间】等相关信息
            var data = new List<DataModel>();
            if (!mysql.IsNullOrEmptyOrWhiteSpace())
            {
                var init = MySqlHelper.Instance;
                data = MySqlHelper.ExecuteBatchQuery(mysql);
            }
               

            //记录从禅道抽取的金蝶已关闭的运维问题对应的禅道号
            //string cids = string.Empty;

            //更新运维问题（操作金蝶未关闭单据）
            this.WriteLog("批量开始更新金蝶未关闭单据...");
            foreach (var item in odjs)
            {
                foreach (var itemm in data)
                {
                    if (Convert.ToString(item["fzenpathnumber"]).Trim().Equals(itemm.id))
                    {
                        this.WriteLog("开始更新单据 ： 单据编号：{0}，禅道号：{1}，开发人员：{2}，开发预计解决时间：{3}，开发实际解决时间：{4} ".Fmt(Convert.ToString(item["fbillno"]).Trim(),
                                                                                                                                                     Convert.ToString(item["fzenpathnumber"]).Trim(),
                                                                                                                                                     Convert.ToString(item["fdeveloper"]).Trim(),
                                                                                                                                                     Convert.ToString(item["festsettletime"]).Trim(),
                                                                                                                                                     Convert.ToString(item["factsettletime"]).Trim()));
                        var dt = DateTime.Now;
                        var fquestionprogress = item["fquestionprogress"].ToString();

                        if (!itemm.deadLine.IsNullOrEmptyOrWhiteSpace())
                            item["festsettletime"] = itemm.deadLine;      //开发预计解决时间

                        //开发实际解决时间
                        if (!itemm.finishedDate.IsNullOrEmptyOrWhiteSpace() && item["factsettletime"].IsNullOrEmptyOrWhiteSpace())
                        {
                            if (fquestionprogress.Equals("q_progress_04"))
                                item["fquestionprogress"] = "q_progress_05";

                            item["factsettletime"] = itemm.finishedDate;
                        }
                        if (!itemm.finishedDate.IsNullOrEmptyOrWhiteSpace() && !item["factsettletime"].IsNullOrEmptyOrWhiteSpace())
                        {
                            var factsettletime = ((DateTime)item["factsettletime"]).ToString("yyyy-MM-dd HH:mm:ss");
                            DateTime dtime1 = dt;
                            DateTime dtime2 = dt;
                            DateTime.TryParseExact(factsettletime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dtime1);
                            DateTime.TryParseExact(itemm.finishedDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dtime2);
                            if (dtime2 > dtime1)
                            {
                                if (fquestionprogress.Equals("q_progress_04"))
                                    item["fquestionprogress"] = "q_progress_05";

                                item["factsettletime"] = itemm.finishedDate;
                            }

                        }


                        //问题由转开发->解决->发版->关闭（金蝶系统主动关闭，禅道不允许关闭）
                        //if (!Convert.ToString(item["fquestionprogress"]).Trim().Equals("q_progress_07") && itemm.status.Equals("closed") && !itemm.closedDate.IsNullOrEmptyOrWhiteSpace())
                        //{
                        //    DateTime dt1 = dt;
                        //    DateTime dt2 = dt;

                        //    DateTime.TryParseExact(itemm.closedDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dt1);

                        //    if (!item["fquestionclosetime"].IsNullOrEmptyOrWhiteSpace())
                        //    {
                        //        var fquestionclosetime = ((DateTime)item["fquestionclosetime"]).ToString("yyyy-MM-dd HH:mm:ss");
                        //        DateTime.TryParseExact(fquestionclosetime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dt2);
                        //        if (!dt1.IsNullOrEmptyOrWhiteSpace() && !dt2.IsNullOrEmptyOrWhiteSpace() && dt1 > dt2)
                        //        {
                        //            item["fquestionprogress"] = "q_progress_07";
                        //            item["fquestionclosetime"] = itemm.closedDate; //问题关闭时间
                        //        }
                        //    }
                        //    else
                        //    {
                        //        item["fquestionprogress"] = "q_progress_07";
                        //        item["fquestionclosetime"] = itemm.closedDate; //问题关闭时间
                        //    }
                        //}

                        if (!Convert.ToString(item["fdeveloper"]).Trim().Equals(itemm.assignedTo.Trim()) && (itemm.status.Equals("doing") || itemm.status.Equals("wait")))
                        {
                            //获取《开发人员》辅助资料
                            string strSql1 = "select fentryid,fenumitem from t_bd_enumdataentry with(nolock) where fid='1065220001303232512'";
                            var dpdatas = this.UserContext.ExecuteDynamicObject(strSql1, new List<SqlParam>() { });

                            //防止《开发人员》辅助资料fid变了
                            if (dpdatas.IsNullOrEmptyOrWhiteSpace() || dpdatas.Count<1)
                            {
                                strSql1 = $@"select t0.fentryid,t0.fenumitem
                                             from t_bd_enumdataentry t0 
                                             inner join t_bd_enumdata t1 on t0.fid=t1.fid
                                             where t1.fname='开发人员'";

                                dpdatas = this.UserContext.ExecuteDynamicObject(strSql1, new List<SqlParam>() { });
                            }
                            

                            //匹配开发人员(只有禅道上指派的开发人员与金蝶系统预置的辅助资料匹配上才更新开发人员，否则不更新)
                            foreach (var item1 in dpdatas)
                            {
                                if (Convert.ToString(item1["fentryid"]).Trim().Equals(itemm.assignedTo.Trim()))
                                {
                                    item["fdeveloper"] = itemm.assignedTo.Trim();  //开发人员
                                    break;
                                }
                            }
                        }

                        modifyodjs.Add(item);
                        this.WriteLog("更新单据结束 ： 单据编号：{0}，禅道号：{1}，开发人员：{2}，开发预计解决时间：{3}，开发实际解决时间：{4} ".Fmt(Convert.ToString(item["fbillno"]).Trim(),
                                                                                                                                                     Convert.ToString(item["fzenpathnumber"]).Trim(),
                                                                                                                                                     Convert.ToString(item["fdeveloper"]).Trim(),
                                                                                                                                                     Convert.ToString(item["festsettletime"]).Trim(),
                                                                                                                                                     Convert.ToString(item["factsettletime"]).Trim()));
                    }
                    else
                    {
                        //string ztnumber = $@"'{itemm.id}',";
                        //cids = cids + ztnumber;
                    }
                }
            }

            var htmlForm = this.UserContext.Container.TryGetService<IMetaModelService>()?.LoadFormModel(this.UserContext, "ydj_maintenance");
            //操作数据库
            var dmService = this.UserContext.Container.GetService<IDataManager>();
            //初始化上下文
            dmService.InitDbContext(this.UserContext, htmlForm.GetDynamicObjectType(this.UserContext));
            // 直接保存数据库
            if (!modifyodjs.IsNullOrEmptyOrWhiteSpace() || modifyodjs.Count > 0)
                dmService.Save(modifyodjs);

            this.WriteLog("批量更新金蝶未关闭单据结束...");


            //if (data.Count > 0 && ids.IsNullOrEmptyOrWhiteSpace())
            //{
            //    foreach (var itemm in data)
            //    {
            //        string ztnumber = $@"'{itemm.id}',";
            //        cids = cids + ztnumber;

            //    }
            //}

            ////去除最后一个逗号
            //int lastIndex = cids.LastIndexOf(",");
            //if (lastIndex >= 0)
            //{
            //    cids = cids.Remove(lastIndex, 1);
            //}

            //this.WriteLog("批量开始更新金蝶已关闭单据（禅道激活）...");
            //List<DynamicObject> modifycodjs = new List<DynamicObject>(); //有变动的单据
            //if (!cids.IsNullOrEmptyOrWhiteSpace())
            //{
            //    //获取单据
            //    var codjs = this.UserContext.LoadBizDataByFilter("ydj_maintenance", $@" fquestionprogress='q_progress_07' and fzenpathnumber in({cids}) ");
            //    //更新运维问题（操作金蝶已关闭单据）
            //    if (!codjs.IsNullOrEmptyOrWhiteSpace() || codjs.Count > 0)
            //    {
            //        foreach (var item in codjs)
            //        {
            //            foreach (var itemm in data)
            //            {
            //                if (Convert.ToString(item["fzenpathnumber"]).Trim().Equals(itemm.id))
            //                {
            //                    this.WriteLog("开始更新单据 ： 单据编号：{0}，禅道号：{1} ".Fmt(Convert.ToString(item["fbillno"]).Trim(), Convert.ToString(item["fzenpathnumber"]).Trim()));

            //                    if (!itemm.deadLine.IsNullOrEmptyOrWhiteSpace())
            //                        item["festsettletime"] = itemm.deadLine;      //开发预计解决时间
            //                    if (!itemm.finishedDate.IsNullOrEmptyOrWhiteSpace())
            //                        item["factsettletime"] = itemm.finishedDate;  //开发实际解决时间

            //                    var dt = DateTime.Now;

            //                    if (Convert.ToString(item["fquestionprogress"]).Trim().Equals("q_progress_07") && !itemm.status.Equals("closed") && !itemm.lastEditedDate.IsNullOrEmptyOrWhiteSpace())
            //                    {
            //                        //激活运维问题

            //                        DateTime dt1 = dt;
            //                        DateTime dt2 = dt;

            //                        DateTime.TryParseExact(itemm.lastEditedDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dt1);

            //                        if (!item["fquestionclosetime"].IsNullOrEmptyOrWhiteSpace())
            //                        {
            //                            var fquestionclosetime = ((DateTime)item["fquestionclosetime"]).ToString("yyyy-MM-dd HH:mm:ss");
            //                            DateTime.TryParseExact(fquestionclosetime, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dt2);
            //                            if (!dt1.IsNullOrEmptyOrWhiteSpace() && !dt2.IsNullOrEmptyOrWhiteSpace() && dt1 > dt2)
            //                            {
            //                                item["fquestionprogress"] = "q_progress_01";
            //                                item["fquestionclosetime"] = null; //问题关闭时间
            //                                var activecounts = Convert.ToInt32(item["factivecounts"]);
            //                                item["factivecounts"] = activecounts + 1;
            //                            }
            //                        }
            //                    }
            //                    modifycodjs.Add(item);
            //                    this.WriteLog("更新单据结束 ： 单据编号：{0}，禅道号：{1} ".Fmt(Convert.ToString(item["fbillno"]).Trim(), Convert.ToString(item["fzenpathnumber"]).Trim()));
            //                }
            //            }
            //        }
            //    }

            //    // 直接保存数据库
            //    if (!modifycodjs.IsNullOrEmptyOrWhiteSpace() || modifycodjs.Count > 0)
            //        dmService.Save(modifycodjs);
            //}
            //this.WriteLog("批量更新金蝶已关闭单据结束（禅道激活）...");
            //this.WriteLog("计划任务批量自动更新运维问题的开发时间成功! {0} ： {1}/{2} ".Fmt("更新任务数量/本次涉及任务数量", modifyodjs.Count + modifycodjs.Count, data.Count));
            this.WriteLog("计划任务批量自动更新运维问题的开发时间成功! {0} ： {1}/{2} ".Fmt("更新任务数量/本次涉及任务数量", modifyodjs.Count , data.Count));
        }
    }
}
