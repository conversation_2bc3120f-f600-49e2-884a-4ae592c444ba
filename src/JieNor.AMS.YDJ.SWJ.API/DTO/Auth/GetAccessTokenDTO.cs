using ServiceStack;

namespace JieNor.AMS.YDJ.SWJ.API.DTO.Auth
{
    /// <summary>
    /// 获取令牌接口
    /// </summary>
    [Api("获取令牌接口")]
    [Route("/swjapi/getAccessToken")]
    public class GetAccessTokenDTO : BaseDTO
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string client_id { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string client_secret { get; set; }
    }
}