using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.SWJ.API.DTO
{
    /// <summary>
    /// 辅助属性项
    /// </summary>
    public class AuxPropValDTO : AuxPropValEntity
    {
        /// <summary>
        /// 属性编码
        /// </summary>
        public string PropNo { get; set; }
        /// <summary>
        /// 属性值编码
        /// </summary>
        public string PropValueNo { get; set; }
        /// <summary>
        /// 属性值名称
        /// </summary>
        public string PropValueName { get; set; }

        public bool IsValid()
        {
            return !PropNo.IsNullOrEmptyOrWhiteSpace() &&
                   (!PropValueNo.IsNullOrEmptyOrWhiteSpace() || !PropValueName.IsNullOrEmptyOrWhiteSpace());
        }
    }

    public class AuxPropValEntity
    {
        internal string PropId { get; set; }

        /// <summary>
        /// 数据类型（'1':'字符','2':'数值'）
        /// </summary>
        internal string DataType { get; set; }

        /// <summary>
        /// 支持非标库
        /// </summary>
        internal bool AllowNoSuitlib { get; set; }

        /// <summary>
        /// 支持非标录入
        /// </summary>
        internal bool AllowCustom { get; set; }

        internal string ValueId { get; set; }

        internal string ValueNumber { get; set; }

        internal string ValueName { get; set; }
    }
}