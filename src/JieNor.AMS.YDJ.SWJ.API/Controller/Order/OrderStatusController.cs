using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.SWJ.API.APIs;
using JieNor.AMS.YDJ.SWJ.API.DTO;
using JieNor.AMS.YDJ.SWJ.API.DTO.Demo;
using JieNor.AMS.YDJ.SWJ.API.DTO.Order;
using JieNor.AMS.YDJ.SWJ.API.Plugin;
using JieNor.AMS.YDJ.SWJ.API.Response;
using JieNor.AMS.YDJ.SWJ.API.Response.SWJ;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.SWJ.API.Controller.Order
{
    /// <summary>
    /// 订单状态接口
    /// </summary>
    public class OrderStatusController : BaseController
    {
        /// <summary>
        /// 客户表单模型
        /// </summary>
        protected HtmlForm CustomerForm { get; set; }
        /// <summary>
        /// 物料表单模型
        /// </summary>
        protected HtmlForm MaterialForm { get; set; }
        /// <summary>
        /// 销售员单模型
        /// </summary>
        protected HtmlForm StaffForm { get; set; }

        protected ILogService loger { get; set; }
        protected UserContext agentCtx { get; set; }

        /// <summary>
        /// 三维家下发单据类型
        /// </summary>
        protected string orderType { get; set; }

        protected object Result { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderStatusDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<object>();
            var dm = this.GetDataManager();
            // todo：根据操作类型进行业务操作
            string orderCode = "", contractNo = "";
            Dictionary<string, object> _urlparam = new Dictionary<string, object>();

            loger = this.Context.Container.GetService<ILogService>();
            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，下发销售合同】，内容:{4}".Fmt(this.Context.UserName,
                    this.Context.UserPhone, this.Context.Company,
                 DateTime.Now.ToString("HH:mm:ss"), JsonConvert.SerializeObject(dto)),
                "SWJLogFile");

            switch (dto.operateType)
            {
                case "AIMES_CREATE_ORDER":

                    //获取三维家下发单据类型
                    orderType = GetSWJOrderType(dto, resp, out _urlparam);
                    if (!resp.Success)
                        return resp;
                    bool billnoIsNotNull = (_urlparam.ContainsKey("billno") && !string.IsNullOrWhiteSpace(Convert.ToString(_urlparam["billno"])));
                    bool idIsNotNull = (_urlparam.ContainsKey("id") && !string.IsNullOrWhiteSpace(Convert.ToString(_urlparam["id"])));

                    if (billnoIsNotNull || idIsNotNull)
                    {
                        string billno = Convert.ToString(_urlparam["billno"]);
                        object entryid = null;
                        object id = null;
                        _urlparam.TryGetValue("entryid", out entryid);
                        _urlparam.TryGetValue("id", out id);
                        bool isService = !string.IsNullOrWhiteSpace(Convert.ToString(entryid));
                        //销售合同过来的数据，这时候销售合同是存在的，只需要创建最新的明细数据进去即可。
                        List<string> subOrderIds = dto.extParam.subOrderIds;
                        List<DynamicObject> _entrys = new List<DynamicObject>();
                        DynamicObject orderObj = null;
                        List<ShopOrderInfoResult> orderList = new List<ShopOrderInfoResult>();
                        foreach (var item in subOrderIds)
                        {
                            // todo：获取3维家合同信息
                            var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, item);
                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取已有合同数据--Step1】，内容:{4}".Fmt(this.Context.UserName,
                                    this.Context.UserPhone, this.Context.Company,
                                 DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                "SWJLogFile");
                            if (orderInfo.success)
                            {
                                var dataModel = orderInfo.result;
                                Dictionary<string, object> urlParam = new Dictionary<string, object>();
                                Validation(resp, dataModel, out urlParam, out contractNo, out string BMID);
                                if (!resp.Success)
                                    return resp;
                                orderList.Add(dataModel);
                            }
                        }
                        if (orderList.Count == 0)
                            return resp;

                        if (billnoIsNotNull)
                        {
                            orderObj = agentCtx.LoadBizDataByNo("ydj_order", "fbillno", new List<string> { billno }).FirstOrDefault();
                        }
                        if (idIsNotNull)
                        {
                            orderObj = agentCtx.LoadBizDataById("ydj_order", Convert.ToString(id));
                        }
                        if (orderObj == null)
                        {
                            return this.OrderResult(resp, dto, $"未查询到销售合同，销售合同编号：{billno}！", dto.extParam.subOrderIds, false);
                        }
                        if (Convert.ToBoolean(orderObj["fcancelstatus"]))
                            return this.OrderResult(resp, dto, $"当前订单{billno}已整单作废，不允许新增商品行，请另行下单处理！", dto.extParam.subOrderIds, false);
                        orderObj["fomsservice"] = true;//启用定制OMS
                        if (!string.IsNullOrWhiteSpace(Convert.ToString(orderObj["fswjordernumber"])))
                        {
                            List<string> swjNumbers = Convert.ToString(orderObj["fswjordernumber"]).Split(',').ToList();
                            swjNumbers.Add(orderList[0].shopOrderCode);
                            List<string> swjdetailurls = Convert.ToString(orderObj["fswjdetailurl"]).Split(',').ToList();
                            swjdetailurls.Add(SWJClient.Instance.DetailUri + "?orderId=" + dto.subjectId);
                            //【三维家单号】为“销售订单号”
                            orderObj["fswjordernumber"] = string.Join(",", swjNumbers.Distinct().Where(a => !a.IsNullOrEmptyOrWhiteSpace()));
                            orderObj["fswjdetailurl"] = string.Join(",", swjdetailurls.Distinct().Where(a => !a.IsNullOrEmptyOrWhiteSpace()));

                        }
                        else
                        {
                            //【三维家单号】为“销售订单号”
                            orderObj["fswjordernumber"] = orderList[0].shopOrderCode;
                            orderObj["fswjdetailurl"] = SWJClient.Instance.DetailUri + "?orderId=" + dto.subjectId;
                        }
                        var objDetail = orderObj["fentry"] as DynamicObjectCollection;
                        foreach (var dataModel in orderList)
                        {
                            bool isAdd = false;
                            DynamicObject detailItem = null;
                            if (!isService)//是否售后服务
                            {
                                detailItem = objDetail.Where(a => Convert.ToString(a["ffactorybillid"]).Equals(dataModel.platformOrderId)).FirstOrDefault();
                                if (detailItem == null)
                                {
                                    isAdd = true;
                                    detailItem = objDetail.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                }
                            }
                            else
                            {
                                detailItem = objDetail.Where(a => Convert.ToString(a["id"]).Equals(entryid)).FirstOrDefault();
                            }
                            if (Convert.ToBoolean(detailItem["fissubmitoms"]))
                            {
                                return this.OrderResult(resp, dto, $"已有订单提交至 OMS，不允许更新!", dto.extParam.subOrderIds, false);
                            }
                            var material = dataModel.productCode;
                            DynamicObject materialObj = null;
                            var materialObjs = agentCtx.ExecuteDynamicObject("select top 1  fid,funitid,fsalunitid,fseriesid from T_BD_MATERIAL with(nolock) where fnumber=@fnumber order by fmainorgid", new List<SqlParam> { new SqlParam("fnumber", DbType.String, material) });
                            if (materialObjs.Count > 0)
                            {
                                materialObj = materialObjs[0];
                            }
                            else
                            {
                                return this.OrderResult(resp, dto, contractNo, dto.extParam.subOrderIds, false);
                            }
                            detailItem["fproductid"] = materialObj["fid"];

                            detailItem["fcustomdes_e"] = string.IsNullOrEmpty(dataModel.platformOrderCode) ? dataModel.shopOrderCode : dataModel.platformOrderCode;//工厂订单号
                            detailItem["ffactorybillid"] = dataModel.platformOrderId;//工厂订单ID
                            detailItem["ffactorybillno"] = string.IsNullOrEmpty(dataModel.platformOrderCode) ? dataModel.shopOrderCode : dataModel.platformOrderCode;//工厂订单号
                            detailItem["funitid"] = materialObj["funitid"];//单位
                            detailItem["fbizunitid"] = materialObj["fsalunitid"];//销售单位
                            detailItem["fresultbrandid"] = materialObj["fseriesid"];//业绩品牌
                            detailItem["fomsprogress"] = "5";//定制订单进度 默认 待提交
                            detailItem["fstockstatus"] = "311858936800219137";//库存状态 默认 可用
                            detailItem["fisomscustomer"] = "1";//启用OMS定制说明
                            detailItem["fswjordernumber_e"] = dataModel.shopOrderCode;
                            detailItem["fswjdetailurl_e"] = SWJClient.Instance.DetailUri + "?orderId=" + dto.subjectId;

                            List<AuxPropValDTO> newPropList = new List<AuxPropValDTO>();
                            var sel_cats = this.Context.LoadBizDataByNo("sel_category", "fnumber", new List<string>() { "VFZ1_M001" }).FirstOrDefault();
                            if (sel_cats != null)
                            {
                                var selentrys = sel_cats["fentity"] as DynamicObjectCollection;
                                foreach (var sel_propobj in selentrys.Where(a => Convert.ToInt32(a["fiswhetherinforce"]) == 1))
                                {
                                    var propItem = this.Context.LoadBizDataById("sel_prop", Convert.ToString(sel_propobj["fpropid"]));
                                    var propValueItem = this.Context.LoadBizDataById("sel_propvalue", Convert.ToString(sel_propobj["fdefaultpropvalueid"]));
                                    newPropList.Add(new AuxPropValDTO
                                    {
                                        PropNo = Convert.ToString(propItem["fnumber"]),
                                        PropValueNo = Convert.ToString(propValueItem["fname"]),
                                        PropValueName = Convert.ToString(propValueItem["fname"])
                                    });
                                }
                            }
                            detailItem["fattrinfo_ref"] = ProductUtil.ConvertAuxPropFieldValue(this.Context, Convert.ToString(materialObj["fid"]), newPropList);
                            detailItem["fdistrate"] = "10";//折扣
                            detailItem["fdistamount"] = "0";//折扣额

                            decimal price = 0;
                            // Convert.ToString(dataModel.orderTypeDictVal) == "售后单" ? dataModel.productTotalPrice : dataModel.saleTotalPrice;
                            if (Convert.ToString(dataModel.orderTypeDictVal).Equals("售后单"))
                            {
                                if (dataModel.productTotalPrice > 0)
                                {
                                    price = dataModel.productTotalPrice * 3;
                                }
                                else price = 1;
                            }
                            else
                            {
                                price = dataModel.saleTotalPrice;
                                if (price == 0)
                                {
                                    return this.OrderResult(resp, dto, $"正单零售价不允许为0：{dataModel.platformOrderId}！", dto.extParam.subOrderIds, false);
                                }
                            }
                            detailItem["fbizqty"] = dataModel.productNumber;//商品数量
                            detailItem["fqty"] = dataModel.productNumber;//商品数量
                            detailItem["fprice"] = price;//成交价
                            detailItem["fdealprice"] = price;//成交价
                            detailItem["fsendtarget"] = dataModel.receiverType;//下发标记
                            detailItem["famount"] = dataModel.productNumber * price;
                            detailItem["fdealamount"] = dataModel.productNumber * price;
                            detailItem["fdistamount"] = "0";
                            //detailItem["fdescription"] = dataModel.platformOrderName;
                            detailItem["ffactorybillname"] = dataModel.platformOrderName;//订单名称取三维家工厂订单接口platformOrderName字段
                            detailItem["fclosestatus_e"] = "0";
                            detailItem["fthirdsource"] = "ts_type_01";  //第三方来源
                                                                        //销售合同【未出库基本数量】=【销售数量】
                            detailItem["funstockoutqty"] = dataModel.productNumber;
                            detailItem["fstonetable"] = dataModel.isTableBoard;  //是否含石材台面
                            detailItem["fordertype"] = dataModel.moduleName;  //订单品类
                            detailItem["fomsoptionid"] = dataModel.platformOrderschemeId;  //方案id
                            detailItem["fomsdesignfile"] = dataModel.schemeContent;  //fomsdesignfile
                            detailItem["fomscategory"] = dataModel.unitCategoryName;  //商品品类


                            if (!string.IsNullOrWhiteSpace(dataModel.schemePreviewImage))
                            {
                                string fileName = dataModel.schemePreviewImage.Substring(dataModel.schemePreviewImage.LastIndexOf("/") + 1, dataModel.schemePreviewImage.Length - dataModel.schemePreviewImage.LastIndexOf("/") - 1);
                                var fileresult = PostFile(dataModel.schemePreviewImage, fileName);
                                if (Convert.ToBoolean(fileresult["isSuccess"]))
                                {
                                    detailItem["fomsfiles"] = fileresult["fileId"];  //缩略图
                                }
                            }

                            detailItem["fomstexture"] = dataModel.material;  //主材质
                            detailItem["fomscolor"] = dataModel.basicMaterial;  //主颜色
                            if (isAdd)
                            {
                                objDetail.Add(detailItem);
                            }
                            //清空预置的明细行
                            List<DynamicObject> removeItem = new List<DynamicObject>();
                            foreach (var entryItem in objDetail)
                            {
                                if (Convert.ToString(entryItem["ffactorybillid"]).IsNullOrEmptyOrWhiteSpace())
                                {
                                    removeItem.Add(entryItem);
                                }
                            }
                            foreach (var item in removeItem)
                            {
                                objDetail.Remove(item);
                            }
                        }
                        //处理国补预收字段
                        foreach (var item in objDetail)
                        {
                            if (objDetail.IndexOf(item) == 0)
                            {
                                item["fsubrecdealamount_gb"] = orderObj["frecdealamount_gb"];
                                item["fsubrecsumamount_gb"] = orderObj["frecsumamount_gb"];
                            }
                            else
                            {
                                item["fsubrecdealamount_gb"] = "0.01";
                                item["fsubrecsumamount_gb"] = "0.01";
                            }
                        }
                        var orderFormMate = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                        var orderService = this.Container.GetService<IOrderService>();
                        orderService.CalculateSettlement(agentCtx, orderObj, orderFormMate);
                        orderService.CalculateUnreceived(agentCtx, new[] { orderObj });
                        orderService.CalculateReceiptStatus(agentCtx, new[] { orderObj });

                        var result1 = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", new[] { orderObj }, "save",
                            new Dictionary<string, object>());
                        if (result1.IsSuccess)
                        {
                            var attrEntrys = this.GetAttachmentList(orderObj);
                            if (attrEntrys != null)
                            {
                                //重新下发的，需要删除附件
                                var attachlist = this.HttpGateway.InvokeBillOperation(agentCtx, "bd_attachlist", new[] { attrEntrys }, "save",
                                    new Dictionary<string, object>());
                            }
                            resp.Message = result1.ComplexMessage.SuccessMessages.Count > 0 ? result1.ComplexMessage.SuccessMessages[0] : "";
                            this.OrderResult(resp, dto, resp.Message, dto.extParam.subOrderIds, true);
                            JArray array = JsonConvert.DeserializeObject<JArray>(JsonConvert.SerializeObject(result1.SrvData));
                            string number = array.Count > 0 ? Convert.ToString(array[0]["number"]) : "";
                            contractNo = number;
                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，一次下发，操作成功】，内容:{4}".Fmt(this.Context.UserName,
                                    this.Context.UserPhone, this.Context.Company,
                                    DateTime.Now.ToString("HH:mm:ss"), "resp：" + resp.ToJson()),
                                "SWJLogFile");

                            Task.Run(() =>
                            {
                                var backinfo = new OrderAPI(this.Context).updateOrderContractNo(SWJClient.Instance, dto.subjectId, orderCode, contractNo);

                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，记录回传结果】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "backinfo：" + JsonConvert.SerializeObject(backinfo)),
                                    "SWJLogFile");
                            });
                        }
                        else
                        {
                            contractNo = "一次下发，销售合同保存失败；result：" + JsonConvert.SerializeObject(result1.ComplexMessage.ErrorMessages);
                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，销售合同保存失败】，内容:{4}".Fmt(agentCtx.UserName,
                                    agentCtx.UserPhone, agentCtx.Company,
                                 DateTime.Now.ToString("HH:mm:ss"), "result：" + JsonConvert.SerializeObject(result1)),
                                "SWJLogFile");
                            this.OrderResult(resp, dto, contractNo, dto.extParam.subOrderIds, false);
                        }
                    }
                    else
                    {
                        if (orderType.IsNullOrEmptyOrWhiteSpace()) //新增销售合同
                        {
                            try
                            {
                                var orderHtml = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                                var saleOrderDt = orderHtml.GetDynamicObjectType(this.Context);
                                DynamicObject obj = new DynamicObject(orderHtml.GetDynamicObjectType(this.Context));

                                obj["fswjorderstate"] = 0;
                                var objDetail = obj["fentry"] as DynamicObjectCollection;
                                var dutobjDetail = obj["fdutyentry"] as DynamicObjectCollection;
                                List<DynamicObject> entrys = new List<DynamicObject>();
                                decimal fsumamount = 0;
                                List<string> subOrderIds = dto.extParam.subOrderIds;

                                foreach (var item in subOrderIds)
                                {
                                    // todo：获取3维家合同信息
                                    var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, item);

                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取合同数据--Step1】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                        "SWJLogFile");
                                    if (orderInfo.success)
                                    {
                                        var dataModel = orderInfo.result;
                                        Dictionary<string, object> urlParam = new Dictionary<string, object>();
                                        Validation(resp, dataModel, out urlParam, out contractNo, out string BMID);
                                        if (!resp.Success)
                                            return resp;
                                        /* 47925
                                         1.2.当【启用定制OMS】=“是”时，如果《销售合同》的【三维家单号】已存在且单据【作废状态】=“否”且所有商品行【定制订单进度】都为“待接单”时，则允许更新整个订单。
                                         1.3.当【启用定制OMS】=“是”时，如果《销售合同》的【三维家单号】已存在且单据【作废状态】=“否”且存在商品行【定制订单进度】不为“待接单”时，则需报错提示：“三维家单号为XXX的订单已存在，且存在商品行状态审核中，请联系总部处理！”
                                         1.4.当【启用定制OMS】=“否”时，如果《销售合同》的【三维家单号】已存在且单据【作废状态】=“否”且【三维家门店审核】=“否”时，则允许更新整个订单。                                
                                        */
                                        var sqlParams = new List<SqlParam>
                                    {
                                      new SqlParam("@fswjordernumber", DbType.String, dataModel.shopOrderCode),
                                    };
                                        string fsaleidsql = "select top 1 a.fid from T_YDJ_ORDER as a with(nolock) inner join T_YDJ_ORDERENTRY as b with(nolock) on a.fid=b.fid where fswjordernumber=@fswjordernumber and fcancelstatus='0' order by fcreatedate desc";
                                        using (var dtr = this.DBService.ExecuteReader(this.Context, fsaleidsql, sqlParams))
                                        {
                                            if (dtr.Read() && Convert.ToString(obj["id"]).IsNullOrEmptyOrWhiteSpace())//已存在销售合同 (未作废)
                                            {
                                                string fsaleid = dtr.GetString("fid");
                                                var orderObj = this.Context.LoadBizDataById("ydj_order", fsaleid);
                                                var fentry = orderObj["fentry"] as DynamicObjectCollection;
                                                var omsservice = Convert.ToBoolean(orderObj["fomsservice"]);
                                                var fswjorderstate = Convert.ToBoolean(orderObj["fswjorderstate"]);

                                                if (!orderObj["fswjordernumber"].IsNullOrEmptyOrWhiteSpace())
                                                { //【三维家单号】已存在 并且合同未作废
                                                    if (omsservice || (!omsservice && !fswjorderstate)) //(当【启用定制OMS】=“是”)或者 (当【启用定制OMS】=“否”时， 且【三维家门店审核】=“否”)
                                                    {
                                                        var omsprogressEntry = fentry.FirstOrDefault(o =>
                                                        {
                                                            int.TryParse(Convert.ToString(o["fomsprogress"]), out var fomsprogress);
                                                            switch (fomsprogress)
                                                            {
                                                                case 5:
                                                                    return false;
                                                                default:
                                                                    return true;
                                                            }
                                                        });//且所有商品行都为“待接单”时，则允许更新整个订单
                                                        if (omsprogressEntry != null && omsservice)
                                                        {
                                                            return this.OrderResult(resp, dto, contractNo, dto.extParam.subOrderIds, false);
                                                        }


                                                        //获取旧合同 (全新覆盖)
                                                        obj = orderObj;
                                                        objDetail = obj["fentry"] as DynamicObjectCollection;
                                                        objDetail.Clear();
                                                    }
                                                }
                                            }
                                        }
                                        var material = dataModel.productCode;
                                        var customerCode = urlParam["customerId"].ToString();

                                        var customerObj = agentCtx.LoadBizDataByNo("ydj_customer", "fnumber", new List<string> { customerCode }).FirstOrDefault();
                                        if (customerObj == null)
                                        {
                                            customerObj = agentCtx.LoadBizDataById("ydj_customer", customerCode);
                                            if (customerObj == null)
                                            {
                                                //contractNo = $"当前系统不存在该客户，客户编码：{customerCode}！";
                                                //resp.Message = $"当前系统不存在该客户，客户编码：{customerCode}！";
                                                //resp.Code = 10001;
                                                //resp.Success = false;
                                                //return resp;
                                            }
                                        }
                                        orderCode = dataModel.shopOrderCode;//关联订单
                                        obj["fswjordernumber"] = dataModel.shopOrderCode;//关联订单
                                        string outUserId = dto.operateUser?.outUserId;
                                        if (!string.IsNullOrWhiteSpace(outUserId))
                                        {
                                            var staff = agentCtx.LoadBizDataByFilter("ydj_staff", "flinkuserid=@flinkuserid", false, new List<SqlParam>() {
                                        new SqlParam("@flinkuserid", DbType.String, outUserId)
                                    });
                                            if (staff != null && staff.Count > 0)
                                            {
                                                obj["fswjdesignerid"] = staff[0]["id"];//设计师 
                                            }
                                        }

                                        IOrderService orderService = this.Context.Container.GetService<IOrderService>();
                                        var fbilltypeid = orderService.GetBillTypeData(this.agentCtx, orderHtml, "v6定制柜合同");
                                        obj["fbilltype"] = fbilltypeid;
                                        DynamicObject materialObj = null;
                                        var materialObjs = agentCtx.ExecuteDynamicObject("select top 1  fid,funitid,fsalunitid,fseriesid from T_BD_MATERIAL with(nolock) where fnumber=@fnumber order by fmainorgid", new List<SqlParam> { new SqlParam("fnumber", DbType.String, material) });
                                        if (materialObjs.Count > 0)
                                        {
                                            materialObj = materialObjs[0];
                                        }
                                        else
                                        {
                                            return this.OrderResult(resp, dto, contractNo, dto.extParam.subOrderIds, false);
                                        }
                                        obj["fswjdetailurl"] = SWJClient.Instance.DetailUri + "?orderId=" + dto.subjectId;
                                        obj["forderdate"] = dataModel.createTime;//业务日期
                                        obj["fdeliverydate"] = dataModel.deliveryDate;//交货日期
                                        obj["fcustomerid"] = customerObj?["id"];//客户
                                                                                // 联系人明细
                                        var sqlText = $@"select t1.fid, t2.fcuscontacttryid from t_ydj_customer t1 with(nolock)
                                                    inner join (
                                                        select row_number() over(partition by fid order by fseq) as count,fid,fcuscontacttryid
                                                        from t_ydj_fcuscontacttry with(nolock)
                                                    ) t2 on t1.fid = t2.fid and t2.count = 1
                                                    where t1.fid ='{customerObj?["id"]}'";
                                        using (var reader = agentCtx.ExecuteReader(sqlText, null))
                                        {
                                            if (reader.Read())
                                            {
                                                obj["fcustomercontactid"] = reader["fcuscontacttryid"];//收货人
                                            }
                                        }
                                        obj["fprovince"] = customerObj?["fprovince"];//省
                                        obj["fcity"] = customerObj?["fcity"];//市
                                        obj["fregion"] = customerObj?["fregion"];//区
                                        obj["ftype"] = "order_type_01";//业务类型

                                        var dutdetailItem = dutobjDetail.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                        //urlParam["SalesManID"] = "903316062535417856";
                                        var staffObj = agentCtx.LoadBizDataById("ydj_staff", Convert.ToString(urlParam["SalesManID"]));

                                        obj["fphone"] = customerCode.ToLower().Equals("gly") ? "" : urlParam["ClientPhone"];//手机号
                                        obj["fstaffid"] = urlParam["SalesManID"];//销售员
                                        string deptid = string.IsNullOrWhiteSpace(BMID) ? Convert.ToString(staffObj?["fdeptid"]) : BMID;
                                        obj["fdeptid"] = deptid;//销售部门
                                                                //门店也赋值
                                        obj["fstore"] = Convert.ToString(agentCtx.LoadBizBillHeadDataById("ydj_dept", Convert.ToString(staffObj?["fdeptid"]), "fstore")?["fstore"]); ;//关联门店

                                        if (urlParam.ContainsKey("ClientModelName"))
                                        {
                                            obj["faddress"] = urlParam["ClientModelName"];//详细地址
                                        }
                                        var spService = this.Container.GetService<ISystemProfile>();
                                        //var fomsservice = spService.GetSystemParameter(agentCtx, "bas_storesysparam", "fopenomsservice", false);
                                        //var fomsservice = true;

                                        //43797 【慕思OMS正式区问题】XSHT101122000000118、XSHT101122000000119、XSHT101181400000084经销商启用了定制OMS参数，但是订单生成是，启用定制OMS=N;
                                        //针对OMS业务，设定默认开启
                                        obj["fomsservice"] = true;//启用定制OMS

                                        decimal price = 0;
                                        // Convert.ToString(dataModel.orderTypeDictVal) == "售后单" ? dataModel.productTotalPrice : dataModel.saleTotalPrice;
                                        if (Convert.ToString(dataModel.orderTypeDictVal).Equals("售后单"))
                                        {
                                            if (dataModel.productTotalPrice > 0)
                                            {
                                                price = dataModel.productTotalPrice * 3;
                                            }
                                            else price = 1;
                                            obj["findenttype"] = "H";
                                        }
                                        else
                                        {
                                            price = dataModel.saleTotalPrice;
                                            if (price == 0)
                                            {
                                                return this.OrderResult(resp, dto, $"正单零售价不允许为0：{item}！", dto.extParam.subOrderIds, false);
                                            }
                                            obj["findenttype"] = "L";
                                        }
                                        if (customerCode.ToLower().Equals("gly"))
                                        {
                                            if (Convert.ToString(dataModel.orderTypeDictVal).Equals("售后单"))
                                                obj["findenttype"] = "H";
                                            else
                                                obj["findenttype"] = "Y";
                                        }
                                        dutdetailItem["fdutyid"] = urlParam["SalesManID"];//销售员
                                        dutdetailItem["fdeptid"] = staffObj?["fdeptid"];//销售部门
                                        dutdetailItem["fismain"] = "1";//主要负责
                                        dutdetailItem["fratio"] = "100";//比例
                                        dutdetailItem["famount"] = dataModel.productNumber * price;//主要负责


                                        var detailItem = objDetail.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                        detailItem["fproductid"] = materialObj["fid"];

                                        detailItem["fcustomdes_e"] = string.IsNullOrEmpty(dataModel.platformOrderCode) ? dataModel.shopOrderCode : dataModel.platformOrderCode;//工厂订单号
                                        detailItem["ffactorybillid"] = item;//工厂订单ID
                                        detailItem["ffactorybillno"] = string.IsNullOrEmpty(dataModel.platformOrderCode) ? dataModel.shopOrderCode : dataModel.platformOrderCode;//工厂订单号
                                        detailItem["funitid"] = materialObj["funitid"];//单位
                                        detailItem["fbizunitid"] = materialObj["fsalunitid"];//销售单位
                                        detailItem["fresultbrandid"] = materialObj["fseriesid"];//业绩品牌
                                        detailItem["fomsprogress"] = "5";//定制订单进度 默认 待提交
                                        detailItem["fstockstatus"] = "311858936800219137";//库存状态 默认 可用
                                        detailItem["fisomscustomer"] = "1";//启用OMS定制说明
                                        detailItem["fomsoptionid"] = dataModel.platformOrderschemeId;  //方案id
                                        detailItem["fomsdesignfile"] = dataModel.schemeContent;  //fomsdesignfile
                                        detailItem["fomscategory"] = dataModel.unitCategoryName;  //商品品类

                                        //【弃用】
                                        //if (!string.IsNullOrWhiteSpace(dataModel.schemePreviewImage))
                                        //{
                                        //    string fileName = dataModel.schemePreviewImage.Substring(dataModel.schemePreviewImage.LastIndexOf("/") + 1, dataModel.schemePreviewImage.LastIndexOf(".") - dataModel.schemePreviewImage.LastIndexOf("/") - 1);
                                        //    var fileresult = PostFile(dataModel.schemePreviewImage, fileName);
                                        //    //if (Convert.ToString(fileresult["isSuccess"]) == "true")
                                        //    //{
                                        //    //    fdrawentity["ffilesize"] = fileresult["fileSize"];
                                        //    //    fdrawentity["ffileid"] = fileresult["fileId"];
                                        //    //}
                                        //    //fdrawentities.Add(fdrawentity);
                                        //}

                                        if (!string.IsNullOrWhiteSpace(dataModel.schemePreviewImage))
                                        {
                                            string fileName = dataModel.schemePreviewImage.Substring(dataModel.schemePreviewImage.LastIndexOf("/") + 1, dataModel.schemePreviewImage.Length - dataModel.schemePreviewImage.LastIndexOf("/") - 1);
                                            var fileresult = PostFile(dataModel.schemePreviewImage, fileName);
                                            if (Convert.ToBoolean(fileresult["isSuccess"]))
                                            {
                                                detailItem["fomsfiles"] = fileresult["fileId"];  //缩略图
                                            }
                                        }
                                        detailItem["fomstexture"] = dataModel.material;  //主材质
                                        detailItem["fomscolor"] = dataModel.basicMaterial;  //主颜色


                                        List<AuxPropValDTO> newPropList = new List<AuxPropValDTO>();
                                        var sel_cats = this.Context.LoadBizDataByNo("sel_category", "fnumber", new List<string>() { "VFZ1_M001" }).FirstOrDefault();
                                        if (sel_cats != null)
                                        {
                                            var selentrys = sel_cats["fentity"] as DynamicObjectCollection;
                                            foreach (var sel_propobj in selentrys.Where(a => Convert.ToInt32(a["fiswhetherinforce"]) == 1))
                                            {
                                                var propItem = this.Context.LoadBizDataById("sel_prop", Convert.ToString(sel_propobj["fpropid"]));
                                                var propValueItem = this.Context.LoadBizDataById("sel_propvalue", Convert.ToString(sel_propobj["fdefaultpropvalueid"]));
                                                newPropList.Add(new AuxPropValDTO
                                                {
                                                    PropNo = Convert.ToString(propItem["fnumber"]),
                                                    PropValueNo = Convert.ToString(propValueItem["fname"]),
                                                    PropValueName = Convert.ToString(propValueItem["fname"])
                                                });
                                            }
                                        }
                                        detailItem["fattrinfo_ref"] = ProductUtil.ConvertAuxPropFieldValue(this.Context, Convert.ToString(materialObj["fid"]), newPropList);
                                        detailItem["fdistrate"] = "10";//折扣
                                        detailItem["fdistamount"] = "0";//折扣额

                                        detailItem["fbizqty"] = dataModel.productNumber;//商品数量
                                        detailItem["fqty"] = dataModel.productNumber;//商品数量
                                        detailItem["fprice"] = price;//成交价
                                        detailItem["fdealprice"] = price;//成交价
                                        detailItem["fsendtarget"] = dataModel.receiverType;//下发标记
                                        fsumamount += dataModel.productNumber * price;
                                        detailItem["famount"] = dataModel.productNumber * price;
                                        detailItem["fdealamount"] = dataModel.productNumber * price;
                                        detailItem["fdistamount"] = "0";
                                        //detailItem["fdescription"] = dataModel.platformOrderName;
                                        detailItem["ffactorybillname"] = dataModel.platformOrderName;//订单名称取三维家工厂订单接口platformOrderName字段
                                        detailItem["fclosestatus_e"] = "0";

                                        detailItem["fthirdsource"] = "ts_type_01";  //第三方来源
                                                                                    //销售合同【未出库基本数量】=【销售数量】
                                        detailItem["funstockoutqty"] = dataModel.productNumber;

                                        detailItem["fstonetable"] = dataModel.isTableBoard;  //是否含石材台面
                                        detailItem["fordertype"] = dataModel.moduleName;  //订单品类

                                        entrys.Add(detailItem);
                                        if (dutobjDetail.Count == 0)
                                        {
                                            dutobjDetail.Add(dutdetailItem);
                                        }
                                        objDetail.Add(detailItem);
                                    }
                                    else
                                    {
                                        contractNo = "通过" + item + "未获取到订单数据，请求失败！";
                                        loger.WriteLogToFile("时间：{0}，操作：【三维家接口，获取数据包失败】，内容:{1}".Fmt(
                                             DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                            "SWJLogFile");
                                        ////设置响应数据包
                                        //resp.Message = "通过" + item + "未获取到订单数据，请求失败！";
                                        //resp.Code = 10001;
                                        //resp.Success = false;
                                        //return resp;
                                        return this.OrderResult(resp, dto, contractNo, dto.extParam.subOrderIds, false);
                                    }
                                }
                                obj["fsumamount"] = fsumamount;
                                obj["fdealamount"] = fsumamount;
                                obj["fprofit"] = fsumamount;//毛利
                                obj["funreceived"] = fsumamount;//未收款
                                obj["ffaceamount"] = fsumamount;//货品原值
                                obj["fdiscscale"] = "10";


                                var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                                prepareSaveDataService.PrepareDataEntity(agentCtx, orderHtml, new[] { obj }, OperateOption.Create());

                                //obj["fbillno"] = Convert.ToString(obj["fbillno"]).Contains("DZ") ? Convert.ToString(obj["fbillno"]) : "DZ" + Convert.ToString(obj["fbillno"]);
                                var result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", new[] { obj }, "save",
                                            new Dictionary<string, object>());
                                if (result.IsSuccess)
                                {
                                    JArray array = JsonConvert.DeserializeObject<JArray>(JsonConvert.SerializeObject(result.SrvData));
                                    string number = array.Count > 0 ? Convert.ToString(array[0]["number"]) : "";
                                    contractNo = number;
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，成功】，内容:{4}".Fmt(agentCtx.UserName,
                                            agentCtx.UserPhone, agentCtx.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "result：" + JsonConvert.SerializeObject(result)),
                                        "SWJLogFile");
                                    resp.Message = result.ComplexMessage.SuccessMessages.Count > 0 ? result.ComplexMessage.SuccessMessages[0] : "";
                                    this.OrderResult(resp, dto, resp.Message, dto.extParam.subOrderIds, true);
                                }
                                else
                                {
                                    contractNo = "销售合同保存失败；result：" + JsonConvert.SerializeObject(result.ComplexMessage.ErrorMessages);
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，销售合同保存失败】，内容:{4}".Fmt(agentCtx.UserName,
                                            agentCtx.UserPhone, agentCtx.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "result：" + JsonConvert.SerializeObject(result)),
                                        "SWJLogFile");
                                    resp.Message = contractNo;
                                    resp.Success = false;
                                    resp.Code = 10001;
                                    this.OrderResult(resp, dto, contractNo, dto.extParam.subOrderIds, false);
                                }
                            }
                            catch (Exception ex)
                            {
                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，Error】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                                    "SWJLogFile");
                                this.OrderResult(resp, dto, "销售合同一次下发出错：" + ex.Message, dto.extParam.subOrderIds, false);
                            }
                            finally
                            {
                                Task.Run(() =>
                                {
                                    var backinfo = new OrderAPI(this.Context).updateOrderContractNo(SWJClient.Instance, dto.subjectId, orderCode, contractNo);

                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，记录回传结果】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "backinfo：" + JsonConvert.SerializeObject(backinfo)),
                                        "SWJLogFile");
                                });

                            }
                        }
                        #region 
                        else if (orderType.Equals("1") || orderType.Equals("2"))  //生成采购订单  1：生成备货订单，2：生成摆场订单  （单据类型根据此来设置）
                        {
                            try
                            {
                                var purchaseorderHtml = this.MetaModelService.LoadFormModel(this.Context, "ydj_purchaseorder");
                                var purchaseorderDt = purchaseorderHtml.GetDynamicObjectType(this.Context);

                                List<string> subOrderIds = dto.extParam.subOrderIds;

                                foreach (var item in subOrderIds)
                                {
                                    // todo：获取3维家合同信息
                                    var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, item);

                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取合同数据--Step1】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                            DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                        "SWJLogFile");
                                    if (orderInfo.success)
                                    {
                                        var dataModel = orderInfo.result;
                                        var platformorderid = item;
                                        var platformOrderCode = dataModel.platformOrderCode;

                                        Dictionary<string, object> urlParam = new Dictionary<string, object>();
                                        Validation(resp, dataModel, out urlParam, out contractNo, out string BMID);
                                        if (!resp.Success)
                                            return resp;

                                        //【送达方】按【销售组织编码】=“2111”，过滤出当前经销商下所有可选【送达方】。如果只有一个就直接固定显示，多个则空着让用户自行选择。
                                        var deliverId = string.Empty;
                                        var sqlText = string.Format(@"select t0.fid fid from t_bas_deliver  t0 with(nolock) 
                                                                        inner join t_bas_organization t1 with(nolock)  on t0.fsaleorgid=t1.fid
                                                                        inner join t_bas_organization t2 with(nolock)  on t0.fagentid=t2.fid
                                                                        inner join t_bas_deliverentry t3 with(nolock)  on t0.fid=t3.fid
                                                                        inner join t_ydj_series t4 with(nolock)  on t3.fserieid=t4.fid
                                                                        where t1.fnumber='2111' and t0.fforbidstatus='0' and t2.fnumber='{0}' and t4.fnumber='F1'", Convert.ToString(urlParam["DealerCode"]));
                                        using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                                        {
                                            var deliverCount = 0;

                                            while (reader.Read())
                                            {
                                                deliverCount++;
                                                if (deliverCount != 1)
                                                {
                                                    deliverId = string.Empty;
                                                    break;
                                                }
                                                deliverId = reader.GetValueToString("fid");
                                            }
                                        }

                                        //4.2.【供应商】取数逻辑：
                                        //4.2.1.如果【送达方】只有一个且固定，则按系统原有代码逻辑直接取【供应商】直接显示。
                                        //4.2.2.如果【送达方】多个且空着：
                                        //4.2.2.1.如果【销售组织编码】=“2111”且【禁用状态】=“否”的《供应商》只有1条，则默认固定为这一条。
                                        //*******.如果【销售组织编码】=“2111”且【禁用状态】=“否”的《供应商》有多条，则需按供应商ID排序取TOP1那一条。
                                        var supplierid = string.Empty;
                                        if (!deliverId.IsNullOrEmptyOrWhiteSpace())
                                        {
                                            IPurchaseOrderService purchaseOrderService = this.Context.Container.GetService<IPurchaseOrderService>();

                                            supplierid = purchaseOrderService.GetSupplierIdByDeliverId(this.Context, deliverId);

                                            if (supplierid.IsNullOrEmptyOrWhiteSpace())
                                            {
                                                var metaModelService = this.Context.Container.GetService<IMetaModelService>();
                                                var deliverForm = metaModelService.LoadFormModel(this.Context, "bas_deliver");
                                                var dmm = this.Context.Container.GetService<IDataManager>();
                                                var formDtt = deliverForm.GetDynamicObjectType(this.Context);
                                                dmm.InitDbContext(this.Context, formDtt);
                                                //根据id获取
                                                var initstockbillObj = dmm.Select(deliverId) as DynamicObject;

                                                // 送达方带出的供应商取当前企业下的
                                                var agents = new[] { agentCtx.Company };

                                                if (initstockbillObj != null)
                                                {
                                                    string strSql = @"select top 1 fid from t_ydj_supplier where forgid=@forgid and fmainorgid in ('{0}')".Fmt(string.Join("','", agents));
                                                    var sqlParam = new List<SqlParam>
                                                {
                                                    new SqlParam("@forgid", DbType.String, Convert.ToString(initstockbillObj["fsaleorgid"]))
                                                };
                                                    using (var dr = this.Context.ExecuteReader(strSql, sqlParam))
                                                    {
                                                        if (dr.Read())
                                                        {
                                                            supplierid = dr.GetValueToString("fid");
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            sqlText = string.Format(@"select top 1  fid from t_ydj_supplier with(nolock)  where fforbidstatus='0' and forgid in (select fid from t_bas_organization  with(nolock) where  fnumber='2111') and fmainorgid in (select fid from t_bas_organization  with(nolock) where  fnumber='{0}') order by fid", Convert.ToString(urlParam["DealerCode"]));
                                            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
                                            {
                                                while (reader.Read())
                                                {
                                                    supplierid = reader.GetValueToString("fid");
                                                }
                                            }
                                        }

                                        DynamicObject obj = new DynamicObject(purchaseorderHtml.GetDynamicObjectType(this.Context));
                                        var objDetail = obj["fentity"] as DynamicObjectCollection;

                                        //生成采购订单时，需根据表头的【第三方单号】是否已经存在采购订单
                                        DynamicObject purchaseorderObj = null;
                                        var purchaseorderObjs = agentCtx.ExecuteDynamicObject("select top 1 fid,fthirdbillno,fhqderstatus from T_YDJ_PURCHASEORDER with(nolock) where fthirdbillno=@fthirdbillno", new List<SqlParam> { new SqlParam("fthirdbillno", DbType.String, platformOrderCode) });

                                        if (purchaseorderObjs.Count > 0) //已存在
                                        {
                                            purchaseorderObj = purchaseorderObjs[0];

                                            if (purchaseorderObj["fhqderstatus"].Equals("02") || purchaseorderObj["fhqderstatus"].Equals("03"))
                                            {
                                                //如果存在且【总部合同状态】=“提交至总部 / 已终审”，则需提示：“【第三方单号】为XXX的采购订单已存在且已提交总部处理，禁止重复创建。”。
                                                resp.Message = $"【第三方单号】为{platformOrderCode}的采购订单已存在且已提交总部处理，禁止重复创建。";
                                                resp.Code = 10001;
                                                resp.Success = false;
                                                return resp;
                                            }

                                            if (purchaseorderObj["fhqderstatus"].Equals("01") || purchaseorderObj["fhqderstatus"].Equals("05"))
                                            {
                                                //如果存在且【总部合同状态】=“创建 / 驳回”，则需调用《获取订单信息》接口，刷新采购订单所有字段数据。
                                                var purObj = agentCtx.LoadBizDataById("ydj_purchaseorder", Convert.ToString(purchaseorderObj["fid"]));

                                                if (!purObj.IsNullOrEmptyOrWhiteSpace())
                                                {
                                                    //获取旧采购订单 (全新覆盖)
                                                    obj = purObj;
                                                    objDetail = obj["fentity"] as DynamicObjectCollection;
                                                    objDetail.Clear();
                                                }
                                            }
                                        }

                                        //供应商id
                                        obj["fsupplierid"] = supplierid;

                                        //送达方id
                                        obj["fdeliverid"] = deliverId;

                                        //【三维家单号】为“销售订单号”
                                        obj["fswjordernumber"] = dataModel.shopOrderCode;
                                        obj["fswjdetailurl"] = SWJClient.Instance.DetailUri + "?orderId=" + dto.subjectId;

                                        //【结算状态】为“全款未付”
                                        obj["fpaystatus"] = "paystatus_type_01";

                                        //【订单日期】取【获取订单信息.createTime】。
                                        obj["fdate"] = dataModel.createTime;

                                        //【交货日期】取【获取订单信息.deliveryDate】。
                                        obj["fpickdate"] = dataModel.deliveryDate;

                                        //【采购员】取【订单推送通知.operateUser】对应的《员工》信息，【采购部门】取【采购员】的主岗位部门。
                                        var fpostaffid = string.Empty;
                                        DynamicObject postaffidObj = null;
                                        var postaffidObjs = agentCtx.ExecuteDynamicObject("select fid from t_bd_staff with(nolock) where flinkuserid=@flinkuserid", new List<SqlParam> { new SqlParam("flinkuserid", DbType.String, dto.operateUser.outUserId) });
                                        if (postaffidObjs.Count > 0)
                                        {
                                            postaffidObj = postaffidObjs[0];
                                            fpostaffid = Convert.ToString(postaffidObj["fid"]).Trim();
                                        }

                                        obj["fpostaffid"] = fpostaffid;

                                        if (!fpostaffid.IsNullOrEmptyOrWhiteSpace())
                                        {
                                            DynamicObject podeptObj = null;
                                            var podeptObjs = agentCtx.ExecuteDynamicObject("select fdeptid from t_bd_staffentry with(nolock) where fid=@fid and fismain='1'", new List<SqlParam> { new SqlParam("fid", DbType.String, fpostaffid) });
                                            if (podeptObjs.Count > 0)
                                            {
                                                podeptObj = podeptObjs[0];
                                                obj["fpodeptid"] = podeptObj["fdeptid"];
                                            }
                                            else
                                            {
                                                obj["fpodeptid"] = string.Empty;
                                            }
                                        }


                                        //【三维家订单的通用ID】自动记录并存储【三维家订单通用ID】数据。
                                        obj["fplatformorderid"] = platformorderid;

                                        //【业务类型】为“正单”
                                        obj["ftype"] = "order_type_01";

                                        //【第三方来源】为“三维家”
                                        obj["fthirdsource"] = "三维家";
                                        //【直发标记】取【获取订单信息.receiverType】
                                        obj["fsendtarget"] = dataModel.receiverType;
                                        //【第三方单号】取【获取订单信息.platformOrderCode】
                                        obj["fthirdbillno"] = platformOrderCode;
                                        //【消费者地址】为【获取订单信息.urlParam.ClientModelName】。
                                        if (urlParam.ContainsKey("ClientModelName"))
                                            obj["fconsumersaddress"] = urlParam["ClientModelName"];
                                        else
                                            obj["fconsumersaddress"] = string.Empty;

                                        var spService = this.Container.GetService<ISystemProfile>();
                                        //var fomsservice = spService.GetSystemParameter(agentCtx, "bas_storesysparam", "fopenomsservice", false);
                                        //43797 【慕思OMS正式区问题】XSHT101122000000118、XSHT101122000000119、XSHT101181400000084经销商启用了定制OMS参数，但是订单生成是，启用定制OMS=N;
                                        //针对OMS业务，设定默认开启
                                        obj["fomsservice"] = true;//启用定制OMS

                                        //【单据类型】金蝶接收到【operateType】=“AIMES_CREATE_ORDER”的《订单推送通知》时，需根据【orderType】判断，
                                        //如果为1则设置为“备货订单”，
                                        //如果为2则设置为“摆场订单”。
                                        obj["fbilltypeid"] = string.Empty;
                                        if (orderType.Equals("1"))
                                        {
                                            IPurchaseOrderService purchaseOrderService = this.Context.Container.GetService<IPurchaseOrderService>();
                                            var fbilltypeid = purchaseOrderService.GetBillTypeData(this.agentCtx, purchaseorderHtml, "备货订单");
                                            obj["fbilltypeid"] = fbilltypeid;
                                        }

                                        if (orderType.Equals("2"))
                                        {
                                            IPurchaseOrderService purchaseOrderService = this.Context.Container.GetService<IPurchaseOrderService>();
                                            var fbilltypeid = purchaseOrderService.GetBillTypeData(this.agentCtx, purchaseorderHtml, "摆场订单");
                                            obj["fbilltypeid"] = fbilltypeid;
                                        }

                                        var material = dataModel.productCode;
                                        DynamicObject materialObj = null;
                                        var materialObjs = agentCtx.ExecuteDynamicObject("select top 1  fid,funitid,fpurunitid,fseriesid,fbrandid,fcategoryid from T_BD_MATERIAL with(nolock) where fnumber=@fnumber order by fmainorgid", new List<SqlParam> { new SqlParam("fnumber", DbType.String, material) });
                                        if (materialObjs.Count > 0)
                                        {
                                            materialObj = materialObjs[0];
                                        }
                                        else
                                        {
                                            resp.Message = $"当前不存在该物料！商品通用料号：{material}。";
                                            resp.Code = 10001;
                                            resp.Success = false;
                                            return resp;
                                        }
                                        var detailItem = objDetail.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;
                                        //商品行字段逻辑：
                                        //商品id
                                        detailItem["fmaterialid"] = materialObj["fid"];
                                        //【商品编码】取【获取订单信息.productCode】
                                        //detailItem["fmtrlnumber"] = dataModel.productCode;
                                        //【数量】取【获取订单信息.productNumber】
                                        detailItem["fbizqty"] = dataModel.productNumber;
                                        //【基本单位采购数量】取【获取订单信息.productNumber】
                                        detailItem["fqty"] = dataModel.productNumber;
                                        //【销售单价】取【获取订单信息.saleTotalPrice】
                                        detailItem["fsalprice"] = dataModel.saleTotalPrice;
                                        //【备注】取【获取订单信息.platformOrderName】
                                        //detailItem["fnote"] = dataModel.platformOrderName;
                                        //【订单名称】取【获取订单信息.platformOrderName】
                                        detailItem["ffactorybillname"] = dataModel.platformOrderName;
                                        //其它字段则需自动带出处理，如业绩品牌、单位等。

                                        //辅助属性
                                        List<AuxPropValDTO> newPropList = new List<AuxPropValDTO>();
                                        var sel_cats = this.Context.LoadBizDataByNo("sel_category", "fnumber", new List<string>() { "VFZ1_M001" }).FirstOrDefault();
                                        if (sel_cats != null)
                                        {
                                            var selentrys = sel_cats["fentity"] as DynamicObjectCollection;
                                            foreach (var sel_propobj in selentrys.Where(a => Convert.ToInt32(a["fiswhetherinforce"]) == 1))
                                            {
                                                var propItem = this.Context.LoadBizDataById("sel_prop", Convert.ToString(sel_propobj["fpropid"]));
                                                var propValueItem = this.Context.LoadBizDataById("sel_propvalue", Convert.ToString(sel_propobj["fdefaultpropvalueid"]));
                                                newPropList.Add(new AuxPropValDTO
                                                {
                                                    PropNo = Convert.ToString(propItem["fnumber"]),
                                                    PropValueNo = Convert.ToString(propValueItem["fname"]),
                                                    PropValueName = Convert.ToString(propValueItem["fname"])
                                                });
                                            }
                                        }
                                        detailItem["fattrinfo_ref"] = ProductUtil.ConvertAuxPropFieldValue(this.Context, Convert.ToString(materialObj["fid"]), newPropList);

                                        //DynamicObject attrinfoObj = null;
                                        //var attrinfoObjs = agentCtx.ExecuteDynamicObject("SELECT  top 1 fid FROM T_SEL_PROP with(nolock) where fname=@fname", new List<SqlParam> { new SqlParam("fname", DbType.String, "定制柜其它定制") });
                                        //if (attrinfoObjs.Count > 0)
                                        //{
                                        //    attrinfoObj = attrinfoObjs[0];
                                        //    detailItem["fattrinfo"] = attrinfoObj["fid"]; //辅助属性
                                        //}

                                        detailItem["fresultbrandid"] = materialObj["fseriesid"]; //业绩品牌
                                        detailItem["funitid"] = materialObj["funitid"]; //基本单位
                                        detailItem["fbizunitid"] = materialObj["fpurunitid"]; //采购单位
                                                                                              //detailItem["fbrandid"] = materialObj["fbrandid"]; //品牌
                                                                                              //detailItem["fseriesid"] = materialObj["fseriesid"]; //系列
                                                                                              //detailItem["fcategoryid"] = materialObj["fcategoryid"]; //商品类别
                                        detailItem["fprice"] = "0"; //采购单价
                                        detailItem["famount"] = "0"; //金额
                                        detailItem["fdistrate"] = "10"; //折扣率 数据库存10，前端显示1（原处理逻辑）
                                        detailItem["fdealprice"] = "0"; //成交单价
                                        detailItem["fdealamount"] = "0"; //成交金额
                                        detailItem["fdistamount"] = "0"; //折扣额
                                        detailItem["fplandate"] = obj["fpickdate"]; //预计交货日期：表头的交货日期
                                        detailItem["fclosestatus_e"] = "0"; //行关闭状态：正常

                                        objDetail.Add(detailItem);

                                        //【三维家门店审核】第一次通知创建采购订单时为FALSE，第二次通知时设置为TRUE。
                                        obj["fswjorderstate"] = 0;  //0:false,1:true

                                        var prepareSaveDataService = agentCtx.Container.GetService<IPrepareSaveDataService>();
                                        prepareSaveDataService.PrepareDataEntity(agentCtx, purchaseorderHtml, new[] { obj }, OperateOption.Create());

                                        var externalContractCode = Convert.ToString(obj["fbillno"]);

                                        var result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { obj }, "draft",
                                            new Dictionary<string, object>());
                                        if (result.IsSuccess)
                                        {
                                            resp.Message = result.ComplexMessage.SuccessMessages.Count > 0 ? result.ComplexMessage.SuccessMessages[0] : "";
                                            resp.Success = true;
                                            resp.Code = 10000;

                                        }
                                        else
                                        {
                                            contractNo = "采购订单保存失败；result：" + JsonConvert.SerializeObject(result.ComplexMessage.ErrorMessages);
                                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，采购订单保存失败】，内容:{4}".Fmt(agentCtx.UserName,
                                                    agentCtx.UserPhone, agentCtx.Company,
                                                    DateTime.Now.ToString("HH:mm:ss"), "result：" + JsonConvert.SerializeObject(result)),
                                                "SWJLogFile");
                                            resp.Message = contractNo;
                                            resp.Success = false;
                                            resp.Code = 10001;
                                        }

                                        Task.Run(() =>
                                        {
                                            //platformOrderId：三维家订单的通用ID，取当前采购订单【三维家订单的通用ID】字段数据回传。
                                            //platformOrderCode：三维家工厂订单编号，取当前采购订单【第三方单号】字段数据回传。
                                            //externalContractCode：外部合同编号(第三方)，取当前采购订单的【单据编号】。
                                            var backinfo = PurchaseOrderAPI.updateOrderExternalContractCode(SWJClient.Instance, platformorderid, platformOrderCode, externalContractCode);

                                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，采购订单回传结果】，内容:{4}".Fmt(this.Context.UserName,
                                                    this.Context.UserPhone, this.Context.Company,
                                                    DateTime.Now.ToString("HH:mm:ss"), "backinfo：" + JsonConvert.SerializeObject(backinfo)),
                                                "SWJLogFile");
                                        });
                                    }
                                    else
                                    {
                                        loger.WriteLogToFile("时间：{0}，操作：【三维家接口，获取数据包失败】，内容:{1}".Fmt(
                                             DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                            "SWJLogFile");
                                        //设置响应数据包
                                        resp.Message = "通过" + item + "未获取到订单数据，请求失败！";
                                        resp.Code = 10001;
                                        resp.Success = false;
                                        //return resp;
                                    }


                                }

                            }
                            catch (Exception ex)
                            {
                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，Error】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                                    "SWJLogFile");
                                resp.Message = "采购订单一次下发出错：" + ex.Message;
                                resp.Success = false;
                                resp.Code = 10001;
                            }
                        }
                        #endregion
                    }
                    break;
                #region 废弃
                //case "AIMES_PLATFORM_QUOTATION"://工厂报价审核
                //    if (dto != null && !string.IsNullOrEmpty(dto.subjectId))
                //    {
                //        try
                //        {
                //            string fctno = dto.subjectId;

                //            var sqlParams = new List<SqlParam>
                //                    {
                //                      new SqlParam("@ffactorybillno", DbType.String, fctno),
                //                    };
                //            string fsaleidsql = "select fid from T_YDJ_ORDERENTRY where ffactorybillid=@ffactorybillno";
                //            var dtr = this.DBService.ExecuteReader(this.Context, fsaleidsql, sqlParams);
                //            if (!dtr.Read())
                //            {
                //                //未查询到销售订单数据
                //                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，销售合同编辑失败】，内容:{4}".Fmt(this.Context.UserName,
                //                        this.Context.UserPhone, this.Context.Company,
                //                     DateTime.Now.ToString("HH:mm:ss"), "未查询到销售订单数据,fctno:" + fctno),
                //                    "SWJLogFile");
                //                resp.Message = "未查询到销售订单数据,fctno:" + fctno;
                //                resp.Success = false;
                //                resp.Code = 10001;
                //                return resp;
                //            }
                //            string fsaleid = dtr.GetString("fid");
                //            var orderObj = this.Context.LoadBizDataById("ydj_order", fsaleid);
                //            var objDetail = orderObj["fentry"] as DynamicObjectCollection;
                //            if (orderObj != null)
                //            {
                //            }
                //            decimal fsumamount = 0;
                //            var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, fctno);

                //            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，下发价格，获取合同数据】，内容:{4}".Fmt(this.Context.UserName,
                //                    this.Context.UserPhone, this.Context.Company,
                //                 DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + fctno + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                //                "SWJLogFile");
                //            if (orderInfo.success)
                //            {
                //                var dataModel = orderInfo.result;
                //                if (dataModel == null)
                //                {
                //                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取合同数据--异常】，内容:{4}".Fmt(this.Context.UserName,
                //                            this.Context.UserPhone, this.Context.Company,
                //                         DateTime.Now.ToString("HH:mm:ss"), "dataModel=null；工厂订单号ID:" + fctno + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                //                        "SWJLogFile");
                //                    resp.Message = "获取订单信息为空，下单失败！";
                //                    resp.Code = 10001;
                //                    resp.Success = false;
                //                }

                //                dataModel.urlParam = !string.IsNullOrEmpty(dataModel.urlParam) ? dataModel.urlParam.UrlDecode() : "";
                //                if (string.IsNullOrWhiteSpace(dataModel.urlParam))
                //                {
                //                    resp.Message = "参数urlParam参数为空，下单失败！";
                //                    resp.Code = 10001;
                //                    resp.Success = false;
                //                }
                //                var urlParam = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(dataModel.urlParam);

                //                if (string.IsNullOrEmpty(Convert.ToString(urlParam["DealerCode"])))
                //                {
                //                    resp.Message = "参数DealerCode未查询到经销商，下单失败！";
                //                    resp.Code = 10001;
                //                    resp.Success = false;
                //                    return resp;
                //                    //异常情况，给个固定值
                //                    //dataModel.dealerCode = "822148199987941388";
                //                }
                //                else
                //                {
                //                    //var agent = this.Context.LoadBizDataByNo("BAS_AGENT", "fnumber", new List<string>() { Convert.ToString(urlParam["DealerCode"]) });

                //                    var bizAgentId = this.Container.GetService<IAgentService>().GetBizAgentIdByNo(this.Context, Convert.ToString(urlParam["DealerCode"]));
                //                    var agent = new List<DynamicObject>() { this.Context.LoadBizDataById("BAS_AGENT", bizAgentId) };

                //                    if (agent == null || agent.Count == 0)
                //                    {
                //                        //兼容二级经销商查询不到的情况
                //                        var sql = $@" select fid id from t_bas_agent with(nolock) where fnumber= '{Convert.ToString(urlParam["DealerCode"])}'";
                //                        agent = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                //                    }
                //                    if (agent != null && agent.Count > 0)
                //                    {
                //                        dataModel.dealerCode = Convert.ToString(agent[0]["id"]);
                //                    }
                //                    else
                //                    {
                //                        resp.Message = "参数DealerCode未查询到经销商，下单失败！";
                //                        resp.Code = 10001;
                //                        resp.Success = false;
                //                        return resp;
                //                        //异常情况，给个固定值
                //                        //dataModel.dealerCode = "822148199987941388";
                //                    }
                //                }
                //                agentCtx = this.Context.CreateAgentDBContext(dataModel.dealerCode);

                //                foreach (var detail in objDetail)
                //                {
                //                    if (Convert.ToString(detail["ffactorybillid"]) == fctno)
                //                    {
                //                        detail["fprice"] = dataModel.saleTotalPrice;//成交价
                //                        detail["fprice"] = dataModel.saleTotalPrice;//成交价
                //                        detail["fdealprice"] = dataModel.saleTotalPrice;//成交价
                //                        fsumamount += Convert.ToDecimal(detail["fqty"]) * dataModel.saleTotalPrice;
                //                        detail["famount"] = Convert.ToDecimal(detail["fqty"]) * dataModel.saleTotalPrice;
                //                        detail["fdealamount"] = Convert.ToDecimal(detail["fqty"]) * dataModel.saleTotalPrice;
                //                    }
                //                    else
                //                    {
                //                        fsumamount += Convert.ToDecimal(detail["famount"]);
                //                    }
                //                }
                //            }
                //            orderObj["fsumamount"] = fsumamount;
                //            orderObj["fdealamount"] = fsumamount;
                //            orderObj["fprofit"] = fsumamount;//毛利
                //            orderObj["funreceived"] = fsumamount;//未收款
                //            orderObj["ffaceamount"] = fsumamount;//货品原值

                //            this.AutoAudit(agentCtx, new[] { orderObj });
                //            resp.Success = true;
                //            resp.Code = 10000;
                //            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，操作成功】，内容:{4}".Fmt(this.Context.UserName,
                //                    this.Context.UserPhone, this.Context.Company,
                //                 DateTime.Now.ToString("HH:mm:ss"), "resp：" + resp.ToJson()),
                //                "SWJLogFile");
                //        }
                //        catch (Exception ex)
                //        {
                //            resp.Message = "三维家接口，二次下发，Error: " + ex.Message;
                //            resp.Success = false;
                //            resp.Code = 10001;
                //            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，Error】，内容:{4}".Fmt(this.Context.UserName,
                //                    this.Context.UserPhone, this.Context.Company,
                //                 DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                //                "SWJLogFile");
                //        }
                //    }
                //    break;
                #endregion
                //2024年停用
                case "AIMES_SHOP_AUDIT"://工厂报价审核
                    if (dto != null && dto.extParam != null &&
                        dto.extParam.subOrderIds.Count > 0)
                    {
                        //获取三维家下发单据类型
                        orderType = GetSWJOrderType(dto, resp, out _urlparam);
                        if (!resp.Success)
                            return resp;

                        if (orderType.IsNullOrEmptyOrWhiteSpace())
                        {
                            //金蝶接收到【operateType】=“AIMES_SHOP_AUDIT”的《订单推送通知》时，需根据【orderType】判断，如果为空，则表示更新《销售合同》
                            try
                            {
                                string fctno = dto.extParam.subOrderIds[0];
                                var sqlParams = new List<SqlParam>
                                    {
                                      new SqlParam("@ffactorybillno", DbType.String, fctno),
                                    };
                                string fsaleidsql = "select top 1 a.fid from T_YDJ_ORDER as a with(nolock) inner join T_YDJ_ORDERENTRY as b with(nolock) on a.fid=b.fid where ffactorybillid=@ffactorybillno order by fcreatedate desc";
                                var dtr = this.DBService.ExecuteReader(this.Context, fsaleidsql, sqlParams);
                                if (!dtr.Read())
                                {
                                    //未查询到销售订单数据
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，销售合同编辑失败】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "未查询到销售订单数据,fctno:" + fctno),
                                        "SWJLogFile");
                                    return this.OrderResult(resp, dto, "未查询到销售订单数据！", fctno, false);
                                }
                                string fsaleid = dtr.GetString("fid");
                                var orderObj = this.Context.LoadBizDataById("ydj_order", fsaleid);
                                orderObj["fswjorderstate"] = 1;
                                var objDetail = orderObj["fentry"] as DynamicObjectCollection;
                                List<string> editList = new List<string>();
                                //decimal fsumamount = 0;
                                bool ischange = false;
                                foreach (var item in dto.extParam.subOrderIds)
                                {
                                    var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, item);
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，下发价格，获取合同数据】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                        "SWJLogFile");
                                    if (orderInfo.success)
                                    {
                                        var dataModel = orderInfo.result;
                                        if (dataModel == null)
                                        {
                                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取合同数据--异常】，内容:{4}".Fmt(this.Context.UserName,
                                                    this.Context.UserPhone, this.Context.Company,
                                                 DateTime.Now.ToString("HH:mm:ss"), "dataModel=null；工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                                "SWJLogFile");
                                            return this.OrderResult(resp, dto, "获取订单信息为空，下单失败！", item, false);
                                        }

                                        if (Convert.ToBoolean(orderObj["fomsservice"]) == true)
                                        {
                                            orderCode = dataModel.shopOrderCode;//关联订单
                                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，Error】，内容:{4}".Fmt(this.Context.UserName,
                                                    this.Context.UserPhone, this.Context.Company,
                                                 DateTime.Now.ToString("HH:mm:ss"), "联单号为 " + orderCode + " 的订单已由定制OMS中台审核，请联系总部处理!"),
                                                "SWJLogFile");
                                            return this.OrderResult(resp, dto, "对不起，三维家单号为 " + orderCode + " 的订单已由定制OMS中台审核，请联系总部处理！", item, false);
                                        }
                                        //dataModel.urlParam = !string.IsNullOrEmpty(dataModel.urlParam) ? dataModel.urlParam.UrlDecode() : "{\"ClientName\":\"三维家测试\",\"customerId\":\"KH100028200011184\",\"SalesMan\":\"朱成兰\",\"SalesManID\":\"826889392940519546\",\"ClientModelName\":\"江西省南昌市青山湖区解放西路101号\",\"DealerCode\":\"1000282\"}";
                                        dataModel.urlParam = !string.IsNullOrEmpty(dataModel.urlParam) ? dataModel.urlParam.UrlDecode() : "";
                                        if (string.IsNullOrWhiteSpace(dataModel.urlParam))
                                        {
                                            return this.OrderResult(resp, dto, "参数urlParam参数为空，下单失败！", item, false);
                                        }
                                        var urlParam = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(dataModel.urlParam);
                                        if (string.IsNullOrEmpty(Convert.ToString(urlParam["DealerCode"])))
                                        {
                                            return this.OrderResult(resp, dto, "参数DealerCode未查询到经销商，下单失败！", item, false);
                                        }
                                        else
                                        {
                                            string dealerCode = Convert.ToString(urlParam["DealerCode"]);
                                            if (dealerCode.IndexOf(" ") > 0)
                                            {
                                                dealerCode = (dealerCode.Split(' ')[0]).Trim();
                                            }
                                            else if (dealerCode.IndexOf("+") > 0)
                                            {
                                                dealerCode = (dealerCode.Split('+')[0]).Trim(); ;
                                            }
                                            // 获取业务经销商
                                            var bizAgentId = this.Container.GetService<IAgentService>().GetBizAgentIdByNo(this.Context, dealerCode);
                                            var agent = new List<DynamicObject>() { this.Context.LoadBizDataById("BAS_AGENT", bizAgentId) };
                                            if (agent == null || agent.Count == 0)
                                            {
                                                //兼容二级经销商查询不到的情况
                                                var sql = $@" select fid id from t_bas_agent with(nolock) where fnumber= '{dealerCode}'";
                                                agent = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                                            }
                                            if (agent != null && agent.Count > 0 && agent[0] != null)
                                            {
                                                dataModel.dealerCode = Convert.ToString(agent[0]["id"]);
                                            }
                                            else
                                            {
                                                return this.OrderResult(resp, dto, "参数DealerCode未查询到经销商，下单失败！", item, false);
                                            }
                                        }
                                        agentCtx = this.Context.CreateAgentDBContext(dataModel.dealerCode);
                                        decimal price = 0;
                                        // Convert.ToString(dataModel.orderTypeDictVal) == "售后单" ? dataModel.productTotalPrice : dataModel.saleTotalPrice;
                                        if (Convert.ToString(dataModel.orderTypeDictVal).Equals("售后单"))
                                        {
                                            if (dataModel.productTotalPrice > 0)
                                            {
                                                price = dataModel.productTotalPrice * 3;
                                            }
                                            else price = 1;
                                        }
                                        else price = dataModel.saleTotalPrice;
                                        foreach (var detail in objDetail)
                                        {
                                            if (Convert.ToString(detail["ffactorybillid"]) == item)
                                            {
                                                editList.Add(item);
                                                decimal oldprice = Convert.ToDecimal(detail["fprice"]);
                                                if (price.Equals(oldprice))
                                                {
                                                    continue;
                                                }
                                                ischange = true;
                                                decimal qty = Convert.ToDecimal(detail["fbizqty"]);
                                                //业务要求，重置当前折率
                                                decimal distrate = 10;
                                                decimal amount = qty * price;

                                                // 成交单价=零售价*折率/10
                                                decimal dealprice = decimal.Round(price * distrate / 10, 6);
                                                // 成交金额=成交单价*数量
                                                decimal dealamount = dealprice * qty;
                                                // 折扣额=金额-成交金额
                                                decimal distamount = amount - dealamount;

                                                detail["fprice"] = price;
                                                detail["famount"] = amount;
                                                detail["fdistamount"] = distamount;
                                                detail["fdealamount"] = dealamount;
                                                detail["fdealprice"] = dealprice;

                                                //detail["fprice"] = price;//成交价
                                                //detail["fdealprice"] = price;//成交价
                                                //detail["fdealprice"] = price;//成交价
                                                //fsumamount += Convert.ToDecimal(detail["fqty"]) * price;
                                                //detail["famount"] = Convert.ToDecimal(detail["fqty"]) * price;
                                                //detail["fdealamount"] = Convert.ToDecimal(detail["fqty"]) * price;
                                            }
                                        }
                                    }
                                }
                                if (ischange)
                                {
                                    //重置整单折
                                    orderObj["fiswholedis"] = 0;
                                    orderObj["fdiscscale"] = 0;

                                    //重置抹尾差
                                    orderObj["ffavoropt"] = "";
                                    orderObj["fisremovetail"] = 0;

                                    //重置一口价
                                    orderObj["fisfixedprice"] = 0;
                                    orderObj["ffixedprice"] = 0;
                                    var orderFormMate = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
                                    var orderService = this.Container.GetService<IOrderService>();
                                    orderService.CalculateSettlement(agentCtx, orderObj, orderFormMate);
                                    orderService.CalculateUnreceived(agentCtx, new[] { orderObj });
                                    orderService.CalculateReceiptStatus(agentCtx, new[] { orderObj });
                                }

                                var result1 = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", new[] { orderObj }, "save",
                                    new Dictionary<string, object>());

                                if (result1.IsSuccess)
                                {
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，销售合同保存结果】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                            DateTime.Now.ToString("HH:mm:ss"), "result1:" + result1.ToJson()),
                                        "SWJLogFile");
                                    // 当接收到三维家《销售合同》二次通知时，取消自动审核订单逻辑
                                    //this.AutoAudit(agentCtx, new[] { orderObj });
                                    this.OrderResult(resp, dto, resp.Message, dto.extParam.subOrderIds, true);
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，操作成功】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                            DateTime.Now.ToString("HH:mm:ss"), "resp：" + resp.ToJson()),
                                        "SWJLogFile");
                                }
                                else
                                {
                                    contractNo = "二次下发，销售合同保存失败；result：" + JsonConvert.SerializeObject(result1.ComplexMessage.ErrorMessages);
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，销售合同保存失败】，内容:{4}".Fmt(agentCtx.UserName,
                                            agentCtx.UserPhone, agentCtx.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "result：" + JsonConvert.SerializeObject(result1)),
                                        "SWJLogFile");
                                    this.OrderResult(resp, dto, contractNo, dto.extParam.subOrderIds, false);
                                }
                            }
                            catch (Exception ex)
                            {
                                resp.Message = "二次下发，销售合同审核异常；" + ex.Message;
                                resp.Success = false;
                                resp.Code = 10001;
                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，Error】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                                    "SWJLogFile");
                                return this.OrderResult(resp, dto, "二次下发，销售合同审核异常；" + ex.Message, string.Join(",", dto.extParam.subOrderIds), false);
                            }
                        }
                        else if (orderType.Equals("1") || orderType.Equals("2"))
                        {
                            //如果不为空，则表示需要更新对应《采购订单》商品行的【销售单价】字段，并需要设置【三维家门店审核】=TRUE。
                            try
                            {
                                List<string> subOrderIds = dto.extParam.subOrderIds;

                                foreach (var item in subOrderIds)
                                {
                                    // todo：获取3维家合同信息
                                    var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, item);

                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取合同数据--Step1】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                            DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                        "SWJLogFile");
                                    if (orderInfo.success)
                                    {
                                        var dataModel = orderInfo.result;
                                        var platformOrderCode = dataModel.platformOrderCode;

                                        //需根据表头的【第三方单号】查询是否已经存在采购订单
                                        DynamicObject purchaseorderObj = null;
                                        var purchaseorderObjs = agentCtx.ExecuteDynamicObject("select top 1 fid,fthirdbillno,fhqderstatus from T_YDJ_PURCHASEORDER with(nolock) where fthirdbillno=@fthirdbillno", new List<SqlParam> { new SqlParam("fthirdbillno", DbType.String, platformOrderCode) });

                                        if (purchaseorderObjs.Count > 0) //已存在
                                        {
                                            purchaseorderObj = purchaseorderObjs[0];

                                            var purObj = agentCtx.LoadBizDataById("ydj_purchaseorder", Convert.ToString(purchaseorderObj["fid"]));

                                            if (!purObj.IsNullOrEmptyOrWhiteSpace())
                                            {
                                                var entrys = purObj["fentity"] as DynamicObjectCollection;
                                                foreach (var entry in entrys) //正常情况下三维家下发采购订单只有一行商品明细
                                                {
                                                    entry["fsalprice"] = dataModel.saleTotalPrice;
                                                }

                                                //【三维家门店审核】第一次通知创建采购订单时为FALSE，第二次通知时设置为TRUE。
                                                purObj["fswjorderstate"] = 1;  //0:false,1:true

                                                var result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_purchaseorder", new[] { purObj }, "draft",
                                                    new Dictionary<string, object>());

                                                if (result.IsSuccess)
                                                {
                                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，采购订单保存结果】，内容:{4}".Fmt(this.Context.UserName,
                                                            this.Context.UserPhone, this.Context.Company,
                                                            DateTime.Now.ToString("HH:mm:ss"), "result:" + result.ToJson()),
                                                        "SWJLogFile");

                                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，采购订单保存操作成功】，内容:{4}".Fmt(this.Context.UserName,
                                                            this.Context.UserPhone, this.Context.Company,
                                                            DateTime.Now.ToString("HH:mm:ss"), "resp：" + resp.ToJson()),
                                                        "SWJLogFile");

                                                    resp.Message = result.ComplexMessage.SuccessMessages.Count > 0 ? result.ComplexMessage.SuccessMessages[0] : "";
                                                    resp.Success = true;
                                                    resp.Code = 10000;

                                                }
                                                else
                                                {
                                                    contractNo = "二次下发，采购订单保存失败；result：" + JsonConvert.SerializeObject(result.ComplexMessage.ErrorMessages);
                                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，采购订单保存失败】，内容:{4}".Fmt(agentCtx.UserName,
                                                            agentCtx.UserPhone, agentCtx.Company,
                                                            DateTime.Now.ToString("HH:mm:ss"), "result：" + JsonConvert.SerializeObject(result)),
                                                        "SWJLogFile");
                                                    resp.Message = contractNo;
                                                    resp.Success = false;
                                                    resp.Code = 10001;
                                                }


                                            }
                                            else
                                            {
                                                //未查询到采购订单数据
                                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，采购订单编辑失败】，内容:{4}".Fmt(this.Context.UserName,
                                                        this.Context.UserPhone, this.Context.Company,
                                                        DateTime.Now.ToString("HH:mm:ss"), "未查询到采购订单数据,fctno:" + item),
                                                    "SWJLogFile");
                                                resp.Message = "未查询到采购订单数据,subOrderId:" + item;
                                                resp.Success = false;
                                                resp.Code = 10001;
                                                return resp;
                                            }
                                        }
                                        else
                                        {
                                            //未查询到采购订单数据
                                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，采购订单编辑失败】，内容:{4}".Fmt(this.Context.UserName,
                                                    this.Context.UserPhone, this.Context.Company,
                                                    DateTime.Now.ToString("HH:mm:ss"), "未查询到销售订单数据,fctno:" + item),
                                                "SWJLogFile");
                                            resp.Message = "未查询到采购订单数据,subOrderId:" + item;
                                            resp.Success = false;
                                            resp.Code = 10001;
                                            return resp;
                                        }
                                    }
                                    else
                                    {
                                        loger.WriteLogToFile("时间：{0}，操作：【三维家接口，获取数据包失败】，内容:{1}".Fmt(
                                             DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                            "SWJLogFile");
                                        //设置响应数据包
                                        resp.Message = "通过" + item + "未获取到订单数据，请求失败！";
                                        resp.Code = 10001;
                                        resp.Success = false;
                                        //return resp;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，Error】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                                    "SWJLogFile");
                                this.OrderResult(resp, dto, "二次下发，采购订单审核异常；" + ex.Message, dto.subjectId, false);
                            }
                        }
                    }
                    break;

                case "AIMES_AFTERSALESORDER_SPLIT_SUBMIT"://售后单保存调用
                    if (dto != null && !string.IsNullOrWhiteSpace(dto.subjectId) && dto.extParam.Source != null && (dto.extParam.Source.Contains("金蝶") || dto.extParam.Source.Contains("杰诺")))
                    {
                        try
                        {
                            string fctno = dto.subjectId;
                            var sqlParams = new List<SqlParam>
                                    {
                                      new SqlParam("@ffactorybillno", DbType.String, fctno),
                                    };
                            string fsaleidsql = "select top 1 a.fid from T_YDJ_ORDER as a with(nolock) inner join T_YDJ_ORDERENTRY as b with(nolock) on a.fid=b.fid where ffactorybillid=@ffactorybillno order by fcreatedate desc";
                            var dtr = this.DBService.ExecuteReader(this.Context, fsaleidsql, sqlParams);
                            if (!dtr.Read())
                            {
                                //未查询到销售订单数据
                                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，售后单保存调用，销售合同编辑失败】，内容:{4}".Fmt(this.Context.UserName,
                                        this.Context.UserPhone, this.Context.Company,
                                     DateTime.Now.ToString("HH:mm:ss"), "未查询到销售订单数据,fctno:" + fctno),
                                    "SWJLogFile");
                                return this.OrderResult(resp, dto, "未查询到销售订单数据,subOrderId:" + fctno, fctno, false);
                            }
                            string fsaleid = dtr.GetString("fid");
                            var orderObj = this.Context.LoadBizDataById("ydj_order", fsaleid);
                            var objDetail = orderObj["fentry"] as DynamicObjectCollection;
                            List<string> editList = new List<string>();
                            //decimal fsumamount = 0;
                            bool ischange = false;
                            string item = dto.subjectId;
                            var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, item);
                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，售后单保存调用，获取合同数据】，内容:{4}".Fmt(this.Context.UserName,
                                    this.Context.UserPhone, this.Context.Company,
                                 DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                "SWJLogFile");
                            if (orderInfo.success)
                            {
                                var dataModel = orderInfo.result;
                                if (dataModel == null)
                                {
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，售后单保存调用，获取合同数据--异常】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "dataModel=null；工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                        "SWJLogFile");
                                    return this.OrderResult(resp, dto, "获取订单信息为空，下单失败！", item, false);
                                }
                                dataModel.urlParam = !string.IsNullOrEmpty(dataModel.urlParam) ? dataModel.urlParam.UrlDecode() : "";
                                if (string.IsNullOrWhiteSpace(dataModel.urlParam))
                                {
                                    return this.OrderResult(resp, dto, "参数urlParam参数为空，下单失败！", item, false);
                                }
                                var urlParam = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(dataModel.urlParam);
                                if (string.IsNullOrEmpty(Convert.ToString(urlParam["DealerCode"])))
                                {
                                    return this.OrderResult(resp, dto, "参数DealerCode未查询到经销商，下单失败！", item, false);
                                }
                                else
                                {
                                    string dealerCode = Convert.ToString(urlParam["DealerCode"]);
                                    if (dealerCode.IndexOf(" ") > 0)
                                    {
                                        dealerCode = (dealerCode.Split(' ')[0]).Trim();
                                    }
                                    else if (dealerCode.IndexOf("+") > 0)
                                    {
                                        dealerCode = (dealerCode.Split('+')[0]).Trim();
                                    }
                                    // 获取业务经销商
                                    var bizAgentId = this.Container.GetService<IAgentService>().GetBizAgentIdByNo(this.Context, dealerCode);
                                    var agent = new List<DynamicObject>() { this.Context.LoadBizDataById("BAS_AGENT", bizAgentId) };
                                    if (agent == null || agent.Count == 0)
                                    {
                                        //兼容二级经销商查询不到的情况
                                        var sql = $@" select fid id from t_bas_agent with(nolock) where fnumber= '{dealerCode}'";
                                        agent = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                                    }
                                    if (agent != null && agent.Count > 0 && agent[0] != null)
                                    {
                                        dataModel.dealerCode = Convert.ToString(agent[0]["id"]);
                                    }
                                    else
                                    {
                                        return this.OrderResult(resp, dto, "参数DealerCode未查询到经销商，下单失败！", item, false);
                                    }
                                }
                                agentCtx = this.Context.CreateAgentDBContext(dataModel.dealerCode);
                                foreach (var detail in objDetail)
                                {
                                    if (Convert.ToString(detail["ffactorybillid"]) == item)
                                    {
                                        ischange = true;
                                        detail["fordertype"] = dataModel.moduleName;  //订单品类
                                        detail["fomsoptionid"] = dataModel.platformOrderschemeId;  //方案id
                                        detail["fomsdesignfile"] = dataModel.schemeContent;  //fomsdesignfile
                                        detail["fomscategory"] = dataModel.unitCategoryName;  //商品品类


                                        if (!string.IsNullOrWhiteSpace(dataModel.schemePreviewImage))
                                        {
                                            string fileName = dataModel.schemePreviewImage.Substring(dataModel.schemePreviewImage.LastIndexOf("/") + 1, dataModel.schemePreviewImage.Length - dataModel.schemePreviewImage.LastIndexOf("/") - 1);
                                            var fileresult = PostFile(dataModel.schemePreviewImage, fileName);
                                            if (Convert.ToBoolean(fileresult["isSuccess"]))
                                            {
                                                detail["fomsfiles"] = fileresult["fileId"];  //缩略图
                                            }
                                        }

                                        detail["fomstexture"] = dataModel.material;  //主材质
                                        detail["fomscolor"] = dataModel.basicMaterial;  //主颜色
                                    }
                                }
                                var result1 = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", new[] { orderObj }, "save",
                                    new Dictionary<string, object>());

                                if (result1.IsSuccess)
                                {
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，售后单保存调用，销售合同保存结果】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                            DateTime.Now.ToString("HH:mm:ss"), "result1:" + result1.ToJson()),
                                        "SWJLogFile");
                                    resp.Message = result1.ComplexMessage.SuccessMessages.Count > 0 ? result1.ComplexMessage.SuccessMessages[0] : "";
                                    this.OrderResult(resp, dto, resp.Message, dto.subjectId, true);
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，售后单保存调用，操作成功】，内容:{4}".Fmt(this.Context.UserName,
                                            this.Context.UserPhone, this.Context.Company,
                                            DateTime.Now.ToString("HH:mm:ss"), "resp：" + resp.ToJson()),
                                        "SWJLogFile");
                                }
                                else
                                {
                                    contractNo = "售后单保存调用，销售合同保存失败；result：" + JsonConvert.SerializeObject(result1.ComplexMessage.ErrorMessages);
                                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，售后单保存调用，销售合同保存失败】，内容:{4}".Fmt(agentCtx.UserName,
                                            agentCtx.UserPhone, agentCtx.Company,
                                         DateTime.Now.ToString("HH:mm:ss"), "result：" + JsonConvert.SerializeObject(result1)),
                                        "SWJLogFile");
                                    return this.OrderResult(resp, dto, contractNo, dto.subjectId, false);
                                }
                            }
                            else
                            {
                                contractNo = "通过" + item + "未获取到订单数据，请求失败！";
                                loger.WriteLogToFile("时间：{0}，操作：【三维家接口，售后单保存调用，获取数据包失败】，内容:{1}".Fmt(
                                     DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                                    "SWJLogFile");
                                //设置响应数据包
                                return this.OrderResult(resp, dto, contractNo, item, false);
                            }
                        }
                        catch (Exception ex)
                        {
                            resp.Message = "二次下发，售后单保存调用；" + ex.Message;
                            resp.Success = false;
                            resp.Code = 10001;
                            loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，售后单保存调用，Error】，内容:{4}".Fmt(this.Context.UserName,
                                    this.Context.UserPhone, this.Context.Company,
                                 DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                                "SWJLogFile");
                            return this.OrderResult(resp, dto, "二次下发，售后单保存调用；" + ex.Message, dto.subjectId, false);
                        }
                    }
                    else
                    {
                        return this.OrderResult(resp, dto, "入参异常，请检查下发的数据！", "", false);
                    }
                    break;
            }
            return resp;
        }

        private object OrderResult(BaseResponse<object> resp, OrderStatusDTO dto, string message, string billno, bool flag)
        {
            resp.Success = flag;
            dto.RecordBillNo = billno;
            resp.Message = message;
            resp.Code = flag ? 10000 : 10001;
            return resp;
        }
        private object OrderResult(BaseResponse<object> resp, OrderStatusDTO dto, string message, List<string> billnos, bool flag)
        {
            resp.Success = flag;
            dto.RecordBillNo = string.Join(",", billnos);
            resp.Message = message;
            resp.Code = flag ? 10000 : 10001;
            return resp;
        }

        private DynamicObject GetAttachmentList(DynamicObject orderObj)
        {
            var attachFormMeta = this.MetaModelService.LoadFormModel(this.Context, "bd_attachlist");
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, attachFormMeta.GetDynamicObjectType(this.Context));
            var pkIdReader = this.Context.GetPkIdDataReader(attachFormMeta, "flinkformid=@linkFormId and flinkbillinterid=@linkBillId", new SqlParam[]
            {
                new SqlParam("linkFormId", System.Data.DbType.String, "ydj_order"),
                new SqlParam("linkBillId", System.Data.DbType.String, orderObj["id"]),
            });

            var linkAttachBillObj = dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>()
                .FirstOrDefault();
            if (linkAttachBillObj == null) return null;

            //var entrys = (DynamicObjectCollection)linkAttachBillObj["fdrawentity"];
            var entrys = linkAttachBillObj["fdrawentity"] as DynamicObjectCollection;
            var copyItem = linkAttachBillObj.Clone() as DynamicObject;

            entrys.Clear();
            //linkAttachBillObj["fdrawentity"]. = entrys.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["ffilegrouping"])));
            var isnull = (copyItem["fdrawentity"] as DynamicObjectCollection).Where(a => string.IsNullOrWhiteSpace(Convert.ToString(a["ffilegrouping"])));
            foreach (var item in isnull)
            {
                entrys.Add(item);
            }
            return linkAttachBillObj;
        }

        /// <summary>
        /// 保存前校验
        /// </summary>
        /// <param name="resp"></param>
        /// <param name="dataModel"></param>
        /// <param name="urlParam"></param>
        /// <returns></returns>
        private object Validation(BaseResponse<object> resp, ShopOrderInfoResult dataModel, out Dictionary<string, object> urlParam, out string contractNo, out string BMID)
        {
            contractNo = "";
            BMID = "";
            if (dataModel == null)
            {
                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取合同数据--异常】，内容:{4}".Fmt(this.Context.UserName,
                        this.Context.UserPhone, this.Context.Company,
                     DateTime.Now.ToString("HH:mm:ss"), "dataModel=null；"),
                    "SWJLogFile");
                resp.Message = "获取订单信息为空，下单失败！";
                contractNo = resp.Message;
                resp.Code = 10001;
                resp.Success = false;
            }
            //测试参数：{\"ClientName\":\"三维家测试\",\"customerId\":\"KH100028200011184\",\"SalesMan\":\"朱成兰\",\"SalesManID\":\"826889392940519546\",\"ClientModelName\":\"江西省南昌市青山湖区解放西路101号\",\"DealerCode\":\"1000282\"}
            dataModel.urlParam = !string.IsNullOrEmpty(dataModel.urlParam) ? dataModel.urlParam.UrlDecode() : "";
            if (string.IsNullOrWhiteSpace(dataModel.urlParam))
            {
                resp.Message = "参数urlParam参数为空，下单失败！";
                contractNo = resp.Message;
                resp.Code = 10001;
                resp.Success = false;
            }
            urlParam = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, object>>(dataModel.urlParam);
            if (!urlParam.ContainsKey("DealerCode"))
            {
                resp.Message = "参数urlParam 不包含DealerCode（经销商编码）字段，下单失败！";
                contractNo = resp.Message;
                resp.Code = 10001;
                resp.Success = false;
                return resp;
            }
            string dealerCode = Convert.ToString(urlParam["DealerCode"]);
            if (dealerCode.IndexOf(" ") > 0)
            {
                if (dealerCode.Split(' ').Length > 1)
                {
                    BMID = dealerCode.Split(' ')[1];
                }
                dealerCode = (dealerCode.Split(' ')[0]).Trim();
            }
            else if (dealerCode.IndexOf("+") > 0)
            {
                if (dealerCode.Split('+').Length > 1)
                {
                    BMID = dealerCode.Split('+')[1];
                }
                dealerCode = (dealerCode.Split('+')[0]).Trim();
            }
            if (string.IsNullOrEmpty(dealerCode))
            {
                resp.Message = "参数DealerCode未查询到经销商，下单失败！";
                contractNo = resp.Message;
                resp.Code = 10001;
                resp.Success = false;
                return resp;
                //异常情况，给个固定值
                //dataModel.dealerCode = "822148199987941388";
            }
            else
            {
                //var agent = this.Context.LoadBizDataByNo("BAS_AGENT", "fnumber", new List<string>() { Convert.ToString(urlParam["DealerCode"]) });
                var bizAgentId = this.Container.GetService<IAgentService>().GetBizAgentIdByNo(this.Context, dealerCode);
                var agent = new List<DynamicObject>() { this.Context.LoadBizDataById("BAS_AGENT", bizAgentId) };

                if (agent == null || agent.Count == 0)
                {
                    //兼容二级经销商查询不到的情况
                    var sql = $@" select fid id from t_bas_agent with(nolock) where fnumber= '{dealerCode}'";
                    agent = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
                }
                if (agent != null && agent.Count > 0)
                {
                    dataModel.dealerCode = Convert.ToString(agent[0]["id"]);
                }
                else
                {
                    resp.Message = "参数DealerCode未查询到经销商，下单失败！";
                    contractNo = resp.Message;
                    resp.Code = 10001;
                    resp.Success = false;
                    return resp;
                    //异常情况，给个固定值
                    //dataModel.dealerCode = "822148199987941388";
                }
            }
            //dataModel.dealerCode = !string.IsNullOrEmpty(Convert.ToString(urlParam["DealerCode"])) ? Convert.ToString(urlParam["DealerCode"]) : "822148199987941388";
            //dataModel.dealerCode = !string.IsNullOrEmpty(dataModel.dealerCode) ? dataModel.dealerCode : "822148199987941388";
            agentCtx = this.Context.CreateAgentDBContext(dataModel.dealerCode);
            if (string.IsNullOrWhiteSpace(agentCtx.TokenId))
            {
                resp.Message = "经销商编码不符合规范！";
                contractNo = resp.Message;
                resp.Code = 10001;
                resp.Success = false;
                return resp;
            }
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 自动提交审核
        /// </summary>
        /// <param name="dataEntities"></param>
        private void AutoAudit(UserContext agentCtx, DynamicObject[] dataEntities)
        {
            try
            {
                if (dataEntities == null || dataEntities.Length == 0) return;

                var noSubmitDataEntities = dataEntities.Where(s =>
                    Convert.ToString(s["fstatus"]).EqualsIgnoreCase("B") ||
                    Convert.ToString(s["fstatus"]).EqualsIgnoreCase("C"));

                // 自动提交
                if (noSubmitDataEntities.Any())
                {
                    var result = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", noSubmitDataEntities, "Submit",
                        new Dictionary<string, object>());
                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，销售合同提交结果】，内容:{4}".Fmt(this.Context.UserName,
                            this.Context.UserPhone, this.Context.Company,
                         DateTime.Now.ToString("HH:mm:ss"), "result:" + result.ToJson()),
                        "SWJLogFile");

                    var resultAudit = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", noSubmitDataEntities, "Audit",
                        new Dictionary<string, object>());
                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，销售合同审核结果】，内容:{4}".Fmt(this.Context.UserName,
                            this.Context.UserPhone, this.Context.Company,
                         DateTime.Now.ToString("HH:mm:ss"), "result:" + resultAudit.ToJson()),
                        "SWJLogFile");
                }
                noSubmitDataEntities = dataEntities.Where(s =>
                    Convert.ToString(s["fstatus"]).EqualsIgnoreCase("D"));

                // 自动提交
                if (noSubmitDataEntities.Any())
                {
                    var resultAudit = this.HttpGateway.InvokeBillOperation(agentCtx, "ydj_order", noSubmitDataEntities, "Audit",
                        new Dictionary<string, object>());
                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发，销售合同审核结果】，内容:{4}".Fmt(this.Context.UserName,
                            this.Context.UserPhone, this.Context.Company,
                         DateTime.Now.ToString("HH:mm:ss"), "result:" + resultAudit.ToJson()),
                        "SWJLogFile");
                }
            }
            catch (Exception ex)
            {
                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，二次下发-自动提交审核，Error】，内容:{4}".Fmt(this.Context.UserName,
                        this.Context.UserPhone, this.Context.Company,
                     DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                    "SWJLogFile");
            }
        }


        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="interAddress">在线地址</param>
        /// <param name="fileName">客户端保存的文件名</param>
        /// <param name="filePath">保存的文件夹路径</param>
        /// <returns></returns>
        public bool FileLoad(string interAddress, string fileName, string filePath)
        {
            try
            {
                //判断保存的文件夹是否存在
                if (!Directory.Exists(filePath))
                {
                    //不存在则创建
                    Directory.CreateDirectory(filePath);
                }
                //System.Net 中的验证和下载方法
                WebClient client = new WebClient();
                client.Credentials = CredentialCache.DefaultCredentials;
                client.DownloadFile(interAddress, filePath + "\\" + fileName);
                return true;
            }
            catch (Exception ex)
            {
                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，下载文件，Error】，内容:{4}".Fmt(this.Context.UserName,
                        this.Context.UserPhone, this.Context.Company,
                     DateTime.Now.ToString("HH:mm:ss"), "interAddress：【" + interAddress + "】filePath【" + filePath + "】fileName【" + fileName + "】ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                    "SWJLogFile");
            }
            return false;
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="filesize">文件传入大小</param>
        /// <returns></returns>
        private static string GetFileSize(long filesize)
        {
            try
            {
                if (filesize < 0)
                {
                    return "0";
                }
                else if (filesize >= 1024 * 1024 * 1024)  //文件大小大于或等于1024MB    
                {
                    return string.Format("{0:0.00} GB", (double)filesize / (1024 * 1024 * 1024));
                }
                else if (filesize >= 1024 * 1024) //文件大小大于或等于1024KB    
                {
                    return string.Format("{0:0.00} MB", (double)filesize / (1024 * 1024));
                }
                else if (filesize >= 1024) //文件大小大于等于1024bytes    
                {
                    return string.Format("{0:0.00} KB", (double)filesize / 1024);
                }
                else
                {
                    return string.Format("{0:0.00} bytes", filesize);
                }
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        /// <summary>
        /// 将二进制文件保存到磁盘
        /// </summary>
        /// <param name="response"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private static bool SaveBinaryFile(WebResponse response, string fileName)
        {
            bool Value = true;
            byte[] buffer = new byte[1024];

            try
            {
                if (File.Exists(fileName)) File.Delete(fileName);

                Stream outStream = File.Create(fileName);
                Stream inStream = response.GetResponseStream();

                int l;
                do
                {
                    l = inStream.Read(buffer, 0, buffer.Length);
                    if (l > 0)
                        outStream.Write(buffer, 0, l);
                }
                while (l > 0);

                outStream.Close();
                inStream.Close();
            }
            catch (Exception ex)
            {
                Value = false;
            }
            return Value;
        }


        /// <summary>
        /// 获取三维家下发单据类型
        /// </summary>
        /// <param name="dto">订单推送通知接口参数数据</param>
        /// <param name="resp"></param>
        /// <returns></returns>
        public string GetSWJOrderType(OrderStatusDTO dto, BaseResponse<object> resp, out Dictionary<string, object> urlParam)
        {
            string orderType = string.Empty;
            urlParam = new Dictionary<string, object>();
            try
            {
                List<string> subOrderIds = dto.extParam.subOrderIds;

                foreach (var item in subOrderIds)
                {
                    // todo：获取3维家合同信息
                    var orderInfo = new OrderAPI(this.Context).GetShopOrderInfo(SWJClient.Instance, item);

                    loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，获取合同数据第一个subOrderId的订单数据--Step0】，内容:{4}".Fmt(this.Context.UserName,
                            this.Context.UserPhone, this.Context.Company,
                         DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                        "SWJLogFile");
                    if (orderInfo.success)
                    {
                        string contractNo = "";

                        var dataModel = orderInfo.result;
                        Validation(resp, dataModel, out urlParam, out contractNo, out string BMID);
                        if (!resp.Success)
                            return orderType;

                        if (urlParam.ContainsKey("OrderType"))
                            orderType = Convert.ToString(urlParam["OrderType"]);
                        else
                            orderType = string.Empty;
                        if (urlParam.ContainsKey("Source"))
                        {
                            string source = Convert.ToString(urlParam["Source"]);
                            if (!source.Equals("金蝶") && !source.Equals("杰诺"))
                            {
                                resp.Message = "当前工厂订单号来源非杰诺！";
                                resp.Success = false;
                                return orderType;
                            }
                        }
                        else
                        {
                            resp.Message = "当前工厂订单号来源非杰诺！";
                            resp.Success = false;
                            return orderType;
                        }
                    }
                    else
                    {
                        loger.WriteLogToFile("时间：{0}，操作：【三维家接口，获取数据包失败】，内容:{1}".Fmt(
                             DateTime.Now.ToString("HH:mm:ss"), "工厂订单号ID:" + item + "；数据包：" + JsonConvert.SerializeObject(orderInfo)),
                            "SWJLogFile");
                        //设置响应数据包
                        resp.Message = "通过" + item + "未获取到订单数据，请求失败！";
                        resp.Code = 10001;
                        resp.Success = false;
                    }

                    //只获取订单推送通知接口subOrderIds中第一个工厂订单号（即第一行商品）对应的获取订单信息中的urlParam中的orderType参数
                    break;
                }

            }
            catch (Exception ex)
            {
                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，Error】，内容:{4}".Fmt(this.Context.UserName,
                        this.Context.UserPhone, this.Context.Company,
                     DateTime.Now.ToString("HH:mm:ss"), "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                    "SWJLogFile");
                resp.Message = "获取三维家下发单据类型出错: " + ex.Message;
                resp.Success = false;
                resp.Code = 10001;
            }

            return orderType;
        }

        /// <summary>
        /// 上传文件服务器
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="filePath"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static Dictionary<string, string> UploadFile(UserContext userCtx, string filePath, string fileName)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", $"文件路径不能为空!"}
                };
            }
            if (filePath.Contains("/"))
            {
                filePath = filePath.Replace('/', '\\');
            }
            if (filePath.StartsWith("\\"))
            {
                filePath = filePath.Substring(1);
            }
            var startupPath = PathUtils.GetStartupPath();
            filePath = Path.Combine(startupPath, filePath);
            if (File.Exists(filePath) == false)
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", $"不存在文件路径:{filePath}"}
                };
            }
            var fileServerInfo = userCtx.GetFirstWorkFileServer();
            if (fileServerInfo == null)
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器信息"}
                };
            }
            string baseUrl = fileServerInfo.Url;
            if (string.IsNullOrWhiteSpace(baseUrl))
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器的地址信息"}
                };
            }
            if (baseUrl.EndsWith("/"))
            {
                baseUrl = baseUrl.Substring(0, baseUrl.Length - 1);
            }
            string url = $"{baseUrl}/FileInfo/AjaxUpload";
            Dictionary<string, string> headers = new Dictionary<string, string>();
            headers.Add("sysCode", fileServerInfo.SysCode);
            headers.Add("authCode", fileServerInfo.AuthCode);
            headers.Add("isThumbnail", "true");
            headers.Add("isPrintText", "false");
            //headers.Add("Content-Type", "application/octet-stream");
            headers.Add("Content-Disposition", "form-data; name=\"file\"; filename=\"" + fileName + "\"");
            Dictionary<string, string> keyValues = new Dictionary<string, string>();
            keyValues.Add("id", "WU_FILE_0");
            keyValues.Add("name", fileName);
            keyValues.Add("type", System.Web.MimeMapping.GetMimeMapping(fileName));
            Dictionary<string, string> fileList = new Dictionary<string, string>();
            fileList.Add("file", filePath);
            var result = userCtx.Container.GetService<IFileInfoService>().SendHttpRequestPost(url, keyValues, fileList, Encoding.UTF8, headers);
            if (string.IsNullOrWhiteSpace(result))
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "文件服务无应答消息"}
                };
            }
            Dictionary<string, string> json = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(result);
            var kvp = json?.FirstOrDefault(x => string.Equals("fileId", x.Key, StringComparison.OrdinalIgnoreCase));
            if (kvp == null || string.IsNullOrWhiteSpace(kvp.Value.Value))
            {
                return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "文件服务没有正确返回文件id"}
                };
            }
            json["isSuccess"] = "true";
            return json;
        }


        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private Dictionary<string, string> PostFile(string filePath, string fileName)
        {
            Dictionary<string, string> json = new Dictionary<string, string>();
            try
            {
                string newFilePath = filePath;
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", $"文件路径不能为空!"}
                };
                }


                var startupPath = PathUtils.GetStartupPath();
                filePath = Path.Combine(startupPath, "");
                bool flag = FileLoad(newFilePath, fileName, filePath + "\\swjTmpFile\\");
                if (!flag)
                {
                    WebResponse response = null;
                    HttpWebRequest request = (HttpWebRequest)WebRequest.Create(newFilePath);
                    response = request.GetResponse();
                    var stream = response.GetResponseStream();
                    //保存操作
                    var Value = SaveBinaryFile(response, filePath + "\\swjTmpFile\\" + fileName);
                }
                FileInfo t = new FileInfo(filePath + "\\swjTmpFile\\" + fileName);//获取文件
                var fileSize = GetFileSize(t.Length);
                var fileServerInfo = this.GetFirstWorkFileServer();
                if (fileServerInfo == null)
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器信息"}
                };
                }
                string baseUrl = fileServerInfo.Url;
                if (string.IsNullOrWhiteSpace(baseUrl))
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "尚未配置文件服务器的地址信息"}
                };
                }

                if (baseUrl.EndsWith("/"))
                {
                    baseUrl = baseUrl.Substring(0, baseUrl.Length - 1);
                }

                string url = $"{baseUrl}/FileInfo/AjaxUpload";

                Dictionary<string, string> headers = new Dictionary<string, string>();

                Dictionary<string, string> keyValues = new Dictionary<string, string>();

                Dictionary<string, string> fileList = new Dictionary<string, string>();
                fileList.Add("file", filePath + "\\swjTmpFile\\" + fileName);

                headers = new Dictionary<string, string>();
                headers.Add("sysCode", fileServerInfo.SysCode);
                headers.Add("authCode", fileServerInfo.AuthCode);
                headers.Add("isThumbnail", "true");
                headers.Add("isPrintText", "false");
                //headers.Add("Content-Type", "application/octet-stream");
                headers.Add("Content-Disposition", "form-data; name=\"file\"; filename=\"" + fileName + "\"");
                keyValues = new Dictionary<string, string>();
                keyValues.Add("id", "WU_FILE_0");
                keyValues.Add("name", fileName);
                keyValues.Add("type", System.Web.MimeMapping.GetMimeMapping(fileName));
                var result1 = this.agentCtx.Container.GetService<IFileInfoService>().SendHttpRequestPost(url, keyValues, fileList, Encoding.UTF8, headers);


                var result = this.Container.GetService<IFileInfoService>().SendHttpRequestPost(url, keyValues, fileList, Encoding.UTF8, headers);
                if (string.IsNullOrWhiteSpace(result))
                {
                    return new Dictionary<string, string>
                        {
                            { "isSuccess", "false"},
                            { "errMsg", "文件服务无应答消息"}
                        };
                }
                json = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(result);
                var kvp = json?.FirstOrDefault(x => string.Equals("fileId", x.Key, StringComparison.OrdinalIgnoreCase));
                if (kvp == null || string.IsNullOrWhiteSpace(kvp.Value.Value))
                {
                    return new Dictionary<string, string>
                {
                    { "isSuccess", "false"},
                    { "errMsg", "文件服务没有正确返回文件id"}
                };
                }
                Stream stream1 = new FileStream(filePath + "\\swjTmpFile\\" + fileName, FileMode.Open);
                var hWCloudFile = this.Context.Container.GetService<IHWCloudFileService>();
                string hwresult = hWCloudFile.UploadFile(json["fileId"], stream1);
                json["fileSize"] = fileSize;
                json["isSuccess"] = "true";
                return json;
            }
            catch (Exception ex)
            {
                loger.WriteLogToFile("用户【{0}-{1}-{2}】,时间：{3}，操作：【三维家接口，文件上传，Error】，内容:{4}".Fmt(this.Context.UserName,
                        this.Context.UserPhone, this.Context.Company,
                     DateTime.Now.ToString("HH:mm:ss"), "filePath" + filePath + "fileName" + fileName + "ex.Message：" + ex.Message + "ex.StackTrace：" + ex.StackTrace),
                    "SWJLogFile");
                json["isSuccess"] = "false";
                return json;
            }
        }
    }
}