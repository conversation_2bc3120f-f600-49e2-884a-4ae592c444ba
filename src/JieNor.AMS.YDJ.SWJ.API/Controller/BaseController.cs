using System;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ServiceStack;

namespace JieNor.AMS.YDJ.SWJ.API.Controller
{
    /// <summary>
    /// 控制器基类
    /// </summary>
    public class BaseController : ServiceStack.Service
    {
        /// <summary>
        /// 服务容器
        /// </summary>
        protected IServiceContainer Container { get; private set; }

        /// <summary>
        /// 用户上下文
        /// </summary>
        protected UserContext Context { get; private set; }

        /// <summary>
        /// 日志服务：直接写文件
        /// </summary>
        protected ILogServiceEx LogService { get; private set; }

        /// <summary>
        /// Redis缓存服务
        /// </summary>
        protected IRedisCache CacheClient { get; private set; }

        /// <summary>
        /// 网关服务
        /// </summary>
        protected IHttpServiceInvoker HttpGateway { get; private set; }

        /// <summary>
        /// 模型服务引擎
        /// </summary>
        protected IMetaModelService MetaModelService { get; private set; }

        /// <summary>
        /// 数据库服务引擎
        /// </summary>
        protected IDBService DBService { get; private set; }

        /// <summary>
        /// 初始化操作上下文
        /// </summary>
        /// <param name="reqDto"></param>
        protected void InitializeOperationContext(object reqDto, bool isauth = false)
        {
            //请求的生命周期标识
            var lifetimeScopeId = Guid.NewGuid().ToString();

            //每一次请求都需要生成一个全新的生命周期标识来确保后续的所有服务实例不会混淆
            this.Request.SetRequestId(lifetimeScopeId);
            this.Container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(lifetimeScopeId);

            //初始化用户上下文
            this.Context = this.InitUserContext(reqDto, isauth);

            //设置请求标识
            this.Context.RequestId = lifetimeScopeId;

            this.CacheClient = this.Container.GetService<IRedisCache>();
            (this.CacheClient as IPreInitCache)?.Init(this.Context);
            this.HttpGateway = this.Container.GetService<IHttpServiceInvoker>();
            this.DBService = this.Container.GetService<IDBService>();
            this.MetaModelService = this.Container.GetService<IMetaModelService>();
            this.LogService = this.Container.GetService<ILogServiceEx>();

            SetNumbers();
        }

        /// <summary>
        /// 初始化操作上下文
        /// </summary>
        /// <param name="reqDto"></param>
        protected void InitializeOperationContext(DTO.Demo.SSODTO reqDto)
        {
            //请求的生命周期标识
            var lifetimeScopeId = Guid.NewGuid().ToString();

            //每一次请求都需要生成一个全新的生命周期标识来确保后续的所有服务实例不会混淆
            this.Request.SetRequestId(lifetimeScopeId);
            this.Container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(lifetimeScopeId);

            //初始化用户上下文
            this.Context = this.InitUserContext(reqDto);

            //设置请求标识
            this.Context.RequestId = lifetimeScopeId;

            this.CacheClient = this.Container.GetService<IRedisCache>();
            (this.CacheClient as IPreInitCache)?.Init(this.Context);
            this.HttpGateway = this.Container.GetService<IHttpServiceInvoker>();
            this.DBService = this.Container.GetService<IDBService>();
            this.MetaModelService = this.Container.GetService<IMetaModelService>();
            this.LogService = this.Container.GetService<ILogServiceEx>();

            SetNumbers();
        }

        /// <summary>
        /// 初始化用户上下文
        /// </summary>
        /// <param name="reqDto"></param>
        /// <returns></returns>
        protected UserContext InitUserContext(object reqDto, bool isauth)
        {
            var session = this.SessionAs<UserAuthTicket>();
            if (session == null)
            {
                throw HttpError.Unauthorized("您没有权限访问此链接！");
            }
            session.CurrentRequestObject = reqDto;
            if (!isauth)
            {
                // 用系统预设的管理员身份操作
                session.UserId = "sysadmin";
                session.DisplayName = "系统管理员";
                session.UserName = "系统管理员";
                session.Product = this.ProductId();
                session.BizOrgId = session.Company;
                session.ParentCompanyId = session.TopCompanyId;
            }

            var userCtx = new UserContext();
            userCtx.Container = this.Container;
            userCtx.SetUserSession(session);

            if (this.Request.UrlReferrer?.AbsoluteUri?.IndexOf("/shell.html?") > 0)
            {
                userCtx.IsTempToken = true;
            }
            else
            {
                userCtx.IsTempToken = false;
            }

            return userCtx;
        }


        /// <summary>
        /// 初始化用户上下文
        /// </summary>
        /// <param name="reqDto"></param>
        /// <returns></returns>
        protected UserContext InitUserContext(DTO.Demo.SSODTO reqDto)
        {
            var session = this.SessionAs<UserAuthTicket>();
            if (session == null)
            {
                throw HttpError.Unauthorized("您没有权限访问此链接！");
            }
            session.CurrentRequestObject = reqDto;

            // 用系统预设的管理员身份操作
            //session.UserId = "sysadmin";
            //session.DisplayName = "系统管理员";
            //session.UserName = "系统管理员";
            //session.Product = this.ProductId();
            //session.BizOrgId = session.Company;
            //session.ParentCompanyId = session.TopCompanyId;

            var userCtx = new UserContext();
            userCtx.Container = this.Container;
            userCtx.SetUserSession(session);

            if (this.Request.UrlReferrer?.AbsoluteUri?.IndexOf("/shell.html?") > 0)
            {
                userCtx.IsTempToken = true;
            }
            else
            {
                userCtx.IsTempToken = false;
            }

            return userCtx;
        }

        /// <summary>
        /// ORM数据读写引擎
        /// </summary>
        protected IDataManager GetDataManager()
        {
            if (this.Context == null) return null;
            var dm = this.Container?.GetService<IDataManager>();
            return dm;
        }

        /// <summary>
        /// 设置编码
        /// </summary>
        /// <returns></returns>
        protected virtual void SetNumbers()
        {

        }
    }
}