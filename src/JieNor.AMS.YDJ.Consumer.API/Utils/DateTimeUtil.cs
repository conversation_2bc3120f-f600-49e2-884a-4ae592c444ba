using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 日期时间扩展类
    /// </summary>
    public static class DateTimeUtil
    {
        /// <summary>
        /// 获取时间戳（毫秒）
        /// </summary>
        /// <param name="utcDate">UTC时间</param>
        /// <returns></returns>
        public static long Timestamp(this DateTime utcDate)
        {
            return (long)(utcDate - new DateTime(1970, 1, 1)).TotalMilliseconds;
        }

        public static long Timestamp()
        {
            return DateTime.UtcNow.Timestamp();
        }

        public static long TimestampFromBeiJingTime(this DateTime beijingTime)
        {
            return beijingTime.AddHours(-8.0D).Timestamp();
        }

        public static DateTime GetUtcTime(double timestamp)
        {
            DateTime converted = new DateTime(1970, 1, 1, 0, 0, 0, 0);
            DateTime newDateTime = converted.AddMilliseconds(timestamp);
            return newDateTime;
        }

        public static DateTime GetBeiJingTime(double timestamp)
        {
            DateTime converted = new DateTime(1970, 1, 1, 0, 0, 0, 0);
            DateTime newDateTime = converted.AddMilliseconds(timestamp);
            return newDateTime.AddHours(8);
        }
    }
}