using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 字符串扩展类
    /// </summary>
    public static class StringUtil
    {
        /// <summary>
        /// 解析为下拉数据模型
        /// </summary>
        /// <param name="idVal"></param>
        /// <param name="txtVal"></param>
        /// <returns></returns>
        public static ComboDataModel ParseComboData(string idVal, string txtVal)
        {
            var model = new ComboDataModel
            {
                Id = JNConvert.ToStringAndTrim(idVal),
                Name = JNConvert.ToStringAndTrim(txtVal)
            };
            return model;
        }

        /// <summary>
        /// 解析为多选下拉框数据模型
        /// </summary>
        /// <param name="idVal"></param>
        /// <param name="txtVal"></param>
        /// <returns></returns>
        public static List<ComboDataModel> ParseComboDatas(string idVal, string txtVal)
        {
            var ids = idVal.Split(new string[] { "," }, StringSplitOptions.None);
            var txts = txtVal.Split(new string[] { "," }, StringSplitOptions.None);

            List<ComboDataModel> models = new List<ComboDataModel>();

            for (int i = 0; i < ids.Length; i++)
            {
                if (i < txtVal.Length)
                {
                    models.Add(ParseComboData(ids[i], txts[i]));
                }
                else
                {
                    models.Add(ParseComboData(ids[i], ""));
                }
            }

            return models;
        }

        /// <summary>
        /// 解析为多选下拉框数据模型
        /// </summary>
        /// <param name="dynObj">表单动态数据包</param>
        /// <param name="propName">多选下拉框字段在数据包中的属性名</param>
        /// <returns></returns>
        public static List<ComboDataModel> ParseComboDatas(DynamicObject dynObj, string propName)
        {
            return ParseComboDatas(JNConvert.ToStringAndTrim(dynObj[propName]),
                JNConvert.ToStringAndTrim(dynObj[propName + "_txt"]));
        }
    }
}