using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：验证登录状态接口
    /// </summary>
    public class IsLoginController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(IsLoginDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            resp.Success = true;
            resp.Message = "已登录！";

            return resp;
        }
    }
}