using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    /// <summary>
    /// 注册基础控制器
    /// </summary>
    public class BaseRegisterController : AuthController
    {
        /// <summary>
        /// 操作名
        /// </summary>
        public string OperationName { get; set; } = "注册";

        /// <summary>
        /// 注册会员（客户）
        /// </summary>
        /// <param name="param"></param>
        /// <param name="resp"></param>
        protected void Register(RegisterParam param, BaseResponse<UserInfoModel> resp)
        {
            var customerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");

            var customer = customerForm.GetBizDataByWhere(this.Context, $"fwxopenid=@fwxopenid", new List<SqlParam>
            {
                new SqlParam("@fwxopenid", DbType.String, param.OpenId)
            }, true).FirstOrDefault();
            if (param.Name == "微信用户")
            {
                //微信获取昵称如果只能获取到“微信用户”，则使用手机号作为用户名称
                param.Name = param.Phone;
            }
            // 已有用户，不再创建
            if (customer == null)
            {
                customer = customerForm.GetBizDataByWhere(this.Context, $"fphone=@fphone", new List<SqlParam>
                {
                    new SqlParam("@fphone", DbType.String,param. Phone)
                }, true).OrderByDescending(s => Convert.ToDateTime(s["fcreatedate"])).FirstOrDefault();

                // 更新
                if (customer != null)
                {
                    string wxOpenId = Convert.ToString(customer["fwxopenid"]);
                    // 绑定其他微信号
                    if (wxOpenId.IsNullOrEmptyOrWhiteSpace() == false && wxOpenId != param.OpenId)
                    {
                        resp.Success = false;
                        resp.Message = $"此手机号已绑定其他微信号，请检查！";
                        return;
                    }

                    Dictionary<string, string> map = new Dictionary<string, string>
                    {
                        { "fwxopenid", param.OpenId },
                        { "fwxunionid", param.UnionId },
                        { "fname", param.Name },
                        { "fgender", param.GenderId },
                        { "fcountry", param.CountryId },
                        { "fprovince", param.ProvinceId },
                        { "fcity", param.CityId },
                        { "fheadimgurl", param.HeadImgUrl },
                        { "fcontacts", param.Name }
                    };

                    foreach (var item in map)
                    {
                        // 客户信息为空，且参数不为空时，更新
                        if (customer[item.Key].IsNullOrEmptyOrWhiteSpace() &&
                            item.Value.IsNullOrEmptyOrWhiteSpace() == false)
                        {
                            customer[item.Key] = item.Value;
                        }
                    }

                    var dt = customerForm.GetDynamicObjectType(this.Context);
                    var dm = this.GetDataManager();
                    dm.InitDbContext(this.Context, dt);
                    dm.Save(customer);
                }
                // 创建
                else
                {
                    var dt = customerForm.GetDynamicObjectType(this.Context);
                    customer = new DynamicObject(dt);
                    customer["fphone"] = param.Phone;
                    customer["fwxopenid"] = param.OpenId;
                    customer["fwxunionid"] = param.UnionId;
                    customer["fname"] = param.Name;
                    customer["fgender"] = param.GenderId;
                    customer["fcountry"] = param.CountryId;
                    customer["fprovince"] = param.ProvinceId;
                    customer["fcity"] = param.CityId;
                    customer["fheadimgurl"] = param.HeadImgUrl;
                    customer["fsource"] = "cussource_08"; // 客户来源：小程序
                    customer["ftype"] = "customertype_00"; // 客户类型：个人
                    customer["fcontacts"] = param.Name; // 联系人默认使用姓名
                    customer["fcusnature"] = "cusnature_00"; // 客户性质：线索
                    customer["fbirthdate"] = null;//不设置默认生日日期
                    customer["fgeneratesource"] = "注册会员（客户）";
                    if (param.InvitorId.IsNullOrEmptyOrWhiteSpace() == false)
                    {
                        var staff = this.Context.GetStaffByUserIdOrNumber(param.InvitorId);
                        var dept = this.Context.GetDeptByUserIdOrNumber(param.InvitorId);

                        if (staff != null && dept != null)
                        {
                            var dutyEntry = customer["fdutyentry"] as DynamicObjectCollection;
                            if (dutyEntry != null)
                            {
                                var duty = new DynamicObject(dutyEntry.DynamicCollectionItemPropertyType);
                                duty["fdutyid"] = staff.Id;
                                duty["fdeptid"] = dept.Id;
                                duty["fjointime"] = DateTime.Now;
                                //duty["FSeq"] = 1;
                                dutyEntry.Add(duty);
                            }
                        }
                    }

                    var prepareService = this.Container.GetService<IPrepareSaveDataService>();
                    prepareService.PrepareDataEntity(this.Context, customerForm, new DynamicObject[] { customer }, OperateOption.Create());

                    var result = this.HttpGateway.InvokeBillOperation(this.SysAdminUserCtx, "ydj_customer",
                        new DynamicObject[] { customer },
                        "save", new Dictionary<string, object>());

                    if (result.IsSuccess == false)
                    {
                        resp = result.ToResponseModel<UserInfoModel>();
                        return;
                    }
                }
            }

            SetResponseData(resp, customer, param.SessionId);
            if (this.Context.Meta.ContainsKey("OpenId"))
            {
                this.Context.Meta["OpenId"] = param.OpenId;
            }
            else
            {
                this.Context.Meta.Add("OpenId", param.OpenId);
            }
            resp.Success = true;
            resp.Message = $"{this.OperationName}成功！";
        }

        public class RegisterParam
        {
            /// <summary>
            /// openid
            /// </summary>
            public string OpenId { get; set; }
            /// <summary>
            /// unionid
            /// </summary>
            public string UnionId { get; set; }
            /// <summary>
            /// 手机号
            /// </summary>
            public string Phone { get; set; }
            /// <summary>
            /// 姓名
            /// </summary>
            public string Name { get; set; }
            /// <summary>
            /// 性别id
            /// </summary>
            public string GenderId { get; set; }
            /// <summary>
            /// 国家或地区id
            /// </summary>
            public string CountryId { get; set; }
            /// <summary>
            /// 省id
            /// </summary>
            public string ProvinceId { get; set; }
            /// <summary>
            /// 市id
            /// </summary>
            public string CityId { get; set; }
            /// <summary>
            /// 头像url
            /// </summary>
            public string HeadImgUrl { get; set; }
            /// <summary>
            /// 邀请人id（麦浩用户id）
            /// </summary>
            public string InvitorId { get; set; }
            /// <summary>
            /// 类似于session_id，由微信项目API获取
            /// </summary>
            public string SessionId { get; set; }
        }
    }
}
