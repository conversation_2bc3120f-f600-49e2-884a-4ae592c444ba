using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.Framework;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：微信解绑接口
    /// </summary>
    public class SwitchAgentUserController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(SwitchAgentDTO dto)
        {
            this.InitializeOperationContext(dto, dto.AgentCode);
            var resp = new BaseResponse<UserInfoModel>();

            var customerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");
            var customer = customerForm.GetBizDataByWhere(this.Context, $"fnumber=@fnumber and fmainorgid=@fmainorgid", new List<SqlParam>
            {
                new SqlParam("@fnumber", DbType.String,this.Context.UserSession.UserName),
                new SqlParam("@fmainorgid", DbType.String,this.Context.TopCompanyId)
            }, true)?.FirstOrDefault();
            if (customer == null)
            {
                resp.Success = false;
                resp.Message = $"总部组织不存在客户编码:{ this.Context.UserSession.UserName}！";
                return resp;
            }
            var agentCustomer = customerForm.GetBizDataByWhere(this.Context, $"fphone=@fphone and fmainorgid=@fmainorgid", new List<SqlParam>
            {
                new SqlParam("@fphone", DbType.String, customer["fphone"]),
                new SqlParam("@fmainorgid", DbType.String,dto.AgentCode)
            }, true)?.FirstOrDefault();
            customer["fbizorgid"] = dto.AgentCode;
            var topCompanyContext = this.Context.CreateCompanyDbContext(this.Context.TopCompanyId);
            var dt = customerForm.GetDynamicObjectType(topCompanyContext);
            var dm = this.GetDataManager();
            dm.InitDbContext(topCompanyContext, dt);
            dm.Save(customer);
            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            if (agentCustomer == null)
            {
                dt = customerForm.GetDynamicObjectType(this.Context);
                agentCustomer = new DynamicObject(dt);
                customer.CopyTo(agentCustomer, clearPrimaryKeyValue: true);
                agentCustomer["fmainorgid"] = "";
                agentCustomer["fmainorgid_txt"] = "";
                agentCustomer["fpublishcid"] = "";
                agentCustomer["fpublishcid_txt"] = "";

                agentCustomer["fwxopenid"] = "";
                agentCustomer["fdutyids"] = dto.CustomerDutyNumber;

                var staff = this.Context.GetStaffByUserIdOrNumber(dto.CustomerDutyNumber);
                var dept = this.Context.GetDeptByUserIdOrNumber(dto.CustomerDutyNumber);
                if (dept != null)
                    agentCustomer["fdeptids"] = dept.Id;
                var dutyEntry = agentCustomer["fdutyentry"] as DynamicObjectCollection;
                if (dutyEntry != null)
                {
                    dutyEntry.Clear();
                    if (staff != null && dept != null)
                    {
                        var duty = new DynamicObject(dutyEntry.DynamicCollectionItemPropertyType);
                        duty["fdutyid"] = staff.Id;
                        duty["fdeptid"] = dept.Id;
                        duty["fjointime"] = DateTime.Now;
                        dutyEntry.Add(duty);
                    }

                }
                prepareService.PrepareDataEntity(this.Context, customerForm, new DynamicObject[] { agentCustomer }, OperateOption.Create());
                var result = this.HttpGateway.InvokeBillOperation(this.SysAdminUserCtx, "ydj_customer",
                    new DynamicObject[] { agentCustomer },
                    "save", new Dictionary<string, object>());

                if (result.IsSuccess == false)
                {
                    resp = result.ToResponseModel<UserInfoModel>();
                    return resp;
                }
            }
            SetResponseData(this.Context, resp, agentCustomer);
            resp.Success = true;
            resp.Message = "切换门店成功！";

            return resp;
        }
        private void SetResponseData(UserContext userContext, BaseResponse<UserInfoModel> resp, DynamicObject customer)
        {
            resp.Data.Country = JNConvert.ToStringAndTrim((customer["fcountry_ref"] as DynamicObject)?["fenumitem"]);
            resp.Data.Province = JNConvert.ToStringAndTrim((customer["fprovince_ref"] as DynamicObject)?["fenumitem"]);
            resp.Data.City = JNConvert.ToStringAndTrim((customer["fcity_ref"] as DynamicObject)?["fenumitem"]);
            resp.Data.CustomerId = JNConvert.ToStringAndTrim(customer["id"]);
            resp.Data.Company = userContext.Company;
            resp.Data.BearerToken = CreateToken(customer, userContext);
        }
        protected string CreateToken(DynamicObject customer, UserContext userContext)
        {
            //企业主体 
            CompanyDCInfo companyInfo = this.Context.Companys.FirstOrDefault(x => x.CompanyId.EqualsIgnoreCase(userContext.Company));
            if (companyInfo == null)
            {
                throw new BusinessException($"当前站点中不存在企业主体：{  this.Context.Company}，请检查！");
            }

            this.Context.UserSession.DisplayName = Convert.ToString(customer["fname"]);
            this.Context.UserSession.Product = this.GetProductInfo()?.Id;
            this.Context.UserSession.QyWxCustomerId = userContext.Company;

            DateTime tokenExpireAt = DateTime.Now.AddDays(7);

            string userName = Convert.ToString(customer["fnumber"]), userId = Convert.ToString(customer["id"]);

            //私钥
            var privateKey = this.GetAppConfig("jwt.PrivateKeyXml");
            if (privateKey.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"当前站点中没有配置jwt私钥，请检查！");
            }

            //创建令牌
            var tokenId = JwtUtil.CreateJwtToken(userContext, privateKey, tokenExpireAt, userName, userId, companyInfo);

            return tokenId;
        }
    }
}