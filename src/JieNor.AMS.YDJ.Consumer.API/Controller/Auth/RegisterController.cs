using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.Framework;
using JieNor.Framework.Interface;
using ServiceStack;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：登录接口
    /// </summary>
    public class RegisterController : BaseRegisterController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(RegisterDTO dto)
        {
            this.InitializeOperationContext(dto);

            this.OperationName = "注册";

            var resp = new BaseResponse<UserInfoModel>
            {
                Data = new UserInfoModel()
            };

            if (!this.ValidateParam(dto, resp)) return resp;

            //调用企业微信临时登录凭证校验接口
            var ewcRes = EwcJsonClient.Invoke<EwcCode2SessionData, EwcCode2SessionDTO>(
                new EwcCode2SessionDTO()
                {
                    JS_Code = dto.Code,
                    Code = dto.CustomerCode
                });
            var ewcData = ewcRes.Data;
            if (!ewcRes.Success)
            {
                var errMsg = $"调用微信小程序API失败：{ewcRes.Code}-{ewcRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                return resp;
            }

            if (ewcData.OpenId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Session_Key.IsNullOrEmptyOrWhiteSpace())
            {
                this.LogService.Error($"调用微信小程序API失败：{ewcData.ToJson()}");
                resp.Message = $"调用微信小程序API失败：返回的微信用户信息为空！";
                return resp;
            }

            var param = new RegisterParam
            {
                CityId = dto.CityId,
                CountryId = dto.CountryId,
                GenderId = dto.GenderId,
                HeadImgUrl = dto.HeadImgUrl,
                InvitorId = dto.InvitorId,
                Name = dto.Name,
                OpenId = ewcData.OpenId,
                Phone = dto.Phone,
                ProvinceId = dto.ProvinceId,
                SessionId = ewcData.Session_Id,
                UnionId = ewcData.UnionId
            };

            Register(param, resp);
            return resp;
        }

        /// <summary>
        /// 校验参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool ValidateParam(RegisterDTO dto, BaseResponse<UserInfoModel> resp)
        {
            if (dto.Code.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"{this.OperationName}失败：微信用户登录凭证 {nameof(dto.Code)} 不能为空！";
                return false;
            }

            if (dto.CustomerCode.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"{this.OperationName}失败：商户编码 {nameof(dto.CustomerCode)} 不能为空！";
                return false;
            }

            if (dto.Phone.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"{this.OperationName}失败：手机号 {nameof(dto.Phone)} 不能为空！";
                return false;
            }

            if (dto.ValidCode.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"{this.OperationName}失败：验证码 {nameof(dto.ValidCode)} 不能为空！";
                return false;
            }

            // 判断验证码是否正确
            var service = this.Container.GetService<IMNSService>();
            if (service.IsValidAuthCode(this.SysAdminUserCtx, dto.Phone, dto.ValidCode) == false)
            {
                resp.Message = $"{this.OperationName}失败：验证码不正确！";
                return false;
            }

            return true;
        }
    }
}