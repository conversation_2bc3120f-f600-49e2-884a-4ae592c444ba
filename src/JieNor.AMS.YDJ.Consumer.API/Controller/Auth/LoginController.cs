using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：登录接口
    /// </summary>
    public class LoginController : AuthController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(LoginDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<UserInfoModel>
            {
                Data = new UserInfoModel()
            };

            if (!this.ValidateParam(dto, resp)) return resp;

            //调用企业微信临时登录凭证校验接口
            var ewcRes = EwcJsonClient.Invoke<EwcCode2SessionData, EwcCode2SessionDTO>(
                new EwcCode2SessionDTO()
                {
                    JS_Code = dto.Code,
                    Code = dto.CustomerCode
                });
            var ewcData = ewcRes.Data;
            if (!ewcRes.Success)
            {
                var errMsg = $"调用微信小程序API失败：{ewcRes.Code}-{ewcRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                return resp;
            }

            if (ewcData.OpenId.IsNullOrEmptyOrWhiteSpace()
                || ewcData.Session_Key.IsNullOrEmptyOrWhiteSpace())
            {
                this.LogService.Error($"调用微信小程序API失败：{ewcData.ToJson()}");
                resp.Message = $"调用微信小程序API失败：返回的微信用户信息为空！";
                return resp;
            }

            var customerForm = this.Container.GetService<IMetaModelService>().LoadFormModel(this.Context, "ydj_customer");
            var customer = customerForm.GetBizDataByWhere(this.Context, $"fwxopenid=@fwxopenid", new List<SqlParam>
            {
                new SqlParam("@fwxopenid", DbType.String, ewcData.OpenId)
            }, true).FirstOrDefault();

            if (customer != null)
            {
                SetResponseData(resp, customer, ewcData.Session_Id);
                if (this.Context.Meta.ContainsKey("OpenId"))
                {
                    this.Context.Meta["OpenId"] = ewcData.OpenId;
                }
                else
                {
                    this.Context.Meta.Add("OpenId", ewcData.OpenId);
                }
                resp.Success = true;
                resp.Message = "登录成功！";
            }
            else
            {
                // 新客户
                resp.Success = true;
                resp.Message = "新用户，请绑定手机！";
                resp.Data.WxOpenSessionId = ewcData.Session_Id;
            }

            return resp;
        }

        /// <summary>
        /// 校验参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool ValidateParam(LoginDTO dto, BaseResponse<UserInfoModel> resp)
        {
            if (dto.Code.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"登录失败：微信用户登录凭证 {nameof(dto.Code)} 不能为空！";
                return false;
            }

            if (dto.CustomerCode.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"登录失败：商户编码 {nameof(dto.CustomerCode)} 不能为空！";
                return false;
            }

            return true;
        }
    }
}