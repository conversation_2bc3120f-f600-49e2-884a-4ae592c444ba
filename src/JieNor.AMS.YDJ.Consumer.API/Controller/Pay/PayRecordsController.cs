using JieNor.AMS.YDJ.Consumer.API.DTO.Pay;
using JieNor.AMS.YDJ.Consumer.API.Model.Pay;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.Pay
{
    public class PayRecordsController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(PayRecordsDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseListPageData<PayRecordsModel>>();
            resp.Data.List = new List<PayRecordsModel>();
            var customerid = this.Context.UserId;
            var sql = $@"select  * from (select ROW_NUMBER() over(order by a.fcreatedate desc) rownum,  fcustomerid,fsourcenumber,fpurpose,fcreatedate,famount,a.fmainorgid,b.fenumitem from T_COO_INCOMEDISBURSE as a 
left join v_bd_enum as b
on a.fpurpose=b.fid
where fcustomerid='{customerid}' and a.fbizstatus='bizstatus_02' and a.fcancelstatus=0) p where rownum > {dto.PageSize} * ({dto.PageIndex} - 1)
";
            var res = DBService.ExecuteDynamicObject(this.Context, sql);

            resp.Data.PageSize = dto.PageSize;
            resp.Data.TotalRecord = GetTotalRecord(this.Context, customerid);
            if (res!=null)
            {
                foreach (var item in res)
                {
                    PayRecordsModel model = new PayRecordsModel();
                    model.amount = Convert.ToDecimal(item["famount"]);
                    model.createDate = Convert.ToString(item["fcreatedate"]);
                    model.billNo = Convert.ToString(item["fsourcenumber"]);
                    model.purpose = new Model.ComboDataModel
                    {
                        Id = Convert.ToString(item["fpurpose"]),
                        Name = Convert.ToString(item["fenumitem"])
                    };
                    resp.Data.List.Add(model);
                }
            }
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 获取总记录数
        /// </summary>
        /// <param name="context"></param>
        /// <param name="customerid"></param>
        /// <returns></returns>
        private int GetTotalRecord(UserContext context, string customerid)
        {
            var sql = $@"select count(1) total from T_COO_INCOMEDISBURSE as a 
left join v_bd_enum as b
on a.fpurpose=b.fid
where fcustomerid='{customerid}' and a.fbizstatus='bizstatus_02' and a.fcancelstatus=0";
            var res = DBService.ExecuteDynamicObject(context, sql);
            if (res!=null&&res.Any())
            {
                return Convert.ToInt32(res[0]["total"]);
            }
            else
            {
                return 0;
            }
        }
    }
}
