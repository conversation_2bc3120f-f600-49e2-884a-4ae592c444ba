using JieNor.AMS.YDJ.Consumer.API.DTO;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.BD.Product
{
    public class ProductListController : BaseNotAuthController
    {
        /// <summary>
        /// 当前日期
        /// </summary>
        private string DataNow { get; set; } = DateTime.Now.ToString("yyyy-MM-dd");

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<ProductListModel>>
            {
                Data = new BaseListPageData<ProductListModel>(dto.PageSize)
            };

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var sqlWhere = new StringBuilder("m.fmainorgid=@fmainorgid and m.fforbidstatus='0' and m.fispulloff='0' and m.fallowshopsale='1'");

            //系列过滤条件
            if (!dto.SeriesId.IsNullOrEmptyOrWhiteSpace())
            {
                sqlWhere.Append(" and m.fseriesid=@fseriesid ");
                sqlParam.Add(new SqlParam("@fseriesid", System.Data.DbType.String, dto.SeriesId));
            }
            //关键字
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                sqlWhere.Append(" and m.fname like @keyword");
                sqlParam.Add(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            //品类过滤条件
            if (!dto.CategoryId.IsNullOrEmptyOrWhiteSpace())
            {
                sqlParam.Add(new SqlParam("@categoryid", System.Data.DbType.String, dto.CategoryId));
            }
            var categoryWhere = this.BuildCategoryWhere(dto.CategoryId);

            //多选过滤条件（品牌、风格、空间 等等）
            this.BuildWhereParam(sqlParam, sqlWhere, dto.BrandIds, "fbrandid");
            this.BuildWhereParam(sqlParam, sqlWhere, dto.StyleIds, "fstyle");
            this.BuildWhereParam(sqlParam, sqlWhere, dto.SpaceIds, "fspace");

            //价格区间过滤条件
            var priceWhere = this.BuildPriceWhereParam(dto, sqlParam);

            //获取分页数据
            var list = this.GetList(dto, sqlParam, sqlWhere, categoryWhere, priceWhere);

            //获取分页总记录数
            var totalRecord = this.GetTotalRecord(sqlParam, sqlWhere, categoryWhere, priceWhere);

            //设置响应数据包
            resp.Data.List = list;
            resp.Data.TotalRecord = totalRecord;
            resp.Message = "取数成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 构建品类递归过滤条件
        /// </summary>
        private Tuple<string, string> BuildCategoryWhere(string categoryId)
        {
            if (categoryId.IsNullOrEmptyOrWhiteSpace())
            {
                return Tuple.Create("", "");
            }

            //按某个品类递归查询所有子品类的Sql
            var recursionSql = @"
            with temp
            as
            (
	            select fid cid,fparentid from ser_ydj_category with(nolock) 
	            where fid=@categoryid
	            union all
	            select c.fid,c.fparentid from ser_ydj_category c with(nolock) 
	            inner join temp on c.fparentid = temp.cid
            )";

            //商品关联品类递归结果集的Sql
            var joinSql = " inner join temp c on c.cid=m.fcategoryid ";

            return Tuple.Create(recursionSql, joinSql);
        }

        /// <summary>
        /// 构建多选过滤条件
        /// </summary>
        private void BuildWhereParam(List<SqlParam> sqlParam, StringBuilder sqlWhere, List<string> fieldVals, string fieldName)
        {
            if (fieldVals == null || !fieldVals.Any()) return;

            fieldVals = fieldVals.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (!fieldVals.Any()) return;

            if (fieldVals.Count == 1)
            {
                sqlWhere.AppendFormat(" and m.{0}=@{0}", fieldName);
                sqlParam.Add(new SqlParam($"@{fieldName}", System.Data.DbType.String, fieldVals[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < fieldVals.Count; i++)
                {
                    paramNames.Add($"@{fieldName}{i}");
                    sqlParam.Add(new SqlParam($"@{fieldName}{i}", System.Data.DbType.String, fieldVals[i]));
                }
                sqlWhere.AppendFormat(" and m.{0} in({1})", fieldName, string.Join(",", paramNames));
            }
        }

        /// <summary>
        /// 构建价格区间过滤条件
        /// </summary>
        private string BuildPriceWhereParam(ProductListDTO dto, List<SqlParam> sqlParam)
        {
            var sqlWhere = new StringBuilder();

            if (dto.MinPrice == 0 && dto.MaxPrice > 0)
            {
                sqlWhere.Append("fsalprice<=@maxprice");
            }
            else if (dto.MinPrice > 0 && dto.MaxPrice == 0)
            {
                sqlWhere.Append("fsalprice>=@minprice");
            }
            else if (dto.MinPrice > 0 && dto.MaxPrice > 0)
            {
                sqlWhere.Append("fsalprice>=@minprice and fsalprice<=@maxprice");
            }
            if (dto.MinPrice > 0)
            {
                sqlParam.Add(new SqlParam("@minprice", System.Data.DbType.Decimal, dto.MinPrice));
            }
            if (dto.MaxPrice > 0)
            {
                sqlParam.Add(new SqlParam("@maxprice", System.Data.DbType.Decimal, dto.MaxPrice));
            }
            if (sqlWhere.Length < 1) return "";

            return " and " + sqlWhere.ToString();
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        private List<ProductListModel> GetList(
            ProductListDTO dto,
            List<SqlParam> sqlParam,
            StringBuilder sqlWhere,
            Tuple<string, string> categoryWhere,
            string priceWhere)
        {
            var list = new List<ProductListModel>();

            //默认按综合排序（推荐+优惠活动+销量降序），推荐 和 优惠活动暂时没有，暂时只按销量降序
            var orderBy = @"
            (select sum(oe.fbizqty) from t_ydj_orderentry oe with(nolock) 
		    inner join t_ydj_order o with(nolock) on o.fid=oe.fid 
		    where oe.fproductid=m.fid and o.fmainorgid=@fmainorgid) desc";
            switch (dto.Sortby)
            {
                case "sale": //销量：按销售合同数量汇总降序
                    orderBy = @"
                    (select sum(oe.fbizqty) from t_ydj_orderentry oe with(nolock) 
		            inner join t_ydj_order o with(nolock) on o.fid=oe.fid 
		            where oe.fproductid=m.fid and o.fmainorgid=@fmainorgid) desc";
                    break;
                case "price": //价格
                    orderBy = $"m.fsalprice {dto.Sortord}";
                    break;
                case "nts": //最新上架：按创建日期降序
                    orderBy = "m.fcreatedate desc";
                    break;
            }

            var sqlText = $@"/*dialect*/
            {categoryWhere.Item1}
            select top {dto.PageSize} * from 
            (
                select row_number() over(order by {orderBy}) rownum,
                fid,fname,fimage,fguideprice,fcreatedate,fdefattrinfo,fsalprice from t_bd_material m with(nolock) 
                {categoryWhere.Item2} 
                where {sqlWhere}{priceWhere}
            ) p 
            where rownum > {dto.PageSize} * ({dto.PageIndex} - 1)";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    list.Add(new ProductListModel
                    {
                        Id = reader.GetValueToString("fid"),
                        Name = reader.GetValueToString("fname"),
                        AuxPropValId = reader.GetValueToString("fdefattrinfo").Trim(),
                        ImageUrl = reader.GetValueToString("fimage").GetSignedFileUrl(true),
                        SalPrice = reader.GetValueToDecimal("fsalprice"),
                        GuidePrice = reader.GetValueToDecimal("fguideprice")
                    });
                }
            }

            this.LoadProductImageList(list);

            return list;
        }

        /// <summary>
        /// 加载商品图片列表，后续可按需优化取数逻辑
        /// </summary>
        private void LoadProductImageList(List<ProductListModel> list)
        {
            foreach (var item in list)
            {
                item.ImageList = ProductUtil.GetImages(this.Context, item.Id, item.AuxPropValId);
            }
        }

        /// <summary>
        /// 获取分页总记录数
        /// </summary>
        private int GetTotalRecord(
            List<SqlParam> sqlParam,
            StringBuilder sqlWhere,
            Tuple<string, string> categoryWhere,
            string priceWhere)
        {
            var totalRecord = 0;

            var sqlText = $@"/*dialect*/
            {categoryWhere.Item1} 
            select count(0) totalRecord from t_bd_material m with(nolock) 
            {categoryWhere.Item2} 
            where {sqlWhere}{priceWhere}";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }

            return totalRecord;
        }
    }
}
