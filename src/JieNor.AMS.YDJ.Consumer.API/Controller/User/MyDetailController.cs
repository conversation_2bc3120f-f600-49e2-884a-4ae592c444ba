using JieNor.AMS.YDJ.Consumer.API.DTO.User;
using JieNor.AMS.YDJ.Consumer.API.Model.User;
using JieNor.AMS.YDJ.Consumer.API.Utils;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using JieNor.AMS.YDJ.Consumer.API.Model;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Utils;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.User
{
    public class MyDetailController : BaseController
    {
        /// <summary>
        /// 客户表单模型
        /// </summary>
        protected HtmlForm CustomerForm { get; set; }

        public object Any(MyDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<MyDetailModel>
            {
                Data = new MyDetailModel()
            };

            //根据唯一标识获取数据
            this.CustomerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");
            var customerObj = this.CustomerForm.GetBizDataById(this.Context, this.Context.UserId, true);

            //设置响应数据包
            this.SetResponseData(customerObj, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="customer"></param>
        /// <param name="resp"></param>
        private void SetResponseData(DynamicObject customer, BaseResponse<MyDetailModel> resp)
        {
            if (customer == null)
            {
                resp.Message = "客户不存在或已被删除！";
                resp.Success = false;
                return;
            }

            resp.Message = "取数成功！";
            resp.Success = true;

            resp.Data.Name = Convert.ToString(customer["fname"]);
            resp.Data.ImgUrl = Convert.ToString(customer["fheadimgurl"]);
            resp.Data.CustomerLevel = new BaseDataSimpleModel(customer["fcustomerlevel_ref"] as DynamicObject);
            resp.Data.AvailableIntegral = Convert.ToInt32(customer["favailableintegral"]);
        }
    }
}
