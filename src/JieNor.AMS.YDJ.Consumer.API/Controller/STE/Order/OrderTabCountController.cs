using JieNor.AMS.YDJ.Consumer.API.DTO.STE.Order;
using JieNor.AMS.YDJ.Consumer.API.Model;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Consumer.API.Controller.STE.Order
{
    /// <summary>
    /// 消费者小程序：合同单Tab数量取数接口
    /// </summary>
    public class OrderTabCountController : BaseOrderController
    {
        private static Dictionary<string, string> _types = new Dictionary<string, string>
        {
            { "paying", "待付款" },
            { "stocking", "待发货" },
            { "receiving", "待收货" },
            {"closed","已完成" }
        };

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderTabCountDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<List<BaseCountModel>>();

            resp.Data = new List<BaseCountModel>();

            foreach (var type in _types)
            {
                resp.Data.Add(new BaseCountModel
                {
                    Id = type.Key,
                    Count = GetTotalRecord(this.Context, type.Key),
                    Name = type.Value
                });
            }

            resp.Message = "取数成功！";
            resp.Success = true;

            return resp;
        }
    }
}