using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 品牌列表取数接口
    /// </summary>
    [Api("品牌列表取数接口")]
    [Route("/consumerapi/brand/list")]
    public class BrandListDTO : BaseNotAuthDTO
    {
        public int PageSize { get; set; }
        public string Keyword { get; set; }
        public string Sortby { get; set; }
        public string Sortord { get; set; }
        public int PageIndex { get; set; }
    }
}