using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 商品列表取数接口
    /// </summary>
    [Api("商品列表取数接口")]
    [Route("/consumerapi/product/list")]
    //[Authenticate]
    public class ProductListDTO : BaseNotAuthDTO
    {
        /// <summary>
        /// 品类ID
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 品牌ID集合
        /// </summary>
        public List<string> BrandIds { get; set; }

        /// <summary>
        /// 风格ID集合
        /// </summary>
        public List<string> StyleIds { get; set; }

        /// <summary>
        /// 空间ID集合
        /// </summary>
        public List<string> SpaceIds { get; set; }

        /// <summary>
        /// 最低价
        /// </summary>
        public decimal MinPrice { get; set; }

        /// <summary>
        /// 最高价
        /// </summary>
        public decimal MaxPrice { get; set; }
        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// 排序依据：按什么规则排序，比如按价格排序
        /// </summary>
        public string Sortby { get; set; }

        /// <summarys>
        /// 系列
        /// </summary>
        public string SeriesId { get; set; }
        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        private string _Sortord = "desc";

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        public string Sortord
        {
            get { return _Sortord; }
            set { _Sortord = string.IsNullOrWhiteSpace(value) ? "desc" : value; }
        }
        private int _PageIndex;
        /// <summary>
        /// 当前页码，默认第1页
        /// </summary>
        public int PageIndex
        {
            get { return _PageIndex; }
            set { _PageIndex = value < 1 ? 1 : value; }
        }

        private int _PageSize;
        /// <summary>
        /// 每页条数，默认每页10条
        /// </summary>
        public int PageSize
        {
            get { return _PageSize; }
            set { _PageSize = value < 1 ? 10 : value; }
        }
    }
}