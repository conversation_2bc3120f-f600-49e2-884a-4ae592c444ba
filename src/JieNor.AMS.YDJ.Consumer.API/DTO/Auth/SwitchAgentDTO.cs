using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 门店切换接口
    /// </summary>
    [Api("门店切换接口")]
    [Route("/consumerapi/switchAgent")]
    public class SwitchAgentDTO : BaseAuthDTO
    {
        /// <summary>
        /// 客户负责人
        /// </summary>
        public string CustomerDutyNumber { get; set; }   
        
        /// <summary>
        /// 待切换门店Id
        /// </summary>
        public string AgentCode { get; set; }
    }
}
