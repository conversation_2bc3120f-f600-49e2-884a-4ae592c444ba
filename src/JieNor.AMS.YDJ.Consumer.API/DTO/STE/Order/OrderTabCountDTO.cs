using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO.STE.Order
{
    /// <summary>
    /// 消费者小程序订单数量接口
    /// </summary>
    [Api("合同单Tab数量取数接口")]
    [Route("/consumerapi/order/tabcount")]
    [Route("/consumerapi/consumerorder/tabcount")]
    [Authenticate]
    public class OrderTabCountDTO : BaseDTO
    {
    }
}
