using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO
{
    /// <summary>
    /// 列表数据传输对象基类
    /// </summary>
    public class BaseListDTO : BaseDTO
    {
        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// 排序依据：按什么规则排序，比如按价格排序
        /// </summary>
        public string Sortby { get; set; }

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        private string _Sortord = "desc";

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        public string Sortord
        {
            get { return _Sortord; }
            set { _Sortord = string.IsNullOrWhiteSpace(value) ? "desc" : value; }
        }
    }
}