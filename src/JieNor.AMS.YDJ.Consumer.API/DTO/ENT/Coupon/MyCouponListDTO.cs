using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.DTO.User
{
    /// <summary>
    /// 消费者小程序：我的优惠券列表接口
    /// </summary>
    [Api("我的优惠券列表接口")]
    [Route("/consumerapi/my/coupon/list")]
    [Authenticate]
    public class MyCouponListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 页签（待使用：using；已使用：used；已失效：cancel）
        /// </summary>
        public string Tab { get; set; }
    }
}
