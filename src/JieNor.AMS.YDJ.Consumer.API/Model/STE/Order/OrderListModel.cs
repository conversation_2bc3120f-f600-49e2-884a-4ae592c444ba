using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Model.STE.Order
{
    public class OrderListModel
    {
        /// <summary>
        /// 数据ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// 编码
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 订单总额
        /// </summary>
        public decimal SumAmount { get; set; }

        /// <summary>
        /// 剩余付款
        /// </summary>
        public decimal UnreceivedAmount { get; set; }

        /// <summary>
        /// 销售部门
        /// </summary>
        public string Dept { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>

        public string Status { get; set; }

        /// <summary>
        /// 商品明细
        /// </summary>
        public List<OrderProductSimpleModel> Products { get; set; } = new List<OrderProductSimpleModel>();

        /// <summary>
        /// 订单状态id
        /// </summary>
        [IgnoreDataMember]
        public string StatusId { get; set; }

        /// <summary>
        /// 总商品数量（用于判断合同状态）
        /// </summary>
        [IgnoreDataMember]
        public int SumQty { get; set; }

        /// <summary>
        /// 总出库数量（用于判断合同状态）
        /// </summary>
        [IgnoreDataMember]
        public int SumOutQty { get; set; }

        /// <summary>
        /// 关闭状态（用于判断合同状态【已完成】）
        /// </summary>
        [IgnoreDataMember]
        public int CloseStatus { get; set; }

        ///// <summary>
        ///// 结算状态（用于判断合同状态【待收款】）
        ///// </summary>
        //[IgnoreDataMember]
        //public decimal UnreceivedAmount { get; set; }
    }

    /// <summary>
    /// 合同商品简易数据模型
    /// </summary>
    public class OrderProductSimpleModel
    {
        /// <summary>
        /// 商品行id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 商品Id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 商品编码
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public BaseImageModel ProductImage { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public List<BaseImageModel> ProductImages { get; set; }

        /// <summary>
        /// 辅助属性
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }
    }
}
