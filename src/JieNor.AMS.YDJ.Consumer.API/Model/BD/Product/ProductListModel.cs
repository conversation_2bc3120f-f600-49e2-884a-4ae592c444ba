using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.Consumer.API.Model
{
    public class ProductsCategoryModel : ProductListModel
    {
        public string TopCategoryId { get; set; }
        public string TopCategoryName { get; set; }
        public string SubCategoryId { get; set; }
        public string SubCategoryName { get; set; }
        public List<ProductListModel> ProductLists { get; set; }
    }
    /// <summary>
    /// 消费者小程序商品列表数据模型
    /// </summary>
    public class ProductListModel : BaseDataModel
    {
        /// <summary>
        /// 数据状态：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 创建时间：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public new DateTime CreateDate { get; set; }

        /// <summary>
        /// 编码：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public new string Number { get; set; }

        /// <summary>
        /// 默认辅助属性组合值ID
        /// </summary>
        public string AuxPropValId { get; set; }

        /// <summary>
        /// 商品主图地址（已废弃）
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 销售价
        /// </summary>
        public decimal SalPrice { get; set; }

        /// <summary>
        /// 厂家指导价
        /// </summary>
        public decimal GuidePrice { get; set; }

        /// <summary>
        /// 可用库存量
        /// </summary>
        public decimal UsableStockQty { get; set; }

        /// <summary>
        /// 是否热销
        /// </summary>
        public bool IsHotSale { get; set; }

        /// <summary>
        /// 是否特价
        /// </summary>
        public bool IsSpecial { get; set; }

        /// <summary>
        /// 是否满减
        /// </summary>
        public bool IsFullReduce { get; set; }

        /// <summary>
        /// 是否满赠
        /// </summary>
        public bool IsFullGift { get; set; }
    }
}