using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Model.BD.Customer.MemberInformation
{
    /// <summary>
    /// 会员信息详情数据模型
    /// </summary>
    public class MemberInformationDetailModel : BaseDataModel
    {
        /// <summary>
        /// 头像
        /// </summary>
        public string Images { get; set; }
        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; }
        /// <summary>
        /// 生日
        /// </summary>
        public string Birthdate { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public ComboDataModel Gender { get; set; }

        /// <summary>
        /// 电子邮件
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; }

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }
    }
}
