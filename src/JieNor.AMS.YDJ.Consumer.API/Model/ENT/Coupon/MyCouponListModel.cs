using System;

namespace JieNor.AMS.YDJ.Consumer.API.Model.ENT.Coupon
{
    /// <summary>
    /// 我的优惠券列表数据模型
    /// </summary>
    public class MyCouponListModel
    {
        public string Id { get; set; }

        /// <summary>
        /// 有效期开发日期
        /// </summary>
        public DateTime? ExpiredStartDate { get; set; }

        /// <summary>
        /// 有效期结束日期
        /// </summary>
        public DateTime? ExpiredEndDate { get; set; }

        /// <summary>
        /// 使用日期（即修改日期）
        /// </summary>
        public DateTime UseTime { get; set; }

        /// <summary>
        /// 优惠券名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 使用条件
        /// </summary>
        public string UseCondition { get; set; }

        /// <summary>
        /// 金额（用于剩余金额、使用金额等）
        /// </summary>
        public decimal Amount { get; set; }
    }
}
