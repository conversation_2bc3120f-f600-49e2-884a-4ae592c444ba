using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Model.Pay
{
    /// <summary>
    /// 小程序调用微信支付接口返回数据模型
    /// </summary>
    public class PerPayModel
    {
        /// <summary>
        /// 业务响应码 KP1100：请求成功 KP1002：支付失败
        /// </summary>
        public string bizCode { get; set; }
        /// <summary>
        /// 业务响应描述
        /// </summary>
        public string bizMsg { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string tradeNO { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string appId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string timestamp { get; set; }
         /// <summary>
        /// 
        /// </summary>
        public string nonceStr { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string packages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string signType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string paySign { get; set; }
    }
}
