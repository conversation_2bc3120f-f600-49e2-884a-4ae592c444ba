using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;

namespace JieNor.AMS.YDJ.Consumer.API
{
    /// <summary>
    /// 动态注册控制器
    /// </summary>
    [InjectService]
    public class RegisterController : IDynamicRegisterController
    {
        /// <summary>
        /// 获取控制器所属程序集
        /// </summary>
        /// <returns></returns>
        public Assembly[] GetControllerAssembly()
        {
            return new Assembly[] { typeof(RegisterController).Assembly };
        }
    }
}