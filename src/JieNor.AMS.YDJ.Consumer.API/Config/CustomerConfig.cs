using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Config
{
    /// <summary>
    /// 运维系统商户配置信息
    /// </summary>
    public class CustomerConfig
    {
        /// <summary>
        /// 运维系统商户列表
        /// </summary>
        public List<CustomerInfo> Customers { get; set; }
    }

    /// <summary>
    /// 运维系统商户信息
    /// </summary>
    public class CustomerInfo
    {
        /// <summary>
        /// 运维系统商户标识
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 运维系统商户编码
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 运维系统商户名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 运维系统商户密钥
        /// </summary>
        public string SecretKey { get; set; }

        /// <summary>
        /// 麦浩企业id
        /// </summary>
        public string CompanyId { get; set; }
    }
}