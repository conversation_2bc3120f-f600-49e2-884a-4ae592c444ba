using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Plugin.SEC.User
{
    /// <summary>
    /// 用户：解绑企业微信，主要用于PC端管理员操作
    /// </summary>
    [InjectService]
    [FormId("sec_user")]
    [OperationNo("unbindqywx")]
    public class UnBindQyWxPlugIn : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length < 1)
            {
                throw new BusinessException($"请先选中一行{this.HtmlForm.Caption}后再执行该操作！");
            }
            if (e.DataEntitys.Length > 1)
            {
                throw new BusinessException($"该操作暂时不支持批量执行，请确保只选中一行{this.HtmlForm.Caption}！");
            }
            var userName = Convert.ToString(e.DataEntitys.FirstOrDefault()?["fnumber"]);
            if (userName.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"当前选中的{this.HtmlForm.Caption}账号为空，请检查！");
            }

            //此处根据用户的账号解绑
            //向认证站点发送请求
            var gateway = this.Container.GetService<IHttpServiceInvoker>();
            var acResponse = gateway.Invoke(null, TargetSEP.AuthService,
                new CommonFormDTO()
                {
                    FormId = "auth_user",
                    OperationNo = "UnBindQyWxUser",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "userName", userName }
                    }
                }) as DynamicDTOResponse;
            var acResult = acResponse.OperationResult;
            acResult?.ThrowIfHasError(true, $"向认证站点发送解绑请求失败！");

            var qyWxInfo = (acResult?.SrvData as string)?.FromJson<QyWxUserInfoModel>();
            var qyWxOpenUserId = qyWxInfo?.QyWxOpenUserId;
            var qyWxCustomerId = qyWxInfo?.QyWxCustomerId;
            if (qyWxOpenUserId.IsNullOrEmptyOrWhiteSpace() || qyWxCustomerId.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"当前选中的{this.HtmlForm.Caption}账号没有绑定过企业微信或者已被解绑，无需解绑！");
            }

            //调用运维系统解绑微信用户
            var opsRes = OpsJsonClient.Post<object>(
                new OpsUnBindUserDTO()
                {
                    OpenId = qyWxOpenUserId,
                    Code = qyWxCustomerId
                });
            if (!opsRes.Success)
            {
                var errMsg = $"调用运维系统失败：{opsRes.Code}-{opsRes.Msg}";
                throw new BusinessException(errMsg);
            }

            this.Result.ComplexMessage.SuccessMessages.Add("解绑成功！");
            this.Result.IsSuccess = true;
        }
    }
}