using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.CustomerRecord
{
    /// <summary>
    /// 微信小程序：商机详情取数接口
    /// </summary>
    public class CustomerRecordDetailController : BaseController
    {
        public HtmlForm CustomerRecordForm;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerRecordDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<CustomerRecordDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            this.CustomerRecordForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord");

            var data = this.CustomerRecordForm.GetBizDataById(this.Context, dto.Id, true);
            if (data == null)
            {
                resp.Message = "商机不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = MapTo(data);
            // 获取商机相关的基础资料
            resp.Data.ComboData = this.CustomerRecordForm.GetComboDataSource(this.Context, "fbusinessclos");

            return resp;
        }

        private CustomerRecordDetailModel MapTo(DynamicObject customerRecordObj)
        {
            var chanceStatusObj = customerRecordObj["fchancestatus_ref"] as DynamicObject;
            var demandObj = customerRecordObj["fdemand_ref"] as DynamicObject;
            var deptObj = customerRecordObj["fdeptid_ref"] as DynamicObject;
            var dutyObj = customerRecordObj["fdutyid_ref"] as DynamicObject;
            var areaObj = customerRecordObj["farea_ref"] as DynamicObject;
            var roomObj = customerRecordObj["froom_enum_ref"] as DynamicObject;
            var balconyObj = customerRecordObj["fbalcony_enum_ref"] as DynamicObject;
            var hallObj = customerRecordObj["fhall_enum_ref"] as DynamicObject;
            var toiletObj = customerRecordObj["ftoilet_enum_ref"] as DynamicObject;
            var styleObj = customerRecordObj["fstyle_ref"] as DynamicObject;
            var renovationObj = customerRecordObj["frenovation_ref"] as DynamicObject;
            var statusObj = customerRecordObj["fstatus_ref"] as DynamicObject;

            // 客户副本信息
            var genderObj = customerRecordObj["fgender_ref"] as DynamicObject;
            var ageObj = customerRecordObj["fage_ref"] as DynamicObject;
            var sourceObj = customerRecordObj["fcustomersource_ref"] as DynamicObject;
            var houseObj = customerRecordObj["fbuildingid_ref"] as DynamicObject;
            var channelObj = customerRecordObj["fchannelid_ref"] as DynamicObject;
            var provinceObj = customerRecordObj["fprovince_ref"] as DynamicObject;
            var cityObj = customerRecordObj["fcity_ref"] as DynamicObject;
            var regionObj = customerRecordObj["fregion_ref"] as DynamicObject;
            var requirementtypeObj = customerRecordObj["frequirement_type_ref"] as DynamicObject;
            var budgetscopeObj = customerRecordObj["fbudget_scope_ref"] as DynamicObject;
            var countryObj = customerRecordObj["fcountry_ref"] == null ? null : customerRecordObj["fcountry_ref"] as DynamicObject;

            CustomerRecordDetailModel model = new CustomerRecordDetailModel
            {
                Id = JNConvert.ToStringAndTrim(customerRecordObj["id"]),
                Number = JNConvert.ToStringAndTrim(customerRecordObj["fbillno"]),
                CreateDate = Convert.ToDateTime(customerRecordObj["fcreatedate"]),

                Description = JNConvert.ToStringAndTrim(customerRecordObj["fdescription"]),
                ExpectDate = customerRecordObj["fexpectdate"] == null ? (DateTime?)null : Convert.ToDateTime(customerRecordObj["fexpectdate"]),
                GoshopDate = Convert.ToDateTime(customerRecordObj["fgoshopdate"]),

                Phone = JNConvert.ToStringAndTrim(customerRecordObj["fphone"]),
                Wechat = JNConvert.ToStringAndTrim(customerRecordObj["fwechat"]),
                CustomerName = JNConvert.ToStringAndTrim(customerRecordObj["fcustomername"]),
                District = this.CustomerRecordForm.GetDistrictText(this.Context, customerRecordObj),
                Address = JNConvert.ToStringAndTrim(customerRecordObj["faddress"]),
                PathName = JNConvert.ToStringAndTrim(customerRecordObj["fpathname"]),
                Budget = new ComboDataModel(budgetscopeObj)
            };
            var dutyentry = customerRecordObj["fdutyentry"] as DynamicObjectCollection;
            if (dutyentry != null)
            {

                foreach (var item in dutyentry)
                {
                    var dutymodel = new CusDutyDetailDto();
                    var dutyobj = item["fdutyid_ref"] as DynamicObject;
                    var deptobj = item["fdeptid_ref"] as DynamicObject;
                    dutymodel.Duty = new BaseDataSimpleModel(dutyobj);
                    dutymodel.DutyType = Convert.ToBoolean(item["fismain"]) ? "1" : "0";
                    dutymodel.Dept = new BaseDataSimpleModel(deptobj);
                    dutymodel.Id = Convert.ToString(item["id"]);
                    dutymodel.JoinTime = Convert.ToDateTime(item["fjoindate"]);
                    model.DutyList.Add(dutymodel);
                }
            }
            var referrer = customerRecordObj["freferrer_ref"] as DynamicObject;
            model.Referrer = new
            {
                Id = JNConvert.ToStringAndTrim(referrer?["id"]),
                Number = JNConvert.ToStringAndTrim(referrer?["fnumber"]),
                Name = JNConvert.ToStringAndTrim(referrer?["fname"]),
                Phone = JNConvert.ToStringAndTrim(referrer?["fphone"])
            };
            model.ChanceStatus = new ComboDataModel(chanceStatusObj);
            model.Demand = new ComboDataModel(demandObj);
            model.Dept = new BaseDataSimpleModel(deptObj);
            model.Duty = new StaffSimpleModel(dutyObj);
            model.Area = new ComboDataModel(areaObj);
            model.Room = new ComboDataModel(roomObj);
            model.Balcony = new ComboDataModel(balconyObj);
            model.Hall = new ComboDataModel(hallObj);
            model.Toilet = new ComboDataModel(toiletObj);
            model.Style = new ComboDataModel(styleObj);
            model.Status = new ComboDataModel(statusObj);

            model.Channel = new BaseDataSimpleModel(channelObj);
            model.House = new BaseDataSimpleModel(houseObj);
            model.Gender = new ComboDataModel(genderObj);
            model.Age = new ComboDataModel(ageObj);
            model.CustomerSource = new ComboDataModel(sourceObj);
            model.Province = new ComboDataModel(provinceObj);
            model.City = new ComboDataModel(cityObj);
            model.Region = new ComboDataModel(regionObj);
            if (countryObj != null)
            {
                model.Country = new ComboDataModel(countryObj);
            }
            var spaceObj = customerRecordObj["fspace_ref"] as DynamicObject;
            model.Space = new ComboDataModel(spaceObj);
            model.Renovation = new ComboDataModel(renovationObj);

            //简单下拉框模型数据
            var phaseId = JNConvert.ToStringAndTrim(customerRecordObj["fphase"]);
            var phaseName = this.CustomerRecordForm.GetSimpleSelectItemText(customerRecordObj, "fphase");
            model.Phase = new ComboDataModel
            {
                Id = phaseId,
                Name = phaseName
            };
            //简单下拉框模型数据
            var typeId = JNConvert.ToStringAndTrim(customerRecordObj["ftype"]);
            var typeName = this.CustomerRecordForm.GetSimpleSelectItemText(customerRecordObj, "ftype");
            model.Type = new ComboDataModel
            {
                Id = typeId,
                Name = typeName
            };

            var customer = customerRecordObj["fcustomerid_ref"] as DynamicObject;
            model.Customer = MapToCustomer(customer);

            string intentionno = JNConvert.ToStringAndTrim(customerRecordObj["fintentionno"]);
            model.SaleIntention = MapToSaleIntention(intentionno);

            string orderno = JNConvert.ToStringAndTrim(customerRecordObj["forderno"]);
            model.Order = MapToOrder(orderno);

            // 附件
            model.Images =
                ImageFieldUtil.ParseImages(JNConvert.ToStringAndTrim(customerRecordObj["fimage"]),
                    JNConvert.ToStringAndTrim(customerRecordObj["fimage_txt"]));

            model.Contact = JNConvert.ToStringAndTrim(customerRecordObj["fcontacts"]);

            DateTime followTime = Convert.ToDateTime(customerRecordObj["ffollowtime"]);
            model.FollowTime = followTime == DateTime.MinValue ? (DateTime?)null : followTime;

            // 意向品类
            model.SaleCategory = new ComboDataModel
            {
                Id = JNConvert.ToStringAndTrim(customerRecordObj["fsalecategory_type"]),
                Name = JNConvert.ToStringAndTrim(customerRecordObj["fsalecategory_type_ref"])
            };
            //企业微信用户编码
            model.WorkWxUserid = JNConvert.ToStringAndTrim(customerRecordObj["fworkwxuserid"]);
            model.ProductType = JNConvert.ToStringAndTrim(customerRecordObj["fproduct_type"]);
            model.ProductTypeName = JNConvert.ToStringAndTrim(customerRecordObj["fproduct_type_txt"]);
            model.RequirementType = new ComboDataModel(requirementtypeObj);
            model.ProductSize = customerRecordObj["fproduct_size"].ToString();
            model.IntentionSign = new
            {
                id = Convert.ToBoolean(customerRecordObj["fintention_sign"]) == true ? 1 : 0,
                name = Convert.ToBoolean(customerRecordObj["fintention_sign"]) == true ? "是" : "否",
            };
            model.WillVisitType =
                 new
                 {
                     id = Convert.ToBoolean(customerRecordObj["fwill_visit_type"]) == true ? 1 : 0,
                     name = Convert.ToBoolean(customerRecordObj["fwill_visit_type"]) == true ? "是" : "否",
                 };
            model.VisitType =
                 new
                 {
                     id = Convert.ToBoolean(customerRecordObj["fvisit_type"]) == true ? 1 : 0,
                     name = Convert.ToBoolean(customerRecordObj["fvisit_type"]) == true ? "是" : "否",
                 };
            model.UrgencyType =
                 new
                 {
                     id = Convert.ToBoolean(customerRecordObj["furgency_type"]) == true ? 1 : 0,
                     name = Convert.ToBoolean(customerRecordObj["furgency_type"]) == true ? "是" : "否",
                 };
            model.DeliveryType =
                 new
                 {
                     id = Convert.ToBoolean(customerRecordObj["fdelivery_type"]) == true ? 1 : 0,
                     name = Convert.ToBoolean(customerRecordObj["fdelivery_type"]) == true ? "是" : "否",
                 };
            model.AddWechatType =
                 new
                 {
                     id = Convert.ToBoolean(customerRecordObj["fadd_wechat_type"]) == true ? 1 : 0,
                     name = Convert.ToBoolean(customerRecordObj["fadd_wechat_type"]) == true ? "是" : "否",
                 };
            //意向商品
            var intentionprod = customerRecordObj["fentry"] as DynamicObjectCollection;
            // 加载引用数据
            var metaModelService = Context.Container.GetService<IMetaModelService>();
            var refMg = Context.Container.GetService<LoadReferenceObjectManager>();
            var productForm = metaModelService.LoadFormModel(Context, "ydj_product");
            refMg.Load(Context, productForm.GetDynamicObjectType(Context), intentionprod?.Select(s => s["goods_id_ref"] as DynamicObject), false);

            var list = new List<Dictionary<string, object>>();
            foreach (var item in intentionprod)
            {
                Dictionary<string, object> dicprod = new Dictionary<string, object>();
                var materialObj = item["goods_id_ref"] as DynamicObject;
                var fbrandObj = materialObj?["fbrandid_ref"] as DynamicObject;
                dicprod["productId"] = Convert.ToString(item["goods_id"]);
                dicprod["productName"] = JNConvert.ToStringAndTrim(materialObj?["fname"]);
                dicprod["attentionNum"] = Convert.ToString(item["fattention_num"]);
                dicprod["fprice"] = Convert.ToString(item["fprice"]);
                dicprod["productNumber"] = JNConvert.ToStringAndTrim(materialObj?["fnumber"]);
                dicprod["specifica"] = JNConvert.ToStringAndTrim(materialObj?["fspecifica"]);
                dicprod["brandName"] = JNConvert.ToStringAndTrim(fbrandObj?["fname"]);
                dicprod["suitCombNumber"] = Convert.ToString(item["fsuitcombnumber"]);
                dicprod["isSuite"] = Convert.ToBoolean(materialObj?["fsuiteflag"]);
                dicprod["imageList"] = ImageFieldUtil.ParseImages(Convert.ToString(item["goods_id"]), JNConvert.ToStringAndTrim(materialObj?["fname"]), true);
                list.Add(dicprod);
            }
            model.prodList = list;
            return model;
        }

        private CustomerRecordCustomerModel MapToCustomer(DynamicObject customerObj)
        {
            if (customerObj == null)
            {
                return new CustomerRecordCustomerModel();
            }

            //简单下拉框模型数据
            var natureId = JNConvert.ToStringAndTrim(customerObj["fcusnature"]);
            var natureName = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer").GetSimpleSelectItemText(customerObj, "fcusnature");

            CustomerRecordCustomerModel model = new CustomerRecordCustomerModel(customerObj);

            model.Nature = new ComboDataModel
            {
                Id = natureId,
                Name = natureName + "客户"
            };

            return model;
        }

        private CustomerRecordSaleIntentionBillModel MapToSaleIntention(string billno)
        {
            if (billno.IsNullOrEmptyOrWhiteSpace())
            {
                return new CustomerRecordSaleIntentionBillModel();
            }

            var saleIntentionObj = this.Context.GetBizDataByNo("ydj_saleintention", billno, "fbillno", true);

            if (saleIntentionObj != null)
            {
                var model = new CustomerRecordSaleIntentionBillModel
                {
                    Id = JNConvert.ToStringAndTrim(saleIntentionObj["id"]),
                    Number = JNConvert.ToStringAndTrim(saleIntentionObj["fbillno"]),
                    CreateDate = Convert.ToDateTime(saleIntentionObj["fcreatedate"]),
                    BillAmount = Convert.ToDecimal(saleIntentionObj["ffbillamount"]),
                    Status = new ComboDataModel(saleIntentionObj["fstatus_ref"] as DynamicObject),
                    CancelStatus = Convert.ToBoolean(saleIntentionObj["fcancelstatus"]),
                    Designer = new StaffListModel(saleIntentionObj["fstylistid_ref"] as DynamicObject),
                    Measurer = new StaffListModel(saleIntentionObj["fmeasurerid_ref"] as DynamicObject)
                };
                return model;
            }
            return null;
        }

        private CustomerRecordRelatedBillModel MapToOrder(string billno)
        {
            if (billno.IsNullOrEmptyOrWhiteSpace())
            {
                return new CustomerRecordRelatedBillModel();
            }

            var orderObj = this.Context.GetBizDataByNo("ydj_order", billno, "fbillno", true);

            var model = new CustomerRecordRelatedBillModel
            {
                Id = JNConvert.ToStringAndTrim(orderObj["id"]),
                Number = JNConvert.ToStringAndTrim(orderObj["fbillno"]),
                CreateDate = Convert.ToDateTime(orderObj["fcreatedate"]),
                BillAmount = Convert.ToDecimal(orderObj["fdealamount"]),
                Status = new ComboDataModel(orderObj["fstatus_ref"] as DynamicObject),
                CancelStatus = Convert.ToBoolean(orderObj["fcancelstatus"])
            };

            return model;
        }
    }
}