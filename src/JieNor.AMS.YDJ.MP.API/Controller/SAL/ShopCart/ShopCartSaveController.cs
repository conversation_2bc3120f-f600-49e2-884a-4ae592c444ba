using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.ShopCart
{
    /// <summary>
    /// 微信小程序：购物车保存接口
    /// </summary>
    public class ShopCartSaveController : BaseController
    {
        /// <summary>
        /// 购物车表单模型
        /// </summary>
        protected HtmlForm ShopCartForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ShopCartSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //验证参数
            if (!this.Validate(dto, resp)) return resp;

            //加载商品信息
            var productObj = this.Context.LoadBizDataById("ydj_product", dto.ProductId);
            if (productObj == null)
            {
                resp.Message = "商品不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            //当前商品是否是套件商品
            var isSuite = Convert.ToBoolean(productObj["fsuiteflag"]);

            this.ShopCartForm = this.MetaModelService.LoadFormModel(this.Context, "sal_shopcart");
            var formDt = this.ShopCartForm.GetDynamicObjectType(this.Context);

            //存在则修改，不存在则新增
            DynamicObject shopCart = null;
            if (!dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                shopCart = this.ShopCartForm.GetBizDataById(this.Context, dto.Id);
            }
            if (shopCart == null)
            {
                shopCart = new DynamicObject(formDt);
            }

            shopCart["fproductid"] = dto.ProductId;

            //填充商品图片字段值
            //this.FillProductImageFieldValue(dto, shopCart);

            //填充辅助属性字段值
            this.FillAuxPropFieldValue(dto, shopCart);

            //保存前的预处理
            //保存前的预处理
            var prepareService = this.Container.GetService<IPrepareSaveDataService>();
            prepareService.PrepareDataEntity(this.Context, this.ShopCartForm, new DynamicObject[] { shopCart }, OperateOption.Create());

            ////如果是新增，则需要按照（商品、辅助属性、定制说明）做数据合并，这几个维度在商品已经在购物车中存在，则不做新增，只是对数量进行汇总
            ////增加仓库仓位库存状态维度做数据合并
            //if (!shopCart.DataEntityState.FromDatabase)
            //{
            //    var auxPropValId = Convert.ToString(shopCart["fattrinfo"]);
            //    var existsShopCart = this.CheckShopCartExists(dto, auxPropValId);
            //    if (existsShopCart != null)
            //    {
            //        dto.Qty += Convert.ToDecimal(existsShopCart["fqty"]); //合并数量
            //        existsShopCart["fmodifierid"] = shopCart["fmodifierid"];
            //        existsShopCart["fmodifydate"] = shopCart["fmodifydate"];
            //        shopCart = existsShopCart;
            //    }
            //}

            var amount = dto.Qty * dto.Price; //金额 = 单价 * 数量
            var dealAmount = dto.Qty * dto.DealPrice; //成交金额 = 成交单价 * 数量
            var distAmount = amount - dealAmount; //折扣额 = 金额 - 成交金额
            shopCart["fresultbrandid"] = dto.ResultBrandId;
            shopCart["fcustomdesc"] = dto.CustomDesc;
            shopCart["fsellprice"] = dto.SellPrice;
            shopCart["fsellamount"] = dto.SellAmount;
            shopCart["fqty"] = dto.Qty;
            shopCart["fprice"] = dto.Price;
            shopCart["famount"] = amount;
            shopCart["fdistrate"] = dto.DistRate;
            shopCart["fdealprice"] = dto.DealPrice;
            shopCart["fdealamount"] = dealAmount;
            shopCart["fdistamount"] = distAmount;
            shopCart["fisoutspot"] = dto.IsOutSpot;
            shopCart["fdeliverymode"] = dto.DeliveryMode;
            shopCart["fdescription"] = dto.Description;
            shopCart["fstorehouseid"] = dto.StoreHouseId ?? "";
            shopCart["fstorelocationid"] = dto.StoreLocationId ?? "";
            shopCart["fspace"] = dto.Space ?? "";
            shopCart["fstockstatus"] = dto.StockStatus ?? "";
            shopCart["funstdtype"] = dto.IsNonStandard;
            shopCart["fsuitproductid"] = dto.SuiteProductId ?? "";
            shopCart["fsuitcombnumber"] = dto.SuitCombNumber;
            //shopCart["fsuitdescription"] = dto.SuiteDesc;
            shopCart["fsubqty"] = dto.SubQty;
            shopCart["fpartscombnumber"] = dto.PartsCombNumber;
            shopCart["fisautocombparts"] = dto.IsAutoCombParts;
            shopCart["fparttype"] = dto.PartType;
            shopCart["fpartqty"] = dto.PartQty;
            shopCart["fsofacombnumber"] = dto.SofaCombNumber;
            shopCart["fiscombmain"] = dto.IsCombMain;
            shopCart["fisgiveaway"] = dto.IsGiveaway;
            shopCart["fprodrequirement"] = dto.ProdRequirement;
            shopCart["fselsuiterequire"] = dto.SelSuiteRequire;
            shopCart["fsuitesumqtyprop"] = dto.SuiteSumQtyPropId;
            shopCart["fpartprop"] = dto.PartPropId;
            shopCart["fbiztype"] = dto.fbiztype;

            FillProductImageFieldValue(dto, shopCart);

            //保存
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, formDt);
            dm.Save(shopCart);

            ////更新套件说明
            //UpdateSuiteDesc(this.Context, dto.SuitCombNumber);

            //设置响应数据包
            resp.Data = new { id = shopCart["id"] };
            resp.Message = "保存成功！";
            resp.Success = true;

            return resp;
        }

        ///// <summary>
        ///// 更新套件说明
        ///// </summary>
        ///// <param name="dto"></param>
        ///// <param name="shopCart"></param>
        //public static void UpdateSuiteDesc(UserContext ctx, string suitcombnumber)
        //{
        //    string shopcartId = null;
        //    var desc = GetSuiteDesc(ctx, suitcombnumber, ref shopcartId);
        //    if (!desc.IsNullOrEmptyOrWhiteSpace())
        //    {
        //        //保存套件信息
        //        var dm = ctx.Container?.GetService<IDataManager>();
        //        var metaService = ctx.Container?.GetService<IMetaModelService>();
        //        var bizForm = metaService?.LoadFormModel(ctx, "sal_shopcart");
        //        var formDt = bizForm.GetDynamicObjectType(ctx);
        //        dm.InitDbContext(ctx, formDt);
        //        var obj = bizForm.GetBizDataById(ctx, shopcartId);
        //        obj["fsuitdescription"] = desc;
        //        dm.Save(obj);
        //    }
        //}

//        public static string GetSuiteDesc(UserContext ctx, string suiteCombNumber, ref string shopCartId)
//        {
//            if (!suiteCombNumber.IsNullOrEmptyOrWhiteSpace())
//            {
//                var sqlText = @"
//SELECT c.fid, t.fpartdesc,c.fsubqty, (CASE WHEN c.fsuitproductid = c.fproductid THEN 1 ELSE 0 END) fsuiteflag
//FROM T_SAL_SHOPCART c
//LEFT JOIN (
//SELECT s.fproductid, e.fpartproductid, e.fpartdesc FROM T_SEL_SUITE s
//INNER JOIN T_SEL_SUITEENTRY e ON s.fid = e.fid
//) t on c.fproductid = t.fpartproductid AND c.fsuitproductid = t.fproductid
//WHERE c.fsuitcombnumber = @fsuitcombnumber";
//                var sqlParam = new List<SqlParam>
//                {
//                    new SqlParam("@fsuitcombnumber", System.Data.DbType.String, suiteCombNumber)
//                };
//                var data = ctx.ExecuteDynamicObject(sqlText, sqlParam);
//                var suite = data.FirstOrDefault(x => Convert.ToString(x["fsuiteflag"]) == "1");
//                if (suite == null) return null;

//                shopCartId = Convert.ToString(suite["fid"]);
//                var suiteqty = 0;
//                var desc = string.Join("; ",
//                    data.Where(x => Convert.ToString(x["fsuiteflag"]) != "1")
//                        .GroupBy(x => (Convert.ToString(x["fpartdesc"]) ?? ""))
//                        .Select(x =>
//                        {
//                            var subqty = x.Sum(o => Convert.ToInt32(o["fsubqty"]));
//                            suiteqty += subqty;
//                            return $"{x.Key}:{subqty}";
//                        })
//                        .Where(x => (x ?? "").Trim() != "")//上面统计完数据subqty再来去掉空子件说明
//                );
//                desc = $"套件总件数:{suiteqty}; " + desc;
//                return desc;
//            }

//            return null;
//        }

        /// <summary>
        /// 检查购物车中是否已存在指定的商品
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="auxPropValId"></param>
        /// <returns></returns>
        private DynamicObject CheckShopCartExists(ShopCartSaveDTO dto, string auxPropValId)
        {
            var customDesc = dto.CustomDesc?.Trim() ?? "";

            var sqlWhere = "fmainorgid=@fmainorgid and fcreatorid=@fcreatorid and fproductid=@fproductid and fattrinfo=@fattrinfo and fcustomdesc=@fcustomdesc and fstorehouseid=@fstorehouseid and    fstorelocationid=@fstorelocationid and fstockstatus=@fstockstatus";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fcreatorid", System.Data.DbType.String, this.Context.UserId),
                new SqlParam("@fproductid", System.Data.DbType.String, dto.ProductId),
                new SqlParam("@fattrinfo", System.Data.DbType.String, auxPropValId),
                new SqlParam("@fcustomdesc", System.Data.DbType.String, customDesc),
                new SqlParam("@fstorehouseid",System.Data.DbType.String,dto.StoreHouseId),
                new SqlParam("@fstorelocationid",System.Data.DbType.String,dto.StoreLocationId),
                new SqlParam("@fstockstatus",System.Data.DbType.String,dto.StockStatus)
            };

            var myShopCarts = this.ShopCartForm.GetBizDataByWhere(this.Context, sqlWhere, sqlParam);

            return myShopCarts.FirstOrDefault();
        }

        /// <summary>
        /// 填充商品图片字段值
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="shopCart"></param>
        private void FillProductImageFieldValue(ShopCartSaveDTO dto, DynamicObject shopCart)
        {
            // 商品图片列表
            if (dto.ImageList == null || dto.ImageList.Count == 0)
            {
                // 没有图片时，使用商品默认图片（兼容）
                dto.ImageList = ProductUtil.GetImages(this.Context, dto.ProductId, dto.AuxPropVals, dto.CustomDesc);
            }

            var mtrlImage = string.Join(",", dto.ImageList.Select(o => o.Id));
            var mtrlImageTxt = string.Join(",", dto.ImageList.Select(o => o.Name));
            shopCart["fmtrlimage"] = mtrlImage;
            shopCart["fmtrlimage_txt"] = mtrlImageTxt;
        }



        /// <summary>
        /// 填充辅助属性字段值
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="shopCart"></param>
        private void FillAuxPropFieldValue(ShopCartSaveDTO dto, DynamicObject shopCart)
        {
            var auxPropValSetForm = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvalueset");
            var auxPropValSetObj = new DynamicObject(auxPropValSetForm.GetDynamicObjectType(this.Context));
            auxPropValSetObj["fmaterialid"] = dto.ProductId;
            var auxPropValSetEntity = auxPropValSetForm.GetEntryEntity("FEntity");
            var auxPropValSetEntrys = auxPropValSetObj["FEntity"] as DynamicObjectCollection;

            if (dto.AuxPropVals != null)
            {
                foreach (var item in dto.AuxPropVals)
                {
                    var auxPropValSetEntry = new DynamicObject(auxPropValSetEntity.DynamicObjectType);
                    if (item.ContainsKey("auxPropId"))
                    {
                        auxPropValSetEntry["fauxpropid"] = (string)item["auxPropId"];
                    }
                    if (item.ContainsKey("valueId"))
                    {
                        auxPropValSetEntry["fvalueid"] = (string)item["valueId"];
                    }
                    
                    if (item.ContainsKey("valueName"))
                    {
                        auxPropValSetEntry["fvaluename"] = (string)item["valueName"];
                    }
                    
                    if (item.ContainsKey("valueNumber"))
                    {
                        auxPropValSetEntry["fvaluenumber"] = (string)item["valueNumber"];
                    }   
                    auxPropValSetEntrys.Add(auxPropValSetEntry);
                }
            }

            shopCart["fattrinfo_ref"] = auxPropValSetObj;
        }

        /// <summary>
        /// 验证参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Validate(ShopCartSaveDTO dto, BaseResponse<object> resp)
        {
            if (dto.ProductId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"商品参数 productId 不能为空！";
                return false;
            }
            if (dto.Qty < 1)
            {
                resp.Success = false;
                resp.Message = $"数量参数 qty 不能小于1！";
                return false;
            }
            //if (dto.Price < 1)
            //{
            //    resp.Success = false;
            //    resp.Message = $"单价参数 price 不能小于1！";
            //    return false;
            //}
            //if (dto.DealPrice < 1)
            //{
            //    resp.Success = false;
            //    resp.Message = $"成交单价参数 dealPrice 不能小于1！";
            //    return false;
            //}
            return true;
        }
    }
}