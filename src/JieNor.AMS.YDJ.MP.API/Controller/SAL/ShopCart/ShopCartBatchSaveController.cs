using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.ShopCart
{
    /// <summary>
    /// 微信小程序：购物车保存接口
    /// </summary>
    public class ShopCartBatchSaveController : BaseController
    {
        /// <summary>
        /// 购物车表单模型
        /// </summary>
        protected HtmlForm ShopCartForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ShopCartBatchSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //套件批量添加时的处理
            bool isSuite = false;
            if (!ValidateSuiteData(dto, resp, ref isSuite))
            {
                return resp;
            }

            //配件批量添加时的处理
            bool isParts = false;
            if (!ValidatePartsData(dto, resp, ref isParts))
            {
                return resp;
            }

            //调用基础服务
            foreach (var item in dto.List)
            {
                resp = this.Gateway.Send<BaseResponse<object>>(item);
                if(!resp.Success)
                {
                    return resp;
                }
            }

            //if (isSuite)
            //{
            //    //更新套件描述
            //    var suitCombNumber = dto.List.FirstOrDefault().SuitCombNumber;
            //    ShopCartSaveController.UpdateSuiteDesc(this.Context, suitCombNumber);
            //}

            resp.Success = true;
            resp.Message = "成功";
            return resp;
        }

        private bool ValidateSuiteData(ShopCartBatchSaveDTO dto, BaseResponse<object> resp, ref bool isSuite)
        {

            if (dto.List.Count > 0 && dto.List.All(x => !x.SuitCombNumber.IsNullOrEmptyOrWhiteSpace()))
            {
                var suiteDict = dto.List.GroupBy(x => x.SuitCombNumber);
                if (suiteDict.Count() == 1) //所有都为同一个套件组合码时，判断为套件批量保存
                {
                    if (!dto.List.Any(x => x.IsSuite))
                    {
                        resp.Success = false;
                        resp.Message = $"不存在套件商品";
                        return false;
                    }

                    isSuite = true;
                    return true;
                }
            }
            
            return true;

        }

        private bool ValidatePartsData(ShopCartBatchSaveDTO dto, BaseResponse<object> resp, ref bool isParts)
        {

            if (dto.List.Count > 0 && dto.List.All(x => !x.PartsCombNumber.IsNullOrEmptyOrWhiteSpace()))
            {
                var partsDict = dto.List.GroupBy(x => x.PartsCombNumber);
                if (partsDict.Count() == 1) //所有都为同一个配件组合码时，判断为配件批量保存
                {
                    if (!dto.List.Any(x => x.IsCombMain))
                    {
                        resp.Success = false;
                        resp.Message = $"不存在配件主商品！";
                        return false;
                    }

                    isParts = true;
                    return true;
                }
            }

            return true;

        }



        /// <summary>
        /// 验证参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Validate(ShopCartBatchSaveDTO dto, BaseResponse<object> resp)
        {
            if(!dto.List.Any(x => x.IsSuite))
            {
                resp.Success = false;
                resp.Message = $"不存在套件商品";
                return false;
            }

            return true;
        }
    }
}