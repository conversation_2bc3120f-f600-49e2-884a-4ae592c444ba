using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Model.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.DataAnalysis
{
    /// <summary>
    /// 统计分析-数据简报
    /// </summary>
    public class StatisticAnalysisBulletinController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(StatisticAnalysisBulletinDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //参数验证
            if (!CalibrationParas(dto, resp))
                return resp;

            //执行SQL
            StringBuilder strSql = new StringBuilder();

            //查询SQL组装
            GetSql(dto, strSql);

            var list = new List<DataResultModel>();
            using (var reader = this.DBService.ExecuteReader(this.Context, strSql.ToString()))
            {
                AddDataModel(list, reader);
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = list;

            return resp;
        }

        /// <summary>
        /// 数据组装
        /// </summary>
        /// <param name="list"></param>
        /// <param name="reader"></param>
        private void AddDataModel(List<DataResultModel> list, IDataReader reader)
        {
            if (reader.Read())
            {
                //list.Add(new DataResultModel("businessnum", "报备商机数", Convert.ToInt32(reader["businessnum"])));
                list.Add(new DataResultModel("customernum", "成交客户数", Convert.ToInt32(reader["customernum"])));
               // list.Add(new DataResultModel("newintentionnum", "新增销售意向数", Convert.ToInt32(reader["newintentionnum"])));
              //  list.Add(new DataResultModel("intentionmoney", "未成单意向额", Convert.ToDecimal(reader["intentionmoney"])));
                list.Add(new DataResultModel("newcontractnum", "新增合同数", Convert.ToInt32(reader["newcontractnum"])));
                list.Add(new DataResultModel("salemoney", "已审核销售额", Convert.ToDecimal(reader["salemoney"])));
            }
        }

        /// <summary>
        /// 查询组装
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="strSql"></param>
        /// <param name="joinTb1"></param>
        /// <param name="joinTb2"></param>
        /// <param name="strWhere"></param>
        private void GetSql(StatisticAnalysisBulletinDTO dto, StringBuilder strSql)
        {


            //连接表
            StringBuilder joinTb1 = new StringBuilder();
            //连接表
            StringBuilder joinTb2 = new StringBuilder();
            StringBuilder joinTb3 = new StringBuilder();
            //条件
            StringBuilder strWhere1 = new StringBuilder();
            //条件
            StringBuilder strWhere2 = new StringBuilder();
            StringBuilder strWhere3 = new StringBuilder();

            //时间1
            StringBuilder strtime1 = new StringBuilder();
            //时间2
            StringBuilder strtime2 = new StringBuilder();
            //时间3
            StringBuilder strtime3 = new StringBuilder();
            //时间4
            StringBuilder strtime4 = new StringBuilder();

            //查询指定人员
            if (!dto.PersonId.IsNullOrEmptyOrWhiteSpace() && dto.PermCaption != "本人")
            {
                joinTb1.Append(" inner join t_bd_staff t2 on t1.fdutyid=t2.fid  ");
                joinTb2.Append(" inner join t_ydj_orderduty t2 on t1.fid=t2.fid  ");
                joinTb3.Append(" inner join t_ydj_customerdutyentry t2 on t1.fid=t2.fid  ");
                strWhere1.Append(string.Format(@" and t2.fid='{0}'", dto.PersonId));
                strWhere2.Append(string.Format(@" and t2.fdutyid='{0}'", dto.PersonId));
            }
            //指定部门
            if (!dto.DeptId.IsNullOrEmptyOrWhiteSpace())
            {
                joinTb2.Append(" inner join t_ydj_orderduty t2 on t1.fid=t2.fid  ");
                joinTb3.Append(" inner join t_ydj_customerdutyentry t2 on t1.fid=t2.fid  ");
                //部门条件
                string depts = "";
                var list = this.Context.GetCurrentDeptIdsByDept(dto.DeptId);
                foreach (var val in list)
                {
                    depts += string.Format(@"'{0}',", val);
                }
                depts += "'" + dto.DeptId + "'";
                strWhere1.Append(string.Format(@" and t1.fdeptid in ({0})", depts.TrimEnd(',')));
                strWhere2.Append(string.Format(@" and t2.fdeptid in ({0})", depts.TrimEnd(',')));
            }
            //销售百分比，当销售额为本企业时，设置为空
            string fratio = "*t2.fratio/100";
            //条件类型
            if (dto.PermCaption == "本人")
            {
                joinTb1.Append(" inner join t_bd_staff t2 on t1.fdutyid=t2.fid  ");
                joinTb2.Append(" inner join t_ydj_orderduty t2 on t1.fid=t2.fid  ");
                joinTb3.Append(" inner join t_ydj_customerdutyentry t2 on t1.fid=t2.fid  ");
                strWhere1.Append(string.Format(@" and t2.fid='{0}'", this.Context.GetCurrentStaffId()));
                strWhere2.Append(string.Format(@" and t2.fdutyid='{0}'", this.Context.GetCurrentStaffId()));
            }
            else if (dto.PermCaption == "本部门")
            {
                joinTb2.Append(" inner join t_ydj_orderduty t2 on t1.fid=t2.fid  ");
                joinTb3.Append(" inner join t_ydj_customerdutyentry t2 on t1.fid=t2.fid  ");
                //部门条件
                string deptStr = "";
                var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
                var subDeptIds = baseFormProvider.GetMySubordinates(this.Context).Select(x => x.Id).ToList();

                //获取本部门和子部门
                var deptlist = this.Context.GetDeptIds(this.Context.GetCurrentDeptId());

                foreach (var val in subDeptIds)
                {
                    deptStr += string.Format(@"'{0}',", val);
                }
                deptStr += "'本部门'";
                strWhere1.Append(string.Format(@" and t1.fdeptid in ({0})", deptStr.TrimEnd(',')));
                strWhere2.Append(string.Format(@" and t2.fdeptid in ({0})", deptStr.TrimEnd(',')));
            }
            else if (dto.PermCaption == "本企业")
            {
                fratio = "";
                strWhere1.Append(string.Format(@" and t1.fmainorgid='{0}'", this.Context.Company));
                strWhere2.Append(string.Format(@" and t1.fmainorgid='{0}'", this.Context.Company));
            }

            //查询条件组装
            strtime1.Append(string.Format(@" and t1.fgoshopdate>='{0}' and t1.fgoshopdate<='{1}'", dto.StartDate, dto.EndDate));
            strtime2.Append(string.Format(@" and t1.fdate>='{0}' and t1.fdate<='{1}'", dto.StartDate, dto.EndDate));
            strtime3.Append(string.Format(@" and t1.ffirstordertime>='{0}' and t1.ffirstordertime<='{1}'", dto.StartDate, dto.EndDate));
            strtime4.Append(string.Format(@" and t1.forderdate>='{0}' and t1.forderdate<='{1}'", dto.StartDate, dto.EndDate));
            //列结果数据
            strSql.Append($@"select (select count(1) businessnum from t_ydj_customerrecord t1 {joinTb1} where t1.fcancelstatus=0 and t1.fchancestatus!='chance_status_04' {strWhere1}{strtime1}) businessnum,");
            strSql.Append($@"(select count(1) customernum from t_ydj_customer t1 with(nolock)  {joinTb3} where t1.fcusnature='cusnature_02' and t1.fforbidstatus = 0 {strWhere2}{strtime3}) customernum,");
            strSql.Append($@"(select count(1) newintentionnum from (select t1.fid from t_ydj_saleintention t1 {joinTb2}  where t1.fcancelstatus=0 {strWhere2}{strtime2} group by t1.fid) tb) newintentionnum,");
            strSql.Append($@"(select CONVERT(decimal(18,2),isnull(sum(ffbillamount{fratio}),0)) intentionmoney from t_ydj_saleintention t1 {joinTb2} where t1.fispushorder=0 and t1.fcancelstatus=0  {strWhere2}{strtime2}) intentionmoney,");
            strSql.Append($@"(select count(1) newcontractnum from (select t1.fid from T_YDJ_ORDER t1 with(nolock)  {joinTb2}  where t1.fcancelstatus=0 {strWhere2}{strtime4} group by t1.fid ) tb) newcontractnum,");
            strSql.Append($@"(select CONVERT(decimal(18,2),isnull(sum(fdealamount{fratio}),0)) salemoney from T_YDJ_ORDER t1 with(nolock)  {joinTb2}  where t1.fcancelstatus=0 and t1.fstatus='E' {strWhere2}{strtime4}) salemoney ");
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <returns></returns>
        private bool CalibrationParas(StatisticAnalysisBulletinDTO dto, BaseResponse<object> resp)
        {
            if (dto.TimeType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"时间类型 TimeType 不能为空！";
                return false;
            }

            DateTime t1 = DateTime.Now;
            DateTime t2 = DateTime.Now;
            if (dto.TimeType != "自定义")
            {
                TimeHelp.GetDateByType(dto.TimeType, out t1, out t2);
                dto.StartDate = t1;
                dto.EndDate = t2;
                return true;
            }

            if (!DateTime.TryParse(dto.StartDate.ToString(), out t1))
            {
                resp.Success = false;
                resp.Message = $"开始日期 StartDate 不能为空！";
                return false;
            }

            if (!DateTime.TryParse(dto.EndDate.ToString(), out t2))
            {
                resp.Success = false;
                resp.Message = $"结束日期 EndDate 不能为空！";
                return false;
            }

            return true;
        }
    }
}
