using JieNor.AMS.YDJ.MP.API.DTO.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Model.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.DataAnalysis
{
    /// <summary>
    /// 统计分析-部门排名
    /// </summary>
    public class RankingDeptController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(RankingDeptDto dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //参数验证
            if (!CalibrationParas(dto, resp))
                return resp;

            //执行SQL
            StringBuilder strSql = new StringBuilder();

            //查询SQL组装
            GetSql(dto, strSql);

            var list = new List<RankingDeptModel>();
            var item = this.DBService.ExecuteDynamicObject(this.Context, strSql.ToString());
            foreach (var val in item)
            {
                list.Add(new RankingDeptModel(JNConvert.ToStringAndTrim(val["fname"]),
                       Convert.ToDecimal(val["fdealamount"])));
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = list;

            return resp;
        }

        /// <summary>
        /// 查询组装
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="strSql"></param>
        /// <param name="joinTb1"></param>
        /// <param name="joinTb2"></param>
        /// <param name="strWhere"></param>
        private void GetSql(RankingDeptDto dto, StringBuilder strSql)
        {
            //条件
            StringBuilder strWhere = new StringBuilder();

            //查询条件组装
            strWhere.Append(string.Format(@" and t1.forderdate>='{0}' and t1.forderdate<='{1}'", dto.StartDate, dto.EndDate));
            if (!this.Context.Company.Equals(this.Context.TopCompanyId))
            {
                strWhere.Append(string.Format(@" and t1.fmainorgid='{0}' ", this.Context.Company));
            }
            //列结果数据
            strSql.Append($@"select top 6 d1.fname,convert(decimal(18, 2), fdealamount/10000) fdealamount from( ");
            strSql.Append($@"select convert(decimal(18,6),sum(fdealamount/100)) fdealamount,t1.fdeptid,t1.fmainorgid from t_ydj_order t1 with(nolock) ");
            strSql.Append($@"where t1.fstatus = 'E'and t1.fcancelstatus = 0  {strWhere} ");
            strSql.Append($@"group by t1.fdeptid,t1.fmainorgid) tb ");
            strSql.Append($@"left join t_bd_department d1 with(nolock) on tb.fdeptid = d1.fid ");
            strSql.Append($@"order by convert(decimal(18, 6), fdealamount/10000) desc,fdeptid desc ");
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <returns></returns>
        private bool CalibrationParas(RankingDeptDto dto, BaseResponse<object> resp)
        {
            if (dto.TimeType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"时间类型 TimeType 不能为空！";
                return false;
            }

            DateTime t1 = DateTime.Now;
            DateTime t2 = DateTime.Now;
            if (dto.TimeType != "自定义")
            {
                TimeHelp.GetDateByType(dto.TimeType, out t1, out t2);
                dto.StartDate = t1;
                dto.EndDate = t2;
                return true;
            }

            if (!DateTime.TryParse(dto.StartDate.ToString(), out t1))
            {
                resp.Success = false;
                resp.Message = $"开始日期 StartDate 不能为空！";
                return false;
            }

            if (!DateTime.TryParse(dto.EndDate.ToString(), out t2))
            {
                resp.Success = false;
                resp.Message = $"结束日期 EndDate 不能为空！";
                return false;
            }

            return true;
        }
    }
}
