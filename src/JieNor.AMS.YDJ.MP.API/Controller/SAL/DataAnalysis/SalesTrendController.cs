using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Model.SAL.DataAnalysis;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.DataAnalysis
{
    /// <summary>
    /// 统计分析-销售趋势
    /// </summary>
    public class SalesTrendController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(SalesTrendDTo dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();
            //参数验证
            if (!CalibrationParas(dto, resp))
                return resp;

            //执行今年SQL
            StringBuilder strYearSql = new StringBuilder();
            //执行去年SQL
            StringBuilder strYesterYearSql = new StringBuilder();

            //查询SQL组装
            GetSql(dto, strYearSql, strYesterYearSql);
            //今年
            var listyear = new List<SalesTrendModel>();
            using (var dr = this.DBService.ExecuteReader(this.Context, strYearSql.ToString()))
            {
                while (dr.Read())
                {
                    listyear.Add(new SalesTrendModel(JNConvert.ToStringAndTrim(dr["ymonth"]),
                          Convert.ToDecimal(dr["fdealamount"])));
                }
            }
            //去年
            var listYesterYear = new List<SalesTrendModel>();
            using (var dr = this.DBService.ExecuteReader(this.Context, strYesterYearSql.ToString()))
            {
                while (dr.Read())
                {
                    listYesterYear.Add(new SalesTrendModel(JNConvert.ToStringAndTrim(dr["yestermonth"]),
                          Convert.ToDecimal(dr["fdealamount"])));
                }
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = new { thisyearData = listyear, yesterYearData = listYesterYear };

            return resp;
        }

        /// <summary>
        /// 查询组装
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="strSql"></param>
        /// <param name="joinTb1"></param>
        /// <param name="joinTb2"></param>
        /// <param name="strWhere"></param>
        private void GetSql(SalesTrendDTo dto, StringBuilder strYearSql, StringBuilder strYesterYearSql)
        {
            //条件
            StringBuilder strWhere = new StringBuilder();
            //查询指定人员
            if (!dto.PersonId.IsNullOrEmptyOrWhiteSpace() && dto.PermCaption != "本人")
            {
                strWhere.Append(string.Format(@" and t1.fstaffid='{0}'", dto.PersonId));
            }
            //指定部门
            if (!dto.DeptId.IsNullOrEmptyOrWhiteSpace())
            {
                //部门条件
                string deptStr = "";
                var depts = this.Context.GetCurrentDeptIdsByDept(dto.DeptId);
                foreach (var val in depts)
                {
                    deptStr += string.Format(@"'{0}',", val);
                }
                deptStr += "'" + dto.DeptId + "'";
                strWhere.Append(string.Format(@" and t1.fdeptid in ({0})", deptStr.TrimEnd(',')));
            }
            //条件类型
            if (dto.PermCaption == "本人")
            {
                strWhere.Append(string.Format(@" and t1.fstaffid='{0}'", this.Context.GetCurrentStaffId()));
            }
            else if (dto.PermCaption == "本部门")
            {
                string deptStr = "";
                var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
                var subDeptIds = baseFormProvider.GetMySubordinates(this.Context).Select(x => x.Id).ToList();

                //获取本部门和子部门
                var deptlist = this.Context.GetDeptIds(this.Context.GetCurrentDeptId());

                foreach (var val in subDeptIds)
                {
                    deptStr += string.Format(@"'{0}',", val);
                }
                deptStr += "'本部门'";
                strWhere.Append(string.Format(@" and t1.fdeptid in ({0})", deptStr.TrimEnd(',')));
            }
            else if (dto.PermCaption == "本企业")
            {
                strWhere.Append(string.Format(@" and t1.fmainorgid='{0}'", this.Context.Company));
            }

            DateTime t1 = DateTime.Now;
            DateTime t2 = DateTime.Now;
            TimeHelp.GetDateByType("今年", out t1, out t2);
            StringBuilder thistime = new StringBuilder();
            thistime.Append(string.Format(@" and t1.fcreatedate>='{0}' and t1.fcreatedate<='{1}'", t1, t2));
            //今年
            strYearSql.Append("/*dialect*/");
            strYearSql.Append($@"select ymonth,isnull(convert(decimal(18,2),tb.fdealamount,2),0) fdealamount from ( ");
            strYearSql.Append($@"select datename(YEAR,fcreatedate)+'-'+datename(MONTH,fcreatedate) as ftime,sum(fdealamount)/10000 as fdealamount ");
            strYearSql.Append($@"from T_YDJ_ORDER t1 with(nolock) where fcancelstatus=0 and fstatus='E' {strWhere} {thistime} ");
            strYearSql.Append($@" group by datename(YEAR,fcreatedate),datename(MONTH,fcreatedate) ) tb right join ({ThisYearSql()}) t1 on tb.ftime = t1.ymonth order by t1.ymonth");

            StringBuilder yestertime = new StringBuilder();
            TimeHelp.GetDateByType("去年", out t1, out t2);
            yestertime.Append(string.Format(@" and t1.fcreatedate>='{0}' and t1.fcreatedate<='{1}'", t1, t2));
            //去年
            strYesterYearSql.Append("/*dialect*/");
            strYesterYearSql.Append($@"select yestermonth,isnull(convert(decimal(18,2),tb.fdealamount,2),0) fdealamount from ( ");
            strYesterYearSql.Append($@"select datename(YEAR,fcreatedate)+'-'+datename(MONTH,fcreatedate) as ftime,sum(fdealamount)/10000 as fdealamount ");
            strYesterYearSql.Append($@"from T_YDJ_ORDER t1 with(nolock) where t1.fcancelstatus=0 and t1.fstatus='E' {strWhere}{yestertime} ");
            strYesterYearSql.Append($@"group by datename(YEAR,fcreatedate),datename(MONTH,fcreatedate) ");
            strYesterYearSql.Append($@") tb right join ({YesterYearSql()}) t1 on tb.ftime = t1.yestermonth order by t1.yestermonth");

        }

        /// <summary>
        /// 今年月
        /// </summary>
        /// <returns></returns>
        private string ThisYearSql()
        {
            return string.Format(@"SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-01' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-02' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-03' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-04' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-05' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-06' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-07' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-08' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-09' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-10' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-11' as ymonth
union all
SELECT CONVERT(varchar(10),YEAR(GetDate()))+'-12' as ymonth");
        }

        /// <summary>
        /// 去年月
        /// </summary>
        /// <returns></returns>
        private string YesterYearSql()
        {
            return string.Format(@"select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-01' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-02' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-03' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-04' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-05' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-06' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-07' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-08' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-09' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-10' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-11' as yestermonth
union all
select CONVERT(varchar(10),YEAR(dateAdd(yy,-1,getdate())))+'-12' as yestermonth");
        }

        /// <summary>
        /// 参数验证
        /// </summary>
        /// <returns></returns>
        private bool CalibrationParas(SalesTrendDTo dto, BaseResponse<object> resp)
        {
            if (dto.PermCaption.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"条件类型 PermCaption 不能为空！";
                return false;
            }
            return true;
        }
    }
}
