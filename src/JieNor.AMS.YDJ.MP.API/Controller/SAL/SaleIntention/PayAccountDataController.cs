using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO.SAL.SaleIntention;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.SAL.SaleIntention;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.SAL.SaleIntention;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.SaleIntention
{
    /// <summary>
    /// 收款账户
    /// </summary>
    public class PayAccountDataController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(PayAccountDataDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<PayAccountDataModel>();

            //// 向麦浩系统发送请求
            //var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
            //    this.Request,
            //    new CommonBillDTO()
            //    {
            //        FormId = "ydj_saleintention",
            //        OperationNo = "LoadSettleInfo",
            //        SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } },
            //        PageId = Guid.NewGuid().ToString("N"),
            //        SimpleData = new Dictionary<string, string>
            //        {
            //            { "settleType", "receipt" }
            //        }
            //    });
            //var result = response?.OperationResult;
            //resp = result.ToResponseModel<PayAccountDataModel>(false);
            //if (!resp.Success)
            //{
            //    return resp;
            //}
            //var srvData = result?.SrvData as string;

            //if (srvData.IsNullOrEmptyOrWhiteSpace())
            //{
            //    resp.Success = false;
            //    resp.Message = "下推收款单失败！";
            //    return resp;
            //}

            //LoadSettleInfoResponse loadSettleInfoResponse =
            //   JsonConvert.DeserializeObject<LoadSettleInfoResponse>(srvData);

            // 获取所有银行账号
            resp.Data.ComboData.Merge(this.Context.GetBankDataSource());
            //获取协同方银行账号信息下拉框数据源
            //resp.Data.ComboData.Merge(GetSynBankNum(loadSettleInfoResponse));
            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 获取协同银行账号
        /// </summary>
        /// <param name="loadSettleInfoResponse"></param>
        /// <returns></returns>
        private IDictionary<string, List<Dictionary<string, object>>> GetSynBankNum(LoadSettleInfoResponse loadSettleInfoResponse)
        {
            Dictionary<string, List<Dictionary<string, object>>> res = new Dictionary<string, List<Dictionary<string, object>>>();
            var synBankNumDic = new List<Dictionary<string, object>>();
            if (loadSettleInfoResponse.synBankNum != null && loadSettleInfoResponse.synBankNum.Count > 0)
            {
                synBankNumDic = (loadSettleInfoResponse.synBankNum.ConvertAll<Dictionary<string, object>>(o => new Dictionary<string, object> { { "id", o.accountId }, { "name", o.bankNum + " " + o.accountName + " " + o.bankName }, { "accountName", o.accountName }, { "number", o.bankNum }, { "bankName", o.bankName } }));
            }
            res.Add("fsynbank", synBankNumDic);
            return res;
        }
    }
}
