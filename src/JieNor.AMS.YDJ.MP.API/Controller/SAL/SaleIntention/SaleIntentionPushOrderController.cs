using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using JieNor.AMS.YDJ.MP.API.Controller.STE.Order;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.STE.Order.PushOrder;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.SaleIntention
{
    /// <summary>
    /// 微信小程序：意向单转合同取数接口
    /// </summary>
    public class SaleIntentionPushOrderController : BaseController
    {
        static List<string> saleIntentionIDs;
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(SaleIntentionPushOrderDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<OrderEditModel>();

            if (dto.IDs == null || dto.IDs.Count == 0)
            {
                resp.Message = "参数 id 不能为空！";
                resp.Success = false;
                resp.Data = null;
                return resp;
            }
            saleIntentionIDs = dto.IDs;
            if (dto.IDs.Count > 0) dto.Id = dto.IDs[0];
            // 判断是否已下推合同
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_saleintention");
            var saleIntentionObj = htmlForm.GetBizDataById(this.Context, dto.Id);

            return DoExecute(this.Context, this.Request, saleIntentionObj,dto);
        }

        public static object DoExecute(UserContext userCtx, ServiceStack.Web.IRequest request, DynamicObject saleIntentionObj, BaseDetailDTO dto)
        {
            var resp = new BaseResponse<OrderEditModel>();
            if (saleIntentionObj == null)
            {
                resp.Message = "意向单不存在或已删除，不允许操作！";
                resp.Success = false;
                resp.Data = null;
                return resp;
            }

            bool isPushed = JNConvert.ToStringAndTrim(saleIntentionObj["forderid"]).IsNullOrEmptyOrWhiteSpace() == false;
            //取消 意向单根据【已下推合同】判断是否下推合同的校验
            isPushed = false;
            if (isPushed)
            {
                OrderEditDTO orderEditDto = new OrderEditDTO { Id = JNConvert.ToStringAndTrim(saleIntentionObj["forderid"]) };

                return OrderEditController.DoExecute(userCtx, orderEditDto);
            }
            string saleIntentionId = JNConvert.ToStringAndTrim(saleIntentionObj["id"]);
            var lstRows = new List<SelectedRow>();
            if (saleIntentionIDs?.Count > 1)
            {
                for (var i = 0; i < saleIntentionIDs.Count; i++)
                {
                    lstRows.Add(new SelectedRow()
                    {
                        PkValue = Convert.ToString(saleIntentionIDs[i])
                    });
                }
            }
            else
            {
                lstRows.Add(new SelectedRow()
                {
                    PkValue = saleIntentionId
                });
            }


            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                request,
                new CommonBillDTO()
                {
                    FormId = "ydj_saleintention",
                    OperationNo = "Push",
                    SelectedRows = lstRows,
                    PageId = Guid.NewGuid().ToString("N"),
                    SimpleData = new Dictionary<string, string>
                    {
                        { "ruleid", "ydj_saleintention2ydj_order" }
                    }
                });

            var result = response?.OperationResult;
            if (result == null || !result.IsSuccess)
            {
                resp.Success = false;
                resp.Message = result.GetErrorMessage("下推合同报错，请检查！");
                return resp;
            }

            string srvData = result?.SrvData as string;

            if (srvData == null)
            {
                resp.Message = "下推合同报错，请检查！";
                resp.Success = false;
                return resp;
            }

            var uiData = (JObject.Parse(srvData)?["fsps"] as JArray)?[0]?["uiData"];

            if (uiData == null || uiData.ToString().IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "下推合同报错，请检查！";
                resp.Success = false;
                return resp;
            }

            PushOrderResponse pushOrderResponse = JsonConvert.DeserializeObject<PushOrderResponse>(uiData.ToString());

            resp.Data = OrderEditController.Push(pushOrderResponse, userCtx);
            if (dto.SrcFormId.IsNullOrEmptyOrWhiteSpace())
            {
                dto.SrcFormId = "ydj_saleintention";
            }
            // 查询所有门店
            resp.Data.ComboData = userCtx.GetDeptDataSource(dto);
            // 获取合同的辅助资料和简单下拉框
            resp.Data.ComboData.Merge(userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "ydj_order")
                .GetComboDataSource(userCtx, "fdeliverymode"));
            //单据类型 
            resp.Data.ComboData.Merge(BillUtil.GetBillTypeByBizobject(userCtx, "ydj_order"));

            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }
    }
}