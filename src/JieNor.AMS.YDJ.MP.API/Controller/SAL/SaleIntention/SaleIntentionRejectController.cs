using JieNor.AMS.YDJ.MP.API.DTO.SAL.SaleIntention;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.SaleIntention
{
    /// <summary>
    /// 微信小程序：意向单反审核接口
    /// </summary>
    public class SaleIntentionRejectController: BaseSaleIntentionController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(SaleIntentionRejectDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }
            //调用通用反审核方法
            return ApprovalHelper.Reject(this.Context, this.Request, "ydj_saleintention", dto.Id, dto.Reason, dto.Terminate);
        }
    }
}
