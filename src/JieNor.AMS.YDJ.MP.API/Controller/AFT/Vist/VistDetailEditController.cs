using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    /// <summary>
    /// 回访单主动评价取数接口
    /// </summary>
    public class VistDetailEditController : BaseController
    {
        /// <summary>
        /// 回访单表单模型
        /// </summary>
        protected HtmlForm VistForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(VisteDetailEditDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<VistDetailEditModel>();
            try
            {
                if (dto.serId.IsNullOrEmptyOrWhiteSpace()/*&&dto.feedBackId.IsNullOrEmptyOrWhiteSpace()*/)
                {
                    resp.Message = "请按照格式传入指定参数！";
                    return resp;
                }
                var serForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_service");
                var serObj = serForm.GetBizDataById(Context, dto.serId, true);
                if (serObj == null)
                {
                    resp.Message = "此{0}不存在或已删除！".Fmt("服务单");
                    return resp;
                }
                string msg = string.Empty;
                var vistInfo = GetVistInfo(serObj, out msg);
                if (!msg.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Message = msg;
                    return resp;
                }
                var masterObj = serObj["fmasterid_ref"] as DynamicObject;
                if (vistInfo["fstatus"].Equals("E"))
                {
                    resp.Data.isAudit = true;
                    resp.Data.Master = new ComboDataModel
                    {
                        Id = JNConvert.ToStringAndTrim(masterObj?["id"]),
                        Name = JNConvert.ToStringAndTrim(masterObj?["fname"]),
                    };
                    resp.Data.BillNo = JNConvert.ToStringAndTrim(serObj["fbillno"]);
                    resp.Data.FinishDate = Convert.ToDateTime(vistInfo["fapprovedate"]).ToString("yyyy-MM-dd HH:mm:ss");
                }
                else
                {
                    if (!serObj["fserstatus"].IsNullOrEmpty()&&!serObj["fserstatus"].Equals("sersta04"))
                    {
                        resp.Message = "此{0}服务状态不为【待评价】！".Fmt("服务单");
                        return resp;
                    }
                    resp.Data.VistResult = JNConvert.ToStringAndTrim(vistInfo["fvistresult"]);
                    resp.Data.CustomerComplaint = JNConvert.ToStringAndTrim(vistInfo["fcustomercomplaint"]);
                    resp.Data.IsRecommend = Convert.ToString(vistInfo["fisrecommend"])=="1";
                    resp.Data.ServiceScore = JNConvert.ToStringAndTrim(vistInfo["fservicescore"]);
                    resp.Data.Score = JNConvert.ToStringAndTrim(vistInfo["fscore"]);
                    resp.Data.SaleScore = JNConvert.ToStringAndTrim(vistInfo["fsalescore"]);
                    resp.Data.InstallScore = JNConvert.ToStringAndTrim(vistInfo["finstallscore"]);
                    resp.Data.InTime = Convert.ToString(vistInfo["fintime"]) == "1";
                    resp.Data.IsSatisfy = Convert.ToString(vistInfo["fissatisfy"]) == "1";
                    resp.Data.Master = new ComboDataModel
                    {
                        Id = JNConvert.ToStringAndTrim(masterObj?["id"]),
                        Name = JNConvert.ToStringAndTrim(masterObj?["fname"]),
                    };
                    resp.Data.BillNo = JNConvert.ToStringAndTrim(serObj["fbillno"]);
                    var FinishDate = Convert.ToDateTime(serObj["ffinishdate"]);
                    resp.Data.FinishDate = FinishDate.Equals(DateTime.MinValue) ? "" : FinishDate.ToString("yyyy-MM-dd HH:mm:ss");
                }
                VistForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_vist");
                if (!resp.Data.isAudit)
                {
                    // 获取服务单相关的基础资料
                    resp.Data.ComboData = this.VistForm.GetComboDataSource(this.Context, "fscore,fsalescore,finstallscore,fservicescore");
                }
                resp.Message = "操作成功！";
                resp.Success = true;
            }
            catch (Exception ex)
            {
                resp.Message = ex.Message;
                resp.Success = false;
            }
            return resp;
        }

        private DynamicObject GetVistInfo(DynamicObject serObj, out string msg)
        {
            msg = string.Empty;
            bool isService = !serObj["fsourcetype"].IsNullOrEmpty()&&!serObj["fsourcetype"].Equals("ste_afterfeedback");
            var vistForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_vist");
            var sqlParam = new List<SqlParam> {
                 new SqlParam("@fmainorgid", DbType.String,serObj["fmainorgid"]),
                 new SqlParam("@fsourcetype", DbType.String,isService?"ydj_service":"ste_afterfeedback"),
                 new SqlParam("@fsourcenumber", DbType.String,isService?serObj["fbillno"]:serObj["fsourcenumber"])
            };
            //改为不按权限获取，因账号可能不具有查看该组织权限
            //var vistObj = this.Context.LoadBizDataByFilter("ydj_vist", "fsourcetype=@fsourcetype and fsourcenumber=@fsourcenumber", true, sqlParam);
            var vistObj = this.Context.ExecuteDynamicObject("select fid as id,fstatus,ffinishdate,fvistresult,fcustomercomplaint,finstallscore,fisrecommend,fservicescore,fscore,fsalescore,fintime,fissatisfy from t_ydj_vist where fsourcetype = @fsourcetype and fsourcenumber = @fsourcenumber and fmainorgid=@fmainorgid", sqlParam);
            //不存在则先自动建一个回访单
            DynamicObject data = null;
            if (!vistObj.Any())
            {
                if (isService)
                {
                    var fserstatus = Convert.ToString(serObj?["fserstatus"]);
                    if (fserstatus != "sersta04" && fserstatus != "sersta05")
                    {
                        msg = "服务状态为待评价或已关闭才能进行回访！";
                        return null;
                    }
                }
                var convertService = this.Container.GetService<IConvertService>();
                string pkid = serObj["id"].ToString();
                if (!isService)
                {
                    //var feedBackInfo = this.Context.LoadBizDataByNo("ste_afterfeedback", "fbillno", new List<string> { serObj["fsourcenumber"].ToString() }).FirstOrDefault();
                    sqlParam = new List<SqlParam>{
                        new SqlParam("@fbillno", DbType.String,serObj["fsourcenumber"]),
                        new SqlParam("@fmainorgid", DbType.String,serObj["fmainorgid"])
                    };
                    var feedBackInfo = this.Context.ExecuteDynamicObject("select fid from ste_afterfeedback where fbillno=@fbillno and fmainorgid=@fmainorgid", sqlParam).FirstOrDefault();
                    if (feedBackInfo == null)
                    {
                        msg = "评价失败！服务单上游售后反馈单{0}不存在或已删除！".Fmt(serObj["fsourcenumber"].ToString());
                        return null;
                    }
                    pkid = feedBackInfo["fid"].ToString();
                }
                var result = convertService.Push(Context, new BillConvertContext()
                {
                    RuleId = !isService ? "ste_afterfeedback2ydj_vist" : "ydj_service2ydj_vist",
                    SourceFormId = !isService ? "ste_afterfeedback" : "ydj_service",
                    TargetFormId = "ydj_vist",
                    SelectedRows = new List<SelectedRow>
                    {
                        new SelectedRow
                        {
                            PkValue = pkid
                        }
                    }.ToConvertSelectedRows(),
                    Option = OperateOption.Create()
                });


                var convertResult = result.SrvData as ConvertResult;
                if (convertResult != null)
                {
                    var prepareSaveDataService = this.Container.GetService<IPrepareSaveDataService>();
                    prepareSaveDataService.PrepareDataEntity(this.Context, vistForm, convertResult.TargetDataObjects.ToArray(), OperateOption.Create());
                    var vistSave = this.Context.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(Context, "ydj_vist", convertResult.TargetDataObjects, "save", new Dictionary<string, object>());
                    vistSave.ThrowIfHasError(true, "评价过程中自动保存回访单失败！");
                }
                else
                {
                    msg = "评价过程中下推出现异常，请联系管理员！";
                    return null;
                }
                data = convertResult.TargetDataObjects.First();
            }
            else if (vistObj.Any(x => !x["fstatus"].Equals("E")))
            {
                data = vistObj.First(x => !x["fstatus"].Equals("E"));
            }
            else
            {
                data = vistObj.First(x => x["fstatus"].Equals("E"));
            }
            return data;
        }
    }
}
