using System;
using JieNor.AMS.YDJ.MP.API.DTO.MS.AIBedSelectAdvise;
using JieNor.AMS.YDJ.MP.API.Model.MS.AISelectAdvise;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.Controller.MS.AIBedSelectAdvise
{
    public class AiBedSelectAdviseController : BaseController
    {
        public object Any(AiSelectAdviseDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<AiSelectAdviseModel>();

            var model = new AiSelectAdviseModel();

            if (dto.Answer.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;

                resp.Message = "参数Answer不能为空";

                return resp;
            }

            if (dto.ChoiceId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;

                resp.Message = "参数ChoiceId不能为空";

                return resp;
            }


            if (dto.Score.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;

                resp.Message = "参数Score不能为空";

                return resp;
            }

            var recommendNumber = string.Empty;

            var advise = string.Empty;

            var recommend = String.Empty;

            switch (dto.Answer)
            {
                case "腰椎不好":
                    recommendNumber = "MCC2-010-2";

                    advise = "挑选承托感较强型材质，极致承托腰椎，保护脊椎健康。";

                    recommend = "太空树脂球360°自适应系统";

                    break;

                case "怕热易出汗":

                    recommendNumber = "MCC2-010-4";

                    advise = "挑选透气性型材质，平衡睡眠温度，减少燥热感。";

                    recommend = "3D护脊透气系统";

                    break;
                case "翻身次数多":

                    recommendNumber = "MCC2-010-3";

                    advise = "挑选多重支承型材质，贴合身体曲线，满足不同睡姿变化。";

                    recommend = "多重支撑系统";

                    break;

                case "没有上述情况":

                    recommendNumber = "MCC2-010-1";

                    advise = "您的睡眠质量优良，私人定制床垫给您带来更好的体验。";

                    recommend = "1.太空树脂球360°自适应系统；2.3D护脊透气系统；3.多重支撑系统";

                    break;

                default:
                    break;

            }

            if (!advise.IsNullOrEmptyOrWhiteSpace() && !recommend.IsNullOrEmptyOrWhiteSpace())
            {
                model.Number = recommendNumber;

                model.Advise = advise;

                model.Recommend = recommend;

                resp.Success = true;

                resp.Message = "获取选配建议成功";

                resp.Data = model;
            }
            else
            {
                resp.Success = true;
                resp.Message = "暂未获取到选配建议信息";
            }
            

            return resp;
        }
    }
}