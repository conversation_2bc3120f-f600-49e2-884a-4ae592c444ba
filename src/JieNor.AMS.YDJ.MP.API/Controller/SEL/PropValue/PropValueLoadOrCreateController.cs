using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;

namespace JieNor.AMS.YDJ.MP.API.Controller.SEL.PropValue
{
    /// <summary>
    /// 微信小程序：加载或创建非标属性值接口
    /// </summary>
    public class PropValueLoadOrCreateController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(PropValueLoadOrCreateDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            //验证参数
            if (!this.Validate(dto, resp)) return resp;

            //属性选配服务
            var propSelService = this.Container.GetService<IPropSelectionService>();

            //处理前端录入的非标值
            var noStdValueList = dto.PropList
                .Where(o => o.IsNosuitCreate && o.ValueId.IsNullOrEmptyOrWhiteSpace())
                .ToList();
            var propValueObjs = propSelService.LoadOrCreatePropValue(this.Context, noStdValueList,dto.ProductId);
            var propValueList = new List<Dictionary<string, object>>();
            foreach (var item in propValueObjs)
            {
                var propId = Convert.ToString(item["fpropid"]);
                var valueId = Convert.ToString(item["id"]);
                var valueName = Convert.ToString(item["fname"]);
                var valueNumber = Convert.ToString(item["fnumber"]);
                var isNosuitCreate = Convert.ToBoolean(item["fnosuitcreate"]);

                propValueList.Add(new Dictionary<string, object>
                {
                    { "propId", propId },
                    { "valueId", valueId },
                    { "valueName", valueName },
                    { "valueNumber", valueNumber },
                    { "isNosuitCreate", isNosuitCreate }
                });
            }

            //设置响应数据包
            resp.Data = new
            {
                propValueList = propValueList,
            };
            resp.Message = "非标值保存成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 验证参数
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        /// <returns></returns>
        private bool Validate(PropValueLoadOrCreateDTO dto, BaseResponse<object> resp)
        {
            if (dto.PropList == null || !dto.PropList.Any())
            {
                resp.Success = false;
                resp.Message = $"属性列表 propList 不能为空！";
                return false;
            }

            return true;
        }
    }
}