using JieNor.AMS.YDJ.MP.API.DTO.Common.Share;
using JieNor.AMS.YDJ.MP.API.Model.Common.Share;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.Common.Share
{
    /// <summary>
    /// 企业 微信小程序：店铺分享获取海报接口
    /// </summary>
    public class ShareShopPosterController:BaseController
    {
        public object Any(ShareShopPosterDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<ShareShopPosterModel>();
            var sql = "select fid,fposter from t_sys_company with(nolock) where fid=@fmainorgid";
            var pars = new List<SqlParam>() { new SqlParam("@fmainorgid", System.Data.DbType.String, Context.Company) };//企业 隔离
            var data = this.DBService.ExecuteDynamicObject(this.Context, sql, pars);//获取数据
            if (data!=null&&data.Any())//组装返回数据
            {
                var model = new ShareShopPosterModel();
                model.Id = Convert.ToString(data[0]["fid"]);
                model.PosterUrl = JNConvert.ToStringAndTrim(data[0]["fposter"]).GetSignedFileUrl();
                resp.Data = model;
                resp.Success = true;
                resp.Message = "取数成功！";
            }
            else
            {
                resp.Success = false;
                resp.Message = "取数失败！";
            }
            return resp;
        }
    }
}
