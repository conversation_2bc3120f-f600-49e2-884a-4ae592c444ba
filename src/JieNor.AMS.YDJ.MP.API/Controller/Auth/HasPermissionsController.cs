using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MP.API.Response.YDJ.MainFW.HasPermissions;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：用户权限检查接口
    /// </summary>
    public class HasPermissionsController : ServiceStack.Service
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(HasPermissionsDTO dto)
        {
            var resp = new BaseResponse<List<HasPermissionsModel>>(); ;

            if (dto.Count == 0)
            {
                resp.Success = false;
                resp.Message = "参数不能为空！";
                return resp;
            }

            List<Dictionary<string, string>> permissionInfos = new List<Dictionary<string, string>>();

            foreach (var item in dto)
            {
                permissionInfos.Add(new Dictionary<string, string>
                {
                    { "formId", item.FormId },
                    { "permId", item.PermId }
                });
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "sys_mainfw",
                    OperationNo = "Haspermissions",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "formId", "sys_mainfw" },
                        { "permissionInfos", permissionInfos.ToJson() }
                    }
                });
            var result = response?.OperationResult;
            if (result == null || !result.IsSuccess)
            {
                resp.Success = false;
                resp.Message = result.GetErrorMessage();
                return resp;
            }

            var hasPermissionsResponse = JsonConvert.DeserializeObject<HasPermissionsResponse>(result?.SrvData as string ?? "{}");
            if (hasPermissionsResponse?.permissionInfos != null)
            {
                foreach (var item in hasPermissionsResponse.permissionInfos)
                {
                    resp.Data.Add(new HasPermissionsModel
                    {
                        PermCaption = item.permCaption,
                        PermId = item.permId,
                        FormId = item.formId
                    });
                }
            }

            resp.Success = true;
            resp.Message = "操作成功！";

            return resp;
        }
    }
}