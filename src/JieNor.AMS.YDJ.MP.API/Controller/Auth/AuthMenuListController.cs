using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.Controller.Auth
{
    /// <summary>
    /// 微信小程序：用户小程序菜单权限列表
    /// </summary>
    public class AuthMenuListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AuthMenuListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<List<AuthTabbarModel>>(); ;
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "mp_rolemenu",
                    OperationNo = "GetUserPermission"
                });
            var result = response?.OperationResult;

            if (result != null && !result.IsSuccess)
            {
                resp = result.ToResponseModel<List<AuthTabbarModel>>(false);
                return resp;
            }

            var data = JsonConvert.DeserializeObject<List<AuthTabbarModel>>(result?.SrvData as string ?? "[]");

            // 如果是超级管理员，返回所有
            if (this.Context.IsAdminRole(this.Context.Company))
            {
                foreach (var tarbar in data)
                {
                    foreach (var group in tarbar.Groups)
                    {
                        foreach (var menu in group.menus)
                        {
                            menu.IsAllow = true;
                        }
                    }
                }
            }
            else
            {
                var muSiAiService = this.Container.GetService<IMuSiAIService>();
                var authorizedOrgIds = muSiAiService.GetAuthorizedOrgIds(this.Context);

                // 过滤没有授权的菜单
                foreach (var tarbar in data)
                {
                    foreach (var group in tarbar.Groups)
                    {
                        group.menus = group.menus.Where(m => m.IsAllow).ToList();

                        // 不包括授权组织内，去掉【AI床垫】入口
                        var aiBedMenu = group.menus.FirstOrDefault(s => s.Number.EqualsIgnoreCase("aibed"));
                        if (aiBedMenu != null 
                            && authorizedOrgIds != null 
                            && authorizedOrgIds.Any() 
                            && !authorizedOrgIds.Contains(this.Context.Company))
                        {
                            group.menus.Remove(aiBedMenu);
                        }
                    }
                }
            }

            resp.Data = data;
            resp.Success = true;
            resp.Message = "操作成功！";

            return resp;
        }
    }
}