using JieNor.AMS.YDJ.MP.API.DTO.COO.Receipt;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.Receipt
{
    /// <summary>
    /// 微信小程序：收款单删除
    /// </summary>
    public class ReceiptDeleteController: BaseController
    {
        public object Any(ReceiptDeleteDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "coo_incomedisburse",
                    OperationNo = "delete",
                    SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } }
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<BaseDataModel>(false);
            return resp;
        }
    }
}
