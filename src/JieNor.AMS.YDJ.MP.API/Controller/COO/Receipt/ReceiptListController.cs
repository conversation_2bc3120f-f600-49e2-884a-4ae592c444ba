using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.Receipt
{
    /// <summary>
    /// 微信小程序：收款单列表取数接口
    /// </summary>
    public class ReceiptListController : BaseController
    {
        /// <summary>
        /// 收支记录表单模型
        /// </summary>
        protected HtmlForm ReceiptForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ReceiptListDTO dto)
        {
            base.InitializeOperationContext(dto);

            this.ReceiptForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");

            var resp = new BaseResponse<BaseListPageData<ReceiptListModel>>
            {
                Data = new BaseListPageData<ReceiptListModel>(dto.PageSize)
            };

            this.SetResponseData(dto, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// 采用平台标准的列表取数方法，以便和PC端的取数条件保持一致（主要是涉及到数据隔离的问题）
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        private void SetResponseData(ReceiptListDTO dto, BaseResponse<BaseListPageData<ReceiptListModel>> resp)
        {
            //参数对象
            var param = new SqlBuilderParameter(this.Context, this.ReceiptForm);
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;
            param.OrderByString = "fcreatedate desc";

            //过滤条件
            if (dto.AuditStatus)
            {
                param.FilterString = param.FilterString.JoinFilterString(@"
                fsourceformid in('ydj_saleintention','ydj_order','ydj_customer') 
                and fstatus='E'");
            }
            else
            {
                param.FilterString = param.FilterString.JoinFilterString(@"
                fsourceformid in('ydj_saleintention','ydj_order','ydj_customer') 
                and fstatus!='E'");
            }
            if (string.IsNullOrWhiteSpace(dto.bizdirection))
            {
                dto.bizdirection = "bizdirection_01";//默认值，兼容小程序版本（默认查收款的数据
            }
            param.FilterString = param.FilterString.JoinFilterString(string.Format(@" fbizdirection='{0}'", dto.bizdirection));

            //根据搜索栏关键字过滤
            dto.Keyword = dto.Keyword?.Trim();

            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" t0.fbillno  like @keyword " +
                                         " or t0.fcreatorid in (" +
                                                                " select tci.fcreatorid " +
                                                                " from t_sec_user as tsu with(nolock),t_coo_incomedisburse as tci with(nolock) " +
                                                                " where tsu.fid = tci.fcreatorid and tsu.fname like @keyword) " +
                                         " or t0.fdeptid in (" +
                                                             " select tci.fdeptid " +
                                                             " from t_bd_department as tbd with(nolock),t_coo_incomedisburse as tci with(nolock) " +
                                                             " where tbd.fid = tci.fdeptid and tbd.fname like @keyword) " +
                                         " or t0.fstaffid in (" +
                                                             " select tci.fstaffid " +
                                                             " from t_bd_staff as tbs with(nolock),t_coo_incomedisburse as tci with(nolock) " +
                                                             " where tbs.fid = tci.fstaffid and tbs.fname like @keyword) " +
                                         " or t0.fmybankid in ( " +
                                                             " select tci.fmybankid " +
                                                             " from t_ydj_banknum as tyb with(nolock),t_coo_incomedisburse as tci with(nolock) " +
                                                             " where tyb.fid = tci.fmybankid and tyb.fname like @keyword) " +
                                         " or t0.fcustomerphone like @keyword " +
                                         " or t0.fcustomerid in (select fid from t_ydj_customer where fname like @keyword)");

                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, "%" + dto.Keyword + "%"));
            }

            //数据状态过滤条件
            //if (!dto.StatusList.IsNullOrEmptyOrWhiteSpace())
            //{
            //    var statuslist = dto.StatusList.Split(',');
            //    //查询优化，如果statuslist只有一个数据状态参数，则使用=号查询，因为使用in会全表扫描,应尽量避免
            //    if (statuslist.Length == 1 && !statuslist[0].IsNullOrEmptyOrWhiteSpace())
            //    {
            //        param.FilterString = param.FilterString.JoinFilterString(@" fstatus=@fstatus ");
            //        var status = statuslist[0].Trim();
            //        param.AddParameter(new SqlParam("@fstatus", System.Data.DbType.String, status));
            //    }
            //    else
            //    {
            //        var insql = " fstatus in (";
            //        for (int i = 0; i < statuslist.Length; i++)
            //        {
            //            var item = statuslist[i].Trim();
            //            insql += $@"@status{i},";
            //            param.AddParameter(new SqlParam($@"@status{i}", System.Data.DbType.String, item));
            //        }
            //        insql = insql.TrimEnd(',');
            //        insql += ")";
            //        param.FilterString = param.FilterString.JoinFilterString(insql);                    
            //    }
            //}

            //当前要查询的字段列表
            var fieldKeys = new string[] { "fbillno", "fbizstatus", "fstatus", "fcustomerid", "fcustomerphone", "famount", "fcreatorid", "fcreatedate", "fpurpose" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(this.Context);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }



            //列表构建器
            var listBuilder = this.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            //获取分页数据
            var listData = listBuilder.GetQueryData(this.Context, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(this.Context, param, queryObj);

            //将平台通用的列表数据结构转换为API数据结构
            var list = new List<ReceiptListModel>();
            foreach (var item in listData)
            {
                list.Add(new ReceiptListModel
                {
                    Id = Convert.ToString(item["fbillhead_id"]),
                    BillNo = Convert.ToString(item["fbillno"]),
                    BizStatus = Convert.ToString(item["fbizstatus_fenumitem"]),
                    AuditStatus = Convert.ToString(item["fstatus"]) == "E",
                    Customer = Convert.ToString(item["fcustomerid_fname"]),
                    Status = JNConvert.GetStatusMdl(Convert.ToString(item["fstatus"])),
                    Phone = Convert.ToString(item["fcustomerphone"]),
                    Amount = Convert.ToDecimal(item["famount"]),
                    Creator = Convert.ToString(item["fcreatorid_fname"]),
                    CreateDate = Convert.ToDateTime(item["fcreatedate"]),
                    Purpose = Convert.ToString(item["fpurpose_fenumitem"])
                });
            }

            //设置响应数据包
            resp.Data.List = list;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Message = "取数成功！";
            resp.Success = true;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="resp"></param>
        private void SetResponseData2(ReceiptListDTO dto, BaseResponse<BaseListPageData<ReceiptListModel>> resp)
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var sqlWhere = new StringBuilder();
            sqlWhere.Append("m.fmainorgid=@fmainorgid and m.fsourceformid in('ydj_saleintention','ydj_order','ydj_customer') and m.fbizdirection='bizdirection_01'");

            if (!dto.BizStatus.IsNullOrEmptyOrWhiteSpace())
            {
                sqlWhere.Append(" and m.fbizstatus=@bizstatus");
                sqlParam.Add(new SqlParam("@bizstatus", System.Data.DbType.String, dto.BizStatus));
            }

            //获取分页数据
            var list = this.GetList(dto, sqlParam, sqlWhere);

            //获取分页总记录数
            var totalRecord = this.GetTotalRecord(sqlParam, sqlWhere);

            //设置响应数据包
            resp.Data.List = list;
            resp.Data.TotalRecord = totalRecord;
            resp.Message = "取数成功！";
            resp.Success = true;
        }

        /// <summary>
        /// 获取分页数据
        /// </summary>
        private List<ReceiptListModel> GetList(ReceiptListDTO dto, List<SqlParam> sqlParam, StringBuilder sqlWhere)
        {
            var list = new List<ReceiptListModel>();

            //默认按创建日期降序
            var orderBy = "";
            switch (dto.Sortby)
            {
                default:
                    orderBy = "m.fcreatedate";
                    break;
            }
            orderBy += " " + dto.Sortord;

            var sqlText = $@"
            select top {dto.PageSize} * from 
            (
	            select row_number() over(order by {orderBy}) rownum,
                m.fid,m.fbillno,ed.fenumitem fbizstatusname,c.fname fcustomer,c.fphone,
                m.famount,u.fname fcreator,m.fcreatedate 
                from t_coo_incomedisburse m with(nolock) 
                left join t_ydj_customer c with(nolock) on c.fid=m.fcustomerid and c.fmainorgid=@fmainorgid 
                left join t_sec_user u with(nolock) on u.fid=m.fcreatorid and u.fmainorgid=@fmainorgid 
                left join t_bd_enumdataentry ed with(nolock) on ed.fentryid=m.fbizstatus 
                where {sqlWhere}
            ) p 
            where rownum > {dto.PageSize} * ({dto.PageIndex} - 1)";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    list.Add(new ReceiptListModel
                    {
                        Id = reader.GetValueToString("fid"),
                        BillNo = reader.GetValueToString("fbillno"),
                        BizStatus = reader.GetValueToString("fbizstatusname"),
                        Customer = reader.GetValueToString("fcustomer"),
                        Phone = reader.GetValueToString("fphone"),
                        Amount = reader.GetValueToDecimal("famount"),
                        Creator = reader.GetValueToString("fcreator"),
                        CreateDate = reader.GetValueToDateTime("fcreatedate")
                    });
                }
            }

            return list;
        }

        /// <summary>
        /// 获取分页总记录数
        /// </summary>
        private int GetTotalRecord(List<SqlParam> sqlParam, StringBuilder sqlWhere)
        {
            var totalRecord = 0;

            var sqlText = $@"
            select count(0) totalRecord from t_coo_incomedisburse m with(nolock) 
            where {sqlWhere}";

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }

            return totalRecord;
        }



    }
}