using System;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.Filter;
using Newtonsoft.Json;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.IncomeDisburse
{
    /// <summary>
    /// 微信小程序：收支记录退款接口
    /// </summary>
    [RepeatedSubmitResponseFilter("coo_incomedisburse")]
    public class RefundSaveController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(IncomeDisburseRefundDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            string formId = string.Empty;

            switch (dto.SourceType)
            {
                case "ydj_saleintention":
                    formId = "ydj_salesettledyn";
                    break;
                case "ydj_order":
                    formId = "ydj_order";
                    break;

            }
            //使用账户支付时无需上传凭证
            var wayIds = new List<string>() { "payway_01", "payway_05" };
            if (!dto.fcontactunitid.IsNullOrEmptyOrWhiteSpace() && (dto.Certificates == null || dto.Certificates.Count <= 0) && !wayIds.Contains(dto.WayId))
            {
                resp.Success = false;
                resp.Message = $"选择了代收单位，必须上传凭证！";
                return resp;
            }
            //支付方式为“商场代收”，“代收单位”不能为空
            if (dto.WayId.EqualsIgnoreCase("payway_13") && dto.fcontactunitid.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"选择了商场代收，代收单位不能为空！";
                return resp;
            }

            if (formId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"单据类型【{dto.SourceType}】不支持退款操作！";
                return resp;
            }

            var profileService = this.Context.Container.GetService<ISystemProfile>();

            var transferamount = 0M;
            var isalltransfer = false;
            if (dto.SourceType.EqualsIgnoreCase("ydj_order"))
            {
                var order = this.Context.LoadBizDataById(dto.SourceType, dto.SourceId);
                if (order == null)
                {
                    resp.Success = false;
                    resp.Message = $"销售合同不存在，请检查！";
                    return resp;
                }
                dto.CustomerId = Convert.ToString(order["fcustomerid"]);
                // 销售合同允许超额收款
                var fcanexcess = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fcanexcess", false);
                if (!fcanexcess)
                {
                    if (Convert.ToDecimal(order["fsumamount"]) == 0)
                    {
                        resp.Message = "当前为0元订单，无需退款！";
                        resp.Success = false;
                        return resp;
                    }
                    var unreceivedAmount = Convert.ToDecimal(order["funreceived"]);
                    if (dto.SettleAmount > unreceivedAmount)
                    {
                        resp.Success = false;
                        resp.Message = $"已超额收款，请修改结算金额！";
                        return resp;
                    }
                }



                //var fhasvoucherwhenreceivable = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fhasvoucherwhenreceivable", false);
                //if (fhasvoucherwhenreceivable)//如果勾选了系统参数<收款必须上传凭证>，则判断上传凭证参数
                //{
                //    if ((dto.Certificates == null || dto.Certificates.Count <= 0) && !wayIds.Contains(dto.WayId))//使用账户支付时无需上传凭证
                //    {
                //        resp.Success = false;
                //        resp.Message = $"退款必须上传凭证！";
                //        return resp;
                //    }
                //}

                // 向麦浩系统发送请求
                var response1 = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
                    this.Request,
                    new CommonBillDTO()
                    {
                        FormId = "coo_settledyn",
                        OperationNo = "checkamount",
                        PageId = Guid.NewGuid().ToString("N"),
                        SimpleData = new Dictionary<string, string>
                        {
                        { "orderAmount",Convert.ToString(order["funreceived"]) },
                        { "orderNo",Convert.ToString(order["fbillno"]) },
                        { "amount", dto.SettleAmount.ToString()}
                        }
                    });
                if (!response1.OperationResult.IsSuccess)
                {
                    resp = response1.OperationResult.ToResponseModel<object>(false);
                    if (response1.OperationResult.IsSuccess)
                        resp.Data = ResultSaveId(dto.SourceNumber);
                    return resp;
                }

                // 向麦浩系统发送请求
                //var response1 = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                //    this.Request,
                //    new CommonBillDTO()
                //    {
                //        FormId = formId,
                //        OperationNo = "checkamount",
                //        BillData = BuildBillData(dto),
                //        SimpleData = SimpleDataData(dto, transferamount, isalltransfer)
                //    });

                // 向麦浩系统发送请求
                var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                    this.Request,
                    new CommonBillDTO()
                    {
                        FormId = "ydj_order",
                        OperationNo = "NewSettle",
                        BillData = BuildBillData(dto, order),
                        SimpleData = SimpleDataData(dto)
                    });
                var result = response?.OperationResult;

                resp = result.ToResponseModel<object>(false);
                if (result.IsSuccess)
                    resp.Data = ResultSaveId(dto.SourceNumber);

            }
            return resp;
        }

        /// <summary>
        /// 返回保存后的ID 
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, string> ResultSaveId(string fsourcenumber)
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            string strSql = string.Format(@"select  top 1 fid from t_coo_incomedisburse with(nolock) where fsourcenumber='{0}' order by fcreatedate desc", fsourcenumber);
            using (var dr = this.DBService.ExecuteReader(this.Context, strSql.ToString()))
            {
                if (dr.Read())
                {
                    dic["id"] = dr["fid"].ToString();
                }
            }
            return dic;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private Dictionary<string, string> SimpleDataData(IncomeDisburseRefundDTO dto)
        {
            Dictionary<string, string> data = new Dictionary<string, string>
            {
                //{ "id", dto.Id },
                { "fsourceid", dto.SourceId },
                //{ "fissyn", dto.IsSyn }, 
                { "fsourcenumber", dto.SourceNumber },
                { "fsourceformid", "ydj_order" },
                { "fsettlemaintype", "ydj_customer" },
                { "fsettlemainid", dto.CustomerId },//
                { "fpurpose", "bizpurpose_06" },//默认退款
                { "fdirection", "direction_01" },
                { "fbizdirection", "bizdirection_02" },


                //{ "fsupplierid", dto.SupplierId },
                //{ "fsettledamount",  dto.SettledAmount },
                //{ "funsettleamount",  dto.UnsettleAmount },
                { "fsettleamount",  dto.SettleAmount.ToString() },
                { "famount",  dto.SettleAmount.ToString() },
                //{ "funconfirmamount",  dto.UnconfirmAmount },
                { "fdeptid",  dto.DeptId },
                //40936 客户充值默认携带业务员，允许业务员修改-后端开发
                { "fstaffid",  dto.StaffId },

                { "fway",  dto.WayId },
                { "fmybankid",  dto.MyBankId },
                { "fdate", dto.Date.ToString("yyyy-MM-dd HH:mm:ss") },
                { "fdescription", dto.Description },
                { "freducedbrokerage", Convert.ToString(dto.ReducedBrokerage) },
                { "paymentdesc", Convert.ToString(dto.PaymentDesc) },
                { "accounts", Convert.ToString(dto.accounts) }
            };
            if (dto.IsSyn)//如果是协同
            {
                data.Add("fissyn", dto.IsSyn.ToString());
                data.Add("fsynbankid", dto.SynBank.Id);
                data.Add("fsynbankname", dto.SynBank.BankName);
                data.Add("fsynbanknum", dto.SynBank.BankNum);
                data.Add("fsynaccountname", dto.SynBank.Name);
            }
            if (dto.Certificates != null && dto.Certificates.Any())
            {
                data.Add("fimage", string.Join(",", dto.Certificates.Select(s => s.Id)));
                data.Add("fimage_txt", string.Join(",", dto.Certificates.Select(s => s.Name)));
            }

            if (dto.ExpenseList != null && dto.ExpenseList.Any())
            {
                List<Dictionary<string, object>> expenseData = new List<Dictionary<string, object>>();

                foreach (var expense in dto.ExpenseList)
                {
                    expenseData.Add(new Dictionary<string, object>
                    {
                        { "fexpenseitemid", expense.ExpenseItemId },
                        { "famount_ee", expense.Amount },
                        { "fdescription_ee", expense.Description }
                    });
                }

                data["fexpenseentry"] = expenseData.ToJson();
            }

            // 新建时带上流水号
            data["ftranid"] = dto.TranId;
            //代收单位
            data["fcontactunitid"] = dto.fcontactunitid;
            data["fcontactunittype"] = dto.UnitType;
            //收款小票号
            data["freceiptno"] = dto.ReceiptNo;
            data["fcusacount"] = dto.cusacount;
            #region 销售员
            var joinStaffs = new List<Dictionary<string, object>>();
            foreach (var js in dto.JoinStaffs)
            {
                joinStaffs.Add(new Dictionary<string, object>
                {
                    { "Id", js.Id },
                    { "IsMain", js.IsMain },
                    { "DutyId", js.Duty?.Id },
                    { "DeptId", js.Dept?.Id },
                    { "Ratio", js.Ratio },
                    { "DeptPerfRatio", js.DeptPerfRatio },
                    { "Amount", js.Amount },
                    { "Description", js.Description }
                });
            }
            data.Add("fdutyentry", joinStaffs.ToJson());
            data.Add("fsettletype", "退款");
            #endregion

            return data;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private Dictionary<string, string> SimpleDataData(IncomeDisburseRefundDTO dto, decimal transferamount, bool isalltransfer)
        {
            Dictionary<string, string> data = new Dictionary<string, string>();
            if (!dto.accounts.IsNullOrEmptyOrWhiteSpace())
            {
                data["accounts"] = dto.accounts;
            }
            if (dto.SourceType.EqualsIgnoreCase("ydj_order"))
            {
                if (transferamount > 0)
                {
                    data["transferamount"] = transferamount.ToString();
                    data["isalltransfer"] = isalltransfer.ToString();
                }
            }
            return data;
        }
        private string BuildBillData(IncomeDisburseRefundDTO dto, DynamicObject order)
        {
            Dictionary<string, object> data = new Dictionary<string, object>
            {
                { "id", order["id"] },
                { "fstatus", order["fstatus"] },
                { "freceivable", order["freceivable"] },
                { "funreceived", order["funreceived"] },
                { "fentry", order["fentry"] },
            };


            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }

        //private 
    }
}