using JieNor.AMS.YDJ.MP.API.DTO.COO.IncomeDisburse;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.COO.IncomeDisburse
{
    /// <summary>
    /// 检查收款小票号跟金额是否重复
    /// </summary>
    public class CheckSourceOrderController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CheckSourceOrderDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse();

            if (dto.ids == null || string.IsNullOrWhiteSpace(dto.ids) || dto.ids.Split(',').Length == 0)
            {
                resp.Success = false;
                resp.Message = "“ID”必填！";
                return resp;
            }

            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("Ids", dto.ids);
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "coo_incomedisburse",
                    OperationNo = "checksourceorder",
                    SimpleData = dic
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<object>(false);
            resp.Message = Convert.ToString(result.SrvData);
            return resp;
        }
    }
}
