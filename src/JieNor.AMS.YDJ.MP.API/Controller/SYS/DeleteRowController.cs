using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System.Collections.Generic;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.AMS.YDJ.MP.API.DTO.SYS;

namespace JieNor.AMS.YDJ.MP.API.Controller.SYS
{
    /// <summary>
    /// 微信小程序：通用单据删除明细行接口
    /// </summary>
    public class DeleteRowController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(DeleteRowDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace()|| dto.FormId.IsNullOrEmptyOrWhiteSpace() 
                || dto.EntityKey.IsNullOrEmptyOrWhiteSpace() || dto.EntryId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"明细行删除接口参数必填，请补齐！";
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = dto.FormId,
                    OperationNo = "deleterow",
                    SelectedRows = new List<SelectedRow> {
                        new SelectedRow {
                            PkValue = dto.Id,
                            EntityKey=dto.EntityKey,
                            EntryPkValue=dto.EntryId
                        }
                    }
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<BaseDataModel>(false);

            return resp;
        }
    }
}