using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    public class ServiceFeedListController : BaseServiceFeedController
    {
        public object Any(ServiceFeedListDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseListPageData<ServiceFeedListModel>>
            {
                Data = new BaseListPageData<ServiceFeedListModel>(dto.PageSize)
            };

            BuildSqlWhereAndParams(this.Context, out var param, out var listBuilder);
            //判断是否回传服务单编号
            if (dto.ServiceID.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }
            //添加筛选
            param.AddParameter(new SqlParam("@ServiceID", System.Data.DbType.String, dto.ServiceID));
            param.AppendFilterString("fsourceser = @ServiceID");
            //添加状态筛选
            //param.AppendFilterString("fhandlestatus != 'handle_sta003'");

            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;
            //排序：默认按创建日期降序
            var orderBy = "fcreatedate";
            param.OrderByString = $"{orderBy} {dto.Sortord}";

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            //获取分页数据
            var listData = listBuilder.GetQueryData(this.Context, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(this.Context, param, queryObj);

            //将平台通用的列表数据结构转换为API数据结构
            var list = new List<ServiceFeedListModel>();
            foreach (var item in listData)
            {
                var model = new ServiceFeedListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fbillhead_id"]),
                    CreateDate = Convert.ToDateTime(item["fcreatedate"]),
                    Ffeeder = JNConvert.ToStringAndTrim(item["ffeeder_fname"]),
                    Fprodesript = JNConvert.ToStringAndTrim(item["fprodesript"]),
                    Number = JNConvert.ToStringAndTrim(item["fbillno"])
                };
                //处理状态
                model.Fhandlestatus = JNConvert.ToStringAndTrim(item["fhandlestatus_fenumitem"]);
                //问题类别 
                model.Fsprotype = JNConvert.ToStringAndTrim(item["fsprotype_fenumitem"]);

                model.Status = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fstatus"]),
                    Name = JNConvert.ToStringAndTrim(item["fstatus_fenumitem"])
                };
                list.Add(model);
            }
            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Code = 200;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Data.List = list;
            return resp;
        }
    }
}
