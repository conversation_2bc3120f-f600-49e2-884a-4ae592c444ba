using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    public class ServiceItemListController : BaseController
    {
        //public HtmlForm ServiceItemForm;

        public object Any(ServiceItemListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<ServiceItemListModel>>
            {
                Data = new BaseListPageData<ServiceItemListModel>(dto.PageSize)
            };

            BuildSqlWhereAndParams(dto, out var param, out var listBuilder);

            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" t0.fnumber like @keyword or t0.fname like @keyword");
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, "%" + dto.Keyword.Trim() + "%"));
            }
            if (!dto.serType.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" t0.fservicetype=@fservicetype");
                param.AddParameter(new SqlParam("@fservicetype", System.Data.DbType.String, dto.serType));
            }
            //param.AppendFilterString(" t0.fmainorgid=@fmainorgid");
            //param.AddParameter(new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.TopCompanyId));
            param.AppendFilterString("  t0.fstatus  = 'E'");
            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;

            //排序：默认按创建日期降序
            var orderBy = "fcreatedate";
            //switch (dto.Sortby)
            //{
            //}
            param.OrderByString = $"{orderBy} {dto.Sortord}";

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            //获取分页数据
            var listData = listBuilder.GetQueryData(this.Context, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(this.Context, param, queryObj);

            //将平台通用的列表数据结构转换为API数据结构
            var list = new List<ServiceItemListModel>();
            foreach (var item in listData)
            {
                var model = new ServiceItemListModel
                {
                    SerItem = new BaseDataSimpleModel
                    {
                        Id = item["fbillhead_id"].ToString(),
                        Number = item["fnumber"].ToString(),
                        Name = item["fname"].ToString()
                    },
                    SerItemType = new ComboDataModel
                    {
                        Id = item["fservicetype"].ToString(),
                        Name = item["fservicetype_fenumitem"].ToString()
                    },
                    Unit = new ComboDataModel
                    {
                        Id = item["funit"].ToString(),
                        Name = item["funit_fname"].ToString()
                    },
                    Description = item["fdescription"].ToString(),
                    Bring = item["fbring"].Equals("1")
                };
                list.Add(model);
            }

            //this.LoadExtendColumn(list);

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Data.List = list;

            return resp;
        }

        protected void BuildSqlWhereAndParams(ServiceItemListDTO dto, out SqlBuilderParameter param, out IListSqlBuilder listBuilder)
        {
            //参数对象
            param = new SqlBuilderParameter(this.Context, "ydj_serviceitem");
            param.ReadDirty = true;
            param.NoColorSetting = true;

            //当前要查询的字段列表
            var fieldKeys = new string[] { "fid", "fnumber", "fservicetype", "fname", "funit", "fdescription", "fbring" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(this.Context);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }
            //列表构建器
            listBuilder = this.Container.GetService<IListSqlBuilder>();
            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

        }

        ///// <summary>
        ///// 加载扩展字段
        ///// </summary>
        ///// <param name="list"></param>
        //private void LoadExtendColumn(List<ServiceListModel> list)
        //{
        //    if (list == null || list.Count == 0) return;

        //    // 加载客户
        //    var customerIds = list.Select(s => s.Customer.Id).Distinct();
        //    if (customerIds.Any())
        //    {
        //        var sqlText = string.Format(@"select fheadimgurl from t_ydj_customer where fid in ({0}) ", string.Join(",", customerIds.Select(s => $"'{s}'")));

        //        using (var reader = this.DBService.ExecuteReader(this.Context, sqlText))
        //        {
        //            while (reader.Read())
        //            {
        //                string customerId = reader.GetValueToString("fid");
        //                string headimg = reader.GetValueToString("fheadimgurl");
        //                foreach (var model in list.Where(s => s.Customer.Id == customerId))
        //                {
        //                    model.CusImg = headimg;
        //                }
        //            }
        //        }
        //    }
        //}
    }
}
