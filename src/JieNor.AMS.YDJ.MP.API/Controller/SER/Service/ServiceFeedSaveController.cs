using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.SER.Service
{
    /// <summary>
    /// 微信小程序：服务单问题反馈
    /// </summary>
    public class ServiceFeedSaveController : BaseController
    {
        public object Any(ServiceFeedSaveDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<object>();
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"id不能为空！";
                return resp;
            }
            if (dto.Fprodesript.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"问题描述不能为空！";
                return resp;
            }
            if (dto.Fsprotype.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"问题类型不能为空！";
                return resp;
            }
            if (dto.Images.Count == 0)
            {
                resp.Success = false;
                resp.Message = $"问题图片至少上传一张！";
                return resp;
            }
            var billData = new Dictionary<string, object>();
            var ServiceObj = this.Context.GetBizDataById("ydj_service", dto.Id, true);//获取服务单模型
            if (ServiceObj == null)
            {
                resp.Message = "服务单不存在或已被删除！";
                resp.Success = false;
                return resp;
            }
            // 新建
            if (!dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                billData.Merge(new Dictionary<string, object>
                {
                    {"fsprotype", dto.Fsprotype}, //问题类型赋值
                    {"fprodesript", dto.Fprodesript}, //问题描述赋值
                    {"fsourceser",Convert.ToString(ServiceObj["fbillno"])},//来源服务单编号赋值
                    {"fprograph", ImageFieldUtil.ConvertImage(dto.Images)},
                    {"fprograph_txt", ImageFieldUtil.ConvertImageTxt(dto.Images)},
                    {"fhandlestatus", "handle_sta001"},
                    //问题反馈同时 将服务单关联部门、关联用户带到反馈单
                    {"fstaffid_link",Convert.ToString(ServiceObj["fstaffid_link"])},//服务单关联用户
                    {"fdeptid_link",Convert.ToString(ServiceObj["fdeptid_link"])},//服务单关联部门

                    {"fcustomerid",Convert.ToString(ServiceObj["fcustomerid"])},//来源客户id赋值
                    {"ffeeder",Convert.ToString(ServiceObj["fmasterid"])},//反馈人
                    {"fdeptid",Convert.ToString(ServiceObj["fdeptid"])},//来源部门id赋值
                    {"ffeeddate", DateTime.Now},
                });
            }
            //向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ser_servicefeed",
                    OperationNo = "save",
                    Id = dto.Id,
                    BillData = new List<Dictionary<string, object>> { billData }.ToJson()
                });
            var result = response?.OperationResult;
            if (result == null || !result.IsSuccess)
            {
                resp.Message = result?.GetErrorMessage() ?? "操作失败！";
                resp.Success = false;
                return resp;
            }
            //将保存后的主键ID返回给前端
            var jsonPkids = "";
            result?.SimpleData?.TryGetValue("pkids", out jsonPkids);
            var savePkid = jsonPkids?.FromJson<List<string>>()?.FirstOrDefault() ?? "";
            //根据问题反馈主键ID获取单据编号返回填充至跟进记录
            var servicefeedObj = this.Context.GetBizDataById("ser_servicefeed", savePkid, true);//获取服务反馈模型
            //获取师傅名
            var masterobj = ServiceObj["fmasterid_ref"] as DynamicObject;
            //写入跟进记录
            ServiceFollowSaveDTO dtoss = new ServiceFollowSaveDTO
            {
                Description = "已反馈问题，问题编号：",//跟进描述
                SourceType = "ydj_service",//来源单
                SourceNumber = Convert.ToString(ServiceObj["fbillno"]),//来源单据编号
                Contacts = Convert.ToString(masterobj["fname"]),//师傅名
                CustomerId = Convert.ToString(ServiceObj["fcustomerid"]),//客户id 新
                FollowTime = DateTime.Now,
                ObjectType = "objecttype29",
                ObjectId = Convert.ToString(servicefeedObj["id"]),//服务反馈的id
                ObjectNo = Convert.ToString(servicefeedObj["fbillno"])//服务反馈的编号
            };
            if ((this.Context.AddServiceFollowerRecord(this.Request, dtoss)).Success)
            {
                
                resp.Data = new { id = savePkid };
                resp.Message = "保存成功！";
                resp.Success = true;
                resp.Code = 200;
            }
            else
            {
                resp.Message = "跟进记录保存失败！";
                resp.Success = false;
            }
            return resp;
        }
    }
}
