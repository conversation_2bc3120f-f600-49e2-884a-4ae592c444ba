using JieNor.AMS.YDJ.MP.API.DTO.STK.ReserveBill;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.STK.ReserveBill
{
    /// <summary>
    /// 微信小程序：根据仓库仓位获取预留数量取数接口
    /// </summary>
    public class ReserveBillMatchQtyByIdController : BaseController
    {
        /// <summary>
        /// 源单映射配置列表
        /// </summary>
        private Dictionary<string, Dictionary<string, string>> SourceFormMappings = new Dictionary<string, Dictionary<string, string>>
        {
            {
                "ydj_saleintention",
                new Dictionary<string, string>
                {
                    { "activeEntityKey", "fentity" },
                    { "productFieldKey", "fmaterialid" }
                }
            },
            {
                "ydj_order",
                new Dictionary<string, string>
                {
                    { "activeEntityKey", "fentry" },
                    { "productFieldKey", "fproductid" }
                }
            }
        };

        /// <summary>
        /// 源单模型
        /// </summary>
        private HtmlForm SourceForm { get; set; }

        /// <summary>
        /// 当前预留操作关联的源单映射配置
        /// </summary>
        private Dictionary<string, string> SourceFormMapping { get; set; }

        public object Any(ReserveBillMatchQtyByIdDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (dto.SourceFormId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceFormId 不能为空！";
                return resp;
            }
            if (dto.SourceOrderId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceOrderId 不能为空！";
                return resp;
            }
            Dictionary<string, string> sourceFormMapping = null;
            this.SourceFormMappings.TryGetValue(dto.SourceFormId, out sourceFormMapping);
            if (sourceFormMapping == null)
            {
                resp.Success = false;
                resp.Message = $"业务单据 {dto.SourceFormId} 暂不支持预留！";
                return resp;
            }
            this.SourceFormMapping = sourceFormMapping;

            this.SourceForm = this.MetaModelService.LoadFormModel(this.Context, dto.SourceFormId);
            var sourceFormDt = this.SourceForm.GetDynamicObjectType(this.Context);

            //源单数据包
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, sourceFormDt);
            var sourceBill = dm.Select(dto.SourceOrderId) as DynamicObject;
            if (sourceBill == null)
            {
                resp.Success = false;
                resp.Message = $"{this.SourceForm.Caption}【{dto.SourceOrderId}】不存在或已被删除，无法预留！";
                return resp;
            }
            //构建预留需求动态表单数据包
            var demandObjs = this.BuildReserveDemandData(dto, sourceBill, resp);

            if (demandObjs.Count == 0)
            {
                resp.Success = false;
                resp.Message = $"服务器繁忙，请稍后再试！";
                return resp;
            }
            //调用麦浩PC端已有的接口
            var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "stk_reservedialog",
                    OperationNo = "loadstockqty",
                    BillData = demandObjs.ToJson()
                });
            var result = response?.OperationResult;
            if (!result.IsSuccess)
            {
                var errMsg = "麦浩预留加载库存数量接口出错！";
                var errMsgs = result.ComplexMessage.ErrorMessages;
                if (errMsgs.Count > 0)
                {
                    errMsg = string.Join("，", errMsgs);
                }
                resp.Message = errMsg;
                resp.Success = false;
                return resp;
            }
            var entryList = new List<Dictionary<string, object>>();

            //数据结构转换
            var srvData = (result.SrvData as string).FromJson<Dictionary<string, List<Dictionary<string, object>>>>();
            var entrys = srvData.GetValue("fentry");
            if (entrys != null)
            {
                foreach (var entry in dto.EntryList)
                {
                    var houseEntrys = entrys.Where(o =>
                        Convert.ToString(o["fsourceentryid"]).EqualsIgnoreCase(entry.SourceEntryId)
                        && !Convert.ToString(o["fstorehouseid"]).IsNullOrEmptyOrWhiteSpace()
                           );

                    var storehouseCanReserveQty = houseEntrys.Sum(o => Convert.ToDecimal(o["fcanreserveqty"]));

                    entryList.Add(new Dictionary<string, object>
                    {
                        { "sourceEntryId", entry.SourceEntryId },
                        { "storehouseId", entry.StoreHouseId },
                        { "storehouseCanReserveQty", storehouseCanReserveQty },
                    });
                }
            }

            resp.Data = new { list = entryList };
            resp.Message = "取数成功！";
            resp.Success = true;
            return resp;




        }
        /// <summary>
        /// 构建预留需求动态表单数据包
        /// </summary>
        private List<DynamicObject> BuildReserveDemandData(ReserveBillMatchQtyByIdDTO dto, DynamicObject sourceBill, BaseResponse<object> resp)
        {
            List<DynamicObject> demandObjs = new List<DynamicObject>();
            //预留需求动态表单模型
            var reserveDemandForm = this.MetaModelService.LoadFormModel(this.Context, "stk_reservedialog");
            var demandDt = reserveDemandForm.GetDynamicObjectType(this.Context);
            var demandEntity = reserveDemandForm.GetEntryEntity("fentry");

            //预留对象默认取源单上的客户
            var demandObj = new DynamicObject(demandDt);
            demandObj["freserveobjecttype"] = "ydj_customer";
            demandObj["freserveobjectid"] = Convert.ToString(sourceBill["fcustomerid"]);
            demandObj["fsourcetype"] = dto.SourceFormId;
            demandObj["fsourcenumber"] = sourceBill["fbillno"];
            var demandEntrys = demandEntity.DynamicProperty?.GetValue<DynamicObjectCollection>(demandObj);
            foreach (var item in dto.EntryList)
            {
                var sourceEntity = this.SourceForm.GetEntryEntity(this.SourceFormMapping["activeEntityKey"]);
                var productFieldKey = this.SourceFormMapping["productFieldKey"];

                //源单明细
                var sourceEntrys = sourceEntity.DynamicProperty?.GetValue<DynamicObjectCollection>(sourceBill);
                var sourceEntry = sourceEntrys.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(item.SourceEntryId));
                if (sourceEntry == null)
                {
                    resp.Success = false;
                    resp.Message = $"业务单据 {dto.SourceFormId} 明细行不存在 {item.SourceEntryId}，无法匹配可预留量！";
                    return null;
                }

                //需求明细

                var demandEntry = demandEntity.DynamicObjectType.CreateInstance() as DynamicObject;
                demandEntry["fmaterialid"] = sourceEntry[$"{productFieldKey}"];
                demandEntry["fattrinfo"] = sourceEntry["fattrinfo"];
                demandEntry["fcustomdesc"] = sourceEntry["fcustomdes_e"];
                demandEntry["funitid"] = sourceEntry["funitid"];
                demandEntry["fstorehouseid"] = item.StoreHouseId;
                demandEntry["fstockstatus"] = sourceEntry["fstockstatus"];
                demandEntry["fmtono"] = sourceEntry["fmtono"];
                demandEntry["fsourceentryid"] = sourceEntry["id"];
                demandEntrys.Add(demandEntry);
            }
            demandObjs.Add(demandObj);
            return demandObjs;
        }
    }
}
