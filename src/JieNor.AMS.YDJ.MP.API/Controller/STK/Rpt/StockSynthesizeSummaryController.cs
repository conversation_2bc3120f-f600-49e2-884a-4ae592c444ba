using JieNor.AMS.YDJ.MP.API.DTO.STK.Rpt;
using JieNor.AMS.YDJ.MP.API.Model.STK.Rpt;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface.Stock;
using JieNor.AMS.YDJ.DataTransferObject.Rpt;

namespace JieNor.AMS.YDJ.MP.API.Controller.STK.Rpt
{
    /// <summary>
    /// 微信小程序：库存汇总查询取数接口
    /// </summary>
    public class StockSynthesizeSummaryController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(StockSynthesizeSummaryDTO dto)
        {
            var resp = new BaseResponse<BaseListPageData<StockSynthesizeSummaryModel>>
            {
                Data = new BaseListPageData<StockSynthesizeSummaryModel>(dto.PageSize)
            };
            base.InitializeOperationContext(dto);

            StockSynthesizeQueryParaInfo para = new StockSynthesizeQueryParaInfo()
            {
                PageIndex = dto.PageIndex,
                PageSize = dto.PageSize,
                Sortord = dto.Sortord,
                Sortby = dto.Sortby,
                Keyword = dto.Keyword,
                BrandIds = dto.BrandIds,
                SeriesId = dto.SeriesId,
                CategoryId = dto.CategoryId,
                StyleIds = dto.StyleIds,
                SpaceIds = dto.SpaceIds,
                SrcFormId = dto.SrcFormId,
                BillTypeName = dto.BillTypeName,
                BillTypeNo = dto.BillTypeNo,
            };
            var page = this.Container.GetService<IInventoryService>().PageInventorySummary(this.Context, para);

            resp.Data.PageSize = dto.PageSize;
            resp.Data.TotalRecord = page.TotalRecord;
            resp.Data.List = new List<StockSynthesizeSummaryModel>();
            resp.Message = "取数成功！";
            resp.Success = true;

            foreach (var item in page.List)
            {
                var model = new StockSynthesizeSummaryModel();
                model.ProductId = item.ProductId;
                model.ProductName = item.ProductName;
                model.ProductNumber = item.ProductNumber;
                model.AuxPropVals = item.AuxPropVals;

                model.StockQty = item.Qty;
                model.ReserveQty = item.ReserveQty;
                model.IntransitQty = item.IntransitQty;
                model.UsableQty = item.UsableQty;

                model.ImageList = ImageFieldUtil.ParseImages(item.ImageId, item.ImageTxt, true);

                model.StockStatus.Id = item.StockStatusId;
                model.StockStatus.Name = item.StockStatusName;

                model.IsClearStock = item.IsClearStock;
                model.Price = item.SalPrice;
                model.IsSample = item.IsSample;

                resp.Data.List.Add(model);
            }

            return resp;
        }
    }
}
