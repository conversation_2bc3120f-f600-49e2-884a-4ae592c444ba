using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using ServiceStack;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.Building
{
    /// <summary>
    /// 微信小程序：楼盘保存接口
    /// </summary>
    public class BuildingSaveController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(BuildingSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            List<string> errorMsgs = new List<string>();

            if (dto.Name.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 name 不能为空！");
            }

            //if (dto.Developer.IsNullOrEmptyOrWhiteSpace())
            //{
            //    errorMsgs.Add("参数 developer 不能为空！");
            //}

            if (dto.BuildingTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 buildingTypeId 不能为空！");
            }

            if (dto.RenovationId.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 renovationId 不能为空！");
            }

            //if (dto.Image == null || dto.Image.Id.IsNullOrEmptyOrWhiteSpace())
            //{
            //    errorMsgs.Add("参数 image 不能为空！");
            //}

            if (dto.Address.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 address 不能为空！");
            }

            if (dto.ProvinceId.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 provinceId 不能为空！");
            }

            if (dto.CityId.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 cityId 不能为空！");
            }

            if (dto.RegionId.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 regionId 不能为空！");
            }

            if (dto.Inseveral.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 Inseveral 不能为空！");
            }

            if (dto.Decoratenum.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 Decoratenum 不能为空！");
            }

            if (dto.Notdecoratenum.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 Notdecoratenum 不能为空！");
            }
            if (dto.TotalCount.IsNullOrEmptyOrWhiteSpace())
            {
                errorMsgs.Add("参数 TotalCount 不能为空！");
            }

            if (!dto.GiveDate.HasValue)
            {
                errorMsgs.Add("参数 GiveDate 不能为空！");
            }



            if (errorMsgs.Any())
            {
                resp.Message = errorMsgs.Join("<br/>");
                resp.Success = false;
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_building",
                    OperationNo = "save",
                    BillData = BuildBillData(dto),
                    Id = dto.Id
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<BaseDataModel>();

            return resp;
        }

        private string BuildBillData(BuildingSaveDTO dto)
        {
            var data =
                new Dictionary<string, object>
                {
                    { "id", dto.Id },
                    { "fdescription", dto.Description },
                    { "fname", dto.Name },
                    { "fmoney", dto.Price },
                    { "fdeveloper", dto.Developer  },
                    { "fbuildingtype", dto.BuildingTypeId },
                    { "frenovation", dto.RenovationId },
                    { "fhouseholds", dto.TotalCount },

                    { "fprovince", dto.ProvinceId },
                    { "fcity", dto.CityId },
                    { "fregion", dto.RegionId },
                    { "faddress", dto.Address },

                    { "fimage", dto.Image?.Id },
                    { "fimage_txt", dto.Image?.Name },

                    { "finseveral", dto.Inseveral },
                    { "fdecoratenum", dto.Decoratenum },
                    { "fnotdecoratenum", dto.Notdecoratenum },
                    { "fabutment", dto.Abutment },
                    { "fphone", dto.Phone }
                };

            if (dto.OpenDate.HasValue)
            {
                data.Add("fopendate", dto.OpenDate);
            }

            if (dto.GiveDate.HasValue)
            {
                data.Add("fgivedate", dto.GiveDate);
            }

            #region 楼盘户型

            var buildingTypes = new List<Dictionary<string, object>>();

            if (dto.BuildingTypeEntries != null)
            {
                foreach (var item in dto.BuildingTypeEntries)
                {
                    var buildingType = new Dictionary<string, object>
                    {
                        { "id", item.Id },
                        { "froom_enum", item.RoomId },
                        { "fhall_enum", item.HallId },
                        { "ftoilet_enum", item.ToiletId },
                        { "fbalcony_enum", item.BalconyId },
                        { "farea", item.Area },
                        { "fimage", string.Join(",", item.Images.Select(s =>s.Id)) },
                        { "fimage_txt", string.Join(",", item.Images.Select(s =>s.Name)) },
                        { "fdescription", item.Description }
                    };

                    buildingTypes.Add(buildingType);
                }
            }

            data.Add("fbuildingtypeentry", buildingTypes);

            #endregion

            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }
    }
}