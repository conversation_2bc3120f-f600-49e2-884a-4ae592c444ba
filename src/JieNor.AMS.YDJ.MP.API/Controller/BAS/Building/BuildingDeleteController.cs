using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.Building
{
    /// <summary>
    /// 微信小程序：楼盘删除接口
    /// </summary>
    public class BuildingDeleteController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(BuildingDeleteDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_building",
                    OperationNo = "delete",
                    SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } }
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<BaseDataModel>(false);

            return resp;
        }
    }
}