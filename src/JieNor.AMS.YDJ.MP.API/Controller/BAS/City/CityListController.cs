using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.BAS.City;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.BAS.City;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.City
{
    public class CityListController : BaseController
    {
        public object Any(CityListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<CityListModel>>
            {
                Data = new BaseListPageData<CityListModel>(dto.PageSize)
            };

            BuildSqlWhereAndParams(dto, out var param, out var listBuilder);

            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" t0.fnumber like @keyword or t0.fname like @keyword");
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, "%" + dto.Keyword.Trim() + "%"));
            }

            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;

            //排序：默认按创建日期降序
            var orderBy = "fcreatedate";
            //switch (dto.Sortby)
            //{
            //}
            param.OrderByString = $"{orderBy} {dto.Sortord}";

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            //获取分页数据
            var listData = listBuilder.GetQueryData(this.Context, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(this.Context, param, queryObj);

            //将平台通用的列表数据结构转换为API数据结构
            var list = new List<CityListModel>();
            foreach (var item in listData)
            {
                var model = new CityListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fbillhead_id"]),
                    Number = JNConvert.ToStringAndTrim(item["fnumber"]),
                    Name = JNConvert.ToStringAndTrim(item["fname"]),
                    Level = new ComboDataModel
                    {
                        Id = JNConvert.ToStringAndTrim(item["flevel"]),
                        Name = JNConvert.ToStringAndTrim(item["flevel_fenumitem"])
                    },
                    CreateTime = JNConvert.ToStringAndTrim(item["fcreatedate"])
                };
                list.Add(model);
            }

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Data.List = list;

            return resp;
        }

        protected void BuildSqlWhereAndParams(CityListDTO dto, out SqlBuilderParameter param, out IListSqlBuilder listBuilder)
        {
            //参数对象
            param = new SqlBuilderParameter(this.Context, "ydj_city");
            param.ReadDirty = true;
            param.NoColorSetting = true;

            //当前要查询的字段列表
            var fieldKeys = new string[] { "fid", "fnumber", "fname", "flevel", "fcreatedate"};
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(this.Context);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }
            //列表构建器
            listBuilder = this.Container.GetService<IListSqlBuilder>();
            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

        }
    }
}
