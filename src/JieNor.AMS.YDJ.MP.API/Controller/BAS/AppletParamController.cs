using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS
{
    /// <summary>
    /// 微信小程序：会员版小程序参数取数接口
    /// </summary>
    public class AppletParamController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AppletParamDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<AppletParamModel>();

            var profileService = this.Container.GetService<ISystemProfile>();
            var appletParameter = profileService.GetSystemParameter(this.Context, "bas_appletparameter");
            resp.Data.Fopenapplet = Convert.ToBoolean(appletParameter["fopenapplet"]);
            resp.Data.Fappletname = Convert.ToString(appletParameter["fappletname"]);
            resp.Data.Fappletid = Convert.ToString(appletParameter["fappletid"]);
            resp.Data.Fappsecret = Convert.ToString(appletParameter["fappsecret"]);

            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }
    }
}
