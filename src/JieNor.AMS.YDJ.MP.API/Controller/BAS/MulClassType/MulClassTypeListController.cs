using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.BAS.MulClassType
{
    /// <summary>
    /// 微信小程序：多类别基础资料类型取数接口
    /// </summary>
    public class MulClassTypeListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(MulClassTypeListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<object>();

            if (dto.FormId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 formId 不能为空！";
                return resp;
            }

            var fieldKeys = dto.FieldKey?.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (fieldKeys == null || fieldKeys.Length < 1)
            {
                resp.Success = false;
                resp.Message = $"参数 fieldKey 不能为空！";
                return resp;
            }

            var metaService = this.Container?.GetService<IMetaModelService>();
            var bizForm = metaService?.LoadFormModel(this.Context, dto.FormId);
            var comboDs = bizForm.GetComboDataSource(this.Context, dto.FieldKey);

            resp.Data = comboDs;
            resp.Message = "取数成功！";
            resp.Success = true;
            return resp;
        }
    }
}