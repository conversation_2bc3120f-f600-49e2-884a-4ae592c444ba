using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.Report
{
    /// <summary>
    /// 统计分析：员工列表接口
    /// </summary>
    public class ReportStaffListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ReportStaffListDto dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<ReportStaffListModel>>
            {
                Data = new BaseListPageData<ReportStaffListModel>(dto.PageSize)
            };

            GetStaffDatasByPage(this.Context, dto, resp);

            return resp;
        }

        /// <summary>
        /// 分页获取员工信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="dto">请求参数</param>ydj_customer
        /// <param name="resp">返回对象</param>
        /// <returns></returns>
        public void GetStaffDatasByPage(UserContext userCtx, ReportStaffListDto dto, BaseResponse<BaseListPageData<ReportStaffListModel>> resp)
        {
            BuildSqlWhereAndParams(userCtx, dto, out var param);

            //列表构建器
            var listBuilder = userCtx.Container.GetService<IListSqlBuilder>();

            //查询对象
            var queryObj = listBuilder.GetQueryObject(userCtx, param);

            //获取分页数据
            var listDatas = listBuilder.GetQueryData(userCtx, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(userCtx, param, queryObj);

            var models = new List<ReportStaffListModel>();
            foreach (var item in listDatas)
            {
                var staff = new ReportStaffListModel()
                {
                    Id = JNConvert.ToStringAndTrim(item["fbillhead_id"]),
                    Number = JNConvert.ToStringAndTrim(item["fnumber"]),
                    Name = JNConvert.ToStringAndTrim(item["fname"]),
                    Phone = JNConvert.ToStringAndTrim(item["fphone"]),
                    Position = JNConvert.ToStringAndTrim(item["fpositionid"]) == "" ? JNConvert.ToStringAndTrim(item["fmainpositionid"]) : JNConvert.ToStringAndTrim(item["fpositionid"])
                };
                staff.Dept = new BaseDataSimpleModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fdeptid_e"]),
                    Name = JNConvert.ToStringAndTrim(item["fdeptid_e_fname"]),
                };
                staff.MainPost = new BaseDataSimpleModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fmainpositionid"]),
                    Name = JNConvert.ToStringAndTrim(item["fmainpositionid_fname"]),
                };
                models.Add(staff);
            }
            //设置响应数据包
            resp.Data.List = models;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Message = "取数成功！";
            resp.Success = true;
        }

        private static void BuildSqlWhereAndParams(UserContext userCtx, ReportStaffListDto dto, out SqlBuilderParameter param)
        {
            //参数对象
            param = new SqlBuilderParameter(userCtx, "ydj_staff");
            param.ReadDirty = true;
            param.NoColorSetting = true;
            //当前要查询的字段列表
            var fieldKeys = new string[] { "id", "fnumber", "fname", "fphone", "fpositionid", "fdeptid_e", "fdeptid_e.fnumber", "fmainpositionid" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(userCtx);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }
            //过滤条件
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" (fname like @keyword or fphone like @keyword)");
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            //统计获取按数据范围查询
            if (!dto.PermCaption.IsNullOrEmptyOrWhiteSpace())
            {
                var baseFormProvider = userCtx.Container.GetService<IBaseFormProvider>();
                switch (dto.PermCaption)
                {
                    case "本部门":
                        //获取本部门人员
                        string deptId = baseFormProvider.GetMyDepartment(userCtx)?.Id;
                        param.AppendFilterString(" fdeptid_e=@deptId)");
                        param.AddParameter(new SqlParam("@deptId", System.Data.DbType.String, deptId));
                        break;
                    case "本人":
                        var personid = baseFormProvider.GetMyStaff(userCtx)?.Id;
                        param.AppendFilterString(" fid=@fid)");
                        param.AddParameter(new SqlParam("@fid", System.Data.DbType.String, personid));
                        break;
                    default:
                        break;
                }
            }
        }
    }
}
