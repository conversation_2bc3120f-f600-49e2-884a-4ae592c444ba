using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Filter;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Config;
using JieNor.Framework;
using JieNor.Framework.Interface.Log;

namespace JieNor.AMS.YDJ.MP.API.Controller.EWC
{
    /// <summary>
    /// 企业微信：配置请求接口
    /// </summary>
    public class EwcConfigController : ServiceStack.Service
    {
        /// <summary>
        /// 服务容器
        /// </summary>
        private IServiceContainer Container { get; set; }

        /// <summary>
        /// 日志服务
        /// </summary>
        private ILogServiceEx LogService { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns> 
        public object Any(EwcWorkConfigDTO dto)
        {
            this.Container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            this.LogService = this.Container.GetService<ILogServiceEx>();

            var resp = new BaseResponse<object>();

            //调用企业微信临时登录凭证校验接口
            var ewcRes = EwcJsonClient.Invoke<EwcConfigData>(new EwcConfigDTO
            {
                Code = ConfigExtentions.GetDefaultCustomer().Id
            });
            var ewcData = ewcRes.Data;
            if (!ewcRes.Success)
            {
                var errMsg = $"调用企业微信API失败：{ewcRes.Code}-{ewcRes.Msg}";
                this.LogService.Error(errMsg);
                resp.Message = errMsg;
                return resp;
            }

            resp.Success = true;
            resp.Data = ewcData;
            resp.Message = "操作成功！";

            return resp;
        }
    }
}