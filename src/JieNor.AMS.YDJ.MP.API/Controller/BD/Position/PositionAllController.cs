using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Position
{
    /// <summary>
    /// 微信小程序：全部岗位接口
    /// </summary>
    public class PositionAllController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(PositionAllDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListData<BaseDataModel>>();

            resp.Message = "操作成功！";
            resp.Success = true; 
            resp.Data.List = MapTo(dto);

            return resp;
        }

        private List<BaseDataModel> MapTo(PositionAllDTO dto)
        {  
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            // fmainorgid='0' 表示系统预置
            var sqlText = $@"select fid,fnumber,fname from t_ydj_position with(nolock) where fmainorgid=@fmainorgid or fmainorgid='0'";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            var models = new List<BaseDataModel>();
            foreach (var item in list)
            {
                models.Add(new BaseDataModel
                {
                    Id = Convert.ToString(item["fid"]),
                    Number = Convert.ToString(item["fnumber"]),
                    Name = Convert.ToString(item["fname"])
                });
            }

            return models;
        }
    }
}