using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer.Contacts
{
    /// <summary>
    /// 微信小程序：客户联系人保存接口
    /// </summary>
    public class CustomerContactsSaveController : BaseController
    {
        /// <summary>
        /// 客户表单模型
        /// </summary>
        protected HtmlForm CustomerForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerContactsSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            this.CustomerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");

            var resp = new BaseResponse<object>();

            if (dto.CustomerId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "参数 customerId 不能为空！";
                resp.Success = false;
                return resp;
            }
            if (dto.Contact.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"联系人不能为空！";
                return resp;
            }
            if (dto.Phone.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"联系电话不能为空！";
                return resp;
            }

            var formDt = this.CustomerForm.GetDynamicObjectType(this.Context);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, formDt);

            var customerObj = dm.Select(dto.CustomerId) as DynamicObject;

            if (customerObj == null)
            {
                //设置响应数据包
                resp.Message = "客户不存在或已删除，不允许操作！";
                resp.Success = false;
                return resp;
            }

            //加载引用数据
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, formDt, customerObj, false);

            var contactsEntryObjs = customerObj["fcuscontacttry"] as DynamicObjectCollection;
            if (contactsEntryObjs == null)
            {
                resp.Message = "操作失败：无法获取客户联系人信息！";
                resp.Success = false;
                return resp;
            }

            if (dto.IsDefault)
            {
                foreach (var contactsEntryObj in contactsEntryObjs)
                {
                    contactsEntryObj["fisdefault"] = false;
                }
            }

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                // 新建
                var contactsEntryObj =
                        contactsEntryObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                contactsEntryObj["fisdefault"] = dto.IsDefault;
                contactsEntryObj["fcontacter"] = dto.Contact;
                contactsEntryObj["fphone"] = dto.Phone;
                contactsEntryObj["fprovince"] = dto.ProvinceId;
                contactsEntryObj["fcity"] = dto.CityId;
                contactsEntryObj["fregion"] = dto.RegionId;
                contactsEntryObj["faddress"] = dto.Address;
                contactsEntryObj["fcdescription"] = dto.Description;

                contactsEntryObjs.Add(contactsEntryObj);
            }
            else
            {
                // 修改
                var contactsEntryObj = contactsEntryObjs?.FirstOrDefault(s => JNConvert.ToStringAndTrim(s["id"]) == dto.Id);

                if (contactsEntryObj == null)
                {
                    contactsEntryObj =
                        contactsEntryObjs.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                    contactsEntryObjs.Add(contactsEntryObj);
                }

                contactsEntryObj["fisdefault"] = dto.IsDefault;
                contactsEntryObj["fcontacter"] = dto.Contact;
                contactsEntryObj["fphone"] = dto.Phone;
                contactsEntryObj["fprovince"] = dto.ProvinceId;
                contactsEntryObj["fcity"] = dto.CityId;
                contactsEntryObj["fregion"] = dto.RegionId;
                contactsEntryObj["faddress"] = dto.Address;
                contactsEntryObj["fcdescription"] = dto.Description;
                if (Convert.ToBoolean(contactsEntryObj["fcisfirst"]) == true)
                {
                    customerObj["fphone"] = dto.Phone;
                    customerObj["fprovince"] = dto.ProvinceId;
                    customerObj["fcity"] = dto.CityId;
                    customerObj["fregion"] = dto.RegionId;
                    customerObj["fcontacts"] = dto.Contact;
                    customerObj["faddress"] = dto.Address;
                    if (Convert.ToString(customerObj["ftype"]) == "customertype_00")
                    {
                        customerObj["fname"] = dto.Contact;
                    }
                    customerObj["fgeneratesource"] = "微信小程序：客户联系人保存接口";
                }
            }

            //var preService = this.Container.GetService<IPrepareSaveDataService>();
            //preService.PrepareDataEntity(this.Context, this.CustomerForm, new DynamicObject[] { customerObj }, OperateOption.Create());
            //dm.Save(customerObj);

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_customer",
                    OperationNo = "save",
                    BillData = JsonConvert.SerializeObject(new List<DynamicObject> { customerObj })
                });
            var result = response?.OperationResult;

            return result.ToResponseModel<object>();

            //resp.Message = "操作成功！";
            //resp.Success = true;

            //return resp;
        }
    }
}