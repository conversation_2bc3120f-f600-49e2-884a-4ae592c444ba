using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.DataTransferObject;
using System;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer
{
    /// <summary>
    /// 微信小程序：客户检查手机唯一性接口
    /// </summary>
    public class CustomerCheckPhoneController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerCheckPhoneDTO dto)
        {
            base.InitializeOperationContext(dto);

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_customer",
                    OperationNo = "checkphone",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "id", dto.Id },
                        { "phone", dto.Phone },
                        { "wechat", dto.Wechat },
                        { "type", dto.Type },
                        { "name", dto.Name },
                        { "srcstoreid", dto.StoreId }
                    }
                });
            var result = response?.OperationResult;
            if (!result.IsSuccess)
            {
                var resp = new BaseResponse<CheckPhoneResponse>();
                var srvData = result.SrvData;
                if (srvData != null)
                {
                    var data = Newtonsoft.Json.JsonConvert.DeserializeObject<CheckPhoneResponse>(srvData.ToString());
                    resp.Data = data;
                    resp.Code = Convert.ToInt32(data.code);
                    resp.Message = data.message;
                    resp.Success = result.IsSuccess;
                }
                return resp;
            }
            return result.ToResponseModel<object>(false);
        }
    }

    public class CheckPhoneResponse
    {
        public string code { get; set; }

        public string message { get; set; }

        public string deptid { get; set; }

        public string customerid { get; set; }

        public string dutyid { get; set; }
    }
}