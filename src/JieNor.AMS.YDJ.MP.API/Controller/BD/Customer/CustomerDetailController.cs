using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer
{
    /// <summary>
    /// 微信小程序：客户详情取数接口
    /// </summary>
    public class CustomerDetailController : BaseController
    {
        /// <summary>
        /// 客户表单模型
        /// </summary>
        protected HtmlForm CustomerForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<CustomerDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //根据唯一标识获取数据
            this.CustomerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");
            var customerObj = this.CustomerForm.GetBizDataById(this.Context, dto.Id, true);

            //设置响应数据包
            this.SetResponseData(customerObj, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="result"></param>
        /// <param name="resp"></param>
        private void SetResponseData(DynamicObject customerObj, BaseResponse<CustomerDetailModel> resp)
        {
            if (customerObj == null)
            {
                resp.Message = "客户不存在或已被删除！";
                resp.Success = false;
                return;
            }

            resp.Message = "取数成功！";
            resp.Success = true;

            var customerId = Convert.ToString(customerObj["id"]);
            var genderObj = customerObj["fgender_ref"] as DynamicObject;
            var ageObj = customerObj["fage_ref"] as DynamicObject;
            var sourceObj = customerObj["fsource_ref"] as DynamicObject;
            var houseObj = customerObj["fbuildingid_ref"] as DynamicObject;
            var channelObj = customerObj["fchannelid_ref"] as DynamicObject;
            //var dutyObj = customerObj["fdutyid_ref"] as DynamicObject;
            //var deptObj = customerObj["fdeptid_ref"] as DynamicObject;
            var customerLevel = customerObj["fcustomerlevel_ref"] as DynamicObject;     // 会员级别
            var countryObj = customerObj["fcountry_ref"] == null ? null : customerObj["fcountry_ref"] as DynamicObject;
            //简单下拉框模型数据
            var natureId = Convert.ToString(customerObj["fcusnature"]).Trim();
            var natureName = this.CustomerForm.GetSimpleSelectItemText(customerObj, "fcusnature");
            var statusObj = new ComboDataModel(customerObj["fstatus_ref"] as DynamicObject);

            resp.Data.Status = statusObj;
            resp.Data.Id = customerId;
            resp.Data.Memberno = Convert.ToString(customerObj["fmemberno"]);
            resp.Data.Number = Convert.ToString(customerObj["fnumber"]);
            resp.Data.Name = Convert.ToString(customerObj["fname"]);
            resp.Data.Contact = Convert.ToString(customerObj["fcontacts"]);
            resp.Data.Phone = Convert.ToString(customerObj["fphone"]).Trim();
            resp.Data.Wechat = Convert.ToString(customerObj["fwechat"]).Trim();
            resp.Data.District = this.CustomerForm.GetDistrictText(this.Context, customerObj);
            resp.Data.Address = Convert.ToString(customerObj["faddress"]).Trim();
            resp.Data.CreateDate = Convert.ToDateTime(customerObj["fcreatedate"]);
            resp.Data.Gender = new ComboDataModel(genderObj);
            resp.Data.Age = new ComboDataModel(ageObj);
            resp.Data.Source = new ComboDataModel(sourceObj);
            if (countryObj != null)
            {
                resp.Data.Country = new ComboDataModel(countryObj);
            }

            resp.Data.Nature = new ComboDataModel
            {
                Id = natureId,
                Name = natureName
            };
            resp.Data.Amount = Convert.ToDecimal(customerObj["fsumamount"]); //this.GetSumDealAmount(customerId);
            resp.Data.Level = new BaseDataSimpleModel(customerLevel);
            resp.Data.House = new BaseDataSimpleModel(houseObj);
            resp.Data.Channel = new BaseDataSimpleModel(channelObj);
            resp.Data.Integral = Convert.ToInt32(customerObj["favailableintegral"]);
            //resp.Data.Duty = new BaseDataSimpleModel(dutyObj);
            //resp.Data.Dept = new BaseDataSimpleModel(deptObj);
            resp.Data.Images = ImageFieldUtil.ParseImages(customerObj, "fimage");
            resp.Data.ServiceList = this.GetServiceList(customerId);
            resp.Data.DutyList = this.GetDutyList(customerObj);
            var building = customerObj["fbuildingid_ref"] as DynamicObject;
            if (building != null)
            {
                resp.Data.Building = new BaseDataSimpleModel
                {
                    Id = Convert.ToString(building["id"]),
                    Name = Convert.ToString(building["fname"])
                };
            }
            var typeId = Convert.ToString(customerObj["ftype"]).Trim();
            var typeName = this.CustomerForm.GetSimpleSelectItemText(customerObj, "ftype");
            resp.Data.Type = new ComboDataModel
            {
                Id = typeId,
                Name = typeName
            };

            DateTime followTime = Convert.ToDateTime(customerObj["flastfollowdata"]);
            resp.Data.FollowTime = followTime == DateTime.MinValue ? (DateTime?)null : followTime;

            resp.Data.CustomerType = new ComboDataModel(customerObj["fcustomertype_ref"] as DynamicObject);

            //货款金额
            resp.Data.LoanAmount = GetLoanAmount(customerId);
            resp.Data.WorkWxUserid = Convert.ToString(customerObj["fworkwxuserid"]).Trim();

            var referrer = customerObj["freferrer_ref"] as DynamicObject;
            var customerData = GetCustomerByid(JNConvert.ToStringAndTrim(customerObj?["freferrer"]));
            resp.Data.Referrer = new
            {
                Id = JNConvert.ToStringAndTrim(referrer?["id"]),
                Number = JNConvert.ToStringAndTrim(referrer?["fnumber"]),
                Name = JNConvert.ToStringAndTrim(referrer?["fname"]),
                Phone = JNConvert.ToStringAndTrim(customerData?["fphone"])
            };
            //来源门店
            var deptId = Convert.ToString(customerObj["fsrcstoreid"]);
            var deptForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_dept");
            var deptObj = deptForm.GetBizDataById(this.Context, deptId, true);
            if (deptObj != null)
            {
                resp.Data.SourceStore = new BaseDataSimpleModel
                {
                    Id = Convert.ToString(deptObj["id"]),
                    Name = Convert.ToString(deptObj["fname"])
                };
            }
            resp.Data.InvoiceEntries = GetInvoiceEntry(customerObj);
            
            resp.Data.CustomerSapNo = Convert.ToString(customerObj["fcustomersapnumber"]);
        }

        /// <summary>
        /// 返回客户
        /// </summary>
        /// <returns></returns>
        private DynamicObject GetCustomerByid(string fid)
        {
            string strSql = $"select top 1 fid id,fnumber,fname,fphone from  t_ydj_customer with(nolock) where fid='{fid}' ";
            var dt = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            return dt.FirstOrDefault();
        }

        /// <summary>
        /// 获取货款金额
        /// </summary>
        /// <returns></returns>
        private decimal GetLoanAmount(string customerId)
        {
            string strSql = string.Format($@"
select fbalance_e 
from t_ydj_CustomerAccount t1 with(nolock)
inner join t_ydj_customer t2 with(nolock) on t1.fid=t2.fid
where fpurpose='settleaccount_type_01' and t2.fid='{customerId}'");
            decimal loanAmount = 0;
            using (var dr = this.DBService.ExecuteReader(this.Context, strSql))
            {
                if (dr.Read())
                {
                    loanAmount = Convert.ToDecimal(dr["fbalance_e"]);
                }
            }
            return decimal.Round(loanAmount, 2);
        }

        ///// <summary>
        ///// 获取客户总成交金额 = 客户所有已审核的销售合同【订单总额】-【实退金额】
        ///// </summary>
        ///// <param name="customerId"></param>
        ///// <returns></returns>
        //private decimal GetSumDealAmount(string customerId)
        //{
        //    var sumDealAmount = 0M;

        //    var sqlText =
        //        "select sum(fsumamount-factrefundamount) sumDealAmount from t_ydj_order where fmainorgid=@fmainorgid and fcustomerid=@fcustomerid and fstatus='E'";

        //    var sqlParam = new List<SqlParam>
        //    {
        //        new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
        //        new SqlParam("@fcustomerid", System.Data.DbType.String, customerId)
        //    };

        //    using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
        //    {
        //        if (reader.Read())
        //        {
        //            sumDealAmount = reader.GetValueToDecimal("sumDealAmount");
        //            sumDealAmount = decimal.Round(sumDealAmount, 2, MidpointRounding.AwayFromZero);
        //        }
        //    }

        //    return sumDealAmount;
        //}

        /// <summary>
        /// 获取客户服务信息列表
        /// </summary>
        /// <param name="customerId"></param>
        /// <returns></returns>
        private List<CustomerServiceModel> GetServiceList(string customerId)
        {
            var serviceList = new List<CustomerServiceModel>();

            var formMeta = this.MetaModelService.LoadFormModel(this.Context, "ydj_customerrecord");
            var formDt = formMeta.GetDynamicObjectType(this.Context);
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, formDt);

            //商机阶段数据源
            var phaseItems = formMeta.GetSimpleSelectItems("fphase");

            var sqlText = @"
            select m.fid,m.fbillno,si.ffbillamount fwishamount,o.fdealamount fpactamount,m.fphase,
            m.fdutyid,s.fnumber fdutynumber,s.fname fdutyname,m.fcreatedate 
            from t_ydj_customerrecord m with(nolock) 
            left join t_ydj_saleintention si with(nolock) on si.fbillno=m.fintentionno and si.fmainorgid=@fmainorgid 
            left join t_ydj_order o with(nolock) on o.fbillno=m.forderno and o.fmainorgid=@fmainorgid 
            left join t_bd_staff s with(nolock) on s.fid=m.fdutyid and s.fmainorgid=@fmainorgid 
            where m.fmainorgid=@fmainorgid and m.fcustomerid=@fcustomerid 
            order by m.fcreatedate desc";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fcustomerid", System.Data.DbType.String, customerId)
            };

            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    var phaseId = reader.GetValueToString("fphase");
                    var phaseName = phaseItems.GetValue(phaseId) ?? "";

                    serviceList.Add(new CustomerServiceModel
                    {
                        Id = reader.GetValueToString("fid"),
                        Number = reader.GetValueToString("fbillno"),
                        WishAmount = reader.GetValueToDecimal("fwishamount"),
                        PactAmount = reader.GetValueToDecimal("fpactamount"),
                        CreateDate = reader.GetValueToDateTime("fcreatedate"),
                        Duty = new BaseDataSimpleModel
                        {
                            Id = reader.GetValueToString("fdutyid"),
                            Number = reader.GetValueToString("fdutynumber"),
                            Name = reader.GetValueToString("fdutyname")
                        },
                        Phase = new ComboDataModel
                        {
                            Id = phaseId,
                            Name = phaseName
                        }
                    });
                }
            }

            return serviceList;
        }

        /// <summary>
        /// 获取客户负责人信息列表
        /// </summary>
        /// <param name="customerObj"></param>
        /// <returns></returns>
        private List<CustomerDutyModel> GetDutyList(DynamicObject customerObj)
        {
            List<CustomerDutyModel> dutyList = new List<CustomerDutyModel>();
            var dutyEntries = (DynamicObjectCollection)customerObj["fdutyentry"];
            if (dutyEntries == null || dutyEntries.Count == 0) return dutyList;

            var formDt = this.CustomerForm.GetEntryEntity("fdutyentry").DynamicObjectType;
            // 加载引用数据
            var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
            refMgr.Load(this.Context, formDt, dutyEntries, false);

            foreach (var dutyEntry in dutyEntries)
            {
                var dutyObj = dutyEntry["fdutyid_ref"] as DynamicObject;
                var deptObj = dutyEntry["fdeptid_ref"] as DynamicObject;

                dutyList.Add(new CustomerDutyModel
                {
                    Id = JNConvert.ToStringAndTrim(dutyEntry["id"]),
                    Description = JNConvert.ToStringAndTrim(dutyEntry["fdescription"]),
                    Duty = new BaseDataSimpleModel(dutyObj),
                    Dept = new BaseDataSimpleModel(deptObj),
                    JoinTime = Convert.ToDateTime(dutyEntry["fjointime"])
                });
            }

            return dutyList;
        }
        
        /// <summary>
        /// 获取开票信息
        /// </summary>
        /// <param name="customerObj"></param>
        /// <returns></returns>
        public List<InvoiceEntry> GetInvoiceEntry(DynamicObject customerObj)
        {
            var invoiceEntrys = new List<InvoiceEntry>();
            var invoiceEntry = customerObj["finvoiceentry"] as DynamicObjectCollection;
            if (invoiceEntry == null || invoiceEntry.Count == 0) return invoiceEntrys;

            foreach (var item in invoiceEntry)
            {
                invoiceEntrys.Add(new InvoiceEntry
                {
                    Id = JNConvert.ToStringAndTrim(item["id"]),
                    IsDefault = Convert.ToBoolean(Convert.ToInt32(item["finvoicedefault"])),
                    InvoiceType = JNConvert.ToStringAndTrim(item["finvoicetype"]),
                    BuyerFullName = JNConvert.ToStringAndTrim(item["fbuyerfullname"]),
                    TaxpayerIdentify = JNConvert.ToStringAndTrim(item["ftaxpayeridentify"]),
                    InvoiceEmail = JNConvert.ToStringAndTrim(item["finvoiceemail"]),
                    InvoiceAddress = JNConvert.ToStringAndTrim(item["finvoiceaddress"]),
                    InvoicePhone = JNConvert.ToStringAndTrim(item["finvoicephone"]),
                    DepositBankName = JNConvert.ToStringAndTrim(item["fdepositbankname"]),
                    BankAccount = JNConvert.ToStringAndTrim(item["fbankaccount"])
                });
            }

            return invoiceEntrys;
        }
    }
}