using JieNor.AMS.YDJ.MP.API.DTO.BD.Customer.Image;
using JieNor.AMS.YDJ.MP.API.Model.BD.Customer.Image;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer.Image
{
    /// <summary>
    /// 微信小程序：获取客户图库列表
    /// </summary>
    public class CustomerImageListController : BaseController
    {
        /// <summary>
        /// 2021-07-27：改成获取商机里的客户和图库
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerImageListDTO dto)
        {
            base.InitializeOperationContext(dto);
            //初始化页面参数
            int pageIndex = dto.PageIndex < 1 ? 1 : dto.PageIndex;
            var pageSize = dto.PageSize < 1 ? 10 : dto.PageSize;
            var resp = new BaseResponse<BaseListPageData<CustomerImageListModel>>
            {
                Data = new BaseListPageData<CustomerImageListModel>(dto.PageSize)
            };

            var pars = new List<SqlParam>();
            //筛选条件sql
            var wheresql = "";
            if (dto.StartDate.HasValue )//时间段筛选
            {
                wheresql += @" and a.fcreatedate >= @startdate ";
                pars.Add(new SqlParam("@startdate", DbType.DateTime, dto.StartDate));
            }

            if (dto.EndDate.HasValue)
            {
                wheresql += @" and a.fcreatedate < @enddate ";
                pars.Add(new SqlParam("@enddate", DbType.DateTime, dto.EndDate.Value.AddDays(1)));
            }

            pars.Add(new SqlParam("@fmainorgid", DbType.String, this.Context.Company));//企业隔离
            wheresql += @" and a.fmainorgid=@fmainorgid ";

            // 客户图库需要排除没有负责人的商机（2021-08-31）
            wheresql += " and a.fdutyid<>'' ";

            // 商机负责人姓名全匹配
            if (dto.DutyName.IsNullOrEmptyOrWhiteSpace() == false)
            {
                wheresql += " and a.fdutyid in (select fid from t_bd_staff with(nolock) where fname like @fdutyname)";
                pars.Add(new SqlParam("@fdutyname", DbType.String, $"%{dto.DutyName.Trim()}%"));
            }

            // 商机负责部门模糊匹配
            if (dto.DeptName.IsNullOrEmptyOrWhiteSpace() == false)
            {
                wheresql += " and a.fdeptid IN (SELECT fid FROM t_bd_department with(nolock) WHERE fname LIKE @fdeptname)";
                pars.Add(new SqlParam("@fdeptname", DbType.String, $"%{dto.DeptName.Trim()}%"));
            }

            var orderby = "";
            if (dto.Sortord.EqualsIgnoreCase("asc"))
            {
                orderby = " a.fcreatedate asc";
            }
            else
            {
                orderby = " a.fcreatedate desc";//按时间排序（默认倒序）
            }

            //组合sql语句
            var sql = $@"select top {pageSize} * from 
                (select row_number() over(order by {orderby}) rownum, a.fimage,a.fcustomerid,a.fdutyid,a.fcreatedate,b.fname as fdutyname,b.fphone,a.fdeptid,c.fname as fdeptname
                    from T_YDJ_CUSTOMERRECORD as a with(nolock)
                    inner join T_BD_STAFF as b with(nolock) on a.fdutyid=b.fid
                    left join T_BD_DEPARTMENT as c with(nolock) on a.fdeptid=c.fid
                    where fimage <>'' {wheresql}
                ) p
                where rownum > {pageSize} * ({pageIndex} - 1)";
            //获取数据
            var datas = this.DBService.ExecuteDynamicObject(this.Context, sql, pars);
            //组装返回数据
            resp.Data.List = new List<CustomerImageListModel>();
            if (datas != null && datas.Any())
            {
                for (int i = 0; i < datas.Count; i++)
                {
                    var model = new CustomerImageListModel();
                    model.CustomerId = JNConvert.ToStringAndTrim(datas[i]["fcustomerid"]);
                    model.CreateTime = Convert.ToDateTime(datas[i]["fcreatedate"]);
                    model.DeptId = JNConvert.ToStringAndTrim(datas[i]["fdeptid"]);
                    model.DeptName = JNConvert.ToStringAndTrim(datas[i]["fdeptname"]);
                    model.DutyId = JNConvert.ToStringAndTrim(datas[i]["fdutyid"]);
                    model.DutyName = JNConvert.ToStringAndTrim(datas[i]["fdutyname"]);
                    model.DutyPhone = JNConvert.ToStringAndTrim(datas[i]["fphone"]);
                    model.ImageList = JNConvert.ToStringAndTrim(datas[i]["fimage"]).Split(',').ToList().ConvertAll(a => JNConvert.ToStringAndTrim(a).GetSignedFileUrl());
                    resp.Data.List.Add(model);
                }
                resp.Data.TotalRecord = GetTotal(dto);
            }
            resp.Success = true;
            resp.Message = "取数成功";
            return resp;
        }

        /// <summary>
        /// 获取总记录数
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public int GetTotal(CustomerImageListDTO dto)
        {
            var pars = new List<SqlParam>();
            var wheresql = "";
            if (dto.StartDate.HasValue && dto.EndDate.HasValue)//时间段筛选
            {
                wheresql += @" and a.fcreatedate >= @startdate and a.fcreatedate < @enddate ";
                pars.Add(new SqlParam("@startdate", System.Data.DbType.DateTime, dto.StartDate));
                pars.Add(new SqlParam("@enddate", System.Data.DbType.DateTime, dto.EndDate.Value.AddDays(1)));
            }
            pars.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company));//企业隔离
            wheresql += @" and a.fmainorgid=@fmainorgid ";

            // 商机负责部门模糊匹配
            if (dto.DeptName.IsNullOrEmptyOrWhiteSpace() == false)
            {
                wheresql += " and a.fdeptid IN (SELECT fid FROM t_bd_department with(nolock) WHERE fname LIKE @fdeptname)";
                pars.Add(new SqlParam("@fdeptname", DbType.String, $"%{dto.DeptName.Trim()}%"));
            }

            var sql = $@"
select count(1) as total 
from T_YDJ_CUSTOMERRECORD a with(nolock) 
inner join T_BD_STAFF as b with(nolock) on a.fdutyid=b.fid
left join T_BD_DEPARTMENT as c with(nolock) on a.fdeptid=c.fid 
where fimage !='' {wheresql}";
            var datas = DBService.ExecuteDynamicObject(this.Context, sql, pars);
            if (datas != null && datas.Any())
            {
                return Convert.ToInt32(datas[0]["total"]);
            }
            return 0;
        }
    }
}
