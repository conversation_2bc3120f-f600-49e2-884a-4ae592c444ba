using JieNor.AMS.YDJ.MP.API.DTO.BD.Customer;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer
{
    public class CustomerChangeFieldController : BaseController
    {
        public object Any(CustomerChangeFieldDTO dto)
        {
            var resp = new BaseResponse<object>();
            resp.Message = "同步更新成功！";
            resp.Success = true;
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"客户ID不能为空！";
                return resp;
            }
            if (dto.Field.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"客户要修改的字段不能为空！";
                return resp;
            }
            //向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_customer",
                    OperationNo = "changecustomerfieldevent",
                    Option = new Dictionary<string, object>
                {
                        { "id", dto.Id },
                        { "field", dto.Field },
                        { "type", dto.Type }
                },
                });
            var result = response?.OperationResult;
            if (result == null || !result.IsSuccess)
            {
                resp.Message = result?.SimpleMessage ?? "操作失败！";
                resp.Success = false;
               
            }
            return resp;
        }
    }
}
