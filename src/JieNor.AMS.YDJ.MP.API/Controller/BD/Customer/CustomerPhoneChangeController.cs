using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.DTO.BD.Customer;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Customer
{
    public class CustomerPhoneChangeController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(CustomerPhoneChangeDTO dto)
        {
            var resp = new BaseResponse<object>();
            if (!dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                //向麦浩系统发送请求
                var response = JsonClient.Invoke<CommonBillDTO, CommonBillDTOResponse>(
                    this.Request,
                    new CommonBillDTO()
                    {
                        FormId = "ydj_customer",
                        OperationNo = "phonechangework",
                        Option = new Dictionary<string, object>
                    {
                        { "id", dto.Id },
                    },
                    });
                var result = response?.OperationResult;
                if (result == null || !result.IsSuccess)
                {
                    resp.Message = result?.GetErrorMessage() ?? "操作失败！";
                    resp.Success = false;
                    return resp;
                }
                else
                {
                    resp.Message = "同步更新成功！";
                    resp.Success = true;
                    return resp;
                }
            }
            else
            {
                resp.Success = false;
                resp.Message = $"客户ID不能为空！";
                return resp;
            }
            return resp;
        }
    }
}
