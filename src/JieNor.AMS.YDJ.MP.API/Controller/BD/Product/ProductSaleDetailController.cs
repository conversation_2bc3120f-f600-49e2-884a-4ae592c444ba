using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.AMS.YDJ.Core;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品销售详情取数接口
    /// </summary>
    public class ProductSaleDetailController : BaseController
    {
        /// <summary>
        /// 商品表单模型
        /// </summary>
        protected HtmlForm ProductForm { get; set; }

        /// <summary>
        /// 单据表单模型
        /// </summary>
        protected HtmlForm OrderForm { get; set; }

        public string PriceMsg { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductSaleDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ProductSaleDetailModel>();
            if (dto.FormId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"业务表单标识参数 formId 不能为空！";
                return resp;
            }

            this.ProductForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            this.OrderForm = this.MetaModelService.LoadFormModel(this.Context, dto.FormId);

            switch (dto.FormId.ToLowerInvariant())
            {
                //购物车
                case "sal_shopcart":
                    this.SetShopCartResponseData(dto, resp);
                    break;

                //意向单
                case "ydj_saleintention":
                //合同单
                case "ydj_order":
                    this.SetOrderResponseData(dto, resp);
                    break;
            }

            //判断商品是否允许下单
            var orderservice = this.Context.Container.GetService<IOrderService>();
            var productObj = this.ProductForm.GetBizDataById(this.Context, dto.Id);
            resp.Data.IsAllowOrders = orderservice.CheckNoOrders(this.Context, new NoOrderParmas
            {
                fisgiveaway = resp.Data.IsGiveaway,
                fmainorgid = Convert.ToString(productObj["fmainorgid"]),
                fissuit = Convert.ToInt32(productObj["fsuiteflag"]) > 0,
                fprice = resp.Data.SalPrice,
                funstdtype = Convert.ToInt32(productObj["funstdtype"]) > 0
            });

            resp.Data.ComboData = this.ProductForm.GetComboDataSource(this.Context, "fdeliverymode,fspace");//提货方式数据源
            Dictionary<string, List<Dictionary<string, object>>> stockstatus = Getstockstatus();
            resp.Data.ComboData.Merge(stockstatus);
            var prodRequirementAndSelSuiteRequires = this.OrderForm.GetComboDataSource(this.Context, "fprodrequirement,fselsuiterequire,fbiztype");//生产要求、家纺套件要求
            resp.Data.ComboData.Merge(prodRequirementAndSelSuiteRequires);
            resp.Data.MoreNumConf = GetMoreNumConf();

            resp.Message = "取数成功！";
            resp.Success = true;
            if (!PriceMsg.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = PriceMsg;
                resp.IsShowMessage = true;
            }
            return resp;
        }

        /// <summary>
        /// 商品非标选配属性值默认个数显示
        /// </summary>
        /// <returns></returns>
        private int GetMoreNumConf()
        {
            //销售合同允许采购的最低金额比例
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var systemParameter = profileService.GetSystemParameter(this.Context, "bas_storesysparam");
            var foptionalattrnumber = Convert.ToInt32(systemParameter["foptionalattrnumber"]);
            return foptionalattrnumber;
        }

        /// <summary>
        /// 获取库存状态
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, List<Dictionary<string, object>>> Getstockstatus()
        {
            Dictionary<string, List<Dictionary<string, object>>> comboDataSource = new Dictionary<string, List<Dictionary<string, object>>>();
            var sql = "select fid,fname from t_ydj_stockstatus with(nolock) where fforbidstatus='0'";

            var res = DBService.ExecuteDynamicObject(Context, sql);
            var stockstatus = new List<Dictionary<string, object>>();
            if (res != null && res.Any())
            {
                foreach (var item in res)
                {
                    Dictionary<string, object> stockstatu = new Dictionary<string, object>();
                    stockstatu.Add("id", Convert.ToString(item["fid"]));
                    stockstatu.Add("name", Convert.ToString(item["fname"]));
                    stockstatus.Add(stockstatu);
                }
                comboDataSource["fstockstatus"] = stockstatus;
            }
            return comboDataSource;
        }

        /// <summary>
        /// 设置响应数据包 -（意向单、合同单）销售详情
        /// 满足以下几种前端场景的取数：
        /// 1、新增意向单并且新增明细行时，此时 orderId、entryId 取不到可不传，id 必传，auxPropVals、customDesc 可选
        /// 2、修改意向单并且新增明细行时，此时 orderId 能取到可选，entryId 取不到可不传，id 必传，auxPropVals、customDesc 可选
        /// 3、修改意向单并且修改明细行时，此时 orderId、entryId 能取到则必传，id、auxPropVals、customDesc 可选
        /// </summary>
        private void SetOrderResponseData(
            ProductSaleDetailDTO dto,
            BaseResponse<ProductSaleDetailModel> resp)
        {
            DynamicObject orderObj = null;
            if (!dto.OrderId.IsNullOrEmptyOrWhiteSpace())
            {
                orderObj = this.OrderForm.GetBizDataById(this.Context, dto.OrderId);
                if (orderObj == null)
                {
                    resp.Message = $"{this.OrderForm.Caption} {dto.OrderId} 不存在或已被删除！";
                    resp.Success = false;
                    return;
                }
            }

            //商品ID
            var productId = "";

            //辅助属性组合值ID
            var auxPropValId = "";

            //定制说明
            var customDesc = "";

            //提货方式
            var deliveryModeId = "";
            var deliveryModeName = "";

            if (orderObj != null && !dto.EntryId.IsNullOrEmptyOrWhiteSpace())
            {
                var entityKv = new Dictionary<string, string>
                {
                    { "entityKey", "fentity" },
                    { "productFieldKey", "fmaterialid" },
                    { "noteFieldKey", "fnote" }
                };
                if (dto.FormId.EqualsIgnoreCase("ydj_order"))
                {
                    entityKv = new Dictionary<string, string>
                    {
                        { "entityKey", "fentry" },
                        { "productFieldKey", "fproductid" },
                        { "noteFieldKey", "fdescription" }
                    };
                }
                //订单明细行数据
                var entryObj = (orderObj?[entityKv["entityKey"]] as DynamicObjectCollection)
                    ?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(dto.EntryId));
                if (entryObj == null)
                {
                    resp.Message = $"{this.OrderForm.Caption} {dto.OrderId} 的明细行 {dto.EntryId} 不存在或已被删除！";
                    resp.Success = false;
                    return;
                }

                productId = Convert.ToString(entryObj[entityKv["productFieldKey"]]);
                auxPropValId = Convert.ToString(entryObj["fattrinfo"]);
                customDesc = Convert.ToString(entryObj["fcustomdes_e"]).Trim();
                deliveryModeId = Convert.ToString(entryObj["fdeliverymode"]).Trim();
                deliveryModeName = this.OrderForm.GetSimpleSelectItemText(entryObj, "fdeliverymode");

                string fbiztypeid = Convert.ToString(entryObj["fbiztype"]).Trim();

                var fbiz = this.OrderForm.GetAssistDataSource(this.Context, "fbiztype")["fbiztype"];

                foreach (var fbi in fbiz)
                {
                    if (fbi["id"].ToString() == fbiztypeid)
                    {
                        resp.Data.fbiztype = new ComboDataModel
                        {
                            Id = fbiztypeid,
                            Name = fbi["name"].ToString()
                        };
                    }
                }

                if (dto.FormId.EqualsIgnoreCase("ydj_order"))
                {
                    resp.Data.SellPrice = Convert.ToDecimal(entryObj["fSellPrice"]);
                    resp.Data.SellAmount = Convert.ToDecimal(entryObj["fSellAmount"]);
                }

                resp.Data.EntryId = Convert.ToString(entryObj["id"]);
                resp.Data.Qty = Convert.ToDecimal(entryObj["fbizqty"]);
                resp.Data.SalPrice = Convert.ToDecimal(entryObj["fprice"]);
                resp.Data.DistRate = Convert.ToDecimal(entryObj["fdistrate"]);
                resp.Data.DistRateRaw = Convert.ToDouble(entryObj["fdistrateraw"]);
                resp.Data.DealPrice = Convert.ToDecimal(entryObj["fdealprice"]);
                resp.Data.IsOutSpot = Convert.ToBoolean(entryObj["fisoutspot"]);
                resp.Data.IsNonStandard = Convert.ToBoolean(entryObj["funstdtype"]);
                resp.Data.Description = Convert.ToString(entryObj[entityKv["noteFieldKey"]]).Trim();
                resp.Data.IsGiveaway = Convert.ToBoolean(entryObj["fisgiveaway"]);
                //沙发组合号
                resp.Data.SofaCombNumber = Convert.ToString(entryObj["fsofacombnumber"]);
                //加载引用数据
                var refMgr = this.Context.Container.GetService<LoadReferenceObjectManager>();
                refMgr.Load(this.Context, entryObj.DynamicObjectType, entryObj, false);

                var spaceobj = entryObj["fspace_ref"] as DynamicObject;//空间
                var stockstatusobj = entryObj["fstockstatus_ref"] as DynamicObject;//库存状态
                var storehouseobj = entryObj["fstorehouseid_ref"] as DynamicObject;//仓库
                var storelocationobj = entryObj["fstorelocationid_ref"] as DynamicObject;//仓位

                var resultbrandobj = entryObj["fresultbrandid_ref"] as DynamicObject;//业绩品牌
                var prodRequirementObj = entryObj["fprodrequirement_ref"] as DynamicObject;//生产要求
                var selSuiteRequireobj = entryObj["fselsuiterequire_ref"] as DynamicObject;//家纺套件要求

                if (resultbrandobj != null)
                {
                    resp.Data.ResultBrand = new ComboDataModel_status()
                    {
                        Id = Convert.ToString(resultbrandobj["id"]),
                        Name = Convert.ToString(resultbrandobj["fname"]),
                        Editstate = 1
                    };
                }
                else
                {
                    //销售详情如果没有业绩品牌值则取默认系列
                    var product = this.ProductForm.GetBizDataById(this.Context, dto.Id, true);
                    var Obj = product["fseriesid_ref"] as DynamicObject; // 系列
                    resp.Data.ResultBrand = new ComboDataModel_status()
                    {
                        Id = Convert.ToString(Obj?["id"]),
                        Name = Convert.ToString(Obj?["fname"]),
                        Editstate = 1
                    };
                }

                resp.Data.Space = new ComboDataModel(spaceobj);

                if (stockstatusobj != null)
                {
                    resp.Data.StockStatus = new ComboDataModel()
                    {
                        Id = Convert.ToString(stockstatusobj["id"]),
                        Name = Convert.ToString(stockstatusobj["fname"])
                    };
                }

                if (storehouseobj != null)
                {
                    resp.Data.StoreHouse = new ComboDataModel()
                    {
                        Id = Convert.ToString(storehouseobj["id"]),
                        Name = Convert.ToString(storehouseobj["fname"])
                    };
                }
                if (storelocationobj != null)
                {
                    resp.Data.StoreLocation = new ComboDataModel()
                    {
                        Id = Convert.ToString(storelocationobj["id"]),
                        Name = Convert.ToString(storelocationobj["fname"])
                    };
                }

                resp.Data.ProdRequirement = new ComboDataModel(prodRequirementObj);//生产要求
                resp.Data.SelSuiteRequire = new ComboDataModel(selSuiteRequireobj);//家纺套件要求
            }

            //如果前端有传递参数，则以前端传递的为准
            if (!dto.Id.IsNullOrEmptyOrWhiteSpace()) productId = dto.Id;
            if (!dto.CustomDesc.IsNullOrEmptyOrWhiteSpace()) customDesc = dto.CustomDesc;

            if (productId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"{this.OrderForm.Caption} {dto.OrderId} 的商品为空！";
                resp.Success = false;
                return;
            }

            var productObj = this.ProductForm.GetBizDataById(this.Context, productId, true);
            if (productObj == null)
            {
                resp.Message = $"{this.OrderForm.Caption} {dto.OrderId} 的商品 {productId} 不存在或已被删除！";
                resp.Success = false;
                return;
            }

            //如果没有取到提货方式，则取商品的提货方式
            if (deliveryModeId.IsNullOrEmptyOrWhiteSpace())
            {
                deliveryModeId = Convert.ToString(productObj["fdeliverymode"]).Trim();
                deliveryModeName = this.ProductForm.GetSimpleSelectItemText(productObj, "fdeliverymode");
            }

            resp.Data.GuidePrice = Convert.ToDecimal(productObj["fguideprice"]);

            //加载商品价格
            this.LoadProductPrice(dto, resp.Data, productObj, auxPropValId);

            //如果商品勾选了预置辅助属性，则获取对应的辅助属性列表
            var isPresetProp = Convert.ToBoolean(productObj["fispresetprop"]);
            if (isPresetProp)
            {
                if (dto.AuxPropVals != null && dto.AuxPropVals.Any())
                {
                    var auxPropInfo = ProductUtil.GetAuxPropInfo(this.Context, productId, dto.AuxPropVals, dto.IsNonStandard);
                    resp.Data.AuxPropInfo = auxPropInfo;
                }
                else
                {
                    var auxPropInfo = ProductUtil.GetAuxPropInfo(this.Context, productId, auxPropValId, dto.IsNonStandard);
                    resp.Data.AuxPropInfo = auxPropInfo;
                }
            }

            //商品信息
            resp.Data.Id = productId;
            resp.Data.Name = Convert.ToString(productObj["fname"]);
            resp.Data.Number = Convert.ToString(productObj["fnumber"]);
            resp.Data.ImageUrl = StringUtil.CheckURLValid(Convert.ToString(productObj["fimage"])) ? Convert.ToString(productObj["fimage"]) : Convert.ToString(productObj["fimage"]).GetSignedFileUrl(true);
            resp.Data.ImageList = ProductUtil.GetImages(this.Context, productObj, dto.AuxPropVals, dto.CustomDesc);
            resp.Data.IsCustom = Convert.ToBoolean(productObj["fcustom"]);

            //单据信息
            resp.Data.FormId = dto.FormId;
            resp.Data.OrderId = dto.OrderId;
            resp.Data.CustomDesc = customDesc;
            resp.Data.DeliveryMode = new ComboDataModel
            {
                Id = deliveryModeId,
                Name = deliveryModeName
            };

            var brandObj = productObj["fbrandid_ref"] as DynamicObject;   // 品牌
            var seriesObj = productObj["fseriesid_ref"] as DynamicObject; // 系列
            resp.Data.Brand = new ComboDataModel
            {
                Id = Convert.ToString(brandObj?["id"]),
                Name = Convert.ToString(brandObj?["fname"])
            };

            resp.Data.Series = new ComboDataModel
            {
                Id = Convert.ToString(seriesObj?["id"]),
                Name = Convert.ToString(seriesObj?["fname"])
            };

            //销售详情如果没有业绩品牌值则取默认系列
            if (resp.Data.ResultBrand.Id.IsNullOrEmptyOrWhiteSpace())
            {
                var billTypeService = this.Container.GetService<IBillTypeService>();
                var billTypeObj = billTypeService.GetBillTypeInfor(this.Context, dto.Billtype);

                resp.Data.ResultBrand = this.Context.GetResultBrand(dto.DeptId, 
                    Convert.ToString(billTypeObj.fname), 
                    Convert.ToString(seriesObj?["id"]), 
                    Convert.ToString(seriesObj?["fnumber"]), 
                    Convert.ToString(seriesObj?["fname"]), 
                    Convert.ToString(productObj?["fauxseriesid"]));
            }

            resp.Data.IsSofaCategory = ProductUtil.HaveAnyCategory(this.Context, "沙发类", productId);

            /*
             1.2.1.当前商品的【商品类别】所有层级中，有一个层级等于”床品套件” 且 对应行【系列】的编码为 “A1” 且 对应行勾选上【允许定制】时，才显示【家纺套件要求】，显示时可以选择，否则隐藏。（此逻辑由后端统一判断返回到小程序）
             */
            //商品类别
            var fcategoryid = productObj["fcategoryid_ref"] as DynamicObject;
            var flag = ProductUtil.HaveAnyCategory(this.Context, "床品套件", productId);
            if (flag && Convert.ToString(seriesObj?["fnumber"]) == "A1" && Convert.ToBoolean(productObj?["fcustom"]))
            {
                resp.Data.IsDisplaySelSuiteRequire = true;
            }
        }

        /// <summary>
        /// 加载商品价格
        /// </summary>
        private void LoadProductPrice(ProductSaleDetailDTO dto, ProductSaleDetailModel model, DynamicObject productObj, string auxPropValId)
        {
            // 如果前端未传商品ID，则无需处理
            if (dto.Id.IsNullOrEmptyOrWhiteSpace()) return;

            // 是否是非标商品
            var isNonStandard = Convert.ToBoolean(productObj["funstdtype"]);

            // 选配计价服务
            var formulaService = this.Container.GetService<ISelectionPriceService>();

            // 销售价（如果是非标商品，则销售价直接返回0）
            var salPrice = 0M;

            // 总部零售价
            var hqSalPrice = 0M;

            // 如果有传递辅助属性值，则按照前端传递的来匹配
            if (dto.AuxPropVals != null && dto.AuxPropVals.Any())
            {
                if (!isNonStandard)
                {
                    var priceInfo = ProductUtil.GetPrices(this.Context, dto.Id, DateTime.Now, false, dto.AuxPropVals);
                    salPrice = priceInfo.SalPrice;
                    hqSalPrice = ProductUtil.GetPrices(this.Context, dto.Id, DateTime.Now, true, dto.AuxPropVals).SalPrice;
                    PriceMsg = priceInfo.PriceMsg;
                    var propList = ProductUtil.LoadPropEntityList(this.Context, dto.AuxPropVals);
                    if (!priceInfo.IgnorePriceFormula)
                    {
                        salPrice = formulaService.GetProductPrice(this.Context, dto.Id, salPrice, propList);
                    }
                    hqSalPrice = formulaService.GetProductPrice(this.Context, dto.Id, hqSalPrice, propList);
                }
            }
            else
            {
                // 按照明细行的辅助属性组合值ID来匹配
                if (!isNonStandard)
                {
                    var priceInfo = ProductUtil.GetPrices(this.Context, dto.Id, auxPropValId, DateTime.Now, false);
                    salPrice = priceInfo.SalPrice;
                    hqSalPrice = ProductUtil.GetPrices(this.Context, dto.Id, auxPropValId, DateTime.Now, true).SalPrice;
                    PriceMsg = priceInfo.PriceMsg;
                    var propList = ProductUtil.LoadPropEntityList(this.Context, auxPropValId);
                    //if (!priceInfo.IgnorePriceFormula)
                    //{
                    //    salPrice = formulaService.GetProductPrice_New(this.Context, dto.Id, salPrice, propList);
                    //}
                    //hqSalPrice = formulaService.GetProductPrice_New(this.Context, dto.Id, hqSalPrice, propList);
                }
            }

            model.SalPrice = salPrice;
            model.HqSalPrice = hqSalPrice;
        }

        //        private ComboDataModel_status DoResultBrand(ProductSaleDetailDTO dto, DynamicObject seriesObj, DynamicObject productObj)
        //        {
        //            //默认取当前商品系列
        //            var deptid = dto.DeptId;
        //            var auxseriesid = Convert.ToString(productObj?["fauxseriesid"]);
        //            List<string> auxseriesids = auxseriesid.Split(',').Where(o => ProductUtil.CheckIsResultBrand(this.Context, Convert.ToString(o))).ToList<string>();
        //            var auxseriesname = GetBaseDataNameById(this.Context, "ydj_series", auxseriesid);
        //            var seriesid = Convert.ToString(seriesObj?["id"]);
        //            var seriesname = Convert.ToString(seriesObj?["fname"]);
        //            var seriescode = GetBaseDataNameById(this.Context, "ydj_series", seriesid, "fnumber");
        //            var billTypeService = this.Container.GetService<IBillTypeService>();
        //            var billTypeObj = billTypeService.GetBillTypeById(this.Context, dto.Billtype);
        //            var Editstate = 1;
        //            //商品授权表 经销商代理情况
        //            //按 城市 + 实控人 获取当前用户对应的所有经销商组织
        //            var AgentInfos = new ProductDataIsolateHelper().GetCurrentUserAgentInfos(this.Context);
        //            //获取 经销商所有的【业绩品牌】
        //            var Agents = AgentInfos.Select(o => o.OrgId).ToList();
        //            // 根据 城市 + 实控人 获取当前用户对应的所有经销商组织，比如说 查出来是(A、B、C)，以及当前企业 A 为主经销商匹配 主经销商配置表下的 A1、A2 最后经销商 结果集就是 （A、B、C、A1、A2）
        //            var MainAgentConfigs = this.Context.LoadBizDataByACLFilter("bas_mainagentconfig", $" fmainagentid = '{this.Context.Company}'").FirstOrDefault();
        //            if (MainAgentConfigs != null)
        //            {
        //                var MainAgentConfigsEntity = (MainAgentConfigs?["fsubagent"] as DynamicObjectCollection).Select(o => Convert.ToString(o["fsubagentid"])).ToList();
        //                Agents.AddRange(MainAgentConfigsEntity);
        //            }
        //            var sqlAllAgent = $@"
        //SELECT DISTINCT te.fserieid FROM t_ydj_productauth t with(nolock) 
        //INNER JOIN t_ydj_productauthbs te with(nolock) ON te.fid = t.fid
        //WHERE t.forgid in ('{string.Join("','", Agents)}') ";
        //            var AllAgentresult = this.DBService.ExecuteDynamicObject(this.Context, sqlAllAgent).ToList();
        //            //获取经销商所有授权的系列
        //            List<Dictionary<string, string>> ResultbrandDic_agent = GetDicBySplitIds(AllAgentresult);

        //            var storeid = ProductDataIsolateHelper.GetStoreOrgIdByDept(this.Context, deptid);
        //            var sql = $@"  
        //SELECT DISTINCT te.fserieid FROM t_ydj_productauth t with(nolock) 
        //INNER JOIN t_ydj_productauthbs te with(nolock) ON te.fid = t.fid
        //WHERE t.forgid = '{storeid}' 
        //                                   ";
        //            var result = this.DBService.ExecuteDynamicObject(this.Context, sql).ToList();
        //            //获取门店授权系列情况
        //            List<Dictionary<string, string>> ResultbrandDic = GetDicBySplitIds(result);
        //            var brandcode = ResultbrandDic.Where(o => Convert.ToString(o["fnumber"]) != "M1" && Convert.ToString(o["fnumber"]) != "Z1").ToList();
        //            var brandcode_M1 = ResultbrandDic.Where(o => Convert.ToString(o["fnumber"]) == "M1").ToList();

        //            //商品授权表 门店仅代理情况
        //            //List<Dictionary<string, string>> ResultBrandList = GetResultBrandDataList(this.Context, Convert.ToString(seriesObj?["id"]), null, deptid, null, null, "ydj_order");
        //            if (Convert.ToString(billTypeObj?["fname"]) == "大客户销售合同")
        //            {
        //                //编码为”Z2” (慕思经典-新渠道)时,默认【业绩品牌】为空 还有 Z1、M1也要清空业绩品牌
        //                if (seriescode == "Z1" || seriescode == "M1")
        //                {
        //                    seriesid = "";
        //                    seriesname = "";
        //                    Editstate = 1;
        //                }
        //                else
        //                {
        //                    //如果当前商品系列有授权，则默认带出系列为业绩品牌
        //                    if (ResultbrandDic_agent.Any(o => Convert.ToString(o["fnumber"]).EqualsIgnoreCase(seriescode)))
        //                    {
        //                        seriesid = Convert.ToString(seriesObj?["id"]);
        //                        seriesname = Convert.ToString(seriesObj?["fname"]);
        //                        Editstate = 0;
        //                    }
        //                    //否则默认带出商品的附属品牌
        //                    else
        //                    {
        //                        //如果附属品牌也没有授权，或者附属品牌为空，则设置为商品系列，不可编辑
        //                        if (!ResultbrandDic.Any(o => auxseriesids.Contains(Convert.ToString(o["fserieid"]))) || auxseriesids.Count == 0)
        //                        {
        //                            seriesid = seriesid;
        //                            seriesname = seriesname;
        //                            Editstate = 0;
        //                        }
        //                        else
        //                        {
        //                            var auxseriesid_has = ResultbrandDic.Find(o => auxseriesids.Contains(Convert.ToString(o["fserieid"])));
        //                            seriesid = Convert.ToString(auxseriesid_has?["fserieid"]);
        //                            seriesname = GetBaseDataNameById(this.Context, "ydj_series", Convert.ToString(auxseriesid_has?["fserieid"]));
        //                            Editstate = 0;
        //                        }

        //                    }
        //                }
        //            }
        //            else
        //            {

        //                //1、如果授权系列 排除 M1、Z1 仅剩A1  则视为仅代理A1
        //                if (brandcode.Count == 1 && brandcode[0]["fnumber"] == "A1")
        //                {
        //                    seriesid = Convert.ToString(brandcode[0]["fserieid"]);
        //                    seriesname = GetBaseDataNameById(this.Context, "ydj_series", Convert.ToString(brandcode[0]["fserieid"]));
        //                    Editstate = 0;
        //                }
        //                //2、如果授权系列 排除 M1、Z1 仅剩Y1  则视为仅代理Y1
        //                else if (brandcode.Count == 1 && brandcode[0]["fnumber"] == "Y1")
        //                {
        //                    seriesid = Convert.ToString(brandcode[0]["fserieid"]);
        //                    seriesname = GetBaseDataNameById(this.Context, "ydj_series", Convert.ToString(brandcode[0]["fserieid"]));
        //                    Editstate = 0;
        //                }
        //                //3、如果授权系列 排除 M1、Z1 为空 则视为仅代理M1
        //                else if (brandcode_M1.Count > 0 && brandcode.Count == 0)
        //                {
        //                    seriesid = Convert.ToString(brandcode_M1[0]["fserieid"]);
        //                    seriesname = GetBaseDataNameById(this.Context, "ydj_series", Convert.ToString(brandcode_M1[0]["fserieid"]));
        //                    Editstate = 0;
        //                }
        //                //其他情况为当前组织代理了多系列的情况
        //                else
        //                {
        //                    //通配商品
        //                    if ((seriescode == "Z1" || seriescode == "M1") && !deptid.IsNullOrEmptyOrWhiteSpace())
        //                    {
        //                        seriesid = "";
        //                        seriesname = "";
        //                        Editstate = 1;
        //                    }
        //                    //正常商品 (非通配品牌 或 非慕思助眠  或 非慕思经典-新渠道)
        //                    else
        //                    {
        //                        //如果当前商品系列有授权，则默认带出系列为业绩品牌
        //                        if (ResultbrandDic.Any(o => Convert.ToString(o["fnumber"]).EqualsIgnoreCase(seriescode)))
        //                        {
        //                            seriesid = Convert.ToString(seriesObj?["id"]);
        //                            seriesname = Convert.ToString(seriesObj?["fname"]);
        //                            Editstate = 0;
        //                        }
        //                        //否则默认带出商品的附属品牌
        //                        else
        //                        {
        //                            //如果附属品牌也没有授权，或者附属品牌为空，则设置为商品系列，不可编辑
        //                            if (!ResultbrandDic.Any(o => auxseriesids.Contains(Convert.ToString(o["fserieid"]))) || auxseriesids.Count == 0)
        //                            {
        //                                seriesid = seriesid;
        //                                seriesname = seriesname;
        //                                Editstate = 0;
        //                            }
        //                            else
        //                            {
        //                                var auxseriesid_has = ResultbrandDic.Find(o => auxseriesids.Contains(Convert.ToString(o["fserieid"])));
        //                                seriesid = Convert.ToString(auxseriesid_has?["fserieid"]);
        //                                seriesname = GetBaseDataNameById(this.Context, "ydj_series", Convert.ToString(auxseriesid_has?["fserieid"]));
        //                                Editstate = 0;
        //                            }
        //                        }
        //                    }
        //                }
        //            }

        //            //特殊逻辑 如果经销商仅授权Z2
        //            if (ResultbrandDic_agent.Any(o => Convert.ToString(o["fnumber"]).Equals("Z2") && ResultbrandDic_agent.Count == 1))
        //            {
        //                seriesid = Convert.ToString(ResultbrandDic_agent[0]["fserieid"]);
        //                seriesname = GetBaseDataNameById(this.Context, "ydj_series", Convert.ToString(ResultbrandDic_agent[0]["fserieid"]));
        //                Editstate = 0;
        //            }

        //            //如果没有传部门 或者没有代理门店的情况 （正常情况是门店仅代理某系列或代理几种系列，这里为了防止没有做门店代理授权逻辑）则默认带出商品系列且可编辑与PC保持一致
        //            if (dto.DeptId.IsNullOrEmptyOrWhiteSpace())
        //            //if (dto.DeptId.IsNullOrEmptyOrWhiteSpace() || ResultBrandList.Count==0) 
        //            {
        //                seriesid = Convert.ToString(seriesObj?["id"]);
        //                seriesname = Convert.ToString(seriesObj?["fname"]);
        //                Editstate = 1;
        //            }
        //            //最后判断系列是否勾选业绩品牌，如果没有勾选则返回空。
        //            if (!ProductUtil.CheckIsResultBrand(this.Context, seriesid))
        //            {
        //                seriesid = "";
        //                seriesname = "";
        //                Editstate = 1;
        //            }
        //            //如果门店或经销商没有商品授权系列，则返回空系列与PC保持一致
        //            if (ResultbrandDic_agent.Count == 0 || ResultbrandDic.Count == 0)
        //            {
        //                if (seriescode == "Z1" || seriescode == "M1")
        //                {
        //                    seriesid = "";
        //                    seriesname = "";
        //                    Editstate = 1;
        //                }
        //                else
        //                {
        //                    seriesid = seriesid;
        //                    seriesname = seriesname;
        //                    Editstate = 0;
        //                }
        //            }
        //            ComboDataModel_status ResultBrand = new ComboDataModel_status()
        //            {
        //                Id = seriesid,
        //                Name = seriesname,
        //                Editstate = Editstate
        //            };
        //            return ResultBrand;
        //        }

        //        //将其中有特殊符号拼接的字段拆分 组合成字典
        //        private List<Dictionary<string, string>> GetDicBySplitIds(List<DynamicObject> list)
        //        {

        //            var Split = new List<string>();
        //            List<Dictionary<string, string>> Dic = new List<Dictionary<string, string>>();
        //            var AllSeries = this.Context.LoadBizDataByFilter("ydj_series", " fisresultbrand =1 ");
        //            foreach (var i in list)
        //            {
        //                var fserieid = i["fserieid"]?.ToString();
        //                Split.AddRange(fserieid.SplitKey());
        //            }
        //            foreach (var c in Split)
        //            {
        //                var fnumber = AllSeries.Where(o => Convert.ToString(o["Id"]) == c).Select(o => o["fnumber"]).FirstOrDefault();
        //                Dictionary<string, string> a = new Dictionary<string, string>
        //                        {
        //                            { "fserieid",c },
        //                            {"fnumber",Convert.ToString(fnumber)}
        //                        };
        //                Dic.Add(a);
        //            }
        //            return Dic;
        //        }

        /// <summary>
        /// 设置响应数据包 - 购物车销售详情
        /// </summary>
        private void SetShopCartResponseData(
            ProductSaleDetailDTO dto,
            BaseResponse<ProductSaleDetailModel> resp)
        {
            if (dto.OrderId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"业务表单数据ID参数 orderId 不能为空！";
                resp.Success = false;
                return;
            }

            var orderObj = this.OrderForm.GetBizDataById(this.Context, dto.OrderId, true);
            if (orderObj == null)
            {
                resp.Message = $"{this.OrderForm.Caption} {dto.OrderId} 不存在或已被删除！";
                resp.Success = false;
                return;
            }

            var productId = Convert.ToString(orderObj["fproductid"]);
            if (productId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = $"{this.OrderForm.Caption} {dto.OrderId} 的商品为空！";
                resp.Success = false;
                return;
            }

            var productObj = this.ProductForm.GetBizDataById(this.Context, productId, true);
            if (productObj == null)
            {
                resp.Message = $"{this.OrderForm.Caption} {dto.OrderId} 的商品 {productId} 不存在或已被删除！";
                resp.Success = false;
                return;
            }

            var auxPropValId = Convert.ToString(orderObj["fattrinfo"]);
            var customDesc = Convert.ToString(orderObj["fcustomdesc"]).Trim();

            //如果没有提货方式，则默认取商品的提货方式
            var deliveryModeId = Convert.ToString(orderObj["fdeliverymode"]).Trim();
            var deliveryModeName = this.OrderForm.GetSimpleSelectItemText(orderObj, "fdeliverymode");
            if (deliveryModeId.IsNullOrEmptyOrWhiteSpace())
            {
                deliveryModeId = Convert.ToString(productObj["fdeliverymode"]).Trim();
                deliveryModeName = this.ProductForm.GetSimpleSelectItemText(productObj, "fdeliverymode");
            }

            string fbiztypeid = Convert.ToString(orderObj["fbiztype"]).Trim();

            var fbiz = this.OrderForm.GetAssistDataSource(this.Context, "fbiztype")["fbiztype"];

            foreach (var fbi in fbiz)
            {
                if (fbi["id"].ToString() == fbiztypeid)
                {
                    resp.Data.fbiztype = new ComboDataModel
                    {
                        Id = fbiztypeid,
                        Name = fbi["name"].ToString()
                    };
                }
            }

            //如果商品勾选了预置辅助属性，则获取对应的辅助属性列表
            var isPresetProp = Convert.ToBoolean(productObj["fispresetprop"]);
            if (isPresetProp)
            {
                var auxPropInfo = ProductUtil.GetAuxPropInfo(this.Context, productId, auxPropValId, dto.IsNonStandard);
                resp.Data.AuxPropInfo = auxPropInfo;
            }

            //商品信息
            resp.Data.Id = productId;
            resp.Data.Name = Convert.ToString(productObj["fname"]);
            resp.Data.Number = Convert.ToString(productObj["fnumber"]);
            resp.Data.ImageUrl = StringUtil.CheckURLValid(Convert.ToString(productObj["fimage"])) ? Convert.ToString(productObj["fimage"]) : Convert.ToString(productObj["fimage"]).GetSignedFileUrl(true);
            resp.Data.ImageList = ProductUtil.GetImages(this.Context, productObj, auxPropValId, customDesc);
            resp.Data.GuidePrice = Convert.ToDecimal(productObj["fguideprice"]);
            resp.Data.IsCustom = Convert.ToBoolean(productObj["fcustom"]);

            //单据信息
            resp.Data.FormId = this.OrderForm.Id;
            resp.Data.OrderId = Convert.ToString(orderObj["id"]);
            resp.Data.CustomDesc = customDesc;
            resp.Data.DeliveryMode = new ComboDataModel
            {
                Id = deliveryModeId,
                Name = deliveryModeName
            };
            resp.Data.SellPrice = Convert.ToDecimal(orderObj["fSellPrice"]);
            resp.Data.SellAmount = Convert.ToDecimal(orderObj["fSellAmount"]);
            resp.Data.Qty = Convert.ToDecimal(orderObj["fqty"]);
            resp.Data.SalPrice = Convert.ToDecimal(orderObj["fprice"]);
            resp.Data.DistRate = Convert.ToDecimal(orderObj["fdistrate"]);
            resp.Data.DealPrice = Convert.ToDecimal(orderObj["fdealprice"]);
            resp.Data.IsOutSpot = Convert.ToBoolean(orderObj["fisoutspot"]);
            resp.Data.IsNonStandard = Convert.ToBoolean(orderObj["funstdtype"]);
            resp.Data.Description = Convert.ToString(orderObj["fdescription"]).Trim();
            resp.Data.IsGiveaway = Convert.ToBoolean(orderObj["fisgiveaway"]);
            //沙发组合号
            resp.Data.SofaCombNumber = Convert.ToString(orderObj["fsofacombnumber"]);

            var spaceobj = orderObj["fspace_ref"] as DynamicObject;//空间
            var stockstatusobj = orderObj["fstockstatus_ref"] as DynamicObject;//库存状态
            var storehouseobj = orderObj["fstorehouseid_ref"] as DynamicObject;//仓库
            var storelocationobj = orderObj["fstorelocationid_ref"] as DynamicObject;//仓位

            resp.Data.Space = new ComboDataModel(spaceobj);
            if (stockstatusobj != null)
            {
                resp.Data.StockStatus = new ComboDataModel()
                {
                    Id = Convert.ToString(stockstatusobj["id"]),
                    Name = Convert.ToString(stockstatusobj["fname"])
                };
            }

            if (storehouseobj != null)
            {
                resp.Data.StoreHouse = new ComboDataModel()
                {
                    Id = Convert.ToString(storehouseobj["id"]),
                    Name = Convert.ToString(storehouseobj["fname"])
                };
            }
            if (storelocationobj != null)
            {
                resp.Data.StoreLocation = new ComboDataModel()
                {
                    Id = Convert.ToString(storelocationobj["id"]),
                    Name = Convert.ToString(storelocationobj["fname"])
                };
            }

            var brandObj = productObj["fbrandid_ref"] as DynamicObject;   // 品牌
            var seriesObj = productObj["fseriesid_ref"] as DynamicObject; // 系列
            var prodRequirementObj = orderObj["fprodrequirement_ref"] as DynamicObject;//生产要求
            var selSuiteRequireobj = orderObj["fselsuiterequire_ref"] as DynamicObject;//家纺套件要求
            resp.Data.Brand = new ComboDataModel
            {
                Id = Convert.ToString(brandObj?["id"]),
                Name = Convert.ToString(brandObj?["fname"])
            };

            resp.Data.Series = new ComboDataModel
            {
                Id = Convert.ToString(seriesObj?["id"]),
                Name = Convert.ToString(seriesObj?["fname"])
            };
            //销售详情如果没有业绩品牌值则取默认系列
            if (resp.Data.ResultBrand.Id.IsNullOrEmptyOrWhiteSpace())
            {
                var billTypeService = this.Container.GetService<IBillTypeService>();
                var billTypeObj = billTypeService.GetBillTypeInfor(this.Context, dto.Billtype);

                resp.Data.ResultBrand = this.Context.GetResultBrand(dto.DeptId, 
                    Convert.ToString(billTypeObj?.fname), 
                    Convert.ToString(seriesObj?["id"]), 
                    Convert.ToString(seriesObj?["fnumber"]), 
                    Convert.ToString(seriesObj?["fname"]), 
                    Convert.ToString(productObj?["fauxseriesid"]));
            }

            resp.Data.ProdRequirement = new ComboDataModel(prodRequirementObj);//生产要求
            resp.Data.SelSuiteRequire = new ComboDataModel(selSuiteRequireobj);//家纺套件要求

            resp.Data.IsSofaCategory = ProductUtil.HaveAnyCategory(this.Context, "沙发类", productId);
            /*
             1.2.1.当前商品的【商品类别】所有层级中，有一个层级等于”床品套件” 且 对应行【系列】的编码为 “A1” 且 对应行勾选上【允许定制】时，才显示【家纺套件要求】，显示时可以选择，否则隐藏。（此逻辑由后端统一判断返回到小程序）
             */
            //商品类别
            var fcategoryid = productObj["fcategoryid_ref"] as DynamicObject;
            var flag = ProductUtil.HaveAnyCategory(this.Context, "床品套件", productId);
            if (flag && Convert.ToString(seriesObj?["fnumber"]) == "A1" && Convert.ToBoolean(productObj?["fcustom"]))
            {
                resp.Data.IsDisplaySelSuiteRequire = true;
            }
        }

        ////根据id返回fname 或者其他字段
        //public string GetBaseDataNameById(UserContext context, string formId, string id, string fieldName = "fname")
        //{
        //    //            this.MetaModelService = context.Container.GetService<IMetaModelService>();

        //    if (id.IsNullOrEmptyOrWhiteSpace()) return "";

        //    var htmlForm = context.Container.GetService<IMetaModelService>()?.LoadFormModel(context, formId);
        //    var dm = context.Container.GetService<IDataManager>();
        //    dm.InitDbContext(context, htmlForm.GetDynamicObjectType(context));

        //    var dynObj = dm.Select(id) as DynamicObject;
        //    if (dynObj != null)
        //    {
        //        return dynObj[fieldName] as string;
        //    }
        //    return "";
        //}
    }
}