using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Core.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品库存详情取数接口
    /// </summary>
    public class ProductStockDetailController : BaseController
    {
        /// <summary>
        /// 商品表单模型
        /// </summary>
        protected HtmlForm ProductForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductStockDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ProductStockDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //根据唯一标识获取数据
            this.ProductForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var productObj = this.ProductForm.GetBizDataById(this.Context, dto.Id);
            if (productObj == null)
            {
                resp.Message = "商品不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            var productId = Convert.ToString(productObj["id"]);
            var salPrice = Convert.ToDecimal(productObj["fsalprice"]);
            var guidePrice = Convert.ToDecimal(productObj["fguideprice"]);

            //获取零售价
            var price = ProductUtil.GetPrices(this.Context, productId, DateTime.Now, false, dto.AuxPropVals);

            //获取总部零售价
            var hqPrice = ProductUtil.GetPrices(this.Context, productId, DateTime.Now, true, dto.AuxPropVals);
            var PriceMsg = price.PriceMsg;
            //选配计价服务
            var formulaService = this.Container.GetService<ISelectionPriceService>();

            var propList = ProductUtil.LoadPropEntityList(this.Context, dto.AuxPropVals);
            //if (!price.IgnorePriceFormula)
            //{
            //    price.SalPrice = formulaService.GetProductPrice_New(this.Context, productId, price.SalPrice, propList);
            //}
            //hqPrice.SalPrice = formulaService.GetProductPrice_New(this.Context, productId, hqPrice.SalPrice, propList);

            //获取即时库存明细
            var realStockDetails = ProductUtil.GetStockDetail(this.Context, productId, dto.AuxPropVals, dto.CustomDesc);

            resp.Data = realStockDetails ?? new ProductStockDetailModel();

            //如果商品勾选了预置辅助属性，则获取对应的辅助属性列表
            var isPresetProp = Convert.ToBoolean(productObj["fispresetprop"]);
            if (isPresetProp && dto.LoadAuxProp)
            {
                var auxPropInfo = ProductUtil.GetAuxPropInfo(this.Context, productId);
                resp.Data.AuxPropInfo = auxPropInfo;
            }
            var orderservice = this.Context.Container.GetService<IOrderService>();
            resp.Data.IsSofaCategory = ProductUtil.HaveAnyCategory(this.Context, "沙发类", Convert.ToString(productObj["id"]));
            resp.Data.Name = Convert.ToString(productObj["fname"]);
            resp.Data.Number = Convert.ToString(productObj["fnumber"]);
            resp.Data.ImageUrl = StringUtil.CheckURLValid(Convert.ToString(productObj["fimage"])) ? Convert.ToString(productObj["fimage"]) : Convert.ToString(productObj["fimage"]).GetSignedFileUrl(); Convert.ToString(productObj["fimage"]).GetSignedFileUrl(true);
            resp.Data.ImageList = ProductUtil.GetImages(this.Context, productObj, dto.AuxPropVals, dto.CustomDesc);
            resp.Data.GuidePrice = Convert.ToDecimal(productObj["fguideprice"]);
            resp.Data.HqSalPrice = hqPrice.SalPrice;
            resp.Data.SalPrice = price.SalPrice;
            resp.Data.GuidePrice = price.GuidePrice;
            resp.Data.IsNonStandard = Convert.ToInt32(productObj["funstdtype"]) > 0;
            resp.Data.IsSuite = Convert.ToInt32(productObj["fsuiteflag"]) > 0;
            resp.Data.IsGiveaway = false;
            resp.Data.Dataorigin = Convert.ToString(productObj["fmainorgid"]).EqualsIgnoreCase(this.Context.TopCompanyId)?"总部":"";
            resp.Data.IsAllowOrders = orderservice.CheckNoOrders(this.Context, new NoOrderParmas
            {
                fisgiveaway = false,
                fmainorgid=Convert.ToString(productObj["fmainorgid"]),
                fissuit= Convert.ToInt32(productObj["fsuiteflag"]) > 0,
                fprice= price.SalPrice,
                funstdtype= Convert.ToInt32(productObj["funstdtype"]) > 0
            });
            //设置响应数据包
            resp.Message = "取数成功！";
            resp.Success = true;
            if (!PriceMsg.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = PriceMsg;
                resp.IsShowMessage = true;
            }
            return resp;
        }
    }
}