using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品列表取数接口
    /// </summary>
    public class ProductListController : BaseController
    {
        /// <summary>
        /// 是否启用耗时日志记录
        /// </summary>
        private bool EnableTimeConsumingLog { get; set; }

        /// <summary>
        /// 计时器
        /// </summary>
        private Stopwatch Stopwatch { get; set; }

        /// <summary>
        /// 当前经销商是否启用经销价
        /// </summary>
        private bool IsEnableSellPrice { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<ProductListModel>>
            {
                Data = new BaseListPageData<ProductListModel>(dto.PageSize)
            };

            this.EnableTimeConsumingLog = "".GetAppConfig<bool>("enableTimeConsumingLog");
            if (this.EnableTimeConsumingLog)
            {
                Stopwatch = new Stopwatch();
                this.Stopwatch.Start();
            }

            //暂时屏蔽取价逻辑
            //this.IsEnableSellPrice = this.Context.IsEnableSellPrice();

            var totalRecord = 0;
            var list = new List<ProductListModel>();
            GetProductListData(dto, out list, out totalRecord);

            //设置响应数据包
            resp.Data.List = list;
            resp.Data.TotalRecord = totalRecord;
            resp.Message = "取数成功！";
            resp.Success = true;
            return resp;
        }

        /// <summary>
        /// 获取商品图册分页数据
        /// 作者：zpf
        /// 日期：2022-04-18
        /// </summary>
        /// <param name="dto">请求参数</param>
        /// <param name="list">商品信息集合</param>
        /// <param name="totalRecord">总记录数</param>
        private void GetProductListData(ProductListDTO dto, out List<ProductListModel> list, out int totalRecord)
        {
            list = new List<ProductListModel>();

            //参数对象
            var param = new SqlBuilderParameter(this.Context, "ydj_product");
            param.ReadBillCount = false;
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;
            param.SrcFormId = dto.SrcFormId == "" ? "ydj_product" : dto.SrcFormId;
            param.SrcFldId = "fproductid";
            param.SrcPara.Add("billtypeName", dto.BillTypeName);
            param.SrcPara.Add("billtypeNo", dto.BillTypeNo);
            param.SrcPara.Add("deptName", dto.DeptName);
            param.SrcPara.Add("deptNo", dto.DeptNo);
            param.SrcPara.Add("deptId", dto.DeptId);

            var fieldKeys = new string[]
            {
                "id", "fname", "fnumber", "fimage", "fsuiteflag",
                //取价用到的逻辑暂时注释掉
                //"fselcategoryid","funstdtype", "fispresetprop"
            };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(this.Context);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }

            //系列过滤条件
            if (!dto.SeriesId.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(" fseriesid=@fseriesid ");
                param.AddParameter(new SqlParam("@fseriesid", DbType.String, dto.SeriesId));
            }

            //关键字过滤条件
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                param.AppendFilterString(@" 
                (
                    t0.fname like @keyword or t0.fnumber like @keyword or 
                    exists (select top 1 1 from t_ydj_series s with(nolock) where s.fid=t0.fseriesid and s.fname like @keyword) or
                    exists (select top 1 1 from t_ydj_brand b with(nolock) where b.fid=t0.fbrandid and b.fname like @keyword)
                )");
                param.AddParameter(new SqlParam("@keyword", DbType.String, $"%{dto.Keyword}%"));
            }

            //品类过滤条件
            if (!dto.CategoryId.IsNullOrEmptyOrWhiteSpace())
            {
                var categoryIds = GetCategorysById(dto.CategoryId);
                if (categoryIds.Any())
                {
                    if (categoryIds.Count == 1)
                    {
                        param.AppendFilterString(" t0.fcategoryid='{0}' ".Fmt(categoryIds[0]));
                    }
                    else
                    {
                        param.AppendFilterString(" t0.fcategoryid in ('{0}') ".Fmt(string.Join("','", categoryIds)));
                    }
                }
                this.WriteLog($"小程序商品列表取数接口：加载品类ID {dto.CategoryId} 的所有下级品类ID");
            }

            //多选过滤条件（品牌、风格、空间 等等）
            this.BuildWhereParamNew(param, dto.BrandIds, "fbrandid");
            this.BuildWhereParamNew(param, dto.StyleIds, "fstyle");
            this.BuildWhereParamNew(param, dto.SpaceIds, "fspace");

            //价格区间过滤条件
            //var priceWhere = this.BuildPriceWhereParamNew(dto, param);

            //列表构建器
            var listBuilder = this.Context.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            if (param.HtmlForm.EnableDAC)
            {
                var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
                param.SetFilter(accessFilter);
            }

            this.WriteLog("小程序商品列表取数接口：设置数据隔离方案的过滤条件 GetListAccessControlFilter");

            //查询对象
            var queryObj = listBuilder.GetQueryObject(this.Context, param);

            this.WriteLog("小程序商品列表取数接口：构建查询对象（包括商品授权隔离逻辑） GetQueryObject");

            //默认按最新上架：按创建日期降序
            var orderBy = @"t0.fcreatedate desc ";
            switch (dto.Sortby)
            {
                case "sales": //销量：按销售合同数量汇总降序
                    orderBy = $@"(select sum(oe.fbizqty) from t_ydj_orderentry oe with(nolock) 
		            inner join t_ydj_order o with(nolock) on o.fid=oe.fid 
		            where oe.fproductid=t0.fid and o.fmainorgid='{this.Context.Company}') desc";
                    break;
                case "price": //价格
                    //取价用到的逻辑暂时注释掉
                    //if (this.IsEnableSellPrice)
                    //{
                    //    orderBy = $"(case when isnull(ap.fsalprice,0)>0 then ap.fsalprice else t0.fsalprice end) {dto.Sortord}";
                    //}
                    //else
                    //{
                    //    orderBy = $"t0.fsalprice {dto.Sortord}";
                    //}
                    break;
                case "nts": //最新上架：按创建日期降序
                    orderBy = "t0.fcreatedate desc";
                    break;
            }
            if (orderBy.Length > 0)
            {
                queryObj.SqlOrderBy = $"order by {orderBy}";
            }

            //启用经销商价时，关联经销商价格表取价
            //if (this.IsEnableSellPrice)
            //{
            //    queryObj.SqlSelect += ",isnull(ap.fsalprice,0) as fagentprice";
            //    var fromSql = $"\r\nleft join t_bas_agentprice as ap with(nolock) on ap.fmainorgid='{this.Context.Company}' and ap.fproductid=t0.fid";
            //    queryObj.SqlFrom += fromSql;
            //    queryObj.BillCountSqlFrom += fromSql;
            //    queryObj.AllCountSqlFrom += fromSql;
            //}

            //获取分页数据
            var listDatas = listBuilder.GetQueryData(this.Context, param, queryObj);
            list = MapProductListModels(listDatas);

            this.WriteLog($"小程序商品列表取数接口：加载分页数据 {dto.PageSize} 条，当前是第 {dto.PageIndex} 页", true, queryObj.Sql, param.DynamicParams);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(this.Context, param, queryObj);
            totalRecord = (int)listDesc.Rows;

            this.WriteLog($"小程序商品列表取数接口：加载分页总记录数 {totalRecord} 条", false, $"{queryObj.AllCountSql}", param.DynamicParams);

            //批量加载商品选配类别的默认属性值集
            //var selCategoryIds = list.Select(o => o.SelCategoryId).ToList();
            //var selCategoryAuxPropValKv = ProductUtil.LoadProductAuxPropVals(this.Context, selCategoryIds);

            //this.WriteLog("小程序商品列表取数接口：批量加载商品选配类别的默认属性值集");

            //处理商品价格
            //this.ProcPrice(list, selCategoryAuxPropValKv, dto);

            //this.WriteLog("小程序商品列表取数接口：处理商品价格");
        }

        /// <summary>
        /// 记录耗时日志
        /// </summary>
        private void WriteLog(string logMsg, bool restart = true, string sqlText = "", List<SqlParam> sqlParam = null)
        {
            if (!this.EnableTimeConsumingLog) return;

            this.Stopwatch.Stop();

            var sqlInfo = new StringBuilder();
            if (!sqlText.IsNullOrEmptyOrWhiteSpace())
            {
                sqlInfo.AppendLine();
                sqlInfo.AppendFormat("Sql语句：{0}", sqlText);
            }
            if (sqlParam != null && sqlParam.Any())
            {
                sqlInfo.AppendLine();
                sqlInfo.AppendFormat("Sql参数：{0}", (sqlParam.Select(o => new { name = o.Name, value = o.Value })).ToJson());
            }

            this.LogService.Info($"企业ID：{this.Context.Company}，用户：{this.Context.UserName}，{logMsg} 耗时 {(System.Math.Round((double)this.Stopwatch.ElapsedMilliseconds / 1000, 5))} 秒{sqlInfo}");

            if (restart)
            {
                this.Stopwatch.Restart();
            }
        }

        /// <summary>
        /// 通过品类Id获取下级品类信息
        /// 作者：zpf
        /// 日期：2022-04-18
        /// </summary>
        /// <param name="categoryid">品类Id</param>
        /// <returns></returns>
        public List<string> GetCategorysById(string categoryid)
        {
            var queryCategorySql = $@"/*dialect*/
            with temp
            as
            (
                select fid FPKId,fparentid from ser_ydj_category with(nolock)
                where fid= '{categoryid}'
                union all
                select c.fid,c.fparentid from ser_ydj_category c with(nolock)
                inner join temp on c.fparentid = temp.FPKId
            )
			SELECT FPKId FROM temp";
            var dbService = this.Context.Container.GetService<IDBService>();
            var queryDatas = dbService.ExecuteDynamicObject(this.Context, queryCategorySql, null);
            var categorys = queryDatas.Select(t => Convert.ToString(t["FPKId"])).ToList();
            return categorys;
        }

        /// <summary>
        /// 构建多选过滤条件
        /// </summary>
        private void BuildWhereParamNew(SqlBuilderParameter param, List<string> fieldVals, string fieldName)
        {
            if (fieldVals == null || !fieldVals.Any()) return;

            fieldVals = fieldVals.Where(o => !o.IsNullOrEmptyOrWhiteSpace()).Distinct().ToList();
            if (!fieldVals.Any()) return;

            if (fieldVals.Count == 1)
            {
                param.AppendFilterString($" t0.{fieldName}=@{fieldName}");
                param.AddParameter(new SqlParam($"@{fieldName}", System.Data.DbType.String, fieldVals[0]));
            }
            else
            {
                var paramNames = new List<string>();
                for (int i = 0; i < fieldVals.Count; i++)
                {
                    string paramName = $"@{fieldName}{i}";
                    param.AddParameter(new SqlParam(paramName, System.Data.DbType.String, fieldVals[i]));
                    paramNames.Add(paramName);
                }
                param.AppendFilterString(string.Format(" t0.{0} in ({1})", fieldName, string.Join(",", paramNames)));
            }
        }

        /// <summary>
        /// 构建价格区间过滤条件
        /// </summary>
        private string BuildPriceWhereParamNew(ProductListDTO dto, SqlBuilderParameter param)
        {
            var sqlWhere = new StringBuilder();

            var priceFieldName = "fsalprice";
            if (this.IsEnableSellPrice)
            {
                priceFieldName = "(case when isnull(ap.fsalprice,0)>0 then ap.fsalprice else t0.fsalprice end)";
            }

            if (dto.MinPrice == 0 && dto.MaxPrice > 0)
            {
                param.AppendFilterString($"{priceFieldName}<=@maxprice");
            }
            else if (dto.MinPrice > 0 && dto.MaxPrice == 0)
            {
                param.AppendFilterString($"{priceFieldName}>=@minprice");
            }
            else if (dto.MinPrice > 0 && dto.MaxPrice > 0)
            {
                param.AppendFilterString($"{priceFieldName}>=@minprice and {priceFieldName}<=@maxprice");
            }
            if (dto.MinPrice > 0)
            {
                param.AddParameter(new SqlParam("@minprice", System.Data.DbType.Decimal, dto.MinPrice));
            }
            if (dto.MaxPrice > 0)
            {
                param.AddParameter(new SqlParam("@maxprice", System.Data.DbType.Decimal, dto.MaxPrice));
            }
            if (sqlWhere.Length < 1) return "";

            return " and " + sqlWhere.ToString();
        }

        /// <summary>
        /// 处理商品价格
        /// </summary>
        /// <param name="list"></param>
        private void ProcPrice(
            List<ProductListModel> list,
            Dictionary<string, List<Dictionary<string, string>>> selCategoryAuxPropValKv,
            ProductListDTO dto)
        {
            if (list == null || list.Count == 0) return;

            //需要反写价格的商品
            var backWriteList = new List<ProductPriceModel>();

            //需要反写经销商价格的商品
            var backWriteAgentList = new List<ProductPriceModel>();

            //没有销售价的【标准品】只取价目表，不需要走选配计价公式
            var standardList = new List<ProductListModel>();

            //没有销售价的【标准定制品】取价目表，并且需要走选配计价公式
            var standardCustomList = new List<ProductListModel>();

            //是否是取总部价格
            var isHqPrice = true;

            if (this.IsEnableSellPrice)
            {
                isHqPrice = false;
                standardList = list.Where(o => !o.IsPresetProp && o.AgentPrice == 0).ToList();
                standardCustomList = list.Where(o => o.IsPresetProp && !o.IsNonStandard && o.AgentPrice == 0).ToList();
            }
            else
            {
                standardList = list.Where(o => !o.IsPresetProp && o.SalPrice == 0).ToList();
                standardCustomList = list.Where(o => o.IsPresetProp && !o.IsNonStandard && o.SalPrice == 0).ToList();
            }

            //需要从价目表中取价的商品
            var needPriceList = new List<ProductListModel>();
            needPriceList.AddRange(standardList);
            needPriceList.AddRange(standardCustomList);

            //先从价目表中获取价格
            var products = new List<Dictionary<string, object>>();
            foreach (var model in needPriceList)
            {
                var auxPropVals = selCategoryAuxPropValKv.GetValue(model.SelCategoryId);
                products.Add(new Dictionary<string, object>
                {
                    { "productId", model.Id },
                    { "auxPropValId", "" },
                    { "auxPropVals", auxPropVals },
                    { "orderDate", dto.Orderdate },
                    { "isHqPrice", isHqPrice },
                    { "salPrice", model.SalPrice }
                });
            }
            ProductUtil.WritePriceLog(this.Context, $"products：{JsonConvert.SerializeObject(products)}", 1);
            var prices = ProductUtil.GetPrices(this.Context, products);
            ProductUtil.WritePriceLog(this.Context, $"prices：{JsonConvert.SerializeObject(prices)}", 2);
            foreach (var model in needPriceList)
            {
                var price = prices.FirstOrDefault(s => s.ProductId == model.Id);
                if (price != null)
                {
                    // 返回给前端统一用 SalPrice，可能是总部零售价，也可能是经销商的统一零售价，这取决于取价服务内部的取价规则
                    model.SalPrice = price.SalPrice;
                    model.FromAgent = price.FromAgent;
                    model.IgnorePriceFormula = price.IgnorePriceFormula;

                    if (isHqPrice)
                    {
                        //如果是明确取总部价格，则需要将总部价格反写到商品档案上
                        backWriteList.Add(new ProductPriceModel
                        {
                            ProductId = model.Id,
                            SalPrice = model.SalPrice
                        });
                    }
                    else
                    {
                        //如果不是取总部价，并且取到了经销商自己定义的销售价，这时需要将经销商价格反写到经销商价格表中
                        backWriteAgentList.Add(new ProductPriceModel
                        {
                            FromAgent = model.FromAgent,
                            ProductId = model.Id,
                            SalPrice = model.SalPrice
                        });

                        //如果不是取总部价而是取经销价，但是又没有取到经销价时，取价服务会重新取总部价，这时也需要将总部价格反写到商品档案中
                        backWriteList.Add(new ProductPriceModel
                        {
                            ProductId = model.Id,
                            SalPrice = model.SalPrice
                        });
                    }
                }
            }

            //再将销售价带入到计价公式中计算得出最终的价格
            if (standardCustomList.Any())
            {
                //选配计价服务
                var formulaService = this.Container.GetService<ISelectionPriceService>();

                //批量加载属性实体集合
                var auxPropValsKv = new Dictionary<string, List<Dictionary<string, string>>>();
                foreach (var model in standardCustomList)
                {
                    var auxPropVals = selCategoryAuxPropValKv.GetValue(model.SelCategoryId);
                    auxPropValsKv[model.SelCategoryId] = auxPropVals;
                }
                var propListKv = ProductUtil.LoadPropEntityList(this.Context, auxPropValsKv);

                foreach (var model in standardCustomList)
                {
                    var propList = propListKv.GetValue(model.SelCategoryId);
                    if (propList == null && !propList.Any()) continue;

                    try
                    {
                        /*
                         * 针对计价公式的取价，目前只能循环取，因为涉及到很多计价公式需要在程序中叠加计算，
                         * 而且经过本来取价策略调整后，真正执行到这来的情况会很少，大部分情况下不会执行这里的逻辑，
                         * 偶尔只有个别商品的取价会执行该逻辑，所以这里循环取价对性能影响不大，暂时可以接受。
                         */
                        //if (!model.IgnorePriceFormula)
                        //{
                        //    model.SalPrice = formulaService.GetProductPrice_New(this.Context, model.Id, model.SalPrice, propList);
                        //}
                    }
                    catch (Exception ex)
                    {
                        var message = $"小程序商品列表 - 商品【{model.Id}/{model.Number}/{model.Name}】根据选配计价公式取价出错：{ex.Message}";
                        this.LogService.Error(message, ex);
                    }

                    backWriteAgentList.Add(new ProductPriceModel
                    {
                        FromAgent = model.FromAgent,
                        ProductId = model.Id,
                        SalPrice = model.SalPrice
                    });

                    backWriteList.Add(new ProductPriceModel
                    {
                        ProductId = model.Id,
                        SalPrice = model.SalPrice
                    });
                }
            }

            //如果是非标商品，则销售价直接返回0
            foreach (var model in list)
            {
                if (model.IsNonStandard)
                {
                    model.SalPrice = 0;
                }
            }

            //需要重新根据价格排序，因为上面的逻辑种，有可能会重新计算价目，导致最终返回前端的跟直接SQL查询的结构不一样
            if (dto.Sortby == "price")
            {
                if (dto.Sortord == "asc")
                {
                    list.OrderBy(t => t.SalPrice);
                }
                else
                {
                    list.OrderByDescending(t => t.SalPrice);
                }
            }

            //异步反写商品价格：需要将取到的价格反写到商品档案中，以便小程序在下次访问商品列表接口时可以直接获取商品档案中的销售价
            ProductUtil.BackWriteProductPriceAsync(this.Context, backWriteList);

            //异步反写经销商价格：需要将取到的价格反写到经销商价格表中，以便小程序在下次访问商品列表接口时可以直接获取经销商价格表中的统一零售价
            ProductUtil.BackWriteAgentProductPriceAsync(this.Context, backWriteAgentList);
        }

        /// <summary>
        /// 映射ProductListModel
        /// 作者：zpf
        /// 日期：2022-04-15
        /// </summary>
        /// <returns></returns>
        private List<ProductListModel> MapProductListModels(List<Dictionary<string, object>> lists)
        {
            var list = new List<ProductListModel>();
            foreach (var data in lists)
            {
                //取价用到的逻辑暂时注释掉
                //var fromAgent = false;
                //var agentPrice = 0M;
                //var salPrice = Convert.ToDecimal(data["fsalprice"]);

                //if (this.IsEnableSellPrice)
                //{
                //    // 经销商价格
                //    fromAgent = true;
                //    agentPrice = Convert.ToDecimal(data["fagentprice"]);

                //    // 如果经销商价格表中已经有现成的价格，则直接返回，无需重复取价
                //    if (agentPrice > 0) salPrice = agentPrice;
                //}

                // 商品图片
                var image = Convert.ToString(data["fimage"]);
                var imageTxt = Convert.ToString(data["fimage_txt"]);
                var images = ImageFieldUtil.ParseImages(image, imageTxt, true);

                list.Add(new ProductListModel
                {
                    Id = Convert.ToString(data["fbillhead_id"]),
                    Number = Convert.ToString(data["fnumber"]),
                    Name = Convert.ToString(data["fname"]),
                    IsSuite = Convert.ToString(data["fsuiteflag"]) == "1",
                    ImageList = images,

                    //取价用到的逻辑暂时注释掉
                    //SalPrice = salPrice,
                    //FromAgent = fromAgent,
                    //AgentPrice = agentPrice,
                    //SelCategoryId = Convert.ToString(data["fselcategoryid"]),
                    //IsNonStandard = Convert.ToString(data["funstdtype"]) == "1",
                    //IsPresetProp = Convert.ToString(data["fispresetprop"]) == "1"
                });
            }
            return list;
        }
    }
}