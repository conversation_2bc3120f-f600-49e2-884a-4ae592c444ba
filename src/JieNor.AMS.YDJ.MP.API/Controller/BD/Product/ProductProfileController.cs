using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;
using JieNor.Framework.DataTransferObject;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品属性取数接口
    /// </summary>
    public class ProductProfileController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductProfileDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ProductProfileModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //根据唯一标识获取数据
            var productObj = this.Context.LoadBizBillHeadDataById("ydj_product", dto.Id, "fsalunitid,fimage,fimage_txt,fmainorgid,funstdtype,fissuit");
            if (productObj == null)
            {
                resp.Message = "商品不存在或已被删除！";
                resp.Success = false;
                return resp;
            }

            var auxPropVals = this.ConvertToAuxPropVals(dto.PropList);
            resp.Data.ImageList = ProductUtil.GetImages(this.Context, productObj, auxPropVals, dto.CustomDesc);

            var productId = Convert.ToString(productObj["id"]);
            var salPrice = 0M;
            var hqSalPrice = 0M;
            var PriceMsg = string.Empty;
            JObject priceParam = new JObject() 
            { 
                { "isresellorder" , dto.isresellorder },
                { "customerid", dto.customerid },
                { "sourcenumber" , dto.sourcenumber }
            };
            //如果是非标，则不需要取价格，不是非标才需要取价格
            //如果库存查询带出的商品，不管是否非标都需要走正常取价
            if (!dto.IsNonStandard || dto.IsFromInventory)
            {
                //如果是在库商品 自动勾选出现货
                if (dto.IsFromInventory) {
                    dto.Isoutspot = true;
                }
                
               //获取零售价
               var priceInfo = ProductUtil.GetPrices(this.Context, productId, dto.OrderDate, false, auxPropVals, true, dto.OrderDate, dto.IsFromInventory, priceParam, dto.Isoutspot);
                salPrice = priceInfo.SalPrice;

                //获取总部零售价
                hqSalPrice = ProductUtil.GetPrices(this.Context, productId, dto.OrderDate, true, auxPropVals, Isoutspot: dto.Isoutspot, priceParam: priceParam).SalPrice;

                PriceMsg = priceInfo.PriceMsg;
                //选配计价服务
                //var propSelService = this.Container.GetService<ISelectionPriceService>();
                //if (!priceInfo.IgnorePriceFormula)
                //{
                //    salPrice = propSelService.GetProductPrice_New(this.Context, productId, salPrice, dto.PropList);
                //}
                //hqSalPrice = propSelService.GetProductPrice_New(this.Context, productId, hqSalPrice, dto.PropList);

                resp.Data.SalPrice = salPrice;
                resp.Data.HqSalPrice = hqSalPrice;
                if (!priceInfo.fpromotionid.IsNullOrEmptyOrWhiteSpace())
                {
                    var promotion = this.Context.LoadBizBillHeadDataById("ydj_productpromotion", priceInfo.fpromotionid,
                        "fnumber,fname");

                    resp.Data.fpromotionid = new JObject()
                    {
                        { "id", priceInfo.fpromotionid },
                        { "fnumber", promotion?["fnumber"]?.ToString() },
                        { "fname", promotion?["fname"]?.ToString() },
                    };
                    resp.Data.fpromotionrule = priceInfo.fpromotionrule;
                    resp.Data.fpromotionsalprice = priceInfo.fpromotionsalprice;
                    resp.Data.fpromotionlowestprice = priceInfo.fpromotionlowestprice;
                }
            }

            var orderservice = this.Context.Container.GetService<IOrderService>();
            resp.Data.isAllowOrders = orderservice.CheckNoOrders(this.Context, new NoOrderParmas
            {
                fisgiveaway = false,
                fissuit = Convert.ToString(productObj["fissuit"]) == "1" ? true : false,
                fmainorgid = Convert.ToString(productObj["fmainorgid"]),
                fprice = resp.Data.SalPrice,
                funstdtype = Convert.ToString(productObj["funstdtype"]) == "1" ? true : false,
            });

            //设置响应数据包
            resp.Message = "取数成功！";
            resp.Success = true;
            if (!PriceMsg.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = PriceMsg;
                resp.IsShowMessage = true;
            }
            return resp;
        }

        private List<Dictionary<string, string>> ConvertToAuxPropVals(List<PropEntity> propList)
        {
            var auxPropVals = new List<Dictionary<string, string>>();
            if (propList != null)
            {
                foreach (var item in propList)
                {
                    auxPropVals.Add(new Dictionary<string, string>
                    {
                        { "auxPropId", item.PropId },
                        { "valueId", item.ValueId }
                    });
                }
            }
            return auxPropVals;
        }
    }
}
