using JieNor.AMS.YDJ.MP.API.DTO.BD.Staff;
using JieNor.AMS.YDJ.MP.API.Model.BD.Staff;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.MetaCore.FormMeta;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Staff
{
    /// <summary>
    /// 员工管理-获取员工跟进中客户列表
    /// </summary>
    public class StaffFollowUpCustomerController : BaseStaffController
    {
        public HtmlForm CustomerForm;

        public object Any(StaffFollowUpCustomerDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseListPageData<StaffFollowUpCustomerModel>>()
            {
                Data = new BaseListPageData<StaffFollowUpCustomerModel>()
            };

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            CustomerForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_customer");

            string sqlWhere = BuildSqlWhere("ydj_customer", dto.Id);

            resp.Message = "取数成功！";
            resp.Success = true;
            resp.Data.TotalRecord = GetTotalRecord(sqlWhere);
            resp.Data.List = MapTo(dto, sqlWhere);
            return resp;
        }

        private int GetTotalRecord(string sqlWhere)
        {
            //查询总记录数
            var totalRecord = 0;
            string sql = $@"select count(1) as totalRecord from t_ydj_customer with(nolock) {sqlWhere}";
            using (var reader = this.DBService.ExecuteReader(this.Context, sql))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }

            return totalRecord;
        }

        private List<StaffFollowUpCustomerModel> MapTo(StaffFollowUpCustomerDTO dto, string sqlWhere)
        {
            //默认fid降序
            var orderBy = "t_ydj_customer.fid desc";
            int pageSize = dto.PageSize;
            int pageIndex = dto.PageIndex;

            //查询分页数据
            var sqlText = $@"
            select top {pageSize} * from 
            (
	            select row_number() over(order by {orderBy}) rownum,
                    t_ydj_customer.fid,
                    t_ydj_customer.fname,
	                t_ydj_customer.fphone, 
	                t_ydj_customer.fcreatedate, 
	                t_ydj_customer.fsource,
                    s.fenumitem as fsourcename,
	                t_ydj_customer.fcusnature
                from t_ydj_customer with(nolock)
                left join v_bd_enum s with(nolock) on t_ydj_customer.fsource = s.fid
                {sqlWhere}
            ) p 
            where rownum > {pageSize} * ({pageIndex} - 1)";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText);

            var models = new List<StaffFollowUpCustomerModel>();
            foreach (var item in list)
            {
                var model = new StaffFollowUpCustomerModel();
                model.Id = Convert.ToString(item["fid"]);
                model.Name = Convert.ToString(item["fname"]);
                model.Phone = Convert.ToString(item["fphone"]);
                model.CreateTime = Convert.ToDateTime(item["fcreatedate"]);
                model.StaffId = dto.Id;
                model.Source = Convert.ToString(item["fsourcename"]);

                var cusnature = Convert.ToString(item["fcusnature"]);
                CustomerForm.GetSimpleSelectItemText("fcusnature", cusnature);

                models.Add(model);
            }

            return models;
        }
    }
}
