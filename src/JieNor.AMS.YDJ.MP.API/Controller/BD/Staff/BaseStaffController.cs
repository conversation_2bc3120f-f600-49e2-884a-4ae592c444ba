using JieNor.Framework.CustomException;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Staff
{
    public class BaseStaffController : BaseController
    { 
        /// <summary>
        /// 构建查询条件
        /// </summary>
        /// <param name="formId"></param>
        /// <param name="dutyId"></param>
        /// <returns></returns>
        protected string BuildSqlWhere(string formId, string dutyId)
        {
            string sqlWhere = string.Empty;

            switch (formId?.ToLowerInvariant())
            {
                case "ydj_order":
                    sqlWhere = $@" where t_ydj_order.fmainorgid='{this.Context.Company}' and t_ydj_order.fstaffid = '{dutyId}' and t_ydj_order.fcancelstatus = '0' and (t_ydj_order.fstatus != 'E' or t_ydj_order.freceiptstatus != 'receiptstatus_type_03') ";
                    break;
                case "ydj_saleintention":
                    sqlWhere = $@" where t_ydj_saleintention.fmainorgid='{this.Context.Company}' and t_ydj_saleintention.fstaffid='{dutyId}' and t_ydj_saleintention.fcancelstatus='0' and t_ydj_saleintention.fispushorder='0' ";//作废&已下推的不参与统计
                    break;
                case "ydj_customerrecord":
                    sqlWhere = $@" where t_ydj_customerrecord.fmainorgid='{this.Context.Company}' and (t_ydj_customerrecord.fdutyid='{dutyId}' or t_ydj_customerrecord.fid in (select fid from t_ydj_customerrecordduty where fdutyid='{dutyId}')) and t_ydj_customerrecord.forderno=''  "; //销售合同编号为空，则为进行中的商机
                    break;
                case "ydj_customer":
                    // 查询负责客户、正在进行中的商机、意向、合同的客户总数
                    sqlWhere = $@" where t_ydj_customer.fmainorgid='{this.Context.Company}' and t_ydj_customer.fforbidstatus=0 
and t_ydj_customer.fid in (
    select fid from t_ydj_customerdutyentry with(nolock) where fdutyid='{dutyId}'
    union
    select fcustomerid from t_ydj_customerrecord with(nolock)  {BuildSqlWhere("ydj_customerrecord", dutyId)}
    union
    select fcustomerid from t_ydj_saleintention with(nolock)  {BuildSqlWhere("ydj_saleintention", dutyId)}
    union 
    select fcustomerid from t_ydj_order with(nolock) {BuildSqlWhere("ydj_order", dutyId)}
) ";
                    break;
                default:
                    throw new BusinessException($"表单标识{formId}没实现！");
            }

            return sqlWhere;
        }
    }
}
