using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.MP.API.DTO.BD.Dept;
using JieNor.AMS.YDJ.MP.API.Model.BD.Dept;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.Interface.QueryBuilder;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Dept
{
    /// <summary>
    /// 微信小程序：部门列表接口
    /// </summary>
    public class DeptListController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(DeptListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<DeptListModel>>
            {
                Data = new BaseListPageData<DeptListModel>(dto.PageSize)
            };

            //resp.Message = "操作成功！";
            //resp.Success = true;
            //resp.Data.TotalRecord = GetTotalRecord(dto);
            //resp.Data.List = MapTo(dto);

            GetDeptDatasByPage(this.Context, dto, resp);

            return resp;
        }

        private int GetTotalRecord(DeptListDTO dto)
        {
            //查询总记录数
            var totalRecord = 0;

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var sqlWhere = new StringBuilder();
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                sqlWhere.Append(" and (fname like @keyword)");
                sqlParams.Add(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            string sql =
                $@"select count(1) as totalRecord from t_bd_department with(nolock)
                where fmainorgid=@fmainorgid {sqlWhere}";
            using (var reader = this.DBService.ExecuteReader(this.Context, sql, sqlParams))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }

            return totalRecord;
        }

        private List<DeptListModel> MapTo(DeptListDTO dto)
        {
            int pageIndex = dto.PageIndex < 1 ? 1 : dto.PageIndex;
            var pageSize = dto.PageSize < 1 ? 10 : dto.PageSize;

            //默认按创建日期降序
            var orderBy = "dept.fcreatedate asc";

            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var sqlWhere = new StringBuilder();
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                sqlWhere.Append(" and (dept.fname like @keyword )");
                sqlParams.Add(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            //查询分页数据
            var sqlText = $@"
            select top {pageSize} * from 
            (
	            select row_number() over(order by {orderBy}) rownum,
	                    fid,fnumber,fname
                    from t_bd_department dept with(nolock)
                where dept.fmainorgid=@fmainorgid {sqlWhere}
            ) p 
            where rownum > {pageSize} * ({pageIndex} - 1)";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            var models = new List<DeptListModel>();
            foreach (var item in list)
            {
                models.Add(new DeptListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fid"]),
                    Number = JNConvert.ToStringAndTrim(item["fnumber"]),
                    Name = JNConvert.ToStringAndTrim(item["fname"])
                });
            }

            return models;
        }

        /// <summary>
        /// 分页获取部门信息
        /// 作者：zpf
        /// 日期：2021-04-01
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="dto">请求参数</param>
        /// <param name="resp">返回对象</param>
        /// <returns></returns>
        public void GetDeptDatasByPage(UserContext userCtx, DeptListDTO dto, BaseResponse<BaseListPageData<DeptListModel>> resp)
        {
            var dataSource = new Dictionary<string, List<Dictionary<string, object>>>();
            //参数对象
            SqlBuilderParameter param = new SqlBuilderParameter(userCtx, "ydj_dept");
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.PageCount = dto.PageSize;
            param.PageIndex = dto.PageIndex;
            param.SrcFormId = dto.SrcFormId;
            param.SrcFormId = dto.SrcFidId.IsNullOrEmptyOrWhiteSpace()?"fdeptid":dto.SrcFidId;

            //默认按创建日期降序
            param.OrderByString = " fcreatedate asc ";

            //过滤条件
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                //param.AppendFilterString(" and (staff.fname like @keyword or staff.fphone like @keyword)");
                param.AppendFilterString(" (fname like @keyword ) ");
                param.AddParameter(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            //当前要查询的字段列表
            var fieldKeys = new string[] { "id", "fnumber", "fname", "fsalecategories" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(userCtx);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }

            //列表构建器
            var listBuilder = userCtx.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(userCtx, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            //查询对象
            var queryObj = listBuilder.GetQueryObject(userCtx, param);

            //获取分页数据
            var listDatas = listBuilder.GetQueryData(userCtx, param, queryObj);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(userCtx, param, queryObj);

            var models = new List<DeptListModel>();
            foreach (var item in listDatas)
            {
                models.Add(new DeptListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fbillhead_id"]),
                    Number = JNConvert.ToStringAndTrim(item["fnumber"]),
                    Name = JNConvert.ToStringAndTrim(item["fname"])
                });
            }

            //设置响应数据包
            resp.Data.List = models;
            resp.Data.TotalRecord = (int)listDesc.Rows;
            resp.Message = "取数成功！";
            resp.Success = true;
        }        
    }
}