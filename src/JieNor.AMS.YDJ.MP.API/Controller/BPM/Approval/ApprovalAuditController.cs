using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.Controller.BPM.Approval
{
    /// <summary>
    /// 微信小程序：审批同意接口
    /// </summary>
    public class ApprovalAuditController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ApprovalAuditDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.SourceId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceId 不能为空！";
                return resp;
            }

            if (dto.SourceType.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 sourceType 不能为空！";
                return resp;
            }

            string execOpinion = dto.Reason ?? "同意";

            //// 向麦浩系统发送请求
            //var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
            //    this.Request,
            //    new CommonBillDTO()
            //    {
            //        FormId = dto.SourceType,
            //        OperationNo = "auditflow",
            //        SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.SourceId } },
            //        SimpleData = new Dictionary<string, string> { { "execOpinion", execOpinion } }
            //    });
            //var result = response?.OperationResult;
            //resp = result.ToResponseModel<BaseDataModel>(false);  

            resp = ApprovalHelper.Audit(this.Context, this.Request, dto.SourceType, dto.SourceId, execOpinion);

            return resp;
        }
    }
}