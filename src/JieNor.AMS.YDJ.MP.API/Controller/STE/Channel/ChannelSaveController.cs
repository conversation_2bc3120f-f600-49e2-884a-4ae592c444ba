using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Channel
{
    /// <summary>
    /// 微信小程序：合作渠道保存接口
    /// </summary>
    public class ChannelSaveController : BaseController
    {
        public string curstaffid { get; set; }

        public string curdeptid { get; set; }

        public HtmlForm ChannelForm;
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ChannelSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();
            List<Dictionary<string, object>> fdutyentry = new List<Dictionary<string, object>>();
            curstaffid = this.Context.GetCurrentStaffId();
            curdeptid = this.Context.GetCurrentDeptId();
            this.ChannelForm = this.MetaModelService.LoadFormModel(this.Context, "ste_channel");
            //如果只是编辑负责人
            if (dto.IsEditStaffList == 1)
            {
                if (dto.Id.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Success = false;
                    resp.Message = $"参数 id 不能为空！";
                    return resp;
                }
                var esresponse = EditStaffList(dto);
                var esresult = esresponse?.OperationResult;
                resp = esresult.ToResponseModel<BaseDataModel>();
                return resp;
            }

            //如果类型是‘个人’，是新增业务,判断是否存在该渠道，是则返回code：1001，代表前端需要引导是否领用
            if (dto.TypeId.ToLower().Equals("channel_type_01") && dto.IsClaim == 0 && dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                //参数对象
                var param = new SqlBuilderParameter(this.Context, this.ChannelForm);
                param.ReadDirty = true;
                param.NoColorSetting = true;
                param.PageCount = 10;
                param.PageIndex = 1;
                if (dto.Phone.IsNullOrEmptyOrWhiteSpace() == false)
                {
                    //过滤条件
                    param.FilterString = param.FilterString.JoinFilterString(@"fphone = @fphone");

                    //条件参数
                    param.AddParameter(new SqlParam("@fphone", System.Data.DbType.String, dto.Phone));
                }


                //当前要查询的字段列表
                var fieldKeys = new string[] { "fname", "ftype" };
                foreach (var fieldKey in fieldKeys)
                {
                    var field = param.HtmlForm.GetField(fieldKey);
                    var columnList = field.ToListColumn(this.Context);
                    foreach (var column in columnList)
                    {
                        param.SelectedFieldKeys.Add(column.Id);
                    }
                }

                //列表构建器
                var listBuilder = this.Container.GetService<IListSqlBuilder>();

                //设置数据隔离方案的过滤条件
                var accessFilter = listBuilder.GetListAccessControlFilter(this.Context, param.HtmlForm.Id);
                param.SetFilter(accessFilter);

                //查询对象
                var queryObj = listBuilder.GetQueryObject(this.Context, param);

                //获取分页数据
                var listData = listBuilder.GetQueryData(this.Context, param, queryObj);

                if (listData != null && listData.Any())
                {
                    resp.Success = true;
                    resp.Data.Id = Convert.ToString(listData[0]["fbillhead_id"]);
                    resp.Message = "已存在该渠道伙伴！";
                    resp.Code = 1001;//已存在，且需要判断是否领用，返回该码
                    return resp;
                }
                Dictionary<string, object> modelnew = new Dictionary<string, object>();
                modelnew.Add("fdutyid", curstaffid);
                modelnew.Add("fdeptid", curdeptid);
                fdutyentry.Add(modelnew);
            }

            //如果类型是个人并且是领用，则在已有的渠道增加负责人和部门
            if (dto.TypeId.ToLower().Equals("channel_type_01") && dto.IsClaim == 1)
            {
                if (dto.Id.IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Success = false;
                    resp.Message = $"参数 id 不能为空！";
                    return resp;
                }
                var obj = this.ChannelForm.GetBizDataById(this.Context, dto.Id, true);
                EntryToDto(ref dto, obj);

                var fdutyentryObjs = obj["fdutyentry"] as DynamicObjectCollection;
                if (fdutyentryObjs != null && fdutyentryObjs.Any())
                {
                    var hasobj = fdutyentryObjs.ToList().Exists(a => Convert.ToString(a["id"]) == curstaffid //如果存在此负责人
                     );
                    if (hasobj)
                    {
                        resp.Code = 1002;//重复领用
                        resp.Success = true;
                        resp.Message = $"已存在该负责人，无需领用！";
                        return resp;
                    }
                    for (int i = 0; i < fdutyentryObjs.Count; i++)
                    {
                        var fdutyentryObj = fdutyentryObjs[i];
                        var id = Convert.ToString(fdutyentryObj["id"]);
                        var fdutyid = Convert.ToString(fdutyentryObj["fdutyid"]);
                        var fdeptid = Convert.ToString(fdutyentryObj["fdeptid"]);
                        var fjoindate = fdutyentryObj["fjoindate"] == null ? BeiJingTime.Now : Convert.ToDateTime(fdutyentryObj["fjoindate"]);
                        var fnote = Convert.ToString(fdutyentryObj["fnote"]);
                        Dictionary<string, object> modelold = new Dictionary<string, object>();
                        modelold.Add("id", id);
                        modelold.Add("fdutyid", fdutyid);
                        modelold.Add("fdeptid", fdeptid);
                        modelold.Add("fjoindate", fjoindate);
                        modelold.Add("fnote", fnote);
                        fdutyentry.Add(modelold);
                    }
                    if (!curstaffid.IsNullOrEmptyOrWhiteSpace() && !curdeptid.IsNullOrEmptyOrWhiteSpace())
                    {
                        Dictionary<string, object> modelnew = new Dictionary<string, object>();
                        modelnew.Add("fdutyid", curstaffid);
                        modelnew.Add("fdeptid", curdeptid);
                        modelnew.Add("fjoindate", BeiJingTime.Now);
                        modelnew.Add("fnote", "");
                        fdutyentry.Add(modelnew);
                    }
                }
                else
                {
                    if (!curstaffid.IsNullOrEmptyOrWhiteSpace() && !curdeptid.IsNullOrEmptyOrWhiteSpace())
                    {
                        Dictionary<string, object> modelnew = new Dictionary<string, object>();
                        modelnew.Add("fdutyid", curstaffid);
                        modelnew.Add("fdeptid", curdeptid);
                        modelnew.Add("fjoindate", BeiJingTime.Now);
                        modelnew.Add("fnote", "");
                        fdutyentry.Add(modelnew);
                    }
                }
            }

            //如果类型是公司，直接新增
            if (dto.TypeId.ToLower().Equals("channel_type_02") && dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                Dictionary<string, object> modelnew = new Dictionary<string, object>();
                modelnew.Add("fdutyid", curstaffid);
                modelnew.Add("fdeptid", curdeptid);
                modelnew.Add("fjoindate", BeiJingTime.Now);
                modelnew.Add("fnote", "");
                fdutyentry.Add(modelnew);
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ste_channel",
                    OperationNo = "save",
                    BillData = BuildBillData(dto, fdutyentry),
                    Id = dto.Id
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<BaseDataModel>();
            return resp;
        }

        private string BuildBillData(ChannelSaveDTO dto, List<Dictionary<string, object>> fdutyentry = null)
        {
            var data =
                new Dictionary<string, object>
                {
                    { "id", dto.Id },
                    { "fdescription", dto.Description },
                    { "fname", dto.Name },
                    { "fphone", dto.Phone },
                    { "ftype", dto.TypeId },
                    { "fcooperation", dto.CooperationId },
                    { "fcompany", dto.Company },

                    { "fprovince", dto.ProvinceId },
                    { "fcity", dto.CityId },
                    { "fregion", dto.RegionId },
                    { "faddress", dto.Address },

                    // 收款信息
                    { "faccountname", dto.AccountName },
                    { "fbank", dto.Bank },
                    { "fbanknumber", dto.BankNumber },
                    { "fbankcode", dto.BankCode },
                    { "fidcard", dto.Idcard },
                    { "fsuppliercode", dto.Suppliercode },
                    { "fchanneltype", dto.ChannelType },

                    { "fcontacts", dto.Contact },

                    //证件
                    {"fmulimage", ImageFieldUtil.ConvertImage(dto.MulImages) },
                    {"fmulimage_txt", ImageFieldUtil.ConvertImageTxt(dto.MulImages) }

                };
            if (fdutyentry != null && fdutyentry.Any())
            {
                data.Add("fdutyentry", fdutyentry);
            }
            else
            {
                fdutyentry = new List<Dictionary<string, object>>();
                Dictionary<string, object> modelnew = new Dictionary<string, object>();
                modelnew.Add("fdutyid", curstaffid);
                modelnew.Add("fdeptid", curdeptid);
                modelnew.Add("fjoindate", BeiJingTime.Now);
                modelnew.Add("fnote", "");
                fdutyentry.Add(modelnew);
                data.Add("fdutyentry", fdutyentry);
            }

            #region 设置抽佣比例
            SetChannelEntryInfo(dto, data);
            #endregion
            
            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }

        /// <summary>
        /// 实体转dto
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="obj"></param>
        public void EntryToDto(ref ChannelSaveDTO dto, DynamicObject obj)
        {
            dto = new ChannelSaveDTO();
            dto.Name = Convert.ToString(obj["fname"]);
            dto.Id = Convert.ToString(obj["id"]);
            dto.TypeId = Convert.ToString(obj["ftype"]);
            dto.Phone = Convert.ToString(obj["fphone"]);
            dto.Contact = Convert.ToString(obj["fcontacts"]);
            dto.Description = Convert.ToString(obj["fdescription"]);
            dto.AccountName = Convert.ToString(obj["faccountname"]);
            dto.Bank = Convert.ToString(obj["fbank"]);
            dto.BankNumber = Convert.ToString(obj["fbanknumber"]);
            dto.BankCode = JNConvert.ToStringAndTrim(obj["fbankcode"]);
            dto.Idcard = JNConvert.ToStringAndTrim(obj["fidcard"]);
            dto.Suppliercode = JNConvert.ToStringAndTrim(obj["fsuppliercode"]);
            dto.ChannelType = JNConvert.ToStringAndTrim(obj["fchanneltype"]);

            dto.Address = Convert.ToString(obj["faddress"]);
            dto.RegionId = Convert.ToString(obj["fregion"]);
            dto.CityId = Convert.ToString(obj["fcity"]);
            dto.ProvinceId = Convert.ToString(obj["fprovince"]);
            dto.RegionId = Convert.ToString(obj["fcity"]);
            dto.CooperationId = Convert.ToString(obj["fcooperation"]);
            dto.Company = Convert.ToString(obj["fcompany"]);
        }

        /// <summary>
        /// 编辑负责人列表
        /// </summary>
        /// <param name="dto"></param>
        public DynamicDTOResponse EditStaffList(ChannelSaveDTO dto)
        {
            List<Dictionary<string, object>> fdutyentry = new List<Dictionary<string, object>>();
            var obj = this.ChannelForm.GetBizDataById(this.Context, dto.Id, true);
            var StaffList = dto.StaffList;
            EntryToDto(ref dto, obj);
            var fdutyentryObjs = obj["fdutyentry"] as DynamicObjectCollection;
            for (int i = 0; i < StaffList.Count; i++)
            {
                var oldobj = CheckObject(fdutyentryObjs, StaffList[i].Id);
                Dictionary<string, object> modelold = new Dictionary<string, object>();
                if (oldobj != null)
                {
                    modelold.Add("id", oldobj["id"]);
                    modelold.Add("fdutyid", oldobj["fdutyid"]);
                    modelold.Add("fdeptid", oldobj["fdeptid"]);
                    modelold.Add("fjoindate", oldobj["fjoindate"]);
                    modelold.Add("fnote", oldobj["fnote"]);
                    fdutyentry.Add(modelold);
                }
                else
                {
                    modelold.Add("fdutyid", StaffList[i].Duty.Id);
                    modelold.Add("fdeptid", StaffList[i].Dept.Id);
                    modelold.Add("fjoindate", DateTime.Now);
                    fdutyentry.Add(modelold);
                }
            }
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ste_channel",
                    OperationNo = "save",
                    BillData = BuildBillData(dto, fdutyentry),
                    Id = dto.Id
                });
            return response;
        }

        /// <summary>
        /// 查询已有负责人
        /// </summary>
        /// <param name="objs"></param>
        /// <param name="staffid"></param>
        /// <returns></returns>
        public DynamicObject CheckObject(DynamicObjectCollection objs, string staffid)
        {
            foreach (var item in objs)
            {
                if (Convert.ToString(item["fdutyid"]) == staffid)
                {

                    return item;
                }
            }
            return null;
        }

        /// <summary>
        /// 设置抽佣比例
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="data"></param>
        public void SetChannelEntryInfo(ChannelSaveDTO dto,Dictionary<string, object> data)
        {
            var channelEntryList = dto.ChannelEntryList;
            if(channelEntryList != null && channelEntryList.Any())
            {
                var entryList = new List<Dictionary<string, object>>();
                foreach (var item in channelEntryList)
                {
                    Dictionary<string, object> model = new Dictionary<string, object>();
                    model.Add("id", item.Id);
                    model.Add("flowerlimit", item.LowerLimitAmount);
                    model.Add("fratio", item.Ratio);
                    model.Add("fupperlimit", item.UpperLimitAmount);
                    entryList.Add(model);
                }
                data.Add("fentry", entryList);
            }
        }
    }
}