using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Controller.BPM.Approval;
using JieNor.AMS.YDJ.MP.API.Controller.SAL.CustomerRecord;
using JieNor.AMS.YDJ.MP.API.Controller.STE.Order;
using ServiceStack;
using Newtonsoft.Json;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.PermData;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    /// <summary>
    /// 微信小程序：首页服务待办及本月服务数接口
    /// </summary>
    public class DashboardServiceController : BaseController
    {
        public string MyDeptId { get; set; }

        public string MyStaffId { get; set; }
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(DashboardServiceDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<DashboardServiceModel>();
            //是否有App查看权限
            if (this.Context.HasPermission("dashboard_service", "mobile_view"))
            {
                resp.Message = "操作成功！";
                resp.Success = true;

                resp.Data.serPending.Add(new BaseCountModel
                {
                    Id = "service_sersta02",
                    Name = "待预约",
                    Count = ServiceListController.DoExecute(this.Context, this.Request, new ServiceListDTO { PageSize = 1, PageIndex = 1, status = "2", cancelStatus = "0" }).Data.TotalRecord
                });
                resp.Data.serPending.Add(new BaseCountModel
                {
                    Id = "service_sersta03",
                    Name = "待完工",
                    Count = ServiceListController.DoExecute(this.Context, this.Request, new ServiceListDTO { PageSize = 1, PageIndex = 1, status = "3", cancelStatus = "0" }).Data.TotalRecord
                });
                resp.Data.serPending.Add(new BaseCountModel
                {
                    Id = "service_sersta04",
                    Name = "待评价",
                    Count = ServiceListController.DoExecute(this.Context, this.Request, new ServiceListDTO { PageSize = 1, PageIndex = 1, status = "4", cancelStatus = "0" }).Data.TotalRecord
                });
                DynamicObject data = null;
                DynamicObject bMonthData = null;
                string[] peritem = new string[] { "myself", "mydepartment", "mycompany" };
                if (peritem.Contains(dto.dataPerm))
                {
                    // 当前员工和部门
                    var baseFormProvider = this.Container.GetService<IBaseFormProvider>();
                    MyDeptId = baseFormProvider.GetMyDepartment(this.Context)?.Id;
                    MyStaffId = baseFormProvider.GetMyStaff(this.Context)?.Id;
                    var subDeptIds = new List<string>();
                    var deptId = string.Empty;
                    var staffId = string.Empty;
                    var strWhere = string.Empty;
                    CheckMyDataPerms2(this.Context, dto.dataPerm, out subDeptIds, out staffId, out deptId);
                    if (subDeptIds.Count > 0)
                    {
                        deptId = subDeptIds.Count == 1 ? subDeptIds.First() : string.Join(",", subDeptIds.Select(x => $"'{x}'"));
                    }
                    if (!string.IsNullOrWhiteSpace(deptId))
                    {
                        if (deptId.Contains(","))
                        {
                            strWhere = $" and fteamid in ({deptId}) ";
                        }
                        else
                        {
                            strWhere = $" and fteamid='{deptId}' ";
                        }
                    }
                    if (!string.IsNullOrWhiteSpace(staffId))
                    {
                        strWhere = $" and fmasterid='{staffId}'";
                    }
                    if (!dto.serviceType.IsNullOrEmptyOrWhiteSpace())
                    {
                        ///空：全部类型；1：送装，2：增值；3：售后
                        if (dto.serviceType.Equals("1"))
                        {
                            strWhere += $" and fservicetype='fres_type_01'";
                        }
                        if (dto.serviceType.Equals("2"))
                        {
                            strWhere += $" and fservicetype='fres_type_02'";
                        }
                        if (dto.serviceType.Equals("3"))
                        {
                            strWhere += $" and fservicetype='fres_type_03'";
                        }
                        
                    }
                    string sql = $@"/*dialect*/with t1 as (select fserstatus,fexpectamount from t_ydj_service with(nolock) where fserstatus in('sersta04','sersta05') and fcancelstatus!='1' and Convert(varchar(7),ffinishdate,23)=@date and fmainorgid=@fmainorgid {strWhere}),
                                t2 as (select fbillno from t_ydj_service with(nolock) where fserstatus='sersta05' and fcancelstatus!='1' and Convert(varchar(7),fclosedate,23)=@date and fmainorgid=@fmainorgid {strWhere})
                                select 
                                (select count(1) from t1 ) finishNum,
                                (select count(1) from (
                                select fsourcenumber from t_ydj_vist with(nolock) where fsourcetype='ydj_service' and fstatus='E' and fmainorgid=@fmainorgid and fsourcenumber in (select fbillno from t2)
                                and fservicetype='fres_type_01' and fscore in('customerscore_03','customerscore_04','customerscore_05')
                                union all
                                select fsourcenumber from t_ydj_vist with(nolock) where fsourcetype='ydj_service' and fstatus='E' and fmainorgid=@fmainorgid and fsourcenumber in (select fbillno from t2)
                                and fservicetype='fres_type_02' and fservicescore in('customerscore_03','customerscore_04','customerscore_05')
                                ) a) goodNum,
                                (select count(1) from (
                                select fsourcenumber from t_ydj_vist with(nolock) where fsourcetype='ydj_service' and fstatus='E' and fmainorgid=@fmainorgid and fsourcenumber in (select fbillno from t2)
                                and fservicetype='fres_type_01' and fscore in('customerscore_01','customerscore_02')
                                union all
                                select fsourcenumber from t_ydj_vist with(nolock) where fsourcetype='ydj_service' and fstatus='E' and fmainorgid=@fmainorgid and fsourcenumber in (select fbillno from t2)
                                and fservicetype='fres_type_02' and fservicescore in('customerscore_01','customerscore_02')
                                ) a) badNum,
                                (select sum(fexpectamount) from t1 where fserstatus='sersta05') serAmount;";

                    data = this.DBService.ExecuteDynamicObject(this.Context, sql, new List<SqlParam>
                    {
                        new SqlParam("@date",DbType.String,DateTime.Now.ToString("yyyy-MM")),
                        new SqlParam("@fmainorgid",DbType.String,this.Context.Company)
                    }).FirstNonDefault();
                    bMonthData = this.DBService.ExecuteDynamicObject(this.Context, sql, new List<SqlParam>
                    {
                        new SqlParam("@date",DbType.String,DateTime.Now.AddMonths(-1).ToString("yyyy-MM")),
                        new SqlParam("@fmainorgid",DbType.String,this.Context.Company)
                    }).FirstNonDefault();
                }
                resp.Data.sameMonthItems.Add(new ServiceCountModel
                {
                    Id = "service_finishNum",
                    Name = "完工数",
                    Count = data.IsNullOrEmpty() ? 0 : Convert.ToInt32(data?["finishNum"]),
                    LastCount= bMonthData.IsNullOrEmpty() ? 0 : Convert.ToInt32(bMonthData?["finishNum"])
                });
                resp.Data.sameMonthItems.Add(new ServiceCountModel
                {
                    Id = "service_goodNum",
                    Name = "好评数",
                    Count = data.IsNullOrEmpty() ? 0 : Convert.ToInt32(data?["goodNum"]),
                    LastCount = bMonthData.IsNullOrEmpty() ? 0 : Convert.ToInt32(bMonthData?["goodNum"])
                });
                resp.Data.sameMonthItems.Add(new ServiceCountModel
                {
                    Id = "service_badNum",
                    Name = "差评数",
                    Count = data.IsNullOrEmpty() ? 0 : Convert.ToInt32(data?["badNum"]),
                    LastCount = bMonthData.IsNullOrEmpty() ? 0 : Convert.ToInt32(bMonthData?["badNum"])
                });
                resp.Data.sameMonthItems.Add(new ServiceCountModel
                {
                    Id = "service_amount",
                    Name = "服务金额",
                    Count = data.IsNullOrEmpty() ? 0 : Convert.ToInt32(data?["serAmount"]),
                    LastCount = bMonthData.IsNullOrEmpty() ? 0 : Convert.ToInt32(bMonthData?["serAmount"])
                });
            }
            else
            {
                resp.Message = "操作失败，您没有服务待办相关的APP查看权限！";
                resp.Success = true;
            }
            return resp;
        }

        /// <summary>
        /// 校验权限范围（注意：忽略麦浩后台数据授权权限范围）
        /// </summary>
        /// <param name="context"></param>
        /// <param name="rptFormId"></param>
        /// <param name="dataPermId"></param>
        /// <param name="subDeptIds"></param>
        /// <param name="staffId"></param>
        /// <param name="deptId"></param>
        private void CheckMyDataPerms2(UserContext context, string dataPermId, out List<string> subDeptIds, out string staffId, out string deptId)
        {
            subDeptIds = new List<string>();

            switch (dataPermId)
            {
                case "myself"://本人
                    deptId = "";
                    staffId = MyStaffId;
                    break;
                case "mydepartment"://本部门
                    staffId = "";
                    deptId = MyDeptId;
                    subDeptIds = GetDeptIds(context, MyDeptId);
                    break;
                default:
                    staffId = "";
                    deptId = "";
                    break;
            }
        }

        /// <summary>
        /// 获取本部门及下属部门
        /// </summary>
        /// <param name="mydeptId"></param>
        /// <returns></returns>
        private List<string> GetDeptIds(UserContext context, string mydeptId)
        {
            List<string> deptlist = new List<string>();
            var sql = @"select fpath,fid from t_bd_department with(nolock) where fpath like @fpath and fmainorgid=@fmainorgid";
            List<SqlParam> pars = new List<SqlParam>();
            pars = new List<SqlParam>();
            pars.Add(new SqlParam("@fpath", System.Data.DbType.String, $@"%{mydeptId}%"));
            pars.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, context.Company));
            var res = DBService.ExecuteDynamicObject(context, sql, pars);
            if (res != null && res.Any())
            {
                foreach (var item in res)
                {
                    deptlist.Add(Convert.ToString(item["fid"]));
                }
            }
            return deptlist;
        }
    }
}