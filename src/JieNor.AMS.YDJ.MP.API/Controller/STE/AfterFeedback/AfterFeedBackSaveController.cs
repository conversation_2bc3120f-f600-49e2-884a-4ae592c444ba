using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.Filter;
using JieNor.Framework.SuperOrm;
using System.Data;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    [RepeatedSubmitResponseFilter("ste_afterfeedback")]
    public class AfterFeedBackSaveController : BaseController
    {
        public HtmlForm FeedBackForm;
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AfterFeedBackSaveDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();
            string message = null;
            if (!CheckAll(dto, out message))
            {
                resp.Message = message;
                resp.Success = false;
                return resp;
            }

            resp = InvokeSave(dto);

            return resp;
        }
        private BaseResponse<BaseDataModel> InvokeSave(AfterFeedBackSaveDTO dto)
        {
            BaseResponse<BaseDataModel> resp = new BaseResponse<BaseDataModel>();
            bool success = false;
            string msg = string.Empty;
            string billData = BuildBillData(dto, out success, out msg);
            if (!success)
            {
                resp.Message = msg;
                resp.Success = false;
                return resp;
            }
            CommonBillDTO commDto = new CommonBillDTO()
            {
                FormId = "ste_afterfeedback",
                OperationNo = "save",
                BillData = billData,
                Option = new Dictionary<string, object>
                    {
                        { "callerTerminal", "MPAPI" },
                    },
                Id = dto.Id
            };
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(this.Request, commDto);
            var result = response?.OperationResult;

            return result.ToResponseModel<BaseDataModel>();
        }

        private string BuildBillData(AfterFeedBackSaveDTO dto, out bool success, out string msg)
        {
            success = true;
            msg = string.Empty;
            UserContext userCtx = this.Context;
            var data = new Dictionary<string, object>
                {
                    { "id",dto.Id}
                };
            if (dto.BillTypeNo.IsNullOrEmptyOrWhiteSpace() && dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                var metaModelService = userCtx.Container.GetService<IMetaModelService>();
                var htmlForm = metaModelService.LoadFormModel(userCtx, "ste_afterfeedback");
                var billTypeService = userCtx.Container.GetService<IBillTypeService>();
                var billTypeId = billTypeService.GetDefaultBillTypeId(userCtx, htmlForm);
                dto.BillTypeNo = billTypeId;
                data.Add("fbilltype", dto.BillTypeNo);
            }
            else if (!dto.BillTypeNo.IsNullOrEmptyOrWhiteSpace())
            {
                data.Add("fbilltype", dto.BillTypeNo);
            }
            //if (!dto.BaseInfo.SourceType.IsNullOrEmptyOrWhiteSpace() && !dto.BaseInfo.SourceNumber.IsNullOrEmptyOrWhiteSpace())
            //{
            data.Add("fsourcetype", dto.BaseInfo.SourceType.IsNullOrEmpty() ? "" : dto.BaseInfo.SourceType);
            data.Add("fsourcenumber", dto.BaseInfo.SourceNumber.IsNullOrEmpty() ? "" : dto.BaseInfo.SourceNumber);
            //}

            data.Merge(new Dictionary<string, object> {
                    { "fagentid", dto.BaseInfo.AgentId },
                    //{ "fauthcity", dto.BaseInfo.AuthCity },
                    { "fdate", dto.BaseInfo.Date },
                    { "fstaffid", dto.BaseInfo.StaffId },
                    { "fphone", dto.BaseInfo.StaffPhone },
                    { "fdeptid", dto.BaseInfo.DeptId },
                    { "forderno", dto.BaseInfo.OrderId },
                    { "fcustomerid",  dto.BaseInfo.CustomerId },
                    { "fprovince", dto.BaseInfo.Province },
                    { "fcity", dto.BaseInfo.City },
                    { "fregion", dto.BaseInfo.Region },
                    { "flinkstaffid", dto.BaseInfo.LinkStaff },
                    { "flinkmobile", dto.BaseInfo.LinkMobile },
                    { "flinkaddress", dto.BaseInfo.Address },
                    { "fengineerid", dto.BaseInfo.EngineerId },
                    { "fcategoryid", dto.BaseInfo.CategoryId },
                    { "fdeliver", dto.BaseInfo.Deliver }
                    });

            if (!dto.CreatDate.IsNullOrEmptyOrWhiteSpace())
            {
                data.Add("fcreatedate", dto.CreatDate );
            }

            //data.Add("fquestionproduct", dto.ProductId);
            //if (dto.ProductId.IsNullOrEmptyOrWhiteSpace())
            //{
            //    var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            //    var productObj = productForm.GetBizDataById(this.Context, dto.ProductId);
            //    data.Add("funitid", productObj?["funitid"]);
            //}
            var productDetails = new List<Dictionary<string, object>>();
            //商品信息明细赋值
            if (dto.Products != null && dto.Products.Any())
            {
                var productForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
                var productObjs = productForm.GetBizDataById(this.Context, dto.Products.Select(s => s.ProductId));
                foreach (var item in dto.Products)
                {
                    var productObj = productObjs.FirstOrDefault(s => s["id"].Equals(item.ProductId));
                    var detail = new Dictionary<string, object>
                    {
                        { "id", item.Id },
                        { "fmaterialid", item.ProductId },
                        { "fattrinfo", item.Attrinfo },
                        { "fcustomdesc", item.CustomDesc },
                        { "funitid",productObj?["funitid"]},
                        { "fqty", item.Qty },
                        { "famount", item.Amount }
                    };
                    productDetails.Add(detail);
                }
            }
            data.Add("fproductentry", productDetails);
            data.Add("fquestiontype", dto.QuestionTypeId);
            data.Add("fquestiondesc", dto.QuestionDesc);
            // 附件
            data.Add("fquestionimage", ImageFieldUtil.ConvertImage(dto.QuestionImgs));
            data.Add("fquestionimage_txt", ImageFieldUtil.ConvertImageTxt(dto.QuestionImgs));

            data.Add("finstitutiontype", dto.WorkTypeId);
            switch (dto.WorkTypeId)
            {
                case "dutyunit_type_01":
                    data.Add("fdutysupplierid", dto.WorkInfo);
                    break;
                case "dutyunit_type_02":
                    data.Add("fdutycustomerid", dto.WorkInfo);
                    break;
                case "dutyunit_type_03":
                    data.Add("fdutystaffid", dto.WorkInfo);
                    break;
                case "dutyunit_type_04":
                    data.Add("fdutydeptid", dto.WorkInfo);
                    break;
            }
            data.Add("fhandleconclusion", dto.ConclusionId);
            data.Add("fscheme", dto.Scheme);

            data.Add("fhqhandleconclusion", dto.HqHandleConclusion);
            data.Add("fhqscheme", dto.HqScheme);

            //内部处理结论为返厂则新增返厂申请
            //if (dto.ConclusionId == "res_type_04")
            //{
            data.Merge(new Dictionary<string, object> {
                    { "freturndate", dto.ReturnApplication.ReturnDate },
                    { "fauthcity", dto.ReturnApplication.AuthCity },
                    { "fengineerphone", dto.ReturnApplication.EngineerTel },
                    //{ "fseltypename",  dto.ReturnApplication.SeltypeName },
                    //{ "fspecifica", dto.ReturnApplication.Specifica },
                    //{ "fprocount", dto.ReturnApplication.ProCount },
                    { "ffeedtype", dto.ReturnApplication.FeedTypeId },
                    { "freturnreson", dto.ReturnApplication.ReturnReson },
                    { "fengineeraddress", dto.ReturnApplication.EngineerAddress },
                    { "fcusreturnadd", dto.ReturnApplication.CusreturnAdd },
                    { "fsapnumber", dto.ReturnApplication.SapNumber },
                    { "farrivaldate", dto.ReturnApplication.ArrivalDate },
                    { "fservicedate", dto.ReturnApplication.ServiceDate },
                    { "fcususetime", dto.ReturnApplication.CususeTime }
                    });

            if (!dto.ReturnApplication.ReturnlogNum.IsNullOrEmptyOrWhiteSpace() && !dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                FeedBackForm = this.MetaModelService.LoadFormModel(this.Context, "ste_afterfeedback");
                var isTranfer = this.Context.ExecuteDynamicObject("select fistransfer from t_ste_afterfeedback with(nolock) where fid=@id",
                new List<SqlParam> {
                        new SqlParam("@id",DbType.String,dto.Id)
                })?.FirstOrDefault()?["fistransfer"];
                ////转总部且物流单号不为空才给保存
                //if (isTranfer.Equals("1"))
                //{
                data.Add("freturnlognum", dto.ReturnApplication.ReturnlogNum);
                //}
            }
            //}
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                data.Add("ffeedstatus", "aft_service_01");
                data.Add("fsourcecancl", "1");
            }
            var billData = (new List<Dictionary<string, object>> { data }).ToJson();

            return billData;
        }

        private bool CheckAll(AfterFeedBackSaveDTO dto, out string message)
        {
            message = null;
            //小程序段省市区限制不能为空，PC端前端限制了，后端不限制，让支持总部省市区
            if (dto.BaseInfo.Province.IsNullOrEmptyOrWhiteSpace())
            {
                message = "参数【省】不能为空！";
                return false;
            }
            else if (dto.BaseInfo.City.IsNullOrEmptyOrWhiteSpace())
            {
                message = "参数【市】不能为空！";
                return false;
            }
            else if (dto.BaseInfo.Region.IsNullOrEmptyOrWhiteSpace())
            {
                message = "参数【区】不能为空！";
                return false;
            }
            //else if (dto.BaseInfo.Address.IsNullOrEmptyOrWhiteSpace())
            //{
            //    message = "参数 Address 不能为空！";
            //    return false;
            //}
            if (!dto.QuestionImgs.IsNullOrEmptyOrWhiteSpace() && dto.QuestionImgs.Count() > 9)
            {
                message = "问题图片张数不能大于9！";
                return false;
            }
            if (dto.BaseInfo.Deliver.IsNullOrEmptyOrWhiteSpace())
            {
                message = "送达方不能为空！";
                return false;
            }
            if (dto.HqHandleConclusion == "fqres_type_08")
            {
                if (dto.ReturnApplication.EngineerTel.IsNullOrEmptyOrWhiteSpace())
                {
                    message = "返厂申请-工程师电话不能为空！";
                    return false;
                }
                else if (dto.ReturnApplication.FeedTypeId.IsNullOrEmptyOrWhiteSpace())
                {
                    message = "返厂申请-返厂售后类型不能为空！";
                    return false;
                }
                else if (dto.ReturnApplication.ReturnReson.IsNullOrEmptyOrWhiteSpace())
                {
                    message = "返厂申请-产品返厂原因不能为空！";
                    return false;
                }
                //else if (dto.ReturnApplication.SeltypeName.IsNullOrEmptyOrWhiteSpace())
                //{
                //    message = "返厂申请-产品型号不能为空！";
                //    return false;
                //}
                //else if (dto.ReturnApplication.Specifica.IsNullOrEmptyOrWhiteSpace())
                //{
                //    message = "返厂申请-尺寸规格不能为空！";
                //    return false;
                //}
                //else if (dto.ReturnApplication.ProCount.IsNullOrEmptyOrWhiteSpace())
                //{
                //    message = "返厂申请-商品数量不能为空！";
                //    return false;
                //}
                else if (dto.ReturnApplication.EngineerAddress.IsNullOrEmptyOrWhiteSpace())
                {
                    message = "返厂申请-退货地址不能为空！";
                    return false;
                }
                else if (dto.ReturnApplication.SapNumber.IsNullOrEmptyOrWhiteSpace())
                {
                    message = "返厂申请-SAP订单号不能为空！";
                    return false;
                }
            }
            //var errorMessage = "";
            //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            //{
            //    if (!newData["fhandleconclusion"].IsNullOrEmptyOrWhiteSpace()&&newData["fhandleconclusion"].Equals("res_type_04"))
            //    {
            //        if (newData["fcountry"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中城市不能为空！";
            //            return false;
            //        }
            //        else if (newData["fdeliver"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中送达方不能为空！";
            //            return false;
            //        }
            //        else if (newData["fengineeraddress"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中退货地址不能为空！";
            //            return false;
            //        }
            //        else if (newData["fseltypename"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中产品型号不能为空！";
            //            return false;
            //        }
            //        else if (newData["fspecifica"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中尺寸规格不能为空！";
            //            return false;
            //        }
            //        else if (newData["fprocount"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中商品数量不能为空！";
            //            return false;
            //        }
            //        else if (newData["ffeedtype"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中返厂售后类型不能为空！";
            //            return false;
            //        }
            //        else if (newData["freturnreson"].IsNullOrEmptyOrWhiteSpace())
            //        {
            //            errorMessage = "返厂申请中产品返厂原因不能为空！";
            //            return false;
            //        }
            //    }
            //    return true;
            //}).WithMessage("{0}", (billObj, propObj) => errorMessage));
            if (dto.Products.IsNullOrEmptyOrWhiteSpace())
            {
                message = "对不起，问题商品不能为空！";
                return false;
            }

            if (!dto.Products.IsNullOrEmptyOrWhiteSpace() && dto.Products.Count>=2)
            {
                message = "对不起，问题商品只能选择1个！";
                return false;
            }

            return true;
        }
    }
}
