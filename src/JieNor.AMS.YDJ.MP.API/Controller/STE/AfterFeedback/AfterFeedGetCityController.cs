using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Controller
{
    public class AfterFeedGetCityController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(AfterFeedGetCityDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataSimpleModel>();
            if (dto.OrderId.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Message = "请补齐必填参数！";
                resp.Success = false;
                return resp;
            }
            CommonBillDTO commDto = new CommonBillDTO()
            {
                FormId = "ydj_city",
                OperationNo = "getcitybyforminfo",
                Option = new Dictionary<string, object>
                    {
                        { "callerTerminal", "MPAPI" },
                        { "hformid", "ydj_order" },
                        { "id", dto.OrderId }
                    },
                Id = dto.OrderId
            };
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(this.Request, commDto);
            var result = response?.OperationResult;
            string jsonStr = result.SrvData as string;
            var reData = jsonStr.FromJson<Dictionary<string, string>>();
            resp.Data = new BaseDataSimpleModel
            {
                Id = reData?["fid"],
                Number = reData?["fnumber"],
                Name = reData?["fname"]
            };

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Code = 200;
            
            return resp;
        }
    }
}
