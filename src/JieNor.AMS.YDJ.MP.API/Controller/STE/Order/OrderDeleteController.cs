using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System.Collections.Generic;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 微信小程序：合同单删除接口
    /// </summary>
    public class OrderDeleteController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderDeleteDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_order",
                    OperationNo = "delete",
                    SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } }
                });
            var result = response?.OperationResult;

            resp = result.ToResponseModel<BaseDataModel>(false);

            return resp;
        }
    }
}