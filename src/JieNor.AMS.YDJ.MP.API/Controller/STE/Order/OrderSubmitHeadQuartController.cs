using System.Collections.Generic;
using JieNor.AMS.YDJ.MP.API.DTO.STE.Order;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    public class OrderSubmitHeadQuartController : BaseOrderController
    {
        public object Any(OrderSubmitHeadQuartDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<BaseDataModel>();
            if (!Valid(dto, resp)) return resp;
            
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_order",
                    OperationNo = "SubmitHeadquart",
                    SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } }
                });
            
            var result = response?.OperationResult;
            resp = result.ToResponseModel<BaseDataModel>(false);

            return resp;
        }
        
        
        private bool Valid(OrderSubmitHeadQuartDTO dto, BaseResponse<BaseDataModel> resp)
        {
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return false;
            }
            
            var metaModelService = this.Context.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(this.Context, "ydj_order");
            
            
            
            return true;
        }
    }
}