using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order.Promotion
{
    /// <summary>
    /// 填充促销活动商品
    /// </summary>
    public class FillPromotionDataController : BaseController
    {
        public override string FormId { get; } = "ydj_order";

        private IOrderService OrderService { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(FillPromotionDataDTO dto)
        {
            base.InitializeOperationContext(dto);

            this.OrderService = this.Container.GetService<IOrderService>();

            var resp = new BaseResponse<object>();

            var result = this.HttpGateway.InvokeBillOperation(this.Context, this.FormId,
                  new List<DynamicObject> { }, "FillPromotionData", new Dictionary<string, object>
                  {
                      { "promotionIds", dto.PromotionIds.JoinEx(",",false) },
                      { "promotionType", dto.PromotionType },
                      { "billData", dto.BillData.ToJson() },
                      { "customParam", dto.Gifts.ToJson() }
                  });

            var uiData = (result.SrvData as JObject)?["uidata"] as JObject;

            PromotionUtil.FillImg(this.Context, this.HtmlForm, uiData);

            // 填充其他字段
            FillExtendFields(uiData);

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data = uiData;

            return resp;
        }

        private void FillExtendFields(JObject uiData)
        {
            var entrys = uiData["fentry"] as JArray;

            var productIds = new HashSet<string>();

            foreach (var item in entrys)
            {
                var fproductid = (item as JObject)?["fproductid"]?.GetJsonValue("id", "");
                if (fproductid.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                productIds.Add(fproductid);
            }

            var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds.ToList(), "fmainorgid,funstdtype");

            var currOrderPartsCombNumber = string.Empty;    // 当前合同明细的配件组合号
            ComboDataModel_status comboMainResultBrandId = null;      // 当前主配件的业绩品牌

            foreach (var item in entrys)
            {
                var entry = item as JObject;

                var productJObj = entry["fproductid"] as JObject;

                var fproductid = productJObj.GetJsonValue("id", "");
                var fissuitflag = entry.GetJsonValue<bool>("fissuitflag");
                var fprice = entry.GetJsonValue<decimal>("fprice");

                if (fproductid.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                var productObj =
                    productObjs.FirstOrDefault(s => Convert.ToString(s["id"]).EqualsIgnoreCase(fproductid));
                if (productObj == null)
                {
                    continue;
                }

                entry["isAllowOrders"] = this.OrderService.CheckNoOrders(this.Context, new NoOrderParmas
                {
                    fisgiveaway = false,
                    fissuit = fissuitflag,
                    fmainorgid = Convert.ToString(productObj["fmainorgid"]),
                    fprice = fprice,
                    funstdtype = Convert.ToString(productObj["funstdtype"]).EqualsIgnoreCase("1")
                });


                var deptId = (uiData["fdeptid"] as JObject)?.GetJsonValue("id", "");
                var billTypeName = (uiData["fbilltype"] as JObject)?.GetJsonValue("fname", "");

                var fauxseriesid = productJObj["fauxseriesid"].GetJsonValue("id", "");
                var seriesJObj = productJObj["fseriesid"] as JObject;
                var fseriesid = seriesJObj.GetJsonValue("id", "");
                var fseriesname = seriesJObj.GetJsonValue("fname", "");
                var fseriesnumber = seriesJObj.GetJsonValue("fnumber", "");

                var resultBrand = this.Context.GetResultBrand(deptId, billTypeName, fseriesid, fseriesnumber, fseriesname, fauxseriesid);

                var fiscombmain = Convert.ToBoolean(entry["fiscombmain"]);
                var fpartscombnumber = Convert.ToString(entry["fpartscombnumber"]);

                // 子件的业绩品牌与主配件是一样的
                if (!fpartscombnumber.IsNullOrEmptyOrWhiteSpace())
                {
                    if (currOrderPartsCombNumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        currOrderPartsCombNumber = fpartscombnumber;
                    }

                    if (fiscombmain)
                    {
                        entry["fresultbrandid"] = JObject.FromObject(resultBrand);
                        comboMainResultBrandId = resultBrand;
                    }

                    if (currOrderPartsCombNumber.EqualsIgnoreCase(fpartscombnumber) && !fiscombmain)
                    {
                        entry["fresultbrandid"] = JObject.FromObject(comboMainResultBrandId);
                    } 
                }
                else
                {
                    entry["fresultbrandid"] = JObject.FromObject(resultBrand);
                }

                entry["isSofaCategory"] = ProductUtil.HaveAnyCategory(this.Context, "沙发类", fproductid);

                var propSuiteCount = new BaseDataModel();
                if (fissuitflag)
                {
                    propSuiteCount = this.Context.GetPropSuiteCount();
                }

                entry["suiteSumQtyProp"] = JObject.FromObject(propSuiteCount);
            }
        }
    }
}