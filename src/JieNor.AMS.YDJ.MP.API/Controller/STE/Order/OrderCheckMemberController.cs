using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using JieNor.AMS.YDJ.MP.API.DTO.STE.Order;
using Newtonsoft.Json.Linq;
using JieNor.Framework.Interface;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    public class OrderCheckMemberController : BaseOrderController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OrderCheckMemberDTO dto)
        {
            base.InitializeOperationContext(dto);
            var resp = new BaseResponse<MemberMdl>();
            var mdl = new MemberMdl();

            mdl.memberId = GetMemberId(dto.customerId);
            List<string> billErrArr = new List<string> { "期初销售合同", "v6定制柜合同", "标准销售合同", "销售转单" };
            if (billErrArr.Contains(dto.billName.ToLower()))
            {
                IOrderService orderService = this.Container.GetService<IOrderService>();
                var res = orderService.GetAgentmember(this.Context, (new { fdeptid = dto.deptId, fcustomerid = dto.customerId }).ToJson());
                if (res)
                {
                    mdl.Res = res;
                    if (dto.type == "1")
                    {
                        resp.Message = "此客户尚未注册会员，若确认无法让客户注册会员，请合同提交前先说明客户“未注册会员原因”！";
                    }
                    else
                    {
                        resp.Message = "该客户未注册会员，请确认手机号与注册会员信息一致，合同提交前需确保对应注册！以免影响权益。";
                    }
                }
            }
            resp.Data = mdl;
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 得到会员ID
        /// </summary>
        /// <returns></returns>
        private string GetMemberId(string cid)
        {
            if (cid.IsNullOrEmptyOrWhiteSpace()) return "";
            //查询客户会员ID 是否不为空
            string strSql = string.Format("select top 1 fmemberno from t_ydj_customer with(nolock) where fid='{0}'", cid);
            var customerDt = this.Context.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
            var fmemberno = customerDt.Select(x => Convert.ToString(x["fmemberno"])).FirstOrDefault();
            return fmemberno;
        }
    }
}
