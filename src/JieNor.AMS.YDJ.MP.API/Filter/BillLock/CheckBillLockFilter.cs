using JieNor.Framework;
using JieNor.Framework.Interface.Cache;
using System;
using System.Net;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Linq;
using JieNor.AMS.YDJ.MP.API.DTO;
using ServiceStack;
using ServiceStack.Web;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Filter
{
    /// <summary>
    /// 校验单据锁定请求过滤器：用于验证此业务对象是否锁定
    /// </summary>
    public class CheckBillLockFilter : BaseRequestFilter
    {
        /// <summary>
        /// 表单标识
        /// </summary>
        protected string FormId { get; set; }

        protected string OperationNo { get; set; }

        protected HtmlForm HtmlForm { get; set; }

        /// <summary>
        /// 单据锁定服务
        /// </summary>
        protected IBillLockService BillLockService { get; private set; }

        public CheckBillLockFilter(string formId, string operationNo)
        {
            this.FormId = formId;
            this.OperationNo = operationNo;
        }

        /// <summary>
        /// 执行过滤器逻辑
        /// </summary>
        /// <param name="req"></param>
        /// <param name="res"></param>
        /// <param name="requestDto"></param>
        public override void Execute(IRequest req, IResponse res, object requestDto)
        {
            base.Execute(req, res, requestDto);

            this.BillLockService = this.Container.GetService<IBillLockService>();
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var dto = requestDto as ICheckBillLockDTO;
            if (dto != null)
            {
                this.CheckLocked(req, res, dto);
            }
        }


        protected void CheckLocked(IRequest req, IResponse res, ICheckBillLockDTO dto)
        {
            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                return;
            }

            DynamicObject bizObj = this.Context.LoadBizBillHeadDataById(this.FormId, dto.Id, this.HtmlForm.NumberFldKey);
            if (bizObj == null)
            {
                return;
            }

            string lockToken = this.Request.GetBillLockToken(); //dto.LockToken;
            string warningMessage = null;

            var operationNo = this.OperationNo.IsNullOrEmptyOrWhiteSpace() ? dto.GetOperationNo() : this.OperationNo;

            // 没有启用加锁
            if (!this.BillLockService.EnableBillLock(this.Context, this.HtmlForm, operationNo))
            {
                return;
            }

            string bizObjId = Convert.ToString(bizObj["id"]);
            string bizObjNo = this.HtmlForm.GetNumberField()?.DynamicProperty.GetValue<string>(bizObj);

            if (this.BillLockService.IsLocked(this.Context, this.HtmlForm.Id, bizObjId, lockToken, out var lockTokenInfo))
            {
                if (lockToken.IsNullOrEmptyOrWhiteSpace())
                {
                    var htmlOperation = this.HtmlForm.FormOperations.FirstOrDefault(s => string.Equals(s.Id, operationNo, StringComparison.OrdinalIgnoreCase));

                    warningMessage =
                        $"{this.HtmlForm.Caption} {bizObjNo} 正在被 {lockTokenInfo.DisplayName} 编辑中，无法执行【{htmlOperation?.OperationName}】";
                }
                else
                {
                    warningMessage =
                        $"{this.HtmlForm.Caption} {bizObjNo} 拥有的锁过期，请重新进入编辑";
                }

                BaseResponse<object> resp = new BaseResponse<object>();
                resp.Success = false;
                resp.Code = 0;
                resp.Message = warningMessage;
                resp.IsLock = true;
                resp.IsShowMessage = true;

                res.WriteToResponse(resp, "application/json");
                res.EndRequest();
            }
        }
    }
}