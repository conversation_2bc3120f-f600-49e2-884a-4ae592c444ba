using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Config;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Model.BAS.CollectingUnit;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.Interface.UsrMgr;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using JieNor.Framework.MetaCore.PermData;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using ServiceStack.Web;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    /// <summary>
    /// 登录上下文扩展类
    /// </summary>
    public static class UserContextExtentions
    {
        /// <summary>
        /// 获取当前用户关联的员工的所在部门
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static string GetCurrentDeptId(this UserContext userCtx)
        {
            return userCtx.GetCurrentDept().Id;
        }

        /// <summary>
        /// 获取当前用户关联的员工的所在部门
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static BaseDataSummary GetCurrentDept(this UserContext userCtx)
        {
            var provider = userCtx?.Container.GetService<IBaseFormProvider>();
            var currDept = provider?.GetMyDepartment(userCtx) ?? new BaseDataSummary();

            return currDept;
        }

        /// <summary>
        /// 获取当前用户关联的员工
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static string GetCurrentStaffId(this UserContext userCtx)
        {
            return userCtx.GetCurrentStaff().Id;
        }

        /// <summary>
        /// 获取当前用户关联的员工
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static BaseDataSummary GetCurrentStaff(this UserContext userCtx)
        {
            var provider = userCtx?.Container.GetService<IBaseFormProvider>();
            var currStaff = provider?.GetMyStaff(userCtx) ?? new BaseDataSummary();

            return currStaff;
        }

        /// <summary>
        /// 根据用户编号（即用户名）获取关联员工的主岗位所在部门
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="userNumber">用户编号（即用户名）</param>
        /// <returns></returns>
        public static BaseDataSummary GetDeptByUserNumber(this UserContext userCtx, string userNumber)
        {
            BaseDataSummary deptData = null;

            var dbService = userCtx.Container.GetService<IDBService>();
            var strSql = @"select top 1 t2.fid,t2.fpath, t2.fnumber,t2.fname
from t_sec_user t0
inner join t_bd_staff t1 on t0.fphone=t1.fphone and t1.fphone>'' and t0.fmainorgid=t1.fmainorgid
inner join t_bd_department t2 on t1.fdeptid=t2.fid
where t0.fnumber=@fnumber";
            var lstSqlPara = new List<SqlParam>
            {
                new SqlParam("fnumber", System.Data.DbType.String, userNumber)
            };
            using (var reader = dbService.ExecuteReader(userCtx, strSql, lstSqlPara))
            {
                if (reader.Read())
                {
                    deptData = new BaseDataSummary();
                    deptData.Id = DBReaderUtils.GetValue<string>(reader, "fid");
                    deptData.Number = DBReaderUtils.GetValue<string>(reader, "fnumber");
                    deptData.Name = DBReaderUtils.GetValue<string>(reader, "fname");
                    deptData.ExtendData.Add("fpath", DBReaderUtils.GetValue<string>(reader, "fpath"));
                }
            }

            return deptData;
        }

        /// <summary>
        /// 根据用户编号（即用户名）获取关联员工
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="userNumber">用户编号（即用户名）</param>
        /// <returns></returns>
        public static BaseDataSummary GetStaffByUserNumber(this UserContext userCtx, string userNumber)
        {
            BaseDataSummary staffData = null;

            var dbService = userCtx.Container.GetService<IDBService>();
            var strSql = @"select top 1 t1.fid,t1.fnumber,t1.fname
from t_sec_user t0
inner join T_BD_staff t1 on (t0.fid=t1.flinkuserid or t0.fphone=t1.fphone and t1.fphone>'' or t0.fwechatid=t1.fwechat and t1.fwechat>'' or t0.femail=t1.femail and t1.femail>'') and t0.fmainorgid=t1.fmainorgid
where t0.fnumber=@fnumber";
            var lstSqlPara = new List<SqlParam>
            {
                new SqlParam("fnumber", System.Data.DbType.String, userNumber)
            };
            using (var reader = dbService.ExecuteReader(userCtx, strSql, lstSqlPara))
            {
                if (reader.Read())
                {
                    staffData = new BaseDataSummary();
                    staffData.Id = DBReaderUtils.GetValue<string>(reader, "fid");
                    staffData.Number = DBReaderUtils.GetValue<string>(reader, "fnumber");
                    staffData.Name = DBReaderUtils.GetValue<string>(reader, "fname");
                }
            }
            return staffData;
        }

        /// <summary>
        /// 获取部门数据源
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static Dictionary<string, List<Dictionary<string, object>>> GetDeptDataSourceOld (this UserContext userCtx)
        {
            var dataSource = new Dictionary<string, List<Dictionary<string, object>>>();

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };

            string sqlText = $"select fid,fnumber,fname,fsalecategories from t_bd_department with(nolock) where fmainorgid=@fmainorgid";

            var list = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sqlText, sqlParams);

            dataSource.Add("fdept", list.Select(s => new Dictionary<string, object>
            {
                { "id",  s["fid"] },
                { "number",  s["fnumber"] },
                { "name",  s["fname"] },
                { "salecategories", JNConvert.ToStringAndTrim(s["fsalecategories"]).Split(new string[]{","}, StringSplitOptions.RemoveEmptyEntries) }
    }).ToList());

            return dataSource;
        }

        /// <summary>
        /// 获取部门数据源(数据过滤)
        /// 修改获取部门相关的方法以支持数据权限过滤
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto">请求参数</param>
        /// <param name="comboDataName">公共数据控制字段名</param>
        /// <returns></returns>
        public static Dictionary<string, List<Dictionary<string, object>>> GetDeptDataSource(this UserContext userCtx, BaseDetailDTO dto,string comboDataName = "fdept")
        {
            var dataSource = new Dictionary<string, List<Dictionary<string, object>>>();
            //参数对象
            SqlBuilderParameter param = new SqlBuilderParameter(userCtx, "ydj_dept");
            param.ReadDirty = true;
            param.NoColorSetting = true;
            param.SrcFormId = dto.SrcFormId;
            param.SrcFldId = dto.SrcFidId.IsNullOrEmptyOrWhiteSpace()?"fdeptid":dto.SrcFidId;
            //当前要查询的字段列表
            var fieldKeys = new string[] { "id", "fnumber", "fname", "fsalecategories" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(userCtx);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }

            //列表构建器
            var listBuilder = userCtx.Container.GetService<IListSqlBuilder>();

            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(userCtx, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            //查询对象
            var queryObj = listBuilder.GetQueryObject(userCtx, param);

            //获取分页数据
            var listDatas = listBuilder.GetQueryData(userCtx, param, queryObj);

            dataSource.Add(comboDataName, listDatas.Select(s => new Dictionary<string, object>
            {
                { "id",  s["fbillhead_id"] },
                { "number",  s["fnumber"] },
                { "name",  s["fname"] },
                { "salecategories", JNConvert.ToStringAndTrim(s["fsalecategories"]).Split(new string[]{","}, StringSplitOptions.RemoveEmptyEntries) }
    }).ToList());

            return dataSource;
        }

        /// <summary>
        /// 获取银行数据源
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static Dictionary<string, List<Dictionary<string, object>>> GetBankDataSource(this UserContext userCtx)
        {
            var dataSource = new Dictionary<string, List<Dictionary<string, object>>>();

            List<SqlParam> sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };

            string sqlText = $"select fid,fnumber,fname from t_ydj_banknum with(nolock) where fmainorgid=@fmainorgid and FForbidStatus='0'";

            var list = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sqlText, sqlParams);

            dataSource.Add("fmybank", list.Select(s => new Dictionary<string, object>
            {
                { "id",  s["fid"] },
                { "number",  s["fnumber"] },
                { "name",  s["fname"] },
                { "disable",  false }
            }).ToList());
            return dataSource;
        }

        /// <summary>
        /// 获取预留单
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        public static DynamicObject GetReserveBillBySource(this UserContext userCtx, string sourceType, string sourceNumber)
        {
            string sqlText =
                $"select top 1 fid,fbillno from t_stk_reservebill with(nolock) where fmainorgid=@fmainorgid and fsourcenumber=@fsourcenumber and fsourcetype=@fsourcetype";

            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fsourcetype", System.Data.DbType.String, sourceType),
                new SqlParam("@fsourcenumber", System.Data.DbType.String, sourceNumber),
            };

            return userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sqlText, sqlParams).FirstOrDefault();
        }

        /// <summary>
        /// 跟进记录
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="request">数据</param>
        /// <param name="dto">数据</param>
        /// <returns></returns>
        public static BaseResponse<BaseDataModel> AddFollowerRecord(this UserContext userCtx, IRequest request, FollowerRecordSaveDTO dto)
        {
            var billData = (new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    { "id", dto.Id },
                    { "fdeptid", userCtx.GetCurrentDeptId() },
                    { "fstaffid", userCtx.GetCurrentStaffId() },

                    { "fimage", dto.AttachmentIds },
                    { "fimage_txt", dto.AttachmentNames },

                    { "fsourcetype",  dto.SourceType },
                    { "fsourcenumber", dto.SourceNumber },
                    { "frelatedbilltype",  dto.SourceType },
                    { "frelatedbillno", dto.SourceNumber },
                    { "ftranid", dto.TranId },
                    { "ftype", "6" },

                    { "fcontacts", dto.Contacts },
                    { "fcustomerid", dto.CustomerId },
                    { "ffollowtime", dto.FollowTime },
                    { "ffollowerid", userCtx.UserId },
                    { "fphone", dto.Phone },
                    { "fdescription", dto.Description },

                    { "fobjecttype", dto.ObjectType },
                    { "fobjectid", dto.ObjectId },
                    { "fobjectno", dto.ObjectNo }
                }
            }).ToJson();

            // 调用麦浩的接口
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                request,
                new CommonBillDTO()
                {
                    FormId = "ydj_followerrecord",
                    OperationNo = "save",
                    BillData = billData,
                    Id = dto.Id
                });
            var result = response?.OperationResult;

            return result.ToResponseModel<BaseDataModel>();
        }

        /// <summary>
        /// 小程序服务单：保存跟进记录
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dto">数据</param>
        /// <returns></returns>
        public static BaseResponse<BaseDataModel> AddServiceFollowerRecord(this UserContext userCtx, IRequest request, ServiceFollowSaveDTO dto)
        {
            var billData = (new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    { "id", dto.Id },
                    { "fdeptid", userCtx.GetCurrentDeptId() },
                    { "fstaffid", userCtx.GetCurrentStaffId() },

                    { "fimage", dto.AttachmentIds },
                    { "fimage_txt", dto.AttachmentNames },

                    { "fsourcetype",  dto.SourceType },
                    { "fsourcenumber", dto.SourceNumber },
                    { "frelatedbilltype",  dto.SourceType },
                    { "frelatedbillno", dto.SourceNumber },

                    { "fcontacts", dto.Contacts },
                    { "fcustomerid", dto.CustomerId },
                    { "ffollowtime", dto.FollowTime },
                    { "ffollowerid", userCtx.UserId },
                    { "fdescription", dto.Description },

                    { "fobjecttype", dto.ObjectType },
                    { "fobjectid", dto.ObjectId },
                    { "fobjectno", dto.ObjectNo }
                }
            }).ToJson();
            // 调用麦浩的接口
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                request,
                new CommonBillDTO()
                {
                    FormId = "ydj_followerrecord",
                    OperationNo = "save",
                    BillData = billData,
                    Id = dto.Id
                });
            var result = response?.OperationResult;

            return result.ToResponseModel<BaseDataModel>();
        }

        /// <summary>
        /// 报备商机反写客户跟进记录
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="dto">数据</param>
        /// <returns></returns>
        public static BaseResponse<BaseDataModel> AddCusFollowerRecord(this UserContext userCtx, IRequest request, FollowerRecordSaveDTO dto)
        {
            var billData = (new List<Dictionary<string, object>>
            {
                new Dictionary<string, object>
                {
                    { "id", dto.Id },
                    { "fdeptid", userCtx.GetCurrentDeptId() },
                    { "fstaffid", userCtx.GetCurrentStaffId() },

                    { "fimage", dto.AttachmentIds },
                    { "fimage_txt", dto.AttachmentNames },

                    { "fsourcetype",  dto.SourceType },
                    { "fsourcenumber", dto.SourceNumber },
                    { "frelatedbilltype",  dto.SourceType },
                    { "frelatedbillno", dto.SourceNumber },
                    { "ftranid", dto.TranId },
                    { "ftype", "6" },

                    { "fcontacts", dto.Contacts },
                    { "fcustomerid", dto.CustomerId },
                    { "ffollowtime", dto.FollowTime },
                    { "ffollowerid", userCtx.UserId },
                    { "fphone", dto.Phone },
                    { "fdescription", dto.Description },

                    { "fobjecttype", dto.ObjectType },
                    {"fclosestatus","0"},
                    { "fobjectid", dto.ObjectId },
                    { "fobjectno", dto.ObjectNo }
                }
            }).ToJson();

            // 调用麦浩的接口
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                request,
                new CommonBillDTO()
                {
                    FormId = "ydj_followerrecord",
                    OperationNo = "save",
                    BillData = billData,
                    Id = dto.Id
                });
            var result = response?.OperationResult;

            ////如果是商机入口添加跟进记录，则把最后跟进时间反写到客户最后跟进时间
            //if (dto.SourceType == "ydj_customerrecord")
            //{
            //    var billdata2 = (new List<Dictionary<string, object>>
            //   {
            //        new Dictionary<string, object>
            //        {
            //            {"id",dto.CustomerId },
            //            {"flastfollowdata",dto.FollowTime }
            //        }
            //    }).ToJson();
            //    var response2 = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
            //        request,
            //        new CommonBillDTO() { FormId = "ydj_customer", OperationNo = "save", BillData = billdata2, Id = dto.CustomerId }
            //        );
            //}
            return result.ToResponseModel<BaseDataModel>();
        }

        /// <summary>
        /// 根据主键ID获取业务表单动态对象
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="formId">单据标识</param>
        /// <param name="pkid">主键ID</param>
        /// <param name="loadRef">是否加载引用数据</param>
        /// <returns>动态对象</returns>
        public static DynamicObject GetBizDataById(this UserContext userCtx,
            string formId,
            object pkid,
            bool loadRef = false)
        {
            var htmlForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, formId);

            return htmlForm.GetBizDataById(userCtx, pkid, loadRef);
        }

        /// <summary>
        /// 按某个字段（如单据编码、基础资料编号）获取业务表单动态对象
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formId">单据标识</param>
        /// <param name="no">编号</param>
        /// <param name="fldKey">字段名</param>
        /// <param name="loadRef">是否加载引用数据</param>
        /// <returns>动态对象</returns>
        public static DynamicObject GetBizDataByNo(this UserContext userCtx,
            string formId,
            string no,
            string fldKey,
            bool loadRef = false)
        {
            var htmlForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, formId);

            return htmlForm.GetBizDataByNo(userCtx, no, fldKey, loadRef);
        }

        /// <summary>
        /// 根据where条件获取业务表单动态对象
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="formId">单据标识</param>
        /// <param name="sqlWhere">主键ID</param>
        /// <param name="sqlParams">参数</param>
        /// <param name="loadRef">是否加载引用数据</param>
        /// <returns>动态对象</returns>
        public static List<DynamicObject> GetBizDataByWhere(this UserContext userCtx,
            string formId,
            string sqlWhere,
            List<SqlParam> sqlParams,
            bool loadRef = false)
        {
            var htmlForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, formId);

            return htmlForm.GetBizDataByWhere(userCtx, sqlWhere, sqlParams, loadRef)?.ToList();
        }

        #region 调用微信项目Api

        /// <summary>
        /// 企业微信消息发送（小程序）
        /// 详情查看：https://work.weixin.qq.com/api/doc/90001/90143/90372#小程序通知消息
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="title">消息标题，必填且长度限制4-12个汉字</param>
        /// <param name="description">消息描述，可NULL。但有内容时，长度限制4-12个汉字</param>
        /// <param name="items">消息内容键值对，最多允许10个item</param>
        /// <param name="page">点击消息卡片后的小程序页面，仅限本小程序内的页面。该字段不填则消息点击后不跳转。</param>
        /// <param name="userNos">麦浩用户编号数组</param>
        /// <returns></returns>
        public static async Task<EwcBaseResponse<object>> EwcSendMsgAsync(this UserContext userCtx, string title, string description,
            Dictionary<string, string> items, string page, IEnumerable<string> userNos)
        {
            return await Task.Run(() =>
            {
                // 根据当前系统用户编号获取企业微信用户信息
                var qywxUsers = UserHelper.GetQyWxInfoByUserName(userCtx, userNos);
                if (qywxUsers.Any() == false) return null;

                // 先取本地，再取总部
                string code = userCtx.Company.GetCustomerByCompanyId().Id ?? userCtx.TopCompanyId.GetCustomerByCompanyId().Id;

                var dto = new EwcSendMsgDTO()
                {
                    Code = code, //userCtx.QyWxCustomerId,
                    Touser = string.Join("|", qywxUsers.Where(s => s.QyWxUserId.IsNullOrEmptyOrWhiteSpace() == false).Select(s => s.QyWxUserId)),
                    Title = title,
                    Description = description,
                    Items = items,
                    Page = page
                };

                // 发送消息
                var ewcResp = EwcJsonClient.Invoke<object>(dto);
                if (!ewcResp.Success)
                {
                    var errMsg = $"调用企业微信API失败：{ewcResp.Code}-{ewcResp.Msg}";
                    userCtx.Container.GetService<ILogServiceEx>().Error(errMsg);
                }

                return ewcResp;
            });
        }

        ///// <summary>
        ///// 获取消费者版小程序二维码
        ///// </summary>
        ///// <param name="userCtx">用户上下文</param>
        ///// <param name="scene">二维码参数，必要</param>
        ///// <param name="page">小程序页面</param>
        ///// <returns></returns>
        //public static EwcBaseResponse<EwcGetConsumerQrCodeData> EwcGetConsumerQrCode(this UserContext userCtx, string scene, string page = null)
        //{
        //    if (scene.IsNullOrEmptyOrWhiteSpace())
        //        throw new BusinessException("scene不能为空！");

        //    var dto = new EwcGetConsumerQrCodeDTO()
        //    {
        //        Code = userCtx.QyWxCustomerId,
        //        Page = page,
        //        Scene = scene
        //    };

        //    // 发送消息
        //    var ewcResp = EwcJsonClient.Invoke<EwcGetConsumerQrCodeData>(dto);
        //    if (!ewcResp.Success)
        //    {
        //        var errMsg = $"调用微信API失败：{ewcResp.Code}-{ewcResp.Msg}";
        //        userCtx.Container.GetService<ILogServiceEx>().Error(errMsg);
        //    }

        //    return ewcResp;
        //}

        #endregion

        /// <summary>
        /// 返回当前用户是否具有某表单某操作的权限
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="formId">表单标识</param>
        /// <param name="permId">权限标识</param>
        /// <returns></returns>
        public static bool HasPermission(this UserContext userCtx, string formId, string permId)
        {
            var htmlForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, formId);

            //没开启权限的表单，不验权
            if (htmlForm.EnableRAC == false)
            {
                return true;
            }

            var permSrv = userCtx.Container.GetService<IPermissionService>();
            return permSrv.HasPermission(userCtx, new PermAuth(userCtx)
            {
                FormId = formId,
                PermId = permId,
            });
        }

        /// <summary>
        /// 获取订单数据范围（意向单/合同单）
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <returns></returns>
        public static string GetOrderDataScope(this UserContext userCtx)
        {
            string sql =
                $"select forderdatascope from t_ydj_userprofile with(nolock) where fuserid='{userCtx.UserId}' and fmainorgid='{userCtx.Company}'";

            string orderDataScope = "all";

            using (var reader = userCtx.Container.GetService<IDBService>().ExecuteReader(userCtx, sql))
            {
                if (reader.Read())
                {
                    orderDataScope = reader.GetValueToString("forderdatascope", "all");
                }
            }

            return orderDataScope;
        }

        ///// <summary>
        ///// 忽略系统数据范围【启用此种写法，部分场景下无效】
        ///// </summary>
        ///// <param name="userCtx">用户上下文</param>
        ///// <param name="roles">原有角色</param>
        //public static void IgnoreSystemDataScope(this UserContext userCtx, out List<string> roles)
        //{
        //    roles = userCtx.UserSession.Roles;

        //    if (userCtx.UserSession.Roles == null)
        //    {
        //        userCtx.UserSession.Roles = new List<string>();
        //    }

        //    if (userCtx.UserSession.Roles.Contains($"admin.{userCtx.Company}") == false)
        //    {
        //        userCtx.UserSession.Roles.Add($"admin.{userCtx.Company}");
        //    }
        //}

        /// <summary>
        /// 恢复系统数据范围
        /// </summary>
        /// <param name="userCtx">用户上下文</param>
        /// <param name="roles">原有角色</param>
        public static void RevertSystemDataScope(this UserContext userCtx, List<string> roles)
        {
            userCtx.UserSession.Roles = roles;
        }

        /// <summary>
        /// 权限部门返回部门和子部门
        /// </summary>
        /// <returns></returns>
        public static List<string> GetCurrentDeptIdsByDept(this UserContext userCtx, string deptid)
        {
            List<string> list = new List<string>();
            string deptsql = string.Format(@"select fid from T_BD_DEPARTMENT with(nolock) where fpath like'%{0}%'", deptid);
            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, deptsql))
            {
                while (reader.Read())
                {
                    list.Add(DBReaderUtils.GetValue<string>(reader, "fid"));
                }
            }
            return list;
        }

        /// <summary>
        /// 获取本部门及下属部门
        /// </summary>
        /// <param name="mydeptId"></param>
        /// <returns></returns>
        public static List<string> GetDeptIds(this UserContext userCtx, string mydeptId)
        {
            List<string> deptlist = new List<string>();
            var sql = @"select fpath,fid from t_bd_department with(nolock) where fpath like @fpath and fmainorgid=@fmainorgid";
            List<SqlParam> pars = new List<SqlParam>();
            pars = new List<SqlParam>();
            pars.Add(new SqlParam("@fpath", System.Data.DbType.String, $@"%{mydeptId}%"));
            pars.Add(new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company));
            var res = userCtx.Container.GetService<IDBService>().ExecuteDynamicObject(userCtx, sql, pars);
            if (res != null && res.Any())
            {
                foreach (var item in res)
                {
                    deptlist.Add(Convert.ToString(item["fid"]));
                }
            }
            return deptlist;
        }

        /// <summary>
        /// 返回代收单位集合
        /// </summary>
        /// <returns></returns>
        public static List<CollectionUnitModel> CollectionUnitList(this UserContext userCtx)
        {
            List<CollectionUnitModel> list = new List<CollectionUnitModel>();
            string strSql = "select * from v_ydj_contactunit where fmainorgid='" + userCtx.Company + "'";
            using (var dr = userCtx.Container.GetService<IDBService>().ExecuteReader(userCtx, strSql))
            {
                while (dr.Read())
                {
                    list.Add(new CollectionUnitModel(
                        dr["fid"].ToString(),
                        dr["fname"].ToString(),
                        dr["ftype"].ToString()
                        ));
                }
            }
            return list;
        }
    }
}
