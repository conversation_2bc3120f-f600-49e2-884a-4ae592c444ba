using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    /// <summary>
    /// 预留单帮助类
    /// </summary>
    public class ReserveBillHelper
    {
        /// <summary>
        /// 获取指定源单关联的预留单信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="sourceForm">源单模型</param>
        /// <param name="sourcePKID">源单编号</param>
        /// <returns>预留单信息</returns>
        public static SaleIntentionReserveBillModel GetSourceReserveBill(UserContext userCtx, HtmlForm sourceForm, string sourcePKID)
        {
            var model = new SaleIntentionReserveBillModel();

            var reserveBill = userCtx.GetReserveBill(sourceForm.Id, sourcePKID);

            if (reserveBill == null) return model;

            model.Id = JNConvert.ToStringAndTrim(reserveBill["id"]);
            model.Number = JNConvert.ToStringAndTrim(reserveBill["fbillno"]);
            model.CreateDate = Convert.ToDateTime(reserveBill["fcreatedate"]);
            model.Products = new List<SaleIntentionReserveBillProductModel>();

            var productObjs = reserveBill["fentity"] as DynamicObjectCollection;
            if (productObjs != null)
            {
                foreach (var productObj in productObjs)
                {
                    //预留数量大于零的行，才需要返回
                    if (Convert.ToDecimal (productObj["fqty"]) >0)
                    {
                        model.Products.Add(new SaleIntentionReserveBillProductModel(productObj));
                    }
                }
            }

            return model;
        }
    }
}