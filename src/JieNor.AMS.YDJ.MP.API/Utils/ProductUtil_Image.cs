using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    /// <summary>
    /// 商品图片帮助类：定义商品图片相关的通用逻辑
    /// </summary>
    public partial class ProductUtil
    {
        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品数据包</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            DynamicObject productObj,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            var fimg = Convert.ToString(productObj?["fimage"]);
            if (StringUtil.CheckURLValid(fimg))
            {
                return new List<BaseImageModel>() { new BaseImageModel {  Id= fimg ,
                     ThumbUrl=fimg,
                      Name=fimg,
                       Url=fimg
                } };
            }
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, null, auxPropVals);
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品数据包</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            DynamicObject productObj,
            string auxPropValId = "",
            string customDesc = "")
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, sqlParam);
        }
        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropVals">辅助属性值列表</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            string productId,
            List<Dictionary<string, string>> auxPropVals = null,
            string customDesc = "")
        {
            var auxPropWhere = GetAuxPropWhere(auxPropVals);
            var productObj = userCtx.LoadBizBillHeadDataById("ydj_product", productId, "fimage,fimage_txt");
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, null, auxPropVals);
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="product">商品对应辅助属性组合值id和定制说明组合字典对象</param>
        /// <returns></returns>
        public static Dictionary<string, List<BaseImageModel>> GetImages(
            UserContext userCtx,
            Dictionary<string, ProOtherInfo> product)
        {
            //var sqlParam = new List<SqlParam>();
            //for (int i = 0; i < product.Keys.Count; i++)
            //{
            //    sqlParam.Add(new SqlParam("@auxPropValId" + i, System.Data.DbType.String, product[product.Keys.ElementAt(i)].auxPropValId));
            //    sqlParam.Add(new SqlParam("@customDesc" + i, System.Data.DbType.String, product[product.Keys.ElementAt(i)].customDesc));
            //}
            //var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            var productIds = product.Select(x => x.Key.Split('-')[0]).ToList();
            var productObjs = userCtx.LoadBizBillHeadDataById("ydj_product", productIds, "fimage,fimage_txt")
                ?.OfType<DynamicObject>()
                ?.ToList();
            return GetImages(userCtx, productObjs, product);
            //return GetImages(userCtx, productObj, auxPropWhere, customDesc, sqlParam);
        }
        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品数据包</param>
        /// <param name="auxPropWhere">辅助属性值过滤条件</param>
        /// <param name="customDesc">定制说明</param>
        /// <param name="paramList">参数列表</param>
        /// <returns>商品图片列表</returns>
        private static Dictionary<string, List<BaseImageModel>> GetImages(
            UserContext userCtx,
            List<DynamicObject> productObjs, Dictionary<string, ProOtherInfo> product)
        {
            Dictionary<string, List<BaseImageModel>> result = new Dictionary<string, List<BaseImageModel>>();

            if (productObjs == null || !productObjs.Any()) return result;

            // 商品图片直接取主图
            foreach (var productObj in productObjs)
            {
                var productId = Convert.ToString(productObj["id"]);
                var imageList = GetProductImages(userCtx, productObj);
                result[productId] = imageList;
            }
            return result;

            var profileService = userCtx.Container.GetService<ISystemProfile>();
            //只取主图
            var onlyProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fonlyproductimg", false);
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company)
            };
            if (onlyProductImg)
            {
                for (int i = 0; i < product.Keys.Count; i++)
                {
                    var kv = product.ElementAt(i);
                    var proObj = productObjs?.FirstOrDefault(x => x["id"].Equals(kv.Key.Split('-')[0]));
                    if (proObj.IsNullOrEmpty())
                    {
                        if (!result.ContainsKey(kv.Key))
                        {
                            result.Add(kv.Key, new List<BaseImageModel>());
                        }
                    }
                    else
                    {
                        if (!result.ContainsKey(kv.Key))
                        {
                            result.Add(kv.Key, GetProductImages(userCtx, proObj));
                        }
                    }
                }
                return result;
            }
            StringBuilder sql = new StringBuilder();
            for (int i = 0; i < product.Keys.Count; i++)
            {
                var kv = product.ElementAt(i);
                var productId = kv.Key.Split('-')[0];
                var proObj = productObjs?.FirstOrDefault(x => x["id"].Equals(productId));
                if (!proObj.IsNullOrEmpty())
                {

                    //sqlParam.Add(new SqlParam("@customDesc" + i, System.Data.DbType.String, product[productId].customDesc));
                    sqlParam.Add(new SqlParam("@fmaterialid" + i, System.Data.DbType.String, productId));
                    if (i > 0)
                    {
                        sql.Append(" union ");
                    }
                    sql.Append($" select top 1  fproductid,'{kv.Value.auxPropValId}' auxPropValId,fimage,fimage_txt from t_ydj_commoditygallery te with(nolock) where " +
                        "te.fmainorgid=@fmainorgid and te.fproductid=@fmaterialid").Append(i);
                    if (!kv.Value.auxPropValId.IsNullOrEmptyOrWhiteSpace())
                    {
                        sqlParam.Add(new SqlParam("@auxPropValId" + i, System.Data.DbType.String, kv.Value.auxPropValId));
                        sql.Append(" and te.fattrinfo=@auxPropValId").Append(i);
                    }
                }
                else
                {
                    continue;
                }
            }
            if (sql.Length > 0)
            {
                var dbService = userCtx.Container.GetService<IDBService>();
                using (var reader = dbService.ExecuteReader(userCtx, sql.ToString(), sqlParam))
                {
                    while (reader.Read())
                    {
                        var image = reader.GetValueToString("fimage");
                        var imageTxt = reader.GetValueToString("fimage_txt");
                        if (!image.IsNullOrEmptyOrWhiteSpace())
                        {
                            var key = reader.GetValueToString("fproductid") + "-" + reader.GetValueToString("auxPropValId");
                            if (!result.ContainsKey(key))
                            {
                                result.Add(key, ImageFieldUtil.ParseImages(image, imageTxt, true));
                            }
                        }
                    }
                }
            }
            var enableProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fenableproductimg", false);
            Dictionary<string, List<Dictionary<string, string>>> auxPropVals = new Dictionary<string, List<Dictionary<string, string>>>();
            foreach (var item in product.Where(x => !result.ContainsKey(x.Key)))
            {
                auxPropVals.Add(item.Key, item.Value.AuxPropVals);
            }
            foreach (var item in auxPropVals)
            {
                var productInfo = productObjs.FirstOrDefault(x => x["id"].Equals(item.Key.Split('-')[0]));
                if (!productInfo.IsNullOrEmpty() && auxPropVals != null && auxPropVals.Count > 0 && !result.ContainsKey(item.Key))
                {
                    result.Add(item.Key, GetMainPropImages(userCtx, productInfo, item.Value));
                }
            }
            foreach (var item in product.Where(x => !result.ContainsKey(x.Key)))
            {
                var productInfo = productObjs.FirstOrDefault(x => x["id"].Equals(item.Key.Split('-')[0]));
                if (!productInfo.IsNullOrEmpty())
                    result.Add(item.Key, GetProductImages(userCtx, productInfo));
            }
            foreach (var item in product.Where(x => !result.ContainsKey(x.Key)))
            {
                result.Add(item.Key, new List<BaseImageModel>());
            }
            return result;
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productId">商品ID</param>
        /// <param name="auxPropValId">商品辅助属性值集ID</param>
        /// <param name="customDesc">定制说明</param>
        /// <returns>商品图片列表</returns>
        public static List<BaseImageModel> GetImages(
            UserContext userCtx,
            string productId,
            string auxPropValId = "",
            string customDesc = "")
        {
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@auxPropValId", System.Data.DbType.String, auxPropValId),
            };
            var auxPropWhere = $" and te.fattrinfo=@auxPropValId";
            var productObj = userCtx.LoadBizBillHeadDataById("ydj_product", productId, "fimage,fimage_txt");
            var fimg = Convert.ToString(productObj?["fimage"]);
            if (StringUtil.CheckURLValid(fimg))
            {
                return new List<BaseImageModel>() { new BaseImageModel {  Id= fimg ,
                     ThumbUrl=fimg,
                      Name=fimg,
                       Url=fimg
                } };
            }
            return GetImages(userCtx, productObj, auxPropWhere, customDesc, sqlParam);
        }

        /// <summary>
        /// 获取商品图片
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品数据包</param>
        /// <param name="auxPropWhere">辅助属性值过滤条件</param>
        /// <param name="customDesc">定制说明</param>
        /// <param name="paramList">参数列表</param>
        /// <returns>商品图片列表</returns>
        private static List<BaseImageModel> GetImages(
            UserContext userCtx,
            DynamicObject productObj,
            string auxPropWhere,
            string customDesc = "",
            List<SqlParam> paramList = null,
            List<Dictionary<string, string>> auxPropVals = null)
        {
            // 商品图片直接取主图
            return GetProductImages(userCtx, productObj);

            customDesc = (customDesc ?? "").Trim();

            var images = new List<BaseImageModel>();

            var productId = Convert.ToString(productObj?["id"]);
            if (productId.IsNullOrEmptyOrWhiteSpace()) return images;

            var profileService = userCtx.Container.GetService<ISystemProfile>();

            //只取主图
            var onlyProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fonlyproductimg", false);
            if (onlyProductImg)
            {
                return GetProductImages(userCtx, productObj);
            }

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, userCtx.Company),
                new SqlParam("@fmaterialid", System.Data.DbType.String, productId)
            };
            if (paramList != null)
            {
                sqlParam.AddRange(paramList);
            }

            var sqlWhere = $@"
            te.fmainorgid=@fmainorgid and te.fproductid=@fmaterialid {auxPropWhere}";

            var sqlText = $@"
            select top 1 fimage,fimage_txt from t_ydj_commoditygallery te with(nolock) 
            where {sqlWhere}";

            var image = "";
            var imageTxt = "";

            var dbService = userCtx.Container.GetService<IDBService>();
            using (var reader = dbService.ExecuteReader(userCtx, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    image = reader.GetValueToString("fimage");
                    imageTxt = reader.GetValueToString("fimage_txt");
                }
            }

            if (!image.IsNullOrEmptyOrWhiteSpace())
            {
                images = ImageFieldUtil.ParseImages(image, imageTxt, true);
            }


            //if (!images.Any() && enableProductImg)
            //{
            //    images = GetProductImages(userCtx, productObj);
            //}

            if (images == null || !images.Any())// 尝试按主要属性取图
            {
                var enableProductImg = profileService.GetSystemParameter(userCtx, "bas_storesysparam", "fenableproductimg", false);
                if (enableProductImg && auxPropVals != null && auxPropVals.Count > 0)
                {
                    images = GetMainPropImages(userCtx, productObj, auxPropVals);
                }
                if (images == null || !images.Any())//最后才取商品主图
                {
                    images = GetProductImages(userCtx, productObj);
                }

            }
            return images;
        }

        /// <summary>
        /// 获取商品主图
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="productObj">商品ID</param>
        /// <returns>商品主图列表</returns>
        private static List<BaseImageModel> GetProductImages(UserContext userCtx, DynamicObject productObj)
        {
            var images = new List<BaseImageModel>();
            if (userCtx == null || productObj == null) return images;

            var image = Convert.ToString(productObj["fimage"]);
            var imageTxt = Convert.ToString(productObj["fimage_txt"]);

            images = ImageFieldUtil.ParseImages(image, imageTxt, true);

            return images;
        }

        /// <summary>
        /// 根据主要属性取商品图库图片
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="productObj"></param>
        /// <returns></returns>
        private static List<BaseImageModel> GetMainPropImages(UserContext userCtx, DynamicObject productObj, List<Dictionary<string, string>> auxPropVals = null)
        {
            //获取主要属性
            var mainauxPropVals = GetMianAuxProp(userCtx, auxPropVals);
            if (mainauxPropVals == null || mainauxPropVals.Count <= 0)
            {
                return null;
            }
            var image = "";//返回的图片地址
            if (auxPropVals != null && auxPropVals.Any())//根据辅助属性值
            {
                List<SqlParam> pars = new List<SqlParam>();
                pars.Add(new SqlParam("@fproductid", System.Data.DbType.String, Convert.ToString(productObj?["id"])));

                var insql = "(";
                // var index = 0;
                foreach (var attrInfo in auxPropVals)
                {
                    // index++;
                    //attrInfo["auxPropId"]
                    // insql += $@"'{attrInfo["auxPropId"]}',";
                    insql += $@"'{attrInfo["auxPropId"]}',";
                    //pars.Add(new SqlParam($@"@insql{index}", System.Data.DbType.String, attrInfo["auxPropId"]));
                }
                insql = insql.TrimEnd(',') + ")";

                var sql = $@" 
select b.fid as auxpropid,a.fvalueid,a.fid,a.fimage,a.fimage_txt,comid from (
    select a1.fauxpropid,b1.fimage,a1.fvalueid,a1.fid,b1.fimage_txt,b1.fid as comid 
    from T_BD_AUXPROPVALUEENTRY as a1 with(nolock) 
    left join T_Ydj_Commoditygallery as b1 with(nolock) on a1.fid=b1.fattrinfo 
    where b1.fproductid=@fproductid
)a left join (
    select fid from t_sel_prop with(nolock) 
    where fid in {insql} and fmainprop='1'
)b on a.fauxpropid=b.fid ";
                var dbService = userCtx.Container.GetService<IDBService>();
                var res = dbService.ExecuteDynamicObject(userCtx, sql, pars);
                if (res != null && res.Any())
                {
                    Dictionary<string, DynamicObjectCollection> objdic = new Dictionary<string, DynamicObjectCollection>();
                    var delcomids = new List<string>();
                    foreach (var item in res)
                    {
                        var fid = Convert.ToString(item["comid"]);
                        var auxpropid = Convert.ToString(item["auxpropid"]);
                        if (!objdic.ContainsKey(fid))
                        {
                            objdic.Add(fid, new DynamicObjectCollection(item.DynamicObjectType));
                        }
                        objdic[fid].Add(item);
                        if (auxpropid.IsNullOrEmpty())
                        {
                            delcomids.Add(fid);
                        }
                    }
                    if (delcomids.Count > 0)//去掉含有非主属性的数据
                    {
                        foreach (var item in delcomids)
                        {
                            objdic.Remove(item);
                        }
                    }

                    if (objdic.Count > 0)
                    {

                        foreach (var item in objdic)//按辅助属性组合值id循环商品图库分组
                        {
                            var equalscount = 0;//匹配成功次数

                            foreach (var obj in item.Value)//循环每个商品图库的辅助属性，把每个商品图片的辅助属性的主属性值和输入的辅助属性值对比，如果包含在内，则返回对应的图片
                            {
                                var newobj = obj as DynamicObject;
                                var auxpropid = Convert.ToString(newobj[0]);
                                var fvalueid = Convert.ToString(newobj[1]);
                                var resdic = new Dictionary<string, string>();
                                resdic.Add("auxPropId", auxpropid);
                                resdic.Add("valueId", fvalueid);
                                resdic.Add("valueName", fvalueid);
                                if (!CheckAuxProp(auxPropVals, resdic))//判断输入的商品主属性是否和商品图库的辅助属性匹配。
                                {
                                    break;
                                }
                                equalscount++;
                            }
                            if (equalscount == mainauxPropVals.Count)//如果每个主属性都相等，则返回该商品图库行的图片
                            {
                                var newobj = item.Value[0] as DynamicObject;
                                image = Convert.ToString(newobj["fimage"]);
                                var imageTxt = Convert.ToString(newobj["fimage_txt"]);
                                return ImageFieldUtil.ParseImages(image, imageTxt, true);
                            }
                        }

                        //如果主要属性取不到值，则通过单个主要属性取值
                        if (mainauxPropVals != null && mainauxPropVals.Count > 0)
                        {
                            var tuple = GetOneMainPropImages(userCtx, auxPropVals, objdic, image, mainauxPropVals);
                            if (tuple != null && !tuple.Item1.IsNullOrEmptyOrWhiteSpace())
                            {
                                return ImageFieldUtil.ParseImages(tuple.Item1, tuple.Item2, true); ;
                            }
                        }

                    }
                }
            }
            return null;
        }

        private static bool CheckAuxProp(List<Dictionary<string, string>> auxPropVals, Dictionary<string, string> resdic)
        {
            foreach (var item in auxPropVals)
            {
                if (item.ContainsKey("auxPropId") && item.ContainsKey("valueId"))
                {
                    if (item["auxPropId"] == resdic["auxPropId"] && item["valueId"] == resdic["valueId"])
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 根据唯一的主要属于获取图片
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        /// <param name="objdic"></param>
        /// <param name="image"></param>
        /// <returns></returns>
        private static Tuple<string, string> GetOneMainPropImages(UserContext userCtx, List<Dictionary<string, string>> auxPropVals, Dictionary<string, DynamicObjectCollection> objdic, string image, List<Dictionary<string, string>> mainauxPropVals)
        {
            if (image.IsNullOrEmptyOrWhiteSpace())
            {
                //从图库结果筛选出只有一个主要属性的记录
                foreach (var item in objdic)
                {
                    var val = item.Value;
                    if (val.Count == 1)//只计算只有一个主要属性的记录
                    {
                        foreach (var item2 in mainauxPropVals)
                        {
                            if (item2.ContainsKey("auxPropId") && item2.ContainsKey("valueId"))
                            {
                                var resdic = val[0] as DynamicObject;
                                if (item2["auxPropId"] == Convert.ToString(resdic["auxpropid"]) && item2["valueId"] == Convert.ToString(resdic["fvalueid"]))
                                {
                                    image = Convert.ToString(resdic["fimage"]);
                                    string image_txt = Convert.ToString(resdic["fimage_txt"]);
                                    return new Tuple<string, string>(image, image_txt);
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 根据请求的属性组合，把非主要属性排除掉
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="auxPropVals"></param>
        /// <returns></returns>
        private static List<Dictionary<string, string>> GetMianAuxProp(UserContext userCtx, List<Dictionary<string, string>> auxPropVals)
        {
            var dbService = userCtx.Container.GetService<IDBService>();
            //筛选主要属性
            var sql2 = "select fid from t_bd_auxproperty with(nolock) where fmainprop='1'";
            var res2 = dbService.ExecuteDynamicObject(userCtx, sql2);
            List<Dictionary<string, string>> mainauxPropVals = new List<Dictionary<string, string>>();
            if (res2 != null && res2.Any())
            {
                List<string> mainpropids = new List<string>();
                foreach (var item in res2)
                {
                    mainpropids.Add(Convert.ToString(item["fid"]));
                }
                foreach (var item in auxPropVals)
                {
                    var auxPropId = item["auxPropId"];
                    if (mainpropids.Contains(auxPropId))
                    {
                        mainauxPropVals.Add(item);
                    }
                }

            }
            return mainauxPropVals;
        }
    }
    public class ProOtherInfo
    {
        /// <summary>
        /// 辅助属性组合值id
        /// </summary>
        public string auxPropValId { get; set; }
        /// <summary>
        /// 辅助属性
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }
        /// <summary>
        /// 定制说明
        /// </summary>
        public string customDesc { get; set; }
    }
}