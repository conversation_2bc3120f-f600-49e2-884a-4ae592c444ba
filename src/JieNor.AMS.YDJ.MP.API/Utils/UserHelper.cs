using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Permission;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Utils
{
    /// <summary>
    /// 用户帮助类
    /// </summary>
    public class UserHelper
    {
        /// <summary>
        /// 获取企业微信信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="userId">用户ID</param>
        /// <returns>企业微信信息</returns>
        public static QyWxUserInfoModel GetQyWxInfoByUserId(UserContext userCtx, string userId)
        {
            if (userId.IsNullOrEmptyOrWhiteSpace()) return null;

            return GetQyWxInfoByUserId(userCtx, new string[] { userId })?.FirstOrDefault();
        }

        /// <summary>
        /// 获取企业微信信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="userIds">用户ID集合</param>
        /// <returns>企业微信信息</returns>
        public static List<QyWxUserInfoModel> GetQyWxInfoByUserId(UserContext userCtx, IEnumerable<string> userIds)
        {
            if (userIds == null || !userIds.Any()) return null;

            //获取本地用户名
            var metaService = userCtx.Container.GetService<IMetaModelService>();
            var userForm = metaService.LoadFormModel(userCtx, "sec_user");
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, userForm?.GetDynamicObjectType(userCtx));
            var localUsers = dm.Select(userIds)?.OfType<DynamicObject>();
            if (localUsers == null || !localUsers.Any()) return null;

            var userNames = localUsers.Select(o => Convert.ToString(o["fnumber"]));

            var qyWxInfo = GetQyWxInfoByUserName(userCtx, userNames);

            //根据用户名映射本地用户ID
            if (qyWxInfo != null)
            {
                foreach (var item in qyWxInfo)
                {
                    var localUser = localUsers.FirstOrDefault(o => Convert.ToString(o["fnumber"]).EqualsIgnoreCase(item.UserName));
                    item.UserId = Convert.ToString(localUser?["id"]);
                }
            }

            return qyWxInfo;
        }

        /// <summary>
        /// 获取企业微信信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="userName">用户名</param>
        /// <returns>企业微信信息</returns>
        public static QyWxUserInfoModel GetQyWxInfoByUserName(UserContext userCtx, string userName)
        {
            if (userName.IsNullOrEmptyOrWhiteSpace()) return null;

            return GetQyWxInfoByUserName(userCtx, new string[] { userName })?.FirstOrDefault();
        }

        /// <summary>
        /// 获取企业微信信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="userNames">用户名集合</param>
        /// <returns>企业微信信息</returns>
        public static List<QyWxUserInfoModel> GetQyWxInfoByUserName(UserContext userCtx, IEnumerable<string> userNames)
        {
            //向认证站点发送请求
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var acResponse = gateway.Invoke(null, TargetSEP.AuthService,
                new CommonFormDTO()
                {
                    FormId = "auth_user",
                    OperationNo = "FetchQyWxInfo",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "userName", string.Join(",", userNames) }
                    }
                }) as DynamicDTOResponse;

            var qyWxInfo = (acResponse?.OperationResult?.SrvData as string)?.FromJson<List<QyWxUserInfoModel>>();

            return qyWxInfo;
        }

        /// <summary>
        /// 获取AC系统用户信息
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="userName">用户名</param>
        /// <returns>用户信息</returns>
        public static CustomUserAuth GetCustomUserAuthByUserName(UserContext userCtx, string userName)
        {
            //向认证站点发送请求
            var gateway = userCtx.Container.GetService<IHttpServiceInvoker>();
            var acResponse = gateway.Invoke(null, TargetSEP.AuthService,
                new CommonFormDTO()
                {
                    FormId = "auth_user",
                    OperationNo = "fetch",
                    SimpleData = new Dictionary<string, string>
                    {
                        { "userName", userName },
                        { "IgnoreCheckPermssion", "true" },
                        { "productId", "".ProductId() },
                        { "productName", "".ProductName() }
                    }
                }) as DynamicDTOResponse;

            var userAuth = (acResponse?.OperationResult?.SrvData as string)?.FromJson<CustomUserAuth>();

            return userAuth;
        }
    }
}