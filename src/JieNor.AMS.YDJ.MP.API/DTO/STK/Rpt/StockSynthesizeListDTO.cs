using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.STK.Rpt
{
    /// <summary>
    /// 库存明细查询取数接口
    /// </summary>
    [Api("库存明细查询取数接口")]
    [Route("/mpapi/stocksynthesize/list")]
    [Authenticate]
    public class StockSynthesizeListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 仓库ID
        /// </summary>
        public string StorehouseId { get; set; }

        /// <summary>
        /// 品类ID
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 品牌ID集合
        /// </summary>
        public List<string> BrandIds { get; set; }

        /// <summary>
        /// 风格ID集合
        /// </summary>
        public List<string> StyleIds { get; set; }

        /// <summary>
        /// 空间ID集合
        /// </summary>
        public List<string> SpaceIds { get; set; }

        ///// <summary>
        ///// 最低价
        ///// </summary>
        //public decimal MinPrice { get; set; }

        ///// <summary>
        ///// 最高价
        ///// </summary>
        //public decimal MaxPrice { get; set; }

        /// <summary>
        /// 系列id
        /// </summary>
        public string SeriesId { get; set; }

        /// <summary>
        /// 是否排除物流跟踪号商品  0不排除 1排除 默认传0
        /// </summary>
        public int IsFilterFmtono { get; set; }

        /// <summary>
        /// 商品id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 库存状态id
        /// </summary>
        public string StockStatusId { get; set; }

        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }


        /// <summary>
        /// 来源业务单据标识
        /// </summary>
        public string SrcFormId { get; set; }


        /// <summary>
        /// 单据类型名称
        /// </summary>
        public string BillTypeName { get; set; }

        /// <summary>
        /// 单据类型编码
        /// </summary>
        public string BillTypeNo { get; set; }

        public DateTime Orderdate { get; set; }

        /// <summary>
        /// 查询数据类型 self:本组织数据，parentorg:上级组织
        /// </summary>
        public string SelectDataType { get; set; } = "self";
    }
}
