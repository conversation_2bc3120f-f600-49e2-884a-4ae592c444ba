using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 首页售后待办及本月售后服务数接口
    /// </summary>
    [Api("首页售后待办及本月售后服务数接口")]
    [Route("/mpapi/dashboard/feedbacklist")]
    [Authenticate]
    public class DashboardFeedBackDTO : BaseDTO
    {
        /// <summary>
        /// 权限项【myself：查看本人,mydepartment：查看本部门,mycompany：查看本企业】
        /// </summary>
        public string dataPerm { get; set; }
    }
}