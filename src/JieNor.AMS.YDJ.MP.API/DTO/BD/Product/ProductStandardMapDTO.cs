using JieNor.AMS.YDJ.Core;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商品标准品映射匹配接口
    /// </summary>
    [Api("商品标准品映射匹配接口")]
    [Route("/mpapi/product/standardmap")]
    [Authenticate]
    public class ProductStandardMapDTO : BaseDetailDTO
    {
        /// <summary>
        /// 属性列表
        /// </summary>
        public List<PropEntity> PropList { get; set; }

        /// <summary>
        /// 是否返回多个匹配到的配件，默认只返回商品分类下的一个配件
        /// </summary>
        public bool IsMuiltiParts { get; set; }

        /// <summary>
        /// 是否非标
        /// </summary>
        public bool IsNonStandard { get; set; }
    }
}