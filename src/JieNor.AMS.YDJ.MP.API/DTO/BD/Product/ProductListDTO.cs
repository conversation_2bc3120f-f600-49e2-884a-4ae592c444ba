using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商品列表取数接口
    /// </summary>
    [Api("商品列表取数接口")]
    [Route("/mpapi/product/list")]
    [Authenticate]
    public class ProductListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 品类ID
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 品牌ID集合
        /// </summary>
        public List<string> BrandIds { get; set; }

        /// <summary>
        /// 风格ID集合
        /// </summary>
        public List<string> StyleIds { get; set; }

        /// <summary>
        /// 空间ID集合
        /// </summary>
        public List<string> SpaceIds { get; set; }

        /// <summary>
        /// 最低价
        /// </summary>
        public decimal MinPrice { get; set; }

        /// <summary>
        /// 最高价
        /// </summary>
        public decimal MaxPrice { get; set; }

        /// <summary>
        /// 清库存
        /// </summary>
        public bool IsClearStock { get; set; }
        /// <summary>
        /// 系列
        /// </summary>
        public string SeriesId { get; set; }

        /// <summary>
        /// 来源业务单据标识
        /// </summary>
        public string SrcFormId { get; set; }


        /// <summary>
        /// 单据类型名称
        /// </summary>
        public string BillTypeName { get; set; }

        /// <summary>
        /// 单据类型编码
        /// </summary>
        public string BillTypeNo { get; set; }


        /// <summary>
        /// 部门id
        /// </summary>
        public string DeptId { get; set; }
        /// <summary>
        /// 部门名称
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public string DeptNo { get; set; }
        /// <summary>
        /// 业务日期（取价根据业务日期去取而非当前如期）
        /// </summary>
        public DateTime Orderdate { get; set; }

    }
}