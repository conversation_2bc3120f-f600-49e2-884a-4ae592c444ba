using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商品销售详情取数接口（购物车、意向单、合同单）
    /// </summary>
    [Api("商品销售详情取数接口")]
    [Route("/mpapi/product/saledetail")]
    [Authenticate]
    public class ProductSaleDetailDTO
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 是否非标选配
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }

        /// <summary>
        /// 业务表单标识：购物车、意向单、合同单
        /// </summary>
        public string FormId { get; set; }

        /// <summary>
        /// 业务表单数据ID：购物车ID、意向单ID、合同单ID
        /// </summary>
        public string OrderId { get; set; }

        /// <summary>
        /// 销售单据明细行ID：意向单明细行ID、合同单明细行ID
        /// </summary>
        public string EntryId { get; set; }

        /// <summary>
        /// 销售部门用于判断业绩品牌
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 单据类型
        /// </summary>
        public string Billtype { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public string fbiztype { get; set; }

        /// <summary>
        /// 生产要求
        /// </summary>
        public string ProdRequirement { get; set; }

        /// <summary>
        /// 家纺套件要求
        /// </summary>
        public string SelSuiteRequire { get; set; }
    }
}