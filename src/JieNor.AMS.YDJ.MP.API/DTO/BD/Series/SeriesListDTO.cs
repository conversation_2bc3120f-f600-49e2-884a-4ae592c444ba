using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.BD.Series
{

    /// <summary>
    /// 系列列表取数接口
    /// </summary>
    [Api("系列列表取数接口")]
    [Route("/mpapi/series/list")]
    [Authenticate]
    public class SeriesListDTO: BaseListPageDTO
    {
        /// <summary>
        /// 品牌id
        /// </summary>
        public string BrandId { get; set; }
    }
}
