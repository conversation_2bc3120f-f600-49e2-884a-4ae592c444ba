using JieNor.AMS.YDJ.MP.API.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 客户保存接口数据传输对象
    /// </summary>
    [Api("客户保存接口")]
    [Route("/mpapi/customer/save")]
    [Authenticate]
    [RepeatedSubmitFilter("ydj_customer")]
    public class CustomerSaveDTO : BaseDataSaveDTO
    {
        /// <summary>
        /// 企业微信用户编码
        /// </summary>
        public string WorkWxUserid { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 客户旧手机号 #35637任务
        /// </summary>
        public string OldPhone { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string Wechat { get; set; }

        /// <summary>
        /// 所在区域 - 省份
        /// </summary>
        public string Province { get; set; }

        /// <summary>
        /// 所在区域 - 城市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 所在区域 - 区县
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 年龄段
        /// </summary>
        public string Age { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 楼盘
        /// </summary>
        public string House { get; set; }

        /// <summary>
        /// 合作渠道
        /// </summary>
        public string Channel { get; set; }

        /// <summary>
        /// 客户类型（ftype）
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 客户分类（fcustomertype）
        /// </summary>
        public string CustomerType { get; set; }
        /// <summary>
        /// 负责人id
        /// </summary>
        public string DutyId { get; set; }
        /// <summary>
        /// 部门id
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        ///  国家ID
        /// </summary>
        public string CountryId { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public List<BaseImageModel> Images { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 推荐人
        /// </summary>
        public string Referrer { get; set; }

        /// <summary>
        /// 来源部门
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// 客户性质（'cusnature_00':'线索','cusnature_01':'意向','cusnature_02':'成交'）
        /// </summary>
        public string Nature { get; set; }
        
        /// <summary>
        /// 开票信息
        /// </summary>
        public List<InvoiceEntry> InvoiceEntries { set; get; }

    }

    public class InvoiceEntry
    {
        /// <summary>
        /// 行Id
        /// </summary>
        public string Id { set; get; }
        
        /// <summary>
        /// 是否默认
        /// </summary>
        public bool IsDefault { get; set; }
        
        /// <summary>
        /// 开票类型 '01':'增值税专用发票','02':'增值税普通发票'
        /// </summary>
        public string InvoiceType { get; set; }
        
        /// <summary>
        /// 购买方全称
        /// </summary>
        public string BuyerFullName { get; set; }
        
        /// <summary>
        /// 纳税人识别号
        /// </summary>
        public string TaxpayerIdentify { get; set; }
        
        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string InvoiceEmail { get; set; }
        
        /// <summary>
        /// 地址
        /// </summary>
        public string InvoiceAddress { get; set; }
        
        /// <summary>
        /// 电话
        /// </summary>
        public string InvoicePhone { get; set; }
        
        /// <summary>
        /// 开户银行
        /// </summary>
        public string DepositBankName { get; set; }
        
        /// <summary>
        /// 银行账户
        /// </summary>
        public string BankAccount { get; set; }
    }
}