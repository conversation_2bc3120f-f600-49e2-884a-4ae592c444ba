using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 楼盘列表取数接口
    /// </summary>
    [Api("楼盘列表取数接口")]
    [Route("/mpapi/building/list")]
    [Authenticate]
    public class BuildingListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 省
        /// </summary>
        public string ProvinceId { get; set; }

        /// <summary>
        /// 市
        /// </summary>
        public string CityId { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public string RegionId { get; set; }

        /// <summary>
        /// 我创建的
        /// </summary>
        public bool IsMy { get; set; }
    }
}