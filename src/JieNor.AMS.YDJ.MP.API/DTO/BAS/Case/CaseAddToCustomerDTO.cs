using JieNor.AMS.YDJ.MP.API.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 经典案例收藏至客户
    /// </summary>
    [Api("经典案例收藏至客户接口")]
    [Route("/mpapi/case/addtocustomer")]
    [Authenticate]
    public class CaseAddToCustomerDTO : BaseDTO
    {
        /// <summary>
        /// 案例单唯一id
        /// </summary>
        public string CaseId { get; set; }

        /// <summary>
        /// 商机唯一id
        /// </summary>
        public string CustomerRecordId { get; set; }
    }
}
