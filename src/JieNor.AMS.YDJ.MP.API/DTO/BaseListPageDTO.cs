using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 列表分页数据传输对象基类
    /// </summary>
    public class BaseListPageDTO : BaseListDTO
    {
        private int _PageIndex;
        /// <summary>
        /// 当前页码，默认第1页
        /// </summary>
        public int PageIndex
        {
            get { return _PageIndex; }
            set { _PageIndex = value < 1 ? 1 : value; }
        }

        private int _PageSize;
        /// <summary>
        /// 每页条数，默认每页10条
        /// </summary>
        public int PageSize
        {
            get { return _PageSize; }
            set { _PageSize = value < 1 ? 10 : value; }
        }
    }
}