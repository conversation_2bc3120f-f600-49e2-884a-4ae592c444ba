using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 床垫选配单列表接口
    /// </summary>
    [Api("床垫选配单列表接口")]
    [Route("/mpapi/aibedorder/list")]
    [Authenticate]
    //[CheckPermissionFilter("ydj_customerrecord", "fw_view")]
    public class AIBedOrderListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 是否转单
        /// </summary>
        public bool IsPushOrder { get; set; }

    }
}