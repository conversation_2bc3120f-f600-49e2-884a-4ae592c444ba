using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO.EWC
{

    /// <summary>
    /// 微信小程序：获取企业微信权限签名的接口
    /// </summary>
    [Route("/qywxopen/getsignature")]

    public class EwcSendSignatureDTO
    {
        /// <summary>
        /// 随机字符串
        /// </summary>
        public string noncestr { get; set; }
        /// <summary>
        /// 时间戳
        /// </summary>
        public string timestamp { get; set; }
        /// <summary>
        /// url
        /// </summary>
        public string url { get; set; }
    }

    /// <summary>
    /// 临时登录凭证校验接口响应数据对象
    /// </summary>
    public class EwcSendSignatureData
    {


        /// <summary>
        /// 签名
        /// </summary>
        public string Signature { get; set; }


    }


}