using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 收款单列表取数接口
    /// </summary>
    [Api("收款单列表取数接口")]
    [Route("/mpapi/receipt/list")]
    [Authenticate]
    [CheckPermissionFilter("coo_incomedisburse", "fw_view")]
    public class ReceiptListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 业务状态  bizstatus_01待确认  bizstatus_02已确认  bizstatus_03已红冲
        /// </summary>
        public string BizStatus { get; set; }

        /// <summary>
        /// 审核状态 true 为已核审 false 未核审
        /// </summary>
        public bool AuditStatus { get; set; }

        /// <summary>
        /// 数据状态 A关联暂存 B创建 C重新审核 D已提交 E已审核 (查询多个状态以‘，’逗号隔开) 备注：需求更改，先弃用此参数
        /// </summary>         
        public string StatusList { get; set; }

        /// <summary>
        /// bizdirection_01：收入 bizdirection_02：支出
        /// </summary>
        public string bizdirection { get; set; }
    }
}