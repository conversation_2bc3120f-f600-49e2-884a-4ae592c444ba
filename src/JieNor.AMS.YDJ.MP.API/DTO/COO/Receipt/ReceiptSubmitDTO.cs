using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO.COO.Receipt
{
    [Api("收款单提交接口")]
    [Route("/mpapi/receipt/submit")]
    [Authenticate]
    [CheckBillLockFilter("coo_incomedisburse", "submit")]
    public class ReceiptSubmitDTO : CheckBillLockDTO
    {

        ///// <summary>
        ///// 收款记录fid
        ///// </summary>
        //public string Id { get; set; }
    }
}
