using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.COO.IncomeDisburse
{
    /// <summary>
    /// 检查源单是否整单关闭
    /// </summary>
    [Api("检查源单是否整单关闭")]
    [Route("/mpapi/incomedisburse/checksourceorder")]
    [Authenticate]
    public class CheckSourceOrderDTO
    {
        public string ids { get; set; }
    }
}
