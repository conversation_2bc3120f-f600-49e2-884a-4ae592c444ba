using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 列表数据传输对象基类
    /// </summary>
    public class BaseListDTO : BaseDTO
    {
        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// 排序依据：按什么规则排序，比如按价格排序
        /// </summary>
        public string Sortby { get; set; }

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        private string _Sortord = "desc";

        /// <summary>
        /// 排序方式：升序为 asc，降序为 desc，默认为 desc
        /// </summary>
        public string Sortord
        {
            get { return _Sortord; }
            set { _Sortord = string.IsNullOrWhiteSpace(value) ? "desc" : value; }
        }

        /// <summary>
        /// 来源业务对象标识（从哪个业务界面查找的数据，比如从销售合同上查询员工信息，则srcformid=ydj_order）
        /// </summary>
        public string SrcFormId { get; set; }

        /// <summary>
        /// 来源业务控制字段（从哪个业务界面查找的数据，比如从销售合同上查询员工信息，则SrcFidId=fdutyid）
        /// </summary>
        public string SrcFidId { get; set; }
    }
}