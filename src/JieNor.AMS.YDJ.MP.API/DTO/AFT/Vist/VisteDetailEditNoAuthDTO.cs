using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 回访单评价操作接口（免授权）
    /// </summary>
    [Api("回访单评价操作接口（免授权）")]
    [Route("/mpapi/vist/detaileditnoauth")]
    public class VisteDetailEditNoAuthDTO: BaseNotAuthDTO
    {
        /// <summary>
        /// 服务单id
        /// </summary>
        public string serId { get; set; }
        ///// <summary>
        ///// 售后反馈单id，暂注释【如果要加上记得考虑售后反馈单未新增服务单的逻辑，此时没有直接间接回访单】
        ///// </summary>
        //public string feedBackId { get; set; }
    }
}
