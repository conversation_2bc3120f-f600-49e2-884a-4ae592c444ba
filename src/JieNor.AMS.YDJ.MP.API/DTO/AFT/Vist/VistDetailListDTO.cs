using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 服务单回访评价列表接口
    /// </summary>
    [Api("服务单回访评价列表接口")]
    [Route("/mpapi/vist/vistdetaillist")]
    [Authenticate]
    public class VistDetailListDTO /*: BaseListPageDTO*/
    {
        /// <summary>
        /// 服务单ID
        /// </summary>
        public string serId { get; set; }
    }
}
