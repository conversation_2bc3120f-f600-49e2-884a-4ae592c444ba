using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 打印模板列表接口
    /// </summary>
    [Api("打印模板列表接口")]
    [Route("/mpapi/common/printtemplatelist")]
    [Authenticate]
    public class PrintTemplateListDTO : BaseDTO
    {
        public string SourceType { get; set; }

        public bool ExportExcel { get; set; }

    }
}