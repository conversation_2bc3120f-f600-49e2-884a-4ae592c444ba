using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.Common.Duty
{
    /// <summary>
    ///更换负责人接口
    /// </summary>
    [Api("更换负责人接口")]
    [Route("/mpapi/common/dutyreplace")]
    [Authenticate]
    public class DutyReplaceDTO
    {
        /// <summary>
        /// 单据id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 源负责人信息id
        /// </summary>
        public string SourceId { get; set; }
        /// <summary>
        /// 部门id
        /// </summary>
        public string DeptId { get; set; }
        /// <summary>
        /// 目标负责人id
        /// </summary>
        public string DutyId { get; set; }

        /// <summary>
        ///  表单识别id
        /// </summary>
        public string FormId { get; set; }
    }
}
