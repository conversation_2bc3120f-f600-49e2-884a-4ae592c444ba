using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 修改密码接口
    /// </summary>
    [Api("修改密码接口")]
    [Route("/mpapi/user/modifypwd")]
    [Authenticate]
    public class ModifyPwdDTO
    {
        /// <summary>
        /// 旧密码
        /// </summary>
        public string OldPwd { get; set; }

        /// <summary>
        /// 新密码
        /// </summary>
        public string NewPwd { get; set; }

        /// <summary>
        /// 确认新密码
        /// </summary>
        public string NewRePwd { get; set; }
    }
}