using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 服务单预约保存接口
    /// </summary>
    [Api("服务单预约保存接口")]
    [Route("/mpapi/servicereservation/save")]
    [Authenticate]
    public class ServiceReservationSaveDTO : BaseDataSaveDTO
    {
        

        /// <summary>
        /// 预约时间
        /// </summary>
        public DateTime ReservationTime { get; set; }

       
        
    }
}
