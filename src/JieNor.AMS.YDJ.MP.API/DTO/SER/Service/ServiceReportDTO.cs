using JieNor.AMS.YDJ.MP.API.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 服务单完工确认接口
    /// </summary>
    [Api("服务单完工确认接口")]
    [Route("/mpapi/service/report")]
    [Authenticate]
    public class ServiceReportDTO : BaseDataSaveDTO
    {
        public string Reporter { get; set; }
        //服务单标识
        public string Id { get; set; }
        /// <summary>
        /// 完工现场图片
        /// </summary>
        public List<BaseImageModel> Images { get; set; } = new List<BaseImageModel>();
        /// <summary>
        /// 完工说明
        /// </summary>
        public string Discription { get; set; }
    }
}
