using JieNor.AMS.YDJ.MP.API.Filter;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 服务单列表接口
    /// </summary>
    [Api("服务单列表取数接口")]
    [Route("/mpapi/service/list")]
    [Authenticate]
    public class ServiceListDTO: BaseListPageDTO
    {
        /// <summary>
        /// 是否金管家尊享卡
        /// </summary>
        public string isGoldStewardCard { get; set; }

        /// <summary>
        /// 服务状态（空：全部、1：待派单、2：待预约、3：待完工、4：待评价、5：已关闭、6：已取消）
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 服务类型（全部：空；送装：1；增值：2；售后：3；）
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 创建时间开始时间
        /// </summary>
        public DateTime bgnDate { get; set; }
        /// <summary>
        /// 创建时间结束时间
        /// </summary>
        public DateTime endDate { get; set; }
        /// <summary>
        /// 服务人员名称
        /// </summary>
        public string staffName { get; set; }
        /// <summary>
        /// 服务团队名称
        /// </summary>
        public string teamName { get; set; }
        /// <summary>
        /// 作废状态，1：已作废，0未作废 空不限
        /// </summary>
        public string cancelStatus { get; set; } 
    }
}
