using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 首页销售目标接口
    /// </summary>
    [Api("首页销售目标接口")]
    [Route("/mpapi/dashboard/salegoal")]
    [Authenticate]
    public class DashboardSaleGoalDTO : BaseDTO
    {
        /// <summary>
        /// 时间维度（本月、本季度、本年）
        /// </summary>
        public string DateType { get; set; }

        /// <summary>
        /// 数据范围权限
        /// myself：本人
        /// mydepartment：本部门
        /// mysubordinates：本部门及下属部门
        /// mycompany：本企业
        /// </summary>
        public string DataPerm { get; set; }

        /// <summary>
        /// 考核维度
        /// fal_001：开单额（销售额）
        /// fal_002：意向额
        /// fal_003：商机数
        /// </summary>
        public string EvalType { get; set; }
    }
}
