using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 售后工程师列表取数接口
    /// </summary>
    [Api("售后工程师列表取数接口")]
    [Route("/mpapi/engineerlist/list")]
    [Authenticate]
    public class FeedBackEngineerListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 送达方id
        /// </summary>
        public string deliverId { get; set; }
    }
}