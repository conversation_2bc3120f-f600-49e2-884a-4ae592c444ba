using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO.SER.Service
{
    /// <summary>
    /// 售后反馈单编辑取数接口
    /// </summary>
    [Api("售后反馈单编辑取数接口")]
    [Route("/mpapi/feedback/edit")]
    [Authenticate]
    [ApplyBillLockFilter("ste_afterfeedback")]
    public class AfterFeedBackEditDTO : BaseDetailDTO, IApplyBillLockDTO
    {
    }
}
