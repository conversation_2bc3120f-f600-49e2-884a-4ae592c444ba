using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using ServiceStack;
using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 售后反馈单保存接口数据传输对象
    /// </summary>
    [Api("售后反馈单保存接口数据传输对象")]
    [Route("/mpapi/feedback/save")]
    [Authenticate]
    [RepeatedSubmitFilter("ste_afterfeedback")]
    [CheckBillLockFilter("ste_afterfeedback", "save")]
    public class AfterFeedBackSaveDTO : BaseDataSaveDTO
    {
        /// <summary>
        /// 售后反馈单id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 粗略基本信息
        /// </summary>
        public AfterFeedBackSaveInfo BaseInfo { get; set; }
        ///// <summary>
        ///// 问题商品id
        ///// </summary>
        //public string ProductId { get; set; }
        /// <summary>
        /// 问题类别id
        /// </summary>
        public string QuestionTypeId { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        public string QuestionDesc { get; set; }
        /// <summary>
        /// 问题图片
        /// </summary>
        public List<BaseImageModel> QuestionImgs { get; set; }
        /// <summary>
        /// 责任单位类型
        /// </summary>
        public string WorkTypeId { get; set; }
        /// <summary>
        /// 责任单位
        /// </summary>
        public string WorkInfo { get; set; }
        /// <summary>
        /// 内部处理结论
        /// </summary>
        public string ConclusionId { get; set; }
        /// <summary>
        /// 内部处理方式
        /// </summary>
        public string Scheme { get; set; }
        ///// <summary>
        ///// 总部处理结论
        ///// </summary>
        public string HqHandleConclusion { get; set; }
        ///// <summary>
        ///// 总部处理方式
        ///// </summary>
        public string HqScheme { get; set; }

        ///// <summary>
        ///// 创建时间
        ///// </summary>
        public string CreatDate { get; set; }

        /// <summary>
        /// 返厂申请
        /// </summary>
        public FeedBackReturnSaveInfo ReturnApplication { get; set; }
        /// <summary>
        /// 商品信息
        /// </summary>
        public List<AftProduct> Products { get; set; }
    }
    /// <summary>
    /// 售后服务单粗略基本信息
    /// </summary>
    public class AfterFeedBackSaveInfo
    {
        #region 粗略基本信息
        /// <summary>
        /// 经销商
        /// </summary>
        public string AgentId { get; set; }
        /// <summary>
        /// 反馈时间
        /// </summary>
        public string Date { get; set; }
        /// <summary>
        /// 售后人员
        /// </summary>
        public string StaffId { get; set; }
        /// <summary>
        /// 售后人员电话
        /// </summary>
        public string StaffPhone { get; set; }
        /// <summary>
        /// 售后部门
        /// </summary>
        public string DeptId { get; set; }
        /// <summary>
        /// 合同信息
        /// </summary>
        public string OrderId { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        public string CustomerId { get; set; }
        /// <summary>
        /// 省
        /// </summary>
        public string Province { get; set; }

        /// <summary>
        /// 市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public string Region { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string LinkStaff { get; set; }
        /// <summary>
        /// 联系人电话
        /// </summary>
        public string LinkMobile { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        ///// <summary>
        ///// 售后状态
        ///// </summary>
        //public ComboDataModel FeedStatus { get; set; }
        /// <summary>
        /// 源单类型
        /// </summary>
        public string SourceType { get; set; }
        /// <summary>
        /// 源单编号
        /// </summary>
        public string SourceNumber { get; set; }
        /// <summary>
        /// 售后工程师
        /// </summary>
        public string EngineerId { get; set; }
        /// <summary>
        /// 商品品类
        /// </summary>
        public string CategoryId { get; set; }
        /// <summary>
        /// 送达方
        /// </summary>
        public string Deliver { get; set; }

        ///// <summary>
        ///// 受理时间
        ///// </summary>
        //public string AcceptDate { get; set; }
        ///// <summary>
        ///// 转总部时间
        ///// </summary>
        //public string TransferDate { get; set; }
        ///// <summary>
        ///// 总部审核时间
        ///// </summary>
        //public string HqAuditDate { get; set; }
        ///// <summary>
        ///// 完成时间
        ///// </summary>
        //public string FinishDate { get; set; }
        ///// <summary>
        ///// 关闭时间
        ///// </summary>
        //public string CloseDate { get; set; }
        #endregion
    }
    /// <summary>
    /// 售后服务单返厂申请
    /// </summary>
    public class FeedBackReturnSaveInfo
    {
        ///// <summary>
        ///// 城市
        ///// </summary>
        //public string City { get; set; }
        /// <summary>
        /// 授权城市
        /// </summary>
        public string AuthCity { get; set; }
        /// <summary>
        /// 工程师电话
        /// </summary>
        public string EngineerTel { get; set; }
        ///// <summary>
        ///// 产品型号
        ///// </summary>
        //public string SeltypeName { get; set; }
        ///// <summary>
        ///// 尺寸规格
        ///// </summary>
        //public string Specifica { get; set; }
        ///// <summary>
        ///// 商品数量
        ///// </summary>
        //public int ProCount { get; set; }
        /// <summary>
        /// 返厂售后类型
        /// </summary>
        public string FeedTypeId { get; set; }
        /// <summary>
        /// 产品返厂原因
        /// </summary>
        public string ReturnReson { get; set; }
        /// <summary>
        /// 退货地址
        /// </summary>
        public string EngineerAddress { get; set; }
        /// <summary>
        /// 详细退货地址
        /// </summary>
        public string CusreturnAdd { get; set; }
        /// <summary>
        /// SAP订单号
        /// </summary>
        public string SapNumber { get; set; }
        /// <summary>
        /// 到货日期
        /// </summary>
        public string ArrivalDate { get; set; }
        ///// <summary>
        ///// 总部审核日期--基本信息已给
        ///// </summary>
        //public string HqauditDate { get; set; }
        /// <summary>
        /// 送客户家日期
        /// </summary>
        public string ServiceDate { get; set; }
        /// <summary>
        /// 返厂日期
        /// </summary>
        public string ReturnDate { get; set; }
        /// <summary>
        /// 客户使用时长
        /// </summary>
        public string CususeTime { get; set; }
        /// <summary>
        /// 返厂物流单号
        /// </summary>
        public string ReturnlogNum { get; set; }
    }
    /// <summary>
    /// 商品信息
    /// </summary>
    public class AftProduct
    {
        /// <summary>
        /// 如果是8位随机码，即为新建
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 商品Id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 辅助属性id
        /// </summary>
        public /*List<Dictionary<string, string>> */ string Attrinfo { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }

        /// <summary>
        /// 基本单位数量
        /// </summary>
        public decimal Qty { get; set; }
        /// <summary>
        /// 成交金额
        /// </summary>
        public decimal Amount { get; set; }

        ///// <summary>
        ///// 单价
        ///// </summary>
        //public decimal Price { get; set; }
    }
}
