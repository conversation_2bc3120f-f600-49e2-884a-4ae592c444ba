using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 售后反馈单根据合同id获取城市信息接口
    /// </summary>
    [Api("售后反馈单根据合同id获取城市信息接口")]
    [Route("/mpapi/feedback/getcityinfo")]
    [Authenticate]
    public class AfterFeedGetCityDTO
    {
        /// <summary>
        /// 合同id
        /// </summary>
        public string OrderId { get; set; }
    }
}
