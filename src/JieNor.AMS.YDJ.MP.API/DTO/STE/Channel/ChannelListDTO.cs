using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 合作渠道列表取数接口
    /// </summary>
    [Api("合作渠道列表取数接口")]
    [Route("/mpapi/channel/list")]
    [Authenticate]
    public class ChannelListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 渠道类型（空：表示全部）
        /// </summary>
        public string Type { get; set; }
    }
}