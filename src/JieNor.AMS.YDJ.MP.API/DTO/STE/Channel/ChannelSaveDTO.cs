using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 合作渠道保存接口
    /// </summary>
    [Api("合作渠道保存接口")]
    [Route("/mpapi/channel/save")]
    [Authenticate]
    public class ChannelSaveDTO : BaseDataSaveDTO
    {
        /// <summary>
        /// 类型id
        /// </summary>
        public string TypeId { get; set; }

        /// <summary>
        /// 合作情况id
        /// </summary>
        public string CooperationId { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 所属公司
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// 省id
        /// </summary>
        public string ProvinceId { get; set; }

        /// <summary>
        /// 市id
        /// </summary>
        public string CityId { get; set; }

        /// <summary>
        /// 区域id
        /// </summary>
        public string RegionId { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        public string Bank { get; set; }

        /// <summary>
        /// 账户名称
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// 银行卡号
        /// </summary>
        public string BankNumber { get; set; }
        /// <summary>
        /// 合作主体类型 '0':'个人','1':'企业'
        /// </summary>
        public string ChannelType { get; set; }
        /// <summary>
        /// 银行代码
        /// </summary>
        public string BankCode { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string Idcard { get; set; }
        /// <summary>
        /// Sap供应商编码
        /// </summary>
        public string Suppliercode { get; set; }

        /// <summary>
        /// 合作门店id
        /// </summary>
        //public string DeptId { get; set; }

        /// <summary>
        /// 负责人id
        /// </summary>
       // public string StaffId { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        ///  是否领用 0否 1是
        /// </summary>
        public int IsClaim { get; set; }
        /// <summary>
        /// 负责人列表
        /// </summary>
        public List<DutyDetailDto> StaffList { get; set; }

        /// <summary>
        /// 是否编辑负责人列表 0否 1是
        /// </summary>
        public int IsEditStaffList { get; set; }

        /// <summary>
        /// 证件
        /// </summary>
        public List<BaseImageModel> MulImages { get; set; }
        
        /// <summary>
        /// 抽佣比例
        /// </summary>
        public List<ChannelEntry> ChannelEntryList { get; set; }
        
    }

    /// <summary>
    /// 负责人详细
    /// </summary>
    public class DutyDetailDto
    {

        /// <summary>
        ///  id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public BaseDataSimpleModel Duty { get; set; }

        /// <summary>
        ///  部门
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; }

        /// <summary>
        ///  加入时间
        /// </summary>
        public DateTime JoinTime { get; set; }
        
        /// <summary>
        /// 返佣比例明细
        /// </summary>
        public List<ChannelEntry> ChannelEntryList { get; set; }

    }

    /// <summary>
    /// 返佣比例
    /// </summary>
    public class ChannelEntry
    {
        /// <summary>
        /// 行ID
        /// </summary>
        public string Id { set; get; }
        
        /// <summary>
        /// 金额下限 flowerlimit
        /// </summary>
        public decimal LowerLimitAmount { set; get; }
        
        /// <summary>
        /// 提成比例：fratio
        /// </summary>
        public decimal Ratio { set; get; }
        
        /// <summary>
        /// 金额上限：fupperlimit
        /// </summary>
        public decimal UpperLimitAmount { set; get; }
        
    }
}