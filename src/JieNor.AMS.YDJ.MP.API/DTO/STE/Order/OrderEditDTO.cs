using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 合同单编辑取数接口
    /// </summary>
    [Api("合同单编辑取数接口")]
    [Route("/mpapi/order/edit")]
    [Authenticate]
    [ApplyBillLockFilter("ydj_order")]
    public class OrderEditDTO : BaseDetailDTO, IApplyBillLockDTO
    {

    }
}