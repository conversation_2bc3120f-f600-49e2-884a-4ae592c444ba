using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;
using JieNor.AMS.YDJ.MP.API.Model;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 填充促销活动商品
    /// </summary>
    [Api("填充促销活动商品")]
    [Route("/mpapi/order/fillPromotionData")]
    [Authenticate]
    public class FillPromotionDataDTO : BaseDTO
    {
        /// <summary>
        /// 销售合同数据包
        /// </summary>
        public JObject BillData { get; set; }

        /// <summary>
        /// 促销活动id
        /// </summary>
        public List<string> PromotionIds { get; set; } = new List<string>();

        /// <summary>
        /// 促销活动类型（ydj_productpromotion、ydj_combopromotion）
        /// </summary>
        public string PromotionType { get; set; }

        /// <summary>
        /// 选择的赠品（仅商品促销使用）
        /// {
        ///     "fmaterialid": "",
        ///     "fattrinfo": "",
        ///     "funitid": "",
        ///     "fqty": 1
        /// }
        /// </summary>
        public JArray Gifts { get; set; } = new JArray();
    }
}