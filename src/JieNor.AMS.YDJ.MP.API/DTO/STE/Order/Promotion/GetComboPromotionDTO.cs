using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;
using JieNor.AMS.YDJ.MP.API.Model;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 获取匹配的组合套餐
    /// </summary>
    [Api("获取匹配的组合套餐")]
    [Route("/mpapi/order/getComboPromotion")]
    [Authenticate]
    public class GetComboPromotionDTO : BaseListPageDTO
    {
        /// <summary>
        /// 业务日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 销售部门
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 来源表单
        /// </summary>
        public string SourceFormId { get; set; }
    }
}