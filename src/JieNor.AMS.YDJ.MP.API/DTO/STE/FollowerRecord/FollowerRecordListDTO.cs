using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 跟进记录列表取数接口
    /// </summary>
    [Api("跟进记录列表取数接口")]
    [Route("/mpapi/followerrecord/list")]
    [Authenticate]
    public class FollowerRecordListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 源单标识
        /// </summary>
        public string SourceNumber { get; set; }

        /// <summary>
        /// 源单类型
        /// </summary>
        public string SourceType { get; set; }
    }
}