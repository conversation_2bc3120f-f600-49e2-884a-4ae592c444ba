using JieNor.AMS.YDJ.MP.API.DTO.SEL.Suite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SEL.Combine
{
    /// <summary>
    /// 配件组合
    /// </summary>
    public class CombineBaseDTO
    {
        /// <summary>
        /// 商品信息
        /// </summary>
        public BaseDataDTO Product { get; set; }

        /// <summary>
        /// 子件列表
        /// </summary>
        public List<PartsBaseDTO> Parts { get; set; }

        //当前商品类别 所有层级是否含有沙发类
        public bool IsSofaCategory { get; set; }
    }
}
