using JieNor.AMS.YDJ.Core;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 加载或创建非标属性值接口数据传输对象
    /// </summary>
    [Api("加载或创建非标属性值接口")]
    [Route("/mpapi/propvalue/loadorcreate")]
    [Authenticate]
    public class PropValueLoadOrCreateDTO
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 是否是非标选配
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 属性列表
        /// </summary>
        public List<PropEntity> PropList { get; set; }
    }
}