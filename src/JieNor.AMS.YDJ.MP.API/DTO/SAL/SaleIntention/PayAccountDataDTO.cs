using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO.SAL.SaleIntention
{
    /// <summary>
    /// 收款账号
    /// </summary>
    [Api("收款账号")]
    [Route("/mpapi/saleintention/payaccountdata")]
    [Authenticate]
    public class PayAccountDataDTO
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string Id { get; set; }
    }
}
