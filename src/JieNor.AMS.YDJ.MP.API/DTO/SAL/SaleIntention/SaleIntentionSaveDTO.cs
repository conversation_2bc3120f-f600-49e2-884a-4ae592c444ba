using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using Newtonsoft.Json.Linq;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 意向单保存接口
    /// </summary>
    [Api("意向单保存接口")]
    [Route("/mpapi/saleintention/save")]
    [Authenticate]
    [RepeatedSubmitFilter("ydj_saleintention")]
    public class SaleIntentionSaveDTO : RepeatedSubmitDTO
    {
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 销售员
        /// </summary>
        public string StaffId { get; set; }

        /// <summary>
        /// 销售门店
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 交货时间
        /// </summary>
        public DateTime? PickDate { get; set; }

        /// <summary>
        /// 客户Id
        /// </summary>
        public string CustomerId { get; set; }

        ///// <summary>
        ///// 收货地址
        ///// </summary>
        //public string AddressId { get; set; }

        /// <summary>
        /// 源单类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 源单单号
        /// </summary>
        public string SourceNumber { get; set; }

        ///// <summary>
        ///// 订单总额（ffbillamount）
        ///// </summary>
        //public decimal BillAmount { get; set; }

        ///// <summary>
        ///// 剩余收款（freceiptamount）
        ///// </summary>
        //public decimal ReceiptAmount { get; set; }

        ///// <summary>
        ///// 已结算金额（freceivedamount）
        ///// </summary>
        //public decimal ReceivedAmount { get; set; }

        ///// <summary>
        ///// 收款确认中（fconfirmamount）
        ///// </summary>
        //public decimal ConfirmAmount { get; set; }

        /// <summary>
        /// 应收定金（fcollectamount）
        /// </summary>
        public decimal CollectAmount { get; set; }

        ///// <summary>
        ///// 已收定金（fcollectedamount）
        ///// </summary>
        //public decimal CollectedAmount { get; set; }

        ///// <summary>
        ///// 确认收款（fconfirmedamount）
        ///// </summary>
        //public decimal ConfirmedAmount { get; set; }
        public string ChannelId { get; set; }
        /// <summary>
        /// 商品
        /// </summary>
        public List<SaleIntentionProductEntrySaveDTO> Products { get; set; }
    }

    public class SaleIntentionProductEntrySaveDTO
    {
        /// <summary>
        /// 当前商品是否是新增的
        /// </summary>
        public bool IsNew
        {
            get
            {
                return this.Id.IsNullOrEmptyOrWhiteSpace() || this.Id.Length <= 32;
            }
        }

        /// <summary>
        /// 如果是8位随机码，即为新建
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 商品Id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 零售价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 成交价
        /// </summary>
        public decimal DealPrice { get; set; }

        /// <summary>
        /// 折率
        /// </summary>
        public decimal DistRate { get; set; }

        /// <summary>
        /// 提货方式
        /// </summary>
        public string DeliveryModeId { get; set; }

        /// <summary>
        /// 提现货
        /// </summary>
        public bool IsOutSpot { get; set; }

        /// <summary>
        /// 商品备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }
        /// <summary>
        /// 仓库
        /// </summary>
        public string StoreHouseId { get; set; }
        /// <summary>
        /// 仓位
        /// </summary>
        public string StoreLocationId { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public string StockStatus { get; set; }
        /// <summary>
        /// 空间
        /// </summary>
        public string Space { get; set; }

        /// <summary>
        /// 商品图片
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; }
    }
}