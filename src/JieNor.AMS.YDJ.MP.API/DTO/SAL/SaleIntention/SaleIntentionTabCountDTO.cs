using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 意向单Tab数量取数接口
    /// </summary>
    [Api("意向单Tab数量取数接口")]
    [Route("/mpapi/saleintention/tabcount")]
    [Authenticate]
    public class SaleIntentionTabCountDTO : BaseDTO
    {
        /// <summary>
        /// 数据范围
        /// mysql：我的
        /// all：所有
        /// </summary>
        public string DataScope { get; set; } = "all";
    }
}