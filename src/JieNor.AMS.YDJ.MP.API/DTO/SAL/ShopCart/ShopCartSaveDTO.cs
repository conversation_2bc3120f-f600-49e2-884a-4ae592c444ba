using JieNor.AMS.YDJ.MP.API.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 购物车保存接口数据传输对象
    /// </summary>
    [Api("购物车保存接口")]
    [Route("/mpapi/shopcart/save")]
    [Authenticate]
    public class ShopCartSaveDTO
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { get; set; } = string.Empty;

        //业绩品牌
        public string ResultBrandId { get; set; }

        /// <summary>
        /// 辅助属性值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; } = string.Empty;

        /// <summary>
        /// 经销价
        /// </summary>
        public decimal SellPrice { get; set; }

        /// <summary>
        /// 经销总价（数量*经销价）其实就是经销金额
        /// </summary>
        public decimal SellAmount { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 成交单价
        /// </summary>
        public decimal DealPrice { get; set; }

        /// <summary>
        /// 折率
        /// </summary>
        public decimal DistRate { get; set; } = 10;

        /// <summary>
        /// 提货方式
        /// </summary>
        public string DeliveryMode { get; set; }

        /// <summary>
        /// 出现货
        /// </summary>
        public bool IsOutSpot { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string StoreHouseId { get; set; }

        /// <summary>
        ///仓位
        /// </summary>
        public string StoreLocationId { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public string fbiztype { get; set; }

        /// <summary>
        /// 空间
        /// </summary>
        public string Space { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public string StockStatus { get; set; }

        /// <summary>
        /// 选配套件
        /// </summary>
        public bool IsSuite { get; set; }

        /// <summary>
        /// 是否非标
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 是否可添加配件
        /// </summary>
        public bool IsAddibleParts { get; set; }

        /// <summary>
        /// 套件商品ID
        /// </summary>
        public string SuiteProductId { get; set; }

        /// <summary>
        /// 套件说明
        /// </summary>
        public string SuiteDesc { get; set; }

        /// <summary>
        /// 套件组合号
        /// </summary>
        public string SuitCombNumber { get; set; }

        /// <summary>
        /// 配件组合号
        /// </summary>
        public string PartsCombNumber { get; set; }

        /// <summary>
        /// 是否自动组合配件
        /// </summary>
        public bool IsAutoCombParts { get; set; }

        /// <summary>
        /// 是否组合主商品
        /// </summary>
        public bool IsCombMain { get; set; }

        /// <summary>
        /// 子件数量
        /// </summary>
        public decimal SubQty { get; set; }

        /// <summary>
        /// 配件类型
        /// </summary>
        public string PartType { get; set; }

        /// <summary>
        /// 配件数量
        /// </summary>
        public decimal PartQty { get; set; }

        /// <summary>
        /// 沙发组合号
        /// </summary>
        public string SofaCombNumber { get; set; }

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; }

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool IsGiveaway { get; set; }

        /// <summary>
        /// 生产要求
        /// </summary>
        public string ProdRequirement { get; set; }

        /// <summary>
        /// 家纺套件要求
        /// </summary>
        public string SelSuiteRequire { get; set; }

        /// <summary>
        /// "套件总件数"属性。是套件商品时才返回。
        /// </summary>
        public string SuiteSumQtyPropId { get; set; }

        /// <summary>
        /// 子件属性
        /// </summary>
        public string PartPropId { get; set; }
    }
}