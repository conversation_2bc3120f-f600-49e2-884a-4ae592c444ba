using JieNor.AMS.YDJ.MP.API.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 购物车批量保存接口数据传输对象
    /// </summary>
    [Api("购物车批量保存接口")]
    [Route("/mpapi/shopcart/batchsave")]
    [Authenticate]
    public class ShopCartBatchSaveDTO
    {
        public List<ShopCartSaveDTO> List { get; set; }
    }
}