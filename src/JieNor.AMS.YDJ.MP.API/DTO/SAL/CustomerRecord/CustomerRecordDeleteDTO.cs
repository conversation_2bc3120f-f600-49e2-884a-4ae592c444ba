using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商机删除接口
    /// </summary>
    [Api("商机删除接口")]
    [Route("/mpapi/customerrecord/delete")]
    [Authenticate]
    public class CustomerRecordDeleteDTO : BaseDTO
    {
        public string Id { get; set; }
    }
}