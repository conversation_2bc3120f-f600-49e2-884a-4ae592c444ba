using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Filter;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商机列表取数接口
    /// </summary>
    [Api("商机列表取数接口")]
    [Route("/mpapi/customerrecord/list")]
    [Authenticate]
    [CheckPermissionFilter("ydj_customerrecord", "fw_view")]
    public class CustomerRecordListDTO : BaseListPageDTO
    {
        /// <summary>
        /// 商机Tab类型（意向跟进：intention；方案设计：design；合同签订：order；商机公海：pool；已关闭：close）
        /// </summary>
        public string Type { get; set; }

        ///// <summary>
        ///// 商机阶段
        ///// </summary>
        //public string Phase { get; set; }

        /// <summary>
        /// 日期类型（followtime、unfollowtime）
        /// </summary>
        public string DateType { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 需求等级Id
        /// </summary>
        public string DemandId { get; set; }

        /// <summary>
        /// 客户来源Id
        /// </summary>
        public string CustomerSourceId { get; set; }

        /// <summary>
        /// 渠道id
        /// </summary>
        public string ChannelId { get; set; }

        /// <summary>
        /// 日期类型： 近一年 0 , 今年1,去年2, 全部3
        /// </summary>
        public int DateRange { get; set; }

        /// <summary>
        /// 负责人姓名（模糊匹配）
        /// </summary>
        public string DutyName { get; set; }

    }
}