using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 商机指派/领用接口
    /// </summary>
    [Api("商机指派/领用接口")]
    [Route("/mpapi/customerrecord/claim")]
    [Authenticate]
    public class CustomerRecordClaimDTO: BaseDTO
    {
        public string Id { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public string DutyId { get; set; }

        /// <summary>
        /// 门店
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 是否领用
        /// </summary>
        public bool IsReceiving { get; set; }
    }
}
