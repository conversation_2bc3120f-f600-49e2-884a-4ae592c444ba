using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 名片分享接口
    /// </summary>
    [Api("名片分享接口")]
    [Route("/mpapi/customerrecord/sharecard")]
    [Authenticate]
    public class CustomerRecordShareCardDTO : BaseDTO
    {
        /// <summary>
        /// 商机id
        /// </summary>
        public string Id { get; set; }
    }
}