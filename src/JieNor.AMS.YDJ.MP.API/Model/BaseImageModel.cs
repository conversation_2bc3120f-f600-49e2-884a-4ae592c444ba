using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 图片数据模型基类
    /// </summary>
    public class BaseImageModel
    {
        /// <summary>
        /// 图片哈希值
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 图片文件名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 原图地址
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 缩略图地址
        /// </summary>
        public string ThumbUrl { get; set; } = string.Empty;
    }
}
