using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.Utils;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商机编辑数据模型
    /// </summary>
    public class CustomerRecordEditModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new string Name { get; set; }

        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 商机类型（1：公司、2：个人）
        /// </summary>
        public string TypeId { get; set; } = "2";

        /// <summary>
        /// 负责人
        /// </summary>
        public BaseDataSimpleModel Duty { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 门店
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 接待时间
        /// </summary>
        public DateTime GoshopDate { get; set; } = BeiJingTime.Today;


        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string Wechat { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public ComboDataModel CustomerSource { get; set; } = new ComboDataModel();

        /// <summary>
        /// 合作渠道
        /// </summary>
        public BaseDataSimpleModel Channel { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 性别
        /// </summary>
        public ComboDataModel Gender { get; set; } = new ComboDataModel();

        /// <summary>
        /// 年龄段
        /// </summary>
        public ComboDataModel Age { get; set; } = new ComboDataModel();

        /// <summary>
        /// 楼盘名称
        /// </summary>
        public BaseDataSimpleModel House { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        ///  国家或地区
        /// </summary>
        public ComboDataModel Country { get; set; } = new ComboDataModel();

        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();

        /// <summary>
        /// 所在区域
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 客户档案
        /// </summary>
        public CustomerRecordCustomerModel Customer { get; set; } = new CustomerRecordCustomerModel();

        #region 需求信息 

        /// <summary>
        /// 需求等级
        /// </summary>
        public ComboDataModel Demand { get; set; } = new ComboDataModel();

        /// <summary>
        /// 预算范围
        /// </summary>
       public ComboDataModel Budget { get; set; } = new ComboDataModel();

        /// <summary>
        /// 预计成交日期
        /// </summary>
        public DateTime? ExpectDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 房屋面积
        /// </summary>
        public ComboDataModel Area { get; set; } = new ComboDataModel();

        /// <summary>
        /// 风格
        /// </summary>
        public ComboDataModel Style { get; set; } = new ComboDataModel();

        /// <summary>
        /// 室
        /// </summary>
        public ComboDataModel Room { get; set; } = new ComboDataModel();

        /// <summary>
        /// 厅
        /// </summary>
        public ComboDataModel Hall { get; set; } = new ComboDataModel();

        /// <summary>
        /// 卫
        /// </summary>
        public ComboDataModel Toilet { get; set; } = new ComboDataModel();

        /// <summary>
        /// 阳台
        /// </summary>
        public ComboDataModel Balcony { get; set; } = new ComboDataModel();

        /// <summary>
        /// 空间
        /// </summary>
        public ComboDataModel Space { get; set; } = new ComboDataModel();

        /// <summary>
        /// 装修进度
        /// </summary>
        public ComboDataModel Renovation { get; set; } = new ComboDataModel();

        /// <summary>
        /// 需求类型
        /// </summary>
        public ComboDataModel RequirementType { get; set; } = new ComboDataModel();

        /// <summary>
        /// 产品尺寸
        /// </summary>
        public string ProductSize { get; set; }

        /// <summary>
        /// 是否有意向
        /// </summary>
        public object IntentionSign { get; set; }

        /// <summary>
        /// 是否愿意到店
        /// </summary>
        public object WillVisitType { get; set; }

        /// <summary>
        /// 是否到店
        /// </summary>
        public object VisitType { get; set; }

        /// <summary>
        /// 是否紧急
        /// </summary>
        public object UrgencyType { get; set; }

        /// <summary>
        /// 是否已交房
        /// </summary>
        public object DeliveryType { get; set; }

        /// <summary>
        /// 是否添加微信
        /// </summary>
        public object AddWechatType { get; set; }

        #endregion

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public List<BaseImageModel> Images { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 防撞组别
        /// </summary>
        public ComboDataModel SaleCategory { get; set; } = new ComboDataModel();

        /// <summary>
        /// 企业微信用户编码
        /// </summary>
        public string WorkWxUserid { get; set; }

        /// <summary>
        /// 意向品类
        /// </summary>
        public string ProductType { get; set; }

        /// <summary>
        /// 意向品类名称
        /// </summary>
        public string ProductTypeName { get; set; }

        public List<Dictionary<string, object>> prodList { get; set; }

        /// <summary>
        /// 推荐人
        /// </summary>
        public BaseDataSimpleModel Referrer { get; set; }
    }

    public class DeptArea: BaseDataSimpleModel
    {
        /// <summary>
        ///  国家或地区
        /// </summary>
        public ComboDataModel Country { get; set; } = new ComboDataModel();

        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();
    }
}