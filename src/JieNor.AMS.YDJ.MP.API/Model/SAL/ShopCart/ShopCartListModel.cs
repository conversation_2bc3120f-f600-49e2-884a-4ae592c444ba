using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 购物车列表数据模型
    /// </summary>
    public class ShopCartListModel
    {
        /// <summary>
        /// 购物车记录ID
        /// </summary>
        /// 
        public string Id { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 商品编码
        /// </summary>
        public string Number { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 辅助属性组合值ID
        /// </summary>
        public string AuxPropValId { get; set; }

        /// <summary>
        /// 规格型号：其实是辅助属性组合值
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }

        /// <summary>
        /// 商品主图Url
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 是否允许选配
        /// </summary>
        public bool IsPresetProp { get; set; }
        /// <summary>
        /// 选配标记
        /// </summary>
        public bool IsPartFlag { get; set; }

        /// <summary>
        /// 选配套件
        /// </summary>
        public bool IsSuite { get; set; }

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Dataorigin { get; set; }

        /// <summary>
        /// 是否允许下单
        /// </summary>
        public bool IsAllowOrders { get; set; }

        /// <summary>
        /// 零售价可编辑
        /// </summary>
        public bool IsEditPrice { get; set; }

        /// <summary>
        /// 是否可添加配件
        /// </summary>
        public bool IsAddibleParts { get; set; }

        /// <summary>
        /// 经销价
        /// </summary>
        public decimal SellPrice { get; set; }

        /// <summary>
        /// 经销总价（数量*经销价）其实就是经销金额
        /// </summary>
        public decimal SellAmount { get; set; }

        /// <summary>
        /// 厂家指导价
        /// </summary>
        public decimal GuidePrice { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 折率
        /// </summary>
        public decimal DistRate { get; set; }

        /// <summary>
        /// 成交单价
        /// </summary>
        public decimal DealPrice { get; set; }

        /// <summary>
        /// 提货方式
        /// </summary>
        public ComboDataModel DeliveryMode { get; set; }

        /// <summary>
        /// 出现货
        /// </summary>
        public bool IsOutSpot { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 套件商品ID
        /// </summary>
        public string SuiteProductId { get; set; }

        /// <summary>
        /// 套件说明
        /// </summary>
        public string SuiteDesc { get; set; }

        /// <summary>
        /// 套件组合号
        /// </summary>
        public string SuitCombNumber { get; set; }

        /// <summary>
        /// 配件组合号
        /// </summary>
        public string PartsCombNumber { get; set; }

        /// <summary>
        /// 是否自动组合配件
        /// </summary>
        public bool IsAutoCombParts { get; set; }

        /// <summary>
        /// 是否组合主商品
        /// </summary>
        public bool IsCombMain { get; set; }

        /// <summary>
        /// 子件数量
        /// </summary>
        public decimal SubQty { get; set; }

        /// <summary>
        /// 配件类型
        /// </summary>
        public string PartType { get; set; }

        /// <summary>
        /// 配件数量
        /// </summary>
        public decimal PartQty { get; set; }

        /// <summary>
        /// 子件描述
        /// </summary>
        public string PartDesc { get; set; }

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool IsGiveaway { get; set; }

        /// <summary>
        /// 沙发组合号
        /// </summary>
        public string SofaCombNumber { get; set; }

        public ComboDataModel fbiztype { get; set; } = new ComboDataModel();

        public BaseDataSimpleModel ProductCategory { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 仓库
        /// </summary>
        public ComboDataModel StoreHouse { get; set; } = new ComboDataModel();
        /// <summary>
        /// 仓位
        /// </summary>
        public ComboDataModel StoreLocation { get; set; } = new ComboDataModel();

        /// <summary>
        /// 库存状态
        /// </summary>
        public ComboDataModel StockStatus { get; set; } = new ComboDataModel();

        /// <summary>
        /// 空间
        /// </summary>
        public ComboDataModel Space { get; set; } = new ComboDataModel();

        /// <summary>
        /// 品牌
        /// </summary>
        public ComboDataModel Brand { get; set; } = new ComboDataModel();

        /// <summary>
        /// 系列
        /// </summary>
        public ComboDataModel Series { get; set; } = new ComboDataModel();
        /// <summary>
        /// 业绩品牌
        /// </summary>
        public ComboDataModel ResultBrand { get; set; } = new ComboDataModel();

        /// <summary>
        /// 生产要求
        /// </summary>
        public ComboDataModel ProdRequirement { get; set; }

        /// <summary>
        /// 家纺套件要求
        /// </summary>
        public ComboDataModel SelSuiteRequire { get; set; }

        /// <summary>
        /// 当前商品类别 所有层级是否含有沙发类
        /// </summary>
        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// "套件总件数"属性。是套件商品时才返回。
        /// </summary>
        public ComboDataModel SuiteSumQtyProp { get; set; }

        /// <summary>
        /// 子件属性
        /// </summary>
        public ComboDataModel PartProp { get; set; }
    }
}