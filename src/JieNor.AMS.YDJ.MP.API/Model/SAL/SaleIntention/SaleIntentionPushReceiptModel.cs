using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.Model.BAS.CollectingUnit;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 意向收款编辑数据模型
    /// </summary>
    public class SaleIntentionPushReceiptModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new string Name { get; set; }

        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 源单类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 源单id
        /// </summary>
        public string SourceId { get; set; }

        /// <summary>
        /// 源单单号    fsourcenumber
        /// </summary>
        public string SourceNumber { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public CustomerSimpleModel Customer { get; set; }

        /// <summary>
        /// fdeptid
        /// </summary>
        public string DeptId { get; set; }

        /// <summary>
        /// 已收款   fsettledamount
        /// </summary>
        public decimal SettledAmount { get; set; }

        /// <summary>
        /// 未收款   funsettleamount
        /// </summary>
        public decimal UnsettleAmount { get; set; }

        /// <summary>
        /// 确认收款
        /// </summary>
        public decimal ConfirmedAmount { get; set; }

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 结算方式
        /// </summary>
        public ComboDataModel Way { get; set; } = new ComboDataModel();

        /// <summary>
        /// 结算日期
        /// </summary>
        public DateTime Date = BeiJingTime.Today;

        /// <summary>
        /// 代收单位
        /// </summary>
        public List<CollectionUnitModel> CollectionUnit { get; set; } = new List<CollectionUnitModel>();

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }
        /// <summary>
        /// 是否协同 0否 1是
        /// </summary>
        public bool IsSyn { get; set; }
        /// <summary>
        /// 货款金额
        /// </summary>
        public decimal LoanAmount { get; set; }

    }
}