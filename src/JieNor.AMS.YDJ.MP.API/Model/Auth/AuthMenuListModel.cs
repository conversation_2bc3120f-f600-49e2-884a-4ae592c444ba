using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class AuthTabbarModel
    {
        public string Tabbar { get; set; }
        public int TabbarSort { get; set; }
        public List<AuthGroupModel> Groups { get; set; }
    }

    public class AuthGroupModel
    {
        public string Group { get; set; }
        public int GroupSort { get; set; }
        public List<AuthMenuModel> menus { get; set; }
    }

    public class AuthMenuModel
    {
        public string Id { get; set; }
        public string Number { get; set; }
        public string Name { get; set; }
        public int Sort { get; set; }
        public bool IsShortcut { get; set; }
        public string Icon { get; set; }
        public bool IsAllow { get; set; }
        public string Url { get; set; }
    }
}