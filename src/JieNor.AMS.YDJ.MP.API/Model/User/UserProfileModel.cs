using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 用户信息数据模型
    /// </summary>
    public class UserProfileModel
    {
        /// <summary>
        /// 用户头像
        /// </summary>
        public string AvatarUrl { get; set; }

        /// <summary>
        /// 用户标识
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        public string Job { get; set; }

        /// <summary>
        /// 微信号
        /// </summary>
        public string Wechat { get; set; }

        /// <summary>
        /// QQ号
        /// </summary>
        public string qq { get; set; }

        /// <summary>
        /// 用户关联的员工
        /// </summary>
        public BaseDataSimpleModel Employee { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 用户关联的部门
        /// </summary>
        public BaseDataSimpleModel Department { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 用户关联的岗位
        /// </summary>
        public BaseDataSimpleModel Position { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 用户当前登录的企业
        /// </summary>
        public CompanyDataModel Company { get; set; } = new CompanyDataModel();

        /// <summary>
        /// 订单数据范围（意向单/合同单）
        /// </summary>
        public string OrderDataScope { get; set; }
    }

    public class CompanyDataModel : BaseDataSimpleModel
    {
        /// <summary>
        /// 组织编码
        /// </summary>
        public string orgNumber { get; set; } = string.Empty;

        /// <summary>
        /// 组织编码
        /// </summary>
        public string orgName { get; set; } = string.Empty;

        /// <summary>
        /// 实控人
        /// </summary>
        public string ActualControl { get; set; } = string.Empty;

        public CompanyDataModel()
        {

        }

        public override string ToString()
        {
            return $"{this.Id} {this.Number} {this.Name} {this.ActualControl} {this.orgNumber} {this.orgName}";
        }
    }
}