using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.IM.Notice
{
    /// <summary>
    /// 公告列表数据模型
    /// </summary>
    public class NoticeListModel
    {
        /// <summary>
        ///  标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        ///  发布时间
        /// </summary>
        public DateTime Sendtime { get; set; }

        /// <summary>
        ///  发布人
        /// </summary>
        public string Senduser { get; set; }

        /// <summary>
        ///  阅读状态 0未读 1已读
        /// </summary>
        public int ReadStatus { get; set; }
        /// <summary>
        /// 公告Id
        /// </summary>
        public string Id { get; set; }
    }

}
