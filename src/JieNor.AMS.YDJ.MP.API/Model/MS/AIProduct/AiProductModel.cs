using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework.IoC;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.Model.MS.AIProduct
{
    public class AiProductModel
    {
        /// <summary>
        /// 商品ID
        /// </summary>
        public string ProductId { set; get; } = string.Empty;

        /// <summary>
        /// 是否为推荐的床垫
        /// </summary>
        public bool IsRecommend { set; get; } = false;

        /// <summary>
        /// 编码
        /// </summary>
        public string Number { set; get; } = string.Empty;

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { set; get; } = string.Empty;

        /// <summary>
        /// 销售价格
        /// </summary>
        public decimal SalPrice { set; get; }

        // /// <summary>
        // /// 辅助属性值
        // /// </summary>
        // public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 辅助属性列表  
        /// </summary>
        //[IgnoreDataMember]
        public List<AuxPropInfo> AuxPropInfos { get; set; }

        //public JArray AuxPropInfoJarray { set; get; }

        /*/// <summary>
        /// 图片地址
        /// </summary>*/
        //public List<string> ImgList { set; get; }

        /// <summary>
        /// 主图片
        /// </summary>
        public string ImgUrl { set; get; } = string.Empty;

        /// <summary>
        /// 排序，根据AI床垫辅助资料来判断排序  
        /// </summary>
        public int Orderby { set; get; } = 0;

        /// <summary>
        /// 支撑层名称
        /// </summary>
        public string SustainLayer { set; get; }

        /// <summary>
        /// 属性和属性值的组合
        /// </summary>
        public string AuxPropsString { set; get; }

        /// <summary>
        /// 选配类别ID
        /// </summary>
        [IgnoreDataMember]
        public string SelectionCategoryId { set; get; }

        public AiProductModel()
        {
            AuxPropInfos = new List<AuxPropInfo>();
        }

    }

    public class AuxPropInfo
    {
        /// <summary>
        /// 属性ID
        /// </summary>
        //[IgnoreDataMember]
        public string PropId { set; get; } = string.Empty;

        /// <summary>
        /// 属性名称
        /// </summary>
        public string PropName { set; get; } = string.Empty;

        /// <summary>
        /// 属性值名称
        /// </summary>
        public string PropValueName { set; get; }

        /// <summary>
        /// 属性值ID
        /// </summary>
        public string PropValueId { set; get; }


        //[IgnoreDataMember]
        //public List<AuxPropValueInfo> AuxPropValueInfos { get; set; } = new List<AuxPropValueInfo>();

        /*/// <summary>
        /// 属性值编码
        /// </summary>
        public string ValueNumber { get; set; } = string.Empty;

        /// <summary>
        /// 属性值名称
        /// </summary>
        public string ValueName { get; set; } = string.Empty;*/
    }

    public class AuxPropValueInfo
    {
        /// <summary>
        /// 属性值编码
        /// </summary>
        //[IgnoreDataMember]
        public string ValueNumber { set; get; }

        /// <summary>
        /// 属性值名称
        /// </summary>
        public string ValueName { set; get; }


        /// <summary>
        /// 属性值是否是非标
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 是否是默认属性值
        /// </summary>
        public bool IsDefVal { get; set; }

        /// <summary>
        /// 属性值是否是由非标录入时生成的
        /// </summary>
        public bool IsNosuitCreate { get; set; }
    }
}