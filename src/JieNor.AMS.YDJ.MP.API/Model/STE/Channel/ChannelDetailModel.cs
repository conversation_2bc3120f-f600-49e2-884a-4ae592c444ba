using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 合作渠道详情数据模型
    /// </summary>
    public class ChannelDetailModel : BaseDataModel
    {
        public ComboDataModel Status { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public ComboDataModel Type { get; set; } = new ComboDataModel();

        /// <summary>
        /// 合作情况
        /// </summary>
        public ComboDataModel Cooperation { get; set; } = new ComboDataModel();

        /// <summary>
        /// 累计带单金额
        /// </summary>
        public decimal SumBillAmount { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 所属公司
        /// </summary>
        public string Company { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 区域（省市区）
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        public string Bank { get; set; }

        /// <summary>
        /// 账户名称
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// 银行卡号
        /// </summary>
        public string BankNumber { get; set; }
        /// <summary>
        /// 合作主体类型 '0':'个人','1':'企业'
        /// </summary>
        public string ChannelType { get; set; }
        /// <summary>
        /// 银行代码
        /// </summary>
        public string BankCode { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string Idcard { get; set; }
        /// <summary>
        /// Sap供应商编码
        /// </summary>
        public string Suppliercode { get; set; }

        /// <summary>
        /// 合作门店
        /// </summary>
     //   public BaseDataSimpleModel Dept { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 负责人
        /// </summary>
     //   public BaseDataSimpleModel Staff { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }
        /// <summary>
        /// 证件
        /// </summary>
        public List<BaseImageModel> MulImages { get; set; } = new List<BaseImageModel>();
        
        public List<DutyDetail> StaffList { get; set; } = new List<DutyDetail>();
        /// <summary>
        /// 累计佣金
        /// </summary>
        public decimal Planbrokerage { get; set; }
        /// <summary>
        /// 未结佣金
        /// </summary>
        public decimal OpenPlanbrokerage { get; set; }

        /// <summary>
        /// 返佣比例明细
        /// </summary>
        public List<ChannelEntry> ChannelEntryList { get; set; } = new List<ChannelEntry>();
    }

    /// <summary>
    /// 负责人详细
    /// </summary>
    public class DutyDetail
    {

        /// <summary>
        ///  id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public BaseDataSimpleModel Duty { get; set; }

        /// <summary>
        ///  部门
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; }

        /// <summary>
        ///  加入时间
        /// </summary>
        public DateTime JoinTime { get; set; }

    }
}