using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 首页任务数据模型
    /// </summary>
    public class DashboardTaskModel
    {
        public List<BaseCountModel> TaskList { get; set; } = new List<BaseCountModel>();
    }
}