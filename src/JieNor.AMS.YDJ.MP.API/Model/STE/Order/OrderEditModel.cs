using JieNor.AMS.YDJ.MP.API.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 合同单编辑数据模型
    /// </summary>
    public class OrderEditModel
    {
        public string Id { get; set; }

        public string Number { get; set; }

        public DateTime CreateDate { get; set; }

        //需转单
        public bool Needtransferorder { get; set; }
        //是否销售转单
        public bool IsSaletransferorder { get; set; }

        /// <summary>
        /// 是否二级分销合同(fisresellorder)
        /// </summary>
        public bool IsSecOrder { get; set; }
        /// <summary>
        /// 关闭状态：0 未关闭、1 已关闭
        /// </summary>
        public ComboDataModel CloseState { get; set; }

        /// <summary>
        /// 数据状态
        /// </summary>
        public ComboDataModel Status { get; set; } = new ComboDataModel();

        /// <summary>
        /// 手工单号
        /// </summary>
        public string Within { get; set; }

        /// <summary>
        /// 整单经销总价
        /// </summary>
        public decimal SumSellAmount { get; set; }

        /// <summary>
        /// 销售员
        /// </summary>
        public BaseDataSimpleModel Staff { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 销售门店
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 设计师
        /// </summary>
        public StaffListModel Designer { get; set; } = new StaffListModel();

        /// <summary>
        /// 销售日期（forderdate）
        /// </summary>
        public DateTime OrderDate { get; set; } = BeiJingTime.Today;

        /// <summary>
        /// 交货日期（fdeliverydate）
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 合作渠道（fchannel）
        /// </summary>
        public object Channel { get; set; } = new object();

        /// <summary>
        /// 物流事项（flogisticsitems）
        /// </summary>
        public string LogisticsItems { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        public List<OrderProductModel> Products { get; set; } = new List<OrderProductModel>();

        /// <summary>
        /// 客户档案
        /// </summary>
        public CustomerSimpleModel Customer { get; set; } = new CustomerSimpleModel();
        /// <summary>
        /// 终端客户档案
        /// </summary>
        public CustomerSimpleModel TerminalCustomer { get; set; } = new CustomerSimpleModel();
        /// <summary>
        /// 楼盘
        /// </summary>
        public BaseDataSimpleModel Building { get; set; } = new BaseDataSimpleModel();

        #region 收货地址相关

        /// <summary>
        /// 客户联系人Id fcustomercontactid
        /// </summary>
        public string CustomercontactId { get; set; }
        /// <summary>
        /// 收货人 flinkstaffid
        /// </summary>
        public string Consignee { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();

        /// <summary>
        /// 所在区域
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }

        #endregion

        /// <summary>
        /// 源单单号
        /// </summary>
        public string SourceNumber { get; set; }

        /// <summary>
        /// 源单类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 联合开单
        /// </summary>
        public List<OrderJoinStaffModel> JoinStaffs { get; set; } = new List<OrderJoinStaffModel>();

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }

        /// <summary>
        /// 合同附件
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; } = new List<BaseImageModel>();

        #region 金额相关

        /// <summary>
        /// 成交金额
        /// fdealamount
        /// 公式：所有商品的成交价之和
        /// </summary>
        public decimal DealAmount { get; set; }

        /// <summary>
        /// 订单总额
        /// fsumamount
        /// 公式：
        /// 订单总额 = 成交金额 + 费用
        /// fsumamount = fdealamount + fexpense
        /// </summary>
        public decimal SumAmount { get; set; }

        /// <summary>
        /// 商品成本
        /// fproductcost
        /// 公式：商品明细的成本之和
        /// 由编辑接口返回
        /// </summary>
        public decimal ProductCost { get; set; }

        /// <summary>
        /// 确认已收
        /// freceivable
        /// 由编辑接口返回
        /// </summary>
        public decimal Receivable { get; set; }

        /// <summary>
        /// 货品原值
        /// ffaceamount
        /// 公式：所有商品的金额之和
        /// </summary>
        public decimal FaceAmount { get; set; }

        /// <summary>
        /// 折扣金额
        /// fdistamount
        /// 公式：所有商品的折扣之和
        /// </summary>
        public decimal DistAmount { get; set; }

        /// <summary>
        /// 货款总折扣率
        /// fdistsumrate
        /// 公式：
        /// 货款总折扣率 = 成交金额 / 货品原值
        /// fdistsumrate = fdealamount / ffaceamount
        /// </summary>
        public decimal DistSumRate { get; set; }

        /// <summary>
        /// 收款金额
        /// fsumreceivable
        /// </summary>
        public decimal SumReceivable { get; set; }

        /// <summary>
        /// 非促销折扣
        /// </summary>
        public decimal NoPromotionDistRate { get; set; }

        /// <summary>
        /// 费用收入
        /// </summary>
        public decimal ExpenseAmount
        {
            get { return ExpenseItems?.Sum(s => s.Amount) ?? 0; }
        }

        /// <summary>
        /// 费用收入项目
        /// </summary>
        public List<OrderExpenseItemModel> ExpenseItems { get; set; } = new List<OrderExpenseItemModel>();

        /// <summary>
        /// 费用收入
        /// </summary>
        public decimal DisburseAmount
        {
            get { return DisburseItems?.Sum(s => s.Amount) ?? 0; }
        }

        /// <summary>
        /// 费用支出项目
        /// </summary>
        public List<OrderExpenseItemModel> DisburseItems { get; set; } = new List<OrderExpenseItemModel>();

        #endregion

        #region 优惠设置

        /// <summary>
        /// 是否抹尾差
        /// </summary>
        public bool IsRemoveTail { get; set; }

        /// <summary>
        /// 优惠抹尾差选项
        /// favoropt_x：小数
        /// favoropt_g：个位
        /// favoropt_s：十位
        /// favoropt_b：百位
        /// favoropt_q：千位
        /// </summary>
        public ComboDataModel FavorOpt { get; set; } = new ComboDataModel();

        /// <summary>
        /// 是否一口价
        /// </summary>
        public bool IsFixedPrice { get; set; }

        #endregion

        #region 折扣设置

        /// <summary>
        /// 整单折
        /// </summary>
        public bool IsWholeDis { get; set; }

        /// <summary>
        /// 折扣系数
        /// </summary>
        public decimal DiscScale { get; set; }

        #endregion

        #region 单据类型配置

        /// <summary>
        /// 启用申购
        /// </summary>
        public bool IsApplyPur { get; set; }

        /// <summary>
        /// 启用通知
        /// </summary>
        public bool EnableNotice { get; set; }

        /// <summary>
        /// 严格跟踪    fstricttrack
        /// </summary>
        public bool StrictTrack { get; set; }

        /// <summary>
        /// 单据类型
        /// </summary>
        public BaseDataSimpleModel BillType { get; set; } = new BaseDataSimpleModel();

        #endregion

        /// <summary>
        /// 允许修改
        /// </summary>
        public bool BillReset { get; set; }
        /// <summary>
        /// 默认规则
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 需转单
        /// </summary>
        public bool NeedTransferOrder { get; set; }
        /// <summary>
        /// 活动
        /// </summary>
        public BaseDataSimpleModel Activity { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 未注册会员原因
        /// </summary>
        public string MemberDesc { get; set; }

        /// <summary>
        /// 会员ID
        /// </summary>
        public string MemberId { get; set; }

        /// <summary>
        /// 商场合同号
        /// </summary>
        public string FmallOrderno { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public ComboDataModel CustomerSource { get; set; }

        /// <summary>
        /// 焕新订单标记
        /// </summary>
        public bool RenewalFlag { get; set; }
        
        /// <summary>
        /// 需开票
        /// </summary>
        public bool IsInvoiceNeed { set; get; }
        
        /// <summary>
        /// 开票信息
        /// </summary>
        public InvoiceEntry InvoiceInfo { set; get; }
        
        /// <summary>
        /// 提交总部时间 fsubmithtime
        /// </summary>
        public DateTime ? SubmitHeadTime { set; get; }

        /// <summary>
        /// 协同SAP状态 '1':'已提交总部','2':'已驳回','3':'已终审'
        /// </summary>
        public string SapStatus { set; get; } 

        /// <summary>
        /// SAP合同类型 fheadcontracttype
        /// </summary>
        public string HeadContractType { set; get; } 
        
        /// <summary>
        /// SAP合同号 fheadquartno
        /// </summary>
        public string HeadQuartNo { set; get; }
        
        /// <summary>
        /// SAP终审时间 fheadquartfrtime
        /// </summary>
        public DateTime ? SapFinalTime { set; get; }

        /// <summary>
        /// SAP同步消息  fheadquartsyncmessage
        /// </summary>
        public string HeadQuartSyncMessage { set; get; }
        /// <summary>
        /// 一件代发标记
        /// </summary>
        public bool piecesendtag { get; set; }

        /// <summary>
        /// 关联合同号
        /// </summary>
        public ComboDataModel relevanceorderno { get; set; }

        /// <summary>
        /// 经销商档案上的直营销售合同赠品成交金额不能为0
        /// </summary>
        public bool IsDirectSaleGiveAwayIsNotZero { set; get; }
        
    }

    /// <summary>
    /// 合同费用项数据模型
    /// </summary>
    public class OrderExpenseItemModel
    {
        public string Id { get; set; }

        public BaseDataSimpleModel Item { get; set; }

        public decimal Amount { get; set; }
    }

    /// <summary>
    /// 联合开单数据模型    fdutyentry
    /// </summary>
    public class OrderJoinStaffModel
    {
        public string Id { get; set; }

        /// <summary>
        /// 主要负责    fismain
        /// </summary>
        public bool IsMain { get; set; }

        /// <summary>
        /// 销售员     fdutyid
        /// </summary>
        public BaseDataSimpleModel Duty { get; set; }

        /// <summary>
        /// 销售员部门     fdeptid
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; }

        /// <summary>
        /// 比例%     fratio
        /// </summary>
        public decimal Ratio { get; set; }

		/// <summary>
		/// 金额      famount
		/// </summary>
		public decimal Amount { get; set; }

		/// <summary>
		/// 备注      fdescription
		/// </summary>
		public string Description { get; set; }
        
        /// <summary>
        /// 部门业绩比例 fdeptperfratio
        /// </summary>
        public decimal DeptPerfRatio { set; get; }
    }
}