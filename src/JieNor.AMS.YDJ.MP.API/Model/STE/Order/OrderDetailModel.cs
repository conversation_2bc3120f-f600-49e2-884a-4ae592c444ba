using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 合同单详情数据模型
    /// </summary>
    public class OrderDetailModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new string Name { get; set; }

        /// <summary>
        /// 销售日期（forderdate）
        /// </summary>
        public DateTime OrderDate { get; set; }
        /// <summary>
        /// 是否二级分销合同(fisresellorder)
        /// </summary>
        public bool IsSecOrder { get; set; }
        /// <summary>
        /// 焕新订单标记
        /// </summary>
        public bool RenewalFlag { get; set; }

        /// <summary>
        /// 总部已退款
        /// </summary>
        public bool IszbRefund { get; set; }
        /// <summary>
        /// 订单进度
        /// </summary>
        public ComboDataModel SettlProgress { get; set; }

        /// <summary>
        /// 交货日期（fdeliverydate）
        /// </summary>
        public DateTime DeliveryDate { get; set; }

        /// <summary>
        /// 销售员
        /// </summary>
        public StaffSimpleModel Staff { get; set; } = new StaffSimpleModel();

        /// <summary>
        /// 部门
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; } = new BaseDataSimpleModel();

        public BaseDataSimpleModel Store { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 设计师
        /// </summary>
        public StaffSimpleModel Designer { get; set; } = new StaffSimpleModel();

        /// <summary>
        /// 审核状态
        /// </summary>
        public bool AuditStatus { get; set; }

        /// <summary>
        /// 订单总额（fsumamount）
        /// </summary>
        public decimal SumAmount { get; set; }
        /// <summary>
        /// 已收款金额
        /// </summary>
        public decimal Sumreceivable { get; set; }

        /// <summary>
        /// 成交金额（fdealamount）
        /// </summary>
        public decimal DealAmount { get; set; }

        /// <summary>
        /// 剩余收款（funreceived）
        /// </summary>
        public decimal UnreceivedAmount { get; set; }

        /// <summary>
        /// 收款待确认
        /// </summary>
        public decimal ReceivableToBeConfirmed { get; set; }

        /// <summary>
        /// 确认收款（freceivable）
        /// </summary>
        public decimal ReceivableAmount { get; set; }

        /// <summary>
        /// 实退金额
        /// </summary>
        public decimal Actrefundamount { get; set; }

        /// <summary>
        /// 费用收入
        /// </summary>
        public decimal ExpenseAmount { get; set; }

        /// <summary>
        /// 费用支出
        /// </summary>
        public decimal DisburseAmount { get; set; }

        /// <summary>
        /// 折扣金额
        /// fdistamount
        /// 公式：所有商品的折扣之和
        /// </summary>
        public decimal DistAmount { get; set; }

        /// <summary>
        /// 货款总折扣率
        /// </summary>
        public decimal DistSumRate { get; internal set; }

        /// <summary>
        /// 货品原值
        /// </summary>
        public decimal FaceAmount { get; internal set; }

        /// <summary>
        /// 商品成本
        /// </summary>
        public decimal ProductCost { get; internal set; }

        /// <summary>
        /// 非促销折扣
        /// </summary>
        public decimal NoPromotionDistRate { get; internal set; }

        /// <summary>
        /// 合作渠道（fchannel）
        /// </summary>
        public BaseDataSimpleModel Channel { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 物流事项（flogisticsitems）
        /// </summary>
        public string LogisticsItems { get; set; }

        /// <summary>
        /// 预留单信息
        /// </summary>
        public SaleIntentionReserveBillModel ReserveBill { get; set; } = new SaleIntentionReserveBillModel();

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 联合开单
        /// </summary>
        public List<OrderJoinStaffModel> JoinStaffs { get; set; } = new List<OrderJoinStaffModel>();

        /// <summary>
        /// 客户
        /// </summary>
        public CustomerSimpleModel Customer { get; set; } = new CustomerSimpleModel();

        /// <summary>
        /// 终端客户
        /// </summary>
        public CustomerSimpleModel TerminalCustomer { get; set; } = new CustomerSimpleModel();

        /// <summary>
        /// 商品
        /// </summary>
        public List<OrderProductModel> Products { get; set; } = new List<OrderProductModel>();

        /// <summary>
        /// 结算状态    freceiptstatus
        /// </summary>
        public ComboDataModel ReceiptStatus { get; set; } = new ComboDataModel();

        public ComboDataModel fbiztype { get; set; } = new ComboDataModel();
        /// <summary>
        /// 报废状态
        /// </summary>
        public bool CancelStatus { get; set; }

        /// <summary>
        ///  严格跟单
        /// </summary>
        public bool Stricttrack { get; set; }

        /// <summary>
        /// 合同附件
        /// </summary>
        public List<OrderAttachmentModel> Attachments { get; set; } = new List<OrderAttachmentModel>();

        /// <summary>
        /// 合同附件
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 楼盘
        /// </summary>
        public BaseDataSimpleModel Building { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 变更状态 '0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'
        /// </summary>
        public int Changestatus { get; set; }

        /// <summary>
        /// 手工单号
        /// </summary>
        public string Within { get; set; }

        /// <summary>
        /// 整单经销总价
        /// </summary>
        public decimal SumSellAmount { get; set; }

        /// <summary>
        /// 需转单
        /// </summary>
        public bool NeedTransferOrder { get; set; }
        //是否销售转单
        public bool IsSaletransferorder { get; set; }

        /// <summary>
        /// 关闭状态：0 未关闭、1 已关闭
        /// </summary>
        public ComboDataModel CloseState { get; set; }
        public string BillTypeId { get; set; }
        /// <summary>
        /// 活动
        /// </summary>
        public BaseDataSimpleModel Activity { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 未注册原因
        /// </summary>
        public string MemberDesc { get; set; }

        /// <summary>
        /// 会员ID
        /// </summary>
        public string MemberNo { get; set; }

        /// <summary>
        /// 商场合同号
        /// </summary>
        public string FmallOrderno { get; set; }

        /// <summary>
        /// 收款比例
        /// </summary>
        public decimal PaymentRatios { get; set; }
        /// <summary>
        /// 已转采购数量
        /// </summary>
        public decimal TransPurQty { get; set; }
        /// <summary>
        /// 已转出库数量
        /// </summary>
        public decimal TransOutQty { get; set; }

        public decimal BizHQDeliveryQty { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public ComboDataModel CustomerSource { get; set; }
        
        /// <summary>
        /// 需开票
        /// </summary>
        public bool IsInvoiceNeed { set; get; }
        
        /// <summary>
        /// 开票信息
        /// </summary>
        public InvoiceEntry InvoiceInfo { set; get; }
        
        /// <summary>
        /// 提交总部时间 fsubmithtime
        /// </summary>
        public DateTime ? SubmitHeadTime { set; get; }

        /// <summary>
        /// 协同SAP状态 '1':'已提交总部','2':'已驳回','3':'已终审'
        /// </summary>
        public string SapStatus { set; get; } 

        /// <summary>
        /// SAP合同类型 fheadcontracttype
        /// </summary>
        public string HeadContractType { set; get; } 
        
        /// <summary>
        /// SAP合同号 fheadquartno
        /// </summary>
        public string HeadQuartNo { set; get; }
        
        /// <summary>
        /// SAP终审时间 fheadquartfrtime
        /// </summary>
        public DateTime ? SapFinalTime { set; get; }

        /// <summary>
        /// SAP同步消息  fheadquartsyncmessage
        /// </summary>
        public string HeadQuartSyncMessage { set; get; }

        /// <summary>
        /// 一件代发标记
        /// </summary>
        public bool piecesendtag { get; set; }

        /// <summary>
        /// 关联合同号
        /// </summary>
        public ComboDataModel relevanceorderno { get; set; }
    }

    /// <summary>
    /// 合同附件数据模型
    /// </summary>
    public class OrderAttachmentModel
    {
        public BaseImageModel Image { get; set; }

        public string Id { get; set; }
    }

    /// <summary>
    /// 合同单产品
    /// </summary>
    public class OrderProductModel
    {
        public static List<OrderProductModel> Init(IEnumerable<DynamicObject> entryObjs, HtmlForm htmlForm,
            UserContext userCtx)
        {
            List<OrderProductModel> models = new List<OrderProductModel>();

            if (entryObjs == null || entryObjs.Count() == 0)
            {
                return models;
            }

            var orderService = userCtx.Container.GetService<IOrderService>();

            foreach (var entryObj in entryObjs)
            {
                models.Add(Init(entryObj, htmlForm, userCtx));
            }

            //套件取“套件总件数”属性
            var suiteModels = models.Where(s => s.IsSuite);
            if (suiteModels.Any())
            {
                var propSuiteCount = userCtx.LoadBizDataByFilter("sel_prop", "fname = '套件总件数'").FirstOrDefault();

                if (propSuiteCount != null)
                {
                    foreach (var model in suiteModels)
                    {
                        model.SuiteSumQtyProp = new ComboDataModel
                        {
                            Id = Convert.ToString(propSuiteCount["Id"]),
                            Name = Convert.ToString(propSuiteCount["fname"])
                        };
                    }
                }
            }

            //子件取子件属性
            var partModels = models.Where(s =>
                !s.SuitCombNumber.IsNullOrEmptyOrWhiteSpace() && !s.SuiteProductId.IsNullOrEmptyOrWhiteSpace()).ToList();

            if (partModels.Any())
            {
                List<string> sqls = new List<string>();
                List<SqlParam> sqlParams = new List<SqlParam>();
                for (var i = 0; i < partModels.Count(); i++)
                {
                    var part = partModels[i];

                    sqls.Add($@"
SELECT p.fid, p.fname, p.fnumber, s.fproductid, e.fpartproductid
FROM t_sel_suite s with(nolock)
INNER JOIN t_sel_suiteentry e with(nolock) ON s.fid = e.fid
INNER JOIN t_sel_prop p with(nolock) ON e.fpartprop = p.fid
WHERE s.fproductid = @fproductid_{i}
AND e.fpartproductid = @fpartproductid_{i}");

                    sqlParams.Add(new SqlParam($"@fproductid_{i}", System.Data.DbType.String, part.SuiteProductId));
                    sqlParams.Add(new SqlParam($"@fpartproductid_{i}", System.Data.DbType.String, part.ProductId));
                }

                string sql = sqls.JoinEx(@" 
union all 
", false);

                var partprops = userCtx.ExecuteDynamicObject(sql, sqlParams);

                foreach (var part in partModels)
                {
                    var partprop = partprops.FirstOrDefault(s =>
                        Convert.ToString(s["fproductid"]).EqualsIgnoreCase(part.SuiteProductId)
                        && Convert.ToString(s["fpartproductid"]).EqualsIgnoreCase(part.ProductId));

                    part.PartProp = new ComboDataModel
                    {
                        Id = Convert.ToString(partprop?["fid"]),
                        Name = Convert.ToString(partprop?["fname"])
                    };
                }
            }

            //加载辅助属性信息
            var auxPropValIds = models.Select(o => o.AuxPropValId).ToList();
            var auxPropValsKv = ProductUtil.PackAuxPropFieldValue(userCtx, auxPropValIds);
            foreach (var item in models)
            {
                item.AuxPropVals = auxPropValsKv.GetValue(item.AuxPropValId) ?? new List<Dictionary<string, string>>();
            }

            // 加载销售合同关联的销售出库数（作废状态=否）
            var outQtyDic = orderService.GetOutQty(userCtx, models.Select(s => s.Id));

            foreach (var model in models)
            {
                if (outQtyDic.TryGetValue(model.Id, out var outQty))
                {
                    model.AllOutQty = Convert.ToInt32(outQty.Item2);
                }
            }

            return models;
        }

        private static OrderProductModel Init(DynamicObject data, HtmlForm htmlForm, UserContext userContext)
        {
            OrderProductModel model = new OrderProductModel();
            if (data == null)
            {
                return model;
            }

            string fbiztypeid = Convert.ToString(data["fbiztype"]).Trim();
            var fbiz = htmlForm.GetAssistDataSource(userContext, "fbiztype")["fbiztype"];

            foreach (var fbi in fbiz)
            {
                if (fbi["id"].ToString() == fbiztypeid)
                {
                    model.fbiztype = new ComboDataModel
                    {
                        Id = fbiztypeid,
                        Name = fbi["name"].ToString()
                    };
                }
            }

            var materialObj = data["fproductid_ref"] as DynamicObject;
            var stockstatusObj = data["fstockstatus_ref"] as DynamicObject;
            var spaceObj = data["fspace_ref"] as DynamicObject;
            var storehouseObj = data["fstorehouseid_ref"] as DynamicObject;
            var activity_eObj = data["factivityid_e_ref"] as DynamicObject;
            var storelocationObj = data["fstorelocationid_ref"] as DynamicObject;

            var brandObj = materialObj?["fbrandid_ref"] as DynamicObject;   // 品牌
            var seriesObj = materialObj?["fseriesid_ref"] as DynamicObject; // 系列
            var categoryObj = materialObj?["fcategoryid_ref"] as DynamicObject; // 商品分类
            var categoryId = Convert.ToString(materialObj?["fcategoryid"]);
            if (categoryObj == null && !categoryId.IsNullOrEmptyOrWhiteSpace())
            {
                categoryObj = userContext.LoadBizDataById("ydj_category", categoryId);
            }

            model.CloseState = new ComboDataModel
            {
                Id = Convert.ToString(data["fclosestatus_e"]),
                Name = CloseStatusConst.KeyValues.ContainsKey(Convert.ToString(data["fclosestatus_e"])) ? CloseStatusConst.KeyValues[Convert.ToString(data["fclosestatus_e"])] : ""
            };
            model.Id = JNConvert.ToStringAndTrim(data["id"]);
            model.Qty = Convert.ToInt32(data["fbizqty"]);
            //销售已出库数
            model.BizOutQty = Convert.ToInt32(data["fbizoutqty"]);
            //销售已退换数量
            model.BizReturnQty = Convert.ToInt32(data["fbizreturnqty"]);
            //销售退换中数量
            model.BizReturningQty = Convert.ToInt32(data["fbizreturningqty"]);
            model.Price = Convert.ToDecimal(data["fprice"]);
            model.HqPrice = Convert.ToDecimal(data["fhqprice"]);
            model.Amount = Convert.ToDecimal(data["famount"]);
            model.DealPrice = Convert.ToDecimal(data["fdealprice"]);
            model.DealAmount = Convert.ToDecimal(data["fdealamount"]);

            model.SellPrice = Convert.ToDecimal(data["fsellprice"]);
            model.SellAmount = Convert.ToDecimal(data["fsellamount"]);

            model.DistRate = Convert.ToDecimal(data["fdistrate"]);
            model.DistRateRaw = Convert.ToDouble(data["fdistrateraw"]);
            model.Flinkpro = JNConvert.ToStringAndTrim(data["flinkpro"]);
            model.DistAmount = Convert.ToDecimal(data["fdistamount"]);
            model.IsOutSpot = Convert.ToBoolean(data["fisoutspot"]);
            model.Description = JNConvert.ToStringAndTrim(data["fdescription"]);
            model.fsourceentryid_e = JNConvert.ToStringAndTrim(data["fsourceentryid_e"]);
            model.fsourcetype_e = JNConvert.ToStringAndTrim(data["fsourcetype_e"]);
            model.fsourcenumber_e = JNConvert.ToStringAndTrim(data["fsourcenumber_e"]);
            model.TransPurQty = Convert.ToDecimal(data["ftranspurqty"]);
            model.TransOutQty = Convert.ToDecimal(data["ftransoutqty"]);
            model.BizHQDeliveryQty = Convert.ToDecimal(data["fbizhqdeliveryqty"]);
            model.ProductId = JNConvert.ToStringAndTrim(materialObj?["id"]);
            model.ProductName = JNConvert.ToStringAndTrim(materialObj?["fname"]);
            model.ProductNumber = JNConvert.ToStringAndTrim(materialObj?["fnumber"]);
            model.GuidePrice = Convert.ToDecimal(materialObj?["fguideprice"]);
            model.ImageList = ImageFieldUtil.ParseImages(data, "fmtrlimage", true);
            model.Mtono = Convert.ToString(data["fmtono"]);
            model.Stricttrack = Convert.ToBoolean(data["fstricttrack"]);//严格跟单
            var transferOrderStatuss = new List<ComboDataModel>() { new ComboDataModel() { Id = "1", Name = "审批中" }, new ComboDataModel() { Id = "2", Name = "审批通过" }, new ComboDataModel() { Id = "3", Name = "审批驳回" }, new ComboDataModel() { Id = "4", Name = "已结算" } };
            var transferOrderStatus = transferOrderStatuss.FirstOrDefault(t => t.Id == Convert.ToString(data["ftransferorderstatus"]));
            model.TransferOrderStatus = transferOrderStatus == null ? new ComboDataModel() { Id = "0", Name = "未发起" } : transferOrderStatus;
            model.IsPresetProp = Convert.ToBoolean(materialObj["fispresetprop"]);//允许选配
            model.IsSuite = Convert.ToBoolean(materialObj["fsuiteflag"]);//选配套件
            model.IsNonStandard = Convert.ToBoolean(data["funstdtype"]);//非标产品
            model.IsAddibleParts = Convert.ToBoolean(materialObj["fbedpartflag"]);//是否可添加配件
            model.isPartFlag = Convert.ToBoolean(materialObj["fispartflag"]);//配件标记
            //model.shipperagent = JNConvert.ToStringAndTrim(data["fshipperagent"]);
            //var shipperagentobj = userContext.LoadBizDataByFilter("bas_agent", $"fnumber = '{JNConvert.ToStringAndTrim(data["fshipperagentid"])}'");
            var shipperagentobj = userContext.LoadBizDataById("bas_agent", JNConvert.ToStringAndTrim(data["fshipperagentid"]));
            if (shipperagentobj != null)
            {
                model.shipperagent = new ComboDataModel
                {
                    Id = JNConvert.ToStringAndTrim(data["fshipperagentid"]),
                    Name = JNConvert.ToStringAndTrim(shipperagentobj["fname"])
                };
            }
            model.transferorderagreetime = JNConvert.ToStringAndTrim(data["ftransferorderagreetime"]);
            model.shipperagentcontactperson = JNConvert.ToStringAndTrim(data["fshipperagentcontactperson"]);
            model.shippercontactpersonphone = JNConvert.ToStringAndTrim(data["fshippercontactpersonphone"]);

            var resultbrandobj = data["fresultbrandid_ref"] as DynamicObject;//业绩品牌
            if (resultbrandobj != null)
            {
                model.ResultBrand = new ComboDataModel()
                {
                    Id = Convert.ToString(resultbrandobj["id"]),
                    Name = Convert.ToString(resultbrandobj["fname"])
                };
            }

            //库存状态
            model.StockStatus = new ComboDataModel
            {
                Id = Convert.ToString(stockstatusObj?["id"]),
                Name = Convert.ToString(stockstatusObj?["fname"])
            };

            //空间
            model.Space = new ComboDataModel(spaceObj);
            //仓库
            model.StoreHouse = new ComboDataModel
            {
                Id = Convert.ToString(storehouseObj?["id"]),
                Name = Convert.ToString(storehouseObj?["fname"])
            };
            //仓位
            model.StoreLocation = new ComboDataModel
            {
                Id = Convert.ToString(storelocationObj?["id"]),
                Name = Convert.ToString(storelocationObj?["fname"])
            };
            //活动
            model.Activity = new ComboDataModel
            {
                Id = Convert.ToString(activity_eObj?["id"]),
                Name = Convert.ToString(activity_eObj?["fname"])
            };

            model.Brand = new ComboDataModel
            {
                Id = Convert.ToString(brandObj?["id"]),
                Name = Convert.ToString(brandObj?["fname"])
            };

            model.Series = new ComboDataModel
            {
                Id = Convert.ToString(seriesObj?["id"]),
                Name = Convert.ToString(seriesObj?["fname"])
            };


            string deliveryMode = JNConvert.ToStringAndTrim(data["fdeliverymode"]);
            string deliveryModeName = htmlForm.GetSimpleSelectItemText(data, "fdeliverymode");

            model.DeliveryMode = new ComboDataModel
            {
                Id = deliveryMode,
                Name = deliveryModeName
            };

            model.AuxPropValId = JNConvert.ToStringAndTrim(data["fattrinfo"]);

            //var setNumber = JNConvert.ToStringAndTrim((data["fattrinfo_ref"] as DynamicObject)?["fnumber"]);
            //model.AuxPropVals = ProductUtil.PackAuxPropFieldValue(setNumber);

            model.CustomDesc = JNConvert.ToStringAndTrim(data["fcustomdes_e"]);

            model.IsGiveaway = Convert.ToBoolean(data["fisgiveaway"]);

            model.IsClearStock = Convert.ToBoolean(data["fisclearstock"]);

            model.SuiteProductId = Convert.ToString(data["fsuitproductid"]);
            model.SuitCombNumber = Convert.ToString(data["fsuitcombnumber"]);
            //model.SuiteDesc = Convert.ToString(data["fsuitdescription"]);
            model.SubQty = Convert.ToDecimal(data["fsubqty"]);
            model.PartsCombNumber = Convert.ToString(data["fpartscombnumber"]);
            model.IsAutoCombParts = Convert.ToBoolean(data["fisautopartflag"]);
            model.PartType = Convert.ToString(data["fparttype"]);
            model.PartQty = Convert.ToDecimal(data["fpartqty"]);
            model.IsSofaCategory = ProductUtil.HaveAnyCategory(userContext, "沙发类", Convert.ToString(materialObj?["id"]));
            model.SofaCombNumber = Convert.ToString(data["fsofacombnumber"]);
            model.IsCombMain = Convert.ToBoolean(data["fiscombmain"]);
            model.ProductCategory = new BaseDataModel(categoryObj);
            var orderservice = userContext.Container.GetService<IOrderService>();
            model.Dataorigin = Convert.ToString(materialObj["fmainorgid"]).EqualsIgnoreCase(userContext.TopCompanyId) ? "总部" : "";
            model.IsAllowOrders = orderservice.CheckNoOrders(userContext, new NoOrderParmas
            {
                fisgiveaway = model.IsGiveaway,
                fissuit = model.IsSuite,
                fmainorgid = Convert.ToString(materialObj["fmainorgid"]),
                fprice = model.Price,
                funstdtype = model.IsNonStandard
            });

            model.IsAllowOrders = orderservice.CheckNoOrders(userContext, new NoOrderParmas
            {
                fisgiveaway = model.IsGiveaway,
                fissuit = model.IsSuite,
                fmainorgid = Convert.ToString(materialObj["fmainorgid"]),
                fprice = model.Price,
                funstdtype = model.IsNonStandard
            });

            model.UnstdTypeComment = JNConvert.ToStringAndTrim(data["funstdtypecomment"]);
            model.UnstdTypeStatus.Id = JNConvert.ToStringAndTrim(data["funstdtypestatus"]);
            model.UnstdTypeStatus.Name = htmlForm.GetSimpleSelectItemText(data, "funstdtypestatus") ?? string.Empty;

            var prodRequirementObj = data["fprodrequirement_ref"] as DynamicObject;//生产要求
            var selSuiteRequireobj = data["fselsuiterequire_ref"] as DynamicObject;//家纺套件要求
            model.ProdRequirement = new ComboDataModel
            {
                Id = Convert.ToString(prodRequirementObj?["id"]),
                Name = Convert.ToString(prodRequirementObj?["fenumitem"])
            };
            model.SelSuiteRequire = new ComboDataModel
            {
                Id = Convert.ToString(selSuiteRequireobj?["id"]),
                Name = Convert.ToString(selSuiteRequireobj?["fenumitem"])
            };

            #region 促销活动

            var fpromotionid = Convert.ToString(data["fpromotionid"]);
            if (!fpromotionid.IsNullOrEmptyOrWhiteSpace())
            {
                var promotion = data["fpromotionid_ref"] as DynamicObject;

                model.fpromotionid["id"] = Convert.ToString(promotion?["id"]);
                model.fpromotionid["fnumber"] = Convert.ToString(promotion?["fnumber"]);
                model.fpromotionid["fname"] = Convert.ToString(promotion?["fname"]);

                model.fpromotionrule = Convert.ToString(data["fpromotionrule"]);
                model.fisusepromotion = Convert.ToBoolean(data["fisusepromotion"]);
                model.fpromotioncombono = Convert.ToString(data["fpromotioncombono"]);
                model.fbizpromotioncomboqty = Convert.ToDecimal(data["fbizpromotioncomboqty"]);
                model.fpromotioncomboqty = Convert.ToDecimal(data["fpromotioncomboqty"]);
                model.fpromotionlowestprice = Convert.ToDecimal(data["fpromotionlowestprice"]);
                model.fpromotiongiftamount = Convert.ToDecimal(data["fpromotiongiftamount"]);
                model.fpromotiongiftqty = Convert.ToDecimal(data["fpromotiongiftqty"]);
                model.fbizpromotiongiftqty = Convert.ToDecimal(data["fbizpromotiongiftqty"]);
                model.fisgiftdiscount = Convert.ToBoolean(data["fisgiftdiscount"]);
            }
            model.Fisfromfirstinventory= Convert.ToBoolean(data["fisfromfirstinventory"]);
            #endregion

            return model;
        }

        public string Id { get; set; }
        /// <summary>
        /// 行关闭状态
        /// </summary>
        public ComboDataModel CloseState { get; set; }

        /// <summary>
        /// 商品Id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 商品编码
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品主图ID（已废弃）
        /// </summary>
        public string ImageId { get; set; }

        /// <summary>
        /// 商品主图地址（已废弃）
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 是否允许选配
        /// </summary>
        public bool IsPresetProp { get; set; }

        /// <summary>
        /// 选配套件
        /// </summary>
        public bool IsSuite { get; set; }

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 是否可添加配件
        /// </summary>
        public bool IsAddibleParts { get; set; }
        /// <summary>
        /// 商品配件标记
        /// </summary>
        public bool isPartFlag { get; set; }

        /// <summary>
        /// 辅助属性组合值ID
        /// </summary>
        public string AuxPropValId { get; set; }

        /// <summary>
        /// 辅助属性
        /// </summary>
        public List<Dictionary<string, string>> AuxPropVals { get; set; }

        /// <summary>
        /// 定制说明
        /// </summary>
        public string CustomDesc { get; set; }

        /// <summary>
        /// 销售数量
        /// </summary>
        public int Qty { get; set; }

        /// <summary>
        /// 销售已出库数（已审核）
        /// </summary>
        public int BizOutQty { get; set; }

        /// <summary>
        /// 销售已出库数（所有）
        /// </summary>
        public int AllOutQty { get; set; }

        /// <summary>
        /// 销售已退换数量
        /// </summary>
        public int BizReturnQty { get; set; }

        /// <summary>
        /// 销售退换中数量
        /// </summary>
        public int BizReturningQty { get; set; }

        /// <summary>
        /// 零售价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 总部零售价
        /// </summary>
        public decimal HqPrice { get; set; }

        /// <summary>
        /// 成交单价
        /// </summary>
        public decimal DealPrice { get; set; }

        /// <summary>
        /// 厂家指导价
        /// </summary>
        public decimal GuidePrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 成交金额
        /// </summary>
        public decimal DealAmount { get; set; }

        /// <summary>
        /// 经销价
        /// </summary>
        public decimal SellPrice { get; set; }

        /// <summary>
        /// 经销总价（数量*经销价）其实就是经销金额
        /// </summary>
        public decimal SellAmount { get; set; }

        /// <summary>
        /// 折率
        /// </summary>
        public decimal DistRate { get; set; }
        /// <summary>
        /// 流程状态
        /// </summary>
        public string Flinkpro { get; set; }
        /// <summary>
        /// 折扣额
        /// </summary>
        public decimal DistAmount { get; set; }

        /// <summary>
        /// 出现货
        /// </summary>
        public bool IsOutSpot { get; set; }


        /// <summary>
        /// 已转采购数量
        /// </summary>
        public decimal TransPurQty { get; set; }
        /// <summary>
        /// 已转出库数量
        /// </summary>
        public decimal TransOutQty { get; set; }

        public decimal BizHQDeliveryQty { get; set; }

        /// <summary>
        /// 转单状态：'1':'审批中','2':'审批通过','3':'审批驳回','4':'已结算'
        /// </summary>
        public ComboDataModel TransferOrderStatus { get; set; } = new ComboDataModel();

        /// <summary>
        /// 提货方式
        /// </summary>
        public ComboDataModel DeliveryMode { get; set; } = new ComboDataModel();

        /// <summary>
        /// 商品备注
        /// </summary>
        public string Description { get; set; }
        public string fsourceentryid_e { get; set; }
        public string fsourcetype_e { get; set; }
        public string fsourcenumber_e { get; set; }


        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string Mtono { get; set; }

        public ComboDataModel fbiztype { get; set; } = new ComboDataModel();
        /// <summary>
        ///  严格跟单
        /// </summary>
        public bool Stricttrack { get; set; }

        public string transferorderagreetime { get; set; }
        //public string shipperagent { get; set; }
        public ComboDataModel shipperagent { get; set; } = new ComboDataModel();

        public string shipperagentcontactperson { get; set; }
        public string shippercontactpersonphone { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public ComboDataModel StockStatus { get; set; } = new ComboDataModel();
        /// <summary>
        /// 空间
        /// </summary>
        public ComboDataModel Space { get; set; } = new ComboDataModel();
        /// <summary>
        /// 仓库
        /// </summary>
        public ComboDataModel StoreHouse { get; set; } = new ComboDataModel();
        /// <summary>
        /// 仓位
        /// </summary>
        public ComboDataModel StoreLocation { get; set; } = new ComboDataModel();
        /// <summary>
        /// 活动
        /// </summary>
        public ComboDataModel Activity { get; set; } = new ComboDataModel();

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool IsGiveaway { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Dataorigin { get; set; }

        /// <summary>
        /// 是否允许下单
        /// </summary>
        public bool IsAllowOrders { get; set; }
        /// <summary>
        /// 零售价可编辑
        /// </summary>
        public bool IsEditPrice { get; set; }
        /// <summary>
        /// 业绩品牌
        /// </summary>
        public ComboDataModel ResultBrand { get; set; } = new ComboDataModel();
        /// <summary>
        /// 品牌
        /// </summary>
        public ComboDataModel Brand { get; set; } = new ComboDataModel();

        /// <summary>
        /// 系列
        /// </summary>
        public ComboDataModel Series { get; set; } = new ComboDataModel();

        /// <summary>
        /// 清库存
        /// </summary>
        public bool IsClearStock { get; set; }

        /// <summary>
        /// 套件商品ID
        /// </summary>
        public string SuiteProductId { get; set; }

        /// <summary>
        /// 套件说明
        /// </summary>
        public string SuiteDesc { get; set; }

        /// <summary>
        /// 套件组合号
        /// </summary>
        public string SuitCombNumber { get; set; }

        /// <summary>
        /// 配件组合号
        /// </summary>
        public string PartsCombNumber { get; set; }

        /// <summary>
        /// 是否自动组合配件
        /// </summary>
        public bool IsAutoCombParts { get; set; }

        /// <summary>
        /// 是否组合主商品
        /// </summary>
        public bool IsCombMain { get; set; }

        /// <summary>
        /// 子件数量
        /// </summary>
        public decimal SubQty { get; set; }

        /// <summary>
        /// 配件类型
        /// </summary>
        public string PartType { get; set; }

        /// <summary>
        /// 配件数量
        /// </summary>
        public decimal PartQty { get; set; }

        /// <summary>
        /// 沙发组合号
        /// </summary>
        public string SofaCombNumber { get; set; }

        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// 商品分类
        /// </summary>
        public BaseDataModel ProductCategory { get; set; } = new BaseDataModel();

        /// <summary>
        /// 非标审批状态
        /// </summary>
        public ComboDataModel UnstdTypeStatus { get; set; } = new ComboDataModel();

        /// <summary>
        /// 非标审批意见
        /// </summary>
        public string UnstdTypeComment { get; set; }

        /// <summary>
        /// 生产要求
        /// </summary>
        public ComboDataModel ProdRequirement { get; set; }

        /// <summary>
        /// 家纺套件要求
        /// </summary>
        public ComboDataModel SelSuiteRequire { get; set; }

        /// <summary>
        /// 折扣
        /// </summary>
        public double DistRateRaw { get; set; }

        /// <summary>
        /// "套件总件数"属性。是套件商品时才返回。
        /// </summary>
        public ComboDataModel SuiteSumQtyProp { get; set; }

        /// <summary>
        /// 子件属性
        /// </summary>
        public ComboDataModel PartProp { get; set; }

        /// <summary>
        /// 促销主题
        /// </summary>
        public JObject fpromotionid { get; set; } = new JObject()
        {
            { "id", "" },
            { "fnumber", "" },
            { "fname", "" }
        };

        ///// <summary>
        ///// 促销编号
        ///// </summary>
        //public string fpromotionno { get; set; }

        ///// <summary>
        ///// 促销类型
        ///// </summary>
        //public string fpromotiontype { get; set; }

        /// <summary>
        /// 促销规则
        /// </summary>
        public string fpromotionrule { get; set; }

        /// <summary>
        /// 已参与促销活动
        /// </summary>
        public bool fisusepromotion { get; set; }

        /// <summary>
        /// 促销活动组合号
        /// </summary>
        public string fpromotioncombono { get; set; }

        /// <summary>
        /// 组合套餐数量
        /// </summary>
        public decimal fbizpromotioncomboqty { get; set; }

        /// <summary>
        /// 基本单位组合套餐数量
        /// </summary>
        public decimal fpromotioncomboqty { get; set; }

        /// <summary>
        /// 特价最低价
        /// </summary>
        public decimal fpromotionlowestprice { get; set; }

        /// <summary>
        /// 满赠金额
        /// </summary>
        public decimal fpromotiongiftamount { get; set; }

        /// <summary>
        /// 满赠数量
        /// </summary>
        public decimal fpromotiongiftqty { get; set; }

        /// <summary>
        /// 基本单位满赠数量
        /// </summary>
        public decimal fbizpromotiongiftqty { get; set; }

        /// <summary>
        /// 赠品参与折扣计算
        /// </summary>
        public bool fisgiftdiscount { get; set; }

        /// <summary>
        /// 是否一级库存携带
        /// </summary>
        public bool Fisfromfirstinventory { get; set; }

    }
}