using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class ServiceFeedListModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new string Name { get; set; }

        /// <summary>
        /// 反馈人员：师傅名
        /// </summary>
        public string Ffeeder { get; set; }

        /// <summary>
        /// 问题类型
        /// </summary>
        public string Fsprotype { get; set; }

        /// <summary>
        /// 问题描述
        /// </summary>
        public string Fprodesript { get; set; }

        /// <summary>
        /// 处理状态：已提交、已受理、已取消
        /// </summary>
        public string Fhandlestatus { get; set; }

        
    }
}
