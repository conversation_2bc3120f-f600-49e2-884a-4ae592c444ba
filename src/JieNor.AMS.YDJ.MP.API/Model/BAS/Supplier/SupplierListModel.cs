using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class SupplierListModel
    {
        /// <summary>
        /// 供应商id
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string Number { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 采购员
        /// </summary>
        public ComboDataModel PoStaffId { get; set; }
        /// <summary>
        /// 采购部门
        /// </summary>
        public ComboDataModel PoDeptId { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string Contacts { get; set; }
        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; }
        /// <summary>
        /// 详细地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }
    }
}


