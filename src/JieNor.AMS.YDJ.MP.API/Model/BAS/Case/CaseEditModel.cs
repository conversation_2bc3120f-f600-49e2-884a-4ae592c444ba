using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 案例单编辑数据模型
    /// </summary>
    public class CaseEditModel : BaseDataModel
    {
        /// <summary>
        /// 楼盘户型
        /// </summary>
        public CaseBuildingTypeEntry BuildingTypeEntries { get; set; } = new CaseBuildingTypeEntry();

        /// <summary>
        /// 设计师
        /// </summary>
        public ComboDataModel Fstylistid { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        public ComboDataModel Fcustomerid { get; set; }

        /// <summary>
        /// 所属门店
        /// </summary>
        public ComboDataModel Fdeptid { get; set; }

        /// <summary>
        /// 楼盘
        /// </summary>
        public ComboDataModel Fbuildingid { get; set; }

        /// <summary>
        /// 合作渠道
        /// </summary>
        public ComboDataModel Fchannel { get; set; }

        /// <summary>
        /// VR链接
        /// </summary>
        public string Fvrlink { get; set; }
        /// <summary>
        /// 案例说明
        /// </summary>
        public string fremark { get; set; }
        /// <summary>
        /// 关键字
        /// </summary>
        public string fkeywords { get; set; }
        /// <summary>
        /// 风格
        /// </summary>
        public ComboDataModel Fstyle { get; set; } = new ComboDataModel();

        /// <summary>
        /// 空间
        /// </summary>
        public ComboDataModel Fspace { get; set; } = new ComboDataModel();

        /// <summary>
        /// 面积
        /// </summary>
        public ComboDataModel Farea { get; set; } = new ComboDataModel();

        /// <summary>
        /// 案例品类
        /// </summary>
        public ComboDataModel Fcasecategory { get; set; } = new ComboDataModel();


        /// <summary>
        /// 案例图片
        /// </summary>
        public List<BaseImageModel> Images { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }

        public class CaseBuildingTypeEntry
        {

            /// <summary>
            /// 室
            /// </summary>
            public ComboDataModel Room { get; set; } = new ComboDataModel();

            /// <summary>
            /// 厅
            /// </summary>
            public ComboDataModel Hall { get; set; } = new ComboDataModel();

            /// <summary>
            /// 卫
            /// </summary>
            public ComboDataModel Toilet { get; set; } = new ComboDataModel();

            /// <summary>
            /// 阳台
            /// </summary>
            public ComboDataModel Balcony { get; set; } = new ComboDataModel();

        }
    }

}
