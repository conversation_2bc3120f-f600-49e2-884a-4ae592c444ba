using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.BAS.CollectingUnit
{
    public class CollectionUnitModel
    {
        public CollectionUnitModel() { }

        public CollectionUnitModel(
            string id,
            string name,
            string type
            )
        {
            this.id = id;
            this.name = name;
            this.type = type;
        }
        public string id { get; set; }

        public string name { get; set; }

        public string type { get; set; }
    }

    public class UnitListModel
    {
        /// <summary>
        /// 代收单位
        /// </summary>
        public List<CollectionUnitModel> CollectionUnit { get; set; } = new List<CollectionUnitModel>();
    }
}
