using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.BAS
{
    public class GetCurrAreaModel
    {
        /// <summary>
        ///  国家或地区
        /// </summary>
        public ComboDataModel Country { get; set; } = new ComboDataModel();
        /// <summary>
        /// 所在区域 - 省份
        /// </summary>
        public ComboDataModel Province { get; set; } = new ComboDataModel();

        /// <summary>
        /// 所在区域 - 城市
        /// </summary>
        public ComboDataModel City { get; set; } = new ComboDataModel();

        /// <summary>
        /// 所在区域 - 区县
        /// </summary>
        public ComboDataModel Region { get; set; } = new ComboDataModel();
    }
}
