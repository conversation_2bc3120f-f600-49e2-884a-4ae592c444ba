using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 销售管理参数数据模型
    /// </summary>
    public class StoreSysParamModel
    {
        /// <summary>
        /// 销售意向合作渠道可为空 fischannelnullable
        /// </summary>
        public bool IsChannelNullable { get; set; }

        /// <summary>
        /// 销售意向零售价可编辑  fissaleusable
        /// </summary>
        public bool IsSaleusable { get; set; }

        /// <summary>
        /// 销售合同零售价可编辑  fisorderusable
        /// </summary>
        public bool IsOrderusable { get; set; }

        /// <summary>
        /// 销售单据（意向及合同）不审核可收款   fcanreceipt
        /// </summary>
        public bool CanReceipt { get; set; }

        /// <summary>
        /// 销售单据（意向及合同）不审核可退款   fcanrefund
        /// </summary>
        public bool CanRefund { get; set; }

        /// <summary>
        /// 出现货商品无需采购   foutspotnopur
        /// </summary>
        public bool OutSpotNoPur { get; set; }

        /// <summary>
        /// 出现货时自动预留    foutspotautores
        /// </summary>
        public bool OutSpotAutoRes { get; set; }

        /// <summary>
        /// 单据商品图同步至图库  fenablesyncgallery
        /// </summary>
        public bool EnableSyncGallery { get; set; }

        /// <summary>
        /// 先取图库后取主图    fenableproductimg
        /// </summary>
        public bool EnableProductImg { get; set; }

        /// <summary>
        /// 只取主图    fonlyproductimg
        /// </summary>
        public bool OnlyProductImg { get; set; }

        /// <summary>
        /// 合同变更必须上传凭证  fhasvoucherwhenorderchange
        /// </summary>
        public bool HasVoucherWhenOrderChange { get; set; }

        /// <summary>
        /// 收款必须上传凭证    fhasvoucherwhenreceivable
        /// </summary>
        public bool HasVoucherWhenReceivable { get; set; }

        /// <summary>
        /// 销售合同允许超额收款  fcanexcess
        /// </summary>
        public bool CanExcess { get; set; }

        /// <summary>
        /// 订单超额收款自动转账户余额  fistransfer
        /// </summary>
        public bool IsTransfer { get; set; }

        /// <summary>
        /// 销售合同实退不体现未收 fdontreflect
        /// </summary>
        public bool DontReflect { get; set; }

        /// <summary>
        /// 收支记录确认前需审核  fincomeconfirmaudit
        /// </summary>
        public bool IncomeConfirmAudit { get; set; }

        /// <summary>
        /// 出现货商品自动更新物流跟踪号  foutspotupdatemtono
        /// </summary>
        public bool OutSpotUpdateMtono { get; set; }

        /// <summary>
        /// 客户地区必录信息    fcustomerarea
        /// </summary>
        public string[] CustomerArea { get; set; }

        /// <summary>
        /// 客户报备唯一性控制规则 fcustomerunique
        /// </summary>
        public string[] CustomerUnique { get; set; }

        /// <summary>
        /// 商机报备唯一性控制规则 fcustomerrecordunique
        /// </summary>
        public string[] CustomerRecordUnique { get; set; }

        /// <summary>
        /// 销售合同允许审核的最低金额比例 fauditminpercentage
        /// </summary>
        public decimal AuditMinPercentage { get; set; }

        /// <summary>
        /// 首款比例控制  ffirstratioctrl
        /// </summary>
        public bool FirstRatioCtrl { get; set; }

        /// <summary>
        /// 非现货首款比例 fnotoutspotratio
        /// </summary>
        public decimal NotOutSpotRatio { get; set; }

        /// <summary>
        /// 现货首款比例  foutspotratio
        /// </summary>
        public decimal OutSpotRatio { get; set; }

        /// <summary>
        /// 合作渠道按比例计算佣金 fenablebrokerageratio
        /// </summary>
        public bool EnableBrokerageRatio { get; set; }

        /// <summary>
        /// 销售取价模式  fgetpricemode
        /// </summary>
        public string GetPriceMode { get; set; }

        /// <summary>
        /// 销售合同支持整单折   fcanwholedis
        /// </summary>
        public bool CanWholeDis { get; set; }

        /// <summary>
        /// 销售合同支持一口价   fcanfixeprice
        /// </summary>
        public bool CanFixePrice { get; set; }

        /// <summary>
        /// 销售合同支持抹尾差   fcanremovetail
        /// </summary>
        public bool CanRemoveTail { get; set; }

        /// <summary>
        /// 抹小数位    fremovedecimal
        /// </summary>
        public bool RemoveDecimal { get; set; }

        /// <summary>
        /// 抹个位 fremoveunidigit
        /// </summary>
        public bool RemoveUnidigit { get; set; }

        /// <summary>
        /// 抹十位 fremovetens
        /// </summary>
        public bool RemoveTens { get; set; }

        /// <summary>
        /// 抹百位 fremovehundred
        /// </summary>
        public bool RemoveHundred { get; set; }

        /// <summary>
        /// 抹千位 fremovethousand
        /// </summary>
        public bool RemoveThousand { get; set; }

        /// <summary>
        /// 销售意向定金启用应收控制    fenablecollectamount
        /// </summary>
        public bool EnableCollectAmount { get; set; }

        /// <summary>
        /// 启用手机号位数控制   fenablephonelengthctrl
        /// </summary>
        public bool EnablePhoneLengthCtrl { get; set; }

        /// <summary>
        /// 允许销售意向商品明细为空    femptysaletentionentry
        /// </summary>
        public bool EmptySaleTentionEntry { get; set; }

        /// <summary>
        /// 刷卡方式银行卡必填   fenablemustinputbankid
        /// </summary>
        public bool EnableMustInputBankId { get; set; }

        /// <summary>
        /// CRM客户隐私保护   fenableprotectecusinfo
        /// </summary>
        public bool EnableProtecteCusInfo { get; set; }

        /// <summary>
        /// 开启可销库存控制    fenablelotinventoryctl
        /// </summary>
        public bool EnableLotInventoryCtl { get; set; }

        /// <summary>
        /// 销售合同收款审核前, 必须收到全款   fpaymentmustbeget
        /// </summary>
        public bool PaymentMustBeGet { get; set; }

        /// <summary>
        /// 销售合同收到全款后自动审核   fafterautomaticaudit
        /// </summary>
        public bool AfterAutoMaticAudit { get; set; }

        /// <summary>
        /// 销售合同审核后自动生成Cloud订单(订购意向书与销售订单)  fafteraudittocloud
        /// </summary>
        public bool AfterAuditToCloud { get; set; }

        /// <summary>
        /// 销售合同结算时不做资金协同   fnocapitalcoordination
        /// </summary>
        public bool NoCapitalCoordination { get; set; }

        /// <summary>
        ///创建客户自动设置负责人及所属门店    fautosetduty
        /// </summary> 
        public bool AutoSetDuty{get;set; }

        /// <summary>
        ///合同收款时允许扣减佣金    fallowreducebrokerage
        /// </summary> 
        public bool AllowReduceBrokerage { get; set; }

        /// <summary>
        ///合同收款时允许添加费用项目    fallowaddexpense
        /// </summary> 
        public bool AllowAddExpense { get; set; }

        /// <summary>
        /// 销售合同赠品参与折扣计算
        /// </summary>
        public bool IsGiftDiscount { get; set; }

        /// <summary>
        /// 收款必填收款小票号   fmustinvoicenumber
        /// </summary>
        public bool MustInvoiceNumber { get; set; }

        /// <summary>
        /// 促销商品不参与折扣计算
        /// </summary>
        public bool NoPromotionDiscount { get; set; }

        /// <summary>
        /// 合同变更允许切换收货人信息
        /// </summary>
        public bool OrderChangeStaff { get; set; }

        /// <summary>
        /// 销售退换货中允许变更合同
        /// </summary>
        public bool ReturnModifyOrder { get; set; }

        /// <summary>
        /// 退款时客户收款账号必录
        /// </summary>
        public bool RefundCusacount { get; set; }
    }
}