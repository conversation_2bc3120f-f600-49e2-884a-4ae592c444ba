using JieNor.AMS.YDJ.DataTransferObject;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商品库存详情数据模型
    /// </summary>
    public class ProductStockDetailModel
    {
        [IgnoreDataMember]
        public string ProductId { get; set; }

        [IgnoreDataMember]
        public string ClientId { get; set; }

        //当前商品类别 所有层级是否含有沙发类
        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// 即时库存量
        /// </summary>
        public string RealStockQty { get; set; } = "0";

        /// <summary>
        /// 在途量
        /// </summary>
        public string IntransitQty { get; set; }

        /// <summary>
        /// 预留量
        /// </summary>
        public string ReserveQty { get; set; }

        /// <summary>
        /// 可用库存量
        /// </summary>
        public string UsableStockQty { get; set; } = "0";

        /// <summary>
        /// 清库存
        /// </summary>
        public bool IsClearStock { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 商品编码
        /// </summary>
        public string Number { get; set; } = string.Empty;

        /// <summary>
        /// 商品主图地址
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// 销售价
        /// </summary>
        public decimal SalPrice { get; set; }

        /// <summary>
        /// 总部零售价
        /// </summary>
        public decimal HqSalPrice { get; set; }

        /// <summary>
        /// 厂家指导价
        /// </summary>
        public decimal GuidePrice { get; set; }

        /// <summary>
        /// 辅助属性列表
        /// </summary>
        public List<PropModel> AuxPropInfo { get; set; } = new List<PropModel>();

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 库存详情列表
        /// </summary>
        public List<StockDetailModel> StockList { get; set; } = new List<StockDetailModel>();

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 选配套件
        /// </summary>
        public bool IsSuite { get; set; }

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool IsGiveaway { get; set; }
        /// <summary>
        /// 数据来源
        /// </summary>
        public string Dataorigin { get; set; }

        /// <summary>
        /// 是否允许下单
        /// </summary>
        public bool IsAllowOrders { get; set; }
    }

    /// <summary>
    /// 库存详情模型
    /// </summary>
    public class StockDetailModel
    {
        /// <summary>
        /// 即时库存量
        /// </summary>
        public string RealStockQty { get; set; } = "0";

        /// <summary>
        /// 可用库存量
        /// </summary>
        public string UsableStockQty { get; set; }

        /// <summary>
        /// 库存状态
        /// </summary>
        public ComboDataModel StockStatus { get; set; } = new ComboDataModel();

        /// <summary>
        /// 仓库
        /// </summary>
        public ComboDataModel StoreHouse { get; set; } = new ComboDataModel();

        /// <summary>
        /// 仓位
        /// </summary>
        public ComboDataModel StoreLocation { get; set; } = new ComboDataModel();
    }
}