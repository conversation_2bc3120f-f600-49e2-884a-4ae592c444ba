using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.Serialization;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.MP.API.DTO.SEL.Suite;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.Core;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 商品详情数据模型
    /// </summary>
    public class ProductDetailModel : BaseDataModel
    {
        /// <summary>
        /// 数据状态：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 创建时间：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public new DateTime CreateDate { get; set; }

        /// <summary>
        /// 商品主图地址
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// 商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; }

        /// <summary>
        /// 销售价
        /// </summary>
        public decimal SalPrice { get; set; }

        /// <summary>
        /// 销售价是否来源于经销商自己的价目表
        /// </summary>
        public bool FromAgent { get; set; }

        /// <summary>
        /// 总部零售价
        /// </summary>
        public decimal HqSalPrice { get; set; }

        /// <summary>
        /// 品牌指导价
        /// </summary>
        public decimal GuidePrice { get; set; }

        ///// <summary>
        ///// 品牌id
        ///// </summary>
        //public string BrandId { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public ComboDataModel Brand { get; set; } = new ComboDataModel();

        /// <summary>
        /// 空间
        /// </summary>
        public string Space { get; set; }

        /// <summary>
        /// 风格
        /// </summary>
        public string Style { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specifica { get; set; }

        /// <summary>
        /// 尺寸
        /// </summary>
        public string Size { get; set; }

        /// <summary>
        /// 净重
        /// </summary>
        public decimal Suttle { get; set; }

        /// <summary>
        /// 毛重
        /// </summary>
        public decimal GrossLoad { get; set; }

        /// <summary>
        /// 体积
        /// </summary>
        public decimal Volume { get; set; }
        /// <summary>
        /// 产地
        /// </summary>
        public string Originplace { get; set; }

        /// <summary>
        /// 是否允许定制
        /// </summary>
        public bool IsCustom { get; set; }

        /// <summary>
        /// 是否允许选配
        /// </summary>
        public bool IsPresetProp { get; set; }

        /// <summary>
        /// 选配套件
        /// </summary>
        public bool IsSuite { get; set; }

        // /// <summary>
        // /// 是否套件
        // /// </summary>
        // public bool FIsSuite { get; set; }

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool IsGiveaway { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Dataorigin { get; set; }

        /// <summary>
        /// 是否允许下单
        /// </summary>
        public bool IsAllowOrders { get; set; }
        /// <summary>
        /// 零售价可编辑
        /// </summary>
        public bool IsEditPrice { get; set; }
        /// <summary>
        /// 是否可添加配件
        /// </summary>
        public bool IsAddibleParts { get; set; }
        /// <summary>
        /// 配件标记
        /// </summary>
        public bool IsPartFlag { get; set; }

        /// <summary>
        /// 业绩品牌
        /// </summary>
        public ComboDataModel_status ResultBrand { get; set; } = new ComboDataModel_status();

        /// <summary>
        /// 是否固定辅助属性组合值
        /// </summary>
        public bool IsFixProp { get; set; }

        ///// <summary>
        ///// 可用库存量
        ///// </summary>
        //public decimal UsableStockQty { get; set; }

        ///// <summary>
        ///// 即时库存量
        ///// </summary>
        //public decimal RealStockQty { get; set; }

        /// <summary>
        /// 商品非标选配属性值默认个数显示配置
        /// </summary>
        public int MoreNumConf { get; set; }

        /// <summary>
        /// 提货方式
        /// </summary>
        public ComboDataModel DeliveryMode { get; set; } = new ComboDataModel();

        /// <summary>
        /// 销售单位
        /// </summary>
        public BaseDataSimpleModel SalUnit { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 辅助属性列表
        /// </summary>
        public List<PropModel> AuxPropInfo { get; set; } = new List<PropModel>();

        /// <summary>
        /// 商品描述
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 商品标签
        /// </summary>
        public List<ProductTagModel> Tags { get; set; } = new List<ProductTagModel>();

        ///// <summary>
        ///// 系列id
        ///// </summary>
        //public string SeriesId { get; set; }

        /// <summary>
        /// 系列
        /// </summary>
        public ComboDataModel Series { get; set; } = new ComboDataModel();

        /// <summary>
        /// 子件商品列表
        /// </summary>
        public List<PartsModel> PartsList { get; set; } = new List<PartsModel>();

        /// <summary>
        /// 商品选配类别ID
        /// </summary>
        [IgnoreDataMember]
        public string SelCategoryId { get; set; } = string.Empty;

        //当前商品类别 所有层级是否含有沙发类
        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// 商品类别
        /// </summary>
        public BaseDataDTO Category { get; set; }

        /// <summary>
        /// "套件总件数"属性。是套件商品时才返回。
        /// </summary>
        public BaseDataDTO SuiteSumQtyProp { get; set; }
    }

    /// <summary>
    /// 子件商品模型
    /// </summary>
    public class PartsModel
    {
        /// <summary>
        /// 子件商品选配类别ID
        /// </summary>
        [IgnoreDataMember]
        public string SelCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// 子件商品ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 子件商品编码
        /// </summary>
        public string Number { get; set; } = string.Empty;

        /// <summary>
        /// 子件商品名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 子件商品数量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 子件商品价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 子件商品总部零售价
        /// </summary>
        public decimal HqPrice { get; set; }

        /// <summary>
        /// 是否默认
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 子件属性
        /// </summary>
        public BaseDataDTO PartProp { get; set; }

        /// <summary>
        /// 子件商品类别
        /// </summary>
        public BaseDataDTO Category { get; set; } = new BaseDataDTO();

        /// <summary>
        /// 子件商品图片列表
        /// </summary>
        public List<BaseImageModel> ImageList { get; set; } = new List<BaseImageModel>();

        /// <summary>
        /// 辅助属性列表
        /// </summary>
        public List<PropEntity> AuxPropVals { get; set; } = new List<PropEntity>();
        /// <summary>
        /// 当前商品类别 所有层级是否含有沙发类
        /// </summary>
        public bool IsSofaCategory { get; set; }

        /// <summary>
        /// 是否允许选配
        /// </summary>
        public bool IsPresetProp { get; set; }


        /// <summary>
        /// 商品促销id
        /// </summary>
        public JObject fpromotionid { get; set; }

        /// <summary>
        /// 促销规则
        /// </summary>
        public string fpromotionrule { get; set; }

        /// <summary>
        /// 特价销售价
        /// </summary>
        public decimal fpromotionsalprice { get; set; }

        /// <summary>
        /// 特价最低价
        /// </summary>
        public decimal fpromotionlowestprice { get; set; }
    }
}
