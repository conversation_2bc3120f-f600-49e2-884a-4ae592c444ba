using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class CustomerSimpleModel : BaseDataModel
    {
        public CustomerSimpleModel()
        {

        }

        public CustomerSimpleModel(DynamicObject data)
        {
            Id = JNConvert.ToStringAndTrim(data?["id"]);
            Number = JNConvert.ToStringAndTrim(data?["fnumber"]);
            Name = JNConvert.ToStringAndTrim(data?["fname"]);
            Phone = JNConvert.ToStringAndTrim(data?["fphone"]);
            CreateDate = Convert.ToDateTime(data?["fcreatedate"]);
        }

        /// <summary>
        /// 数据状态：该属性不需要被序列化到前端
        /// </summary>
        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }
    }
}
