using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 客户列表数据模型
    /// </summary>
    public class CustomerListModel : BaseDataModel
    {
        public new ComboDataModel Status { get; set; }

        /// <summary>
        /// 客户手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 客户性质
        /// </summary>
        public ComboDataModel Nature { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public ComboDataModel Source { get; set; }

        /// <summary>
        /// 客户等级
        /// </summary>
        public BaseDataSimpleModel Level { get; set; }

        /// <summary>
        /// 含未成交合同
        /// </summary>
        public bool HasNotDealOrder { get; set; }

        /// <summary>
        /// 客户类型（ftype）
        /// </summary>
        public ComboDataModel Type { get; set; }

        /// <summary>
        /// 最后跟进时间
        /// </summary>
        public DateTime? FollowTime { get; set; }

        /// <summary>
        /// 多负责人
        /// </summary>
        public ComboDataModel Duty { get; set; }

        /// <summary>
        /// 多部门
        /// </summary>
        public ComboDataModel Dept { get; set; }

        /// <summary>
        /// 省
        /// </summary>
        public ComboDataModel Province { get; set; }

        /// <summary>
        /// 市
        /// </summary>
        public ComboDataModel City { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public ComboDataModel Region { get; set; }

        /// <summary>
        /// 详情地址
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 完整地址
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 收货地址明细
        /// </summary>
        public List<CustomerAddressListModel> AddressEntry { get; set; } = new List<CustomerAddressListModel>();
        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 累计消费金额
        /// </summary>
        public decimal SumAmount { get; set; }
        /// <summary>
        /// 楼盘信息
        /// </summary>

        public BaseDataSimpleModel Building { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 合作渠道
        /// </summary>
        public Object Channel { get; set; } = new Object();

        /// <summary>
        /// 客户来源门店
        /// </summary>
        public ComboDataModel CustomerSrcStore { get; set; }

        /// <summary>
        /// 默认客户联系人Id
        /// </summary>
        public string CustomercontactId { get; set; }
    }
}