using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.BD.Staff
{
    public  class StaffFollowUpCustomerModel
    {
        /// <summary>
        /// 客户Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 客户电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 客户建档时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        ///  客户性质
        /// </summary>
        public string Cusnature { get; set; }

        /// <summary>
        ///  负责人
        /// </summary>
        public string StaffId { get; set; }
    }

  
}
