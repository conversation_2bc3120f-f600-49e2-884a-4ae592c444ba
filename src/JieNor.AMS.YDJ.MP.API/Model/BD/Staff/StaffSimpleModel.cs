using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class StaffSimpleModel : BaseDataSimpleModel
    {
        public StaffSimpleModel()
        {

        }

        public StaffSimpleModel(DynamicObject data) : base(data)
        {
            Phone = JNConvert.ToStringAndTrim(data?["fphone"]);
            Position = JNConvert.ToStringAndTrim(data?["fmainpositionid"]);
        }

        public string Position { get; set; }

        public string Phone { get; set; }
    }
}
